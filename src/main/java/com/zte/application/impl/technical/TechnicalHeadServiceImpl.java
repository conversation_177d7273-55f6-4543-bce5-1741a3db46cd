package com.zte.application.impl.technical;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Lists;
import com.zte.application.*;
import com.zte.application.technical.TechnicalHeadService;
import com.zte.common.CommonUtils;
import com.zte.common.DateUtil;
import com.zte.common.excel.ExcelCommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.*;
import com.zte.domain.model.*;
import com.zte.domain.model.technical.TechnicalChangeDetailRepository;
import com.zte.domain.model.technical.TechnicalChangeExecInfo;
import com.zte.domain.model.technical.TechnicalChangeExecInfoRepository;
import com.zte.domain.model.technical.TechnicalChangeHeadRepository;
import com.zte.infrastructure.remote.*;
import com.zte.interfaces.dto.PsWorkOrderDTO;
import com.zte.interfaces.dto.*;
import com.zte.interfaces.dto.technical.*;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.annotation.RecordLogAnnotation;
import com.zte.springbootframe.common.annotation.RedisDistributedLockAnnotation;
import com.zte.springbootframe.common.annotation.RedisLockParamAnnotation;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.FactoryConfig;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.springbootframe.common.redis.RedisLock;
import com.zte.springbootframe.util.EmailUtils;
import com.zte.springbootframe.util.ImesExcelUtil;
import com.zte.springbootframe.util.JsonConvertUtil;
import com.zte.springbootframe.util.RequestHeadValidationUtil;
import com.zte.ums.nfv.eco.hsif.msb.common.RouteException;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.zte.common.utils.Constant.*;

/**
 * <AUTHOR>
 * @date 2022-09-29 10:26
 */
@Log4j2
@Service
public class TechnicalHeadServiceImpl implements TechnicalHeadService {

    @Autowired
    private CenterfactoryRemoteService centerfactoryRemoteService;
    @Autowired
    private TechnicalChangeHeadRepository technicalChangeHeadRepository;
    @Autowired
    private TechnicalChangeDetailRepository technicalChangeDetailRepository;
    @Autowired
    private TechnicalChangeExecInfoRepository technicalChangeExecInfoRepository;
    @Autowired
    private BarcodeLockHeadService barcodeLockHeadService;
    @Autowired
    private BarcodeLockDetailService barcodeLockDetailService;
    @Autowired
    private BarcodeLockHeadRepository barcodeLockHeadRepository;
    @Autowired
    private BarcodeUnlockService barcodeUnlockService;
    @Autowired
    private PsWipInfoRepository psWipInfoRepository;
    @Autowired
    private FactoryConfig factoryConfig;
    @Autowired
    private EmailUtils emailUtils;
    @Autowired
    private IMESLogService iMESLogService;
    @Autowired
    private CommonTechnicalService commonTechnicalService;
    @Autowired
    private BarcodeLockDetailRepository barcodeLockDetailRepository;
    @Autowired
    private BarcodeCenterRemoteService barcodeCenterRemoteService;

    private static int roundCount = 0;

    @Value("${tech.sync.predays:1}")
    private int preDays;
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * kafka消费处理技改单信息
     *
     * @param pdmTechnicalDealForKafkaDTO
     * @throws Exception
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void technicalChangeInfoDeal(PdmTechnicalDealForKafkaDTO pdmTechnicalDealForKafkaDTO) throws Exception {
        //技改单号
        String chgReqNo = pdmTechnicalDealForKafkaDTO.getChgReqNo();
        //单据状态
        String sendStatus = pdmTechnicalDealForKafkaDTO.getSendStatus();
        //最后更新人
        setUpdateByAndName(pdmTechnicalDealForKafkaDTO);
        //避免重复执行
        RedisLock redisLock = new RedisLock(RedisKeyConstant.PDM_TECHNICAL_DEAL_KEY + Constant.COLON + chgReqNo
                + Constant.COLON + pdmTechnicalDealForKafkaDTO.getFactoryId(), NumConstant.NUM_60 * NumConstant.NUM_TEN);
        try {
            if (!redisLock.lock()) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.TECHNICAL_TRANSFORMATION_SHEET_IS_BEING_PROCESSED);
            }
            //获取对应批次任务信息和工艺路径
            this.setPsTaskAndRouteInfo(pdmTechnicalDealForKafkaDTO.getNeedDealList());
            //未发放
            if (StringUtils.equals(sendStatus, Constant.PdmTechnicalChangeStatus.UNISSUED)) {
                dealForUnissued(pdmTechnicalDealForKafkaDTO);
            }
            //已发放
            else if (StringUtils.equals(sendStatus, Constant.PdmTechnicalChangeStatus.ISSUED)) {
                dealForIssued(pdmTechnicalDealForKafkaDTO);
            }
            //已删除 已报废单据
            else if (StringUtils.equals(sendStatus, Constant.PdmTechnicalChangeStatus.DELETED) || StringUtils.equals(sendStatus, Constant.PdmTechnicalChangeStatus.VOIDED)) {
                TechnicalChangeHeadDTO technicalChangeHeadDTO = technicalChangeHeadRepository.getTechnicalChangeHeadDTOByChgReqNo(chgReqNo);
                //存在不是已作废技改单 解锁技改单
                if (technicalChangeHeadDTO != null && !StringUtils.equals(Constant.TechnicalChangeStatus.VOIDED, technicalChangeHeadDTO.getTechnicalStatus())) {
                    //0待提交，1拟制中，2锁定中，3已解锁，4已作废
                    technicalChangeHeadRepository.updateStatusByChgReqNo(Constant.TechnicalChangeStatus.VOIDED, chgReqNo, pdmTechnicalDealForKafkaDTO.getUpdatedBy());
                    technicalChangeDetailRepository.updateStatusByChgReqNo(Constant.TechnicalChangeStatus.VOIDED, chgReqNo, pdmTechnicalDealForKafkaDTO.getUpdatedBy());
                    //发送邮件
                    sendMailForDeleteOrVoided(pdmTechnicalDealForKafkaDTO);
                }
                //处理锁定单
                dealLockDetail(chgReqNo, pdmTechnicalDealForKafkaDTO.getUpdatedBy(), sendStatus);
            } else {
                return;
            }
            //更新中心工厂数据状态
            updateCenterFactoryTechicalChangeInfoStatus(pdmTechnicalDealForKafkaDTO);
        } finally {
            if (redisLock != null) {
                redisLock.unlock();
            }
        }
    }

    /**
     * 设置最后更新人以及名称
     *
     * @param pdmTechnicalDealForKafkaDTO
     * @return
     * @throws Exception
     */
    private void setUpdateByAndName(PdmTechnicalDealForKafkaDTO pdmTechnicalDealForKafkaDTO) throws Exception {
        String updateBy = pdmTechnicalDealForKafkaDTO.getUpdatedBy();
        String updateByName = "";
        if (StringUtils.length(updateBy) > NumConstant.NUM_SEVEN) {
            Pattern pattern = Pattern.compile(Constant.LAST_NUMBER_REGEX);
            Matcher matcher = pattern.matcher(updateBy);
            StringBuffer stringBuffer = new StringBuffer();
            if(matcher.find()) {
                updateBy = matcher.group();
                matcher.appendReplacement(stringBuffer, EMPTY_STRING);
            }
            matcher.appendTail(stringBuffer);
            updateByName = stringBuffer.toString();
        } else {
            updateByName = getUpdateByName(updateBy);
        }
        pdmTechnicalDealForKafkaDTO.setUpdatedBy(updateBy);
        pdmTechnicalDealForKafkaDTO.setUpdatedName(updateByName);
    }


    @Override
    public List<TechnicalChangeDetailDTO> queryTechnicalDetailList(String chgReqNo) {
        // 1、检查head头表状态，对该单号的合法性进行校验，校验完成后带出该技改单涉及的批次填充至批次下拉框，校验规则如下：
        //    a、该技改单是否存在，不存在则报错“XXX技改单不存在，请确认技改单号是否正确”
        //    b、该单据状态是否为“已解锁”，如为“已解锁”则报错“XXX技改单已解锁，请确认技改单号是否正确”
        TechnicalChangeHeadDTO technicalChangeHeadDTO = technicalChangeHeadRepository.getTechnicalChangeHeadDTOByChgReqNo(chgReqNo);
        if (null == technicalChangeHeadDTO) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.TECHICAL_CHANGE_NOT_EXITS, new Object[]{chgReqNo});
        }
        if (NumConstant.STRING_THREE.equals(technicalChangeHeadDTO.getTechnicalStatus())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.TECHICAL_CHANGE_UNLOCKED, new Object[]{chgReqNo});
        }
        // 2、查询详细表返回批次、所属料单、管控工序
        List<TechnicalChangeDetailDTO> list = technicalChangeDetailRepository.getListByBillNo(chgReqNo);
        this.setMBom(list);
        return list;
    }

    /**
     * 查询滞留清单 通过条码
     *
     * @param query 条码
     * @return 技改信息
     */
    @Override
    public List<TechnicalChangeDetailDTO> retentionSn(TechnicalChangeRetentionListQueryDTO query) throws Exception {
        List<TechnicalChangeDetailDTO> resultList = new LinkedList<>();
        this.checkSnProperties(query);
        List<PsTask> psTasks = CenterfactoryRemoteService.getPsTaskListFromCenterFactory(query.getProdplanId());
        if (CollectionUtils.isEmpty(psTasks)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PRODPLANID_IS_NOT_EXITS,
                    new Object[]{query.getProdplanId()});
        }
        PsTask psTask = psTasks.get(0);
        String factoryId = Objects.isNull(psTask.getFactoryId()) ? FACTORY_ID_CENTER : psTask.getFactoryId().toString();
        if (!query.getFactoryId().equals(factoryId)) {
            // 请去XX条码不在本工厂加工，请切换至XX工厂反馈技改结果
            this.buildNoticeMsg(query, resultList, factoryId);
            return resultList;
        }
        // 3. 查询技改单锁定信息
        TechnicalChangeDetailDTO technical = new TechnicalChangeDetailDTO();
        // 锁定中的单据
        technical.setTechnicalStatus(STR_2);
        technical.setProdplanIdList(Arrays.asList(query.getProdplanId()));
        List<TechnicalChangeDetailDTO> technicalList = technicalChangeHeadRepository.queryTechnicalDetail(technical);
        if (CollectionUtils.isEmpty(technicalList)) {
            return resultList;
        }

        // 4. 技改已经反馈，不需要再次反馈
        List<String> chgNoList = technicalList.stream().map(TechnicalChangeDetailDTO::getChgReqNo)
                .distinct().collect(Collectors.toList());
        TechnicalChangeExecInfo execInfo = new TechnicalChangeExecInfo();
        execInfo.setChgReqNoList(chgNoList);
        execInfo.setSn(query.getSn());
        execInfo.setFinishUnlock(INT_1);
        execInfo.setUnlockType(query.getUnlockType());
        List<TechnicalChangeExecInfo> execInfoList = technicalChangeExecInfoRepository.select(execInfo);
        if (Objects.isNull(execInfoList)) {
            execInfoList = new LinkedList<>();
        }
        List<String> feedbackList = execInfoList.stream().map(TechnicalChangeExecInfo::getChgReqNo)
                .collect(Collectors.toList());
        technicalList.removeIf(item -> feedbackList.contains(item.getChgReqNo()));
        if (CollectionUtils.isEmpty(technicalList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.TECHNICAL_EXEC_INFO_EXISTED,
                    new Object[]{query.getSn()});
        }
        technicalList.forEach(item -> {
            item.setSn(query.getSn());
            item.setCurrProcess(StringUtils.EMPTY);
            item.setUnlockCraftSection(StringUtils.EMPTY);
        });
        // 过滤掉右边已经选择的条码
        List<TechnicalChangeRetentionListQueryDTO> excludeList = query.getExcludeList();
        if (Objects.isNull(excludeList)) {
            excludeList = new LinkedList<>();
        }
        List<String> chgReqSnList = excludeList.stream().map(item -> item.getChgReqNo() + item.getSn())
                .collect(Collectors.toList());
        technicalList.removeIf(item -> chgReqSnList.contains(item.getChgReqNo() + item.getSn()));
        return technicalList;
    }

    private void buildNoticeMsg(TechnicalChangeRetentionListQueryDTO query, List<TechnicalChangeDetailDTO> resultList, String factoryId) throws Exception {
        List<SysLookupValuesDTO> batchSysValueByCode = BasicsettingRemoteService.getBatchSysValueByCode(Arrays.asList(LOOKUP_TYPE_1245));
        SysLookupValuesDTO sysLookupValuesDTO = batchSysValueByCode.stream()
                .filter(item -> factoryId.equals(item.getLookupMeaning()))
                .findFirst().orElse(new SysLookupValuesDTO() {{
                    setDescriptionChin(factoryId);
                }});
        TechnicalChangeDetailDTO snDTO = new TechnicalChangeDetailDTO();
        snDTO.setSn(query.getSn());
        snDTO.setFactoryId(sysLookupValuesDTO.getDescriptionChin());
        snDTO.setMsg(CommonUtils.getLmbMessage(MessageId.SN_EXIST_FACTORY, new String[]{query.getSn(),
                sysLookupValuesDTO.getDescriptionChin()}));
        resultList.add(snDTO);
    }

    /**
     * 校验条码正确性规则
     *
     * @param query 查询参数
     * @throws Exception 业务异常
     */
    private void checkSnProperties(TechnicalChangeRetentionListQueryDTO query) throws Exception {
        String sn = query.getSn();
        if (StringUtils.isBlank(sn) || sn.length() <= INT_5) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SN_RULE_ERROR,
                    new Object[]{sn});
        }
        BarcodeExpandQueryDTO barcodeQueryDTO = new BarcodeExpandQueryDTO();
        barcodeQueryDTO.setParentCategoryCode(MpConstant.SN_CODE);
        barcodeQueryDTO.setBarcodeList(Collections.singletonList(query.getSn()));
        List<BarcodeExpandDTO> expandDTOList = barcodeCenterRemoteService.expandQuery(barcodeQueryDTO);
        if (CollectionUtils.isEmpty(expandDTOList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SN_NULL,
                    new Object[]{query.getSn()});
        }
        // 查询中心工厂任务批次
        String prodplanId = sn.substring(0, sn.length() - INT_5);
        query.setProdplanId(prodplanId);
    }

    @Override
    public Page<TechnicalChangeSnDTO> queryRetentionPage(Page<TechnicalChangeRetentionListQueryDTO> query) throws Exception {
        TechnicalChangeRetentionListQueryDTO queryDTO = BeanUtil.toBean(query.getParams(), TechnicalChangeRetentionListQueryDTO.class);
        if (StringUtils.isNotBlank(queryDTO.getSn())) {
            // 已经有了扫描记录,提示信息
            TechnicalChangeExecInfo execInfo = new TechnicalChangeExecInfo();
            BeanUtils.copyProperties(queryDTO, execInfo);
            execInfo.setFinishUnlock(NumConstant.NUM_ONE);
            List<TechnicalChangeExecInfo> execInfoList = technicalChangeExecInfoRepository.select(execInfo);
            if (CollectionUtils.isNotEmpty(execInfoList)) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.TECHNICAL_EXEC_INFO_EXISTED,
                        new Object[]{queryDTO.getSn()});
            }

            // 先看在wip_info表是否存在
            Map<String, Object> record = new HashMap<>();
            record.put("sn", queryDTO.getSn());
            List<PsWipInfo> wipInfoList = psWipInfoRepository.getList(record);
            if (CollectionUtils.isEmpty(wipInfoList)) {
                if (queryDTO.getSn().startsWith(queryDTO.getProdplanId())) {
                    //查询条码中心是否存在该条码
                    BarcodeExpandQueryDTO barcodeQueryDTO = new BarcodeExpandQueryDTO();
                    barcodeQueryDTO.setParentCategoryCode(MpConstant.SN_CODE);
                    barcodeQueryDTO.setBarcodeList(Collections.singletonList(queryDTO.getSn()));
                    List<BarcodeExpandDTO> expandDTOList = barcodeCenterRemoteService.expandQuery(barcodeQueryDTO);
                    if (CollectionUtils.isNotEmpty(expandDTOList)) {
                        TechnicalChangeSnDTO changeSnDTO = new TechnicalChangeSnDTO();
                        BeanUtils.copyProperties(queryDTO, changeSnDTO);
                        Page<TechnicalChangeSnDTO> res = new Page<>();
                        List<TechnicalChangeSnDTO> technicalChangeSnDTOList = technicalChangeHeadRepository.selectRetentionList(query);
                        res.setRows(technicalChangeSnDTOList);
                        res.setTotal(technicalChangeSnDTOList.size());
                        return res;
                    }
                } else {
                    throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SN_NOT_BELONG_PRODPLANID,
                            new Object[]{queryDTO.getSn(), queryDTO.getProdplanId()});
                }
                // wip_info和条码中心都不存在
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SN_NULL,
                        new Object[]{queryDTO.getSn()});
            }

            // wip_info批次 和界面批次不一致
            if (!queryDTO.getProdplanId().equals(wipInfoList.get(NumConstant.NUM_ZERO).getAttribute1())) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SN_NOT_BELONG_PRODPLANID,
                        new Object[]{queryDTO.getSn(), queryDTO.getProdplanId()});
            }
        }

        Page<TechnicalChangeSnDTO> res = new Page<>();
        List<TechnicalChangeSnDTO> technicalChangeSnDTOList = technicalChangeHeadRepository.selectRetentionPage(query);
        setProcessName(technicalChangeSnDTOList);
        res.setRows(technicalChangeSnDTOList);
        res.setTotal(query.getTotal());
        res.setTotalPage(query.getTotalPage());
        res.setPageSize(query.getPageSize());
        res.setCurrent(query.getCurrent());
        return res;
    }

    private void setProcessName(List<TechnicalChangeSnDTO> technicalChangeSnDTOList) {
        try {
            List<BSProcessInfoDTO> processInfoDTOList = getBatchProcessName(technicalChangeSnDTOList.stream()
                    .map(TechnicalChangeSnDTO::getCurrProcess).filter(string -> !string.isEmpty()).distinct().collect(Collectors.toList()));
            if (CollectionUtils.isEmpty(processInfoDTOList)) {
                return;
            }
            for (TechnicalChangeSnDTO technicalChangeSnDTO : technicalChangeSnDTOList) {
                technicalChangeSnDTO.setCurrProcessName(processInfoDTOList.stream()
                        .filter(item -> item.getProcessCode().equals(technicalChangeSnDTO.getCurrProcess()))
                        .findFirst().orElse(new BSProcessInfoDTO()).getProcessName());
            }
        } catch (Exception e) {
            log.error("获取工序名称异常");
        }
    }

    private List<BSProcessInfoDTO> getBatchProcessName(List<String> processCodeList) throws Exception {
        BSProcessInfoDTO dto = new BSProcessInfoDTO();
        dto.setProcessCodeList(processCodeList);
        JsonNode json = CrafttechRemoteService.getBatchProcessName(dto);
        if (null == json) {
            return null;
        }
        String retCode = json.get(MpConstant.JSON_CODE).get(MpConstant.JSON_CODE).asText();
        String bo = json.get(MpConstant.JSON_BO).toString();
        if (!com.zte.itp.msa.core.model.RetCode.SUCCESS_CODE.equalsIgnoreCase(retCode)) {
            throw new Exception(bo);
        }
        return JsonConvertUtil.jsonToBean(bo, List.class, BSProcessInfoDTO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void scanSubmit(List<TechnicalChangeExecInfo> paramList) {
        if (CollectionUtils.isEmpty(paramList)) {
            return;
        }
        Pair<String, String> pair = RequestHeadValidationUtil.validaFactoryIdAndEmpno();
        List<String> lockKey = paramList.stream()
                .map(item -> String.format(Constant.TECHNICAL_SCAN_REDIS_KEY, pair.getFirst(), item.getChgReqNo(),
                        item.getProdplanId()))
                .distinct().collect(Collectors.toList());
        DefaultRedisScript<Long> redisScript = new DefaultRedisScript<>();
        redisScript.setResultType(Long.class);
        redisScript.setLocation(new ClassPathResource("lock.lua"));
        String uuid = UUID.randomUUID().toString();
        try {
            Long index = redisTemplate.execute(redisScript, lockKey, uuid);
            if (!Objects.isNull(index) && index > -1) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.CHG_SUBMIT_ERROR,
                        new Object[]{lockKey.get(index.intValue())});
            }
            checkScanData(paramList);
            // 新增扫描数据
            for (TechnicalChangeExecInfo technicalChangeExecInfo : paramList) {
                technicalChangeExecInfo.setId(UUID.randomUUID().toString());
                technicalChangeExecInfo.setCreatedBy(pair.getSecond());
                technicalChangeExecInfo.setLastUpdatedBy(pair.getSecond());
                technicalChangeExecInfo.setUnlockStatus(NumConstant.NUM_ZERO);
            }
            CommonUtils.splitList(paramList, Constant.BATCH_SIZE).forEach(list -> {
                technicalChangeExecInfoRepository.insertBatch(list);
            });
        } finally {
            redisScript.setLocation(new ClassPathResource("unlock.lua"));
            redisTemplate.execute(redisScript, lockKey, uuid);
        }
    }

    private void checkScanData(List<TechnicalChangeExecInfo> paramList) {
        // 检查已经提交了的条码
        List<TechnicalChangeExecInfo> snList = new ArrayList<>();
        CommonUtils.splitList(paramList, Constant.BATCH_SIZE)
                .forEach(list -> snList.addAll(technicalChangeExecInfoRepository.selectSnBatch(list)));
        Map<String, List<TechnicalChangeExecInfo>> collectMap =
                snList.stream().collect(Collectors.groupingBy(TechnicalChangeExecInfo::getChgReqNo));
        StringBuffer msgBuffer = new StringBuffer();
        for (Map.Entry<String, List<TechnicalChangeExecInfo>> entry : collectMap.entrySet()) {
            List<TechnicalChangeExecInfo> value = entry.getValue();
            String sns = value.stream().map(TechnicalChangeExecInfo::getSn).distinct().collect(Collectors.joining(COMMA));
            msgBuffer.append(CommonUtils.getLmbMessage(MessageId.SN_CHG_EXIST, new String[]{entry.getKey(), sns}));
        }
        if (StringUtils.isNotBlank(msgBuffer.toString())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.CUSTOMIZE_MSG,
                    new Object[]{msgBuffer.toString()});
        }
    }

    @Override
    public void unLockProdPlan(String chgReqNo, String updateBy, String sendStatus) throws Exception {
        this.dealLockDetail(chgReqNo, updateBy, sendStatus);
    }

    /**
     * 更新中心工厂数据状态
     *
     * @param pdmTechnicalDealForKafkaDTO
     * @throws Exception
     */
    private void updateCenterFactoryTechicalChangeInfoStatus(PdmTechnicalDealForKafkaDTO pdmTechnicalDealForKafkaDTO) throws Exception {
        if (CollectionUtils.isEmpty(pdmTechnicalDealForKafkaDTO.getNeedDealList())) {
            return;
        }
        for (PdmTechnicalChangeInfoEntityDTO pdmTechnicalChangeInfoEntityDTO : pdmTechnicalDealForKafkaDTO.getNeedDealList()) {
            //已删除已作废的不关注任务和工艺路径 已发放的都会处理成功
            if (StringUtils.equals(pdmTechnicalChangeInfoEntityDTO.getSendStatus(), Constant.PdmTechnicalChangeStatus.DELETED)
                    || StringUtils.equals(pdmTechnicalChangeInfoEntityDTO.getSendStatus(), Constant.PdmTechnicalChangeStatus.VOIDED)
                    || StringUtils.equals(pdmTechnicalChangeInfoEntityDTO.getSendStatus(), Constant.PdmTechnicalChangeStatus.ISSUED)) {
                pdmTechnicalChangeInfoEntityDTO.setDealStatus(Constant.S);
                pdmTechnicalChangeInfoEntityDTO.setDealResult("");
            }
            //更新中心工厂的技改单号对应数据
            else if (StringUtils.isNotEmpty(pdmTechnicalChangeInfoEntityDTO.getErrorMsg())) {
                pdmTechnicalChangeInfoEntityDTO.setDealStatus(Constant.F);
                pdmTechnicalChangeInfoEntityDTO.setDealResult(pdmTechnicalChangeInfoEntityDTO.getErrorMsg());
            } else {
                pdmTechnicalChangeInfoEntityDTO.setDealStatus(Constant.S);
                pdmTechnicalChangeInfoEntityDTO.setDealResult("");
            }
        }
        centerfactoryRemoteService.updateTechnicalChangeInfoDealStatus(pdmTechnicalDealForKafkaDTO.getNeedDealList());
    }


    /**
     * 设置批次对应任务信息以及工艺路径数据
     *
     * @param pdmTechnicalChangeInfoEntityDTOList
     * @throws Exception
     */
    private void setPsTaskAndRouteInfo(List<PdmTechnicalChangeInfoEntityDTO> pdmTechnicalChangeInfoEntityDTOList) throws Exception {
        if (CollectionUtils.isEmpty(pdmTechnicalChangeInfoEntityDTOList)) {
            return;
        }
        List<String> prodPlanIdList = pdmTechnicalChangeInfoEntityDTOList.stream().map(e -> e.getProdplanId()).distinct().collect(Collectors.toList());
        List<PsTask> psTaskList = PlanscheduleRemoteService.getPsTaskByProdplanIdList(prodPlanIdList);
        Map<String, PsTask> psTaskMap = CollectionUtils.isEmpty(psTaskList) ? new HashMap<>() : psTaskList.stream().collect(Collectors.toMap(PsTask::getProdplanId, a -> a, (k1, k2) -> k1));
        //获取批次工艺路径
        Map<String, List<CtRouteDetailDTO>> routeMap = barcodeLockDetailService.getRouteInfoByProdPlanIdList(prodPlanIdList);
        for (PdmTechnicalChangeInfoEntityDTO tempDTO : pdmTechnicalChangeInfoEntityDTOList) {
            PsTask psTask = psTaskMap.get(tempDTO.getProdplanId());
            tempDTO.setPsTask(psTask);
            if (psTask == null) {
                String errorMsg = String.format(Constant.BATCH_DOES_NOT_EXIST, tempDTO.getProdplanId());
                tempDTO.setErrorMsg((StringUtils.isEmpty(tempDTO.getErrorMsg()) ? "" : tempDTO.getErrorMsg()) + COMMA + errorMsg);
            }
            List<CtRouteDetailDTO> ctRouteDetailDTOList = routeMap.get(tempDTO.getProdplanId());
            tempDTO.setCtRouteDetailDTOList(ctRouteDetailDTOList);
            String taskStatus = psTask == null ? "" : psTask.getTaskStatus();
            //没有工艺路径并且任务状态不是已完工的报错
            setErrorMsg(tempDTO, ctRouteDetailDTOList, taskStatus);
        }
    }

    /**
     * 设置错误信息
     *
     * @param tempDTO
     * @param ctRouteDetailDTOList
     * @param taskStatus
     */
    private void setErrorMsg(PdmTechnicalChangeInfoEntityDTO tempDTO, List<CtRouteDetailDTO> ctRouteDetailDTOList, String taskStatus) {
        if (CollectionUtils.isEmpty(ctRouteDetailDTOList) && !StringUtils.equals(Constant.FINISH_WORK, taskStatus)) {
            String errorMsg = String.format(Constant.ROUTE_DOES_NOT_EXIST, tempDTO.getProdplanId());
            if (StringUtils.isEmpty(tempDTO.getErrorMsg())) {
                tempDTO.setErrorMsg(errorMsg);
            } else {
                tempDTO.setErrorMsg(tempDTO.getErrorMsg() + COMMA + errorMsg);
            }
        }
    }

    /**
     * 获取最后更新人姓名
     *
     * @param updateBy
     * @return
     * @throws Exception
     */
    private String getUpdateByName(String updateBy) throws Exception {
        String updateByName = "";
        try {
            List<String> userList = new ArrayList<>();
            userList.add(updateBy);
            Map<String, HrmPersonInfoDTO> hrmPersonInfoMap = centerfactoryRemoteService.getHrmPersonInfo(userList);
            updateByName = hrmPersonInfoMap.get(updateBy) == null ? "" : hrmPersonInfoMap.get(updateBy).getEmpName();
        } catch (Exception e) {
            log.error(e.getMessage(), Constant.GET_TECHICAL_UPDATE_BY_NAME_ERROR);
        }
        return updateByName;
    }

    /**
     * 发送邮件
     *
     * @param contentCN
     * @param updateBy
     */
    private void sendEmail(String updateBy, String contentCN, String subjectCN) {
        try {
            //主送 抄送
            StringBuilder recipenter = new StringBuilder();
            List<SysLookupTypesDTO> sysLookupTypesDTOList = BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_6726);
            if (CollectionUtils.isNotEmpty(sysLookupTypesDTOList)) {
                List<String> mainSendList = sysLookupTypesDTOList.stream().filter(e -> StringUtils.equals(Constant.MAIN_SEND, e.getAttribute1())).map(e -> e.getLookupMeaning()).collect(Collectors.toList());
                List<String> ccSendList = sysLookupTypesDTOList.stream().filter(e -> StringUtils.equals(Constant.CC_SEND, e.getAttribute1())).map(e -> e.getLookupMeaning()).collect(Collectors.toList());
                addSender(recipenter, mainSendList);
                addSender(recipenter, ccSendList);

            }
            if (StringUtils.isEmpty(recipenter.toString())) {
                recipenter.append(updateBy + MpConstant.MAILBOX_SUFFIX + Constant.SEMICOLON);
            } else if (!StringUtils.contains(recipenter.toString(), updateBy)) {
                recipenter.append(updateBy + MpConstant.MAILBOX_SUFFIX + Constant.SEMICOLON);
            }
            String sendMailer = recipenter.toString();
            if (StringUtils.isEmpty(sendMailer)) {
                return;
            }
            String[] sendMailerArr = sendMailer.split(Constant.SEMICOLON);
            List<String> sendMailerList = Arrays.asList(sendMailerArr).stream().distinct().collect(Collectors.toList());
            emailUtils.sendMail(StringUtils.join(sendMailerList, Constant.SEMICOLON), subjectCN, "", contentCN, "");
        } catch (Exception e) {
            log.error(e.getMessage());
            iMESLogService.log(e.getStackTrace(), Constant.SEND_EMAIL_NAME);
        }
    }

    /**
     * 获取邮件主送人
     *
     * @param recipenter
     * @throws Exception
     */
    private void addSender(StringBuilder recipenter, List<String> sendList) throws Exception {
        if (CollectionUtils.isEmpty(sendList)) {
            return;
        }
        List<String> senderList = getSenderList(sendList);
        if (CollectionUtils.isNotEmpty(senderList)) {
            for (String sender : senderList) {
                if (sender.contains(MpConstant.MAILBOX_SUFFIX)) {
                    recipenter.append(sender + Constant.SEMICOLON);
                } else {
                    recipenter.append(sender + MpConstant.MAILBOX_SUFFIX + Constant.SEMICOLON);
                }
            }
        }
    }


    /**
     * 处理技改单信息
     *
     * @param pdmTechnicalDealForKafkaDTO
     * @throws Exception
     */
    private void dealTechnicalChangeInfo(PdmTechnicalDealForKafkaDTO pdmTechnicalDealForKafkaDTO) throws Exception {
        if (CollectionUtils.isEmpty(pdmTechnicalDealForKafkaDTO.getNeedDealList())) {
            return;
        }
        TechnicalChangeHeadDTO insertDTO = new TechnicalChangeHeadDTO();
        insertDTO.setEmailSend(pdmTechnicalDealForKafkaDTO.getUpdatedBy());
        insertDTO.setEmailCopy("");

        //设置邮件主送人 抄送人
        setEmailSend(insertDTO);
        String headerId = UUID.randomUUID().toString();
        insertDTO.setHeadId(headerId);
        insertDTO.setChgReqNo(pdmTechnicalDealForKafkaDTO.getChgReqNo());
        insertDTO.setChgReqName(pdmTechnicalDealForKafkaDTO.getChgReqName());
        insertDTO.setCreatedBy(pdmTechnicalDealForKafkaDTO.getUpdatedBy());
        insertDTO.setLastUpdatedBy(pdmTechnicalDealForKafkaDTO.getUpdatedBy());
        insertDTO.setTechnicalStatus(Constant.TechnicalChangeStatus.LOCKING);
        List<TechnicalChangeHeadDTO> technicalChangeHeadDTOList = new ArrayList<>();
        technicalChangeHeadDTOList.add(insertDTO);
        technicalChangeHeadRepository.batchInsert(technicalChangeHeadDTOList);

        //写技改明细
        insertTechnicalChangeDetail(pdmTechnicalDealForKafkaDTO.getNeedDealList(), headerId, pdmTechnicalDealForKafkaDTO.getUpdatedBy());
    }

    /**
     * 处理已发放单据
     *
     * @param pdmTechnicalDealForKafkaDTO
     * @throws Exception
     */
    private void dealForIssued(PdmTechnicalDealForKafkaDTO pdmTechnicalDealForKafkaDTO) throws Exception {
        String chgReqNo = pdmTechnicalDealForKafkaDTO.getChgReqNo();
        String updateBy = pdmTechnicalDealForKafkaDTO.getUpdatedBy();
        TechnicalChangeHeadDTO technicalChangeHeadDTO = technicalChangeHeadRepository.getTechnicalChangeHeadDTOByChgReqNo(chgReqNo);
        //不存在技改单 新建技改单
        if (technicalChangeHeadDTO == null) {
            //存在需处理批次才新建技改单
            dealTechnicalChangeInfo(pdmTechnicalDealForKafkaDTO);
            //处理锁定单
            dealLockDetail(chgReqNo, updateBy, pdmTechnicalDealForKafkaDTO.getSendStatus());
            //发送邮件
            if (CollectionUtils.isNotEmpty(pdmTechnicalDealForKafkaDTO.getNeedDealList())) {
                sentMailWhenHasBeenIssued(pdmTechnicalDealForKafkaDTO);
            }
        } else {
            //存在非拟制的技改单不处理
            if (!StringUtils.equals(Constant.TechnicalChangeStatus.IN_PREPARATION, technicalChangeHeadDTO.getTechnicalStatus())) {
                return;
            }
            //更新技改单为锁定中
            technicalChangeHeadRepository.updateStatusByChgReqNo(Constant.TechnicalChangeStatus.LOCKING, chgReqNo, updateBy);
            //现有明细
            List<TechnicalChangeDetailDTO> technicalChangeDetailDTOList = technicalChangeDetailRepository.getListByBillNo(chgReqNo);

            if (CollectionUtils.isNotEmpty(technicalChangeDetailDTOList)) {
                //删除明细
                technicalChangeDetailRepository.deleteByChgReqNo(chgReqNo, updateBy);
            }
            String headerId = technicalChangeHeadDTO.getHeadId();
            //写技改明细
            insertTechnicalChangeDetail(pdmTechnicalDealForKafkaDTO.getNeedDealList(), headerId, pdmTechnicalDealForKafkaDTO.getUpdatedBy());
            //处理锁定单
            dealLockDetail(chgReqNo, updateBy, pdmTechnicalDealForKafkaDTO.getSendStatus());
            //技改明细有变动才发送邮件
            determineWhetherToMail(pdmTechnicalDealForKafkaDTO, technicalChangeDetailDTOList);
        }
    }

    /**
     * 明细有变动才发送邮件
     *
     * @param pdmTechnicalDealForKafkaDTO
     * @param technicalChangeDetailDTOList
     */
    private void determineWhetherToMail(PdmTechnicalDealForKafkaDTO pdmTechnicalDealForKafkaDTO, List<TechnicalChangeDetailDTO> technicalChangeDetailDTOList) {
        //技改单数据库已存在批次
        List<String> existProdPlanIdList = CollectionUtils.isEmpty(technicalChangeDetailDTOList) ? new ArrayList<>() : technicalChangeDetailDTOList.stream().map(e -> e.getProdplanId()).distinct().collect(Collectors.toList());
        //PDM技改单属于当前工厂批次
        List<String> nowProdPlanIdList = CollectionUtils.isEmpty(pdmTechnicalDealForKafkaDTO.getNeedDealList()) ? new ArrayList<>() : pdmTechnicalDealForKafkaDTO.getNeedDealList().stream().map(e -> e.getProdplanId()).distinct().collect(Collectors.toList());
        //新增批次
        List<String> needAddProdPlanIdList = nowProdPlanIdList.stream().filter(e -> !existProdPlanIdList.contains(e)).distinct().collect(Collectors.toList());
        //需删除批次
        List<String> needDeleteProdPlanIdList = existProdPlanIdList.stream().filter(e -> !nowProdPlanIdList.contains(e)).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(needAddProdPlanIdList) || CollectionUtils.isNotEmpty(needDeleteProdPlanIdList)) {
            sentMailWhenHasBeenIssued(pdmTechnicalDealForKafkaDTO);
        }
    }

    /**
     * 已发放发邮件
     *
     * @param pdmTechnicalDealForKafkaDTO
     */
    private void sentMailWhenHasBeenIssued(PdmTechnicalDealForKafkaDTO pdmTechnicalDealForKafkaDTO) {
        String content = StringUtils.EMPTY;
        try {
            content = commonTechnicalService.getContent(pdmTechnicalDealForKafkaDTO.getChgReqNo());
        } catch (Exception e) {
            content = e.getMessage();
        }
        if (StringUtils.isBlank(content)) {
            return;
        }
        List<PdmTechnicalChangeInfoEntityDTO> pdmTechnicalChangeInfoEntityDTOList = pdmTechnicalDealForKafkaDTO.getNeedDealList();
        String title = "";
        if (CollectionUtils.isNotEmpty(pdmTechnicalChangeInfoEntityDTOList)) {
            Integer size = pdmTechnicalChangeInfoEntityDTOList.size();
            if (pdmTechnicalChangeInfoEntityDTOList.size() > NumConstant.NUM_FIVE) {
                size = NumConstant.NUM_FIVE;
            }
            List<String> partTitleList = new ArrayList<>();
            for (int i = NumConstant.NUM_ZERO; i < size; i++) {
                PsTask psTask = pdmTechnicalChangeInfoEntityDTOList.get(i).getPsTask();
                String partTitle = String.format(Constant.BATCH_AND_BOARD_NAME, psTask == null ? "" : psTask.getItemName(), pdmTechnicalChangeInfoEntityDTOList.get(i).getProdplanId());
                partTitleList.add(partTitle);
            }
            if (pdmTechnicalChangeInfoEntityDTOList.size() > NumConstant.NUM_FIVE) {
                partTitleList.add(SEE_THE_TEXT_FOR_DETAILS);
            }
            String partTitle = StringUtils.join(partTitleList, COMMA);
            title = String.format(Constant.SEND_EMAIL_FOR_ISSUED, partTitle,pdmTechnicalDealForKafkaDTO.getChgReqNo(), pdmTechnicalDealForKafkaDTO.getUpdatedName() + pdmTechnicalDealForKafkaDTO.getUpdatedBy());
        }
        sendEmail(pdmTechnicalDealForKafkaDTO.getUpdatedBy(), content, title);
    }

    /** 
    * @Description: 
    * @Param: [pdmTechnicalDealForKafkaDTO]
    * @return: void
    * @Author: Saber[10307315]
    * @Date: 2023/4/21 上午10:47
    */
    private void sendMailForDeleteOrVoided(PdmTechnicalDealForKafkaDTO pdmTechnicalDealForKafkaDTO) {
        String content = STRING_EMPTY;
        try {
            content = commonTechnicalService.getContent(pdmTechnicalDealForKafkaDTO.getChgReqNo());
        } catch (Exception e) {
            content = e.getMessage();
        }
        if (StringUtils.isBlank(content)) {
            return;
        }
        //单据状态
        String sendStatus = pdmTechnicalDealForKafkaDTO.getSendStatus();
        List<PdmTechnicalChangeInfoEntityDTO> pdmTechnicalChangeInfoEntityDTOList = pdmTechnicalDealForKafkaDTO.getNeedDealList();
        String title = "";
        if (CollectionUtils.isNotEmpty(pdmTechnicalChangeInfoEntityDTOList)) {
            Integer size = pdmTechnicalChangeInfoEntityDTOList.size();
            if (pdmTechnicalChangeInfoEntityDTOList.size() > NumConstant.NUM_FIVE) {
                size = NumConstant.NUM_FIVE;
            }
            List<String> partTitleList = new ArrayList<>();
            for (int i = NumConstant.NUM_ZERO; i < size; i++) {
                PsTask psTask = pdmTechnicalChangeInfoEntityDTOList.get(i).getPsTask();
                String partTitle = String.format(Constant.BATCH_AND_BOARD_NAME,psTask == null ? "" : psTask.getItemName(),
                        pdmTechnicalChangeInfoEntityDTOList.get(i).getProdplanId());
                partTitleList.add(partTitle);
            }
            if (pdmTechnicalChangeInfoEntityDTOList.size() > NumConstant.NUM_FIVE) {
                partTitleList.add(SEE_THE_TEXT_FOR_DETAILS);
            }
            String partTitle = StringUtils.join(partTitleList, COMMA);
            String basicTitle = STRING_EMPTY;
            if (StringUtils.equals(sendStatus, Constant.PdmTechnicalChangeStatus.DELETED)) {
                basicTitle = Constant.DELETE_THE_TECHNICAL_TRANSFORMATION_SHEET;
            } else {
                basicTitle = Constant.VOID_THE_TECHNICAL_TRANSFORMATION_SHEET;
            }
            title = String.format(basicTitle,partTitle, pdmTechnicalDealForKafkaDTO.getChgReqNo(),
                    pdmTechnicalDealForKafkaDTO.getUpdatedName() + pdmTechnicalDealForKafkaDTO.getUpdatedBy());
        }
        sendEmail(pdmTechnicalDealForKafkaDTO.getUpdatedBy(), content, title);
    }

    /**
     * 设置邮件主送人 抄送人
     *
     * @param insertDTO
     * @throws Exception
     */
    private void setEmailSend(TechnicalChangeHeadDTO insertDTO) throws Exception {
        List<SysLookupTypesDTO> sysLookupTypesDTOList = BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_6726);
        if (CollectionUtils.isEmpty(sysLookupTypesDTOList)) {
            return;
        }
        List<String> mainSendList = sysLookupTypesDTOList.stream().filter(e -> StringUtils.equals(Constant.MAIN_SEND, e.getAttribute1())).map(e -> e.getLookupMeaning()).collect(Collectors.toList());
        List<String> mainSenderList = getSenderList(mainSendList);
        mainSenderList.add(insertDTO.getEmailSend());
        insertDTO.setEmailSend(StringUtils.join(mainSenderList, Constant.COMMA));

        List<String> ccSendList = sysLookupTypesDTOList.stream().filter(e -> StringUtils.equals(Constant.CC_SEND, e.getAttribute1())).map(e -> e.getLookupMeaning()).collect(Collectors.toList());
        List<String> ccSenderList = getSenderList(ccSendList);
        insertDTO.setEmailCopy(StringUtils.join(ccSenderList, Constant.COMMA));
    }

    /**
     * 获取发送邮件人员
     *
     * @param ccSendList
     */
    private List<String> getSenderList(List<String> ccSendList) {
        if (CollectionUtils.isNotEmpty(ccSendList)) {
            String[] senderArr = StringUtils.join(ccSendList, Constant.COMMA).split(Constant.COMMA);
            if (senderArr != null && senderArr.length > NumConstant.NUM_ZERO) {
                return Arrays.asList(senderArr).stream().distinct().collect(Collectors.toList());
            }
        }
        return new ArrayList<>();
    }

    /**
     * 处理锁定单
     *
     * @param chgReqNo
     * @param updateBy
     * @throws Exception
     */
    private void dealLockDetail(String chgReqNo, String updateBy, String sendStatus) throws Exception {
        BarcodeLockHead barcodeLockHead = barcodeLockHeadService.getHeadByBillNo(chgReqNo);
        if (barcodeLockHead != null && StringUtils.equals(barcodeLockHead.getStatus(), Constant.LOCKING)) {
            //解锁头表数据
            BarcodeUnlockDTO barcodeUnlockDTO = new BarcodeUnlockDTO();
            barcodeUnlockDTO.setUnlockUser(updateBy);
            barcodeUnlockDTO.setBillNo(chgReqNo);
            barcodeLockHeadRepository.unLock(barcodeUnlockDTO);
            List<BarcodeLockDetailEntityDTO> barcodeLockDetailEntityDTOList = barcodeLockDetailService.getListByBillNoAndStatus(chgReqNo, Constant.LOCKING);
            if (CollectionUtils.isNotEmpty(barcodeLockDetailEntityDTOList)) {
                //删除批次解锁
                List<String> needUnLockProdPlanIdList = barcodeLockDetailEntityDTOList.stream().map(e -> e.getBatchSn()).distinct().collect(Collectors.toList());
                unLockIsDeleteProdPlanId(chgReqNo, updateBy, sendStatus, needUnLockProdPlanIdList);
            }
        }
    }

    /**
     * 写技改明细
     *
     * @param pdmTechnicalChangeInfoEntityDTOList
     * @param headerId
     */
    private void insertTechnicalChangeDetail(List<PdmTechnicalChangeInfoEntityDTO> pdmTechnicalChangeInfoEntityDTOList, String headerId, String updateBy) throws Exception {
        if (CollectionUtils.isEmpty(pdmTechnicalChangeInfoEntityDTOList)) {
            return;
        }
        List<TechnicalChangeDetailDTO> insertDetailList = new ArrayList<>();
        for (PdmTechnicalChangeInfoEntityDTO pdmTechnicalChangeInfoEntityDTO : pdmTechnicalChangeInfoEntityDTOList) {
            TechnicalChangeDetailDTO technicalChangeDetailDTO = new TechnicalChangeDetailDTO();
            technicalChangeDetailDTO.setChgReqNo(pdmTechnicalChangeInfoEntityDTO.getChgReqNo());
            technicalChangeDetailDTO.setDetailId(UUID.randomUUID().toString());
            technicalChangeDetailDTO.setHeadId(headerId);
            technicalChangeDetailDTO.setTechnicalStatus(Constant.TechnicalChangeStatus.LOCKING);
            technicalChangeDetailDTO.setProdplanId(pdmTechnicalChangeInfoEntityDTO.getProdplanId());
            technicalChangeDetailDTO.setCreatedBy(updateBy);
            technicalChangeDetailDTO.setLastUpdatedBy(updateBy);
            PsTask psTask = pdmTechnicalChangeInfoEntityDTO.getPsTask();
            if (psTask != null) {
                technicalChangeDetailDTO.setProductName(psTask.getItemName());
                technicalChangeDetailDTO.setProductCode(psTask.getItemNo());
            }
            technicalChangeDetailDTO.setCompletionStatus(this.isTaskStatusCompleted(psTask) ? Constant.FLAG_Y : Constant.FLAG_N);
            List<CtRouteDetailDTO> routeDetailDTOList = pdmTechnicalChangeInfoEntityDTO.getCtRouteDetailDTOList();
            //设置技改管控主工序
            setCraftSection(technicalChangeDetailDTO, routeDetailDTOList, psTask);
            insertDetailList.add(technicalChangeDetailDTO);
        }
        if (CollectionUtils.isNotEmpty(insertDetailList)) {
            technicalChangeDetailRepository.batchInsert(insertDetailList);
        }
    }

    /**
     * 设置技改管控主工序
     *
     * @param technicalChangeDetailDTO
     * @param routeDetailDTOList
     */
    private void setCraftSection(TechnicalChangeDetailDTO technicalChangeDetailDTO, List<CtRouteDetailDTO> routeDetailDTOList, PsTask psTask) throws Exception {
        //已完工写入库，没工艺路径管控主工序写1004的全部主工序 有工艺路径按工艺路径写
        if (this.isTaskStatusCompleted(psTask)) {
            technicalChangeDetailDTO.setCraftSection(Constant.WAREHOUSE_ENTRY);
        } else if (CollectionUtils.isEmpty(routeDetailDTOList)) {
            Map<String, Object> map = new HashMap<>();
            map.put("lookupType", Constant.SYS_LOOK_CRAFTSECTION);
            List<SysLookupTypesDTO> list = BasicsettingRemoteService.getListByLookupType(map);
            String craftSection = CollectionUtils.isEmpty(list) ? STR_EMPTY : list.stream().filter(e -> StringUtils.isNotEmpty(e.getLookupMeaning())).map(e -> e.getLookupMeaning()).distinct().collect(Collectors.joining(COMMA));
            technicalChangeDetailDTO.setCraftSection(craftSection);
        } else {
            List<CtRouteDetailDTO> smtRoute = routeDetailDTOList.stream()
                    .filter(item -> item.getCraftSection().startsWith(SMT))
                    .collect(Collectors.toList());
            CtRouteDetailDTO removeEntity = new CtRouteDetailDTO();
            String removeSmt = "";
            if (smtRoute.size() > 0) {
                // 设置技改文件下发工序
                technicalChangeDetailDTO.setIssuanceCraftSection(SMT);
                if (smtRoute.size() > 1) {
                    smtRoute.sort(Comparator.comparing(CtRouteDetailDTO::getProcessSeq));
                    removeSmt = smtRoute.get(0).getCraftSection();
                }
            }
            removeEntity.setCraftSection(removeSmt);
            technicalChangeDetailDTO.setCraftSection(routeDetailDTOList.stream()
                    // 排除SMT首工序
                    .filter(e -> !StringUtils.equals(removeEntity.getCraftSection(), e.getCraftSection()))
                    .map(e -> e.getCraftSection()).distinct().collect(Collectors.joining(Constant.COMMA)));
        }
    }

    /**
     * 未发放
     *
     * @param pdmTechnicalDealForKafkaDTO
     * @throws Exception
     */
    private void dealForUnissued(PdmTechnicalDealForKafkaDTO pdmTechnicalDealForKafkaDTO) throws Exception {

        List<PdmTechnicalChangeInfoEntityDTO> needDealList = pdmTechnicalDealForKafkaDTO.getNeedDealList();
        String chgReqNo = pdmTechnicalDealForKafkaDTO.getChgReqNo();
        //单据状态
        String sendStatus = pdmTechnicalDealForKafkaDTO.getSendStatus();
        //最后更新人
        String updateBy = pdmTechnicalDealForKafkaDTO.getUpdatedBy();
        TechnicalChangeHeadDTO technicalChangeHeadDTO = technicalChangeHeadRepository.getTechnicalChangeHeadDTOByChgReqNo(chgReqNo);
        //存在非拟制中状态的技改单则不处理 拟制中的技改单这里不处理：等前端修改的时候处理
        if (technicalChangeHeadDTO != null && !StringUtils.equals(Constant.TechnicalChangeStatus.IN_PREPARATION, technicalChangeHeadDTO.getTechnicalStatus())) {
            return;
        }

        //查询锁定单
        BarcodeLockHead barcodeLockHead = barcodeLockHeadService.getHeadByBillNo(chgReqNo);
        //状态不为锁定中，已解锁(不存在锁定单)则新增锁定单据
        if (barcodeLockHead == null) {
            //新增锁定单
            insertLockHeadAndDetail(pdmTechnicalDealForKafkaDTO.getUpdatedBy(), needDealList);
            //发送邮件
            sendMailForUnissued(pdmTechnicalDealForKafkaDTO, chgReqNo, updateBy);
        }
        //状态为锁定中则更新锁定单明细：新增批次更新明细 删除批次解锁
        else if (StringUtils.equals(Constant.LOCKING, barcodeLockHead.getStatus())) {
            //这次需要处理的全部批次
            List<String> prodPlanIdList = CollectionUtils.isEmpty(needDealList) ? new ArrayList<>() : needDealList.stream().map(e -> e.getProdplanId()).distinct().collect(Collectors.toList());
            List<BarcodeLockDetailEntityDTO> list = getLockAndFinishUnlockList(chgReqNo);
            if (CollectionUtils.isNotEmpty(list)) {
                //锁定单明细已有批次
                List<String> existProdPlanIdList = list.stream().map(e -> e.getBatchSn()).distinct().collect(Collectors.toList());
                //需新增批次
                List<String> needInsertProdPlanIdList = prodPlanIdList.stream().filter(e -> !existProdPlanIdList.contains(e)).distinct().collect(Collectors.toList());
                //需解锁批次
                List<String> needUnLockProdPlanIdList = existProdPlanIdList.stream().filter(e -> !prodPlanIdList.contains(e)).distinct().collect(Collectors.toList());
                //完工的批次
                List<String> completedList = needDealList.stream().filter(e -> this.isTaskStatusCompleted(e.getPsTask())).map(e -> e.getProdplanId()).distinct().collect(Collectors.toList());
                //需要更新为完工解锁的锁定明细批次
                List<String> updateToFinishedBatchList = existProdPlanIdList.stream().filter(e -> completedList.contains(e)).distinct().collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(updateToFinishedBatchList)) {
                    barcodeLockDetailService.updateToFinished(updateToFinishedBatchList, chgReqNo);
                }
                //新增批次新增数据
                if (CollectionUtils.isNotEmpty(needInsertProdPlanIdList)) {
                    insertLockDetail(needDealList, needInsertProdPlanIdList);
                }
                //pdm已删除的批次进行解锁
                unLockIsDeleteProdPlanId(chgReqNo, updateBy, sendStatus, needUnLockProdPlanIdList);
                //不存在任务信息或者工艺路径的批次(不包括任务已完工的)
                List<String> errorProdplanIdList = needDealList.stream().filter(e -> StringUtils.isNotEmpty(e.getErrorMsg())).map(e -> e.getProdplanId()).distinct().collect(Collectors.toList());
                List<String> finalNeedInsertProdPlanIdList = needInsertProdPlanIdList.stream().filter(e -> !errorProdplanIdList.contains(e)).distinct().collect(Collectors.toList());
                //发送邮件
                sendMailForUnissued(pdmTechnicalDealForKafkaDTO, finalNeedInsertProdPlanIdList, needUnLockProdPlanIdList);

            } else {
                //所有批次新增数据
                List<BarcodeLockDetailEntityDTO> barcodeLockDetailEntityDTOList  = insertLockDetail(needDealList, prodPlanIdList);
                //发送邮件
                this.sendMailForUnissued(pdmTechnicalDealForKafkaDTO, chgReqNo, updateBy, barcodeLockDetailEntityDTOList);
            }
        }
    }

    /**
     * 需要新增批次锁定明细才发送邮件
     * @param pdmTechnicalDealForKafkaDTO
     * @param chgReqNo
     * @param updateBy
     * @param barcodeLockDetailEntityDTOList
     */
    public void sendMailForUnissued(PdmTechnicalDealForKafkaDTO pdmTechnicalDealForKafkaDTO, String chgReqNo, String updateBy, List<BarcodeLockDetailEntityDTO> barcodeLockDetailEntityDTOList) {
        if(CollectionUtils.isNotEmpty(barcodeLockDetailEntityDTOList)) {
            sendMailForUnissued(pdmTechnicalDealForKafkaDTO, chgReqNo, updateBy);
        }
    }

    /**
     * 获取完工解锁和锁定中的数据
     *
     * @param chgReqNo
     * @return
     * @throws Exception
     */
    private List<BarcodeLockDetailEntityDTO> getLockAndFinishUnlockList(String chgReqNo) throws Exception {
        List<BarcodeLockDetailEntityDTO> list = new ArrayList<>();
        //现有锁定单状态为锁定中的明细
        List<BarcodeLockDetailEntityDTO> lockingList = barcodeLockDetailService.getListByBillNoAndStatus(chgReqNo, Constant.LOCKING);
        if (!CollectionUtils.isEmpty(lockingList)) {
            list.addAll(lockingList);
        }
        //现有锁定单状态为完工解锁中的明细
        List<BarcodeLockDetailEntityDTO> completionUnlockingList = barcodeLockDetailService.getListByBillNoAndStatus(chgReqNo, Constant.COMPLETION_UNLOCKING);
        if (!CollectionUtils.isEmpty(completionUnlockingList)) {
            list.addAll(completionUnlockingList);
        }
        return list;
    }

    /**
     * 未发放发送邮件
     *
     * @param pdmTechnicalDealForKafkaDTO
     * @param finalNeedInsertProdPlanIdList
     * @param needUnLockProdPlanIdList
     */
    private void sendMailForUnissued(PdmTechnicalDealForKafkaDTO pdmTechnicalDealForKafkaDTO, List<String> finalNeedInsertProdPlanIdList, List<String> needUnLockProdPlanIdList) {
        if (CollectionUtils.isNotEmpty(finalNeedInsertProdPlanIdList) || CollectionUtils.isNotEmpty(needUnLockProdPlanIdList)) {
            undistributedMail(pdmTechnicalDealForKafkaDTO, pdmTechnicalDealForKafkaDTO.getNeedDealList(), pdmTechnicalDealForKafkaDTO.getChgReqNo(), pdmTechnicalDealForKafkaDTO.getUpdatedBy());
        }
    }

    /**
     * 未发放发送邮件
     *
     * @param pdmTechnicalDealForKafkaDTO
     * @param chgReqNo
     * @param updateBy
     */
    private void sendMailForUnissued(PdmTechnicalDealForKafkaDTO pdmTechnicalDealForKafkaDTO, String chgReqNo, String updateBy) {
        if (CollectionUtils.isNotEmpty(pdmTechnicalDealForKafkaDTO.getNeedDealList())) {
            undistributedMail(pdmTechnicalDealForKafkaDTO, pdmTechnicalDealForKafkaDTO.getNeedDealList(), chgReqNo, updateBy);
        }
    }

    /**
     * 未发放发送邮件
     *
     * @param pdmTechnicalDealForKafkaDTO
     * @param needDealList
     * @param chgReqNo
     * @param updateBy
     */
    private void undistributedMail(PdmTechnicalDealForKafkaDTO pdmTechnicalDealForKafkaDTO, List<PdmTechnicalChangeInfoEntityDTO> needDealList, String chgReqNo, String updateBy) {
        if (CollectionUtils.isEmpty(needDealList)) {
            return;
        }
        Integer size = needDealList.size();
        if (needDealList.size() > NumConstant.NUM_FIVE) {
            size = NumConstant.NUM_FIVE;
        }
        List<String> partTitleList = new ArrayList<>();
        for (int i = NumConstant.NUM_ZERO; i < size; i++) {
            PsTask psTask = needDealList.get(i).getPsTask();
            String partTitle = String.format(Constant.BATCH_AND_BOARD_NAME, psTask == null ? "" : psTask.getItemName(),needDealList.get(i).getProdplanId());
            partTitleList.add(partTitle);
        }
        if (needDealList.size() > NumConstant.NUM_FIVE) {
            partTitleList.add(SEE_THE_TEXT_FOR_DETAILS);
        }
        String partTitle = StringUtils.join(partTitleList, SEMICOLON);
        String title = String.format(Constant.TECHNICAL_TRANSFORMATION_SHEET_HAS_BEEN_SYNCHRONIZED, partTitle,chgReqNo, pdmTechnicalDealForKafkaDTO.getUpdatedName() + pdmTechnicalDealForKafkaDTO.getUpdatedBy());
        String contentCN = this.getContentForUnissued(needDealList);
        sendEmail(updateBy, contentCN, title);
    }

    /**
     * 未发放技改邮件内容
     *
     * @param pdmTechnicalChangeInfoEntityDTOList
     * @return
     */
    private String getContentForUnissued(List<PdmTechnicalChangeInfoEntityDTO> pdmTechnicalChangeInfoEntityDTOList) {
        if (CollectionUtils.isEmpty(pdmTechnicalChangeInfoEntityDTOList)) {
            return "";
        }
        // 查看是否需要错误信息。
        List<PdmTechnicalChangeInfoEntityDTO> errorTechList = new ArrayList<>();
        for (PdmTechnicalChangeInfoEntityDTO entity : pdmTechnicalChangeInfoEntityDTOList) {
            if (StringUtils.isNotEmpty(entity.getErrorMsg())) {
                errorTechList.add(entity);
            }
        }
        StringBuilder stringBuilderHtml = new StringBuilder("<br/><div>");
        stringBuilderHtml.append("<p style='margin:0;font-size:13pt'>" + TECH_CHG_CONTENT + "</p>");
        stringBuilderHtml.append("<table style='border:1px solid black;width:100%;border-collapse:collapse;background-color:white;font-size:9pt;'cellspacing=0 cellpadding=3>");
        stringBuilderHtml.append("<tr style ='color:white;font-weight:bold;background-color:rgb(0,102,153);font-weight:bold'>");
        stringBuilderHtml.append("<td>" + INDEX + "</td>");
        stringBuilderHtml.append("<td>" + BARCODE_UNLOCK_BATCH + "</td>");
        stringBuilderHtml.append("<td>" + BARCODE_ITEM_NO + "</td>");
        stringBuilderHtml.append("</tr>");
        // 技改详细信息
        for (int i = NumConstant.NUM_ZERO; i < pdmTechnicalChangeInfoEntityDTOList.size(); i++) {
            stringBuilderHtml.append("<tr style='border:1px solid black;'>");
            stringBuilderHtml.append("<td style='border:1px solid black;'>" + (i + INT_1) + "</td>");
            stringBuilderHtml.append("<td style='border:1px solid black;'>" + CommonUtils.getStrTransNull(pdmTechnicalChangeInfoEntityDTOList.get(i).getProdplanId()) + "</td>");
            PsTask psTask = pdmTechnicalChangeInfoEntityDTOList.get(i).getPsTask();
            stringBuilderHtml.append("<td style='border:1px solid black;'>" + CommonUtils.getStrTransNull(psTask == null ? "" : psTask.getItemName()) + "</td>");
            stringBuilderHtml.append("</tr>");
        }
        stringBuilderHtml.append("</table>");
        if(errorTechList.size() > 0) {
            stringBuilderHtml.append("<p style='margin:0;font-size:13pt'>" + TECH_CHG_ERROR_PROD + "</p>");
            stringBuilderHtml.append("<table style='border:1px solid black;width:100%;border-collapse:collapse;background-color:white;font-size:9pt;'cellspacing=0 cellpadding=3>");
            stringBuilderHtml.append("<tr style ='color:white;font-weight:bold;background-color:rgb(0,102,153);font-weight:bold'>");
            stringBuilderHtml.append("<td>" + INDEX + "</td>");
            stringBuilderHtml.append("<td>" + BARCODE_UNLOCK_BATCH + "</td>");
            stringBuilderHtml.append("<td>" + ERROR_MSG + "</td>");
            stringBuilderHtml.append("</tr>");
            // 技改错误信息
            for (int i = NumConstant.NUM_ZERO; i < errorTechList.size(); i++) {
                stringBuilderHtml.append("<tr style='border:1px solid black;'>");
                stringBuilderHtml.append("<td style='border:1px solid black;'>" + (i + INT_1) + "</td>");
                stringBuilderHtml.append("<td style='border:1px solid black;'>" + CommonUtils.getStrTransNull(errorTechList.get(i).getProdplanId()) + "</td>");
                stringBuilderHtml.append("<td style='border:1px solid black;'>" + CommonUtils.getStrTransNull(errorTechList.get(i).getErrorMsg()) + "</td>");
                stringBuilderHtml.append("</tr>");
            }
            stringBuilderHtml.append("</table>");
        }
        stringBuilderHtml.append("<br/></div>");
        return stringBuilderHtml.toString();
    }


    /**
     * 新增锁定单
     *
     * @param updateBy
     * @param pdmTechnicalChangeInfoEntityDTOList
     * @throws Exception
     */
    private void insertLockHeadAndDetail(String updateBy, List<PdmTechnicalChangeInfoEntityDTO> pdmTechnicalChangeInfoEntityDTOList) throws Exception {
        if (CollectionUtils.isEmpty(pdmTechnicalChangeInfoEntityDTOList)) {
            return;
        }
        BarcodeLockHeadEntityDTO barcodeLockHeadEntityDTO = new BarcodeLockHeadEntityDTO();
        barcodeLockHeadEntityDTO.setStatus(Constant.LOCKING);
        barcodeLockHeadEntityDTO.setBillNo(pdmTechnicalChangeInfoEntityDTOList.get(NumConstant.NUM_ZERO).getChgReqNo());
        barcodeLockHeadEntityDTO.setOperationMode(NumConstant.STR_TWO);
        barcodeLockHeadEntityDTO.setOperationType(Constant.SUBMIT);
        barcodeLockHeadEntityDTO.setType(Constant.LOCK_TYPE_BATCH);
        barcodeLockHeadEntityDTO.setSchedulingLockFlag(Constant.FLAG_N);
        barcodeLockHeadEntityDTO.setSourceSys(Constant.STR_NUMBER_ONE);
        //获取抄送人
        List<String> ccList = getCcList();
        this.setCcList(ccList, barcodeLockHeadEntityDTO);
        //设置工厂id
        setFactoryIdAndEntityId(pdmTechnicalChangeInfoEntityDTOList, barcodeLockHeadEntityDTO);
        String reason = String.format(Constant.LOCK_REASON, pdmTechnicalChangeInfoEntityDTOList.get(NumConstant.NUM_ZERO).getChgReqNo(), pdmTechnicalChangeInfoEntityDTOList.get(NumConstant.NUM_ZERO).getUpdatedBy());
        barcodeLockHeadEntityDTO.setReason(reason);
        barcodeLockHeadEntityDTO.setCreateBy(updateBy);
        barcodeLockHeadEntityDTO.setLastUpdatedBy(updateBy);

        //保存头
        List<BarcodeLockHeadEntityDTO> insertList = new ArrayList<>();
        barcodeLockHeadEntityDTO.setId(UUID.randomUUID().toString());
        insertList.add(barcodeLockHeadEntityDTO);
        barcodeLockHeadRepository.batchInsert(insertList);
        //生成锁定单明细
        List<BarcodeLockDetailEntityDTO> barcodeLockDetailEntityDTOList = new ArrayList<>();
        for (PdmTechnicalChangeInfoEntityDTO pdmTechnicalChangeInfoEntityDTO : pdmTechnicalChangeInfoEntityDTOList) {
            generateBarcodeLockDetailEntityDTO(updateBy, barcodeLockHeadEntityDTO, barcodeLockDetailEntityDTOList, pdmTechnicalChangeInfoEntityDTO);
        }
        barcodeLockBatchInsert(barcodeLockDetailEntityDTOList);
    }

    private void barcodeLockBatchInsert(List<BarcodeLockDetailEntityDTO> barcodeLockDetailEntityDTOList) throws Exception {
        if (CollectionUtils.isNotEmpty(barcodeLockDetailEntityDTOList)) {
            barcodeLockDetailService.batchInsert(barcodeLockDetailEntityDTOList);
        }
    }

    /**
     * 生成锁定明细
     *
     * @param updateBy
     * @param barcodeLockHeadEntityDTO
     * @param barcodeLockDetailEntityDTOList
     * @param pdmTechnicalChangeInfoEntityDTO
     */
    private void generateBarcodeLockDetailEntityDTO(String updateBy, BarcodeLockHeadEntityDTO barcodeLockHeadEntityDTO,
                                                    List<BarcodeLockDetailEntityDTO> barcodeLockDetailEntityDTOList,
                                                    PdmTechnicalChangeInfoEntityDTO pdmTechnicalChangeInfoEntityDTO) {
        PsTask psTask = pdmTechnicalChangeInfoEntityDTO.getPsTask();
        List<CtRouteDetailDTO> ctRouteDetailDTOList = pdmTechnicalChangeInfoEntityDTO.getCtRouteDetailDTOList();
        //已完工的没工艺路径的写入库
        if (CollectionUtils.isEmpty(ctRouteDetailDTOList) && this.isTaskStatusCompleted(psTask)) {
            BarcodeLockDetailEntityDTO barcodeLockDetailEntityDTO = getLockDetailEntityDTO(updateBy, barcodeLockHeadEntityDTO, pdmTechnicalChangeInfoEntityDTO, PROCESS_CODE_WH);
            barcodeLockDetailEntityDTO.setStatus(Constant.COMPLETION_UNLOCKING);
            barcodeLockDetailEntityDTOList.add(barcodeLockDetailEntityDTO);
            return;
        }
        if (CollectionUtils.isEmpty(ctRouteDetailDTOList)) {
            String errorMsg = StringUtils.isEmpty(pdmTechnicalChangeInfoEntityDTO.getErrorMsg())
                    ? Constant.PROD_NOT_HAVE_CRAFT_INFO : pdmTechnicalChangeInfoEntityDTO.getErrorMsg();
            pdmTechnicalChangeInfoEntityDTO.setErrorMsg(errorMsg);
            return;
        }
        //任务状态已完工的写到明细,状态为完工解锁，其他为锁定中
        for (CtRouteDetailDTO ctRouteDetailDTO : ctRouteDetailDTOList) {
            if (StringUtils.equals(ctRouteDetailDTO.getCraftSection(), Constant.WAREHOUSE_ENTRY)) {
                continue;
            }
            BarcodeLockDetailEntityDTO barcodeLockDetailEntityDTO = getLockDetailEntityDTO(updateBy, barcodeLockHeadEntityDTO, pdmTechnicalChangeInfoEntityDTO, ctRouteDetailDTO.getNextProcess());
            setStatus(psTask, barcodeLockDetailEntityDTO);
            barcodeLockDetailEntityDTOList.add(barcodeLockDetailEntityDTO);
        }
    }

    private void setStatus(PsTask psTask, BarcodeLockDetailEntityDTO barcodeLockDetailEntityDTO) {
        if (isTaskStatusCompleted(psTask)) {
            barcodeLockDetailEntityDTO.setStatus(Constant.COMPLETION_UNLOCKING);
        }
    }

    /**
     * 组装锁定明细
     *
     * @param updateBy
     * @param barcodeLockHeadEntityDTO
     * @param pdmTechnicalChangeInfoEntityDTO
     * @param lockProcessCode
     * @return
     */
    private BarcodeLockDetailEntityDTO getLockDetailEntityDTO(String updateBy, BarcodeLockHeadEntityDTO barcodeLockHeadEntityDTO,
                                                              PdmTechnicalChangeInfoEntityDTO pdmTechnicalChangeInfoEntityDTO, String lockProcessCode) {
        BarcodeLockDetailEntityDTO barcodeLockDetailEntityDTO = new BarcodeLockDetailEntityDTO();
        barcodeLockDetailEntityDTO.setLockProcessCode(lockProcessCode);
        barcodeLockDetailEntityDTO.setId(UUID.randomUUID().toString());
        barcodeLockDetailEntityDTO.setType(barcodeLockHeadEntityDTO.getType());
        barcodeLockDetailEntityDTO.setBatchSn(pdmTechnicalChangeInfoEntityDTO.getProdplanId());
        barcodeLockDetailEntityDTO.setStatus(Constant.LOCKING);
        barcodeLockDetailEntityDTO.setBillNo(barcodeLockHeadEntityDTO.getBillNo());
        barcodeLockDetailEntityDTO.setCreateBy(updateBy);
        barcodeLockDetailEntityDTO.setLastUpdatedBy(updateBy);
        barcodeLockDetailEntityDTO.setFactoryId(barcodeLockHeadEntityDTO.getFactoryId());
        barcodeLockDetailEntityDTO.setEntityId(barcodeLockHeadEntityDTO.getEntityId());
        return barcodeLockDetailEntityDTO;
    }

    /**
     * 设置工厂id
     *
     * @param pdmTechnicalChangeInfoEntityDTOList
     * @param barcodeLockHeadEntityDTO
     */
    private void setFactoryIdAndEntityId(List<PdmTechnicalChangeInfoEntityDTO> pdmTechnicalChangeInfoEntityDTOList, BarcodeLockHeadEntityDTO barcodeLockHeadEntityDTO) {
        String factoryId = pdmTechnicalChangeInfoEntityDTOList.get(NumConstant.NUM_ZERO).getFactoryId();
        if (StringUtils.isNotEmpty(factoryId)) {
            barcodeLockHeadEntityDTO.setFactoryId(Integer.parseInt(factoryId));
        }
        String entityId = factoryConfig.getCommonEntityId();
        if (StringUtils.isNotEmpty(entityId)) {
            barcodeLockHeadEntityDTO.setEntityId(Integer.parseInt(entityId));
        }
    }

    /**
     * 批次解锁
     *
     * @param chgReqNo
     * @param updateBy
     * @param prodPlanIdList
     */
    private void unLockIsDeleteProdPlanId(String chgReqNo, String updateBy, String sendStatus, List<String> prodPlanIdList) throws Exception {
        if (CollectionUtils.isEmpty(prodPlanIdList)) {
            return;
        }
        BarcodeUnlockDTO barcodeUnlockDTO = new BarcodeUnlockDTO();
        barcodeUnlockDTO.setBillNo(chgReqNo);
        barcodeUnlockDTO.setProdPlanIdList(prodPlanIdList);
        barcodeUnlockDTO.setUnlockUser(updateBy);
        String updateName = this.getUpdateByName(updateBy);
        if (StringUtils.equals(sendStatus, Constant.PdmTechnicalChangeStatus.ISSUED)) {
            barcodeUnlockDTO.setUnlockReason(String.format(Constant.UNLOCK_REASON_FOR_VOIDED, chgReqNo, updateName + updateBy));
        } else {
            barcodeUnlockDTO.setUnlockReason(String.format(Constant.UNLOCK_REASON, chgReqNo, updateName + updateBy));
        }
        barcodeUnlockService.unlockBatchCraftByProdPlanIds(barcodeUnlockDTO);
    }

    /**
     * 新增锁定明细
     *
     * @param pdmTechnicalChangeInfoEntityDTOList
     * @param needInsertProdPlanIdList
     * @throws Exception
     */
    private  List<BarcodeLockDetailEntityDTO> insertLockDetail(List<PdmTechnicalChangeInfoEntityDTO> pdmTechnicalChangeInfoEntityDTOList, List<String> needInsertProdPlanIdList) throws Exception {
        if (CollectionUtils.isEmpty(pdmTechnicalChangeInfoEntityDTOList)) {
            return null;
        }
        //生成锁定单明细
        List<BarcodeLockDetailEntityDTO> barcodeLockDetailEntityDTOList = new ArrayList<>();
        for (PdmTechnicalChangeInfoEntityDTO pdmTechnicalChangeInfoEntityDTO : pdmTechnicalChangeInfoEntityDTOList) {
            if (!needInsertProdPlanIdList.contains(pdmTechnicalChangeInfoEntityDTO.getProdplanId())) {
                continue;
            }
            PsTask psTask = pdmTechnicalChangeInfoEntityDTO.getPsTask();
            List<CtRouteDetailDTO> ctRouteDetailDTOList = pdmTechnicalChangeInfoEntityDTO.getCtRouteDetailDTOList();
            //已完工的没工艺路径的子工序写入库 状态为完工解锁
            if (CollectionUtils.isEmpty(ctRouteDetailDTOList) && this.isTaskStatusCompleted(psTask)) {
                BarcodeLockDetailEntityDTO barcodeLockDetailEntityDTO = generateLockDetails(pdmTechnicalChangeInfoEntityDTO, PROCESS_CODE_WH);
                setStatus(psTask, barcodeLockDetailEntityDTO);
                barcodeLockDetailEntityDTOList.add(barcodeLockDetailEntityDTO);
                continue;
            }
            if (CollectionUtils.isEmpty(ctRouteDetailDTOList)) {
                String errorMsg = StringUtils.isEmpty(pdmTechnicalChangeInfoEntityDTO.getErrorMsg())
                        ? Constant.PROD_NOT_HAVE_CRAFT_INFO : pdmTechnicalChangeInfoEntityDTO.getErrorMsg();
                pdmTechnicalChangeInfoEntityDTO.setErrorMsg(errorMsg);
                continue;
            }
            generateLockDetails(barcodeLockDetailEntityDTOList, pdmTechnicalChangeInfoEntityDTO, ctRouteDetailDTOList);
        }
        barcodeLockBatchInsert(barcodeLockDetailEntityDTOList);
        return barcodeLockDetailEntityDTOList;
    }

    /**
     * 判断任务是否已完工
     *
     * @param psTask
     * @return
     */
    private boolean isTaskStatusCompleted(PsTask psTask) {
        return psTask != null && StringUtils.equals(Constant.FINISH_WORK, psTask.getTaskStatus());
    }

    /**
     * 生成锁定单明细
     *
     * @param barcodeLockDetailEntityDTOList
     * @param pdmTechnicalChangeInfoEntityDTO
     * @param ctRouteDetailDTOList
     */
    private void generateLockDetails(List<BarcodeLockDetailEntityDTO> barcodeLockDetailEntityDTOList,
                                     PdmTechnicalChangeInfoEntityDTO pdmTechnicalChangeInfoEntityDTO, List<CtRouteDetailDTO> ctRouteDetailDTOList) throws Exception {
        for (CtRouteDetailDTO ctRouteDetailDTO : ctRouteDetailDTOList) {
            BarcodeLockDetailEntityDTO barcodeLockDetailEntityDTO = generateLockDetails(pdmTechnicalChangeInfoEntityDTO, ctRouteDetailDTO.getNextProcess());
            //任务状态已完工的写完工解锁
            PsTask psTask = pdmTechnicalChangeInfoEntityDTO.getPsTask();
            setStatus(psTask, barcodeLockDetailEntityDTO);
            barcodeLockDetailEntityDTOList.add(barcodeLockDetailEntityDTO);
        }
    }

    /**
     * 生成锁定明细
     *
     * @param pdmTechnicalChangeInfoEntityDTO
     * @param lockProcessCode
     * @return
     */
    private BarcodeLockDetailEntityDTO generateLockDetails(PdmTechnicalChangeInfoEntityDTO pdmTechnicalChangeInfoEntityDTO, String lockProcessCode) {
        BarcodeLockDetailEntityDTO barcodeLockDetailEntityDTO = new BarcodeLockDetailEntityDTO();
        barcodeLockDetailEntityDTO.setLockProcessCode(lockProcessCode);
        barcodeLockDetailEntityDTO.setId(UUID.randomUUID().toString());
        barcodeLockDetailEntityDTO.setType(Constant.LOCK_TYPE_BATCH);
        barcodeLockDetailEntityDTO.setBatchSn(pdmTechnicalChangeInfoEntityDTO.getProdplanId());
        barcodeLockDetailEntityDTO.setStatus(Constant.LOCKING);
        barcodeLockDetailEntityDTO.setBillNo(pdmTechnicalChangeInfoEntityDTO.getChgReqNo());
        barcodeLockDetailEntityDTO.setCreateBy(pdmTechnicalChangeInfoEntityDTO.getUpdatedBy());
        barcodeLockDetailEntityDTO.setLastUpdatedBy(pdmTechnicalChangeInfoEntityDTO.getUpdatedBy());
        String factoryId = pdmTechnicalChangeInfoEntityDTO.getFactoryId();
        if (StringUtils.isNotEmpty(factoryId)) {
            barcodeLockDetailEntityDTO.setFactoryId(Integer.parseInt(factoryId));
        }
        String entityId = factoryConfig.getCommonEntityId();
        if (StringUtils.isNotEmpty(entityId)) {
            barcodeLockDetailEntityDTO.setEntityId(Integer.parseInt(entityId));
        }
        return barcodeLockDetailEntityDTO;
    }

    private List<String> getCcList() throws Exception {
        List<SysLookupTypesDTO> sysLookupTypesDTOList = BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_6726);
        List<String> ccList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(sysLookupTypesDTOList)) {
            List<String> mainSendList = sysLookupTypesDTOList.stream().filter(e -> StringUtils.equals(Constant.MAIN_SEND, e.getAttribute1())).map(e -> e.getLookupMeaning()).collect(Collectors.toList());
            List<String> ccSendList = sysLookupTypesDTOList.stream().filter(e -> StringUtils.equals(Constant.CC_SEND, e.getAttribute1())).map(e -> e.getLookupMeaning()).collect(Collectors.toList());
            ccList.addAll(getSenderList(mainSendList));
            ccList.addAll(getSenderList(ccSendList));
        }
        return ccList;
    }

    /**
     * 设置抄送人
     *
     * @param ccList
     * @param barcodeLockHeadEntityDTO
     */
    private void setCcList(List<String> ccList, BarcodeLockHeadEntityDTO barcodeLockHeadEntityDTO) {
        if (CollectionUtils.isEmpty(ccList)) {
            return;
        }
        ccList = ccList.stream().distinct().collect(Collectors.toList());
        if (ccList.size() > NumConstant.NUM_200) {
            List<String> tempList = new ArrayList<>();
            for (int i = 0; i < NumConstant.NUM_200; i++) {
                tempList.add(ccList.get(i));
            }
            barcodeLockHeadEntityDTO.setCcList(StringUtils.join(tempList, Constant.COMMA));
        } else {
            barcodeLockHeadEntityDTO.setCcList(StringUtils.join(ccList, Constant.COMMA));
        }
    }

    /**
     * 分页查询技改信息头表
     *
     * @param pageParams 分页参数
     * @return 分页结果
     */
    @Override
    public Page<TechnicalChangeHeadDTO> queryTechnicalHeadByPage(Page<TechnicalChangeHeadDTO> pageParams) {
        this.transSnToSnList(pageParams, null);
        List<TechnicalChangeHeadDTO> list = technicalChangeHeadRepository.queryTechnicalHeadByPage(pageParams);
        if (CollectionUtils.isEmpty(list)) {
            list = new ArrayList<>();
        }
        // 3. 转换人员工号信息
        this.coverEmpInfo(list);
        // 4. 转换状态描述
        this.setTechnicalStatusDesc(list);
        pageParams.setRows(list);
        return pageParams;
    }

    /**
     * 根据批次获取技改头行信息
     * @param prodplanId
     * @return
     */
    @Override
    public List<TechnicalChangeHeadDTO> queryTechnicalInfoByProdPlanId(String prodplanId) {
        return technicalChangeHeadRepository.queryTechnicalInfoByProdPlanId(prodplanId);
    }

    /**
     * 迁移技改以及锁定信息
     * @param technicalAndLockInfoDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void moveTechnicalAndLockInfoByProdplanId(TechnicalAndLockInfoDTO technicalAndLockInfoDTO) throws Exception {
        if (technicalAndLockInfoDTO == null) {
            return;
        }
        //处理技改单
        this.dealTechnicalModificationForm(technicalAndLockInfoDTO);
        //处理锁定单
        this.dealLockOrders(technicalAndLockInfoDTO);

    }

    /**
     * 处理锁定单
     * @param technicalAndLockInfoDTO
     * @throws Exception
     */
    public void dealLockOrders(TechnicalAndLockInfoDTO technicalAndLockInfoDTO) throws Exception {
        String toFactoryId = technicalAndLockInfoDTO.getToFactoryId();
        String formFactoryName = technicalAndLockInfoDTO.getFormFactoryName();
        String reason = String.format(Constant.LOCK_REASON_ADD,formFactoryName);
        String empNo = technicalAndLockInfoDTO.getEmpNo();
        List<BarcodeLockHeadEntityDTO> sourceBarcodeLockHeadEntityDTOList = technicalAndLockInfoDTO.getBarcodeLockHeadEntityDTOList();
        if (CollectionUtils.isEmpty(sourceBarcodeLockHeadEntityDTOList)) {
            return;
        }
        sourceBarcodeLockHeadEntityDTOList.forEach(p->{
            if(StringUtils.isNotEmpty(toFactoryId)){
                p.setFactoryId(Integer.parseInt(toFactoryId));
            }
        });
        List<String> technicalLockInBillNoList = sourceBarcodeLockHeadEntityDTOList.stream().map(e->e.getBillNo()).distinct().collect(Collectors.toList());

        //目的工厂无锁定单 在目的工厂新建该单号的锁定单，且写入该批次，状态为“锁定中”。锁定原因也同样迁移，并在原因后加上“从源工厂（具体哪个工厂）迁移”
        List<BarcodeLockHeadEntityDTO> barcodeLockHeadEntityDTOS = barcodeLockHeadService.getHeadInfoByBillNoList(technicalLockInBillNoList);
        if(CollectionUtils.isEmpty(barcodeLockHeadEntityDTOS)){
            this.batchInsert(reason,toFactoryId, sourceBarcodeLockHeadEntityDTOList);
            return;
        }
        List<String> existBillNoList = barcodeLockHeadEntityDTOS.stream().filter(e->StringUtils.isNotEmpty(e.getBillNo())).map(e->e.getBillNo()).distinct().collect(Collectors.toList());
        //目的工厂无锁定单的直接写入
        List<BarcodeLockHeadEntityDTO> destinationFactoryHasNoLockOrderList = sourceBarcodeLockHeadEntityDTOList.stream().filter(e-> !existBillNoList.contains(e.getBillNo())).collect(Collectors.toList());
        this.batchInsert(reason,toFactoryId, destinationFactoryHasNoLockOrderList);
        //存在锁定单的，如果锁定单是锁定中，直接新增锁定明细,如果是已解锁，看单据有没有技改单，存在技改单的讲技改单更新为锁定中，新增技改明细，不存在技改单的，更新锁定单位锁定中，新增锁定明细
        List<BarcodeLockHeadEntityDTO> existLockList = sourceBarcodeLockHeadEntityDTOList.stream().filter(e-> existBillNoList.contains(e.getBillNo())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(existLockList)){
            return;
        }
        List<String> lockBillNoList = barcodeLockHeadEntityDTOS.stream().filter(e->StringUtils.equals(e.getStatus(), LOCKING)).map(e->e.getBillNo()).collect(Collectors.toList());
        //锁定中锁定单的锁定单 头不用更新 新增明细
        List<BarcodeLockHeadEntityDTO> lockingList = existLockList.stream().filter(e->lockBillNoList.contains(e.getBillNo())).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(lockingList)){
            List<BarcodeLockDetailEntityDTO> detailEntityDTOS = this.getBarcodeLockDetailEntityDTOS(toFactoryId, lockingList);
            barcodeLockDetailService.batchInsertOrUpdate(detailEntityDTOS);
        }
        //已解锁的锁定单
        List<String> unLockBillNoList = barcodeLockHeadEntityDTOS.stream().filter(e->StringUtils.equals(e.getStatus(), UN_LOCK)).map(e->e.getBillNo()).collect(Collectors.toList());
        List<BarcodeLockHeadEntityDTO> unLockList = existLockList.stream().filter(e->unLockBillNoList.contains(e.getBillNo())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(unLockList)){
            return;
        }
        this.dealUnLockBarcodeList(toFactoryId,unLockList,empNo,reason);
    }

    /**
     * 处理已解锁的锁定单 正常情况下存在技改单 不存在则不做处理
     * @param unLockList
     */
    public void dealUnLockBarcodeList(String factoryId,List<BarcodeLockHeadEntityDTO> unLockList,String empNo,String reason) throws Exception {
        if(CollectionUtils.isEmpty(unLockList)){
            return;
        }
        List<String> unLockBillNoList = unLockList.stream().filter(e->StringUtils.isNotEmpty(e.getBillNo())).map(e->e.getBillNo()).distinct().collect(Collectors.toList());
        List<TechnicalChangeHeadDTO>  technicalChangeHeadDTOList = technicalChangeHeadRepository.getListByChgNoSet(new HashSet<>(unLockBillNoList));
        if(CollectionUtils.isEmpty(technicalChangeHeadDTOList)){
            iMESLogService.log(Constant.CANCEL_DISTRIBUTION_LOG_NO_TECHNICAL_MODIFICATION_ORDER,JSON.toJSONString(unLockList));
            return;
        }
        //不存在技改单的锁定单  不处理
        List<String> existTechnicalBillNoList = technicalChangeHeadDTOList.stream().filter(e->StringUtils.isNotEmpty(e.getChgReqNo())).map(e->e.getChgReqNo()).distinct().collect(Collectors.toList());

        //存在技改单的更新技改头状态为锁定中，写技改明细
        List<BarcodeLockHeadEntityDTO> existTechnicalList = unLockList.stream().filter(e-> existTechnicalBillNoList.contains(e.getBillNo())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(existTechnicalList)){
            return;
        }
        technicalChangeHeadRepository.batchUpdateStatus(existTechnicalBillNoList, STR_2,empNo);

        Map<String, TechnicalChangeHeadDTO> technicalChangeHeadDTOMap = technicalChangeHeadDTOList.stream().collect
                (Collectors.toMap(k -> k.getChgReqNo(), v -> v, (oldValue, newValue) -> newValue));

        List<BarcodeLockDetailEntityDTO> barcodeLockDetailEntityDTOList = this.getBarcodeLockDetailEntityDTOS(factoryId,existTechnicalList);
        if(CollectionUtils.isEmpty(barcodeLockDetailEntityDTOList)){
            return;
        }
        List<TechnicalChangeDetailDTO> technicalChangeDetailDTOList = this.generateTechnicalChangeDetailDTO(technicalChangeHeadDTOMap, barcodeLockDetailEntityDTOList);
        if(CollectionUtils.isEmpty(technicalChangeDetailDTOList)){
            return;
        }
        technicalChangeDetailDTOList.forEach(p->{p.setRemark(reason);});
        for (List<TechnicalChangeDetailDTO> detailDTOList : CommonUtils.splitList(technicalChangeDetailDTOList, INT_100)) {
            technicalChangeDetailRepository.batchInsertOrUpdate(detailDTOList);
        }
    }

    /**
     * 获取批次工艺路径
     * @param technicalChangeHeadDTOMap
     * @param barcodeLockDetailEntityDTOList
     * @throws Exception
     */
    private  List<TechnicalChangeDetailDTO>  generateTechnicalChangeDetailDTO(Map<String, TechnicalChangeHeadDTO> technicalChangeHeadDTOMap,List<BarcodeLockDetailEntityDTO> barcodeLockDetailEntityDTOList) throws Exception {
        List<TechnicalChangeDetailDTO> technicalChangeDetailDTOList = new ArrayList<>();
        String prodplanId = barcodeLockDetailEntityDTOList.get(NumConstant.NUM_ZERO).getBatchSn();
        List<String> prodPlanIdList = new ArrayList<>();
        prodPlanIdList.add(prodplanId);

        List<PsTaskDTO> psTaskDTOList = centerfactoryRemoteService.getPsTaskByProdplanIdList(prodPlanIdList);
        String itemNo = CollectionUtils.isEmpty(psTaskDTOList)?"":psTaskDTOList.get(NumConstant.NUM_ZERO).getItemNo();

        List<CtRouteDetailDTO> ctRouteDetailDTOList = this.getCtRouteList(prodplanId, itemNo);

        PsTask psTask = new PsTask();
        for (BarcodeLockDetailEntityDTO barcodeLockDetailEntityDTO : barcodeLockDetailEntityDTOList) {
            TechnicalChangeDetailDTO technicalChangeDetailDTO = new TechnicalChangeDetailDTO();
            TechnicalChangeHeadDTO technicalChangeHeadDTO = technicalChangeHeadDTOMap.get(barcodeLockDetailEntityDTO.getBillNo());
            technicalChangeDetailDTO.setDetailId(UUID.randomUUID().toString());
            technicalChangeDetailDTO.setTechnicalStatus(STR_2);
            if(technicalChangeHeadDTO != null) {
                technicalChangeDetailDTO.setHeadId(technicalChangeHeadDTO.getHeadId());
                technicalChangeDetailDTO.setChgReqNo(technicalChangeHeadDTO.getChgReqNo());
            }
            technicalChangeDetailDTO.setCreatedBy(barcodeLockDetailEntityDTO.getCreateBy());
            technicalChangeDetailDTO.setCreatedDate(barcodeLockDetailEntityDTO.getCreateDate());
            technicalChangeDetailDTO.setLastUpdatedDate(barcodeLockDetailEntityDTO.getLastUpdatedDate());
            technicalChangeDetailDTO.setLastUpdatedBy(barcodeLockDetailEntityDTO.getLastUpdatedBy());
            technicalChangeDetailDTO.setProdplanId(barcodeLockDetailEntityDTO.getBatchSn());
            technicalChangeDetailDTO.setCompletionStatus(FLAG_N);
            technicalChangeDetailDTO.setSourceSystem(IMES);
            this.setCraftSection(technicalChangeDetailDTO, ctRouteDetailDTOList, psTask);
            if(!CollectionUtils.isEmpty(psTaskDTOList)){
                technicalChangeDetailDTO.setProductName(psTaskDTOList.get(NumConstant.NUM_ZERO).getItemName());
                technicalChangeDetailDTO.setProductCode(psTaskDTOList.get(NumConstant.NUM_ZERO).getItemNo());
            }
            technicalChangeDetailDTOList.add(technicalChangeDetailDTO);
        }
        return technicalChangeDetailDTOList;
    }

    /**
     * 根据批次 料单获取工艺路径
     * @param prodplanId
     * @param itemNo
     * @return
     * @throws RouteException
     * @throws IOException
     */
    private  List<CtRouteDetailDTO> getCtRouteList(String prodplanId, String itemNo) throws RouteException, IOException {
        List<CtRouteInfoDTO> ctRouteInfoDTOListTemp = CrafttechRemoteService.getRouteInfo(Constant.QUO_MARK+ itemNo +Constant.QUO_MARK, Constant.QUO_MARK+ prodplanId+Constant.QUO_MARK);
        Map<String, CtRouteInfoDTO> ctRouteInfoDTOMap = CollectionUtils.isEmpty(ctRouteInfoDTOListTemp)?new HashMap<>():ctRouteInfoDTOListTemp.stream().collect(Collectors.toMap(CtRouteInfoDTO::getItemNo, a -> a, (k1, k2) -> k1));
        CtRouteInfoDTO ctRouteInfoDTO = ctRouteInfoDTOMap.get(prodplanId);
        if(ctRouteInfoDTO != null){
            return ctRouteInfoDTO.getListDetail();
        }
        ctRouteInfoDTO = ctRouteInfoDTOMap.get(itemNo);
        if(ctRouteInfoDTO != null){
            return ctRouteInfoDTO.getListDetail();
        }
        return new ArrayList<>();

    }


    /**
     * 获取明细信息
     * @param factoryId
     * @param lockingList
     * @return
     */
    private List<BarcodeLockDetailEntityDTO> getBarcodeLockDetailEntityDTOS(String factoryId, List<BarcodeLockHeadEntityDTO> lockingList) {
        List<BarcodeLockDetailEntityDTO> detailEntityDTOS = new ArrayList<>();
        for (BarcodeLockHeadEntityDTO barcodeLockHeadEntityDTO : lockingList) {
            if (!CollectionUtils.isEmpty(barcodeLockHeadEntityDTO.getBarcodeLockDetailEntityDTOList())) {
                detailEntityDTOS.addAll(barcodeLockHeadEntityDTO.getBarcodeLockDetailEntityDTOList());
            }
        }
        detailEntityDTOS.forEach(detailEntityDTO -> {
            if (StringUtils.isNotEmpty(factoryId)) {
                detailEntityDTO.setFactoryId(Integer.parseInt(factoryId));
                detailEntityDTO.setId(UUID.randomUUID().toString());
            }
        });
        return detailEntityDTOS;
    }

    /**
     * 处理技改单信息
     * @param technicalAndLockInfoDTO
     */
    private void dealTechnicalModificationForm(TechnicalAndLockInfoDTO technicalAndLockInfoDTO)throws Exception {
        List<TechnicalChangeHeadDTO> technicalChangeHeadDTOList = technicalAndLockInfoDTO.getTechnicalChangeHeadDTOList();
        if (CollectionUtils.isEmpty(technicalChangeHeadDTOList)) {
           return;
        }
        List<String> technicalBillNoList = technicalChangeHeadDTOList.stream().map(e->e.getChgReqNo()).distinct().collect(Collectors.toList());
        List<BarcodeLockHeadEntityDTO> barcodeLockHeadEntityDTOList = barcodeLockHeadService.getHeadInfoByBillNoList(technicalBillNoList);
        List<TechnicalChangeHeadDTO> technicalChangeHeadDTOS = technicalChangeHeadRepository.getListByChgNoSet(new HashSet<>(technicalBillNoList));
        Map<String, BarcodeLockHeadEntityDTO> barcodeLockHeadEntityDTOMap = CollectionUtils.isEmpty(barcodeLockHeadEntityDTOList) ? new HashMap<>() : barcodeLockHeadEntityDTOList.stream().collect(Collectors.toMap(BarcodeLockHeadEntityDTO::getBillNo, a -> a, (k1, k2) -> k1));
        Map<String, TechnicalChangeHeadDTO> technicalChangeHeadDTOMap = CollectionUtils.isEmpty(technicalChangeHeadDTOS) ? new HashMap<>() : technicalChangeHeadDTOS.stream().collect(Collectors.toMap(TechnicalChangeHeadDTO::getChgReqNo, a -> a, (k1, k2) -> k1));

        //1 目的工厂没有该单号的锁定单和技改单	在目标工厂，新建该单号的技改单（状态为“锁定中”，且新增该批次为明细，状态为“锁定中”）
        List<TechnicalChangeHeadDTO> notExistTecialAndLockList = technicalChangeHeadDTOList.stream().filter(e->!barcodeLockHeadEntityDTOMap.containsKey(e.getChgReqNo()) && !technicalChangeHeadDTOMap.containsKey(e.getChgReqNo())).collect(Collectors.toList());
        this.handlingTheNotExistLockAndTecial(notExistTecialAndLockList);
        // 2 目的工厂存在该单号的锁定单	 锁定单状态为“锁定中” 新增该批次到明细，且状态为“锁定中”
        // 锁定单状态为“已解锁”  这种必定存在技改单，后面处理
        //   锁定单状态为“拟制中”——没这个可能性，因为技改单形成的锁定单不会有这个状态
        List<String> lockingBarcodeList = CollectionUtils.isEmpty(barcodeLockHeadEntityDTOList)?new ArrayList<>():barcodeLockHeadEntityDTOList.stream().filter(e->StringUtils.equals(e.getStatus(),LOCKING)).map(e->e.getBillNo()).distinct().collect(Collectors.toList());
        List<TechnicalChangeHeadDTO> existLockingList = technicalChangeHeadDTOList.stream().filter(e->lockingBarcodeList.contains(e.getChgReqNo())).collect(Collectors.toList());
        this.handlingTheSituationWhereALockOrderExists(technicalAndLockInfoDTO, existLockingList);

        // 3 技改单状态为“锁定中” "已解锁"的更新头状态为锁定中,新增锁定中明细

        this.dealForLockedOrUnlockedTechnicalModificationOrder(technicalAndLockInfoDTO, technicalChangeHeadDTOList, technicalChangeHeadDTOS);
    }

    /**
     * 目的工厂存在已解锁或者锁定中的技改单处理
     * @param technicalAndLockInfoDTO
     * @param technicalChangeHeadDTOList
     * @param technicalChangeHeadDTOS
     */
    private void dealForLockedOrUnlockedTechnicalModificationOrder(TechnicalAndLockInfoDTO technicalAndLockInfoDTO, List<TechnicalChangeHeadDTO> technicalChangeHeadDTOList, List<TechnicalChangeHeadDTO> technicalChangeHeadDTOS) {
        List<String> lockedOrUnlockedList = CollectionUtils.isEmpty(technicalChangeHeadDTOS)?new ArrayList<>():
                technicalChangeHeadDTOS.stream().filter(e->StringUtils.equals(e.getTechnicalStatus(),STR_3) || StringUtils.equals(e.getTechnicalStatus(),STR_2)).map(e->e.getChgReqNo()).distinct().collect(Collectors.toList());
        List<TechnicalChangeHeadDTO> existTecialList = technicalChangeHeadDTOList.stream().filter(e-> lockedOrUnlockedList.contains(e.getChgReqNo())).collect(Collectors.toList());

        if(CollectionUtils.isEmpty(existTecialList)){
           return;
        }
        for (List<TechnicalChangeHeadDTO> existTecialTempList : CommonUtils.splitList(existTecialList, INT_100)) {
            List<String> chgReqNoList = existTecialTempList.stream().map(e->e.getChgReqNo()).distinct().collect(Collectors.toList());
            technicalChangeHeadRepository.batchUpdateStatus(chgReqNoList,STR_2, technicalAndLockInfoDTO.getEmpNo());
        }
        List<TechnicalChangeDetailDTO> detailEntityDTOS = this.getTechnicalChangeDetailDTOList(existTecialList);

        Map<String, String> headIdMap = CollectionUtils.isEmpty(technicalChangeHeadDTOS)?new HashMap<>():technicalChangeHeadDTOS.stream()
                .collect(Collectors.toMap(TechnicalChangeHeadDTO::getChgReqNo, TechnicalChangeHeadDTO::getHeadId));
        for (TechnicalChangeDetailDTO detailEntityDTO : detailEntityDTOS) {
            detailEntityDTO.setHeadId(headIdMap.get(detailEntityDTO.getChgReqNo()));
            detailEntityDTO.setRemark(String.format(Constant.LOCK_REASON_ADD, technicalAndLockInfoDTO.getFormFactoryName()));
        }
        for (List<TechnicalChangeDetailDTO> technicalChangeDetailDTOList : CommonUtils.splitList(detailEntityDTOS, INT_100)) {
            technicalChangeDetailRepository.batchInsertOrUpdate(technicalChangeDetailDTOList);
        }
    }

    /**
     * 处理不存在技改单以及锁定单的情况
     * @param notExistTecialAndLockList
     */
    private void handlingTheNotExistLockAndTecial(List<TechnicalChangeHeadDTO> notExistTecialAndLockList) {
        if(CollectionUtils.isEmpty(notExistTecialAndLockList)){
            return;
        }
        for (List<TechnicalChangeHeadDTO> notExistTecialAndLockTempList : CommonUtils.splitList(notExistTecialAndLockList, INT_100)) {
            technicalChangeHeadRepository.batchInsert(notExistTecialAndLockTempList);
        }
        List<TechnicalChangeDetailDTO> detailEntityDTOS = this.getTechnicalChangeDetailDTOList(notExistTecialAndLockList);
        for (List<TechnicalChangeDetailDTO> technicalChangeDetailDTOList : CommonUtils.splitList(detailEntityDTOS, INT_100)) {
            technicalChangeDetailRepository.batchInsert(technicalChangeDetailDTOList);
        }
    }

    /**
     * 处理锁定单存在的情况
     * @param technicalAndLockInfoDTO
     * @param existLockList
     * @throws Exception
     */
    private void handlingTheSituationWhereALockOrderExists(TechnicalAndLockInfoDTO technicalAndLockInfoDTO, List<TechnicalChangeHeadDTO> existLockList) throws Exception {
        if(CollectionUtils.isEmpty(existLockList)){
            return;
        }
        List<CtRouteDetailDTO> ctRouteDetailDTOList = this.getCtRouteDetailDTOS(technicalAndLockInfoDTO);

        for (List<TechnicalChangeHeadDTO> existLockTempList : CommonUtils.splitList(existLockList, INT_100)) {
            barcodeLockHeadRepository.batchUpdateStatus(existLockTempList.stream().map(e->e.getChgReqNo()).distinct().collect(Collectors.toList()), LOCKING, technicalAndLockInfoDTO.getEmpNo());
        }

        List<TechnicalChangeDetailDTO> detailEntityDTOS = this.getTechnicalChangeDetailDTOList(existLockList);
        List<BarcodeLockDetailEntityDTO> barcodeLockDetailEntityDTOList = new ArrayList<>();
        for (TechnicalChangeDetailDTO technicalChangeDetailDTO : detailEntityDTOS) {
            for (CtRouteDetailDTO ctRouteDetailDTO : ctRouteDetailDTOList) {
                BarcodeLockDetailEntityDTO barcodeLockDetailEntityDTO = new BarcodeLockDetailEntityDTO();
                barcodeLockDetailEntityDTO.setId(UUID.randomUUID().toString());
                barcodeLockDetailEntityDTO.setBillNo(technicalChangeDetailDTO.getChgReqNo());
                barcodeLockDetailEntityDTO.setBatchSn(technicalAndLockInfoDTO.getProdplanId());
                barcodeLockDetailEntityDTO.setStatus(LOCKING);
                barcodeLockDetailEntityDTO.setLockProcessCode(ctRouteDetailDTO.getCurrProcess());
                barcodeLockDetailEntityDTO.setType(Constant.LOCK_TYPE_BATCH);
                barcodeLockDetailEntityDTO.setRemark(String.format(Constant.LOCK_REASON_ADD, technicalAndLockInfoDTO.getFormFactoryName()));
                barcodeLockDetailEntityDTO.setCreateBy(technicalChangeDetailDTO.getCreatedBy());
                barcodeLockDetailEntityDTO.setLastUpdatedBy(technicalChangeDetailDTO.getLastUpdatedBy());
                barcodeLockDetailEntityDTO.setCreateDate(technicalChangeDetailDTO.getCreatedDate());
                barcodeLockDetailEntityDTO.setLastUpdatedDate(technicalChangeDetailDTO.getLastUpdatedDate());
                String factoryId = technicalAndLockInfoDTO.getToFactoryId();
                if(!StringUtils.isEmpty(factoryId)){
                    barcodeLockDetailEntityDTO.setFactoryId(Integer.parseInt(factoryId));
                }
                barcodeLockDetailEntityDTOList.add(barcodeLockDetailEntityDTO);
            }

        }
        barcodeLockDetailService.batchInsertOrUpdate(barcodeLockDetailEntityDTOList);
    }

    /**
     * 获取工艺路径
     * @param technicalAndLockInfoDTO
     * @return
     * @throws Exception
     */
    private List<CtRouteDetailDTO> getCtRouteDetailDTOS(TechnicalAndLockInfoDTO technicalAndLockInfoDTO) throws Exception {
        String prodPlanId = technicalAndLockInfoDTO.getProdplanId();
        List<String> prodPlanIdList = new ArrayList<>();
        prodPlanIdList.add(prodPlanId);
        List<PsTaskDTO> psTaskDTOList = centerfactoryRemoteService.getPsTaskByProdplanIdList(prodPlanIdList);
        String itemNo = CollectionUtils.isEmpty(psTaskDTOList)?"":psTaskDTOList.get(NumConstant.NUM_ZERO).getItemNo();
        List<CtRouteDetailDTO> ctRouteDetailDTOList = this.getCtRouteList(prodPlanId,itemNo);
        if(CollectionUtils.isEmpty(ctRouteDetailDTOList)){
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,MessageId.CT_ROUTE_DETAIL_OF_PRODPLANID_NOT_EXIST,new Object[]{prodPlanId});
        }
        return ctRouteDetailDTOList;
    }

    private List<TechnicalChangeDetailDTO> getTechnicalChangeDetailDTOList(List<TechnicalChangeHeadDTO> technicalChangeHeadDTOList) {
        List<TechnicalChangeDetailDTO> detailEntityDTOS = new ArrayList<>();
        for (TechnicalChangeHeadDTO technicalChangeHeadDTO : technicalChangeHeadDTOList) {
            if (CollectionUtils.isNotEmpty(technicalChangeHeadDTO.getDetails())) {
                detailEntityDTOS.addAll(technicalChangeHeadDTO.getDetails());
            }
        }
        return detailEntityDTOS;
    }



    /**
     * 新增锁定单
     * @param factoryId
     * @param insertList
     * @throws Exception
     */
    private void batchInsert(String reason,String factoryId, List<BarcodeLockHeadEntityDTO> insertList) throws Exception {
        if(CollectionUtils.isEmpty(insertList)){
            return;
        }
        insertList.forEach(p->{p.setReason(p.getReason()+reason);});
        for (List<BarcodeLockHeadEntityDTO> insertTempList : CommonUtils.splitList(insertList, INT_100)) {
            barcodeLockHeadService.batchInsert(insertTempList);
            List<BarcodeLockDetailEntityDTO> detailEntityDTOS = this.getBarcodeLockDetailEntityDTOS(factoryId, insertTempList);
            barcodeLockDetailService.batchInsert(detailEntityDTOS);
        }
    }

    /**
     * 转换状态描述
     * * when '0' then '待提交'
     * * when '1' then '拟制中'
     * * when '2' then '锁定中'
     * * when '3' then '已解锁'
     * * when '4' then '已作废'
     *
     * @param list 头信息
     */
    private void setTechnicalStatusDesc(List<TechnicalChangeHeadDTO> list) {
        List<SysLookupValuesDTO> batchSysValueByCode = BasicsettingRemoteService.getBatchSysValueByCode(Collections.singletonList(LOOKUP_TYPE_1004103));
        Map<String, String> statusMap = batchSysValueByCode.stream()
                .collect(Collectors.toMap(SysLookupValuesDTO::getLookupMeaning, SysLookupValuesDTO::getDescriptionChin));
        for (TechnicalChangeHeadDTO item : list) {
            String technicalStatus = item.getTechnicalStatus();
            if (StringUtils.isBlank(technicalStatus)) {
                continue;
            }
            item.setTechnicalStatusDesc(statusMap.get(technicalStatus));
            switch (technicalStatus) {
                // 待提交'
                case MpConstant.STRING_ZERO:
                    item.setViewButton(true);
                    break;
                // '拟制中' 修改、删除按钮，
                case MpConstant.STRING_ONE:
                    item.setDeleteButton(true);
                    item.setEditButton(true);
                    break;
                // 锁定中  执行扫描、查看按钮
                case MpConstant.STRING_TWO:
                    item.setScanButton(true);
                    item.setEditButton(true);
                    break;
                // '已解锁'
                case MpConstant.STRING_THREE:
                    item.setViewButton(true);
                    break;
                // 已作废
                case MpConstant.STRING_FOUR:
                    item.setViewButton(true);
                    break;
                default:
                    item.setTechnicalStatusDesc(technicalStatus);
            }
        }
    }

    /**
     * 转换人员信息
     *
     * @param list 技改单信息
     */
    private void coverEmpInfo(List<TechnicalChangeHeadDTO> list) {
        List<String> userIdList = new LinkedList<>();
        list.forEach(item -> {
            userIdList.add(item.getCreatedBy());
            userIdList.add(item.getLastUpdatedBy());
        });
        List<String> userCollect = userIdList.stream().filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userCollect)) {
            return;
        }
        try {
            Map<String, HrmPersonInfoDTO> hrmPersonInfo = centerfactoryRemoteService.getHrmPersonInfo(userCollect);
            for (TechnicalChangeHeadDTO headDTO : list) {
                headDTO.setCreatedBy(this.getUserName(headDTO.getCreatedBy(), hrmPersonInfo));
                headDTO.setLastUpdatedBy(this.getUserName(headDTO.getLastUpdatedBy(), hrmPersonInfo));
            }
        } catch (Exception e) {
            log.error(e);
        }
    }

    /**
     * 分页查询明细信息
     *
     * @param pageParams 分页参数
     * @return 明细分页数据
     */
    @Override
    public Page<TechnicalChangeDetailDTO> queryTechnicalDetailByPage(Page<TechnicalChangeDetailDTO> pageParams) {
        this.transSnToSnList(null, pageParams);
        // 双击头表带出明细 ，条码，批次，主工序
        List<TechnicalChangeDetailDTO> resultList = technicalChangeHeadRepository.queryTechnicalDetailByPage(pageParams);
        if (resultList == null) {
            resultList = new LinkedList<>();
        }
        // 装换技改单状态 0待提交，1拟制中，2锁定中，3已解锁，4已作废
        this.coverTechnicalStatus(resultList);
        // 转换工号
        this.coverDetailsEmpNo(resultList);
        this.setMBom(resultList);
        pageParams.setRows(resultList);
        return pageParams;
    }

    private void setMBom(List<TechnicalChangeDetailDTO> list) {
        if(CollectionUtils.isEmpty(list)){
            return;
        }
        List<String> prodPlanIdList = list.stream().map(TechnicalChangeDetailDTO::getProdplanId).distinct().collect(Collectors.toList());
        List<BProdBomHeaderDTO>  bProdBomHeaderDTOList = centerfactoryRemoteService.queryProductCodeByProdPlanIdList(prodPlanIdList);
        Map<String, BProdBomHeaderDTO> prodBomHeaderDTOMap = CollectionUtils.isEmpty(bProdBomHeaderDTOList)?new HashMap<>():bProdBomHeaderDTOList.stream().collect(Collectors.toMap(BProdBomHeaderDTO::getProdplanId, BProdBomHeaderDTO -> BProdBomHeaderDTO, (k1, k2) -> k1));
        for (TechnicalChangeDetailDTO technicalChangeDetailDTO : list) {
            BProdBomHeaderDTO bProdBomHeaderDTO = prodBomHeaderDTOMap.get(technicalChangeDetailDTO.getProdplanId());
            technicalChangeDetailDTO.setMbomProductCode(bProdBomHeaderDTO != null?bProdBomHeaderDTO.getProductCode():technicalChangeDetailDTO.getProductCode());
        }
    }

    /**
     * 0待提交，1拟制中，2锁定中，3已解锁，4已作废
     *
     * @param resultList 技改单明细
     */
    private void coverTechnicalStatus(List<TechnicalChangeDetailDTO> resultList) {
        List<SysLookupValuesDTO> batchSysValueByCode = BasicsettingRemoteService.getBatchSysValueByCode(Collections.singletonList(LOOKUP_TYPE_1004103));
        Map<String, String> statusMap = batchSysValueByCode.stream()
                .collect(Collectors.toMap(SysLookupValuesDTO::getLookupMeaning, SysLookupValuesDTO::getDescriptionChin));
        String technicalStatus;
        for (TechnicalChangeDetailDTO detailDTO : resultList) {
            // 是否完工处理
            this.dealCompletionStatus(detailDTO);
            technicalStatus = detailDTO.getTechnicalStatus();
            detailDTO.setTechnicalStatusDesc(statusMap.get(technicalStatus));
            String technicalStatusHead = detailDTO.getTechnicalStatusHead();
            detailDTO.setTechnicalStatusHeadDesc(statusMap.get(technicalStatusHead));
        }
    }

    /**
     * @return
     * <AUTHOR>
     * 技改管控查询sn转snList
     * @Date 2023/2/17 15:31
     * @Param
     **/
    private void transSnToSnList(Page<TechnicalChangeHeadDTO> headParams, Page<TechnicalChangeDetailDTO> detailParams) {
        if (null != headParams) {
            TechnicalChangeHeadDTO headParam = BeanUtil.toBean(headParams.getParams(), TechnicalChangeHeadDTO.class);
            String sn = headParam.getSn();
            if (StringUtils.isEmpty(sn)) {
                return;
            }
            String[] snList = sn.trim().split("[,|\\n]");
            headParam.setSn(null);
            headParam.setSnList(Arrays.asList(snList));
            headParams.setParams(headParam);
        }
        if (null != detailParams) {
            TechnicalChangeDetailDTO detailParam = BeanUtil.toBean(detailParams.getParams(), TechnicalChangeDetailDTO.class);
            String sn = detailParam.getSn();
            if (StringUtils.isEmpty(sn)) {
                return;
            }
            String[] snList = sn.trim().split("[,|\\n]");
            detailParam.setSn(null);
            detailParam.setSnList(Arrays.asList(snList));
            detailParams.setParams(detailParam);
        }
    }

    /**
     * 分页查询条码明细信息
     *
     * @param pageParams 分页参数
     * @return 明细分页数据
     */
    @Override
    public Page<TechnicalChangeDetailDTO> querySnDetailByPage(Page<TechnicalChangeDetailDTO> pageParams) {
        this.transSnToSnList(null, pageParams);
        // 1. 查条码明细
        List<TechnicalChangeDetailDTO> snDetails = technicalChangeHeadRepository.querySnDetailByPage(pageParams);
        if (snDetails == null) {
            snDetails = new LinkedList<>();
        }
        // 处理解锁状态 0 已提交 1 已确认 2 已驳回
        this.coverUnlockStatus(snDetails);
        // 处理解锁类型 0 完成技改，1 无需技改 2 本工序完成技改 3 本工序无需技改
        this.coverUnlockType(snDetails);
        // 处理执行人，QC确认人
        this.coverDetailsEmpNo(snDetails);
        pageParams.setRows(snDetails);
        return pageParams;
    }

    /**
     * 处理是否完工字段
     *
     * @param snDetail 明细
     */
    private void dealCompletionStatus(TechnicalChangeDetailDTO snDetail) {
        if (Constant.FLAG_Y.equals(snDetail.getCompletionStatus())) {
            snDetail.setCompletionStatus(Constant.YES);
        }
        if (Constant.FLAG_N.equals(snDetail.getCompletionStatus())) {
            snDetail.setCompletionStatus(Constant.NO);
        }
    }

    /**
     * 处理解锁状态 0 已提交 1 已确认 2 已驳回
     * 转换解锁状态
     *
     * @param snDetails 条码信息
     */
    private void coverUnlockStatus(List<TechnicalChangeDetailDTO> snDetails) {
        List<SysLookupValuesDTO> batchSysValueByCode =
                BasicsettingRemoteService.getBatchSysValueByCode(Collections.singletonList(LOOKUP_TYPE_1004102));
        Map<String, String> statusMap = batchSysValueByCode.stream()
                .collect(Collectors.toMap(SysLookupValuesDTO::getLookupMeaning, SysLookupValuesDTO::getDescriptionChin));
        for (TechnicalChangeDetailDTO snDetail : snDetails) {
            Integer unlockStatus = snDetail.getUnlockStatus();
            if (null == unlockStatus) {
                continue;
            }
            snDetail.setUnlockStatusDesc(statusMap.get(unlockStatus.toString()));
        }
    }

    /**
     * 转换解锁类型
     * 0 完成技改，1 无需技改 2 本工序完成技改 3 本工序无需技改
     *
     * @param detailDTOList 条码信息
     */
    private void coverUnlockType(List<TechnicalChangeDetailDTO> detailDTOList) {
        List<SysLookupValuesDTO> batchSysValueByCode =
                BasicsettingRemoteService.getBatchSysValueByCode(Collections.singletonList(LOOKUP_TYPE_1004096));
        Map<String, String> statusMap = batchSysValueByCode.stream()
                .collect(Collectors.toMap(SysLookupValuesDTO::getLookupMeaning, SysLookupValuesDTO::getDescriptionChin));
        for (TechnicalChangeDetailDTO detailDTO : detailDTOList) {
            String unlockType = detailDTO.getUnlockType();
            if (StringUtils.isBlank(unlockType)) {
                continue;
            }
            detailDTO.setUnlockTypeDesc(statusMap.get(unlockType));
        }
    }

    /**
     * 处理解锁员工信息
     *
     * @param resultList 详细数据
     */
    private void coverDetailsEmpNo(List<TechnicalChangeDetailDTO> resultList) {
        List<String> userList = new LinkedList<>();
        for (TechnicalChangeDetailDTO item : resultList) {
            if (StringUtils.isNotBlank(item.getLastUpdatedBy())) {
                userList.add(item.getLastUpdatedBy());
            }
            if (StringUtils.isNotBlank(item.getOperator())) {
                userList.add(item.getOperator());
            }
            if (StringUtils.isNotBlank(item.getCreatedBy())) {
                userList.add(item.getCreatedBy());
            }
            if (StringUtils.isNotBlank(item.getConfirmedBy())) {
                userList.add(item.getConfirmedBy());
            }
        }
        userList = userList.stream().distinct().collect(Collectors.toList());
        try {
            if (CollectionUtils.isEmpty(userList)) {
                return;
            }
            Map<String, HrmPersonInfoDTO> hrmPersonInfo = centerfactoryRemoteService.getHrmPersonInfo(userList);
            for (TechnicalChangeDetailDTO temp : resultList) {
                temp.setOperator(this.getUserName(temp.getOperator(), hrmPersonInfo));
                temp.setLastUpdatedBy(this.getUserName(temp.getLastUpdatedBy(), hrmPersonInfo));
                temp.setCreatedBy(this.getUserName(temp.getCreatedBy(), hrmPersonInfo));
                temp.setConfirmedBy(this.getUserName(temp.getConfirmedBy(), hrmPersonInfo));
            }
        } catch (Exception e) {
            log.error(e);
        }
    }

    /**
     * 获取用户名
     *
     * @param userId        用户id
     * @param hrmPersonInfo 用户信息
     * @return 用户id + 用户名
     */
    private String getUserName(String userId, Map<String, HrmPersonInfoDTO> hrmPersonInfo) {
        if (StringUtils.isBlank(userId)) {
            return userId;
        }
        HrmPersonInfoDTO hrmPersonInfoDTO = hrmPersonInfo.get(userId);
        if (hrmPersonInfoDTO == null) {
            return userId;
        }
        return hrmPersonInfoDTO.getEmpName() + userId;
    }

    /**
     * 导出excel
     *
     * @param queryParam 查询参数
     * @param response   响应
     * @throws Exception 业务异常
     */
    @Override
    @RedisDistributedLockAnnotation(redisPrefix = "exportTechnicalDetails", factoryId = true, redisLockTime = 3600,
            lockFailMsgZh = "正在导出请稍后再试", lockFailMsgEn = "Exporting. Please try again later.", redisLockParam = {
            @RedisLockParamAnnotation(paramName = "queryParam", propertiesString = "empNo")
    })
    public void exportTechnicalDetails(TechnicalChangeHeadDTO queryParam, HttpServletResponse response) throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat(MpConstant.DATE_FORMAT_YYYYMMDDHHMM_ONE);
        String fileName = sdf.format(new Date()) + Constant.TECHNICAL_EXPORT_NAME;
        ImesExcelUtil.setResponseHeader(response, fileName);
        ExcelWriter excelWriter = EasyExcelFactory.write(response.getOutputStream(), TechnicalChangeDetailDTO.class)
                .excelType(ExcelTypeEnum.XLSX).build();
        this.writeExcelData(queryParam, excelWriter, null);
    }

    /**
     * 分页写excel 文件
     *
     * @param queryParam  查询条件
     * @param excelWriter excelWriter
     */
    private void writeExcelData(TechnicalChangeHeadDTO queryParam, ExcelWriter excelWriter,
                                TechnicalChangeDetailDTO detail) {
        // 获取最大导出数量
        List<SysLookupValuesDTO> valueByTypeCodes = BasicsettingRemoteService.getLookupValueByTypeCodes(Constant.lookupType.VALUE_1004041);
        if (valueByTypeCodes == null) {
            valueByTypeCodes = new LinkedList<>();
        }
        String maxCount = valueByTypeCodes.stream().filter(item -> Constant.TECHNICAL.equals(item.getAttribute1()))
                .map(SysLookupValuesDTO::getLookupMeaning)
                .findFirst().orElse(MpConstant.STRING_100000);
        int maxSize = Integer.parseInt(maxCount);
        WriteSheet build = EasyExcelFactory.writerSheet(0, Constant.TECHNICAL_EXPORT_SHEET_NAME).build();
        int headCurrentCount = 0;
        List<TechnicalChangeDetailDTO> rows;
        do {
            // 先查单据头， 再根据单据头一条一条查明细直至 满足导出数据要求
            Page<?> pageHead = new Page<>();
            pageHead.setCurrent(++headCurrentCount);
            pageHead.setPageSize(Constant.INT_5000);
            pageHead.setSearchCount(false);
            if (Objects.isNull(detail)) {
                pageHead.setParams(queryParam);
                rows = technicalChangeHeadRepository.exportDetailByPage(pageHead);
            } else {
                pageHead.setParams(detail);
                rows = technicalChangeHeadRepository.querySnDetailSnByPage(pageHead);
            }
            if (CollectionUtils.isEmpty(rows)) {
                break;
            }
            if (maxSize < rows.size()) {
                List<List<TechnicalChangeDetailDTO>> lists = CommonUtils.splitList(rows, maxSize);
                rows = lists.get(0);// 区第一个
            }
            maxSize -= rows.size();
            // 转换技改状态
            this.coverTechnicalStatus(rows);
            // 转换单据转态
            this.coverUnlockType(rows);
            this.coverUnlockStatus(rows);
            this.coverDetailsEmpNo(rows);
            this.setMBom(rows);
            excelWriter.write(rows, build);
        } while (CollectionUtils.isNotEmpty(rows) && maxSize > 0 && rows.size() == Constant.INT_5000);
        excelWriter.finish();
    }

    /**
     * 批量删除 技改单头和明细
     *
     * @param list  头id
     * @param empNo 工号
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDeleteByHeadId(List<String> list, String empNo) {
        List<List<String>> splitList = CommonUtils.splitList(list, Constant.IN_MAX_BATCH_SIZE);
        for (List<String> items : splitList) {
            technicalChangeHeadRepository.batchDeleteByHeadId(items);
            technicalChangeDetailRepository.batchDeleteByHeadId(items);
        }
    }

    /**
     * 获取批次及城堡卡子批次锁定信息
     *
     * @param technicalChangeDetailDTO 查询参数
     * @return 技改信息
     * @throws Exception 业务异常
     */
    @Override
    @RecordLogAnnotation("开工及该提示")
    public String queryTechnicalDetail(TechnicalChangeDetailDTO technicalChangeDetailDTO) throws Exception {
        if (StringUtils.isBlank(technicalChangeDetailDTO.getTaskNo())
                || StringUtils.isBlank(technicalChangeDetailDTO.getProdplanId())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PARAM_IS_NULL);
        }
        List<PsTask> childTasks = PlanscheduleRemoteService.getPsTaskByPartsPlanNo(technicalChangeDetailDTO.getTaskNo());
        if (childTasks == null) {
            childTasks = new LinkedList<>();
        }
        List<PsWorkOrderDTO> childWos = new ArrayList();
        for (PsTask task : childTasks) {
            List<PsWorkOrderDTO> workOrderDTOS = PlanscheduleRemoteService.getWorkOrderInfo(task.getProdplanId());
            if (CollectionUtils.isEmpty(workOrderDTOS)) {
                continue;
            }
            workOrderDTOS = workOrderDTOS.stream()
                    .filter(e -> e.getChildFlag() && StringUtils.equals(e.getCraftSection(),
                            technicalChangeDetailDTO.getCraftSection())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(workOrderDTOS)) {
                childWos.addAll(workOrderDTOS);
            }
        }
        List<String> collect = childWos.stream().map(PsWorkOrderDTO::getSourceTask).distinct().collect(Collectors.toList());
        collect.add(technicalChangeDetailDTO.getProdplanId());
        technicalChangeDetailDTO.setProdplanIdList(collect);
        // 锁定中的单据
        technicalChangeDetailDTO.setTechnicalStatus(Constant.STR_2);
        List<TechnicalChangeDetailDTO> detailDTOList = technicalChangeHeadRepository.queryTechnicalDetail(technicalChangeDetailDTO);
        // 查询批次锁定单
        List<BarcodeLockDetail> lockDetailList = barcodeLockDetailRepository.queryLockDetail(collect, null);
        if (CollectionUtils.isEmpty(detailDTOList) && CollectionUtils.isEmpty(lockDetailList)) {
            return StringUtils.EMPTY;
        }
        String msg = StringUtils.EMPTY;
        if (CollectionUtils.isNotEmpty(detailDTOList)) {
            msg = detailDTOList.stream().map(TechnicalChangeDetailDTO::getChgReqNo)
                    .collect(Collectors.joining(Constant.COMMA));
            msg = CommonUtils.getLmbMessage(MessageId.CHECK_TECHNICAL_DETAIL, new String[]{msg});
        }
        if (CollectionUtils.isNotEmpty(lockDetailList)) {
            String lock = lockDetailList.stream().map(BarcodeLockDetail::getBillNo).distinct()
                    .collect(Collectors.joining(COMMA));
            msg += CommonUtils.getLmbMessage(MessageId.LOCK_PRODPLAN_MSG, new String[]{lock});
        }
        return msg;
    }

    /**
     * 同步技改单数据
     */
    @Override
    public void syncTechnicalInfo(String factoryId) throws Exception {
        // 查询数据字典7000014获取上次同步结束时间
        SysLookupTypesDTO sysLookupTypesDTO = BasicsettingRemoteService.getSysLookUpValue(Constant.LookUpKey.LOOK_7000, Constant.LookUpKey.LOOK_7000014);
        if (sysLookupTypesDTO == null || StringUtils.isEmpty(sysLookupTypesDTO.getLookupMeaning())) {
            String[] param = {Constant.LookUpKey.LOOK_7000014};
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SYS_LOOK_NOT_CONFIG, param);
        }
        // 同步开始时间
        Date startTime = DateUtil.convertStringToDate(sysLookupTypesDTO.getLookupMeaning(), DateUtil.DATE_FORMATE_FULL);
        // 同步数据冗余时间
        startTime = DateUtil.addDay(startTime, -preDays);

        // 分批循环同步,直至全部同步完成或循环超过1000次
        roundCount = NumConstant.NUM_ZERO;
        this.syncTechnicalInfo(sysLookupTypesDTO.getLookupCode(), startTime, NumConstant.NUM_1000, factoryId, sysLookupTypesDTO.getAttribute1());
    }

    /**
     * 同步技改信息并更新数据字典时间
     *
     * @param lookupCode 数据字典code
     * @param startTime  同步开始时间
     * @param limit      分批数量
     * @throws Exception
     */
    private void syncTechnicalInfo(BigDecimal lookupCode, Date startTime, int limit, String factoryId, String id) throws Exception {
        if (roundCount > NumConstant.NUM_1000) {
            return;
        }
        roundCount++;
        List<TechnicalChangeDetailDTO> syncDataList = technicalChangeDetailRepository.getSyncDataByLastUpdatedDate(startTime, limit, id);
        if (CollectionUtils.isEmpty(syncDataList)) {
            return;
        }
        for (TechnicalChangeDetailDTO dto : syncDataList) {
            dto.setFactoryId(factoryId);
        }
        centerfactoryRemoteService.syncTechnicalInfo(syncDataList);
        TechnicalChangeDetailDTO lastDto = syncDataList.get(syncDataList.size() - 1);
        // 取最后一条数据的最后更新时间
        Date lastUpdatedDate = lastDto.getLastUpdatedDate();
        // 同步成功后更新下次同步开始时间
        BasicsettingRemoteService.updateSysLookupValuesMeaning(lookupCode, DateUtil.convertDateToString(lastUpdatedDate, DateUtil.DATE_FORMATE_FULL), lastDto.getId());
        if (syncDataList.size() >= limit) {
            // 清空列表，避免内存无法回收
            syncDataList.clear();
            this.syncTechnicalInfo(lookupCode, lastUpdatedDate, limit, factoryId, lastDto.getId());
        }
    }

    @Override
    public List<TechnicalChangeDetailDTO> pageSelectNotFinishTechSPMDate(SynchronizeSpmDateDTO synchronizeSpmDateDTO, int page, int row) {
        synchronizeSpmDateDTO.setPage(page);
        synchronizeSpmDateDTO.setRow(row);
        return technicalChangeDetailRepository.pageSelectNotFinishTechSPMDate(synchronizeSpmDateDTO);
    }

    @Override
    public void batchUpdateStatusOfDetail(List<TechnicalChangeDetailDTO> updateList) {
        technicalChangeDetailRepository.batchUpdateStatusOfDetail(updateList);
    }

    /**
     * 分页查询条码明细信息
     *
     * @param pageParams 分页参数
     * @return 明细分页数据
     */
    @Override
    public Page<TechnicalChangeDetailDTO> querySnDetailSnByPage(Page<TechnicalChangeDetailDTO> pageParams) throws MesBusinessException {
        // 处理数据
        this.extracted(pageParams);
        // 1. 查条码明细
        List<TechnicalChangeDetailDTO> snDetails = technicalChangeHeadRepository.querySnDetailSnByPage(pageParams);
        if (snDetails == null) {
            snDetails = new LinkedList<>();
        }
        // 装换技改单状态 0待提交，1拟制中，2锁定中，3已解锁，4已作废
        this.coverTechnicalStatus(snDetails);
        // 处理解锁状态 0 已提交 1 已确认 2 已驳回
        this.coverUnlockStatus(snDetails);
        // 处理解锁类型 0 完成技改，1 无需技改 2 本工序完成技改 3 本工序无需技改
        this.coverUnlockType(snDetails);
        // 处理执行人，QC确认人
        this.coverDetailsEmpNo(snDetails);
        pageParams.setRows(snDetails);
        return pageParams;
    }

    private void extracted(Page<TechnicalChangeDetailDTO> pageParams) throws MesBusinessException {
        Object params = pageParams.getParams();
        if (!Objects.isNull(params)) {
            TechnicalChangeDetailDTO detailDTO = JacksonJsonConverUtil.jsonToBean(JSON.toJSONString(params), TechnicalChangeDetailDTO.class);
            if (Objects.isNull(detailDTO)) {
                detailDTO = new TechnicalChangeDetailDTO();
            }
            if (maxSizeOver(detailDTO)) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.MAX_QUERY_SIZE);
            }
            // 批次为空，条码非空处理
            if (CollectionUtils.isNotEmpty(detailDTO.getSnList()) && CollectionUtils.isEmpty(detailDTO.getProdplanIdList())) {
                this.dealSnList(detailDTO);
                pageParams.setParams(detailDTO);
            }
        }
    }

    private void dealSnList(TechnicalChangeDetailDTO detailDTO) {
        List<String> snList = detailDTO.getSnList();
        List<String> prodplanIdList = new LinkedList<>();
        snList = snList.stream().distinct().collect(Collectors.toList());
        detailDTO.setSnList(snList);
        List<TechnicalChangeHeadDTO> childList = new LinkedList<>();
        for (String sn : snList) {
            String trim = sn.trim();
            if (StringUtils.isNotBlank(trim) && trim.length() > INT_5) {
                String prodId = trim.substring(0, trim.length() - INT_5);
                prodplanIdList.add(prodId);
                TechnicalChangeHeadDTO temp = new TechnicalChangeHeadDTO();
                temp.setSn(trim);
                temp.setProdplanId(prodId);
                childList.add(temp);
            }
        }
        detailDTO.setNoExecList(childList);
        List<String> collect = prodplanIdList.stream().distinct().collect(Collectors.toList());
        detailDTO.setProdplanIdList(collect);
    }

    private boolean maxSizeOver(TechnicalChangeDetailDTO detailDTO) {
        return (CollectionUtils.isNotEmpty(detailDTO.getSnList()) && detailDTO.getSnList().size() > INT_500)
                || (CollectionUtils.isNotEmpty(detailDTO.getProdplanIdList()) && detailDTO.getProdplanIdList().size() > INT_500)
                || (CollectionUtils.isNotEmpty(detailDTO.getChgReqNoList()) && detailDTO.getChgReqNoList().size() > INT_500);
    }


    /**
     * 导出
     *
     * @param response 响应
     * @param params   参数
     * @throws Exception 业务异常
     */
    @Override
    public void exportTechnicalSn(HttpServletResponse response, TechnicalChangeDetailDTO params) throws Exception {
        if (CollectionUtils.isEmpty(params.getProdplanIdList()) && CollectionUtils.isEmpty(params.getSnList())
                && CollectionUtils.isEmpty(params.getChgReqNoList())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.ENTER_QUERY_CRITERIA_BEFORE_EXPORTING);
        }
        if (CollectionUtils.isNotEmpty(params.getSnList()) && CollectionUtils.isEmpty(params.getProdplanIdList())) {
            this.dealSnList(params);
        }
        SimpleDateFormat sdf = new SimpleDateFormat(MpConstant.DATE_FORMAT_YYYYMMDDHHMM_ONE);
        String fileName = sdf.format(new Date()) + Constant.TECHNICAL_EXPORT_NAME;
        ImesExcelUtil.setResponseHeader(response, fileName);
        List<String> includeColumnList = new LinkedList<>();
        includeColumnList.add(TechnicalChangeDetailDTO.Fields.chgReqNo);
        includeColumnList.add(TechnicalChangeDetailDTO.Fields.chgReqName);
        includeColumnList.add(TechnicalChangeDetailDTO.Fields.productCode);
        includeColumnList.add(TechnicalChangeDetailDTO.Fields.productName);
        includeColumnList.add(TechnicalChangeDetailDTO.Fields.prodplanId);
        includeColumnList.add(TechnicalChangeDetailDTO.Fields.technicalStatusDesc);
        includeColumnList.add(TechnicalChangeDetailDTO.Fields.sn);
        includeColumnList.add(TechnicalChangeDetailDTO.Fields.craftSection);
        includeColumnList.add(TechnicalChangeDetailDTO.Fields.unlockCraftSection);
        includeColumnList.add(TechnicalChangeDetailDTO.Fields.unlockTypeDesc);
        includeColumnList.add(TechnicalChangeDetailDTO.Fields.unlockStatusDesc);
        includeColumnList.add(TechnicalChangeDetailDTO.Fields.technicalTeam);
        includeColumnList.add(TechnicalChangeDetailDTO.Fields.technicalDepartment);
        includeColumnList.add(TechnicalChangeDetailDTO.Fields.remark);
        includeColumnList.add(TechnicalChangeDetailDTO.Fields.operator);
        includeColumnList.add(TechnicalChangeDetailDTO.Fields.operatorDate);
        includeColumnList.add(TechnicalChangeDetailDTO.Fields.confirmedBy);
        includeColumnList.add(TechnicalChangeDetailDTO.Fields.rejectionReason);
        includeColumnList.add(TechnicalChangeDetailDTO.Fields.confirmedDate);
        ExcelWriter excelWriter = EasyExcelFactory.write(response.getOutputStream(), TechnicalChangeDetailDTO.class)
                .includeColumnFiledNames(includeColumnList)
                .excelType(ExcelTypeEnum.XLSX).build();
        this.writeExcelData(null, excelWriter, params);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleSubmitAll(TechnicalChangeRetentionListQueryDTO dto) throws Exception {
        validateParamsOfTechSubmit(dto);
        // 分500一批查询处理
        Page<TechnicalChangeRetentionListQueryDTO> queryParams= new Page<>();
        queryParams.setParams(dto);
        queryParams.setPageSize(INT_500);
        queryParams.setSearchCount(false);
        Integer page = 0;
        List<TechnicalChangeSnDTO> tempList = new ArrayList<>();
        while (page == 0 || tempList.size() >= INT_500) {
            page += 1;
            queryParams.setCurrent(page);
            tempList = technicalChangeHeadRepository.selectRetentionPage(queryParams);
            if (CollectionUtils.isEmpty(tempList)) {
                if (page == 1) {
                    throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SUBMIT_ALL_SN_NULL);
                }
                continue;
            }
            // 插入执行表逻辑
            // 添加字段，转换类型。
            List<TechnicalChangeExecInfo> transferResultList = transferEntityType(tempList,dto);
            scanSubmit(transferResultList);
        }
    }

    @Override
    public void downloadTemplate(HttpServletRequest request, HttpServletResponse response) throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat(MpConstant.DATE_FORMAT_YYYYMMDDHHMMSS);
        String fileName = sdf.format(new Date()) + Constant.TechnicalExport.FILE_NAME;
        List<TechnicalChangeSnDTO> list = new ArrayList<>();
        TechnicalChangeSnDTO dto = new TechnicalChangeSnDTO();
        list.add(dto);
        dto.setSn(STRING_EMPTY);
        dto.setChgReqNo(STRING_EMPTY);
        ImesExcelUtil.exportExcelModel(fileName, Constant.TechnicalExport.HEADER_LIST, list,
                Constant.TechnicalExport.PROPS,
                Constant.TechnicalExport.SHEETNAME, response);
    }

    /**
     * 文件解析
     */
    @Override
    public TechnicalChangeExcelAnalysisDTO excelAnalysis(TechnicalChangeExcelImportDTO importDto) throws Exception {
        MultipartFile file = importDto.getFile();
        //文件上传信息入库
        ResultData resultData = ExcelCommonUtils.resolveExcel(file.getInputStream(), TechnicalChangeSnDTO.class, Constant.TechnicalExport.PROPS);
        if (!com.zte.itp.msa.core.model.RetCode.SUCCESS_CODE.equals(resultData.getCode()) || resultData.getData() == null) {
            throw new MesBusinessException(com.zte.itp.msa.core.model.RetCode.BUSINESSERROR_CODE, MessageId.FILE_EXCELANALYSIS_ERROR);
        }
        List<TechnicalChangeSnDTO> excelInfoList = (List<TechnicalChangeSnDTO>) resultData.getData();
        if (org.springframework.util.CollectionUtils.isEmpty(excelInfoList)) {
            throw new MesBusinessException(com.zte.itp.msa.core.model.RetCode.BUSINESSERROR_CODE, MessageId.FILE_EMPTY);
        }
        TechnicalChangeExcelAnalysisDTO technicalChangeExcelAnalysisDTO = verifyExcelInfoList(importDto, excelInfoList);
        technicalChangeExcelAnalysisDTO.setImportFileName(file.getOriginalFilename());
        return technicalChangeExcelAnalysisDTO;
    }

    private int getUpperLimit() throws Exception {
        // 查询字典获取一次导入的上限
        SysLookupTypesDTO sysLookupTypesDTO = BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_6826, Constant.LOOKUP_CODE_6826001);
        if (sysLookupTypesDTO == null || !StringUtils.isNumeric(sysLookupTypesDTO.getLookupMeaning())) {
            return INT_1000;
        }
        return Integer.parseInt(sysLookupTypesDTO.getLookupMeaning());
    }

    /**
     * 校验导入的电子表格
     */
    private TechnicalChangeExcelAnalysisDTO verifyExcelInfoList(TechnicalChangeExcelImportDTO importDto, List<TechnicalChangeSnDTO> excelInfoList) throws Exception {
        int upperLimit = getUpperLimit();
        if (excelInfoList.size() > upperLimit) {
            throw new MesBusinessException(com.zte.itp.msa.core.model.RetCode.BUSINESSERROR_CODE, MessageId.BATCH_SCAN_NOT_MORETHAN_500,
                    new Object[]{upperLimit});
        }
        String sourceSys = importDto.getSourceSys();
        TechnicalChangeExcelAnalysisDTO res = new TechnicalChangeExcelAnalysisDTO();
        // 根据批次分组
        for (TechnicalChangeSnDTO detail : excelInfoList) {
            if (detail.getSn() != null && detail.getSn().length() == INT_12) {
                detail.setProdplanId(detail.getSn().substring(INT_0, INT_7));
            } else {
                detail.setProdplanId(FLAG_E);
            }
        }
        Map<String, List<TechnicalChangeSnDTO>> groupByProdplanId = excelInfoList.stream()
                .collect(Collectors.groupingBy(TechnicalChangeSnDTO::getProdplanId));
        for (Map.Entry<String, List<TechnicalChangeSnDTO>> entry : groupByProdplanId.entrySet()) {
            // 按照500分批处理
            List<TechnicalChangeSnDTO> entrySameProdplanId = new ArrayList(entry.getValue());
            List<List<TechnicalChangeSnDTO>> batchs = Lists.partition(entrySameProdplanId, INT_500);
            for (List<TechnicalChangeSnDTO> batch : batchs) {
                Map<String, TechnicalChangeSnDTO> detailMap = new HashMap<>();
                // 用来保存第一个不为空的ChgReqNo、ProdplanId 来对比后边的校验
                TechnicalChangeSnDTO checkParam = new TechnicalChangeSnDTO();
                for (TechnicalChangeSnDTO detail : batch) {
                    putDetailToMap(detailMap, detail, importDto.getChgReqNo(), importDto.getProdplanId(), checkParam);
                }
                checkDetail(detailMap, sourceSys);
            }
        }
        res.setDetailList(excelInfoList);
        return res;
    }

    private void putDetailToMap(Map<String, TechnicalChangeSnDTO> detailMap, TechnicalChangeSnDTO detail,
                                String chgReqNoParam, String prodplanIdParam, TechnicalChangeSnDTO checkParam) {
        if (StringUtils.isEmpty(detail.getChgReqNo())) {
            // 技改单号为空
            detail.setErrorMsg(CommonUtils.getLmbMessage(MessageId.TECHICAL_CHANGE_IS_NULL));
            return;
        }
        if (checkParam.getChgReqNo() == null) {
            checkParam.setChgReqNo(detail.getChgReqNo());
        } else if (!StringUtils.equals(detail.getChgReqNo(), checkParam.getChgReqNo())) {
            // 技改单号应为同一个
            detail.setErrorMsg(CommonUtils.getLmbMessage(MessageId.TECHICAL_CHANGE_BE_ONE));
            return;
        } else if (StringUtils.isNotBlank(chgReqNoParam) && !StringUtils.equals(detail.getChgReqNo(), checkParam.getChgReqNo())) {
            // 技改单号导入与界面不一致
            detail.setErrorMsg(CommonUtils.getLmbMessage(MessageId.TECHICAL_CHANGE_NOT_EQUAL));
            return;
        }
        if (StringUtils.isEmpty(detail.getSn())) {
            // 条码为空
            detail.setErrorMsg(CommonUtils.getLmbMessage(MessageId.SN_IS_NULL));
            return;
        }
        if (detailMap.containsKey(detail.getSn())) {
            // 条码重复
            detail.setErrorMsg(CommonUtils.getLmbMessage(MessageId.SN_IS_REPEAT, new String[]{detail.getSn()}));
            return;
        }
        if (detail.getSn().length() != INT_12) {
            // 条码应为12位
            detail.setErrorMsg(CommonUtils.getLmbMessage(MessageId.SN_MUST_BE_12_NUMBER));
            return;
        }
        String prodplanId = detail.getSn().substring(INT_0, INT_7);
        if (StringUtils.isNotBlank(prodplanIdParam) && !StringUtils.equals(prodplanIdParam, prodplanId)) {
            // 批次导入与界面不一致
            detail.setErrorMsg(CommonUtils.getLmbMessage(MessageId.PRODPLANID_NOT_EQUAL));
            return;
        }
        detail.setProdplanId(prodplanId);
        detailMap.put(detail.getSn(), detail);
    }

    /**
     * 批量校验导入电子表格每一条
     */
    private void checkDetail(Map<String, TechnicalChangeSnDTO> detailMap, String sourceSys) throws Exception {
        if (detailMap.isEmpty()) {
            return;
        }
        // 取第一个 chgReqNo、prodplanId都一样
        TechnicalChangeSnDTO first = detailMap.entrySet().iterator().next().getValue();
        // detailMap中存放待验证的，验证过的部分就去掉，继续走下边的验证
        // 校验是否已经有扫描记录
        checkHasRecord(detailMap, first);
        // 先看在wip_info表是否存在
        checkSnLocal(detailMap, first, sourceSys);
        // 处理条码中心存在的条码
        checkSnBarcodeCenter(detailMap, first, sourceSys);
    }

    /**
     * 校验是否已经有扫描记录
     */
    private void checkHasRecord(Map<String, TechnicalChangeSnDTO> detailMap, TechnicalChangeSnDTO first) {
        // 已经有了扫描记录,提示信息
        TechnicalChangeExecInfo technicalChangeQuery = new TechnicalChangeExecInfo();
        technicalChangeQuery.setSnList(new ArrayList<>(detailMap.keySet()));
        technicalChangeQuery.setProdplanId(first.getProdplanId());
        technicalChangeQuery.setChgReqNo(first.getChgReqNo());
        technicalChangeQuery.setFinishUnlock(NumConstant.NUM_ONE);
        List<TechnicalChangeExecInfo> execInfoList = technicalChangeExecInfoRepository.select(technicalChangeQuery);
        for (TechnicalChangeExecInfo p : execInfoList) {
            TechnicalChangeSnDTO detail = detailMap.get(p.getSn());
            if (detail == null) {
                continue;
            }
            detail.setErrorMsg(CommonUtils.getLmbMessage(MessageId.TECHNICAL_EXEC_INFO_EXISTED, new String[]{p.getSn()}));
            detailMap.remove(p.getSn());
        }
    }

    /**
     * 校验处理wip_info中存在的条码
     */
    private void checkSnLocal(Map<String, TechnicalChangeSnDTO> detailMap, TechnicalChangeSnDTO first, String sourceSys) {
        if (detailMap.isEmpty()) {
            return;
        }
        // 先看在wip_info表是否存在
        List<String> wipInfoSnList = psWipInfoRepository.checkSnExist(detailMap.keySet());
        // 从wip_info找到的条码
        Set<String> snFromwip = new HashSet<>();
        for (String wipInfoSn : wipInfoSnList) {
            TechnicalChangeSnDTO detail = detailMap.get(wipInfoSn);
            if (detail == null) {
                continue;
            }
            snFromwip.add(wipInfoSn);
        }
        // 查询技改信息
        Page<TechnicalChangeRetentionListQueryDTO> query = getQueryDto(first, snFromwip, sourceSys);
        List<TechnicalChangeSnDTO> technicalChangeSnDTOList = technicalChangeHeadRepository.selectRetentionPage(query);
        setProcessName(technicalChangeSnDTOList);
        // 找到技改信息的复制值返回
        for (TechnicalChangeSnDTO p : technicalChangeSnDTOList) {
            TechnicalChangeSnDTO detail = detailMap.get(p.getSn());
            if (detail == null) {
                continue;
            }
            copyToDetail(p, detail);
            detailMap.remove(p.getSn());
            snFromwip.remove(p.getSn());
        }
        // wip中存在，没有找到技改信息的报错
        for (String p : snFromwip) {
            TechnicalChangeSnDTO detail = detailMap.get(p);
            if (detail == null) {
                continue;
            }
            detail.setErrorMsg(CommonUtils.getLmbMessage(MessageId.THE_CHG_REQ_NO_IS_NOT_FOUND));
            detailMap.remove(p);
        }
    }

    /**
     * 处理条码中心存在的条码
     */
    private void checkSnBarcodeCenter(Map<String, TechnicalChangeSnDTO> detailMap, TechnicalChangeSnDTO first, String sourceSys) throws Exception {
        if (detailMap.isEmpty()) {
            return;
        }
        // 查询条码中心是否存在该条码
        BarcodeExpandQueryDTO barcodeQueryDTO = new BarcodeExpandQueryDTO();
        barcodeQueryDTO.setParentCategoryCode(MpConstant.SN_CODE);
        barcodeQueryDTO.setBarcodeList(new ArrayList<>(detailMap.keySet()));
        List<BarcodeExpandDTO> expandDTOList = barcodeCenterRemoteService.expandQuery(barcodeQueryDTO);
        Set<String> snFromBarcodeCenter = expandDTOList.stream().map(p -> p.getBarcode()).collect(Collectors.toSet());
        Iterator<Map.Entry<String, TechnicalChangeSnDTO>> it = detailMap.entrySet().iterator();
        while (it.hasNext()) {
            Map.Entry<String, TechnicalChangeSnDTO> entry = it.next();
            if (!snFromBarcodeCenter.contains(entry.getKey())) {
                // wip_info和条码中心都不存在 报错
                entry.getValue().setErrorMsg(CommonUtils.getLmbMessage(MessageId.SN_NULL, new String[]{entry.getKey()}));
                it.remove();
            }
        }
        if (detailMap.isEmpty()) {
            return;
        }
        // 查询技改信息
        Page<TechnicalChangeRetentionListQueryDTO> query = getQueryDto(first, detailMap.keySet(), sourceSys);
        List<TechnicalChangeSnDTO> technicalChangeSnDTORetentionList = technicalChangeHeadRepository.selectRetentionBarcodeCenter(query);
        // 找到技改信息的复制值返回
        for (TechnicalChangeSnDTO p : technicalChangeSnDTORetentionList) {
            TechnicalChangeSnDTO detail = detailMap.get(p.getSn());
            if (detail == null) {
                continue;
            }
            copyToDetail(p, detail);
            detailMap.remove(p.getSn());
        }
        // 条码中心存在，没有找到技改信息的报错
        for (TechnicalChangeSnDTO detail : detailMap.values()) {
            detail.setErrorMsg(CommonUtils.getLmbMessage(MessageId.THE_CHG_REQ_NO_IS_NOT_FOUND));
        }
    }

    private void copyToDetail(TechnicalChangeSnDTO source, TechnicalChangeSnDTO detail) {
        detail.setDetailId(source.getDetailId());
        detail.setSn(source.getSn());
        detail.setCurrProcess(source.getCurrProcess());
        detail.setCurrProcessName(source.getCurrProcessName());
        detail.setWorkOrderNo(source.getWorkOrderNo());
        detail.setChgReqNo(source.getChgReqNo());
        detail.setChgReqName(source.getChgReqName());
        detail.setProdplanId(source.getProdplanId());
        detail.setProductCode(source.getProductCode());
        detail.setProductName(source.getProductName());
        detail.setUnlockCraftSection(source.getUnlockCraftSection());
    }

    /**
     * 获取查询参数
     */
    private Page<TechnicalChangeRetentionListQueryDTO> getQueryDto(TechnicalChangeSnDTO first, Collection<String> snList, String sourceSys) {
        Page<TechnicalChangeRetentionListQueryDTO> query = new Page<>();
        query.setPageSize(INT_500);
        query.setCurrent(INT_1);
        TechnicalChangeRetentionListQueryDTO param = new TechnicalChangeRetentionListQueryDTO();
        query.setParams(param);
        param.setChgReqNo(first.getChgReqNo());
        param.setProdplanId(first.getProdplanId());
        if (CollectionUtils.isEmpty(snList)) {
            param.setSnList(Arrays.asList(Constant.E));
        } else {
            param.setSnList(new ArrayList<>(snList));
        }
        param.setSourceSys(sourceSys);
        return query;
    }

    private List<TechnicalChangeExecInfo> transferEntityType(List<TechnicalChangeSnDTO> tempList,
                                                             TechnicalChangeRetentionListQueryDTO dto) {
        List<TechnicalChangeExecInfo> resultList = new ArrayList<>();
        for (TechnicalChangeSnDTO entity: tempList) {
            TechnicalChangeExecInfo technicalChangeExecInfo = new TechnicalChangeExecInfo();
            technicalChangeExecInfo.setSn(entity.getSn());
            technicalChangeExecInfo.setCurrProcess(entity.getCurrProcess());
            technicalChangeExecInfo.setChgReqNo(entity.getChgReqNo());
            technicalChangeExecInfo.setProdplanId(entity.getProdplanId());
            technicalChangeExecInfo.setProductCode(entity.getProductCode());
            technicalChangeExecInfo.setUnlockType(dto.getUnlockType());
            technicalChangeExecInfo.setUnlockCraftSection(dto.getCraftSection());
            technicalChangeExecInfo.setRemark(dto.getRemark());
            technicalChangeExecInfo.setTechnicalDepartment(dto.getTechnicalDepartment());
            technicalChangeExecInfo.setTechnicalTeam(dto.getTechnicalTeam());
            resultList.add(technicalChangeExecInfo);
        }
        return resultList;
    }

    private void validateParamsOfTechSubmit(TechnicalChangeRetentionListQueryDTO dto) throws Exception {
        if (dto == null) {
            throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, MessageId.PARAM_IS_NULL);
        }
        if (StringUtils.isEmpty(dto.getChgReqNo())) {
            throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, MessageId.TECH_SUBMIT_ALL_CHG_NULL);
        }
        if (StringUtils.isEmpty(dto.getCraftSection())) {
            throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, MessageId.TECH_SUBMIT_ALL_CRAFT_SECTION_NULL);
        }
        if (StringUtils.isEmpty(dto.getProdplanId())) {
            throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, MessageId.TECH_SUBMIT_ALL_PROD_NULL);
        }
        if (StringUtils.isEmpty(dto.getProductCode())) {
            throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, MessageId.TECH_SUBMIT_ALL_PRODUCT_CODE_NULL);
        }
        if (StringUtils.isEmpty(dto.getUnlockType())) {
            throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, MessageId.TECH_SUBMIT_ALL_UNLOCK_TYPE_NULL);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertLockForRouteChange(OnlineRouteChangeHistoryDTO onlineRouteChangeHistoryDTO) {
        List<String> processCodes = onlineRouteChangeHistoryDTO.getAddProcessList();
        List<String> craftSections = onlineRouteChangeHistoryDTO.getAddSectionList();
        // 1.根据批次+锁定中状态查询条码锁定详情表
        // 2.将子工序记录到详情表中（备注工艺路径变更记录）
        handleBarcodeLockDetail(onlineRouteChangeHistoryDTO, processCodes);
        // 3.根据批次+锁定中状态查询技改锁定详情表
        // 4.将新增主工序记录到详情表中（备注工艺路径变更记录）
        handleTechnicalDetail(onlineRouteChangeHistoryDTO, craftSections);
    }

    /**
     * 在线工艺路径变更有新增主工序时，对应技改单的管控主工序也需要新增
     * @param technicalAndLockInfoDTO
     * @param craftSections
     */
    private void handleTechnicalDetail(OnlineRouteChangeHistoryDTO technicalAndLockInfoDTO, List<String> craftSections) {
        if (CollectionUtils.isEmpty(craftSections)) {
            return;
        }
        String newCraft = StringUtils.join(craftSections, COMMA);
        List<TechnicalChangeHeadDTO> technicalChangeHeadDTOList = technicalChangeHeadRepository.queryTechnicalInfoByProdPlanId(technicalAndLockInfoDTO.getProdplanId());
        List<TechnicalChangeDetailDTO> updateTechList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(technicalChangeHeadDTOList)) {
            List<List<TechnicalChangeDetailDTO>> detailList = technicalChangeHeadDTOList.stream().filter(d -> !CollectionUtils.isEmpty(d.getDetails()))
                    .map(TechnicalChangeHeadDTO::getDetails).collect(Collectors.toList());
            List<TechnicalChangeDetailDTO> list = new ArrayList<>();
            for (List<TechnicalChangeDetailDTO> detailDTOS : detailList) {
                list.addAll(detailDTOS);
            }
            for (TechnicalChangeDetailDTO technicalChangeDetailDTO : list) {
                String craftSection = technicalChangeDetailDTO.getCraftSection();
                if (!StringUtils.isEmpty(craftSection)) {
                    List<String> crafSectionOld = Lists.newArrayList(craftSection.split(COMMA));
                    craftSections.addAll(crafSectionOld);
                }
                craftSections = craftSections.stream().distinct().collect(Collectors.toList());
                craftSection = StringUtils.join(craftSections, COMMA);
                technicalChangeDetailDTO.setCraftSection(craftSection);
                technicalChangeDetailDTO.setRemark(ONLINE_ROUTE_CHANGE + newCraft);
                technicalChangeDetailDTO.setLastUpdatedBy(technicalAndLockInfoDTO.getCreateBy());
                updateTechList.add(technicalChangeDetailDTO);
            }
            technicalChangeHeadRepository.batchUpdateForRouteChange(updateTechList);
        }
    }

    /**
     * 在线工艺路径变更，有新增子工序的话,条码锁定单也需要新增子工序管控
     * @param onlineChangeHistoryDTO
     * @param processCodes
     */
    private void handleBarcodeLockDetail(OnlineRouteChangeHistoryDTO onlineChangeHistoryDTO, List<String> processCodes) {
        if (CollectionUtils.isEmpty(processCodes)) {
            return;
        }
        BarcodeLockDetail barcodeLockDetail = new BarcodeLockDetail();
        barcodeLockDetail.setBatchSn(onlineChangeHistoryDTO.getProdplanId());
        barcodeLockDetail.setStatus(Constant.LOCKING);
        barcodeLockDetail.setType(Constant.LOCK_TYPE_BATCH);
        List<BarcodeLockDetail> barcodeLockDetails = barcodeLockDetailRepository.getLockInfoSnByPlanAndStatus(barcodeLockDetail);
        List<BarcodeLockDetail> insertList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(barcodeLockDetails)) {
            // 获取是否为子卡批次
            Map<String, List<BarcodeLockDetail>> detailMap = barcodeLockDetails.stream().collect(Collectors.groupingBy(BarcodeLockDetail::getBillNo));
            for (Map.Entry<String, List<BarcodeLockDetail>> listEntry : detailMap.entrySet()) {
                String billNo = listEntry.getKey();
                List<BarcodeLockDetail> list = listEntry.getValue();
                BarcodeLockDetail barcodeTemp = list.get(NumConstant.NUM_ZERO);
                for (String processCode : processCodes) {
                    BarcodeLockDetail lockForInsert = new BarcodeLockDetail();
                    BeanUtils.copyProperties(barcodeTemp, lockForInsert);
                    lockForInsert.setId(UUID.randomUUID().toString());
                    lockForInsert.setLockProcessCode(processCode);
                    lockForInsert.setCreateBy(onlineChangeHistoryDTO.getCreateBy());
                    lockForInsert.setLastUpdatedBy(onlineChangeHistoryDTO.getCreateBy());
                    lockForInsert.setRemark(ONLINE_ROUTE_CHANGE);
                    lockForInsert.setBillNo(billNo);
                    insertList.add(lockForInsert);
                }
            }
            barcodeLockDetailRepository.batchInsertForRouteChange(insertList);
        }
    }
}
