package com.zte.application.infordt.impl;

import com.zte.domain.model.infordt.EdiOrderStatusRepository;
import com.zte.domain.model.infordt.EdiOrderStatusViewDTO;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.Collections;

@RunWith(PowerMockRunner.class)
public class EdiOrderStatusViewServiceImplTest {

    @InjectMocks
    private EdiOrdersStatusViewServiceImpl service;
    @Mock
    private EdiOrderStatusRepository ediOrderStatusRepository;

    @Before
    public void init(){

    }


    @Test
    public void test_queryStatusByCondition() {
        EdiOrderStatusViewDTO record = new EdiOrderStatusViewDTO();
        Assert.assertTrue(service.queryStatusByCondition(record).isEmpty());
        record.setExternalorderkey2List(Collections.singletonList("1111"));
        Assert.assertTrue(service.queryStatusByCondition(record).isEmpty());
        record.setWhseId("WMWHSE1");
        PowerMockito.when(ediOrderStatusRepository.queryStatusByCondition(Mockito.any())).thenReturn(Collections.emptyList());
        Assert.assertTrue(service.queryStatusByCondition(record).isEmpty());
    }

}

