<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.datawb.CodeDetailInfoRepository">

    <resultMap id="FinalSelectResult" type="com.zte.domain.model.datawb.CodeDetailInfo">
        <result column="entityname" jdbcType="VARCHAR" property="entityName" />
        <result column="itemcode" jdbcType="VARCHAR" property="itemCode" />
        <result column="itemname" jdbcType="VARCHAR" property="itemName" />
        <result column="reqnum" jdbcType="VARCHAR" property="reqNum" />
        <result column="issuednum" jdbcType="VARCHAR" property="issuedNum" />
        <result column="missednum" jdbcType="VARCHAR" property="missedNum" />
        <result column="currnum" jdbcType="VARCHAR" property="currNum" />
    </resultMap>

    <resultMap id="FinalUserAddress" type="com.zte.domain.model.datawb.EntityAndAddress">
        <result column="ENTITY_NAME" jdbcType="VARCHAR" property="entityName" />
        <result column="CONTRACT_NUMBER" jdbcType="VARCHAR" property="contractNumber" />
        <result column="USER_ADDRESS" jdbcType="VARCHAR" property="userAddress" />
    </resultMap>


    <select id="selectFinalResult"  resultMap="FinalSelectResult" parameterType="java.lang.String">
        select MSI.SEGMENT1 as itemcode , MSI.DESCRIPTION as itemname,ROUND (NVL (A.REQUIRED_QUANTITY, 0), 4) as reqnum,ROUND (NVL (A.QUANTITY_ISSUED, 0), 4) as issuednum,
        ROUND (NVL (A.REQUIRED_QUANTITY, 0)- NVL (A.QUANTITY_ISSUED, 0),4) as missednum,C.WIP_ENTITY_NAME as entityname, 0 as currnum  from  WIP.WIP_REQUIREMENT_OPERATIONS A,
        WIP.WIP_ENTITIES C,APPS.MTL_SYSTEM_ITEMS MSI where A.WIP_ENTITY_ID = C.WIP_ENTITY_ID AND A.INVENTORY_ITEM_ID = MSI.INVENTORY_ITEM_ID AND A.ORGANIZATION_ID = MSI.ORGANIZATION_ID  and C.WIP_ENTITY_NAME in
        <foreach collection="list" item="item" index="index" open="("
                 separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        GROUP BY
        A.REQUIRED_QUANTITY,A.QUANTITY_ISSUED,A.LAST_UPDATE_DATE, A.SUPPLY_SUBINVENTORY, MSI.SEGMENT1,MSI.DESCRIPTION,C.WIP_ENTITY_NAME order by C.WIP_ENTITY_NAME
    </select>

    <select id="selectUserAddress"  resultMap="FinalUserAddress" parameterType="java.lang.String">
        select A.ENTITY_NAME ENTITY_NAME,
        CCH.CONTRACT_NUMBER CONTRACT_NUMBER,
        A.USER_ADDRESS  from wmes.CPM_CONTRACT_ENTITIES A, WMES.CDM_CONTRACT_LINES ccl,WMES.CDM_CONTRACT_HEADERS CCH where A.contract_line_id=ccl.contract_line_id
        and CCL.CONTRACT_HEADER_ID = CCH.CONTRACT_HEADER_ID and A.ENTITY_NAME in
        <foreach collection="list" item="item" index="index" open="("
                 separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        order by A.ENTITY_NAME
    </select>

    <select id="getItemVersionBySegment1" resultType="java.lang.String">
        select
        MIR.REVISION
        from APPS.MTL_SYSTEM_ITEMS_B MSI, APPS.MTL_ITEM_REVISIONS_B MIR
        WHERE MSI.INVENTORY_ITEM_ID = MIR.INVENTORY_ITEM_ID
        AND MSI.ORGANIZATION_ID = MIR.ORGANIZATION_ID
        AND MSI.REVISION_QTY_CONTROL_CODE = '2'
        <if test="segment1 !=null and segment1 !=''">and MSI.SEGMENT1=#{segment1,jdbcType=VARCHAR}</if>
        <if test="segment1 ==null and segment1 ==''">and 1=2</if>
    </select>

    <select id="getItemVersionBatch" resultType="com.zte.domain.model.BaItem">
        select
        MIR.REVISION as item_revision, MSI.SEGMENT1 as item_no
        from APPS.MTL_SYSTEM_ITEMS_B MSI, APPS.MTL_ITEM_REVISIONS_B MIR
        WHERE MSI.INVENTORY_ITEM_ID = MIR.INVENTORY_ITEM_ID
        AND MSI.ORGANIZATION_ID = MIR.ORGANIZATION_ID
        AND MSI.REVISION_QTY_CONTROL_CODE = '2'
        and MSI.SEGMENT1 in
        <foreach collection="list" item="item" index="index" open="("
                 separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
</mapper>
