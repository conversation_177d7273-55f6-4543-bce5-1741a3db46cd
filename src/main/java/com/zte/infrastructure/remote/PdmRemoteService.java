package com.zte.infrastructure.remote;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.common.utils.NumConstant;
import com.zte.common.utils.StringWithChineseUtils;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.core.constant.SysGlobalConst;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.microservice.name.MicroServiceNameEum;
import com.zte.springbootframe.common.annotation.AlarmAnnotation;
import com.zte.springbootframe.common.annotation.RecordLogAnnotation;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 点对点调用DATAWB服务接口类
 * <AUTHOR>
 */
@Service
public class PdmRemoteService {

    private static final Logger logger = LoggerFactory.getLogger(PdmRemoteService.class);
    /**
     * 获取适用于PDM头信息
     * @param
     * @return
     */
    public static Map<String, String> getHeaderForPdm(Map<String, SysLookupTypesDTO> sysLookupTypesDTOMap)throws Exception {
        Map<String, String> header = MESHttpHelper.getHttpRequestHeader();
        header.put(SysGlobalConst.HTTP_HEADER_X_ORIGIN_SERVICE_NAME, Constant.IMES);
        if(StringUtils.isNotBlank(SysGlobalConst.HTTP_HEADER_X_TENANT_ID)){
            header.put(SysGlobalConst.HTTP_HEADER_X_TENANT_ID, Constant.HTTP_HEADER_X_TENANT_ID_VALUE);
        }
        SysLookupTypesDTO tokenDto = sysLookupTypesDTOMap.get(MpConstant.LOOKUP_6678004);
        if(tokenDto == null || StringUtils.isEmpty(tokenDto.getLookupMeaning())){
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SYS_LOOK_NOT_CONFIG,new Object[]{MpConstant.LOOKUP_6678004});
        }
        header.put(SysGlobalConst.HTTP_HEADER_X_AUTH_VALUE, tokenDto.getLookupMeaning());
        header.put(SysGlobalConst.HTTP_HEADER_X_AUTH_VALUE.toLowerCase(), tokenDto.getLookupMeaning());
        SysLookupTypesDTO systemCodeDto = sysLookupTypesDTOMap.get(MpConstant.LOOKUP_6678005);
        if(systemCodeDto == null || StringUtils.isEmpty(systemCodeDto.getLookupMeaning())){
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SYS_LOOK_NOT_CONFIG,new Object[]{MpConstant.LOOKUP_6678005});
        }
        header.put(MpConstant.X_SYSTEM_CODE, systemCodeDto.getLookupMeaning());
        return header;
    }

    /**
     * BOM关系服务
     *
     * @param dto
     * @return
     * @throws Exception
     */
    @RecordLogAnnotation("BOM关系服务")
    @AlarmAnnotation(alarmName = "call_pdm_error", alarmKey = "9907", alarmTitle = "调用PDM接口异常或超时", alarmTime = 30000)
    public PdmBomItemPageDTO<PdmBomItemResultDTO> bomItemQuery(PdmBomItemQueryDTO dto) throws Exception {
        List<SysLookupTypesDTO> sysLookupTypesDTOList = BasicsettingRemoteService.getSysLookUpValue(MpConstant.LOOKUP_6678);
        if(CollectionUtils.isEmpty(sysLookupTypesDTOList)){
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SYS_LOOK_NOT_CONFIG,new Object[]{MpConstant.LOOKUP_6678});
        }
        Map<String, SysLookupTypesDTO> sysLookupTypesDTOMap = sysLookupTypesDTOList.stream().collect(Collectors.toMap(e->e.getLookupCode().toString(), a -> a, (k1, k2) -> k1));
        SysLookupTypesDTO urlDto = sysLookupTypesDTOMap.get(MpConstant.LOOKUP_6678003);
        if(urlDto == null || StringUtils.isEmpty(urlDto.getLookupMeaning())){
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SYS_LOOK_NOT_CONFIG,new Object[]{MpConstant.LOOKUP_6678003});
        }
        Map<String, String> headerParamsMap = this.getHeaderForPdm(sysLookupTypesDTOMap);
        Map<String,Object> map=new HashMap<>();
        map.put("MSName", MicroServiceNameEum.PRODUCTIONMGMT);
        dto.setQueryType(MpConstant.B0001);
        dto.setQueryVer(MpConstant.V1);
        map.put("queryCondition", dto);
        map.put("pageNo",dto.getPageNo());
        map.put("pageSize",dto.getPageSize());
        String getResult = HttpRemoteUtil.remoteExeFoExternal(JacksonJsonConverUtil.beanToJson(map), headerParamsMap, urlDto.getLookupMeaning(), MicroServiceNameEum.SENDTYPEPOST);
        JSONObject json=JSON.parseObject(getResult);
        String boJson = checkJson(json);
        return (PdmBomItemPageDTO<PdmBomItemResultDTO>)JacksonJsonConverUtil.jsonToListBean(boJson,
                new TypeReference<PdmBomItemPageDTO<PdmBomItemResultDTO>>() {
                });
    }

    @RecordLogAnnotation("获取PDM模板清单")
    @AlarmAnnotation(alarmName = "call_pdm_error", alarmKey = "9907", alarmTitle = "调用PDM接口异常或超时", alarmTime = 30000)
    public List<TemplateInfoDTO> getPdmTemplateInfoList(PdmTemplateInfoQueryDTO pdmTemplateInfoQueryDTO) throws Exception {
        List<SysLookupTypesDTO> sysLookupTypesDTOList = BasicsettingRemoteService.getSysLookUpValue(MpConstant.LOOKUP_6678);
        if(CollectionUtils.isEmpty(sysLookupTypesDTOList)){
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SYS_LOOK_NOT_CONFIG,new Object[]{MpConstant.LOOKUP_6678});
        }
        Map<String, SysLookupTypesDTO> sysLookupTypesDTOMap = sysLookupTypesDTOList.stream().collect(Collectors.toMap(e->e.getLookupCode().toString(), a -> a, (k1, k2) -> k1));
        SysLookupTypesDTO urlDto = sysLookupTypesDTOMap.get(MpConstant.LOOKUP_6678007);
        if(urlDto == null || StringUtils.isEmpty(urlDto.getLookupMeaning())){
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SYS_LOOK_NOT_CONFIG,new Object[]{MpConstant.LOOKUP_6678007});
        }
        Map<String, String> headerParamsMap = getHeaderForPdm(sysLookupTypesDTOMap);
        Map<String,Object> map=new HashMap<>();
        map.put("queryCondition", pdmTemplateInfoQueryDTO);
        map.put("pageNo",pdmTemplateInfoQueryDTO.getPageNo());
        map.put("pageSize",pdmTemplateInfoQueryDTO.getPageSize());
        String getResult = HttpRemoteUtil.remoteExeFoExternal(JacksonJsonConverUtil.beanToJson(map), headerParamsMap, urlDto.getLookupMeaning(), MicroServiceNameEum.SENDTYPEPOST);
        JSONObject json=JSON.parseObject(getResult);
        String boJson = checkJson(json);
        List<TemplateInfoDTO> res=JSON.parseArray(boJson, TemplateInfoDTO.class);
        return res;
    }

    private String checkJson(JSONObject json) throws MesBusinessException {
        if (null == json) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.FAILED_TO_CALL_PDM);
        }
        String retCode = json.getJSONObject(Constant.CODE).get(Constant.CODE).toString();
        if (!RetCode.SUCCESS_CODE.equalsIgnoreCase(retCode)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.FAILED_TO_CALL_PDM, new Object[]{json.getJSONObject(Constant.CODE).get(Constant.MSG).toString()});
        }
        String boJson = json.getString(Constant.BO);
        if (StringUtils.isEmpty(boJson)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.FAILED_TO_CALL_PDM, new Object[]{json.getJSONObject(Constant.CODE).get(Constant.MSG).toString()});
        }
        return boJson;
    }

    /**
     * BOM基础信息获取服务
     *
     * @param dto
     * @return
     * @throws Exception
     */
    @RecordLogAnnotation("BOM基础信息获取服务")
    @AlarmAnnotation(alarmName = "call_pdm_error", alarmKey = "9907", alarmTitle = "调用PDM接口异常或超时", alarmTime = 30000)
    public List<PdmBomItemResultDTO> query(PdmBomItemResultDTO dto) throws Exception {
        if(StringUtils.isEmpty(dto.getNo())){
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PARMS_ERR);
        }
        List<SysLookupTypesDTO> sysLookupTypesDTOList = BasicsettingRemoteService.getSysLookUpValue(MpConstant.LOOKUP_6678);
        if(CollectionUtils.isEmpty(sysLookupTypesDTOList)){
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SYS_LOOK_NOT_CONFIG,new Object[]{MpConstant.LOOKUP_6678});
        }
        Map<String, SysLookupTypesDTO> sysLookupTypesDTOMap = sysLookupTypesDTOList.stream().collect(Collectors.toMap(e->e.getLookupCode().toString(), a -> a, (k1, k2) -> k1));
        SysLookupTypesDTO urlDto = sysLookupTypesDTOMap.get(MpConstant.LOOKUP_6678002);
        if(urlDto == null || StringUtils.isEmpty(urlDto.getLookupMeaning())){
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SYS_LOOK_NOT_CONFIG,new Object[]{MpConstant.LOOKUP_6678002});
        }
        Map<String, String> headerParamsMap = this.getHeaderForPdm(sysLookupTypesDTOMap);
        Map<String,Object> map=new HashMap<>();
        Map<String,Object> nosMap=new HashMap<>();
        nosMap.put("no", dto.getNo());
        nosMap.put("version", dto.getBomVersion());
        map.put("NOs", new Object[]{nosMap});
        Map<String,Object> infoBlocksMap=new HashMap<>();
        infoBlocksMap.put("block", MpConstant.B0000);
        infoBlocksMap.put("ver", MpConstant.V1);
        map.put("infoBlocks", new Object[]{infoBlocksMap});
        String getResult = HttpRemoteUtil.remoteExeFoExternal(JSON.toJSONString(map), headerParamsMap, urlDto.getLookupMeaning(), MicroServiceNameEum.SENDTYPEPOST);
        JSONObject json=JSON.parseObject(getResult);
        String boJson = checkJson(json);
        List<PdmBomItemResultDTO> res=(List<PdmBomItemResultDTO>)JSON.parseArray(boJson, PdmBomItemResultDTO.class);
        return CollectionUtils.isEmpty(res)?new ArrayList<>():res;
    }

    /**
     * 物料代码查询PDM最新版本的英文代号字段
     */
    @AlarmAnnotation(alarmName = "call_pdm_error", alarmKey = "9907", alarmTitle = "调用PDM接口异常或超时", alarmTime = 30000)
    public List<PdmBomItemResultDTO> queryEnCode(PdmBomItemResultDTO dto) throws Exception {
        if(StringUtils.isEmpty(dto.getNo())){
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PARMS_ERR);
        }
        List<SysLookupTypesDTO> sysList = BasicsettingRemoteService.getSysLookUpValue(MpConstant.LOOKUP_6678);
        if(CollectionUtils.isEmpty(sysList)){
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SYS_LOOK_NOT_CONFIG,new Object[]{MpConstant.LOOKUP_6678});
        }
        Map<String, SysLookupTypesDTO> lookupTypesDTOMap = sysList.stream().collect(Collectors.toMap(e->e.getLookupCode().toString(), a -> a, (k1, k2) -> k1));
        SysLookupTypesDTO url = lookupTypesDTOMap.get(MpConstant.LOOKUP_6678006);
        if(url == null || StringUtils.isEmpty(url.getLookupMeaning())){
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SYS_LOOK_NOT_CONFIG,new Object[]{MpConstant.LOOKUP_6678006});
        }
        Map<String, String> headerParamsMap = this.getHeaderForPdm(lookupTypesDTOMap);
        Map<String,Object> nosMap=new HashMap<>();
        Map<String,Object> queryMap=new HashMap<>();
        nosMap.put("no", dto.getNo());
        queryMap.put("NOs", new Object[]{nosMap});
        Map<String,Object> blockMap=new HashMap<>();
        blockMap.put("block", MpConstant.B0001);
        blockMap.put("ver", MpConstant.V1);
        queryMap.put("infoBlocks", blockMap);
        String result = HttpRemoteUtil.remoteExeFoExternal(JSON.toJSONString(queryMap), headerParamsMap, url.getLookupMeaning(), MicroServiceNameEum.SENDTYPEPOST);
        JSONObject json=JSON.parseObject(result);
        String boJson = checkJson(json);
        List<PdmBomItemResultDTO> list=(List<PdmBomItemResultDTO>)JSON.parseArray(boJson, PdmBomItemResultDTO.class);
        return CollectionUtils.isEmpty(list)?new ArrayList<>():list;
    }

    /**
     * 推送组装关系到pdm
     */
    @AlarmAnnotation(alarmName = "call_pdm_error", alarmKey = "9907", alarmTitle = "调用PDM接口异常或超时", alarmTime = 30000)
    public List<PDMSendInfoDTO> sendToPdm(List<PDMSendInfoDTO> pdmSendInfoDTOList, String url) throws MesBusinessException, Exception {
        try {
            Map<String, String> header = MESHttpHelper.getHttpRequestHeader();
            Map<String, Object> map = new HashMap<>();
            map.put("assemblyInfoList", pdmSendInfoDTOList);
            String params = JacksonJsonConverUtil.beanToJson(map);
            String result = HttpRemoteUtil.remoteExeFoExternal(params, header, url, MicroServiceNameEum.SENDTYPEPOST);
            JsonNode jsonNode = JacksonJsonConverUtil.getMapperInstance().readTree(result);
            if (null == jsonNode || jsonNode.get(MpConstant.JSON_BO) == null) {
                return pdmSendInfoDTOList;
            }
            String codeStr = jsonNode.get(Constant.STR_CODE).get(Constant.STR_CODE).asText();
            if (!Constant.SUCCESS_CODE.equals(codeStr)) {
                return pdmSendInfoDTOList;
            }
            String bo = jsonNode.get(MpConstant.JSON_BO).toString();
            PdmReceiveResultDTO resultDTO = JSON.parseObject(bo, PdmReceiveResultDTO.class);
            if (resultDTO == null) {
                return pdmSendInfoDTOList;
            }
            return resultDTO.getReceiveRespInfoList();
        } catch (Exception e) {
            //报错时也将错误信息写入记录表
            String message = e.getMessage() == null? "":e.getMessage();
            List<String> strList = StringWithChineseUtils.getStrList(message, NumConstant.NUM_250);
            String processMessage= org.apache.commons.collections.CollectionUtils.isEmpty(strList)?"":strList.get(NumConstant.NUM_ZERO);
            pdmSendInfoDTOList.forEach(p->{
                p.setSynMessage(processMessage);
            });
            return  pdmSendInfoDTOList;
        }
    }

    /**
     * 获取pdm英文代号
     * @param dto
     * @return
     * @throws Exception
     */
    @AlarmAnnotation(alarmName = "call_pdm_error", alarmKey = "9907", alarmTitle = "调用PDM接口异常或超时", alarmTime = 30000)
    public PdmPageDTO<PdmEnCodeDTO> getPdmEnCode(PdmEnCodeDTO dto) throws Exception {
        if (StringUtils.isEmpty(dto.getPartNo())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.ITEM_NO_NULL);
        }
        String url = BasicsettingRemoteService.getLookupMeaningByCode(MpConstant.LOOKUP_6678001);
        Map<String, String> headerParamsMap = MESHttpHelper.getHttpRequestHeader();
        Map<String,Object> map=new HashMap<>();
        map.put("queryCondition", dto);
        map.put("pageNo",dto.getPageNo());
        map.put("pageSize",dto.getPageSize());
        String getResult = HttpRemoteUtil.remoteExeFoExternal(JSON.toJSONString(map), headerParamsMap, url, MicroServiceNameEum.SENDTYPEPOST);

        ServiceData<PdmPageDTO<PdmEnCodeDTO>> serviceData = JacksonJsonConverUtil.jsonToListBean(getResult,
                new TypeReference<ServiceData<PdmPageDTO<PdmEnCodeDTO>>>() {
        });
        if (serviceData == null || serviceData.getCode() == null || serviceData.getBo() == null
                || !RetCode.SUCCESS_CODE.equalsIgnoreCase(serviceData.getCode().getCode())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.FAILED_TO_OBTAIN_PDM_ENGLISH_CODE);
        }
        return serviceData.getBo();
    }

    /**
     * 获取最新一条pdm英文代号
     *
     * @param dto
     * @return
     * @throws Exception
     */
    @AlarmAnnotation(alarmName = "call_pdm_error", alarmKey = "9907", alarmTitle = "调用PDM接口异常或超时", alarmTime = 30000)
    public PdmEnCodeDTO getLastPdmEnCode(PdmEnCodeDTO dto) throws Exception {
        if (StringUtils.isEmpty(dto.getPartNo())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.ITEM_NO_NULL);
        }
        List<PdmEnCodeDTO> pdmEnCodeDTOList = new ArrayList<>();
        dto.setPageNo(NumConstant.NUM_ONE);
        dto.setPageSize(NumConstant.NUM_100);
        PdmPageDTO<PdmEnCodeDTO> pdmPageDTO = this.getPdmEnCode(dto);
        if (CollectionUtils.isEmpty(pdmPageDTO.getData())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.FAILED_TO_OBTAIN_PDM_ENGLISH_CODE);
        }
        pdmEnCodeDTOList.addAll(pdmPageDTO.getData());
        int pageNo = NumConstant.NUM_ONE;
        boolean flag = true;
        while (flag && pdmPageDTO.getTotal() > NumConstant.NUM_100) {
            pageNo++;
            dto.setPageNo(pageNo);
            PdmPageDTO<PdmEnCodeDTO> pdmPageDTOTemp = this.getPdmEnCode(dto);
            pdmEnCodeDTOList.addAll(pdmPageDTOTemp.getData());
            if (CollectionUtils.isEmpty(pdmPageDTOTemp.getData())) {
                flag = false;
            }
        }
        //按最后更新时间排序，取最新
        PdmEnCodeDTO pdmEnCodeDTO = pdmEnCodeDTOList.stream().sorted(Comparator.comparing(PdmEnCodeDTO::getLastUpdateDate).reversed()).findFirst().orElse(null);
        return pdmEnCodeDTO;
    }
}
