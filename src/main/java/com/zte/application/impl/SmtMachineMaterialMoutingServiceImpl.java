package com.zte.application.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Lists;
import com.zte.application.BSmtBomDetailService;
import com.zte.application.IMESLogService;
import com.zte.application.ImesPDACommonService;
import com.zte.application.PkCodeInfoService;
import com.zte.application.PsWipInfoService;
import com.zte.application.SmtMachineMTLHistoryHService;
import com.zte.application.SmtMachineMaterialMoutingService;
import com.zte.application.SmtMachineMaterialPrepareService;
import com.zte.application.SmtMachineMtlOnlineStandbyService;
import com.zte.application.SmtSnMtlTracingTService;
import com.zte.application.WipScanHistoryService;
import com.zte.common.CommonUtils;
import com.zte.common.DateUtil;
import com.zte.common.SpringUtil;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.common.utils.NumConstant;
import com.zte.common.utils.RedisKeyConstant;
import com.zte.common.utils.SqlUtils;
import com.zte.common.utils.SymbolConstant;
import com.zte.consts.CommonConst;
import com.zte.domain.model.BBomDetail;
import com.zte.domain.model.BBomTechnicalChangeInfo;
import com.zte.domain.model.BSProcess;
import com.zte.domain.model.BSmtBomDetail;
import com.zte.domain.model.BSmtBomDetailRepository;
import com.zte.domain.model.CFLine;
import com.zte.domain.model.PkCodeInfo;
import com.zte.domain.model.PkCodeInfoRepository;
import com.zte.domain.model.PsEntityPlanBasic;
import com.zte.domain.model.PsWipInfo;
import com.zte.domain.model.PsWorkOrderSmt;
import com.zte.domain.model.SmtLocationInfo;
import com.zte.domain.model.SmtMachineMTLHistoryH;
import com.zte.domain.model.SmtMachineMTLHistoryHRepository;
import com.zte.domain.model.SmtMachineMTLHistoryL;
import com.zte.domain.model.SmtMachineMTLHistoryLRepository;
import com.zte.domain.model.SmtMachineMaterialMouting;
import com.zte.domain.model.SmtMachineMaterialMoutingRepository;
import com.zte.domain.model.SmtMachineMaterialPrepare;
import com.zte.domain.model.SmtMachineMaterialPrepareRepository;
import com.zte.domain.model.SmtMachineMtlOnlineStandby;
import com.zte.domain.model.SmtSnMtlTracingT;
import com.zte.domain.model.WipScanHistory;
import com.zte.domain.vo.EquipmentConsumeReelIdVO;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.CrafttechRemoteService;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.infrastructure.remote.ProductionDeliveryRemoteService;
import com.zte.infrastructure.remote.ProductionmgmtRemoteService;
import com.zte.interfaces.assembler.SmtMachineMaterialMoutingAssembler;
import com.zte.interfaces.assembler.SmtMachineMaterialPrepareAssembler;
import com.zte.interfaces.dto.AgeingInfoFencePointToPointQueryItemInfoDTO;
import com.zte.interfaces.dto.BBomDetailDTO;
import com.zte.interfaces.dto.BManufactureCapacityDTO;
import com.zte.interfaces.dto.BSmtBomDetailDTO;
import com.zte.interfaces.dto.ContainerContentInfoDTO;
import com.zte.interfaces.dto.DipBoardScanningDTO;
import com.zte.interfaces.dto.DipBoardScanningParamDTO;
import com.zte.interfaces.dto.EquipmentConsumeReelIdDTO;
import com.zte.interfaces.dto.OneKeySwitchMoutingDTO;
import com.zte.interfaces.dto.PDAReceiveItemsScanDTO;
import com.zte.interfaces.dto.PDATransferScanDTO;
import com.zte.interfaces.dto.PDAReceiveCheckItemDTO;
import com.zte.interfaces.dto.PkCodeInfoDTO;
import com.zte.interfaces.dto.PsWorkOrderBasicDTO;
import com.zte.interfaces.dto.PsWorkOrderDTO;
import com.zte.interfaces.dto.PsWorkOrderSmtDTO;
import com.zte.interfaces.dto.SMTScanDTO;
import com.zte.interfaces.dto.SMTScanParamDTO;
import com.zte.interfaces.dto.SmtMachineMaterialMoutingDTO;
import com.zte.interfaces.dto.SmtMachineMaterialPrepareDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.interfaces.dto.TaskMaterialIssueSeqEntityDTO;
import com.zte.interfaces.dto.WipScanHistoryDTO;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.itp.msa.util.string.StringHelper;
import com.zte.springbootframe.common.annotation.RecordLogAnnotation;
import com.zte.springbootframe.common.consts.SysConst;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.FactoryConfig;
import com.zte.springbootframe.common.redis.RedisLock;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.RequestHeadValidationUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collection;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.zte.common.utils.Constant.INT_0;
import static com.zte.common.utils.MpConstant.*;

/**
 * // TODO 添加类/接口功能描述
 *
 * <AUTHOR>
 **/
@Service
public class SmtMachineMaterialMoutingServiceImpl implements SmtMachineMaterialMoutingService {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Autowired
    private SmtMachineMaterialPrepareService smtMachineMaterialPrepareService;

    @Autowired
    private SmtMachineMtlOnlineStandbyService smtMachineMtlOnlineStandbyService;

    @Autowired
    private SmtMachineMaterialMoutingRepository smtMachineMaterialMoutingRepository;
    @Autowired
    private PkCodeInfoRepository pkCodeInfoRepository;

    @Autowired
    private SmtMachineMTLHistoryHService smtMachineMTLHistoryHService;

    @Autowired
    private PkCodeInfoService pkCodeInfoService;

    @Autowired
    private PsWipInfoService psWipInfoService;

    @Autowired
    private BSmtBomDetailService bSmtBomDetailService;

    @Autowired
    private PsScanMaterailServiceImpl psScanMaterialService;

    @Autowired
    private SmtSnMtlTracingTService smtSnMtlTracingTService;

    @Autowired
    private WipScanHistoryService wipScanHistoryService;

    @Autowired
    private CenterfactoryRemoteService centerfactoryRemoteService;
    @Autowired
    private SmtMachineMaterialPrepareRepository smtMachineMaterialPrepareRepository;
    @Autowired
    private ImesPDACommonService imesPDACommonService;

    @Autowired
    private FactoryConfig factoryConfig;

    @Autowired
    private IMESLogService imesLogService;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private BSmtBomDetailRepository smtBomDetailRepository;

    @Autowired
    private SmtMachineMTLHistoryHRepository historyHRepository;

    @Autowired
    private SmtMachineMTLHistoryLRepository historyLRepository;

    @Autowired
    private DatawbRemoteService datawbRemoteService;

    private static final String CFG_HEADER_ID = MpConstant.CFG_HEADERID;
    private static final String LOCATION_NO = "locationNo";
    private static final String LAST_UPDATED_DATE = "lastUpdatedDate";

    public void setSmtMachineMaterialMoutingRepository(SmtMachineMaterialMoutingRepository smtMachineMaterialMoutingRepository) {
        this.smtMachineMaterialMoutingRepository = smtMachineMaterialMoutingRepository;
    }

    /**
     * 增加实体数据
     *
     * @param record
     **/
    @Override
    public int insertSmtMachineMaterialMouting(SmtMachineMaterialMouting record) {
        return smtMachineMaterialMoutingRepository.insertSmtMachineMaterialMouting(record);
    }

    /**
     * 获取当前线体+SMT指令有效机台在用,可用数量来自pk_code_info
     *
     * @param record
     **/
    @Override
    public List<SmtMachineMaterialMouting> getMountingByLineAndWorkOrder(SmtMachineMaterialMouting record) {

        List<SmtMachineMaterialMouting> list = smtMachineMaterialMoutingRepository.selectByLineAndWorkOrderNo(record.getLineCode(), record.getWorkOrder());
        if (CollectionUtils.isEmpty(list)) {
            return list;
        }
        List<String> pkCodes = list.stream().map(SmtMachineMaterialMouting::getObjectId).distinct().collect(Collectors.toList());
        Map<String, BigDecimal> moutingMap = new HashMap<>();
        List<PkCodeInfo> pkCodeInfoList = new ArrayList<>();
        CommonUtils.splitList(pkCodes, Constant.IN_MAX_BATCH_SIZE).forEach(p -> {
            List<PkCodeInfo> tempList = pkCodeInfoRepository.getListByPkCodes(p);
            if (!CollectionUtils.isEmpty(tempList)) {
                pkCodeInfoList.addAll(tempList);
            }
        });
        moutingMap = pkCodeInfoList.stream().collect(Collectors.toMap(PkCodeInfo::getPkCode, PkCodeInfo::getItemQty, (k1, k2) -> k1));
        for (SmtMachineMaterialMouting mouting : list) {
            BigDecimal moutingQty = moutingMap.get(mouting.getObjectId()) == null ? new BigDecimal("0") : moutingMap.get(mouting.getObjectId());
            mouting.setQty(moutingQty);
        }

        return list;
    }

    /**
     * d点对点调用箱内容
     *
     * @param map object_id
     **/

    private List<ContainerContentInfoDTO> getContainerContentInfo(Map map) {
        JsonNode json = ProductionDeliveryRemoteService.getContainerContentInfo(map);
        if (json != null && json.get(MpConstant.JSON_BO) != null) {
            String bo = json.get(MpConstant.JSON_BO).toString();
            List<ContainerContentInfoDTO> list = (List<ContainerContentInfoDTO>) JacksonJsonConverUtil.jsonToListBean(bo,
                    new TypeReference<ArrayList<ContainerContentInfoDTO>>() {
                    });
            return list;
        }
        return new ArrayList<ContainerContentInfoDTO>();
    }

    /**
     * 根据主键删除实体数据
     *
     * @param record
     **/
    @Override
    public int deleteSmtMachineMaterialMoutingById(SmtMachineMaterialMouting record) {
        return smtMachineMaterialMoutingRepository.deleteSmtMachineMaterialMoutingById(record);
    }


    /**
     * 将机台在用表信息插入提前备料表 wl
     * 产策略为4时，再用表数据搬到smt提前备料表，位号为空，forward=“to 挂起”,删除再用表数据
     * 1.去向清空后，查询机台在用表，得到机台在用信息
     * 2. 遍历机台在用信息，产生提前备料信息实体，添加到列表中。
     * 3. 数据批量插入到提前备料表。
     * 4. 物理删除机台在用信息。
     *
     * @param
     **/
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertSMTPrepare(SmtMachineMaterialMouting dto) {
        String sforward = dto.getForward().trim();
        dto.setForward("");
        List<SmtMachineMaterialMouting> smtMachineMaterialMoutingList = smtMachineMaterialMoutingRepository
                .selectByWorkOrderNO(dto);
        if (CollectionUtils.isEmpty(smtMachineMaterialMoutingList)) {
            return INT_0;
        }
        List<String> preIdDel = new ArrayList<>();
        List<SmtMachineMaterialPrepareDTO> list = new ArrayList<>();
        for (SmtMachineMaterialMouting smtmt : smtMachineMaterialMoutingList) {
            SmtMachineMaterialPrepareDTO entity = new SmtMachineMaterialPrepareDTO();
            entity.setMtlPrepareId(java.util.UUID.randomUUID().toString());
            entity.setWorkOrder(smtmt.getWorkOrder());
            entity.setItemCode(smtmt.getItemCode());
            entity.setLineCode(smtmt.getLineCode());
            entity.setLocationNo(smtmt.getLocationNo());
            entity.setForward(sforward);
            entity.setMachineNo(smtmt.getMachineNo());
            entity.setTrackNo(smtmt.getTrackNo());
            entity.setFeederNo(smtmt.getFeederNo());
            entity.setObjectId(smtmt.getObjectId());
            entity.setQty(smtmt.getQty());
            entity.setModuleNo(smtmt.getModuleNo());
            entity.setCreateUser(smtmt.getLastUpdatedBy());
            entity.setLastUpdatedBy(smtmt.getLastUpdatedBy());
            entity.setOrgId(smtmt.getOrgId());
            entity.setFactoryId(smtmt.getFactoryId());
            entity.setEntityId(smtmt.getEntityId());
            entity.setIsLead(smtmt.getIsLead());
            entity.setAvl(smtmt.getAvl());
            entity.setPolarInfo(smtmt.getPolarInfo());
            // 存在续料盘则将续料写入prepare表,盘旧reelid保存在原料盘字段
            if (StringUtils.isNotBlank(smtmt.getNextReelRowid())) {
                entity.setObjectId(smtmt.getNextReelRowid());
                entity.setOldReelId(smtmt.getObjectId());
                // 续料盘接料生成的备料记录需删除
                preIdDel.add(smtmt.getNextReelRowid());
            }
            list.add(entity);
        }
        // 数据插入提前备料表
        int count = 0;
        imesLogService.log(new Object[]{smtMachineMaterialMoutingList, list}, "smtHungupProcess");
        if (CollectionUtils.isEmpty(list)) {
            return count;
        }
        // 续料盘接料生成的备料记录需删除
        delNextPre(preIdDel);
        List<List<SmtMachineMaterialPrepareDTO>> listOfList = CommonUtils.splitList(list, CommonConst.BATCH_SIZE);
        if (!CollectionUtils.isEmpty(listOfList)) {
            for (List<SmtMachineMaterialPrepareDTO> paramsList : listOfList) {
                count += smtMachineMaterialPrepareService.insertSmtMachineMaterialPrepareBatch(paramsList);
            }
        }
        if (count > 0) {
            // 数据从机台在用表删除
            List<List<SmtMachineMaterialMouting>> listOfList2 = CommonUtils.splitList(smtMachineMaterialMoutingList, CommonConst.BATCH_SIZE);
            if (!CollectionUtils.isEmpty(listOfList2)) {
                for (List<SmtMachineMaterialMouting> paramsList : listOfList2) {
                    smtMachineMaterialMoutingRepository.deleteSmtMachineMaterialMoutingByIdBatch(paramsList);
                }
            }
        }
        return count;
    }

    private void delNextPre(List<String> preIdDel) {
        if (!CollectionUtils.isEmpty(preIdDel)) {
            List<List<String>> delList = CommonUtils.splitList(preIdDel, CommonConst.BATCH_SIZE);
            for (List<String> del : delList) {
                smtMachineMaterialPrepareRepository.deleteByObjectId(del);
            }
        }
    }

    /**
     * wl
     * 指令表全不同的部分，策略=2时，修改在用表，插入备料表，删除在用表。
     * List<Map<String, Object>> returnlist
     *
     * @param ?test
     **/
    @Override
    public int updateMountingDiffByline2tr(List<SmtMachineMaterialMouting> dto) {
        int count = NumConstant.NUM_ZERO;
        for (SmtMachineMaterialMouting smtmt1 : dto) {
            List<SmtMachineMaterialMouting> temp = smtMachineMaterialMoutingRepository
                    .selectSmtMachineMaterialMoutingSelective(smtmt1);
            List<SmtMachineMaterialPrepare> prelist = new ArrayList<>();
            if (!temp.isEmpty() && temp.size() > 0) {
                for (SmtMachineMaterialMouting smtmt : temp) {
                    SmtMachineMaterialPrepare entity = new SmtMachineMaterialPrepare();
                    entity.setMtlPrepareId(java.util.UUID.randomUUID().toString());
                    entity.setWorkOrder(smtmt.getWorkOrder());
                    entity.setItemCode(smtmt.getItemCode());
                    entity.setLineCode(smtmt.getLineCode());
                    entity.setLocationNo(" ");
                    entity.setForward(MpConstant.TO_HANG_UP);
                    entity.setMachineNo(smtmt.getMachineNo());
                    entity.setTrackNo(smtmt.getTrackNo());
                    entity.setFeederNo(smtmt.getFeederNo());
                    entity.setObjectId(smtmt.getObjectId());
                    entity.setQty(smtmt.getQty());
                    entity.setModuleNo(smtmt.getModuleNo());
                    entity.setCreateUser(smtmt.getLastUpdatedBy());
                    entity.setLastUpdatedBy(smtmt.getLastUpdatedBy());
                    entity.setOrgId(smtmt.getOrgId());
                    entity.setFactoryId(smtmt.getFactoryId());
                    entity.setEntityId(smtmt.getEntityId());
                    entity.setIsLead(smtmt.getIsLead());
                    entity.setAvl(smtmt.getAvl());
                    entity.setPolarInfo(smtmt.getPolarInfo());
                    prelist.add(entity);
                }
            }
            List<SmtMachineMaterialPrepareDTO> list = SmtMachineMaterialPrepareAssembler
                    .toSmtMachineMaterialPrepareDTOList(prelist);
            // 数据插入提前备料表

            if (!list.isEmpty() && list.size() > 0) {
                count = smtMachineMaterialPrepareService.insertSmtMachineMaterialPrepareBatch(list);
                if (count > 0) {
                    // 数据从机台在用表删除
                    count = smtMachineMaterialMoutingRepository.deleteSmtMachineMaterialMoutingByOther(smtmt1);//?要做批量删除
                }
            }

        }

        return count;
    }


    /**
     * wl 两条指令不同的部分，=3时，修改在用表，备料表，提前备料表。de forward =to挂起 List<Map<String,
     * Object>> returnlist
     *
     * @param ?test
     **/
    @Override
    public int updateMountingDiffByline3tr(List<SmtMachineMaterialMouting> dto) {
        int count1 = 0, count2 = 0, count3 = 0;
        for (SmtMachineMaterialMouting smtmt : dto) {
            // 更新在用表
            smtmt.setForward(MpConstant.TO_WAREHOUSE);
            smtmt.setEnabledFlag(CommonConst.ENABLE_FLAG_N);
            count1 = smtMachineMaterialMoutingRepository.updateSmtMachineMaterialMoutingByOthers(smtmt);

            // 更新提前备料
            SmtMachineMaterialPrepare smp = new SmtMachineMaterialPrepare();
            smp.setWorkOrder(smtmt.getWorkOrder());
            smp.setLocationNo(smtmt.getLocationNo());
            smp.setItemCode(smtmt.getItemCode());
            smp.setForward(MpConstant.TO_WAREHOUSE);
            smp.setEnabledFlag(CommonConst.ENABLE_FLAG_N);
            count2 = smtMachineMaterialPrepareService.updateSmtMachineMaterialPrepareByOthers(smp);

            // 更新提机台备用表wl
            SmtMachineMtlOnlineStandby smos = new SmtMachineMtlOnlineStandby();
            smos.setWorkOrder(smtmt.getWorkOrder());
            smos.setLocationNo(smtmt.getLocationNo());
            smos.setItemCode(smtmt.getItemCode());
            smos.setForward(MpConstant.TO_WAREHOUSE);
            smos.setEnabledFlag(CommonConst.ENABLE_FLAG_N);
            count3 = smtMachineMtlOnlineStandbyService.updateSmtMachineMtlOnlineStandbyByOthers(smos);

        }
        if (count1 > 0 && count2 > 0 && count3 > 0) {
            return Constant.INT_1;
        } else {
            return INT_0;
        }
    }


    /**
     * wl 部分相同，全部相同，修改在用表，插入提前备料表
     * returnlist
     *
     * @param
     **/
    @Override
    public int updateMountingTheSameByline1tr(List<SmtMachineMaterialMouting> dto, int flag) {
        int count = NumConstant.NUM_ZERO;
        if (CollectionUtils.isEmpty(dto)) {
            return INT_0;
        }
        List<SmtMachineMaterialPrepare> listpaper = new ArrayList<>();
        for (SmtMachineMaterialMouting smtmt : dto) {

            SmtMachineMaterialMouting en = new SmtMachineMaterialMouting();

            en.setWorkOrder(smtmt.getWorkOrder());
            en.setItemCode(smtmt.getItemCode());
            en.setLocationNo(smtmt.getLocationNo());

            // entity.setLastUpdatedBy(worknoN);//借用该字段，存放下条指令N号
            String sworkN = smtmt.getLastUpdatedBy().trim();
            String sMachineNo = "", sLocationNo = "";
            if (UPDATE_MOUNTING_FLAG_TWO == flag) {// bb.setCreateUser(wip.getMachineNo());//机器号
                // bb.setItemName(wip.getLocationNo());//位号
                sMachineNo = smtmt.getCreateUser();
                sLocationNo = smtmt.getItemName();
            }

            List<SmtMachineMaterialMouting> llist = smtMachineMaterialMoutingRepository
                    .selectSmtMachineMaterialMoutingSelective(en);
            //count = llist.size();
            if (!CollectionUtils.isEmpty(llist)) {

                for (SmtMachineMaterialMouting lis : llist) {

                    lis.setWorkOrder(sworkN);
                    lis.setCreateDate(new Date());
                    lis.setRemark(MpConstant.REMARK_N);
                    if (flag == UPDATE_MOUNTING_FLAG_TWO) {// bb.setCreateUser(wip.getMachineNo());//机器号
                        // bb.setItemName(wip.getLocationNo());//位号
                        lis.setMachineNo(sMachineNo);
                        lis.setLocationNo(sLocationNo);
                    }

                    smtMachineMaterialMoutingRepository.updateSmtMachineMaterialMoutingByIdSelective(lis);

                    SmtMachineMaterialPrepare smp = new SmtMachineMaterialPrepare();
                    smp.setMtlPrepareId(java.util.UUID.randomUUID().toString());
                    smp.setWorkOrder(lis.getWorkOrder());
                    smp.setItemCode(lis.getItemCode());
                    smp.setLineCode(lis.getLineCode());
                    smp.setLocationNo(lis.getLocationNo());
                    smp.setForward(lis.getForward());
                    smp.setMachineNo(lis.getMachineNo());
                    smp.setTrackNo(lis.getTrackNo());
                    smp.setFeederNo(lis.getFeederNo());
                    smp.setObjectId(lis.getObjectId());
                    smp.setQty(lis.getQty());
                    smp.setModuleNo(lis.getModuleNo());
                    smp.setCreateUser(lis.getLastUpdatedBy());
                    smp.setLastUpdatedBy(lis.getLastUpdatedBy());
                    smp.setOrgId(lis.getOrgId());
                    smp.setFactoryId(lis.getFactoryId());
                    smp.setEntityId(lis.getEntityId());
                    smp.setRemark(lis.getRemark());
                    smp.setIsLead(lis.getIsLead());
                    smp.setAvl(lis.getAvl());
                    smp.setPolarInfo(lis.getPolarInfo());
                    listpaper.add(smp);


                }
            }
        }
        List<SmtMachineMaterialPrepareDTO> listDTO = SmtMachineMaterialPrepareAssembler
                .toSmtMachineMaterialPrepareDTOList(listpaper);
        if (!CollectionUtils.isEmpty(listpaper)) {
            count = smtMachineMaterialPrepareService.insertSmtMachineMaterialPrepareBatch(listDTO);

        }
        return count;

    }


    /**
     * 根据一系列条件修改记录
     * wl
     *
     * @param record
     **/
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateSmtMachineMaterialMoutingfg(SmtMachineMaterialMouting record) {

        SmtMachineMaterialMouting je = new SmtMachineMaterialMouting();
        je.setWorkOrder(record.getWorkOrder());
        List<SmtMachineMaterialMouting> re = smtMachineMaterialMoutingRepository.selectSmtMachineMaterialMoutingSelective(je);
        if (!CollectionUtils.isEmpty(re)) {
            List<SmtMachineMaterialMouting> co = new ArrayList<>();
            for (SmtMachineMaterialMouting im : re) {
                im.setWorkOrder(record.getForward());// 借用该字段，存放的是下条指令号
                im.setRemark(record.getRemark());
                im.setMachineNo(record.getMachineNo());
                im.setLocationNo(record.getLocationNo());
                co.add(im);
            }

            // return smtMachineMaterialMoutingRepository.updateSmtMachineMaterialMoutingByIdBatch(co);
            // zzc
            int count = NumConstant.NUM_ZERO;
            List<List<SmtMachineMaterialMouting>> listOfList = CommonUtils.splitList(co, CommonConst.BATCH_SIZE);
            if (!CollectionUtils.isEmpty(listOfList)) {
                for (List<SmtMachineMaterialMouting> paramsList : listOfList) {
                    count += smtMachineMaterialMoutingRepository.updateSmtMachineMaterialMoutingByIdBatch(paramsList);
                }
            }
            return count;
        } else {
            return INT_0;
        }
    }

    /**
     * 根据主键更新实体数据
     *
     * @param record
     **/
    @Override
    public int updateSmtMachineMaterialMoutingByOthers(SmtMachineMaterialMouting record) {
        return smtMachineMaterialMoutingRepository.updateSmtMachineMaterialMoutingByOthers(record);
    }

    /**
     * Feeder替换更新机台在用
     *
     * @param record
     **/
    @Override
    public int updateMoutingByIdForFeederChange(SmtMachineMaterialMouting record) {
        return smtMachineMaterialMoutingRepository.updateMoutingByIdForFeederChange(record);
    }

    /**
     * 根据主键更新实体数据
     *
     * @param record
     **/
    @Override
    public int updateSmtMachineMaterialMoutingById(SmtMachineMaterialMouting record) {
        return smtMachineMaterialMoutingRepository.updateSmtMachineMaterialMoutingById(record);
    }


    /**
     * 根据主键查询实体信息
     *
     * @param record
     * @return SmtMachineMaterialMouting
     **/
    @Override
    public SmtMachineMaterialMouting selectSmtMachineMaterialMoutingById(SmtMachineMaterialMouting record) {
        return smtMachineMaterialMoutingRepository.selectSmtMachineMaterialMoutingById(record);
    }

    /**
     * 增加实体数据
     *
     * @param record
     * @return List<SmtMachineMaterialMouting>
     **/
    @Override
    public java.util.List<SmtMachineMaterialMouting> selectSmtMachineMaterialMoutingSelective(SmtMachineMaterialMouting record) {
        java.util.List<SmtMachineMaterialMouting> list = smtMachineMaterialMoutingRepository.selectSmtMachineMaterialMoutingSelective(record);
        try {
            this.setLineName(list);
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
        return list;
    }


    @Override
    public List<SmtMachineMaterialMoutingDTO> queryListWithWorkorderOnline(SmtMachineMaterialMouting entity) {
        return smtMachineMaterialMoutingRepository.queryListWithWorkorderOnline(entity);
    }

    @Override
    public List<SmtMachineMaterialMoutingDTO> queryListWithSmtBomHeader(SmtMachineMaterialMouting entity) {
        return smtMachineMaterialMoutingRepository.queryListWithSmtBomHeader(entity);
    }

    @Override
    public int countLocationMt(PDATransferScanDTO entity) {
        return smtMachineMaterialMoutingRepository.countLocationMt(entity);
    }

    @Override
    public List<SmtMachineMaterialMouting> getMoutingWithPkCode(String lineCode, String workOrderNo, List<String> machineNoList) {
        return smtMachineMaterialMoutingRepository.getMoutingWithPkCode(lineCode, workOrderNo, machineNoList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void receiveReelId(SmtMachineMaterialMouting mounting, PkCodeInfo nextReelPkCode) {
        mounting.setCreateDate(new Date());
        mounting.setLastUpdatedDate(new Date());
        mounting.setIsLead(nextReelPkCode.getIsLead());
        mounting.setItemCode(nextReelPkCode.getItemCode());
        mounting.setItemName(nextReelPkCode.getItemName());
        mounting.setLastUpdatedBy(nextReelPkCode.getLastUpdatedBy());
        mounting.setAvl(nextReelPkCode.getSysLotCode());
        mounting.setCreateUser(nextReelPkCode.getLastUpdatedBy());
        mounting.setQty(nextReelPkCode.getItemQty());
        mounting.setRamQty(nextReelPkCode.getRawQty());
        mounting.setForward(Constant.STR_EMPTY);
        mounting.setObjectId(nextReelPkCode.getPkCode());
        mounting.setNextReelRowid(Constant.STR_EMPTY);
        mounting.setSourceBatchCode(nextReelPkCode.getSourceBatchCode());
        // 更新机台在用
        smtMachineMaterialMoutingRepository.updateMoutingById(mounting);
        // 删除prepare数据
        smtMachineMaterialPrepareRepository.deletePrepareByObjectId(nextReelPkCode.getPkCode());
    }

    /**
     * 转化线体名称
     *
     * @param retList
     * @throws Exception
     */
    public void setLineName(List<SmtMachineMaterialMouting> retList) throws Exception {
        if (CollectionUtils.isEmpty(retList)) {
            return;
        }
        Set<String> lineSet = retList.stream().map(SmtMachineMaterialMouting::getLineCode).collect(Collectors.toSet());
        Map<String, String> lineMap = smtSnMtlTracingTService.getLineInfoMap(lineSet);
        if (lineMap == null) {
            return;
        }
        //循环设置线体名称
        retList.forEach(p -> {
            p.setLineName(lineMap.get(p.getLineCode()));
        });
    }

    @Override
    public SmtMachineMaterialMouting selectSmtMachineMaterialMoutingSelective(String objectId, String lineCode) {
        SmtMachineMaterialMouting record = new SmtMachineMaterialMouting();
        record.setObjectId(objectId);
        record.setLineCode(lineCode);
        List<SmtMachineMaterialMouting> smtMachineMaterialMoutings = smtMachineMaterialMoutingRepository.selectSmtMachineMaterialMoutingSelective(record);
        return CollectionUtils.isEmpty(smtMachineMaterialMoutings) ? null : smtMachineMaterialMoutings.get(NumConstant.NUM_ZERO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchInsertSmtMachineMaterialMouting(List<SmtMachineMaterialMoutingDTO> listMmm) {
        // return smtMachineMaterialMoutingRepository.batchInsertSmtMachineMaterialMouting(listMmm);
        // zzc
        int count = NumConstant.NUM_ZERO;
        List<List<SmtMachineMaterialMoutingDTO>> listOfList = CommonUtils.splitList(listMmm, CommonConst.BATCH_SIZE);
        if (!CollectionUtils.isEmpty(listOfList)) {
            for (List<SmtMachineMaterialMoutingDTO> paramsList : listOfList) {
                count += smtMachineMaterialMoutingRepository.batchInsertSmtMachineMaterialMouting(paramsList);
            }
        }
        return count;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchUpdateSmtMachineMaterialMouting(List<SmtMachineMaterialMouting> list) {
        // return smtMachineMaterialMoutingRepository.batchUpdateSmtMachineMaterialMouting(list);
        // zzc
        int count = NumConstant.NUM_ZERO;
        List<List<SmtMachineMaterialMouting>> listOfList = CommonUtils.splitList(list, CommonConst.BATCH_SIZE);
        if (!CollectionUtils.isEmpty(listOfList)) {
            for (List<SmtMachineMaterialMouting> paramsList : listOfList) {
                count += smtMachineMaterialMoutingRepository.batchUpdateSmtMachineMaterialMouting(paramsList);
            }
        }
        return count;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int pdaBatchUpdateSmtMachineMaterialMoutingBatch(List<SmtMachineMaterialMoutingDTO> list) {
        // return smtMachineMaterialMoutingRepository.pdaBatchUpdateSmtMachineMaterialMoutingBatch(list);
        // zzc
        int count = NumConstant.NUM_ZERO;
        List<List<SmtMachineMaterialMoutingDTO>> listOfList = CommonUtils.splitList(list, CommonConst.BATCH_SIZE);
        if (!CollectionUtils.isEmpty(listOfList)) {
            for (List<SmtMachineMaterialMoutingDTO> paramsList : listOfList) {
                count += smtMachineMaterialMoutingRepository.pdaBatchUpdateSmtMachineMaterialMoutingBatch(paramsList);
            }
        }
        return count;
    }

    @Override
    public long pdaSelectCountSmtMachineMaterialMouting(SmtMachineMaterialMoutingDTO record) {
        return smtMachineMaterialMoutingRepository.pdaSelectCountSmtMachineMaterialMouting(record);
    }

    @Override
    public long getCount(Map<String, Object> map) {
        return smtMachineMaterialMoutingRepository.getCount(map);
    }

    @Override
    public List<SmtMachineMaterialMouting> getList(Map<String, Object> map, String orderField, String order) throws Exception {
        List<SmtMachineMaterialMouting> list = null;
        if (orderField == null || orderField.isEmpty()) {
            orderField = MpConstant.ORDER_FIELD;
        }
        map.put("orderField", orderField);
        map.put("order", order);

        list = smtMachineMaterialMoutingRepository.getList(map);

        return list;
    }


    @Override
    public List<SmtMachineMaterialMouting> getPage(Map<String, Object> map, String orderField, String order, Long curPage,
                                                   Long pageSize) throws Exception {
        List<SmtMachineMaterialMouting> list = null;
        if (orderField == null || orderField.isEmpty()) {
            orderField = MpConstant.ORDER_FIELD;
        }
        map.put("orderField", orderField);
        map.put("order", order);

        long page = curPage < 1 ? 1 : curPage;
        long rows = pageSize < 1 ? Constant.INT_5000 : pageSize;
        map.put("startRow", (page - 1) * rows + 1);
        map.put("endRow", page * rows);

        list = smtMachineMaterialMoutingRepository.getPage(map);

        return list;
    }

    /**
     * 根据PkCodeInfo更新mounting数据
     *
     * @param mounting       SmtMachineMaterialMouting
     * @param nextReelPkCode 新料盘
     * @param curDate        当前日期
     * @param preReelId      当前pkCode
     * @return int
     */
    @Override
    public SmtMachineMaterialMouting updateSmtMachineMaterialMoutingFromPrepare(SmtMachineMaterialMouting mounting,
                                                                                PkCodeInfo nextReelPkCode, Date curDate, String preReelId) {
        mounting.setCreateDate(curDate);
        mounting.setLastUpdatedDate(curDate);
        mounting.setIsLead(nextReelPkCode.getIsLead());
        mounting.setItemCode(nextReelPkCode.getItemCode());
        mounting.setItemName(nextReelPkCode.getItemName());
        mounting.setLastUpdatedBy(nextReelPkCode.getLastUpdatedBy());
        mounting.setEntityId(nextReelPkCode.getEntityId());
        mounting.setFactoryId(nextReelPkCode.getFactoryId());
        mounting.setOrgId(nextReelPkCode.getOrgId());
        mounting.setAvl(nextReelPkCode.getSysLotCode());
        mounting.setCreateUser(nextReelPkCode.getLastUpdatedBy());
        mounting.setQty(nextReelPkCode.getItemQty());
        mounting.setRamQty(nextReelPkCode.getRawQty());
        mounting.setForward(Constant.STR_EMPTY);
        mounting.setObjectId(nextReelPkCode.getPkCode());
        mounting.setNextReelRowid(Constant.STR_EMPTY);
        mounting.setSourceBatchCode(nextReelPkCode.getSourceBatchCode());
        SmtMachineMaterialMoutingDTO updateMouningDto = SmtMachineMaterialMoutingAssembler.toDTO(mounting);
        updateMouningDto.setPreReelId(preReelId);
        updateMouningDto.setQty(nextReelPkCode.getItemQty());
        updateMouningDto.setRawQty(nextReelPkCode.getRawQty());
        updateMouningDto.setLineCode(null);
        updateMouningDto.setWorkOrder(null);
        smtMachineMaterialMoutingRepository.updateMoutingByPreReelSelectiveWithTime(updateMouningDto);
        return mounting;
    }

    public SmtMachineMaterialMouting updateSmtMtFromPrepare(SmtMachineMaterialMouting mounting,
                                                            PkCodeInfo nextReelPkCode, Date curDate, String preReelId) {
        mounting.setCreateDate(curDate);
        mounting.setItemCode(nextReelPkCode.getItemCode());
        mounting.setLastUpdatedDate(curDate);
        mounting.setItemName(nextReelPkCode.getItemName());
        mounting.setIsLead(nextReelPkCode.getIsLead());
        mounting.setLastUpdatedBy(nextReelPkCode.getLastUpdatedBy());
        mounting.setFactoryId(nextReelPkCode.getFactoryId());
        mounting.setEntityId(nextReelPkCode.getEntityId());
        mounting.setOrgId(nextReelPkCode.getOrgId());
        mounting.setCreateUser(nextReelPkCode.getLastUpdatedBy());
        mounting.setAvl(nextReelPkCode.getSysLotCode());
        mounting.setQty(nextReelPkCode.getItemQty());
        mounting.setForward(Constant.STR_EMPTY);
        mounting.setRamQty(nextReelPkCode.getRawQty());
        mounting.setObjectId(nextReelPkCode.getPkCode());
        mounting.setSourceBatchCode(nextReelPkCode.getSourceBatchCode());
        mounting.setNextReelRowid(null);
        SmtMachineMaterialMoutingDTO updateMouningDto = SmtMachineMaterialMoutingAssembler.toDTO(mounting);
        updateMouningDto.setLineCode(null);
        updateMouningDto.setWorkOrder(null);
        updateMouningDto.setQty(nextReelPkCode.getItemQty());
        updateMouningDto.setRawQty(nextReelPkCode.getRawQty());
        updateMouningDto.setPreReelId(preReelId);
        smtMachineMaterialMoutingRepository.updateMoutingByPreReel(updateMouningDto);
        return mounting;
    }

    @Override
    public List<SmtMachineMaterialMouting> getQtyInfo(List<SmtMachineMaterialMouting> list) throws Exception {
        for (int i = 0; i < list.size(); i++) {
            String objectId = list.get(i).getObjectId();
            String lpn = list.get(i).getLpn();
            PkCodeInfo entity = new PkCodeInfo();
            //判断是否为组合物料，是组合物料的话，原始数量取机台在用物料表原始数量，数量取箱内容表的数量
            // (如果箱内容表为空，数量取机台在用物料表)
            if (!StringHelper.isEmpty(list.get(i).getCombination()) && "Y".equals(list.get(i).getCombination())) {
                Map<String, String> containerMap = new HashMap<String, String>();
                containerMap.put("pkCode", objectId);
                containerMap.put("lpn", lpn);
                List<ContainerContentInfoDTO> container = getContainerContentInfo(containerMap);
                if (!CollectionUtils.isEmpty(container)) {
                    list.get(i).setQty(container.get(0).getContentQty());
                    list.get(i).setPkCodeInfoLastUpdatedDate(container.get(0).getLastUpdatedDate());
                } else {
//					list.get(i).setQyt(new BigDecimal(NumConstant.NUM_ZERO) );
                    list.get(i).setPkCodeInfoLastUpdatedDate(null);
                }
            } else {
                entity.setPkCode(objectId);
                PkCodeInfo returnEntity = pkCodeInfoService.selectPkCodeInfoById(entity);
                if (null != returnEntity) {
                    list.get(i).setQty(returnEntity.getItemQty());
                    list.get(i).setRamQty(returnEntity.getRawQty());
                    list.get(i).setPkCodeInfoLastUpdatedDate(returnEntity.getLastUpdatedDate());
                } else {
                    list.get(i).setQty(new BigDecimal(NumConstant.NUM_ZERO));
                    list.get(i).setRamQty(new BigDecimal(NumConstant.NUM_ZERO));
                    list.get(i).setPkCodeInfoLastUpdatedDate(null);
                }
            }
        }
        // 点对点调用接口——根据物料代码获取abc材类型
        this.queryAbcType(list);
        return list;
    }

    @Override
    public void addBgBrandNo(List<SmtMachineMaterialMouting> list) throws Exception {
        StringBuilder itemCode = new StringBuilder();
        Map<String, String> validMap = new HashMap<>();
        for (SmtMachineMaterialMouting dto : list) {
            if (StringHelper.isEmpty(validMap.get(dto.getSourceBatchCode()))) {
                validMap.put(dto.getSourceBatchCode(), dto.getSourceBatchCode());
                itemCode.append(SymbolConstant.SINGLE_MARK).append(dto.getSourceBatchCode()).append(SymbolConstant.SINGLE_MARK);
                itemCode.append(SymbolConstant.DOUHAO_CONSTANT);
            }
        }
        Map<String, Object> map = new HashMap<>();
        map.put("itemBarcodeList", itemCode);
        JsonNode jsonNode = DatawbRemoteService.getStItemBarcode(map);
        Map<String, Map<String, String>> colMap = new HashMap<>();
        if (null != jsonNode && jsonNode.get(SymbolConstant.STR_BO) != null) {
            colMap = JacksonJsonConverUtil.jsonToListBean(jsonNode.get(SymbolConstant.STR_BO).toString(),
                    new TypeReference<Map<String, Map<String, String>>>() {
                    });
        }
        for (SmtMachineMaterialMouting dto : list) {
            String sourceBatchCode = dto.getSourceBatchCode();
            if (null != colMap && null != sourceBatchCode) {
                Map<String, String> sourceBatchCodeMap = colMap.get(sourceBatchCode);
                if (null != sourceBatchCodeMap) {
                    dto.setBgBrandNo(sourceBatchCodeMap.get(Constant.BG_BRAND_NO));
                }
            }
        }
    }

    @Override
    public void addIssueSeqRelation(List<SmtMachineMaterialMouting> list) throws Exception {
        List<String> pkCodeList = list.stream().map(t -> t.getObjectId()).collect(Collectors.toList());
        // 查询pkCodeInfo
        List<PkCodeInfo> pkCodeInfoList = pkCodeInfoService.getListByPkCodes(pkCodeList);
        if (CollectionUtils.isEmpty(pkCodeInfoList)) {
            return;
        }
        // 查询发料信息list
        List<TaskMaterialIssueSeqEntityDTO> packSpecList = datawbRemoteService.getIsHasDirFlag(pkCodeInfoList);
        if (CollectionUtils.isEmpty(packSpecList)) {
            return;
        }
        // uuid + 供应商编码 为key, dto为value
        Map<String, TaskMaterialIssueSeqEntityDTO> uuidSupToDto = packSpecList.stream().collect(
                Collectors.toMap(k -> k.getSysLotCode() + k.getSupplerCode(), v -> v, (oldValue, newValue) -> newValue));
        for (PkCodeInfo entity : pkCodeInfoList) {
            TaskMaterialIssueSeqEntityDTO tempDTO = uuidSupToDto.get(entity.getSysLotCode() + entity.getSupplerCode());
            if (tempDTO == null) {
                continue;
            }
            entity.setIsDir(tempDTO.getIsDir());
            entity.setBraidDirection(tempDTO.getBraidDirection());
        }
        //添加极性相关字段
        Map<String, PkCodeInfo> pkCodeInfoMap = pkCodeInfoList.stream().collect(Collectors.toMap(k -> k.getPkCode(), v -> v, (oldValue, newValue) -> newValue));
        for (SmtMachineMaterialMouting smtMachineMaterialMouting : list) {
            PkCodeInfo pkCodeInfo = pkCodeInfoMap.get(smtMachineMaterialMouting.getObjectId());
            if (Objects.isNull(pkCodeInfo)) {
                continue;
            }
            smtMachineMaterialMouting.setIsDir(pkCodeInfo.getIsDir());
            smtMachineMaterialMouting.setBraidDirection(pkCodeInfo.getBraidDirection());
        }
    }

    /**
     * 通过物料代码查询基础物料信息，获取abc材料类型
     *
     * @param smtMachineMaterialMoutingList
     * @throws Exception
     */
    public void queryAbcType(List<SmtMachineMaterialMouting> smtMachineMaterialMoutingList) throws Exception {
        // 对查询到的机台用料List针对物料代码itemCode去重
        List<String> listString = smtMachineMaterialMoutingList.stream().filter(item -> StringUtils.isNotBlank(item.getItemCode()))
                .map(SmtMachineMaterialMouting::getItemCode).distinct().collect(Collectors.toList());
        List<AgeingInfoFencePointToPointQueryItemInfoDTO> itemInfoCollect = new ArrayList<>();
        for (String s : listString) {
            AgeingInfoFencePointToPointQueryItemInfoDTO itemInfoNew = new AgeingInfoFencePointToPointQueryItemInfoDTO();
            itemInfoNew.setItemNo(s);
            itemInfoCollect.add(itemInfoNew);
        }
        // 点对点调用——查询对应abcType
        List<AgeingInfoFencePointToPointQueryItemInfoDTO> reItemInfoList = new ArrayList<>();
        // 将需要点对点查询的参数列表分批处理
        List<List<AgeingInfoFencePointToPointQueryItemInfoDTO>> lists = CommonUtils.splitList(itemInfoCollect, NumConstant.NUM_500);
        for (List<AgeingInfoFencePointToPointQueryItemInfoDTO> splitList : lists) {
            reItemInfoList.addAll(BasicsettingRemoteService.getItemInfo(splitList));
        }
        // 将查询到的abcType赋值给机台用料List
        for (SmtMachineMaterialMouting smtMaterialMouting : smtMachineMaterialMoutingList) {
            if (StringUtils.isNotBlank(smtMaterialMouting.getItemCode())) {
                AgeingInfoFencePointToPointQueryItemInfoDTO itemInfoDTO = reItemInfoList.stream()
                        .filter(reItemInfo -> StringUtils.equals(smtMaterialMouting.getItemCode(), reItemInfo.getItemNo()))
                        .findFirst().orElse(null);
                if (itemInfoDTO == null) {
                    continue;
                }
                smtMaterialMouting.setAbcType(itemInfoDTO.getAbcType());
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteSmtMachineMaterialMoutingByIdBatch(List<SmtMachineMaterialMouting> list) {
        List<List<SmtMachineMaterialMouting>> listOfList = CommonUtils.splitList(list, CommonConst.BATCH_SIZE);
        if (!CollectionUtils.isEmpty(listOfList)) {
            for (List<SmtMachineMaterialMouting> paramsList : listOfList) {
                smtMachineMaterialMoutingRepository.deleteSmtMachineMaterialMoutingByIdBatch(paramsList);
            }
        }
    }

    /**
     * 查询实体数据，关联PK_CODE_INFO
     *
     * @param map
     * @return List<SmtMachineMaterialMouting>
     **/
    @Override
    public List<SmtMachineMaterialMouting> selectMoutingWithPkCodeInfo(Map map) {
        return smtMachineMaterialMoutingRepository.selectMoutingWithPkCodeInfo(map);
    }

    @Override
    public void deleteSmtMachineMaterialMountingByDoubleReelId(SmtMachineMaterialMouting record) {
        smtMachineMaterialMoutingRepository.deleteSmtMachineMaterialMountingByDoubleReelId(record);
    }

    @Override
    public ServiceData getLineTransfer(String factoryId, String lineCode, String taskCode) {
        ServiceData ret = new ServiceData<>();
        if (StringHelper.isEmpty(factoryId)) {
            ret.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.FACTORY_ID_IS_NULL));
            return ret;
        }
        List<SmtMachineMaterialMouting> moutingList = smtMachineMaterialMoutingRepository.getAllByLineCode(factoryId, lineCode, taskCode);
        ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        ret.setBo(MpConstant.YES);
        if (moutingList.size() > 0) {
            return ret;
        } else {
            List<SmtMachineMTLHistoryH> historyHList = smtMachineMTLHistoryHService.getAllByLineCodeAndMountType(factoryId, lineCode, "1", taskCode);
            if (historyHList.size() > 0) {
                return ret;
            }
        }
        ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        ret.setBo(MpConstant.NO);
        return ret;
    }

    @Override
    @RecordLogAnnotation("清除机台再用")
    public ServiceData deleteLineTransferRecord(String factoryId, String lineCode, String taskCode) {
        ServiceData ret = new ServiceData<>();
        if (StringHelper.isEmpty(factoryId)) {
            ret.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.FACTORY_ID_IS_NULL));
            return ret;
        }
        try {
            smtMachineMaterialMoutingRepository.deleteAllByLineCode(factoryId, lineCode, taskCode);
            smtMachineMTLHistoryHService.deleteAllByLineType(factoryId, lineCode, NumConstant.STR_ONE, taskCode);
            ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
            ret.setBo(CommonUtils.getLmbMessage(MessageId.RETCODE_SUCCESS));
        } catch (Exception e) {
            throw e;
        }
        return ret;
    }

    private ServiceData checkDipBoardScanningDTO(DipBoardScanningDTO param, PsWipInfo psWipInfo, PsWorkOrderDTO psWorkOrderDTO) throws Exception {
        ServiceData ret = new ServiceData();
        // 1.校验单板条码是否存在
        if (psWipInfo == null) {
            ret.setCode(new RetCode(RetCode.VALIDATIONERROR_CODE, MessageId.SN_NOT_EXIST));
            return ret;
        }
        // 2.校验单板条码的指令与界面所选指令是否一致--20201126 改成校验主工序是DIP 校验条码与指令批次是否一致--23-07-05DIP过板扫描包括背板线，先去掉校验指令主工序是DIP
        if (!param.getWorkOrderNo().contains(psWipInfo.getAttribute1())) {
            ret.setCode(new RetCode(RetCode.VALIDATIONERROR_CODE, MessageId.SN_IS_NOT_THIS_PLANID));
            return ret;
        }

        // 3. 校验指令是否开工
        if (!Constant.IS_START.equals(psWorkOrderDTO.getWorkOrderStatus())) {
            ret.setCode(new RetCode(RetCode.VALIDATIONERROR_CODE, MessageId.WORKORDER_STATUS_IS_NOT_STARTED));
            return ret;
        }
        // 根据线体、指令、mountType（10）、pickStatus（2）查询机台上料历史头表信息
        // 4.校验指令上料是否已完成
        boolean complete = ifWorkOrderPickComplete(param);
        if (!complete) {
            ret.setCode(new RetCode(RetCode.VALIDATIONERROR_CODE, MessageId.WORKORDER_LOADING_SCAN_NOT_COMPLETED));
            return ret;
        }
        //校验sn是否过板扫描完成
        //根据指令子工序查过板记录
        WipScanHistoryDTO wipScanHistoryDTO = new WipScanHistoryDTO();
        wipScanHistoryDTO.setSn(param.getSn());
        wipScanHistoryDTO.setWorkOrderNo(param.getWorkOrderNo());
        wipScanHistoryDTO.setSourceSys(MpConstant.WIP_SCAN_HISTORY_SOURCESYS_GB);
        wipScanHistoryDTO.setFactoryId(new BigDecimal(param.getFactoryId()));
        wipScanHistoryDTO.setDipFinishFlag(Constant.FLAG_Y);
        List<WipScanHistory> wipScanHistorylist = wipScanHistoryService.getOverBoardList(wipScanHistoryDTO);
        if (!CollectionUtils.isEmpty(wipScanHistorylist)) {
            ret.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.DIP_SCAN_FINISHED));
            return ret;
        }
        //

        //判断子工序是否按工位扫描,是的话校验工站是否为空
//		if(veifyScanByProcessCode(param.getProcessCode())){
//			param.setScanByWorkStation(MpConstant.TRUE);
//			if(StringUtils.isEmpty(param.getWorkStation())){
//				ret.setCode(new RetCode(RetCode.VALIDATIONERROR_CODE,MessageId.WORKER_STATION_CANNOT_BE_EMPTY));
//				return ret;
//			}
//		}else{
//			param.setScanByWorkStation(MpConstant.FALSE);
//		}
        ret.setCode(new RetCode(RetCode.SUCCESS_CODE, MessageId.SUCCESS));
        return ret;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteMountingDataOfDip(SmtMachineMaterialMoutingDTO dto) throws Exception {
        String workOrderNo = dto.getWorkOrder();
        if (StringUtils.isBlank(workOrderNo) || StringUtils.isBlank(dto.getLineCode())) {
            return;
        }
        Map<String, Object> map = new HashMap<>();
        map.put("workOrder", workOrderNo);
        List<SmtMachineMaterialMouting> mountingList = smtMachineMaterialMoutingRepository.selectMoutingWithPkCodeInfo(map);
        if (CollectionUtils.isEmpty(mountingList)) {
            return;
        }
        // 删除机台在用数据
        this.deleteSmtMachineMaterialMoutingByIdBatch(mountingList);
        //根据线体和指令失效上料历史头表
        SmtMachineMTLHistoryH smtMachineMTLHistoryH = new SmtMachineMTLHistoryH();
        smtMachineMTLHistoryH.setLineCode(dto.getLineCode());
        smtMachineMTLHistoryH.setWorkOrder(dto.getWorkOrder());
        smtMachineMTLHistoryH.setLastUpdatedBy(dto.getLastUpdatedBy());
        //DIP上料历史数据
        smtMachineMTLHistoryH.setMountType(DIP_MATERIAL_SCAN_TYPE);
        smtMachineMTLHistoryHService.updateSmtHistoryByLineCodeAndWorkOrderAndMountType(smtMachineMTLHistoryH);
        //查询箱内容数据
        Set<String> lpnSet = mountingList.stream().map(SmtMachineMaterialMouting::getLpn).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(lpnSet)) {
            // 箱码为空直接返回
            return;
        }
        List<String> lpnList = new ArrayList<>(lpnSet);
        List<ContainerContentInfoDTO> containerContentInfoList = this.getContainerContentInfoOfLpns(lpnList);
        if (!CollectionUtils.isEmpty(containerContentInfoList)) {
            List<ContainerContentInfoDTO> useUpContainerContentInfoList = containerContentInfoList.stream().filter(containerContentInfo -> containerContentInfo.getContentQty() != null && containerContentInfo.getContentQty().compareTo(BigDecimal.ZERO) == 0).collect(Collectors.toList());
            //删除数量为0的
            if (!CollectionUtils.isEmpty(useUpContainerContentInfoList)) {
                ProductionmgmtRemoteService.deleteContainerContentInfoDTOByIdList(useUpContainerContentInfoList.stream().map(contentInfoDTO -> contentInfoDTO.getContentId()).collect(Collectors.toList()));
            }
        }
    }

    /**
     * 同步设备reelId数量
     *
     * @param reelIdVO 设备上传同步的reelId及数量列表
     * @throws Exception
     */
    @Override
    public List<EquipmentConsumeReelIdVO> updateEquipmentDockingMsg(EquipmentConsumeReelIdVO reelIdVO) throws Exception {
        // 放置本次处理失败或未处理的reelId
        Set<String> returnReelIdSet = new HashSet<>();
        List<EquipmentConsumeReelIdVO> returnReelIdList = new ArrayList<>();
        List<EquipmentConsumeReelIdDTO> consumeReelIdList = reelIdVO.getRidList();

        if (CollectionUtils.isEmpty(consumeReelIdList)) {
            return returnReelIdList;
        }
        List<List<EquipmentConsumeReelIdDTO>> reelIdPartitionList = Lists.partition(consumeReelIdList, NumConstant.NUM_500);
        List<EquipmentConsumeReelIdVO> reelIdUseInfos = new ArrayList<>();
        // 根据设备传入reelId批量循环查机台在用
        for (List<EquipmentConsumeReelIdDTO> reelIdList : reelIdPartitionList) {
            reelIdUseInfos.addAll(smtMachineMaterialMoutingRepository.getReelIdUseInfo(reelIdList));
        }
        Map<String, Long> reelIdAndQtyMap = this.getReelIdAndQtyMap(consumeReelIdList, reelIdUseInfos);
        /**
         * 第一次处理
         * 处理机台在用可查询到的reelId
         */
        this.updatePkCodeQtyAndDealMountingData(reelIdUseInfos, reelIdAndQtyMap, reelIdVO.getEmpNo(), returnReelIdSet);
        // 当前机台在用的reelId集合
        Set<String> firstMountingReelIdSet = reelIdUseInfos.stream().map(EquipmentConsumeReelIdVO::getReelId).collect(Collectors.toSet());
        /**
         * 第二次处理
         * 筛选第一次查询不在机台在用的reelId进行第二次查询并处理
         * 避免新旧料盘同时上传后续无法接料问题
         */
        List<EquipmentConsumeReelIdDTO> firstNotDealReelIdList = consumeReelIdList.stream().filter(o -> !firstMountingReelIdSet.contains(o.getReelId())).collect(Collectors.toList());
        reelIdPartitionList = Lists.partition(firstNotDealReelIdList, NumConstant.NUM_500);
        reelIdUseInfos = new ArrayList<>();
        // 获取设备传入reelId对应的线体 批量循环查机台在用
        for (List<EquipmentConsumeReelIdDTO> reelIdList : reelIdPartitionList) {
            reelIdUseInfos.addAll(smtMachineMaterialMoutingRepository.getReelIdUseInfo(reelIdList));
        }
        this.updatePkCodeQtyAndDealMountingData(reelIdUseInfos, reelIdAndQtyMap, reelIdVO.getEmpNo(), returnReelIdSet);
        /**
         * 第三次处理
         * 筛选两次查询都不在机台在用的reelId
         * 查询是否是续料盘
         * 如果是续料盘则进行接料操作(在用reelId数量扣为0,同时切换续料盘为在用reelId)
         */
        // 第二次处理查询到的机台在用reelId
        Set<String> secondMountingReelIdSet = reelIdUseInfos.stream().map(EquipmentConsumeReelIdVO::getReelId).collect(Collectors.toSet());
        // 筛选两次处理均未在机台在用查询到的reelId
        List<EquipmentConsumeReelIdDTO> secondNotDealReelIdList = firstNotDealReelIdList.stream().filter(o -> !secondMountingReelIdSet.contains(o.getReelId())).collect(Collectors.toList());
        reelIdPartitionList = Lists.partition(secondNotDealReelIdList, NumConstant.NUM_500);
        List<EquipmentConsumeReelIdVO> nextReelIdUseInfos = new ArrayList<>();
        // 根据NEXT_REEL_ROWID查机台在用数据
        for (List<EquipmentConsumeReelIdDTO> reelIdList : reelIdPartitionList) {
            nextReelIdUseInfos.addAll(smtMachineMaterialMoutingRepository.getNextReelIdUseInfo(reelIdList));
        }
        this.dealNextReelId(nextReelIdUseInfos, reelIdAndQtyMap, reelIdVO.getEmpNo(), returnReelIdSet);
        Set<String> thirdMountingReelIdSet = nextReelIdUseInfos.stream().map(EquipmentConsumeReelIdVO::getNextReelRowid).collect(Collectors.toSet());
        // 筛选得到三次处理都未在机台在用查询到的reelId
        Set<String> thirdNotDealReelIdSet = secondNotDealReelIdList.stream().filter(o -> !thirdMountingReelIdSet.contains(o.getReelId())).map(EquipmentConsumeReelIdDTO::getReelId).collect(Collectors.toSet());
        returnReelIdSet.addAll(thirdNotDealReelIdSet);
        // 校验reelId处理失败次数。失败次数超过限制则不再返回重试
        try {
            this.checkFailedCount(returnReelIdSet);
        } catch (Exception e) {
            logger.error(e.getMessage());
            // 失败次数校验异常不影响功能。直接返回
        }
        for (String reelId : returnReelIdSet) {
            EquipmentConsumeReelIdVO vo = new EquipmentConsumeReelIdVO();
            vo.setReelId(reelId);
            returnReelIdList.add(vo);
        }
        return returnReelIdList;
    }

    public Map<String, Long> getReelIdAndQtyMap(List<EquipmentConsumeReelIdDTO> consumeReelIdList, List<EquipmentConsumeReelIdVO> reelIdUseInfos) {
        Map<String, Long> reelIdUseQtyMap = reelIdUseInfos.stream().filter(e -> e.getQty() != null)
                .collect(Collectors.toMap(EquipmentConsumeReelIdVO::getReelId, EquipmentConsumeReelIdVO::getQty, (a, b) -> a));
        // 上传数据的reelId和数量map
        Map<String, Long> reelIdAndQtyMap = new HashMap<>();
        for (EquipmentConsumeReelIdDTO uploadReelIdInfo : consumeReelIdList) {
            String rid = uploadReelIdInfo.getReelId();
            Long uploadQty = uploadReelIdInfo.getQty();
            Long inUseQty = reelIdUseQtyMap.get(rid) == null ? uploadQty : reelIdUseQtyMap.get(rid);
            // 上传的数量不能比在用数量大
            if (uploadQty == null || uploadQty > inUseQty) {
                uploadQty = inUseQty;
            }
            reelIdAndQtyMap.put(rid, uploadQty);
        }
        return reelIdAndQtyMap;
    }

    /**
     * 校验reelId处理失败次数。失败次数超过限制则不再返回重试
     *
     * @param returnReelIdSet
     */
    private void checkFailedCount(Set<String> returnReelIdSet) {
        // 最大失败次数
        int maxCount = NumConstant.NUM_FIVE;
        List<String> totalFailedList = new ArrayList<>();
        Iterator<String> it = returnReelIdSet.iterator();
        while (it.hasNext()) {
            String reelId = it.next();
            String redisKey = Constant.REDIS_REELID_QTY_SYNC_FAILED_COUNTER + Constant.SYMBOL_COLON + reelId;
            // key不存在设置初始值0。超时时间60分钟
            redisTemplate.opsForValue().setIfAbsent(redisKey, NumConstant.NUM_ZERO, NumConstant.NUM_SIXTY, TimeUnit.MINUTES);
            long count = redisTemplate.opsForValue().increment(redisKey);
            if (count > maxCount) {
                totalFailedList.add(reelId);
                it.remove();
            }
        }
        if (totalFailedList.size() > NumConstant.NUM_ZERO) {
            imesLogService.log(totalFailedList, "reelId同步失败");
        }
    }

    /**
     * 1、更新pkCode数量
     * 2、处理机台在用数据(当reelId数量为0时使用新料盘替换旧料盘)
     *
     * @param reelIdUseInfos
     * @throws Exception
     */
    private void updatePkCodeQtyAndDealMountingData(List<EquipmentConsumeReelIdVO> reelIdUseInfos, Map<String, Long> reelIdAndQtyMap, String empNo, Set<String> returnReelIdList) throws Exception {
        if (CollectionUtils.isEmpty(reelIdUseInfos)) {
            return;
        }
        // 获取线体编号集合
        Set<String> lineCodeSet = reelIdUseInfos.stream().map(EquipmentConsumeReelIdVO::getLineCode).collect(Collectors.toSet());
        // 查询线体信息
        List<CFLine> cfLineList = BasicsettingRemoteService.getLineCodeMaterielMarker(lineCodeSet);
        // 筛选线体中MATERIEL_MARKER = Y的线体
        Set<String> materielMarkerLineCodeSet = cfLineList.stream().filter(o -> Constant.FLAG_Y.equalsIgnoreCase(o.getMaterielMarker())).map(CFLine::getLineCode).collect(Collectors.toSet());

        // 筛选已开启同步配置线体上的reelId
        Set<EquipmentConsumeReelIdVO> needToDealReelIdList = reelIdUseInfos.stream().filter(o -> materielMarkerLineCodeSet.contains(o.getLineCode())).collect(Collectors.toSet());

        List<String> useUpReelIdList = new ArrayList<>();
        for (EquipmentConsumeReelIdVO needToDealReelId : needToDealReelIdList) {
            Long qty = reelIdAndQtyMap.get(needToDealReelId.getReelId());
            qty = qty == null ? NumConstant.LONG_ZERO : qty;
            if (qty.intValue() <= NumConstant.NUM_ZERO) {
                // 当前料盘数量为0且续料盘不为空需更新机台在用
                useUpReelIdList.add(needToDealReelId.getReelId());
            }
            needToDealReelId.setQty(qty);
        }
        // 批量更新数量(数量更新无需添加事务。失败抛出异常)
        this.updatePkCodeInfoQty(empNo, needToDealReelIdList);

        // 处理数量为0机台在用物料
        this.mountingReplaceByNextReelId(useUpReelIdList, returnReelIdList);
    }

    /**
     * 处理通过NEXT_REEL_ROWID查询到的机台在用数据
     * 1.object_id数量更新为0
     * 2.NEXT_REEL_ROWID数量更新为上传的数量
     * 3.NEXT_REEL_ROWID替换object_id作为机台在用
     *
     * @param nextReelIdUseInfos
     * @param reelIdAndQtyMap
     * @param empNo
     * @param returnReelIdSet
     * @throws Exception
     */
    private void dealNextReelId(List<EquipmentConsumeReelIdVO> nextReelIdUseInfos, Map<String, Long> reelIdAndQtyMap,
                                String empNo, Set<String> returnReelIdSet) throws Exception {
        if (CollectionUtils.isEmpty(nextReelIdUseInfos)) {
            return;
        }
        // 获取线体编号集合
        Set<String> lineCodeSet = nextReelIdUseInfos.stream().map(EquipmentConsumeReelIdVO::getLineCode).collect(Collectors.toSet());
        // 查询线体信息
        List<CFLine> cfLineList = BasicsettingRemoteService.getLineCodeMaterielMarker(lineCodeSet);
        // 筛选线体中MATERIEL_MARKER = Y的线体
        Set<String> materielMarkerLineCodeSet = cfLineList.stream().filter(o -> Constant.FLAG_Y.equalsIgnoreCase(o.getMaterielMarker())).map(CFLine::getLineCode).collect(Collectors.toSet());

        // 筛选已开启同步配置线体上的reelId
        Set<EquipmentConsumeReelIdVO> needToDealReelIdList = nextReelIdUseInfos.stream().filter(o -> materielMarkerLineCodeSet.contains(o.getLineCode())).collect(Collectors.toSet());
        // 获取接料扫描记录的物料数量，作为设备上传的最大值
        Map<String, Long> nextReelIdQty = getNextReelIdQty(needToDealReelIdList);
        Set<EquipmentConsumeReelIdVO> needToUpdateReelIdList = new HashSet<>();
        List<String> useUpReelIdList = new ArrayList<>();
        for (EquipmentConsumeReelIdVO needToDealReelId : needToDealReelIdList) {
            // 当前在用reelId数量扣为0
            needToDealReelId.setQty(NumConstant.LONG_ZERO);
            needToUpdateReelIdList.add(needToDealReelId);
            String nextRID = needToDealReelId.getNextReelRowid();
            Long qty = reelIdAndQtyMap.get(nextRID);
            qty = qty == null ? NumConstant.LONG_ZERO : qty;
            Long orgQty = nextReelIdQty.get(nextRID);
            orgQty = orgQty == null ? qty : orgQty;
            qty = qty > orgQty ? orgQty : qty;
            EquipmentConsumeReelIdVO nextReelId = new EquipmentConsumeReelIdVO();
            nextReelId.setReelId(nextRID);
            nextReelId.setQty(qty);
            needToUpdateReelIdList.add(nextReelId);

            // 当前料盘数量为0且续料盘不为空需更新机台在用
            useUpReelIdList.add(needToDealReelId.getReelId());

        }
        // 批量更新数量(数量更新无需添加事务。失败抛出异常)
        this.updatePkCodeInfoQty(empNo, needToUpdateReelIdList);

        // 处理数量为0机台在用物料
        this.mountingReplaceByNextReelId(useUpReelIdList, returnReelIdSet);
    }

    private Map<String, Long> getNextReelIdQty(Set<EquipmentConsumeReelIdVO> needToDealReelIdList) {
        if (CollectionUtils.isEmpty(needToDealReelIdList)) {
            return new HashMap();
        }
        List<List<String>> nextLL = CommonUtils.splitList(
                needToDealReelIdList.stream().map(EquipmentConsumeReelIdVO::getNextReelRowid)
                        .collect(Collectors.toList()));
        List<SmtMachineMTLHistoryL> hisList = new ArrayList<>();
        for (List<String> nextL : nextLL) {
            hisList.addAll(historyLRepository.getByNextReelId(nextL));
        }
        if (CollectionUtils.isEmpty(hisList)) {
            return new HashMap();
        }
        return hisList.stream().filter(e -> e.getQty() != null)
                .collect(Collectors.toMap(SmtMachineMTLHistoryL::getObjectId, e -> e.getQty().longValue(),
                        (a, b) -> a));
    }

    /**
     * 接料并删除备料
     *
     * @param useUpReelIdList
     * @param returnReelIdList 放置处理失败的reelId
     */

    private void mountingReplaceByNextReelId(List<String> useUpReelIdList, Set<String> returnReelIdList) {
        if (CollectionUtils.isEmpty(useUpReelIdList)) {
            return;
        }
        // 数量为0机台在用reelId
        List<SmtMachineMaterialMouting> useUpMoutingList = new ArrayList<>();
        // 数量为0reelId集合
        List<List<String>> listOfReelIdList = CommonUtils.splitList(useUpReelIdList);
        for (List<String> list : listOfReelIdList) {
            SmtMachineMaterialMouting param = new SmtMachineMaterialMouting();
            param.setInObjectId(SqlUtils.convertStrCollectionToSqlType(list));
            useUpMoutingList.addAll(smtMachineMaterialMoutingRepository.getAllByParamsOfFeederInsertion(param));
        }

        // 筛选续料盘不为空数据(不可在更新数量完成前筛选此数据。防止查询数据后、更新数量完成前用户接料出现问题)
        Set<SmtMachineMaterialMouting> hasNextReelIdMountingList = useUpMoutingList.stream().filter(o -> StringUtils.isNotBlank(o.getNextReelRowid())).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(hasNextReelIdMountingList)) {
            return;
        }
        // 查询续料盘pk_code_info
        List<PkCodeInfo> nextPkCodeInfolist = new ArrayList<>();
        Set<String> nextReelIdSet = hasNextReelIdMountingList.stream().map(SmtMachineMaterialMouting::getNextReelRowid).collect(Collectors.toSet());
        List<List<String>> listOfList = CommonUtils.splitList(new ArrayList<String>(nextReelIdSet));
        for (List<String> list : listOfList) {
            PkCodeInfoDTO dto = new PkCodeInfoDTO();
            dto.setInPkCode(SqlUtils.convertStrCollectionToSqlType(list));
            nextPkCodeInfolist.addAll(pkCodeInfoService.getList(dto));
        }
        Map<String, PkCodeInfo> reelIdToPkCodeInfoMap = new HashMap<>();
        for (PkCodeInfo pkCodeInfo : nextPkCodeInfolist) {
            reelIdToPkCodeInfoMap.put(pkCodeInfo.getPkCode(), pkCodeInfo);
        }

        // 数量为0且续料盘不为空物料。接料，并删除备料
        for (SmtMachineMaterialMouting mounting : hasNextReelIdMountingList) {
            String reelId = mounting.getObjectId();
            String nextReelRowId = mounting.getNextReelRowid();
            PkCodeInfo pkCodeInfoNew = reelIdToPkCodeInfoMap.get(nextReelRowId);
            if (pkCodeInfoNew == null) {
                logger.error("pk_code_info不存在! " + nextReelRowId);
                continue;
            }
            try {
                // 接料，并删除备料(单个reelId的操作添加事务。)
                SpringUtil.getBean(this.getClass()).receive(mounting, pkCodeInfoNew, new Date(), reelId, nextReelRowId);
            } catch (Exception e) {
                logger.error(e.getMessage());
                returnReelIdList.add(reelId);
                continue;
            }
        }
    }

    /**
     * 接料，并删除备料
     *
     * @param mounting
     * @param pkCodeInfoNew
     * @param curDate
     * @param preReelId
     * @param nextReelRowId
     */
    @Transactional(rollbackFor = Exception.class)
    public void receive(SmtMachineMaterialMouting mounting,
                        PkCodeInfo pkCodeInfoNew, Date curDate, String preReelId, String nextReelRowId) {
        // 接料，并删除备料（失效机台在用也更新）
        this.updateSmtMtFromPrepare(mounting, pkCodeInfoNew, new Date(), preReelId);
        SmtMachineMaterialPrepare smtPrepare = new SmtMachineMaterialPrepare();
        smtPrepare.setObjectId(nextReelRowId);
        smtMachineMaterialPrepareRepository.deleteSmtMachineMaterialPrepareById(smtPrepare);
    }


    /**
     * 更新pkCodeInfo 数量
     *
     * @param equipmentConsumeReelId 设备物料消耗的reelId
     * @throws Exception
     */
    public void updatePkCodeInfoQty(String empNo, Collection<EquipmentConsumeReelIdVO> equipmentConsumeReelId) throws Exception {
        List<PkCodeInfoDTO> pkCodeInfoList = new ArrayList<>(NumConstant.NUM_TEN);
        equipmentConsumeReelId.forEach(e -> {
            PkCodeInfoDTO pkCodeInfoDTO = new PkCodeInfoDTO();
            pkCodeInfoDTO.setLastUpdatedBy(empNo);
            pkCodeInfoDTO.setItemQty(new BigDecimal(e.getQty()));
            pkCodeInfoDTO.setPkCode(e.getReelId());
            pkCodeInfoList.add(pkCodeInfoDTO);
        });
        // 读取配置：pk_code数量是否可改大
        boolean canIncrease = canIncreaseQty();
        // 分批更新数量
        List<List<PkCodeInfoDTO>> listOfList = CommonUtils.splitList(pkCodeInfoList, NumConstant.NUM_FIFTY);
        for (List<PkCodeInfoDTO> list : listOfList) {
            if (canIncrease) {
                pkCodeInfoRepository.updateItemQtyBatch(list);
                continue;
            }
            pkCodeInfoRepository.updateItemQtyBatchByEqp(list);
        }
    }

    private boolean canIncreaseQty() {
        try {
            SysLookupTypesDTO typesDTO = BasicsettingRemoteService.getSysLookUpValue(MpConstant.LOOKUP_6400, MpConstant.LOOKUP_6400001);
            return typesDTO != null && "Y".equals(typesDTO.getLookupMeaning());
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取物料标识的reelId
     */
    private List<EquipmentConsumeReelIdVO> getEquipmentConsumeReelId(List<EquipmentConsumeReelIdVO> reelIdUseInfo,
                                                                     List<EquipmentConsumeReelIdVO> materielMarker) throws Exception {
        List<EquipmentConsumeReelIdVO> consumeReelId = new ArrayList<>(NumConstant.NUM_TEN);
        reelIdUseInfo.forEach(reelIdUse -> {
            materielMarker.forEach(marker -> {
                if (reelIdUse.getLineCode().equals(marker.getLineCode())
                        && Constant.FLAG_Y.equals(marker.getMaterielMarker())) {
                    consumeReelId.add(reelIdUse);
                }
            });
        });
        return consumeReelId;
    }

    @Override
    @RecordLogAnnotation("清理DIP指令机台在用物料")
    @Transactional(rollbackFor = Exception.class)
    public void deleteUseUpMountingDataOfDip(String workOrderNo) throws Exception {
        if (StringUtils.isBlank(workOrderNo)) {
            return;
        }
        Map<String, Object> map = new HashMap<>();
        map.put("workOrder", workOrderNo);
        List<SmtMachineMaterialMouting> mountingList = this.selectMoutingWithPkCodeInfo(map);
        if (CollectionUtils.isEmpty(mountingList)) {
            return;
        }
        // 筛选出当前数量为0机台在用物料
        List<SmtMachineMaterialMouting> useUpMountingList = mountingList.stream().filter(mounting -> mounting.getQty() != null && mounting.getQty().compareTo(BigDecimal.ZERO) == 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(useUpMountingList)) {
            // 没有用完机台在用物料则直接返回。(DIP指令机台在用物料由用户解绑)
            return;
        }
        Set<String> lpnSet = useUpMountingList.stream().map(SmtMachineMaterialMouting::getLpn).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(lpnSet)) {
            // 箱码为空直接返回
            return;
        }
        List<String> lpnList = new ArrayList<>(lpnSet);
        List<ContainerContentInfoDTO> containerContentInfoList = this.getContainerContentInfoOfLpns(lpnList);
        List<String> needDeleteMountOfLpnList = new ArrayList<>();

        // 筛选出存在箱内容的容器
        Set<String> hasDataLpnSet = containerContentInfoList.stream().map(ContainerContentInfoDTO::getLpn).collect(Collectors.toSet());

        // 筛选出箱内容无数据的箱码
        if (CollectionUtils.isEmpty(hasDataLpnSet)) {
            needDeleteMountOfLpnList.addAll(lpnSet);
        } else {
            needDeleteMountOfLpnList = lpnSet.stream().filter(lpn -> !hasDataLpnSet.contains(lpn)).collect(Collectors.toList());
        }

        // 根据箱码删除机台在用数据
        this.deleteSmtMachineMaterialMoutingBatch(needDeleteMountOfLpnList);

    }

    /**
     * 获取箱码对应箱内容(分批查询)
     *
     * @param lpnList
     * @return
     */
    private List<ContainerContentInfoDTO> getContainerContentInfoOfLpns(List<String> lpnList) {
        List<ContainerContentInfoDTO> containerContentInfoList = new ArrayList<>();
        List<List<String>> listOfList = CommonUtils.splitList(lpnList);
        Map<String, Object> map = null;
        for (List<String> list : listOfList) {
            // 查询箱内容
            map = new HashMap<>();
            map.put("lpns", SqlUtils.convertStrCollectionToSqlType(list));
            List<ContainerContentInfoDTO> tempList = this.getContainerContentInfo(map);
            if (!CollectionUtils.isEmpty(tempList)) {
                containerContentInfoList.addAll(tempList);
            }
        }
        return containerContentInfoList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ServiceData<Object> dipBoardScanning(DipBoardScanningDTO param) throws Exception {
        ServiceData<Object> ret = new ServiceData<>();
        // 根据sn获取wipInfo数据
        PsWipInfo psWipInfo = psWipInfoService.getPsWipInfoBySn(param.getSn());
        setItemNo(param, psWipInfo);
        // 查询指令数据
        PsWorkOrderDTO psWorkOrderDTO = this.getPsWorkOrderBasicByWorkOrderNoAndFactoryId(param.getWorkOrderNo(), param.getFactoryId());
        if (psWorkOrderDTO == null) {
            ret.setCode(new RetCode(RetCode.VALIDATIONERROR_CODE, MessageId.WORDER_ORDER_NOT_FOUND));
            return ret;
        }
        ServiceData<Object> resultRet = checkDipBoardScanningDTO(param, psWipInfo, psWorkOrderDTO);
        if (!RetCode.SUCCESS_CODE.equals(resultRet.getCode().getCode())) {
            return resultRet;
        }
        //根据指令子工序查过板记录
        List<WipScanHistory> wipScanHistorylist = this.getScanHistories(param);
        // 根据指令数据获取上料表id
        String cfgHeaderId = this.getCfgHeaderIdByWorkOrder(psWorkOrderDTO);
        if (StringUtils.isBlank(cfgHeaderId)) {
            ret.setCode(new RetCode(RetCode.VALIDATIONERROR_CODE, MessageId.B_SMT_BOM_DETAIL_OF_WORKORDER_IS_NULL));
            return ret;
        }
        // 查询非前加工上料表数据
        List<BSmtBomDetail> bSmtBomDetailList = this.getBSmtBomDetailListByCfgHeaderId(cfgHeaderId, param);
        if (CollectionUtils.isEmpty(bSmtBomDetailList)) {
            // 上料表数据为空
            ret.setCode(new RetCode(RetCode.VALIDATIONERROR_CODE, MessageId.B_SMT_BOM_DETAIL_OF_WORKORDER_IS_NULL));
            return ret;
        }
        if (StringUtils.isEmpty(psWorkOrderDTO.getItemNo())) {
            ret.setCode(new RetCode(RetCode.VALIDATIONERROR_CODE, MessageId.ITEM_INFO_NOT_FOUND));
            return ret;
        }
        //获取bom清单
        Map<String, BBomDetail> bBomDetailDTOMap = getBomNumByProductCode(psWorkOrderDTO.getItemNo(), psWorkOrderDTO.getWorkOrderNo());
        //上料表数据map
        Map<String, BSmtBomDetail> bSmtBomDetailMap = bSmtBomDetailList.stream().collect(Collectors.toMap(BSmtBomDetail::getItemCode, a -> a, (k1, k2) -> k1));
        //该指令的非前加工的上料表记录
        List<BSmtBomDetail> bSmtBomDetailAllList = this.getBSmtBomDetailListByCfgHeaderId(cfgHeaderId);
        //是否按工位扫描
        if (!param.isScanByWorkStation()) {
            //  条码是否在扫描历史表（wip_scan_history）中该子工序的过板记录（source_sys= GB）的记录，报错XXX条码已在XXX子工序过板完成
            if (!CollectionUtils.isEmpty(wipScanHistorylist)) {
                ret.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.SN_HAS_SCANNED));
                ret.getCode().setMsg(CommonUtils.getLmbMessage(MessageId.SN_HAS_SCANNED, param.getSn()));
                return ret;
            }
        } else {
            //  条码是否在扫描历史表（wip_scan_history）中该工站的过板记录（source_sys= GB）的记录，报错XXX条码已在XXX子工序过板完成
            if (isScanedInWorkStation(wipScanHistorylist, param)) {
                ret.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.SN_HAS_SCANNED));
                ret.getCode().setMsg(CommonUtils.getLmbMessage(MessageId.SN_HAS_SCANNED, param.getSn()));
                return ret;
            }
            this.checkBomQtyAndDeductQty(wipScanHistorylist, bBomDetailDTOMap, bSmtBomDetailMap, bSmtBomDetailAllList);
        }
        //DIP拆箱锁 延时3秒
        RedisLock dipSplitRedisLock = new RedisLock(RedisKeyConstant.DIP_CONTAINER_SPLIT_KEY + param.getWorkOrderNo());
        // 获取分布式锁。防止多线程扣数异常(如：同时扫描两个单板)
        RedisLock redisLock = this.getReelIdUpdateLock(cfgHeaderId, param.getWorkStation());
        try {
            if (!dipSplitRedisLock.lock(NumConstant.LONG_THREE)) {
                ret.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.THE_CURRENT_ORDER_IS_BEING_UNBOXED_OR_DEDUCTED));
                return ret;
            }
            if (redisLock == null) {
                ret.setCode(new RetCode(RetCode.VALIDATIONERROR_CODE, MessageId.DIP_BOARD_SCAN_GET_REDIS_LOCK_FAILED));
                return ret;
            }
            param.setPsWipInfo(psWipInfo);
            return getObjectServiceData(param, psWorkOrderDTO, bSmtBomDetailList, bBomDetailDTOMap, bSmtBomDetailAllList);
        } finally {
            closeRedisLock(dipSplitRedisLock, redisLock);
        }
    }

    public ServiceData<Object> getObjectServiceData(DipBoardScanningDTO param, PsWorkOrderDTO psWorkOrderDTO,
                                                    List<BSmtBomDetail> bSmtBomDetailList, Map<String, BBomDetail> bBomDetailDTOMap, List<BSmtBomDetail> bSmtBomDetailAllList) throws Exception {
        PsWipInfo psWipInfo = param.getPsWipInfo();
        ServiceData<Object> ret = new ServiceData<>();
        List<SmtMachineMaterialMouting> smtMachineMaterialMoutingList = this.getMoutingWithPkCodeInfoByWorkOrder(param);
        if (CollectionUtils.isEmpty(smtMachineMaterialMoutingList)) {
            // 在用表对应数据为空
            logger.info("在用表对应数据为空");
            ret.setCode(new RetCode(RetCode.VALIDATIONERROR_CODE, MessageId.SMT_MACHINE_MATERIAL_OF_WORKORDER_IS_NULL));
            return ret;
        }
        //组合物料查询箱内容表数量
        dealSmtMachineMaterialMoutingList(smtMachineMaterialMoutingList);
        // 扣数
        List<SmtMachineMaterialMouting> insertSmtSnMtlTracingInfoList = this.reducePkCodeQyt(param, bSmtBomDetailList, smtMachineMaterialMoutingList);
        // 写追溯记录
        this.insertSmtSnMtlTracingInfo(insertSmtSnMtlTracingInfoList, param, psWipInfo, psWorkOrderDTO);
        //dip过板完成更新扫描历史数据
        updateWipScanHisttoryList(param, bSmtBomDetailAllList, bBomDetailDTOMap);
        ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        ret.setBo(psWipInfo);
        return ret;
    }

    public void checkBomQtyAndDeductQty(List<WipScanHistory> wipScanHistorylist, Map<String, BBomDetail> bBomDetailDTOMap, Map<String, BSmtBomDetail> bSmtBomDetailMap, List<BSmtBomDetail> bSmtBomDetailAllList) throws Exception {
        //根据该工位的上料表的数量+已进行扣数的物料代码数量是否大于清单，大于则报错不允许在改工位扫描
        //工位已扣数记录,循环指令对应所有上料表数据
        List<BSmtBomDetail> bSmtBomDetailDeductList = getBsmtBomDetailDeductListByWorkStation(wipScanHistorylist, bSmtBomDetailAllList);
        //汇总扣数map,根据物料代码
        Map<String, BigDecimal> deductByItemCodeMap = getDeductByItemCodeMap(bSmtBomDetailDeductList);
        //插件数量map
        Map<String, BigDecimal> plugInQtyByItemCodeMap = getPlugInQtyByItemCodeMap(bSmtBomDetailAllList);
        //循环判断所有物料代码上料表数量+已扣数数量+插件数量   大于BOM清单用量,大于则跑出异常
        checkBomQtyAndDeductQty(bSmtBomDetailMap, plugInQtyByItemCodeMap, deductByItemCodeMap, bBomDetailDTOMap);
    }

    private List<WipScanHistory> getScanHistories(DipBoardScanningDTO param) throws Exception {
        //子工序是否按工位扫描
        String processCode = param.getProcessCode();
        WipScanHistoryDTO wipScanHistoryDTO = new WipScanHistoryDTO();
        wipScanHistoryDTO.setSn(param.getSn());
        wipScanHistoryDTO.setWorkOrderNo(param.getWorkOrderNo());
        wipScanHistoryDTO.setCurrProcessCode(processCode);
        wipScanHistoryDTO.setSourceSys(MpConstant.WIP_SCAN_HISTORY_SOURCESYS_GB);
        wipScanHistoryDTO.setFactoryId(new BigDecimal(param.getFactoryId()));
        List<WipScanHistory> wipScanHistorylist = wipScanHistoryService.getOverBoardList(wipScanHistoryDTO);
        return wipScanHistorylist;
    }

    public void closeRedisLock(RedisLock dipSplitRedisLock, RedisLock redisLock) {
        if (redisLock != null) {
            redisLock.unlock();
        }
        if (dipSplitRedisLock != null) {
            dipSplitRedisLock.unlock();
        }
    }

    //设置物料代码
    private void setItemNo(DipBoardScanningDTO param, PsWipInfo psWipInfo) {
        if (psWipInfo != null) {
            param.setItemNo(psWipInfo.getItemNo());
        }
    }

    //实体类转换
    private static List<BBomDetail> toBBomDetailList(List<BBomTechnicalChangeInfo> entityList) {
        List<BBomDetail> dtoList = new ArrayList<BBomDetail>();
        for (BBomTechnicalChangeInfo entity : entityList) {
            dtoList.add(toBBomDetail(entity));
        }
        return dtoList;
    }

    //实体类转换
    private static BBomDetail toBBomDetail(BBomTechnicalChangeInfo entity) {
        BBomDetail dto = new BBomDetail();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }


    private void checkBomQtyAndDeductQty(Map<String, BSmtBomDetail> bSmtBomDetailMap, Map<String, BigDecimal> plugInQtyByItemCodeMap,
                                         Map<String, BigDecimal> deductByItemCodeMap, Map<String, BBomDetail> bBomDetailDTOMap) throws MesBusinessException {
        Iterator<Map.Entry<String, BSmtBomDetail>> it = bSmtBomDetailMap.entrySet().iterator();
        while (it.hasNext()) {
            Map.Entry<String, BSmtBomDetail> entry = it.next();
            BSmtBomDetail bSmtBomDetail = entry.getValue();
            String itemCode = entry.getKey();
            //工位的上料表数量
            BigDecimal bSmtNum = bSmtBomDetail.getQty();
            //已进行扣数数量=插件数量+扫描历史表记录对应上料表数量
            //插件数量
            BigDecimal pluginNum = plugInQtyByItemCodeMap.get(itemCode) == null ? new BigDecimal(MpConstant.STRING_ZERO) : plugInQtyByItemCodeMap.get(itemCode);
            //已扣数数量
            BigDecimal deductNum = deductByItemCodeMap.get(itemCode) == null ? new BigDecimal(MpConstant.STRING_ZERO) : deductByItemCodeMap.get(itemCode);
            //BOM清单数量
            BBomDetail bbomDetail = bBomDetailDTOMap.get(itemCode);
            BigDecimal bomNum = bbomDetail == null ? new BigDecimal(MpConstant.STRING_ZERO) : bbomDetail.getUsageCount();
            if (bSmtNum.add(pluginNum).add(deductNum).compareTo(bomNum) == NumConstant.NUM_ONE) {
                logger.info("上料表数量+已扣数数量+插件数量   大于BOM清单用量");
                throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, MessageId.GREATER_THAN_BOM_QTY);
            }
        }
    }

    /**
     * 获取所有的请求进行封装
     *
     * @param smtMachineMaterialMoutingList
     * @return
     */
    private JSONArray handleRequestData(List<SmtMachineMaterialMouting> smtMachineMaterialMoutingList) {
        // 获取所有的请求进行封装
        JSONArray requestJsonArray = new JSONArray();
        for (SmtMachineMaterialMouting materialMouting : smtMachineMaterialMoutingList) {
            //组合物料取箱内容表数量
            if (Constant.FLAG_Y.equals(materialMouting.getCombination())) {
                if (StringUtils.isBlank(materialMouting.getObjectId()) && StringUtils.isBlank(materialMouting.getLpn())) {
                    continue;
                }
                JSONObject param = new JSONObject();
                param.put("pkCode", materialMouting.getObjectId());
                param.put("lpn", materialMouting.getLpn());
                requestJsonArray.add(param);
            }
        }
        return requestJsonArray;
    }

    /**
     * 组合物料查询箱内容表数量
     *
     * @param smtMachineMaterialMoutingList
     * @throws MesBusinessException
     * @throws Exception
     */
    private void dealSmtMachineMaterialMoutingList(List<SmtMachineMaterialMouting> smtMachineMaterialMoutingList) throws MesBusinessException, Exception {
        JSONArray requestJsonArray = handleRequestData(smtMachineMaterialMoutingList);
        if (requestJsonArray.isEmpty()) {
            return;
        }
        List<ContainerContentInfoDTO> contentInfoDTOList = ProductionmgmtRemoteService.batchQueryContentInfo(requestJsonArray); //批量查询容器内信息
        for (SmtMachineMaterialMouting smtMachineMaterialMouting : smtMachineMaterialMoutingList) {
            if (Constant.FLAG_Y.equals(smtMachineMaterialMouting.getCombination())) {
                List<ContainerContentInfoDTO> containerContentInfoDTOAllList = filterContainerContentInfoDTOAllList(smtMachineMaterialMouting, contentInfoDTOList);
                if (CollectionUtils.isEmpty(containerContentInfoDTOAllList)) {
                    smtMachineMaterialMouting.setQty(null);
                    continue;
                }
                ContainerContentInfoDTO containerContentInfoDTO = containerContentInfoDTOAllList.get(NumConstant.NUM_ZERO);
                if (containerContentInfoDTO.getContentQty() != null) {
                    smtMachineMaterialMouting.setQty(containerContentInfoDTO.getContentQty());
                } else {
                    smtMachineMaterialMouting.setQty(null);
                }
            }
        }
    }

    /**
     * @param smtMachineMaterialMouting
     * @param contentInfoDTOList
     * @return
     */
    private List<ContainerContentInfoDTO> filterContainerContentInfoDTOAllList(SmtMachineMaterialMouting smtMachineMaterialMouting
            , List<ContainerContentInfoDTO> contentInfoDTOList) {
        return contentInfoDTOList.stream()
                .filter(contentInfo -> {
                    if (StringUtils.isNotBlank(smtMachineMaterialMouting.getObjectId()) && !smtMachineMaterialMouting.getObjectId().equals(contentInfo.getPkCode())) {
                        return false;
                    }
                    if (StringUtils.isNotBlank(smtMachineMaterialMouting.getLpn()) && !smtMachineMaterialMouting.getLpn().equals(contentInfo.getLpn())) {
                        return false;
                    }
                    return true;
                }).collect(Collectors.toList());
    }

    //校验是否有该工站扫描历史记录
    private boolean isScanedInWorkStation(List<WipScanHistory> wipScanHistorylist, DipBoardScanningDTO param) {
        if (!CollectionUtils.isEmpty(wipScanHistorylist)) {
            for (WipScanHistory wipScanHistory : wipScanHistorylist) {
                if (param.getWorkStation().equals(wipScanHistory.getWorkStation())) {
                    return true;
                }
            }
        }
        return false;
    }

    //已扣数记录,循环指令对应所有上料表数据
    private List<BSmtBomDetail> getBsmtBomDetailDeductListByWorkStation(List<WipScanHistory> wipScanHistorylist, List<BSmtBomDetail> bSmtBomDetailAllList) {
        List<BSmtBomDetail> bSmtBomDetailDeductList = new ArrayList<BSmtBomDetail>();
        for (WipScanHistory wipScanHistory : wipScanHistorylist) {
            for (BSmtBomDetail bSmtBomDetail : bSmtBomDetailAllList) {
                if (StringUtils.isNotEmpty(wipScanHistory.getWorkStation()) && StringUtils.isNotEmpty(bSmtBomDetail.getLocationNo())
                        && wipScanHistory.getWorkStation().equals(bSmtBomDetail.getLocationNo())) {
                    bSmtBomDetailDeductList.add(bSmtBomDetail);
                }
            }
        }
        return bSmtBomDetailDeductList;
    }

    //插件数量map
    private Map<String, BigDecimal> getPlugInQtyByItemCodeMap(List<BSmtBomDetail> bSmtBomDetailAllList) throws Exception {
        Map<String, BigDecimal> plugInQtyByItemCodeMap = new HashMap<String, BigDecimal>();
        for (BSmtBomDetail bSmtBomDetail : bSmtBomDetailAllList) {
            String processsCode = bSmtBomDetail.getProcessCode();
            //不是按工位扫描的
            if (!veifyScanByProcessCode(processsCode)) {
                String itemCode = bSmtBomDetail.getItemCode();
                BigDecimal bigItem = plugInQtyByItemCodeMap.get(itemCode);
                if (bigItem != null) {
                    plugInQtyByItemCodeMap.put(itemCode, bigItem.add(bSmtBomDetail.getQty()));
                } else {
                    plugInQtyByItemCodeMap.put(itemCode, bSmtBomDetail.getQty());
                }
            }
        }
        return plugInQtyByItemCodeMap;
    }


    //按物料代码汇总数量
    private Map<String, BigDecimal> getDeductByItemCodeMap(List<BSmtBomDetail> bSmtBomDetailDeductList) {
        Map<String, BigDecimal> deductByItemCodeMap = new HashMap<String, BigDecimal>();
        if (!CollectionUtils.isEmpty(bSmtBomDetailDeductList)) {
            for (BSmtBomDetail bSmtBomDetail : bSmtBomDetailDeductList) {
                String itemCode = bSmtBomDetail.getItemCode();
                BigDecimal bdItem = deductByItemCodeMap.get(itemCode);
                if (bdItem != null) {
                    deductByItemCodeMap.put(itemCode, bdItem.add(bSmtBomDetail.getQty()));
                } else {
                    deductByItemCodeMap.put(itemCode, bSmtBomDetail.getQty());
                }
            }
        }
        return deductByItemCodeMap;
    }

    //判断是否过板完成
    private void updateWipScanHisttoryList(DipBoardScanningDTO param, List<BSmtBomDetail> bSmtBomDetailAllList, Map<String, BBomDetail> bBomDetailDTOMap) throws Exception {
        /**
         * 根据条码查询扫描历史表（wip_scan_history）中的过板记录（source_sys= GB）的记录，遍历过板记录，
         *       a、 如果过板记录中的子工序是按工位扫描，则根据指令、子工序找到上料表维护的物料代码和数量作为该条码的扣数记录
         *      b、否则根据指令、工位找到上料表维护的物料代码和数量作为改条码的扣数记录
         *     c、将所有找到的扣数记录按物料代码对数量进行汇总
         *    d、查询该指令的非前加工的上料表记录，得到应该进行扣数的物料代码，根据物料代码查询bom清单中的用量，组合在一起得到每个物料代码的总用量
         *   如果c 的数据和 d的数据完全相等则更新wip_info的dip_finish_flag为Y标识dip过板扫描已完成
         */
        //已扣数记录
        //根据条码指令查过板记录
        WipScanHistoryDTO wshDto = new WipScanHistoryDTO();
        wshDto.setSn(param.getSn());
        wshDto.setWorkOrderNo(param.getWorkOrderNo());
        wshDto.setSourceSys(MpConstant.WIP_SCAN_HISTORY_SOURCESYS_GB);
        wshDto.setFactoryId(new BigDecimal(param.getFactoryId()));
        List<WipScanHistory> wipScanHistoryBySnlist = wipScanHistoryService.getOverBoardList(wshDto);
        //获取扣数记录，子工序是按工位扫描的按工位取上料表数据，否则按子工序取
        List<BSmtBomDetail> bSmtBomDetailDeductList = getBsmtBomDetailDeductList(wipScanHistoryBySnlist, bSmtBomDetailAllList);

        //扣数map,根据物料代码汇总
        Map<String, BigDecimal> deductByItemCodeMap = getDeductByItemCodeMap(bSmtBomDetailDeductList);
        //非前加工上料表记录map，按物料代码汇总
        Map<String, BigDecimal> preManueByItemCodeMap = getPreManueByItemCodeMap(bBomDetailDTOMap, bSmtBomDetailAllList);
        //如果preManueByItemCodeMap 的数据和 deductByItemCodeMap的数据完全相等则更新wip_scan_history的dip_finish_flag为Y标识dip过板扫描已完成
        if (checkNeedUpdateDipFinishFlag(deductByItemCodeMap, preManueByItemCodeMap) && !CollectionUtils.isEmpty(wipScanHistoryBySnlist)) {
            WipScanHistory wipScanHistory = wipScanHistoryBySnlist.get(NumConstant.NUM_ZERO);
            WipScanHistoryDTO dto = new WipScanHistoryDTO();
            dto.setSmtScanId(wipScanHistory.getSmtScanId());
            dto.setDipFinishFlag(Constant.FLAG_Y);
            wipScanHistoryService.updateWipScanHistory(dto);
        }
    }

    //子工序是按工位扫描的按子工序，工位取上料表数据
    private void getBsmtDetailByWorkStation(WipScanHistory wipScanHistory, List<BSmtBomDetail> bSmtBomDetailAllList, List<BSmtBomDetail> bSmtBomDetailDeductList) {
        for (BSmtBomDetail bSmtBomDetail : bSmtBomDetailAllList) {
            if (StringUtils.isNotEmpty(wipScanHistory.getWorkStation()) && StringUtils.isNotEmpty(bSmtBomDetail.getLocationNo())
                    && wipScanHistory.getWorkStation().equals(bSmtBomDetail.getLocationNo())
                    && StringUtils.equals(wipScanHistory.getCurrProcessCode(), bSmtBomDetail.getProcessCode())) {
                bSmtBomDetailDeductList.add(bSmtBomDetail);
            }
        }
    }

    //子工序是按子工序扫描的按子工序，取上料表数据
    private void getBsmtDetailByProcessCode(WipScanHistory wipScanHistory, List<BSmtBomDetail> bSmtBomDetailAllList, List<BSmtBomDetail> bSmtBomDetailDeductList) {
        for (BSmtBomDetail bSmtBomDetail : bSmtBomDetailAllList) {
            if (StringUtils.isNotEmpty(wipScanHistory.getCurrProcessCode()) && StringUtils.isNotEmpty(bSmtBomDetail.getProcessCode())
                    && wipScanHistory.getCurrProcessCode().equals(bSmtBomDetail.getProcessCode())) {
                bSmtBomDetailDeductList.add(bSmtBomDetail);
            }
        }
    }

    private List<BSmtBomDetail> getBsmtBomDetailDeductList(List<WipScanHistory> wipScanHistoryBySnlist, List<BSmtBomDetail> bSmtBomDetailAllList) throws Exception {
        List<BSmtBomDetail> bSmtBomDetailDeductList = new ArrayList<BSmtBomDetail>();
        for (WipScanHistory wipScanHistory : wipScanHistoryBySnlist) {
            String processsCode = wipScanHistory.getCurrProcessCode();
            //按工位扫描
            if (veifyScanByProcessCode(processsCode)) {
                getBsmtDetailByWorkStation(wipScanHistory, bSmtBomDetailAllList, bSmtBomDetailDeductList);
            } else {
                getBsmtDetailByProcessCode(wipScanHistory, bSmtBomDetailAllList, bSmtBomDetailDeductList);
            }

        }
        return bSmtBomDetailDeductList;
    }


    private Map<String, BigDecimal> getPreManueByItemCodeMap(Map<String, BBomDetail> bBomDetailDTOMap, List<BSmtBomDetail> bSmtBomDetailAllList) {
        Map<String, BigDecimal> preManueByItemCodeMap = new HashMap<String, BigDecimal>();
        for (BSmtBomDetail bSmtBomDetail : bSmtBomDetailAllList) {
            String itemCode = bSmtBomDetail.getItemCode();
            BBomDetail bdd = bBomDetailDTOMap.get(bSmtBomDetail.getItemCode());
            if (bdd != null) {
                preManueByItemCodeMap.put(itemCode, bdd.getUsageCount());
            }
        }
        return preManueByItemCodeMap;
    }

    private boolean checkNeedUpdateDipFinishFlag(Map<String, BigDecimal> deductByItemCodeMap, Map<String, BigDecimal> preManueByItemCodeMap) {
        Set<String> preManueByItemCodeMapKeys = preManueByItemCodeMap.keySet();
        for (String key : preManueByItemCodeMapKeys) {
            if (deductByItemCodeMap.get(key) == null) {
                return false;
            }
            if (deductByItemCodeMap.get(key).compareTo(preManueByItemCodeMap.get(key)) != NumConstant.NUM_ZERO) {
                return false;
            }
        }
        return true;
    }

    /**
     * 获取reelId扣数分布式锁
     *
     * @param cfgHeaderId
     */
    private RedisLock getReelIdUpdateLock(String cfgHeaderId, String workStation) {
        if (workStation == null) {
            workStation = "";
        }
        final String key = "dipBoardScanning_" + cfgHeaderId + "_" + workStation;
        RedisLock redisLock = new RedisLock(key);
        boolean lock = redisLock.lock();
        if (!lock) {
            logger.info("获取分布式失败。key:{}", key);
            return null;
        }
        return redisLock;
    }

    /**
     * DIP过板写追溯记录
     *
     * @throws Exception
     */
    private void insertSmtSnMtlTracingInfo(List<SmtMachineMaterialMouting> insertSmtSnMtlTracingInfoList, DipBoardScanningDTO param, PsWipInfo psWipInfo, PsWorkOrderDTO psWorkOrder) throws Exception {
        List<SmtSnMtlTracingT> insertList = new ArrayList<>();
        for (SmtMachineMaterialMouting smtMachineMaterialMouting : insertSmtSnMtlTracingInfoList) {
            SMTScanDTO dto = new SMTScanDTO();
            dto.setCreateBy(param.getEmpNo());
            dto.setLineCode(psWorkOrder.getLineCode());
            dto.setWorkOrderNo(psWorkOrder.getWorkOrderNo());
            dto.setEntityId(smtMachineMaterialMouting.getEntityId());
            dto.setFactoryId(smtMachineMaterialMouting.getFactoryId());
            // 批次号
            String productBatchCode = psWipInfo.getAttribute1();
            // 适应原有方法,qty为实际扣料数据
            BigDecimal qty = smtMachineMaterialMouting.getReduceQty();
            Integer bomQty = smtMachineMaterialMouting.getBomQty() == null ? NumConstant.NUM_ZERO : smtMachineMaterialMouting.getBomQty();
            // eqpCount 抛料数量（已扣除，无需再次扣除，此处传0）
            int eqpCount = NumConstant.NUM_ZERO;
            // attribute1存料盘实际扣除数
            int type = MpConstant.TRACE_TYPE_ONE;
            SMTScanParamDTO smtScanParamDTO = new SMTScanParamDTO();
            smtScanParamDTO.setDto(dto);
            smtScanParamDTO.setSn(psWipInfo.getSn());
            smtScanParamDTO.setCurDate(new Date());
            smtScanParamDTO.setProductCode(psWorkOrder.getItemNo());
            smtScanParamDTO.setProductBatchCode(productBatchCode);
            smtScanParamDTO.setMounting(smtMachineMaterialMouting);
            smtScanParamDTO.setQty(qty);
            smtScanParamDTO.setEqpCount(eqpCount);
            smtScanParamDTO.setBomQty(new BigDecimal(bomQty));
            SmtSnMtlTracingT smtSnMtlTracingT = psScanMaterialService.generateOneSmtSnMtlTracingInfo(smtScanParamDTO, type);
            //产品物料追溯时，DIP过板的记录不关联
            smtSnMtlTracingT.setAttribute6(SMT_SN_MTL_TRACING_T_DIP_GB);
            insertList.add(smtSnMtlTracingT);
        }
        if (!CollectionUtils.isEmpty(insertList)) {
            smtSnMtlTracingTService.insertBatch(insertList);
        }
    }

    //上料表数据扣数成功才添加，不足时不添加
    private void addWipScanHistoryDTO(DipBoardScanningDTO param, List<WipScanHistoryDTO> wipScanHisttoryList, BSmtBomDetail bSmtBomDetail) {
        //写wip_scan_history记录
        WipScanHistoryDTO dto = new WipScanHistoryDTO();
        dto.setSmtScanId(java.util.UUID.randomUUID().toString());
        //按工位扫描才设置
        if (param.isScanByWorkStation()) {
            dto.setWorkStation(param.getWorkStation());
        }
        dto.setSn(param.getSn());
        dto.setItemNo(param.getItemNo());
        dto.setWorkOrderNo(param.getWorkOrderNo());
        //已回写
        dto.setIsWriteBackScan(Constant.FLAG_Y);
        dto.setCurrProcessCode(param.getProcessCode());
        dto.setSourceSys(MpConstant.WIP_SCAN_HISTORY_SOURCESYS_GB);
        dto.setFactoryId(new BigDecimal(param.getFactoryId()));
        dto.setCreateBy(param.getEmpNo());
        dto.setEnabledFlag(Constant.FLAG_Y);
        dto.setLineCode(param.getLineCode());
        if (StringUtils.isNotBlank(param.getSourceSys())) {
            dto.setRemark(param.getSourceSys());
        }
        wipScanHisttoryList.add(dto);
    }

    /**
     * pc_code_info扣数并写追溯记录,如果ReelID数量为0，删除机台在用信息
     * 机台在用表数量无需扣数
     *
     * @param bSmtBomDetailList
     * @param smtMachineMaterialMoutingList 按照createDate正序排序数据
     * @throws Exception
     */
    private List<SmtMachineMaterialMouting> reducePkCodeQyt(DipBoardScanningDTO param, List<BSmtBomDetail> bSmtBomDetailList, List<SmtMachineMaterialMouting> smtMachineMaterialMoutingList) throws Exception {
        // 是否满足条件可以进行扣数
        boolean enough = true;
        // 需删除的机台在用数据
        DipBoardScanningParamDTO dipBoardScanningParamDTO = new DipBoardScanningParamDTO();
        List<SmtMachineMaterialMouting> needDelMountingList = dipBoardScanningParamDTO.getNeedDelMountingList();
        // 需更新对应数量到pk_code_info的数据列表
        List<SmtMachineMaterialMouting> needUpdateToPkcodeInfoList = dipBoardScanningParamDTO.getNeedUpdateToPkcodeInfoList();
        // 存储物料不足的上料表数据
        List<BSmtBomDetail> notEnoughList = dipBoardScanningParamDTO.getNotEnoughList();
        //需删除箱内容表数据
        List<ContainerContentInfoDTO> needDeleteContainerContentList = dipBoardScanningParamDTO.getNeedDeleteContainerContentList();
        //需更新箱内容表数据
        List<ContainerContentInfoDTO> needUpdateContainerContentList = dipBoardScanningParamDTO.getNeedUpdateContainerContentList();
        //写追溯记录
        List<SmtMachineMaterialMouting> insertSmtSnMtlTracingInfoList = dipBoardScanningParamDTO.getInsertSmtSnMtlTracingInfoList();
        //扫描历史记录
        List<WipScanHistoryDTO> wipScanHisttoryList = new ArrayList<WipScanHistoryDTO>();
        for (BSmtBomDetail bSmtBomDetail : bSmtBomDetailList) {
            // 物料代码、物料数量不为空且物料数量不能小于或等于0
            if (bSmtBomDetail.getItemCode() == null
                    || bSmtBomDetail.getQty() == null
                    || bSmtBomDetail.getQty().compareTo(BigDecimal.ZERO) <= NumConstant.NUM_ZERO) {
                continue;
            }
            enough = this.reducePkCodeQytOfBSmtBomDetail(bSmtBomDetail, smtMachineMaterialMoutingList,
                    dipBoardScanningParamDTO);
            if (!enough) {
                notEnoughList.add(bSmtBomDetail);
            }
        }

        //写wip历史扫描记录
        addWipScanHistoryDTO(param, wipScanHisttoryList, bSmtBomDetailList.get(NUM_0));
        // 不为空表示存在物料不足
        if (!notEnoughList.isEmpty()) {
            // 组织并返回物料不足的工位、物料信息
            this.returnNotEnoughMsg(notEnoughList);
        }
        // 更新pk_code_info数量
        List<PkCodeInfo> pkCodeInfoList = new ArrayList<>();
        for (SmtMachineMaterialMouting smtMachineMaterialMouting : needUpdateToPkcodeInfoList) {
            PkCodeInfo pkCodeInfo = new PkCodeInfo();
            pkCodeInfo.setPkCode(smtMachineMaterialMouting.getObjectId());
            pkCodeInfo.setItemQty(smtMachineMaterialMouting.getQty());
            pkCodeInfoList.add(pkCodeInfo);
        }
        if (!CollectionUtils.isEmpty(pkCodeInfoList)) {
            // 更新pk_code_info数量
            List<List<PkCodeInfo>> updatePkCodeSplitList = CommonUtils.splitList(pkCodeInfoList, Constant.BATCH_SIZE);
            for (List<PkCodeInfo> updateList : updatePkCodeSplitList) {
                pkCodeInfoService.updatePkCodeInfoByIdSelectWithTime(updateList);
            }
        }
        if (!CollectionUtils.isEmpty(needDelMountingList)) {
            // 删除在用表数据
            logger.info("删除在用表数据：" + JSON.toJSONString(needDelMountingList));
            this.deleteSmtMachineMaterialMoutingByIdBatch(needDelMountingList);
        }
        updateContainerContentInfo(needUpdateContainerContentList, needDeleteContainerContentList);
        if (!CollectionUtils.isEmpty(wipScanHisttoryList)) {
            for (WipScanHistoryDTO wipDto : wipScanHisttoryList) {
                wipScanHistoryService.insert(wipDto);
            }
        }
        return insertSmtSnMtlTracingInfoList;
    }

    private void updateContainerContentInfo(List<ContainerContentInfoDTO> needUpdateContainerContentList, List<ContainerContentInfoDTO> needDeleteContainerContentList) throws Exception {
        //更新箱内容表数据
        if (!CollectionUtils.isEmpty(needUpdateContainerContentList)) {
            JSONArray jsonArray = new JSONArray();
            for (ContainerContentInfoDTO containerContentInfoDTO : needUpdateContainerContentList) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("contentId", containerContentInfoDTO.getContentId());
                jsonObject.put("lpn", containerContentInfoDTO.getLpn());
                jsonObject.put("contentQty", containerContentInfoDTO.getContentQty());
                jsonArray.add(jsonObject);
            }
            ProductionmgmtRemoteService.batchUpdateContainerContentInfoById(jsonArray);
        }
        //删除箱内容表数据
        if (!CollectionUtils.isEmpty(needDeleteContainerContentList)) {
            imesLogService.log(JSON.toJSONString(needDeleteContainerContentList), Constant.DIP_BOARD_DELETION_BOX_CONTENT);
            ProductionmgmtRemoteService.deleteContainerContentInfoDTOByIdList(needDeleteContainerContentList.stream().map(contentInfoDTO -> contentInfoDTO.getContentId()).collect(Collectors.toList()));
        }
    }

    //有续料盘，则将该ReelID删除，将续料盘作为容器码查询箱内容表，将箱内容表中的ReelID写入机台在用物料表
    private List<SmtMachineMaterialMoutingDTO>
    dealLastReelid(SmtMachineMaterialMouting smmm) throws Exception {
        SmtMachineMaterialMouting smtMachineMaterialMouting = new SmtMachineMaterialMouting();
        BeanUtils.copyProperties(smmm, smtMachineMaterialMouting);
        String nextReelRowid = smtMachineMaterialMouting.getNextReelRowid();
        smtMachineMaterialMoutingRepository.deleteSmtMachineMaterialMoutingById(smtMachineMaterialMouting);
        List<SmtMachineMaterialMoutingDTO> listMmm = new ArrayList<SmtMachineMaterialMoutingDTO>();
        List<ContainerContentInfoDTO> list = ProductionmgmtRemoteService.queryContentInfo(null, nextReelRowid);
        String combination = checkCombination(list) == true ? Constant.FLAG_Y : Constant.FLAG_N;
        for (ContainerContentInfoDTO containerContentInfoDTO : list) {
            smtMachineMaterialMouting.setMachineMaterialMoutingId(java.util.UUID.randomUUID().toString());
            smtMachineMaterialMouting.setQty(containerContentInfoDTO.getContentQty());
            smtMachineMaterialMouting.setObjectId(containerContentInfoDTO.getPkCode());
            // 220条码从pk_code_info表中取出更新机台在用，使得产品物料追溯查询220条码正确匹配。
            PkCodeInfo pkCodeInfo = pkCodeInfoService.getPkCodeInfoByCode(containerContentInfoDTO.getPkCode());
            if (pkCodeInfo != null) {
                smtMachineMaterialMouting.setSourceBatchCode(pkCodeInfo.getSourceBatchCode());
            }
            smtMachineMaterialMouting.setNextReelRowid("");
            smtMachineMaterialMouting.setLpn(nextReelRowid);
            smtMachineMaterialMouting.setLastUpdatedDate(new Date());
            smtMachineMaterialMouting.setCombination(combination);
            listMmm.add(SmtMachineMaterialMoutingAssembler.toDTO(smtMachineMaterialMouting));
        }
        smtMachineMaterialMoutingRepository.batchInsertSmtMachineMaterialMouting(listMmm);
        return listMmm;
    }

    //校验是否组合物料
    private boolean checkCombination(List<ContainerContentInfoDTO> list) {
        if (list.size() < NUM_2) {
            return false;
        }
        int count = 1;
        String itemCode = list.get(0).getItemCode();
        for (int i = 1; i < list.size(); i++) {
            ContainerContentInfoDTO containerContentInfoDTO = list.get(i);
            if (!StringUtils.equals(itemCode, containerContentInfoDTO.getItemCode())) {
                count++;
            }
        }
        if (count > 1) {
            return true;
        }
        return false;
    }

    /**
     * 组织并返回物料不足的工位、物料信息(抛出异常)
     *
     * @param notEnoughList
     * @throws Exception
     */
    private void returnNotEnoughMsg(List<BSmtBomDetail> notEnoughList) throws Exception {
        // map存储不同工位对应的物料不足情况
        Map<String, List<String>> map = new HashMap<>();
        Set<String> workStationSet = new HashSet<>();
        for (BSmtBomDetail tempSmtBomDetail : notEnoughList) {
            List<String> tempItemCodeList = map.get(tempSmtBomDetail.getLocationNo());
            if (tempItemCodeList == null) {
                tempItemCodeList = new ArrayList<>();
                map.put(tempSmtBomDetail.getLocationNo(), tempItemCodeList);
            }
            tempItemCodeList.add(tempSmtBomDetail.getItemCode());
        }
        StringBuffer workStations = new StringBuffer();
        StringBuffer itemCodes = new StringBuffer();
        for (Map.Entry<String, List<String>> entry : map.entrySet()) {
            if (workStations.length() > NumConstant.NUM_ZERO) {
                workStations.append(Constant.FORWARD_SLASH);
            }
            if (itemCodes.length() > NumConstant.NUM_ZERO) {
                itemCodes.append(Constant.FORWARD_SLASH);
            }
            workStations.append(entry.getKey());
            List<String> itemCodeList = entry.getValue();
            itemCodes.append(StringUtils.join(itemCodeList.toArray(), ","));
        }
        // 所需物料不足。提示 工位***对应的ReelID数量不足，请续料
        throw new Exception(CommonUtils.getLmbMessage(MessageId.ITEM_OF_REELID_IS_NOT_ENOUGH, workStations.toString(), itemCodes.toString()));
    }

    //校验箱list是否为空
    private boolean checkContainerContentInfoDTOList(List<ContainerContentInfoDTO> containerContentInfoDTOAllList, SmtMachineMaterialMouting smtMachineMaterialMouting) throws Exception {
        if (CollectionUtils.isEmpty(containerContentInfoDTOAllList)) {
            throw new Exception(CommonUtils.getLmbMessage(MessageId.ITEM_OF_REELID_IS_NOT_ENOUGH, smtMachineMaterialMouting.getLocationNo(), smtMachineMaterialMouting.getItemCode()));
        }
        return true;
    }

    //校验物料代码以及工位是否相等
    private boolean checkItemCodeAndLocationNO(SmtMachineMaterialMouting smtMachineMaterialMouting, BSmtBomDetail bSmtBomDetail) {
        if (bSmtBomDetail.getItemCode().equals(smtMachineMaterialMouting.getItemCode())
                && bSmtBomDetail.getLocationNo().equals(smtMachineMaterialMouting.getLocationNo())) {
            return true;
        }
        return false;
    }

    /**
     * 根据上料表数据进行扣数
     *
     * @return
     */
    private boolean reducePkCodeQytOfBSmtBomDetail(BSmtBomDetail bSmtBomDetail, List<SmtMachineMaterialMouting> smtMachineMaterialMoutingList, DipBoardScanningParamDTO dipBoardScanningParamDTO) throws Exception {
        // 仍然需要的数量。在当前料盘物料数量小于所需物料数量时将不为空。
        BigDecimal needQyt = bSmtBomDetail.getQty();
        String lpn = Constant.STR_EMPTY;
        List<ContainerContentInfoDTO> containerContentInfoDTOAllList = null;
        for (int i = 0; i < smtMachineMaterialMoutingList.size(); i++) {
            SmtMachineMaterialMouting smtMachineMaterialMouting = smtMachineMaterialMoutingList.get(i);
            if (smtMachineMaterialMouting.getQty() == null) {
                continue;
            }
            // 物料代码和工位都需匹配
            if (checkItemCodeAndLocationNO(smtMachineMaterialMouting, bSmtBomDetail)) {
                // 设置单位用量，写追溯记录使用
                smtMachineMaterialMouting.setBomQty(this.getBomQty(bSmtBomDetail));
                // 需更新对应pk_code_info数量
                String reelid = smtMachineMaterialMouting.getObjectId();
                if (!lpn.equals(smtMachineMaterialMouting.getLpn()) || containerContentInfoDTOAllList == null) {
                    lpn = smtMachineMaterialMouting.getLpn();
                    containerContentInfoDTOAllList = ProductionmgmtRemoteService.queryContentInfo(null, lpn);
                }
                checkContainerContentInfoDTOList(containerContentInfoDTOAllList, smtMachineMaterialMouting);
                ContainerContentInfoDTO containerContentInfoDTO = getContainerContentInfoDTO(reelid, containerContentInfoDTOAllList, smtMachineMaterialMouting);
                //是否组合物料
                boolean isCombineMaterials = Constant.FLAG_Y.equals(smtMachineMaterialMouting.getCombination());
                if (smtMachineMaterialMouting.getQty().compareTo(needQyt) > NumConstant.NUM_ZERO) {
                    // pkcode物料数大于或等于要扣除数量。直接减去数量
                    setSmtMachineMaterialMouting(needQyt, smtMachineMaterialMouting, true, smtMachineMaterialMouting.getQty().subtract(needQyt));
                    //更新箱内容表
                    containerContentInfoDTO.setContentQty(containerContentInfoDTO.getContentQty().subtract(needQyt));
                    dipBoardScanningParamDTO.getNeedUpdateContainerContentList().add(containerContentInfoDTO);
                    checkNeedUpdateToPkcodeInfo(smtMachineMaterialMouting, isCombineMaterials, dipBoardScanningParamDTO.getNeedUpdateToPkcodeInfoList());
                    //写追溯记录
                    dipBoardScanningParamDTO.getInsertSmtSnMtlTracingInfoList().add(smtMachineMaterialMouting);
                    needQyt = null;
                    break;
                } else if (smtMachineMaterialMouting.getQty().compareTo(needQyt) == NumConstant.NUM_ZERO) {
                    setSmtMachineMaterialMouting(needQyt, smtMachineMaterialMouting, true, BigDecimal.ZERO);
                    checkNeedUpdateToPkcodeInfo(smtMachineMaterialMouting, isCombineMaterials, dipBoardScanningParamDTO.getNeedUpdateToPkcodeInfoList());
                    //如果是最后一个
                    dealQtyIsZero(smtMachineMaterialMouting, containerContentInfoDTOAllList, dipBoardScanningParamDTO.getNeedDelMountingList(), smtMachineMaterialMoutingList);
                    //删除该reelid在箱内容表数据
                    dipBoardScanningParamDTO.getNeedDeleteContainerContentList().add(containerContentInfoDTO);
                    //删除箱list里面数据，还没实际更新到表，避免影响判断dealQtyIsZero
                    containerContentInfoDTOAllList.remove(containerContentInfoDTO);
                    //写追溯记录
                    dipBoardScanningParamDTO.getInsertSmtSnMtlTracingInfoList().add(smtMachineMaterialMouting);
                    needQyt = null;
                    break;
                } else {
                    // reelID数量不足,扣除数量等于剩余数量
                    needQyt = needQyt.subtract(smtMachineMaterialMouting.getQty());
                    setSmtMachineMaterialMouting(smtMachineMaterialMouting.getQty(), smtMachineMaterialMouting, false, BigDecimal.ZERO);
                    checkNeedUpdateToPkcodeInfo(smtMachineMaterialMouting, isCombineMaterials, dipBoardScanningParamDTO.getNeedUpdateToPkcodeInfoList());
                    //如果是最后一个
                    dealQtyIsZero(smtMachineMaterialMouting, containerContentInfoDTOAllList, dipBoardScanningParamDTO.getNeedDelMountingList(), smtMachineMaterialMoutingList);
                    //删除该reelid在箱内容表数据
                    dipBoardScanningParamDTO.getNeedDeleteContainerContentList().add(containerContentInfoDTO);
                    //删除箱list里面数据，还没实际更新到表，避免影响判断dealQtyIsZero
                    containerContentInfoDTOAllList.remove(containerContentInfoDTO);
                    //写追溯记录
                    dipBoardScanningParamDTO.getInsertSmtSnMtlTracingInfoList().add(smtMachineMaterialMouting);
                    continue;
                }
            }
        }
        // 在用物料全部遍历完成后needQyt仍然不为空则看是否有续料盘，有就扣减，没有就提示物料不足
        if (needQyt != null) {
            return false;
        }
        return true;
    }

    private void setSmtMachineMaterialMouting(BigDecimal needQyt, SmtMachineMaterialMouting smtMachineMaterialMouting, boolean b, BigDecimal zero) {
        smtMachineMaterialMouting.setEnough(b);
        smtMachineMaterialMouting.setReduceQty(needQyt);
        smtMachineMaterialMouting.setQty(zero);
    }

    private int getBomQty(BSmtBomDetail bSmtBomDetail) {
        return bSmtBomDetail.getQty() == null ? 0 : bSmtBomDetail.getQty().intValue();
    }

    private void checkNeedUpdateToPkcodeInfo(SmtMachineMaterialMouting smtMachineMaterialMouting, boolean isCombineMaterials, List<SmtMachineMaterialMouting> needUpdateToPkcodeInfoList) {
        if (!isCombineMaterials) {
            //非组合物料更新pkcodeinfo表
            needUpdateToPkcodeInfoList.add(smtMachineMaterialMouting);
        }
    }


    private ContainerContentInfoDTO getContainerContentInfoDTO(String reelid, List<ContainerContentInfoDTO> containerContentInfoDTOAllList, SmtMachineMaterialMouting smtMachineMaterialMouting) throws Exception {
        for (ContainerContentInfoDTO ccid : containerContentInfoDTOAllList) {
            if (reelid.equals(ccid.getPkCode())) {
                return ccid;
            }
        }
        logger.error("机台在用表 objectid " + reelid);
        logger.error("箱内容list " + JSON.toJSONString(containerContentInfoDTOAllList));
        throw new Exception(CommonUtils.getLmbMessage(MessageId.ITEM_OF_REELID_IS_NOT_ENOUGH, smtMachineMaterialMouting.getLocationNo(), smtMachineMaterialMouting.getItemCode()));
    }

    private void dealQtyIsZero(SmtMachineMaterialMouting smtMachineMaterialMouting,
                               List<ContainerContentInfoDTO> containerContentInfoDTOAllList,
                               List<SmtMachineMaterialMouting> needDelMountingList,
                               List<SmtMachineMaterialMouting> smtMachineMaterialMoutingList) throws Exception {
        if (isLastReelId(smtMachineMaterialMouting, containerContentInfoDTOAllList)) {
            if (StringUtils.isNotEmpty(smtMachineMaterialMouting.getNextReelRowid())) {
                List<SmtMachineMaterialMoutingDTO> smmmList = dealLastReelid(smtMachineMaterialMouting);
                //添加到list，以便继续扣数
                smtMachineMaterialMoutingList.addAll(SmtMachineMaterialMoutingAssembler.toSmtMachineMaterialMoutingList(smmmList));
            } else {
                //没续料盘更新机台表数量为空
                smtMachineMaterialMoutingRepository.updateSmtMachineMaterialMoutingById(smtMachineMaterialMouting);
            }
        } else {
            // ReelID数量为0，删除机台在用信息
            needDelMountingList.add(smtMachineMaterialMouting);
        }
    }


    /**
     * 是否最后一个
     * 是组合物料,判断该ReelID对应的物料代码是否在所属容器内中最后一个
     * 否则判断该ReelID是否是所属容器内中最后一个
     *
     * @return
     */
    private boolean isLastReelId(SmtMachineMaterialMouting smtMachineMaterialMouting, List<ContainerContentInfoDTO> containerContentInfoDTOAllList) {
        int count = 0;
        if (Constant.FLAG_Y.equals(smtMachineMaterialMouting.getCombination())) {
            String itemCode = smtMachineMaterialMouting.getItemCode();
            for (ContainerContentInfoDTO containerContentInfoDTO : containerContentInfoDTOAllList) {
                if (itemCode.equals(containerContentInfoDTO.getItemCode())) {
                    count++;
                }
            }
        } else {
            count = containerContentInfoDTOAllList.size();
        }
        if (count > NumConstant.NUM_ONE) {
            return false;
        }
        return true;
    }

    /**
     * 获取子工序非前加工上料表数据
     *
     * @param cfgHeaderId
     * @param param
     * @return
     */
    private List<BSmtBomDetail> getBSmtBomDetailListByCfgHeaderId(String cfgHeaderId, DipBoardScanningDTO param) throws Exception {
        Map<String, Object> record = new HashMap<>();
        record.put(CFG_HEADER_ID, cfgHeaderId);
        //非前加工
        record.put("preManue", Constant.FLAG_N);
        record.put("processCode", param.getProcessCode());
        if (param.isScanByWorkStation()) {
            // DIP段机台、模组、站位都是保存的工位数据
            record.put(LOCATION_NO, param.getWorkStation());
        }
        return bSmtBomDetailService.getList(record, null, null);
    }

    private List<BSmtBomDetail> getBSmtBomDetailList(List<BSmtBomDetail> list, DipBoardScanningDTO param) {
        List<BSmtBomDetail> newList = new ArrayList<BSmtBomDetail>();
        if (param.isScanByWorkStation()) {
            for (BSmtBomDetail bSmtBomDetail : list) {
                if (StringUtils.equals(param.getProcessCode(), bSmtBomDetail.getProcessCode()) && StringUtils.equals(param.getWorkStation(), bSmtBomDetail.getLocationNo())) {
                    newList.add(bSmtBomDetail);
                }
            }
        } else {
            for (BSmtBomDetail bSmtBomDetail : list) {
                if (StringUtils.equals(param.getProcessCode(), bSmtBomDetail.getProcessCode())) {
                    newList.add(bSmtBomDetail);
                }
            }
        }
        return newList;
    }

    /**
     * 获取上料表数据
     *
     * @param cfgHeaderId
     * @return
     */
    private List<BSmtBomDetail> getBSmtBomDetailListByCfgHeaderId(String cfgHeaderId) throws Exception {
        Map<String, Object> record = new HashMap<>();
        record.put(CFG_HEADER_ID, cfgHeaderId);
        //非前加工
        record.put("preManue", Constant.FLAG_N);
        return bSmtBomDetailService.getList(record, null, null);
    }

    /**
     * 获取在用物料数据(按照createDate正序排序)
     *
     * @param cfgHeaderId
     * @param param
     * @return
     * @throws Exception
     */
    private List<SmtMachineMaterialMouting> getMoutingWithPkCodeInfoByCfgHeaderId(String cfgHeaderId, DipBoardScanningDTO param) throws Exception {
        Map<String, Object> record = new HashMap<>();
        record.put(CFG_HEADER_ID, cfgHeaderId);
        if (param.isScanByWorkStation()) {
            // DIP段机台、模组、站位都是保存的工位数据
            record.put(LOCATION_NO, param.getWorkStation());
        }
        record.put("orderField", LAST_UPDATED_DATE);
        record.put("order", Constant.ASC);
        return this.selectMoutingWithPkCodeInfo(record);
    }

    /**
     * 获取在用物料数据(按照createDate正序排序)
     *
     * @param param
     * @return
     * @throws Exception
     */
    public List<SmtMachineMaterialMouting> getMoutingWithPkCodeInfoByWorkOrder(DipBoardScanningDTO param) throws Exception {
        Map<String, Object> record = new HashMap<>();
        record.put(WORK_ORDER, param.getWorkOrderNo());
        if (param.isScanByWorkStation()) {
            // DIP段机台、模组、站位都是保存的工位数据
            record.put(LOCATION_NO, param.getWorkStation());
        }
        record.put("orderField", LAST_UPDATED_DATE);
        record.put("order", Constant.ASC);
        return this.selectMoutingWithPkCodeInfo(record);
    }

    /**
     * 根据指令数据获取上料表id
     *
     * @param psWorkOrderDTO
     * @return
     * @throws Exception
     */
    private String getCfgHeaderIdByWorkOrder(PsWorkOrderDTO psWorkOrderDTO) throws Exception {
        String cfgHeaderId = "";
        List<PsWorkOrderSmt> workOrderSmtList = PlanscheduleRemoteService.getWorkOrderSMTByWorkOrder(psWorkOrderDTO);
        if (!CollectionUtils.isEmpty(workOrderSmtList)) {
            cfgHeaderId = workOrderSmtList.get(NumConstant.NUM_ZERO).getCfgHeaderId();
        }
        return cfgHeaderId;
    }

    /**
     * 校验指令上料是否已完成
     *
     * @param param
     * @return
     * @throws Exception
     */
    private boolean ifWorkOrderPickComplete(DipBoardScanningDTO param) throws Exception {
        boolean complete = false;
        Map<String, Object> map = new HashMap<>();
        map.put("lineCode", param.getLineCode());
        map.put("workOrder", param.getWorkOrderNo());
        // 上料类型。10：DIP上料
        map.put("mountType", NumConstant.STR_TEN);
        // 状态。2：对比完毕
        map.put("pickStatus", NumConstant.STR_TWO);
        map.put("enabledFlag", Constant.FLAG_Y);
        Long count = smtMachineMTLHistoryHService.getCount(map);
        if (count > NumConstant.NUM_ZERO) {
            complete = true;
        }
        return complete;
    }

    /**
     * 根据workOrderno和工厂id获取指令信息（workOrderno + factoryId唯一）
     *
     * @param workOrderno
     * @return
     * @throws Exception
     */
    private PsWorkOrderDTO getPsWorkOrderBasicByWorkOrderNoAndFactoryId(String workOrderno, String factoryId) throws Exception {
        PsWorkOrderDTO psWorkOrderDTO = null;
        Map<String, Object> map = new HashMap<>();
        map.put("workOrderNo", workOrderno);
        map.put("factoryId", factoryId);
        List<PsWorkOrderDTO> workOrderList = PlanscheduleRemoteService.getPsWorkOrderBasic(map);
        if (!CollectionUtils.isEmpty(workOrderList)) {
            psWorkOrderDTO = workOrderList.get(NumConstant.NUM_ZERO);
        }
        return psWorkOrderDTO;
    }

    @Override
    public List<SmtMachineMaterialMouting> selectSmtMachineMaterialMouting(SmtMachineMaterialMouting record) {
        return smtMachineMaterialMoutingRepository.selectSmtMachineMaterialMouting(record);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteAndInsertSmtMachineMaterialMouting(SmtMachineMaterialMouting record) throws Exception {
        SmtMachineMaterialMouting smt = new SmtMachineMaterialMouting();
        smt.setLpn(record.getLpn());
        List<SmtMachineMaterialMouting> smtMountingList = smtMachineMaterialMoutingRepository.selectSmtMachineMaterialMoutingSelective(smt);
        if (CollectionUtils.isEmpty(smtMountingList)) {
            throw new Exception(CommonUtils.getLmbMessage(MessageId.MOUNTING_DATA_IS_NULL));
        }
        PkCodeInfoDTO pkCode = new PkCodeInfoDTO();
        pkCode.setInPkCode(record.getInPkCode());
        String newLpn = record.getNewLpn();
        List<PkCodeInfo> pkCodeInfoList = pkCodeInfoRepository.getPickCodeList(pkCode);
        if (CollectionUtils.isEmpty(pkCodeInfoList)) {
            throw new Exception(CommonUtils.getLmbMessage(MessageId.PKCODE_IS_NULL));
        }
        BeanUtils.copyProperties(smtMountingList.get(0), record);
        smtMachineMaterialMoutingRepository.deleteSmtMachineMaterialMoutingById(smt);
        record.setLpn(newLpn);
        List<SmtMachineMaterialMoutingDTO> list = new ArrayList<>();
        for (PkCodeInfo info : pkCodeInfoList) {
            SmtMachineMaterialMoutingDTO recordDto = SmtMachineMaterialMoutingAssembler.toDTO(record);
            recordDto.setMachineMaterialMoutingId(UUID.randomUUID().toString());
            recordDto.setItemCode(info.getItemCode());
            recordDto.setItemName(info.getItemName());
            recordDto.setQty(info.getItemQty());
            recordDto.setObjectId(info.getPkCode());
            recordDto.setSourceBatchCode(info.getSourceBatchCode());
            list.add(recordDto);
        }
        smtMachineMaterialMoutingRepository.batchInsertSmtMachineMaterialMouting(list);
        return;
    }


    @Override
    public ServiceData<Object> verifyProcessCodeScan(DipBoardScanningDTO param) throws Exception {
        ServiceData<Object> ret = new ServiceData<Object>();
        if (!veifyScanByProcessCode(param.getProcessCode())) {
            param.setScanByWorkStation(false);
            // 查询指令数据
            PsWorkOrderDTO psWorkOrderDTO = this.getPsWorkOrderBasicByWorkOrderNoAndFactoryId(param.getWorkOrderNo(), param.getFactoryId());
            if (psWorkOrderDTO == null) {
                ret.setCode(new RetCode(RetCode.VALIDATIONERROR_CODE, MessageId.WORDER_ORDER_NOT_FOUND));
                return ret;
            }
            String cfgHeaderId = this.getCfgHeaderIdByWorkOrder(psWorkOrderDTO);
            if (StringUtils.isBlank(cfgHeaderId)) {
                ret.setCode(new RetCode(RetCode.VALIDATIONERROR_CODE, MessageId.B_SMT_BOM_DETAIL_OF_WORKORDER_IS_NULL));
                return ret;
            }
            logger.info("指令对应上料表id为: {}", cfgHeaderId);
            // 查询该子工序上料表数据
            List<BSmtBomDetail> bSmtBomDetailList = this.getBSmtBomDetailListByCfgHeaderId(cfgHeaderId, param);
            if (CollectionUtils.isEmpty(bSmtBomDetailList)) {
                // 上料表数据为空
                logger.info("上料表对应数据为空");
                ret.setCode(new RetCode(RetCode.VALIDATIONERROR_CODE, MessageId.B_SMT_BOM_DETAIL_OF_WORKORDER_IS_NULL));
                return ret;
            }

            Map paramMap = new HashMap();
            paramMap.put("processCode", param.getProcessCode());
            paramMap.put("productCode", psWorkOrderDTO.getItemNo());
            paramMap.put("preManue", Constant.FLAG_N);
            List<BBomDetail> bBomDetailDTOList = BasicsettingRemoteService.getBomListofDipNew(paramMap);
            Map<String, Object> params = new HashMap<>();
            params.put("workOrderNo", param.getWorkOrderNo());
            List<BBomTechnicalChangeInfo> bbtciList = BasicsettingRemoteService.getBBomTechnicalChangeInfoList(params);
            List<BBomDetail> bbtcbomList = toBBomDetailList(bbtciList);
            bBomDetailDTOList.addAll(bbtcbomList);

            if (CollectionUtils.isEmpty(bSmtBomDetailList) || CollectionUtils.isEmpty(bBomDetailDTOList)) {
                ret.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, RetCode.BUSINESSERROR_MSGID));
                return ret;
            }

            // list转map
            Map<String, BigDecimal> bSmtBomDetailMap = getDeductByItemCodeMap(bSmtBomDetailList);
            Map<String, BBomDetail> bBomDetailDTOMap = bBomDetailDTOList.stream().collect(Collectors.toMap(BBomDetail::getItemCode, a -> a, (k1, k2) -> k1));
            return verifyQtyAndBomQty(bSmtBomDetailMap, bBomDetailDTOMap);
        }
        ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        return ret;
    }

    private ServiceData<Object> verifyQtyAndBomQty(Map<String, BigDecimal> bSmtBomDetailMap, Map<String, BBomDetail> bBomDetailDTOMap) {
        ServiceData<Object> ret = new ServiceData<Object>();
        ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        if (bSmtBomDetailMap.size() > 0) {
            Set<String> bSmtBomDetailMapKeys = bSmtBomDetailMap.keySet();
            for (String key : bSmtBomDetailMapKeys) {
                BigDecimal qty = bSmtBomDetailMap.get(key);
                if (qty == null) {
                    continue;
                }
                BigDecimal usageCount = null;
                if (!bBomDetailDTOMap.containsKey(key)) {
                    ret.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.ITEM_CODE_NOT_EXIST_BOM_LIST));
                    return ret;
                }
                BBomDetail bbdd = bBomDetailDTOMap.get(key);
                usageCount = bbdd.getUsageCount();
                //大于则报错，物料代码用量大于bom用量，请修改子工序属性
                if (qty.compareTo(usageCount) == 1) {
                    ret.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.MATERIAL_QTY_GREATER_BOM_USAGE));
                    return ret;
                }
            }
        }
        return ret;
    }


    //根据料单代码获取bom清单标准用量map
    private Map<String, BBomDetail> getBomNumByProductCode(String productCode, String workOrderNo) throws Exception {
//		//先从缓存拿数据
//		String key=RedisKeyConstant.GET_BOM_LIST_KEY+productCode;
//		RedisHelper.get(key.getBytes());
        Map paramMap = new HashMap();
        paramMap.put("productCode", productCode);
        List<BBomDetail> bBomDetailDTOList = BasicsettingRemoteService.getBomListofDipNew(paramMap);
        if (CollectionUtils.isEmpty(bBomDetailDTOList)) {
            // 上料表数据为空
            logger.info("获取BOM清单list为空");
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.BOM_LIST_IS_NULL);
        }
        Map<String, Object> params = new HashMap<>();
        params.put("workOrderNo", workOrderNo);
        List<BBomTechnicalChangeInfo> bbtciList = BasicsettingRemoteService.getBBomTechnicalChangeInfoList(params);
        List<BBomDetail> bbtcbomList = toBBomDetailList(bbtciList);
        bBomDetailDTOList.addAll(bbtcbomList);
        Map<String, BBomDetail> bBomDetailDTOMap = bBomDetailDTOList.stream().collect(Collectors.toMap(BBomDetail::getItemCode, a -> a, (k1, k2) -> k1));
        if (bBomDetailDTOMap.size() < NumConstant.NUM_ONE) {
            // 上料表数据为空
            logger.info("获取BOM清单map为空");
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.BOM_LIST_IS_NULL);
        }
        return bBomDetailDTOMap;
    }


    //条码是否在扫描历史表（wip_scan_history）中该子工序的过板记录（source_sys= GB）的记录
    private List<WipScanHistory> getWipScanHistoryList(WipScanHistoryDTO wipScanHistoryDTO) throws Exception {
        List<WipScanHistory> list = wipScanHistoryService.getOverBoardList(wipScanHistoryDTO);
        return list;
    }


    //校验子工序是否按工位扫描
    @Override
    public boolean veifyScanByProcessCode(String processCode) throws Exception {
        BSProcess bSProcess = CrafttechRemoteService.getBSProcessInfo(processCode);
        if (bSProcess == null) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PROCESS_DETAILS_NOT_FOUND);
        }
        BigDecimal scanByStation = bSProcess.getScanByStation();
        if (scanByStation != null && scanByStation.compareTo(new BigDecimal(NumConstant.STR_ONE)) == 0) {
            return true;
        }
        return false;
    }

    @Override
    public List<SmtMachineMaterialMouting> getSmtMachineMaterialMoutingList(SmtMachineMaterialMoutingDTO dto) throws Exception {
        List<SmtMachineMaterialMouting> smtMachineMaterialMoutingList = smtMachineMaterialMoutingRepository.getSmtMachineMaterialMoutingList(dto);
        if (CollectionUtils.isEmpty(smtMachineMaterialMoutingList)) {
            return smtMachineMaterialMoutingList;
        }
        Set<String> reelIdList = smtMachineMaterialMoutingList.stream().map(i -> i.getObjectId()).filter(i -> StringUtils.isNotBlank(i)).collect(Collectors.toSet());
        List<PkCodeInfoDTO> pkCodeInfoDTOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(reelIdList)) {
            PkCodeInfoDTO pkCodeInfoDTO = new PkCodeInfoDTO();
            pkCodeInfoDTO.setPkCodeList(reelIdList.stream().collect(Collectors.toList()));
            pkCodeInfoDTOList = centerfactoryRemoteService.queryPkCodeInfoList(pkCodeInfoDTO);
        }
        if (CollectionUtils.isEmpty(pkCodeInfoDTOList)) {
            return smtMachineMaterialMoutingList;
        }
        for (SmtMachineMaterialMouting smt : smtMachineMaterialMoutingList) {
            if (!org.springframework.util.CollectionUtils.isEmpty(pkCodeInfoDTOList)) {
                List<PkCodeInfoDTO> pkDTOList = pkCodeInfoDTOList.stream().filter(i -> i.getPkCode().equals(smt.getObjectId())).collect(Collectors.toList());
                if (org.springframework.util.CollectionUtils.isEmpty(pkDTOList)) {
                    continue;
                }
                BigDecimal pkQty = BigDecimal.ZERO;
                for (PkCodeInfoDTO pkCodeInfoDTO : pkDTOList) {
                    pkQty = pkQty.add(CommonUtils.transformNullToZero(pkCodeInfoDTO.getRawQty()));
                }
                smt.setQty(pkQty);
            }
        }

        return smtMachineMaterialMoutingList;
    }


    /**
     * 手工补料-分页查询上料数据-总数量
     */
    @Override
    public long getMoutingInfoCount(SmtMachineMaterialMouting dto) throws Exception {
        return smtMachineMaterialMoutingRepository.getMoutingInfoCount(dto);
    }

    /**
     * 手工补料-分页查询上料数据
     */
    @Override
    public List<SmtMachineMaterialMouting> getMoutingInfo(SmtMachineMaterialMouting dto) throws Exception {
        //根据指令查询机台在用表,关联pk_code_info得到机台剩余剩余数量
        List<SmtMachineMaterialMouting> itemList = smtMachineMaterialMoutingRepository.getMoutingInfo(dto);
        //遍历机台在用数据，根据令、机台、模组、站位、物料代码查询提前备料表
        for (SmtMachineMaterialMouting e : itemList) {
            //根据令、机台、模组、站位、物料代码查询提前备料表
            SmtMachineMaterialPrepare queryDto = new SmtMachineMaterialPrepare();
            queryDto.setWorkOrder(e.getWorkOrder());
            queryDto.setMachineNo(e.getMachineNo());
            queryDto.setModuleNo(e.getModuleNo());
            queryDto.setLocationNo(e.getLocationNo());
            queryDto.setItemCode(e.getItemCode());
            List<SmtMachineMaterialPrepare> preList = smtMachineMaterialPrepareRepository.getPrepareInfo(queryDto);
            if (CollectionUtils.isEmpty(preList)) {
                SmtMachineMaterialPrepare queryItemDto = new SmtMachineMaterialPrepare();
                queryItemDto.setItemCode(e.getItemCode());
                preList = smtMachineMaterialPrepareRepository.getPrepareInfoByItemCode(queryItemDto);
            }
            String reeildList = "";
            BigDecimal preQty = new BigDecimal(0);
            if (CollectionUtils.isEmpty(preList)) {
                e.setPreQty(preQty);
                e.setObjectId(reeildList);
                continue;
            }
            //将提前备料数量相加，得到提前备料总数料
            for (SmtMachineMaterialPrepare preDto : preList) {
                preQty = preQty.add(preDto.getQty());
            }
            e.setPreQty(preQty);
            //拼接提前备料REELID
            reeildList = preList.stream().map(SmtMachineMaterialPrepare::getObjectId).collect(Collectors.joining(","));
            e.setReelidList(reeildList);
        }
        return itemList;
    }

    /**
     * 手工补料-计算提前备料数量
     */
    public void calPreNum(SmtMachineMaterialMouting reDto, List<SmtMachineMaterialMouting> eItemList, List<SmtMachineMaterialMouting> preList) throws Exception {
        List<SmtMachineMaterialMouting> colList = preList.stream().filter(preDto -> eItemList.get(0).getItemCode()
                .equals(preDto.getItemCode())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(colList)) {
            //拼接提前备料REELID
            String reeildList = colList.stream().map(SmtMachineMaterialMouting::getObjectId).collect(Collectors.joining(","));
            BigDecimal preQty = new BigDecimal(0);
            //将提前备料数量相加，得到提前备料总数料
            for (SmtMachineMaterialMouting e : colList) {
                if (e.getPreQty() != new BigDecimal(0) && StringHelper.isNotEmpty(e.getPreQty().toString())) {
                    preQty = preQty.add(e.getPreQty());
                }
            }
            reDto.setObjectId(reeildList);
            reDto.setPreQty(preQty);
        }
    }

    //根据容器删除机台在用信息
    @Override
    public boolean deleteSmtMachineMaterialMoutingBatch(List<String> lpns) throws Exception {
        List<List<String>> totalList = CommonUtils.splitList(lpns, CommonConst.BATCH_SIZE);
        int count = NumConstant.NUM_ZERO;
        for (List<String> spList : totalList) {
            count += smtMachineMaterialMoutingRepository.deleteSmtMachineMaterialMoutingBatch(spList);
        }
        if (count == lpns.size()) {
            return true;
        }
        return false;
    }

    /**
     * 容器解绑删除机台在用
     *
     * @param dto
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteMoutingByUnbind(SmtMachineMaterialMoutingDTO dto) throws Exception {
        //删除该容器的机台在用数据
        SmtMachineMaterialMouting paramDto = new SmtMachineMaterialMouting();
        paramDto.setLpn(dto.getLpn());
        smtMachineMaterialMoutingRepository.deleteMountingByLpn(paramDto);

        //判断容器码是否在续料盘，如果存在需要清空 DIP接料时，按容器接料
        Map<String, Object> map = new HashMap<>();
        map.put("nextReelRowid", dto.getLpn());
        //查询当前REELID所在续料盘机台数据
        List<SmtMachineMaterialMouting> nextReelidMountingList = smtMachineMaterialMoutingRepository
                .getList(map);
        if (CollectionUtils.isEmpty(nextReelidMountingList)) {
            return;
        }
        //更新续料盘数据为空
        List<List<SmtMachineMaterialMouting>> lists1 =
                CommonUtils.splitList(nextReelidMountingList, NumConstant.NUM_500);
        for (List<SmtMachineMaterialMouting> eList1 : lists1) {
            smtMachineMaterialMoutingRepository.updateNextReelRowId(eList1);
        }
        return;
    }

    /**
     * 产线物料消耗查询机台在用
     */
    @Override
    public List<SmtMachineMaterialMouting> getMoutingList(SmtMachineMaterialMoutingDTO dto) throws Exception {
        return smtMachineMaterialMoutingRepository.getMoutingList(dto);
    }

    /**
     * 根据指令号集合批量修改flag。
     *
     * @param workOrderNoSet
     * @throws Exception
     */
    @Override
    public void updateSmtMoutingEnabledFlag(String workOrderNoSet) throws Exception {
        smtMachineMaterialMoutingRepository.updateSmtMoutingEnabledFlag(workOrderNoSet);
    }

    @Override
    public OneKeySwitchMoutingDTO checkSmtLocationLineInfo(String locationSn) throws MesBusinessException {
        OneKeySwitchMoutingDTO oneKeySwitch = BasicsettingRemoteService.getSmtLocationLineInfo(locationSn);
        if (oneKeySwitch == null) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.LOCATION_NOT_EXIST);
        }
        if (!Constant.FLAG_Y.equals(oneKeySwitch.getSupChgLineOnModule())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.LINE_NO_SUP_MODEL_CHG,
                    new String[]{oneKeySwitch.getLineCode()});
        }
        if (!Constant.FLAG_Y.equals(oneKeySwitch.getIntelligentFeederFlag())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.LINE_NO_SUP_IN_FEEDER,
                    new String[]{oneKeySwitch.getLineCode()});
        }
        return oneKeySwitch;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int oneKeySwitchMouting(OneKeySwitchMoutingDTO dto) throws Exception {
        if (StringUtils.isBlank(dto.getTarWorkOrder())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.TAR_WORK_ORDER_IS_NULL);
        }
        RedisLock redisLock = new RedisLock(RedisKeyConstant.ONE_KEY_SWITCH_MOUTING_LOCK + dto.getLineCode() + dto.getModuleNo(), NumConstant.NUM_1800);
        try {
            if (!redisLock.lock()) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.THE_THREAD_BODY_IS_SWITCHING_WITH_ONE_BUTTON);
            }
            // 获取目标上料头表id
            Map<String, PsWorkOrderSmtDTO> woSmt = PlanscheduleRemoteService.getWoSmtByWos(dto.getTarWorkOrder(), dto.getCurWorkOrder());
            PsWorkOrderSmtDTO tarSmt = woSmt.get(dto.getTarWorkOrder());
            if (tarSmt == null || StringUtils.isBlank(tarSmt.getCfgHeaderId())) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.WORK_ORDER_NO_CFG_ID);
            }

            dto.setCfgHeaderId(tarSmt.getCfgHeaderId());
            // 获取目标上料详细
            Map<String, List<BSmtBomDetail>> woSmtDetail = getBSmtBomDetail(woSmt, dto);
            List<BSmtBomDetail> tarSmtDetail = woSmtDetail.get(dto.getTarWorkOrder());
            if (CollectionUtils.isEmpty(tarSmtDetail)) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.WORK_ORDER_NO_SMT_DETAIL,
                        new String[]{dto.getTarWorkOrder(), dto.getModuleNo()});
            }

            // 面别校验
            checkCraftSection(dto);

            // 校验机台在用存在Y，排除共用料站位
            checkMountingWhenHaveCommonItem(woSmt, woSmtDetail, dto);
            // 当前指令和目标指令同批次时，同站位物料特殊处理，
            List<String> sameLocationItems = this.dealSameLocationItem(dto, woSmt, woSmtDetail);

            // 对比目标指令上料表和备料表（排除同站位物料），判断是否备料完成
            Pair<List<String>,List<SmtMachineMaterialPrepare>> pair = this.compareBomAndPrepare(dto, woSmtDetail,
                    tarSmt, sameLocationItems);
            List<String> prepareIds = pair.getFirst();
            List<SmtMachineMaterialPrepare> prepareList = pair.getSecond();
            // 将备料表数据移入机台在用
            if (!CollectionUtils.isEmpty(prepareIds)) {
                // 写上料历史
                this.insertMTLHistory(prepareIds, dto, INT_0);
                // 写入reelId轨迹表
                this.insertReelIdHistory(prepareIds, dto);
                this.movePrepareToMouting(prepareIds, prepareList, dto);
            }
            return 0;
        } finally {
            if (redisLock != null) {
                redisLock.unlock();
            }
        }
    }

    /**
     * 当前批次AB指令是否在同一线体，如在同一线体则看当前切换指令是否为首指令，如不为首指令则判断首指令是否有当前模组的一键切换记录/转机扫描
     *
     * @param dto
     * @throws Exception
     */
    private void checkCraftSection(OneKeySwitchMoutingDTO dto) {
        // 切换指令的所属批次的AB面是否排在同一线体，排在同一线体的则看当前切换指令是否为首指令
        String sourceTask = dto.getTarWorkOrder().substring(0, 7);
        Map<String, String> params = new HashMap<>();
        params.put(Constant.SOURCE_TASK, sourceTask);
        params.put(Constant.LINE_CODE, dto.getLineCode());
        List<PsEntityPlanBasic> psEntityPlanBasicList = PlanscheduleRemoteService.selectSmtWorkOrderBySourceTask(params);
        if (CollectionUtils.isEmpty(psEntityPlanBasicList) || psEntityPlanBasicList.size() < NumConstant.NUM_TWO) {
            return;
        }
        // 获取首指令
        psEntityPlanBasicList.sort(Comparator.comparing(PsEntityPlanBasic::getRemark));
        PsEntityPlanBasic firstWorkOrder = psEntityPlanBasicList.get(0);
        // 切换指令为首指令
        if (dto.getTarWorkOrder().equals(firstWorkOrder.getWorkOrderNo())) {
            return;
        }
        // 切换指令不为首指令时，根据首指令上料表在这个模组是否有物料,校验首指令在当前模组是否有转机记录
        String cfgHeaderId = firstWorkOrder.getCfgHeaderId();
        if (StringUtils.isBlank(cfgHeaderId)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.WORK_ORDER_NO_CFG_ID);
        }
        BSmtBomDetail bomDetail = new BSmtBomDetail();
        bomDetail.setCfgHeaderId(cfgHeaderId);
        bomDetail.setLineCode(firstWorkOrder.getLineCode());
        bomDetail.setModuleNo(dto.getModuleNo());
        bomDetail.setAttr1(firstWorkOrder.getSourceTask());
        int count = smtBomDetailRepository.countBomDetailByModuleNo(bomDetail);
        // 首指令上料表在这个模组没有物料,不校验
        if (count < Constant.INT_1) {
            return;
        }

        // 切换指令不为首指令时，判断首指令是否有当前模组的一键切换或转机扫描记录
        SmtMachineMTLHistoryL record = new SmtMachineMTLHistoryL();
        record.setLineCode(firstWorkOrder.getLineCode());
        record.setWorkOrder(firstWorkOrder.getWorkOrderNo());
        record.setModuleNo(dto.getModuleNo());
        record.setMountTypeList(new ArrayList<String>() {{
            add(NumConstant.STR_ONE);
            add(NumConstant.STR_FOURTEEN);
        }});

        // 没有记录，报错提示：当前模组不存在{0}首指令的转机记录，请先切换首指令
        if (historyLRepository.countHistoryByWorkOrder(record) < Constant.INT_1) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SWITCH_FIRST_WORK_ORDER,
                    new String[]{firstWorkOrder.getWorkOrderNo()});
        }
    }

    private void checkMountingWhenHaveCommonItem(Map<String, PsWorkOrderSmtDTO> woSmt, Map<String, List<BSmtBomDetail>> woSmtDetail,
                                                 OneKeySwitchMoutingDTO dto) throws Exception {
        PsWorkOrderSmtDTO tarSmt = woSmt.get(dto.getTarWorkOrder()) == null ? new PsWorkOrderSmtDTO() : woSmt.get(dto.getTarWorkOrder());
        PsWorkOrderSmtDTO curSmt = woSmt.get(dto.getCurWorkOrder()) == null ? new PsWorkOrderSmtDTO() : woSmt.get(dto.getCurWorkOrder());
        // 找出当前线体，模组存在的有效机台在用，
        List<SmtMachineMaterialMouting> smtMachMatMoutList = smtMachineMaterialMoutingRepository.selectByLineModel
                (dto.getLineCode(), dto.getModuleNo());
        if (CollectionUtils.isEmpty(smtMachMatMoutList)) {
            return;
        }
        // 指令批次不同，那么没有共用料，机台存在Y，报错
        if (curSmt.getSourceTask() == null || !curSmt.getSourceTask().equals(tarSmt.getSourceTask())) {
            // 当前线体、模组存在有效机台在用，不可切换
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.LINE_MODULE_HAS_MOUTING,
                    new String[]{dto.getLineCode(), dto.getModuleNo()});
        }
        // 批次相同，找共用料
        List<String> commonItemLocationAndCode = getCommonItemLocationAndCode(woSmtDetail, dto);

        // 提前备料已经备的。当成非共用料2022-09-22 BA核对过
        List<String> preList = getPreparationList(dto);

        // 校验有效的机台是否都是共用料
        for (int i = 0; i < smtMachMatMoutList.size(); i++) {
            SmtMachineMaterialMouting entity = smtMachMatMoutList.get(i);
            String key = entity.getLocationNo() + entity.getItemCode();
            // 为Y的不是共用料，则报错。
            if (!commonItemLocationAndCode.contains(key) || preList.contains(key)) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.LINE_MODULE_HAS_MOUTING,
                        new String[]{dto.getLineCode(), dto.getModuleNo()});
            }
        }

        // 目标指令该模组所有站位都是共用物料，且目标指令存在有效机台在用，则不允许切换
        checkAllComItem(dto, smtMachMatMoutList, woSmtDetail.get(dto.getTarWorkOrder()).stream().map(e -> e.getLocationNo() + e.getItemCode()).collect(Collectors.toList()), commonItemLocationAndCode);
    }

    /**
     * 找共用料
     *
     * @param woSmtDetail
     * @param dto
     */
    public List<String> getCommonItemLocationAndCode(Map<String, List<BSmtBomDetail>> woSmtDetail,
                                                     OneKeySwitchMoutingDTO dto) {
        List<String> commonItemLocationAndCode = new ArrayList<>();
        List<BSmtBomDetail> curSmtDetail = woSmtDetail.get(dto.getCurWorkOrder());
        List<String> curLocationItem = new ArrayList<>();
        if (!CollectionUtils.isEmpty(curSmtDetail)) {
            // 当前指令站位物料集合
            curLocationItem = curSmtDetail.stream().map(e -> e.getLocationNo() + e.getItemCode()).collect(Collectors.toList());
        }

        for (int i = 0; i < woSmtDetail.get(dto.getTarWorkOrder()).size(); i++) {
            BSmtBomDetail entity = woSmtDetail.get(dto.getTarWorkOrder()).get(i);
            String key = entity.getLocationNo() + entity.getItemCode();
            if (curLocationItem.contains(key)) {
                commonItemLocationAndCode.add(key);
            }
        }
        return commonItemLocationAndCode;
    }

    /**
     * 目标指令该模组所有站位都是共用物料，且目标指令存在有效机台在用，则不允许切换
     *
     * @param dto
     * @param smtMachMatMoutList
     * @param tarLocationItem
     * @param commonItemLocationAndCode
     * @throws MesBusinessException
     */
    public void checkAllComItem(OneKeySwitchMoutingDTO dto, List<SmtMachineMaterialMouting> smtMachMatMoutList,
                                List<String> tarLocationItem, List<String> commonItemLocationAndCode) throws MesBusinessException {
        // 目标指令是否存在有效机台在用
        boolean tarHasMt = smtMachMatMoutList.stream().anyMatch(mt -> dto.getTarWorkOrder().equals(mt.getWorkOrder()));
        if (tarHasMt && commonItemLocationAndCode.containsAll(tarLocationItem)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.LINE_MODULE_HAS_MOUTING,
                    new String[]{dto.getTarWorkOrder() + Constant.COMMA + dto.getLineCode(), dto.getModuleNo()});
        }
    }

    /**
     * 获取指令上料表详细
     *
     * @param woSmt
     * @param dto
     * @return
     */
    private Map<String, List<BSmtBomDetail>> getBSmtBomDetail(Map<String, PsWorkOrderSmtDTO> woSmt, OneKeySwitchMoutingDTO dto) {
        Map<String, List<BSmtBomDetail>> reMap = new HashMap<>();
        BSmtBomDetail detail = new BSmtBomDetail();
        detail.setLineCode(dto.getLineCode());
        detail.setModuleNo(dto.getModuleNo());
        detail.setCfgHeaderIdList(woSmt.entrySet().stream().map(e -> e.getValue().getCfgHeaderId()).collect(Collectors.toList()));
        List<BSmtBomDetail> bomDetails = smtBomDetailRepository.selectBSmtBomDetailListById(detail);
        if (CollectionUtils.isEmpty(bomDetails)) {
            return reMap;
        }
        Map<String, List<BSmtBomDetail>> detailMap = bomDetails.stream().collect(Collectors.groupingBy(BSmtBomDetail::getCfgHeaderId));
        woSmt.entrySet().forEach(e -> reMap.put(e.getKey(), detailMap.get(e.getValue().getCfgHeaderId())));
        return reMap;
    }

    /**
     * 当前指令和目标指令同批次时，同站位物料特殊处理
     *
     * @param dto
     * @param woSmt
     * @param woSmtDetail
     * @return
     * @throws Exception
     */
    private List<String> dealSameLocationItem(OneKeySwitchMoutingDTO dto, Map<String, PsWorkOrderSmtDTO> woSmt,
                                              Map<String, List<BSmtBomDetail>> woSmtDetail) throws Exception {
        if (StringUtils.isBlank(dto.getCurWorkOrder())) {
            return new ArrayList<>();
        }
        PsWorkOrderSmtDTO tarSmt = woSmt.get(dto.getTarWorkOrder());
        PsWorkOrderSmtDTO curSmt = woSmt.get(dto.getCurWorkOrder());
        // 当前指令在模组上无上料表信息，跳过共用料逻辑处理，直接按非共用料处理
        if (curSmt == null || StringUtils.isBlank(curSmt.getCfgHeaderId())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.WORK_ORDER_NO_CFG_ID);
        }
        // 指令批次不同，不处理
        if (curSmt.getSourceTask() == null || !curSmt.getSourceTask().equals(tarSmt.getSourceTask())) {
            return new ArrayList<>();
        }
        List<BSmtBomDetail> curSmtDetail = woSmtDetail.get(dto.getCurWorkOrder());
        if (CollectionUtils.isEmpty(curSmtDetail)) {
            return new ArrayList<>();
        }
        // 当前指令站位物料集合
        List<String> curLocationItem = curSmtDetail.stream().map(e -> e.getLocationNo() + e.getItemCode()).collect(Collectors.toList());
        List<String> preList = getPreparationList(dto);

        List<BSmtBomDetail> sameSmtDetail = woSmtDetail.get(dto.getTarWorkOrder()).stream()
                // 当前指令和目标指令同站位物料
                .filter(e -> curLocationItem.contains(e.getLocationNo() + e.getItemCode()))
                // 过滤 共用站位在preppare 有数据的 ，目标指令+站位+物料代码
                .filter(e -> !preList.contains(e.getLocationNo() + e.getItemCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(sameSmtDetail)) {
            return new ArrayList<>();
        }
        // 获取当前批次同站位物料机台在用信息
        List<SmtMachineMaterialMouting> sameMouting = smtMachineMaterialMoutingRepository.getBySmtBomDetail(sameSmtDetail, dto);
        if (CollectionUtils.isEmpty(sameMouting)) {
            return new ArrayList<>();
        }
        // 原指令物料机台在用信息置为失效的id集合
        List<String> moutingIds = sameMouting.stream().map(SmtMachineMaterialMouting::getMachineMaterialMoutingId).collect(Collectors.toList());
        // 筛选出有nextReelId的当前指令机台在用数据
        List<SmtMachineMaterialMouting> haveNextReelMoutingList = sameMouting.stream().filter(e -> StringUtils.isNotEmpty
                (e.getNextReelRowid())).collect(Collectors.toList());
        // 处理没有下一指令的共用料机台
        handlerNotHaveNextReel(sameMouting, haveNextReelMoutingList, dto);
        // 处理存在nextReelId的机台在用。
        handlerHaveNextReel(haveNextReelMoutingList, dto);
        // 将原指令物料机台在用信息置为失效
        List<List<String>> splitMoutingIds = CommonUtils.splitList(moutingIds, Constant.IN_MAX_BATCH_SIZE);
        for (List<String> subList : splitMoutingIds) {
            smtMachineMaterialMoutingRepository.disableSameLocationMouting(subList, dto.getEmpNo());
        }
        // 写上料历史
        this.insertMTLHistory(moutingIds, dto, Constant.INT_1);
        return sameMouting.stream().map(e -> e.getLocationNo() + e.getItemCode()).collect(Collectors.toList());
    }

    private void handlerHaveNextReel(List<SmtMachineMaterialMouting> haveNextReelMoutingList, OneKeySwitchMoutingDTO dto) {
        if (CollectionUtils.isEmpty(haveNextReelMoutingList)) {
            return;
        }
        // 组合objectId和nextReelId
        List<String> pkCodeList = new ArrayList<>();
        for (int i = 0; i < haveNextReelMoutingList.size(); i++) {
            SmtMachineMaterialMouting entity = haveNextReelMoutingList.get(i);
            pkCodeList.add(entity.getObjectId());
            pkCodeList.add(entity.getNextReelRowid());
        }
        // 查询pkCode表item_qty信息
        List<PkCodeInfo> pkCodeInfoList = new ArrayList<>();
        List<List<String>> pkCodeLists = CommonUtils.splitList(pkCodeList, Constant.IN_MAX_BATCH_SIZE);
        for (List<String> subList : pkCodeLists) {
            List<PkCodeInfo> subPkCodeInfo = pkCodeInfoService.getListByPkCodes(subList);
            if (!CollectionUtils.isEmpty(subPkCodeInfo)) {
                pkCodeInfoList.addAll(subPkCodeInfo);
            }
        }
        List<SmtMachineMaterialMoutingDTO> dtoList = generateMoutingDtoList(dto, haveNextReelMoutingList, pkCodeInfoList);
        smtMachineMaterialMoutingRepository.batchInsertSmtMachineMaterialMouting(dtoList);
        // 相应的nextReel在提前备料的要删除。
        List<String> nextReelIdList = haveNextReelMoutingList.stream().map(e -> e.getNextReelRowid()).collect(Collectors.toList());
        String workOrder = haveNextReelMoutingList.get(0).getWorkOrder();
        List<List<String>> nextReelIdLists = CommonUtils.splitList(nextReelIdList, Constant.IN_MAX_BATCH_SIZE);
        for (List<String> subList : nextReelIdLists) {
            smtMachineMaterialPrepareRepository.deleteBatchByWorkNoAndObjectIdList(subList, workOrder);
        }
    }

    private List<SmtMachineMaterialMoutingDTO> generateMoutingDtoList(OneKeySwitchMoutingDTO dto, List<SmtMachineMaterialMouting>
            haveNextReelMoutingList, List<PkCodeInfo> pkCodeInfoList) {
        List<SmtMachineMaterialMoutingDTO> resultList = new ArrayList<>();
        if (CollectionUtils.isEmpty(pkCodeInfoList) || CollectionUtils.isEmpty(haveNextReelMoutingList)) {
            return resultList;
        }
        // 转换成key 为pkCode, value 为 item_qty的map
        Map<String, BigDecimal> mapPkCodeToQty = pkCodeInfoList.stream().collect(Collectors.toMap(k -> k.getPkCode(),
                v -> v.getItemQty(), (oldValue, newValue) -> newValue));
        for (int i = 0; i < haveNextReelMoutingList.size(); i++) {
            SmtMachineMaterialMouting entity = haveNextReelMoutingList.get(i);
            SmtMachineMaterialMoutingDTO moutingDto = new SmtMachineMaterialMoutingDTO();
            BeanUtils.copyProperties(entity, moutingDto);
            moutingDto.setMachineMaterialMoutingId(UUID.randomUUID().toString());
            moutingDto.setWorkOrder(dto.getTarWorkOrder());
            moutingDto.setCreateUser(dto.getEmpNo());
            moutingDto.setLastUpdatedBy(dto.getEmpNo());
            BigDecimal objectQty = mapPkCodeToQty.get(entity.getObjectId());
            BigDecimal curQty = Objects.isNull(objectQty) ? BigDecimal.ZERO : objectQty;
            BigDecimal nextQty = mapPkCodeToQty.get(entity.getNextReelRowid());
            BigDecimal nextReelQty = Objects.isNull(nextQty) ? BigDecimal.ZERO : nextQty;
            moutingDto.setQty(curQty.add(nextReelQty));
            moutingDto.setObjectId(entity.getNextReelRowid());
            moutingDto.setNextReelRowid(null);
            resultList.add(moutingDto);
        }
        return resultList;
    }

    private void handlerNotHaveNextReel(List<SmtMachineMaterialMouting> sameMouting, List<SmtMachineMaterialMouting>
            haveNextReelMoutingList, OneKeySwitchMoutingDTO dto) {
        Set<String> haveNextReelIdList = haveNextReelMoutingList.stream().map(SmtMachineMaterialMouting
                ::getMachineMaterialMoutingId).collect(Collectors.toSet());
        List<String> machineMaterialMoutingIdList = new ArrayList<>();
        for (int i = 0; i < sameMouting.size(); i++) {
            SmtMachineMaterialMouting entity = sameMouting.get(i) == null ? new SmtMachineMaterialMouting() : sameMouting.get(i);
            // 不包含在nextReelSet中的直接复制同站位物料机台在用信息到目标指令
            if (!haveNextReelIdList.contains(entity.getMachineMaterialMoutingId())) {
                machineMaterialMoutingIdList.add(entity.getMachineMaterialMoutingId());
            }
        }
        List<List<String>> machineMaterialMoutingIdLists = CommonUtils.splitList(machineMaterialMoutingIdList, Constant.IN_MAX_BATCH_SIZE);
        // 复制同站位物料机台在用信息到目标指令，
        for (List<String> subList : machineMaterialMoutingIdLists) {
            smtMachineMaterialMoutingRepository.copySameLocationMouting(subList, dto);
        }
    }

    /**
     * 获取目标指令已经在提前备料表的数据
     *
     * @param dto 参数
     * @return 提前备料参数
     */
    private List<String> getPreparationList(OneKeySwitchMoutingDTO dto) {
        // 过滤 共用站位在preppare 有数据的 ，目标指令+站位+物料代码
        SmtMachineMaterialPrepare smtMachineMaterialPrepare = new SmtMachineMaterialPrepare();
        smtMachineMaterialPrepare.setWorkOrder(dto.getTarWorkOrder());
        smtMachineMaterialPrepare.setLineCode(dto.getLineCode());
        smtMachineMaterialPrepare.setModuleNo(dto.getModuleNo());
        List<SmtMachineMaterialPrepare> prepares = smtMachineMaterialPrepareRepository.getPrepareInfoWithFeeder(dto);
        if (CollectionUtils.isEmpty(prepares)) {
            prepares = new LinkedList<>();
        }
        List<String> preList = prepares.stream().map(item -> item.getLocationNo() + item.getItemCode())
                .collect(Collectors.toList());
        return preList;
    }

    /**
     * 对比目标指令上料表和备料表（排除同站位物料）
     *
     * @param dto
     * @param woSmtDetail
     * @param sameLocationItems 未提前备料的共用料的机台在用信息
     */
    private Pair<List<String>,List<SmtMachineMaterialPrepare>> compareBomAndPrepare(OneKeySwitchMoutingDTO dto, Map<String, List<BSmtBomDetail>> woSmtDetail,
                                              PsWorkOrderSmtDTO tarSmt, List<String> sameLocationItems) throws Exception {
        List<BSmtBomDetail> tarSmtDetail = woSmtDetail.get(dto.getTarWorkOrder());
        // 排除未提前备料的共用料的机台在用信息后，若目标指令的上料集合为空，则无需备料
        List<BSmtBomDetail> smtDetail = tarSmtDetail.stream()
                .filter(e -> !sameLocationItems.contains(e.getLocationNo() + e.getItemCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(smtDetail)) {
            return Pair.of(new ArrayList<>(), new ArrayList<>());
        }
        // 查询目标指令备料信息
        List<SmtMachineMaterialPrepare> prepares = smtMachineMaterialPrepareRepository.getPrepareInfoWithFeeder(dto);

        // noPreSmt--未提前备料的数据
        List<BSmtBomDetail> noPreSmt = new ArrayList<>();
        // preparesFilterSmt存放要切换的备料数据
        List<SmtMachineMaterialPrepare> preparesFilterSmt = new ArrayList<>();

        // 如果目标指令有提前备料，则未提前备料数据为上料集合剔除目标指令已备料的，否则未提前备料数据与上料表数据一致
        if (!CollectionUtils.isEmpty(prepares)) {
            // 目标指令备料信息不为空
            List<String> prepareList = prepares.stream().map(e -> e.getLocationNo() + e.getItemCode()).collect(Collectors.toList());
            // 排除备料信息后的上料集合
            noPreSmt = smtDetail.stream()
                    .filter(e -> !prepareList.contains(e.getLocationNo() + e.getItemCode()))
                    .collect(Collectors.toList());

            List<String> smtDetailList = tarSmtDetail.stream().map(e -> e.getLocationNo() + e.getItemCode()).collect(Collectors.toList());
            // 目标指令提前备料数据存放到要切换的备料记录集合中
            preparesFilterSmt.addAll(prepares.stream().filter(e -> smtDetailList.contains(e.getLocationNo() + e.getItemCode())).collect(Collectors.toList()));
        } else {
            noPreSmt = smtDetail;
        }

        // 目标指令有未备料的，则需要判断未备料的是否为共用料，且全存在另一面指令的备料信息中，是则备料完成，否则备料未完成
        if (!CollectionUtils.isEmpty(noPreSmt)) {
            checkCommItemPreparationCompleted(noPreSmt, woSmtDetail, tarSmt, dto, preparesFilterSmt);
        }

        // 同一机台、模组、站位、物料代码 有多条备料记录的，取创建时间最早的记录
        Map<String, List<SmtMachineMaterialPrepare>> preparesMap = preparesFilterSmt.stream().collect(Collectors.groupingBy(item -> item.getMachineNo() + item.getModuleNo() + item.getLocationNo() + item.getItemCode()));
        List<SmtMachineMaterialPrepare> earliestPrepareList = new ArrayList<>();
        preparesMap.entrySet().forEach(e -> earliestPrepareList.add(e.getValue().stream().min(Comparator.comparing(SmtMachineMaterialPrepare::getCreateDate)).get()));
        List<String> prepareIds = earliestPrepareList.stream()
                .map(SmtMachineMaterialPrepare::getMtlPrepareId).collect(Collectors.toList());
        List<SmtMachineMaterialPrepare> prepareList = earliestPrepareList.stream()
                .filter(item -> StringUtils.isNotEmpty(item.getOldReelId()))
                .collect(Collectors.toList());
        return Pair.of(prepareIds,prepareList);
    }

    /**
     * 校验未备料的是否为共用料且全存在另一面指令的备料信息中，是则备料完成，否则备料未完成
     */
    public void checkCommItemPreparationCompleted(List<BSmtBomDetail> noPreSmt, Map<String, List<BSmtBomDetail>> woSmtDetail,
                                                  PsWorkOrderSmtDTO tarSmt, OneKeySwitchMoutingDTO dto,
                                                  List<SmtMachineMaterialPrepare> preparesFilterSmt) throws Exception {
        // noPreLocations--未提前备料 站位+物料集合
        List<String> noPreLocations = noPreSmt.stream().map(e -> e.getLocationNo() + e.getItemCode()).collect(Collectors.toList());

        // 另一面指令的上料表数据
        getAnotherWorkOrderSmtDetail(dto, woSmtDetail, tarSmt);

        // 取共用料
        List<String> commonItemLocationAndCode = getCommonItemLocationAndCode(woSmtDetail, dto);
        // 非共用料站位
        String noCommonItems = noPreSmt.stream().
                filter(e -> !commonItemLocationAndCode.contains(e.getLocationNo() + e.getItemCode()))
                .map(BSmtBomDetail::getLocationNo)
                .distinct()
                .collect(Collectors.joining(Constant.COMMA));

        // 排除目标指令备料信息后的上料集合中，判断剩余的是否都是共用料，不是则提示备料未完成
        if (StringUtils.isNotBlank(noCommonItems)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.WORK_ORDER_MODULE_NO_PREPARE,
                    new String[]{dto.getTarWorkOrder(), dto.getModuleNo(), noCommonItems});
        }

        // 剩余未备料的都是共用料，则从另一面指令(当前指令)备料表中查找
        // 查询另一面指令备料信息
        List<SmtMachineMaterialPrepare> curPrepares = smtMachineMaterialPrepareRepository.getPrepareInfoWithFeederByCurWorkOrder(dto);
        // 另一面指令备料信息备料信息为空，则提示备料未完成
        if (CollectionUtils.isEmpty(curPrepares)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.WORK_ORDER_MODULE_NO_PREPARE,
                    new String[]{dto.getTarWorkOrder(), dto.getModuleNo(), noPreSmt.stream().map(BSmtBomDetail::getLocationNo)
                            .collect(Collectors.joining(Constant.COMMA))});
        }
        List<String> cruPrepareList = curPrepares.stream().map(e -> e.getLocationNo() + e.getItemCode()).collect(Collectors.toList());

        // 判断未备料数据（共用料）是否都在另一面指令的备料表中，否 则提示备料未完成
        String commItemNotInCurPrepare = noPreSmt.stream().
                filter(e -> !cruPrepareList.contains(e.getLocationNo() + e.getItemCode()))
                .map(BSmtBomDetail::getLocationNo)
                .distinct()
                .collect(Collectors.joining(Constant.COMMA));
        if (StringUtils.isNotBlank(commItemNotInCurPrepare)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.WORK_ORDER_MODULE_NO_PREPARE,
                    new String[]{dto.getTarWorkOrder(), dto.getModuleNo(), commItemNotInCurPrepare});
        }

        // 另一面（当前）指令中共用料信息记录到要切换的备料记录中
        preparesFilterSmt.addAll(curPrepares.stream().filter(e -> noPreLocations.contains(e.getLocationNo() + e.getItemCode())).collect(Collectors.toList()));
    }

    /**
     * 同线体、同批次另一面指令的上料表数据
     *
     * @param dto
     * @param woSmtDetail
     */
    public void getAnotherWorkOrderSmtDetail(OneKeySwitchMoutingDTO dto,
                                             Map<String, List<BSmtBomDetail>> woSmtDetail,
                                             PsWorkOrderSmtDTO tarSmt) throws Exception {
        // 查询同线体、同批次的另一面指令上料头表
        PsWorkOrderSmt curWorkOrderSmt = null;
        if (StringUtils.isBlank(dto.getCurWorkOrder())) {
            curWorkOrderSmt = PlanscheduleRemoteService.getPsWorkSmtBySourceTaskAndLineCode(tarSmt.getSourceTask(), dto.getTarWorkOrder(), dto.getLineCode());
        }
        // 另一面指令上料明细
        if (curWorkOrderSmt != null && StringUtils.isNotBlank(curWorkOrderSmt.getCfgHeaderId())) {
            dto.setCurWorkOrder(curWorkOrderSmt.getWorkOrderNo());
            BSmtBomDetail detail = new BSmtBomDetail();
            detail.setLineCode(dto.getLineCode());
            detail.setModuleNo(dto.getModuleNo());
            detail.setCfgHeaderId(curWorkOrderSmt.getCfgHeaderId());
            List<BSmtBomDetail> bomDetails = smtBomDetailRepository.selectBSmtBomDetailListById(detail);
            if (!CollectionUtils.isEmpty(bomDetails)) {
                woSmtDetail.put(curWorkOrderSmt.getWorkOrderNo(), bomDetails);
            }
        }
    }

    /**
     * 将备料表数据移入机台在用
     *
     * @param prepareIds
     * @param prepareList 存在oldrellid的备料信息
     * @param dto
     */
    private void movePrepareToMouting(List<String> prepareIds,List<SmtMachineMaterialPrepare> prepareList ,
                                      OneKeySwitchMoutingDTO dto) {
        List<List<String>> prepareSplitIds = CommonUtils.splitList(prepareIds, Constant.IN_MAX_BATCH_SIZE);
        for (List<String> subList : prepareSplitIds) {
            // 备料数据移到机台在用时，指令号设置为目标指令号（需要移动的可能在当前指令备料信息中）
            smtMachineMaterialMoutingRepository.copyPrepareToMouting(subList, dto.getEmpNo(), dto.getTarWorkOrder());
            smtMachineMaterialPrepareRepository.deleteByIds(subList);
        }
        if (CollectionUtils.isNotEmpty(prepareList)) {
            List<SmtMachineMaterialMouting> moutingList = new ArrayList<>();
            prepareList.forEach(item -> {
                SmtMachineMaterialMouting mouting = new SmtMachineMaterialMouting();
                mouting.setObjectId(item.getOldReelId());
                mouting.setNextReelRowid(item.getObjectId());
                moutingList.add(mouting);
            });
            smtMachineMaterialMoutingRepository.batchUpdateNextReelRowidByReelId(moutingList);
        }
    }

    /**
     * 写上料历史
     *
     * @param ids
     * @param dto
     * @param idType 0:备料 1:机台在用
     */
    private void insertMTLHistory(List<String> ids, OneKeySwitchMoutingDTO dto, int idType) {
        // 目标指令一键切换类型上料历史头表是否存在，不存在则新增
        String headId = historyHRepository.countOneKeySwitchByWorkOrder(dto);
        if (StringUtils.isBlank(headId)) {
            headId = UUID.randomUUID().toString();
            SmtMachineMTLHistoryH historyH = new SmtMachineMTLHistoryH();
            historyH.setHeaderId(headId);
            historyH.setMountType(NumConstant.STR_FOURTEEN);
            historyH.setLineCode(dto.getLineCode());
            historyH.setWorkOrder(dto.getTarWorkOrder());
            historyH.setCfgHeaderId(dto.getCfgHeaderId());
            historyH.setCreateUser(dto.getEmpNo());
            historyH.setLastUpdatedBy(dto.getEmpNo());
            historyH.setFactoryId(dto.getFactoryId());
            historyH.setEnabledFlag(Constant.FLAG_Y);
            historyH.setEntityId(new BigDecimal(NumConstant.NUM_TWO));
            historyH.setPickStatus(NumConstant.STRING_ONE);
            historyHRepository.insertSmtMachineMTLHistoryH(historyH);
        }
        dto.setHisHeadId(headId);
        List<List<String>> splitIds = CommonUtils.splitList(ids, Constant.IN_MAX_BATCH_SIZE);
        if (INT_0 == idType) {
            // 根据备料数据写上料历史
            for (List<String> subList : splitIds) {
                historyLRepository.insetFromPrepare(subList, dto);
            }
            return;
        }
        // 根据机台在用数据写上料历史
        for (List<String> subList : splitIds) {
            historyLRepository.insetFromMouting(subList, dto);
        }
    }

    /**
     * 写reelId轨迹表
     *
     * @param ids
     */
    private void insertReelIdHistory(List<String> ids, OneKeySwitchMoutingDTO dto) {
        //批次
        String sourceTaskInReelId = dto.getTarWorkOrder().substring(0, 7);
        dto.setSourceTask(sourceTaskInReelId);
        List<List<String>> splitIds = CommonUtils.splitList(ids, Constant.IN_MAX_BATCH_SIZE);
        // 根据备料数据写reelId历史
        for (List<String> subList : splitIds) {
            historyHRepository.insertReelIdHistoryFromPrepare(subList, dto);
        }
    }

    @Override
    public void setMaterialMoutingEnabled(SmtMachineMaterialMouting smtMachineMaterialMouting) {
        smtMachineMaterialMoutingRepository.setMaterialMoutingEnabled(smtMachineMaterialMouting);
    }

    @Override
    public void setMaterialMoutingDisenabledbyIdSet(Set<String> moutingIdSet, String lastUpdatedBy) {
        smtMachineMaterialMoutingRepository.setMaterialMoutingDisenabledbyIdSet(moutingIdSet, lastUpdatedBy);
    }

    @Override
    public void updateFeederAndSetEnabled(SmtMachineMaterialMouting smtMachineMaterialMouting) {
        smtMachineMaterialMoutingRepository.updateFeederAndSetEnabled(smtMachineMaterialMouting);
    }

    @Override
    public List<SmtMachineMaterialMouting> getAllByParamsOfFeederInsertion(SmtMachineMaterialMouting entity) {
        return smtMachineMaterialMoutingRepository.getAllByParamsOfFeederInsertion(entity);
    }

    /**
     * 接料扫描-扫描旧料盘
     *
     * @param pkCode
     * @param factoryId
     * @return
     */
    public List<SmtMachineMaterialMouting> rcvScanOldPkCode(String pkCode, String factoryId) throws Exception {
        // 1.按料盘查找SMT_machine_material_mounting表
        List<SmtMachineMaterialMouting> mountingList = this.getMoutingDataAndCheck(pkCode);
        // 2.查询指令信息并校验状态是否为已开工
        List<PsEntityPlanBasic> workOrderList = this.getWorkorderAndCheck(mountingList);
        // 3.根据PK_CODE查询PK_CODE_INFO
        PkCodeInfo pkCodeInfo = pkCodeInfoService.getPkCodeInfoByCode(pkCode);
        if (pkCodeInfo == null) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PK_CODE_NOT_REGIST);
        }
        // 组装接料管控参数及返回值
        SmtMachineMaterialMoutingDTO dto = this.packCheckParam(mountingList, workOrderList, pkCodeInfo);
        // 4.接料管控
        ServiceData<SmtMachineMaterialMoutingDTO> serviceData = imesPDACommonService.pdaReceiveCheck(factoryId, dto);
        if (!RetCode.SUCCESS_CODE.equals(serviceData.getCode().getCode())) {
            // 接料管控异常
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, serviceData.getCode().getMsgId());
        }
        // 添加旧reelId用量，根据cfgHeaderId、模组、站位获取B_SMT_BOM_DETAIL用量数据
        int bomQty = queryReelIdNeedQty(mountingList.get(0));
        mountingList.get(0).setBomQty(bomQty);
        return mountingList;
    }

    private int queryReelIdNeedQty(SmtMachineMaterialMouting dto) throws Exception {
        if (StringUtils.isNotBlank(dto.getCfgHeaderId())) {
            return bSmtBomDetailService.getBomQtyByMtInfo(dto);
        }
        PsWorkOrderDTO psWorkOrderDTO = new PsWorkOrderDTO();
        psWorkOrderDTO.setWorkOrderNo(dto.getWorkOrder());
        List<PsWorkOrderSmt> psWorkOrderSmtList = PlanscheduleRemoteService.getWorkOrderSMTByWorkOrder(psWorkOrderDTO);
        if (CollectionUtils.isEmpty(psWorkOrderSmtList) || psWorkOrderSmtList.get(0) == null) {
            return INT_0;
        }
        if (StringUtils.isBlank(psWorkOrderSmtList.get(0).getCfgHeaderId())) {
            return INT_0;
        }
        dto.setCfgHeaderId(psWorkOrderSmtList.get(0).getCfgHeaderId());
        return bSmtBomDetailService.getBomQtyByMtInfo(dto);
    }

    /**
     * 接料扫描-扫描续料盘校验
     *
     * @param oldPkCode
     * @param newPkCode
     * @return;
     */
    public SmtLocationInfo rcvScanNewPkCodeCheck(String oldPkCode, String newPkCode, String checkPkCode) throws Exception {
        if (StringUtils.isEmpty(oldPkCode) || StringUtils.isEmpty(newPkCode)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.OLD_AND_NEW_PK_CODE_CAN_NOT_BE_NULL);
        }
        // 根据旧料盘获取机台在用数据
        List<SmtMachineMaterialMouting> oldMountingList = this.getMoutingDataAndCheck(oldPkCode);
        // 查询指令信息并校验状态是否为已开工
        List<PsEntityPlanBasic> workOrderList = this.getWorkorderAndCheck(oldMountingList);
        // 根据PK_CODE查询PK_CODE_INFO
        PkCodeInfo oldPkCodeInfo = pkCodeInfoService.getPkCodeInfoByCode(oldPkCode);

        SmtMachineMaterialMouting oldMounting = oldMountingList.get(0);
        // PDA接料扫描极性校验-组装参数
        PDAReceiveItemsScanDTO entity = this.packPolarCheckParam(oldMounting, oldPkCodeInfo, newPkCode, workOrderList.get(0), "");
        // PDA接料扫描--增加料盘找站位reelID和新料盘一致性校验，不一致要写入上料历史
        entity.setCheckPkCode(checkPkCode);

        //当物料属性为A材校验新旧料盘物料编码是否一致
        checkItemTypeAndSupplerCode(oldPkCode, newPkCode, oldMounting, entity);

        // PDA接料扫描极性校验
        String msg = imesPDACommonService.polarCheck(entity);
        if (MpConstant.PDA_RS_MESSAGE_THREE.equals(msg)) {
            entity.setContinueFlag(Constant.FLAG_N);
            msg = imesPDACommonService.polarCheck(entity);
        }
        if ((MpConstant.PDA_RS_MESSAGE_THREE + MpConstant.PDA_RS_MESSAGE_SEVEN).equals(msg)) {
            entity.setContinueFlag(Constant.FLAG_N);
            msg = imesPDACommonService.polarCheck(entity) + MpConstant.PDA_RS_MESSAGE_SEVEN;
        }
        if (MpConstant.PDA_RS_MESSAGE_ONE.equals(msg) || MpConstant.PDA_RS_MESSAGE_TWO.equals(msg) ||
                MpConstant.PDA_RS_MESSAGE_FIVE.equals(msg) || MpConstant.PDA_RS_MESSAGE_SIX.equals(msg)
                || MpConstant.PDA_RS_MESSAGE_SEVEN.equals(msg)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, msg);
        }
        // 新料盘:%s的方向为%s，旧料盘:%s的方向为%s，方向不一致，请确认
        if (MpConstant.PDA_RS_MESSAGE_FOUR.equals(msg)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PDARSERROR_008, new Object[]{entity.getNewPkCode(), entity.getNewDirection(), entity.getOldPkCode(), entity.getOldDirection()});
        }
        if ((MpConstant.PDA_RS_MESSAGE_FOUR + MpConstant.PDA_RS_MESSAGE_SEVEN).equals(msg)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PDARSERROR_009, new Object[]{entity.getNewPkCode(), entity.getNewDirection(), entity.getOldPkCode(), entity.getOldDirection()});
        }
        // 校验站位并返回线体配置
        SmtLocationInfo locationInfo = BasicsettingRemoteService.asmInfoQuery(oldMounting.getLineCode(), oldMounting.getModuleNo(),
                oldMounting.getMachineNo(), oldMounting.getLocationNo());
        return locationInfo;
    }

    /**
     * 当物料属性为A材校验新旧料盘物料编码是否一致
     *
     * @param oldPkCode
     * @param newPkCode
     * @param oldMounting
     * @param entity
     */
    private void checkItemTypeAndSupplerCode(String oldPkCode, String newPkCode, SmtMachineMaterialMouting oldMounting, PDAReceiveItemsScanDTO entity) throws Exception {
        List<AgeingInfoFencePointToPointQueryItemInfoDTO> splitList = new ArrayList<>();
        AgeingInfoFencePointToPointQueryItemInfoDTO itemInfoNew = new AgeingInfoFencePointToPointQueryItemInfoDTO();
        itemInfoNew.setItemNo(oldMounting.getItemCode());
        splitList.add(itemInfoNew);
        List<AgeingInfoFencePointToPointQueryItemInfoDTO> itemInfo = BasicsettingRemoteService.getItemInfo(splitList);
        if (CollectionUtils.isEmpty(itemInfo) || itemInfo.get(0) == null) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.BINDLIST_IS_NULL);
        }
        if (StringUtils.isNotBlank(itemInfo.get(0).getAbcType())
                && Constant.ITEM_TYPE_IS_A.equals(itemInfo.get(0).getAbcType())) {
            List<String> pkCodeList = new ArrayList() {{
                add(oldPkCode);
                add(newPkCode);
            }};
            //查询结果包含供应商代码suppler_code
            List<PkCodeInfo> listByPkCodes = pkCodeInfoService.getListByPkCodes(pkCodeList);
            if (CollectionUtils.isEmpty(listByPkCodes)) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PK_CODE_NOT_EXISTS, new String[]{oldPkCode + Constant.SYMBOL_COMMA + newPkCode});
            }
            //对供应商代码去重
            List<String> supplierCodes = listByPkCodes.stream().map(PkCodeInfo::getSupplerCode).distinct().collect(Collectors.toList());
            // 如果供应商代码不匹配记录异常信息
            if (supplierCodes.size() == listByPkCodes.size()) {
                entity.setItemCodeFlag(true);
            }
        }
    }

    /**
     * 极性校验参数组装
     *
     * @param oldPkCodeMouting
     * @param newPkCode
     * @param continueFlag
     * @return
     */
    private PDAReceiveItemsScanDTO packPolarCheckParam(SmtMachineMaterialMouting oldPkCodeMouting, PkCodeInfo oldPkCodeInfo, String newPkCode, PsEntityPlanBasic psEntityPlanBasic, String continueFlag) {
        Map<String, String> headerParamsMap = MESHttpHelper.getHttpRequestHeader();
        String factoryId = headerParamsMap.get(SysConst.HTTP_HEADER_X_FACTORY_ID_LOW_CASE);
        String empNo = headerParamsMap.get(SysConst.HTTP_HEADER_X_EMP_NO_LOW_CASE);
        PDAReceiveItemsScanDTO entity = new PDAReceiveItemsScanDTO();
        entity.setEntityId(new BigDecimal(factoryConfig.getCommonEntityId()));
        entity.setFactoryId(new BigDecimal(factoryId));
        entity.setUserId(empNo);
        entity.setNewPkCode(newPkCode);
        entity.setContinueFlag(continueFlag);
        entity.setLineCode(oldPkCodeMouting.getLineCode());
        entity.setWorkOrder(oldPkCodeMouting.getWorkOrder());
        entity.setCfgHeaderId(oldPkCodeMouting.getCfgHeaderId());
        entity.setSourceTask(psEntityPlanBasic.getSourceTask());
        entity.setBomNo(psEntityPlanBasic.getItemNo());
        entity.setOldItemCode(oldPkCodeMouting.getItemCode());
        entity.setOldLocationNo(oldPkCodeMouting.getLocationNo());
        entity.setOldMachineNo(oldPkCodeMouting.getMachineNo());
        entity.setOldMountingId(oldPkCodeMouting.getMachineMaterialMoutingId());
        entity.setOldPkCode(oldPkCodeMouting.getObjectId());
        entity.setIsLead(oldPkCodeMouting.getIsLead());
        entity.setOldQty(oldPkCodeInfo.getItemQty());
        entity.setNextReelId(newPkCode);
        entity.setNewPkCode(newPkCode);
        entity.setContinueFlag(continueFlag);
        entity.setModuleNo(oldPkCodeMouting.getModuleNo());
        return entity;
    }

    /**
     * 组装接料管控参数及返回值
     *
     * @param mountingList
     * @param workOrderList
     * @param pkCodeInfo
     * @return
     */
    private SmtMachineMaterialMoutingDTO packCheckParam(List<SmtMachineMaterialMouting> mountingList, List<PsEntityPlanBasic> workOrderList, PkCodeInfo pkCodeInfo) {
        if (CollectionUtils.isEmpty(workOrderList)) {
            return null;
        }
        PsEntityPlanBasic workOrder = workOrderList.get(0);
        for (SmtMachineMaterialMouting smtMachineMaterialMouting : mountingList) {
            // 组装返回的机台在用数据
            smtMachineMaterialMouting.setQty(pkCodeInfo.getItemQty());
            smtMachineMaterialMouting.setSourceTask(workOrder.getSourceTask());
            smtMachineMaterialMouting.setBomNo(workOrder.getItemNo());
        }
        SmtMachineMaterialMoutingDTO dto = new SmtMachineMaterialMoutingDTO();
        BeanUtils.copyProperties(mountingList.get(0), dto);
        dto.setOperateType(Constant.FLAG_Y);

        return dto;
    }

    /**
     * 获取指令信息校验指令状态
     *
     * @param mountingList
     */
    private List<PsEntityPlanBasic> getWorkorderAndCheck(List<SmtMachineMaterialMouting> mountingList) throws Exception {
        List<PsEntityPlanBasic> workOrderList = new ArrayList<>();
        // 最大两个，不做批量处理
        for (SmtMachineMaterialMouting mounting : mountingList) {
            if (StringUtils.isEmpty(mounting.getWorkOrder())) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.MOUNTING_DATA_ERROR_WORKORDER_EMPTY);
            }
            PsEntityPlanBasic psEntityPlanBasic = PlanscheduleRemoteService.getWorkOrderInfoByWorkOrderNo(mounting.getWorkOrder());
            if (psEntityPlanBasic == null) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.WORKORDER_NOT_FOUND);
            }
            if (!Constant.IS_START.equals(psEntityPlanBasic.getWorkOrderStatus())) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.WORKORDER_NOT_START);
            }
            workOrderList.add(psEntityPlanBasic);
        }
        return workOrderList;
    }

    /**
     * 查询旧料盘机台在用数据并校验
     *
     * @param pkCode
     * @return
     * @throws MesBusinessException
     */
    private List<SmtMachineMaterialMouting> getMoutingDataAndCheck(String pkCode) throws MesBusinessException {
        SmtMachineMaterialMouting record = new SmtMachineMaterialMouting();
        record.setObjectId(pkCode);
        List<SmtMachineMaterialMouting> list = this.selectSmtMachineMaterialMoutingSelective(record);
        if (CollectionUtils.isEmpty(list)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PKCODE_NOT_USED_ERROR);
        }
        // 同一个reelId最多存在两条记录(双轨线共用料)
        if (list.size() > NumConstant.NUM_TWO) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.TOO_MUCH_DATA_ERROR);
        }

        if (StringUtils.isNotEmpty(list.get(0).getNextReelRowid())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PKCODE_HAS_RECEIVED);
        }
        return list;
    }

    /**
     * 设备自动过站服务接口
     * 通过条码查指令表获取条码信息
     * 调用计划服务获取指令信息
     * 根据工序代码获取当前工序的指令信息， 如果不是已开工指令报错提示。
     * 调用工艺服务获取工序的配置属性，如果不是则需要校验该子工序的上料表是否所有工位维护的物料代码用量总和是否<=bom清单的标准用量，如果大于则报错，物料代码用量大于bom用量，请修改子工序属性
     * 调用DIP 过版扫描，执行DIP 过版扫描逻辑
     *
     * @param param 请求参数
     * @throws Exception 业务异常
     */
    @Override
    public void dipBoardScanningForEm(DipBoardScanningDTO param) throws Exception {
        Pair<String, String> pair = RequestHeadValidationUtil.validaFactoryIdAndEmpno();
        param.setFactoryId(pair.getFirst());
        param.setEmpNo(pair.getSecond());
        // 1. 获取条码信息
        PsWipInfo psWipInfo = psWipInfoService.getPsWipInfoBySn(param.getSn());
        if (Objects.isNull(psWipInfo)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SNS_IS_NOT_EXITS,
                    new Object[]{param.getSn()});
        }
        // 2. 获取批次信息查询并校验指令基础信息数据
        this.queryAndSetWorkInfo(param, psWipInfo);
        // 3. 用工艺服务获取工序的配置属性 不是则需要校验该子工序的上料表是否所有工位维护的物料代码用量总
        SmtMachineMaterialMoutingService aopProxy = (SmtMachineMaterialMoutingService) AopContext.currentProxy();
        ServiceData<Object> serviceData = aopProxy.verifyProcessCodeScan(param);
        this.checkMethodResult(serviceData);
        // 4. 调用DIP 过站
        this.callDipPass(param, aopProxy);
    }

    /**
     * 校验返回参数
     *
     * @param serviceData
     */
    private void checkMethodResult(ServiceData<Object> serviceData) {
        RetCode code = serviceData.getCode();
        if (!StringUtils.equals(code.getCode(), RetCode.SUCCESS_CODE)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.CUSTOMIZE_MSG,
                    new Object[]{code.getMsg()});
        }
    }

    /**
     * 调用DIP过站
     *
     * @param param    请求桉树
     * @param aopProxy 本代理
     * @throws Exception
     */
    private void callDipPass(DipBoardScanningDTO param, SmtMachineMaterialMoutingService aopProxy) throws Exception {
        param.setScanByWorkStation(MpConstant.FALSE);
        if (StringUtils.isNotBlank(param.getWorkStation())) {
            param.setScanByWorkStation(MpConstant.TRUE);
        }
        ServiceData<Object> data = aopProxy.dipBoardScanning(param);
        checkMethodResult(data);
    }

    /**
     * 查询并校验指令基础信息数据
     *
     * @param param     请求参数
     * @param psWipInfo 条码信息
     */
    private void queryAndSetWorkInfo(DipBoardScanningDTO param, PsWipInfo psWipInfo) {
        if (StringUtils.isBlank(psWipInfo.getAttribute1())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PRODPLANID_ID_NULL);
        }
        List<PsWorkOrderDTO> workOrderInfo = PlanscheduleRemoteService.getWorkOrderInfo(psWipInfo.getAttribute1());
        if (CollectionUtils.isEmpty(workOrderInfo)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.WORKORDER_NOT_FIND);
        }
        PsWorkOrderDTO psWorkOrderDTO = workOrderInfo.stream().filter(item -> StringUtils.isNotBlank(item.getProcessGroup()))
                .filter(item -> {
                    boolean result = false;
                    String[] split = item.getProcessGroup().split(Constant.STR_SPLIT_$);
                    for (String s : split) {
                        if (s.contains(param.getProcessCode())) {
                            result = true;
                            break;
                        }
                    }
                    return result;
                }).findFirst().orElseThrow(() -> new MesBusinessException(RetCode.BUSINESSERROR_CODE,
                        MessageId.WORKORDER_NOT_FIND));
        // 不是已开工不能过站
        if (!Constant.IS_START.equals(psWorkOrderDTO.getWorkOrderStatus())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.WORK_ORDER_NO_NOT_START,
                    new Object[]{psWorkOrderDTO.getWorkOrderNo()});
        }
        param.setWorkOrderNo(psWorkOrderDTO.getWorkOrderNo());
        param.setLineCode(psWorkOrderDTO.getLineCode());
    }

    @Override
    public PDAReceiveCheckItemDTO pdaReceiveCheckItem(String oldPkCode, String newPkCode) throws Exception {
        if (StringUtils.isEmpty(oldPkCode) || StringUtils.isEmpty(newPkCode)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.OLD_AND_NEW_PK_CODE_CAN_NOT_BE_NULL);
        }
        List<String> pkCodeList = new ArrayList() {{
            add(oldPkCode);
            add(newPkCode);
        }};
        List<PkCodeInfo> listByPkCodes = pkCodeInfoService.getListByPkCodes(pkCodeList);
        if (CollectionUtils.isEmpty(listByPkCodes)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PK_CODE_NOT_EXISTS, new String[]{oldPkCode + Constant.SYMBOL_COMMA + newPkCode});
        }
        PkCodeInfo oldPkCodeInfo = listByPkCodes.stream().filter(x -> x.getPkCode().equals(oldPkCode)).findFirst().orElse(null);
        if (oldPkCodeInfo == null) {
            //旧料盘未找到
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PK_CODE_NOT_EXISTS, new String[]{oldPkCode});
        }
        PkCodeInfo newCodeInfo = listByPkCodes.stream().filter(x -> x.getPkCode().equals(newPkCode)).findFirst().orElse(null);
        if (newCodeInfo == null) {
            // 新料盘未找到
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PK_CODE_NOT_EXISTS, new String[]{newPkCode});
        }
        // 新旧料盘物料代码一致
        if (oldPkCodeInfo.getItemCode().equals(newCodeInfo.getItemCode())) {
            return null;
        }
        String operateMsg = CommonUtils.getLmbMessage(MessageId.ITEM_CODES_INCONSISTENT, new String[]{oldPkCode, newPkCode});
        PDAReceiveCheckItemDTO checkItemDTO = new PDAReceiveCheckItemDTO();
        try {
            // 返回线体配置，调用设备时使用
            checkItemDTO.setItemCode(oldPkCodeInfo.getItemCode());
            checkItemDTO.setOperateMsg(operateMsg);

            // 根据旧料盘获取机台在用数据
            SmtMachineMaterialMouting record = new SmtMachineMaterialMouting();
            record.setObjectId(oldPkCode);
            List<SmtMachineMaterialMouting> list = this.selectSmtMachineMaterialMoutingSelective(record);
            if (CollectionUtils.isEmpty(list)) {
                return checkItemDTO;
            }
            SmtMachineMaterialMouting oldMounting = list.get(0);
            PsEntityPlanBasic psEntityPlanBasic = PlanscheduleRemoteService.getWorkOrderInfoByWorkOrderNo(oldMounting.getWorkOrder());
            if (psEntityPlanBasic == null) {
                return checkItemDTO;
            }
            // 记录上料历史
            PDAReceiveItemsScanDTO entity = this.packPolarCheckParam(oldMounting, oldPkCodeInfo, newPkCode, psEntityPlanBasic, "");
            entity.setOperateMsg(operateMsg);
            imesPDACommonService.insertMtlHistoryForPda(entity, newCodeInfo);

            // 获取线体配置
            SmtLocationInfo locationInfo = BasicsettingRemoteService.asmInfoQuery(oldMounting.getLineCode(), oldMounting.getModuleNo(),
                    oldMounting.getMachineNo(), oldMounting.getLocationNo());
            if (locationInfo != null) {
                BeanUtils.copyProperties(locationInfo, checkItemDTO);
            }
        } catch (Exception e) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.ITEM_CODES_INCONSISTENT, new Object[]{oldPkCode, newPkCode});
        }
        return checkItemDTO;
    }

    public void addRemainingTimeAndAmount(List<SmtMachineMaterialMouting> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        //设置UPH
        setUphList(list);
        //设置剩余生产大板数量=物料剩余数量/UPH
        setRemainingProductQty(list);
        //设置剩余生产时间=物料剩余数量/标准用量/UPH*60
        setRemainingProductTime(list);
    }

    /*
    * 获取uph集合
    * */
    private void setUphList(List<SmtMachineMaterialMouting> list) {
        //提取指令
        List<String> workOrderNoList = list.stream().map(SmtMachineMaterialMouting::getWorkOrder).collect(Collectors.toList());
        //根据指令批量获取料单代码
        List<PsWorkOrderBasicDTO> itemNoByWorkOrderNoList = PlanscheduleRemoteService.getItemNoByWorkOrderNoList(workOrderNoList);
        if (CollectionUtils.isEmpty(itemNoByWorkOrderNoList)) {
            return;
        }
        //提取料单代码
        List<String> itemNoList = itemNoByWorkOrderNoList.stream().map(PsWorkOrderBasicDTO::getItemNo).collect(Collectors.toList());
        //获取uph
        List<BManufactureCapacityDTO> bManufactureCapacityInfoList = BasicsettingRemoteService.getBManufactureCapacityInfo(list.get(INT_0).getLineCode(), itemNoList);
        if (CollectionUtils.isEmpty(bManufactureCapacityInfoList)) {
            return;
        }
        //指令-料单代码map
        Map<String, String> workOrderNoAndItemNoMap = itemNoByWorkOrderNoList.stream()
                .filter(dto -> StringUtils.isNotBlank(dto.getWorkOrderNo()) && StringUtils.isNotBlank(dto.getItemNo()))
                .collect(Collectors.toMap(
                        PsWorkOrderBasicDTO::getWorkOrderNo,
                        PsWorkOrderBasicDTO::getItemNo
                ));
        //料单代码-uph集合
        Map<String, String> itemNoAnUphMap = bManufactureCapacityInfoList.stream()
                .filter(dto -> StringUtils.isNotBlank(dto.getItemCode()) && StringUtils.isNotBlank(dto.getUph()))
                .collect(Collectors.toMap(
                        BManufactureCapacityDTO::getItemCode,
                        BManufactureCapacityDTO::getUph,
                        (existing, replacement) -> existing
                ));
        //根据料单代码设置uph
        for (SmtMachineMaterialMouting item : list) {
            String bomNo = workOrderNoAndItemNoMap.get(item.getWorkOrder());
            String uph = itemNoAnUphMap.get(bomNo);
            if (StringUtils.isBlank(bomNo) || StringUtils.isBlank(uph)){
                continue;
            }
            //设置料单代码、uph
            item.setBomNo(bomNo);
            item.setUph(new BigDecimal(uph));
        }
    }


    /*
    * 设置剩余生产大板数量=物料剩余数量/UPH
    * */
    private void setRemainingProductQty(List<SmtMachineMaterialMouting> list) {
        for (SmtMachineMaterialMouting item : list) {
            //物料剩余数量
            BigDecimal qty = item.getQty();
            BigDecimal uph = item.getUph();
            if (qty == null || uph == null) {
                continue;
            }
            //设置剩余生产大板数量=物料剩余数量/UPH
            BigDecimal remainingProductQty = qty.divide(uph, new MathContext(2));
            item.setRemainingProductQty(remainingProductQty);
        }
    }

    private void setRemainingProductTime(List<SmtMachineMaterialMouting> list) {
        List<BSmtBomDetailDTO> dtoList = list.stream()
                .map(mouting -> new BSmtBomDetailDTO(
                        mouting.getMachineNo(),
                        mouting.getModuleNo(),
                        mouting.getLocationNo()
                ))
                .collect(Collectors.toList());
        //机台、站位、模组查询标准用量
        List<BSmtBomDetailDTO> bSmtBomDetailQty = smtBomDetailRepository.getQtyByMachineLocationModuleNo(dtoList);
        if (CollectionUtils.isEmpty(bSmtBomDetailQty)) {
            return;
        }
        Map<String, BSmtBomDetailDTO> dtoMap = bSmtBomDetailQty.stream()
                .collect(Collectors.toMap(
                        dto -> dto.getMachineNo() + REPAIR_FROM_STATION + dto.getModuleNo() + REPAIR_FROM_STATION + dto.getLocationNo(),
                        dto -> dto,
                        (existing, replacement) -> existing
                ));
        for (SmtMachineMaterialMouting item : list) {
            String key = item.getMachineNo() + REPAIR_FROM_STATION + item.getModuleNo() + REPAIR_FROM_STATION + item.getLocationNo();
            BSmtBomDetailDTO bSmtBomDetailDTO = dtoMap.get(key);
            if (bSmtBomDetailDTO == null) {
                continue;
            }
            if (bSmtBomDetailDTO.getQty() == null || item.getQty() == null || item.getUph() == null) {
                continue;
            }
            BigDecimal remainingQty = item.getQty();
            BigDecimal standardQty = bSmtBomDetailDTO.getQty();
            BigDecimal uph = item.getUph();

            //设置剩余生产时间=物料剩余数量/标准用量/UPH*60
            BigDecimal remainingProductTime = remainingQty
                    .divide(standardQty, RoundingMode.HALF_UP)
                    .divide(uph, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal(NumConstant.NUM_60),new MathContext(2));
            item.setRemainingProductTime(remainingProductTime);
        }
    }

}
