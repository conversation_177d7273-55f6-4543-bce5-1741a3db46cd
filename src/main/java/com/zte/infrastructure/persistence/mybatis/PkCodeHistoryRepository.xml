<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.PkCodeHistoryRepository">
  <resultMap id="BaseResultMap" type="com.zte.domain.model.PkCodeHistory">
    <result column="HISTORY_ID" jdbcType="VARCHAR" property="historyId" />
    <result column="OBJECT_ID" jdbcType="VARCHAR" property="objectId" />
    <result column="OBJECT_TYPE" jdbcType="VARCHAR" property="objectType" />
    <result column="ITEM_CODE" jdbcType="VARCHAR" property="itemCode" />
    <result column="STATUS" jdbcType="VARCHAR" property="status" />
    <result column="PROGRAM_NAME" jdbcType="VARCHAR" property="programName" />
    <result column="LINE_CODE" jdbcType="VARCHAR" property="lineCode" />
    <result column="CRAFT_SECTION" jdbcType="VARCHAR" property="craftSection" />
    <result column="WORK_ORDER" jdbcType="VARCHAR" property="workOrder" />
    <result column="SOURCE_TASK" jdbcType="VARCHAR" property="sourceTask" />
    <result column="PROCESS_CODE" jdbcType="VARCHAR" property="processCode" />
    <result column="WORK_STATION" jdbcType="VARCHAR" property="workStation" />
    <result column="CURRENT_QTY" jdbcType="DECIMAL" property="currentQty" />
    <result column="MACHINE_NO" jdbcType="VARCHAR" property="machineNo" />
    <result column="MODULE_NO" jdbcType="VARCHAR" property="moduleNo" />
    <result column="LOCATION_NO" jdbcType="VARCHAR" property="locationNo" />
    <result column="SOURCE_BATCH_CODE" jdbcType="VARCHAR" property="sourceBatchCode" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
    <result column="LAST_UPDATED_BY" jdbcType="VARCHAR" property="lastUpdatedBy" />
    <result column="LAST_UPDATED_DATE" jdbcType="TIMESTAMP" property="lastUpdatedDate" />
    <result column="ENABLED_FLAG" jdbcType="VARCHAR" property="enabledFlag" />
    <result column="ORG_ID" jdbcType="DECIMAL" property="orgId" />
    <result column="FACTORY_ID" jdbcType="DECIMAL" property="factoryId" />
    <result column="ENTITY_ID" jdbcType="DECIMAL" property="entityId" />
    <result column="ATTRIBUTE1" jdbcType="VARCHAR" property="attribute1" />
    <result column="ATTRIBUTE2" jdbcType="VARCHAR" property="attribute2" />
    <result column="ATTRIBUTE3" jdbcType="VARCHAR" property="attribute3" />
    <result column="ATTRIBUTE4" jdbcType="VARCHAR" property="attribute4" />
    <result column="ATTRIBUTE5" jdbcType="VARCHAR" property="attribute5" />
    <result column="OLD_ITEM_QTY" jdbcType="DECIMAL" property="oldItemQty" />
  </resultMap>

  <resultMap id="BaseResultDtoMap" type="com.zte.interfaces.dto.PkCodeHistoryDTO">
    <result column="HISTORY_ID" jdbcType="VARCHAR" property="historyId" />
    <result column="OBJECT_ID" jdbcType="VARCHAR" property="objectId" />
    <result column="OBJECT_TYPE" jdbcType="VARCHAR" property="objectType" />
    <result column="ITEM_CODE" jdbcType="VARCHAR" property="itemCode" />
    <result column="STATUS" jdbcType="VARCHAR" property="status" />
    <result column="PROGRAM_NAME" jdbcType="VARCHAR" property="programName" />
    <result column="LINE_CODE" jdbcType="VARCHAR" property="lineCode" />
    <result column="CRAFT_SECTION" jdbcType="VARCHAR" property="craftSection" />
    <result column="WORK_ORDER" jdbcType="VARCHAR" property="workOrder" />
    <result column="SOURCE_TASK" jdbcType="VARCHAR" property="sourceTask" />
    <result column="PROCESS_CODE" jdbcType="VARCHAR" property="processCode" />
    <result column="WORK_STATION" jdbcType="VARCHAR" property="workStation" />
    <result column="CURRENT_QTY" jdbcType="DECIMAL" property="currentQty" />
    <result column="MACHINE_NO" jdbcType="VARCHAR" property="machineNo" />
    <result column="MODULE_NO" jdbcType="VARCHAR" property="moduleNo" />
    <result column="LOCATION_NO" jdbcType="VARCHAR" property="locationNo" />
    <result column="SOURCE_BATCH_CODE" jdbcType="VARCHAR" property="sourceBatchCode" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
    <result column="LAST_UPDATED_BY" jdbcType="VARCHAR" property="lastUpdatedBy" />
    <result column="LAST_UPDATED_DATE" jdbcType="TIMESTAMP" property="lastUpdatedDate" />
    <result column="ENABLED_FLAG" jdbcType="VARCHAR" property="enabledFlag" />
    <result column="ORG_ID" jdbcType="DECIMAL" property="orgId" />
    <result column="FACTORY_ID" jdbcType="DECIMAL" property="factoryId" />
    <result column="ENTITY_ID" jdbcType="DECIMAL" property="entityId" />
    <result column="ATTRIBUTE1" jdbcType="VARCHAR" property="attribute1" />
    <result column="ATTRIBUTE2" jdbcType="VARCHAR" property="attribute2" />
    <result column="ATTRIBUTE3" jdbcType="VARCHAR" property="attribute3" />
    <result column="ATTRIBUTE4" jdbcType="VARCHAR" property="attribute4" />
    <result column="ATTRIBUTE5" jdbcType="VARCHAR" property="attribute5" />
  </resultMap>

  <sql id="Base_Column_List">
    HISTORY_ID, OBJECT_ID, OBJECT_TYPE, ITEM_CODE, STATUS, PROGRAM_NAME, LINE_CODE, CRAFT_SECTION, WORK_ORDER, SOURCE_TASK,
    PROCESS_CODE, WORK_STATION, CURRENT_QTY, MACHINE_NO, MODULE_NO, LOCATION_NO, SOURCE_BATCH_CODE, CREATE_BY, CREATE_DATE,
    LAST_UPDATED_BY, LAST_UPDATED_DATE, ENABLED_FLAG, ORG_ID, FACTORY_ID, ENTITY_ID, ATTRIBUTE1, ATTRIBUTE2, ATTRIBUTE3,
    ATTRIBUTE4, ATTRIBUTE5,OLD_ITEM_QTY
  </sql>

  <insert id="insertPkCodeHistory" parameterType="com.zte.domain.model.PkCodeHistory">
    insert into PK_CODE_HISTORY (HISTORY_ID, OBJECT_ID, OBJECT_TYPE, 
      ITEM_CODE, STATUS, PROGRAM_NAME, 
      LINE_CODE, CRAFT_SECTION, WORK_ORDER, 
      SOURCE_TASK, PROCESS_CODE, WORK_STATION, 
      CURRENT_QTY, MACHINE_NO, MODULE_NO, 
      LOCATION_NO, SOURCE_BATCH_CODE, CREATE_BY, 
      CREATE_DATE, LAST_UPDATED_BY, LAST_UPDATED_DATE, 
      ENABLED_FLAG, ORG_ID, FACTORY_ID, 
      ENTITY_ID, ATTRIBUTE1, ATTRIBUTE2, 
      ATTRIBUTE3, ATTRIBUTE4, ATTRIBUTE5,IS_LEAD, OLD_ITEM_QTY
      )
    values (#{historyId,jdbcType=VARCHAR}, #{objectId,jdbcType=VARCHAR}, #{objectType,jdbcType=VARCHAR}, 
      #{itemCode,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, #{programName,jdbcType=VARCHAR}, 
      #{lineCode,jdbcType=VARCHAR}, #{craftSection,jdbcType=VARCHAR}, #{workOrder,jdbcType=VARCHAR}, 
      #{sourceTask,jdbcType=VARCHAR}, #{processCode,jdbcType=VARCHAR}, #{workStation,jdbcType=VARCHAR}, 
      #{currentQty,jdbcType=DECIMAL}, #{machineNo,jdbcType=VARCHAR}, #{moduleNo,jdbcType=VARCHAR}, 
      #{locationNo,jdbcType=VARCHAR}, #{sourceBatchCode,jdbcType=VARCHAR}, #{createBy,jdbcType=VARCHAR}, 
      SYSDATE, #{lastUpdatedBy,jdbcType=VARCHAR}, SYSDATE,
      'Y', #{orgId,jdbcType=DECIMAL}, #{factoryId,jdbcType=DECIMAL},
      #{entityId,jdbcType=DECIMAL}, #{attribute1,jdbcType=VARCHAR}, #{attribute2,jdbcType=VARCHAR}, 
      #{attribute3,jdbcType=VARCHAR}, #{attribute4,jdbcType=VARCHAR}, #{attribute5,jdbcType=VARCHAR},#{isLead,jdbcType=VARCHAR},
      #{oldItemQty,jdbcType=DECIMAL}
      )
  </insert>

  <insert id="insertPkCodeHistoryBatch" parameterType="java.util.List">
    insert into PK_CODE_HISTORY (
      HISTORY_ID, OBJECT_ID, OBJECT_TYPE,
      ITEM_CODE, STATUS, PROGRAM_NAME,
      LINE_CODE, CRAFT_SECTION, WORK_ORDER,
      SOURCE_TASK, PROCESS_CODE, WORK_STATION,
      CURRENT_QTY, MACHINE_NO, MODULE_NO,
      LOCATION_NO, SOURCE_BATCH_CODE, CREATE_BY,
      CREATE_DATE, LAST_UPDATED_BY, LAST_UPDATED_DATE,
      ENABLED_FLAG, ORG_ID, FACTORY_ID,
      ENTITY_ID, ATTRIBUTE1, ATTRIBUTE2,
      ATTRIBUTE3, ATTRIBUTE4, ATTRIBUTE5
    ) values
    <foreach collection ="list" item="item" index= "index" separator =",">
      (#{item.historyId,jdbcType=VARCHAR},
      #{item.objectId,jdbcType=VARCHAR},
      #{item.objectType,jdbcType=VARCHAR},
      #{item.itemCode,jdbcType=VARCHAR},
      #{item.status,jdbcType=VARCHAR},
      #{item.programName,jdbcType=VARCHAR},
      #{item.lineCode,jdbcType=VARCHAR},
      #{item.craftSection,jdbcType=VARCHAR},
      #{item.workOrder,jdbcType=VARCHAR},
      #{item.sourceTask,jdbcType=VARCHAR},
      #{item.processCode,jdbcType=VARCHAR},
      #{item.workStation,jdbcType=VARCHAR},
      #{item.currentQty,jdbcType=DECIMAL},
      #{item.machineNo,jdbcType=VARCHAR},
      #{item.moduleNo,jdbcType=VARCHAR},
      #{item.locationNo,jdbcType=VARCHAR},
      #{item.sourceBatchCode,jdbcType=VARCHAR},
      #{item.createBy,jdbcType=VARCHAR},
      SYSDATE,
      #{item.lastUpdatedBy,jdbcType=VARCHAR},
      SYSDATE,
      'Y',
      #{item.orgId,jdbcType=DECIMAL},
      #{item.factoryId,jdbcType=DECIMAL},
      #{item.entityId,jdbcType=DECIMAL},
      #{item.attribute1,jdbcType=VARCHAR},
      #{item.attribute2,jdbcType=VARCHAR},
      #{item.attribute3,jdbcType=VARCHAR},
      #{item.attribute4,jdbcType=VARCHAR},
      #{item.attribute5,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <select id="getList" parameterType="com.zte.interfaces.dto.PkCodeHistoryDTO" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM PK_CODE_HISTORY t
    WHERE ENABLED_FLAG='Y'
    <if test="objectId != null and objectId != ''"> AND OBJECT_ID = #{objectId,jdbcType=VARCHAR}</if>
    <if test="objectType != null and objectType != ''"> AND OBJECT_TYPE = #{objectType,jdbcType=VARCHAR}</if>
    <if test="itemCode != null and itemCode != ''"> AND ITEM_CODE = #{itemCode,jdbcType=VARCHAR}</if>
    <if test="programName != null and programName != ''"> AND PROGRAM_NAME = #{programName,jdbcType=VARCHAR}</if>
    <if test="lineCode != null and lineCode != ''"> AND LINE_CODE = #{lineCode,jdbcType=VARCHAR}</if>
    <if test="workOrder != null and workOrder != ''"> AND WORK_ORDER = #{workOrder,jdbcType=VARCHAR}</if>
    <if test="sourceTask != null and sourceTask != ''"> AND SOURCE_TASK = #{sourceTask,jdbcType=VARCHAR}</if>
    <if test="sourceBatchCode != null and sourceBatchCode != ''"> AND SOURCE_BATCH_CODE = #{sourceBatchCode,jdbcType=VARCHAR}</if>
    <if test="startTime != null and startTime != ''"> <![CDATA[and t.CREATE_DATE >=to_timestamp(#{startTime},'yyyy-MM-dd hh24:mi:ss')]]></if>
    <if test="endTime != null and endTime != ''"> <![CDATA[and t.CREATE_DATE <=to_timestamp(#{endTime},'yyyy-MM-dd hh24:mi:ss')]]></if>
    <if test="factoryId != null and factoryId != ''"> AND FACTORY_ID = #{factoryId,jdbcType=VARCHAR}</if>
    <if test="(objectId == null or objectId =='') and
          (objectType == null or objectType == '') and
          (itemCode == null or itemCode == '') and
          (programName == null or programName == '') and
          (lineCode == null or lineCode == '') and
          (workOrder == null or workOrder == '') and
          (sourceTask == null or sourceTask == '') and
          (sourceBatchCode == null or sourceBatchCode == '') and
          (startTime == null or startTime == '') and
          (endTime == null or endTime == '') and
          (factoryId == null or factoryId == '')">
      AND 1 = 2
    </if>
    <if test="sort != null">
      <choose>
        <when test="sort=='objectId'"> order by t.objectId <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='createBy'"> order by t.CREATE_BY <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='createDate'"> order by t.CREATE_DATE <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='lastUpdatedBy'"> order by t.LAST_UPDATED_BY <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='lastUpdatedDate'"> order by t.LAST_UPDATED_DATE <if test="order != null and order == 'desc'"> desc </if> </when>
      </choose>
    </if>
  </select>
  <!-- 翻页函数:获取符合条件的记录数 -->
  <select id="getCount" parameterType="com.zte.interfaces.dto.PkCodeHistoryDTO" resultType="java.lang.Long">
    SELECT count(*)
    FROM PK_CODE_HISTORY t
    WHERE t.ENABLED_FLAG = 'Y'
    <if test="objectId != null and objectId != ''"> AND OBJECT_ID = #{objectId,jdbcType=VARCHAR}</if>
    <if test="objectType != null and objectType != ''"> AND OBJECT_TYPE = #{objectType,jdbcType=VARCHAR}</if>
    <if test="itemCode != null and itemCode != ''"> AND ITEM_CODE = #{itemCode,jdbcType=VARCHAR}</if>
    <if test="programName != null and programName != ''"> AND PROGRAM_NAME = #{programName,jdbcType=VARCHAR}</if>
    <if test="lineCode != null and lineCode != ''"> AND LINE_CODE = #{lineCode,jdbcType=VARCHAR}</if>
    <if test="workOrder != null and workOrder != ''"> AND WORK_ORDER = #{workOrder,jdbcType=VARCHAR}</if>
    <if test="sourceTask != null and sourceTask != ''"> AND SOURCE_TASK = #{sourceTask,jdbcType=VARCHAR}</if>
    <if test="sourceBatchCode != null and sourceBatchCode != ''"> AND SOURCE_BATCH_CODE = #{sourceBatchCode,jdbcType=VARCHAR}</if>
    <if test="startTime != null and startTime != ''"> <![CDATA[and t.CREATE_DATE >=to_timestamp(#{startTime},'yyyy-MM-dd hh24:mi:ss')]]></if>
    <if test="endTime != null and endTime != ''"> <![CDATA[and t.CREATE_DATE <=to_timestamp(#{endTime},'yyyy-MM-dd hh24:mi:ss')]]></if>
    <if test="factoryId != null and factoryId != ''"> AND FACTORY_ID = #{factoryId,jdbcType=VARCHAR}</if>
  </select>

  <!-- 翻页函数:获取一页的记录集 -->
  <select id="getPage" parameterType="com.zte.interfaces.dto.PkCodeHistoryDTO" resultMap="BaseResultMap">
    SELECT * FROM PK_CODE_HISTORY t
    WHERE t.ENABLED_FLAG = 'Y'
    <if test="objectId != null and objectId != ''"> AND OBJECT_ID = #{objectId,jdbcType=VARCHAR}</if>
    <if test="objectType != null and objectType != ''"> AND OBJECT_TYPE = #{objectType,jdbcType=VARCHAR}</if>
    <if test="itemCode != null and itemCode != ''"> AND ITEM_CODE = #{itemCode,jdbcType=VARCHAR}</if>
    <if test="programName != null and programName != ''"> AND PROGRAM_NAME = #{programName,jdbcType=VARCHAR}</if>
    <if test="lineCode != null and lineCode != ''"> AND LINE_CODE = #{lineCode,jdbcType=VARCHAR}</if>
    <if test="workOrder != null and workOrder != ''"> AND WORK_ORDER = #{workOrder,jdbcType=VARCHAR}</if>
    <if test="sourceTask != null and sourceTask != ''"> AND SOURCE_TASK = #{sourceTask,jdbcType=VARCHAR}</if>
    <if test="sourceBatchCode != null and sourceBatchCode != ''"> AND SOURCE_BATCH_CODE = #{sourceBatchCode,jdbcType=VARCHAR}</if>
    <if test="startTime != null and startTime != ''"> <![CDATA[and t.CREATE_DATE >=to_timestamp(#{startTime},'yyyy-MM-dd hh24:mi:ss')]]></if>
    <if test="endTime != null and endTime != ''"> <![CDATA[and t.CREATE_DATE <=to_timestamp(#{endTime},'yyyy-MM-dd hh24:mi:ss')]]></if>
    <if test="factoryId != null and factoryId != ''"> AND FACTORY_ID = #{factoryId,jdbcType=VARCHAR}</if>
    <if test="sort != null">
      <choose>
        <when test="sort=='objectId'"> order by t.objectId <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='createBy'"> order by t.CREATE_BY <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='createDate'"> order by t.CREATE_DATE <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='lastUpdatedBy'"> order by t.LAST_UPDATED_BY <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='lastUpdatedDate'"> order by t.LAST_UPDATED_DATE <if test="order != null and order == 'desc'"> desc </if> </when>
      </choose>
    </if>
    limit #{endRow}::numeric-#{startRow}::numeric+1 offset case when ( #{startRow}::numeric-1 &lt; 0) then 0 else  #{startRow}::numeric-1 end
  </select>


  <select id="queryHandoverQty" parameterType="com.zte.interfaces.dto.PkCodeHistoryDTO" resultMap="BaseResultDtoMap">
    select WORK_ORDER,ITEM_CODE,sum(nvl(CURRENT_QTY,0)) as CURRENT_QTY from PK_CODE_HISTORY t
    where t.enabled_flag = 'Y'
    <if test="objectType != null and objectType != ''"> AND t.OBJECT_TYPE = #{objectType,jdbcType=VARCHAR}</if>
    <if test="itemCode != null and itemCode != ''"> AND t.ITEM_CODE = #{itemCode,jdbcType=VARCHAR}</if>
    <if test="workOrder != null and workOrder != ''"> AND t.WORK_ORDER = #{workOrder,jdbcType=VARCHAR}</if>
    <if test="programName != null and programName != ''"> AND t.PROGRAM_NAME = #{programName,jdbcType=VARCHAR}</if>
    <if test="itemCodeList != null and itemCodeList.size != 0">
     AND t.ITEM_CODE in
        <foreach collection="itemCodeList" item="itemCode" index="index" open="("  separator="," close=")">
          #{itemCode,jdbcType=VARCHAR}
        </foreach>
     </if>
    <if test="workOrderNoList != null and workOrderNoList.size != 0">
     AND t.WORK_ORDER in
        <foreach collection="workOrderNoList" item="workOrder" index="index" open="("  separator="," close=")">
          #{workOrder,jdbcType=VARCHAR}
        </foreach>
     </if>
    <if test="(objectType == null or objectType == '') and
          (itemCode == null or itemCode == '') and
          (workOrder == null or workOrder == '') and
          (programName == null or programName == '') and
          (itemCodeList == null or itemCodeList.size ==0) and
          (workOrderNoList == null or workOrderNoList.size ==0)
          ">
      AND 1 = 2
    </if>
     group by WORK_ORDER,ITEM_CODE
  </select>

	<select id="queryHandoverQtyByWorkOrders" parameterType="com.zte.interfaces.dto.PkCodeHistoryDTO" resultMap="BaseResultDtoMap">
		select LINE_CODE,WORK_ORDER,sum(nvl(CURRENT_QTY,0)) as CURRENT_QTY from PK_CODE_HISTORY t
		where t.enabled_flag = 'Y'
		<if test="programName != null and programName != ''"> AND t.PROGRAM_NAME = #{programName,jdbcType=VARCHAR}</if>
		<if test="workOrderNoList != null and workOrderNoList.size() != 0">
			AND t.WORK_ORDER in
			<foreach collection="workOrderNoList" item="workOrder" index="index" open="("  separator="," close=")">
				#{workOrder,jdbcType=VARCHAR}
			</foreach>
		</if>
		<if test="(programName == null or programName == '') and (workOrderNoList == null or workOrderNoList.size() ==0)">
			AND 1 = 2
		</if>
		group by LINE_CODE,WORK_ORDER
	</select>

  <select id="getListAllInfo" parameterType="com.zte.interfaces.dto.PkCodeHistoryDTO" resultMap="BaseResultDtoMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM PK_CODE_HISTORY t
    WHERE ENABLED_FLAG='Y'
    <if test="objectId != null and objectId != ''"> AND OBJECT_ID = #{objectId,jdbcType=VARCHAR}</if>
    <if test="objectType != null and objectType != ''"> AND OBJECT_TYPE = #{objectType,jdbcType=VARCHAR}</if>
    <if test="itemCode != null and itemCode != ''"> AND ITEM_CODE = #{itemCode,jdbcType=VARCHAR}</if>
    <if test="programName != null and programName != ''"> AND PROGRAM_NAME = #{programName,jdbcType=VARCHAR}</if>
    <if test="lineCode != null and lineCode != ''"> AND LINE_CODE = #{lineCode,jdbcType=VARCHAR}</if>
    <if test="workOrder != null and workOrder != ''"> AND WORK_ORDER = #{workOrder,jdbcType=VARCHAR}</if>
    <if test="sourceTask != null and sourceTask != ''"> AND SOURCE_TASK = #{sourceTask,jdbcType=VARCHAR}</if>
    <if test="sourceBatchCode != null and sourceBatchCode != ''"> AND SOURCE_BATCH_CODE = #{sourceBatchCode,jdbcType=VARCHAR}</if>
    <if test="workOrderNoList != null and workOrderNoList.size !=0">
      AND WORK_ORDER IN
      <foreach collection="workOrderNoList" open="(" separator="," close=")" index="index" item="workOrder">
        #{workOrder,jdbcType=VARCHAR}
      </foreach>
    </if>
    <if test="objectIdList != null and objectIdList.size !=0">
      AND OBJECT_ID IN
      <foreach collection="objectIdList" open="(" separator="," close=")" index="index" item="objectId">
        #{objectId,jdbcType=VARCHAR}
      </foreach>
    </if>
    <if test="(objectId == null or objectId =='') and
          (objectType == null or objectType == '') and
          (itemCode == null or itemCode == '') and
          (programName == null or programName == '') and
          (lineCode == null or lineCode == '') and
          (workOrder == null or workOrder == '') and
          (sourceTask == null or sourceTask == '') and
          (sourceBatchCode == null or sourceBatchCode == '') and
          (workOrderNoList == null or workOrderNoList.size ==0) and
          (objectIdList == null or objectIdList.size ==0)
          ">
      AND 1 = 2
    </if>
    <if test="sort != null">
      <choose>
        <when test="sort=='objectId'"> order by t.objectId <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='createBy'"> order by t.CREATE_BY <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='createDate'"> order by t.CREATE_DATE <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='lastUpdatedBy'"> order by t.LAST_UPDATED_BY <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='lastUpdatedDate'"> order by t.LAST_UPDATED_DATE <if test="order != null and order == 'desc'"> desc </if> </when>
      </choose>
    </if>
  </select>

  <select id="getListAllInfoPage" parameterType="com.zte.springbootframe.common.model.Page" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM PK_CODE_HISTORY t
    WHERE ENABLED_FLAG='Y'
    <if test="params.objectId != null and params.objectId != ''"> AND OBJECT_ID = #{params.objectId,jdbcType=VARCHAR}</if>
    <if test="params.objectType != null and params.objectType != ''"> AND OBJECT_TYPE = #{params.objectType,jdbcType=VARCHAR}</if>
    <if test="params.itemCode != null and params.itemCode != ''"> AND ITEM_CODE = #{params.itemCode,jdbcType=VARCHAR}</if>
    <if test="params.programName != null and params.programName != ''"> AND PROGRAM_NAME = #{params.programName,jdbcType=VARCHAR}</if>
    <if test="params.lineCode != null and params.lineCode != ''"> AND LINE_CODE = #{params.lineCode,jdbcType=VARCHAR}</if>
    <if test="params.workOrder != null and params.workOrder != ''"> AND WORK_ORDER = #{params.workOrder,jdbcType=VARCHAR}</if>
    <if test="params.sourceTask != null and params.sourceTask != ''"> AND SOURCE_TASK = #{params.sourceTask,jdbcType=VARCHAR}</if>
    <if test="params.sourceBatchCode != null and params.sourceBatchCode != ''"> AND SOURCE_BATCH_CODE = #{params.sourceBatchCode,jdbcType=VARCHAR}</if>
    <if test="params.workOrderNoList != null and params.workOrderNoList.size !=0">
      AND WORK_ORDER IN
      <foreach collection="params.workOrderNoList" open="(" separator="," close=")" index="index" item="workOrder">
        #{params.workOrderNoList[${index}],jdbcType=VARCHAR}
      </foreach>
    </if>
    <if test="params.objectIdList != null and params.objectIdList.size !=0">
      AND OBJECT_ID IN
      <foreach collection="params.objectIdList" open="(" separator="," close=")" index="index" item="objectId">
        #{params.objectIdList[${index}],jdbcType=VARCHAR}
      </foreach>
    </if>
  </select>

  <insert id="insertInfo" parameterType="com.zte.domain.model.PkCodeHistory">
    insert into PK_CODE_HISTORY (HISTORY_ID, OBJECT_ID, PROGRAM_NAME, CREATE_BY,
    CREATE_DATE, LAST_UPDATED_BY, LAST_UPDATED_DATE, ENABLED_FLAG ,FACTORY_ID,CURRENT_QTY,
    OBJECT_TYPE,WORK_ORDER,OLD_ITEM_QTY,ITEM_CODE
    )
    values (#{historyId,jdbcType=VARCHAR}, #{objectId,jdbcType=VARCHAR}, #{programName,jdbcType=VARCHAR},
    #{createBy,jdbcType=VARCHAR}, SYSDATE, #{lastUpdatedBy,jdbcType=VARCHAR}, SYSDATE,
    'Y', #{factoryId,jdbcType=DECIMAL}, #{currentQty,jdbcType=DECIMAL}, #{objectType,jdbcType=VARCHAR}
    , #{workOrder,jdbcType=VARCHAR},#{oldItemQty,jdbcType=DECIMAL},#{itemCode,jdbcType=VARCHAR}
    )
  </insert>

  <!--获取指令开工前最后交接时间-->
  <select id="getLastMaterialHandoverInfo" parameterType="com.zte.interfaces.dto.PkCodeHistoryDTO" resultMap="BaseResultMap">
    SELECT
    CREATE_DATE
    FROM PK_CODE_HISTORY t
    WHERE ENABLED_FLAG='Y'
    AND PROGRAM_NAME = #{programName,jdbcType=VARCHAR}
    AND WORK_ORDER = #{workOrder,jdbcType=VARCHAR}
    <![CDATA[AND CREATE_DATE < #{actualStartDate,jdbcType=TIMESTAMP}]]>
    ORDER BY CREATE_DATE DESC,HISTORY_ID LIMIT 1
  </select>

</mapper>
