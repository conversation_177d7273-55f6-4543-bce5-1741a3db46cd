package com.zte.application;

import com.zte.interfaces.dto.TechnicalChangeInfoEditDTO;

/**
 * <AUTHOR>
 * @date 2022-12-20 23:18
 */
public interface CommonTechnicalService {
    int DOWNLOAD = 1;
    int PREVIEW = 2;
    /**
     * 获取技改文件 邮件内容
     *
     * @param chgReqNo 技改单号
     * @return 文本内容
     * @throws Exception 业务异常
     */
    String getContent(String chgReqNo) throws Exception;

    /**
     * 获取完整用户信息
     *
     * @param userId 用户id
     * @return 完整用户信息
     */
    String buildHrmPersonInfo(String userId);

    String getFileUrl(String fileId, String fileName, int type);
}
