/*Started by AICoder, pid:2b67d84bffcc4b67821ae5995c577d96*/
package com.zte.application.stepdt.impl;

import com.zte.domain.model.WarehouseEntryInfo;
import com.zte.domain.model.datawb.BarSubmitRepository;
import com.zte.domain.model.datawb.BoardOnlinelist;
import com.zte.domain.model.stepdt.KxStepBoardOnline;
import com.zte.domain.model.stepdt.KxStepBoardOnlineRepository;
import com.zte.interfaces.dto.WipScanHisExtraInfoDTO;
import com.zte.interfaces.stepdt.dto.KxStepBoardOnlineDTO;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.assertEquals;

import static org.junit.Assert.assertTrue;

@RunWith(PowerMockRunner.class)
public class KxStepBoardOnlineServiceImplTest {
    @InjectMocks
    private KxStepBoardOnlineServiceImpl boardOnlineService;
    @InjectMocks
    private ApsProdModelCountServiceImpl apsProdModelCountService;
    @Mock
    private KxStepBoardOnlineRepository kxStepBoardOnlineRepository;
    @Mock
    private BarSubmitRepository barSubmitRepository;
    @Test
    public void selectBoardOnlineWithParam() {
        KxStepBoardOnlineDTO kxStepBoardOnlineDTO =new KxStepBoardOnlineDTO();
        kxStepBoardOnlineDTO.setBoardSn(new BigDecimal("12"));
        List<String> listString = new ArrayList<>();
        listString.add("123321100001");
        kxStepBoardOnlineDTO.setNotInSnList(listString);
        List<KxStepBoardOnline> list = new ArrayList<>();
        KxStepBoardOnline kxStepBoardOnline =new KxStepBoardOnline();
        kxStepBoardOnline.setBoardSn(new BigDecimal("12"));
        list.add(kxStepBoardOnline);
        PowerMockito.when(kxStepBoardOnlineRepository.selectBoardOnlineWithPageParam(Mockito.any())).thenReturn(list);
        PowerMockito.when(kxStepBoardOnlineRepository.selectBoardOnlineCount(Mockito.any())).thenReturn(1);
        Assert.assertNotNull(boardOnlineService.selectBoardOnlineWithPageParam(kxStepBoardOnlineDTO));
    }

    @Test
    public void batchUpdateBoardOnLine() {
        KxStepBoardOnlineDTO kxStepBoardOnlineDTO =new KxStepBoardOnlineDTO();
        kxStepBoardOnlineDTO.setBoardSn(new BigDecimal("12"));
        List<KxStepBoardOnlineDTO> list = new ArrayList<>();
        KxStepBoardOnlineDTO kxStepBoardOnline =new KxStepBoardOnlineDTO();
        kxStepBoardOnline.setBoardSn(new BigDecimal("12"));
        list.add(kxStepBoardOnline);
        PowerMockito.when(kxStepBoardOnlineRepository.batchUpdateBoardOnLine(Mockito.anyList())).thenReturn(1);
        Assert.assertNotNull(boardOnlineService.batchUpdateBoardOnLine(list));
    }

    @Test
    public void updateIsRepairAndDelBillBatch() throws Exception {
        List<WarehouseEntryInfo> list =new ArrayList<>();
        WarehouseEntryInfo warehouseEntryInfo=new WarehouseEntryInfo();
        List<BoardOnlinelist> boardOnlinelists=new ArrayList<>();
        BoardOnlinelist boardOnlinelist=new BoardOnlinelist();
        boardOnlinelist.setProdplanId(new BigDecimal("1"));
        boardOnlinelist.setBoardSn(new BigDecimal("1"));
        boardOnlinelist.setCardNo("00286523");
        boardOnlinelists.add(boardOnlinelist);
        warehouseEntryInfo.setBoardOnlinelist(boardOnlinelists);
        list.add(warehouseEntryInfo);
        PowerMockito.when(kxStepBoardOnlineRepository.batchUpdateBoardOnLine(Mockito.anyList())).thenReturn(1);
        PowerMockito.when(barSubmitRepository.deleteByBillNoAndProdplanIdBatch(Mockito.anyList())).thenReturn(1);
        boardOnlineService.updateIsRepairAndDelBillBatch(list);
        Assert.assertNotNull(list);
    }

    @Test
    public void SelectBoardOnlineWithParam() {
        KxStepBoardOnlineDTO kxStepBoardOnlineDTO =new KxStepBoardOnlineDTO();
        kxStepBoardOnlineDTO.setBoardSn(new BigDecimal("12"));
        List<String> listString = new ArrayList<>();
        listString.add("123321100001");
        kxStepBoardOnlineDTO.setNotInSnList(listString);
        List<KxStepBoardOnline> list = new ArrayList<>();
        KxStepBoardOnline kxStepBoardOnline =new KxStepBoardOnline();
        kxStepBoardOnline.setBoardSn(new BigDecimal("12"));
        list.add(kxStepBoardOnline);
        PowerMockito.when(kxStepBoardOnlineRepository.selectBoardOnlineWithParam(Mockito.any())).thenReturn(list);
        Assert.assertNotNull(boardOnlineService.selectBoardOnlineWithParam(kxStepBoardOnlineDTO));
    }

    @Test
    public void callBoardOnline() {
        List<WipScanHisExtraInfoDTO> list = new ArrayList<>();
        List<KxStepBoardOnlineDTO> updateList = new ArrayList<>();
        WipScanHisExtraInfoDTO dto = new WipScanHisExtraInfoDTO();
        WipScanHisExtraInfoDTO dto2 = new WipScanHisExtraInfoDTO();
        KxStepBoardOnlineDTO dto11 = new KxStepBoardOnlineDTO();
        KxStepBoardOnlineDTO dto22 = new KxStepBoardOnlineDTO();
        KxStepBoardOnlineDTO dto33 = new KxStepBoardOnlineDTO();
        KxStepBoardOnlineDTO dto44 = new KxStepBoardOnlineDTO();
        KxStepBoardOnlineDTO dto55 = new KxStepBoardOnlineDTO();
        KxStepBoardOnlineDTO dto66 = new KxStepBoardOnlineDTO();
        WipScanHisExtraInfoDTO dto3 = new WipScanHisExtraInfoDTO();
        WipScanHisExtraInfoDTO dto4 = new WipScanHisExtraInfoDTO();
        WipScanHisExtraInfoDTO dto5 = new WipScanHisExtraInfoDTO();
        WipScanHisExtraInfoDTO dto6 = new WipScanHisExtraInfoDTO();
        WipScanHisExtraInfoDTO dto7 = new WipScanHisExtraInfoDTO();
        WipScanHisExtraInfoDTO dto8 = new WipScanHisExtraInfoDTO();
        WipScanHisExtraInfoDTO dto9 = new WipScanHisExtraInfoDTO();
        dto.setExtraData("{\"prodplanId\":\"7777701\",\"sn\":\"************\",\"imuId\":\"111\",\"prodplanIdAndSn\":\"77777014\"}");
        dto.setProdplanIdAndSn("77777014");
        dto2.setExtraData("{\"prodplanId\":\"7777701\",\"sn\":\"************\",\"imuId\":\"111\",\"prodplanIdAndSn\":\"77777014\"}");
        dto2.setProdplanIdAndSn("77777014");
        dto3.setExtraData("{\"prodplanId\":\"7777700001\",\"sn\":\"************\",\"imuId\":\"111\"}");
        dto4.setExtraData("{\"prodplanId\":\"7777700001\",\"sn\":\"************\",\"imuId\":\"111\"}");
        dto5.setExtraData("{\"prodplanId\":\"7777701\",\"sn\":\"************\",\"imuId\":\"42\",\"prodplanIdAndSn\":\"77777015\"}");
        dto2.setProdplanIdAndSn("77777015");
        dto6.setExtraData("{\"prodplanId\":\"7777701\",\"sn\":\"************\",\"imuId\":\"601\",\"prodplanIdAndSn\":\"77777016\"}");
        dto2.setProdplanIdAndSn("77777016");
        dto7.setExtraData("{\"prodplanId\":\"7777701\",\"sn\":\"************\",\"imuId\":\"612\",\"prodplanIdAndSn\":\"77777017\"}");
        dto2.setProdplanIdAndSn("77777017");
        dto8.setExtraData("{\"prodplanId\":\"7777701\",\"sn\":\"************\",\"imuId\":\"613\",\"prodplanIdAndSn\":\"77777018\"}");
        dto2.setProdplanIdAndSn("77777018");
        dto11.setBoardSn(new BigDecimal("4"));
        dto11.setProdplanId(new BigDecimal("7777701"));
        dto11.setImuId(new BigDecimal("42"));
        dto22.setBoardSn(new BigDecimal("4"));
        dto22.setProdplanId(new BigDecimal("7777701"));
        dto22.setImuId(new BigDecimal("601"));
        dto33.setBoardSn(new BigDecimal("4"));
        dto33.setProdplanId(new BigDecimal("7777701"));
        dto33.setImuId(new BigDecimal("612"));
        dto44.setBoardSn(new BigDecimal("4"));
        dto44.setProdplanId(new BigDecimal("7777701"));
        dto44.setImuId(new BigDecimal("613"));
        dto55.setBoardSn(new BigDecimal("99"));
        dto55.setProdplanId(new BigDecimal("7777701"));
        dto55.setImuId(new BigDecimal("11"));
        dto66.setBoardSn(new BigDecimal("4"));
        dto66.setProdplanId(new BigDecimal("7777701"));
        dto66.setImuId(new BigDecimal("11"));
        updateList.add(dto11);
        updateList.add(dto22);
        updateList.add(dto33);
        updateList.add(dto44);
        updateList.add(dto55);
        updateList.add(dto66);
        list.add(dto);
        list.add(dto2);
        list.add(dto3);
        list.add(dto4);
        list.add(dto5);
        list.add(dto6);
        list.add(dto7);
        list.add(dto8);
        PowerMockito.when(kxStepBoardOnlineRepository.checkIsExist(Mockito.any())).thenReturn(updateList);
        PowerMockito.when(kxStepBoardOnlineRepository.updateBoardOnlineBatch(Mockito.any())).thenReturn(1);
        PowerMockito.when(kxStepBoardOnlineRepository.insertBoardOnlineBatch(Mockito.any())).thenReturn(1);
        apsProdModelCountService.callBoardOnline(list);
        list.clear();
        updateList.clear();
        PowerMockito.when(kxStepBoardOnlineRepository.checkIsExist(Mockito.any())).thenReturn(updateList);
        PowerMockito.when(kxStepBoardOnlineRepository.updateBoardOnlineBatch(Mockito.anyList())).thenReturn(1);
        PowerMockito.when(kxStepBoardOnlineRepository.insertBoardOnlineBatch(Mockito.anyList())).thenReturn(1);
        apsProdModelCountService.callBoardOnline(list);
        Assert.assertNotNull(list);
    }
    @Test(timeout = 8000)
    public void testUpdateBoardOnline_EmptyList() {
        KxStepBoardOnlineDTO dto = new KxStepBoardOnlineDTO();
        dto.setBoardSnList(new ArrayList<>());
        int result = boardOnlineService.updateBoardOnline(dto);
        Assert.assertEquals(0, result);
    }

    @Test(timeout = 8000)
    public void testUpdateBoardOnline_SingleBatch() {
        KxStepBoardOnlineDTO dto = new KxStepBoardOnlineDTO();
        dto.setBoardSnList(Arrays.asList(new BigDecimal("1"), new BigDecimal("2")));
        int result = boardOnlineService.updateBoardOnline(dto);
        Assert.assertEquals(0, result);
    }

    @Test(timeout = 8000)
    public void selectBoardOnlineIsRepair() {
        KxStepBoardOnlineDTO dto = new KxStepBoardOnlineDTO();
        dto.setBoardSnList(new ArrayList<>());
        dto.setProdplanId(new BigDecimal(123232));
        List<String> list=new ArrayList<>();
        list.add("3434");
        PowerMockito.when(kxStepBoardOnlineRepository.selectBoardOnlineIsRepair(dto)).thenReturn(list);
        boardOnlineService.selectBoardOnlineIsRepair(dto);
        Assert.assertNotNull(list);
    }

    /*Started by AICoder, pid:9c4a1m91eegdcd41487b0972800db65b74d7d573*/
    @Test(timeout = 8000)
    public void testGetRepairCount_EmptyProdList() {
        List<String> prodList = new ArrayList<>();
        Assert.assertTrue(CollectionUtils.isEmpty(boardOnlineService.getRepairCount(prodList)));
        prodList.add("qwe");
        Assert.assertTrue(CollectionUtils.isEmpty(boardOnlineService.getRepairCount(prodList)));
    }
    /*Ended by AICoder, pid:9c4a1m91eegdcd41487b0972800db65b74d7d573*/
}
/*Ended by AICoder, pid:2b67d84bffcc4b67821ae5995c577d96*/