<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.RProcessRequestDealTRepository">
  <resultMap id="BaseResultMap" type="com.zte.domain.model.RProcessRequestDealT">
    <id column="DEAL_T_ID" jdbcType="VARCHAR" property="dealTId" />
    <result column="BILL_NO" jdbcType="VARCHAR" property="billNo" />
    <result column="REQUEST_LINE" jdbcType="VARCHAR" property="requestLine" />
    <result column="OBJECT_ID" jdbcType="VARCHAR" property="objectId" />
    <result column="OBJECT_TYPE" jdbcType="VARCHAR" property="objectType" />
    <result column="QTY" jdbcType="DECIMAL" property="qty" />
    <result column="LPN" jdbcType="VARCHAR" property="lpn" />
    <result column="STATUS" jdbcType="VARCHAR" property="status" />
    <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="LAST_UPDATED_DATE" jdbcType="TIMESTAMP" property="lastUpdatedDate" />
    <result column="LAST_UPDATED_BY" jdbcType="VARCHAR" property="lastUpdatedBy" />
    <result column="ENABLED_FLAG" jdbcType="VARCHAR" property="enabledFlag" />
    <result column="ORG_ID" jdbcType="DECIMAL" property="orgId" />
    <result column="ENTITY_ID" jdbcType="DECIMAL" property="entityId" />
    <result column="FACTORY_ID" jdbcType="DECIMAL" property="factoryId" />
  </resultMap>

  <resultMap id="BaseResultDTOMap" type="com.zte.interfaces.dto.RProcessRequestDealTDTO">
    <id column="DEAL_T_ID" jdbcType="VARCHAR" property="dealTId" />
    <result column="BILL_NO" jdbcType="VARCHAR" property="billNo" />
    <result column="REQUEST_LINE" jdbcType="VARCHAR" property="requestLine" />
    <result column="OBJECT_ID" jdbcType="VARCHAR" property="objectId" />
    <result column="OBJECT_TYPE" jdbcType="VARCHAR" property="objectType" />
    <result column="QTY" jdbcType="DECIMAL" property="qty" />
    <result column="LPN" jdbcType="VARCHAR" property="lpn" />
    <result column="STATUS" jdbcType="VARCHAR" property="status" />
    <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="LAST_UPDATED_DATE" jdbcType="TIMESTAMP" property="lastUpdatedDate" />
    <result column="LAST_UPDATED_BY" jdbcType="VARCHAR" property="lastUpdatedBy" />
    <result column="ENABLED_FLAG" jdbcType="VARCHAR" property="enabledFlag" />
    <result column="ORG_ID" jdbcType="DECIMAL" property="orgId" />
    <result column="ENTITY_ID" jdbcType="DECIMAL" property="entityId" />
    <result column="FACTORY_ID" jdbcType="DECIMAL" property="factoryId" />
    <result column="ITEM_CODE" jdbcType="VARCHAR" property="itemCode" />
    <result column="WORK_ORDER" jdbcType="VARCHAR" property="workOrder" />
  </resultMap>

  <sql id="Base_Column_List">
    DEAL_T_ID, BILL_NO, REQUEST_LINE, OBJECT_ID, OBJECT_TYPE, QTY, LPN, STATUS, CREATE_DATE, 
    CREATE_BY, LAST_UPDATED_DATE, LAST_UPDATED_BY, ENABLED_FLAG, ORG_ID, ENTITY_ID, FACTORY_ID
  </sql>


  <select id="selectRProcessRequestDealTById" parameterType="com.zte.domain.model.RProcessRequestDealT" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from R_PROCESS_REQUEST_DEAL_T
    where DEAL_T_ID = #{dealTId,jdbcType=VARCHAR}
  </select>

  <delete id="deleteRProcessRequestDealTById" parameterType="com.zte.domain.model.RProcessRequestDealT">
    delete from R_PROCESS_REQUEST_DEAL_T
    where DEAL_T_ID = #{dealTId,jdbcType=VARCHAR}
  </delete>

  <insert id="insertRProcessRequestDealT" parameterType="com.zte.domain.model.RProcessRequestDealT">
    insert into R_PROCESS_REQUEST_DEAL_T (DEAL_T_ID, BILL_NO, REQUEST_LINE, 
      OBJECT_ID, OBJECT_TYPE, QTY, 
      LPN, STATUS, CREATE_DATE, 
      CREATE_BY, LAST_UPDATED_DATE, LAST_UPDATED_BY, 
      ENABLED_FLAG, ORG_ID, ENTITY_ID, 
      FACTORY_ID)
    values (#{dealTId,jdbcType=VARCHAR}, #{billNo,jdbcType=VARCHAR}, #{requestLine,jdbcType=VARCHAR}, 
      #{objectId,jdbcType=VARCHAR}, #{objectType,jdbcType=VARCHAR}, #{qty,jdbcType=DECIMAL}, 
      #{lpn,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, SYSDATE,
      #{createBy,jdbcType=VARCHAR}, SYSDATE, #{lastUpdatedBy,jdbcType=VARCHAR},
      'Y', #{orgId,jdbcType=DECIMAL}, #{entityId,jdbcType=DECIMAL},
      #{factoryId,jdbcType=DECIMAL})
  </insert>

  <insert id="insertRProcessRequestDealTSelective" parameterType="com.zte.domain.model.RProcessRequestDealT">
    insert into R_PROCESS_REQUEST_DEAL_T
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="dealTId != null">
        DEAL_T_ID,
      </if>

      <if test="billNo != null">
        BILL_NO,
      </if>

      <if test="requestLine != null">
        REQUEST_LINE,
      </if>

      <if test="objectId != null">
        OBJECT_ID,
      </if>

      <if test="objectType != null">
        OBJECT_TYPE,
      </if>

      <if test="qty != null">
        QTY,
      </if>

      <if test="lpn != null">
        LPN,
      </if>

      <if test="status != null">
        STATUS,
      </if>

      <if test="createDate != null">
        CREATE_DATE,
      </if>

      <if test="createBy != null">
        CREATE_BY,
      </if>

      <if test="lastUpdatedDate != null">
        LAST_UPDATED_DATE,
      </if>

      <if test="lastUpdatedBy != null">
        LAST_UPDATED_BY,
      </if>

      <if test="enabledFlag != null">
        ENABLED_FLAG,
      </if>

      <if test="orgId != null">
        ORG_ID,
      </if>

      <if test="entityId != null">
        ENTITY_ID,
      </if>

      <if test="factoryId != null">
        FACTORY_ID,
      </if>

    </trim>

    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="dealTId != null">
        #{dealTId,jdbcType=VARCHAR},
      </if>

      <if test="billNo != null">
        #{billNo,jdbcType=VARCHAR},
      </if>

      <if test="requestLine != null">
        #{requestLine,jdbcType=VARCHAR},
      </if>

      <if test="objectId != null">
        #{objectId,jdbcType=VARCHAR},
      </if>

      <if test="objectType != null">
        #{objectType,jdbcType=VARCHAR},
      </if>

      <if test="qty != null">
        #{qty,jdbcType=DECIMAL},
      </if>

      <if test="lpn != null">
        #{lpn,jdbcType=VARCHAR},
      </if>

      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>

      <if test="createDate != null">
        SYSDATE,
      </if>

      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>

      <if test="lastUpdatedDate != null">
        SYSDATE,
      </if>

      <if test="lastUpdatedBy != null">
        #{lastUpdatedBy,jdbcType=VARCHAR},
      </if>

      <if test="enabledFlag != null">
        'Y',
      </if>

      <if test="orgId != null">
        #{orgId,jdbcType=DECIMAL},
      </if>

      <if test="entityId != null">
        #{entityId,jdbcType=DECIMAL},
      </if>

      <if test="factoryId != null">
        #{factoryId,jdbcType=DECIMAL},
      </if>

    </trim>

  </insert>

  <update id="updateRProcessRequestDealTByIdSelective" parameterType="com.zte.domain.model.RProcessRequestDealT">
    update R_PROCESS_REQUEST_DEAL_T
    <set>
      <if test="billNo != null">
        BILL_NO = #{billNo,jdbcType=VARCHAR},
      </if>

      <if test="requestLine != null">
        REQUEST_LINE = #{requestLine,jdbcType=VARCHAR},
      </if>

      <if test="objectId != null">
        OBJECT_ID = #{objectId,jdbcType=VARCHAR},
      </if>

      <if test="objectType != null">
        OBJECT_TYPE = #{objectType,jdbcType=VARCHAR},
      </if>

      <if test="qty != null">
        QTY = #{qty,jdbcType=DECIMAL},
      </if>

      <if test="lpn != null">
        LPN = #{lpn,jdbcType=VARCHAR},
      </if>

      <if test="status != null">
        STATUS = #{status,jdbcType=VARCHAR},
      </if>

      <if test="createDate != null">
        SYSDATE,
      </if>

      <if test="createBy != null">
        CREATE_BY = #{createBy,jdbcType=VARCHAR},
      </if>

      <if test="lastUpdatedDate != null">
        SYSDATE,
      </if>

      <if test="lastUpdatedBy != null">
        LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR},
      </if>

      <if test="enabledFlag != null">
        'Y',
      </if>

      <if test="orgId != null">
        ORG_ID = #{orgId,jdbcType=DECIMAL},
      </if>

      <if test="entityId != null">
        ENTITY_ID = #{entityId,jdbcType=DECIMAL},
      </if>

      <if test="factoryId != null">
        FACTORY_ID = #{factoryId,jdbcType=DECIMAL},
      </if>

    </set>

    where DEAL_T_ID = #{dealTId,jdbcType=VARCHAR}
  </update>

  <update id="updateRProcessRequestDealTById" parameterType="com.zte.domain.model.RProcessRequestDealT">
    update R_PROCESS_REQUEST_DEAL_T
    set BILL_NO = #{billNo,jdbcType=VARCHAR},
      REQUEST_LINE = #{requestLine,jdbcType=VARCHAR},
      OBJECT_ID = #{objectId,jdbcType=VARCHAR},
      OBJECT_TYPE = #{objectType,jdbcType=VARCHAR},
      QTY = #{qty,jdbcType=DECIMAL},
      LPN = #{lpn,jdbcType=VARCHAR},
      STATUS = #{status,jdbcType=VARCHAR},
      LAST_UPDATED_DATE = #{lastUpdatedDate,jdbcType=TIMESTAMP},
      LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR},
      ORG_ID = #{orgId,jdbcType=DECIMAL},
      ENTITY_ID = #{entityId,jdbcType=DECIMAL},
      FACTORY_ID = #{factoryId,jdbcType=DECIMAL}
    where DEAL_T_ID = #{dealTId,jdbcType=VARCHAR}
  </update>

  <!--批量新增工序间叫料结果-->
  <insert id="batchInsertRProcessRequestDealT" parameterType="java.util.List">
    insert into R_PROCESS_REQUEST_DEAL_T (DEAL_T_ID, BILL_NO, REQUEST_LINE,
    OBJECT_ID, OBJECT_TYPE, QTY,
    LPN, STATUS, CREATE_DATE,
    CREATE_BY, LAST_UPDATED_DATE, LAST_UPDATED_BY,
    ENABLED_FLAG, ORG_ID, ENTITY_ID,
    FACTORY_ID
    )
    SELECT DEAL_T_ID, BILL_NO, REQUEST_LINE,
    OBJECT_ID, OBJECT_TYPE, QTY,
    LPN, STATUS, CREATE_DATE,
    CREATE_BY, LAST_UPDATED_DATE, LAST_UPDATED_BY,
    ENABLED_FLAG, ORG_ID, ENTITY_ID,
    FACTORY_ID
    FROM (
    <foreach collection ="list" item="item" index= "index" separator ="UNION ALL">
      SELECT #{item.dealTId,jdbcType=VARCHAR} DEAL_T_ID,
      #{item.billNo,jdbcType=VARCHAR} BILL_NO,
      #{item.requestLine,jdbcType=VARCHAR} REQUEST_LINE,
      #{item.objectId,jdbcType=VARCHAR} OBJECT_ID,
      #{item.objectType,jdbcType=VARCHAR} OBJECT_TYPE,
      #{item.qty,jdbcType=DECIMAL} QTY,
      #{item.lpn,jdbcType=VARCHAR} LPN,
      #{item.status,jdbcType=VARCHAR} STATUS,
      SYSDATE CREATE_DATE,
      #{item.createBy,jdbcType=VARCHAR} CREATE_BY,
      SYSDATE LAST_UPDATED_DATE,
      #{item.lastUpdatedBy,jdbcType=VARCHAR} LAST_UPDATED_BY,
      'Y' ENABLED_FLAG,
      #{item.orgId,jdbcType=DECIMAL} ORG_ID,
      #{item.entityId,jdbcType=DECIMAL} ENTITY_ID,
      #{item.factoryId,jdbcType=DECIMAL} FACTORY_ID

    </foreach>
    ) A
  </insert>

  <select id="pdaSelectSumRProcessRequestDealT" parameterType="com.zte.domain.model.RProcessRequestDealT" resultMap="BaseResultMap">
    SELECT T.REQUEST_LINE, SUM(T.QTY) QTY
    FROM R_PROCESS_REQUEST_DEAL_T T
    WHERE T.ENABLED_FLAG = 'Y'
    AND T.BILL_NO = #{billNo}
    GROUP BY T.REQUEST_LINE
  </select>

  <update id="pdaUpdateRProcessRequestLLineStatus" parameterType="com.zte.domain.model.RProcessRequestDealT">
    UPDATE R_PROCESS_REQUEST_L L
    SET L.LINE_STATUS = CASE WHEN L.REQUEST_QTY = #{qty,jdbcType=DECIMAL} THEN '交付完毕' ELSE '交付中' END,
    LAST_UPDATED_DATE = SYSDATE,
    LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR}
    WHERE L.REQUEST_LINE = #{requestLine,jdbcType=VARCHAR}
  </update>
  
  <update id="batchPdaUpdateRProcessRequestLLineStatus" parameterType="java.util.List">
  	<foreach collection="list" item="item" index="index" open="" close="" separator=";">
	    UPDATE R_PROCESS_REQUEST_L 
	    SET LINE_STATUS = CASE WHEN REQUEST_QTY >= #{item.qty,jdbcType=DECIMAL} THEN '交付完毕' ELSE '交付中' END,
	    LAST_UPDATED_DATE = SYSDATE,
	    LAST_UPDATED_BY = #{item.lastUpdatedBy,jdbcType=VARCHAR}
	    WHERE <![CDATA[ LINE_STATUS <> '交付完毕' ]]> AND REQUEST_LINE = #{item.requestLine,jdbcType=VARCHAR}
	</foreach>
  </update>

  <update id="pdaUpdateRProcessRequestHStatus" parameterType="com.zte.domain.model.RProcessRequestDealT">
    UPDATE R_PROCESS_REQUEST_H H
    SET H.STATUS = CASE WHEN EXISTS (SELECT *
    FROM R_PROCESS_REQUEST_L L
    WHERE L.ENABLED_FLAG = 'Y'
    AND L.BILL_NO = H.BILL_NO
   <![CDATA[ AND L.LINE_STATUS <> '交付完毕') THEN '交付中' ELSE '交付完毕' END ]]>
    ,LAST_UPDATED_DATE = SYSDATE,
    LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR}
    WHERE H.BILL_NO = #{billNo,jdbcType=VARCHAR}
  </update>

  <update id="pdaUpdateRProcessRequestDealStatus" parameterType="com.zte.domain.model.RProcessRequestDealT">
    update R_PROCESS_REQUEST_DEAL_T
    set STATUS = #{status,jdbcType=VARCHAR},
    LAST_UPDATED_DATE = #{lastUpdatedDate,jdbcType=TIMESTAMP},
    LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR}
    where LPN = #{lpn,jdbcType=VARCHAR}
  </update>

  <select id="getListRProcessRequestDealT" parameterType="com.zte.domain.model.RProcessRequestDealT" resultMap="BaseResultDTOMap">
    SELECT T.DEAL_T_ID, T.BILL_NO, T.REQUEST_LINE, T.OBJECT_ID, T.OBJECT_TYPE, T.QTY, T.LPN, T.STATUS,
    T.CREATE_DATE, T.CREATE_BY, T.LAST_UPDATED_DATE, T.LAST_UPDATED_BY, T.ENABLED_FLAG, T.ORG_ID,
    T.ENTITY_ID, T.FACTORY_ID,L.SP_ITEM_CODE ITEM_CODE,H.WORK_ORDER
    FROM R_PROCESS_REQUEST_DEAL_T T,R_PROCESS_REQUEST_L L,R_PROCESS_REQUEST_H H
    WHERE T.ENABLED_FLAG='Y' AND L.ENABLED_FLAG='Y' AND H.ENABLED_FLAG='Y'
    AND T.REQUEST_LINE=L.REQUEST_LINE AND L.BILL_NO=H.BILL_NO
    <if test="billNo != null and billNo != ''"> AND T.BILL_NO = #{billNo}</if>
    <if test="requestLine != null and requestLine != ''"> and T.REQUEST_LINE = #{requestLine}</if>
    <if test="objectId != null and objectId != ''"> and t.OBJECT_ID = #{objectId}</if>
    <if test="lpn != null and lpn != ''"> and t.LPN = #{lpn}</if>
    <if test="status != null and status != ''"> and t.STATUS = #{status}</if>
    <if test="orgId != null"> and t.ORG_ID = cast(#{orgId} as numeric)</if>
    <if test="factoryId != null"> and t.FACTORY_ID = cast(#{factoryId} as numeric)</if>
    <if test="entityId != null"> and t.ENTITY_ID = cast(#{entityId} as numeric)</if>
  </select>
  <select id="getRProcessRequestDealTList" parameterType="com.zte.domain.model.RProcessRequestDealT" resultMap="BaseResultDTOMap">
    SELECT
     <include refid="Base_Column_List" />
    FROM R_PROCESS_REQUEST_DEAL_T t
    WHERE t.enabled_flag='Y' 
    <if test="lpn != null and lpn != ''"> and t.LPN = #{lpn}</if>   
    <if test="orgId != null"> and t.ORG_ID = cast(#{orgId} as numeric)</if>
    <if test="factoryId != null"> and t.FACTORY_ID = cast(#{factoryId} as numeric)</if>
    <if test="entityId != null"> and t.ENTITY_ID = cast(#{entityId} as numeric)</if>
  </select>
  <!-- 按条件查询工序间叫料结果（携带任务信息） -->
  <select id="getRProcessRequestDealTListWithWorkOrder" parameterType="com.zte.domain.model.RProcessRequestDealT" resultMap="BaseResultDTOMap">
    SELECT
	    h.WORK_ORDER, t.DEAL_T_ID, t.BILL_NO, t.REQUEST_LINE, t.OBJECT_ID, t.OBJECT_TYPE, t.QTY, t.LPN, t.STATUS, t.CREATE_DATE, 
	    t.CREATE_BY, t.LAST_UPDATED_DATE, t.LAST_UPDATED_BY, t.ENABLED_FLAG, t.ORG_ID, t.ENTITY_ID, t.FACTORY_ID
	FROM 
		R_PROCESS_REQUEST_DEAL_T t,
		R_PROCESS_REQUEST_H h
    WHERE h.enabled_flag='Y' and t.bill_no=h.bill_no and t.enabled_flag='Y'
    <if test="billNo != null and billNo != ''"> and t.BILL_NO = #{billNo}</if>
    <if test="requestLine != null and requestLine != ''"> and t.REQUEST_LINE = #{requestLine}</if>
    <if test="objectId != null and objectId != ''"> and t.OBJECT_ID = #{objectId}</if> 
    <if test="lpn != null and lpn != ''"> and t.LPN = #{lpn}</if>
    <if test="orgId != null"> and t.ORG_ID = cast(#{orgId} as numeric)</if>
    <if test="status != null and status!= ''"> and t.STATUS in (${status})</if>
    <if test="orgId == null and (billNo == null or billNo == '') and ( requestLine == null or requestLine == '') and (objectId == null or objectId == '') and ( lpn == null or lpn == '') and
              (status == null or status == '')"> and 1=2 </if>
  </select>
</mapper>
