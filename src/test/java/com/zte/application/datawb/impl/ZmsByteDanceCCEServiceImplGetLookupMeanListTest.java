/*Started by AICoder, pid:bb1c8d72e8meb01140a80a82d024e76a8a9128a7*/
package com.zte.application.datawb.impl;

import com.alibaba.fastjson.JSON;
import com.zte.application.datawb.WsmAssembleLinesService;
import com.zte.common.CommonUtils;
import com.zte.common.utils.Constant;
import com.zte.common.utils.DateUtil;
import com.zte.domain.model.SysLookupValues;
import com.zte.interfaces.dto.ZmsByteDanceCCEHeadDTO;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.util.json.JacksonJsonConverUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.anyString;

@RunWith(PowerMockRunner.class)
@PrepareForTest({WsmAssembleLinesService.class, JacksonJsonConverUtil.class,
        JSON.class, CommonUtils.class, DateUtil.class})
@PowerMockIgnore({"javax.net.ssl.*", "javax.security.*"})
public class ZmsByteDanceCCEServiceImplGetLookupMeanListTest {
    @InjectMocks
    private ZmsByteDanceCCEServiceImpl zmsByteDanceCCEService;
    @Mock
    private WsmAssembleLinesService wsmAssembleLinesService;

    private List<SysLookupValues> sysLookupValuesList;

    @Before
    public void setUp() {
        sysLookupValuesList = new ArrayList<>();
        SysLookupValues sysLookupValues1 = new SysLookupValues();
        sysLookupValues1.setLookupCode(BigDecimal.valueOf(Long.parseLong(Constant.LOOKUP_TYPE_824004200020)));
        sysLookupValues1.setDescription("111");
        sysLookupValuesList.add(sysLookupValues1);
        SysLookupValues sysLookupValues2 = new SysLookupValues();
        sysLookupValues2.setLookupCode(BigDecimal.valueOf(Long.parseLong(Constant.LOOKUP_TYPE_824004200021)));
        sysLookupValues2.setDescription("222");
        sysLookupValuesList.add(sysLookupValues2);
        SysLookupValues sysLookupValues3 = new SysLookupValues();
        sysLookupValues3.setLookupCode(BigDecimal.valueOf(Long.parseLong(Constant.LOOKUP_TYPE_824004200023)));
        sysLookupValues3.setDescription("333");
        sysLookupValuesList.add(sysLookupValues3);
        SysLookupValues sysLookupValues4 = new SysLookupValues();
        sysLookupValues4.setLookupCode(BigDecimal.valueOf(Long.parseLong(Constant.LOOKUP_TYPE_824004200024)));
        sysLookupValues4.setDescription("222");
        sysLookupValuesList.add(sysLookupValues4);
    }

    @Test(expected = MesBusinessException.class)
    public void testGetLookupMeanList_EmptyList() {
        PowerMockito.when(wsmAssembleLinesService.getSysLookupValues(anyString())).thenReturn(new ArrayList<>());
        ZmsByteDanceCCEHeadDTO dto = new ZmsByteDanceCCEHeadDTO();
        zmsByteDanceCCEService.getLookupMeanList(dto);
        PowerMockito.when(wsmAssembleLinesService.getSysLookupValues(anyString())).thenReturn(sysLookupValuesList);
        zmsByteDanceCCEService.getLookupMeanList(dto);
        assertNotNull(dto);
    }
}
/*Ended by AICoder, pid:bb1c8d72e8meb01140a80a82d024e76a8a9128a7*/