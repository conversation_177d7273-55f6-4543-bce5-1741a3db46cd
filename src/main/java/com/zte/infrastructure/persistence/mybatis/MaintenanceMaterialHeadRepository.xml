<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.MaintenanceMaterialHeadRepository">
    <resultMap id="BaseResultMap" type="com.zte.interfaces.dto.MaintenanceMaterialHeadDTO">
        <result column="HEAD_ID" jdbcType="VARCHAR" property="headId"/>
        <result column="MATERIAL_REQUISITION_BILL" jdbcType="VARCHAR" property="materialRequisitionBill"/>
        <result column="BILL_STATUS" jdbcType="VARCHAR" property="billStatus"/>
        <result column="TEAM_GROUP" jdbcType="VARCHAR" property="teamGroup"/>
        <result column="PRODPLAN_ID" jdbcType="VARCHAR" property="prodplanId"/>
        <result column="ITEM_NO" jdbcType="VARCHAR" property="itemNo"/>
        <result column="ITEM_NAME" jdbcType="VARCHAR" property="itemName"/>
        <result column="APPROVED_BY" jdbcType="VARCHAR" property="approvedBy"/>
        <result column="APPROVED_RESULT" jdbcType="VARCHAR" property="approvedResult"/>
        <result column="APPROVED_NOTES" jdbcType="VARCHAR" property="approvedNotes"/>
        <result column="APPROVED_DATE" jdbcType="TIMESTAMP" property="approvedDate"/>
        <result column="OLD_MATERIAL_STATUS" jdbcType="VARCHAR" property="oldMaterialStatus"/>
        <result column="CHANGE_TYPE" jdbcType="VARCHAR" property="changeType"/>
        <result column="CONTROL_FLAG" jdbcType="VARCHAR" property="controlFlag"/>
        <result column="CHILD_CARD" jdbcType="VARCHAR" property="childCard"/>
        <result column="COLLECTORS" jdbcType="VARCHAR" property="collectors"/>
        <result column="CREATE_USER" jdbcType="VARCHAR" property="createUser"/>
        <result column="UPDATE_USER" jdbcType="VARCHAR" property="updateUser"/>
        <result column="COLLECTORS_DATE" jdbcType="TIMESTAMP" property="collectorsDate"/>
        <result column="ENABLE_FLAG" jdbcType="VARCHAR" property="enableFlag"/>
        <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="UPDATE_DATE" jdbcType="TIMESTAMP" property="updateDate"/>
        <result column="INFOR_BILL_NO" jdbcType="VARCHAR" property="inforBillNo"/>
        <result column="SCRAP_REASON" jdbcType="VARCHAR" property="scrapReason"/>
        <collection property="details" columnPrefix="line_" resultMap="childResultMap"/>
    </resultMap>

    <resultMap id="childResultMap" type="com.zte.interfaces.dto.MaintenanceMaterialLineDTO">
        <result column="LINE_ID" jdbcType="VARCHAR" property="lineId"/>
        <result column="HEAD_ID" jdbcType="VARCHAR" property="headId"/>
        <result column="MAIN_SN" jdbcType="VARCHAR" property="mainSn"/>
        <result column="MAIN_BOM_NAME" jdbcType="VARCHAR" property="mainBomName"/>
        <result column="SUB_SN" jdbcType="VARCHAR" property="subSn"/>
        <result column="SUB_BOM_NAME" jdbcType="VARCHAR" property="subBomName"/>
        <result column="BOM_NO" jdbcType="VARCHAR" property="bomNo"/>
        <result column="LOCATION_NO" jdbcType="VARCHAR" property="locationNo"/>
        <result column="BRAND_NAME" jdbcType="VARCHAR" property="brandName"/>
        <result column="USAGE_COUNT" jdbcType="DECIMAL" property="usageCount"/>
        <result column="NEED_QTY" jdbcType="DECIMAL" property="needQty"/>
        <result column="DELIVERY_NO" jdbcType="VARCHAR" property="deliveryNo"/>
        <result column="ITEM_STATUS" jdbcType="VARCHAR" property="itemStatus"/>
        <result column="ENABLE_FLAG" jdbcType="VARCHAR" property="enableFlag"/>
        <result column="CREATE_USER" jdbcType="VARCHAR" property="createUser"/>
        <result column="UPDATE_USER" jdbcType="VARCHAR" property="updateUser"/>
        <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="UPDATE_DATE" jdbcType="TIMESTAMP" property="updateDate"/>
    </resultMap>

    <!-- 导出字段映射 -->
    <resultMap id="exprotMap" type="com.zte.interfaces.dto.MaintenanceMaterialExportDTO">
        <result column="MATERIAL_REQUISITION_BILL" jdbcType="VARCHAR" property="materialRequisitionBill"/>
        <result column="BILL_STATUS" jdbcType="VARCHAR" property="billStatus"/>
        <result column="TEAM_GROUP" jdbcType="VARCHAR" property="teamGroup"/>
        <result column="PRODPLAN_ID" jdbcType="VARCHAR" property="prodplanId"/>
        <result column="ITEM_NO" jdbcType="VARCHAR" property="itemNo"/>
        <result column="ITEM_NAME" jdbcType="VARCHAR" property="itemName"/>
        <result column="APPROVED_BY" jdbcType="VARCHAR" property="approvedBy"/>
        <result column="APPROVED_RESULT" jdbcType="VARCHAR" property="approvedResult"/>
        <result column="OLD_MATERIAL_STATUS" jdbcType="VARCHAR" property="oldMaterialStatus"/>
        <result column="CHANGE_TYPE" jdbcType="VARCHAR" property="changeType"/>
        <result column="CONTROL_FLAG" jdbcType="VARCHAR" property="controlFlag"/>
        <result column="COLLECTORS" jdbcType="VARCHAR" property="collectors"/>
        <result column="CREATE_USER" jdbcType="VARCHAR" property="createUser"/>
        <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="INFOR_BILL_NO" jdbcType="VARCHAR" property="inforBillNo"/>
        <result column="MAIN_SN" jdbcType="VARCHAR" property="mainSn"/>
        <result column="MAIN_BOM_NAME" jdbcType="VARCHAR" property="mainBomName"/>
        <result column="SUB_SN" jdbcType="VARCHAR" property="subSn"/>
        <result column="SUB_BOM_NAME" jdbcType="VARCHAR" property="subBomName"/>
        <result column="LOCATION_NO" jdbcType="VARCHAR" property="locationNo"/>
        <result column="BRAND_NAME" jdbcType="VARCHAR" property="brandName"/>
        <result column="NEED_QTY" jdbcType="DECIMAL" property="needQty"/>
        <result column="DELIVERY_NO" jdbcType="VARCHAR" property="deliveryNo"/>
        <result column="ITEM_STATUS" jdbcType="VARCHAR" property="itemStatus"/>

        <!--维修信息 -->
        <result column="WX_SN" jdbcType="VARCHAR" property="wxSn"/>
        <result column="WX_SUB_ITEM_SN" jdbcType="VARCHAR" property="wxSubItemSn"/>
        <result column="WX_ITEM_CODE" jdbcType="VARCHAR" property="wxItemCode"/>
        <result column="WX_LOCATION_NO" jdbcType="VARCHAR" property="wxLocationNo"/>
        <result column="WX_REPAIR_PRODUCT_TYPE" jdbcType="VARCHAR" property="wxRepairProductType"/>
        <result column="WX_REPAIR_PRODUCT_STYPE" jdbcType="VARCHAR" property="wxRepairProductStype"/>
        <result column="WX_REPAIR_PRODUCT_MSTYPE" jdbcType="VARCHAR" property="wxRepairProductMstype"/>
        <result column="WX_REPAIR_BY" jdbcType="VARCHAR" property="wxRepairBy"/>

    </resultMap>

    <sql id="Base_Column_List">
        HEAD_ID,MATERIAL_REQUISITION_BILL,INFOR_BILL_NO,BILL_STATUS,TEAM_GROUP,PRODPLAN_ID,ITEM_NO,ITEM_NAME,APPROVED_BY,APPROVED_RESULT,
        APPROVED_NOTES,APPROVED_DATE,OLD_MATERIAL_STATUS,COLLECTORS,COLLECTORS_DATE,CHANGE_TYPE,ENABLE_FLAG,CONTROL_FLAG,
        CREATE_DATE,CREATE_USER,UPDATE_DATE,UPDATE_USER,CHILD_CARD
    </sql>

    <insert id="insertOneHead" parameterType="com.zte.interfaces.dto.MaintenanceMaterialHeadDTO">
        INSERT INTO MAINTENANCE_MATERIAL_HEADER
        (HEAD_ID,MATERIAL_REQUISITION_BILL,BILL_STATUS,TEAM_GROUP,PRODPLAN_ID,
        ITEM_NO,ITEM_NAME,APPROVED_BY,APPROVED_RESULT,APPROVED_NOTES,APPROVED_DATE,
        OLD_MATERIAL_STATUS,COLLECTORS,COLLECTORS_DATE,CHANGE_TYPE,ENABLE_FLAG,
        CREATE_DATE,CREATE_USER,UPDATE_DATE,UPDATE_USER, CONTROL_FLAG,CHILD_CARD ) VALUES
        (
        #{headId,jdbcType=VARCHAR},#{materialRequisitionBill,jdbcType=VARCHAR},#{billStatus,jdbcType=VARCHAR},#{teamGroup,jdbcType=VARCHAR}
        ,#{prodplanId,jdbcType=VARCHAR} ,#{itemNo,jdbcType=VARCHAR},#{itemName,jdbcType=VARCHAR},#{approvedBy,jdbcType=VARCHAR},
        #{approvedResult,jdbcType=VARCHAR},#{approvedNotes,jdbcType=VARCHAR},#{approvedDate,jdbcType=TIMESTAMP},
        #{oldMaterialStatus,jdbcType=VARCHAR},#{collectors,jdbcType=VARCHAR},#{collectorsDate,jdbcType=TIMESTAMP},
        #{changeType,jdbcType=VARCHAR},'Y',
        sysdate,#{createUser,jdbcType=VARCHAR},sysdate,#{updateUser,jdbcType=VARCHAR},#{controlFlag,jdbcType=VARCHAR},#{childCard,jdbcType=VARCHAR}
        )

    </insert>

    <insert id="insertBatchHead" parameterType="java.util.List">
        INSERT ALL
        <foreach collection="headDTOList" index="index" item="item"
                 separator="">
            INTO MAINTENANCE_MATERIAL_HEADER
            (HEAD_ID,MATERIAL_REQUISITION_BILL,BILL_STATUS,TEAM_GROUP,PRODPLAN_ID,
            ITEM_NO,ITEM_NAME,APPROVED_BY,APPROVED_RESULT,APPROVED_NOTES,APPROVED_DATE,
            OLD_MATERIAL_STATUS,COLLECTORS,COLLECTORS_DATE,CHANGE_TYPE,ENABLE_FLAG,
            CREATE_DATE,CREATE_USER,UPDATE_DATE,UPDATE_USER,CONTROL_FLAG,CHILD_CARD) VALUES
            (
            #{item.headId,jdbcType=VARCHAR},#{item.materialRequisitionBill,jdbcType=VARCHAR},#{item.billStatus,jdbcType=VARCHAR},#{item.teamGroup,jdbcType=VARCHAR},#{item.prodplanId,jdbcType=VARCHAR}
            ,#{item.itemNo,jdbcType=VARCHAR},#{item.itemName,jdbcType=VARCHAR},#{item.approvedBy,jdbcType=VARCHAR},#{item.approvedResult,jdbcType=VARCHAR},#{item.approvedNotes,jdbcType=VARCHAR},#{item.approvedDate,jdbcType=TIMESTAMP},
            #{item.oldMaterialStatus,jdbcType=VARCHAR},#{item.collectors,jdbcType=VARCHAR},#{item.collectorsDate,jdbcType=TIMESTAMP},#{item.changeType,jdbcType=VARCHAR},'Y',
            sysdate,#{item.createUser,jdbcType=VARCHAR},sysdate,#{item.updateUser,jdbcType=VARCHAR},#{item.controlFlag,jdbcType=VARCHAR},#{childCard,jdbcType=VARCHAR}
            )
        </foreach>
        SELECT *
    </insert>

    <update id="updateHeadByAI">
        update MAINTENANCE_MATERIAL_HEADER
        set
        BILL_STATUS = #{billStatus, jdbcType=VARCHAR},
        INFOR_BILL_NO = #{inforBillNo, jdbcType=VARCHAR},
        update_user = #{updateUser, jdbcType=VARCHAR},
        update_date = sysdate
        where MATERIAL_REQUISITION_BILL = #{materialRequisitionBill,jdbcType=VARCHAR}
    </update>

    <update id="updateHeadByHeadId" parameterType="com.zte.interfaces.dto.MaintenanceMaterialHeadDTO">
        update MAINTENANCE_MATERIAL_HEADER
        SET ENABLE_FLAG = 'Y'
        ,UPDATE_DATE = sysdate
        <if test="updateUser != null and updateUser != ''">
            ,UPDATE_USER = #{updateUser,jdbcType=VARCHAR}
        </if>
        <if test="billStatus != null and billStatus != ''">
            ,BILL_STATUS = #{billStatus,jdbcType=VARCHAR}
        </if>
        <if test="materialRequisitionBill != null and materialRequisitionBill != ''">
            ,MATERIAL_REQUISITION_BILL = #{materialRequisitionBill,jdbcType=VARCHAR}
        </if>
        <if test="changeType != null">
            ,CHANGE_TYPE = #{changeType,jdbcType=VARCHAR}
        </if>
        <if test="oldMaterialStatus != null">
            ,OLD_MATERIAL_STATUS = #{oldMaterialStatus,jdbcType=VARCHAR}
        </if>
        <if test="inforBillNo != null">
            ,INFOR_BILL_NO = #{inforBillNo,jdbcType=VARCHAR}
        </if>
        <if test="scrapReason != null">
            ,SCRAP_REASON = #{scrapReason,jdbcType=VARCHAR}
        </if>
        <if test="approvedBy != null and approvedBy != ''">
            ,APPROVED_BY = #{approvedBy,jdbcType=VARCHAR}
        </if>
        <if test="approvedDate != null">
            ,APPROVED_DATE = #{approvedDate,jdbcType=TIMESTAMP}
        </if>
        <if test="approvedResult != null and approvedResult != ''">
            ,APPROVED_RESULT = #{approvedResult,jdbcType=VARCHAR}
        </if>
        <if test="(approvedNotes == null or approvedNotes =='') and approvedFlag">
            ,APPROVED_NOTES = null
        </if>
        <if test="approvedNotes != null and approvedNotes !=''">
            ,APPROVED_NOTES = #{approvedNotes,jdbcType=VARCHAR}
        </if>
        <if test="collectors != null and collectors !=''">
            ,COLLECTORS = #{collectors,jdbcType=VARCHAR}
        </if>
        <if test="collectorsDate != null">
            ,COLLECTORS_DATE = #{collectorsDate,jdbcType=TIMESTAMP}
        </if>
        WHERE
        <if test="headId != null and headId !=''">
            HEAD_ID = #{headId,jdbcType=VARCHAR}
        </if>
        <if test="headId == null or headId ==''">
            1 = 2
        </if>
    </update>

    <delete id="deleteOneByHeadId" parameterType="com.zte.interfaces.dto.MaintenanceMaterialHeadDTO">
        DELETE FROM MAINTENANCE_MATERIAL_HEADER
        WHERE ENABLE_FLAG = 'Y'
        <if test="headId != null and headId !=''">
            AND HEAD_ID=#{headId,jdbcType=VARCHAR}
        </if>
        <if test="headId == null or headId ==''">
            AND 1 = 2
        </if>
    </delete>

    <select id="queryPageByCondition" resultMap="BaseResultMap"
            parameterType="com.zte.springbootframe.common.model.Page">
        SELECT distinct
        h.HEAD_ID,h.MATERIAL_REQUISITION_BILL,h.INFOR_BILL_NO,h.BILL_STATUS,h.TEAM_GROUP,h.PRODPLAN_ID,h.ITEM_NO,h.ITEM_NAME,h.APPROVED_BY,h.APPROVED_RESULT,
        h.APPROVED_NOTES,h.APPROVED_DATE,h.OLD_MATERIAL_STATUS,h.COLLECTORS,h.COLLECTORS_DATE,h.CHANGE_TYPE,h.ENABLE_FLAG,h.CONTROL_FLAG,
            h.CREATE_DATE,h.CREATE_USER,h.UPDATE_DATE,h.UPDATE_USER,h.CHILD_CARD
        FROM MAINTENANCE_MATERIAL_HEADER h
        left join maintenance_material_line l
        on l.head_id = h.head_id
        WHERE h.ENABLE_FLAG = 'Y'
        <if test="params.headId != null and params.headId != ''">
            AND h.HEAD_ID = #{params.headId,jdbcType=VARCHAR}
        </if>
        <if test="params.mainSn != null and params.mainSn != ''"> AND l.main_sn = #{params.mainSn}</if>
        <if test="params.subSn != null and params.subSn != ''"> AND l.sub_sn = #{params.subSn}</if>
        <if test="params.createUser != null and params.createUser != ''">
            AND h.CREATE_USER = #{params.createUser,jdbcType=VARCHAR}
        </if>
        <if test="params.teamGroup != null and params.teamGroup != ''">
            AND h.TEAM_GROUP = #{params.teamGroup,jdbcType=VARCHAR}
        </if>
        <if test="params.materialRequisitionBill !=null and params.materialRequisitionBill !=''">
            AND h.MATERIAL_REQUISITION_BILL = #{params.materialRequisitionBill,jdbcType=VARCHAR}
        </if>
        <if test="params.billStatus !=null and params.billStatus !=''">
            AND h.BILL_STATUS = #{params.billStatus,jdbcType=VARCHAR}
        </if>
        <if test="params.inforBillNo != null and params.inforBillNo != ''">
            AND h.INFOR_BILL_NO = #{params.inforBillNo,jdbcType=VARCHAR}
        </if>
        <if test="params.createDateStart !=null and params.createDateEnd !=null">
            AND h.CREATE_DATE between #{params.createDateStart,jdbcType=TIMESTAMP} and #{params.createDateEnd,jdbcType=TIMESTAMP}
        </if>
        order by update_date desc
    </select>


    <select id="queryPageByConditionWithDetails"
            resultMap="BaseResultMap" parameterType="com.zte.springbootframe.common.model.Page">
        SELECT
        h.HEAD_ID,h.MATERIAL_REQUISITION_BILL,h.BILL_STATUS,h.TEAM_GROUP,h.PRODPLAN_ID,h.ITEM_NO,h.ITEM_NAME,h.APPROVED_BY,h.APPROVED_RESULT,h.APPROVED_NOTES,
        h.APPROVED_DATE,h.OLD_MATERIAL_STATUS,h.COLLECTORS,h.COLLECTORS_DATE,h.CHANGE_TYPE,h.ENABLE_FLAG,h.CONTROL_FLAG,h.CREATE_DATE,h.CREATE_USER,
        h.UPDATE_DATE,h.UPDATE_USER,H.CHILD_CARD,h.INFOR_BILL_NO,h.SCRAP_REASON,
        l.HEAD_ID line_HEAD_ID,l.LINE_ID line_LINE_ID,l.MAIN_SN line_MAIN_SN ,l.MAIN_BOM_NAME line_MAIN_BOM_NAME,
        l.SUB_SN line_SUB_SN,l.SUB_BOM_NAME line_SUB_BOM_NAME,l.BOM_NO line_BOM_NO,l.LOCATION_NO line_LOCATION_NO,l.BRAND_NAME line_BRAND_NAME,l.USAGE_COUNT line_USAGE_COUNT,
        l.NEED_QTY line_NEED_QTY,l.DELIVERY_NO line_DELIVERY_NO,l.ITEM_STATUS line_ITEM_STATUS, l.ENABLE_FLAG
        line_ENABLE_FLAG,l.CREATE_DATE line_CREATE_DATE,l.CREATE_USER line_CREATE_USER,
        l.UPDATE_DATE line_UPDATE_DATE,l.UPDATE_USER line_UPDATE_USER
        FROM MAINTENANCE_MATERIAL_HEADER h
        left join MAINTENANCE_MATERIAL_line l
        on h.HEAD_ID=l.HEAD_ID and l.ENABLE_FLAG='Y'
        WHERE h.ENABLE_FLAG = 'Y'
        <if test="params.headId != null and params.headId != ''">
            AND h.HEAD_ID = #{params.headId,jdbcType=VARCHAR}
        </if>
        <if test="params.createUser != null and params.createUser != ''">
            AND h.CREATE_USER = #{params.createUser,jdbcType=VARCHAR}
        </if>
        <if test="params.teamGroup != null and params.teamGroup != ''">
            AND h.TEAM_GROUP = #{params.teamGroup,jdbcType=VARCHAR}
        </if>
        <if test="params.materialRequisitionBill !=null and params.materialRequisitionBill !=''">
            AND h.MATERIAL_REQUISITION_BILL = #{params.materialRequisitionBill,jdbcType=VARCHAR}
        </if>
        <if test="params.billStatus !=null and params.billStatus !=''">
            AND h.BILL_STATUS = #{params.billStatus,jdbcType=VARCHAR}
        </if>
        <if test="params.createDateStart !=null and params.createDateEnd !=null">
            AND h.CREATE_DATE between #{params.createDateStart,jdbcType=TIMESTAMP} and #{params.createDateEnd,jdbcType=TIMESTAMP}
        </if>
    </select>

    <select id="queryReleaseHeadAndDetailInfo"
            resultMap="BaseResultMap" parameterType="com.zte.interfaces.dto.MaintenanceMaterialHeadDTO">
        SELECT
        h.HEAD_ID,h.MATERIAL_REQUISITION_BILL,h.BILL_STATUS,h.TEAM_GROUP,h.PRODPLAN_ID,h.ITEM_NO,h.ITEM_NAME,h.APPROVED_BY,h.APPROVED_RESULT,h.APPROVED_NOTES,
        h.APPROVED_DATE,h.OLD_MATERIAL_STATUS,h.COLLECTORS,h.COLLECTORS_DATE,h.CHANGE_TYPE,h.ENABLE_FLAG,h.CONTROL_FLAG,h.CREATE_DATE,h.CREATE_USER,
        h.UPDATE_DATE,h.UPDATE_USER,h.ATTRIBUTE1,h.ATTRIBUTE2,h.ATTRIBUTE3,h.ATTRIBUTE4,h.ATTRIBUTE5,
        h.INFOR_BILL_NO,h.SCRAP_REASON,H.CHILD_CARD,
        l.HEAD_ID line_HEAD_ID,l.LINE_ID line_LINE_ID,l.MAIN_SN line_MAIN_SN ,l.MAIN_BOM_NAME line_MAIN_BOM_NAME,
        l.SUB_SN line_SUB_SN,l.SUB_BOM_NAME line_SUB_BOM_NAME,l.BOM_NO line_BOM_NO,l.LOCATION_NO line_LOCATION_NO,l.BRAND_NAME line_BRAND_NAME,l.USAGE_COUNT line_USAGE_COUNT,
        l.NEED_QTY line_NEED_QTY,l.DELIVERY_NO line_DELIVERY_NO,l.ITEM_STATUS line_ITEM_STATUS, l.ENABLE_FLAG
        line_ENABLE_FLAG,l.CREATE_DATE line_CREATE_DATE,l.CREATE_USER line_CREATE_USER,
        l.UPDATE_DATE line_UPDATE_DATE,l.UPDATE_USER line_UPDATE_USER
        FROM MAINTENANCE_MATERIAL_HEADER h
        left join MAINTENANCE_MATERIAL_line l
        on h.HEAD_ID=l.HEAD_ID and l.ENABLE_FLAG='Y'
        WHERE h.ENABLE_FLAG = 'Y'
        <if test="headId != null and headId != ''">
            AND h.HEAD_ID = #{headId,jdbcType=VARCHAR}
        </if>
        <if test="createUser != null and createUser != ''">
            AND h.CREATE_USER = #{createUser,jdbcType=VARCHAR}
        </if>
        <if test="teamGroup != null and teamGroup != ''">
            AND h.TEAM_GROUP = #{teamGroup,jdbcType=VARCHAR}
        </if>
        <if test="materialRequisitionBill !=null and materialRequisitionBill !=''">
            AND h.MATERIAL_REQUISITION_BILL = #{materialRequisitionBill,jdbcType=VARCHAR}
        </if>
        <if test="billStatus !=null and billStatus !=''">
            AND h.BILL_STATUS = #{billStatus,jdbcType=VARCHAR}
        </if>
        <if test="createDateStart !=null and createDateEnd !=null">
            AND h.CREATE_DATE between #{createDateStart,jdbcType=TIMESTAMP} and #{createDateEnd,jdbcType=TIMESTAMP}
        </if>
        <if test="(headId == null or headId == '') and (createUser == null or createUser == '') and (teamGroup == null or teamGroup == '') and
                (materialRequisitionBill == null or materialRequisitionBill == '') and (billStatus == null or billStatus == '') and
                (createDateStart == null or createDateStart == '')">
            and 1=2
        </if>

    </select>

    <select id="queryPageByWithDetails"
            resultMap="exprotMap" parameterType="com.zte.springbootframe.common.model.Page">
        SELECT
        h.HEAD_ID,h.MATERIAL_REQUISITION_BILL,h.BILL_STATUS,h.TEAM_GROUP,h.PRODPLAN_ID,h.ITEM_NO,h.ITEM_NAME,h.APPROVED_BY,h.APPROVED_RESULT,h.APPROVED_NOTES,
        h.APPROVED_DATE,h.OLD_MATERIAL_STATUS,h.COLLECTORS,h.COLLECTORS_DATE,h.CHANGE_TYPE,h.CONTROL_FLAG,h.CREATE_DATE,h.CREATE_USER,
        h.UPDATE_DATE,h.UPDATE_USER,H.CHILD_CARD,h.INFOR_BILL_NO,
        l.MAIN_SN,l.MAIN_BOM_NAME,l.SUB_SN ,l.SUB_BOM_NAME,l.BOM_NO ,l.LOCATION_NO ,l.BRAND_NAME ,l.USAGE_COUNT ,l.NEED_QTY ,l.DELIVERY_NO ,l.ITEM_STATUS,
        rd.SN WX_SN,rd.SUB_ITEM_SN WX_SUB_ITEM_SN,rd.ITEM_CODE WX_ITEM_CODE,rd.LOCATION_NO
        WX_LOCATION_NO,rd.REPAIR_PRODUCT_TYPE WX_REPAIR_PRODUCT_TYPE,
        rd.REPAIR_PRODUCT_STYPE WX_REPAIR_PRODUCT_STYPE,rd.REPAIR_PRODUCT_MSTYPE WX_REPAIR_PRODUCT_MSTYPE,rd.REPAIR_BY WX_REPAIR_BY
        FROM MAINTENANCE_MATERIAL_HEADER h
        left join MAINTENANCE_MATERIAL_line l
        on h.HEAD_ID=l.HEAD_ID and l.ENABLE_FLAG='Y'
        left join
        (select r.*,
        row_number() over(PARTITION BY r.SUB_ITEM_SN, r.LOCATION_NO, r.ITEM_CODE, r.SN ORDER BY r.LAST_UPDATED_DATE DESC) AS rn_no
        from pm_repair_detail r
        where r.ENABLED_FLAG = 'Y' and r.REPAIR_PRODUCT_MSTYPE = '更换器件' ) rd on rd.SN = l.MAIN_SN
        and rd.SUB_ITEM_SN = l.SUB_SN
        and l.LOCATION_NO =
        rd.LOCATION_NO
        and h.ITEM_NO = rd.ITEM_CODE
        and rd.ENABLED_FLAG = 'Y'
        and rn_no = 1
        WHERE h.ENABLE_FLAG = 'Y'

        <if test="params.headId != null and params.headId != ''">
            AND h.HEAD_ID = #{params.headId,jdbcType=VARCHAR}
        </if>
        <if test="params.createUser != null and params.createUser != ''">
            AND h.CREATE_USER = #{params.createUser,jdbcType=VARCHAR}
        </if>
        <if test="params.teamGroup != null and params.teamGroup != ''">
            AND h.TEAM_GROUP = #{params.teamGroup,jdbcType=VARCHAR}
        </if>
        <if test="params.materialRequisitionBill !=null and params.materialRequisitionBill !=''">
            AND h.MATERIAL_REQUISITION_BILL = #{params.materialRequisitionBill,jdbcType=VARCHAR}
        </if>
        <if test="params.billStatus !=null and params.billStatus !=''">
            AND h.BILL_STATUS = #{params.billStatus,jdbcType=VARCHAR}
        </if>
        <if test="params.inforBillNo != null and params.inforBillNo != ''">
            AND INFOR_BILL_NO = #{params.inforBillNo,jdbcType=VARCHAR}
        </if>
        <if test="params.createDateStart !=null and params.createDateEnd !=null">
            AND h.CREATE_DATE between #{params.createDateStart,jdbcType=TIMESTAMP} and #{params.createDateEnd,jdbcType=TIMESTAMP}
        </if>
    </select>

    <select id="getTBCBillLast2Year" resultType="com.zte.interfaces.dto.MaintenanceMaterialInfoDTO">
        select h.head_id, h.material_requisition_bill, h.change_type, h.item_no, h.prodplan_id,
        h.create_user,l.line_id, l.main_sn, l.sub_sn, l.brand_name
        from maintenance_material_header h
        left join maintenance_material_line l
        on h.head_id = l.head_id
        where h.enable_flag = 'Y'
        and l.enable_flag='Y'
        and h.bill_status = '2'
        and h.create_date > sysdate - 732
        order by h.create_date
        limit 500
    </select>

    <select id="getNeedQty" resultType="com.zte.interfaces.dto.MaintenanceMaterialInfoDTO">
        select h.material_requisition_bill, h.item_no, sum(l.need_qty) as needQty, nvl(l.brand_name, '') as brand_name
        from maintenance_material_header h, maintenance_material_line l
        where h.head_id = l.head_id
        and h.enable_flag = 'Y' and l.enable_flag='Y'
        and h.material_requisition_bill in
        <foreach collection="billNoList" item="billNo" open="(" close=")" separator=",">
            #{billNo}
        </foreach>
        group by h.material_requisition_bill, h.item_no, brand_name
    </select>

    <select id="getBillInfoByBillNo" resultType="com.zte.interfaces.dto.MaintenanceMaterialInfoDTO">
        select h.material_requisition_bill, h.item_no, h.prodplan_id, h.change_type, l.need_qty, nvl(l.brand_name, '') as brand_name
        ,l.main_sn, l.sub_sn
        from maintenance_material_header h, maintenance_material_line l
        where h.head_id = l.head_id
        and h.enable_flag = 'Y' and l.enable_flag='Y'
        and h.material_requisition_bill = #{billNo}
    </select>
</mapper>
