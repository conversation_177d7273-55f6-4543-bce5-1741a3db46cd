<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.erpdt.WipEntitiesRepository">
  <resultMap id="BaseResultMap" type="com.zte.domain.model.erpdt.WipEntities">
    <result column="WIP_ENTITY_ID" jdbcType="DECIMAL" property="wipEntityId" />
    <result column="ORGANIZATION_ID" jdbcType="DECIMAL" property="organizationId" />
    <result column="LAST_UPDATE_DATE" jdbcType="TIMESTAMP" property="lastUpdateDate" />
    <result column="LAST_UPDATED_BY" jdbcType="DECIMAL" property="lastUpdatedBy" />
    <result column="CREATION_DATE" jdbcType="TIMESTAMP" property="creationDate" />
    <result column="CREATED_BY" jdbcType="DECIMAL" property="createdBy" />
    <result column="LAST_UPDATE_LOGIN" jdbcType="DECIMAL" property="lastUpdateLogin" />
    <result column="REQUEST_ID" jdbcType="DECIMAL" property="requestId" />
    <result column="PROGRAM_APPLICATION_ID" jdbcType="DECIMAL" property="programApplicationId" />
    <result column="PROGRAM_ID" jdbcType="DECIMAL" property="programId" />
    <result column="PROGRAM_UPDATE_DATE" jdbcType="TIMESTAMP" property="programUpdateDate" />
    <result column="WIP_ENTITY_NAME" jdbcType="VARCHAR" property="wipEntityName" />
    <result column="ENTITY_TYPE" jdbcType="DECIMAL" property="entityType" />
    <result column="DESCRIPTION" jdbcType="VARCHAR" property="description" />
    <result column="PRIMARY_ITEM_ID" jdbcType="DECIMAL" property="primaryItemId" />
    <result column="GEN_OBJECT_ID" jdbcType="DECIMAL" property="genObjectId" />
  </resultMap>
  <resultMap id="BaseResultDTOMap" type="com.zte.interfaces.dto.WipEntitiesDTO">
    <result column="WIP_ENTITY_ID" jdbcType="DECIMAL" property="wipEntityId" />
    <result column="ORGANIZATION_ID" jdbcType="DECIMAL" property="organizationId" />
    <result column="LAST_UPDATE_DATE" jdbcType="TIMESTAMP" property="lastUpdateDate" />
    <result column="LAST_UPDATED_BY" jdbcType="DECIMAL" property="lastUpdatedBy" />
    <result column="CREATION_DATE" jdbcType="TIMESTAMP" property="creationDate" />
    <result column="CREATED_BY" jdbcType="DECIMAL" property="createdBy" />
    <result column="LAST_UPDATE_LOGIN" jdbcType="DECIMAL" property="lastUpdateLogin" />
    <result column="REQUEST_ID" jdbcType="DECIMAL" property="requestId" />
    <result column="PROGRAM_APPLICATION_ID" jdbcType="DECIMAL" property="programApplicationId" />
    <result column="PROGRAM_ID" jdbcType="DECIMAL" property="programId" />
    <result column="PROGRAM_UPDATE_DATE" jdbcType="TIMESTAMP" property="programUpdateDate" />
    <result column="WIP_ENTITY_NAME" jdbcType="VARCHAR" property="wipEntityName" />
    <result column="ENTITY_TYPE" jdbcType="DECIMAL" property="entityType" />
    <result column="DESCRIPTION" jdbcType="VARCHAR" property="description" />
    <result column="PRIMARY_ITEM_ID" jdbcType="DECIMAL" property="primaryItemId" />
    <result column="GEN_OBJECT_ID" jdbcType="DECIMAL" property="genObjectId" />
    <result column="MPS_NET_QUANTITY" jdbcType="DECIMAL" property="mpsNetQuantity" />
    <result column="SCHEDULED_START_DATE" jdbcType="TIMESTAMP" property="scheduledStartDate" />
    <result column="START_QUANTITY" jdbcType="DECIMAL" property="startQuantity" />
    <result column="MEANING" jdbcType="VARCHAR" property="meaning" />
    <result column="STATUS_TYPE" jdbcType="VARCHAR" property="statusType" />
  </resultMap>
  <resultMap id="WipResultMap" type="com.zte.interfaces.dto.WipEntitiesGetDTO">
     <result column="wip_entity_name" jdbcType="VARCHAR" property="prodPlanNo" />
     <result column="start_quantity" jdbcType="DECIMAL" property="qty" />
     <result column="quantity_completed" jdbcType="DECIMAL" property="qtyOk" />
     <result column="date_released" jdbcType="VARCHAR" property="dateReleased" />
     <result column="noqty" jdbcType="DECIMAL" property="qtyNo" />
     <result column="organization_id" jdbcType="DECIMAL" property="orgId" />
  </resultMap>
  <resultMap id="ErpMoneyResultMap" type="com.zte.interfaces.dto.ErpMoneyDTO">
     <result column="wip_entity_name" jdbcType="VARCHAR" property="prodPlanNo" />
     <result column="get_money" jdbcType="DECIMAL" property="getMoney" />
  </resultMap>
  <resultMap id="SmReportMap" type="com.zte.interfaces.dto.SmDailyStatisticReportDTO">
    <result column="taskNo" jdbcType="VARCHAR" property="taskNo" />
    <result column="erpFQty" jdbcType="DECIMAL" property="erpFQty" />
    <result column="scheduledStartDate" jdbcType="TIMESTAMP" property="scheduledStartDate" />
    <result column="dateReleased" jdbcType="TIMESTAMP" property="dateReleased" />
    <result column="scheduledCompletionDate" jdbcType="TIMESTAMP" property="scheduledCompletionDate" />
    <result column="erpStatus" jdbcType="VARCHAR" property="erpStatus" />
    <result column="erpStatusCode" jdbcType="DECIMAL" property="erpStatusCode" />
  </resultMap>


  <select id="selectErpSendEnitity" resultMap="WipResultMap" parameterType="com.zte.interfaces.dto.WipEntitiesSetDTO">
      select b.wip_entity_name,
       a.start_quantity,
       a.quantity_completed,
       to_char(a.date_released,'yyyy-MM-dd HH24:mi:ss') date_released ,
       (a.start_quantity - a.quantity_completed) noqty,
       b.organization_id
  from apps.wip_discrete_jobs a, apps.wip_entities b
 where a.wip_entity_id = b.wip_entity_id
   AND a.organization_id = b.organization_id
   and a.status_type = 3
   and ((a.class_code = 'STD_SYS' and a.organization_id = 855) or
       a.organization_id = 395)
       <if test="prodPlanNo != null">
           and b.wip_entity_name like  CONCAT(#{prodPlanNo},'%')
       </if>
       <if test="prodPlanId != null and prodPlanId!=0 ">
           and b.wip_entity_id =  #{prodPlanId}
       </if>
  </select>
  <select id="selectErpMoney" resultMap="ErpMoneyResultMap" parameterType="com.zte.interfaces.dto.WipEntitiesSetDTO">
      SELECT we.wip_entity_name, SUM(mta.base_transaction_value) get_money
	  FROM APPS.MTL_MATERIAL_TRANSACTIONS MMT,
	       apps.wip_entities              we,
	       apps.mtl_transaction_accounts  mta
	 WHERE MMT.TRANSACTION_SOURCE_TYPE_ID = 5
	   and mmt.transaction_source_id = WE.WIP_ENTITY_ID
	   AND WE.WIP_ENTITY_NAME = #{prodPlanNo}
	   and we.organization_id = #{orgId}
	   and mta.transaction_id = mmt.transaction_id
	   and mta.accounting_line_type = 7
	 group by WE.WIP_ENTITY_NAME
  </select>
  <select id="selectMpsNetQty" resultMap="BaseResultDTOMap" parameterType="java.lang.String">
      SELECT B.MPS_NET_QUANTITY,B.SCHEDULED_START_DATE,B.START_QUANTITY
      FROM WIP.WIP_ENTITIES A
      LEFT JOIN WIP.WIP_DISCRETE_JOBS B ON A.WIP_ENTITY_ID = B.WIP_ENTITY_ID
      WHERE A.WIP_ENTITY_NAME = #{wipEntityName, jdbcType=VARCHAR}
  </select>

  <select id="getMovableQuantity" resultType="com.zte.interfaces.dto.WipOperationsDto" parameterType="java.lang.String">
       select * from ( SELECT  T.WIP_ENTITY_ID wipEntityId,T.OPERATION_SEQ_NUM operationSeqNum,T.QUANTITY_IN_QUEUE quantityInQueue
        FROM APPS.WIP_OPERATIONS T left join APPS.WIP_ENTITIES  W on W.WIP_ENTITY_ID=T.WIP_ENTITY_ID
        WHERE 1=1 AND W.WIP_ENTITY_NAME = #{wipEntityName, jdbcType=VARCHAR}
           ORDER BY T.OPERATION_SEQ_NUM) where rownum=1
  </select>

  <select id="getSmReport" resultMap="SmReportMap" parameterType="com.zte.interfaces.dto.WipEntitiesReportGetDTO">
    SELECT * FROM ( SELECT t.*, ROWNUM RN FROM (
    SELECT A.SCHEDULED_START_DATE scheduledStartDate,
    A.DATE_RELEASED dateReleased,
    A.SCHEDULED_COMPLETION_DATE scheduledCompletionDate,
    A.QUANTITY_COMPLETED erpFQty,
    B.WIP_ENTITY_NAME taskNo,
    A.STATUS_TYPE erpStatusCode,
    (SELECT C.MEANING
    FROM (SELECT LOOKUP_TYPE,
    LOOKUP_CODE,
    MEANING
    FROM APPS.FND_LOOKUP_VALUES
    WHERE LANGUAGE = 'ZHS') C
    WHERE TO_CHAR(A.STATUS_TYPE) = C.LOOKUP_CODE
    AND C.LOOKUP_TYPE = 'WIP_JOB_STATUS') erpStatus
    FROM APPS.WIP_DISCRETE_JOBS A, APPS.WIP_ENTITIES B
    WHERE A.WIP_ENTITY_ID = B.WIP_ENTITY_ID
    AND A.ORGANIZATION_ID = B.ORGANIZATION_ID
    <if test="status != null">
      AND A.STATUS_TYPE =  #{status, jdbcType=DECIMAL}
    </if>
    <choose>
      <when test="tasks != null and tasks.size()>0">
        AND B.WIP_ENTITY_NAME IN
        <foreach collection="tasks" item="task" open=" (" separator=" UNION ALL " close=")">
          SELECT #{task, jdbcType=VARCHAR} FROM DUAL
        </foreach>
      </when>
      <otherwise>AND 1=2 </otherwise>
    </choose>
    ORDER BY taskNo) t)
    <if test="end != null and start != null ">
      WHERE (RN BETWEEN #{start, jdbcType=DECIMAL} AND #{end, jdbcType=DECIMAL})
    </if>
  </select>

  <select id="getSmReportCount" resultType="Integer" parameterType="com.zte.interfaces.dto.WipEntitiesReportGetDTO">
    SELECT COUNT(1)
    FROM APPS.WIP_DISCRETE_JOBS A, APPS.WIP_ENTITIES B
    WHERE A.WIP_ENTITY_ID = B.WIP_ENTITY_ID
    AND A.ORGANIZATION_ID = B.ORGANIZATION_ID
    <if test="status != null">
      AND A.STATUS_TYPE =  #{status, jdbcType=DECIMAL}
    </if>
    <choose>
      <when test="tasks != null and tasks.size()>0">
        AND B.WIP_ENTITY_NAME IN
        <foreach collection="tasks" item="task" open=" (" separator=" UNION ALL " close=")">
          SELECT #{task, jdbcType=VARCHAR} FROM DUAL
        </foreach>
      </when>
      <otherwise>AND 1=2 </otherwise>
    </choose>
  </select>

  <select id="getErpStatus" resultType="Map">
    SELECT MEANING statusName,
    LOOKUP_CODE statusCode
    FROM FND_LOOKUP_VALUES_VL
    WHERE LOOKUP_TYPE = 'WIP_JOB_STATUS'
  </select>

    <select id="getBomVerByTaskNo" resultType="com.zte.interfaces.dto.PDMProductMaterialResultDTO"
            parameterType="com.zte.interfaces.dto.PDMProductMaterialResultDTO">
        select j.bom_revision bomRevision,t.wip_entity_name entityName from apps.wip_entities t ,apps.wip_discrete_jobs
        j where t.wip_entity_id = j.wip_entity_id
        and t.wip_entity_name in
        <foreach collection="entityNameList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


    <select id="getTaskNoStatusByErp" parameterType="com.zte.interfaces.dto.WipEntitiesDTO" resultMap="BaseResultDTOMap">
        <foreach collection="list"  item="item" separator ="UNION ALL">
        SELECT we.wip_entity_name,
        wdj.status_type,
        lu.MEANING
        FROM APPS.WIP_ENTITIES WE
        ,apps.wip_discrete_jobs wdj
        ,apps.fnd_lookup_values_vl lu
        WHERE WE.WIP_ENTITY_NAME=#{item.taskNo, jdbcType=VARCHAR}
        AND WE.ORGANIZATION_ID=#{item.orgId, jdbcType=DECIMAL}
        and wdj.wip_entity_id=we.Wip_Entity_Id
        and lu.lookup_type = 'WIP_JOB_STATUS'
        and to_number(lu.LOOKUP_CODE)=wdj.status_type
        </foreach>
    </select>

</mapper>
