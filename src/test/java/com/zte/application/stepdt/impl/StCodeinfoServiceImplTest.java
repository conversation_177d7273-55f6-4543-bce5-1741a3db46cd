package com.zte.application.stepdt.impl;

import com.zte.domain.model.stepdt.StCodeinfoRepository;
import com.zte.domain.model.stepdt.SyssubBomInfoRepository;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;

import static org.junit.Assert.*;
@RunWith(PowerMockRunner.class)
public class StCodeinfoServiceImplTest {
    @Mock
    private StCodeinfoRepository stCodeinfoRepository;
    @InjectMocks
    private StCodeinfoServiceImpl stCodeinfoService;
    @Test
    public void getCodeByBraidDirection() {
        PowerMockito.when(stCodeinfoRepository.getCodeByBraidDirection()).thenReturn(new ArrayList<>());
        Assert.assertNotNull(stCodeinfoService.getCodeByBraidDirection());
    }

    @Test
    public void getOrgAndFacRel() {
        PowerMockito.when(stCodeinfoRepository.getOrgAndFacRel()).thenReturn(new ArrayList<>());
        assertNotNull(stCodeinfoService.getOrgAndFacRel());
    }
}