/*Started by AICoder, pid:v951dqeacaha411142230abab19ba3520721ac95*/
package com.zte.application.datawb.impl;

import com.alibaba.fastjson.JSON;
import com.zte.application.datawb.WsmAssembleLinesService;
import com.zte.application.datawb.ZmsOverallUnitService;
import com.zte.common.CommonUtils;
import com.zte.common.utils.DateUtil;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.interfaces.dto.CpmConfigItemAssembleDTO;
import com.zte.interfaces.dto.CustomerItemsDTO;
import com.zte.interfaces.dto.WsmAssembleLinesEntityDTO;
import com.zte.interfaces.dto.ZmsByteDanceCCEHeadDTO;
import com.zte.springbootframe.util.json.JacksonJsonConverUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;

@RunWith(PowerMockRunner.class)
@PrepareForTest({WsmAssembleLinesService.class, JacksonJsonConverUtil.class, ZmsOverallUnitService.class,
        JSON.class, CommonUtils.class, DateUtil.class})
@PowerMockIgnore({"javax.net.ssl.*", "javax.security.*"})
public class ZmsByteDanceCCEServiceImplGetProdOrderComponentsTest {

    @InjectMocks
    private ZmsByteDanceCCEServiceImpl zmsByteDanceCCEService;

    @Mock
    private WsmAssembleLinesService wsmAssembleLinesService;

    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Mock
    private ZmsOverallUnitService zmsOverallUnitService;

    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);
    }

    @Test(timeout = 8000)
    public void testGetProdOrderComponents_EmptyConfigDetailDTOList() throws Exception {
        ZmsByteDanceCCEHeadDTO zmsByteDanceCCEHeadDTO = new ZmsByteDanceCCEHeadDTO();
        zmsByteDanceCCEHeadDTO.setEntityId("1");
        List<CpmConfigItemAssembleDTO> configDetailDTOList = new ArrayList<>();
        PowerMockito.when(wsmAssembleLinesService.getAssemblyMaterialsByEntityId(anyInt()))
                .thenReturn(configDetailDTOList);

        assertNull(zmsByteDanceCCEService.getProdOrderComponents(zmsByteDanceCCEHeadDTO));
    }

    @Test(timeout = 8000)
    public void testGetProdOrderComponents_EmptyCustomerItemsDTOList() throws Exception {
        ZmsByteDanceCCEHeadDTO zmsByteDanceCCEHeadDTO = new ZmsByteDanceCCEHeadDTO();
        zmsByteDanceCCEHeadDTO.setUserAddress("address");
        zmsByteDanceCCEHeadDTO.setEntityId("111");
        List<CpmConfigItemAssembleDTO> configDetailDTOList = new ArrayList<>();
        CpmConfigItemAssembleDTO cpmConfigItemAssembleDTO = new CpmConfigItemAssembleDTO();
        cpmConfigItemAssembleDTO.setItemCode("code");
        configDetailDTOList.add(cpmConfigItemAssembleDTO);
        PowerMockito.when(wsmAssembleLinesService.getAssemblyMaterialsByEntityId(anyInt()))
                .thenReturn(configDetailDTOList);
        PowerMockito.when(centerfactoryRemoteService.getCustomerItemsInfo(anyString(), anyList()))
                .thenReturn(new ArrayList<>());

        assertNull(zmsByteDanceCCEService.getProdOrderComponents(zmsByteDanceCCEHeadDTO));
    }

    @Test(timeout = 8000)
    public void testGetProdOrderComponents_MultipleCompleteMachineList() throws Exception {
        ZmsByteDanceCCEHeadDTO zmsByteDanceCCEHeadDTO = new ZmsByteDanceCCEHeadDTO();
        zmsByteDanceCCEHeadDTO.setUserAddress("address");
        zmsByteDanceCCEHeadDTO.setEntityId("111");
        List<CpmConfigItemAssembleDTO> configDetailDTOList = new ArrayList<>();
        CpmConfigItemAssembleDTO cpmConfigItemAssembleDTO = new CpmConfigItemAssembleDTO();
        cpmConfigItemAssembleDTO.setItemCode("code");
        configDetailDTOList.add(cpmConfigItemAssembleDTO);
        PowerMockito.when(wsmAssembleLinesService.getAssemblyMaterialsByEntityId(anyInt()))
                .thenReturn(configDetailDTOList);
        List<CustomerItemsDTO> customerItemsDTOList = new ArrayList<>();
        CustomerItemsDTO customerItemsDTO3 = new CustomerItemsDTO();
        customerItemsDTO3.setZteCode("code");
        customerItemsDTO3.setProjectType("2");
        customerItemsDTOList.add(customerItemsDTO3);
        PowerMockito.when(centerfactoryRemoteService.getCustomerItemsInfo(anyString(), anyList()))
                .thenReturn(customerItemsDTOList);
        assertNull(zmsByteDanceCCEService.getProdOrderComponents(zmsByteDanceCCEHeadDTO));

        CustomerItemsDTO customerItemsDTO1 = new CustomerItemsDTO();
        customerItemsDTO1.setZteCode("code");
        customerItemsDTO1.setProjectType("3");
        customerItemsDTOList.add(customerItemsDTO1);
        CustomerItemsDTO customerItemsDTO2 = new CustomerItemsDTO();
        customerItemsDTO2.setZteCode("code");
        customerItemsDTO2.setProjectType("3");
        customerItemsDTOList.add(customerItemsDTO2);
        PowerMockito.when(centerfactoryRemoteService.getCustomerItemsInfo(anyString(), anyList()))
                .thenReturn(customerItemsDTOList);
        assertNull(zmsByteDanceCCEService.getProdOrderComponents(zmsByteDanceCCEHeadDTO));
    }

    @Test(timeout = 8000)
    public void testGetProdOrderComponents_NullCpmConfigItemAssembleDTO() throws Exception {
        ZmsByteDanceCCEHeadDTO zmsByteDanceCCEHeadDTO = new ZmsByteDanceCCEHeadDTO();
        zmsByteDanceCCEHeadDTO.setUserAddress("address");
        zmsByteDanceCCEHeadDTO.setEntityId("111");
        List<CpmConfigItemAssembleDTO> configDetailDTOList = new ArrayList<>();
        CpmConfigItemAssembleDTO cpmConfigItemAssembleDTO = new CpmConfigItemAssembleDTO();
        cpmConfigItemAssembleDTO.setItemCode("code");
        configDetailDTOList.add(cpmConfigItemAssembleDTO);
        PowerMockito.when(wsmAssembleLinesService.getAssemblyMaterialsByEntityId(anyInt()))
                .thenReturn(configDetailDTOList);
        List<CustomerItemsDTO> customerItemsDTOList = new ArrayList<>();
        CustomerItemsDTO customerItemsDTO = new CustomerItemsDTO();
        customerItemsDTO.setZteCode("code1");
        customerItemsDTO.setProjectType("3");
        customerItemsDTOList.add(customerItemsDTO);
        PowerMockito.when(centerfactoryRemoteService.getCustomerItemsInfo(anyString(), anyList()))
                .thenReturn(customerItemsDTOList);
        PowerMockito.when(wsmAssembleLinesService.getAssemblyMaterials(anyString())).thenReturn(new ArrayList<>());

        assertNull(zmsByteDanceCCEService.getProdOrderComponents(zmsByteDanceCCEHeadDTO));

        customerItemsDTOList.clear();
        CustomerItemsDTO customerItemsDTO1 = new CustomerItemsDTO();
        customerItemsDTO1.setZteCode("code");
        customerItemsDTO1.setProjectType("3");
        customerItemsDTOList.add(customerItemsDTO1);
        PowerMockito.when(centerfactoryRemoteService.getCustomerItemsInfo(anyString(), anyList()))
                .thenReturn(customerItemsDTOList);
        PowerMockito.when(wsmAssembleLinesService.getAssemblyMaterials(anyString())).thenReturn(new ArrayList<>());

        assertNull(zmsByteDanceCCEService.getProdOrderComponents(zmsByteDanceCCEHeadDTO));
    }

    @Test(timeout = 8000)
    public void testGetProdOrderComponents_EmptyWsmAssembleLinesList() throws Exception {
        ZmsByteDanceCCEHeadDTO zmsByteDanceCCEHeadDTO = new ZmsByteDanceCCEHeadDTO();
        zmsByteDanceCCEHeadDTO.setUserAddress("address");
        zmsByteDanceCCEHeadDTO.setEntityId("111");
        List<CpmConfigItemAssembleDTO> configDetailDTOList = new ArrayList<>();
        CpmConfigItemAssembleDTO cpmConfigItemAssembleDTO = new CpmConfigItemAssembleDTO();
        cpmConfigItemAssembleDTO.setItemCode("code");
        cpmConfigItemAssembleDTO.setItemBarcode("barcode");
        cpmConfigItemAssembleDTO.setBarcodeQty("20");
        configDetailDTOList.add(cpmConfigItemAssembleDTO);
        CpmConfigItemAssembleDTO cpmConfigItemAssembleDTO1 = new CpmConfigItemAssembleDTO();
        cpmConfigItemAssembleDTO1.setItemCode("code1");
        cpmConfigItemAssembleDTO1.setItemBarcode("barcode1");
        cpmConfigItemAssembleDTO1.setBarcodeQty("30");
        configDetailDTOList.add(cpmConfigItemAssembleDTO1);
        CpmConfigItemAssembleDTO cpmConfigItemAssembleDTO2 = new CpmConfigItemAssembleDTO();
        cpmConfigItemAssembleDTO2.setItemCode("code2");
        cpmConfigItemAssembleDTO2.setItemBarcode("barcode2");
        cpmConfigItemAssembleDTO2.setBarcodeQty("50");
        configDetailDTOList.add(cpmConfigItemAssembleDTO2);
        PowerMockito.when(wsmAssembleLinesService.getAssemblyMaterialsByEntityId(anyInt()))
                .thenReturn(configDetailDTOList);
        List<CustomerItemsDTO> customerItemsDTOList = new ArrayList<>();
        CustomerItemsDTO customerItemsDTO = new CustomerItemsDTO();
        customerItemsDTO.setZteCode("code");
        customerItemsDTO.setProjectType("2");
        customerItemsDTOList.add(customerItemsDTO);
        /* Started by AICoder, pid:p263cv1b8cq2ddf142a70b75b005ab0da31865be */
        CustomerItemsDTO customerItemsDTO2 = new CustomerItemsDTO();
        customerItemsDTO2.setZteCode("code2");
        customerItemsDTO2.setProjectType("1");
        customerItemsDTO2.setCustomerCode("customer2");
        customerItemsDTOList.add(customerItemsDTO2);
        CustomerItemsDTO customerItemsDTO1 = new CustomerItemsDTO();
        customerItemsDTO1.setZteCode("code1");
        customerItemsDTO1.setProjectType("3");
        customerItemsDTO1.setCustomerCode("customer1");
        customerItemsDTO1.setCustomerItemName("item1");
        customerItemsDTOList.add(customerItemsDTO1);
        /* Ended by AICoder, pid:p263cv1b8cq2ddf142a70b75b005ab0da31865be */
        PowerMockito.when(centerfactoryRemoteService.getCustomerItemsInfo(anyString(), anyList()))
                .thenReturn(customerItemsDTOList);
        PowerMockito.when(wsmAssembleLinesService.getAssemblyMaterials(anyString())).thenReturn(new ArrayList<>());
        List<WsmAssembleLinesEntityDTO> wsmAssembleLinesList = new ArrayList<>();
        WsmAssembleLinesEntityDTO wsmAssembleLinesEntityDTO1 = new WsmAssembleLinesEntityDTO();
        wsmAssembleLinesEntityDTO1.setItemCode("code");
        wsmAssembleLinesEntityDTO1.setItemQty(10);
        wsmAssembleLinesList.add(wsmAssembleLinesEntityDTO1);
        PowerMockito.when(wsmAssembleLinesService.getAssemblyMaterials(anyString())).thenReturn(wsmAssembleLinesList);
        PowerMockito.when(wsmAssembleLinesService.getAssemblyMaterialList(anyList())).thenReturn(null);

        assertNotNull(zmsByteDanceCCEService.getProdOrderComponents(zmsByteDanceCCEHeadDTO));
    }
}
/*Ended by AICoder, pid:v951dqeacaha411142230abab19ba3520721ac95*/