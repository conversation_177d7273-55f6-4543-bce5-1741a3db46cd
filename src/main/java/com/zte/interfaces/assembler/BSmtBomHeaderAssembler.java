/**
 * 项目名称 : ${project_name}
 * 创建日期 : ${date}
 * 修改历史 :
 *   1. [${date}] 创建文件 by ${user}
 **/
package com.zte.interfaces.assembler;

import com.zte.domain.model.BSmtBomHeader;
import com.zte.interfaces.dto.BSmtBomHeaderDTO;
import java.util.ArrayList;
import java.util.List;
import org.springframework.beans.BeanUtils;

/**
 * // TODO 添加类/接口功能描述
 * 
 * <AUTHOR>
 **/
public class BSmtBomHeaderAssembler {

    /**
     * // TODO 添加方法功能描述
     * @param entity
     * @return BSmtBomHeaderDTO
     **/
    public static BSmtBomHeaderDTO toDTO(BSmtBomHeader entity) {
        BSmtBomHeaderDTO dto = new BSmtBomHeaderDTO();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }

    /**
     * // TODO 添加方法功能描述
     * @param dto
     * @return BSmtBomHeader
     **/
    public static BSmtBomHeader toEntity(BSmtBomHeaderDTO dto) {
        BSmtBomHeader entity = new BSmtBomHeader();
        BeanUtils.copyProperties(dto, entity);
        return entity;
    }

    /**
     * // TODO 添加方法功能描述
     * @param entityList
     * @return List<BSmtBomHeaderDTO>
     **/
    public static java.util.List<BSmtBomHeaderDTO> toBSmtBomHeaderDTOList(java.util.List<BSmtBomHeader> entityList) {
        List<BSmtBomHeaderDTO> dtoList = new ArrayList<BSmtBomHeaderDTO>();
        for (BSmtBomHeader entity : entityList) { 
        	dtoList.add(toDTO(entity));
    }
    return dtoList;
}

    /**
     * // TODO 添加方法功能描述
     * @param dtoList
     * @return List<BSmtBomHeader>
     **/
    public static java.util.List<BSmtBomHeader> toBSmtBomHeaderList(java.util.List<BSmtBomHeaderDTO> dtoList) {
        List<BSmtBomHeader> entityList = new ArrayList<BSmtBomHeader>();
        for (BSmtBomHeaderDTO dto : dtoList) { 
        	entityList.add(toEntity(dto));
    }
    return entityList;
}
}