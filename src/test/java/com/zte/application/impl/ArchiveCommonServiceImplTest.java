package com.zte.application.impl;

import com.zte.application.MesGetDictInforService;
import com.zte.common.config.ArchiveFileProperties;
import com.zte.common.utils.ArchiveConstant;
import com.zte.common.utils.SftpConfig;
import com.zte.domain.model.ArchiveBillFileRelation;
import com.zte.domain.model.ArchiveBillFileRelationRepository;
import com.zte.domain.model.ArchiveSendLogRepository;
import com.zte.domain.model.ArchiveTaskSend;
import com.zte.infrastructure.feign.ArchiveClient;
import com.zte.interfaces.dto.ArchiveIpqcDTO;
import com.zte.interfaces.dto.ArchiveReq;
import com.zte.km.udm.exception.RouteException;
import com.zte.springbootframe.util.CloudDiskHelper;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.util.FileSystemUtils;

import java.io.File;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;

/**
 * <AUTHOR>
 * @since 2023/4/11
 */
public class ArchiveCommonServiceImplTest extends BaseTestCase {

    @InjectMocks
    private ArchiveCommonServiceImpl archiveCommonService;

    @Mock
    private CloudDiskHelper cloudDiskHelper;

    @Mock
    private ArchiveClient archiveClient ;

    @Mock
    private ArchiveBillFileRelationRepository archiveBillFileRelationRepository;

    @Mock
    private ArchiveSendLogRepository archiveSendLogRepository;

    @Mock
    ArchiveFileProperties archiveFileProperties;

    @Mock
    private ArchiveSendLogRepository sendArchiveLogRepository;

    @Mock
    private MesGetDictInforService mesGetDictInforService;

    @Mock
    private SftpConfig sftpConfig;


    @Test(expected = RouteException.class)
    public void uploadFileTo1(){
        ArchiveTaskSend taskSendArchive = new ArchiveTaskSend();
        taskSendArchive.setTaskId("1");
        taskSendArchive.setBillNo("001");
        String fileName="IPQC确认单";
        archiveCommonService.uploadFileTo(fileName,taskSendArchive,"001");
    }


    @Test
    public void createExcelAndUpload1(){
        try {
            ArchiveTaskSend taskSendArchive = new ArchiveTaskSend();
            taskSendArchive.setTaskId("1");
            taskSendArchive.setBillNo("001");
            uploadFileToHasFileKey();
            ArchiveIpqcDTO dto = new ArchiveIpqcDTO();
            archiveCommonService.createExcelAndUpload(taskSendArchive,"IPQC确认单", ArchiveConstant.IPQC_SHEET_HEAD_MAP,Collections.singletonList(dto));
        }catch (Exception e){
            e.printStackTrace();
            Assert.assertEquals(e.getMessage(), e.getMessage());
        }
    }


    @Test(expected = RouteException.class)
    public void createExcelAndUpload2() throws Exception {
            ArchiveTaskSend taskSendArchive = new ArchiveTaskSend();
            taskSendArchive.setTaskId("1");
            taskSendArchive.setBillNo("001");
            uploadFileToHasNotFileKey();
            ArchiveIpqcDTO dto = new ArchiveIpqcDTO();
            Assert.assertNotNull(archiveCommonService.createExcelAndUpload(taskSendArchive,"IPQC确认单", ArchiveConstant.IPQC_SHEET_HEAD_MAP,Collections.singletonList(dto)));
    }


    @Test
    public void createExcelAndUpload21(){
        try {
            ArchiveTaskSend taskSendArchive = new ArchiveTaskSend();
            taskSendArchive.setTaskId("1");
            taskSendArchive.setBillNo("001");
            uploadFileToHasFileKey();
            ArchiveIpqcDTO dto = new ArchiveIpqcDTO();

            LinkedHashMap<String, LinkedHashMap<String, String>> sheetHeadMap=new LinkedHashMap<>();

            LinkedHashMap<String, List> dataListMap=new LinkedHashMap<>();
            dataListMap.put("ipqc确认", Collections.singletonList(dto));
            archiveCommonService.createExcelAndUpload(taskSendArchive,"IPQC确认单", sheetHeadMap,dataListMap);
        }catch (Exception e){
            e.printStackTrace();
            Assert.assertEquals(e.getMessage(), e.getMessage());
        }
    }

    @Test(expected = RouteException.class)
    public void createExcelAndUpload22() throws Exception {

            ArchiveTaskSend taskSendArchive = new ArchiveTaskSend();
            taskSendArchive.setTaskId("1");
            taskSendArchive.setBillNo("001");
            uploadFileToHasNotFileKey();
            ArchiveIpqcDTO dto = new ArchiveIpqcDTO();

            LinkedHashMap<String, LinkedHashMap<String, String>> sheetHeadMap=new LinkedHashMap<>();
            sheetHeadMap.put("ipqc确认",ArchiveConstant.IPQC_SHEET_HEAD_MAP);

            LinkedHashMap<String, List> dataListMap=new LinkedHashMap<>();
            dataListMap.put("ipqc确认", Collections.singletonList(dto));
            archiveCommonService.createExcelAndUpload(taskSendArchive,"IPQC确认单", sheetHeadMap,dataListMap);
    }

    @Test(expected = RouteException.class)
    public void createExcelAndUpload33() throws Exception {

        ArchiveTaskSend taskSendArchive = new ArchiveTaskSend();
        taskSendArchive.setTaskId("1");
        taskSendArchive.setBillNo("001");
        uploadFileToHasNotFileKey();
        ArchiveIpqcDTO dto = new ArchiveIpqcDTO();

        LinkedHashMap<String, LinkedHashMap<String, String>> sheetHeadMap=new LinkedHashMap<>();
        sheetHeadMap.put("ipqc确认",null);

        LinkedHashMap<String, List> dataListMap=new LinkedHashMap<>();
        dataListMap.put("ipqc确认", Collections.singletonList(dto));
        archiveCommonService.createExcelAndUpload(taskSendArchive,"IPQC确认单", sheetHeadMap,dataListMap);
    }

    @Test(expected = RouteException.class)
    public void createExcelAndUpload44() throws Exception {
        ArchiveTaskSend taskSendArchive = new ArchiveTaskSend();
        taskSendArchive.setTaskId("1");
        taskSendArchive.setBillNo("001");
        uploadFileToHasNotFileKey();
        ArchiveIpqcDTO dto = new ArchiveIpqcDTO();

        LinkedHashMap<String, LinkedHashMap<String, String>> sheetHeadMap=new LinkedHashMap<>();
        sheetHeadMap.put("ipqc确认",ArchiveConstant.IPQC_SHEET_HEAD_MAP);

        LinkedHashMap<String, List> dataListMap=new LinkedHashMap<>();
        dataListMap.put("ipqc确认", null);
        archiveCommonService.createExcelAndUpload(taskSendArchive,"IPQC确认单", sheetHeadMap,dataListMap);
    }

    @Test(expected = RouteException.class)
    public void createExcelAndUpload55() throws Exception {
        ArchiveTaskSend taskSendArchive = new ArchiveTaskSend();
        taskSendArchive.setTaskId("1");
        taskSendArchive.setBillNo("001");
        uploadFileToHasNotFileKey();
        ArchiveIpqcDTO dto = new ArchiveIpqcDTO();

        LinkedHashMap<String, LinkedHashMap<String, String>> sheetHeadMap=new LinkedHashMap<>();
        sheetHeadMap.put("ipqc确认",ArchiveConstant.IPQC_SHEET_HEAD_MAP);

        LinkedHashMap<String, List> dataListMap=new LinkedHashMap<>();
        dataListMap.put("ipqc确认", null);
        archiveCommonService.createExcelAndUpload(taskSendArchive,"IPQC确认单", null,dataListMap);
    }

    private void uploadFileToHasFileKey() throws Exception {
        String fileKey="123456";
        Mockito.when(cloudDiskHelper.fileUpload(Mockito.anyString(),Mockito.any(),Mockito.anyInt())).thenReturn(fileKey);
        Mockito.doNothing().when(archiveBillFileRelationRepository).insert(Mockito.any(ArchiveBillFileRelation.class));
    }

    private void uploadFileToHasNotFileKey() throws Exception {
        String fileKey="";
        Mockito.when(cloudDiskHelper.fileUpload(Mockito.anyString(),Mockito.any(),Mockito.anyInt())).thenReturn(fileKey);
       // Mockito.doNothing().when(archiveBillFileRelationRepository).insert(Mockito.any(ArchiveBillFileRelation.class));
    }

    @Test
    public void sendDataToArchive1(){
        try {
            ArchiveReq.ArchiveItem archiveItem = new ArchiveReq.ArchiveItem();
            ArchiveTaskSend taskSend = new ArchiveTaskSend();
            insertSendArchiveLog();
            archiveCommonService.sendDataToArchive(archiveItem,taskSend);
        } catch (Exception e) {
            e.printStackTrace();
            Assert.assertEquals(e.getMessage(), e.getMessage());
        }
    }
    @Test(expected = RouteException.class)
    public void sendDataToArchive2(){
        ArchiveReq.ArchiveItem archiveItem = new ArchiveReq.ArchiveItem();
        ArchiveTaskSend taskSend = new ArchiveTaskSend();
        sendDataToArchiveEx();
        archiveCommonService.sendDataToArchive(archiveItem,taskSend);
    }

    @Test
    public void sendDataToArchive3(){
        ArchiveReq.ArchiveItem archiveItem = new ArchiveReq.ArchiveItem();
        ArchiveTaskSend taskSend = new ArchiveTaskSend();
        Mockito.when(archiveFileProperties.getUrl()).thenReturn(null);
        insertSendArchiveLog();
        Assert.assertNull(archiveCommonService.sendDataToArchive(archiveItem,taskSend));
    }

    private void sendDataToArchiveEx(){
        Mockito.when(archiveClient.archiveFile(Mockito.any())).thenThrow(new RouteException(""));
    }
    private void insertSendArchiveLog(){
        Mockito.doNothing().when(sendArchiveLogRepository).insert(any());
    }

    @Test
    public void lockTrue(){
        Mockito.when(mesGetDictInforService.updateArchiveFlag(Mockito.anyMap())).thenReturn(1);
        Assert.assertNotNull(archiveCommonService.lock("123"));
        Assert.assertNotNull(archiveCommonService.unlock("123"));
    }

    @Test
    public void lockFalse(){
        Mockito.when(mesGetDictInforService.updateArchiveFlag(Mockito.anyMap())).thenReturn(0);
        Assert.assertNotNull(archiveCommonService.lock("123"));
        Assert.assertNotNull(archiveCommonService.unlock("123"));
    }

    @Test
    public void getDescriptionNotNull(){
        Mockito.when(mesGetDictInforService.getDicDescription(Mockito.anyString())).thenReturn("10");
        Assert.assertNotNull(archiveCommonService.getDicDescriptionByCode("123"));
    }
    @Test
    public void getDescriptionNull(){
        Mockito.when(mesGetDictInforService.getDicDescription(Mockito.anyString())).thenReturn(null);
        Assert.assertNull(archiveCommonService.getDicDescriptionByCode("123"));
    }

    @Test
    public void downloadFromFtpAndUploadDirNotExist(){
        ArchiveTaskSend archiveTaskSend = new ArchiveTaskSend();
        archiveTaskSend.setTaskId("1");
        archiveTaskSend.setTaskType("18");
        archiveTaskSend.setBillId("262");
        archiveTaskSend.setBillNo("B20160415143561");
//        Sftp sftp = createSftp();
//        Mockito.when(sftpConfig.createSftp()).thenReturn(sftp);
        Mockito.when(sftpConfig.createSftp()).thenReturn(null);
        try {
            uploadFileToHasFileKey();
            deleteDir();
            archiveCommonService.downloadFromFtpAndUpload(archiveTaskSend,"MF910_MF910_南非MTN_白色_整机代码126682301096_机头代码126682311065 制造通知单 (A)版本.xlsx","/home/<USER>/mes_upload/ManuNotice/MF910/126682301096/manuNoticeFile/MF910_MF910_南非MTN_白色_整机代码126682301096_机头代码126682311065 制造通知单 (A)版本.xlsx");
        } catch (Exception e) {
            e.printStackTrace();
            Assert.assertEquals(e.getMessage(),e.getMessage());
        }
    }

    @Test
    public void downloadFromFtpAndUploadDirExist(){
        ArchiveTaskSend archiveTaskSend = new ArchiveTaskSend();
        archiveTaskSend.setTaskId("1");
        archiveTaskSend.setTaskType("18");
        archiveTaskSend.setBillId("262");
        archiveTaskSend.setBillNo("B20160415143561");
//        Sftp sftp = createSftp();
//        Mockito.when(sftpConfig.createSftp()).thenReturn(sftp);
        Mockito.when(sftpConfig.createSftp()).thenReturn(null);
        try {
            uploadFileToHasFileKey();
            archiveCommonService.downloadFromFtpAndUpload(archiveTaskSend,
"EE20151103100841.xlsx",
 "/home/<USER>/mes_upload/csgy/P816A30/129188551000/C/EE20151103100841.xlsx");
        } catch (Exception e) {
            e.printStackTrace();
            Assert.assertEquals(e.getMessage(),e.getMessage());
        }
    }



    @Test
    public void downloadFromFtpAndUploadFail(){
        ArchiveTaskSend archiveTaskSend = new ArchiveTaskSend();
        archiveTaskSend.setTaskId("1");
        archiveTaskSend.setTaskType("18");
        archiveTaskSend.setBillId("262");
        archiveTaskSend.setBillNo("B20160415143561");
//        Sftp sftp = createSftp();
//        Mockito.when(sftpConfig.createSftp()).thenReturn(sftp);
        Mockito.when(sftpConfig.createSftp()).thenReturn(null);
        try {
            uploadFileToHasFileKey();
            archiveCommonService.downloadFromFtpAndUpload(archiveTaskSend,"","/home/<USER>/mes_upload/ManuNotice/MF910/126682301096/manuNoticeFile/MF910_MF910_南非MTN_白色_整机代码126682301096_机头代码126682311065 制造通知单 (A)版本.xlsx");
        } catch (Exception e) {
            e.printStackTrace();
            Assert.assertEquals(e.getMessage(),e.getMessage());
        }
    }

    @Test
    public void downloadFromFtpAndUploadFileNotFound(){
        ArchiveTaskSend archiveTaskSend = new ArchiveTaskSend();
        archiveTaskSend.setTaskId("1");
        archiveTaskSend.setTaskType("18");
        archiveTaskSend.setBillId("262");
        archiveTaskSend.setBillNo("B20160415143561");
//        Sftp sftp = createSftp();
//        Mockito.when(sftpConfig.createSftp()).thenReturn(sftp);
        Mockito.when(sftpConfig.createSftp()).thenReturn(null);

        try {
            uploadFileToHasFileKey();
            archiveCommonService.downloadFromFtpAndUpload(archiveTaskSend,"MF910_MF910_南非MTN_白色_整机代码126682301096_机头代码126682311065 制造通知单 (A)版本.xlsx","/mes_upload/ManuNotice/MF910/126682301096/manuNoticeFile/MF910_MF910_南非MTN_白色_整机代码126682301096_机头代码126682311065 制造通知单 (A)版本.xlsx");
        } catch (Exception e) {
            e.printStackTrace();
            Assert.assertEquals(e.getMessage(),e.getMessage());
        }
    }



    public void deleteDir(){
        try {
            File root = new File(ArchiveConstant.TEMP_FILE_PATH);
            FileSystemUtils.deleteRecursively(root);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }


}
