<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.datawb.CurbNameTagsPrintRepository">

    <resultMap type="com.zte.interfaces.dto.BoxNameDTO" id="boxNameSelect">
        <result property="packageSn" jdbcType="VARCHAR" column="PACKAGE_SN" />
        <result property="productionDate" jdbcType="VARCHAR" column="PRODUCTION_DATE" />
        <result property="selfhelpFlag" jdbcType="VARCHAR" column="SELFHELP_FlAG" />
        <result property="site" jdbcType="VARCHAR" column="SITE" />
        <result property="fiveGradeSite" jdbcType="VARCHAR" column="FIVE_GRADE_SITE" />
        <result property="clientName" jdbcType="VARCHAR" column="CLIENT_NAME" />
        <result property="siteEquipment" jdbcType="VARCHAR" column="SITE_EQUIPMENT" />
        <result property="contractNo" jdbcType="VARCHAR" column="CONTRACT_NO" />
        <result property="mfgNo" jdbcType="VARCHAR" column="MFG_NO" />
        <result property="siteCode" jdbcType="VARCHAR" column="SITE_CODE" />
        <result property="materialClass" jdbcType="VARCHAR" column="MATERIAL_CLASS" />
        <result property="poNo" jdbcType="VARCHAR" column="PO_NO" />
        <result property="projectName" jdbcType="VARCHAR" column="PROJECT_NAME" />
        <result property="projectDeliver" jdbcType="VARCHAR" column="PROJECT_DELIVER" />
        <result property="modelSpeck" jdbcType="VARCHAR" column="MODEL_SPEC" />
        <result property="isTally" jdbcType="VARCHAR" column="IS_TALLY" />
        <result property="tempLamguage" jdbcType="VARCHAR" column="TEMP_LANGUAGE" />
        <result property="billFlag" jdbcType="VARCHAR" column="MERGE_BILL_TYPE" />
        <result property="pasteLabel" jdbcType="VARCHAR" column="PASTE_LABEL" />
        <result property="euNo" jdbcType="VARCHAR" column="EU_NO" />
        <result property="euDesc" jdbcType="VARCHAR" column="EU_DESC" />
        <result property="euDescEn" jdbcType="VARCHAR" column="EU_DESC_EN" />
        <result property="euUnit" jdbcType="VARCHAR" column="EU_UNIT" />
        <result property="euUnitEn" jdbcType="VARCHAR" column="EU_UNIT_EN" />
        <result property="euQty" jdbcType="INTEGER" column="EU_QTY" />
        <result property="singleEuboxNum" jdbcType="INTEGER" column="SINGLE_EUBOX_NUM" />
        <result property="euPosition" jdbcType="INTEGER" column="EU_POSITION" />

        <result property="packageDesc" jdbcType="VARCHAR" column="PACKAGE_DESC" />
        <result property="boqFlag" jdbcType="VARCHAR" column="BOQ_FLAG" />

        <result property="batteryLabeltype" jdbcType="VARCHAR" column="BATTERY_LABELTYPE" />
        <result property="boxDangerGoodsType" jdbcType="VARCHAR" column="BOX_DANGERGOODSTYPE" />
        <result property="boxIsDanger" jdbcType="VARCHAR" column="BOX_IS_DANGER" />

        <result property="boxNumber" jdbcType="INTEGER" column="BOX_NUMBER" />
        <result property="totalBoxes" jdbcType="INTEGER" column="TOTAL_BOXES" />
        <result property="customerPo" jdbcType="VARCHAR" column="CUSTOMER_PO" />
        <result property="projectNo" jdbcType="VARCHAR" column="PROJECT_NUMBER" />
        <result property="projectNameTrim" jdbcType="VARCHAR" column="PROJECT_NAMETRIM" />

    </resultMap>

    <resultMap type="com.zte.interfaces.dto.BoqBomDTO" id="boqBomSelect">
        <result property="clientMaterialCode" jdbcType="VARCHAR" column="CLIENT_MATERIAL_CODE" />
        <result property="materialCode" jdbcType="VARCHAR" column="MATERIAL_CODE" />
        <result property="codeName" jdbcType="VARCHAR" column="CODE_NAME" />
        <result property="materialName" jdbcType="VARCHAR" column="MATERIAL_NAME" />
        <result property="materialBarcode" jdbcType="VARCHAR" column="MATERIAL_BARCODE" />
        <result property="unit" jdbcType="VARCHAR" column="UNIT" />
        <result property="materialQty" jdbcType="INTEGER" column="MATERIAL_QTY" />
        <result property="suCode" jdbcType="VARCHAR" column="SU_CODE" />
        <result property="suName" jdbcType="VARCHAR" column="SU_NAME" />
        <result property="gBomSuItemQty" jdbcType="INTEGER" column="GBOM_SU_ITEM_QTY" />
        <result property="suQty" jdbcType="INTEGER" column="SU_QTY" />
        <result property="cBomCode" jdbcType="VARCHAR" column="CBOM_CODE" />
        <result property="cBomName" jdbcType="VARCHAR" column="CBOM_NAME" />
        <result property="cBomUnitZh" jdbcType="VARCHAR" column="CBOM_UNIT_ZH" />
        <result property="cBomUnitEn" jdbcType="VARCHAR" column="CBOM_UNIT_EN" />
        <result property="cBomQty" jdbcType="INTEGER" column="CBOM_QTY" />
        <result property="suNameEn" jdbcType="VARCHAR" column="SU_NAME_EN" />
        <result property="cBomNameEn" jdbcType="VARCHAR" column="CBOM_NAME_EN" />
        <result property="suUnitZh" jdbcType="VARCHAR" column="SU_UNIT_ZH" />
        <result property="suUnitEn" jdbcType="VARCHAR" column="SU_UNIT_EN" />
        <result property="suNumber" jdbcType="VARCHAR" column="SU_NUMBER" />


        <result property="boqLevel" jdbcType="VARCHAR" column="BOQ_Level" />

    </resultMap>

    <resultMap id="CURBCountResultMap" type="java.lang.Long">
        <result property="resultCursor" jdbcType="VARCHAR" column="RES_COUNT"/>
    </resultMap>

    <select id="getConfigDetails" parameterType="java.util.Map" resultType="java.lang.String">
        SELECT  ITEMS.Config_Detail_Id
        FROM APP_MES.CPM_BOXUP_BILLS   BILLS,
        APP_MES.CPM_BOXUP_BILL_ITEMS   ITEMS
        WHERE ITEMS.BILL_ID = BILLS.BILL_ID
        AND ITEMS.ENABLED_FLAG = 'Y'
        <if test="billId != null">
            AND BILLS.BILL_ID =#{billId,jdbcType=VARCHAR}
        </if>
        <if test="billNo != null">
            AND BILLS.bill_number = #{billNo,jdbcType=VARCHAR}
        </if>
        <if test="billId == null and billNo == null">
            and 1=2
        </if>
        AND BILLS.bill_number not  like 'C%'
        UNION ALL
        SELECT  DETAIL.Config_Detail_Id
        FROM APP_MES.CPM_BOXUP_BILLS                  BILLS,
        APP_MES.CPM_BOXUP_BILL_ITEMS             ITEMS,
        APP_MES.CPM_BOXUP_BILL_ITEMS_DATAIL DETAIL
        WHERE DETAIL.BILL_ITEM_ID = ITEMS.BILL_ITEM_ID
        AND DETAIL.ENABLED_FLAG = 'Y'
        AND ITEMS.BILL_ID = BILLS.BILL_ID
        AND ITEMS.ENABLED_FLAG = 'Y'
        <if test="billId != null">
            AND BILLS.BILL_ID =#{billId,jdbcType=VARCHAR}
        </if>
        <if test="billNo != null">
            AND BILLS.bill_number = #{billNo,jdbcType=VARCHAR}
        </if>
        <if test="billId == null and billNo == null">
            and 1=2
        </if>
        AND BILLS.bill_number like 'C%'
    </select>


    <select id="querryBoxNameCount" parameterType="java.util.Map" resultType="Long">
        select count(distinct v.bill_number) FROM APP_MES.MES_CONTRACT_BOXUP_SHIPMENT_V V,
        APP_MES.CDM_BOXUP_ITEM_TYPE           CBIT,
        APP_MES.CDM_DELIVER_SETS              CDS,
        APP_MES.CDM_ENGINEER_DELIVER_SETS             CEDS,
        APP_MES.CDM_CONTRACT_PROJECT_SHIPMENTS        CCPS,
        APP_MES.MTL_SYSTEM_ITEMS               BB,
        APP_MES.ITEM_ATTACHED_INFORMATION      AA,
        APP_MES.CPM_INNER_ORDER_HEADERS        CION,
        APP_MES.CPM_CONTRACT_ENTITIES          CCE
        WHERE V.ENTITY_ID = CBIT.ENTITY_ID(+)
        AND V.INNER_ORDER_HEADER_ID = CION.INNER_ORDER_HEADER_ID(+)
        AND CION.DELIVER_SET_ID = CDS.DELIVER_SET_ID(+)
        AND CDS.PROJECT_SHIPMENT_ID = CCPS.PROJECT_SHIPMENT_ID(+)
        AND CCPS.ENGINEER_DELIVER_SET_ID = CEDS.ENGINEER_DELIVER_SET_ID(+)
        AND V.PTO_CODE = BB.SEGMENT1
        AND BB.INVENTORY_ITEM_ID = AA.INVENTORY_ITEM_ID(+)
        AND V.ENTITY_ID = CCE.ENTITY_ID
        AND V.ENABLED_FLAG = 'Y'
        AND CCE.ENABLED_FLAG = 'Y'
        <if test="billId != null">
            AND V.BILL_ID =#{billId,jdbcType=VARCHAR}
        </if>
        <if test="billNo != null">
            AND V.bill_number = #{billNo,jdbcType=VARCHAR}
        </if>
        <if test="billId == null and billNo == null">
            and 1=2
        </if>
    </select>

    <select id="boxNameSelect" parameterType="java.util.Map" resultMap="boxNameSelect">
        select distinct
        CASE WHEN BILL_NUMBER IS NULL THEN '' ELSE BILL_NUMBER END AS PACKAGE_SN,
        CASE WHEN PRODUCTION_DATE IS NULL THEN '' ELSE PRODUCTION_DATE END AS PRODUCTION_DATE,
        CASE WHEN SELFHELP_FlAG IS NULL THEN '' ELSE SELFHELP_FlAG END AS SELFHELP_FlAG,
        DECODE(TEMPLATE_LANGUAGE,'中文',decode(SHIPMENT_MODE,'按站点发货',SITE_ADDRESS1,SITE_ADDRESS3),decode(SHIPMENT_MODE,'按站点发货',SITE_ADDRESS2,SITE_ADDRESS4)) AS SITE,
        CASE WHEN DECODE(SHIPMENT_MODE,'按站点发货','',old_site_address) IS NULL THEN '' ELSE DECODE(SHIPMENT_MODE,'按站点发货','',old_site_address) END as FIVE_GRADE_SITE,
        CASE WHEN END_USER IS NULL THEN '' ELSE END_USER END AS CLIENT_NAME,
        CASE WHEN MFG_SITE_NAME IS NULL THEN '' ELSE MFG_SITE_NAME END AS SITE_EQUIPMENT,
        CASE WHEN EQUIPMENT_DESC IS NULL THEN '' ELSE EQUIPMENT_DESC END AS PACKAGE_DESC,
        CASE WHEN CONTRACT_NUMBER IS NULL THEN '' ELSE CONTRACT_NUMBER END AS CONTRACT_NO,
        CASE WHEN ENTITY_NAME IS NULL THEN '' ELSE ENTITY_NAME END AS MFG_NO,
        CASE WHEN SITE_NUMBER IS NULL THEN '' ELSE SITE_NUMBER END AS SITE_CODE,
        CASE WHEN BOXUP_ITEM_TYPE IS NULL THEN '' ELSE BOXUP_ITEM_TYPE END AS MATERIAL_CLASS,
        CASE WHEN USER_CON_NUMBER IS NULL THEN '' ELSE USER_CON_NUMBER END AS PO_NO,
        PROJECT_NAME,
        CASE WHEN PROJECT_NAME IS NULL THEN '' ELSE  SUBSTR(PROJECT_NAME, 1, INSTR(PROJECT_NAME, '(NO:') - 1)  END AS PROJECT_NAMETRIM,
        CASE WHEN ENGINEER_DELIVER_SET_NUM IS NULL THEN '' ELSE ENGINEER_DELIVER_SET_NUM END AS  PROJECT_DELIVER,
        MODEL AS MODEL_SPEC,
        'N' IS_TALLY,
        TEMPLATE_LANGUAGE AS TEMP_LANGUAGE,
        BOQ_FLAG,
        BATTERY_LABELTYPE,
        BOX_DANGERGOODSTYPE,
        BOX_IS_DANGER,
        decode(MERGE_BILL_FLAG,1,'合箱',2,'虚拟箱',3,'主箱',4,'辅箱','正常箱') MERGE_BILL_TYPE,
        PASTE_LABEL,
        EU_NO,
        EU_DESC,
        EU_DESC_EN,
        EU_UNIT,
        EU_UNIT_EN,
        EU_QTY,
        SINGLE_EUBOX_NUM,
        EU_POSITION,
        BOX_NUMBER,
        TOTAL_BOXES,
        BOX_TYPE_NAME,
        DELIVER_SET_NUM,
        PTO_NAME,
        DEVICES_CODE,
        CUSTOMER_PO,
        PROJECT_NUMBER
        from (
        SELECT
        V.BILL_NUMBER,
        TO_CHAR(V.CREATION_DATE, 'YYYY') || '/' ||
        TO_CHAR(V.CREATION_DATE, 'MM') PRODUCTION_DATE,
        CASE
        WHEN APP_MES.FIND_CONTRACT_SHIPMENT_METHOD(V.CONTRACT_HEADER_ID) = '自提' THEN '自提'
        WHEN INSTR(V.SHIP_TO_ADDRESS || V.CONTACT_NAME || V.CONTACT_PHONE, '自提') > 0 THEN '自提'
        ELSE ''
        END SELFHELP_FlAG ,
        V.NEW_SITE_ADDRESS,
        V.old_site_address,
        V.END_USER,
        V.MFG_SITE_NAME,
        V.EQUIPMENT_DESC,
        V.CONTRACT_NUMBER,
        V.ENTITY_NAME,
        V.SITE_NUMBER,
        CBIT.BOXUP_ITEM_TYPE,
        V.USER_CON_NUMBER,
        V.PRODUCT_NAME,
        CEDS.ENGINEER_DELIVER_SET_NUM,
        V.PTO_CODE || '/' || AA.OLD_ITEM_CODE MODEL,
        DECODE(v.country_id,1,'中文','英文') TEMPLATE_LANGUAGE,
        V.SITE_ADDRESS,
        V.CONTRACT_HEADER_ID,
        V.BOX_NUMBER,
        V.TOTAL_BOXES,
        V.SHIP_TO_ADDRESS,
        V.SHIP_TO_COMPANY,
        V.CONTACT_NAME || '' || V.CONTACT_PHONE SHIP_TO_PERSON,
        V.PTO_CODE,
        V.SHIPMENT_PATCH,
        V.MERGE_BILL_FLAG,
        V.PACK_TYPE_NAME_EN,
        V.PACK_TYPE_NAME_CN,
        V.MFG_SITE_ID,
        V.PRODUCT_TYPE_NAME,
        V.COUNTRY_ID,
        CASE CEDS.DELIVERY_MODE
        WHEN '1' THEN
        '按站点发货'
        WHEN '2' THEN
        '按站型发货'
        WHEN '3' THEN
        '按ITEM发货'
        ELSE
        DECODE(CCE.POST_ONLY, 'Y', '', '按站型发货')
        END SHIPMENT_MODE,
        V.MFG_SITE_TYPE,
        V.SITE_ADDRESS1,
        V.SITE_ADDRESS2,
        V.SITE_ADDRESS3,
        V.SITE_ADDRESS4,
        V.PROJECT_NAME,
        decode(TO_CHAR(V.CUSTOMER_PO),null,'', TO_CHAR(V.CUSTOMER_PO)||'/') CUSTOMER_PO,
        V.PROJECT_NUMBER,
        case upper(V.MFG_SITE_TYPE) when 'C' then '软件' when 'ZC' then '否' else case upper(V.BOQ_FLAG) when 'Y' then '是' else '否' end end BOQ_FLAG,
        APP_MES.FIND_BOXUP_BATTERY_LABELTYPE(v.bill_id) BATTERY_LABELTYPE,
        APP_MES.FIND_BOXUP_DANGERGOODSTYPE(v.bill_id) BOX_DANGERGOODSTYPE,
        APP_MES.FIND_BOXUP_IS_DANGER(v.bill_id)  BOX_IS_DANGER,
        APP_MES.FIND_BOXUP_PASTE_LABEL(v.bill_id)  PASTE_LABEL,
        V.EU_NO,
        V.EU_DESC,
        V.EU_DESC_EN,
        V.EU_UNIT,
        V.EU_UNIT_EN,
        V.EU_QTY,
        V. SINGLE_EUBOX_NUM,
        V.EU_POSITION,
        VV.box_type_name,
        CDS.deliver_set_num,
        V.PTO_NAME,
        APP_MES.GET_DEVICES_SITE_CODE(V.CONTRACT_HEADER_ID,MFG.MFG_SITE_CODE) DEVICES_CODE
        FROM APP_MES.MES_CONTRACT_BOXUP_SHIPMENT_V V,
        app_mes.cpm_boxup_topbox_types_v VV,
        APP_MES.CDM_BOXUP_ITEM_TYPE           CBIT,
        APP_MES.CDM_DELIVER_SETS              CDS,
        APP_MES.CDM_ENGINEER_DELIVER_SETS             CEDS,
        APP_MES.CDM_CONTRACT_PROJECT_SHIPMENTS        CCPS,
        APP_MES.MTL_SYSTEM_ITEMS               BB,
        APP_MES.ITEM_ATTACHED_INFORMATION      AA,
        APP_MES.CPM_INNER_ORDER_HEADERS        CION,
        APP_MES.CPM_CONTRACT_ENTITIES          CCE,
        APP_MES.CPM_CONTRACT_MFG_SITES         MFG
        WHERE V.ENTITY_ID = CBIT.ENTITY_ID(+)
        AND V.INNER_ORDER_HEADER_ID = CION.INNER_ORDER_HEADER_ID(+)
        AND CION.DELIVER_SET_ID = CDS.DELIVER_SET_ID(+)
        AND CDS.PROJECT_SHIPMENT_ID = CCPS.PROJECT_SHIPMENT_ID(+)
        AND CCPS.ENGINEER_DELIVER_SET_ID = CEDS.ENGINEER_DELIVER_SET_ID(+)
        AND V.PTO_CODE = BB.SEGMENT1
        AND BB.INVENTORY_ITEM_ID = AA.INVENTORY_ITEM_ID(+)
        AND V.ENTITY_ID = CCE.ENTITY_ID
        AND V.ENABLED_FLAG = 'Y'
        AND CCE.ENABLED_FLAG = 'Y'
        AND V.MFG_SITE_ID=MFG.MFG_SITE_ID(+)
        AND v.box_type_id = vv.box_type_id(+)
        <if test="billId != null">
            AND V.BILL_ID =#{billId,jdbcType=VARCHAR}
        </if>
        <if test="billNo != null">
            AND V.bill_number = #{billNo,jdbcType=VARCHAR}
        </if>
        <if test="billId == null and billNo == null">
            and 1=2
        </if>
        )
    </select>


    <select id="boqBomSelect" parameterType="java.util.Map" resultMap="boqBomSelect">
        <![CDATA[
        SELECT
          层次 AS BOQ_Level,
    CASE WHEN ITEM_BARCODE IS NULL THEN '' ELSE ITEM_BARCODE END AS  MATERIAL_BARCODE,
    CASE WHEN MODULE_ZY_CODE IS NULL THEN '' ELSE MODULE_ZY_CODE END AS CLIENT_MATERIAL_CODE,
    CASE WHEN 数量 IS NULL THEN '' ELSE TO_CHAR(数量) END AS MATERIAL_QTY,
    CASE WHEN DECODE(#{userName},'中文',单位,单位英文) IS NULL THEN '' ELSE DECODE(#{userName},'中文',单位,单位英文) END AS  UNIT,
    CASE WHEN LENGTH(CMS_CODE) >25 THEN SUBSTR(CMS_CODE,0,25) ELSE CMS_CODE END AS  CODE_NAME,
    CASE WHEN 物料代码 IS NULL THEN '' ELSE 物料代码 END AS MATERIAL_CODE,
    CASE WHEN DECODE(#{userName},'中文',
    DECODE(length(regexp_replace(层次||'.','[^.]','')),'4',物料名称,DECODE(BOQ物料名称,'',物料名称,BOQ物料名称)),
    DECODE(length(regexp_replace(层次||'.','[^.]','')),'4',英文物料名称,DECODE(BOQ英文物料名称,'',英文物料名称,BOQ英文物料名称))) IS NULL THEN '' ELSE DECODE( #{userName},'中文',
    DECODE(length(regexp_replace(层次||'.','[^.]','')),'4',物料名称,DECODE(BOQ物料名称,'',物料名称,BOQ物料名称)),
    DECODE(length(regexp_replace(层次||'.','[^.]','')),'4',英文物料名称,DECODE(BOQ英文物料名称,'',英文物料名称,BOQ英文物料名称))) END AS MATERIAL_NAME,
      SU_CODE,
      SU_NAME,
      GBOM_SU_ITEM_QTY,
      SU_QTY,
      CBOM_CODE,
      CBOM_NAME,
      CBOM_UNIT_ZH,
      CBOM_UNIT_EN,
      CBOM_QTY,
      SU_NAME_EN,
      CBOM_NAME_EN,
      SU_UNIT_ZH,
      SU_UNIT_EN,
      SU_NUMBER,
      IS_DANGERGOODS,
      DANGERGOODSTYPE,
      BATTERY_LABELTYPE

     FROM(
               SELECT
               'BOXUP_FLAG' 层次,
               APP_MES.FIND_ITEM_CODE(E.INVENTORY_ITEM_ID, E.ORGANIZATION_ID) 物料代码,
               E.ITEM_NAME 物料名称,
               APP_MES.FIND_ITEM_ENGLISH_NAME(E.INVENTORY_ITEM_ID,
                                              E.ORGANIZATION_ID) 英文物料名称,
               APP_MES.FIND_ITEM_UNIT_REPAIR_TL(K.SEGMENT1,
                                                K.PRIMARY_UNIT_OF_MEASURE,
                                                'ZHS') 单位,
               APP_MES.FIND_ITEM_UNIT_REPAIR_TL(K.SEGMENT1,
                                                K.PRIMARY_UNIT_OF_MEASURE,
                                                'US') 单位英文,
               APP_MES.GETPACKAGEBAR_BOQ(A.BILL_ID,A.BILL_NUMBER,
                                                     E.INVENTORY_ITEM_ID,E.CONFIG_DETAIL_ID) ITEM_BARCODE,
               '' BOQ物料名称,
               '' BOQ英文物料名称,
               'Z' IMPORT_ITEM_TYPE,
               '' CUSTOMER_PO_BY_CMS,
               '' CMS_UNIT_CN,
               '' CMS_UNIT_EN,
               '' CMS_CODE,
               '' MODULE_ZY_CODE,
               ROUND(E.BOXUP_QTY, 4) 数量

                          , E.SU_CODE,
                             E. SU_NAME,
                              E.GBOM_SU_ITEM_QTY,
                              E.SU_QTY,
                              E.CBOM_CODE,
                              E.CBOM_NAME,
                              E.CBOM_UNIT_ZH,
                             E. CBOM_UNIT_EN,
                              E.CBOM_QTY,
                             E. SU_NAME_EN,
                              E.CBOM_NAME_EN,
                              E.SU_UNIT_ZH,
                              E.SU_UNIT_EN,
                              E.SU_NUMBER,
                             E. IS_DANGERGOODS,
                             E. DANGERGOODSTYPE,
                            E.BATTERY_LABELTYPE
          FROM APP_MES.MTL_SYSTEM_ITEMS       K,
               APP_MES.CPM_BOXUP_BILL_ITEMS   E,
               APP_MES.CPM_BOXUP_BILLS        A
         WHERE (E.CONFIG_DETAIL_ID IS NULL OR E.CONFIG_DETAIL_ID<0)
           AND A.BILL_ID = E.BILL_ID
           AND E.INVENTORY_ITEM_ID = K.INVENTORY_ITEM_ID(+)
           AND E.ORGANIZATION_ID = K.ORGANIZATION_ID(+)
           AND A.ENABLED_FLAG = 'Y'
           AND E.ENABLED_FLAG = 'Y'
           AND A.BILL_ID = #{billId}

        UNION ALL

        SELECT *
          FROM (SELECT DISTINCT
                                TEMP1.RELATION_IDENTIFIER 层次,
                                TEMP1.ITEM_CODE 物料代码,
                                TEMP1.ITEM_NAME 物料名称,
                                TEMP1.ITEM_ENGLISH_NAME 英文物料名称,
                                DECODE(TEMP1.CMS_UNIT_CN,'', TEMP1.ITEM_UNIT, TEMP1.CMS_UNIT_CN) 单位,
                                DECODE(TEMP1.CMS_UNIT_EN,'', TEMP1.ITEM_UNIT_EN, TEMP1.CMS_UNIT_EN) 单位英文,
                                TEMP2.ITEM_BARCODE,
                                TEMP1.IMPORT_ITEM_NAME BOQ物料名称,
                                TEMP1.IMPORT_ITEM_ENG_NAME 英文BOQ物料名称,
                                TEMP1.IMPORT_ITEM_TYPE,
                                TEMP1.CUSTOMER_PO_BY_CMS,
                                TEMP1.CMS_UNIT_CN,
                                TEMP1.CMS_UNIT_EN,
                                TEMP1.CMS_CODE,
                                TEMP1.MODULE_ZY_CODE,
                                ROUND(TEMP2.BOXUP_QTY, 4) 数量,

                             TEMP2.SU_CODE,
                            TEMP2. SU_NAME,
                            TEMP2.GBOM_SU_ITEM_QTY,
                             TEMP2.SU_QTY,
                             TEMP2.CBOM_CODE,
                             TEMP2.CBOM_NAME,
                             TEMP2.CBOM_UNIT_ZH,
                            TEMP2. CBOM_UNIT_EN,
                             TEMP2.CBOM_QTY,
                            TEMP2. SU_NAME_EN,
                             TEMP2.CBOM_NAME_EN,
                             TEMP2.SU_UNIT_ZH,
                             TEMP2.SU_UNIT_EN,
                            TEMP2.SU_NUMBER,
                            TEMP2. IS_DANGERGOODS,
                            TEMP2. DANGERGOODSTYPE,
                            TEMP2. BATTERY_LABELTYPE

                  FROM (
                   SELECT BOQ.*
                   FROM
                  (SELECT B.BILL_ID,
                         B.INVENTORY_ITEM_ID ITEM_ID,
                         B.CONFIG_DETAIL_ID,
                         A.BILL_NUMBER,
                         B.MEMO,
                         ROUND(SUM(B.BOXUP_QTY), 4) BOXUP_QTY,
                         APP_MES.GETPACKAGEBAR_BOQ(B.BILL_ID,A.BILL_NUMBER,
                                                     B.INVENTORY_ITEM_ID,B.CONFIG_DETAIL_ID) ITEM_BARCODE,
                         APP_MES.FIND_ITEM_CODE(B.INVENTORY_ITEM_ID, B.ORGANIZATION_ID) ITEM_CODE,
                         A.MFG_SITE_ID

                          ,B.SU_CODE,
                            B. SU_NAME,
                             B.GBOM_SU_ITEM_QTY,
                             B.SU_QTY,
                             B.CBOM_CODE,
                             B.CBOM_NAME,
                             B.CBOM_UNIT_ZH,
                            B. CBOM_UNIT_EN,
                             B.CBOM_QTY,
                            B. SU_NAME_EN,
                             B.CBOM_NAME_EN,
                             B.SU_UNIT_ZH,
                             B.SU_UNIT_EN,
                             B.SU_NUMBER,
                            B. IS_DANGERGOODS,
                            B. DANGERGOODSTYPE,
                            B. BATTERY_LABELTYPE
                    FROM APP_MES.CPM_BOXUP_BILL_ITEMS B, APP_MES.CPM_BOXUP_BILLS A
                   WHERE A.BILL_ID = B.BILL_ID
                     AND A.BILL_ID = #{billId}
                     AND A.ENABLED_FLAG = 'Y'
                     AND B.ENABLED_FLAG = 'Y'
                   GROUP BY A.BILL_NUMBER,
                            B.BILL_ID,
                            B.INVENTORY_ITEM_ID,
                            B.CONFIG_DETAIL_ID,
                            B.ORGANIZATION_ID,
                            B.MEMO,
                            A.MFG_SITE_ID

                             ,B.SU_CODE,
                            B. SU_NAME,
                             B.GBOM_SU_ITEM_QTY,
                             B.SU_QTY,
                             B.CBOM_CODE,
                             B.CBOM_NAME,
                             B.CBOM_UNIT_ZH,
                            B. CBOM_UNIT_EN,
                             B.CBOM_QTY,
                            B. SU_NAME_EN,
                             B.CBOM_NAME_EN,
                             B.SU_UNIT_ZH,
                             B.SU_UNIT_EN,
                             B.SU_NUMBER,
                            B. IS_DANGERGOODS,
                            B. DANGERGOODSTYPE,
                            B. BATTERY_LABELTYPE
                            ) BOQ
                      UNION ALL
                       SELECT DISTINCT CBBI2.BILL_ID,
                       DETAIL.INVENTORY_ITEM_ID ITEM_ID,
                       DETAIL.CONFIG_DETAIL_ID,
                       CBB2.BILL_NUMBER,
                       DETAIL.MEMO,
                       DETAIL.BOXUP_QTY,
                       '' ITEM_BARCODE,
                       APP_MES.FIND_ITEM_CODE(CBBI2.INVENTORY_ITEM_ID,
                                              CBBI2.ORGANIZATION_ID) ITEM_CODE,
                       CBB2.MFG_SITE_ID

                        ,CBBI2.SU_CODE,
                            CBBI2. SU_NAME,
                             CBBI2.GBOM_SU_ITEM_QTY,
                             CBBI2.SU_QTY,
                             CBBI2.CBOM_CODE,
                             CBBI2.CBOM_NAME,
                             CBBI2.CBOM_UNIT_ZH,
                            CBBI2. CBOM_UNIT_EN,
                             CBBI2.CBOM_QTY,
                            CBBI2. SU_NAME_EN,
                             CBBI2.CBOM_NAME_EN,
                             CBBI2.SU_UNIT_ZH,
                             CBBI2.SU_UNIT_EN,
                             CBBI2.SU_NUMBER,
                            CBBI2. IS_DANGERGOODS,
                            CBBI2. DANGERGOODSTYPE,
                           CBBI2. BATTERY_LABELTYPE

                  FROM APP_MES.CPM_BOXUP_BILLS      CBB2,
                       APP_MES.CPM_BOXUP_BILL_ITEMS CBBI2,
                       APP_MES.CPM_BOXUP_BILL_ITEMS_DATAIL DETAIL
                 WHERE CBB2.BILL_ID = CBBI2.BILL_ID
                   AND CBB2.BILL_ID = #{billId}
                   AND CBB2.ENABLED_FLAG = 'Y'
                   AND CBBI2.ENABLED_FLAG = 'Y'
                   AND CBBI2.BILL_ITEM_ID = DETAIL.BILL_ITEM_ID
                   AND DETAIL.ENABLED_FLAG = 'Y'
                     ) TEMP2,
                       APP_MES.CPM_BOXUP_BILLS TEMP3,
                       APP_MES.CPM_CONTRACT_ENTITIES F,
                       APP_MES.CDM_CONTRACT_LINES G,
                       APP_MES.CDM_CONTRACT_HEADERS H,
                       APP_MES.CDM_CONTRACT_SHIPMENTS I,
                       APP_MES.CPM_BOXUP_CALC_QTY BOQ_QTY,
                       (SELECT CCD.CONFIG_DETAIL_ID,
                               CCD.RELATION_IDENTIFIER,
                               CCD.PARENT_CONFIG_DETAIL_ID,
                               CCD.ASSIST_FLAG,
                               B.INVENTORY_ITEM_ID ITEM_ID,
                               B.SEGMENT1 ITEM_CODE,
                               B.DESCRIPTION ITEM_NAME,
                               APP_MES.FIND_ITEM_UNIT_REPAIR_TL(B.SEGMENT1,
                                                                B.PRIMARY_UNIT_OF_MEASURE,
                                                                'ZHS') ITEM_UNIT,
                               APP_MES.FIND_ITEM_UNIT_REPAIR_TL(B.SEGMENT1,
                                                                B.PRIMARY_UNIT_OF_MEASURE,
                                                                'US') ITEM_UNIT_EN,
                               '' ITEM_VERSION,
                               APP_MES.FIND_ITEM_ENGLISH_NAME(B.INVENTORY_ITEM_ID,
                                                              B.ORGANIZATION_ID) ITEM_ENGLISH_NAME,
                               ROUND(APP_MES.GETCONFIGQTYBYMFGSITE(CCD.MFG_SITE_ID,
                                                                   CCD.CONFIG_DETAIL_ID),
                                     4) QUANTITY,
                               CCD.IMPORT_ITEM_NAME,
                               MFG.PTO_INVENTORY_ITEM_ID,
                               CCD.IS_BOQ_ITEM,
                               CCD.IMPORT_ITEM_TYPE,
                               CCD.Code,
                               CCD.BOQ_ITEM_NAME_ENG IMPORT_ITEM_ENG_NAME,
                               CCD.QTY_FLAG,
                               CCD.ORDER_NUMBER,
                               CCD.ORDER_LINE_NUMBER,
                               CCD.MODULE_QUANTITY,
                               A.ORGANIZATION_ID,
                               A.ENTITY_ID,
                               A.BILL_ID,
                               SITE.CITY || ',' || SITE.DISTRICT || ',' ||
                               SITE.SITE_ADDRESS SITE_CITY_ADDR,
                               '' CUSTOMER_PO_BY_CMS,
                               CCD.CN_UNIT CMS_UNIT_CN,
                               CCD.EN_UNIT CMS_UNIT_EN,
                               CCD.CODE CMS_CODE,
                               MFG.MFG_SITE_ID,
                               case when ccd.PCONFIG_DETAIL_ID is null then CCD.MODULE_ZY_CODE else cpl.ITEM_NUMBER end MODULE_ZY_CODE,
                               case when ccd.PCONFIG_DETAIL_ID is null then CCD.ZY_LINE_NUMBER else cpl.item_desc end ZY_LINE_NUMBER,
                               LENGTHB(CCD.RELATION_IDENTIFIER)-LENGTHB(REPLACE(CCD.RELATION_IDENTIFIER,'.','')) LEVELCNT
                          FROM APP_MES.MTL_SYSTEM_ITEMS       B,
                               APP_MES.MTL_SYSTEM_ITEMS       PTO,
                               APP_MES.CPM_CONFIG_DETAILS     CCD,
                               APP_MES.CPM_CONTRACT_SITES     SITE,
                               APP_MES.CPM_CONTRACT_MFG_SITES MFG,
                               APP_MES.CPM_PO_LINES           cpl,
                               APP_MES.CPM_BOXUP_BILLS        A
                         WHERE CCD.ORGANIZATION_ID = B.ORGANIZATION_ID
                           AND CCD.INVENTORY_ITEM_ID = B.INVENTORY_ITEM_ID
                           AND CCD.ENABLED_FLAG = 'Y'
                           AND MFG.PTO_INVENTORY_ITEM_ID = PTO.INVENTORY_ITEM_ID
                           AND MFG.ORGANIZATION_ID = PTO.ORGANIZATION_ID
                           AND CCD.MFG_SITE_ID = MFG.MFG_SITE_ID
                           AND MFG.SITE_ID = SITE.SITE_ID
                           AND A.MFG_SITE_ID = MFG.MFG_SITE_ID
                           AND ccd.PO_LINE_ID = cpl.PO_LINE_ID(+)
                           AND A.BILL_ID = #{billId}
                       ) TEMP1
                 WHERE TEMP1.CONFIG_DETAIL_ID = TEMP2.CONFIG_DETAIL_ID(+)
                   AND TEMP1.BILL_ID = BOQ_QTY.BILL_ID(+)
                   AND TEMP1.MFG_SITE_ID = BOQ_QTY.MFG_SITE_ID(+)
                   AND TEMP1.CONFIG_DETAIL_ID = BOQ_QTY.CONFIG_DETAIL_ID(+)
                   AND TEMP1.BILL_ID = TEMP3.BILL_ID(+)
                   AND TEMP1.ENTITY_ID = F.ENTITY_ID(+)
                   AND F.CONTRACT_LINE_ID = G.CONTRACT_LINE_ID(+)
                   AND G.CONTRACT_HEADER_ID = H.CONTRACT_HEADER_ID(+)
                   AND F.SHIPMENT_ID = I.SHIPMENT_ID(+)

                 START WITH TEMP1.ITEM_ID = TEMP2.ITEM_ID

                CONNECT BY PRIOR
                            TEMP1.PARENT_CONFIG_DETAIL_ID = TEMP1.CONFIG_DETAIL_ID)

        )
        ]]>
    </select>




    <select id="checkTally" statementType="CALLABLE" parameterType= "map" >
        {call APP_MES.PKG_CURBS_INFO.GET_CURBS_INFO_COUNT(
        #{map.box_id,mode=IN,jdbcType=VARCHAR,javaType=java.lang.String},
        #{map.resultCursor,mode=OUT,jdbcType=CURSOR,resultMap=CURBCountResultMap}
        )}
    </select>
</mapper>