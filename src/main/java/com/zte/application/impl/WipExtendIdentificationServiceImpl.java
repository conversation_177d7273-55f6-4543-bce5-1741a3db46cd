/**
 * 项目名称 : ${project_name}
 * 创建日期 : ${date}
 * 修改历史 :
 * 1. [${date}] 创建文件 by ${user}
 **/
package com.zte.application.impl;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Lists;
import com.zte.application.AssemblExecLogService;
import com.zte.application.AssemblyOptRecordService;
import com.zte.application.AssemblyResultHisService;
import com.zte.application.AssemblyResultService;
import com.zte.application.CommonSyncService;
import com.zte.application.ProdBindingSettingService;
import com.zte.application.WipExtendIdentificationService;
import com.zte.application.scan.FlowControlCommonService;
import com.zte.common.CommonUtils;
import com.zte.common.ConstantInterface;
import com.zte.common.DateUtil;
import com.zte.common.enums.InterfaceEnum;
import com.zte.common.enums.PdmSourceSysEnum;
import com.zte.common.excel.ExcelCommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.BusinessConstant;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.common.utils.NumConstant;
import com.zte.common.utils.RedisKeyConstant;
import com.zte.common.utils.SnCheckUtil;
import com.zte.common.utils.SqlUtils;
import com.zte.common.utils.StringWithChineseUtils;
import com.zte.domain.model.*;
import com.zte.gei.processor.handler.exporter.AbstractExportTaskHandler;
import com.zte.infrastructure.remote.BarcodeCenterRemoteService;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.CrafttechRemoteService;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.infrastructure.remote.HttpRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.assembler.ProdBindingSettingAssembler;
import com.zte.interfaces.assembler.WipExtendIdentificationAssembler;
import com.zte.interfaces.dto.AssemblExecLogEntityDTO;
import com.zte.interfaces.dto.AssemblyOptRecordEntityDTO;
import com.zte.interfaces.dto.AssemblyRelationshipQueryDTO;
import com.zte.interfaces.dto.AssemblyResultEntityDTO;
import com.zte.interfaces.dto.AssemblyResultHisEntityDTO;
import com.zte.interfaces.dto.AuxMaterialBindingDTO;
import com.zte.interfaces.dto.BSProcessDTO;
import com.zte.interfaces.dto.BarcodeExpandDTO;
import com.zte.interfaces.dto.BarcodeExpandQueryDTO;
import com.zte.interfaces.dto.BarcodeLockDetailEntityDTO;
import com.zte.interfaces.dto.CtRouteDetailDTO;
import com.zte.interfaces.dto.HrmPersonInfoDTO;
import com.zte.interfaces.dto.MtlRelatedItemsEntityDTO;
import com.zte.interfaces.dto.PDMProductMaterialResultDTO;
import com.zte.interfaces.dto.ProdBindingSettingDTO;
import com.zte.interfaces.dto.PsWipInfoDTO;
import com.zte.interfaces.dto.StItemMessage;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.interfaces.dto.SysLookupValuesDTO;
import com.zte.interfaces.dto.ToBindingItemsDTO;
import com.zte.interfaces.dto.WipExtendIdentificationDTO;
import com.zte.interfaces.dto.WsmAssembleLinesDTO;
import com.zte.interfaces.dto.scan.BarcodeBindingDTO;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.itp.msa.util.string.StringHelper;
import com.zte.microservice.name.MicroServiceNameEum;
import com.zte.springbootframe.common.consts.SysConst;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.FactoryConfig;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.redis.RedisLock;
import com.zte.springbootframe.util.EmailUtils;
import com.zte.springbootframe.util.IdGenerator;
import com.zte.springbootframe.util.JsonConvertUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.RequestHeadValidationUtil;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TimeZone;
import java.util.UUID;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * // TODO 添加类/接口功能描述
 *
 * <AUTHOR>
 **/
@Service
public class WipExtendIdentificationServiceImpl extends AbstractExportTaskHandler<AssemblyRelationshipQueryDTO, WipExtendIdentification> implements WipExtendIdentificationService {

    @Autowired
    private WipExtendIdentificationRepository wipExtendIdentificationRepository;

    @Autowired
    private ProdBindingSettingRepository prodBindingSettingRepository;

    @Autowired
    private PsWipInfoRepository psWipInfoRepository;

    @Autowired
    private WipEntityScanInfoServiceImpl wipEntityScanInfoService;

    @Autowired
    private AssemUnbindHistoryInfoRepository assemUnbindHistoryInfoRepository;

    @Autowired
    private ConstantInterface constantInterface;

    @Autowired
    private ProdUnbindingSettingRepository prodUnbindingSettingRepository;

    @Autowired
    private ProdBindingSettingService prodBindingSettingService;

    @Autowired
    private DatawbRemoteService datawbRemoteService;
    @Autowired
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Autowired
    private AssemblExecLogService assemblExecLogService;

    @Autowired
    private AssemblyResultService assemblyResultService;

    @Autowired
    private AssemblyResultHisService assemblyResultHisService;


    @Autowired
    private FactoryConfig factoryConfig;

    @Autowired
    private EmailUtils emailUtils;

    @Autowired
    private CommonSyncService commonSyncService;

    @Autowired
    private FlowControlCommonService flowControlCommonService;

    @Autowired
    private BarcodeCenterRemoteService barcodeCenterRemoteService;

    @Autowired
    private WriteBackSpmFailInfoRepository writeBackSpmFailInfoRepository;

    @Autowired
    private AssemblyOptRecordService assemblyOptRecordService;

    @Autowired
    private AssemblyOptRecordRepository assemblyOptRecordRepository;

    @Autowired
    private IdGenerator idGenerator;

    @Value("${imes.push.wip.spm.leadTime:3}")
    private int leadTime;

    @Autowired
    private WipExtendIdentificationExportImpl wipExtendIdentificationExport;

    @Autowired
    private AssemblyRelaScanServiceImpl assemblyRelaScanService;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private WarehouseEntryDetailServiceImpl warehouseEntryDetailService;

    // 日志对象
    private static final Logger LOG = LoggerFactory.getLogger(WipExtendIdentificationServiceImpl.class);

    public void setWipExtendIdentificationRepository(
            WipExtendIdentificationRepository wipExtendIdentificationRepository) {

        this.wipExtendIdentificationRepository = wipExtendIdentificationRepository;
    }

    /* Started by AICoder, pid:h7db6pae43ve3b2147630a1aa0af193231c3a813 */
    /**
     * 中试获取整机条码向下绑定结果
     *
     * @param formSn@return 绑定关系
     */
    @Override
    public List<WipExtendIdentification> queryingWipExtendIdentification(String formSn) {
        RequestHeadValidationUtil.validaFactoryId();
        if (StringUtils.isBlank(formSn)) {
            return new LinkedList<>();
        }
        AssemblyRelationshipQueryDTO query = new AssemblyRelationshipQueryDTO();
        query.setFormSnList(Collections.singletonList(formSn));
        query.setPage(1);
        // 整机绑定关系
        query.setFormType(Constant.STR_2);
        // 层级向下展开
        query.setQueryType(Constant.STR_1);
        int total = wipExtendIdentificationRepository.assemblyRelationshipCount(query);
        if (total == Constant.INT_0) {
            return new LinkedList<>();
        }
        query.setRows(total);
        return wipExtendIdentificationRepository.assemblyRelationshipPage(query);
    }
    /* Ended by AICoder, pid:h7db6pae43ve3b2147630a1aa0af193231c3a813 */

    /**
     * 增加实体数据
     *
     * @param record
     **/
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertWipExtendIdentification(WipExtendIdentification record) {
        wipExtendIdentificationRepository.insertWipExtendIdentification(record);
        this.insertOptRecord(NumConstant.STR_ONE,record);
    }


    /**
     * 导出
     * @param query
     * @return
     */
    @Override
    public Integer countExportTotal(AssemblyRelationshipQueryDTO query) {
        this.parameterVerification(query);
        Page<AssemblyRelationshipQueryDTO> pageInfo = new Page<>(query.getPage(), query.getRows());
        pageInfo.setParams(query);
        wipExtendIdentificationRepository.assemblyRelationshipQuery(pageInfo);
        // 查询数据总数
        return pageInfo.getTotal();
    }



    @Override
    public List<WipExtendIdentification> queryExportData(AssemblyRelationshipQueryDTO query, int pageNo,int pageSize) {
        this.parameterVerification(query);
        Page<AssemblyRelationshipQueryDTO> pageInfo = new Page<>(pageNo, pageSize);
        pageInfo.setSearchCount(false);
        pageInfo.setParams(query);
        List<WipExtendIdentification> wipExtendIdentificationList = wipExtendIdentificationRepository.assemblyRelationshipQuery(pageInfo);
        if (CollectionUtils.isEmpty(wipExtendIdentificationList)) {
            return wipExtendIdentificationList;
        }
        // 为辅料绑定查询时，需要根据任务号点对点查询计划服务获取任务的料单信息f
        if (Objects.equals(query.getFormType(), Constant.STR_4)) {
            this.getItemNoAndNameForAuxBinding(wipExtendIdentificationList);
        }
        //判断子条码是否存在下级子条码
        try {
            //设置绑定类型 子工序 工站名称
            this.setProcessNameAndWorkStationName(wipExtendIdentificationList);
            //设置创建人
            this.setEmpName(wipExtendIdentificationList);
        }catch (MesBusinessException e){
            throw e;
        }catch (Exception e){
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,MessageId.BLINK,new Object[]{e.getMessage()});
        }
        return wipExtendIdentificationList;
    }


    /**
     * 根据主键删除实体数据
     *
     * @param record
     **/
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteWipExtendIdentificationById(WipExtendIdentification record) {
        this.insertOptRecord(NumConstant.STR_TWO,record);
        wipExtendIdentificationRepository.deleteWipExtendIdentificationById(record);
    }

    /**
     * 根据主键更新实体数据
     *
     * @param record
     **/
    @Override
    public void updateWipExtendIdentificationById(WipExtendIdentification record) {
        this.insertOptRecord(NumConstant.STRING_THREE,record);
        wipExtendIdentificationRepository.updateWipExtendIdentificationById(record);
    }

    /**
     * 根据主键查询实体信息
     *
     * @param record
     * @return WipExtendIdentification
     **/
    @Override
    public WipExtendIdentification selectWipExtendIdentificationById(WipExtendIdentification record) {

        return wipExtendIdentificationRepository.selectWipExtendIdentificationById(record);
    }

    /**
     * 根据工装编码查询实体信息
     *
     * @param formSn
     * @return
     */
    @Override
    public WipExtendIdentification selectWipExtendIdentificationByFormSn(String formSn) {

        return wipExtendIdentificationRepository.selectWipExtendIdentificationByFormSn(formSn);
    }

    /**
     * 增加实体数据
     *
     * @param record
     * @return WipExtendIdentification
     **/
    @Override
    public java.util.List<WipExtendIdentification> getList(Map<String, Object> record) {

        return wipExtendIdentificationRepository.getList(record);
    }

    /**
     * 增加实体数据
     *
     * @param record
     * @return Long
     **/
    @Override
    public Long getCount(Map<String, Object> record) {

        return wipExtendIdentificationRepository.getCount(record);
    }

    /**
     * 增加实体数据
     *
     * @param record
     * @return WipExtendIdentification
     **/
    @Override
    public java.util.List<WipExtendIdentification> getPage(Map<String, Object> record) {

        return wipExtendIdentificationRepository.getPage(record);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteWipExtendIdentificationBySn(WipExtendIdentification record) {
        insertOptRecordBySn(record.getSn());
        return wipExtendIdentificationRepository.deleteWipExtendIdentificationBySn(record);
    }

    /**
     * 按条码写解绑记录
     * @param sn
     */
    public void insertOptRecordBySn(String sn) {
        WipExtendIdentification wipExtendIdentification = wipExtendIdentificationRepository.selectEntityBySn(sn);
        this.insertOptRecord(NumConstant.STRING_TWO,wipExtendIdentification);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int saveOrUpdateBatch(List<WipExtendIdentification> recordList) {
        int num = 0;
        if (!CollectionUtils.isEmpty(recordList)) {
            List<List<WipExtendIdentification>> splitLists = CommonUtils.splitList(recordList, Constant.SPLITSIZE_HUNDRED);

            for (List<WipExtendIdentification> oneList : splitLists) {
                this.batchInsertOptRecord(NumConstant.STRING_ONE,oneList);
                num += wipExtendIdentificationRepository.saveOrUpdateBatch(oneList);
            }
        }


        return num;
    }

    /**
     * 查询待绑定物料清单--（需绑定物料清单-已绑定物料清单）
     *
     * @param record
     * @return ToBindingItemsDTO
     */
    @Override
    public ToBindingItemsDTO getToBindingItems(ToBindingItemsDTO record) {

        String sn = record.getSn(); //条码:用来查询已绑定清单
        String productCode = record.getProductCode();   //料单代码：用来查询需绑定清单
        List<ProdBindingSettingDTO> toBindingItems = new ArrayList<>();

        Map bindedItemsMap = new HashMap();
        bindedItemsMap.put("sn", sn);
        List<WipExtendIdentification> bindedItems = wipExtendIdentificationRepository.getList(bindedItemsMap);
        List<WipExtendIdentificationDTO> bindedList = WipExtendIdentificationAssembler.toWipExtendIdentificationDTOList(bindedItems);
        record.setBindedItems(bindedList);

        Map needBindingItemsMap = new HashMap();
        needBindingItemsMap.put("productCode", productCode);
        needBindingItemsMap.put("bindType", Constant.INT_1);
        List<ProdBindingSetting> needBindingItems = prodBindingSettingRepository.getList(needBindingItemsMap);
        List<ProdBindingSettingDTO> needBindingList = ProdBindingSettingAssembler.toProdBindingSettingDTOList(needBindingItems);
        record.setNeedBindingItems(needBindingList);

        if (CollectionUtils.isEmpty(bindedList)) {
            record.setToBindingItems(needBindingList);
            return record;
        }
        for (ProdBindingSettingDTO item : needBindingList) {
            String itemCode = item.getItemCode();
            String processCode = item.getProcessCode();
            List<WipExtendIdentificationDTO> bindedItemNum = bindedList.stream().filter(bindedItem ->
                    itemCode.equals(bindedItem.getItemNo()) && processCode.equals(bindedItem.getProcessCode())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(bindedItemNum)) {
                toBindingItems.add(item);
            } else {
                int usageCount = item.getUsageCount().intValue();
                int formQty = bindedItemNum.get(NumConstant.NUM_ZERO).getFormQty().intValue();
                int needBindNum = usageCount - formQty;
                if (needBindNum > 0) {
                    ProdBindingSettingDTO toBindingItem = new ProdBindingSettingDTO();
                    BeanUtils.copyProperties(item, toBindingItem);
                    toBindingItem.setUsageCount(new BigDecimal(needBindNum));
                    toBindingItems.add(toBindingItem);
                }
            }
        }
        record.setToBindingItems(toBindingItems);
        return record;
    }

    /**
     * 绑定物料清单
     *
     * @param record
     * @return ToBindingItemsDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int bindingItems(ToBindingItemsDTO record) throws Exception {
        String sn = record.getSn(); //条码:用来查询已绑定清单
        String empNo = record.getEmpNo();   //工号
        Map<String, Object> snMap = new HashMap();
        snMap.put("sn", sn);
        List<WipExtendIdentification> bindedItems = wipExtendIdentificationRepository.getList(snMap);
        ProdBindingSettingDTO toBindingItem = record.getToBindingItem();

        List<WipExtendIdentification> existItems = bindedItems.stream().filter(bindedItem ->
                        toBindingItem.getItemCode().equals(bindedItem.getItemNo()) && toBindingItem.getProcessCode().equals(bindedItem.getProcessCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(existItems)) {
            WipExtendIdentification insertItem = this.generateWEI(toBindingItem, sn, empNo);   //需插入的绑定清单
            insertItem.setEmpNo(empNo);
            this.insertOptRecord(NumConstant.STR_ONE,insertItem);
            return wipExtendIdentificationRepository.insertWipExtendIdentificationSelective(insertItem);
        } else {
            WipExtendIdentification existItem = existItems.get(NumConstant.NUM_ZERO);
            String identiId = existItem.getIdentiId();
            WipExtendIdentification updateItem = new WipExtendIdentification();
            updateItem.setIdentiId(identiId);
            updateItem.setLastUpdatedBy(empNo);
            return wipExtendIdentificationRepository.updateFormQtyById(updateItem);
        }
    }

    /**
     * 绑定物料清单
     *
     * @param record
     * @return ToBindingItemsDTO
     */
    public WipExtendIdentification generateWEI(ProdBindingSettingDTO record, String sn, String empNo) {
        WipExtendIdentification wipExtendIdentification = new WipExtendIdentification(); //生成新的绑定对象
        wipExtendIdentification.setIdentiId(UUID.randomUUID().toString());
        wipExtendIdentification.setFormQty(new BigDecimal(1));
        wipExtendIdentification.setFormSn(record.getItemCode());
        wipExtendIdentification.setFormType(record.getItemType());
        wipExtendIdentification.setItemNo(record.getItemCode());
        wipExtendIdentification.setSn(sn);
        wipExtendIdentification.setCreateBy(empNo);
        wipExtendIdentification.setLastUpdatedBy(empNo);
        wipExtendIdentification.setProcessCode(record.getProcessCode());
        wipExtendIdentification.setFactoryId(record.getFactoryId());
        wipExtendIdentification.setEntityId(record.getEntityId());
        wipExtendIdentification.setIsIdentifier(Constant.FLAG_Y);

        return wipExtendIdentification;
    }

    /**
     * 绑定物料清单
     *
     * @param workOrder   指令
     * @param processCode 子工序
     * @return RetCode
     * @Param sn 条码
     */
    @Override
    public RetCode bindedChecking(String workOrder, String processCode, String sn, String factoryId) throws Exception {
        RetCode retCode = new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID);
        //查找sn，不存在即返回
        LOG.info(" param: workOrder {}; processCode {}; sn {}", workOrder, processCode, sn);
        Map map = new HashMap();
        map.put("sn", sn);
        map.put("workOrderNo", workOrder);
        map.put("factoryId", factoryId);
        List<PsWipInfo> snList = psWipInfoRepository.getList(map);
        if (CollectionUtils.isEmpty(snList)) {
            retCode.setMsg(sn + CommonUtils.getLmbMessage(MessageId.SN_IS_NOT_EXIST));
            return retCode;
        }

        //根据条码的指令得到routeId,加当前工序得到routeDetail,取attribute1字段
        PsWipInfo psWipInfo = snList.get(NumConstant.NUM_ZERO);
        String routeId = CommonUtils.getRouteIdByWip(psWipInfo);
        if (StringUtils.isEmpty(routeId)) {
            LOG.info("sn: {}, workOrder: {}", sn, workOrder);
            throw new Exception(CommonUtils.getLmbMessage(MessageId.ROUTEID_IS_EMPTY));
        }
        List<CtRouteDetail> ctRouteDetailList = this.getRouteDetail(routeId, processCode, factoryId);
        if (CollectionUtils.isEmpty(ctRouteDetailList)) {
            LOG.info("routeId: {}, processCode: {}", routeId, processCode);
            throw new Exception(CommonUtils.getLmbMessage(MessageId.ROUTEDETAIL_INFO_NOT_FOUND));
        }
        CtRouteDetail ctRouteDetail = ctRouteDetailList.get(NumConstant.NUM_ZERO);
        String attribute1 = ctRouteDetail.getAttribute1();
        if (StringUtils.isEmpty(attribute1)) {
            retCode.setMsg(CommonUtils.getLmbMessage(MessageId.NO_NEED_TO_BIND_MATERIALS));
            return retCode;
        }

        //根据sn和processCode得到绑定的物料数
        Map bindedItemsMap = new HashMap();
        bindedItemsMap.put("sn", sn);
        bindedItemsMap.put("processCode", processCode);
        bindedItemsMap.put("factoryId", factoryId);
        List<WipExtendIdentification> bindedItems = wipExtendIdentificationRepository.getList(bindedItemsMap);
        int formQty = 0;
        if (!CollectionUtils.isEmpty(bindedItems)) {
            for (WipExtendIdentification item : bindedItems) {
                formQty += item.getFormQty().intValue();
            }
        }
        if (attribute1.equals(new Integer(formQty).toString())) {
            retCode.setMsg(CommonUtils.getLmbMessage(MessageId.DAUGHTER_CARD_BINDING_VERIFICATION_PASSED));
        } else {
            int num = Integer.parseInt(attribute1) - formQty;
            if (num > 0) {
                String[] msgParams = {processCode, attribute1, new Integer(num).toString()};
                String returnStr = CommonUtils.getLmbMessage(MessageId.DAUGHTER_MOTHER_CARD_BINDING_VERIFICATION_FAILED, msgParams);
                //String returnStr = "子母卡绑定校验失败！工序" + processCode + "需要绑定物料" + attribute1 + "个，还差"
                //      + new Integer(num).toString() + "个";
                throw new Exception(returnStr);
            } else {
                retCode.setMsg(CommonUtils.getLmbMessage(MessageId.DAUGHTER_CARD_BINDING_VERIFICATION_PASSED));
            }
        }
        return retCode;
    }

    /**
     * 点对点获取routeDetail
     *
     * @param routeId
     * @param processCode
     * @return
     * @throws Exception
     */
    private List<CtRouteDetail> getRouteDetail(String routeId, String processCode, String factoryId) throws Exception {

        LOG.info(" 查找routeDetail, routeId:{}, processCode:{} ", routeId, processCode);
        //查找参数
        Map<String, String> map = new HashMap<String, String>();
        map.put("NextProcess", processCode);
        map.put("routeId", routeId);
        map.put("factoryId", factoryId);
        String params = JacksonJsonConverUtil.beanToJson(map);
        Map<String, String> headerParamsMap = MESHttpHelper.getHttpRequestHeader();

        // 点对点调用服务
        String serviceName = MicroServiceNameEum.CRAFTTECH;
        String version = MicroServiceNameEum.VERSION;
        String sendType = MicroServiceNameEum.SENDTYPEGET;
        String getUrl = "/CT/CtRouteDetail";

        String getresult = MicroServiceRestUtil.invokeService(serviceName, version, sendType, getUrl, params,
                headerParamsMap);
        JsonNode json = JacksonJsonConverUtil.getMapperInstance().readTree(getresult);
        String bo = json.get(MpConstant.JSON_BO).toString();
        List<CtRouteDetail> listDetail = (List<CtRouteDetail>) JacksonJsonConverUtil.jsonToListBean(bo,
                new TypeReference<ArrayList<CtRouteDetail>>() {
                });

        return listDetail;
    }

    /**
     * 获取绑定类型列表
     *
     * @return ServiceData
     */
    @Override
    public ServiceData getBindType(String factoryId) throws Exception {
        ServiceData serviceData = new ServiceData();
        // step1: 校验工厂ID
        serviceData.setCode(CommonUtils.validFactoryId(factoryId));
        if (serviceData.getCode().getCode().equals(RetCode.SUCCESS_CODE)) {
            // step2: 从字典中获取绑定类型数据
            serviceData.setBo(BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_BIND_TYPE));
            LOG.info("点对点获取字典数据:{},详细数据：{}", Constant.LOOKUP_TYPE_BIND_TYPE, serviceData.getBo());
        }
        return serviceData;
    }

    /**
     * 获取组装关系查询数据
     *
     * @return ServiceData
     */
    @Override
    public ServiceData getBindRelaDataPage(WipExtendIdentificationDTO conditions, String factoryId) throws Exception {
        ServiceData serviceData = new ServiceData();
        //step1 校验工厂ID  及 传入参数
        serviceData.setCode(validParam(conditions, factoryId));
        if (serviceData.getCode().getCode().equals(RetCode.SUCCESS_CODE)) {
            //step2 获取数据   关联wip_info中获取 物料代码、物料名称
            List<WipExtendIdentification> wipExtendIdentifications = wipExtendIdentificationRepository.getListLinkWipInfoPage(conditions);
            //step3 翻译子工序、绑定类型, 为了减少远程调用， 采用in process_code
            transforResult(wipExtendIdentifications, BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_BIND_TYPE));
            serviceData.setBo(packageWipExtendIdentification(wipExtendIdentifications, wipExtendIdentificationRepository.getListLinkWipInfoCount(conditions), conditions.getPage()));
        }
        return serviceData;
    }


    /**
     * 解绑
     * @param assemblyRelationshipList
     * @param empNo
     * @return
     * @throws Exception
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int unBindAssemblyRelationship(List<AssemblyRelationshipQueryDTO> assemblyRelationshipList, String empNo) throws Exception {
        if (CollectionUtils.isEmpty(assemblyRelationshipList)) {
            return 0;
        }
        List<String>  idList = assemblyRelationshipList.stream().map(AssemblyRelationshipQueryDTO::getIdentiId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        this.checkSnReceived(assemblyRelationshipList);
        AssemblyRelationshipQueryDTO assemblyRelationshipQueryDTO = new AssemblyRelationshipQueryDTO();
        assemblyRelationshipQueryDTO.setIdList(idList);
        assemblyRelationshipQueryDTO.setEmpNo(empNo);
        //写解绑日志
        assemblyOptRecordRepository.insertOptRecordFromWipExtend(assemblyRelationshipQueryDTO);
        //更新主条码wipInfo表装配标识
        psWipInfoRepository.updateAssemblyIdentification(assemblyRelationshipQueryDTO);
        return wipExtendIdentificationRepository.unBindAssemblyRelationship(assemblyRelationshipQueryDTO);
    }

    /**
     *<AUTHOR>
     * 校验解绑条码是否已接收
     *@Date 2025/5/6 15:56
     *@Param [java.util.List<com.zte.interfaces.dto.AssemblyRelationshipQueryDTO>]
     *@return
     **/
    private void checkSnReceived (List<AssemblyRelationshipQueryDTO> assemblyRelationshipList) {
        SysLookupValues valueByCode = BasicsettingRemoteService.getSysLookUpValueByCode(Constant.LOOKU_VALUE_7503001);
        if (valueByCode == null || !Constant.FLAG_Y.equals(valueByCode.getLookupMeaning())) {
            return;
        }
        List<String> snList = assemblyRelationshipList.stream().map(AssemblyRelationshipQueryDTO::getFormSn).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(snList)) {
            return;
        }
        List<String> receivedSnList = warehouseEntryDetailService.getReceivedSnList(snList);
        if (!CollectionUtils.isEmpty(receivedSnList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SN_RECEIVED_CAN_NOT_UN_BIND, new String[] {receivedSnList.toString()});
        }
    }

    /**
     * 新增操作记录
     * @param optType
     * @param wipExtendIdentification
     * @throws Exception
     */
    @Override
    public void insertOptRecord(String optType, WipExtendIdentification wipExtendIdentification) {
        if(wipExtendIdentification == null){
            return;
        }
        List<WipExtendIdentification> wipExtendIdentificationList = new ArrayList<>();
        wipExtendIdentificationList.add(wipExtendIdentification);
        this.batchInsertOptRecord(optType,wipExtendIdentificationList);
    }

    /**
     * 新增操作记录
     * @param optType
     * @param wipExtendIdentificationList
     * @throws Exception
     */
    @Override
    public void batchInsertOptRecord(String optType, List<WipExtendIdentification> wipExtendIdentificationList) {
        if(CollectionUtils.isEmpty(wipExtendIdentificationList)){
            return;
        }
        String empNo = getEmpNo();
        List<AssemblyOptRecordEntityDTO> list = new ArrayList<>();
        for (WipExtendIdentification wipExtendIdentification : wipExtendIdentificationList) {
            AssemblyOptRecordEntityDTO assemblyOptRecordEntityDTO = new AssemblyOptRecordEntityDTO();
            assemblyOptRecordEntityDTO.setId(UUID.randomUUID().toString());
            assemblyOptRecordEntityDTO.setFormType(wipExtendIdentification.getFormType());
            assemblyOptRecordEntityDTO.setOptType(optType);
            assemblyOptRecordEntityDTO.setTaskNo(StringUtils.isEmpty(wipExtendIdentification.getTaskNo())?"":wipExtendIdentification.getTaskNo());
            assemblyOptRecordEntityDTO.setProdplanId(StringUtils.isEmpty(wipExtendIdentification.getProdPlanId())?"":wipExtendIdentification.getProdPlanId());
            assemblyOptRecordEntityDTO.setProcessCode(wipExtendIdentification.getProcessCode());
            assemblyOptRecordEntityDTO.setWorkStation(wipExtendIdentification.getWorkStation());
            assemblyOptRecordEntityDTO.setFormSn(StringUtils.isEmpty(wipExtendIdentification.getFormSn())?"":wipExtendIdentification.getFormSn());
            assemblyOptRecordEntityDTO.setSn(StringUtils.isEmpty(wipExtendIdentification.getSn())?"":wipExtendIdentification.getSn());
            assemblyOptRecordEntityDTO.setSubItemNo(wipExtendIdentification.getItemNo());
            assemblyOptRecordEntityDTO.setCreateBy(StringUtils.isEmpty(wipExtendIdentification.getEmpNo())?empNo:wipExtendIdentification.getEmpNo());
            list.add(assemblyOptRecordEntityDTO);
        }
        assemblyOptRecordService.batchInsert(list);
    }

    /**
     * 获取请求工号
     * @return
     */
    public String getEmpNo() {
        Map<String, String> headerMap = MESHttpHelper.getHttpRequestHeader();
        String empNo =  headerMap.get(Constant.X_EMP_NO);
        if(StringUtils.isEmpty(empNo)){
            empNo =  StringUtils.isEmpty(headerMap.get(Constant.X_EMP_NO_SMALL)) ? Constant.SYSTEM
                    : headerMap.get(Constant.X_EMP_NO_SMALL);
        }
        return empNo;
    }

    /**
     * 查询下层所有子条码
     * @param assemblyRelationshipQueryDTO
     * @return
     * @throws Exception
     */
    @Override
    public List<WipExtendIdentification> assemblyRelationshipQueryAll(AssemblyRelationshipQueryDTO assemblyRelationshipQueryDTO) throws Exception {
        this.parameterVerification(assemblyRelationshipQueryDTO);
        assemblyRelationshipQueryDTO.setPage(NumConstant.NUM_ONE);
        assemblyRelationshipQueryDTO.setRows(NumConstant.NUM_500);
        List<WipExtendIdentification> wipExtendIdentificationList = wipExtendIdentificationRepository.assemblyRelationshipPage(assemblyRelationshipQueryDTO);
        if (CollectionUtils.isEmpty(wipExtendIdentificationList)) {
            return wipExtendIdentificationList;
        }
        //设置绑定类型 子工序 工站名称
        this.setProcessNameAndWorkStationName(wipExtendIdentificationList);
        //设置创建人
        this.setEmpName(wipExtendIdentificationList);
        return wipExtendIdentificationList;
    }

    /**
     * 装配关系查询
     * @param assemblyRelationshipQueryDTO
     * @return
     * @throws Exception
     */
    @Override
    public Page<WipExtendIdentification> assemblyRelationshipQuery(AssemblyRelationshipQueryDTO assemblyRelationshipQueryDTO) throws Exception {
        this.parameterVerification(assemblyRelationshipQueryDTO);
        Page<WipExtendIdentification> page = new Page<>(assemblyRelationshipQueryDTO.getPage(), assemblyRelationshipQueryDTO.getRows());
        Page<AssemblyRelationshipQueryDTO> pageInfo = new Page<>(assemblyRelationshipQueryDTO.getPage(), assemblyRelationshipQueryDTO.getRows());
        pageInfo.setSearchCount(assemblyRelationshipQueryDTO.isSearchCount());
        pageInfo.setParams(assemblyRelationshipQueryDTO);
        List<WipExtendIdentification> wipExtendIdentifications = wipExtendIdentificationRepository.assemblyRelationshipQuery(pageInfo);
        page.setTotalPage(pageInfo.getTotalPage());
        page.setPageSize(pageInfo.getPageSize());
        page.setTotal(pageInfo.getTotal());
        page.setCurrent(pageInfo.getCurrent());
        page.setSearchCount(assemblyRelationshipQueryDTO.isSearchCount());
        if (CollectionUtils.isEmpty(wipExtendIdentifications)) {
            return page;
        }
        // 为辅料绑定查询时，需要点对点查询计划服务获取任务的料单信息
        if (Objects.equals(assemblyRelationshipQueryDTO.getFormType(), Constant.STR_4)) {
            this.getItemNoAndNameForAuxBinding(wipExtendIdentifications);
        }
        //判断子条码是否存在下级子条码
        this.setHasSubSn(assemblyRelationshipQueryDTO, wipExtendIdentifications);
        //设置绑定类型 子工序 工站名称
        this.setProcessNameAndWorkStationName(wipExtendIdentifications);
        //设置创建人
        this.setEmpName(wipExtendIdentifications);
        page.setRows(wipExtendIdentifications);
        return page;
    }

    private void getItemNoAndNameForAuxBinding(List<WipExtendIdentification> wipExtendIdentifications) {
        List<String> taskNoList = wipExtendIdentifications.stream().map(WipExtendIdentification::getTaskNo).filter(Objects::nonNull).collect(Collectors.toList());
        List<PsTask> psTaskList = PlanscheduleRemoteService.getPsTaskByTaskNoList(taskNoList);
        if (CollectionUtils.isEmpty(psTaskList)) {
            return;
        }
        Map<String, PsTask> psTaskMap = psTaskList.stream().collect(Collectors.toMap(PsTask::getTaskNo, k -> k, (k1, k2) -> k1));
        for (WipExtendIdentification wipExtendIdentification : wipExtendIdentifications) {
            PsTask psTask = psTaskMap.get(wipExtendIdentification.getTaskNo());
            if (psTask == null) {
                continue;
            }
            // 取任务号的料单代码和名称
            wipExtendIdentification.setFormItemNo(psTask.getItemNo());
            wipExtendIdentification.setFormItemName(psTask.getItemName());
        }
    }

    /**
     * 参数校验
     * @param assemblyRelationshipQueryDTO
     */
    public void parameterVerification(AssemblyRelationshipQueryDTO assemblyRelationshipQueryDTO) {
        if(StringUtils.isEmpty(assemblyRelationshipQueryDTO.getFormType())){
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,MessageId.PARMS_ERR);
        }
        if(StringUtils.isEmpty(assemblyRelationshipQueryDTO.getQueryType())){
            assemblyRelationshipQueryDTO.setQueryType(Constant.STR_1);
        }
        List<String> formSnList = assemblyRelationshipQueryDTO.getFormSnList();
        List<String> snList = assemblyRelationshipQueryDTO.getSnList();
        assemblyRelationshipQueryDTO.setFormSnList(this.verifySnSize(formSnList, MessageId.THE_NUMBER_OF_PRIMARY_BARCODES_CANNOT_EXCEED_100));
        assemblyRelationshipQueryDTO.setSnList(this.verifySnSize(snList, MessageId.THE_NUMBER_OF_SUB_BARCODES_CANNOT_EXCEED_100));
        if(assemblyRelationshipQueryDTO.getStartTime() == null && this.verifyTaskNoAndProdplanIdAndSn(assemblyRelationshipQueryDTO)){
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,MessageId.TASK_BATCH_BARCODES_CANNOT_ALL_BE_EMPTY_SEARCH);
        }
        if(assemblyRelationshipQueryDTO.getStartTime() != null && assemblyRelationshipQueryDTO.getEndTime() != null ){
            Date startTime = assemblyRelationshipQueryDTO.getStartTime();
            Date endTime = assemblyRelationshipQueryDTO.getEndTime();
            Calendar cal = Calendar.getInstance();
            cal.setTime(endTime);
            cal.add(Calendar.DATE,1);
            endTime = cal.getTime();
            if(null != startTime && startTime.getTime() > endTime.getTime()){
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.START_TIME_IS_LATER);
            }
            if(null != startTime && (endTime.getTime() - startTime.getTime()) > NumConstant.MILLIS_NUM_OF_DAY * NumConstant.LONG_THIRTY_ONE){
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.DURATION_IS_MORE_THAN_MONTH);
            }
        }
    }

    /**
     * 校验条码数量
     * @param snList
     * @param p
     */
    private List<String> verifySnSize(List<String> snList, String p) {
        if (!CollectionUtils.isEmpty(snList)) {
            snList = snList.stream().distinct().collect(Collectors.toList());
            if (snList.size() > NumConstant.NUM_100) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, p);
            }
        }
        return snList;
    }

    /**
     * 任务 批次 条码校验
     * @param assemblyRelationshipQueryDTO
     * @return
     */
    private boolean verifyTaskNoAndProdplanIdAndSn(AssemblyRelationshipQueryDTO assemblyRelationshipQueryDTO) {
        return StringUtils.isEmpty(assemblyRelationshipQueryDTO.getTaskNo()) && StringUtils.isEmpty(assemblyRelationshipQueryDTO.getProdPlanId())
                && CollectionUtils.isEmpty(assemblyRelationshipQueryDTO.getFormSnList()) && CollectionUtils.isEmpty(assemblyRelationshipQueryDTO.getSnList());
    }

    /**
     * 设置姓名
     * @param wipExtendIdentifications
     * @throws Exception
     */
    public void setEmpName(List<WipExtendIdentification> wipExtendIdentifications) throws Exception {
        //创建人
        List<String> empNoList = wipExtendIdentifications.stream().filter(e->StringUtils.isNotEmpty(e.getCreateBy())).map(e->e.getCreateBy()).distinct().collect(Collectors.toList());
        Map<String, HrmPersonInfoDTO> hrmPersonInfoDTOMap = centerfactoryRemoteService.getHrmPersonInfo(empNoList);
        for (WipExtendIdentification wipExtendIdentification : wipExtendIdentifications) {
            HrmPersonInfoDTO hrmPersonInfoDTO = hrmPersonInfoDTOMap.get(wipExtendIdentification.getCreateBy());
            wipExtendIdentification.setCreateByName(wipExtendIdentification.getCreateBy());
            if(hrmPersonInfoDTO != null){
                wipExtendIdentification.setCreateByName(hrmPersonInfoDTO.getEmpName()+wipExtendIdentification.getCreateBy());
            }
        }
    }

    /**
     * 设置工装 子工序 绑定类型
     * @param wipExtendIdentifications
     */
    public void setProcessNameAndWorkStationName(List<WipExtendIdentification> wipExtendIdentifications) throws Exception{
        List<String> processAndWorkStationList = wipExtendIdentifications.stream().flatMap(p -> Stream.of(p.getProcessCode(),p.getWorkStation())).distinct().collect(Collectors.toList());
        List<BSProcessDTO> bsProcessDTOS = wipEntityScanInfoService.getProcessInfo(null, null, null, null, SqlUtils.convertStrCollectionToSqlType(processAndWorkStationList));
        //按主条码分组
        Map<String, BSProcessDTO> wipExtendIdentificationMap =CollectionUtils.isEmpty(bsProcessDTOS)?new HashMap<>():bsProcessDTOS.stream().collect(Collectors.toMap(k -> k.getProcessCode(), v -> v, (oldValue, newValue) -> newValue));

        List<SysLookupTypesDTO> sysLookupTypesDTOS = BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_BIND_TYPE);

        for (WipExtendIdentification wipExtendIdentification : wipExtendIdentifications) {
            BSProcessDTO bsProcessDTO = wipExtendIdentificationMap.get(wipExtendIdentification.getProcessCode());
            if(bsProcessDTO != null){
                wipExtendIdentification.setFormProcessName(bsProcessDTO.getProcessName());
            }
            bsProcessDTO = wipExtendIdentificationMap.get(wipExtendIdentification.getWorkStation());
            if(bsProcessDTO != null){
                wipExtendIdentification.setWorkStationName(bsProcessDTO.getProcessName());
            }
            wipExtendIdentification.setFormTypeName(getFormType(sysLookupTypesDTOS, wipExtendIdentification.getFormType()));
        }
    }

    /**
     * 设置是否有子条码
     * @param assemblyRelationshipQueryDTO
     * @param wipExtendIdentifications
     */
    public void setHasSubSn(AssemblyRelationshipQueryDTO assemblyRelationshipQueryDTO, List<WipExtendIdentification> wipExtendIdentifications) {
        if(!assemblyRelationshipQueryDTO.isNeedHasSubSn()){
            return;
        }
        if (Constant.STR_NUMBER_ZERO.equals(assemblyRelationshipQueryDTO.getQueryType())) {
            // queryType为0表示前端选择的向上展开的查询方式,将查出的装配关系中的主条码作为子条码查子级记录,否则保持原逻辑
            setHasSubByUpQuery(assemblyRelationshipQueryDTO, wipExtendIdentifications);
        } else {
            // queryType为1表示前端选择的向下展开的查询方式,将查出的装配关系中的子条码作为主条码查子级记录
            setHasSubByDowmQuery(assemblyRelationshipQueryDTO, wipExtendIdentifications);
        }
    }

    private void setHasSubByUpQuery(AssemblyRelationshipQueryDTO assemblyRelationshipQueryDTO, List<WipExtendIdentification> wipExtendIdentifications) {
        //主条码
        List<String> formSnList = wipExtendIdentifications.stream().filter(e->StringUtils.isNotEmpty(e.getFormSn())).map(e->e.getFormSn()).distinct().collect(Collectors.toList());
        List<WipExtendIdentification> wipExtendIdentificationList = new ArrayList<>();
        for (List<String> tempFormSnList : CommonUtils.splitList(formSnList, Constant.INT_100)) {
            List<WipExtendIdentification> tempList = wipExtendIdentificationRepository.queryBindingQtyBySnList(tempFormSnList, assemblyRelationshipQueryDTO.getFormType());
            if(!CollectionUtils.isEmpty(tempList)){
                wipExtendIdentificationList.addAll(tempList);
            }
        }
        //按子条码分组
        Map<String, WipExtendIdentification> wipExtendIdentificationMap = wipExtendIdentificationList.stream().collect(
                Collectors.toMap(k -> k.getSn(), v -> v, (oldValue, newValue) -> newValue));
        for (WipExtendIdentification wipExtendIdentification : wipExtendIdentifications) {
            WipExtendIdentification wip = wipExtendIdentificationMap.get(wipExtendIdentification.getFormSn());
            wipExtendIdentification.setHasChildren(false);
            if(wip != null){
                wipExtendIdentification.setHasChildren(true);
            }
        }
    }

    private void setHasSubByDowmQuery(AssemblyRelationshipQueryDTO assemblyRelationshipQueryDTO, List<WipExtendIdentification> wipExtendIdentifications) {
        //子条码
        List<String> subSnList = wipExtendIdentifications.stream().filter(e->StringUtils.isNotEmpty(e.getSn())).map(e->e.getSn()).distinct().collect(Collectors.toList());
        List<WipExtendIdentification> wipExtendIdentificationList = new ArrayList<>();
        for (List<String> tempSnList : CommonUtils.splitList(subSnList, Constant.INT_100)) {
            List<WipExtendIdentification> tempList = wipExtendIdentificationRepository.queryBindingQtyByFormSnList(tempSnList, assemblyRelationshipQueryDTO.getFormType());
            if(!CollectionUtils.isEmpty(tempList)){
                wipExtendIdentificationList.addAll(tempList);
            }
        }
        //按主条码分组
        Map<String, WipExtendIdentification> wipExtendIdentificationMap = wipExtendIdentificationList.stream().collect(
                Collectors.toMap(k -> k.getFormSn(), v -> v, (oldValue, newValue) -> newValue));
        for (WipExtendIdentification wipExtendIdentification : wipExtendIdentifications) {
            WipExtendIdentification wip = wipExtendIdentificationMap.get(wipExtendIdentification.getSn());
            wipExtendIdentification.setHasChildren(false);
            if(wip != null){
                wipExtendIdentification.setHasChildren(true);
            }
        }
    }

    @Override
    public List<WipExtendIdentification> queryBindRelDataBatch(WipExtendIdentificationDTO conditions) throws Exception {
        List<WipExtendIdentification> list = wipExtendIdentificationRepository.queryBindRelDataBatch(conditions);
        if (CollectionUtils.isEmpty(list)) {
            return new LinkedList<>();
        }
        this.transforResult(list, BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_BIND_TYPE));
        return list;
    }

    /**
     * 关联wip_info获取分页查询数据
     *
     * @param condition
     * @return List<WipExtendIdentificationDTO>
     * <AUTHOR>
     */
    @Override
    public PageRows<WipExtendIdentificationDTO> queryListLinkWipInfoPage(WipExtendIdentificationDTO condition) {
        PageRows<WipExtendIdentificationDTO> page = new PageRows<>();
        //对前台传来的主条码集合进行分割处理
        String formSns = condition.getFormSn().replaceAll(" ", ",");
        condition.setFormSn(formSns);
        //对前台传来的子条码集合进行分割处理
        String sns = condition.getSn().replaceAll(" ", ",");
        condition.setSn(sns);
        long total = wipExtendIdentificationRepository.queryListLinkWipInfoCount(condition);
        page.setTotal(total);
        condition.setStartRow((condition.getPage() - Constant.INT_1)
                * condition.getRows() + Constant.INT_1);
        condition.setEndRow(condition.getPage() * condition.getRows());
        condition.setFormType(NumConstant.STR_THREE);
        List<WipExtendIdentificationDTO> list = wipExtendIdentificationRepository.queryListLinkWipInfoPage(condition);
        page.setRows(list);
        return page;
    }

    /**
     * 组合解绑
     *
     * @param list
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    public void assemblyUnbind(String empNo, List<WipExtendIdentification> list) {
        List<String> wipExtendIds = new ArrayList();
        Set<String> assSns = new HashSet<>();
        Set<String> secAssSns = new HashSet();
        List<AssemUnbindHistoryInfo> hisList = new ArrayList<>();
        for (WipExtendIdentification wipExtend : list) {
            wipExtend.setEnabledFlag(Constant.ENABLED_FLAG_FALSE);
            wipExtend.setLastUpdatedBy(empNo);
            wipExtendIds.add(wipExtend.getIdentiId());

            // 包含主卡料单则为二次装配
            if (StringUtils.isBlank(wipExtend.getMainProductCode())) {
                assSns.add(wipExtend.getFormSn());
            } else {
                secAssSns.add(wipExtend.getFormSn());
            }
            hisList.add(getHisInfo(wipExtend));
        }

        // 1.将wip_extend_identification的ENABLED_FLAG批量替换为‘N'
        this.batchInsertOptRecordByIdsAndEmpNo(empNo, wipExtendIds);
        wipExtendIdentificationRepository.disableByIds(empNo, wipExtendIds);
        // 2.将wip_info的ASSEMBlE_FLAG装配完成标志修改为’N'
        if (!CollectionUtils.isEmpty(assSns)) {
            psWipInfoRepository.updateAssembleFlagBySns(assSns);
        }
        // 3.将wip_info的SEC_ASSEMBlE_FLAG二次装配完成标志修改为’N'
        if (!CollectionUtils.isEmpty(secAssSns)) {
            psWipInfoRepository.updateSecAssembleFlagBySns(secAssSns);
        }
        // 4.插入解绑日志
        assemUnbindHistoryInfoRepository.insertBatch(hisList);
    }

    /**
     * 新增操作记录
     * @param empNo
     * @param wipExtendIds
     */
    public void batchInsertOptRecordByIdsAndEmpNo(String empNo, List<String> wipExtendIds) {
        List<WipExtendIdentification> wipExtendIdentificationList = this.getWipExtendByIds(wipExtendIds);
        wipExtendIdentificationList.forEach(p->{p.setEmpNo(empNo);});
        this.batchInsertOptRecord(NumConstant.STRING_TWO,wipExtendIdentificationList);
    }

    private AssemUnbindHistoryInfo getHisInfo(WipExtendIdentification wipExtend) {
        AssemUnbindHistoryInfo hisInfo = new AssemUnbindHistoryInfo();
        hisInfo.setFormSn(wipExtend.getFormSn());
        hisInfo.setFormItemNo(wipExtend.getFormItemNo());
        hisInfo.setSn(wipExtend.getSn());
        hisInfo.setItemNo(wipExtend.getItemNo());
        hisInfo.setUnbindPerson(wipExtend.getLastUpdatedBy());
        return hisInfo;
    }

    /**
     * 导出组装关系查询数据
     */
    @Override
    public void export(HttpServletResponse response, WipExtendIdentificationDTO conditions) throws Exception {
        ServiceData serviceData = new ServiceData();
        //step1 校验工厂ID  及 传入参数
        serviceData.setCode(validParam(conditions, conditions.getFactoryId() + "", true));
        if (serviceData.getCode().getCode().equals(RetCode.SUCCESS_CODE)) {
            //step2 获取数据   关联wip_info中获取 物料代码、物料名称
            List<WipExtendIdentification> wipExtendIdentifications = wipExtendIdentificationRepository.getListLinkWipInfoList(conditions);
            //step3 翻译子工序、绑定类型, 为了减少远程调用， 采用in process_code
            transforResult(wipExtendIdentifications, BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_BIND_TYPE));
            for (WipExtendIdentification dto : wipExtendIdentifications) {
                if (StringHelper.isNotEmpty(dto.getLastUpdatedDate())) {
                    dto.setLastUpdatedDateStr(DateUtil.convertDateToString(dto.getLastUpdatedDate(), DateUtil.DATE_FORMATE_FULL));
                }
            }
            //导出
            ExcelCommonUtils.export(response, wipExtendIdentifications, Constant.ASSEMBLER_RELA_MODEL_NAME, Constant.ASSEMBLER_RELA_FILE_NAME);
        }
    }

    /**
     * 封装分页数据
     *
     * @param wipExtendIdentifications
     * @param total
     * @param current
     * @return
     */
    private PageRows<WipExtendIdentification> packageWipExtendIdentification(List<WipExtendIdentification> wipExtendIdentifications, long total, long current) {
        PageRows<WipExtendIdentification> page = new PageRows<WipExtendIdentification>();
        page.setCurrent(current);
        page.setTotal(total);
        page.setRows(wipExtendIdentifications);
        return page;
    }

    /**
     * 翻译子工序代码
     */
    private void transforResult(List<WipExtendIdentification> wipExtendIdentifications, List<SysLookupTypesDTO> sysLookupTypesDTOS) throws Exception {
        //step3 翻译子工序, 为了减少远程调用， 采用in process_code
        List<BSProcessDTO> bsProcessDTOS = wipEntityScanInfoService.getProcessInfo(null, null, null, null, getInProcessCode(wipExtendIdentifications));
        for (WipExtendIdentification wipExtendIdentification : wipExtendIdentifications) {
            wipExtendIdentification.setProcessName(getProcessName(bsProcessDTOS, wipExtendIdentification.getProcessCode()));
            wipExtendIdentification.setFormTypeName(getFormType(sysLookupTypesDTOS, wipExtendIdentification.getFormType()));
        }
    }

    /**
     * 获取绑定类型
     *
     * @param sysLookupTypesDTOS
     * @param formType
     * @return
     */
    private String getFormType(List<SysLookupTypesDTO> sysLookupTypesDTOS, String formType) {
        for (SysLookupTypesDTO sysLookupValuesDTO : sysLookupTypesDTOS) {
            if (!StringUtils.isEmpty(sysLookupValuesDTO.getLookupMeaning()) && !StringUtils.isEmpty(formType)
                    && formType.equals(sysLookupValuesDTO.getLookupMeaning())) {
                return sysLookupValuesDTO.getDescriptionChinV();
            }
        }
        return "";
    }

    /**
     * 获取所有的子工序
     *
     * @param wipExtendIdentifications
     * @return
     */
    private String getInProcessCode(List<WipExtendIdentification> wipExtendIdentifications) {
        List<String> processCodes = new ArrayList<String>();
        for (WipExtendIdentification wipExtendIdentification : wipExtendIdentifications) {
            if (!processCodes.contains(wipExtendIdentification.getProcessCode())) {
                processCodes.add(wipExtendIdentification.getProcessCode());
            }
        }
        return CollectionUtils.isEmpty(processCodes) ? "" : Constant.SINGLE_QUOTE +
                org.apache.commons.lang3.StringUtils.join(processCodes, "','") + Constant.SINGLE_QUOTE;
    }

    private RetCode validParam(WipExtendIdentificationDTO conditions, String factoryId) {
        return validParam(conditions, factoryId, MpConstant.FALSE);
    }

    /**
     * 校验参数
     *
     * @param conditions
     * @param factoryId
     * @return
     */
    private RetCode validParam(WipExtendIdentificationDTO conditions, String factoryId, boolean isLoading) {
        RetCode retCode = CommonUtils.validFactoryId(factoryId);
        if (retCode.getCode().equals(RetCode.SUCCESS_CODE)) {
            retCode = validConditions(conditions, isLoading);
            if (RetCode.SUCCESS_CODE.equals(retCode.getCode())) {
                conditions.setFactoryId(new BigDecimal(factoryId));
            }
        }
        return retCode;
    }

    /**
     * 校验传入的参数  校验规则， 绑定类型不能为空  且传入参数不能小于2个
     *
     * @param conditions
     * @return
     */
    public RetCode validConditions(WipExtendIdentificationDTO conditions, boolean isLoading) {
        RetCode retCode = new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID);
        if (ObjectUtils.isEmpty(conditions)) {
            //  2019/8/2  详细错误提示  双语
            return new RetCode(RetCode.VALIDATIONERROR_CODE, MessageId.CONDITION_NOT_FOUND);
        }
        if (StringUtils.isEmpty(conditions.getFormType())) {
            //  2019/8/2  详细错误提示 双语
            return new RetCode(RetCode.VALIDATIONERROR_CODE, MessageId.FORM_TYPE_NOT_FOUND);
        }
        // 除了绑定类型外 必须还有最少一个条件不为空
        if (CommonUtils.isAllNull(getValidValue(conditions))) {
            //  2019/8/2  详细错误提示 双语
            return new RetCode(RetCode.VALIDATIONERROR_CODE, MessageId.MUST_HAS_OTHER_CONDITION);
        }
        //若是导出数据 则需要添加时间校验
        boolean validCreateTime = StringUtils.isEmpty(conditions.getProdPlanId()) && !conditions.getShowRes() && isLoading && !validCreateTime(conditions.getStartTime(), conditions.getEndTime());
        if (validCreateTime) {
            //  2019/8/2  详细提示  导出时间不能大于两个月
            return new RetCode(RetCode.VALIDATIONERROR_CODE, MessageId.EXPORT_TIME_THAN_TWO_MONTH);
        }
        if (!isLoading) {
            handlePageParam(conditions);
        }
        return retCode;
    }

    /**
     * 处理分页参数
     *
     * @param conditions
     */
    private void handlePageParam(WipExtendIdentificationDTO conditions) {
        if (StringHelper.isNotEmpty(conditions.getPage()) && StringHelper.isNotEmpty(conditions.getRows())
                && 0L != conditions.getPage() && 0L != conditions.getRows()) {
            conditions.setStartRow((conditions.getPage() - 1) * conditions.getRows() + 1);
            conditions.setEndRow(conditions.getPage() * conditions.getRows());
        }
    }

    /**
     * 判断时间差是否小于两个自然月
     *
     * @param startTime
     * @param endTime
     * @return
     */
    private boolean validCreateTime(Date startTime, Date endTime) {
        if (ObjectUtils.isEmpty(startTime)) {
            return MpConstant.FALSE;
        }
        if (ObjectUtils.isEmpty(endTime)) {
            endTime = new Date();
        }
        Calendar nowTime = Calendar.getInstance();
        nowTime.setTime(endTime);
        nowTime.add(Calendar.MONTH, Constant.EXPORT_DATA_INTERVAL_MONTH);
        return nowTime.getTime().compareTo(startTime) > 0 ? false : true;
    }

    /**
     * 获取需要校验的参数
     *
     * @param conditions
     * @return
     */
    private Object[] getValidValue(WipExtendIdentificationDTO conditions) {
        return new Object[]{conditions.getProdPlanId(), conditions.getTaskNo(),
                conditions.getProcessCode(), conditions.getFormSn(),
                conditions.getSn(), conditions.getCreateBy(), conditions.getStartTime(), conditions.getEndTime()};
    }

    /**
     * 翻译子工序编码
     *
     * @param bsProcessDTOS
     * @param processCode
     * @return
     */
    private String getProcessName(List<BSProcessDTO> bsProcessDTOS, String processCode) {
        if (!CollectionUtils.isEmpty(bsProcessDTOS)) {
            for (BSProcessDTO bsProcessDTO : bsProcessDTOS) {
                if (!StringUtils.isEmpty(bsProcessDTO.getProcessCode()) && bsProcessDTO.getProcessCode().equals(processCode)) {
                    return bsProcessDTO.getProcessName();
                }
            }
        }
        return "";
    }

    /**
     * 装配关系查询
     *
     * @param dto
     * @return
     * @throws Exception
     */
    @Override
    public ServiceData getWsmAssembleLinesDTOList(WsmAssembleLinesDTO dto) throws Exception {
        ServiceData ret = new ServiceData();
        ret.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, RetCode.BUSINESSERROR_MSGID));

        String mainItemBarcode = dto.getMainItemBarcode();
        String subItemBarcode = dto.getSubItemBarcode();
        if (StringUtils.isEmpty(mainItemBarcode) && StringUtils.isEmpty(subItemBarcode)) {
            ret.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.PARAMS_IS_NULL));
            return ret;
        }
        Map<String, String> map = new HashMap<>(16);
        map.put(BusinessConstant.MAIN_ITEM_BARCODE, mainItemBarcode);
        map.put(BusinessConstant.SUB_ITEM_BARCODE, subItemBarcode);
        map.put(BusinessConstant.PAGE_STRING, dto.getPageString());
        map.put(BusinessConstant.ROWS_STRING, dto.getRowsString());
        // 查询装配关系列表
        List<WsmAssembleLinesDTO> wsmAssembleLinesDTOList = this.getWsmAssembleLinesDTOPage(map);
        LOG.info("装配关系查询，列表：{}", wsmAssembleLinesDTOList);
        if (CollectionUtils.isEmpty(wsmAssembleLinesDTOList)) {
            if (StringUtils.isEmpty(subItemBarcode)) {
                String[] params = new String[]{mainItemBarcode};
                ret.setCode(new com.zte.springbootframe.common.model.RetCode(RetCode.BUSINESSERROR_CODE, MessageId.FORM_SN_NO_BINDING_RELATION, params));
                return ret;
            }
            if (StringUtils.isEmpty(mainItemBarcode)) {
                String[] params = new String[]{subItemBarcode};
                ret.setCode(new com.zte.springbootframe.common.model.RetCode(RetCode.BUSINESSERROR_CODE, MessageId.SN_NO_BINDING_RELATION, params));
                return ret;
            }
            String[] params = new String[]{mainItemBarcode, subItemBarcode};
            ret.setCode(new com.zte.springbootframe.common.model.RetCode(RetCode.BUSINESSERROR_CODE, MessageId.FORM_SN_AND_SN_NO_BINDING_RELATION, params));
            return ret;
        }
        // 获取装配关系总数
        long total = this.getWsmAssembleLinesDTOCount(map);
        LOG.info("装配关系查询，总数：{}", total);
        Map<String, Object> objectMap = new HashMap<>(16);
        List<String> formSnList = wsmAssembleLinesDTOList.stream().map(i -> i.getMainItemBarcode()).collect(Collectors.toList());
        objectMap.put(BusinessConstant.FORM_SN_LIST, formSnList);
        // 获取主条码对应的条码附属表数据
        List<WipExtendIdentification> wipExtendIdentificationList = wipExtendIdentificationRepository.getList(objectMap);
        LOG.info("装配关系查询，条码附属表：{}", wipExtendIdentificationList);
        if (CollectionUtils.isEmpty(wipExtendIdentificationList)) {
            ret.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.FORM_SN_NOT_IN_WIP_EXTEND));
            return ret;
        }
        // 查询processName
        this.addProcessMame(wipExtendIdentificationList);
        for (WsmAssembleLinesDTO wsm : wsmAssembleLinesDTOList) {
            for (WipExtendIdentification wip : wipExtendIdentificationList) {
                if (wsm.getMainItemBarcode().equals(wip.getFormSn())) {
                    wsm.setTaskNo(wip.getTaskNo());
                    wsm.setProdplanId(wip.getProdPlanId());
                    wsm.setProcessCode(wip.getProcessCode());
                    wsm.setProcessName(wip.getProcessName());
                    wsm.setSubLastUpdatedBy(wip.getLastUpdatedBy());
                }
                break;
            }
        }
        // 分页属性
        PageRows<WsmAssembleLinesDTO> pageRows = new PageRows<WsmAssembleLinesDTO>();
        pageRows.setCurrent(Long.parseLong(dto.getPageString()));
        pageRows.setTotal(total);
        pageRows.setRows(wsmAssembleLinesDTOList);
        ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        ret.setBo(pageRows);
        return ret;
    }

    private void addProcessMame(List<WipExtendIdentification> wipExtendIdentificationList) throws Exception {
        List<BSProcessDTO> bsProcessDTOS = wipEntityScanInfoService.getProcessInfo(null, null,
                null, null, this.getInProcessCode(wipExtendIdentificationList));
        LOG.info("装配关系查询，工序表：{}", bsProcessDTOS);
        for (WipExtendIdentification wip : wipExtendIdentificationList) {
            wip.setProcessName(this.getProcessName(bsProcessDTOS, wip.getProcessCode()));
        }
    }

    /**
     * 远程调用生产服务
     */
    private List<WsmAssembleLinesDTO> getWsmAssembleLinesDTOPage(Map<String, String> map) throws Exception {
        Map<String, String> headParams = MESHttpHelper.getHttpRequestHeader();
        String url = constantInterface.getUrl(InterfaceEnum.dataWbSysWsmAssembleLinesDTOPage);
        LOG.info("远程调用同步的URL :{}", url);
        String msg = HttpRemoteService.remoteExe(InterfaceEnum.dataWbSysWsmAssembleLinesDTOPage, map, headParams, url);
        if (org.apache.commons.lang3.StringUtils.isBlank(msg)) {
            throw new Exception(CommonUtils.getLmbMessage(MessageId.SELECT_ERROR));
        }
        JsonNode json = JacksonJsonConverUtil.getMapperInstance().readTree(msg);
        LOG.info("远程调用同步的结果 :{}", json);
        String retCode = json.get(MpConstant.JSON_CODE).get(MpConstant.JSON_CODE).asText();
        String bo = json.get(MpConstant.JSON_BO).toString();
        if (!RetCode.SUCCESS_CODE.equalsIgnoreCase(retCode)) {
            throw new Exception(bo);
        }
        return JsonConvertUtil.jsonToBean(bo, List.class, WsmAssembleLinesDTO.class);
    }

    private Long getWsmAssembleLinesDTOCount(Map<String, String> map) throws Exception {
        Map<String, String> headParams = MESHttpHelper.getHttpRequestHeader();
        String url = constantInterface.getUrl(InterfaceEnum.dataWbSysWsmAssembleLinesDTOCount);
        LOG.info("远程调用同步的URL :{}", url);
        String msg = HttpRemoteService.remoteExe(InterfaceEnum.dataWbSysWsmAssembleLinesDTOCount, map, headParams, url);
        if (org.apache.commons.lang3.StringUtils.isBlank(msg)) {
            throw new Exception(CommonUtils.getLmbMessage(MessageId.SELECT_ERROR));
        }
        JsonNode json = JacksonJsonConverUtil.getMapperInstance().readTree(msg);
        LOG.info("远程调用同步的结果 :{}", json);
        String retCode = json.get(MpConstant.JSON_CODE).get(MpConstant.JSON_CODE).asText();
        String bo = json.get(MpConstant.JSON_BO).toString();
        if (!RetCode.SUCCESS_CODE.equalsIgnoreCase(retCode)) {
            throw new Exception(bo);
        }
        return Long.parseLong(bo);
    }

    @Override
    public ServiceData getFormSnBindingList(WipExtendIdentificationDTO dto) throws Exception {
        ServiceData ret = new ServiceData();
        ret.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, RetCode.BUSINESSERROR_MSGID));
        // 防止全表扫描
        String formSn = dto.getFormSn();
        if (StringHelper.isEmpty(formSn)) {
            ret.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.SN_IS_NOT_EXISTS));
            return ret;
        }
        //主条码sn查询在wip_info表中是否存在
        Map<String, Object> infoMap = new HashMap<>();
        infoMap.put("sn", dto.getFormSn());
        List<PsWipInfo> wipInfoList = psWipInfoRepository.getList(infoMap);
        if (CollectionUtils.isEmpty(wipInfoList)) {
            ret.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.SN_IS_NOT_EXISTS));
            return ret;
        }
        //是否是该批次的主条码
        if (!wipInfoList.get(NumConstant.NUM_ZERO).getAttribute1().equals(dto.getProdPlanId())) {
            ret.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.MAIN_SN_NOT_BELONGS_TO_THIS_PRODPLANID));
            return ret;
        }
        // 获取待绑定清单
        List<ProdBindingSettingDTO> toBindingList = getProdBindingList(dto, formSn, dto.getProdPlanId());
        if (null == toBindingList || CollectionUtils.isEmpty(toBindingList)) {
            ret.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.BOUND_LIST_IS_NULL));
            return ret;
        }
        // 通过将绑定结果中的物料代码数量与待绑定清单中物料代码数量进行对比，判断是否绑定完成
        PsWipInfo psWipInfo = wipInfoList.get(NumConstant.NUM_ZERO);
        //装配已完成报错  未完成校验是否锁定
        if (assemblyIsComplete(psWipInfo, toBindingList)) {
            ret.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.MAIN_SN_IS_BINDED_COMPLETED));
            ret.setBo(toBindingList);
            return ret;
        } else {
            //校验条码是否锁定
            verifySnIsLockMain(psWipInfo);
        }
        ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        ret.setBo(toBindingList);
        return ret;
    }

    /**
    * @Description: 新增根据子工序进行查询
    * @Param: [dto, formSn]
    * @return: java.util.List<com.zte.interfaces.dto.ProdBindingSettingDTO>
    * @Author: Saber[10307315]
    * @Date: 2023/7/5 下午2:47
    */
    public List<ProdBindingSettingDTO> getProdBindingList
    (WipExtendIdentificationDTO dto, String formSn, String prodPlanId) throws Exception {
        if (StringUtils.isEmpty(dto.getProcessCode())) {
            return getBindList(formSn, dto.getItemNo(), Constant.EMPTY_STRING, prodPlanId);
        } else {
            ProdBindingSettingDTO params = new ProdBindingSettingDTO();
            params.setMainSn(formSn);
            params.setProductCode(dto.getItemNo());
            if (StringUtils.isNotEmpty(prodPlanId)) {
                params.setProdPlanId(prodPlanId);
            }
            params.setProcessCode(dto.getProcessCode());
            ServiceData bindingListServiceData = prodBindingSettingService.getProdBindingSettingList(params);
            return null == bindingListServiceData ? null : (List<ProdBindingSettingDTO>) bindingListServiceData.getBo();
        }

    }

    /**
     * 校验条码是否锁定
     *
     * @param psWipInfo
     * @throws Exception
     */
    public void verifySnIsLock(PsWipInfo psWipInfo) throws Exception {
        //获取条码对应工艺子工序
        List<String> routeIdList = new ArrayList<>();
        PsWorkOrderBasic psWorkOrderBasic = PlanscheduleRemoteService.findWorkOrder(psWipInfo.getWorkOrderNo());
        if(!Objects.isNull(psWorkOrderBasic)){
            routeIdList.add(psWorkOrderBasic.getRouteId());
        }
        List<CtRouteDetailDTO> ctRouteDetailDTOList = CrafttechRemoteService.getCtRouteDetailByRouteIds(routeIdList);
        if (CollectionUtils.isEmpty(ctRouteDetailDTOList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.NOT_FOUND_SN_ROUTE, new Object[]{psWipInfo.getSn()});
        }
        List<String> processCodeList = ctRouteDetailDTOList.stream().map(e -> e.getNextProcess()).distinct().collect(Collectors.toList());
        BarcodeLockDetailEntityDTO barcodeLockDetailEntityDTO = new BarcodeLockDetailEntityDTO();
        List<String> snList = new ArrayList<>();
        snList.add(psWipInfo.getSn());
        barcodeLockDetailEntityDTO.setProcessCodeList(processCodeList);
        barcodeLockDetailEntityDTO.setSnList(snList);
        flowControlCommonService.snLockControl(barcodeLockDetailEntityDTO);
    }

    /**
     * 校验主条码是否锁定 根据条码、批次 当前工序
     *
     * @param psWipInfo
     * @throws Exception
     */
    private void verifySnIsLockMain(PsWipInfo psWipInfo) throws Exception {
        BarcodeLockDetailEntityDTO barcodeLockDetailEntityDTO = new BarcodeLockDetailEntityDTO();
        List<String> processCodeList = new ArrayList<>();
        processCodeList.add(psWipInfo.getCurrProcessCode());
        List<String> snList = new ArrayList<>();
        snList.add(psWipInfo.getSn());
        barcodeLockDetailEntityDTO.setProcessCodeList(processCodeList);
        barcodeLockDetailEntityDTO.setSnList(snList);
        flowControlCommonService.snLockControl(barcodeLockDetailEntityDTO);
    }

    /**
     * 判断是否绑定完成
     *
     * @param psWipInfo
     * @param bindingList
     * @return
     */
    private boolean assemblyIsComplete(PsWipInfo psWipInfo, List<ProdBindingSettingDTO> bindingList) {
        //存在一次装配物料校验一次装配物料是否都绑定完成
        List<ProdBindingSettingDTO> oneAssembleItem = bindingList.stream().filter(e -> StringUtils.isEmpty(e.getMainProductCode())).collect(Collectors.toList());
        List<ProdBindingSettingDTO> secAssembleItem = bindingList.stream().filter(e -> !StringUtils.isEmpty(e.getMainProductCode())).collect(Collectors.toList());
        //存在一次装配物料，并且一次装配标识不为Y
        if (!CollectionUtils.isEmpty(oneAssembleItem) && !StringUtils.equals(psWipInfo.getAssembleFlag(), Constant.FLAG_Y)) {
            return false;
        }
        //存在二次装配物料，并且二次装配标识不为Y
        if (!CollectionUtils.isEmpty(secAssembleItem) && !StringUtils.equals(psWipInfo.getSecAssembleFlag(), Constant.FLAG_Y)) {
            return false;
        }
        return true;
    }

    //是否单个绑定完成
    private boolean isFinish(List<ProdBindingSettingDTO> toBindingList, String itemCode) {
        if (CollectionUtils.isEmpty(toBindingList)) {
            return false;
        }
        for (ProdBindingSettingDTO prodBindingSettingDTO : toBindingList) {
            //如果有一个物料未完成则待绑定清单未完成
            if (null != prodBindingSettingDTO && null != prodBindingSettingDTO.getUsageCount()
                    && StringHelper.isNotEmpty(itemCode) && itemCode.trim().equals(prodBindingSettingDTO.getItemCode())
                    && prodBindingSettingDTO.getUsageCount().compareTo(prodBindingSettingDTO.getBoundNo()) <= Constant.INT_0) {
                return true;
            }
        }
        return false;
    }

    // 查询主条码是否已绑定完成（主条码、主条码对应的物料代码,批次）
    public boolean isFinish(String sn, String productCode, String prodPlanId) {
        //根据指令料单代码查询，排除在反向维护表中同批次的物料代码,得到需要绑定的物料代码清单
        ProdBindingSettingDTO prodInfo = new ProdBindingSettingDTO();
        prodInfo.setProductCode(productCode);
        prodInfo.setProdPlanId(prodPlanId);
        prodInfo.setBindType(1);
        List<ProdBindingSettingDTO> toBindingList = prodBindingSettingRepository.getProdBindingSettingList(prodInfo);
        if (CollectionUtils.isEmpty(toBindingList)) {
            return true;
        }
        //获取该主条码下所有子条码，并根据物料代码进行统计
        List<WipExtendIdentification> allChildSnByItemcodeList = wipExtendIdentificationRepository.getAllChildSnNoByItemcode(sn);
        if (CollectionUtils.isEmpty(allChildSnByItemcodeList)) {
            for (ProdBindingSettingDTO prod : toBindingList) {
                if (prod.getUsageCount().intValue() != Constant.INT_0) {
                    return false;
                }
            }
            return true;
        }
        Map<String, BigDecimal> itemNoMap = new HashMap<>();
        for (WipExtendIdentification wip : allChildSnByItemcodeList) {
            itemNoMap.put(wip.getItemNo(), wip.getQty());
        }
        //将待绑定清单与已绑定物料代码清单进行对比，判断是否已完成
        if (compareComplete(toBindingList, itemNoMap)) {
            return false;
        }
        return true;
    }


    private boolean compareComplete(List<ProdBindingSettingDTO> toBindingList, Map<String, BigDecimal> itemNoMap) {
        for (ProdBindingSettingDTO bind : toBindingList) {
            if (bind.getUsageCount() == null) {
                bind.setUsageCount(BigDecimal.ZERO);
            }
            BigDecimal itemCode = itemNoMap.get(bind.getItemCode());
            if (itemCode == null || (bind.getUsageCount().intValue() != itemCode.intValue())) {
                return true;
            }
        }
        return false;
    }


    /**
     * 查询数据字典子母卡绑定是否按批次管控信息
     *
     * @param subSnPsWipInfo
     * @param mainSnPsWipInfo
     * @throws Exception
     */
    public void verifyControlByProdPlanId(PsWipInfo subSnPsWipInfo, PsWipInfo mainSnPsWipInfo) throws Exception {
        // 查询数据字典子母卡绑定是否按批次管控信息
        Map<String, Object> sysLookUpMap = new HashMap<>();
        sysLookUpMap.put("lookupType", Constant.SYS_LOOK_TYPE_1252);
        List<SysLookupTypesDTO> sysLookupTypesDTOList = BasicsettingRemoteService.getSysLookUpValue(sysLookUpMap);
        if (CollectionUtils.isEmpty(sysLookupTypesDTOList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SYS_LOOKUPTYPE_1252_IS_NULL);
        }
        // 如果按批次管控开关为Y则判断子条码所属批次和主条码批次存在绑定关系（子条码所属批次在ps_task parts_planno等于主条码的计划跟踪单号）
        if (!StringUtils.equals(sysLookupTypesDTOList.get(NumConstant.NUM_ZERO).getLookupMeaning(), Constant.FLAG_Y)) {
            return;
        }
        //子条码任务截取掉后2位再和主任务比
        String subProdPlanId = subSnPsWipInfo.getAttribute1();
        String mainProdPlanId = mainSnPsWipInfo.getAttribute1();
        List<String> prodPlanIdList = new ArrayList<>();
        prodPlanIdList.add(subProdPlanId);
        prodPlanIdList.add(mainProdPlanId);
        Map<String, Object> map = new HashMap<>();
        map.put("inProdplanId", SqlUtils.convertStrCollectionToSqlType(prodPlanIdList));
        List<PsTask> psTaskList = PlanscheduleRemoteService.getPsTask(map);
        if (CollectionUtils.isEmpty(psTaskList) || psTaskList.size() < NumConstant.NUM_TWO) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.FAILED_TO_GET_TASK_INFO_BY_PRODPLANID, new Object[]{StringUtils.join(prodPlanIdList, Constant.COMMA)});
        }
        Map<String, PsTask> psTaskMap = psTaskList.stream().collect(Collectors.toMap(e -> e.getProdplanId(), a -> a, (k1, k2) -> k1));
        if (psTaskMap == null) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.FAILED_TO_GET_TASK_INFO_BY_PRODPLANID, new Object[]{StringUtils.join(prodPlanIdList, Constant.COMMA)});
        }
        PsTask tempMainProdPlanIdDto = psTaskMap.get(mainProdPlanId);
        PsTask tempSubProdPlanIdDto = psTaskMap.get(subProdPlanId);
        String mainTaskNo = tempMainProdPlanIdDto == null ? StringUtils.EMPTY : tempMainProdPlanIdDto.getTaskNo();
        String subTaskNo = tempSubProdPlanIdDto == null ? StringUtils.EMPTY : tempSubProdPlanIdDto.getTaskNo();
        String tempMainTaskNo = StringUtils.EMPTY;
        if (!org.apache.commons.lang3.StringUtils.isEmpty(subTaskNo) && subTaskNo.length() > NumConstant.NUM_TWO) {
            tempMainTaskNo = subTaskNo.substring(NumConstant.NUM_ZERO, subTaskNo.length() - NumConstant.NUM_TWO);
        }

        if (!StringUtils.equals(tempMainTaskNo, mainTaskNo)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.NOT_CONTROL_BY_BATECH);
        }
    }

    //获取待绑定清单
    private List<ProdBindingSettingDTO> getBindList(String mainSn, String productCode, String itemCode, String prodplanId) throws Exception {
        // 获取待绑定清单
        ProdBindingSettingDTO prodBindingSettingDTO = new ProdBindingSettingDTO();
        prodBindingSettingDTO.setMainSn(mainSn);
        prodBindingSettingDTO.setProductCode(productCode);
        if (StringHelper.isNotEmpty(itemCode)) {
            prodBindingSettingDTO.setItemCode(itemCode);
        }
        if (StringHelper.isNotEmpty(prodplanId)) {
            prodBindingSettingDTO.setProdPlanId(prodplanId);
        }
        ServiceData toBindingListServiceData = prodBindingSettingService.getProdBindingSettingList(prodBindingSettingDTO);
        return null == toBindingListServiceData ? null : (List<ProdBindingSettingDTO>) toBindingListServiceData.getBo();
    }

    //是否在待绑定清单中
    private boolean inBindList(List<ProdBindingSettingDTO> bindList, String itemCode, WipExtendIdentificationDTO dto) {
        if (CollectionUtils.isEmpty(bindList) || StringHelper.isEmpty(itemCode)) {
            return false;
        }
        for (ProdBindingSettingDTO bind : bindList) {
            if (null != bind && itemCode.equals(bind.getItemCode())) {
                dto.setSubItemNo(bind.getItemCode());
                dto.setRemark(bind.getItemName());
                return true;
            }
        }
        return false;
    }

    // 查询子条码是否已有绑定记录
    private List<WipExtendIdentification> getWpExtend(String sn, String formType) {
        Map<String, Object> snMap = new HashMap<>();
        snMap.put("sn", sn);
        snMap.put("formType", formType);
        return wipExtendIdentificationRepository.getList(snMap);
    }

    //将材料代码转成物料代码并判断是否在绑定清单中
    private boolean itemCode2MaterialCode(List<ProdBindingSettingDTO> bindList, String itemCode, WipExtendIdentificationDTO dto) throws Exception {
        //  2021/1/13  将物料代码转为材料代码   !!
        StItemMessage stItemMessage = datawbRemoteService.queryItemNoAndName(itemCode);
        String materialCode = null == stItemMessage ? "" : stItemMessage.getItemNo();
        if (inBindList(bindList, materialCode, dto)) {
            //材料代码在绑定清单中
            return true;
        }
        return false;
    }

    //将序列码转成物料代码并判断是否在绑定清单中
    private boolean sn2MaterialCode(List<ProdBindingSettingDTO> bindList, WipExtendIdentificationDTO dto) throws Exception {
        //  2021/1/13 将序列码转为材料代码
        StItemMessage stItemMessage = datawbRemoteService.queryMaterialMessage(dto.getSn());
        String materialCode = null == stItemMessage ? "" : stItemMessage.getItemNo();
        if (inBindList(bindList, materialCode, dto)) {//在绑定清单中
            return true;
        }
        return false;
    }

    //条码在在制表不存在
    private void dealWhenNotExistWipinfo(WipExtendIdentificationDTO dto, List<ProdBindingSettingDTO> bindList) throws Exception {
        //  2021/1/12  条码在在制表中不存在
        if (SnCheckUtil.snStart7Or2AndLength12(dto.getSn())) { //条码是以7或2开头并且是12位
            // 截取前7位转为批次，并在批次表中查找物料代码
            String prodplanId = dto.getSn().trim().substring(Constant.INT_0, Constant.INT_7);
            // 2022-4-21 改为从中心工厂获取ps_task信息。
            List<PsTask> psTasks = CenterfactoryRemoteService.getPsTaskListFromCenterFactory(prodplanId);
            boolean flag = false;
            if (!CollectionUtils.isEmpty(psTasks)) {
                //子条码对应料单转换材料代码
                flag = itemCode2MaterialCode(bindList, psTasks.get(NumConstant.NUM_ZERO).getItemNo(), dto);
            }
            //转换后不存在绑定清单 当序列码转材料代码 判断是否在绑定清单
            verifyWhetherIsInBindList(dto, bindList, flag);
        } else {
            //  2021/1/13 将序列码转为材料代码
            boolean flag = sn2MaterialCode(bindList, dto);
            //不存在 当做序列码从条码中心查询物料代码 判断是否在绑定清单
            if (!flag) {
                flag = barCodeCenterMaterialCode(dto, bindList);
            }
            if (!flag) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.NOT_IN_TO_BINDING_LIST);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ServiceData insertSubSnBinding(WipExtendIdentificationDTO dto) throws Exception {
        ServiceData ret = new ServiceData();
        ret.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, RetCode.BUSINESSERROR_MSGID));
        if (null == dto || StringUtils.isEmpty(dto.getSn()) || StringUtils.isEmpty(dto.getFormSn())) {
            return ret;
        }
        //是否已经绑定；
        List<WipExtendIdentification> bindingList = getWpExtend(dto.getSn(), Constant.STR_3);
        if (!CollectionUtils.isEmpty(bindingList)) {
            ret.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.SUB_SN_IS_BINDED));
            ret.getCode().setMsg(CommonUtils.getLmbMessage(MessageId.SUB_SN_IS_BINDED, bindingList.get(0).getFormSn()));
            return ret;
        }
        //  2021/1/12   条码在在制表中是否存在
        Map<String, Object> snMap = new HashMap<>();
        snMap.put("sn", dto.getSn());
        List<PsWipInfo> wipInfos = psWipInfoRepository.getList(snMap);
        snMap.put("sn", dto.getFormSn());
        List<PsWipInfo> formSnWipInfoList = psWipInfoRepository.getList(snMap);
        //主条码不存在报错
        if (CollectionUtils.isEmpty(formSnWipInfoList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SN_NO_WIP, new Object[]{dto.getFormSn()});
        }
        String itemCode = getItemNo(wipInfos);
        String prodPlanId = getAttribute(formSnWipInfoList);
        // 添加子条码筛选
        List<ProdBindingSettingDTO> bindList = getProdBindingList(dto, dto.getFormSn(), prodPlanId);
        if (CollectionUtils.isEmpty(bindList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.BIND_RELATION_NOT_FOUND);
        }
        if (CollectionUtils.isEmpty(wipInfos)) {
            //  2021/1/12  条码在在制表中不存在
            this.dealWhenNotExistWipinfo(dto, bindList);
        } else {
            // 子条码存在wip表的时候 先校验锁定关系
            this.verifySnIsLock(wipInfos.get(NumConstant.NUM_ZERO));
            // 校验子条码是否存在技改
            this.technicalControl(wipInfos.get(NumConstant.NUM_ZERO));
            this.dealWhenExistWipInfo(dto, wipInfos, formSnWipInfoList, bindList);
        }
        //处理数据
        return dealCardAfter(dto, wipInfos, formSnWipInfoList, bindList);
    }

    /**
     * 校验子条码是否存在技改
     *
     * @param psWipInfo
     * @throws Exception
     */
    private void technicalControl(PsWipInfo psWipInfo) throws Exception {
        BarcodeBindingDTO barcodeBindingDTO = new BarcodeBindingDTO();
        barcodeBindingDTO.setSnList(Lists.newArrayList(psWipInfo.getSn()));
        barcodeBindingDTO.setWorkOrderNo(psWipInfo.getWorkOrderNo());
        barcodeBindingDTO.setJumpProcess(Boolean.TRUE);
        barcodeBindingDTO.setJumpCraftSection(Boolean.TRUE);
        flowControlCommonService.technicalControl(barcodeBindingDTO);
    }

    /**
     * 后续处理绑定数据
     *
     * @param dto
     * @param wipInfoList
     * @return
     * @throws Exception
     */
    private ServiceData dealCardAfter(WipExtendIdentificationDTO dto, List<PsWipInfo> wipInfoList, List<PsWipInfo> formSnWipInfoList, List<ProdBindingSettingDTO> bindingList) throws Exception {
        //、如果不在需绑定物料清单中：提示栏显示红色并将错误信息显示在提示栏上
        ServiceData ret = new ServiceData();
        ret.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, RetCode.BUSINESSERROR_MSGID));
        //子条码是否绑定完成
        String itemNo = dto.getSubItemNo();
        boolean isSnBinded = isFinish(bindingList, itemNo);
        if (isSnBinded) {
            ret.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, CommonUtils.getLmbMessage(MessageId.ITEMCODE_BINDED_COMPLETE, itemNo)));
            return ret;
        }
        String prodId = getAttribute(formSnWipInfoList);
        List<ProdBindingSettingDTO> bindListAll = bindingList;
        // 如果是选了子工序的，则必须查询主卡全部绑定清单
        if (!StringUtils.isEmpty(dto.getProcessCode())) {
            bindListAll = getBindList(dto.getFormSn(), dto.getItemNo(), Constant.EMPTY_STRING, prodId);
        }
        //未绑定完成就继续绑定，将子条码新增到wip_extend_identification中,同时bindListAll中本次待绑的数量也加上，用于判断
        insertBindInfo(dto, wipInfoList, bindListAll);
        //更新一次，二次装配标识
        updatePsWipInfoAssembleFlag(dto, formSnWipInfoList, bindListAll);
        // 获取所有已扫描条码，如果选择了子工序，那么返回的是该子工序下所有已绑记录，并判断是否在该子工序下全绑定完。
        WipExtendIdentification returnInfo = handlerQueryBoundSnAndCheckCanPassStation(dto, bindingList);
        ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        ret.setBo(returnInfo);
        return ret;
    }

    public WipExtendIdentification handlerQueryBoundSnAndCheckCanPassStation(WipExtendIdentificationDTO dto,
                                                           List<ProdBindingSettingDTO> bindingList) {
        WipExtendIdentification returnInfo = new WipExtendIdentification();
        if (StringUtils.isEmpty(dto.getProcessCode())) {
            returnInfo.setScanedList(wipExtendIdentificationRepository.getAllChildProdBindingInfo(dto.getFormSn()));
            return returnInfo;
        }
        // 如果是勾选了过站，输入了子工序
        List<WipExtendIdentification> boundList = wipExtendIdentificationRepository
                .listProdBindingInfoBySnAndProcessCode(dto.getFormSn(), dto.getProcessCode());

        for (ProdBindingSettingDTO entity : bindingList) {
            if (StringUtils.equals(entity.getItemCode(), dto.getSubItemNo())) {
                //已绑定数量加一
                BigDecimal bindQty = entity.getBoundNo() == null ? BigDecimal.ZERO : entity.getBoundNo();
                entity.setBoundNo(bindQty.add(BigDecimal.ONE));
            }
        }
        boolean finishProcessBindFlag = true;
        for (ProdBindingSettingDTO entity : bindingList) {
            if (entity.getUsageCount().compareTo(entity.getBoundNo()) > 0) {
                finishProcessBindFlag = false;
                break;
            }
        }
        returnInfo.setFinishProcessBindFlag(finishProcessBindFlag);
        returnInfo.setScanedList(boundList);
        return returnInfo;
    }

    /**
     * 更新一次，二次装配标识
     *
     * @param dto
     * @param formSnWipInfoList
     * @param bindingList
     */
    private void updatePsWipInfoAssembleFlag(WipExtendIdentificationDTO dto, List<PsWipInfo> formSnWipInfoList, List<ProdBindingSettingDTO> bindingList) {
        PsWipInfo mainPsWipInfo = formSnWipInfoList.get(NumConstant.NUM_ZERO);
        //存在一次装配物料校验一次装配物料是否都绑定完成 完成则更新主条码一次装配标识
        boolean oneFlag = judgeOneAssembleIsCompleted(bindingList, mainPsWipInfo);
        //存在二次装配物料校验二次装配是否完成
        boolean secFlag = judgeSecAssembleIsCompleted(bindingList, mainPsWipInfo);
        if (!oneFlag && !secFlag) {
            return;
        }
        mainPsWipInfo.setLastUpdatedBy(dto.getLastUpdatedBy());
        psWipInfoRepository.updatePsWipInfoAssembleFlag(mainPsWipInfo);
    }

    /**
     * 存在一次装配物料校验一次装配物料是否都绑定完成 完成则更新主条码一次装配标识
     *
     * @param bindingList
     */
    private boolean judgeSecAssembleIsCompleted(List<ProdBindingSettingDTO> bindingList, PsWipInfo mainPsWipInfo) {
        if (CollectionUtils.isEmpty(bindingList)) {
            return false;
        }
        List<ProdBindingSettingDTO> secAssembleItem = bindingList.stream().filter(e -> !StringUtils.isEmpty(e.getMainProductCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(secAssembleItem)) {
            return false;
        }
        for (ProdBindingSettingDTO prodBindingSettingDTO : secAssembleItem) {
            if (prodBindingSettingDTO.getUsageCount().compareTo(prodBindingSettingDTO.getBoundNo()) > Constant.INT_0) {
                return false;
            }
        }
        mainPsWipInfo.setSecAssembleFlag(Constant.FLAG_Y);
        return true;
    }

    /**
     * 存在一次装配物料校验一次装配物料是否都绑定完成 完成则更新主条码一次装配标识
     *
     * @param bindingList
     */
    private boolean judgeOneAssembleIsCompleted(List<ProdBindingSettingDTO> bindingList, PsWipInfo mainPsWipInfo) {
        if (CollectionUtils.isEmpty(bindingList)) {
            return false;
        }
        List<ProdBindingSettingDTO> oneAssembleItem = bindingList.stream().filter(e -> StringUtils.isEmpty(e.getMainProductCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(oneAssembleItem)) {
            return false;
        }
        for (ProdBindingSettingDTO prodBindingSettingDTO : oneAssembleItem) {
            if (prodBindingSettingDTO.getUsageCount().compareTo(prodBindingSettingDTO.getBoundNo()) > Constant.INT_0) {
                return false;
            }
        }
        mainPsWipInfo.setAssembleFlag(Constant.FLAG_Y);
        return true;
    }

    /***
     * 存在在制表情况下处理
     * @param dto
     * @param wipInfos
     * @param formSnWipInfoList
     * @param bindList
     * @return
     * @throws Exception
     */
    private void dealWhenExistWipInfo(WipExtendIdentificationDTO dto, List<PsWipInfo> wipInfos,
                                      List<PsWipInfo> formSnWipInfoList, List<ProdBindingSettingDTO> bindList) throws Exception {
        String subItemNo = wipInfos.get(NumConstant.NUM_ZERO).getItemNo();
        //判断子条码对应物料代码是否存在绑定清单
        boolean inBindFlag = inBindList(bindList, subItemNo, dto);
        //不存在绑定清单
        if (!inBindFlag) {
            //子条码对应料单转换材料代码
            boolean flag = itemCode2MaterialCode(bindList, subItemNo, dto);
            verifyWhetherIsInBindList(dto, bindList, flag);
        } else {
            //在绑定清单中
            //校验父条码和子条码是否存在子母卡关系
            PsWipInfo subPsWipInfo = wipInfos.get(NumConstant.NUM_ZERO);
            PsWipInfo mainPsWipInfo = formSnWipInfoList.get(NumConstant.NUM_ZERO);
            this.verifyControlByProdPlanId(subPsWipInfo, mainPsWipInfo);
            //子条码存在一次装配物料情况下，校验一次装配是否完成
            String subSn = subPsWipInfo.getSn();
            List<ProdBindingSettingDTO> subBindList = getBindList(subSn, subPsWipInfo.getItemNo(), StringUtils.EMPTY, subPsWipInfo.getAttribute1());
            if (CollectionUtils.isEmpty(subBindList)) {
                return;
            }
            //存在一次装配物料校验一次装配是否完成
            List<ProdBindingSettingDTO> oneAssembleItem = subBindList.stream().filter(e -> StringUtils.isEmpty(e.getMainProductCode())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(oneAssembleItem) && !StringUtils.equals(Constant.FLAG_Y, subPsWipInfo.getAssembleFlag())) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.AN_ASSEMBLY_IS_NOT_COMPLETED, new Object[]{subSn});
            }
            //存在二次装配物料校验二次装配是否完成
            List<ProdBindingSettingDTO> secAssembleItem = subBindList.stream().filter(e -> !StringUtils.isEmpty(e.getMainProductCode())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(secAssembleItem) && !StringUtils.equals(Constant.FLAG_Y, subPsWipInfo.getSecAssembleFlag())) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SECONDARY_ASSEMBLY_NOT_COMPLETED, new Object[]{subSn});
            }
        }
    }

    /**
     * 校验是否在绑定清单
     *
     * @param dto
     * @param bindList
     * @param flag
     * @throws Exception
     */
    private void verifyWhetherIsInBindList(WipExtendIdentificationDTO dto, List<ProdBindingSettingDTO> bindList, boolean flag) throws Exception {
        //转换后不存在绑定清单 当序列码转材料代码 判断是否在绑定清单
        if (!flag) {
            flag = this.sn2MaterialCode(bindList, dto);
        }
        //还不存在 当做序列码从条码中心查询物料代码 判断是否在绑定清单
        if (!flag) {
            flag = barCodeCenterMaterialCode(dto, bindList);
        }
        if (!flag) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.NOT_IN_TO_BINDING_LIST);
        }
    }

    /**
     * 当做序列码从条码中心查询物料代码 判断是否在绑定清单
     *
     * @param dto
     * @param bindList
     * @throws Exception
     */
    private boolean barCodeCenterMaterialCode(WipExtendIdentificationDTO dto, List<ProdBindingSettingDTO> bindList) throws Exception {
        List<String> barcodeList = new ArrayList<>();
        barcodeList.add(dto.getSn());
        BarcodeExpandQueryDTO barcodeExpandQueryDTO = new BarcodeExpandQueryDTO();
        barcodeExpandQueryDTO.setParentCategoryCode(MpConstant.SN_CODE);
        barcodeExpandQueryDTO.setBarcodeList(barcodeList);
        List<BarcodeExpandDTO> barcodeExpandDTOList = barcodeCenterRemoteService.expandQuery(barcodeExpandQueryDTO);
        if (CollectionUtils.isEmpty(barcodeExpandDTOList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SN_NOT_EXIST_WIP_INFO);
        }
        BarcodeExpandDTO returnDto = barcodeExpandDTOList.get(NumConstant.NUM_ZERO);
        String materialCode = returnDto == null ? "" : returnDto.getItemCode();
        if (inBindList(bindList, materialCode, dto)) {//在绑定清单中
            return true;
        }
        return false;
    }

    private String getItemNo(List<PsWipInfo> wipInfos) {
        return CollectionUtils.isEmpty(wipInfos) ? "" : wipInfos.get(0).getItemNo();
    }

    private String getAttribute(List<PsWipInfo> wipInfos) {
        return CollectionUtils.isEmpty(wipInfos) ? "" : wipInfos.get(0).getAttribute1();
    }


    /**
     * 未绑定完成就继续绑定，将子条码新增到wip_extend_identification中
     *
     * @param dto
     * @param wipInfoList
     * @param itemCodeToBindingList
     */
    public void insertBindInfo(WipExtendIdentificationDTO dto, List<PsWipInfo> wipInfoList,
              List<ProdBindingSettingDTO> itemCodeToBindingList) {
        if (CollectionUtils.isEmpty(itemCodeToBindingList)) {
            return;
        }
        WipExtendIdentification bindInsertInfo = new WipExtendIdentification();
        bindInsertInfo.setIdentiId(UUID.randomUUID().toString());
        bindInsertInfo.setSn(dto.getSn());
        bindInsertInfo.setFormSn(dto.getFormSn());
        bindInsertInfo.setItemNo(dto.getSubItemNo());
        bindInsertInfo.setFormType(Constant.STR_3);
        bindInsertInfo.setFormQty(new BigDecimal(Constant.INT_1));
        bindInsertInfo.setCreateBy(dto.getCreateBy());
        bindInsertInfo.setLastUpdatedBy(dto.getCreateBy());
        bindInsertInfo.setFactoryId(dto.getFactoryId());
        bindInsertInfo.setProdPlanId(dto.getProdPlanId());
        bindInsertInfo.setTaskNo(dto.getTaskNo());
        bindInsertInfo.setHbCode(CollectionUtils.isEmpty(wipInfoList) ? "" : wipInfoList.get(0).getAttribute3());
        bindInsertInfo.setAttribute1(CollectionUtils.isEmpty(wipInfoList) ? dto.getRemark() : wipInfoList.get(0).getItemName());
        //设置主卡料单
        for (ProdBindingSettingDTO prodBindingSettingDTO : itemCodeToBindingList) {
            if (StringUtils.equals(prodBindingSettingDTO.getItemCode(), dto.getSubItemNo())) {
                //已绑定数量加一
                BigDecimal bindQty = prodBindingSettingDTO.getBoundNo() == null ? BigDecimal.ZERO : prodBindingSettingDTO.getBoundNo();
                prodBindingSettingDTO.setBoundNo(bindQty.add(BigDecimal.ONE));
                bindInsertInfo.setMainProductCode(prodBindingSettingDTO.getMainProductCode());
                bindInsertInfo.setProcessCode(prodBindingSettingDTO.getProcessCode());
            }
        }
        this.insertOptRecord(NumConstant.STR_ONE,bindInsertInfo);
        wipExtendIdentificationRepository.insertWipExtendIdentificationSelective(bindInsertInfo);
    }

    //该方法存在性能问题，已换
    public ServiceData getMainSnCompleteNo(WipExtendIdentificationDTO dto) throws Exception {
        ServiceData ret = new ServiceData();
        ret.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, RetCode.BUSINESSERROR_MSGID));
        //根据批次查询主条码
        Map<String, Object> wipSnMap = new HashMap<>();
        wipSnMap.put("prodplanId", dto.getProdPlanId());
        List<WipExtendIdentification> wipExtendList = wipExtendIdentificationRepository.getList(wipSnMap);
        if (CollectionUtils.isEmpty(wipExtendList)) {
            ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
            ret.setBo(Constant.INT_0);
            return ret;
        }
        //查询主条码的物料代码
        StringBuilder inSns = new StringBuilder();
        for (WipExtendIdentification wip : wipExtendList) {
            inSns.append(Constant.SINGLE_QUOTE).append(wip.getFormSn()).append(Constant.SINGLE_QUOTE).append(Constant.SEPARATED_COMMA);
        }
        String queryStr = inSns.toString();
        if (StringHelper.isEmpty(queryStr)) {
            ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
            ret.setBo(Constant.INT_0);
            return ret;
        }
        Map<String, Object> formSnMap = new HashMap<>();
        formSnMap.put("inSns", inSns.toString().substring(0, inSns.length() - 1));
        List<PsWipInfo> formSnList = psWipInfoRepository.getList(formSnMap);
        if (CollectionUtils.isEmpty(formSnList)) {
            if (StringHelper.isEmpty(queryStr)) {
                ret.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.SN_IS_NOT_EXISTS));
                ret.setBo(Constant.INT_0);
                return ret;
            }
        }
        //判断主条码是否绑定完成
        long completeNo = getMainSnCompleteNo(dto, wipExtendList, formSnList);
        ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        ret.setBo(completeNo);
        return ret;
    }

    private long getMainSnCompleteNo(WipExtendIdentificationDTO dto, List<WipExtendIdentification> wipExtendList, List<PsWipInfo> formSnList) {
        Map<String, String> formSnItemNoMap = new HashMap<>();
        for (PsWipInfo info : formSnList) {
            formSnItemNoMap.put(info.getSn(), info.getItemNo());
        }
        // 分别对每一个主条码进行递归，统计已完成绑定主条码数量
        long completeNo = 0;
        for (WipExtendIdentification info : wipExtendList) {
            if (isFinish(info.getFormSn(), formSnItemNoMap.get(info.getFormSn()), dto.getProdPlanId())) {
                completeNo++;
            }
        }
        return completeNo;
    }

    public ServiceData getMainSnCompleteNoOne(WipExtendIdentificationDTO dto) {
        ServiceData ret = new ServiceData();
        ret.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, RetCode.BUSINESSERROR_MSGID));
        //查询该批次中已完成主条码数量
        PsWipInfo infoY = new PsWipInfo();
        infoY.setAttribute1(dto.getProdPlanId());
        List<PsWipInfo> completeList = psWipInfoRepository.getCompleteNo(infoY);
        //查询该批次中未完成主条码
        PsWipInfo infoN = new PsWipInfo();
        infoN.setAttribute1(dto.getProdPlanId());
        List<PsWipInfo> neverCompleteSnList = psWipInfoRepository.getNeverCompleteNo(infoN);
        if (!CollectionUtils.isEmpty(neverCompleteSnList)) {
            for (PsWipInfo info : neverCompleteSnList) {
                //如果还有未完成主条码，根据料单代码（批次）去反向维护表中查询是否存在
                ProdUnbindingSetting prod = new ProdUnbindingSetting();
                prod.setProductCode(info.getItemNo());
                prod.setProdPlanId(dto.getProdPlanId());
                List<ProdUnbindingSetting> neverCompleteList = prodUnbindingSettingRepository.getUnProdBindingList(prod);
                //存在则重新统计是否已绑定完成,完成则在wip_info中统计已完成数量上加1
                if (!CollectionUtils.isEmpty(neverCompleteList)) {
                    if (isFinish(info.getSn(), info.getItemNo(), info.getAttribute1())) {
                        completeList.add(info);
                    }
                }
            }
        }
        ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        ret.setBo(completeList.size());
        return ret;
    }

    /**
     * 检查已绑定数量
     *
     * @param dto
     * @return
     * @throws Exception
     */
    @Override
    public List<WipExtendIdentification> getSubSnBinding(WipExtendIdentificationDTO dto) throws Exception {
        return wipExtendIdentificationRepository.getAllChildProdBindingInfo(dto.getFormSn());
    }

    /**
     * 最近24小时
     *
     * @return
     * @throws Exception
     */
    @Override
    public String sendWipExtToPdmAlarm() throws Exception {
        Map<String, String> map = new HashMap<>();
        AssemblExecLogEntityDTO assemblExecLogEntityDTO = assemblExecLogService.getLastSuccessInfoWithin24Hours();
        if (assemblExecLogEntityDTO == null) {
            map.put(Constant.STATUS, Constant.FLAG_N);
        } else {
            map.put(Constant.STATUS, Constant.FLAG_Y);
        }
        return JSON.toJSONString(map);
    }

    private SimpleDateFormat getSimpleDateFormatForYyyymmdd() {
        TimeZone gmt = TimeZone.getTimeZone(MpConstant.GMT_8);
        SimpleDateFormat sdf = new SimpleDateFormat(MpConstant.DATE_FORMAT_YYYYMMDD);
        sdf.setTimeZone(gmt);
        sdf.setLenient(true);
        return sdf;
    }

    /**
     * 定时任务-推送组装关系到PDM
     *
     * @param taskNo
     * @return
     * @throws Exception
     */
    @Override
    public void sendWipExtendIdentificationToPdm(String taskNo, String factoryId) throws Exception {
        List<SysLookupTypesDTO> sysLookupTypesDTOList = BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_6688);
        List<SysLookupTypesDTO> patterList = BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_6689);
        Map<String, SysLookupTypesDTO> sysLookupValuesDTOMap = null == sysLookupTypesDTOList ?
                new HashMap() : sysLookupTypesDTOList.stream().collect(Collectors.toMap(e -> e.getLookupCode().toString(), a -> a, (k1, k2) -> k1));
        //前置时间
        String forwardTimeStr = sysLookupValuesDTOMap.get(Constant.LOOKUP_TYPE_6688001).getLookupMeaning();
        //pdm接口地址
        String url = sysLookupValuesDTOMap.get(Constant.LOOKUP_TYPE_6688002).getLookupMeaning();
        //告警邮件人
        String sender = sysLookupValuesDTOMap.get(Constant.LOOKUP_TYPE_6688004).getLookupMeaning();
        //最大告警推送临界值
        int maxSendCount = Integer.parseInt(sysLookupValuesDTOMap.get(Constant.LOOKUP_TYPE_6688005).getLookupMeaning());
        //执行结果
        String execResult = Constant.FLAG_Y;
        //执行错误信息
        StringBuffer errorInfoSb = new StringBuffer();
        //此次推送数据数量
        int pushNum = NumConstant.NUM_ZERO;
        RedisLock redisLock = new RedisLock(RedisKeyConstant.SEND_WIP_EXTENDIDENTIFICATION_TO_PDM + factoryId);
        //结束时间 即当前时间
        Date nowDate = new Date();
        long currentTime = nowDate.getTime();
        try {
            //锁2小时
            if (!redisLock.lock(NumConstant.NUM_1800 * NumConstant.NUM_FOUR)) {
                return;
            }
            //获取上一次执行成功时间
            AssemblExecLogEntityDTO assemblExecLogEntityDTO = assemblExecLogService.getLastSuccessInfo();
            //如果为空查询全部数据
            long forwardTimeInt = org.apache.commons.lang.StringUtils.isEmpty(forwardTimeStr) ? MpConstant.ZERO_POINT_FIVE : Long.parseLong(forwardTimeStr);
            WipExtendIdentificationDTO wipExtendIdentificationDTO = new WipExtendIdentificationDTO();
            //开始时间
            Date startTime = getStartTime(nowDate, currentTime, assemblExecLogEntityDTO, forwardTimeInt, wipExtendIdentificationDTO);
            //按时间范围查询 如果没有取到上次成功的记录 当第一次，取全部数据
            wipExtendIdentificationDTO.setEndTime(nowDate);
            wipExtendIdentificationDTO.setTaskNo(taskNo);
            List<WipExtendIdentification> wipExtendIdentificationList = wipExtendIdentificationRepository.getWipExtendIdentificationToPdm(wipExtendIdentificationDTO);
            if (!CollectionUtils.isEmpty(wipExtendIdentificationList)) {
                setBomVersion(patterList, wipExtendIdentificationList);
            }
            wipExtendIdentificationList = wipExtendIdentificationList.stream().filter(e -> org.apache.commons.lang3.StringUtils.isNotBlank(e.getBomVersion())).collect(Collectors.toList());
            //获取全部结果数据 根据料单代码
            List<AssemblyResultEntityDTO> assemblyResultEntityDTOList = getAssemblyResultEntityDTOS(wipExtendIdentificationList);
            //此次请求需处理数据
            List<WipExtendIdentification> needInsertList = new ArrayList<>();
            List<AssemblyResultEntityDTO> needUpdateList = new ArrayList<>();
            List<AssemblyResultHisEntityDTO> needInsertHisList = new ArrayList<>();
            this.getWipExtendIdentifications(needInsertHisList, needInsertList, needUpdateList, wipExtendIdentificationList, assemblyResultEntityDTOList);
            //过滤料单代码为空以及版本为空的数据
            needInsertList = needInsertList.stream().filter(e -> (org.apache.commons.lang.StringUtils.isNotEmpty(e.getItemNo()) && org.apache.commons.lang.StringUtils.isNotEmpty(e.getBomVersion()))).collect(Collectors.toList());
            //处理结果表新增数据
            insertAssResult(nowDate, startTime, needInsertList);
            //处理结果表更新数据
            assemblyResultService.batchUpdate(needUpdateList);
            //处理结果历史表数据
            assemblyResultHisService.batchInsert(needInsertHisList);
        } catch (Exception e) {
            String message = org.apache.commons.lang.StringUtils.isEmpty(e.getMessage()) ? "" : e.getMessage();
            errorInfoSb.append(message);
            execResult = Constant.FLAG_N;
        } finally {
            String logId = UUID.randomUUID().toString();
            pushNum = this.sendToPdmAndUpdateResult(url, pushNum, nowDate, logId);
            //写执行记录 任务号为空才写执行记录
            if (org.apache.commons.lang.StringUtils.isEmpty(taskNo)) {
                insertAssExecInfo(execResult, errorInfoSb, pushNum, nowDate, logId);
            }
            this.sendEmail(sender, maxSendCount);
            this.unLock(redisLock);
        }
    }

    private void unLock(RedisLock redisLock) {
        if (redisLock != null) {
            redisLock.unlock();
        }
    }

    private int sendToPdmAndUpdateResult(String url, int pushNum, Date nowDate, String logId) {
        try {
            //推送pdm以及更新结果表 写流水表
            pushNum = commonSyncService.sendToPdmAndUpdateResult(nowDate, url, logId);
        } catch (Exception e) {
            LOG.error(e.getMessage());
        }
        return pushNum;
    }

    private void sendEmail(String sender, int maxSendCount) {
        try {
            //发送邮件
            sendEmailAlloc(sender, maxSendCount);
        } catch (Exception e) {
            LOG.error(e.getMessage());
        }
    }

    /**
     * 设置起始日期
     *
     * @param nowDate
     * @param currentTime
     * @param assemblExecLogEntityDTO
     * @param forwardTimeInt
     * @param wipExtendIdentificationDTO
     * @return
     */
    private Date getStartTime(Date nowDate, long currentTime, AssemblExecLogEntityDTO assemblExecLogEntityDTO, long forwardTimeInt, WipExtendIdentificationDTO wipExtendIdentificationDTO) {
        Date startTime = null;
        if (assemblExecLogEntityDTO != null) {
            Date lastSuccessDate = assemblExecLogEntityDTO.getExecTime();
            SimpleDateFormat sdf = getSimpleDateFormatForYyyymmdd();
            String dateYyyymmdd = sdf.format(lastSuccessDate);
            String dateYyyymmddNow = sdf.format(nowDate);
            //如果不是同一天 取前24小时数据
            long startTimeLong = System.currentTimeMillis();
            if (!org.apache.commons.lang3.StringUtils.equalsIgnoreCase(dateYyyymmdd, dateYyyymmddNow)) {
                startTimeLong = currentTime - MpConstant.HOUR_24;
            } else {
                startTimeLong = lastSuccessDate.getTime() - forwardTimeInt;
            }
            //如果往前推24小时，还是在上次执行成功时间后，则取上次执行成功时间往前推xxx时间（数据字典配置）
            if (startTimeLong > lastSuccessDate.getTime()) {
                startTimeLong = lastSuccessDate.getTime() - forwardTimeInt;
            }
            startTime = new Date(startTimeLong);
            wipExtendIdentificationDTO.setStartTime(startTime);
        }
        return startTime;
    }

    /**
     * 设置料单版本
     *
     * @param patterList
     * @param wipExtendIdentificationList
     * @throws Exception
     */
    private void setBomVersion(List<SysLookupTypesDTO> patterList, List<WipExtendIdentification> wipExtendIdentificationList) throws Exception {
        //取任务对应料单版本
        List<String> taskNoList = wipExtendIdentificationList.stream().map(WipExtendIdentification::getTaskNo).distinct().filter(e -> org.apache.commons.lang.StringUtils.isNotEmpty(e)).collect(Collectors.toList());
        //没任务号 返回
        if (CollectionUtils.isEmpty(taskNoList)) {
            return;
        }
        List<PDMProductMaterialResultDTO> pdmProductMaterialResultDTOList = getPdmProductMaterialResultDTOS(taskNoList);
        //获取不到版本号信息 返回
        if (CollectionUtils.isEmpty(pdmProductMaterialResultDTOList)) {
            return;
        }
        List<PsTask> psTaskList = new ArrayList<>();
        getTaskInfoList(taskNoList, psTaskList);
        //获取不到任务信息 返回
        if (CollectionUtils.isEmpty(psTaskList)) {
            return;
        }
        //任务版本信息按任务号分组
        Map<String, PDMProductMaterialResultDTO> pdmProductMaterialResultDTOMap = new HashMap<>(pdmProductMaterialResultDTOList.size());
        for (PDMProductMaterialResultDTO tempDto : pdmProductMaterialResultDTOList) {
            pdmProductMaterialResultDTOMap.put(tempDto.getEntityName(), tempDto);
        }
        //任务信息按任务号分组
        Map<String, String> psTaskMap = new HashMap<>(psTaskList.size());
        for (PsTask tempDto : psTaskList) {
            psTaskMap.put(tempDto.getTaskNo(), tempDto.getItemNo());
        }
        //版本号匹配正则表达式  预留字段为Y则正向匹配 否则反向匹配
        //正向
        Set<String> patterListZxList = CollectionUtils.isEmpty(patterList) ? new HashSet<>() : patterList.stream().filter(p -> org.apache.commons.lang.StringUtils.equals(p.getAttribute1(), Constant.FLAG_Y)).distinct().map(SysLookupTypesDTO::getLookupMeaning).collect(Collectors.toSet());
        //反向
        Set<String> patterListFxList = CollectionUtils.isEmpty(patterList) ? new HashSet<>() : patterList.stream().filter(p -> !org.apache.commons.lang.StringUtils.equals(p.getAttribute1(), Constant.FLAG_Y)).distinct().map(SysLookupTypesDTO::getLookupMeaning).collect(Collectors.toSet());
        for (WipExtendIdentification tempDto : wipExtendIdentificationList) {
            String itemNo = psTaskMap.get(tempDto.getTaskNo());
            tempDto.setItemNo(itemNo);
            PDMProductMaterialResultDTO productMaterialResultDTO = pdmProductMaterialResultDTOMap.get(tempDto.getTaskNo());
            //料单版本为空 为000 为数据字典配置正则不匹配的都设置版本为空，后续不会处理版本为空的数据
            String bomVersion = productMaterialResultDTO == null ? "" : productMaterialResultDTO.getBomRevision();
            if (checkBomversionByPatter(patterListFxList, bomVersion) || !checkBomversionByPatter(patterListZxList, bomVersion)) {
                tempDto.setBomVersion(MpConstant.STR_EMPTY);
            } else {
                tempDto.setBomVersion(bomVersion);
            }
        }
    }

    /**
     * 正则匹配
     *
     * @param patterListList
     * @param bomVersion
     */
    public boolean checkBomversionByPatter(Set<String> patterListList, String bomVersion) {
        if (org.apache.commons.lang.StringUtils.isEmpty(bomVersion)) {
            return true;
        }
        for (String patter : patterListList) {
            if (Pattern.matches(patter, bomVersion)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取任务版本信息
     *
     * @param taskNoList
     * @return
     * @throws Exception
     */
    private List<PDMProductMaterialResultDTO> getPdmProductMaterialResultDTOS(List<String> taskNoList) throws Exception {
        List<PDMProductMaterialResultDTO> pdmProductMaterialResultDTOList = new ArrayList<>();
        List<List<String>> splitList = CommonUtils.splitList(taskNoList, NumConstant.NUM_300);
        for (List<String> tempList : splitList) {
            PDMProductMaterialResultDTO pdmProductMaterialResultDTO = new PDMProductMaterialResultDTO();
            pdmProductMaterialResultDTO.setEntityNameList(tempList);
            List<PDMProductMaterialResultDTO> tempPdmList = DatawbRemoteService.getBomVerByTaskNo(pdmProductMaterialResultDTO);
            if (!CollectionUtils.isEmpty(tempPdmList)) {
                pdmProductMaterialResultDTOList.addAll(tempPdmList);
            }
        }
        return pdmProductMaterialResultDTOList;
    }

    /**
     * 获取任务信息
     *
     * @param taskNoList
     * @param psTaskList
     */
    private void getTaskInfoList(List<String> taskNoList, List<PsTask> psTaskList) {
        if (CollectionUtils.isEmpty(taskNoList)) {
            return;
        }
        List<List<String>> splitList = CommonUtils.splitList(taskNoList, NumConstant.NUM_999);
        for (List<String> tempList : splitList) {
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("taskNo", SqlUtils.convertStrCollectionToSqlType(tempList));
            List<PsTask> tempPaskList = PlanscheduleRemoteService.getTaskListByTaskNos(map);
            if (!CollectionUtils.isEmpty(tempPaskList)) {
                psTaskList.addAll(tempPaskList);
            }
        }
    }

    /**
     * 此次请求需处理数据
     *
     * @param needInsertHisList
     * @param needUpdateList
     * @param assemblyResultEntityDTOList
     * @return
     */
    private void getWipExtendIdentifications(List<AssemblyResultHisEntityDTO> needInsertHisList, List<WipExtendIdentification> needInsertList, List<AssemblyResultEntityDTO> needUpdateList,
                                             List<WipExtendIdentification> wipExtendIdentificationList, List<AssemblyResultEntityDTO> assemblyResultEntityDTOList) {
        //结果表没有捞到这次需处理的料单，说明全部都是需处理数据
        //按物料代码，版本分组
        Map<String, AssemblyResultEntityDTO> assemblyResultEntityDTOMap = new HashMap<>(assemblyResultEntityDTOList.size());
        for (AssemblyResultEntityDTO tempDto : assemblyResultEntityDTOList) {
            assemblyResultEntityDTOMap.put(tempDto.getItemCode() + Constant.UNDER_LINE + tempDto.getItemVersion(), tempDto);
        }
        //过滤在结果表的数据以及料单代码为空的数据
        for (WipExtendIdentification tempDto : wipExtendIdentificationList) {
            String key = tempDto.getItemNo() + Constant.UNDER_LINE + tempDto.getBomVersion();
            AssemblyResultEntityDTO assemblyResultEntityDTO = assemblyResultEntityDTOMap.get(key);
            //结果表没有是需新增数据
            if (assemblyResultEntityDTO == null) {
                needInsertList.add(tempDto);
                continue;
            }
            //结果表有但是COMBINE_FLAG为N,并且状态已推送1，则写历史表 再更新COMBINE_FLAG为Y，PROCESS_STATUS 为待推送0
            if (assemblyResultEntityDTO != null && assemblyResultEntityDTO.getProcessStatus() == NumConstant.NUM_ONE
                    && org.apache.commons.lang.StringUtils.equalsIgnoreCase(assemblyResultEntityDTO.getCombineFlag(), Constant.FLAG_N)) {
                addHisInfo(needInsertHisList, assemblyResultEntityDTO);
            }
            //结果表有但是COMBINE_FLAG为N，则更新COMBINE_FLAG为Y，PROCESS_STATUS 为待推送0
            if (assemblyResultEntityDTO != null && org.apache.commons.lang.StringUtils.equalsIgnoreCase(assemblyResultEntityDTO.getCombineFlag(), Constant.FLAG_N)) {
                assemblyResultEntityDTO.setCombineFlag(Constant.FLAG_Y);
                assemblyResultEntityDTO.setProcessStatus(NumConstant.NUM_ZERO);
                needUpdateList.add(assemblyResultEntityDTO);
            }
        }
    }

    /**
     * 组装历史信息
     *
     * @param needInsertHisList
     * @param assemblyResultEntityDTO
     */
    public void addHisInfo(List<AssemblyResultHisEntityDTO> needInsertHisList, AssemblyResultEntityDTO assemblyResultEntityDTO) {
        AssemblyResultHisEntityDTO assemblyResult = new AssemblyResultHisEntityDTO();
        assemblyResult.setHisId(UUID.randomUUID().toString());
        assemblyResult.setResultId(assemblyResultEntityDTO.getResultId());
        assemblyResult.setItemCode(assemblyResultEntityDTO.getItemCode());
        assemblyResult.setProcessStatus(assemblyResultEntityDTO.getProcessStatus());
        assemblyResult.setItemVersion(assemblyResultEntityDTO.getItemVersion());
        assemblyResult.setCombineFlag(assemblyResultEntityDTO.getCombineFlag());
        assemblyResult.setStartTime(assemblyResultEntityDTO.getStartTime());
        assemblyResult.setEndTime(assemblyResultEntityDTO.getEndTime());
        assemblyResult.setRemark(assemblyResultEntityDTO.getRemark());
        assemblyResult.setCreateBy(assemblyResultEntityDTO.getCreateBy());
        assemblyResult.setLastUpdatedBy(assemblyResultEntityDTO.getLastUpdatedBy());
        assemblyResult.setEntityId(assemblyResultEntityDTO.getEntityId());
        assemblyResult.setFactoryId(assemblyResultEntityDTO.getFactoryId());
        assemblyResult.setOrgId(assemblyResultEntityDTO.getOrgId());
        needInsertHisList.add(assemblyResult);
    }

    /**
     * 处理结果表新增数据
     *
     * @param nowDate
     * @param startTime
     * @param needDealList
     * @throws Exception
     */
    private void insertAssResult(Date nowDate, Date startTime, List<WipExtendIdentification> needDealList) throws Exception {
        List<AssemblyResultEntityDTO> needInsertList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(needDealList)) {
            //新数据写入结果表
            Set<String> set = new HashSet<>();
            for (WipExtendIdentification wipExtendIdentification : needDealList) {
                String key = wipExtendIdentification.getItemNo() + Constant.UNDER_LINE + wipExtendIdentification.getBomVersion();
                if (set.contains(key)) {
                    continue;
                }
                set.add(key);
                AssemblyResultEntityDTO assemblyResult = new AssemblyResultEntityDTO();
                assemblyResult.setResultId(UUID.randomUUID().toString());
                assemblyResult.setItemCode(wipExtendIdentification.getItemNo());
                assemblyResult.setProcessStatus(NumConstant.NUM_ZERO);
                assemblyResult.setItemVersion(wipExtendIdentification.getBomVersion());
                assemblyResult.setSendCount(NumConstant.NUM_ZERO);
                assemblyResult.setCombineFlag(Constant.FLAG_Y);
                assemblyResult.setStartTime(startTime);
                assemblyResult.setEndTime(nowDate);
                assemblyResult.setCreateBy(Constant.SYSTEM);
                assemblyResult.setLastUpdatedBy(Constant.SYSTEM);
                String entityId = factoryConfig.getCommonEntityId();
                if (org.apache.commons.lang.StringUtils.isNotEmpty(entityId)) {
                    assemblyResult.setEntityId(new BigDecimal(entityId));
                }
                String factoryId = getFactoryId();
                if (org.apache.commons.lang.StringUtils.isNotEmpty(factoryId)) {
                    assemblyResult.setFactoryId(new BigDecimal(factoryId));
                }
                needInsertList.add(assemblyResult);
            }
            assemblyResultService.batchInsert(needInsertList);
        }
    }

    /**
     * 写执行记录
     *
     * @param execResult
     * @param errorInfoSb
     * @param pushNum
     * @param nowDate
     * @throws Exception
     */
    private void insertAssExecInfo(String execResult, StringBuffer errorInfoSb, int pushNum, Date nowDate, String logId) throws Exception {
        try {
            AssemblExecLogEntityDTO assemblExecLogEntityDTO = new AssemblExecLogEntityDTO();
            assemblExecLogEntityDTO.setLogId(logId);
            assemblExecLogEntityDTO.setExecTime(nowDate);
            assemblExecLogEntityDTO.setExecResult(execResult);
            List<String> strList = StringWithChineseUtils.getStrList(errorInfoSb.toString(), NumConstant.NUM_250);
            if (!CollectionUtils.isEmpty(strList)) {
                assemblExecLogEntityDTO.setErrorInfo(strList.get(NumConstant.NUM_ZERO));
            }
            assemblExecLogEntityDTO.setPushNum(pushNum);
            String factoryId = setSourceSys(assemblExecLogEntityDTO);
            assemblExecLogEntityDTO.setCreateBy(Constant.SYSTEM);
            assemblExecLogEntityDTO.setLastUpdatedBy(Constant.SYSTEM);
            assemblExecLogEntityDTO.setFactoryId(Integer.parseInt(factoryId));
            String entityId = factoryConfig.getCommonEntityId();
            if (org.apache.commons.lang.StringUtils.isNotEmpty(entityId)) {
                assemblExecLogEntityDTO.setEntityId(Integer.parseInt(entityId));
            }
            assemblExecLogService.insert(assemblExecLogEntityDTO);
        } catch (Exception e) {
            LOG.error(e.getMessage());
        }
    }

    /**
     * 设置来源
     *
     * @param assemblExecLogEntityDTO
     */
    private String setSourceSys(AssemblExecLogEntityDTO assemblExecLogEntityDTO) {
        String factoryId = getFactoryId();
        if (org.apache.commons.lang.StringUtils.equals(factoryId, Constant.FACTORY_ID_CS)) {
            assemblExecLogEntityDTO.setSourceSystem(PdmSourceSysEnum.CS.getSourceSys());
        } else if (org.apache.commons.lang.StringUtils.equals(factoryId, Constant.FACTORY_ID_XA)) {
            assemblExecLogEntityDTO.setSourceSystem(PdmSourceSysEnum.XA.getSourceSys());
        } else if (org.apache.commons.lang.StringUtils.equals(factoryId, Constant.FACTORY_ID_NJ)) {
            assemblExecLogEntityDTO.setSourceSystem(PdmSourceSysEnum.NJ.getSourceSys());
        } else if (org.apache.commons.lang.StringUtils.equals(factoryId, Constant.FACTORY_ID_SZ)) {
            assemblExecLogEntityDTO.setSourceSystem(PdmSourceSysEnum.SZ.getSourceSys());
        } else if (org.apache.commons.lang.StringUtils.equals(factoryId, Constant.FACTORY_ID_HY)) {
            assemblExecLogEntityDTO.setSourceSystem(PdmSourceSysEnum.HY.getSourceSys());
        } else {
            assemblExecLogEntityDTO.setSourceSystem(PdmSourceSysEnum.MES.getSourceSys());
        }
        return factoryId;
    }

    /**
     * 获取工厂id
     *
     * @return
     */
    private String getFactoryId() {
        Map<String, String> header = MESHttpHelper.getHttpRequestHeader();
        String factoryId = header.get(SysConst.HTTP_HEADER_X_FACTORY_ID);
        factoryId = org.apache.commons.lang.StringUtils.isEmpty(factoryId) ? header.get(SysConst.HTTP_HEADER_X_FACTORY_ID_LOW_CASE) : factoryId;
        return factoryId;
    }

    /**
     * 发送邮件
     *
     * @param sender
     */
    private void sendEmailAlloc(String sender, int sendCount) throws Exception {
        if (org.apache.commons.lang.StringUtils.isEmpty(sender)) {
            return;
        }
        String[] senderArr = sender.split(Constant.COMMA);
        if (senderArr == null || senderArr.length < NumConstant.NUM_ONE) {
            return;
        }
        StringBuilder currentHandlerSb = new StringBuilder();
        for (String currentHandlerStr : senderArr) {
            currentHandlerSb.append(currentHandlerStr).append(MpConstant.MAILBOX_SUFFIX).append(MpConstant.COLON);
        }

        //失败并且推送次数超过五次的 可配置
        AssemblyResultEntityDTO assemblyResultEntityDTO = new AssemblyResultEntityDTO();
        List<String> processStatusList = new ArrayList<>(NumConstant.NUM_TWO);
        processStatusList.add(NumConstant.STRING_TWO);
        assemblyResultEntityDTO.setInProcessStatus(SqlUtils.convertStrCollectionToSqlType(processStatusList));
        assemblyResultEntityDTO.setSendCount(sendCount);
        List<AssemblyResultEntityDTO> assemblyTempList = assemblyResultService.getListByStatus(assemblyResultEntityDTO);
        if (CollectionUtils.isEmpty(assemblyTempList)) {
            return;
        }
        String subject = MpConstant.SUBJECT_182;
        StringBuffer sb = new StringBuffer();
        for (AssemblyResultEntityDTO contentDto : assemblyTempList) {
            sb.append(String.format(MpConstant.CONTENT_182, contentDto.getItemCode(), contentDto.getItemVersion(), contentDto.getSendCount()));
        }
        //当前处理人发送邮件
        boolean flag = emailUtils.sendMail(currentHandlerSb.toString(), subject, "", sb.toString(), "");
    }


    /**
     * 根据物料代码获取结果数据
     *
     * @param wipExtendIdentificationList
     * @return
     * @throws Exception
     */
    private List<AssemblyResultEntityDTO> getAssemblyResultEntityDTOS(List<WipExtendIdentification> wipExtendIdentificationList) throws Exception {
        List<AssemblyResultEntityDTO> assemblyResultEntityDTOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(wipExtendIdentificationList)) {
            List<String> itemCodeList = wipExtendIdentificationList.stream().filter(e -> org.apache.commons.lang.StringUtils.isNotEmpty(e.getItemNo())).map(WipExtendIdentification::getItemNo).distinct().collect(Collectors.toList());
            List<List<String>> splitList = CommonUtils.splitList(itemCodeList, NumConstant.NUM_999);
            for (List<String> tempList : splitList) {
                AssemblyResultEntityDTO assemblyResultEntityDTO = new AssemblyResultEntityDTO();
                List<String> processStatusList = new ArrayList<>(NumConstant.NUM_TWO);
                processStatusList.add(NumConstant.STRING_ZERO);
                processStatusList.add(NumConstant.STRING_TWO);
                processStatusList.add(NumConstant.STRING_ONE);
                assemblyResultEntityDTO.setInProcessStatus(SqlUtils.convertStrCollectionToSqlType(processStatusList));
                assemblyResultEntityDTO.setExistItemList(tempList);
                List<AssemblyResultEntityDTO> assemblyTempList = assemblyResultService.getListByStatus(assemblyResultEntityDTO);
                if (!CollectionUtils.isEmpty(assemblyTempList)) {
                    assemblyResultEntityDTOList.addAll(assemblyTempList);
                }
            }
        }
        return assemblyResultEntityDTOList;
    }

    @Override
    public WipExtendIdentification selectWipExtendIdentificationByFormSnAndSn(String formSn, String sn) {
        return wipExtendIdentificationRepository.selectWipExtendIdentificationByFormSnAndSn(formSn, sn);
    }

    /**
     * @return
     * <AUTHOR>
     * 装配关系回写spm
     * @Date 2022/9/21 13:43
     * @Param []
     **/
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ServiceData sendWipExtToSpm(String empNo, String factoryId, String day) throws Exception {
        RedisLock redisLock = new RedisLock(RedisKeyConstant.SEND_WIP_EXT_TO_SPM_LOCK + factoryId);
        if (!redisLock.lock()) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SEND_WIP_EXT_TO_SPM_LOCK);
        }
        try {
            //整机条码打印
            ServiceData ret = new ServiceData();
            WipExtendIdentificationDTO dto = getObtainScope(day);
            // 获取formType=3的装配关系
            List<WipExtendIdentification> wipExtendIdentificationList = wipExtendIdentificationRepository.getWipExtendIdentificationByCreateDate(dto);
            List<String> itemList = new ArrayList<>();
            if (CollectionUtils.isEmpty(wipExtendIdentificationList)) {
                ret.setBo(0);
                return ret;
            }
            Date lastCreateDate = wipExtendIdentificationList.get(0).getCreateDate();
            for (WipExtendIdentification wipExtendIdentification : wipExtendIdentificationList) {
                itemList.add(wipExtendIdentification.getItemNo());
            }
            // 获取条码物料类型
            List<BsItemInfo> itemInfoList = BasicsettingRemoteService.getItemType(itemList);
            if (CollectionUtils.isEmpty(itemInfoList)) {
                ret.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.SN_NOT_EXIST));
                ret.setBo(0);
                return ret;
            }
            // 原材料条码
            List<WipExtendIdentification> materialList = new ArrayList<>();
            // 半成品条码
            List<WipExtendIdentification> semiList = new ArrayList<>();
            List<String> snList = new ArrayList<>();
            // 校验条码物料类型
            checkItemType(wipExtendIdentificationList, itemInfoList, materialList, semiList, snList);
            // 回写物料类型为原材料的条码到spm
            Integer materialCount = pushMaterialSpm(materialList, snList);
            Integer semiCount = pushSemiSpm(semiList);
            int intMaterialCount = materialCount == null ? 0 : materialCount;
            int intSemiCount = semiCount == null ? 0 : semiCount;
            updateLastDate(lastCreateDate);
            ret.setBo(intMaterialCount + intSemiCount);
            return ret;
        } finally {
            redisLock.unlock();
        }
    }

    /**
     * @return
     * <AUTHOR>
     * 修改数据字典值
     * @Date 2022/9/30 10:23
     * @Param [java.util.Date]
     **/
    private void updateLastDate(Date lastCreateDate) throws Exception {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String strIssueSeqBeginDate = dateFormat.format(lastCreateDate);
        BasicsettingRemoteService.updateSysLookupValuesMeaning(new BigDecimal(Constant.SYS_LOOK_7000005), strIssueSeqBeginDate);
    }

    /**
     * @return
     * <AUTHOR>
     * 子条码物料类型为半成品推送spm
     * @Date 2022/9/21 14:49
     * @Param
     **/
    private Integer pushSemiSpm(List<WipExtendIdentification> semiList) throws Exception {
        if (CollectionUtils.isEmpty(semiList)) {
            return 0;
        }
        Map<String, String> snMap = new HashMap<>();
        Map<String, String> empMap = new HashMap<>();
        for (WipExtendIdentification wip : semiList) {
            snMap.put(wip.getSn(), wip.getFormSn());
            empMap.put(wip.getSn(), wip.getCreateBy());
        }
        Map<String, String> topSnMap = new HashMap<>();
        // 递归获取顶级主条码
        for (Map.Entry<String, String> entry : snMap.entrySet()) {
            String topFormSnKey = entry.getValue();
            String topFormSn = getTopFormSn(Constant.INT_0, topFormSnKey, snMap);
            topSnMap.put(entry.getKey(), topFormSn);
        }
        // 获取顶级批次
        PsTask psTask = PlanscheduleRemoteService.getTopProdplan(topSnMap);
        return checkIsTopProdplanMap(psTask, topSnMap, empMap);
    }

    /***
     *<AUTHOR>
     * 对顶级批次的结果进行处理
     *@Date 2022/9/30 8:33
     *@Param [com.zte.domain.model.PsTask, java.util.Map<java.lang.String,java.lang.String>, java.lang.String]
     *@return
     **/
    private Integer checkIsTopProdplanMap(PsTask psTask, Map<String, String> topSnMap, Map<String, String> empMap) throws Exception {
        if (psTask == null) {
            return 0;
        }
        Integer count = 0;
        // 是顶级批次：回写spm
        if (psTask.getIsTopProdplanMap() != null) {
            count = pushSemiSpm(psTask.getIsTopProdplanMap(), empMap);
        }
        // 不是顶级批次：写入失败表
        if (psTask.getNotTopProdplanMap() != null) {
            writeFailInfo(psTask.getNotTopProdplanMap(), topSnMap, empMap);
        }
        return count;
    }

    /**
     * @return
     * <AUTHOR>
     * 非顶级批次写入失败表
     * @Date 2022/9/30 8:33
     * @Param [java.util.Map<java.lang.String, java.lang.String>, java.util.Map<java.lang.String,java.lang.String>, java.lang.String]
     **/
    private void writeFailInfo(Map<String, String> notTopProdplanMap, Map<String, String> topSnMap, Map<String, String> empMap) {
        List<WriteBackSpmFailInfo> writeBackSpmFailInfoList = new ArrayList<>();
        for (Map.Entry<String, String> entry : notTopProdplanMap.entrySet()) {
            WriteBackSpmFailInfo writeBackSpmFailInfo = new WriteBackSpmFailInfo();
            writeBackSpmFailInfo.setSn(entry.getKey());
            writeBackSpmFailInfo.setFormSn(topSnMap.get(entry.getKey()));
            writeBackSpmFailInfo.setTopProdplanId(entry.getValue());
            writeBackSpmFailInfo.setFormType(Constant.STR_3);
            writeBackSpmFailInfo.setCreateBy(empMap.get(entry.getKey()));
            writeBackSpmFailInfo.setCreateDate(new Date());
            writeBackSpmFailInfo.setLastUpdatedBy(empMap.get(entry.getKey()));
            writeBackSpmFailInfo.setLastUpdatedDate(new Date());
            writeBackSpmFailInfoList.add(writeBackSpmFailInfo);
        }
        List<List<WriteBackSpmFailInfo>> splitList = CommonUtils.splitList(writeBackSpmFailInfoList, Constant.INT_100);
        for (List<WriteBackSpmFailInfo> list : splitList) {
            writeBackSpmFailInfoRepository.batchInsert(list);
        }
    }

    /**
     * @return
     * <AUTHOR>
     * 半成品装配关系回写spm
     * @Date 2022/9/30 8:34
     * @Param [java.util.Map<java.lang.String, java.lang.String>]
     **/
    private Integer pushSemiSpm(Map<String, String> map, Map<String, String> empMap) throws Exception {
        List<ScanPartstoboard> partstoboardList = new ArrayList<>();
        for (Map.Entry<String, String> entry : map.entrySet()) {
            String prodplanId = entry.getKey().substring(Constant.INT_0, Constant.INT_7);
            String formProdplanId = entry.getValue().substring(Constant.INT_0, Constant.INT_7);
            ScanPartstoboard scanPartstoboard = new ScanPartstoboard();
            scanPartstoboard.setPartsPlanid(Integer.parseInt(formProdplanId));
            scanPartstoboard.setPartsSn(Integer.parseInt(StringUtils.substring(entry.getValue(), Constant.INT_7, Constant.INT_12)));
            scanPartstoboard.setBoardPlanid(Integer.parseInt(prodplanId));
            scanPartstoboard.setBoardSn(Integer.parseInt(StringUtils.substring(entry.getKey(), Constant.INT_7, Constant.INT_12)));
            scanPartstoboard.setScanedBy(empMap.get((entry.getKey())));
            partstoboardList.add(scanPartstoboard);
        }
        return datawbRemoteService.pushSemiSpm(partstoboardList);
    }

    /**
     * @return
     * <AUTHOR>
     * 递归获取顶级主条码
     * @Date 2022/9/26 9:56
     * @Param
     **/
    private String getTopFormSn(int layer, String topFormSnKey, Map<String, String> snMap) {
        String topFormSn;
        if (layer == Constant.INT_5) {
            return topFormSnKey;
        }
        if (null != snMap.get(topFormSnKey)) {
            layer++;
            topFormSn = getTopFormSn(layer, snMap.get(topFormSnKey), snMap);
        } else {
            topFormSn = topFormSnKey;
        }
        return topFormSn;
    }

    /**
     * @return
     * <AUTHOR>
     * 子条码物料类型为原材料推送spm
     * @Date 2022/9/21 14:49
     * @Param
     **/
    private Integer pushMaterialSpm(List<WipExtendIdentification> materialList, List<String> snList) throws Exception {
        List<PartsbarScanInfo> partsbarScanInfoList = new ArrayList<>();
        if (CollectionUtils.isEmpty(materialList)) {
            return 0;
        }
        List<List<String>> splitList = CommonUtils.splitList(snList, Constant.IN_MAX_BATCH_SIZE);
        List<PsWipInfoDTO> wipList = new ArrayList<>();
        for (List<String> list : splitList) {
            List<PsWipInfoDTO> itemInfoList = psWipInfoRepository.getListByBatchSnList(list);
            if (CollectionUtils.isEmpty(itemInfoList)) {
                continue;
            }
            wipList.addAll(itemInfoList);
        }
        if (CollectionUtils.isEmpty(wipList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SN_NOT_EXIST);
        }
        assembleData(materialList, wipList, partsbarScanInfoList);
        return datawbRemoteService.pushMaterialSpm(partsbarScanInfoList);
    }

    private void assembleData(List<WipExtendIdentification> materialList, List<PsWipInfoDTO> wipList, List<PartsbarScanInfo> partsbarScanInfoList) {
        for (WipExtendIdentification wipExtendIdentification : materialList) {
            PartsbarScanInfo partsbarScanInfo = new PartsbarScanInfo();
            // 主条码后五位
            if (!StringUtils.isEmpty(wipExtendIdentification.getFormSn()) &&
                    StringUtils.isNumeric(wipExtendIdentification.getFormSn()) && wipExtendIdentification.getFormSn().length() == Constant.INT_12) {
                String formSn = StringUtils.substring(wipExtendIdentification.getFormSn(), Constant.INT_12 - 5, Constant.INT_12);
                partsbarScanInfo.setPartsSn(Integer.parseInt(formSn));
            }
            partsbarScanInfo.setPartsBar(wipExtendIdentification.getFormSn());
            // 主条码批次
            if (!StringUtils.isEmpty(wipExtendIdentification.getProdPlanId())) {
                partsbarScanInfo.setPartsPlanid(Integer.parseInt(wipExtendIdentification.getProdPlanId()));
            }
            partsbarScanInfo.setZkmoudleCode(wipExtendIdentification.getItemNo());
            partsbarScanInfo.setZkmoudleBar(wipExtendIdentification.getSn());
            partsbarScanInfo.setZkmoudleName(wipExtendIdentification.getItemName());
            partsbarScanInfo.setScanedBy(wipExtendIdentification.getCreateBy());
            partsbarScanInfo.setScanDate(wipExtendIdentification.getCreateDate());
            // 子条码批次
            for (PsWipInfoDTO dto : wipList) {
                if (!StringUtils.isEmpty(wipExtendIdentification.getSn()) &&
                        StringUtils.equals(wipExtendIdentification.getSn(), dto.getSn()) &&
                        !StringUtils.isEmpty(dto.getAttribute1())) {
                    partsbarScanInfo.setBoardPlanid(Integer.parseInt(dto.getAttribute1()));
                    break;
                }
            }
            // 子条码后五位
            if (!StringUtils.isEmpty(wipExtendIdentification.getSn()) &&
                    StringUtils.isNumeric(wipExtendIdentification.getSn()) && wipExtendIdentification.getSn().length() == Constant.INT_12) {
                String sn = StringUtils.substring(wipExtendIdentification.getSn(), Constant.INT_12 - 5, Constant.INT_12);
                partsbarScanInfo.setBoardSn(Integer.parseInt(sn));
            }
            partsbarScanInfoList.add(partsbarScanInfo);
        }
    }

    /**
     * @return
     * <AUTHOR>
     * 遍历匹配的物料类型
     * @Date 2022/9/21 13:39
     **/
    private void checkItemType(List<WipExtendIdentification> wipExtendIdentificationList, List<BsItemInfo> itemInfoList,
                               List<WipExtendIdentification> materialList, List<WipExtendIdentification> semiList, List<String> snList) {
        for (WipExtendIdentification wipExtendIdentification : wipExtendIdentificationList) {
            for (BsItemInfo bsItemInfo : itemInfoList) {
                if (!wipExtendIdentification.getItemNo().equals(bsItemInfo.getItemNo())) {
                    continue;
                }
                // 物料类型为半成品
                if (!StringUtils.isEmpty(bsItemInfo.getItemType()) && Constant.STR_NUMBER_ONE.equals(bsItemInfo.getItemType())) {
                    semiList.add(wipExtendIdentification);
                    // 物料类型为原材料
                } else if (!StringUtils.isEmpty(bsItemInfo.getItemType()) && Constant.STR_0.equals(bsItemInfo.getItemType())) {
                    wipExtendIdentification.setItemName(bsItemInfo.getItemName());
                    materialList.add(wipExtendIdentification);
                    snList.add(wipExtendIdentification.getSn());
                }
            }
        }
    }

    /**
     * @return
     * <AUTHOR>
     * 装配关系推送SPM获取推送数据时间范围
     * @Date 2022/9/21 9:48
     * @Param []
     **/
    private WipExtendIdentificationDTO getObtainScope(String day) throws Exception {
        // 获取上次推送截止时间
        SysLookupTypesDTO lookupValuesDTO = BasicsettingRemoteService.getSysLookUpValue(Constant.SYS_LOOK_7000, Constant.SYS_LOOK_7000005);
        if (lookupValuesDTO == null) {
            String[] params = {Constant.SYS_LOOK_7000005};
            throw new MesBusinessException(com.zte.springbootframe.common.model.RetCode.BUSINESSERROR_CODE, MessageId.SYS_LOOK_NOT_CONFIG, params);
        }
        WipExtendIdentificationDTO dto = new WipExtendIdentificationDTO();
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date startTime = df.parse(lookupValuesDTO.getLookupMeaning());
        // 将数据字典配置时间往前推3秒作为起始时间
        if (leadTime != 0) {
            startTime.setTime(startTime.getTime() - 1000 * leadTime);
        }
        dto.setStartTime(startTime);
        // 处理数据的天数，大于当前时间则取当前时间为结束时间
        double lookupDay = Float.parseFloat(day) * 24 * 60 * 60 * 1000;
        Date lookupEndDate = new Date(dto.getStartTime().getTime() + (long) lookupDay);
        dto.setEndTime(lookupEndDate.compareTo(new Date()) > 0 ? new Date() : lookupEndDate);
        return dto;
    }

    /**
     * @return
     * <AUTHOR>
     * 处理失败表中的数据推送spm
     * @Date 2022/9/30 13:56
     * @Param [java.lang.String]
     **/
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ServiceData sendSemiFailSpm(String day, String factoryId) throws Exception {
        RedisLock redisLock = new RedisLock(RedisKeyConstant.SEND_SEMI_FAIL_SPM_LOCK + factoryId);
        if (!redisLock.lock()) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SEND_WIP_EXT_TO_SPM_LOCK);
        }
        try {
            ServiceData ret = new ServiceData();
            ret.setBo(0);
            // 设置数据处理时间范围
            WriteBackSpmFailInfo writeBackSpmFailInfo = setStartDate(day);
            // 获取时间范围内的数据
            List<WriteBackSpmFailInfo> writeBackSpmFailInfoList = writeBackSpmFailInfoRepository.getWriteBackSpmFailInfoByCreateDate(writeBackSpmFailInfo);
            if (CollectionUtils.isEmpty(writeBackSpmFailInfoList)) {
                return ret;
            }
            Set<String> set = new HashSet<>();
            for (WriteBackSpmFailInfo backSpmFailInfo : writeBackSpmFailInfoList) {
                set.add(backSpmFailInfo.getFormSn());
            }
            // 递归获取条码的装配关系数据
            List<WipExtendIdentification> wipExtendIdentifications = wipExtendIdentificationRepository.getTopFormSn(set);
            if (CollectionUtils.isEmpty(wipExtendIdentifications)) {
                return ret;
            }
            Map<String, String> wipExtendIdentificationMap = new HashMap<>();
            for (WipExtendIdentification wipExtendIdentification : wipExtendIdentifications) {
                wipExtendIdentificationMap.put(wipExtendIdentification.getSn(), wipExtendIdentification.getFormSn());
            }
            // 校验数据并处理
            ret.setBo(checkIsTopSnAndPushSpm(wipExtendIdentificationMap, writeBackSpmFailInfoList));
            return ret;
        } finally {
            redisLock.unlock();
        }
    }

    /**
     * @return
     * <AUTHOR>
     * 校验是否顶级批次、回写spm
     * @Date 2022/9/30 13:49
     * @Param [java.util.Map<java.lang.String, java.lang.String>, java.util.List<com.zte.domain.model.WriteBackSpmFailInfo>]
     **/
    private Integer checkIsTopSnAndPushSpm(Map<String, String> wipExtendIdentificationMap, List<WriteBackSpmFailInfo> writeBackSpmFailInfoList) throws Exception {
        Integer count = 0;
        Map<String, String> topSnMap = new HashMap<>();
        // 递归获取在装配关系表中的顶级主条码
        for (Map.Entry<String, String> entry : wipExtendIdentificationMap.entrySet()) {
            String topFormSnKey = entry.getValue();
            String topFormSn = getTopFormSn(Constant.INT_0, topFormSnKey, wipExtendIdentificationMap);
            topSnMap.put(entry.getKey(), topFormSn);
        }
        Map<String, String> pushSemiMap = new HashMap<>();
        Map<String, String> empMap = new HashMap<>();
        List<String> deleteList = new ArrayList<>();
        for (WriteBackSpmFailInfo writeBackSpmFailInfo : writeBackSpmFailInfoList) {
            if (topSnMap.get(writeBackSpmFailInfo.getFormSn()) == null) {
                continue;
            }
            String prodplanId = topSnMap.get(writeBackSpmFailInfo.getFormSn()).substring(Constant.INT_0, Constant.INT_7);
            // 校验获取到的顶级批次是否与之前获取到的顶级批次一致，一致则处理
            if (prodplanId.equals(writeBackSpmFailInfo.getTopProdplanId())) {
                pushSemiMap.put(writeBackSpmFailInfo.getSn(), topSnMap.get(writeBackSpmFailInfo.getFormSn()));
                empMap.put(writeBackSpmFailInfo.getSn(), writeBackSpmFailInfo.getCreateBy());
                deleteList.add(writeBackSpmFailInfo.getSn());
            }
        }
        if (!CollectionUtils.isEmpty(deleteList)) {
            // 回写spm
            count = this.pushSemiSpm(pushSemiMap, empMap);
            // 回写spm后删除失败表中对应数据
            writeBackSpmFailInfoRepository.deleteWriteBackSpmFailInfoBySn(deleteList);
        }
        return count;
    }

    /**
     * @return
     * <AUTHOR>
     * 设置数据处理时间范围，单位：天
     * @Date 2022/9/30 13:51
     * @Param [java.lang.String]
     **/
    private WriteBackSpmFailInfo setStartDate(String day) {
        WriteBackSpmFailInfo writeBackSpmFailInfo = new WriteBackSpmFailInfo();
        int lookupDay = Integer.parseInt(day);
        Date endDate = new Date();
        Date startDate = new Date(endDate.getTime() - (long) lookupDay * 24 * 60 * 60 * 1000);
        writeBackSpmFailInfo.setStartDate(startDate);
        writeBackSpmFailInfo.setEndDate(endDate);
        return writeBackSpmFailInfo;
    }

    /**
     * @return
     * <AUTHOR>
     * 装配关系推送本地工厂
     * @Date 2023/2/9 14:21
     * @Param [java.util.List<com.zte.domain.model.WipExtendIdentification>]
     **/
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int pushWipExtSemiToFactory(List<WipExtendIdentification> list) throws Exception {
        if (CollectionUtils.isEmpty(list)) {
            return 0;
        }
        List<List<WipExtendIdentification>> lists = CommonUtils.splitList(list, Constant.BATCH_SIZE);
        int count = 0;

        this.batchInsertOptRecord(lists);

        for (List<WipExtendIdentification> wipExtendIdentificationList : lists) {
            count += wipExtendIdentificationRepository.batchInsertWipExt(wipExtendIdentificationList);
        }
        return count;
    }

    /**
     * 写操作历史
     * @param lists
     */
    public void batchInsertOptRecord(List<List<WipExtendIdentification>> lists) {
        for (List<WipExtendIdentification> oneList : lists) {
            List<String> snList = oneList.stream().map(e->e.getSn()).filter(e->StringUtils.isNotEmpty(e)).distinct().collect(Collectors.toList());
            List<String> formSnList = oneList.stream().map(e->e.getFormSn()).filter(e->StringUtils.isNotEmpty(e)).distinct().collect(Collectors.toList());
            List<WipExtendIdentification> wipExtendIdentificationList = wipExtendIdentificationRepository.getWipExtendBySnAndFormSn(snList,formSnList);
            //按条码分组
            oneList.forEach(p->{p.setEmpNo(this.getEmpNo());});
            Map<String, WipExtendIdentification> wipExtendIdentificationMap = CollectionUtils.isEmpty(wipExtendIdentificationList)?new HashMap<>():wipExtendIdentificationList.stream().collect(
                    Collectors.toMap(k -> k.getSn()+Constant.UNDER_LINE+k.getFormSn(), v -> v, (oldValue, newValue) -> newValue));
            List<WipExtendIdentification> insertList = oneList.stream().filter(e->!wipExtendIdentificationMap.containsKey(e.getSn()+Constant.UNDER_LINE+e.getFormSn())).collect(Collectors.toList());
            this.batchInsertOptRecord(NumConstant.STRING_ONE,insertList);
        }
    }

    /**
     * 获取子条码
     *
     * @return
     * @Param snList
     **/
    @Override
    public List<WipExtendIdentification> getAllChildSn(List<String> snList) throws Exception {
        List<WipExtendIdentification> wipExtendIdentificationList = new ArrayList<>();
        if (CollectionUtils.isEmpty(snList)) {
            return wipExtendIdentificationList;
        }
        for (List<String> snTempList : CommonUtils.splitList(snList, Constant.BATCH_SIZE_30)) {
            List<WipExtendIdentification> tempList = wipExtendIdentificationRepository.getAllChildSn(snTempList);
            if (!CollectionUtils.isEmpty(tempList)) {
                wipExtendIdentificationList.addAll(tempList);
            }
        }
        return wipExtendIdentificationList;
    }

    /**
     * 根据id查询绑定信息
     * @param list
     * @return
     */
    @Override
    public List<WipExtendIdentification> getWipExtendByIds(List<String> list) {
        List<WipExtendIdentification> wipExtendIdentificationList = new ArrayList<>();
        for (List<String> tempIdList : CommonUtils.splitList(list, Constant.BATCH_SIZE_NINE_HUNDRED)) {
            List<WipExtendIdentification> wipExtendIdentifications = wipExtendIdentificationRepository.getWipExtendByIds(tempIdList);
            if (!CollectionUtils.isEmpty(wipExtendIdentifications)) {
                wipExtendIdentificationList.addAll(wipExtendIdentifications);
            }

        }
        return wipExtendIdentificationList;
    }

    /**
     * 查询任务料单下的需绑清单和已绑清单
     * @param prodBindingSetting
     * @return
     * @throws Exception
     */
    @Override
    public AuxMaterialBindingDTO getWipAndBindingSettingList(ProdBindingSettingDTO prodBindingSetting) throws Exception {
        AuxMaterialBindingDTO auxMaterialBindingDTO = new AuxMaterialBindingDTO();
        // 绑定清单
        List<ProdBindingSettingDTO> bindSettingLists = prodBindingSettingRepository.getBindingSettingList(prodBindingSetting);
        if (CollectionUtils.isEmpty(bindSettingLists)){
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,MessageId.PRODUCT_CODE_IS_NOT_EXIST_BIND_LIST,
                    new Object[]{prodBindingSetting.getProductCode()});
        }
        // 已绑定的辅料清单
        WipExtendIdentification wipExtendIdentification = new WipExtendIdentification();
        wipExtendIdentification.setTaskNo(prodBindingSetting.getTaskNo());
        List<WipExtendIdentification> bindingList = wipExtendIdentificationRepository.bindingWipListByTaskNo(wipExtendIdentification);
        // 按物料代码分组
        Map<String, List<WipExtendIdentification>>  bindMap = new HashMap<>();
        // 按替代物料代码分组
        Map<String, List<WipExtendIdentification>>  replaceBindMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(bindingList)){
            bindMap = bindingList.stream().filter(w -> StringUtils.isNotEmpty(w.getItemNo()))
                    .collect(Collectors.groupingBy(WipExtendIdentification::getItemNo));
            replaceBindMap = bindingList.stream().filter(w -> StringUtils.isNotEmpty(w.getReplaceItemNo()))
                    .collect(Collectors.groupingBy(WipExtendIdentification::getReplaceItemNo));
        }
        // 获取物料代码及替代物料的绑定信息
        setNeedAndHasBindCount(bindSettingLists, bindMap, replaceBindMap);
        auxMaterialBindingDTO.setBindingSettingList(bindSettingLists);
        return auxMaterialBindingDTO;
    }

    private void setNeedAndHasBindCount(List<ProdBindingSettingDTO> bindSettingLists, Map<String, List<WipExtendIdentification>> bindMap, Map<String, List<WipExtendIdentification>> replaceBindMap) {
        for (ProdBindingSettingDTO bindingSetting : bindSettingLists) {
            if(null==bindingSetting.getUsageCount()){
                throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE,MessageId.BIND_SETTING_USAGE_COUNT_IS_NULL,new String[]{bindingSetting.getProductCode(),bindingSetting.getItemCode()});
            }
            String itemCode = bindingSetting.getItemCode();
            // 获取有效的绑定数量
            List<WipExtendIdentification> itemBind = new ArrayList<>();
            List<WipExtendIdentification> bindListForItemNo = bindMap.get(itemCode);
            List<WipExtendIdentification> bindListForReplaceItemNo = replaceBindMap.get(itemCode);
            if(!CollectionUtils.isEmpty(bindListForItemNo)){
                itemBind.addAll(bindListForItemNo);
            }
            if(!CollectionUtils.isEmpty(bindListForReplaceItemNo)){
                itemBind.addAll(bindListForReplaceItemNo);
            }
            // 计算已绑定数量
            double formQty = 0;
            if (!CollectionUtils.isEmpty(itemBind)) {
                for (WipExtendIdentification item : itemBind) {
                    formQty += item.getFormQty().doubleValue();
                }
            }
            bindingSetting.setBindedCount(BigDecimal.valueOf(formQty));
            bindingSetting.setNumOfBind(BigDecimal.valueOf(formQty).divide(bindingSetting.getUsageCount(), 2, RoundingMode.DOWN));
        }
    }

    /**
     * 校验辅料条码有效性
     * @param wipExtendIdentification
     * @return
     * @throws Exception
     */
    @Override
    public BarcodeExpandDTO checkBarcodeNeedBind(WipExtendIdentification wipExtendIdentification) throws Exception {
        if(StringUtils.isEmpty(wipExtendIdentification.getSn())){
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,MessageId.SN_NULL_VALID);
        }
        List<BarcodeExpandDTO> barcodeExpandDTOList = assemblyRelaScanService.getBarCodeBySubSn(wipExtendIdentification.getSn());
        if(CollectionUtils.isEmpty(barcodeExpandDTOList)){
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,MessageId.SN_IS_NOT_EXIST);
        }
        BarcodeExpandDTO barcodeExpandDTO = getBarcodeExpandDTO(wipExtendIdentification, barcodeExpandDTOList);
        // 根据条码类型校验是否已存在于绑定清单中
        List<WipExtendIdentification> bindingList = wipExtendIdentificationRepository.bindingWipListByTaskNo(wipExtendIdentification);
        List<String> existList = bindingList.stream().map(WipExtendIdentification::getSn).filter(item -> item.equals(wipExtendIdentification.getSn())).collect(Collectors.toList());
        if (Constant.TYPE_SEQUENCE_CODE.equals(barcodeExpandDTO.getParentCategoryName()) && !CollectionUtils.isEmpty(existList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,MessageId.BARCODE_EXIST_IN_BIND_LIST,new Object[]{wipExtendIdentification.getSn()});
        }
        List<String> existCategoryList = bindingList.stream().map(WipExtendIdentification::getCategoryCode).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if (!existCategoryList.isEmpty()) {
            List<String> categoryList = existCategoryList.stream().filter(item -> item.equals(barcodeExpandDTO.getParentCategoryCode())).collect(Collectors.toList());
            if (categoryList.isEmpty()) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.BARCODE_CATEGORY_NOT_IN_BIND_LIST, new Object[]{wipExtendIdentification.getSn(), barcodeExpandDTO.getParentCategoryName()});
            }
        }
        return barcodeExpandDTO;
    }

    /**
     * 获取条码中心条码属性
     * @param wipExtendIdentification
     * @param barcodeExpandDTOList
     * @return
     * @throws Exception
     */
    public BarcodeExpandDTO getBarcodeExpandDTO(WipExtendIdentification wipExtendIdentification, List<BarcodeExpandDTO> barcodeExpandDTOList) throws Exception {
        BarcodeExpandDTO barcodeExpandDTO = barcodeExpandDTOList.get(NumConstant.NUM_ZERO);
        // 根据料单代码和绑定类型查询需绑定清单
        ProdBindingSettingDTO prodBindingSetting = new ProdBindingSettingDTO();
        prodBindingSetting.setBindType(NumConstant.NUM_TWO);
        prodBindingSetting.setProductCode(wipExtendIdentification.getFormItemNo());
        List<ProdBindingSettingDTO> bindSettingLists = prodBindingSettingRepository.getBindingSettingList(prodBindingSetting);
        if (CollectionUtils.isEmpty(bindSettingLists)){
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,MessageId.PRODUCT_CODE_IS_NOT_EXIST_BIND_LIST,
                    new Object[]{wipExtendIdentification.getTaskNo()});
        }
        List<String> bindItemCode = bindSettingLists.stream().map(ProdBindingSettingDTO::getItemCode).distinct().collect(Collectors.toList());
        if(bindItemCode.contains(barcodeExpandDTO.getItemCode())){
            return barcodeExpandDTO;
        }
        List<String> itemCodes = new ArrayList<>();
        itemCodes.add(barcodeExpandDTO.getItemCode());
        // 获取替代物料
        List<MtlRelatedItemsEntityDTO> resultDTOs = assemblyRelaScanService.getReplaceItemByErp(itemCodes, true);
        if(!CollectionUtils.isEmpty(resultDTOs)){
            for (MtlRelatedItemsEntityDTO mtlRelatedItemsEntityDTO : resultDTOs) {
                // 替代物料
                String replaceItemCode = mtlRelatedItemsEntityDTO.getInventoryItemCode();
                if(bindItemCode.contains(replaceItemCode)){
                    barcodeExpandDTO.setReplaceItemCode(replaceItemCode);
                    wipExtendIdentification.setReplaceItemNo(replaceItemCode);
                    return barcodeExpandDTO;
                }
            }
        }
        throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,MessageId.AUX_SN_CODE_IS_NOT_EXIST_IN_BIND_LIST,new Object[]{barcodeExpandDTO.getBarcode(),barcodeExpandDTO.getItemCode()});
    }

    @Override
    public WipExtendIdentification auxMaterialBinding(WipExtendIdentification wipExtendIdentification) throws Exception {
        String key = RedisKeyConstant.AUX_MATERIAL_BIND_LOCK + wipExtendIdentification.getFactoryId() + wipExtendIdentification.getTaskNo();
        RedisLock redisLock = new RedisLock(key);
        WipExtendIdentification returnInfo = new WipExtendIdentification();
        if (!redisLock.lock()) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.TASK_NO_EXIST_AUX_BIND_LOCK);
        }
        try{
            // 校验辅料条码的物料代码或替代物料是否在绑定清单
            BarcodeExpandDTO barcodeExpandDTO = this.checkBarcodeNeedBind(wipExtendIdentification);
            // 校验环保属性
            checkLead(wipExtendIdentification, barcodeExpandDTO);
            // 写入辅料绑定记录
            WipExtendIdentification extendInfo = new WipExtendIdentification();
            extendInfo.setIdentiId(java.util.UUID.randomUUID().toString());
            extendInfo.setSn(wipExtendIdentification.getSn());
            extendInfo.setMainProductCode(wipExtendIdentification.getFormItemNo());
            extendInfo.setFormQty(wipExtendIdentification.getFormQty());
            extendInfo.setItemNo(barcodeExpandDTO.getItemCode());
            extendInfo.setAttribute1(barcodeExpandDTO.getItemName());
            extendInfo.setReplaceItemNo(wipExtendIdentification.getReplaceItemNo());
            extendInfo.setFormType(MpConstant.STRING_FOUR);
            extendInfo.setTaskNo(wipExtendIdentification.getTaskNo());
            extendInfo.setLastUpdatedBy(wipExtendIdentification.getCreateBy());
            extendInfo.setCreateBy(wipExtendIdentification.getCreateBy());
            extendInfo.setFactoryId(wipExtendIdentification.getFactoryId());
            extendInfo.setIsZsIdentification(Constant.FLAG_N);
            extendInfo.setCategoryCode(barcodeExpandDTO.getParentCategoryCode());
            wipExtendIdentificationRepository.insertWipExtendIdentification(extendInfo);
            //组装前端信息
            setReturnInfo(returnInfo, extendInfo);
            // 写绑定操作历史
            this.insertOptRecord(NumConstant.STR_ONE,extendInfo);
        }finally {
            redisLock.unlock();
        }
        return returnInfo;
    }

    /**
     * 组装前端信息
     * @param returnInfo
     * @param extendInfo
     * @throws Exception
     */
    private void setReturnInfo(WipExtendIdentification returnInfo, WipExtendIdentification extendInfo) throws Exception {
        BarcodeExpandQueryDTO barcodeExpandQueryDTO = new BarcodeExpandQueryDTO();
        List<String> barcodList = new ArrayList<>();
        barcodList.add(extendInfo.getSn());
        barcodeExpandQueryDTO.setBarcodeList(barcodList);
        List<BarcodeExpandDTO> barcodeTempList = barcodeCenterRemoteService.expandQuery(barcodeExpandQueryDTO);
        returnInfo.setIdentiId(extendInfo.getIdentiId());
        returnInfo.setFormCatogary(barcodeTempList.get(0).getParentCategoryName());
        returnInfo.setItemNo(extendInfo.getItemNo());
        returnInfo.setAttribute1(extendInfo.getAttribute1());
        returnInfo.setSupplierName(barcodeTempList.get(0).getSupplierName());
        returnInfo.setSpecModel(barcodeTempList.get(0).getSpecModel());
        returnInfo.setBrandName(barcodeTempList.get(0).getBrandName());
    }

    public void checkLead(WipExtendIdentification wipExtendIdentification, BarcodeExpandDTO barcodeExpandDTO) {
        List<SysLookupValuesDTO> lookupValues = BasicsettingRemoteService.getSysLookupValues(Constant.LEAD_CHECK_FLAG_652303);
        boolean leadCheckOpen = lookupValues.stream()
                .filter(lk -> Constant.LEAD_CHECK_FLAG_652303001.compareTo(lk.getLookupCode()) == Constant.INT_0)
                .anyMatch(lk -> Constant.FLAG_Y.equals(lk.getLookupMeaning()));
        //查询1036环保属性数据字典
        if (!leadCheckOpen) {
            return;
        }
        Map<String, Object> map = new HashMap<>(2);
        map.put("lookupType", Constant.ENV_LOOK_UP_TYPES);
        List<SysLookupValuesDTO> listSys = BasicsettingRemoteService.getSysLookupValuesList(new SysLookupValuesDTO() {{
            setLookupType(new BigDecimal(Constant.ENV_LOOK_UP_TYPES));
        }});
        if (CollectionUtils.isEmpty(listSys)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SYS_LOOK_NOT_CONFIG, new Object[]{Constant.ENV_LOOK_UP_TYPES});
        }
        Map<String, SysLookupValuesDTO> leadMap = listSys.stream().collect(Collectors.toMap(SysLookupValuesDTO::getDescriptionChin, a -> a, (k1, k2) -> k1));
        String barcodeLead = barcodeExpandDTO.getIsLead();
        String taskLead = wipExtendIdentification.getFormHbCode();
        SysLookupValuesDTO barcodeSys = leadMap.get(barcodeLead);
        if (barcodeSys == null) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.ISLEAD_DATA_DICTIONARY_DOES_NOT_EXIST, new Object[]{barcodeExpandDTO.getIsLead()});
        }
        // 单板场景环保属性为1036中lookupMeaning值，直接比较,数据字典>条码环保属性说明可以使用
        if (dealStepCase(barcodeSys.getLookupMeaning(), taskLead)) {
            return;
        }
        SysLookupValuesDTO taskSys = leadMap.get(taskLead);
        if (taskSys == null) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.ISLEAD_DATA_DICTIONARY_DOES_NOT_EXIST, new Object[]{wipExtendIdentification.getFormHbCode()});
        }
        //环保属性校验
        if (new BigDecimal(barcodeSys.getAttribute1()).compareTo(new BigDecimal(taskSys.getAttribute1())) < NumConstant.NUM_ZERO) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.BARCODE_LEAD_NEED_OVER_TASK_LEAD, new Object[]{barcodeLead, taskLead});
        }
    }

    private boolean dealStepCase(String lookupMeaning, String taskLead) {
        /* Started by AICoder, pid:mc75a9d6a2a3525141010ae04094f11e16447bbb */
        try {
            int num1 = Integer.parseInt(lookupMeaning);
            int num2 = Integer.parseInt(taskLead);
            if (num1 >= num2) {
                return true;
            }
        } catch (NumberFormatException e) {
            return false;
        }
        return false;
        /* Ended by AICoder, pid:mc75a9d6a2a3525141010ae04094f11e16447bbb */
    }

    /**
     * 解绑操作
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int auxMaterialUnBind(WipExtendIdentification wipExtendIdentification) throws Exception {
        if(StringUtils.isEmpty(wipExtendIdentification.getTaskNo()) || StringUtils.isEmpty(wipExtendIdentification.getSn())){
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,MessageId.TASK_BATCH_BARCODES_CANNOT_ALL_BE_EMPTY);
        }
        //写解绑日志
        AssemblyRelationshipQueryDTO assemblyRelationshipQueryDTO = new AssemblyRelationshipQueryDTO();
        assemblyRelationshipQueryDTO.setCreateBy(wipExtendIdentification.getCreateBy());
        assemblyRelationshipQueryDTO.setTaskNo(wipExtendIdentification.getTaskNo());
        assemblyRelationshipQueryDTO.setSn(wipExtendIdentification.getSn());
        assemblyRelationshipQueryDTO.setId(wipExtendIdentification.getIdentiId());
        int update = wipExtendIdentificationRepository.unBindAuxBindRelation(wipExtendIdentification);
        int insert = assemblyOptRecordRepository.insertOptRecordFromAuxBind(assemblyRelationshipQueryDTO);
        redisTemplate.delete(RedisKeyConstant.AUX_BINDING_CHECK_KEY + wipExtendIdentification.getTaskNo());
        return update;
    }

    /**
     * 查询已产出数量
     * @param taskNo
     * @return
     * @throws Exception
     */
    @Override
    public Map<String, Integer> queryQuantityProducedOfTaskNo(String taskNo) throws Exception {
        if(StringUtils.isEmpty(taskNo)){
            return new HashMap<>();
        }
        List<WipExtendIdentification> wipExtendIdentificationList = wipExtendIdentificationRepository.queryQuantityProducedOfTaskNo(taskNo);
        if(CollectionUtils.isEmpty(wipExtendIdentificationList)){
            return new HashMap<>();
        }
        Map<String, Integer> returnMap = new HashMap<>();
        Map<String, Integer> map = wipExtendIdentificationList.stream().collect(Collectors.toMap(WipExtendIdentification::getProdPlanId,WipExtendIdentification::getOutputQty));
        for (Map.Entry<String, Integer> entry : map.entrySet()) {
            if(entry.getKey() == null){
                returnMap.put("",entry.getValue());
            }else{
                returnMap.put(entry.getKey(),entry.getValue());
            }
        }
        int totalQty = wipExtendIdentificationRepository.queryTotalQty(taskNo);
        returnMap.put(Constant.TOTAL_QTY,totalQty);
        return returnMap;
    }
}
