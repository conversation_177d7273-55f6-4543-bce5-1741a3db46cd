package com.zte.autoTest.unitTest;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.builder.ExcelWriterSheetBuilder;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.zte.application.datawb.impl.BarSubmitServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.datawb.BarSubmit;
import com.zte.domain.model.datawb.BarSubmitRepository;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.HttpRemoteService;
import com.zte.interfaces.dto.centerfactory.HrmPersonInfoDTO;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.common.redis.RedisLock;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.ImesExcelUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.util.PowerBaseTestCase;
import org.aspectj.weaver.patterns.DeclareTypeErrorOrWarning;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;

import static org.mockito.ArgumentMatchers.*;

/**
 * <AUTHOR>
 * @date 2021-11-23 15:42
 */
@PrepareForTest({MicroServiceRestUtil.class, EasyExcelFactory.class,ImesExcelUtil.class,ServiceDataBuilderUtil.class,JacksonJsonConverUtil.class,RedisHelper.class, RedisLock.class})
public class KxStepKxStepBarSubmitServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    private BarSubmitServiceImpl barSubmitServiceImpl;

    @Mock
    private BarSubmitRepository repository;
	@Mock
	private CenterfactoryRemoteService centerfactoryRemoteService;
	@Mock
	HttpServletRequest request;
	@Mock
	HttpServletResponse response;
	@Mock
	private ExcelWriter excelWriter;
	@Mock
	private WriteSheet build;
	@Mock
	private ExcelWriterSheetBuilder excelWriterSheetBuilder;
	@Mock
	private ExcelWriterBuilder write;
	@Mock
	private ExcelWriterBuilder excelWriterBuilder;

    @Test
    public void queryBarSubmitInfo() {
        List<String> billNoList = new LinkedList<>();
        billNoList.add("123");
        List<BarSubmit> barSubmits = new LinkedList<>();
        BarSubmit barSubmit = new BarSubmit();
        barSubmits.add(barSubmit);
        PowerMockito.when(repository.queryBarSubmitInfo(Mockito.anyList()))
                .thenReturn(barSubmits)
        ;
		Assert.assertNotNull(barSubmitServiceImpl.queryBarSubmitInfo(billNoList));
    }

    @Test
    public void getByPlanIds() {
        List<String> billNoList = new LinkedList<>();
        billNoList.add("123");
        List<BarSubmit> barSubmits = new LinkedList<>();
        BarSubmit barSubmit = new BarSubmit();
        barSubmits.add(barSubmit);
        barSubmitServiceImpl.getByPlanIds(billNoList);
        PowerMockito.when(repository.getByPlanIds(Mockito.anyList()))
                .thenReturn(barSubmits)
        ;
		Assert.assertNotNull(barSubmitServiceImpl.getByPlanIds(billNoList));
    }

	@Test
	public void getInfoPage() throws Exception {
		BarSubmit dto = new BarSubmit();
		dto.setPoster("00286569");
		dto.setVerifier("00286569");
		try{
			barSubmitServiceImpl.getInfoPage(dto);
		}catch (Exception e){
			Assert.assertEquals(MessageId.QUERY_PARAM_ERROR, e.getMessage());
		}
		dto.setProdplanId(7116112L);
		List<BarSubmit> list = new ArrayList<>();
		list.add(dto);
		PowerMockito.when(repository.getInfoPage(Mockito.any())).thenReturn(list);
		Map<String, HrmPersonInfoDTO> hrmPersonInfoDTOMap = new HashMap<>();
		HrmPersonInfoDTO hrmPersonInfoDTO = new HrmPersonInfoDTO();
		hrmPersonInfoDTOMap.put("00286569", hrmPersonInfoDTO);
		PowerMockito.when(centerfactoryRemoteService.getHrmPersonInfo(anyList())).thenReturn(hrmPersonInfoDTOMap);
		barSubmitServiceImpl.getInfoPage(dto);

		dto.setSubmitDateStart("2023-02-13 00:00:00");
		dto.setSubmitDateEnd("2023-09-13 00:00:00");
		try{
			barSubmitServiceImpl.getInfoPage(dto);
		}catch (Exception e){
			Assert.assertEquals(MessageId.SUBMIT_TIME_INTERVAL_ERROR, e.getMessage());
		}
		BarSubmit dto1 = new BarSubmit();
		dto1.setVerifyDateStart("2023-02-13 00:00:00");
		dto1.setVerifyDateEnd("2023-09-13 00:00:00");
		try{
			barSubmitServiceImpl.getInfoPage(dto1);
		}catch (Exception e){
			Assert.assertEquals(MessageId.VERIFY_TIME_INTERVAL_ERROR, e.getMessage());
		}
	}

	@Test
	public void getCountByCondition() throws Exception {
		BarSubmit dto = new BarSubmit();
		dto.setProdplanId(7116112L);
		PowerMockito.when(repository.getCountByCondition(Mockito.any())).thenReturn(1);
		Assert.assertNotNull(barSubmitServiceImpl.getCountByCondition(dto));
	}
	@Test
	public void exportBarSubmitInfo() throws Exception {
		PowerMockito.mockStatic(EasyExcelFactory.class);
		PowerMockito.mockStatic(ImesExcelUtil.class);
		PowerMockito.mockStatic(MicroServiceRestUtil.class);
		PowerMockito.mockStatic(ServiceDataBuilderUtil.class, JacksonJsonConverUtil.class);
		BarSubmit dto = new BarSubmit();
		dto.setPoster("00286569");
		dto.setVerifier("00286569");
		dto.setSubmitDate(new Date());
		dto.setVerifyDate(new Date());
		dto.setProdplanId(7116112L);
		dto.setVerifyDate(new Date());
		dto.setSubmitDate(new Date());
		List<BarSubmit> list = new ArrayList<>();
		list.add(dto);
		PowerMockito.when(repository.getCountByCondition(Mockito.any())).thenReturn(1);
		PowerMockito.when(repository.getInfoPage(Mockito.any())).thenReturn(list);


		PowerMockito.when(EasyExcelFactory.write(response.getOutputStream(), BarSubmit.class)).thenReturn(write);
		PowerMockito.when(write.excelType(ExcelTypeEnum.XLSX)).thenReturn(excelWriterBuilder);
		PowerMockito.when(excelWriterBuilder.build()).thenReturn(excelWriter);

		PowerMockito.when(EasyExcelFactory.writerSheet(0,  Constant.BAR_SUBMIT_EXPORT)).thenReturn(excelWriterSheetBuilder);
		PowerMockito.when(excelWriterSheetBuilder.build()).thenReturn(build);

		Map<String, HrmPersonInfoDTO> hrmPersonInfoDTOMap = new HashMap<>();
		HrmPersonInfoDTO hrmPersonInfoDTO = new HrmPersonInfoDTO();
		hrmPersonInfoDTOMap.put("00286569", hrmPersonInfoDTO);
		PowerMockito.when(centerfactoryRemoteService.getHrmPersonInfo(anyList())).thenReturn(hrmPersonInfoDTOMap);
		barSubmitServiceImpl.exportBarSubmitInfo(request,response,dto,"00286569");

		BarSubmit dto2 = new BarSubmit();
		dto2.setPoster("00286569");
		dto2.setVerifier("00286569");
		dto2.setProdplanId(7116112L);
		barSubmitServiceImpl.exportBarSubmitInfo(request,response,dto2,"00286569");
		Assert.assertNotNull(dto);
	}

	@Test
	public void exportBarSubmitInfoTwo() throws Exception {
		PowerMockito.mockStatic(EasyExcelFactory.class);
		PowerMockito.mockStatic(ImesExcelUtil.class);
		PowerMockito.mockStatic(MicroServiceRestUtil.class);
		PowerMockito.mockStatic(ServiceDataBuilderUtil.class, JacksonJsonConverUtil.class);
		BarSubmit dto2 = new BarSubmit();
		dto2.setProdplanId(7116112L);
		PowerMockito.when(repository.getCountByCondition(Mockito.any())).thenReturn(0);
		PowerMockito.when(EasyExcelFactory.write(response.getOutputStream(), BarSubmit.class)).thenReturn(write);
		PowerMockito.when(write.excelType(ExcelTypeEnum.XLSX)).thenReturn(excelWriterBuilder);
		PowerMockito.when(excelWriterBuilder.build()).thenReturn(excelWriter);

		PowerMockito.when(EasyExcelFactory.writerSheet(0,  Constant.BAR_SUBMIT_EXPORT)).thenReturn(excelWriterSheetBuilder);
		PowerMockito.when(excelWriterSheetBuilder.build()).thenReturn(build);
		barSubmitServiceImpl.exportBarSubmitInfo(request,response,dto2,"00286569");
		Assert.assertNotNull(dto2);
	}

	@Test
	public void exportBarSubmitByEmail() throws Exception {
		PowerMockito.mockStatic(EasyExcelFactory.class,RedisHelper.class, RedisLock.class);
		PowerMockito.mockStatic(ImesExcelUtil.class);
		PowerMockito.mockStatic(MicroServiceRestUtil.class);
		PowerMockito.mockStatic(ServiceDataBuilderUtil.class,JacksonJsonConverUtil.class);

		PowerMockito.when(RedisHelper.setnx(Mockito.any(), Mockito.any(), Mockito.anyInt())).thenReturn(true);
		BarSubmit dto = new BarSubmit();
		dto.setProdplanId(7116112L);
		dto.setPoster("00286569");
		dto.setVerifier("00286569");
		dto.setVerifyDate(new Date());
		dto.setSubmitDate(new Date());
		List<BarSubmit> list = new ArrayList<>();
		list.add(dto);
		PowerMockito.when(repository.getInfoPage(Mockito.any())).thenReturn(list);
		PowerMockito.when(EasyExcelFactory.write(response.getOutputStream(), BarSubmit.class)).thenReturn(write);
		PowerMockito.when(EasyExcelFactory.write(Mockito.anyString())).thenReturn(write);
		PowerMockito.when(write.excelType(ExcelTypeEnum.XLSX)).thenReturn(excelWriterBuilder);
		PowerMockito.when(excelWriterBuilder.build()).thenReturn(excelWriter);
		PowerMockito.when(EasyExcelFactory.writerSheet(0, Constant.BAR_SUBMIT_EXPORT)).thenReturn(excelWriterSheetBuilder);
		PowerMockito.when(excelWriterSheetBuilder.build()).thenReturn(build);
		Map<String, HrmPersonInfoDTO> hrmPersonInfoDTOMap = new HashMap<>();
		HrmPersonInfoDTO hrmPersonInfoDTO = new HrmPersonInfoDTO();
		hrmPersonInfoDTOMap.put("00286569", hrmPersonInfoDTO);
		PowerMockito.when(centerfactoryRemoteService.getHrmPersonInfo(anyList())).thenReturn(hrmPersonInfoDTOMap);

		PowerMockito.mockStatic(RedisHelper.class);
		PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(false);
		barSubmitServiceImpl.exportBarSubmitByEmail(request,response,dto,"00286569");
		Assert.assertNotNull(dto);
	}
}
