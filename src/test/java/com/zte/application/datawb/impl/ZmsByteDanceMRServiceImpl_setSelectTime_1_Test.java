/*Started by AICoder, pid:7ca9fg7e597cc5e14ad60b9aa0dc2d621e8595a7*/
package com.zte.application.datawb.impl;

import com.alibaba.fastjson.JSON;
import com.zte.application.datawb.WsmAssembleLinesService;
import com.zte.common.CommonUtils;
import com.zte.common.utils.DateUtil;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.interfaces.dto.ZmsByteDanceMRDTO;
import com.zte.interfaces.dto.ZmsByteDanceMRSelectDTO;
import com.zte.springbootframe.util.json.JacksonJsonConverUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import static org.junit.Assert.assertNotNull;

@RunWith(PowerMockRunner.class)
@PrepareForTest({CenterfactoryRemoteService.class,
        WsmAssembleLinesService.class, JacksonJsonConverUtil.class,
        JSON.class, CommonUtils.class, DateUtil.class})
@PowerMockIgnore({"javax.net.ssl.*", "javax.security.*"})
public class ZmsByteDanceMRServiceImpl_setSelectTime_1_Test {
    private ZmsByteDanceMRServiceImpl zmsByteDanceMRService;
    private ZmsByteDanceMRDTO zmsByteDanceMRDTO;
    private ZmsByteDanceMRSelectDTO zmsByteDanceMRSelectDTO;

    @Before
    public void setUp() throws Exception {
        zmsByteDanceMRService = new ZmsByteDanceMRServiceImpl();
        zmsByteDanceMRDTO = new ZmsByteDanceMRDTO();
        zmsByteDanceMRSelectDTO = new ZmsByteDanceMRSelectDTO();
    }

    @Test(timeout = 8000)
    public void testSetSelectTimeWithEmptyDataTime() {
        zmsByteDanceMRDTO.setDataTime("");
        zmsByteDanceMRService.setSelectTime(zmsByteDanceMRDTO, zmsByteDanceMRSelectDTO);
        assertNotNull(zmsByteDanceMRSelectDTO.getStartTime());
        assertNotNull(zmsByteDanceMRSelectDTO.getEndTime());
    }

    @Test(timeout = 8000)
    public void testSetSelectTimeWithNonEmptyDataTime() {
        String dateString = "2022-01-01";
        zmsByteDanceMRDTO.setDataTime(dateString);
        zmsByteDanceMRService.setSelectTime(zmsByteDanceMRDTO, zmsByteDanceMRSelectDTO);
        assertNotNull(zmsByteDanceMRSelectDTO.getStartTime());
        assertNotNull(zmsByteDanceMRSelectDTO.getEndTime());
    }
}
/*Ended by AICoder, pid:7ca9fg7e597cc5e14ad60b9aa0dc2d621e8595a7*/