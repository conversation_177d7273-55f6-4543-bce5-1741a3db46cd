package com.zte.application.stepdt.impl;

import com.zte.common.utils.Constant;
import com.zte.domain.model.stepdt.SyssubBomInfo;
import com.zte.domain.model.stepdt.SyssubBomInfoRepository;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;

import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023/1/30 15:06
 * @Description
 */
public class SyssubBomInfoServiceImplTest {
    @Mock
    SyssubBomInfoRepository syssubBomInfoRepository;
    @InjectMocks
    SyssubBomInfoServiceImpl syssubBomInfoServiceImpl;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testSaveOrUpdateSyssubBomInfo() throws Exception {
        when(syssubBomInfoRepository.selectListByBomNo(any())).thenReturn(Arrays.<SyssubBomInfo>asList(new SyssubBomInfo()));
        when(syssubBomInfoRepository.insertBatch(any())).thenReturn(Long.valueOf(1));
        when(syssubBomInfoRepository.updateByBomNo(any())).thenReturn(Long.valueOf(1));

        syssubBomInfoServiceImpl.saveOrUpdateSyssubBomInfo(Arrays.<SyssubBomInfo>asList(new SyssubBomInfo()));
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

    @Test
    public void testSaveOrUpdateSyssubBomInfo2() throws Exception {
        when(syssubBomInfoRepository.selectListByBomNo(any())).thenReturn(Arrays.<SyssubBomInfo>asList());
        when(syssubBomInfoRepository.insertBatch(any())).thenReturn(Long.valueOf(1));
        when(syssubBomInfoRepository.updateByBomNo(any())).thenReturn(Long.valueOf(1));

        syssubBomInfoServiceImpl.saveOrUpdateSyssubBomInfo(Arrays.<SyssubBomInfo>asList(new SyssubBomInfo()));
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }
}