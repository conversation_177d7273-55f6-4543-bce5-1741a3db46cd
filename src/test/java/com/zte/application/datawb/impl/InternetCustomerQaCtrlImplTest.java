package com.zte.application.datawb.impl;

import com.google.common.collect.Lists;
import com.zte.application.datawb.ZmsCommonService;
import com.zte.application.datawb.ZmsOverallUnitService;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.datawb.*;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.interfaces.dto.*;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

import static com.zte.common.utils.Constant.*;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;

@PrepareForTest({CommonUtils.class})
public class InternetCustomerQaCtrlImplTest extends PowerBaseTestCase {
    @InjectMocks
    InternetCustomerQaCtrlImpl internetCustomerQaCtrlImpl;

    @Mock
    CenterfactoryRemoteService centerfactoryRemoteService;

    @Mock
    WsmAssembleLinesRepository wsmAssembleLinesRepository;

    @Mock
    PDVMBarCodeLMSRepository pDVMBarCodeLMSRepository;

    @Mock
    private ZmsStationLogUploadRepository zmsStationLogUploadRepository;
    @Mock
    private CfgCodeRuleItemServiceImpl cfgCodeRuleItemService;
    @Mock
    private ZmsForwardTencentRepository zmsForwardTencentRepository;
    @Mock
    private ZmsOverallUnitService zmsOverallUnitService;
    @Mock
    private ZmsAlibabaRepository zmsAlibabaRepository;
    @Mock
    private ZmsCommonService zmsCommonService;

    @Test
    public void handleInternetCustomerQaCtrl() throws Exception {
        InternetCustomerQaCtrlDTO dto = new InternetCustomerQaCtrlDTO();
        try {
            internetCustomerQaCtrlImpl.handleInternetCustomerQaCtrl(dto);
        } catch (Exception ex) {
            Assert.assertEquals(((MesBusinessException) ex).getExCode(), "0005");
        }
        dto.setCustomerName("333");
        try {
            List<String> serverSnList = new ArrayList<>();
            PowerMockito.when(zmsStationLogUploadRepository.getServerSnList()).thenReturn(serverSnList);
            internetCustomerQaCtrlImpl.handleInternetCustomerQaCtrl(dto);
        } catch (Exception ex) {
            Assert.assertEquals(((MesBusinessException) ex).getExCode(), "0005");
        }

        try {
            List<String> serverSnList = new ArrayList<>();
            serverSnList.add("1111");
            PowerMockito.when(zmsStationLogUploadRepository.getServerSnList()).thenReturn(serverSnList);
            internetCustomerQaCtrlImpl.handleInternetCustomerQaCtrl(dto);
        } catch (Exception ex) {
            Assert.assertEquals(((MesBusinessException) ex).getExCode(), "0005");
        }

        List<String> tasknos = new ArrayList<>();
        for (int i = 0; i < 103; i++) {
            tasknos.add(i + "");
        }
        dto.setTasknos(tasknos);
        try {
            internetCustomerQaCtrlImpl.handleInternetCustomerQaCtrl(dto);
        } catch (Exception ex) {
            Assert.assertEquals(((MesBusinessException) ex).getExCode(), "0005");
        }

        List<String> snList = new ArrayList<>();
        for (int i = 0; i < 506; i++) {
            snList.add(i + "");
        }
        dto.setTasknos(null);
        dto.setSnList(snList);
        try {
            internetCustomerQaCtrlImpl.handleInternetCustomerQaCtrl(dto);
        } catch (Exception ex) {
            Assert.assertEquals(((MesBusinessException) ex).getExCode(), "0005");
        }

        dto = new InternetCustomerQaCtrlDTO();
        dto.setCustomerName("333");
        tasknos = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            tasknos.add(i + "");
        }
        dto.setTasknos(tasknos);
        try {
            internetCustomerQaCtrlImpl.handleInternetCustomerQaCtrl(dto);
        } catch (Exception ex) {
            Assert.assertEquals(true, true);
        }
        dto = new InternetCustomerQaCtrlDTO();
        dto.setCustomerName("333");
        snList = new ArrayList<>();
        for (int i = 0; i < 6; i++) {
            snList.add(i + "");
        }
        dto.setTasknos(null);
        dto.setSnList(snList);
        try {
            internetCustomerQaCtrlImpl.handleInternetCustomerQaCtrl(dto);
        } catch (Exception ex) {
            Assert.assertEquals(true, true);
        }
    }

    @Test
    public void setInternetCustomerQaCtrl() throws Exception {
        InternetCustomerQaCtrlDTO dto = new InternetCustomerQaCtrlDTO();
        List<InternetCustomerQaCtrlOutDTO> internetList = new ArrayList<>();
        List<InternetCustomerQaCtrlOutDTO> result =  internetCustomerQaCtrlImpl.setInternetCustomerQaCtrl(dto,internetList);
        Assert.assertEquals(result.size(),0);

        InternetCustomerQaCtrlOutDTO  dtoo = new InternetCustomerQaCtrlOutDTO();
        internetList.add(dtoo);
        List<PDVMInfoManageDTO> pdvmInfoManageDTOList = new ArrayList<>();
        PowerMockito.when(pDVMBarCodeLMSRepository.getSegmentInfoMangeOnly4(Mockito.any())).thenReturn(pdvmInfoManageDTOList);
        List<CustomerItemsDTO> customerItemsDTOList =new ArrayList<>();
        PowerMockito.when(centerfactoryRemoteService.getCustomerItemsInfo(Mockito.any(), Mockito.any())).thenReturn(customerItemsDTOList);
        result =  internetCustomerQaCtrlImpl.setInternetCustomerQaCtrl(dto,internetList);
        Assert.assertEquals(result.size() ,1 );

        dtoo = new InternetCustomerQaCtrlOutDTO();
        internetList.add(dtoo);
        pdvmInfoManageDTOList = new ArrayList<>();
        PowerMockito.when(pDVMBarCodeLMSRepository.getSegmentInfoMangeOnly4(Mockito.any())).thenReturn(pdvmInfoManageDTOList);
        customerItemsDTOList =new ArrayList<>();
        CustomerItemsDTO dfo = new CustomerItemsDTO();
        dfo.setProjectType("33");
        customerItemsDTOList.add(dfo);
        PowerMockito.when(centerfactoryRemoteService.getCustomerItemsInfo(Mockito.any(), Mockito.any())).thenReturn(customerItemsDTOList);
        result =  internetCustomerQaCtrlImpl.setInternetCustomerQaCtrl(dto,internetList);
        Assert.assertEquals(result.size() ,2 );

        dtoo = new InternetCustomerQaCtrlOutDTO();
        internetList.add(dtoo);
        pdvmInfoManageDTOList = new ArrayList<>();
        PowerMockito.when(pDVMBarCodeLMSRepository.getSegmentInfoMangeOnly4(Mockito.any())).thenReturn(pdvmInfoManageDTOList);
        customerItemsDTOList =new ArrayList<>();
        dfo = new CustomerItemsDTO();
        dfo.setProjectType("3");
        customerItemsDTOList.add(dfo);
        dfo = new CustomerItemsDTO();
        dfo.setProjectType("3");
        customerItemsDTOList.add(dfo);
        PowerMockito.when(centerfactoryRemoteService.getCustomerItemsInfo(Mockito.any(), Mockito.any())).thenReturn(customerItemsDTOList);
        result =  internetCustomerQaCtrlImpl.setInternetCustomerQaCtrl(dto,internetList);
        Assert.assertEquals(result.size() ,3 );

        dtoo = new InternetCustomerQaCtrlOutDTO();
        internetList.add(dtoo);
        pdvmInfoManageDTOList = new ArrayList<>();
        PowerMockito.when(pDVMBarCodeLMSRepository.getSegmentInfoMangeOnly4(Mockito.any())).thenReturn(pdvmInfoManageDTOList);
        customerItemsDTOList =new ArrayList<>();
        dfo = new CustomerItemsDTO();
        dfo.setProjectType("3");
        customerItemsDTOList.add(dfo);
        PowerMockito.when(centerfactoryRemoteService.getCustomerItemsInfo(Mockito.any(), Mockito.any())).thenReturn(customerItemsDTOList);
        List<CpmConfigItemAssembleDTO> cpmConfigItemAssembleDTOList =new ArrayList<>();
        PowerMockito.when(wsmAssembleLinesRepository.getAssemblyMaterialsByEntityName(Mockito.any())).thenReturn(cpmConfigItemAssembleDTOList);
        result =  internetCustomerQaCtrlImpl.setInternetCustomerQaCtrl(dto,internetList);
        Assert.assertEquals(result.size() ,4 );

        dtoo = new InternetCustomerQaCtrlOutDTO();
        internetList.add(dtoo);
        pdvmInfoManageDTOList = new ArrayList<>();
        PowerMockito.when(pDVMBarCodeLMSRepository.getSegmentInfoMangeOnly4(Mockito.any())).thenReturn(pdvmInfoManageDTOList);
        customerItemsDTOList =new ArrayList<>();
        dfo = new CustomerItemsDTO();
        dfo.setProjectType("3");
        customerItemsDTOList.add(dfo);
        PowerMockito.when(centerfactoryRemoteService.getCustomerItemsInfo(Mockito.any(), Mockito.any())).thenReturn(customerItemsDTOList);
        cpmConfigItemAssembleDTOList =new ArrayList<>();
        CpmConfigItemAssembleDTO dgo = new CpmConfigItemAssembleDTO();
        cpmConfigItemAssembleDTOList.add(dgo);
        PowerMockito.when(wsmAssembleLinesRepository.getAssemblyMaterialsByEntityName(Mockito.any())).thenReturn(cpmConfigItemAssembleDTOList);
        try {
            result = internetCustomerQaCtrlImpl.setInternetCustomerQaCtrl(dto, internetList);
        }catch (Exception ex) {
            Assert.assertEquals(result.size(), 5);
        }
        dtoo = new InternetCustomerQaCtrlOutDTO();
        internetList.add(dtoo);
        pdvmInfoManageDTOList = new ArrayList<>();
        PowerMockito.when(pDVMBarCodeLMSRepository.getSegmentInfoMangeOnly4(Mockito.any())).thenReturn(pdvmInfoManageDTOList);
        customerItemsDTOList =new ArrayList<>();
        dfo = new CustomerItemsDTO();
        dfo.setProjectType("3");
        customerItemsDTOList.add(dfo);
        PowerMockito.when(centerfactoryRemoteService.getCustomerItemsInfo(Mockito.any(), Mockito.any())).thenReturn(customerItemsDTOList);
        cpmConfigItemAssembleDTOList =new ArrayList<>();
        dgo = new CpmConfigItemAssembleDTO();
        cpmConfigItemAssembleDTOList.add(dgo);
        dgo = new CpmConfigItemAssembleDTO();
        cpmConfigItemAssembleDTOList.add(dgo);
        PowerMockito.when(wsmAssembleLinesRepository.getAssemblyMaterialsByEntityName(Mockito.any())).thenReturn(cpmConfigItemAssembleDTOList);
        result =  internetCustomerQaCtrlImpl.setInternetCustomerQaCtrl(dto,internetList);
        Assert.assertEquals(result.size() ,6);
    }

    @Test
    public void setInternetCustomerQaCtrlPlus() throws Exception {
        InternetCustomerQaCtrlDTO dto = new InternetCustomerQaCtrlDTO();
        List<InternetCustomerQaCtrlOutDTO> internetList =new ArrayList<>();
        List<InternetCustomerQaCtrlOutDTO> result = internetCustomerQaCtrlImpl.setInternetCustomerQaCtrlPlus(dto,internetList);
        Assert.assertEquals(result.size(),0);

        SysLookupValues sysLookupMPTSwitchY = new SysLookupValues();
        sysLookupMPTSwitchY.setDescription("Y");
        PowerMockito.when(zmsCommonService.getSysLookupValues(anyString())).thenReturn(sysLookupMPTSwitchY);

        InternetCustomerQaCtrlOutDTO dtooo= new InternetCustomerQaCtrlOutDTO();
        dtooo.setCode("");
        dtooo.setSn("1111");
        internetList.add(dtooo);
        dtooo= new InternetCustomerQaCtrlOutDTO();
        dtooo.setCode("000");
        dtooo.setSn("1111");
        internetList.add(dtooo);
        dtooo= new InternetCustomerQaCtrlOutDTO();
        dtooo.setCode("");
        dtooo.setSn("3333");
        internetList.add(dtooo);
        dtooo= new InternetCustomerQaCtrlOutDTO();
        dtooo.setCode("");
        dtooo.setSn("4444");
        internetList.add(dtooo);
        List<String> qualityCodeList =new ArrayList<>();
        qualityCodeList.add("1111,");
        qualityCodeList.add("2222,");

        List<String> zmsServerSNList =new ArrayList<>();
        zmsServerSNList.add("1111");
        zmsServerSNList.add("3333");
        dto.setCustomerName("北京字跳网络技术有限公司");
        PowerMockito.when(wsmAssembleLinesRepository.getZmsQualityCode(Mockito.any())).thenReturn(qualityCodeList);
        PowerMockito.when(wsmAssembleLinesRepository.getZmsServerSn(Mockito.any())).thenReturn(zmsServerSNList);
        result = internetCustomerQaCtrlImpl.setInternetCustomerQaCtrlPlus(dto,internetList);
        Assert.assertEquals(result.size(),4);

        dtooo= new InternetCustomerQaCtrlOutDTO();
        dtooo.setCode("");
        dtooo.setSn("1111");
        internetList.add(dtooo);
        dto.setCustomerName("北京三快在线科技有限公司");
        PowerMockito.when(wsmAssembleLinesRepository.getZmsQualityCode(Mockito.any())).thenReturn(qualityCodeList);
        PowerMockito.when(zmsStationLogUploadRepository.getMeiTuanServerSnList(Mockito.anyList())).thenReturn(zmsServerSNList);
        result = internetCustomerQaCtrlImpl.setInternetCustomerQaCtrlPlus(dto,internetList);
        Assert.assertEquals(result.size(),5);

        dtooo= new InternetCustomerQaCtrlOutDTO();
        dtooo.setCode("");
        dtooo.setSn("1111");
        internetList.add(dtooo);
        dto.setCustomerName("北京三快在线科技有限公司1111");
        PowerMockito.when(wsmAssembleLinesRepository.getZmsQualityCode(Mockito.any())).thenReturn(qualityCodeList);
        result = internetCustomerQaCtrlImpl.setInternetCustomerQaCtrlPlus(dto,internetList);
        Assert.assertEquals(result.size(),6);

        dtooo= new InternetCustomerQaCtrlOutDTO();
        dtooo.setCode("");
        dtooo.setSn("1111");
        internetList.add(dtooo);
        dto.setCustomerName(TENCENT_ZH);
        List<ZmsInternetMainDTO> mtpTestSnList = new ArrayList<>();
        ZmsInternetMainDTO zmsInternetMainDTO = new ZmsInternetMainDTO();
        try {
            PowerMockito.when(zmsForwardTencentRepository.getInternetMain(Mockito.any(), Mockito.any())).thenReturn(mtpTestSnList);
            internetCustomerQaCtrlImpl.setInternetCustomerQaCtrlPlus(dto, internetList);
        } catch (Exception ex) {
            Assert.assertEquals(((MesBusinessException) ex).getExCode(), "0005");
        }

        mtpTestSnList.add(zmsInternetMainDTO);
        PowerMockito.when(cfgCodeRuleItemService.getSmallBoxSize(Mockito.any())).thenReturn("1");
        List<String> snList = new ArrayList<>();
        result = internetCustomerQaCtrlImpl.setInternetCustomerQaCtrlPlus(dto,internetList);
        Assert.assertEquals(result.size(),7);
    }

    @Test
    public void handleByteDance() throws Exception {
        List<QualityCodeOutputDTO> qualityCodeOutputDTOList = new ArrayList<>();

        List<InternetCustomerQaCtrlOutDTO> internetList = new ArrayList<>();
        InternetCustomerQaCtrlOutDTO dto = new InternetCustomerQaCtrlOutDTO();
        dto.setCode("0001");
        dto.setSn("3333");
        internetList.add(dto);
        dto = new InternetCustomerQaCtrlOutDTO();
        dto.setCode("");
        dto.setSn("4444");
        internetList.add(dto);
        CustomerQualityDTO customerQualityDTO = new CustomerQualityDTO();
        PowerMockito.when(centerfactoryRemoteService.getRealTimeInteractiveB2B(Mockito.any())).thenReturn(qualityCodeOutputDTOList);
        internetCustomerQaCtrlImpl.handleByteDance(customerQualityDTO, internetList);
        Assert.assertEquals(true, true);

        qualityCodeOutputDTOList = new ArrayList<>();
        QualityCodeOutputDTO dtt = new QualityCodeOutputDTO();
        dtt.setServerSn("3333");
        qualityCodeOutputDTOList.add(dtt);
        dtt = new QualityCodeOutputDTO();
        dtt.setServerSn("4444");
        dtt.setQualityCode("4334");
        qualityCodeOutputDTOList.add(dtt);
        dtt = new QualityCodeOutputDTO();
        dtt.setServerSn("555");
        qualityCodeOutputDTOList.add(dtt);

        internetList = new ArrayList<>();
        dto = new InternetCustomerQaCtrlOutDTO();
        dto.setCode("");
        dto.setSn("3355533");
        internetList.add(dto);

        List<ZmsInternetMainDTO> zmsInternetMainDTOList = new ArrayList<>();
        ZmsInternetMainDTO zmsInternetMainDTO1 = new ZmsInternetMainDTO();
        zmsInternetMainDTO1.setServerSn("3333");
        zmsInternetMainDTOList.add(zmsInternetMainDTO1);
        ZmsInternetMainDTO zmsInternetMainDTO2 = new ZmsInternetMainDTO();
        zmsInternetMainDTO2.setServerSn("4444");
        zmsInternetMainDTOList.add(zmsInternetMainDTO2);

        PowerMockito.when(centerfactoryRemoteService.getRealTimeInteractiveB2B(Mockito.any())).thenReturn(qualityCodeOutputDTOList);
        PowerMockito.when(wsmAssembleLinesRepository.selectMainLogBySn(Mockito.any())).thenReturn(zmsInternetMainDTOList);
        customerQualityDTO.setMessageType(ZTEIMES_MEI_TUAN_QUALITYCODE);
        internetCustomerQaCtrlImpl.handleByteDance(customerQualityDTO, internetList);
        dto = new InternetCustomerQaCtrlOutDTO();
        dto.setCode("001");
        dto.setSn("3333");
        internetList.add(dto);
        dto = new InternetCustomerQaCtrlOutDTO();
        dto.setCode("002");
        dto.setSn("4444");
        internetList.add(dto);
        internetCustomerQaCtrlImpl.handleByteDance(customerQualityDTO, internetList);
        List<String> snList = new LinkedList<String>(){{add("123");}};
        customerQualityDTO.setServerSnList(snList);
        internetCustomerQaCtrlImpl.handleByteDance(customerQualityDTO, internetList);
        Assert.assertEquals(true, true);
    }
    @Test
    public void handleAlibaba() throws Exception {
        InternetCustomerQaCtrlDTO dto =new InternetCustomerQaCtrlDTO();
        dto.setCustomerName(ALIBABA);
        List<String> snList =new ArrayList<>();
        snList.add("111");
        dto.setSnList(snList);
        List<InternetCustomerQaCtrlOutDTO> internetList =new ArrayList<>();
        InternetCustomerQaCtrlOutDTO model = new InternetCustomerQaCtrlOutDTO();
        model.setSn("111");
        internetList.add(model);List<String> qualityCodeList =new ArrayList<>();
        qualityCodeList.add("1111,");
        qualityCodeList.add("2222,");
        PowerMockito.when(wsmAssembleLinesRepository.getZmsQualityCode(Mockito.any())).thenReturn(qualityCodeList);
        PowerMockito.when(zmsAlibabaRepository.getAliBabaServerSnList(Mockito.any())).thenReturn(qualityCodeList);
        List<InternetCustomerQaCtrlOutDTO> result = internetCustomerQaCtrlImpl.setInternetCustomerQaCtrlPlus(dto,internetList);
        Assert.assertEquals(true, true);
    }

    @Test
    public void handleTencent() throws Exception {
        String forwardDataType = "正向数据";
        String mtpTestDataType = "MPT测试日志_MTHC";
        SysLookupValues sysLookupMPTSwitchY = new SysLookupValues();
        sysLookupMPTSwitchY.setDescription("Y");
        PowerMockito.when(zmsCommonService.getSysLookupValues(anyString())).thenReturn(sysLookupMPTSwitchY);
        CustomerQualityDTO customerQualityDTO = new CustomerQualityDTO();
        List<InternetCustomerQaCtrlOutDTO> internetList = new ArrayList<>();
        InternetCustomerQaCtrlOutDTO internetCustomerQaCtrlOutDTO = new InternetCustomerQaCtrlOutDTO();
        internetCustomerQaCtrlOutDTO.setSn("222");
        internetList.add(internetCustomerQaCtrlOutDTO);
        List<String> snList = new ArrayList<>();
        snList.add("222");
        customerQualityDTO.setServerSnList(snList);
        List<QualityCodeOutputDTO> forWardDTOList = new ArrayList<>();
        List<QualityCodeOutputDTO> mtpTestDTOList = new ArrayList<>();
        PowerMockito.when(centerfactoryRemoteService.getRealTimeInteractiveB2B(Mockito.any())).thenReturn(forWardDTOList);
        PowerMockito.when(centerfactoryRemoteService.getRealTimeInteractiveB2B(Mockito.any())).thenReturn(mtpTestDTOList);
        internetCustomerQaCtrlImpl.handleTencent(forwardDataType, mtpTestDataType, customerQualityDTO, internetList);
        Assert.assertEquals(forWardDTOList.size(),0);

        QualityCodeOutputDTO qualityCodeOutputDTO = new QualityCodeOutputDTO();
        qualityCodeOutputDTO.setServerSn("111");
        forWardDTOList.add(qualityCodeOutputDTO);
        PowerMockito.when(centerfactoryRemoteService.getRealTimeInteractiveB2B(Mockito.any())).thenReturn(forWardDTOList);
        PowerMockito.when(centerfactoryRemoteService.getRealTimeInteractiveB2B(Mockito.any())).thenReturn(mtpTestDTOList);
        internetCustomerQaCtrlImpl.handleTencent(forwardDataType, mtpTestDataType, customerQualityDTO, internetList);
        Assert.assertEquals(forWardDTOList.size(),1);

        List<QualityCodeOutputDTO> forWardDTOList1 = new ArrayList<>();
        List<QualityCodeOutputDTO> mtpTestDTOList1 = new ArrayList<>();
        QualityCodeOutputDTO qualityCodeOutputDTO1 = new QualityCodeOutputDTO();
        qualityCodeOutputDTO1.setServerSn("222");
        forWardDTOList1.add(qualityCodeOutputDTO1);
        customerQualityDTO.setMessageType(ZTE_IMES_TENCENT_FORWARD_QUERY);
        PowerMockito.when(centerfactoryRemoteService.getRealTimeInteractiveB2B(customerQualityDTO)).thenReturn(mtpTestDTOList1);
        customerQualityDTO.setMessageType(ZTE_IMES_TENCENT_QUERY_MPT_TEST_DATA);
        PowerMockito.when(centerfactoryRemoteService.getRealTimeInteractiveB2B(customerQualityDTO)).thenReturn(forWardDTOList1);
        internetCustomerQaCtrlImpl.handleTencent(forwardDataType, mtpTestDataType, customerQualityDTO, internetList);
        Assert.assertEquals(forWardDTOList1.size(),1);

        List<ZmsInternetMainDTO> zmsInternetMainDTOS = new ArrayList<>();
        ZmsInternetMainDTO zmsInternetMainDTO = new ZmsInternetMainDTO();
        PowerMockito.when(zmsForwardTencentRepository.getInternetMain(anyString(),anyList())).thenReturn(zmsInternetMainDTOS);

        QualityCodeOutputDTO qualityCodeOutputDTO2= new QualityCodeOutputDTO();
        qualityCodeOutputDTO2.setServerSn("111");
        mtpTestDTOList.add(qualityCodeOutputDTO2);
        forWardDTOList.clear();
        PowerMockito.when(centerfactoryRemoteService.getRealTimeInteractiveB2B(Mockito.any())).thenReturn(forWardDTOList);
        PowerMockito.when(centerfactoryRemoteService.getRealTimeInteractiveB2B(Mockito.any())).thenReturn(mtpTestDTOList);
        internetCustomerQaCtrlImpl.handleTencent(forwardDataType, mtpTestDataType, customerQualityDTO, internetList);
        Assert.assertEquals(forWardDTOList.size(),0);

        zmsInternetMainDTOS.clear();
        zmsInternetMainDTO.setDataUpStatus(STR_NUMBER_TWO);
        zmsInternetMainDTOS.add(zmsInternetMainDTO);
        PowerMockito.when(zmsForwardTencentRepository.getInternetMain(anyString(),anyList())).thenReturn(zmsInternetMainDTOS);

        mtpTestDTOList.clear();
        qualityCodeOutputDTO2.setServerSn("111");
        mtpTestDTOList.add(qualityCodeOutputDTO2);
        forWardDTOList.clear();
        qualityCodeOutputDTO1.setServerSn("111");
        forWardDTOList.add(qualityCodeOutputDTO1);
        PowerMockito.when(centerfactoryRemoteService.getRealTimeInteractiveB2B(Mockito.any())).thenReturn(forWardDTOList);
        PowerMockito.when(centerfactoryRemoteService.getRealTimeInteractiveB2B(Mockito.any())).thenReturn(mtpTestDTOList);
        internetCustomerQaCtrlImpl.handleTencent(forwardDataType, mtpTestDataType, customerQualityDTO, internetList);
        Assert.assertEquals(forWardDTOList.size(),1);

        zmsInternetMainDTOS.clear();
        zmsInternetMainDTO.setDataUpStatus(STR_NUMBER_THREE);
        zmsInternetMainDTOS.add(zmsInternetMainDTO);
        PowerMockito.when(zmsForwardTencentRepository.getInternetMain(anyString(),anyList())).thenReturn(zmsInternetMainDTOS);

        mtpTestDTOList.clear();
        qualityCodeOutputDTO2.setServerSn("222");
        mtpTestDTOList.add(qualityCodeOutputDTO2);
        forWardDTOList.clear();
        qualityCodeOutputDTO1.setServerSn("111");
        forWardDTOList.add(qualityCodeOutputDTO1);
        PowerMockito.when(centerfactoryRemoteService.getRealTimeInteractiveB2B(Mockito.any())).thenReturn(forWardDTOList);
        PowerMockito.when(centerfactoryRemoteService.getRealTimeInteractiveB2B(Mockito.any())).thenReturn(mtpTestDTOList);
        internetCustomerQaCtrlImpl.handleTencent(forwardDataType, mtpTestDataType, customerQualityDTO, internetList);
        Assert.assertEquals(forWardDTOList.size(),1);

        zmsInternetMainDTOS.clear();
        zmsInternetMainDTO.setDataUpStatus(STR_NUMBER_ONE);
        zmsInternetMainDTOS.add(zmsInternetMainDTO);
        PowerMockito.when(zmsForwardTencentRepository.getInternetMain(anyString(),anyList())).thenReturn(zmsInternetMainDTOS);

        PowerMockito.when(centerfactoryRemoteService.getRealTimeInteractiveB2B(Mockito.any())).thenReturn(null);
        internetCustomerQaCtrlImpl.handleTencent(forwardDataType, mtpTestDataType, customerQualityDTO, internetList);
        mtpTestDTOList.clear();
        QualityCodeOutputDTO qualityCodeOutputDTO3 = null;
        mtpTestDTOList.add(qualityCodeOutputDTO3);
        PowerMockito.when(centerfactoryRemoteService.getRealTimeInteractiveB2B(Mockito.any())).thenReturn(mtpTestDTOList);
        internetCustomerQaCtrlImpl.handleTencent(forwardDataType, mtpTestDataType, customerQualityDTO, internetList);

        mtpTestDTOList.clear();
        qualityCodeOutputDTO2.setServerSn("111");
        mtpTestDTOList.add(qualityCodeOutputDTO2);
        forWardDTOList.clear();
        qualityCodeOutputDTO1.setServerSn("222");
        forWardDTOList.add(qualityCodeOutputDTO1);
        PowerMockito.when(centerfactoryRemoteService.getRealTimeInteractiveB2B(Mockito.any())).thenReturn(forWardDTOList);
        PowerMockito.when(centerfactoryRemoteService.getRealTimeInteractiveB2B(Mockito.any())).thenReturn(mtpTestDTOList);
        internetCustomerQaCtrlImpl.handleTencent(forwardDataType, mtpTestDataType, customerQualityDTO, internetList);
        Assert.assertEquals(forWardDTOList.size(),1);

        mtpTestDTOList.clear();
        qualityCodeOutputDTO2.setServerSn("222");
        qualityCodeOutputDTO2.setQualityCode("2");
        mtpTestDTOList.add(qualityCodeOutputDTO2);
        forWardDTOList.clear();
        qualityCodeOutputDTO1.setServerSn("222");
        qualityCodeOutputDTO1.setQualityCode("2");
        qualityCodeOutputDTO1.setErrCause("ddd");
        forWardDTOList.add(qualityCodeOutputDTO1);
        PowerMockito.when(centerfactoryRemoteService.getRealTimeInteractiveB2B(Mockito.any())).thenReturn(forWardDTOList);
        PowerMockito.when(centerfactoryRemoteService.getRealTimeInteractiveB2B(Mockito.any())).thenReturn(mtpTestDTOList);
        internetCustomerQaCtrlImpl.handleTencent(forwardDataType, mtpTestDataType, customerQualityDTO, internetList);
        Assert.assertEquals(forWardDTOList.size(),1);

        mtpTestDTOList.clear();
        qualityCodeOutputDTO2.setServerSn("222");
        qualityCodeOutputDTO2.setQualityCode("1");
        qualityCodeOutputDTO2.setErrCause("adbb");
        mtpTestDTOList.add(qualityCodeOutputDTO2);
        forWardDTOList.clear();
        qualityCodeOutputDTO1.setServerSn("222");
        qualityCodeOutputDTO1.setQualityCode("2");
        forWardDTOList.add(qualityCodeOutputDTO1);
        PowerMockito.when(centerfactoryRemoteService.getRealTimeInteractiveB2B(Mockito.any())).thenReturn(forWardDTOList);
        PowerMockito.when(centerfactoryRemoteService.getRealTimeInteractiveB2B(Mockito.any())).thenReturn(mtpTestDTOList);
        internetCustomerQaCtrlImpl.handleTencent(forwardDataType, mtpTestDataType, customerQualityDTO, internetList);
        Assert.assertEquals(forWardDTOList.size(),1);

        mtpTestDTOList.clear();
        qualityCodeOutputDTO2.setServerSn("222");
        qualityCodeOutputDTO2.setQualityCode("2");
        mtpTestDTOList.add(qualityCodeOutputDTO2);
        forWardDTOList.clear();
        qualityCodeOutputDTO1.setServerSn("222");
        qualityCodeOutputDTO1.setQualityCode("0");
        forWardDTOList.add(qualityCodeOutputDTO1);
        PowerMockito.when(centerfactoryRemoteService.getRealTimeInteractiveB2B(Mockito.any())).thenReturn(forWardDTOList);
        PowerMockito.when(centerfactoryRemoteService.getRealTimeInteractiveB2B(Mockito.any())).thenReturn(mtpTestDTOList);
        internetCustomerQaCtrlImpl.handleTencent(forwardDataType, mtpTestDataType, customerQualityDTO, internetList);
        Assert.assertEquals(forWardDTOList.size(),1);

        mtpTestDTOList.clear();
        qualityCodeOutputDTO2.setServerSn("222");
        qualityCodeOutputDTO2.setQualityCode("0");
        mtpTestDTOList.add(qualityCodeOutputDTO2);
        forWardDTOList.clear();
        qualityCodeOutputDTO1.setServerSn("222");
        qualityCodeOutputDTO1.setQualityCode("0");
        forWardDTOList.add(qualityCodeOutputDTO1);
        PowerMockito.when(centerfactoryRemoteService.getRealTimeInteractiveB2B(Mockito.any())).thenReturn(forWardDTOList);
        PowerMockito.when(centerfactoryRemoteService.getRealTimeInteractiveB2B(Mockito.any())).thenReturn(mtpTestDTOList);
        internetCustomerQaCtrlImpl.handleTencent(forwardDataType, mtpTestDataType, customerQualityDTO, internetList);
        sysLookupMPTSwitchY.setDescription("N");
        PowerMockito.when(zmsCommonService.getSysLookupValues(anyString())).thenReturn(sysLookupMPTSwitchY);
        internetCustomerQaCtrlImpl.handleTencent(forwardDataType, mtpTestDataType, customerQualityDTO, internetList);
        sysLookupMPTSwitchY.setDescription("");
        PowerMockito.when(zmsCommonService.getSysLookupValues(anyString())).thenReturn(sysLookupMPTSwitchY);
        internetCustomerQaCtrlImpl.handleTencent(forwardDataType, mtpTestDataType, customerQualityDTO, internetList);
        Assert.assertEquals(forWardDTOList.size(),1);
    }

    @Test
    public void taskFiltering() throws Exception {
        InternetCustomerQaCtrlDTO dto = new InternetCustomerQaCtrlDTO();
        dto.setCustomerName(ALIBABA);
        List<InternetCustomerQaCtrlOutDTO> internetList = new ArrayList<>();
        internetList.add(new InternetCustomerQaCtrlOutDTO(){{setTaskno("taskNo");}});
        internetList.add(new InternetCustomerQaCtrlOutDTO(){{setTaskno("taskNo2");}});
        List<ZmsCommonEntityDTO> zmsCommonEntityDTOList = new ArrayList<>();
        zmsCommonEntityDTOList.add(new ZmsCommonEntityDTO());
        zmsCommonEntityDTOList.add(new ZmsCommonEntityDTO(){{setContractNumber("ContractNumber");setEntityName("taskNo");}});
        PowerMockito.when(zmsCommonService.getZmsEntityByTaskNoList(anyList())).thenReturn(zmsCommonEntityDTOList);
        PowerMockito.when(zmsCommonService.getNeedUploadContractList(anyList())).thenReturn(anyList()).thenReturn(Lists.newArrayList("ContractNumber","ContractNumber2"));
        Whitebox.invokeMethod(internetCustomerQaCtrlImpl,"taskFiltering",dto,internetList);
        Assert.assertNotNull(internetList);

    }
}
