package com.zte.application.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.zte.application.ConciseDailyService;
import com.zte.application.PsWipInfoService;
import com.zte.application.TaskDailyStatDetailService;
import com.zte.application.TaskDailyStatHeadService;
import com.zte.application.WarehousehmEntryDetailService;
import com.zte.application.WipScanHistoryService;
import com.zte.common.CommonUtils;
import com.zte.common.DateUtil;
import com.zte.common.model.MessageId;
import com.zte.common.utils.BusinessUtil;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.domain.model.ProdPlanStock;
import com.zte.domain.model.PsTask;
import com.zte.domain.model.PsWorkOrderBasic;
import com.zte.domain.model.TaskDailyStatDetail;
import com.zte.domain.model.TaskDailyStatDetailRepository;
import com.zte.domain.model.TaskDailyStatHead;
import com.zte.domain.model.TaskDailyStatHeadRepository;
import com.zte.domain.model.WipDailyStatisticReport;
import com.zte.domain.model.WorkOrderOperateHis;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.CrafttechRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.BBomHeaderDTO;
import com.zte.interfaces.dto.BProdBomChangeDetailDTO;
import com.zte.interfaces.dto.BProdBomHeaderDTO;
import com.zte.interfaces.dto.ConciseDailyDTO;
import com.zte.interfaces.dto.CtBasicRouteDTO;
import com.zte.interfaces.dto.PsWipInfoDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.interfaces.dto.WipScanHistoryDTO;
import com.zte.itp.msa.redis.util.RedisCacheUtils;
import com.zte.springbootframe.common.annotation.AsyncExport;
import com.zte.springbootframe.common.annotation.RedisDistributedLockAnnotation;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.springbootframe.util.AsyncExportFileCommonService;
import com.zte.springbootframe.util.BigExcelProcesser;
import com.zte.springbootframe.util.CloudDiskHelper;
import com.zte.springbootframe.util.EmailUtils;
import com.zte.springbootframe.util.FileUtils;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.ThreadUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

import static com.zte.common.utils.Constant.COMMA;
import static com.zte.common.utils.Constant.EMPTY_STRING;
import static com.zte.common.utils.Constant.P0007;
import static com.zte.common.utils.Constant.P0008;
import static com.zte.itp.msa.core.exception.GlobalDefaultBaseExceptionHandler.getTrace;


/**
* @Author: Saber[10307315]
* @Date: 2022/12/7
*/
@Service
public class ConciseDailyServiceImpl implements ConciseDailyService {

    @Autowired
    TaskDailyStatDetailService taskDailyStatDetailService;

    @Autowired
    WipScanHistoryService wipScanHistoryService;

    @Autowired
    PsWipInfoService psWipInfoService;

    @Autowired
    ConciseDailyService conciseDailyService;

    @Autowired
    WarehousehmEntryDetailService warehousehmEntryDetailService;

    @Autowired
    TaskDailyStatHeadService taskDailyStatHeadService;

    @Autowired
    private TaskDailyStatHeadRepository taskDailyStatHeadRepository;

    @Autowired
    private TaskDailyStatDetailRepository taskDailyStatDetailRepository;

    @Autowired
    private EmailUtils emailUtils;
    @Autowired
    private CloudDiskHelper cloudDiskHelper;

    @Autowired
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Autowired
    private AsyncExportFileCommonService asyncExportFileCommonService;

    private static final Logger LOG = LoggerFactory.getLogger(ConciseDailyServiceImpl.class);


    @Override
    @RedisDistributedLockAnnotation(redisPrefix = "conciseDailyScheduledTask", redisLockTime = 3600, factoryId = true,
            redisLockParam = {})
    public void handleScheduledTask(ConciseDailyDTO conciseDailyDTO, Integer preDay) throws Exception {
        // preDay代表往前几天，0当天，1昨天
        if (preDay == null || preDay > Constant.INT_1 || preDay < Constant.INT_0) {
            preDay = Constant.INT_1;
        }
        // 用于单独调用来补充漏掉的批次。正常定时任务不会用到该批次号
        List<String> prodPlanIds = conciseDailyDTO.getProdPlanIds();
        // 统一提取时间
        Date date = DateUtil.addDay(new Date(), Constant.INT_0 - preDay);
        conciseDailyDTO.setStatisticDate(date);
        // 每天凌晨，跑前一天的报表，先清除前一天已有数据,可带批次
        taskDailyStatDetailService.deleteYesterdayConciseDaily(prodPlanIds, preDay);
        taskDailyStatHeadService.deleteYesterdayConciseDaily(prodPlanIds, preDay);
        // 循环查询处理，每一次查询100条(TL确认)。
        int page = 0;
        int row = Constant.INT_100;
        // 用于存储待计算的批次的list 简明日报只查询单板任务
        List<PsTask> psTaskList = new ArrayList<>();
        // 查询数据字典，配置子工序的数据字典，1003010
        List<SysLookupTypesDTO> sysLookupTypesList = BasicsettingRemoteService.getSysLookUpValue
                (Constant.LOOKUP_TYPE_1003010);
        // 切目录值中多个子工序代码为数组当做value，字典中文表述作key,
        Map<String, String[]> sysDesChinToProcessCodes = handleSplitSysProcessGroup(sysLookupTypesList);
        conciseDailyDTO.setSysDesChinToProcessCodes(sysDesChinToProcessCodes);
        // 查出不为空，就继续计算(首次进入page为0)
        while (page == 0 || !CollectionUtils.isEmpty(psTaskList)) {
            try {
                // 查询本次需要计算的批次
                page += 1;
                psTaskList = PlanscheduleRemoteService.pageSelectForConciseDaily(prodPlanIds, page, row);
                if (CollectionUtils.isEmpty(psTaskList)) {
                    continue;
                }

                queryAndCalculateData(conciseDailyDTO, psTaskList, sysLookupTypesList, date);

                conciseDailyService.saveToDataBase(conciseDailyDTO);
            } catch (Exception e) {
                this.sendEmailMsg(e, CommonUtils.getLmbMessage(MessageId.CONCISE_DAILY_SCHEDULED_TASK_FAILED,
                        new String[]{psTaskList.stream().map(k -> k.getProdplanId()).collect(Collectors.toList()).toString(),
                        String.valueOf(page)}));
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveToDataBase(ConciseDailyDTO conciseDailyDTO) {
        if (conciseDailyDTO == null) {
            return;
        }
        // 写入数据
        if (!CollectionUtils.isEmpty(conciseDailyDTO.getStatHeadList())) {
            taskDailyStatHeadService.insertBatchByList(conciseDailyDTO.getStatHeadList());
        }
        if (!CollectionUtils.isEmpty(conciseDailyDTO.getStatDetailList())) {
            taskDailyStatDetailService.insertBatchByList(conciseDailyDTO.getStatDetailList());
        }
    }

    private void queryAndCalculateData(ConciseDailyDTO conciseDailyDTO, List<PsTask> psTaskList,
                                       List<SysLookupTypesDTO> sysLookupTypesList, Date yesterday) throws Exception {
        List<String> prodPlanIdList = psTaskList.stream().map(k -> k.getProdplanId()).collect(Collectors.toList());
        // 查询必要数据，用于计算。简明日报信息、工艺路径、单板版本、提单数量、入库数量
        Map<String, Map<String, Long>> prodToProcDaiDetCountMap = handleCountDailyDetail(yesterday, prodPlanIdList);
        // 查询扫描历史表wip_scan_history。不带工站为0条件,
        Map<String, Map<String, Long>> prodToCrossHistoryCountMap = handleCountScanHistory(yesterday, prodPlanIdList, false);
        // 查询扫描历史表wip_scan_history。带工站为0条件,
        Map<String, Map<String, Long>> prodToTransmitHistoryCountMap = handleCountScanHistory(yesterday, prodPlanIdList, true);
        // 查询扫描历史表wip_scan_history得到报废、维修的子工序转入数据
        Map<String, Map<String, Long>> prodToScrapOrHistoryCountMap = handleProdScrapOrMaintenceHistory(yesterday, prodPlanIdList, false);
        // 查询扫描历史表wip_scan_history得到报废、维修的子工序转出数据
        Map<String, Map<String, Long>> prodFromScrapOrHistoryCountMap = handleProdScrapOrMaintenceHistory(yesterday, prodPlanIdList, true);
        // 查询扫描历史表，工站不为0，统计批次的SMT,DIP,TEST主工序的首次扫描时间，作为上线时间。
        handleFindOnLineTimeOfProcess(prodPlanIdList, conciseDailyDTO);
        // 查询工艺路径，未排产使用料单代码查询，排产的使用routid查询。主要查询，中文路径和子工序代码组成的group。
        // 同时，为了减少一次查询指令表，将指令的首工序和最后工序都记录下来，以及批次对应的首指令的开工时间，用于后续计算判断。
        Map<String, Pair<String, String>> prodToProcessInfoMap = handleQueryProcessInfo(psTaskList, prodPlanIdList, conciseDailyDTO);
        // 查询wip_info 子工序结存。统计当前在wipinfo表中，每个批次的每个工序还有多少条码未扫描，同时记录批次的备料区结存。
        handleCountWipInfo(psTaskList, prodPlanIdList, conciseDailyDTO);
        // 单板版本，
        Map<String, String> prodToBomVerMap = handleQueryBomVer(psTaskList);
        // 按ProdplanId，查询当天提交入库信息，和当天接收信息，以及累计入库数量（提单数量）
        List<ProdPlanStock> submitList = warehousehmEntryDetailService.getSubmitQtyByPlanIds(yesterday, prodPlanIdList);
        List<ProdPlanStock> inBoundList = warehousehmEntryDetailService.getInBoundQtyByPlanIds(yesterday, prodPlanIdList);
        List<ProdPlanStock> submitHisList = warehousehmEntryDetailService.getStockQtyByPlanIds(prodPlanIdList);
        // 提单数量 warehouse_entry_info 按ProdplanId 分组聚合
        Map<String, Integer> prodToSubmitItemMap = submitList.stream()
                .collect(Collectors.toMap(e -> e.getProdPlanId(), e -> e.getSubmitQty(), (oldVal, newVal) -> newVal));
        // 入库数量 warehouse_entry_detail 按ProdplanId 分组聚合
        Map<String, Integer> prodToInBoundMap = inBoundList.stream()
                .collect(Collectors.toMap(e -> e.getProdPlanId(), e -> e.getInboundQty(), (oldVal, newVal) -> newVal));
        // 累计提单数量，用于计算在线数量
        Map<String, Integer> prodToSubmitHisMap = submitHisList.stream()
                .collect(Collectors.toMap(e -> e.getProdPlanId(), e -> e.getSubmitHisQty(), (oldVal, newVal) -> newVal));
        // 设置参数到DTO
        conciseDailyDTO.setSysLookupTypesList(sysLookupTypesList);
        conciseDailyDTO.setPsTaskList(psTaskList);
        conciseDailyDTO.setProdToProcDaiDetCountMap(prodToProcDaiDetCountMap);
        conciseDailyDTO.setProdToCrossHistoryCountMap(prodToCrossHistoryCountMap);
        conciseDailyDTO.setProdToTransmitHistoryCountMap(prodToTransmitHistoryCountMap);
        conciseDailyDTO.setProdToProcessInfoMap(prodToProcessInfoMap);
        conciseDailyDTO.setProdToBomVerMap(prodToBomVerMap);
        conciseDailyDTO.setProdToSubmitItemMap(prodToSubmitItemMap);
        conciseDailyDTO.setProdToInBoundMap(prodToInBoundMap);
        conciseDailyDTO.setProdToSubmitHisMap(prodToSubmitHisMap);
        conciseDailyDTO.setProdToScrapOrHistoryCountMap(prodToScrapOrHistoryCountMap);
        conciseDailyDTO.setProdFromScrapOrHistoryCountMap(prodFromScrapOrHistoryCountMap);
        handleScheduledTaskCorn(conciseDailyDTO);
    }

    /**
     *
     * @param yesterday
     * @param prodPlanIdList
     * @param preProcessCodeFlag
     */
    public  Map<String, Map<String, Long>> handleProdScrapOrMaintenceHistory(Date yesterday, List<String> prodPlanIdList,boolean preProcessCodeFlag) {
        Map<String, Map<String, Long>> resultMap = new HashMap<>(Constant.INT_128);
        // 查询维修、报废等子工序在扫描历史的转入记录
        List<WipScanHistoryDTO> turnList = wipScanHistoryService.countQtyForScrapOrMaintence(yesterday, prodPlanIdList, preProcessCodeFlag);
        if (CollectionUtils.isEmpty(turnList)) {
            return resultMap;
        }
        for (int i = 0; i < turnList.size(); i++) {
            WipScanHistoryDTO entity = turnList.get(i);
            String prodId = entity.getAttribute1();
            Map<String, Long> tempMap = resultMap.get(prodId);
            if (tempMap == null) {
                tempMap = new HashMap<>(Constant.INT_64);
                resultMap.put(prodId, tempMap);
            }
            // flag为true,表示计算前子工序为维修报废,即作为转出数据,取transmitQty;否则取crossStationQty
            if(preProcessCodeFlag){
                tempMap.put(entity.getPreProcessCode(), entity.getTransmitQty());
            }else{
                tempMap.put(entity.getCurrProcessCode(), entity.getCrossStationQty());
            }

        }
        return resultMap;
    }

    /** 
    * @Description: 将数字字典中的多个子工序代码切割
     * 如果单个子工序，则切割后为只有本身元素的String[]
    * @Param: [sysLookupTypesList]
    * @return: java.util.Map<java.lang.String,java.lang.String[]>
    * @Author: Saber[10307315]
    * @Date: 2022/12/13 上午11:05
    */
    private Map<String, String[]> handleSplitSysProcessGroup(List<SysLookupTypesDTO> sysLookupTypesList) {
        Map<String, String[]> resultMap = new HashMap<>();
        for (int i = 0; i < sysLookupTypesList.size(); i++) {
            SysLookupTypesDTO entity = sysLookupTypesList.get(i);
            String lookMeaning = entity.getLookupMeaning();
            resultMap.put(entity.getAttribute3(), lookMeaning.split(Constant.STR_SPLIT_$));
        }
        return resultMap;
    }

    /**
    * @Description: 找到主工序的上线时间
    * @Param: [prodPlanIdList, conciseDailyDTO]
    * @return: void
    * @Author: Saber[10307315]
    * @Date: 2022/12/16 下午5:11
    */
    private void handleFindOnLineTimeOfProcess(List<String> prodPlanIdList, ConciseDailyDTO conciseDailyDTO) {
        Map<String, Map<String, Date>> prodToProcessOnLineTimeMap = new HashMap<>();
        conciseDailyDTO.setProdToProcessOnLineTimeMap(prodToProcessOnLineTimeMap);
        List<WipScanHistoryDTO> wipScanInfoList = wipScanHistoryService.findOnLineTimeOfProcess(prodPlanIdList);
        if (CollectionUtils.isEmpty(wipScanInfoList)) {
            return;
        }
        for (WipScanHistoryDTO wipScanInfo : wipScanInfoList) {
            String prodId = wipScanInfo.getAttribute1();
            Map<String, Date> tempMap = prodToProcessOnLineTimeMap.get(prodId);
            if (tempMap == null) {
                tempMap = new HashMap<>();
                prodToProcessOnLineTimeMap.put(prodId, tempMap);
            }
            // smt-a/smt-b要当做SMT统一计算上线时间。
            if (Constant.CRAFTSECTION_SMT_A.equals(wipScanInfo.getCraftSection())
                    || Constant.CRAFTSECTION_SMT_B.equals(wipScanInfo.getCraftSection())) {
                // 如果当前时间更早。就更新
                if (tempMap.get(Constant.SMT) == null || tempMap.get(Constant.SMT).compareTo(wipScanInfo.getOnLineTime()) > 0) {
                    tempMap.put(Constant.SMT, wipScanInfo.getOnLineTime());
                }
                // 如果没有，则跳过
                continue;
            }
            tempMap.put(wipScanInfo.getCraftSection(), wipScanInfo.getOnLineTime());
        }
    }

    private void handleScheduledTaskCorn(ConciseDailyDTO conciseDailyDTO) {
        // 创建用于存储新建简明日报实体的头表和详表List
        List<TaskDailyStatHead> statHeadList = new ArrayList<>();
        List<TaskDailyStatDetail> statDetailList = new ArrayList<>();
        // 遍历批次列表
        for(int i = 0; i < conciseDailyDTO.getPsTaskList().size(); i++) {
            PsTask psTask = conciseDailyDTO.getPsTaskList().get(i);
            //  先生成头表数据
            TaskDailyStatHead headEntity = generateDailyStatHead(psTask, conciseDailyDTO);
            statHeadList.add(headEntity);
            // 处理详表数据
            handleGenerateDetail(statDetailList, psTask, headEntity.getHeadId(), conciseDailyDTO);
        }
        conciseDailyDTO.setStatHeadList(statHeadList);
        conciseDailyDTO.setStatDetailList(statDetailList);
    }

    /**
     * @Description: 找数据字典额外说明中在工艺路径上第一个和最后一个子工序
     * @Param: [conciseDailyDTO, processToSeqMap, sysEntity]
     * @return: org.springframework.data.util.Pair<java.lang.String,java.lang.String>
     * @Author: Saber[10307315]
     * @Date: 2022/12/13 下午4:19
     */
    public Pair<String, String> findFirstAndLastProcess(String[] processArr, Map<String, Integer> processToSeqMap,
                                                         SysLookupTypesDTO sysEntity) {
        if (processArr == null) {
            return Pair.of(Constant.STRING_EMPTY, Constant.STRING_EMPTY);
        }
        String first = null;
        String last = null;
        Integer min = Integer.MAX_VALUE;
        Integer max = -1;
        List<String> processScrapOrRepair = Arrays.asList(Constant.PROCESS_SCRAP_OR_REPAIR);
        for (int i = 0; i < processArr.length; i++) {
            // 如果工艺路径不存在该子工序，则跳过。
            Integer seq = processToSeqMap.get(processArr[i]);
            if (seq == null) {
                // 不包含的情况下，如果是报废、维修统计项，则首尾子工序都是该子工序
                if(processScrapOrRepair.contains(processArr[i])){
                    first = processArr[i];
                    last = processArr[i];
                }
                continue;
            }
            if (seq.compareTo(min) < 0) {
                min = seq;
                first = processArr[i];
            }
            if (seq.compareTo(max) > 0) {
                max = seq;
                last = processArr[i];
            }
        }
        first = first == null ? Constant.STRING_EMPTY : first;
        last = last == null ? Constant.STRING_EMPTY : last;
        return Pair.of(first, last);
    }

    private void handleGenerateDetail(List<TaskDailyStatDetail> statDetailList, PsTask psTask, String headId,
                                      ConciseDailyDTO conciseDailyDTO) {
        String prodId = psTask.getProdplanId();
        Long yesterdayQty = null;
        Long turnIntoQty = null;
        Long turnOutQty = null;
        Long onhandQty = null;
        String firstProcess = null;
        String lastProcess = null;
        //  (子工序代码工艺路径，中文工艺路径)
        Pair<String, String> pairRouteInfo = conciseDailyDTO.getProdToProcessInfoMap().get(prodId);
        // 取批次工艺路径，使用+切割成数组。并转化为key子工序，value为顺序的map。--查询sql改成按照$拼接,这里也改成按照$拆分
        String[] routePath = (pairRouteInfo == null || pairRouteInfo.getFirst() == null)
                ? null : pairRouteInfo.getFirst().split(Constant.STR_SPLIT_$);
        // 工艺路径如果为空，详表不处理
        if (routePath == null || routePath.length <= 0) {
            return;
        }
        Map<String, Integer> processToSeqMap = new HashMap<>();
        String firstOfRoute = routePath[0];
        String lastBeforeInBound = routePath.length < Constant.INT_2 ? Constant.NULL : routePath[routePath.length - 2];
        for (int i = 0; i < routePath.length; i++) {
            processToSeqMap.put(routePath[i], i);
        }
        for (SysLookupTypesDTO sysEntity : conciseDailyDTO.getSysLookupTypesList()) {
            // 如果数据字典目录值，数据库statItem应填字段没有填，或者额外字段1(表示是否计算高级日报)为N，则不处理
            if (StringUtils.isEmpty(sysEntity.getAttribute3())
                    ||StringUtils.isEmpty(sysEntity.getLookupMeaning())
                || StringUtils.isEmpty(sysEntity.getAttribute1()) || Constant.FLAG_N.equals(sysEntity.getAttribute1())) {
                continue;
            }
            // 创建基础实体，并先添加到列表中，因为后续会使用continue打断循环，所以提前存入，后续计算直接更改引用对象
            TaskDailyStatDetail detail = generateDetailBasicInfo(headId, sysEntity.getAttribute3());
            statDetailList.add(detail);
            yesterdayQty = 0L;
            turnIntoQty = 0L;
            turnOutQty = 0L;
            onhandQty = 0L;
            // 首先确认该数据库行用于计算转入和转出的子工序代码。
            String[] processArr = conciseDailyDTO.getSysDesChinToProcessCodes().get(sysEntity.getAttribute3());
            // 赋值线体
            setLineMatchProcess(conciseDailyDTO, prodId, processArr, detail);
            // 取批次指令首工序，如果为空，说明未产生指令，还未入缓冲池，则4个值全为空。
            if(CollectionUtils.isEmpty(conciseDailyDTO.getProdToFirstProcessSet().get(prodId))) {
                setFourQtyOfDetail(detail, yesterdayQty, turnIntoQty, turnOutQty, onhandQty);
                continue;
            }
            Pair<String, String> firstAndLastProcess = findFirstAndLastProcess(processArr, processToSeqMap, sysEntity);
            firstProcess = firstAndLastProcess.getFirst();
            lastProcess = firstAndLastProcess.getSecond();
            // 针对第一个子工序，计算转入， 最后一个子工序计算转出。
            // 计算转入
            turnIntoQty = computeTurnIntoQty(conciseDailyDTO, prodId, firstProcess, firstOfRoute);
            // 计算转出
            String nextProcess = getNextProcess(lastProcess, routePath, processToSeqMap);
            turnOutQty = computeTurnOutQty(conciseDailyDTO, prodId, lastProcess, lastBeforeInBound, nextProcess);
            // 计算结存
            onhandQty = computeOnhandQty(conciseDailyDTO, prodId, processArr, processToSeqMap);
            // 计算昨存
            yesterdayQty = computeYesterdayQty(conciseDailyDTO, prodId, sysEntity.getAttribute3());
            setFourQtyOfDetail(detail, yesterdayQty, turnIntoQty, turnOutQty, onhandQty);
        }
    }

    public void setLineMatchProcess(ConciseDailyDTO conciseDailyDTO, String prodId, String[] processArr, TaskDailyStatDetail detail) {
        Map<String, String> processMatchLineMap = conciseDailyDTO.getProdProcessToLineMap().get(prodId);
        if(CollectionUtils.isEmpty(processMatchLineMap)){
            return;
        }
        List<String> lines = new ArrayList<>();
        for (int i = 0; i < processArr.length; i++) {
            String line = processMatchLineMap.get(processArr[i]);
            lines.add(line);
        }
        String processLine = lines.stream().filter(l -> StringUtils.isNotEmpty(l)).distinct().collect(Collectors.joining(COMMA));
        detail.setLineCode(processLine);
    }

    private String getNextProcess(String lastProcess, String[] routePath, Map<String, Integer> processToSeqMap) {
        // 为空表示不存在工序，返回空，或者子工序时最后子工序，也返回空。
        if (processToSeqMap.get(lastProcess) == null || processToSeqMap.get(lastProcess)  > routePath.length - Constant.INT_2) {
           return Constant.STRING_EMPTY;
        }
        return routePath[processToSeqMap.get(lastProcess) + 1];
    }

    private Long computeYesterdayQty(ConciseDailyDTO conciseDailyDTO, String prodId, String attribute3) {
        Map<String, Long> yesterdayOnHandQty = conciseDailyDTO.getProdToProcDaiDetCountMap().get(prodId);
        Long yesterdayQty = yesterdayOnHandQty == null ? 0L : yesterdayOnHandQty.get(attribute3);
        return yesterdayQty;
    }

    public Long computeOnhandQty(ConciseDailyDTO conciseDailyDTO, String prodId, String[] processArr,
                                  Map<String, Integer> processToSeqMap) {
        Map<String, Long> processToWipInfoCountMap = conciseDailyDTO.getProdToWipInfoCountMap().get(prodId);
        if (processArr == null || processToWipInfoCountMap == null) {
            return 0L;
        }
        Long onHandQty = 0L;
        List<String> processScrapOrRepair = Arrays.asList(Constant.PROCESS_SCRAP_OR_REPAIR);
        for (int i = 0; i < processArr.length; i++) {
            // 报废和维修的计算结存
            if (processScrapOrRepair.contains(processArr[i])) {
                Long tempQty = processToWipInfoCountMap.get(processArr[i]) == null ? 0L : processToWipInfoCountMap.get(processArr[i]);
                return tempQty;
            }
            // 工艺路径包含的才计算结存
            if (processToSeqMap.keySet().contains(processArr[i])) {
                Long tempQty = processToWipInfoCountMap.get(processArr[i]) == null ? 0L : processToWipInfoCountMap.get(processArr[i]);
                onHandQty += tempQty;
            }
        }
        return onHandQty;
    }

    public Long computeTurnOutQty(ConciseDailyDTO conciseDailyDTO, String prodId, String lastProcess,
                                   String lastBeforeInBound,  String nextProcess) {
        if (StringUtils.isEmpty(lastProcess)) {
            return 0L;
        }
        // 工序的过站信息,key 为子工序，value为过站信息
        Map<String, Long> crossHistoryCount = conciseDailyDTO.getProdToCrossHistoryCountMap().get(prodId);
        // 子工序的转交信息
        Map<String, Long> processToTransmitHisMap = conciseDailyDTO.getProdToTransmitHistoryCountMap().get(prodId);
        // 提单数量
        Integer submitQty = conciseDailyDTO.getProdToSubmitItemMap().get(prodId);
        // 报废、维修工序的过站信息,key 为子工序，value为过站信息
        Map<String, Long> toScrapHistoryCount = conciseDailyDTO.getProdToScrapOrHistoryCountMap().get(prodId);
        // 报废、维修工序的转交信息
        Map<String, Long> fromScrapHistoryCount = conciseDailyDTO.getProdFromScrapOrHistoryCountMap().get(prodId);
        // 报废和维修子工序转出单独计算
        List<String> processScrapOrRepair = Arrays.asList(Constant.PROCESS_SCRAP_OR_REPAIR);
        if(processScrapOrRepair.contains(lastProcess)){
            return outFromScrapOrRepair(lastProcess, toScrapHistoryCount, fromScrapHistoryCount);
        }

        // 子工序是入库前子工序，取提交入库单的数量
        if (lastProcess.equals(lastBeforeInBound)) {
            return submitQty == null ? 0L : submitQty.longValue();
        }
        //  子工序是对应指令的最后工序，则取工序转交数量，注意表中字段此时是下工序
        Set<String> lastProcessSet = conciseDailyDTO.getProdToLastProcessSet().get(prodId);
        if (CollectionUtils.isEmpty(lastProcessSet)) {
            return 0L;
        }
        if (lastProcessSet.contains(lastProcess)) {
             return processToTransmitHisMap == null ? 0L : processToTransmitHisMap.get(nextProcess);
        }
        // 剩下情况取下工序的过站数量
        return crossHistoryCount == null || StringUtils.isEmpty(nextProcess) ? 0L : crossHistoryCount.get(nextProcess);

    }

    private Long outFromScrapOrRepair(String lastProcess, Map<String, Long> toScrapHistoryCount, Map<String, Long> fromScrapHistoryCount) {
        if(P0007.equals(lastProcess)){
            return toScrapHistoryCount == null ? 0L : toScrapHistoryCount.get(P0008);
        }
        return fromScrapHistoryCount == null ? 0L : fromScrapHistoryCount.get(lastProcess);
    }

    public Long computeTurnIntoQty(ConciseDailyDTO conciseDailyDTO, String prodId, String firstProcess, String firstOfRoute) {
        if (StringUtils.isEmpty(firstProcess)) {
            return 0L;
        }
        // 工序的过站信息,key 为子工序，value为过站信息
        Map<String, Long> crossHistoryCount = conciseDailyDTO.getProdToCrossHistoryCountMap().get(prodId);
        // 子工序的转交信息
        Map<String, Long> processToTransmitHisMap = conciseDailyDTO.getProdToTransmitHistoryCountMap().get(prodId);
        // 报废、维修工序的过站信息,key 为子工序，value为过站信息
        Map<String, Long> toScrapHistoryCount = conciseDailyDTO.getProdToScrapOrHistoryCountMap().get(prodId);
        // 报废和维修子工序转入单独计算
        List<String> processScrapOrRepair = Arrays.asList(Constant.PROCESS_SCRAP_OR_REPAIR);
        if(processScrapOrRepair.contains(firstProcess)){
            return toScrapHistoryCount== null ? 0L : toScrapHistoryCount.get(firstProcess);
        }
        // 子工序是工艺路径首工序
        if (firstProcess.equals(firstOfRoute)) {
            // 取当日此工序过站数量
            return crossHistoryCount == null ? 0L : crossHistoryCount.get(firstProcess);
        }
        
        Set<String> firstProcessSet = conciseDailyDTO.getProdToFirstProcessSet().get(prodId);
        // 前面为空的处理过，这边SET不可能为空
        // 子工序是指令的首工序
        if (firstProcessSet.contains(firstProcess)) {
            // 取上指令的工序转交数量，wip_scan_history表中子工序为此子工序，且工站为0
            return processToTransmitHisMap == null ? 0L : processToTransmitHisMap.get(firstProcess);
        }
        // 剩下情况取当前工序当天过站数量。
        return crossHistoryCount == null ? 0L : crossHistoryCount.get(firstProcess);
    }


    private void setFourQtyOfDetail(TaskDailyStatDetail detail, Long yesterdayQty, Long turnIntoQty, Long turnOutQty, Long onhandQty) {
        detail.setOnhandQty(onhandQty == null ? 0L : onhandQty);
        detail.setTurnIntoQty(turnIntoQty == null ? 0L : turnIntoQty);
        detail.setTurnOutQty(turnOutQty == null ? 0L : turnOutQty);
        detail.setYesterdayOnhandQty(yesterdayQty == null ? 0L : yesterdayQty);
    }

    private TaskDailyStatDetail generateDetailBasicInfo(String headId, String attribute3) {
        TaskDailyStatDetail result = new TaskDailyStatDetail();
        result.setDetailId(UUID.randomUUID().toString());
        result.setHeadId(headId);
        // 注意塞入的值是数据字典中的attribute3字段
        result.setStatItem(attribute3);
        // 获取工号,设置创建人
        Map<String, String> headerMap = MESHttpHelper.getHttpRequestHeader();
        result.setCreateBy(StringUtils.isEmpty(headerMap.get(Constant.X_EMP_NO_SMALL)) ? Constant.SYSTEM
                : headerMap.get(Constant.X_EMP_NO_SMALL));
        return result;
    }

    private TaskDailyStatHead generateDailyStatHead(PsTask pstask, ConciseDailyDTO conciseDailyDTO) {
        String prodId = pstask.getProdplanId();
        Pair<String, String> pairRouteInfo = conciseDailyDTO.getProdToProcessInfoMap().get(prodId);
        TaskDailyStatHead headEntity = new TaskDailyStatHead();
        headEntity.setHeadId(UUID.randomUUID().toString());
        headEntity.setStatisticDate(conciseDailyDTO.getStatisticDate());
        headEntity.setTaskNo(pstask.getTaskNo());
        headEntity.setProdplanId(prodId);
        headEntity.setItemName(pstask.getItemName());
        headEntity.setVerNo(conciseDailyDTO.getProdToBomVerMap().get(prodId));
        headEntity.setItemNo(pstask.getItemNo());
        headEntity.setLeadFlag(pstask.getLeadFlag());
        headEntity.setSourceSysNo(pairRouteInfo == null ? Constant.STRING_EMPTY : pairRouteInfo.getSecond());
        headEntity.setOuterFlag(getOuterFlag(pairRouteInfo));
        headEntity.setTaskQty(pstask.getTaskQty() == null ? 0L : pstask.getTaskQty().longValue());
        headEntity.setReleaseDate(pstask.getReleaseDate());
        headEntity.setFirstOrderStartTime(conciseDailyDTO.getProdToFirstStartTime().get(prodId));
        headEntity.setPrepareAreaOnhand(conciseDailyDTO.getProdToPrepareAreaMap().get(prodId) == null ?
                0L : conciseDailyDTO.getProdToPrepareAreaMap().get(prodId));
        Map<String, Date> processOnlineMap = conciseDailyDTO.getProdToProcessOnLineTimeMap().get(prodId) == null
                ? new HashMap<>() : conciseDailyDTO.getProdToProcessOnLineTimeMap().get(prodId);
        headEntity.setSmtStartTime(processOnlineMap.get(Constant.SMT));
        headEntity.setDipStartTime(processOnlineMap.get(Constant.DIP));
        headEntity.setTestStartTime(processOnlineMap.get(Constant.TEST));
        headEntity.setSubmitQty(conciseDailyDTO.getProdToSubmitItemMap().get(prodId) == null ?
                0L : conciseDailyDTO.getProdToSubmitItemMap().get(prodId).longValue());
        headEntity.setInboundQty(conciseDailyDTO.getProdToInBoundMap().get(prodId) == null ?
                0L : conciseDailyDTO.getProdToInBoundMap().get(prodId).longValue());
        long submitHis = conciseDailyDTO.getProdToSubmitHisMap().get(prodId) == null ?
                0L : conciseDailyDTO.getProdToSubmitHisMap().get(prodId).longValue();
        headEntity.setOnlineQty(headEntity.getTaskQty() - submitHis);
        headEntity.setExternalType(pstask.getExternalType());
        headEntity.setInternalType(pstask.getInternalType());
        headEntity.setProductTypeName(BusinessUtil.getProductTypeName(pstask));
        // 获取工号,设置创建人
        Map<String, String> headerMap = MESHttpHelper.getHttpRequestHeader();
        headEntity.setCreateBy(StringUtils.isEmpty(headerMap.get(Constant.X_EMP_NO_SMALL)) ? Constant.SYSTEM
                : headerMap.get(Constant.X_EMP_NO_SMALL));
        return headEntity;
    }

    public String getOuterFlag(Pair<String, String> pairRouteInfo) {
        if (pairRouteInfo == null || StringUtils.isBlank(pairRouteInfo.getSecond())) {
            return Constant.NO;
        }
        if (pairRouteInfo.getSecond().contains(Constant.OUTSOURCE)) {
            return Constant.YES;
        }
        return Constant.NO;
    }

    /** 
    * @Description: 根据批次查询对应的单板版本
    * @Param: [psTaskList]
    * @return: java.util.Map<java.lang.String,java.lang.String>
    * @Author: Saber[10307315]
    * @Date: 2022/12/15 下午4:44
    */
    private Map<String, String> handleQueryBomVer(List<PsTask> psTaskList) throws Exception {
        Map<String, String> resultMap = new HashMap<>(Constant.INT_128);
        Set<String> itemNoSet = psTaskList.stream().map(k -> k.getItemNo()).collect(Collectors.toSet());
        List<BBomHeaderDTO> bBomHeaderDTOList = BasicsettingRemoteService.getBBomHeaderByProductCodeSet(itemNoSet);
        Map<String, String> itemNoToVerNoMap = bBomHeaderDTOList.stream().collect(Collectors.toMap(k -> k.getProductCode(),
                v -> StringUtils.isEmpty(v.getVerNo()) ? Constant.STRING_EMPTY : v.getVerNo(), (oldValue, newValue) -> newValue));
        for(int i = 0; i < psTaskList.size(); i++) {
            PsTask entity = psTaskList.get(i);
            resultMap.put(entity.getProdplanId(), itemNoToVerNoMap.get(entity.getItemNo()));
        }
        return resultMap;
    }

    /**
    * @Description: 计算wipINfo中各个子工序的结存，以及批次的备料区结存，并转为map记录
    * @Param: [psTaskList, prodPlanIdList, conciseDailyDTO]
    * @return: void
    * @Author: Saber[10307315]
    * @Date: 2022/12/12 下午3:51
    */
    private void  handleCountWipInfo(List<PsTask> psTaskList,List<String> prodPlanIdList, ConciseDailyDTO conciseDailyDTO) {
        Map<String, Map<String, Long>> prodToWipInfoCountMap = new HashMap<>(Constant.INT_128);
        Map<String, Long> prodToSnCountMap = new HashMap<>(Constant.INT_128);
        Map<String, Long> prodToPrepareAreaMap = new HashMap<>(Constant.INT_128);
        List<PsWipInfoDTO> psWipInfoDTOList = psWipInfoService.countSnQtyGroupByProdAndProcess(prodPlanIdList);
        conciseDailyDTO.setProdToWipInfoCountMap(prodToWipInfoCountMap);
        conciseDailyDTO.setProdToPrepareAreaMap(prodToPrepareAreaMap);
        if (CollectionUtils.isEmpty(psWipInfoDTOList)) {
            return;
        }
        for (int i = 0; i < psWipInfoDTOList.size(); i++) {
            PsWipInfoDTO entity = psWipInfoDTOList.get(i);
            String prodId = entity.getAttribute1();
            Map<String, Long> tempMap = prodToWipInfoCountMap.get(prodId);
            if (tempMap == null) {
                tempMap = new HashMap<>(Constant.INT_64);
                prodToWipInfoCountMap.put(prodId, tempMap);
            }
            tempMap.put(entity.getCurrProcessCode(), entity.getOnhandQty());
        }
        for (int i = 0; i < psWipInfoDTOList.size(); i++) {
            PsWipInfoDTO entity = psWipInfoDTOList.get(i);
            String prodId = entity.getAttribute1();
            Long qty = prodToSnCountMap.get(prodId);
            if (qty == null) {
                qty = 0L;
            }
            prodToSnCountMap.put(prodId, qty + entity.getOnhandQty());
        }
        for (PsTask entity : psTaskList) {
            Long taskQty = entity.getTaskQty() == null ? 0L : entity.getTaskQty().longValue();
            Long qty = taskQty -
                    (prodToSnCountMap.get(entity.getProdplanId()) == null ? 0L : prodToSnCountMap.get(entity.getProdplanId()));
            prodToPrepareAreaMap.put(entity.getProdplanId(), qty);
        }
    }

    /**
    * @Description:
    * @Param: [prodToItemNoMap, prodPlanIdList]
    * @return: key 为批次，value的第一个值为processCode组成的工艺路径用于后续判断，第二个为中文值用于前端展示。
    * @Author: Saber[10307315]
    * @Date: 2022/12/9 上午10:17
     */
    private Map<String, Pair<String, String>> handleQueryProcessInfo(List<PsTask> psTaskList, List<String> prodPlanIdList,
                                                                     ConciseDailyDTO conciseDailyDTO) throws Exception {
        Map<String, Pair<String, String>> resultMap = new HashMap<>(Constant.INT_128);
        List<String> itemNoList = psTaskList.stream().filter(k -> StringUtils.isEmpty(k.getTaskStatus()))
                .map(k -> k.getItemNo()).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(itemNoList)) {
            List<CtBasicRouteDTO> routeInfoList = CrafttechRemoteService.getRouteAndSeqByItemNos(itemNoList);
            Map<String, CtBasicRouteDTO> itemNoToEntityMap = routeInfoList.stream().collect(Collectors.toMap(k -> k.getItemNo(),
                    v -> v, (oldValue, newValue) -> newValue));
            for(int i = 0; i < psTaskList.size(); i++) {
                PsTask entity = psTaskList.get(i);
                CtBasicRouteDTO ctBasicRouteDTO = itemNoToEntityMap.get(entity.getItemNo());
                if (ctBasicRouteDTO == null) {
                    continue;
                }
                String first = ctBasicRouteDTO.getRoutePathCode();
                String second = ctBasicRouteDTO.getRouteDetail();
                resultMap.put(entity.getProdplanId(), Pair.of(first, second));
            }
        }
        if (!CollectionUtils.isEmpty(prodPlanIdList)) {
            // 同时在查询指令表后，将指令的首工序和最后工序记录到DTO中，以及批次对应的首指令的开工时间
            resultMap.putAll(queryProcessInfoByProd(prodPlanIdList, conciseDailyDTO));
        }
        return resultMap;
    }

    /**
    * @Description: 找到批次对应的中英文路径信息，同时记录了批次所有指令的首工序和最后工序信息，以及批次对应的首指令的开工时间。
    * @Param: [prodPlanIdList, conciseDailyDTO]
    * @return: java.util.Map<java.lang.String,org.springframework.data.util.Pair<java.lang.String,java.lang.String>>
    * @Author: Saber[10307315]
    * @Date: 2022/12/12 下午2:40
    */
    private Map<String, Pair<String, String>> queryProcessInfoByProd(List<String> prodPlanIdList,  ConciseDailyDTO conciseDailyDTO) throws Exception {
        Map<String, Pair<String, String>> resultMap = new HashMap<>(Constant.INT_128);
        // 因此使用prodPlanId查询计划指令表，找routeId
        List<PsWorkOrderBasic> workOrderList = PlanscheduleRemoteService.queryRouteIdByProdIdList(prodPlanIdList);
        // 额外记录批次下所有指令的首工序和最后工序，用于后续计算判断。
        findFirstAndLastProcessOfWorkList(workOrderList, conciseDailyDTO);
        // 额外记录批次对应的首指令的开工时间
        conciseDailyDTO.setProdToFirstStartTime(findFirstStartTime(workOrderList));
        // 记录指令对应子工序所在线体
        matchLineForProcessOfWorkList(conciseDailyDTO, workOrderList);
        Set<String> routeIdSet = workOrderList.stream().map(k -> k.getRouteId()).collect(Collectors.toSet());
        // 使用routID，找工艺路径信息，并且根据routdetail表的seq进行排序组合子工序返回
        List<CtBasicRouteDTO> routeInfoList = CrafttechRemoteService.getRouteAndSeqByRouteIds(new ArrayList<>(routeIdSet));
        // 组合成批次为key,路径信息为value返回。
        Map<String, CtBasicRouteDTO> routeIdToEntityMap = routeInfoList.stream().collect(Collectors.toMap(k -> k.getRouteId(),
                v -> v, (oldValue, newValue) -> newValue));
        for(int i = 0; i < workOrderList.size(); i++) {
            PsWorkOrderBasic entity = workOrderList.get(i);
            CtBasicRouteDTO ctBasicRouteDTO = routeIdToEntityMap.get(entity.getRouteId());
            if (ctBasicRouteDTO == null || resultMap.containsKey(entity.getProdPlanId())) {
                continue;
            }
            String first = ctBasicRouteDTO.getRoutePathCode();
            String second = ctBasicRouteDTO.getRouteDetail();
            resultMap.put(entity.getSourceTask(), Pair.of(first, second));
        }
        return resultMap;
    }

    public void matchLineForProcessOfWorkList(ConciseDailyDTO conciseDailyDTO, List<PsWorkOrderBasic> workOrderList) {
        Map<String, Map<String, String>> prodProcessToLineMap = new HashMap<>();
        conciseDailyDTO.setProdProcessToLineMap(prodProcessToLineMap);
        if (CollectionUtils.isEmpty(workOrderList)) {
            return;
        }
        for (int i = 0; i < workOrderList.size(); i++) {
            PsWorkOrderBasic entity = workOrderList.get(i);
            String processGroup = entity.getProcessGroup();
            String lineCode = entity.getLineCode();
            if (StringUtils.isEmpty(processGroup)) {
                continue;
            }
            // 如果多个子工序，需要切割
            String[] processList = processGroup.split(Constant.STR_SPLIT_$);
            Map<String, String> tempMap = prodProcessToLineMap.get(entity.getSourceTask());
            if (tempMap == null) {
                tempMap = new HashMap<>();
                prodProcessToLineMap.put(entity.getSourceTask(), tempMap);
            }
            for (String process : processList) {
                tempMap.put(process, lineCode);
            }
        }
    }


    /** 
    * @Description: 找到批次对应的所有指令的子工序组的第一个子工序代码。
    * @Param: [workOrderList]
    * @return: java.util.Map<java.lang.String,java.util.Set<java.lang.String>>
    * @Author: Saber[10307315]
    * @Date: 2022/12/13 下午5:12
    */
    private void findFirstAndLastProcessOfWorkList(List<PsWorkOrderBasic> workOrderList,
                                                                       ConciseDailyDTO conciseDailyDTO) {
        Map<String, Set<String>> prodToFirstProcessSet = new HashMap<>(Constant.INT_128);
        Map<String, Set<String>> prodToLastProcessSet = new HashMap<>(Constant.INT_128);
        conciseDailyDTO.setProdToFirstProcessSet(prodToFirstProcessSet);
        conciseDailyDTO.setProdToLastProcessSet(prodToLastProcessSet);
        if (CollectionUtils.isEmpty(workOrderList)) {
            return;
        }
        for (int i = 0; i < workOrderList.size(); i++) {
            PsWorkOrderBasic entity = workOrderList.get(i);
            String processGroup = entity.getProcessGroup();
            String firstProcessCode = processGroup;
            String lastProcessCode = processGroup;
            if (StringUtils.isEmpty(processGroup)) {
                continue;
            }
            // 找到指令的首工序
            int firstPosition = processGroup.indexOf(Constant.STR_$);
            int lastPosition = processGroup.lastIndexOf(Constant.STR_$);
            // 如果多个子工序，需要切割取第一个作为首工序，最后一个当做最后工序
            if (firstPosition >= 0) {
                firstProcessCode = processGroup.substring(0, firstPosition);
            }
            if (lastPosition >= 0 && lastPosition + 1 < processGroup.length()) {
                lastProcessCode = processGroup.substring(lastPosition + 1);
            }
            Set<String> tempSet = prodToFirstProcessSet.get(entity.getSourceTask());
            Set<String> tempSetLast = prodToLastProcessSet.get(entity.getSourceTask());
            if (tempSet == null) {
                tempSet = new HashSet<>();
                prodToFirstProcessSet.put(entity.getSourceTask(), tempSet);
            }
            if (tempSetLast == null) {
                tempSetLast = new HashSet<>();
                prodToLastProcessSet.put(entity.getSourceTask(), tempSetLast);
            }
            tempSet.add(firstProcessCode);
            tempSetLast.add(lastProcessCode);
        }
    }

    private Map<String, Map<String, Long>> handleCountScanHistory(Date yesterday, List<String> prodPlanIdList, boolean stationFlag) throws Exception {
        Map<String, Map<String, Long>> resultMap = new HashMap<>(Constant.INT_128);
        // 注意工站为0和不为0，都要排除工站为空的数据。
        List<WipScanHistoryDTO> wipScanInfoList = wipScanHistoryService.countQtyGroupByProdAndProcess(yesterday, prodPlanIdList, stationFlag);
        if (CollectionUtils.isEmpty(wipScanInfoList)) {
            return resultMap;
        }
        for (int i = 0; i < wipScanInfoList.size(); i++) {
            WipScanHistoryDTO entity = wipScanInfoList.get(i);
            String prodId = entity.getAttribute1();
            Map<String, Long> tempMap = resultMap.get(prodId);
            if (tempMap == null) {
                tempMap = new HashMap<>(Constant.INT_64);
                resultMap.put(prodId, tempMap);
            }
            tempMap.put(entity.getCurrProcessCode(), stationFlag ? entity.getTransmitQty() : entity.getCrossStationQty());
        }
        return resultMap;
    }

    /**
    * @Description:  查询批次昨日的简明日报信息，用于计算昨存。
    * @Param: [yesterday, prodPlanIdList]
    * @return: 返回key为批次，value为记录了子工序和结存关系的map。
    * @Author: Saber[10307315]
    * @Date: 2022/12/8 下午6:38
    */
    private Map<String, Map<String, Long>> handleCountDailyDetail(Date yesterday, List<String> prodPlanIdList) throws Exception {
        Map<String, Map<String, Long>> resultMap = new HashMap<>(Constant.INT_128);
        List<TaskDailyStatDetail> dailyDetailList = taskDailyStatDetailService.selectByTimeAndProds(yesterday, prodPlanIdList);
        if (CollectionUtils.isEmpty(dailyDetailList)) {
            return resultMap;
        }
        for (int i = 0; i < dailyDetailList.size(); i++) {
            TaskDailyStatDetail entity = dailyDetailList.get(i);
            String prodId = entity.getProdplanId();
            Map<String, Long> tempMap = resultMap.get(prodId);
            if (tempMap == null) {
                tempMap = new HashMap<>(Constant.INT_64);
                resultMap.put(prodId, tempMap);
            }
            tempMap.put(entity.getStatItem(), entity.getOnhandQty());
        }
        return resultMap;
    }

    /**
     * 发送邮件
     *
     * @param e          异常信息
     * @param emailTitle 邮件主题
     */
    private void sendEmailMsg(Exception e, String emailTitle) {
        try {
            List<SysLookupTypesDTO> sysLookupTypesList = BasicsettingRemoteService.getSysLookUpValue
                    (Constant.LOOKUP_TYPE_1004095);
            String emails = sysLookupTypesList.stream().filter(item -> Constant.EMAIL.equals(item.getAttribute1())
                            && StringUtils.isNotBlank(item.getLookupMeaning()))
                    .map(SysLookupTypesDTO::getLookupMeaning).collect(Collectors.joining());
            if (StringUtils.isBlank(emails)) {
                return;
            }
            String msg = e.toString();
            emailUtils.sendMail(emails, emailTitle, StringUtils.EMPTY, msg, "");
        } catch (Exception exception) {
            LOG.error("send email", exception);
        }
    }

    /**
     * @Description: 通过remark来找到批次首指令，在查询指令操作历史表找到首指令首次开工时间。
     * @Param: [workOrderList]
     * @return: java.util.Map<java.lang.String,java.util.Date>
     * @Author: Saber[10307315]
     * @Date: 2022/12/12 下午2:39
     */
    private Map<String, Date> findFirstStartTime(List<PsWorkOrderBasic> workOrderList) throws Exception {
        Map<String, Date> resultMap = new HashMap<>(Constant.INT_128);
        Map<String, Pair<String, String>> tempMap = new HashMap<>(Constant.INT_128);
        if (CollectionUtils.isEmpty(workOrderList)) {
            return resultMap;
        }
        for (int i = 0; i < workOrderList.size(); i++) {
            PsWorkOrderBasic entity = workOrderList.get(i);
            if (StringUtils.isEmpty(entity.getRemark()) || StringUtils.isEmpty(entity.getWorkOrderNo())) {
                continue;
            }
            String prodId = entity.getSourceTask();
            Pair<String, String> pair = tempMap.get(prodId);
            String remark = Objects.isNull(pair) ? null : pair.getFirst();
            // 为空则新增,不为空，当前指令排在前面(remark小)，则更新MAP
            if (StringUtils.isEmpty(remark) || remark.compareTo(entity.getRemark()) > 0) {
                tempMap.put(prodId, Pair.of(entity.getRemark(), entity.getWorkOrderNo()));
            }
        }
        List<String> workOrderNoList = new ArrayList<>();
        for (Map.Entry<String, Pair<String, String>> entry : tempMap.entrySet()) {
            workOrderNoList.add(entry.getValue().getSecond());
        }
        // 查询指令历史表，找指令最新开工时间。
        List<WorkOrderOperateHis> workOrderOperateHisList = PlanscheduleRemoteService.getWorkFirstStartTimeByWorkNoList(workOrderNoList);
        Map<String, Date> workNoToFirstStartTime = workOrderOperateHisList.stream().collect(Collectors.toMap
                (k -> k.getWorkOrderNo(), v -> v.getCreateDate(), (oldValue, newValue) -> oldValue));
        for (Map.Entry<String, Pair<String, String>> entry : tempMap.entrySet()) {
            resultMap.put(entry.getKey(), workNoToFirstStartTime.get(entry.getValue().getSecond()));
        }
        return resultMap;
    }
    @Override
    public Integer countByConciseDailyDTO(ConciseDailyDTO conciseDailyDTO) throws Exception {
        return taskDailyStatHeadService.countByConciseDailyDTO(conciseDailyDTO);
    }
    @Override
    public Page<Map<String, Object>> conciseDailyQuery(ConciseDailyDTO conciseDailyDTO) throws Exception {
        try {
            // 校验参数
            validateQueryParams(conciseDailyDTO, true);
            List<TaskDailyStatHead> headList = new ArrayList<>();
            List<TaskDailyStatDetail> detailList = new ArrayList<>();

            // 查询数据字典，配置子工序的数据字典，1003010
            List<SysLookupTypesDTO> sysLookupTypesList = BasicsettingRemoteService.getSysLookUpValue
                    (Constant.LOOKUP_TYPE_1003010);
            Integer total = 0;
            // 如果是实时查询
            if (conciseDailyDTO.isRealTimeQueryFlag()) {
                // 根据条件查询批次,这些批次前端限定了最多一次分页查询，row不超过100. 因此可以直接查询，不用切割
                Page<PsTask> pageTask = PlanscheduleRemoteService.pageSelectForConciseDailyReelTime(conciseDailyDTO);
                List<PsTask> psTaskList = pageTask.getRows();
                total = pageTask.getTotal();
                reelTimeCompute(conciseDailyDTO, psTaskList, sysLookupTypesList);
                // 赋值数据
                headList = conciseDailyDTO.getStatHeadList();
                detailList = conciseDailyDTO.getStatDetailList();
            }
            // 如果不是实时，则查询简明日报表
            else {
                // 为了后续转化，这边头详表分开查询
                // 先查头表
                total = taskDailyStatHeadService.countByConciseDailyDTO(conciseDailyDTO);
                headList = taskDailyStatHeadService.selectByConciseDailyDTO(conciseDailyDTO);
                // 转化为头表idList
                List<String> headIdList = headList.stream().map(k -> k.getHeadId()).collect(Collectors.toList());
                // 再查详表
                detailList = taskDailyStatDetailService.selectByHeadIds(headIdList);
            }
            // 数据转化为List<Map>用于返回前端
            List<Map<String, Object>> resultMapList = transferToMapList(headList, detailList, sysLookupTypesList);
            // 环保属性转化
            transferLeadFlag(resultMapList);
            // 制造MBOM查询
            setMBomProductCode(resultMapList);
            // 转为PAGE格式
            Page<Map<String, Object>> result = new Page<>();
            result.setCurrent(conciseDailyDTO.getPage());
            result.setTotal(total);
            result.setRows(resultMapList);
            return result;
        } finally {
            // 不管查询失败与否，都记录缓存
            conciseDailyDTO = conciseDailyDTO == null ? new ConciseDailyDTO() : conciseDailyDTO;
            String jsonString = JSONObject.toJSONString(conciseDailyDTO.getSelectColList());
            //设置缓存
            String redisKey = Constant.CONCISE_DAILY_SELECT_COL_CACHE + Constant.AND + conciseDailyDTO.getEmpNo()
                    + Constant.AND + conciseDailyDTO.getFactoryId();
            RedisCacheUtils.set(redisKey, jsonString, Constant.CACHE_EXPIRED_TIME_30_DAY);
        }
    }

    private void setMBomProductCode(List<Map<String, Object>> list) {
        List<String> prodplanIds = list.stream().map(i -> String.valueOf(i.get(Constant.PRODPLAN_ID))).collect(Collectors.toList());
        List<BProdBomHeaderDTO> mBomList = centerfactoryRemoteService.queryProductCodeByProdPlanIdList(prodplanIds);
        Map<String, String> mBomProductCodeByProdplanIdMap = mBomList.stream().collect(Collectors.toMap(BProdBomHeaderDTO::getProdplanId, BProdBomHeaderDTO::getProductCode, (v1, v2) -> v1));
        for(Map<String, Object> map : list) {
            String mBomProductCode = mBomProductCodeByProdplanIdMap.get(String.valueOf(map.get(Constant.PRODPLAN_ID)));
            map.put("mBom", mBomProductCode == null ? String.valueOf(map.get(Constant.ITEMNO)) : mBomProductCode);
        }
    }

    private void transferLeadFlag(List<Map<String, Object>> resultMapList) throws Exception {
        // 查询数据字典1036
        List<SysLookupTypesDTO> sysLookupTypesList = BasicsettingRemoteService.getSysLookUpValue
                (Constant.LOOK_UP_TYPE_LEAD);
        if (CollectionUtils.isEmpty(sysLookupTypesList)) {
            return;
        }
        Map<String, String> leadFlagMap = sysLookupTypesList.stream().collect(Collectors.toMap(
                SysLookupTypesDTO::getLookupMeaning, v -> StringUtils.isEmpty(v.getDescriptionChinV()) ?
                        Constant.STRING_EMPTY : v.getDescriptionChinV(), (oldValue, newValue) -> newValue));
        // 循环更改值
        for (Map<String, Object> entity : resultMapList) {
            String leadFlagCode = String.valueOf(entity.get(Constant.LEAD_FLAG));
            String leadFlag = leadFlagMap.get(leadFlagCode);
            leadFlag = leadFlag == null ? leadFlagCode : leadFlag;
            entity.put(Constant.LEAD_FLAG, leadFlag);
        }
    }


    /**
    * @Description:
    * @Param: [statItemsToQtyMap, entityMap, statItem] 详表数据，整个实体MAP，数据字典一个子项中attribute3值
    * @return: void
    * @Author: Saber[10307315]
    * @Date: 2022/12/20 下午7:19
    */
    private void handleInsertDetail(Map<String, Object> statItemsToQtyMap, Map<String, Object> entityMap, String statItem) {
        // 查看详表是否有数据，有的就去详表，没有默认为0
        Object qtyA = statItemsToQtyMap.get(statItem + Constant.SUFFIX_A) == null ?
                0L : statItemsToQtyMap.get(statItem + Constant.SUFFIX_A);
        Object qtyB = statItemsToQtyMap.get(statItem + Constant.SUFFIX_B) == null ?
                0L : statItemsToQtyMap.get(statItem + Constant.SUFFIX_B);
        Object qtyC = statItemsToQtyMap.get(statItem + Constant.SUFFIX_C) == null ?
                0L : statItemsToQtyMap.get(statItem + Constant.SUFFIX_C);
        Object qtyD = statItemsToQtyMap.get(statItem + Constant.SUFFIX_D) == null ?
                0L : statItemsToQtyMap.get(statItem + Constant.SUFFIX_D);
        Object lineName = statItemsToQtyMap.get(statItem + Constant.SUFFIX_E) == null ?
                0L : statItemsToQtyMap.get(statItem + Constant.SUFFIX_E);
        entityMap.put(statItem + Constant.SUFFIX_A, qtyA);
        entityMap.put(statItem + Constant.SUFFIX_B, qtyB);
        entityMap.put(statItem + Constant.SUFFIX_C, qtyC);
        entityMap.put(statItem + Constant.SUFFIX_D, qtyD);
        entityMap.put(statItem + Constant.SUFFIX_E, lineName);
    }

    private void reelTimeCompute(ConciseDailyDTO conciseDailyDTO, List<PsTask> psTaskList,
                                 List<SysLookupTypesDTO> sysLookupTypesList) throws Exception {
        // 统一提取时间。取今天
        conciseDailyDTO.setStatisticDate(new Date());
        // 切目录值中多个子工序代码为数组当做value，字典中文表述作key,
        Map<String, String[]> sysDesChinToProcessCodes = handleSplitSysProcessGroup(sysLookupTypesList);
        conciseDailyDTO.setSysDesChinToProcessCodes(sysDesChinToProcessCodes);
        if (CollectionUtils.isEmpty(psTaskList)) {
            return;
        }
        Date time = new Date();
        // 调用计算逻辑，赋值DTO中的头表和详表
        queryAndCalculateData(conciseDailyDTO, psTaskList, sysLookupTypesList, time);
    }

    private void validateQueryParams(ConciseDailyDTO conciseDailyDTO, boolean queryFlag) throws Exception {
        if (conciseDailyDTO == null) {
            throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, MessageId.PARAM_IS_NULL);
        }
        if (queryFlag && (conciseDailyDTO.getPage() <= 0 || conciseDailyDTO.getRow() <= 0)) {
            throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, MessageId.CONCISE_DAILY_QUERY_PAGE_OR_ROW_ILLEGAL);
        }
        // 时间参数不能同时为空，如果实时计算，则前端默认当天时间，如果非实时，则可选不超过半年
        if(conciseDailyDTO.getStartDate() == null || conciseDailyDTO.getEndDate() == null){
            throw new MesBusinessException(com.zte.itp.msa.core.model.RetCode.VALIDATIONERROR_CODE, MessageId.DATE_RANGE_IS_EMPTY);
        }
        // 时间不能超过184天。
        if (conciseDailyDTO.getEndDate().getTime() - conciseDailyDTO.getStartDate().getTime() > Constant.TIME_STAMP_184_DAY) {
            throw new MesBusinessException(com.zte.itp.msa.core.model.RetCode.VALIDATIONERROR_CODE, MessageId.DATE_RANGE_TOO_LONG);
        }
    }

    private List<Map<String, Object>> transferToMapList(List<TaskDailyStatHead> headList,
                                                        List<TaskDailyStatDetail> detailList,
                                                        List<SysLookupTypesDTO> sysLookupTypesList) throws Exception {
        List<Map<String, Object>> resultMapList = new ArrayList<>();
        if (CollectionUtils.isEmpty(headList)) {
            return resultMapList;
        }
        // 详表转换为key-value格式
        Map<String, Map<String, Object>> headIdToStatItemsMap = new HashMap<>();
        Map<String, String> mapCodeToName = getLineNameFromCode(detailList);
        for (int i = 0; i < detailList.size(); i++) {
            TaskDailyStatDetail entity = detailList.get(i);
            String headId = entity.getHeadId();
            Map<String, Object> tempMap = headIdToStatItemsMap.get(headId);
            if (tempMap == null) {
                // 目前数据字典子项×4超过100多，初始化时可指定大小，避免MAP扩容浪费资源
                tempMap = new HashMap<>(Constant.INT_128);
                headIdToStatItemsMap.put(headId, tempMap);
            }
            // 存储的是数据字典中的attribute3
            String statItem = entity.getStatItem();
            // 每一个详表数据需要转为4个key-value,推到结果MAP中。
            tempMap.put(statItem + Constant.SUFFIX_A, entity.getYesterdayOnhandQty());
            tempMap.put(statItem + Constant.SUFFIX_B, entity.getTurnIntoQty());
            tempMap.put(statItem + Constant.SUFFIX_C, entity.getTurnOutQty());
            tempMap.put(statItem + Constant.SUFFIX_D, entity.getOnhandQty());
            putLineName(entity, mapCodeToName, tempMap, statItem);
        }
        //工艺段结存-取数据字典286524
        List<SysLookupTypesDTO> sysCraftBalanceList = BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_CRAFT_BALANCE);
        // 工艺段-字段名键值对
        Map<String,String> craftBalanceMap = sysCraftBalanceList.stream().collect(Collectors.toMap(o -> o.getLookupMeaning(), o -> o.getAttribute1()));
        // 遍历头表，因为头表的一行数据，在前端显示为一行
        for (TaskDailyStatHead headEntity : headList) {
            String headId = headEntity.getHeadId();
            // 得到详表四项信息
            Map<String, Object> map = headIdToStatItemsMap.get(headId);
            Map<String, Object> statItemsToQtyMap = map == null ? new HashMap<>() : map;
            // 先将头表所有信息转为MAP。
            Map<String, Object> entityMap = JSON.parseObject(JSON.toJSONStringWithDateFormat
                            (headEntity, "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteDateUseDateFormat),
                    new TypeReference<Map<String, Object>>(){});
            if (entityMap.get(Constant.STRING_STATISTIC_DATE) == null) {
                continue;
            }
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            entityMap.put(Constant.STRING_STATISTIC_DATE, sdf.format(sdf.parse((String)entityMap.get(Constant.STRING_STATISTIC_DATE))));
            // 遍历数据字典，每一子项，都进行转化后，添加到MAP中
            for(SysLookupTypesDTO sysEntity : sysLookupTypesList) {
                // attribute3存储的是数据库中statItem存放的字段值。
                String statItem = sysEntity.getAttribute3();
                if (StringUtils.isEmpty(statItem)) {
                    continue;
                }
                // 添加到整体的map中，
                handleInsertDetail(statItemsToQtyMap, entityMap, statItem);
                // 计算工艺段结存
                handleCraftBalance(sysEntity, craftBalanceMap, entityMap, statItem);
            }
            // 循环完数据字典，则所需值都已经填入，则添加到结果List中。
            resultMapList.add(entityMap);
        }
        return resultMapList;
    }

    public Map<String, String> getLineNameFromCode(List<TaskDailyStatDetail> detailList) {
        Map<String, String> mapCodeToName = new HashMap<>();
        List<String> lines = new ArrayList<>();
        for (TaskDailyStatDetail taskDailyStatDetail : detailList) {
            String lineCode = taskDailyStatDetail.getLineCode();
            if(StringUtils.isNotEmpty(lineCode)){
                lines.addAll(Arrays.asList(lineCode.split(COMMA)));
            }
        }
        lines = lines.stream().filter(l->StringUtils.isNotEmpty(l)).distinct().collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(lines)) {
            mapCodeToName = BasicsettingRemoteService.getLineNameByCodeList(lines);
        }
        return mapCodeToName;
    }

    public void putLineName(TaskDailyStatDetail entity, Map<String, String> mapCodeToName, Map<String, Object> tempMap, String statItem) {
        String lineCode = entity.getLineCode();
        if(StringUtils.isNotEmpty(lineCode)){
           List<String> lineNameArr = new ArrayList<>();
            String[] lineArr = lineCode.split(COMMA);
            for (String line : lineArr) {
                String lineNameTemp = mapCodeToName.get(line);
                if(StringUtils.isNotEmpty(lineNameTemp)){
                    lineNameArr.add(lineNameTemp);
                }else{
                    lineNameArr.add(line);
                }
            }
            String lineName = lineNameArr.stream().distinct().collect(Collectors.joining(COMMA));
            tempMap.put(statItem + Constant.SUFFIX_E, lineName);
        } else{
            tempMap.put(statItem + Constant.SUFFIX_E, EMPTY_STRING);
        }
    }

    /**
     *
     * @param sysEntity
     * @param craftBalanceMap
     * @param entityMap
     * @param statItem
     */
    public void handleCraftBalance(SysLookupTypesDTO sysEntity, Map<String, String> craftBalanceMap, Map<String, Object> entityMap, String statItem) {
        // 数据字典子项英文描述代表统计项所属工序段
        String sysEntityCraft = sysEntity.getDescriptionEngV();
        // 工艺段对应的日报字段名称，用于前端或表格导出对应取值
        String craftValue = craftBalanceMap.get(sysEntityCraft);
        if(StringUtils.isNotEmpty(craftValue)){
            Object craftBalance = entityMap.get(craftValue);
            if (Objects.isNull(craftBalance)) {
                entityMap.put(craftValue, 0L);
            }
            Long currBalance = (long) entityMap.get(craftValue);
            // 同工艺段所有子项结存累加
            Object balance = entityMap.get(statItem + Constant.SUFFIX_D);
            currBalance += null == balance? 0L: (long)balance;
            entityMap.put(craftValue, currBalance);
        }
    }

    /** 
    * @Description: 导出
    * @Param: [conciseDailyDTO]
    * @return: void
    * @Author: Saber[10307315]
    * @Date: 2022/12/26 下午7:20
    */
    @Override
    @AsyncExport(functionName = "简明日报导出")
    public void conciseDailyExport(HttpServletResponse response, ConciseDailyDTO conciseDailyDTO) throws Exception {
        List<String> propList = conciseDailyDTO.getExportPropList();
        if (CollectionUtils.isEmpty(propList)) {
            propList = new ArrayList();
        }
        // 校验参数
        validateQueryParams(conciseDailyDTO, false);
        // 查询数据字典，配置子工序的数据字典，1003010
        List<SysLookupTypesDTO> sysLookupList = BasicsettingRemoteService.getSysLookUpValue
                (Constant.LOOKUP_TYPE_1003010);
        Map<String, String> propToHeadMap = new HashMap<>(Constant.INT_64);
        for (SysLookupTypesDTO entity : sysLookupList) {
            if (StringUtils.isEmpty(entity.getAttribute3()) || StringUtils.isEmpty(entity.getDescriptionChinV())) {
                throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, MessageId.SYS_LOOK_CONFIG_ERROR);
            }
            propToHeadMap.put(entity.getAttribute3(), entity.getDescriptionChinV());
        }
        // 根据数据字典找到表格头
        List<String> headers = new ArrayList<>();
        for(int i = 0; i < propList.size(); i++) {
            if (StringUtils.isEmpty(propToHeadMap.get(propList.get(i)))) {
                throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, MessageId.SYS_LOOK_CONFIG_ERROR);
            }
            headers.add(propToHeadMap.get(propList.get(i)));
        }
        // 处理表格文件头名称和属性
        //工艺段结存的行名和属性
        List<SysLookupTypesDTO> sysCraftBalanceList = BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_CRAFT_BALANCE);
        handlerExportNameAndProp(conciseDailyDTO, headers, propList, sysCraftBalanceList);
        // 导出类
        BigExcelProcesser bigExcelProcesser = new BigExcelProcesser();
        setNumHeader(sysLookupList, bigExcelProcesser, sysCraftBalanceList);
        // 新建文件
        bigExcelProcesser.createOrAppend(conciseDailyDTO.getHeaderArr(), new ArrayList<>(), conciseDailyDTO.getPropArr());
        // 遍历添加。分情况
        if (conciseDailyDTO.isRealTimeQueryFlag()) {
            reelTimeExport(conciseDailyDTO, conciseDailyDTO.getHeaderArr(), sysLookupList, conciseDailyDTO.getPropArr(), bigExcelProcesser);
            this.uploadDocumentCloud(bigExcelProcesser);
        }
        // 如果不是实时，则查询简明日报表
        else {
           notReelTimeExport(conciseDailyDTO, sysLookupList, bigExcelProcesser,response);
        }
    }

    private void setNumHeader(List<SysLookupTypesDTO> sysLookupList, BigExcelProcesser bigExcelProcesser, List<SysLookupTypesDTO> sysCraftBalanceList ) {
        Set<String> headerSet = new HashSet<>();
        sysLookupList.stream().filter(item -> Constant.FLAG_Y.equals(item.getAttribute4()))
                .map(SysLookupTypesDTO::getDescriptionChinV).forEach(header -> {
                    // 转为四项
                    // 昨存
                    headerSet.add(header + Constant.YESTERDAY_ON_HAND_QTY);
                    // 转入
                    headerSet.add(header + Constant.TURN_IN);
                    // 转出
                    headerSet.add(header + Constant.TURN_OUT);
                    // 结存
                    headerSet.add(header + Constant.ON_HAND_QTY);
                });
        // 工艺段结存表格字段名
        sysCraftBalanceList.stream().map(SysLookupTypesDTO::getDescriptionChinV).forEach(des -> {
                    headerSet.add(des);
                });
        // 固定列设置数值格式
        headerSet.add(Constant.CONCISE_DAILY_EXPORT_TASKQTY);
        headerSet.add(Constant.CONCISE_DAILY_EXPORT_PREPAREAREAONHAND);
        headerSet.add(Constant.CONCISE_DAILY_EXPORT_SUBMITQTY);
        headerSet.add(Constant.CONCISE_DAILY_EXPORT_INBOUNDQTY);
        headerSet.add(Constant.CONCISE_DAILY_EXPORT_ONLINEQTY);
        bigExcelProcesser.setNumHeaders(headerSet);
    }

    private String getFileName() {
        SimpleDateFormat sdf = new SimpleDateFormat(MpConstant.DATE_FORMAT_YYYYMMDD);
        String fileName = sdf.format(new Date()) + Constant.CONCISE_DAILY_EXPORT_FILE_NAME;
        return fileName;
    }

    /**
     * 历史数据
     * @param conciseDailyDTO
     * @param sysLookupList
     * @param bigExcelProcesser
     * @param response
     * @return
     * @throws Exception
     */
    private void notReelTimeExport(ConciseDailyDTO conciseDailyDTO, List<SysLookupTypesDTO> sysLookupList, BigExcelProcesser bigExcelProcesser,HttpServletResponse response) throws Exception {
        List<TaskDailyStatHead> headList = new ArrayList<>();
        Integer page = 0;
        conciseDailyDTO.setRow(Constant.INT_1000);
        exportDataToFile(conciseDailyDTO, sysLookupList, bigExcelProcesser, headList, page);
        this.uploadDocumentCloud(bigExcelProcesser);
    }

    private void uploadDocumentCloud(BigExcelProcesser bigExcelProcesser) throws Exception {
        String realTimeFileName = getFileName();
        String filePath = FileUtils.tempPath + System.currentTimeMillis()+Constant.GANG+ realTimeFileName;
        FileUtils.checkFilePath(filePath);
        bigExcelProcesser.saveAsFile(filePath);
        asyncExportFileCommonService.uploadFileThenClearLocalFileUpdateLog(realTimeFileName, filePath);
    }

    /**
     * 异步导出到邮件
     * @param conciseDailyDTO
     * @param sysLookupList
     * @param bigExcelProcesser
     * @param headList
     * @param page
     */
    private void asynchronousExportToMail(ConciseDailyDTO conciseDailyDTO, List<SysLookupTypesDTO> sysLookupList, BigExcelProcesser bigExcelProcesser, List<TaskDailyStatHead> headList, Integer page) {
        Map<String, String>  header = MESHttpHelper.getHttpRequestHeader();
        ThreadUtil.EXECUTOR.execute(() -> {
            // 文件写成功后，将文档云文件路径写到reids
            try {
                MESHttpHelper.setHttpRequestHeader(header);
                StringBuffer stringBuffer = new StringBuffer();
                //多次分批导出
                exportDataToFile(conciseDailyDTO, sysLookupList, bigExcelProcesser, headList, page);
                String fileName = getFileName();
                String filePath = FileUtils.tempPath+System.currentTimeMillis()+fileName;
                FileUtils.checkFilePath(filePath);
                bigExcelProcesser.saveAsFile(filePath);

                // 上传文档云，并获取文档路径
                String fileKey = cloudDiskHelper.fileUpload(filePath, conciseDailyDTO.getEmpNo(), CloudDiskHelper.MAX_RETRY_TIMES);
                // 上传成功后删除本地文件
                FileUtils.deleteFile(filePath);
                String fileUrl = cloudDiskHelper.getFileDownloadUrl(fileKey, null, conciseDailyDTO.getEmpNo());
                // 消息内容
                stringBuffer.append(Constant.ExportConciseDailyReport.EMAIL_PREFIX)
                        .append(fileName).append(Constant.ExportConciseDailyReport.LEFT_BRACKET)
                        .append(Constant.ExportConciseDailyReport.VALID_FOR_SEVEN_DAYS)
                        .append(Constant.ExportConciseDailyReport.RIGHT_BRACKET)
                        .append(Constant.ExportConciseDailyReport.EMAIL_PREFIX_A)
                        .append(fileUrl)
                        .append(Constant.ExportConciseDailyReport.EMAIL_COLO)
                        .append(Constant.ExportConciseDailyReport.CLICK_DOWN)
                        .append(Constant.ExportConciseDailyReport.EMAIL_SUFFIX)
                        .append(Constant.STR_WRAP);

                // 发送邮件通知
                emailUtils.sendMail(conciseDailyDTO.getEmpNo(), Constant.ExportConciseDailyReport.EMAIL_TITLE_ZH,  null,
                        stringBuffer.toString(), null);
            } catch (Exception e) {
                // 发送邮件通知
                emailUtils.sendMail(conciseDailyDTO.getEmpNo(), Constant.ExportConciseDailyReport.EMAIL_TITLE_ZH_ERROR, null,
                        getTrace(e),null);
            }finally {
                MESHttpHelper.removeHttpRequestHeader();
            }
        });
    }

    /**
     * 导出数据到文件
     * @param conciseDailyDTO
     * @param sysLookupList
     * @param bigExcelProcesser
     * @param headList
     * @param page
     * @throws Exception
     */
    private void exportDataToFile(ConciseDailyDTO conciseDailyDTO, List<SysLookupTypesDTO> sysLookupList, BigExcelProcesser bigExcelProcesser, List<TaskDailyStatHead> headList, Integer page) throws Exception {
        while (page == 0 || !CollectionUtils.isEmpty(headList)) {
            page += 1;
            conciseDailyDTO.setPage(page);
            // 分页查询
            headList = taskDailyStatHeadService.selectByConciseDailyDTO(conciseDailyDTO);

            if (CollectionUtils.isEmpty(headList)) {
                continue;
            }
            // 转化为头表idList
            List<String> headIdList = headList.stream().map(k -> k.getHeadId()).collect(Collectors.toList());
            // 再查详表
            List<TaskDailyStatDetail> detailList = taskDailyStatDetailService.selectByHeadIds(headIdList);
            // 数据转化为List<Map>
            List<Map<String, Object>> resultMapList = transferToMapList(headList, detailList, sysLookupList);
            this.transferLeadFlag(resultMapList);
            this.setMBomProductCode(resultMapList);
            bigExcelProcesser.createOrAppend(conciseDailyDTO.getHeaderArr(), resultMapList, conciseDailyDTO.getPropArr());
        }
    }

    private void reelTimeExport(ConciseDailyDTO conciseDailyDTO, String[] headArr, List<SysLookupTypesDTO> sysLookupList,
                                String[] propArr, BigExcelProcesser bigExcelProcesser) throws Exception {
        // 根据条件查询批次,进行分页查询
        Page<PsTask> pageTask = new Page<>();
        Integer page = 0;
        conciseDailyDTO.setRow(Constant.INT_500);
        List<Future<List<Map<String, Object>>>> futureList = new ArrayList<>();
        Map<String, String> httpRequestHeader = MESHttpHelper.getHttpRequestHeader();
        while (page == 0 || !CollectionUtils.isEmpty(pageTask.getRows())) {
            page += 1;
            conciseDailyDTO.setPage(page);
            pageTask = PlanscheduleRemoteService.pageSelectForConciseDailyReelTime(conciseDailyDTO);
            List<PsTask> psTaskList = pageTask.getRows();
            if (CollectionUtils.isEmpty(psTaskList)) {
                continue;
            }
            multiThreadExportCore(conciseDailyDTO, sysLookupList, httpRequestHeader, psTaskList, futureList);
        }
        for (Future<List<Map<String, Object>>> future : futureList) {
            List<Map<String, Object>> temp = future.get();
            bigExcelProcesser.createOrAppend(headArr, temp, propArr);
        }
    }

    private void multiThreadExportCore(ConciseDailyDTO conciseDailyDTO, List<SysLookupTypesDTO> sysLookupList, Map<String, String> httpRequestHeader, List<PsTask> psTaskList, List<Future<List<Map<String, Object>>>> futureList) {
        // 深拷贝，避免多线程共同操作同一个对象实例，导致并发错误
        ConciseDailyDTO conciseDailyDTOTemp = JSONObject.parseObject(JSONObject.toJSONString(conciseDailyDTO), ConciseDailyDTO.class);
        Future<List<Map<String, Object>>> submit = ThreadUtil.EXECUTOR.submit(() -> {
            MESHttpHelper.setHttpRequestHeader(httpRequestHeader);
            List<Map<String, Object>> conciseDailyMapList = getConciseDailyMapList(conciseDailyDTOTemp, sysLookupList, psTaskList);
            MESHttpHelper.removeHttpRequestHeader();
            return conciseDailyMapList;
        });
        futureList.add(submit);
    }

    private List<Map<String, Object>> getConciseDailyMapList(ConciseDailyDTO conciseDailyDTO, List<SysLookupTypesDTO> sysLookupList, List<PsTask> psTaskList) throws Exception {
        reelTimeCompute(conciseDailyDTO, psTaskList, sysLookupList);
        // 数据转化为List<Map>
        List<Map<String, Object>> resultMapList = transferToMapList(conciseDailyDTO.getStatHeadList(),
                conciseDailyDTO.getStatDetailList(), sysLookupList);
        this.transferLeadFlag(resultMapList);
        this.setMBomProductCode(resultMapList);
        return resultMapList;
    }

    /**
    * @Description: 需要额外添加头表字段，并且需要将详表字段拆分为4项：昨存，转入，转出，结存
    * @Param: [headers, propList]
    * @return: void
    * @Author: Saber[10307315]
    * @Date: 2022/12/26 下午9:41
    */
    private void handlerExportNameAndProp(ConciseDailyDTO conciseDailyDTO, List<String> headers, List<String> props, List<SysLookupTypesDTO>sysCraftBalanceList) throws Exception {
        // 头表默认全部显示
        List<String> headerList = new ArrayList<>();
        List<String> propList = new ArrayList<>();
        for (int i = 0; i < Constant.CONCISE_DAILY_EXPORT_HEADER.length; i++) {
            headerList.add(Constant.CONCISE_DAILY_EXPORT_HEADER[i]);
            propList.add(Constant.CONCISE_DAILY_EXPORT_PROP[i]);
        }
        for (int i = 0; i < headers.size(); i++) {
            // 转为四项
            // 昨存
            String temp = headers.get(i) + Constant.YESTERDAY_ON_HAND_QTY;
            headerList.add(temp);
            propList.add(props.get(i) + Constant.SUFFIX_A);
            // 转入
            headerList.add(headers.get(i) + Constant.TURN_IN);
            propList.add(props.get(i) + Constant.SUFFIX_B);
            // 转出
            headerList.add(headers.get(i) + Constant.TURN_OUT);
            propList.add(props.get(i) + Constant.SUFFIX_C);
            // 结存
            headerList.add(headers.get(i) + Constant.ON_HAND_QTY);
            propList.add(props.get(i) + Constant.SUFFIX_D);
            // 线体
            headerList.add(headers.get(i) + Constant.LINE_NAME);
            propList.add(props.get(i) + Constant.SUFFIX_E);
        }
        // 添加工艺段结存的导出字段
        handleCraftBalanceExportProp(conciseDailyDTO, sysCraftBalanceList, headerList, propList);

        String[] headerArr = headerList.toArray(new String[headerList.size()]);
        String[] propArr = propList.toArray(new String[propList.size()]);
        conciseDailyDTO.setHeaderArr(headerArr);
        conciseDailyDTO.setPropArr(propArr);
    }

    /**
     * 添加工艺段结存的导出字段--取数据字典286524的配置
     * @param conciseDailyDTO
     * @param sysCraftBalanceList
     * @param headerList
     * @param propList
     */
    private void handleCraftBalanceExportProp(ConciseDailyDTO conciseDailyDTO, List<SysLookupTypesDTO> sysCraftBalanceList, List<String> headerList, List<String> propList) {
        List<String> craftBalancePropList = conciseDailyDTO.getExportCraftPropList();
        // 工艺段-字段名键值对
        Map<String,SysLookupTypesDTO> craftBalanceMap = sysCraftBalanceList.stream().collect(Collectors.toMap(SysLookupTypesDTO::getLookupMeaning, a -> a, (k1, k2) -> k1));
        // 默认所有结存都导出
        if (CollectionUtils.isEmpty(craftBalancePropList)) {
            craftBalancePropList = sysCraftBalanceList.stream().map(SysLookupTypesDTO::getLookupMeaning).distinct().collect(Collectors.toList());
        }
        for (String craftProp : craftBalancePropList) {
            SysLookupTypesDTO propSys = craftBalanceMap.get(craftProp);
            if(null != propSys){
                headerList.add(propSys.getDescriptionChinV());
                propList.add(propSys.getAttribute1());
            }
        }
    }

    @Override
    public void conciseDailyDelete(ConciseDailyDTO conciseDailyDTO) throws Exception {
        taskDailyStatDetailRepository.deleteConciseDaily(conciseDailyDTO);
        taskDailyStatHeadRepository.deleteConciseDailyHead(conciseDailyDTO);
    }

    @Override
    public List<String> querySelectColCache(String empNo, String factoryId) {
        List<String> resultList = new ArrayList<>();
        String redisKey = Constant.CONCISE_DAILY_SELECT_COL_CACHE + Constant.AND + empNo
                + Constant.AND + factoryId;
        // 读取redis缓存
        String value = RedisCacheUtils.get(redisKey, String.class);
        // 区分空串和null
        if (value == null) {
            return null;
        }
        if (value.equals(Constant.STRING_EMPTY)) {
            return resultList;
        }
        resultList = JSONObject.parseObject(value, List.class);
        return resultList;
    }

}
