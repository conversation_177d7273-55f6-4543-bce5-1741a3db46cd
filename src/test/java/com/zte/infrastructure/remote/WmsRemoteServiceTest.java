package com.zte.infrastructure.remote;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zte.common.CommonUtils;
import com.zte.common.ConstantInterface;
import com.zte.interfaces.dto.VMesBoxNoNewDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.JsonConvertUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;

@PrepareForTest({ServiceDataBuilderUtil.class, JsonConvertUtil.class, HttpClientUtil.class, JacksonJsonConverUtil.class,
        MicroServiceRestUtil.class, MESHttpHelper.class, HttpRemoteUtil.class, CommonUtils.class})
public class WmsRemoteServiceTest extends PowerBaseTestCase {
    @InjectMocks
    private WmsRemoteService wmsRemoteService;
    @Mock
    private ConstantInterface constantInterface;
    @Mock
    ObjectMapper mapperInstance;
    @Mock
    JsonNode json;
    @Test
    public void sendToWms() throws Exception {
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class,JsonConvertUtil.class, CommonUtils.class,JacksonJsonConverUtil.class,MESHttpHelper.class, HttpClientUtil.class,HttpRemoteUtil.class);
        List<Map<String,Object>> inforList = new ArrayList<>();
        VMesBoxNoNewDTO vMesBoxNoNewDTO = new VMesBoxNoNewDTO();
        vMesBoxNoNewDTO.setWmsAddress("url");
        try{
            wmsRemoteService.sendToWms(inforList,vMesBoxNoNewDTO);
        }catch (Exception e){
            Assert.assertTrue(inforList.size()>=0);
        }
        Map<String,Object> result = new HashMap<>();
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.anyString(), Mockito.anyString(), Mockito.anyMap())).thenReturn(JSON.toJSONString(new ServiceData(){{
            setCode(new RetCode(RetCode.BUSINESSERROR_CODE, RetCode.BUSINESSERROR_MSGID));
            setBo(result);
        }}));
        PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
        PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json);
        PowerMockito.when(json.get(Mockito.anyString())).thenReturn(json);
        PowerMockito.when(json.asText()).thenReturn("0001");

        inforList.add(new HashMap<>());
        wmsRemoteService.sendToWms(inforList,vMesBoxNoNewDTO);
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(any(), any(), any())).thenReturn("{\n" +
                "    \"infCode\": null,\n" +
                "    \"infDesc\": null,\n" +
                "    \"list\": [],\n" +
                "}");
        PowerMockito.when(CommonUtils.getLmbMessage(Mockito.anyString(),Mockito.anyString())).thenReturn("result");
        try{
            wmsRemoteService.sendToWms(inforList,vMesBoxNoNewDTO);
        }catch (Exception e) {
            Assert.assertTrue(inforList.size()>=0);
        }
        PowerMockito.when(json.asText()).thenReturn("0000");
        try{
            wmsRemoteService.sendToWms(inforList,vMesBoxNoNewDTO);
        }catch (Exception e) {
            Assert.assertTrue(inforList.size()>=0);
        }
        String jsonString = "{\n" +
                "  \"code\": {\n" +
                "    \"code\": \"0000\",\n" +
                "    \"msgId\": \"RetCode.Success\",\n" +
                "    \"msg\": \"操作成功\"\n" +
                "  },\n" +
                "  \"bo\": {\n" +
                "    \"isAllSuccess\": \"true\",\n" +
                "    \"syncFailedIncomingDetails\": [\n" +
                "    \n" +
                "  ]\n" +
                "}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(jsonString);
        PowerMockito.when(JsonConvertUtil.jsonToBean(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(inforList);
        try{
            wmsRemoteService.sendToWms(inforList,vMesBoxNoNewDTO);
        }catch (Exception e) {
            Assert.assertTrue(inforList.size()>=0);
        }
    }
}