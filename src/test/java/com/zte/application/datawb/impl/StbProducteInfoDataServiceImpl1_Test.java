/*Started by AICoder, pid:i3ba2m15892c5c7140a00825313bfe22b8654270*/
package com.zte.application.datawb.impl;
import com.zte.application.datawb.CpeBoxupBillService;
import com.zte.domain.model.datawb.StbProductSnCount;
import com.zte.domain.model.datawb.StbProducteInfoDataRepository;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class StbProducteInfoDataServiceImpl1_Test {

    @Mock
    private StbProducteInfoDataRepository stbProducteInfoDataRepository;

    @Mock
    private CpeBoxupBillService cpeBoxupBillService;

    @InjectMocks
    private StbProducteInfoDataServiceImpl service;

    @Test
    public void testGetDhomeSnListCount_EmptySnStartList() {
        Map<String, Integer> result = service.getDhomeSnListCount(Collections.emptyList(), 1, 0);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetDhomeSnListCount_NullDaysAgoStart() {
        List<String> snList = Arrays.asList("SN001", "SN002");
        when(stbProducteInfoDataRepository.getDhomeSnCount(anyList(), eq(1), eq(0)))
            .thenReturn(Collections.emptyList());
        when(cpeBoxupBillService.getItemBarcodesDaysAgo(eq(1), eq(0)))
            .thenReturn(Collections.emptyList());

        Map<String, Integer> result = service.getDhomeSnListCount(snList, null, 0);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetDhomeSnListCount_RepositoryReturnsEmpty() {
        List<String> snList = Arrays.asList("SN001", "SN002");
        when(stbProducteInfoDataRepository.getDhomeSnCount(anyList(), anyInt(), anyInt()))
            .thenReturn(Collections.emptyList());
        when(cpeBoxupBillService.getItemBarcodesDaysAgo(anyInt(), anyInt()))
            .thenReturn(Arrays.asList("ITEM001"));

        Map<String, Integer> result = service.getDhomeSnListCount(snList, 1, 0);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetDhomeSnListCount_CpeReturnsEmpty() {
        List<String> snList = Arrays.asList("SN001");
        when(stbProducteInfoDataRepository.getDhomeSnCount(anyList(), anyInt(), anyInt()))
            .thenReturn(Arrays.asList(new StbProductSnCount("SN001", 5)));
        when(cpeBoxupBillService.getItemBarcodesDaysAgo(anyInt(), anyInt()))
            .thenReturn(Collections.emptyList());

        Map<String, Integer> result = service.getDhomeSnListCount(snList, 1, 0);
        assertEquals(5, result.get("SN001").intValue());
    }

    @Test
    public void testGetDhomeSnListCount_MergeCounts() {
        List<String> snList = Arrays.asList("SN001");
        when(stbProducteInfoDataRepository.getDhomeSnCount(anyList(), anyInt(), anyInt()))
            .thenReturn(Arrays.asList(new StbProductSnCount("SN001", 5)));
        when(cpeBoxupBillService.getItemBarcodesDaysAgo(anyInt(), anyInt()))
            .thenReturn(Arrays.asList("ITEM001"));
        when(stbProducteInfoDataRepository.getDhomeSnCountByProductSnList(anyList(), anyList()))
            .thenReturn(Arrays.asList(new StbProductSnCount("SN001", 3)));

        Map<String, Integer> result = service.getDhomeSnListCount(snList, 1, 0);
        assertEquals(8, result.get("SN001").intValue());
    }

    @Test
    public void testGetDhomeSnListCount_FilterInvalidSns() {
        List<String> snList = Arrays.asList("", "SN001", "SN001", "SN002");
        when(stbProducteInfoDataRepository.getDhomeSnCount(argThat(list -> list.size() == 2), anyInt(), anyInt()))
            .thenReturn(Arrays.asList(new StbProductSnCount("SN001", 5)));
        when(cpeBoxupBillService.getItemBarcodesDaysAgo(anyInt(), anyInt()))
            .thenReturn(Collections.emptyList());

        Map<String, Integer> result = service.getDhomeSnListCount(snList, 1, 0);
        assertEquals(1, result.size());
        assertEquals(5, result.get("SN001").intValue());
    }

    @Test
    public void testGetDhomeSnListCount_DaysAgoEndIsNull() {
        List<String> snList = Arrays.asList("SN001");
        when(stbProducteInfoDataRepository.getDhomeSnCount(anyList(), eq(1), eq(0)))
            .thenReturn(Collections.emptyList());
        when(cpeBoxupBillService.getItemBarcodesDaysAgo(eq(1), eq(0)))
            .thenReturn(Collections.emptyList());

        Map<String, Integer> result = service.getDhomeSnListCount(snList, 1, null);
        assertTrue(result.isEmpty());
    }
}
/*Ended by AICoder, pid:i3ba2m15892c5c7140a00825313bfe22b8654270*/