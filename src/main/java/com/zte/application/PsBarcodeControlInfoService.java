/**
 * 项目名称 : ${project_name}
 * 创建日期 : ${date}
 * 修改历史 :
 * 1. [${date}] 创建文件 by 10192968
 **/
package com.zte.application;

import java.util.List;
import java.util.Map;

import com.zte.domain.model.PsBarcodeControlInfo;
import com.zte.interfaces.dto.BarcodeDTO;
import com.zte.interfaces.dto.SnCheckControlResultDTO;

/**
 * 条码管控service
 * 
 * <AUTHOR>
 **/
public interface PsBarcodeControlInfoService {

    /**
     * 增加实体数据
     * <AUTHOR>
     * @param record PsBarcodeControlInfo实体
     * @return 插入数量
     **/
    int insertPsBarcodeControlInfo(PsBarcodeControlInfo record);

    /**
     * 有选择性的增加实体数据
     * <AUTHOR>
     * @param record PsBarcodeControlInfo实体
     * @return 插入数量
     **/
    int insertPsBarcodeControlInfoSelective(PsBarcodeControlInfo record);

    /**
     * 根据主键删除实体数据
     * <AUTHOR>
     * @param record PsBarcodeControlInfo实体
     * @return 删除数量
     **/
    int deletePsBarcodeControlInfoById(PsBarcodeControlInfo record);

    /**
     * 有选择性的更新实体数据
     * <AUTHOR>
     * @param record PsBarcodeControlInfo实体
     * @return 更新数量
     **/
    int updatePsBarcodeControlInfoByIdSelective(PsBarcodeControlInfo record);

    /**
     * 根据主键更新实体数据
     * <AUTHOR>
     * @param record PsBarcodeControlInfo实体
     * @return 更新数量
     **/
    int updatePsBarcodeControlInfoById(PsBarcodeControlInfo record);

    /**
     * 根据主键查询实体信息
     * <AUTHOR>
     * @param record PsBarcodeControlInfo实体
     * @return PsBarcodeControlInfo
     **/
    PsBarcodeControlInfo selectPsBarcodeControlInfoById(PsBarcodeControlInfo record);

    /**
     * 根据条码查询管控信息（下线）
     * <AUTHOR>
     * @param record PsBarcodeControlInfo实体list
     * @return PsBarcodeControlInfo
     **/
    List<PsBarcodeControlInfo> selectPsBarcodeControlInfoByBarcode(String barcode);

    /**
     * 根据  批量条码  查询管控信息（下线）,查到有管控的条码就返回，不全部查出来
     * <AUTHOR>
     * @param List<BarcodeDTO> 条码list
     * @return record PsBarcodeControlInfo实体list
     **/
    List<PsBarcodeControlInfo> selectPsBarcodeControlInfoByBarcodeList(List<BarcodeDTO> listBarcode);

    /**
     * 根据条码查询管控信息（下线）(只适用于批量过站扫描)
     * 
     * @param List<BarcodeDTO> list
     * @return List<SnCheckControlResultDTO>
     **/
    List<SnCheckControlResultDTO> selectPsBarcodeControlInfoByBarcodes(List<BarcodeDTO> list,
            String workOrderNo);

    /**
     * getList
     * <AUTHOR>
     * @param record HashMap参数列表
     * @return PsBarcodeControlInfo实体List
     **/
    List<PsBarcodeControlInfo> getList(Map<String, Object> record);

    /**
     * 
     * getCount
     * <AUTHOR>
     * @param record HashMap参数列表
     * @return 数量
     */
    Long getCount(Map<String, Object> record);

    /**
     * 
     * getPage分页查询服务
     * <AUTHOR>
     * @param record HashMap参数列表
     * @return PsBarcodeControlInfo实体list
     */
    List<PsBarcodeControlInfo> getPage(Map<String, Object> record);
}
