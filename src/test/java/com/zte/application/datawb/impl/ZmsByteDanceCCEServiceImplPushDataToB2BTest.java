/*Started by AICoder, pid:v2fc49bbd4395ff1497f0a5d0090865cf4281ea0*/
package com.zte.application.datawb.impl;

import com.alibaba.fastjson.JSON;
import com.zte.application.datawb.WsmAssembleLinesService;
import com.zte.common.CommonUtils;
import com.zte.common.utils.DateUtil;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.interfaces.dto.ZmsByteDanceCCEHeadDTO;
import com.zte.springbootframe.util.json.JacksonJsonConverUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertNotNull;

@RunWith(PowerMockRunner.class)
@PrepareForTest({WsmAssembleLinesService.class, JacksonJsonConverUtil.class,
        JSON.class, CommonUtils.class, DateUtil.class})
@PowerMockIgnore({"javax.net.ssl.*", "javax.security.*"})
public class ZmsByteDanceCCEServiceImplPushDataToB2BTest {
    @InjectMocks
    private ZmsByteDanceCCEServiceImpl zmsByteDanceCCEService;

    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Before
    public void setup() {
        ReflectionTestUtils.setField(zmsByteDanceCCEService, "b2bSize", 100);
        MockitoAnnotations.initMocks(this);
    }

    @Test//(timeout = 8000)
    public void testPushDataToB2B_EmptyList() throws Exception {
        List<ZmsByteDanceCCEHeadDTO> list = new ArrayList<>();
        ZmsByteDanceCCEHeadDTO zmsByteDanceCCEHeadDTO = new ZmsByteDanceCCEHeadDTO();
        zmsByteDanceCCEHeadDTO.setDataDate("11");
        zmsByteDanceCCEHeadDTO.setDataTransferBatchNo("222");
        list.add(zmsByteDanceCCEHeadDTO);
        zmsByteDanceCCEService.pushDataToB2B("123", list);
        assertNotNull(list);
    }
}
/*Ended by AICoder, pid:v2fc49bbd4395ff1497f0a5d0090865cf4281ea0*/