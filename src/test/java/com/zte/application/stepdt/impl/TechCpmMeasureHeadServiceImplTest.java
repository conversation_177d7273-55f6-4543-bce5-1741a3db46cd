package com.zte.application.stepdt.impl;

import com.zte.application.kxbariii.impl.TechCpmMeasureHeadServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.domain.model.kxbariii.TechCpmMeasureHeadRepository;
import com.zte.interfaces.dto.kxbariii.TechCpmMeasureHeadDTO;
import com.zte.interfaces.dto.kxbariii.TechSupcpHeadDTO;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
/* Started by AICoder, pid:c90ac7629771406b8a4531b3fc1ab27a */
/**
 * <AUTHOR>
 * @date 2024-06-18 16:54
 */
public class TechCpmMeasureHeadServiceImplTest extends BaseTestCase {

    @InjectMocks
    private TechCpmMeasureHeadServiceImpl techCpmMeasureHeadService;

    @Mock
    private TechCpmMeasureHeadRepository techCpmMeasureHeadRepository;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testInsertTechCpmMeasureHead_BillNoExists() {
        TechCpmMeasureHeadDTO headDTO = new TechCpmMeasureHeadDTO();
        techCpmMeasureHeadService.insertTechCpmMeasureHead(headDTO);

        PowerMockito.when(techCpmMeasureHeadRepository.queryTechCpmMeasureHeadByBillNo(Mockito.any()))
                .thenReturn(null);
        try {
            techCpmMeasureHeadService.insertTechCpmMeasureHead(headDTO);
        } catch (Exception e) {
            Assert.assertTrue(MessageId.BILL_NO_EXISTS.equals(e.getMessage()));
        }
        PowerMockito.when(techCpmMeasureHeadRepository.queryTechCpmMeasureHeadByBillNo(Mockito.any()))
                .thenReturn(3);
        try {
            techCpmMeasureHeadService.insertTechCpmMeasureHead(headDTO);
        } catch (Exception e) {
            Assert.assertTrue(MessageId.BILL_NO_EXISTS.equals(e.getMessage()));
        }

    }

    @Test
    public void insertTechSupcpHead() {
        TechSupcpHeadDTO headDTO = new TechSupcpHeadDTO();
        techCpmMeasureHeadService.insertTechSupcpHead(headDTO);

        PowerMockito.when(techCpmMeasureHeadRepository.queryTechSupcpHeadByBillNo(Mockito.any()))
                .thenReturn(null);
        try {
            techCpmMeasureHeadService.insertTechSupcpHead(headDTO);
        } catch (Exception e) {
            Assert.assertTrue(MessageId.BILL_NO_EXISTS.equals(e.getMessage()));
        }
        PowerMockito.when(techCpmMeasureHeadRepository.queryTechSupcpHeadByBillNo(Mockito.any()))
                .thenReturn(3);
        try {
            techCpmMeasureHeadService.insertTechSupcpHead(headDTO);
        } catch (Exception e) {
            Assert.assertTrue(MessageId.BILL_NO_EXISTS.equals(e.getMessage()));
        }

    }
    /* Ended by AICoder, pid:c90ac7629771406b8a4531b3fc1ab27a */

}
