/**
 * 项目名称 : ${project_name}
 * 创建日期 : ${date}
 * 修改历史 :
 * 1. [${date}] 创建文件 by 10192968
 **/
package com.zte.domain.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.zte.interfaces.dto.CtRouteDetailDTO;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * // TODO 添加类/接口功能描述
 *
 * <AUTHOR>
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
public class PsWipInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    private BigDecimal isPrint;

    /**
     * // TODO remarks
     **/
    private String wipId;

    /**
     * // TODO remarks
     **/
    private String sn;

    /**
     * // TODO remarks
     **/
    private String workOrderNo;

    private String taskNo;

    /**
     * // TODO remarks
     **/
    private String itemNo;

    /**
     * // TODO remarks
     **/
    private String itemName;

    /**
     * // TODO remarks
     **/
    private String routeId;

    /**
     * // TODO remarks
     **/
    private String currProcessCode;

    /**
     * // TODO remarks
     **/
    private String status;

    /**
     * // TODO remarks
     **/
    private String worker;

    /**
     * // TODO remarks
     **/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date inTime;

    /**
     * // TODO remarks
     **/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date outTime;

    /**
     * // TODO remarks
     **/
    private String parentSn;

    /**
     * // TODO remarks
     **/
    private String errorCode;

    /**
     * // TODO remarks
     **/
    private String lineCode;

    /**
     * // TODO remarks
     **/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date timeSpan;

    /**
     * // TODO remarks
     **/
    private String workshopCode;

    /**
     * // TODO remarks
     **/
    private BigDecimal opeTimes;

    private String craftSection;

    private String workStation;

    /**
     * // TODO remarks
     **/
    private String remark;

    /**
     * // TODO remarks
     **/
    private String createBy;

    /**
     * // TODO remarks
     **/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;

    /**
     * // TODO remarks
     **/
    private String lastUpdatedBy;

    /**
     * // TODO remarks
     **/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastUpdatedDate;

    /**
     * // TODO remarks
     **/
    private String enabledFlag;

    /**
     * // TODO remarks
     **/
    private BigDecimal orgId;

    /**
     * // TODO remarks
     **/
    private BigDecimal factoryId;

    /**
     * // TODO remarks
     **/
    private BigDecimal entityId;

    /**
     * // TODO remarks
     **/
    private String attribute1;

    /**
     * // TODO remarks
     **/
    private String attribute2;

    /**
     * // TODO remarks
     **/
    private String attribute3;

    /**
     * // TODO remarks
     **/
    private String attribute4;

    /**
     * // TODO remarks
     **/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date attribute5;

    private String lastProcess;

    private String sourceSys;

    private String nextProcess;

    private long total;

    private BigDecimal sourceImu;

    private BigDecimal sourceBimu;

    private String colName;

    private String sourceSysName;
    /**
     * 工序
     **/
    private String processCode;
    /**
     * 测试描述
     **/
    private String testDescription;
    /**
     * 测试
     **/
    private String lastTest;
    /**
     * 测试值
     **/
    private String testValue;
    /**
     * 故障现象
     **/
    private String errorDescription;
    /**
     * 来源
     */
    private String fromStation;
    /**
     * 校验字段
     */
    private String valiedMsg;

    private String processName;

    private String lpn;

    public String inAttribute1;

    private String barcodeNumber;

    private BigDecimal qty;

    private List<String> inProcessGroup;

    private List<String> inCraftSection;

    private List<String> notInProcessList;

    public List<String> getNotInProcessList() {
        return notInProcessList;
    }

    public void setNotInProcessList(List<String> notInProcessList) {
        this.notInProcessList = notInProcessList;
    }

    //是否清掉指令编号、线体、车间
    private String isClearWlw;
    //原任务
    private String originalTask;

    private String assembleFlag;

    //二次装配标识
    private String secAssembleFlag;

	/**
	 * 环保属性
	 */
	private String isLead;

    private String mappingProcess;

    private List<CtRouteDetailDTO> ctRouteDetailDTOList;

    public List<CtRouteDetailDTO> getCtRouteDetailDTOList() {
        return ctRouteDetailDTOList;
    }

    public void setCtRouteDetailDTOList(List<CtRouteDetailDTO> ctRouteDetailDTOList) {
        this.ctRouteDetailDTOList = ctRouteDetailDTOList;
    }

    public String getMappingProcess() {
        return mappingProcess;
    }

    public void setMappingProcess(String mappingProcess) {
        this.mappingProcess = mappingProcess;
    }

    public String getSecAssembleFlag() {
        return secAssembleFlag;
    }

    public void setSecAssembleFlag(String secAssembleFlag) {
        this.secAssembleFlag = secAssembleFlag;
    }

    public String getOriginalTask() {
        return originalTask;
    }

    public void setOriginalTask(String originalTask) {
        this.originalTask = originalTask;
    }

    public String getDipFinishFlag() {
        return dipFinishFlag;
    }

    public void setDipFinishFlag(String dipFinishFlag) {
        this.dipFinishFlag = dipFinishFlag;
    }

    private String dipFinishFlag;

    public String getLpn() {
        return lpn;
    }

    public void setLpn(String lpn) {
        this.lpn = lpn;
    }

    public String getProcessName() {

        return processName;
    }

    public void setProcessName(String processName) {

        this.processName = processName;
    }

    /**
     * 维修详情记录
     */
    private List<PmRepairInfo> pmRepairInfos;

    private String itemCode;

    public String getItemCode() {

        return itemCode;
    }

    public void setItemCode(String itemCode) {

        this.itemCode = itemCode;
    }

    public List<PmRepairInfo> getPmRepairInfos() {

        return pmRepairInfos;
    }

    public void setPmRepairInfos(List<PmRepairInfo> pmRepairInfos) {

        this.pmRepairInfos = pmRepairInfos;
    }

    public String getErrorDescription() {

        return errorDescription;
    }

    public void setErrorDescription(String errorDescription) {

        this.errorDescription = errorDescription;
    }

    public String getFromStation() {

        return fromStation;
    }

    public void setFromStation(String fromStation) {

        this.fromStation = fromStation;
    }

    public String getValiedMsg() {

        return valiedMsg;
    }

    public void setValiedMsg(String valiedMsg) {

        this.valiedMsg = valiedMsg;
    }

    public String getProcessCode() {

        return processCode;
    }

    public void setProcessCode(String processCode) {

        this.processCode = processCode;
    }

    public String getTestDescription() {

        return testDescription;
    }

    public void setTestDescription(String testDescription) {

        this.testDescription = testDescription;
    }

    public String getLastTest() {

        return lastTest;
    }

    public void setLastTest(String lastTest) {

        this.lastTest = lastTest;
    }

    public String getTestValue() {

        return testValue;
    }

    public void setTestValue(String testValue) {

        this.testValue = testValue;
    }

    @ApiModelProperty(value = "当前子工序名称")
    private String currProcessName;


    public String getCurrProcessName() {
        return currProcessName;
    }

    public void setCurrProcessName(String currProcessName) {
        this.currProcessName = currProcessName;
    }

    public BigDecimal getSourceImu() {
        return sourceImu;
    }

    public void setSourceImu(BigDecimal sourceImu) {
        this.sourceImu = sourceImu;
    }

    public BigDecimal getSourceBimu() {
        return sourceBimu;
    }

    public void setSourceBimu(BigDecimal sourceBimu) {
        this.sourceBimu = sourceBimu;
    }

    public String getColName() {
        return colName;
    }

    public void setColName(String colName) {
        this.colName = colName;
    }

    public String getSourceSysName() {
        return sourceSysName;
    }

    public void setSourceSysName(String sourceSysName) {
        this.sourceSysName = sourceSysName;
    }

    public long getTotal() {
        return total;
    }

    public void setTotal(long total) {
        this.total = total;
    }

    public void setLastProcess(String lastProcess) {

        this.lastProcess = lastProcess;
    }

    public String getLastProcess() {

        return lastProcess;
    }

    public void setWipId(String wipId) {

        this.wipId = wipId;
    }

    public String getWipId() {

        return wipId;
    }

    public void setSn(String sn) {

        this.sn = sn;
    }

    public String getSn() {

        return sn;
    }

    public void setWorkOrderNo(String workOrderNo) {

        this.workOrderNo = workOrderNo;
    }

    public String getWorkOrderNo() {

        return workOrderNo;
    }

    public void setItemNo(String itemNo) {

        this.itemNo = itemNo;
    }

    public String getItemNo() {

        return itemNo;
    }

    public void setItemName(String itemName) {

        this.itemName = itemName;
    }

    public String getItemName() {

        return itemName;
    }

    public void setRouteId(String routeId) {

        this.routeId = routeId;
    }

    public String getRouteId() {

        return routeId;
    }

    public void setCurrProcessCode(String currProcessCode) {

        this.currProcessCode = currProcessCode;
    }

    public String getCurrProcessCode() {

        return currProcessCode;
    }

    public void setStatus(String status) {

        this.status = status;
    }

    public String getStatus() {

        return status;
    }

    public void setWorker(String worker) {

        this.worker = worker;
    }

    public String getWorker() {

        return worker;
    }

    public void setInTime(Date inTime) {

        this.inTime = inTime;
    }

    public Date getInTime() {

        return inTime;
    }

    public void setOutTime(Date outTime) {

        this.outTime = outTime;
    }

    public Date getOutTime() {

        return outTime;
    }

    public void setParentSn(String parentSn) {

        this.parentSn = parentSn;
    }

    public String getParentSn() {

        return parentSn;
    }

    public void setErrorCode(String errorCode) {

        this.errorCode = errorCode;
    }

    public String getErrorCode() {

        return errorCode;
    }

    public void setLineCode(String lineCode) {

        this.lineCode = lineCode;
    }

    public String getLineCode() {

        return lineCode;
    }

    public void setTimeSpan(Date timeSpan) {

        this.timeSpan = timeSpan;
    }

    public Date getTimeSpan() {

        return timeSpan;
    }

    public void setWorkshopCode(String workshopCode) {

        this.workshopCode = workshopCode;
    }

    public String getWorkshopCode() {

        return workshopCode;
    }

    public void setOpeTimes(BigDecimal opeTimes) {

        this.opeTimes = opeTimes;
    }

    public BigDecimal getOpeTimes() {

        return opeTimes;
    }

    public String getCraftSection() {

        return craftSection;
    }

    public void setCraftSection(String craftSection) {

        this.craftSection = craftSection;
    }

    public String getWorkStation() {

        return workStation;
    }

    public void setWorkStation(String workStation) {

        this.workStation = workStation;
    }

    public void setRemark(String remark) {

        this.remark = remark;
    }

    public String getRemark() {

        return remark;
    }

    public void setCreateBy(String createBy) {

        this.createBy = createBy;
    }

    public String getCreateBy() {

        return createBy;
    }

    public void setCreateDate(Date createDate) {

        this.createDate = createDate;
    }

    public Date getCreateDate() {

        return createDate;
    }

    public void setLastUpdatedBy(String lastUpdatedBy) {

        this.lastUpdatedBy = lastUpdatedBy;
    }

    public String getLastUpdatedBy() {

        return lastUpdatedBy;
    }

    public void setLastUpdatedDate(Date lastUpdatedDate) {

        this.lastUpdatedDate = lastUpdatedDate;
    }

    public Date getLastUpdatedDate() {

        return lastUpdatedDate;
    }

    public void setEnabledFlag(String enabledFlag) {

        this.enabledFlag = enabledFlag;
    }

    public String getEnabledFlag() {

        return enabledFlag;
    }

    public void setOrgId(BigDecimal orgId) {

        this.orgId = orgId;
    }

    public BigDecimal getOrgId() {

        return orgId;
    }

    public void setFactoryId(BigDecimal factoryId) {

        this.factoryId = factoryId;
    }

    public BigDecimal getFactoryId() {

        return factoryId;
    }

    public void setEntityId(BigDecimal entityId) {

        this.entityId = entityId;
    }

    public BigDecimal getEntityId() {

        return entityId;
    }

    public void setAttribute1(String attribute1) {

        this.attribute1 = attribute1;
    }

    public String getAttribute1() {

        return attribute1;
    }

    public void setAttribute2(String attribute2) {

        this.attribute2 = attribute2;
    }

    public String getAttribute2() {

        return attribute2;
    }

    public void setAttribute3(String attribute3) {

        this.attribute3 = attribute3;
    }

    public String getAttribute3() {

        return attribute3;
    }

    public void setAttribute4(String attribute4) {

        this.attribute4 = attribute4;
    }

    public String getAttribute4() {

        return attribute4;
    }

    public void setAttribute5(Date attribute5) {

        this.attribute5 = attribute5;
    }

    public Date getAttribute5() {

        return attribute5;
    }

    public String getSourceSys() {
        return sourceSys;
    }

    public void setSourceSys(String sourceSys) {
        this.sourceSys = sourceSys;
    }

    public String getNextProcess() {
        return nextProcess;
    }

    public void setNextProcess(String nextProcess) {
        this.nextProcess = nextProcess;
    }

    public BigDecimal getIsPrint() {
        return isPrint;
    }

    public void setIsPrint(BigDecimal isPrint) {
        this.isPrint = isPrint;
    }

    private String fixId;
    private String stationName;

    public String getFixId() {
        return fixId;
    }

    public void setFixId(String fixId) {
        this.fixId = fixId;
    }

    public String getStationName() {
        return stationName;
    }

    public void setStationName(String stationName) {
        this.stationName = stationName;
    }

    public String getTaskNo() {
        return taskNo;
    }

    public void setTaskNo(String taskNo) {
        this.taskNo = taskNo;
    }

    public String getInAttribute1() {
        return inAttribute1;
    }

    public void setInAttribute1(String inAttribute1) {
        this.inAttribute1 = inAttribute1;
    }

    public String getBarcodeNumber() {
        return barcodeNumber;
    }

    public void setBarcodeNumber(String barcodeNumber) {
        this.barcodeNumber = barcodeNumber;
    }

    public BigDecimal getQty() {
        return qty;
    }

    public void setQty(BigDecimal qty) {
        this.qty = qty;
    }

    public List<String> getInProcessGroup() {
        return inProcessGroup;
    }

    public void setInProcessGroup(List<String> inProcessGroup) {
        this.inProcessGroup = inProcessGroup;
    }

    public List<String> getInCraftSection() {
        return inCraftSection;
    }

    public void setInCraftSection(List<String> inCraftSection) {
        this.inCraftSection = inCraftSection;
    }

    public String getIsClearWlw() {
        return isClearWlw;
    }

    public void setIsClearWlw(String isClearWlw) {
        this.isClearWlw = isClearWlw;
    }

    public String getAssembleFlag() {
        return assembleFlag;
    }

    public void setAssembleFlag(String assembleFlag) {
        this.assembleFlag = assembleFlag;
    }

	public String getIsLead() {
		return isLead;
	}

	public void setIsLead(String isLead) {
		this.isLead = isLead;
	}
}
