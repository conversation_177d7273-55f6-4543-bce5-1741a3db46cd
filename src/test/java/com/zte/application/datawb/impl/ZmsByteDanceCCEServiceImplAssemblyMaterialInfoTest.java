/*Started by AICoder, pid:16e7fx995bvd44a1479c0b38d0b3ea847dd72937*/
package com.zte.application.datawb.impl;

import com.alibaba.fastjson.JSON;
import com.zte.application.datawb.WsmAssembleLinesService;
import com.zte.common.CommonUtils;
import com.zte.common.utils.DateUtil;
import com.zte.interfaces.dto.ZmsByteDanceCCEHeadDTO;
import com.zte.interfaces.dto.ZmsCbomInfoDTO;
import com.zte.interfaces.dto.ZmsExtendedAttributeDTO;
import com.zte.interfaces.dto.ZmsInputDTO;
import com.zte.springbootframe.util.json.JacksonJsonConverUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;

@RunWith(PowerMockRunner.class)
@PrepareForTest({WsmAssembleLinesService.class, JacksonJsonConverUtil.class,
        JSON.class, CommonUtils.class, DateUtil.class})
@PowerMockIgnore({"javax.net.ssl.*", "javax.security.*"})
public class ZmsByteDanceCCEServiceImplAssemblyMaterialInfoTest {
    @InjectMocks
    private ZmsByteDanceCCEServiceImpl zmsByteDanceCCEService;
    private ZmsByteDanceCCEHeadDTO zmsByteDanceCCEHeadDTO;
    private ZmsCbomInfoDTO zmsCbomInfoDTO;
    private ZmsInputDTO dto;

    @Before
    public void setUp() {
        zmsByteDanceCCEService = new ZmsByteDanceCCEServiceImpl();
        zmsByteDanceCCEHeadDTO = new ZmsByteDanceCCEHeadDTO();
        zmsCbomInfoDTO = new ZmsCbomInfoDTO();
        dto = new ZmsInputDTO();
    }

    @Test(timeout = 8000)
    public void testAssemblyMaterialInfoEmptyExtendedAttributes() {
        zmsCbomInfoDTO.setExtendedAttributes(new ArrayList<>());
        zmsByteDanceCCEService.assemblyMaterialInfo(zmsByteDanceCCEHeadDTO, zmsCbomInfoDTO, dto);
        assertNull(zmsByteDanceCCEHeadDTO.getMaterialCode());
        assertNull(zmsByteDanceCCEHeadDTO.getMaterialDesc());
    }

    @Test(timeout = 8000)
    public void testAssemblyMaterialInfoWithMaterialCode() {
        List<ZmsExtendedAttributeDTO> attributes = new ArrayList<>();
        ZmsExtendedAttributeDTO materialCodeAttr = new ZmsExtendedAttributeDTO();
        materialCodeAttr.setCbomExtentionZh("Material Code");
        materialCodeAttr.setCbomExtentionValue("Code123");
        attributes.add(materialCodeAttr);
        zmsCbomInfoDTO.setExtendedAttributes(attributes);
        dto.setMaterialCode("Material Code");
        dto.setMaterialDesc("Material Desc");
        zmsByteDanceCCEService.assemblyMaterialInfo(zmsByteDanceCCEHeadDTO, zmsCbomInfoDTO, dto);
        assertEquals("Code123", zmsByteDanceCCEHeadDTO.getMaterialCode());
    }

    @Test(timeout = 8000)
    public void testAssemblyMaterialInfoWithMaterialDesc() {
        List<ZmsExtendedAttributeDTO> attributes = new ArrayList<>();
        ZmsExtendedAttributeDTO materialDescAttr = new ZmsExtendedAttributeDTO();
        materialDescAttr.setCbomExtentionZh("Material Desc");
        materialDescAttr.setCbomExtentionValue("Desc123");
        attributes.add(materialDescAttr);
        zmsCbomInfoDTO.setExtendedAttributes(attributes);
        dto.setMaterialCode("Material Code");
        dto.setMaterialDesc("Material Desc");
        zmsByteDanceCCEService.assemblyMaterialInfo(zmsByteDanceCCEHeadDTO, zmsCbomInfoDTO, dto);
        assertEquals("Desc123", zmsByteDanceCCEHeadDTO.getMaterialDesc());
    }
}
/*Ended by AICoder, pid:16e7fx995bvd44a1479c0b38d0b3ea847dd72937*/