/*Started by AICoder, pid:v7afbj7b289fcb4143b108ea5070ad8a41f39463*/
package com.zte.application.datawb.impl;

import com.alibaba.fastjson.JSON;
import com.zte.application.datawb.WsmAssembleLinesService;
import com.zte.common.CommonUtils;
import com.zte.common.utils.Constant;
import com.zte.common.utils.DateUtil;
import com.zte.domain.model.SysLookupValues;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.interfaces.dto.ZmsByteDanceMRSelectDTO;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.util.json.JacksonJsonConverUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertNotNull;

@RunWith(PowerMockRunner.class)
@PrepareForTest({CenterfactoryRemoteService.class,
        WsmAssembleLinesService.class, JacksonJsonConverUtil.class,
        JSON.class, CommonUtils.class, DateUtil.class})
@PowerMockIgnore({"javax.net.ssl.*", "javax.security.*"})
public class ZmsByteDanceMRServiceImpl_getSelectLookupMeanList_3_Test {
    @InjectMocks
    private ZmsByteDanceMRServiceImpl zmsByteDanceMRService;

    @Mock
    private WsmAssembleLinesService wsmAssembleLinesService;

    @Test(expected = MesBusinessException.class)
    public void testGetSelectLookupMeanList_EmptyStatusNameList() {
        ZmsByteDanceMRSelectDTO dto = new ZmsByteDanceMRSelectDTO();
        List<SysLookupValues> emptyList = new ArrayList<>();
        PowerMockito.when(wsmAssembleLinesService.getSysLookupValues(Constant.LOOKUP_TYPE_8240043)).thenReturn(emptyList);
        PowerMockito.when(wsmAssembleLinesService.getSysLookupValues(Constant.LOOKUP_TYPES_8240059)).thenReturn(listWithItems());
        PowerMockito.when(wsmAssembleLinesService.getSysLookupValues(Constant.LOOKUP_TYPE_3020035)).thenReturn(listWithItems());
        zmsByteDanceMRService.getSelectLookupMeanList(dto);
    }

    @Test(expected = MesBusinessException.class)
    public void testGetSelectLookupMeanList_EmptyEntityLikeList() {
        ZmsByteDanceMRSelectDTO dto = new ZmsByteDanceMRSelectDTO();
        PowerMockito.when(wsmAssembleLinesService.getSysLookupValues(Constant.LOOKUP_TYPE_8240043)).thenReturn(listWithItems());
        List<SysLookupValues> emptyList = new ArrayList<>();
        PowerMockito.when(wsmAssembleLinesService.getSysLookupValues(Constant.LOOKUP_TYPES_8240059)).thenReturn(emptyList);
        zmsByteDanceMRService.getSelectLookupMeanList(dto);
        PowerMockito.when(wsmAssembleLinesService.getSysLookupValues(Constant.LOOKUP_TYPE_3020035)).thenReturn(emptyList);
        zmsByteDanceMRService.getSelectLookupMeanList(dto);
    }

    @Test
    public void testGetSelectLookupMeanList_ValidStatusNameAndEntityLikeList() {
        ZmsByteDanceMRSelectDTO dto = new ZmsByteDanceMRSelectDTO();
        PowerMockito.when(wsmAssembleLinesService.getSysLookupValues(Constant.LOOKUP_TYPE_8240043)).thenReturn(listWithItems());
        PowerMockito.when(wsmAssembleLinesService.getSysLookupValues(Constant.LOOKUP_TYPES_8240059)).thenReturn(listWithItems());
        PowerMockito.when(wsmAssembleLinesService.getSysLookupValues(Constant.LOOKUP_TYPE_3020035)).thenReturn(listWithItems());
        zmsByteDanceMRService.getSelectLookupMeanList(dto);
        assertNotNull(dto.getStatusNameIn());

        PowerMockito.when(wsmAssembleLinesService.getSysLookupValues(Constant.LOOKUP_TYPE_8240043)).thenReturn(listWithItems());
        PowerMockito.when(wsmAssembleLinesService.getSysLookupValues(Constant.LOOKUP_TYPES_8240059)).thenReturn(listWithItems());
        PowerMockito.when(wsmAssembleLinesService.getSysLookupValues(Constant.LOOKUP_TYPE_3020035)).thenReturn(new ArrayList<>());
        zmsByteDanceMRService.getSelectLookupMeanList(dto);
        assertNotNull(dto.getStatusNameIn());
    }

    private List<SysLookupValues> listWithItems() {
        List<SysLookupValues> list = new ArrayList<>();
        SysLookupValues item1 = new SysLookupValues();
        item1.setDescription("Description1");
        item1.setLookupMeaning("111");
        item1.setLookupCode(new BigDecimal("824005900001"));
        list.add(item1);
        SysLookupValues item2 = new SysLookupValues();
        item2.setDescription("Description2");
        item2.setLookupMeaning("222");
        item2.setLookupCode(new BigDecimal("824005900002"));
        list.add(item2);
        SysLookupValues item3 = new SysLookupValues();
        item3.setDescription("Description3");
        item3.setLookupMeaning("222");
        item3.setLookupCode(new BigDecimal("111"));
        list.add(item3);
        return list;
    }
}
/*Ended by AICoder, pid:v7afbj7b289fcb4143b108ea5070ad8a41f39463*/