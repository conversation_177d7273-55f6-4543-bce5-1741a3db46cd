<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.WipRepairSnRepository">
  <resultMap id="BaseResultMap" type="com.zte.domain.model.WipRepairSn">
    <id column="REPAIR_SN_ID" jdbcType="VARCHAR" property="repairSnId" />
    <result column="SN" jdbcType="VARCHAR" property="sn" />
    <result column="INPUT_FLAG" jdbcType="VARCHAR" property="inputFlag" />
    <result column="PROCESS_CODE" jdbcType="VARCHAR" property="processCode" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
    <result column="LAST_UPDATED_BY" jdbcType="VARCHAR" property="lastUpdatedBy" />
    <result column="LAST_UPDATED_DATE" jdbcType="TIMESTAMP" property="lastUpdatedDate" />
    <result column="ENABLED_FLAG" jdbcType="VARCHAR" property="enabledFlag" />
    <result column="ORG_ID" jdbcType="DECIMAL" property="orgId" />
    <result column="FACTORY_ID" jdbcType="DECIMAL" property="factoryId" />
    <result column="ENTITY_ID" jdbcType="DECIMAL" property="entityId" />
    <result column="ATTRIBUTE1" jdbcType="VARCHAR" property="attribute1" />
    <result column="ATTRIBUTE2" jdbcType="VARCHAR" property="attribute2" />
    <result column="ATTRIBUTE3" jdbcType="VARCHAR" property="attribute3" />
    <result column="ATTRIBUTE4" jdbcType="VARCHAR" property="attribute4" />
    <result column="ATTRIBUTE5" jdbcType="TIMESTAMP" property="attribute5" />
  </resultMap>

  <sql id="Base_Column_List">
    REPAIR_SN_ID, SN, INPUT_FLAG, PROCESS_CODE, REMARK, CREATE_BY, CREATE_DATE, LAST_UPDATED_BY, 
    LAST_UPDATED_DATE, ENABLED_FLAG, ORG_ID, FACTORY_ID, ENTITY_ID, ATTRIBUTE1, ATTRIBUTE2, 
    ATTRIBUTE3, ATTRIBUTE4, ATTRIBUTE5
  </sql>


  <select id="selectWipRepairSnById" parameterType="com.zte.domain.model.WipRepairSn" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from WIP_REPAIR_SN
    where REPAIR_SN_ID = #{repairSnId,jdbcType=VARCHAR}
      and ENABLED_FLAG = 'Y' 
  </select>

  <delete id="deleteWipRepairSnById" parameterType="com.zte.domain.model.WipRepairSn">
   update WIP_REPAIR_SN
      set ENABLED_FLAG = 'N'
    where REPAIR_SN_ID = #{repairSnId,jdbcType=VARCHAR}
  </delete>

  <insert id="insertWipRepairSn" parameterType="com.zte.domain.model.WipRepairSn">
    insert into WIP_REPAIR_SN (REPAIR_SN_ID, SN, INPUT_FLAG, 
      PROCESS_CODE, REMARK, CREATE_BY, 
      CREATE_DATE, LAST_UPDATED_BY, LAST_UPDATED_DATE, 
      ENABLED_FLAG, ORG_ID, FACTORY_ID, 
      ENTITY_ID, ATTRIBUTE1, ATTRIBUTE2, 
      ATTRIBUTE3, ATTRIBUTE4, ATTRIBUTE5
      )
    values (#{repairSnId,jdbcType=VARCHAR}, #{sn,jdbcType=VARCHAR}, #{inputFlag,jdbcType=VARCHAR}, 
      #{processCode,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{createBy,jdbcType=VARCHAR}, 
      sysdate, #{lastUpdatedBy,jdbcType=VARCHAR},sysdate,
      'Y', #{orgId,jdbcType=DECIMAL}, #{factoryId,jdbcType=DECIMAL}, 
      #{entityId,jdbcType=DECIMAL}, #{attribute1,jdbcType=VARCHAR}, #{attribute2,jdbcType=VARCHAR}, 
      #{attribute3,jdbcType=VARCHAR}, #{attribute4,jdbcType=VARCHAR}, #{attribute5,jdbcType=TIMESTAMP}
      )
  </insert>

  <insert id="insertWipRepairSnSelective" parameterType="com.zte.domain.model.WipRepairSn">
    insert into WIP_REPAIR_SN
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="repairSnId != null">
        REPAIR_SN_ID,
      </if>

      <if test="sn != null">
        SN,
      </if>

      <if test="inputFlag != null">
        INPUT_FLAG,
      </if>

      <if test="processCode != null">
        PROCESS_CODE,
      </if>

      <if test="remark != null">
        REMARK,
      </if>

      <if test="createBy != null">
        CREATE_BY,
      </if>

      <if test="lastUpdatedBy != null">
        LAST_UPDATED_BY,
      </if>

      <if test="orgId != null">
        ORG_ID,
      </if>

      <if test="factoryId != null">
        FACTORY_ID,
      </if>

      <if test="entityId != null">
        ENTITY_ID,
      </if>

      <if test="attribute1 != null">
        ATTRIBUTE1,
      </if>

      <if test="attribute2 != null">
        ATTRIBUTE2,
      </if>

      <if test="attribute3 != null">
        ATTRIBUTE3,
      </if>

      <if test="attribute4 != null">
        ATTRIBUTE4,
      </if>

      <if test="attribute5 != null">
        ATTRIBUTE5,
      </if>
      
      ENABLED_FLAG,
      CREATE_DATE,
      LAST_UPDATED_DATE

    </trim>

    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="repairSnId != null">
        #{repairSnId,jdbcType=VARCHAR},
      </if>

      <if test="sn != null">
        #{sn,jdbcType=VARCHAR},
      </if>

      <if test="inputFlag != null">
        #{inputFlag,jdbcType=VARCHAR},
      </if>

      <if test="processCode != null">
        #{processCode,jdbcType=VARCHAR},
      </if>

      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>

      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>

      <if test="lastUpdatedBy != null">
        #{lastUpdatedBy,jdbcType=VARCHAR},
      </if>

      <if test="orgId != null">
        #{orgId,jdbcType=DECIMAL},
      </if>

      <if test="factoryId != null">
        #{factoryId,jdbcType=DECIMAL},
      </if>

      <if test="entityId != null">
        #{entityId,jdbcType=DECIMAL},
      </if>

      <if test="attribute1 != null">
        #{attribute1,jdbcType=VARCHAR},
      </if>

      <if test="attribute2 != null">
        #{attribute2,jdbcType=VARCHAR},
      </if>

      <if test="attribute3 != null">
        #{attribute3,jdbcType=VARCHAR},
      </if>

      <if test="attribute4 != null">
        #{attribute4,jdbcType=VARCHAR},
      </if>

      <if test="attribute5 != null">
        #{attribute5,jdbcType=TIMESTAMP},
      </if>
      
      'Y',
      sysdate,
      sysdate

    </trim>

  </insert>

  <update id="updateWipRepairSnByIdSelective" parameterType="com.zte.domain.model.WipRepairSn">
    update WIP_REPAIR_SN
    <set>
      <if test="sn != null">
        SN = #{sn,jdbcType=VARCHAR},
      </if>

      <if test="inputFlag != null">
        INPUT_FLAG = #{inputFlag,jdbcType=VARCHAR},
      </if>

      <if test="processCode != null">
        PROCESS_CODE = #{processCode,jdbcType=VARCHAR},
      </if>

      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>

      <if test="lastUpdatedBy != null">
        LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR},
      </if>

      <if test="attribute1 != null">
        ATTRIBUTE1 = #{attribute1,jdbcType=VARCHAR},
      </if>

      <if test="attribute2 != null">
        ATTRIBUTE2 = #{attribute2,jdbcType=VARCHAR},
      </if>

      <if test="attribute3 != null">
        ATTRIBUTE3 = #{attribute3,jdbcType=VARCHAR},
      </if>

      <if test="attribute4 != null">
        ATTRIBUTE4 = #{attribute4,jdbcType=VARCHAR},
      </if>

      <if test="attribute5 != null">
        ATTRIBUTE5 = #{attribute5,jdbcType=TIMESTAMP},
      </if>
      
      LAST_UPDATED_DATE =sysdate

    </set>

    where REPAIR_SN_ID = #{repairSnId,jdbcType=VARCHAR}
  </update>
  
  <update id="updateWipRepairSnBySnSelective" parameterType="com.zte.domain.model.WipRepairSn">
    update WIP_REPAIR_SN
    <set>
      <if test="inputFlag != null">
        INPUT_FLAG = #{inputFlag,jdbcType=VARCHAR},
      </if>

      <if test="processCode != null">
        PROCESS_CODE = #{processCode,jdbcType=VARCHAR},
      </if>

      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>

      <if test="lastUpdatedBy != null">
        LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR},
      </if>

      <if test="attribute1 != null">
        ATTRIBUTE1 = #{attribute1,jdbcType=VARCHAR},
      </if>

      <if test="attribute2 != null">
        ATTRIBUTE2 = #{attribute2,jdbcType=VARCHAR},
      </if>

      <if test="attribute3 != null">
        ATTRIBUTE3 = #{attribute3,jdbcType=VARCHAR},
      </if>

      <if test="attribute4 != null">
        ATTRIBUTE4 = #{attribute4,jdbcType=VARCHAR},
      </if>

      <if test="attribute5 != null">
        ATTRIBUTE5 = #{attribute5,jdbcType=TIMESTAMP},
      </if>
      
      LAST_UPDATED_DATE =sysdate

    </set>

    where SN = #{sn,jdbcType=VARCHAR}
      and ENABLED_FLAG = 'Y'
  </update>

  <update id="updateWipRepairSnById" parameterType="com.zte.domain.model.WipRepairSn">
    update WIP_REPAIR_SN
    set SN = #{sn,jdbcType=VARCHAR},
      INPUT_FLAG = #{inputFlag,jdbcType=VARCHAR},
      PROCESS_CODE = #{processCode,jdbcType=VARCHAR},
      REMARK = #{remark,jdbcType=VARCHAR},
      LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR},
      LAST_UPDATED_DATE =sysdate,
      ATTRIBUTE1 = #{attribute1,jdbcType=VARCHAR},
      ATTRIBUTE2 = #{attribute2,jdbcType=VARCHAR},
      ATTRIBUTE3 = #{attribute3,jdbcType=VARCHAR},
      ATTRIBUTE4 = #{attribute4,jdbcType=VARCHAR},
      ATTRIBUTE5 = #{attribute5,jdbcType=TIMESTAMP}
    where REPAIR_SN_ID = #{repairSnId,jdbcType=VARCHAR}
  </update>
  
  <!-- 获取符合条件的记录列表 -->
  <select id="getList" parameterType="java.util.Map" resultMap="BaseResultMap">
    select * from WIP_REPAIR_SN t
    where 1=1
    and t.ENABLED_FLAG = 'Y'
	<if test="repairSnId != null and repairSnId != ''"> and t.REPAIR_SN_ID = #{repairSnId}</if>
	<if test="sn != null and sn != ''"> and t.SN = #{sn}</if>
	<if test="inputFlag != null and inputFlag != ''"> and t.INPUT_FLAG = #{inputFlag}</if>
	<if test="processCode != null and processCode != ''"> and t.PROCESS_CODE = #{processCode}</if>
    <if test="orgId != null"> and t.ORG_ID = cast(#{orgId} as numeric)</if>
    <if test=" orgId == null and (repairSnId == null or repairSnId == '' ) and (sn == null or sn == '' ) and (inputFlag == null or inputFlag == '' ) and  (processCode == null or processCode == '' )">
      and 1=2
    </if>

    <if test="orderField != null">
    <choose>
        <when test="orderField=='inputFlag'"> order by t.INPUT_FLAG <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="orderField=='sn'"> order by t.SN <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="orderField=='processCode'"> order by t.PROCESS_CODE <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="orderField=='createBy'"> order by t.CREATE_BY <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="orderField=='createDate'"> order by t.CREATE_DATE <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="orderField=='lastUpdatedBy'"> order by t.LAST_UPDATED_BY <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="orderField=='lastUpdatedDate'"> order by t.LAST_UPDATED_DATE <if test="order != null and order == 'desc'"> desc </if> </when>
    </choose>
	</if>
  </select>
  
  <!-- 翻页函数:获取符合条件的记录数 -->
  <select id="getCount" parameterType="java.util.Map" resultType="java.lang.Long">
      select count(*)
      from WIP_REPAIR_SN t
     where 1=1
       and t.ENABLED_FLAG = 'Y'
	<if test="repairSnId != null and repairSnId != ''"> and t.REPAIR_SN_ID = #{repairSnId}</if>
	<if test="sn != null and sn != ''"> and t.SN = #{sn}</if>
	<if test="inputFlag != null and inputFlag != ''"> and t.INPUT_FLAG = #{inputFlag}</if>
	<if test="processCode != null and processCode != ''"> and t.PROCESS_CODE = #{processCode}</if>
	<if test="orgId != null"> and t.ORG_ID = cast(#{orgId} as numeric)</if>
	<if test="factoryId != null"> and t.FACTORY_ID = cast(#{factoryId} as numeric)</if>
	<if test="entityId != null"> and t.ENTITY_ID = cast(#{entityId} as numeric)</if>
  </select>
	
  <!-- 翻页函数:获取一页的记录集 -->
  <select id="getPage" parameterType="java.util.Map" resultMap="BaseResultMap">
	 select * from WIP_REPAIR_SN t
      where 1=1
        and t.ENABLED_FLAG = 'Y'
	<if test="repairSnId != null and repairSnId != ''"> and t.REPAIR_SN_ID = #{repairSnId}</if>
	<if test="sn != null and sn != ''"> and t.SN = #{sn}</if>
	<if test="inputFlag != null and inputFlag != ''"> and t.INPUT_FLAG = #{inputFlag}</if>
	<if test="processCode != null and processCode != ''"> and t.PROCESS_CODE = #{processCode}</if>
	<if test="orgId != null"> and t.ORG_ID = cast(#{orgId} as numeric)</if>
	<if test="factoryId != null"> and t.FACTORY_ID = cast(#{factoryId} as numeric)</if>
	<if test="entityId != null"> and t.ENTITY_ID = cast(#{entityId} as numeric)</if>
		<if test="orderField != null">
		<choose>
		    <when test="orderField=='inputFlag'"> order by t.INPUT_FLAG <if test="order != null and order == 'desc'"> desc </if> </when>
		    <when test="orderField=='sn'"> order by t.SN <if test="order != null and order == 'desc'"> desc </if> </when>
		    <when test="orderField=='processCode'"> order by t.PROCESS_CODE <if test="order != null and order == 'desc'"> desc </if> </when>
		    <when test="orderField=='createBy'"> order by t.CREATE_BY <if test="order != null and order == 'desc'"> desc </if> </when>
			<when test="orderField=='createDate'"> order by t.CREATE_DATE <if test="order != null and order == 'desc'"> desc </if> </when>
			<when test="orderField=='lastUpdatedBy'"> order by t.LAST_UPDATED_BY <if test="order != null and order == 'desc'"> desc </if> </when>
			<when test="orderField=='lastUpdatedDate'"> order by t.LAST_UPDATED_DATE <if test="order != null and order == 'desc'"> desc </if> </when>
		</choose>
	</if>
	limit #{endRow}::numeric-#{startRow}::numeric+1 offset case when ( #{startRow}::numeric-1 &lt; 0) then 0 else  #{startRow}::numeric-1 end
  </select>
  
  <!-- 获取符合条件的记录列表 -->
  <select id="getRepairSnBatch" parameterType="java.util.Map" resultMap="BaseResultMap">
    select t.* from WIP_REPAIR_SN t
    where 1=1
    and t.ENABLED_FLAG = 'Y'
	<if test="condition != null and condition != ''">
      and t.SN in
      <foreach collection="condition" index="index" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
	<if test="inputFlag != null and inputFlag != ''"> and t.INPUT_FLAG = #{inputFlag}</if>
	<if test="processCode != null and processCode != ''"> and t.PROCESS_CODE = #{processCode}</if>
    <if test="orgId != null"> and t.ORG_ID = cast(#{orgId} as numeric)</if>
    <if test=" orgId == null and (condition == null or condition == '' ) and (inputFlag == null or inputFlag == '' ) and  (processCode == null or processCode == '' )">
      and 1=2
    </if>
  </select>
</mapper>
