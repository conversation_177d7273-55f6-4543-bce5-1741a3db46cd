/*Started by AICoder, pid:n04909eb7cecb7614d2d0aa76169000809d57eb6*/
package com.zte.infrastructure.remote;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.zte.interfaces.dto.IcmsContractInfo;
import com.zte.interfaces.dto.SalesOrderUniformInfo;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@PrepareForTest({ServiceDataBuilderUtil.class,HttpClientUtil.class,JacksonJsonConverUtil.class})
public class IcmsRemoteServiceTest extends PowerBaseTestCase {
    @InjectMocks
    private IcmsRemoteService icmsRemoteService;

    @Before
    public void setup() {
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class,HttpClientUtil.class,JacksonJsonConverUtil.class);
        ReflectionTestUtils.setField(icmsRemoteService, "inoneBaseUrl", "http://mock-base-url");
        ReflectionTestUtils.setField(icmsRemoteService, "appCode", "mock-app-code");
        ReflectionTestUtils.setField(icmsRemoteService, "xInterfaceKey", "mock-x-interface-key");
    }

    @Test
    public void testGetContract_EmptyInput() {
        List<String> emptyList = Collections.emptyList();
        List<SalesOrderUniformInfo> result = icmsRemoteService.getContract(emptyList);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetContract_ValidResponse() throws Exception {
        List<String> contractList = Lists.newArrayList("CON123", "CON456");
        String mockUrl = "http://mock-base-url/ZXISS_iCMS300/zte-erp-contract-share/uniform/interface/getContract";
        String mockParamsJson = "{\"contractNumber\":[\"CON123\",\"CON456\"]}";
        String mockBoJson = "{\"salesOrderUniformList\":[{\"id\":\"SO123\"}]}";
        IcmsContractInfo mockContractInfo = new IcmsContractInfo();
        mockContractInfo.setSalesOrderUniformList(Lists.newArrayList(new SalesOrderUniformInfo()));

        when(JacksonJsonConverUtil.beanToJson(any(Map.class))).thenReturn(mockParamsJson);
        when(HttpClientUtil.httpPostWithJSON(eq(mockUrl), eq(mockParamsJson), anyMap()))
            .thenReturn("success_response");
        when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(eq("success_response"), anyString()))
            .thenReturn(mockBoJson);
        when(JacksonJsonConverUtil.jsonToBean(eq(mockBoJson), any(TypeReference.class)))
            .thenReturn(mockContractInfo);

        List<SalesOrderUniformInfo> result = icmsRemoteService.getContract(contractList);
        assertEquals(1, result.size());
    }

    @Test
    public void testGetContract_InvalidResponse() throws Exception {
        List<String> contractList = Lists.newArrayList("CON789");
        String mockUrl = "http://mock-base-url/ZXISS_iCMS300/zte-erp-contract-share/uniform/interface/getContract";
        String mockParamsJson = "{\"contractNumber\":[\"CON789\"]}";

        when(JacksonJsonConverUtil.beanToJson(any(Map.class))).thenReturn(mockParamsJson);
        when(HttpClientUtil.httpPostWithJSON(eq(mockUrl), eq(mockParamsJson), anyMap()))
            .thenReturn("error_response");
        when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(eq("error_response"), anyString()))
            .thenReturn(null);

        List<SalesOrderUniformInfo> result = icmsRemoteService.getContract(contractList);
        assertTrue(result.isEmpty());
    }
}
/*Ended by AICoder, pid:n04909eb7cecb7614d2d0aa76169000809d57eb6*/