/**
 * 项目名称 : ${project_name}
 * 创建日期 : ${date}
 * 修改历史 :
 * 1. [${date}] 创建文件 by ${user}
 **/
package com.zte.interfaces.dto;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * // TODO 添加类/接口功能描述
 * 
 * <AUTHOR>
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
public class BsPubHrvOrgIdDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * // TODO remarks
     **/
    private String userName;

    /**
     * // TODO remarks
     **/
    private String userEnName;

    /**
     * // TODO remarks
     **/
    private String userFullId;

    /**
     * // TODO remarks
     **/
    private String userId;

    /**
     * // TODO remarks
     **/
    private String orgCnName;

    /**
     * // TODO remarks
     **/
    private String orgEnName;

    /**
     * // TODO remarks
     **/
    private String orgNo;

    /**
     * // TODO remarks
     **/
    private String topOrgNo;

    /**
     * // TODO remarks
     **/
    private String topOrgCnName;

    /**
     * // TODO remarks
     **/
    private String topOrgEnName;

    /**
     * // TODO remarks
     **/
    private String twoOrgNo;

    /**
     * // TODO remarks
     **/
    private String twoOrgCnName;

    /**
     * // TODO remarks
     **/
    private String twoOrgEnName;

    /**
     * // TODO remarks
     **/
    private String status;

    /**
     * // TODO remarks
     **/
    private String email;

    /**
     * // TODO remarks
     **/
    private String notesmail;

    /**
     * // TODO remarks
     **/
    private String orgType;

    /**
     * // TODO remarks
     **/
    private String userTelephone;

    /**
     * // TODO remarks
     **/
    private String relationDeptNo;

    /**
     * // TODO remarks
     **/
    // 指定spring mvc绑定日期类型请求参数时使用的格式/转换为JSON字符串的格式
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastUpdateDate;

    /**
     * // TODO remarks
     **/
    // 指定spring mvc绑定日期类型请求参数时使用的格式/转换为JSON字符串的格式
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date activeDate;

    /**
     * // TODO remarks
     **/
    // 指定spring mvc绑定日期类型请求参数时使用的格式/转换为JSON字符串的格式
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date inactiveDate;

    /**
     * // TODO remarks
     **/
    private String employeetype;

    /**
     * // TODO remarks
     **/
    private String worktype;

    /**
     * // TODO remarks
     **/
    private String nationality;

    /**
     * // TODO remarks
     **/
    private String userFullIdBackup;

    /**
     * // TODO remarks
     **/
    private String userIdBackup;

    /**
     * // TODO remarks
     **/
    private String empidComputer;

    private java.lang.String sort; // 排序字段(值areaId或者areaCode)

    private java.lang.String order; // 排序方式(默认升序,设为desc时降序)

    private java.lang.String page; // 请求的页码

    private java.lang.String rows; // 每页条数

    public java.lang.String getSort() {

        return sort;
    }

    public void setSort(java.lang.String sort) {

        this.sort = sort;
    }

    public java.lang.String getOrder() {

        return order;
    }

    public void setOrder(java.lang.String order) {

        this.order = order;
    }

    public java.lang.String getPage() {

        return page;
    }

    public void setPage(java.lang.String page) {

        this.page = page;
    }

    public java.lang.String getRows() {

        return rows;
    }

    public void setRows(java.lang.String rows) {

        this.rows = rows;
    }

    public void setUserName(String userName) {

        this.userName = userName;
    }

    public String getUserName() {

        return userName;
    }

    public void setUserEnName(String userEnName) {

        this.userEnName = userEnName;
    }

    public String getUserEnName() {

        return userEnName;
    }

    public void setUserFullId(String userFullId) {

        this.userFullId = userFullId;
    }

    public String getUserFullId() {

        return userFullId;
    }

    public void setUserId(String userId) {

        this.userId = userId;
    }

    public String getUserId() {

        return userId;
    }

    public void setOrgCnName(String orgCnName) {

        this.orgCnName = orgCnName;
    }

    public String getOrgCnName() {

        return orgCnName;
    }

    public void setOrgEnName(String orgEnName) {

        this.orgEnName = orgEnName;
    }

    public String getOrgEnName() {

        return orgEnName;
    }

    public void setOrgNo(String orgNo) {

        this.orgNo = orgNo;
    }

    public String getOrgNo() {

        return orgNo;
    }

    public void setTopOrgNo(String topOrgNo) {

        this.topOrgNo = topOrgNo;
    }

    public String getTopOrgNo() {

        return topOrgNo;
    }

    public void setTopOrgCnName(String topOrgCnName) {

        this.topOrgCnName = topOrgCnName;
    }

    public String getTopOrgCnName() {

        return topOrgCnName;
    }

    public void setTopOrgEnName(String topOrgEnName) {

        this.topOrgEnName = topOrgEnName;
    }

    public String getTopOrgEnName() {

        return topOrgEnName;
    }

    public void setTwoOrgNo(String twoOrgNo) {

        this.twoOrgNo = twoOrgNo;
    }

    public String getTwoOrgNo() {

        return twoOrgNo;
    }

    public void setTwoOrgCnName(String twoOrgCnName) {

        this.twoOrgCnName = twoOrgCnName;
    }

    public String getTwoOrgCnName() {

        return twoOrgCnName;
    }

    public void setTwoOrgEnName(String twoOrgEnName) {

        this.twoOrgEnName = twoOrgEnName;
    }

    public String getTwoOrgEnName() {

        return twoOrgEnName;
    }

    public void setStatus(String status) {

        this.status = status;
    }

    public String getStatus() {

        return status;
    }

    public void setEmail(String email) {

        this.email = email;
    }

    public String getEmail() {

        return email;
    }

    public void setNotesmail(String notesmail) {

        this.notesmail = notesmail;
    }

    public String getNotesmail() {

        return notesmail;
    }

    public void setOrgType(String orgType) {

        this.orgType = orgType;
    }

    public String getOrgType() {

        return orgType;
    }

    public void setUserTelephone(String userTelephone) {

        this.userTelephone = userTelephone;
    }

    public String getUserTelephone() {

        return userTelephone;
    }

    public void setRelationDeptNo(String relationDeptNo) {

        this.relationDeptNo = relationDeptNo;
    }

    public String getRelationDeptNo() {

        return relationDeptNo;
    }

    public void setLastUpdateDate(Date lastUpdateDate) {

        this.lastUpdateDate = lastUpdateDate;
    }

    public Date getLastUpdateDate() {

        return lastUpdateDate;
    }

    public void setActiveDate(Date activeDate) {

        this.activeDate = activeDate;
    }

    public Date getActiveDate() {

        return activeDate;
    }

    public void setInactiveDate(Date inactiveDate) {

        this.inactiveDate = inactiveDate;
    }

    public Date getInactiveDate() {

        return inactiveDate;
    }

    public void setEmployeetype(String employeetype) {

        this.employeetype = employeetype;
    }

    public String getEmployeetype() {

        return employeetype;
    }

    public void setWorktype(String worktype) {

        this.worktype = worktype;
    }

    public String getWorktype() {

        return worktype;
    }

    public void setNationality(String nationality) {

        this.nationality = nationality;
    }

    public String getNationality() {

        return nationality;
    }

    public void setUserFullIdBackup(String userFullIdBackup) {

        this.userFullIdBackup = userFullIdBackup;
    }

    public String getUserFullIdBackup() {

        return userFullIdBackup;
    }

    public void setUserIdBackup(String userIdBackup) {

        this.userIdBackup = userIdBackup;
    }

    public String getUserIdBackup() {

        return userIdBackup;
    }

    public void setEmpidComputer(String empidComputer) {

        this.empidComputer = empidComputer;
    }

    public String getEmpidComputer() {

        return empidComputer;
    }
}
