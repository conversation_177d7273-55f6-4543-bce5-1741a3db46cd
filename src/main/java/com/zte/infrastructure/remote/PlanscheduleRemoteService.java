package com.zte.infrastructure.remote;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zte.common.CommonUtils;
import com.zte.common.enums.InterfaceEnum;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.common.utils.NumConstant;
import com.zte.common.utils.SqlUtils;
import com.zte.domain.model.PsEntityPlanBasic;
import com.zte.domain.model.PsTask;
import com.zte.domain.model.PsWorkOrderBasic;
import com.zte.domain.model.PsWorkOrderSmt;
import com.zte.domain.model.WorkOrderOperateHis;
import com.zte.interfaces.dto.BsBomHierarchicalDetail;
import com.zte.interfaces.dto.CfInventoryDTO;
import com.zte.interfaces.dto.ConciseDailyDTO;
import com.zte.interfaces.dto.LineProduceInParamDTO;
import com.zte.interfaces.dto.PmRepairVirtualSnDTO;
import com.zte.interfaces.dto.ProdPlanProgramInfoDTO;
import com.zte.interfaces.dto.PsEntityPlanBasicDTO;
import com.zte.interfaces.dto.PsTaskBasicDTO;
import com.zte.interfaces.dto.PsTaskDTO;
import com.zte.interfaces.dto.PsTaskExtraDTO;
import com.zte.interfaces.dto.PsTaskInfoDTO;
import com.zte.interfaces.dto.PsTaskScanlistDTO;
import com.zte.interfaces.dto.PsWorkOrderBasicDTO;
import com.zte.interfaces.dto.PsWorkOrderBasicOutDTO;
import com.zte.interfaces.dto.PsWorkOrderDTO;
import com.zte.interfaces.dto.PsWorkOrderSmtDTO;
import com.zte.interfaces.dto.QueryWorkOrderBasicDTO;
import com.zte.interfaces.dto.SnTaskRelDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.interfaces.dto.TechChgTaskDTO;
import com.zte.interfaces.dto.scan.PlanSysIdempotnalInfoDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.itp.msa.util.string.StringHelper;
import com.zte.microservice.name.MicroServiceNameEum;
import com.zte.springbootframe.common.consts.SysConst;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.util.JsonConvertUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 点对点调用计划服务接口类
 *
 * <AUTHOR>
 */
@Service
public class PlanscheduleRemoteService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    /* Started by AICoder, pid:c5cfacfc35c2001144940918c04ed84ca5313c52 */
    /**
     * 批量更新指令当前状态
     *
     * @param workOrderNoList        指令集合
     * @param currentWorkOrderStatus 指令当前状态
     * @param newWorkOrderStatus     指令新状态
     */
    public static void updateWorkOrderStatusBatch(List<String> workOrderNoList,
                                                  String currentWorkOrderStatus,
                                                  String newWorkOrderStatus) {
        if (CollectionUtils.isEmpty(workOrderNoList)) {
            return;
        }
        Map<String, Object> params = new HashMap<>();
        params.put("workNoList", workOrderNoList);
        params.put("currentWorkOrderStatus", currentWorkOrderStatus);
        params.put("newWorkOrderStatus", newWorkOrderStatus);
        String service = MicroServiceNameEum.PLANSCHEDULE;
        String ver = MicroServiceNameEum.VERSION;
        String type = MicroServiceNameEum.SENDTYPEPUT;
        String url = "/PsWorkOrderBasicCtrl/updateWorkOrderStatusBatch";
        String msg = MicroServiceRestUtil.invokeService(service, ver, type, url, JacksonJsonConverUtil.beanToJson(params),
                MESHttpHelper.getHttpRequestHeader());
        ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
    }
    /* Ended by AICoder, pid:c5cfacfc35c2001144940918c04ed84ca5313c52 */


    /**
     * getPsEntityPlanBasic // 根据指令号点对点获取指令信息
     *
     * @param orderNo 指令号
     * @return PsEntityPlanBasic实体List
     **/
    public List<PsWorkOrderDTO> getWorkOrderInfo(String orderNo, String task, String section, String inWoNo) {

        Map<String, Object> hashMap = new HashMap<String, Object>();
        if (StringHelper.isNotEmpty(orderNo)) {
            hashMap.put("workOrderNo", orderNo);
        }
        if (StringHelper.isNotEmpty(task)) {
            hashMap.put("sourceTask", task);
        }
        if (StringHelper.isNotEmpty(section)) {
            hashMap.put("craftSection", section);
        }
        if (StringHelper.isNotEmpty(inWoNo)) {
            hashMap.put("inWorkOrderNo", inWoNo);
        }
        hashMap.put("workOrderStatus", MpConstant.WORKORDER_STATUS_FOUR);
        String params = JacksonJsonConverUtil.beanToJson(hashMap);
        Map<String, String> header = MESHttpHelper.getHttpRequestHeader();
        // 点对点调用服务
        String service = MicroServiceNameEum.PLANSCHEDULE;
        String ver = MicroServiceNameEum.VERSION;
        String type = MicroServiceNameEum.SENDTYPEGET;
        String url = "/PS/getWorkOrderInfo";

        String msg = MicroServiceRestUtil.invokeService(service, ver, type, url, params,
                header);
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<PsWorkOrderDTO>>() {
        });
    }

    /**
     * 根据 workOrderNo 读取 workorderbasic
     *
     * @param workOrderNo
     * @return
     */
    public static PsWorkOrderBasic findWorkOrder(String workOrderNo) {
        Map<String, Object> map = new HashMap<String, Object>();
        String params = JacksonJsonConverUtil.beanToJson(map);
        Map<String, String> headerParamsMap = MESHttpHelper.getHttpRequestHeader();
        // 点对点调用服务
        String serviceName = MicroServiceNameEum.PLANSCHEDULE;
        String version = MicroServiceNameEum.VERSION;
        String sendType = MicroServiceNameEum.SENDTYPEGET;
        String getUrl = "/PsWorkOrderBasicCtrl/workOrderNo/" + workOrderNo;
        String msg = MicroServiceRestUtil.invokeService(serviceName,
                version, sendType, getUrl, params, headerParamsMap);
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
        if (StringUtils.isBlank(bo)) {
            return null;
        }
        return JacksonJsonConverUtil.jsonToBean(bo, PsWorkOrderBasic.class);
    }

    /**
     * 根据条件查指令信息
     *
     * @param dto
     * @return
     * @throws Exception
     */
    public static List<PsWorkOrderBasicDTO> queryWorkOrderList(PsWorkOrderBasicDTO dto) {
        try {
            List<PsWorkOrderBasicDTO> reList = new ArrayList<>();
            String getUrl = "/PsWorkOrderBasicCtrl/queryWorkOrderList";
            Map<String, Object> paramsMap = new HashMap<>();
            paramsMap.put("sourceTask", dto.getSourceTask());
            String msg = MicroServiceRestUtil.invokeService(MicroServiceNameEum.PLANSCHEDULE,
                    MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST, getUrl, JSON.toJSONString(paramsMap),
                    MESHttpHelper.getHttpRequestHeader());
            String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
            return JsonConvertUtil.jsonToBean(bo, List.class, PsWorkOrderBasicDTO.class);
        } catch (Exception e) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.GET_WORKORDER_CONDITION_ERROR, new Object[]{e.getMessage()});
        }
    }

    /**
     * 获取workOrderBasicList
     * 用workOrderNo字段存储'','',''
     *
     * @param map
     * @return
     */
    public static List<PsWorkOrderBasic> getListByWorkOrderNos(Map<String, Object> map) {
        String getUrl = "/PsWorkOrderBasicCtrl/getListByWorkOrderNos";
        JsonNode json = HttpRemoteService.pointToPointSelective(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST, getUrl, map);
        if (null == json) {
            return null;
        }
        String bo = json.get(MpConstant.JSON_BO).toString();
        List<PsWorkOrderBasic> list = (List<PsWorkOrderBasic>) JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<ArrayList<PsWorkOrderBasic>>() {
        });
        return list;
    }

    /**
     * @param workOrderNoList
     * @return
     */
    public static List<PsTask> getPsTaskListByWorkOrderNos(List<String> workOrderNoList) {
        List<PsTask> list = new ArrayList<>();
        if (CollectionUtils.isEmpty(workOrderNoList)) {
            return list;
        }
        String getUrl = "/PS/getListByWorkOrderNos";
        JsonNode json = HttpRemoteService.pointToPointSelectiveByObj(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST, getUrl, workOrderNoList);
        if (null == json) {
            return null;
        }
        String bo = json.get(MpConstant.JSON_BO).toString();
        return (List<PsTask>) JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<ArrayList<PsTask>>() {
        });
    }

    /**
     * 获取批次对应主卡任务信息
     *
     * @param prodPlanIdList
     * @return
     */
    public static List<PsTask> getPartsPlannoInfoByProdPlanIdList(List<String> prodPlanIdList) {
        List<PsTask> list = new ArrayList<>();
        if (CollectionUtils.isEmpty(prodPlanIdList)) {
            return list;
        }
        String getUrl = "/PS/getPartsPlannoInfoByProdPlanIdList";
        JsonNode json = HttpRemoteService.pointToPointSelectiveByObj(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST, getUrl, prodPlanIdList);
        if (null == json) {
            return null;
        }
        String bo = json.get(MpConstant.JSON_BO).toString();
        List<PsTask> tempList = JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<ArrayList<PsTask>>() {
        });
        if (!CollectionUtils.isEmpty(tempList)) {
            list.addAll(tempList);
        }
        return list;
    }


    /**
     * 获取pstask信息
     *
     * @param map
     * @return
     */
    public static List<PsTask> getPsTask(Map<String, Object> map) {
        String getUrl = "/PS/psTask";
        JsonNode json = HttpRemoteService.pointToPointSelective(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEGET, getUrl, map);
        if (null == json) {
            return null;
        }
        String bo = json.get(MpConstant.JSON_BO).toString();
        if (null != map.get(Constant.PAGE) && null != map.get(Constant.ROWS)) {
            bo = json.get(MpConstant.JSON_BO).get(MpConstant.JSON_ROWS).toString();
        }
        List<PsTask> list = (List<PsTask>) JacksonJsonConverUtil.jsonToListBean(bo,
                new TypeReference<ArrayList<PsTask>>() {
                });
        return list;
    }

    public static List<PsTask> getSchedulingTask(String lastUpdatedDateBegin) throws MesBusinessException {
        String url = "/PS/getSchedulingTask";
        Map<String, Object> map = new HashMap<>();
        map.put("lastUpdatedDateBegin", lastUpdatedDateBegin);
        Map<String, String> requestHeader = MESHttpHelper.getHttpRequestHeader();
        String msg = MicroServiceRestUtil.invokeService(MicroServiceNameEum.PLANSCHEDULE, MicroServiceNameEum.VERSION,
                MicroServiceNameEum.SENDTYPEGET, url, JacksonJsonConverUtil.beanToJson(map), requestHeader);
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
        if (StringUtils.isBlank(bo)) {
            return new ArrayList<>();
        }
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<PsTask>>() {
        });
    }

    /**
     * 获取pstask信息
     *
     * @param map
     * @return
     */
    public static List<PsTask> getPsTaskByPost(Map<String, Object> map) {
        String getUrl = "/PS/psTaskPost";
        JsonNode json = HttpRemoteService.pointToPointSelective(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST, getUrl, map);
        if (null == json) {
            return null;
        }
        String bo = json.get(MpConstant.JSON_BO).toString();
        List<PsTask> list = (List<PsTask>) JacksonJsonConverUtil.jsonToListBean(bo,
                new TypeReference<ArrayList<PsTask>>() {
                });
        return list;
    }

    /**
     * 获取pstask信息
     *
     * @param prodPlanIdList
     * @return
     */
    public static List<PsTask> getPsTaskByProdplanIdList(List<String> prodPlanIdList) {
        String getUrl = "/PS/getPsTaskByProdplanIdList";
        JsonNode json = HttpRemoteService.pointToPointSelectiveByObj(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST, getUrl, prodPlanIdList);
        if (null == json) {
            return null;
        }
        String bo = json.get(MpConstant.JSON_BO).toString();
        List<PsTask> list = (List<PsTask>) JacksonJsonConverUtil.jsonToListBean(bo,
                new TypeReference<ArrayList<PsTask>>() {
                });
        return list;
    }

    /**
     * 获取psTask信息
     *
     * @param map
     * @return
     */
    public static PageRows<PsTask> getPsTaskPage(Map<String, Object> map) {
        String getUrl = "/PS/psTaskPageForWip";
        JsonNode json = HttpRemoteService.pointToPointSelective(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST, getUrl, map);
        if (null == json) {
            return null;
        }
        String bo = json.get(MpConstant.JSON_BO).toString();
        PageRows<PsTask> page = JacksonJsonConverUtil.jsonToListBean(bo,
                new TypeReference<PageRows<PsTask>>() {
                });
        return page;
    }

    /**
     * 获取指令批次信息
     *
     * @return
     */
    public static List<String> getSourceTaskPage(String lastProdplanId, Date nowDate, Integer pageSize, String planId) {
        PsTaskDTO psTaskDTO = new PsTaskDTO();
        psTaskDTO.setProdplanId(planId);
        psTaskDTO.setNowDate(nowDate);
        psTaskDTO.setPageSize(pageSize);
        psTaskDTO.setLastProdplanId(lastProdplanId);
        String getUrl = "/PS/getSourceTasks";
        JsonNode json = HttpRemoteService.pointToPointSelectiveByObj(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST, getUrl, psTaskDTO);
        if (null == json) {
            return null;
        }
        String bo = json.get(MpConstant.JSON_BO).toString();
        List<String> page = JacksonJsonConverUtil.jsonToListBean(bo,
                new TypeReference<List<String>>() {
                });
        return page;
    }

    /**
     * 根据批次号获取任务信息
     *
     * @param prodplanId
     * @return
     */
    public static List<PsTask> getPsTaskByProdPlanId(String prodplanId) {
        if (StringUtils.isBlank(prodplanId)) {
            return null;
        }
        Map<String, Object> map = new HashMap<>();
        map.put("prodplanId", prodplanId);
        return getPsTask(map);
    }

    public static List<PsWorkOrderDTO> getInfoByWorkOrderNo(String workOrderNo) throws MesBusinessException {
        String url = "/PS/getInfoByWorkOrderNo";
        Map<String, Object> map = new HashMap<>();
        map.put("workOrderNo", workOrderNo);
        Map<String, String> requestHeader = MESHttpHelper.getHttpRequestHeader();
        String msg = MicroServiceRestUtil.invokeService(MicroServiceNameEum.PLANSCHEDULE, MicroServiceNameEum.VERSION,
                MicroServiceNameEum.SENDTYPEGET, url, JacksonJsonConverUtil.beanToJson(map), requestHeader);
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
        if (StringUtils.isBlank(bo)) {
            return new ArrayList<>();
        }
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<PsWorkOrderDTO>>() {
        });
    }

    public static List<PsWorkOrderDTO> getWorkWorkInfoForCal(String queryOption) throws MesBusinessException {
        String url = "/PS/getWorkWorkInfoForCal";
        Map<String, Object> map = new HashMap<>();
        map.put("queryOption", queryOption);
        Map<String, String> requestHeader = MESHttpHelper.getHttpRequestHeader();
        String msg = MicroServiceRestUtil.invokeService(MicroServiceNameEum.PLANSCHEDULE, MicroServiceNameEum.VERSION,
                MicroServiceNameEum.SENDTYPEGET, url, JacksonJsonConverUtil.beanToJson(map), requestHeader);
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
        if (StringUtils.isBlank(bo)) {
            return new ArrayList<>();
        }
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<PsWorkOrderDTO>>() {
        });
    }

    public static List<PsWorkOrderDTO> getWorkOrderInfoByLineCode(PsWorkOrderDTO dto) throws MesBusinessException {
        String postUrl = "/PS/getWorkOrderInfoByLineCode";
        Map<String, String> requestHeader = MESHttpHelper.getHttpRequestHeader();
        String msg = MicroServiceRestUtil.invokeService(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST,
                postUrl, JacksonJsonConverUtil.beanToJson(dto), requestHeader);
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
        if (StringUtils.isBlank(bo)) {
            return new ArrayList<>();
        }
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<PsWorkOrderDTO>>() {
        });
    }

    /**
     * 根据任务号获取pstask信息
     *
     * @param taskNo
     * @return
     * @throws Exception
     */
    public static PsTask getPsTaskByTaskNo(String taskNo) throws Exception {
        PsTask psTask = null;
        Map<String, Object> map = new HashMap<>();
        map.put("taskNo", taskNo);
        String getUrl = "/PS/psTask";
        JsonNode json = HttpRemoteService.pointToPointSelective(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEGET, getUrl, map);
        if (null == json || null == json.get(MpConstant.JSON_BO)) {
            String[] param = {taskNo};
            throw new Exception(CommonUtils.getLmbMessage(MessageId.GET_TASK_INFO_ERROR, param));
        }
        String bo = json.get(MpConstant.JSON_BO).toString();
        List<PsTask> list = JacksonJsonConverUtil.jsonToListBean(bo,
                new TypeReference<ArrayList<PsTask>>() {
                });
        if (list != null && !list.isEmpty()) {
            psTask = list.get(NumConstant.NUM_ZERO);
        }
        return psTask;
    }

    /**
     * 根据taskNos查询任务信息
     * map参数为"item": PsTask
     * taskNo字段拼好'','','','' 再传入
     *
     * @param map
     * @return
     */
    public static List<PsTask> getTaskListByTaskNos(Map<String, Object> map) {
        String getUrl = "/PS/getTaskListByTaskNo";
        JsonNode json = HttpRemoteService.pointToPointSelective(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST, getUrl, map);
        if (null == json) {
            return null;
        }
        String bo = json.get(MpConstant.JSON_BO).toString();
        List<PsTask> list = (List<PsTask>) JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<ArrayList<PsTask>>() {
        });
        return list;
    }


    public static JsonNode getBasicWorkOrderInfo(Map<String, Object> map) {
        String getUrl = "/PS/getBasicWorkOrderInfo";
        return HttpRemoteService.pointToPointSelective(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEGET, getUrl, map);
    }

    /* Started by AICoder, pid:e83b2n7422a882214b110b8930ced73d4541e5b3 */
    /**
     * 根据指令查找基础的指令信息(workOrderBasic/workOrderSmt/psTask)
     *
     * @param workOrderNo
     */
    public static PsWorkOrderDTO getBasicWorkerOrderInfo(String workOrderNo) {
        if (StringUtils.isBlank(workOrderNo)) {
            return null;
        }
        // 点对点调用指令查询服务
        Map<String, String> map = new HashMap<String, String>();
        map.put("workOrderNo", workOrderNo);
        Map<String, String> headerParamsMap = MESHttpHelper.getHttpRequestHeader();
        String params = JacksonJsonConverUtil.beanToJson(map);
        String serviceName = MicroServiceNameEum.PLANSCHEDULE;
        String version = MicroServiceNameEum.VERSION;
        String sendType = MicroServiceNameEum.SENDTYPEGET;
        String getUrl = "/PS/getBasicWorkOrderInfo";
        String result = MicroServiceRestUtil.invokeService(serviceName, version, sendType, getUrl,
                params, headerParamsMap);
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(result);
        List<PsWorkOrderDTO> workList = JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<PsWorkOrderDTO>>() {
        });
        if (CollectionUtils.isEmpty(workList)) {
            return null;
        }
        return workList.get(NumConstant.NUM_ZERO);
    }
    /* Ended by AICoder, pid:e83b2n7422a882214b110b8930ced73d4541e5b3 */


    /**
     * 根据指令编号获取指令信息
     *
     * @param map
     * @param map
     * @return
     * @throws Exception
     */
    public static List<PsWorkOrderDTO> getPsWorkOrderBasic(Map<String, Object> map) throws Exception {
        List<PsWorkOrderDTO> workOrderList = null;
        JsonNode json = PlanscheduleRemoteService.getBasicWorkOrderInfo(map);
        if (null == json || null == json.get(MpConstant.JSON_BO)) {
            throw new Exception(CommonUtils.getLmbMessage(MessageId.WORKORDER_NOT_FIND));
        }
        String bo = json.get(MpConstant.JSON_BO).toString();
        workOrderList = JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<PsWorkOrderDTO>>() {
        });
        return workOrderList;
    }

    /**
     * 根据批次获取指令
     *
     * @param prodplanId
     * @return
     * @throws Exception
     */
    public static List<PsWorkOrderDTO> getWorkOrderBasicByProdPlanId(String prodplanId, String taskNo) throws Exception {
        if (StringUtils.isEmpty(prodplanId) && StringUtils.isEmpty(taskNo)) {
            return null;
        }
        Map<String, Object> map = new HashMap<>();
        map.put("prodplanId", prodplanId);
        map.put("taskNo", taskNo);
        return getPsWorkOrderBasic(map);
    }

    /**
     * 获取指令SMT信息
     *
     * @param map
     * @return
     */
    public static PsWorkOrderSmt getWorkOrderSMTInfo(Map<String, Object> map) {

        String getUrl = "/PsWorkOrderSmtCtrl/getWorkOrderSMTByWorkOrder";
        JsonNode json = HttpRemoteService.pointToPointSelective(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST, getUrl, map);
        if (null == json) {
            return null;
        }
        String bo = json.get(MpConstant.JSON_BO).toString();

        List<PsWorkOrderSmt> psWorkOrderSmtList = JacksonJsonConverUtil.jsonToListBean(bo,
                new TypeReference<List<PsWorkOrderSmt>>() {
                });
        if (!CollectionUtils.isEmpty(psWorkOrderSmtList)) {
            return psWorkOrderSmtList.get(NumConstant.NUM_ZERO);
        }
        return null;
    }

    /**
     * 根据实际开工时间和实际完工时间查询指令信息
     *
     * @param queryDto
     * @return
     * @throws Exception
     */
    public static List<PsWorkOrderBasicDTO> selectWorkOrderBasicByActualStartDateAndEndTime(QueryWorkOrderBasicDTO queryDto) throws Exception {
        String getUrl = "/PsWorkOrderBasicCtrl/selectWorkOrderBasicByActualStartDateAndEndTime";

        JsonNode json = HttpRemoteService.pointToPointSelectiveByObj(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST, getUrl, queryDto);

        if (null == json) {
            return null;
        }
        String bo = json.get(Constant.BO).toString();
        String retCode = json.get(Constant.JSON_CODE).get(Constant.JSON_CODE).asText();
        if (!RetCode.SUCCESS_CODE.equalsIgnoreCase(retCode)) {
            throw new Exception(bo);
        }
        List<PsWorkOrderBasicDTO> list = (List<PsWorkOrderBasicDTO>) JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<ArrayList<PsWorkOrderBasicDTO>>() {
        });
        return list;
    }

    public static String updateWorkOrderAndTask(PsEntityPlanBasicDTO entity) {
        String getUrl = "/PS/updateWorkOrderAndTask";
        JsonNode json = HttpRemoteService.pointToPointSelectiveByObj(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST, getUrl, entity);
        if (null == json || null == json.get(MpConstant.JSON_BO)) {
            return "";
        }
        String bo = json.get(MpConstant.JSON_BO).toString();
        return bo;
    }


    /**
     * 获取车间生产指令集
     *
     * @param dto 入参
     * @return List<PsWorkOrderBasicDTO> 指令集
     * @throws Exception 点对点调用异常
     */
    public static List<PsWorkOrderBasicOutDTO> getWorkshopProductionInstructionSet(LineProduceInParamDTO dto) throws Exception {
        String getUrl = "/PsWorkOrderBasicCtrl/getWorkshopProductionInstructionSet";

        JsonNode json = HttpRemoteService.pointToPointSelectiveByObj(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEGET, getUrl, dto);

        if (null == json) {
            return null;
        }
        String bo = json.get(Constant.BO).toString();
        String retCode = json.get(Constant.JSON_CODE).get(Constant.JSON_CODE).asText();
        if (!RetCode.SUCCESS_CODE.equalsIgnoreCase(retCode)) {
            throw new Exception(bo);
        }
        List<PsWorkOrderBasicOutDTO> list = (List<PsWorkOrderBasicOutDTO>) JSONArray.parseArray(bo, PsWorkOrderBasicOutDTO.class);
        return list;
    }

    public static JsonNode getPsEntityPlanBasic(Map<String, Object> map) {
        String getUrl = "/PS/psEntityPlanBasic";
        return HttpRemoteService.pointToPointSelective(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEGET, getUrl, map);
    }

    /**
     * 根据指令号获取指令信息
     *
     * @param linCode
     * @param workOrderStatus
     * @return
     */
    public static List<PsEntityPlanBasic> getWorkOrderInfoByWorkOrderStatusAndLineCode(String linCode, String workOrderStatus) {
        Map<String, Object> map = new HashMap<>();
        map.put("workOrderStatus", workOrderStatus);
        map.put("lineCode", linCode);
        return getPsEntityPlanBasicList(map);
    }

    /**
     * 根据指令号获取指令信息
     *
     * @param workOrderNo
     * @return
     */
    public static PsEntityPlanBasic getWorkOrderInfoByWorkOrderNo(String workOrderNo) {
        if (StringUtils.isEmpty(workOrderNo)) {
            return null;
        }
        Map<String, Object> map = new HashMap<>();
        map.put("workOrderNo", workOrderNo);
        List<PsEntityPlanBasic> psInfoList = getPsEntityPlanBasicList(map);
        if (CollectionUtils.isEmpty(psInfoList)) {
            return null;
        }
        return psInfoList.get(NumConstant.NUM_ZERO);
    }

    public static List<PsEntityPlanBasic> getPsEntityPlanBasicList(Map<String, Object> map) {
        String getUrl = "/PS/psEntityPlanBasic";
        JsonNode json = HttpRemoteService.pointToPointSelective(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEGET, getUrl, map);
        if (null == json || json.get(Constant.JSON_CODE) == null || json.get(Constant.JSON_CODE).get(Constant.JSON_CODE) == null) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.CALL_SERVICE_FAILED);
        }
        String retCode = json.get(Constant.JSON_CODE).get(Constant.JSON_CODE).asText();
        String bo = json.get(Constant.BO).toString();
        if (!RetCode.SUCCESS_CODE.equalsIgnoreCase(retCode)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.CALL_SERVICE_FAILED);
        }
        return JacksonJsonConverUtil
                .jsonToListBean(bo, new TypeReference<ArrayList<PsEntityPlanBasic>>() {
                });
    }

    public static List<Map<String, String>> getRouteIdAndNoList(Map<String, Object> map) throws Exception {
        String getUrl = "/PsWorkOrderBasicCtrl/getRouteIdAndNoList";

        JsonNode json = HttpRemoteService.pointToPointSelectiveByObj(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST, getUrl, map);

        if (null == json) {
            return null;
        }
        String bo = json.get(Constant.BO).toString();
        if (json.get(Constant.JSON_CODE) == null || json.get(Constant.JSON_CODE).get(Constant.JSON_CODE) == null) {
            return null;
        }
        String retCode = json.get(Constant.JSON_CODE).get(Constant.JSON_CODE).asText();
        if (!RetCode.SUCCESS_CODE.equalsIgnoreCase(retCode)) {
            throw new Exception(bo);
        }
        List<Map<String, String>> list = (List<Map<String, String>>) JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<ArrayList<Map<String, String>>>() {
        });
        return list;
    }

    public static List<PsTask> getPsTaskByPartsPlanNo(String taskNo) {
        String getUrl = "/PS/psTask";
        Map<String, Object> map = new HashMap<>();
        map.put("partsPlanno", taskNo);
        JsonNode json = HttpRemoteService.pointToPointSelective(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEGET, getUrl, map);
        if (null == json) {
            return null;
        }
        String bo = json.get(MpConstant.JSON_BO).toString();
        List<PsTask> list = JacksonJsonConverUtil.jsonToListBean(bo,
                new TypeReference<ArrayList<PsTask>>() {
                });
        return list;
    }

    public List<PsTask> getSubTaskInfoByZbjPlanNoList(List<String> zbjPlanNoList) {
        String url = "/PS/getSubTaskInfoByZbjPlanNoList";
        String response = MicroServiceRestUtil.invokeService(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST, url, JSON.toJSONString(zbjPlanNoList),
                MESHttpHelper.getHttpRequestHeader());
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(response);
        if (StringUtils.isBlank(bo)) {
            return new ArrayList<>();
        }
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<PsTask>>() {
        });
    }

    public static Integer updateOrInsertPsTaskScanInfo(PsTaskScanlistDTO dto) {
        String getUrl = "/pstaskscanlist/updateOrInsertPsTaskScanInfo?" + SysConst.HTTP_HEADER_X_FACTORY_ID + "=" + dto.getFactoryId();
        JsonNode json = HttpRemoteService.pointToPointSelectiveByObj(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST, getUrl, dto);
        if (json != null && json.get(Constant.BO) != null) {
            return json.get(Constant.BO).asInt();
        }
        return null;
    }

    public static PsWorkOrderBasic getProductCodeByWorkNo(String workOrderNo) throws Exception {
        String getUrl = "/PsWorkOrderBasicCtrl/workOrderNo/" + workOrderNo;
        JsonNode json = HttpRemoteService.pointToPointSelectiveByObj(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEGET, getUrl, null);
        if (null == json) {
            return null;
        }
        String bo = json.get(Constant.BO).toString();
        String retCode = json.get(Constant.CODE).get(Constant.CODE).asText();
        if (!RetCode.SUCCESS_CODE.equalsIgnoreCase(retCode)) {
            throw new Exception(bo);
        }
        return JsonConvertUtil.jsonToBean(bo, PsWorkOrderBasic.class);
    }

    public static List<PsWorkOrderSmt> getWorkOrderSMTByWorkOrder(PsWorkOrderDTO psWorkOrderDTO) throws Exception {
        List<PsWorkOrderSmt> workOrderSmtList = null;
        String getUrl = "/PsWorkOrderSmtCtrl/getWorkOrderSMTByWorkOrder";
        JsonNode json = HttpRemoteService.pointToPointSelectiveByObj(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST, getUrl, psWorkOrderDTO);
        if (null == json || null == json.get(MpConstant.JSON_BO)) {
            throw new Exception(CommonUtils.getLmbMessage(MessageId.WORKORDER_NOT_FIND));
        }
        String bo = json.get(MpConstant.JSON_BO).toString();
        workOrderSmtList = JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<PsWorkOrderSmt>>() {
        });
        return workOrderSmtList;
    }

    public static List<PsTaskExtraDTO> getPrintScene(String prodplanId) throws Exception {
        String getUrl = "/PS/getPrintScene";
        Map<String, Object> map = new HashMap<>();
        map.put("prodplanId", prodplanId);
        JsonNode json = HttpRemoteService.pointToPointSelective(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEGET, getUrl, map);

        if (null == json || null == json.get(MpConstant.JSON_BO)) {
            return null;
        }
        String bo = json.get(MpConstant.JSON_BO).toString();
        PageRows<PsTaskExtraDTO> psTaskExtraDTOPageRows = JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<PageRows<PsTaskExtraDTO>>() {
        });
        return psTaskExtraDTOPageRows.getRows();
    }

    /**
     * 获取pstask
     *
     * @param taskNoOrPlanId
     * @return
     */
    public static PsTask getPsTaskByTaskNoOrPlanId(String taskNoOrPlanId) {
        String getUrl = "/PS/getTaskAndLead/" + taskNoOrPlanId;
        JsonNode json = HttpRemoteService.pointToPointSelective(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEGET, getUrl, null);
        if (null == json) {
            return null;
        }
        String bo = json.get(MpConstant.JSON_BO).toString();
        PsTask psTask = (PsTask) JacksonJsonConverUtil.jsonToBean(bo, PsTask.class);
        return psTask;
    }

    /**
     * 获取pstaskList
     *
     * @param taskNoOrPlanIds
     * @return
     */
    public static List<PsTask> getEnvAttrBatchByIds(List<String> taskNoOrPlanIds) {
        String getUrl = "/PS/getPsTaskListByIds";
        JsonNode json = HttpRemoteService.pointToPointSelectiveByObj(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST, getUrl, taskNoOrPlanIds);
        if (null == json) {
            return null;
        }
        String bo = json.get(MpConstant.JSON_BO).toString();
        List<PsTask> list = (List<PsTask>) JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<ArrayList<PsTask>>() {
        });
        return list;
    }

    /**
     * 获取TaskNo
     *
     * @param requestParam
     * @return
     */
    public static List<String> getTaskNoByErpStatusAndAttr(Map<String, Object> requestParam) {
        String getUrl = "/PS/getTaskNoByErpStatusAndAttr";
        JsonNode json = HttpRemoteService.pointToPointSelectiveByObj(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST, getUrl, requestParam);
        if (null == json) {
            return null;
        }
        String bo = json.get(MpConstant.JSON_BO).toString();
        List<String> list = (List<String>) JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<ArrayList<String>>() {
        });
        return list;
    }

    public static List<PsWorkOrderSmt> getBsmtBomDetailListOfTwoDip(String workOrderNo) throws Exception {

        String getUrl = "/PsWorkOrderBasicCtrl/getBsmtBomDetailListOfTwoDip/" + workOrderNo;
        JsonNode json = HttpRemoteService.pointToPointSelectiveByObj(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEGET, getUrl, null);
        String bo = getString(json);
        if (bo == null) {
            return null;
        }
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<PsWorkOrderSmt>>() {
        });
    }

    public static List<PsWorkOrderSmt> selectPsWorkOrderSmtList(Collection<String> workOrderNoList) throws Exception {
        if (CollectionUtils.isEmpty(workOrderNoList)) {
            // 不能为空
            return new ArrayList<PsWorkOrderSmt>();
        }
        if (workOrderNoList.size() > NumConstant.NUM_1000) {
            // 单次查询不能超过1000
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.LIST_SIZE_CAN_NOT_EXCEED_1000);
        }
        Map<String, Object> map = new HashMap<>();
        map.put("inWorkOrderNo", SqlUtils.convertStrCollectionToSqlType(workOrderNoList));
        String getUrl = "/PsWorkOrderSmtCtrl/selectPsWorkOrderSmtList";
        JsonNode json = HttpRemoteService.pointToPointSelectiveByObj(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST, getUrl, map);
        if (null == json) {
            return null;
        }
        String bo = json.get(Constant.BO).toString();
        String retCode = json.get(Constant.CODE).get(Constant.CODE).asText();
        if (!RetCode.SUCCESS_CODE.equalsIgnoreCase(retCode)) {
            throw new Exception(bo);
        }
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<PsWorkOrderSmt>>() {
        });
    }

    public static PsWorkOrderBasic getPsWorkOrderSmtList(String workOrderNo) throws Exception {

        String getUrl = "/PsWorkOrderSmtCtrl/getPsWorkOrderSmtList/" + workOrderNo;
        JsonNode json = HttpRemoteService.pointToPointSelectiveByObj(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEGET, getUrl, null);
        String bo = getString(json);
        if (bo == null) {
            return null;
        }
        return JsonConvertUtil.jsonToBean(bo, PsWorkOrderBasic.class);
    }

    /**
     * 根据配置编号cfgHeaderId获取拼版信息
     *
     * @return
     */
    public static PsWorkOrderSmt getPcbQty(String cfgHeaderId) throws Exception {

        String getUrl = "/PsWorkOrderSmtCtrl/getSmtWorkOrderList?" + "cfgHeaderId" + '=' + cfgHeaderId;
        JsonNode json = HttpRemoteService.pointToPointSelectiveByObj(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEGET, getUrl, null);
        if (null == json) {
            return null;
        }
        String bo = json.get(Constant.BO).toString();
        String retCode = json.get(Constant.CODE).get(Constant.CODE).asText();
        if (!RetCode.SUCCESS_CODE.equalsIgnoreCase(retCode)) {
            throw new Exception(bo);
        }
        return JsonConvertUtil.jsonToBean(bo, PsWorkOrderSmt.class);
    }


    public static List<PsWorkOrderSmt> getList(String workOrderNo) throws Exception {

        String getUrl = "/PsWorkOrderSmtCtrl/getList/" + workOrderNo;
        JsonNode json = HttpRemoteService.pointToPointSelectiveByObj(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEGET, getUrl, null);
        String bo = getString(json);
        if (bo == null) {
            return null;
        }
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<PsWorkOrderSmt>>() {
        });
    }

    private static String getString(JsonNode json) throws Exception {
        if (null == json) {
            return null;
        }
        String bo = json.get(Constant.BO).toString();
        String retCode = json.get(Constant.CODE).get(Constant.CODE).asText();
        if (!RetCode.SUCCESS_CODE.equalsIgnoreCase(retCode)) {
            throw new Exception(bo);
        }
        return bo;
    }

    /**
     * @param prodplanIdAndQtyMap
     * @return 如果不为空 则表示返回错误信息
     * @throws Exception
     */
    public static void batchHandleCompleteQty(Map<String, BigDecimal> prodplanIdAndQtyMap)
            throws Exception {
        Map<String, String> headerParamsMap = MESHttpHelper.getHttpRequestHeader();
        // 点对点调用服务
        String serviceName = MicroServiceNameEum.PLANSCHEDULE;
        String version = MicroServiceNameEum.VERSION;
        String sendType = MicroServiceNameEum.SENDTYPEPOST;
        String getUrl = "/PS/batchHandleCompleteQty";

        String getresult = MicroServiceRestUtil.invokeService(serviceName,
                version, sendType, getUrl, JSON.toJSONString(prodplanIdAndQtyMap), headerParamsMap);
        ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(getresult);
    }

    /**
     * 根据批次查询工艺路径route_id
     *
     * @param planIds
     * @return
     */
    public static List<PsWorkOrderBasic> getWorkBasicByTask(List<String> planIds, List<String> craftSectionList, String getLineName) {
        Map<String,Object> param = Maps.newHashMap();
        param.put("souceTasksList", planIds);
        param.put("craftSectionList", craftSectionList);
        param.put("lineName", getLineName);
        String getUrl = "/PsWorkOrderBasicCtrl/getWorkBasicByTask";
        String msg = MicroServiceRestUtil.invokeService(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST,
                getUrl, JacksonJsonConverUtil.beanToJson(param), MESHttpHelper.getHttpRequestHeader());
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<ArrayList<PsWorkOrderBasic>>() {});
    }

    /**
     * 按条件更新任务信息
     *
     * @param params
     * @return
     * @throws Exception
     */
    public static long updatePsTaskInfoSelective(Map<String, Object> params) throws Exception {
        String putUrl = "/PS/psTask";
        JsonNode json = HttpRemoteService.pointToPointSelective(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPUT, putUrl, params);
        if (null == json || null == json.get(MpConstant.JSON_BO)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.UPDATE_TASK_INFO_ERROR);
        }
        String bo = json.get(MpConstant.JSON_BO).toString();
        if (StringUtils.isEmpty(bo)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.UPDATE_TASK_INFO_ERROR);
        }
        long count = Long.parseLong(bo);
        if (count < NumConstant.NUM_ONE) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.UPDATE_TASK_INFO_ERROR);
        }
        return count;
    }

    /**
     * 根据批次获取指令
     *
     * @param prodplanId
     * @throws Exception
     * @return+
     */
    public static List<PsWorkOrderDTO> getBasicWorkOrderInfoByProdplanId(String prodplanId) {
        return getBasicWorkOrderInfo(prodplanId, null, null);
    }

    /**
     * 根据条件获取指令
     *
     * @param prodplanId
     * @return 指令信息
     */
    public static List<PsWorkOrderDTO> getBasicWorkOrderInfo(String prodplanId, String lineCode, String craftSections) {
        if (StringUtils.isBlank(prodplanId)) {
            return null;
        }
        Map<String, Object> map = new HashMap<>();
        map.put("prodplanId", prodplanId);
        if (StringUtils.isNotBlank(lineCode)) {
            map.put("lineCode", lineCode);
        }
        if (StringUtils.isNotBlank(craftSections)) {
            map.put("inCraftSecton", craftSections);
        }
        String getUrl = "/PS/getBasicWorkOrderInfo";
        String msg = MicroServiceRestUtil.invokeService(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEGET,
                getUrl, JacksonJsonConverUtil.beanToJson(map), MESHttpHelper.getHttpRequestHeader());
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<PsWorkOrderDTO>>() {
        });
    }

    public static PsWorkOrderBasic findPsWorkOrderSmtList(String workOrderNo) throws Exception {

        String getUrl = "/PsWorkOrderSmtCtrl/findPsWorkOrderSmtList/" + workOrderNo;
        JsonNode json = HttpRemoteService.pointToPointSelectiveByObj(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEGET, getUrl, null);
        if (null == json) {
            return null;
        }
        String bo = json.get(Constant.BO).toString();
        String retCode = json.get(Constant.CODE).get(Constant.CODE).asText();
        if (!RetCode.SUCCESS_CODE.equalsIgnoreCase(retCode)) {
            throw new Exception(bo);
        }
        return JsonConvertUtil.jsonToBean(bo, PsWorkOrderBasic.class);
    }

    /**
     * 获取指令集合
     *
     * @param sourceTask
     * @throws Exception
     * @return+
     */
    public static List<PsWorkOrderDTO> getWorkOrderInfo(String sourceTask) throws MesBusinessException {
        String getUrl = "/PS/getWorkOrderInfo";
        Map<String, Object> map = new HashMap<>();
        map.put("sourceTask", sourceTask);
        JsonNode json = HttpRemoteService.pointToPointSelective(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEGET, getUrl, map);

        if (null == json || null == json.get(MpConstant.JSON_BO)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.WORKORDER_NOT_FIND);
        }
        String bo = json.get(MpConstant.JSON_BO).toString();
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<PsWorkOrderDTO>>() {
        });
    }


    /**
     * 获取指令所有信息
     */
    public static List<PsWorkOrderDTO> findBasicWorkOrderInfo(String sourceTask) throws MesBusinessException {
        String getUrl = "/PS/findBasicWorkOrderInfo";
        PsWorkOrderDTO entity = new PsWorkOrderDTO();
        entity.setSourceTask(sourceTask);
        JsonNode json = HttpRemoteService.pointToPointSelectiveByObj(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEGET, getUrl, entity);
        if (null == json || null == json.get(MpConstant.JSON_BO)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.WORKORDER_NOT_FIND);
        }
        String bo = json.get(MpConstant.JSON_BO).toString();
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<PsWorkOrderDTO>>() {
        });
    }

    /**
     * 根据批次获取指令集合
     *
     * @param prodPlanIdList
     * @throws Exception
     * @return+
     */
    public static List<PsWorkOrderDTO> getWorkOrderInfoByProdplanIdList(List<String> prodPlanIdList) throws Exception {
        String getUrl = "/PS/getWorkOrderInfoByProdIdList";
        JsonNode json = HttpRemoteService.pointToPointSelectiveByObj(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST, getUrl, prodPlanIdList);
        List<PsWorkOrderDTO> returnList = new ArrayList<>();
        if (null == json || null == json.get(MpConstant.JSON_BO)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.WORKORDER_NOT_FIND);
        }
        String bo = json.get(MpConstant.JSON_BO).toString();
        returnList = JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<PsWorkOrderDTO>>() {
        });
        return returnList;
    }

    /**
     * 清理条码已全部入库，但状态不是已完工的批次
     *
     * @param planIds
     * @throws Exception
     * @return+
     */
    public static void batchCompleteTask(List<String> planIds) {
        String getUrl = "/PS/batchCompleteTask";
        HttpRemoteService.pointToPointSelectiveByObj(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST, getUrl, planIds);
    }

    /**
     * 获取指令上料表头Id
     *
     * @param tarWorkOrder
     * @param curWorkOrder
     * @return
     * @throws Exception
     */
    public static Map<String, PsWorkOrderSmtDTO> getWoSmtByWos(String tarWorkOrder, String curWorkOrder) throws Exception {
        String url = "/PsWorkOrderSmtCtrl/getByWorkOrders";
        List<String> woNos = Lists.newArrayList(tarWorkOrder);
        if (StringUtils.isNotBlank(curWorkOrder)) {
            woNos.add(curWorkOrder);
        }
        String reStr = MicroServiceRestUtil.invokeService(MicroServiceNameEum.PLANSCHEDULE, MicroServiceNameEum.VERSION,
                MicroServiceNameEum.SENDTYPEPOST, url, JSON.toJSONString(woNos), MESHttpHelper.getHttpRequestHeader());

        ServiceData result = JSON.parseObject(reStr, ServiceData.class);
        if (result == null || result.getCode() == null) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.CALL_SERVICE_ERROR,
                    new String[]{MicroServiceNameEum.PLANSCHEDULE, url});
        }
        if (!Constant.SUCCESS.equals(result.getCode().getCode())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.CALL_SERVICE_ERROR,
                    new String[]{MicroServiceNameEum.PLANSCHEDULE + url, result.getCode().getMsg()});
        }
        List<PsWorkOrderSmtDTO> workOrderList = JSON.parseArray(JSON.toJSONString(result.getBo()), PsWorkOrderSmtDTO.class);
        if (CollectionUtils.isEmpty(workOrderList)) {
            return new HashMap<>();
        }
        return workOrderList.stream()
                .collect(Collectors.toMap(PsWorkOrderSmtDTO::getWorkOrderNo,
                        e -> e, (a, b) -> a));
    }

    /**
     * 向上递归获取主卡批次for技改创建
     *
     * @param planIds
     * @param includeFinished 是否包含已完工批次
     * @return
     * @throws MesBusinessException
     */
    public static List<TechChgTaskDTO> getTopTaskRecForTechChg(List<String> planIds, String includeFinished) throws MesBusinessException {
        String url = "/PS/getTopTaskRecForTechChg?includeFinished=" + includeFinished;
        JsonNode json = HttpRemoteService.postHandler(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST, url, JSON.toJSONString(planIds));
        if (null == json) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PRODUCT_TASK_IS_NULL);
        }
        String bo = json.get(MpConstant.JSON_BO).toString();
        List<TechChgTaskDTO> list = JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<ArrayList<TechChgTaskDTO>>() {
        });
        if (CollectionUtils.isEmpty(list)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PRODUCT_TASK_IS_NULL);
        }
        return list;
    }

    /**
     * @Description: 为简明日报查询计划服务单板任务
     * @Param: [prodPlanIds, page, row]
     * @return: java.util.List<com.zte.domain.model.PsTask>
     * @Author: Saber[10307315]
     * @Date: 2022/12/15 上午9:53
     */
    public static List<PsTask> pageSelectForConciseDaily(List<String> prodPlanIds, int page, int row) throws Exception {
        List<PsTask> resultList = new ArrayList<>();
        String url = "/PS/pageSelectForConciseDaily";
        PsTaskDTO psTaskDTO = new PsTaskDTO();
        psTaskDTO.setPage(new Long((long) page));
        psTaskDTO.setRows(new Long((long) row));
        psTaskDTO.setProdPlanIds(prodPlanIds);
        String response = MicroServiceRestUtil.invokeService(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST, url, JSON.toJSONString(psTaskDTO),
                MESHttpHelper.getHttpRequestHeader());
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(response);
        if (StringUtils.isBlank(bo)) {
            return resultList;
        }
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<PsTask>>() {
        });
    }

    /**
     * @Description: 查询指令历史表，找指令第一次开工时间。
     * @Param: [workOrderNoList]
     * @return: java.util.List<com.zte.domain.model.WorkOrderOperateHis>
     * @Author: Saber[10307315]
     * @Date: 2022/12/16 上午9:29
     */
    public static List<WorkOrderOperateHis> getWorkFirstStartTimeByWorkNoList(List<String> workOrderNoList) throws Exception {
        List<WorkOrderOperateHis> resultList = new ArrayList<>();
        String url = "/WorkOrderOperateHis/getWorkFirstStartTimeByWorkNoList";
        String response = MicroServiceRestUtil.invokeService(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST, url, JSON.toJSONString(workOrderNoList),
                MESHttpHelper.getHttpRequestHeader());
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(response);
        if (StringUtils.isBlank(bo)) {
            return resultList;
        }
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<WorkOrderOperateHis>>() {
        });
    }

    public static Page<PsTask> pageSelectForConciseDailyReelTime(ConciseDailyDTO conciseDailyDTO) throws Exception {
        ConciseDailyDTO param = new ConciseDailyDTO();
        // 避免不必要的参数，增加传输时长
        param.setTaskNo(conciseDailyDTO.getTaskNo());
        param.setProdPlanId(conciseDailyDTO.getProdPlanId());
        param.setExternalType(conciseDailyDTO.getExternalType());
        param.setInternalType(conciseDailyDTO.getInternalType());
        param.setProductType(conciseDailyDTO.getProductType());
        param.setItemNo(conciseDailyDTO.getItemNo());
        param.setItemName(conciseDailyDTO.getItemName());
        param.setPage(conciseDailyDTO.getPage());
        param.setRow(conciseDailyDTO.getRow());
        Page<PsTask> result = new Page<>();
        String url = "/PS/pageSelectForConciseDailyReelTime";
        String response = MicroServiceRestUtil.invokeService(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST, url, JSON.toJSONString(param),
                MESHttpHelper.getHttpRequestHeader());
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(response);
        if (StringUtils.isBlank(bo)) {
            return result;
        }
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<Page<PsTask>>() {
        });
    }

    /**
     * @Description: 使用prodPlanId查询计划指令表，找routeId
     * @Param: [prodPlanIdList]
     * @return: java.util.List<com.zte.domain.model.PsWorkOrderBasic>
     * @Author: Saber[10307315]
     * @Date: 2022/12/15 下午2:18
     */
    public static List<PsWorkOrderBasic> queryRouteIdByProdIdList(List<String> prodPlanIdList) {
        List<PsWorkOrderBasic> resultList = new ArrayList<>();
        String url = "/PsWorkOrderBasicCtrl/queryRouteIdByProdIdList";
        String response = MicroServiceRestUtil.invokeService(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST, url, JSON.toJSONString(prodPlanIdList),
                MESHttpHelper.getHttpRequestHeader());
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(response);
        if (StringUtils.isBlank(bo)) {
            return resultList;
        }
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<PsWorkOrderBasic>>() {
        });
    }

    public static void updateTaskGetDate(List<String> planIds) throws MesBusinessException {
        String getUrl = "/PS/updateTaskGetDate";
        JsonNode json = HttpRemoteService.pointToPointSelectiveByObj(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST, getUrl, planIds);
        if (null == json) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.CALL_SERVICE_ERROR,
                    new String[]{getUrl, planIds.toString()});
        }
        String retCode = json.get(Constant.CODE).get(Constant.CODE).asText();
        if (!RetCode.SUCCESS_CODE.equalsIgnoreCase(retCode)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.CALL_SERVICE_ERROR,
                    new String[]{getUrl + planIds, json.get(Constant.CODE).get(Constant.MSG).asText()});
        }
    }

    public static List<PsTask> selectPsTaskByProdIdSet(Set<String> prodIdSet) throws Exception {
        List<PsTask> resultList = new ArrayList<>();
        String url = "/PS/selectPsTaskByProdIdSet";
        String response = MicroServiceRestUtil.invokeService(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST, url, JSON.toJSONString(prodIdSet),
                MESHttpHelper.getHttpRequestHeader());
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(response);
        if (StringUtils.isBlank(bo)) {
            return resultList;
        }
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<PsTask>>() {
        });
    }

    /**
     * @Description: 根据批次列表获取对应的routeID
     * @Param: [prodIdList]
     * @return: java.util.List<com.zte.domain.model.PsWorkOrderBasic>
     * @Author: Saber[10307315]
     * @Date: 2023/1/17 上午9:12
     */
    public static List<PsWorkOrderBasic> queryRouteIdDistinctByProdIdList(List<String> prodIdList) throws Exception {
        List<PsWorkOrderBasic> resultList = new ArrayList<>();
        String url = "/PsWorkOrderBasicCtrl/queryRouteIdDistinctByProdIdList";
        String response = MicroServiceRestUtil.invokeService(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST, url, JSON.toJSONString(prodIdList),
                MESHttpHelper.getHttpRequestHeader());
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(response);
        if (StringUtils.isBlank(bo)) {
            return resultList;
        }
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<PsWorkOrderBasic>>() {
        });
    }

    public static Set<String> selectProdOfWorkOrderByProdList(List<String> prodList) throws Exception {
        Set<String> resultSet = new HashSet<>();
        String url = "/PS/selectHaveWorkProd";
        Set<String> params = new HashSet<>(prodList);
        String response = MicroServiceRestUtil.invokeService(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST, url, JSON.toJSONString(params),
                MESHttpHelper.getHttpRequestHeader());
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(response);
        if (StringUtils.isBlank(bo)) {
            return resultSet;
        }
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<Set<String>>() {
        });
    }

    public static Set<String> selectHaveWorkProd(String factoryId, String address,
                                                 Set<String> prodIdSet) throws Exception {
        Set<String> resultSet = new HashSet<>();
        Map<String, String> headParams = MESHttpHelper.getHttpRequestHeader();
        //设置相应工厂ID
        headParams.put(SysConst.HTTP_HEADER_X_FACTORY_ID, factoryId);
        headParams.put(SysConst.HTTP_HEADER_X_FACTORY_ID_LOW_CASE, factoryId);
        headParams.put(SysConst.HTTP_HEADER_X_EMP_NO, Constant.SYSTEM);
        headParams.put(SysConst.HTTP_HEADER_X_LANG_ID, Constant.CHINA);
        if (StringUtils.isEmpty(address)) {
            return resultSet;
        }
        // 设置参数
        String params = JacksonJsonConverUtil.beanToJson(prodIdSet);
        String url = Constant.STRING_EMPTY;
        url = new StringBuilder(address).append("/")
                .append(InterfaceEnum.selectHaveWorkProd.getServiceName()).append("/")
                .append(InterfaceEnum.selectHaveWorkProd.getUrl()).toString();
        String response = HttpClientUtil.httpPostWithJSON(url, params, headParams);
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(response);
        resultSet = JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<Set<String>>() {
        });
        return resultSet;
    }

    public static List<String> selectWorkNoOfComMachine(String lineCode, String taskNo, String processCode) {
        Map<String, Object> hashMap = new HashMap<String, Object>();
        hashMap.put("lineCode", lineCode);
        hashMap.put("taskNo", taskNo);
        hashMap.put("processCode", processCode);
        String params = JacksonJsonConverUtil.beanToJson(hashMap);
        Map<String, String> header = MESHttpHelper.getHttpRequestHeader();
        String result = MicroServiceRestUtil.invokeService(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEGET, "/PS/selectWorkNoOfComMachine", params, header);
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(result);
        if (StringUtils.isEmpty(bo)) {
            return null;
        }
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<String>>() {
        });
    }

    public static List<PsTaskDTO> selectPsTaskOfWmesByTaskNos(Set<String> taskNoSet) {
        String params = JacksonJsonConverUtil.beanToJson(taskNoSet);
        Map<String, String> header = MESHttpHelper.getHttpRequestHeader();
        String result = MicroServiceRestUtil.invokeService(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST, "/PS/selectPsTaskOfWmesByTaskNos", params, header);
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(result);
        if (StringUtils.isEmpty(bo)) {
            return new ArrayList<>();
        }
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<PsTaskDTO>>() {
        });
    }

    /**
     * 根据条件统计指令数
     *
     * @param map
     * @return
     */
    public static int getWorkOrderInfoCount(Map<String, Object> map) {
        Map<String, String> header = MESHttpHelper.getHttpRequestHeader();
        String params = JacksonJsonConverUtil.beanToJson(map);
        String url = "/PS/getWorkOrderInfoCount";
        String result = MicroServiceRestUtil.invokeService(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST, url, params, header);
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(result);
        if (StringUtils.isEmpty(bo)) {
            return 0;
        }
        return Integer.parseInt(bo);
    }

    /**
     * 获取批次信息 sourceTask and  workOrderStatus
     *
     * @param dto
     * @return
     */
    public List<PsWorkOrderBasicDTO> querySourceTaskInfo(PsWorkOrderBasicDTO dto) throws Exception {
        String post = "/PsWorkOrderBasicCtrl/querySourceTaskInfo";
        JsonNode json = HttpRemoteService.postHandler(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST, post, JSON.toJSONString(dto));
        if (null == json) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PRODUCT_TASK_IS_NULL);
        }
        String bo = json.get(MpConstant.JSON_BO).toString();
        List<PsWorkOrderBasicDTO> list = JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<ArrayList<PsWorkOrderBasicDTO>>() {
        });
        if (CollectionUtils.isEmpty(list)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PRODUCT_TASK_IS_NULL);
        }
        return list;
    }

    public List<PsTaskScanlistDTO> getScanList(Map<String, Object> map) throws Exception {
        String getUrl = "/pstaskscanlist/getScanList";
        JsonNode json = HttpRemoteService.pointToPointSelectiveByObj(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEGET, getUrl, map);
        if (null == json) {
            return null;
        }
        String bo = json.get(Constant.BO).toString();
        String retCode = json.get(Constant.CODE).get(Constant.CODE).asText();
        if (!RetCode.SUCCESS_CODE.equalsIgnoreCase(retCode)) {
            throw new Exception(bo);
        }
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<ArrayList<PsTaskScanlistDTO>>() {
        });
    }

    public int insertOrUpdatePsWorkOrderSmtInfo(PsWorkOrderSmtDTO dto) throws Exception {
        String getUrl = "/PsWorkOrderSmtCtrl/psWorkOrderSmt";
        JsonNode json = HttpRemoteService.pointToPointSelectiveByObj(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST, getUrl, dto);
        if (json == null) {
            logger.error(":" + MpConstant.INSERT_OR_UPDATE_SMT);
            throw new Exception(MpConstant.INSERT_OR_UPDATE_SMT);
        }
        return Constant.INT_1;

    }

    /**
     * 调用点对点服务获取无条码送修的虚拟条码
     */
    public static List<String> getRepairSnListRemote(PmRepairVirtualSnDTO dto)
            throws IOException {
        List<String> list = null;
        String serviceName = MicroServiceNameEum.PLANSCHEDULE;
        String version = MicroServiceNameEum.VERSION;
        String sendType = MicroServiceNameEum.SENDTYPEPOST;
        String getUrl = "/BoardSnCtrl/getRepairSnList/";
        String params = JacksonJsonConverUtil.beanToJson(dto);
        // 点对点调用服务
        Map<String, String> headerParamsMap = MESHttpHelper.getHttpRequestHeader();
        String getResult = MicroServiceRestUtil.invokeService(serviceName, version, sendType, getUrl, params, headerParamsMap);
        JsonNode json = JacksonJsonConverUtil.getMapperInstance().readTree(getResult);
        String retCode = json.get(MpConstant.JSON_CODE).get(MpConstant.JSON_CODE).asText();
        JsonNode bo = json.get(MpConstant.JSON_BO);
        if (!RetCode.SUCCESS_CODE.equalsIgnoreCase(retCode)) {
            throw new RuntimeException(json.get(MpConstant.JSON_CODE).get(MpConstant.JSON_MSG).asText());
        }
        if (bo != null) {
            list = JacksonJsonConverUtil.jsonToListBean(bo.toString(), new TypeReference<List<String>>() {
            });
        }
        return list;
    }

    /**
     * 通过指令号获取指令信息
     *
     * @param workOrderNo 指令号
     * @return 指令信息
     */
    public List<PsEntityPlanBasicDTO> getPsEntityPlanInfo(String workOrderNo) {
        if (StringUtils.isBlank(workOrderNo)) {
            return new LinkedList<>();
        }
        Map<String, Object> hashMap = new HashMap<String, Object>();
        hashMap.put("workOrderNo", workOrderNo);
        String params = JacksonJsonConverUtil.beanToJson(hashMap);
        Map<String, String> header = MESHttpHelper.getHttpRequestHeader();
        String result = MicroServiceRestUtil.invokeService(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEGET, "/PS/psEntityPlanInfo", params, header);
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(result);
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<ArrayList<PsEntityPlanBasicDTO>>() {
        });
    }

    /**
     * 点对点更新指令的投入产出数量和指令状态
     *
     * @param planBasicDTO
     * @return bo
     * @throws Exception
     * <AUTHOR>
     */
    public String updatePsWorkorderQty(PsEntityPlanBasicDTO planBasicDTO) throws Exception {
        // 点对点调用执行指令更新操作
        Map<String, String> header = MESHttpHelper.getHttpRequestHeader();
        String params = JacksonJsonConverUtil.beanToJson(planBasicDTO);
        String msg = MicroServiceRestUtil.invokeService(MicroServiceNameEum.PLANSCHEDULE, MicroServiceNameEum.VERSION,
                MicroServiceNameEum.SENDTYPEPUT, "/PS/updateWorkOrderInputOutQty", params, header);
        return ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
    }

    /**
     * 通过指令查询得到子工序组和线体
     *
     * @param workOrderNo
     * @return
     * @throws Exception
     */
    public static List<PsWorkOrderBasicDTO> getProcessCodeAndLineList(String workOrderNo) throws Exception {
        // 点对点调用服务
        String getUrl = "/PsWorkOrderBasicCtrl/queryProcessGroupAndLineCode";
        Map<String, Object> map = new HashMap<>();
        map.put("workOrderNo", workOrderNo);
        JsonNode json = HttpRemoteService.pointToPointSelective(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEGET, getUrl, map);

        if (null == json || null == json.get(MpConstant.JSON_BO)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.WORKORDER_NOT_FIND);
        }
        String bo = json.get(MpConstant.JSON_BO).toString();
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<PsWorkOrderBasicDTO>>() {
        });
    }


    /**
     * 更新投入数量
     *
     * @param workOrderNo
     * @return
     * @throws Exception
     */
    public static Integer updateInputQtyByWorkOrderNo(String workOrderNo, Long alertCount) throws Exception {
        Map<String, Object> map = new HashMap<>();
        map.put("workOrderNo", workOrderNo);
        map.put("alertCount", alertCount);
        // 点对点调用服务
        String getUrl = "/PsWorkOrderBasicCtrl/updateInPutQty?" + "workOrderNo" + '=' + workOrderNo + '&' + "alertCount" + '=' + alertCount;
        JsonNode json = HttpRemoteService.pointToPointSelectiveByObj(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST, getUrl, map);
        if (json != null && json.get(Constant.BO) != null) {
            return json.get(Constant.BO).asInt();
        }
        return null;
    }

    /**
     * 更新投入删除幂等key
     * @param planSysIdempotnalInfoDTO 更新投入删除幂等key
     */
    public static void updateInPutQtyThenDeleteKey(PlanSysIdempotnalInfoDTO planSysIdempotnalInfoDTO){
        String url = "/PsWorkOrderBasicCtrl/updateInPutQtyThenDeleteKey";
        String responseStr = MicroServiceRestUtil.invokeService(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST,
                url, JacksonJsonConverUtil.beanToJson(planSysIdempotnalInfoDTO), MESHttpHelper.getHttpRequestHeader());
        ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(responseStr);
    }

    /**
     * 更新投入产出数量
     *
     * @param workOrderNo
     * @return
     * @throws Exception
     */
    public static Integer updateOutPutQtyByWorkOrderNo(String workOrderNo, Long alertCount) throws Exception {
        Map<String, Object> map = new HashMap<>();
        map.put("workOrderNo", workOrderNo);
        map.put("alertCount", alertCount);
        // 点对点调用服务
        String getUrl = "/PsWorkOrderBasicCtrl/updateOutPutQty?" + "workOrderNo" + '=' + workOrderNo + '&' + "alertCount" + '=' + alertCount;
        JsonNode json = HttpRemoteService.pointToPointSelectiveByObj(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST, getUrl, map);
        if (json != null && json.get(Constant.BO) != null) {
            return json.get(Constant.BO).asInt();
        }
        return null;
    }

    /**
     * 根据批次和主工序获取指令信息
     *
     * @param
     * @return workOrderNo
     * @throws Exception
     */
    public static PsWorkOrderBasicDTO queryWorkOrderNo(String sourceTask, String craftSection) throws Exception {
        Map<String, Object> map = new HashMap<>();
        map.put("sourceTask", sourceTask);
        map.put("craftSection", craftSection);
        // 点对点调用服务
        String getUrl = "/PsWorkOrderBasicCtrl/queryWorkOrderBySourceTaskAndCraftSection";
        JsonNode json = HttpRemoteService.pointToPointSelectiveByObj(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEGET, getUrl, map);
        if (null == json) {
            return null;
        }
        String bo = json.get(Constant.BO).toString();
        String retCode = json.get(Constant.CODE).get(Constant.CODE).asText();
        if (!RetCode.SUCCESS_CODE.equalsIgnoreCase(retCode)) {
            throw new Exception(bo);
        }
        return JsonConvertUtil.jsonToBean(bo, PsWorkOrderBasicDTO.class);
    }

    /**
     * 获取首指令
     *
     * @param
     * @return workOrderNo
     * @throws Exception
     */
    public static PsWorkOrderBasicDTO queryFirstWorkOrderByProdplanId(String sourceTask) throws Exception {
        Map<String, Object> map = new HashMap<>();
        map.put("sourceTask", sourceTask);
        // 点对点调用服务
        String getUrl = "/PsWorkOrderBasicCtrl/queryFirstWorkOrderByProdplanId";
        JsonNode json = HttpRemoteService.pointToPointSelectiveByObj(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEGET, getUrl, map);
        if (null == json) {
            return null;
        }
        String bo = json.get(Constant.BO).toString();
        String retCode = json.get(Constant.CODE).get(Constant.CODE).asText();
        if (!RetCode.SUCCESS_CODE.equalsIgnoreCase(retCode)) {
            throw new Exception(bo);
        }
        return JsonConvertUtil.jsonToBean(bo, PsWorkOrderBasicDTO.class);
    }


    /**
     * 条码是否在条码与任务关系表中存在
     */
    public static List<SnTaskRelDTO> getSnTaskRel(List<String> sns) throws MesBusinessException {
        String getUrl = "/SnTaskRel/selectBySns";
        JsonNode jsonSnTaskRelDTO = HttpRemoteService.pointToPointSelectiveByObj(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST, getUrl, sns);
        if (null == jsonSnTaskRelDTO) {
            return null;
        }
        String bo = jsonSnTaskRelDTO.get(Constant.BO).toString();
        String retCode = jsonSnTaskRelDTO.get(Constant.JSON_CODE).get(Constant.JSON_CODE).asText();
        if (!RetCode.SUCCESS_CODE.equalsIgnoreCase(retCode)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.CALL_SERVICE_FAILED);
        }
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<ArrayList<SnTaskRelDTO>>() {
        });
    }

    /**
     * 根据批次获取上料头表id列表
     *
     * @param
     * @param prodPlanId
     * @return java.util.List<java.lang.String>
     * @Author: 10307315陈俊熙
     * @date 2022/6/6 下午3:18
     */
    public static List<PsWorkOrderSmt> getPsWorkSmtListByProdPlanId(String prodPlanId) throws MesBusinessException {
        String url = "/PsWorkOrderSmtCtrl/getPsWorkSmtListByProdPlanId";
        Map<String, Object> map = new HashMap<>();
        map.put("prodPlanId", prodPlanId);
        JsonNode jsonSnTaskRelDTO = HttpRemoteService.pointToPointSelective(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEGET, url, map);
        if (null == jsonSnTaskRelDTO) {
            return null;
        }
        String bo = jsonSnTaskRelDTO.get(Constant.BO).toString();
        String retCode = jsonSnTaskRelDTO.get(Constant.JSON_CODE).get(Constant.JSON_CODE).asText();
        if (!RetCode.SUCCESS_CODE.equalsIgnoreCase(retCode)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.CALL_SERVICE_FAILED);
        }
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<ArrayList<PsWorkOrderSmt>>() {
        });
    }

    public static List<PsTask> getSubTaskByTaskNo(List<String> taskNos) throws MesBusinessException {
        String url = "/PS/getSubTaskByTaskNo";
        JsonNode jsonSnTaskRelDTO = HttpRemoteService.pointToPointSelectiveByObj(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST, url, taskNos);
        if (null == jsonSnTaskRelDTO) {
            return null;
        }
        String bo = jsonSnTaskRelDTO.get(Constant.BO).toString();
        String retCode = jsonSnTaskRelDTO.get(Constant.JSON_CODE).get(Constant.JSON_CODE).asText();
        if (!RetCode.SUCCESS_CODE.equalsIgnoreCase(retCode)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.CALL_SERVICE_FAILED);
        }
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<ArrayList<PsTask>>() {
        });
    }

    public static PsTask getTopProdplan(Map<String, String> topSnMap) throws Exception {
        String url = "/PS/getTopProdplan";
        PsTask psTask = null;
        JsonNode json = HttpRemoteService.pointToPointSelectiveByObj(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST, url, topSnMap);
        if (null == json) {
            return null;
        }
        JsonNode codeObj = json.get(MpConstant.JSON_CODE);
        if (null != codeObj && RetCode.SUCCESS_CODE.equals(codeObj.get(MpConstant.JSON_CODE).asText())) {
            String bo = json.get(MpConstant.JSON_BO).toString();
            psTask = JacksonJsonConverUtil.jsonToBean(bo, PsTask.class);
        }
        return psTask;
    }

    /**
     * @return
     * <AUTHOR>
     * 获取任务状态不为“已完工、已废止”或当天完工的批次及指令信息
     * @Date 2022/11/8 11:26
     * @Param [com.zte.interfaces.dto.PsTaskDTO]
     **/
    public static List<PsTaskBasicDTO> getPsTaskAndWorkOrder(PsTaskBasicDTO dto, List<SysLookupTypesDTO> lookupTypesDTOList) throws Exception {
        String url = "/PS/getPsTaskAndWorkOrder";
        List<String> meaningList = new ArrayList<>();
        for (SysLookupTypesDTO sysLookupTypesDTO : lookupTypesDTOList) {
            meaningList.add(sysLookupTypesDTO.getLookupMeaning());
        }
        Map<String, Object> map = new HashMap<>();
        map.put("prodplanId", dto.getProdplanId());
        map.put("meaningList", meaningList);
        map.put("notFinishIncludeNullAndTodayFinish", dto.getNotFinishIncludeNullAndTodayFinish());
        map.put("notFinishIncludeNullAndYesterdayFinish", dto.getNotFinishIncludeNullAndYesterdayFinish());
        JsonNode json = HttpRemoteService.pointToPointSelective(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST, url, map);
        if (null == json || null == json.get(Constant.BO)) {
            return null;
        }
        String bo = json.get(Constant.BO).toString();
        String retCode = json.get(Constant.CODE).get(Constant.CODE).asText();
        if (!RetCode.SUCCESS_CODE.equalsIgnoreCase(retCode)) {
            throw new Exception(json.get(Constant.CODE).get(Constant.MSG).asText());
        }
        List<PsTaskBasicDTO> psTaskDTOList = JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<PsTaskBasicDTO>>() {
        });
        if (CollectionUtils.isEmpty(psTaskDTOList)) {
            return null;
        }
        return psTaskDTOList;
    }

    /**
     * 获取子卡多层递归关系
     *
     * @param taskNoList 计划跟踪单号
     * @param needRoot   是够需要跟节点
     * @return 子卡关系
     * @throws Exception 接口调用异常
     */
    public List<PsTask> getSubTaskTreeByTaskNo(List<String> taskNoList, String needRoot) throws Exception {
        String url = "/PS/getSubTaskTreeByTaskNo?needRoot=" + needRoot;
        String responseStr = MicroServiceRestUtil.invokeService(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST, url, JSON.toJSONString(taskNoList),
                MESHttpHelper.getHttpRequestHeader());
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(responseStr);
        if (StringUtils.isBlank(bo)) {
            return new LinkedList<>();
        }
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<PsTask>>() {
        });
    }

    /**
     * 根据批次获取任务数量
     */
    public static List<PsTask> getTaskQtyByPlan(Map<String, Object> map) {
        String getUrl = "/PS/getTaskQtyByPlan";
        JsonNode json = HttpRemoteService.pointToPointSelective(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST, getUrl, map);
        if (null == json) {
            return null;
        }
        String bo = json.get(MpConstant.JSON_BO).toString();
        List<PsTask> list = (List<PsTask>) JacksonJsonConverUtil.jsonToListBean(bo,
                new TypeReference<ArrayList<PsTask>>() {
                });
        return list;
    }

    /**
     * 更新批次已完工数量信息
     *
     * @param prodplanId  批次
     * @param completeQty 数量
     * @throws Exception 业务异常
     */
    public static void updateTaskCompleteQtyAndStatus(String prodplanId, BigDecimal completeQty) throws Exception {
        String url = "/PS/updateTaskCompleteQtyAndStatus";
        PsTaskDTO psTaskDTO = new PsTaskDTO();
        psTaskDTO.setProdplanId(prodplanId);
        psTaskDTO.setCompleteQty(completeQty);
        String result = MicroServiceRestUtil.invokeService(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST,
                url, JSON.toJSONString(psTaskDTO), MESHttpHelper.getHttpRequestHeader());
        ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(result);
    }

    /**
     * 获取指令相关信息
     *
     * @param map 请求参数
     * @return 执行信息
     */
    public static List<PsWorkOrderDTO> getBasicWorkOrder(Map<String, Object> map) throws Exception {
        String postUrl = "/PS/queryBasicWorkOrderInfo";
        Map<String, String> requestHeader = MESHttpHelper.getHttpRequestHeader();
        String msg = MicroServiceRestUtil.invokeService(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST,
                postUrl, JacksonJsonConverUtil.beanToJson(map), requestHeader);
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
        if (StringUtils.isBlank(bo)) {
            return new LinkedList<>();
        }
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<PsWorkOrderDTO>>() {
        });
    }

    /**
     * 获取批次首件入库日期
     *
     * @return 执行信息
     */
    public static List<PsTaskBasicDTO> getFirstWarehouseDate(List<String> prodplanList) throws Exception {
        String postUrl = "/PS/getFirstWarehouseDate";
        Map<String, String> requestHeader = MESHttpHelper.getHttpRequestHeader();
        JsonNode json = HttpRemoteService.pointToPointSelectiveByObj(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST, postUrl, prodplanList);
        if (null == json) {
            return new ArrayList<>();
        }
        String bo = json.get(MpConstant.JSON_BO).toString();
        if (StringUtils.isBlank(bo)) {
            return new ArrayList<>();
        }
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<PsTaskBasicDTO>>() {
        });
    }

    public static String updateInputOutQtyForRefresh(List<String> prodplanList) throws Exception {
        String getUrl = "/PS/updateInputOutQtyForRefresh";
        JsonNode json = HttpRemoteService.pointToPointSelectiveByObj(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST, getUrl, prodplanList);
        if (null == json || null == json.get(MpConstant.JSON_BO)) {
            return "";
        }
        return json.get(MpConstant.JSON_BO).toString();
    }

    /**
     * 获取pstask 任务信息
     *
     * @return 任务信息
     */
    public List<PsTask> getTaskListByProdPlanId(List<String> prodplanIdList) {
        if (CollectionUtils.isEmpty(prodplanIdList)) {
            return new LinkedList<>();
        }
        String prods = prodplanIdList.stream()
                .collect(Collectors.joining(Constant.SEPARATED_COMMA_COMMA, Constant.QUO_MARK, Constant.QUO_MARK));
        PsTask psTask = new PsTask();
        psTask.setProdplanId(prods);
        String postUrl = "/PS/getTaskListByProdPlanId";
        String responseStr = MicroServiceRestUtil.invokeService(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST,
                postUrl, JacksonJsonConverUtil.beanToJson(psTask), MESHttpHelper.getHttpRequestHeader());
        String s = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(responseStr);
        if (StringUtils.isBlank(s)) {
            return new LinkedList<>();
        }
        List<PsTask> psTasks = JacksonJsonConverUtil.jsonToListBean(s, new TypeReference<List<PsTask>>() {
        });
        if (Objects.isNull(psTasks)) {
            psTasks = new LinkedList<>();
        }
        return psTasks;
    }

    public PsTask getPstaskByProdId(String prodPlanId) {
        if (StringUtils.isBlank(prodPlanId)) {
            return null;
        }
        List<PsTask> taskList = this.getTaskListByProdPlanId(new ArrayList<String>() {{
            add(prodPlanId);
        }});
        if (CollectionUtils.isEmpty(taskList)) {
            return null;
        }
        return taskList.get(0);
    }

    /**
     * 获取批次对应已开工SMT指令信息
     *
     * @return 执行信息
     */
    public static List<PsTaskBasicDTO> getLineCodeInfoBySourceTask(String sourceTask) throws Exception {
        String postUrl = "/PsWorkOrderBasicCtrl/getLineInfoBySourceTask/" + sourceTask;
        Map<String, String> requestHeader = MESHttpHelper.getHttpRequestHeader();
        Map<String, String> header = MESHttpHelper.getHttpRequestHeader();
        String result = MicroServiceRestUtil.invokeService(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEGET, postUrl, null, header);

        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(result);
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<ArrayList<PsTaskBasicDTO>>() {
        });
    }

    /**
     * 获取批次对应已开工SMT指令信息
     *
     * @return 执行信息
     */
    public static void finishWithChildForScan(PsEntityPlanBasicDTO planBasicDTO) throws Exception {
        String postUrl = "/PS/finishWithChildForScan";
        Map<String, String> requestHeader = MESHttpHelper.getHttpRequestHeader();
        String msg = MicroServiceRestUtil.invokeService(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST,
                postUrl, JacksonJsonConverUtil.beanToJson(planBasicDTO), requestHeader);
        ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
    }

    /**
     * 更新任务完工状态，完工数量等信息
     *
     * @param list 更新批次信息
     */
    public void batchUpdatePsTask(List<PsTask> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        String postUrl = "/PS/batchUpdatePsTask";
        Map<String, String> requestHeader = MESHttpHelper.getHttpRequestHeader();
        String msg = MicroServiceRestUtil.invokeService(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPUT,
                postUrl, JacksonJsonConverUtil.beanToJson(list), requestHeader);
        ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
    }

    /**
     * 通过批次查询线体
     *
     * @param taskList
     * @return
     * @throws Exception
     */
    public static List<PsWorkOrderBasicDTO> getLineList(List<String> taskList) throws Exception {
        // 点对点调用服务
        String getUrl = "/PsWorkOrderBasicCtrl/getLineList";
        JsonNode json = HttpRemoteService.pointToPointSelectiveByObj(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST, getUrl, taskList);
        if (null == json) {
            return new ArrayList<>();
        }
        String bo = json.get(MpConstant.JSON_BO).toString();
        if (StringUtils.isBlank(bo)) {
            return new ArrayList<>();
        }
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<PsWorkOrderBasicDTO>>() {
        });
    }

    /**
     * 根据任务号和子工序获取指令集合
     *
     * @param taskNo
     * @throws Exception
     * @return+
     */
    public static List<PsWorkOrderDTO> getWorkOrderInfoByTaskNoAndProcess(String taskNo, String process) throws MesBusinessException {
        if (StringUtils.isBlank(taskNo) || StringUtils.isBlank(process)) {
            return new ArrayList<>();
        }
        String getUrl = "/PS/getBasicWorkOrderInfo";
        Map<String, Object> map = new HashMap<>();
        map.put("taskNo", taskNo);
        map.put("containProcess", process);
        map.put("workOrderStatus", Constant.SCAN_STATUS);
        Map<String, String> requestHeader = MESHttpHelper.getHttpRequestHeader();
        String msg = MicroServiceRestUtil.invokeService(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEGET,
                getUrl, JacksonJsonConverUtil.beanToJson(map), requestHeader);
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<PsWorkOrderDTO>>() {
        });
    }

    /**
     * 根据指令号获取infor_exe信息
     *
     * @param workOrderNos
     * @return
     * @throws Exception
     */
    public static List<PsTask> getInforExeByWorkOrderNos(List<String> workOrderNos) {
        if (CollectionUtils.isEmpty(workOrderNos)) {
            return new ArrayList<>();
        }
        String getUrl = "/PS/getInforExeByWorkOrderNos";
        Map<String, String> requestHeader = MESHttpHelper.getHttpRequestHeader();
        String msg = MicroServiceRestUtil.invokeService(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST,
                getUrl, JacksonJsonConverUtil.beanToJson(workOrderNos), requestHeader);
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<PsTask>>() {
        });
    }

    /**
     * 获取批次信息
     *
     * @param map
     * @return
     */
    public static List<PsTaskInfoDTO> getPstaskNew(Map<String, Object> map) {
        String params = JacksonJsonConverUtil.beanToJson(map);
        String serviceName = MicroServiceNameEum.PLANSCHEDULE;
        String version = MicroServiceNameEum.VERSION;
        String sendType = MicroServiceNameEum.SENDTYPEGET;
        String getUrl = "/PS/psTask";
        String msg = MicroServiceRestUtil.invokeService(serviceName, version, sendType, getUrl, params, MESHttpHelper.getHttpRequestHeader());
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
        return JacksonJsonConverUtil.jsonToListBean(bo,
                new TypeReference<ArrayList<PsTaskInfoDTO>>() {
                });
    }

    /**
     * 获取pstask信息
     *
     * @param taskNoList
     * @return
     */
    public static List<PsTask> getPsTaskByTaskNoList(List<String> taskNoList) {
        if (CollectionUtils.isEmpty(taskNoList)) {
            return new ArrayList<>();
        }
        String getUrl = "/PS/getPsTaskByTaskNoList";
        JsonNode json = HttpRemoteService.pointToPointSelectiveByObj(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST, getUrl, taskNoList);
        if (null == json) {
            return new ArrayList<>();
        }
        String bo = json.get(MpConstant.JSON_BO).toString();
        List<PsTask> list = (List<PsTask>) JacksonJsonConverUtil.jsonToListBean(bo,
                new TypeReference<ArrayList<PsTask>>() {
                });
        return list;
    }

    public static List<PsWorkOrderDTO> getWorkOrderInfoWithPsTask(Map<String, Object> map) throws MesBusinessException {
        String postUrl = "/PS/getWorkOrderInfoWithPsTask";
        Map<String, String> requestHeader = MESHttpHelper.getHttpRequestHeader();
        String msg = MicroServiceRestUtil.invokeService(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST,
                postUrl, JacksonJsonConverUtil.beanToJson(map), requestHeader);
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<PsWorkOrderDTO>>() {
        });
    }

    public static List<ProdPlanProgramInfoDTO> getProgramStatusByProdplanIds(List<String> hasPreBomInfo) {
        String postUrl = "/prodPlanProgramInfo/getProgramStatusByProdplanIds";
        Map<String, String> requestHeader = MESHttpHelper.getHttpRequestHeader();
        String msg = MicroServiceRestUtil.invokeService(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST,
                postUrl, JacksonJsonConverUtil.beanToJson(hasPreBomInfo), requestHeader);
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<ProdPlanProgramInfoDTO>>() {
        });
    }

    public static Page<PsWorkOrderDTO> getWorkOrderInfoWithPsTaskPage(Map<String, Object> map) throws MesBusinessException {
        String postUrl = "/PS/getWorkOrderInfoWithPsTaskPage";
        Map<String, String> requestHeader = MESHttpHelper.getHttpRequestHeader();
        String msg = MicroServiceRestUtil.invokeService(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST,
                postUrl, JacksonJsonConverUtil.beanToJson(map), requestHeader);
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<Page<PsWorkOrderDTO>>() {
        });
    }

    /**
     * 根据批次获取上料头表id列表
     *
     * @param
     * @return java.util.List<java.lang.String>
     * @Author: 10307315陈俊熙
     * @date 2022/6/6 下午3:18
     */
    public static PsWorkOrderSmt getPsWorkSmtBySourceTaskAndLineCode(String sourceTask, String workOrderNo, String lineCode) throws Exception {
        String url = "/PsWorkOrderSmtCtrl/getPsWorkSmtBySourceTaskAndLineCode";
        Map<String, Object> map = new HashMap<>();
        map.put("sourceTask", sourceTask);
        map.put("workOrderNo", workOrderNo);
        map.put("lineCode", lineCode);

        JsonNode jsonSnTaskRelDTO = HttpRemoteService.pointToPointSelective(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST, url, map);

        if (null == jsonSnTaskRelDTO) {
            return null;
        }
        String bo = jsonSnTaskRelDTO.get(Constant.BO).toString();
        String retCode = jsonSnTaskRelDTO.get(Constant.JSON_CODE).get(Constant.JSON_CODE).asText();
        if (!RetCode.SUCCESS_CODE.equalsIgnoreCase(retCode)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.CALL_SERVICE_FAILED);
        }
        return JacksonJsonConverUtil.jsonToBean(bo, PsWorkOrderSmt.class);
    }

    /**
     * 根据批次查询线体
     *
     * @param sourceTask
     * @return
     * @throws MesBusinessException
     */
    public static List<String> getLineCodeBySourceTask(String sourceTask) throws MesBusinessException {
        String postUrl = "/PS/getLineCodeBySourceTask";
        Map<String, Object> map = new HashMap<>();
        map.put("sourceTask", sourceTask);
        Map<String, String> requestHeader = MESHttpHelper.getHttpRequestHeader();
        String msg = MicroServiceRestUtil.invokeService(MicroServiceNameEum.PLANSCHEDULE, MicroServiceNameEum.VERSION,
                MicroServiceNameEum.SENDTYPEGET, postUrl, JacksonJsonConverUtil.beanToJson(map), requestHeader);
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
        if (StringUtils.isBlank(bo)) {
            return new ArrayList<>();
        }
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<String>>() {
        });
    }

    /**
     * 查询排在线体上的批次
     *
     * @param lineCode
     * @return
     */
    public static List<PsEntityPlanBasic> getSourceTaskByLineCode(String lineCode) {
        String postUrl = "/PS/getSourceTaskByLineCode";
        Map<String, Object> map = new HashMap<>();
        map.put("lineCode", lineCode);
        Map<String, String> requestHeader = MESHttpHelper.getHttpRequestHeader();
        String msg = MicroServiceRestUtil.invokeService(MicroServiceNameEum.PLANSCHEDULE, MicroServiceNameEum.VERSION,
                MicroServiceNameEum.SENDTYPEGET, postUrl, JacksonJsonConverUtil.beanToJson(map), requestHeader);
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
        if (StringUtils.isBlank(bo)) {
            return new ArrayList<>();
        }
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<PsEntityPlanBasic>>() {
        });
    }

    /**
     * 获取指令上料表头Id
     *
     * @param workOrderNoList
     * @return
     * @throws Exception
     */
    public static List<PsWorkOrderSmtDTO> getByWorkOrders(List<String> workOrderNoList) {
        String url = "/PsWorkOrderSmtCtrl/getByWorkOrders";
        String reStr = MicroServiceRestUtil.invokeService(MicroServiceNameEum.PLANSCHEDULE, MicroServiceNameEum.VERSION,
                MicroServiceNameEum.SENDTYPEPOST, url, JSON.toJSONString(workOrderNoList), MESHttpHelper.getHttpRequestHeader());
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(reStr);
        if (StringUtils.isBlank(bo)) {
            return new ArrayList<>();
        }
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<PsWorkOrderSmtDTO>>() {
        });
    }

    /**
     * 根据条件查询指令信息
     *
     * @param map
     * @return
     */
    public static List<PsWorkOrderDTO> getWorkOrderInfoByQuery(Map map) {
        // 点对点调用服务
        String url = "/PS/getWorkOrderInfo";
        String msg = MicroServiceRestUtil.invokeService(MicroServiceNameEum.PLANSCHEDULE, MicroServiceNameEum.VERSION,
                MicroServiceNameEum.SENDTYPEGET, url, JacksonJsonConverUtil.beanToJson(map), MESHttpHelper.getHttpRequestHeader());
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
        if (StringUtils.isBlank(bo)) {
            return new ArrayList<>();
        }
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<PsWorkOrderDTO>>() {
        });
    }

    /**
     * 根据 workOrderNo 读取 workorderbasic
     *
     * @param workOrderNo
     * @return
     * @throws Exception
     */
    public static PsWorkOrderBasic getTaskByWorkOrderNo(String workOrderNo) {
        // 点对点调用服务
        Map<String, String> map = new HashMap<>();
        map.put("workOrderNo", workOrderNo);
        String getUrl = "/PsWorkOrderBasicCtrl/getTaskByWorkOrderNo";
        String msg = MicroServiceRestUtil.invokeService(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEGET, getUrl, JacksonJsonConverUtil.beanToJson(map), MESHttpHelper.getHttpRequestHeader());
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
        if (StringUtils.isBlank(bo)) {
            return null;
        }
        return JacksonJsonConverUtil.jsonToBean(bo, PsWorkOrderBasic.class);
    }

    /**
     * 根据料单代码获取任务清单中数量
     *
     */
    public static List<CfInventoryDTO> getTaskAndItemUsageCountByProCodeAndItemNo(String productCode) {
        // 点对点调用服务
        Map<String, String> map = new HashMap<>();
        map.put("itemNo", productCode);
        String getUrl = "/PS/getTaskAndItemUsageCountByProCodeAndItemNo";
        String msg = MicroServiceRestUtil.invokeService(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEGET, getUrl, JacksonJsonConverUtil.beanToJson(map), MESHttpHelper.getHttpRequestHeader());
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
        if (StringUtils.isBlank(bo)) {
            return new ArrayList<>();
        }
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<CfInventoryDTO>>() {
        });
    }

    /**
     * 根据批次和线体获取前加工信息
     *
     */
    public static Page<BsBomHierarchicalDetail> queryPreProcessInfo(String attr1, String lineCode) {
        Page<BsBomHierarchicalDetail> result = new Page<>();
        PsWorkOrderBasicDTO dto = new PsWorkOrderBasicDTO();
        dto.setSourceTask(attr1);
        dto.setLineCode(lineCode);
        dto.setRows(Constant.LONG_5000);
        dto.setPage(Constant.LONG_1);
        String url = "/PsWorkOrderBasicCtrl/queryPreProcessInfo";
        String reStr = MicroServiceRestUtil.invokeService(MicroServiceNameEum.PLANSCHEDULE, MicroServiceNameEum.VERSION,
                MicroServiceNameEum.SENDTYPEPOST, url, JSON.toJSONString(dto), MESHttpHelper.getHttpRequestHeader());
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(reStr);
        if (StringUtils.isBlank(bo)) {
            return result;
        }
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<Page<BsBomHierarchicalDetail>>() {
        });
    }

    /**
     * 获取批次附加信息
     *
     * @param prodplanId
     * @return
     * @throws MesBusinessException
     */
    public static List<String> getExtraAttr(String prodplanId, String extraType) throws MesBusinessException {
        String postUrl = "/PS/getExtraAttr";
        PsTaskExtraDTO psTaskExtraDTO = new PsTaskExtraDTO();
        psTaskExtraDTO.setExtraType(extraType);
        psTaskExtraDTO.setProdplanId(prodplanId);
        Map<String, String> requestHeader = MESHttpHelper.getHttpRequestHeader();
        String msg = MicroServiceRestUtil.invokeService(MicroServiceNameEum.PLANSCHEDULE, MicroServiceNameEum.VERSION,
                MicroServiceNameEum.SENDTYPEGET, postUrl, JacksonJsonConverUtil.beanToJson(psTaskExtraDTO), requestHeader);
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
        if (StringUtils.isBlank(bo)) {
            return new ArrayList<>();
        }
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<String>>() {
        });
    }

    /**
     * 获取指定批次SMT指令信息
     * @param params
     * @return
     */
    public static List<PsEntityPlanBasic> selectSmtWorkOrderBySourceTask(Map<String, String> params) {
        // 点对点调用服务
        String url = "/PS/selectSmtWorkOrderBySourceTask";
        String msg = MicroServiceRestUtil.invokeService(MicroServiceNameEum.PLANSCHEDULE, MicroServiceNameEum.VERSION,
                MicroServiceNameEum.SENDTYPEGET, url, JacksonJsonConverUtil.beanToJson(params), MESHttpHelper.getHttpRequestHeader());
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
        if (StringUtils.isBlank(bo)) {
            return new ArrayList<>();
        }
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<PsEntityPlanBasic>>() {
        });
    }

    public static boolean checkIsPartTask(String prodplanId) {
        Map<String, String> params = new HashMap<>();
        params.put("prodplanId",prodplanId);
        // 点对点调用服务
        String url = "/PS/checkIsPartTask";
        String msg = MicroServiceRestUtil.invokeService(MicroServiceNameEum.PLANSCHEDULE, MicroServiceNameEum.VERSION,
                MicroServiceNameEum.SENDTYPEPOST, url, JacksonJsonConverUtil.beanToJson(params), MESHttpHelper.getHttpRequestHeader());
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
        if (StringUtils.isBlank(bo)) {
            return false;
        }
        return Boolean.parseBoolean(bo);
    }

    /**
     * 根据指令集查询料单代码
     *
     * @param workOrderNoList
     * @return
     * @throws Exception
     */
    public static List<PsWorkOrderBasicDTO> getItemNoByWorkOrderNoList(List<String> workOrderNoList) {
        if (CollectionUtils.isEmpty(workOrderNoList)) {
            return new ArrayList<>();
        }
        String postUrl = "/PsWorkOrderBasicCtrl/getItemNoByWorkOrderNoList";
        String msg = MicroServiceRestUtil.invokeService(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST,
                postUrl, JSON.toJSONString(workOrderNoList), MESHttpHelper.getHttpRequestHeader());
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
        if (StringUtils.isBlank(bo)) {
            return new ArrayList<>();
        }
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<PsWorkOrderBasicDTO>>() {
        });
    }

    /**
     * 批量查询任务额外信息
     * @param psTaskExtraDTOS
     * @return
     * @throws MesBusinessException
     */
    public static List<PsTaskExtraDTO> getPsTaskExtras(List<PsTaskExtraDTO> psTaskExtraDTOS) throws MesBusinessException {
        String postUrl = "/PS/getPsTaskExtras";
        Map<String, String> requestHeader = MESHttpHelper.getHttpRequestHeader();
        String msg = MicroServiceRestUtil.invokeService(MicroServiceNameEum.PLANSCHEDULE,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST,
                postUrl, JacksonJsonConverUtil.beanToJson(psTaskExtraDTOS), requestHeader);
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
        if (StringUtils.isBlank(bo)) {
            return new ArrayList<>();
        }
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<PsTaskExtraDTO>>() {
        });
    }

}
