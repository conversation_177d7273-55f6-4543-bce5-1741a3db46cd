package com.zte.interfaces;

import java.math.BigDecimal;
import com.zte.common.model.MessageId;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.common.annotation.OpenApi;
import com.zte.springbootframe.common.annotation.RecordLogAnnotation;
import com.zte.springbootframe.common.consts.SysConst;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import io.swagger.annotations.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.model.RetCode;

import com.zte.interfaces.dto.WarehouseEntryErpInfoEntityDTO;
import com.zte.application.WarehouseEntryErpInfoService;

import javax.servlet.http.HttpServletRequest;



/**
 * 入库单推送ERP失败补偿表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-01 14:28:32
 */
@RestController
@RequestMapping("/warehouseEntryErpInfo")
public class WarehouseEntryErpInfoController {
    @Autowired
    private WarehouseEntryErpInfoService warehouseEntryErpInfoService;

    /**
     * 分页列表
     */
    @ApiOperation("分页列表")
    @ApiResponses({ @ApiResponse(code = 400, message = "请求参数没填好"),
            @ApiResponse(code = 404, message = "请求路径没有或页面跳转路径不对") })
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "id", dataType = "java.lang.String",
                    required = false, value = "id", defaultValue = "String"),
            @ApiImplicitParam(paramType = "query", name = "erpId", dataType = "java.lang.String",
                    required = false, value = "推送ERP幂等ID", defaultValue = "String"),
            @ApiImplicitParam(paramType = "query", name = "erpParam", dataType = "java.lang.String",
                    required = false, value = "推送ERP参数", defaultValue = "String"),
            @ApiImplicitParam(paramType = "query", name = "errorTimes", dataType = "java.lang.String",
                    required = false, value = "推送ERP失败次数", defaultValue = "BigDecimal"),
            @ApiImplicitParam(paramType = "query", name = "createdBy", dataType = "java.lang.String",
                    required = false, value = "创建人", defaultValue = "String"),
            @ApiImplicitParam(paramType = "query", name = "createdDate", dataType = "java.lang.String",
                    required = false, value = "createdDate", defaultValue = "Date"),
            @ApiImplicitParam(paramType = "query", name = "lineId", dataType = "java.lang.String",
                    required = false, value = "线体主键", defaultValue = ""),
            @ApiImplicitParam(paramType = "query", name = "sort", dataType = "String", required = false,
                    value = "排序字段(值areaId或者areaCode)", defaultValue = ""),
            @ApiImplicitParam(paramType = "query", name = "order", dataType = "String", required = false,
                    value = "排序方式(默认升序,设为desc时降序)", defaultValue = ""),
            @ApiImplicitParam(paramType = "query", name = "page", dataType = "String", required = false,
                    value = "请求的页码", defaultValue = ""),
            @ApiImplicitParam(paramType = "query", name = "rows", dataType = "String", required = false,
                    value = "每页条数", defaultValue = "") })
    @RequestMapping(value = "/pagelist", method = RequestMethod.GET,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ServiceData pageList(HttpServletRequest request, WarehouseEntryErpInfoEntityDTO params) throws Exception{
        ServiceData ret = new ServiceData();
        String factoryId = request.getHeader(SysConst.HTTP_HEADER_X_FACTORY_ID);
        String empNo = request.getHeader(SysConst.HTTP_HEADER_X_EMP_NO);
        if (StringUtils.isEmpty(factoryId)) {
            ret.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.FACTORY_ID_IS_NULL));
            return ret;
        }
        params.setFactoryId(new BigDecimal(factoryId));

        Page<WarehouseEntryErpInfoEntityDTO> pageRow = warehouseEntryErpInfoService.pageList(params);
        ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        ret.setBo(pageRow);
        return ret;
    }



    /**
     * 修改
     */
    @ApiOperation("修改")
    @ApiResponses({ @ApiResponse(code = 400, message = "请求参数没填好"),
            @ApiResponse(code = 404, message = "请求路径没有或页面跳转路径不对") })
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "id", dataType = "java.lang.String",
                    required = false, value = "id", defaultValue = "String"),
            @ApiImplicitParam(paramType = "query", name = "erpId", dataType = "java.lang.String",
                    required = false, value = "推送ERP幂等ID", defaultValue = "String"),
            @ApiImplicitParam(paramType = "query", name = "erpParam", dataType = "java.lang.String",
                    required = false, value = "推送ERP参数", defaultValue = "String"),
            @ApiImplicitParam(paramType = "query", name = "errorTimes", dataType = "java.lang.String",
                    required = false, value = "推送ERP失败次数", defaultValue = "BigDecimal"),
            @ApiImplicitParam(paramType = "query", name = "createdBy", dataType = "java.lang.String",
                    required = false, value = "创建人", defaultValue = "String"),
            @ApiImplicitParam(paramType = "query", name = "createdDate", dataType = "java.lang.String",
                    required = false, value = "createdDate", defaultValue = "Date"),
            @ApiImplicitParam(paramType = "query", name = "lineId", dataType = "java.lang.String",
                    required = false, value = "结尾凑数用", defaultValue = "")})
    @RequestMapping(value = "/update", method = RequestMethod.POST,consumes = MediaType.APPLICATION_JSON_VALUE,produces = MediaType.APPLICATION_JSON_VALUE)
    public ServiceData update(HttpServletRequest request, @RequestBody WarehouseEntryErpInfoEntityDTO dto) throws Exception{
        ServiceData ret = new ServiceData();
        String factoryId = request.getHeader(SysConst.HTTP_HEADER_X_FACTORY_ID);
        String empNo = request.getHeader(SysConst.HTTP_HEADER_X_EMP_NO);
        if (StringUtils.isEmpty(factoryId)) {
            ret.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.FACTORY_ID_IS_NULL));
            return ret;
        }
        dto.setFactoryId(new BigDecimal(factoryId));

        int result = warehouseEntryErpInfoService.update(dto);
        ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        ret.setBo(result);
        return ret;
    }

    /**
     * 定时补偿子卡入库单ERP信息
     */
    @ApiOperation("定时补偿子卡入库单ERP信息")
    @RequestMapping(value = "/excuteSubCardErpInfo", method = RequestMethod.POST,consumes = MediaType.APPLICATION_JSON_VALUE,produces = MediaType.APPLICATION_JSON_VALUE)
    public ServiceData excuteSubCardErpInfo(HttpServletRequest request, @RequestBody WarehouseEntryErpInfoEntityDTO dto) throws Exception{
        ServiceData ret = new ServiceData();
        String empNo = request.getHeader(SysConst.HTTP_HEADER_X_EMP_NO);
        dto.setCreatedBy(empNo);
        warehouseEntryErpInfoService.excuteSubCardErpInfo(dto);
        ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        return ret;
    }

    /**
     * 自动扫描接收
     *
     * @param sn
     * @return
     * @throws Exception
     */
    @ApiOperation("自动扫描接收")
    @RecordLogAnnotation("自动扫描接收")
    @GetMapping("/automaticsSanReceiveWarehouseEntryInfo")
    @OpenApi(name = "自动扫描接收", provider = "IMES")
    public ServiceData automaticsSanReceiveWarehouseEntryInfo( @RequestParam @ApiParam("条码") String sn) throws Exception {
        warehouseEntryErpInfoService.automaticsSanReceiveWarehouseEntryInfo(sn);
        return ServiceDataBuilderUtil.success();
    }


}
