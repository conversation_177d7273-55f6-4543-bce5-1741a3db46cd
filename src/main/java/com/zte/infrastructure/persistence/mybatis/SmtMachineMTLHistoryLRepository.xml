<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.SmtMachineMTLHistoryLRepository">
    <resultMap id="BaseResultMap" type="com.zte.domain.model.SmtMachineMTLHistoryL">
        <id column="LINE_ID" jdbcType="VARCHAR" property="lineId" />
        <result column="HEADER_ID" jdbcType="VARCHAR" property="headerId" />
        <result column="LINE_CODE" jdbcType="VARCHAR" property="lineCode" />
        <result column="WORK_ORDER" jdbcType="VARCHAR" property="workOrder" />
        <result column="ITEM_CODE" jdbcType="VARCHAR" property="itemCode" />
        <result column="ITEM_NAME" jdbcType="VARCHAR" property="itemName" />
        <result column="MACHINE_NO" jdbcType="VARCHAR" property="machineNo" />
        <result column="MODULE_NO" jdbcType="VARCHAR" property="moduleNo" />
        <result column="LOCATION_NO" jdbcType="VARCHAR" property="locationNo" />
        <result column="TRACK_NO" jdbcType="VARCHAR" property="trackNo" />
        <result column="FEEDER_NO" jdbcType="VARCHAR" property="feederNo" />
        <result column="OBJECT_ID" jdbcType="VARCHAR" property="objectId" />
        <result column="QTY" jdbcType="DECIMAL" property="qty" />
        <result column="ENABLED_FLAG" jdbcType="VARCHAR" property="enabledFlag" />
        <result column="PICK_STATUS" jdbcType="VARCHAR" property="pickStatus" />
        <result column="MOUNT_TYPE" jdbcType="VARCHAR" property="mountType" />
        <result column="OPERATE_MSG" jdbcType="VARCHAR" property="operateMsg" />
        <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
        <result column="CREATE_USER" jdbcType="VARCHAR" property="createUser" />
        <result column="MAC_ADDR" jdbcType="VARCHAR" property="macAddr" />
        <result column="ORG_ID" jdbcType="DECIMAL" property="orgId" />
        <result column="FACTORY_ID" jdbcType="DECIMAL" property="factoryId" />
        <result column="ENTITY_ID" jdbcType="DECIMAL" property="entityId" />
        <result column="LAST_UPDATED_DATE" jdbcType="TIMESTAMP" property="lastUpdatedDate" />
        <result column="LAST_UPDATED_BY" jdbcType="VARCHAR" property="lastUpdatedBy" />
        <result column="ATTR1" jdbcType="VARCHAR" property="attr1" />
        <result column="ATTR2" jdbcType="VARCHAR" property="attr2" />
        <result column="ATTR3" jdbcType="VARCHAR" property="attr3" />
        <result column="CFG_HEADER_ID" jdbcType="VARCHAR" property="cfgHeaderId" />
        <result column="IS_LEAD" jdbcType="VARCHAR" property="isLead" />
        <result column="AVL" jdbcType="VARCHAR" property="avl" />
        <result column="POLAR_INFO" jdbcType="VARCHAR" property="polarInfo" />
        <result column="SOURCE_BATCH_CODE" jdbcType="VARCHAR" property="sourceBatchCode" />
        <result column="TRACING_QTY" jdbcType="DECIMAL" property="tracingQty" />
        <result column="COMBINATION" jdbcType="VARCHAR" property="combination" />
        <result column="LPN" jdbcType="VARCHAR" property="lpn" />
		<result column="PROCESS_STATUS" jdbcType="VARCHAR" property="processStatus" />
		<result column="material_rack" jdbcType="VARCHAR" property="materialRack" />
		<result column="comp_duration" jdbcType="INTEGER" property="compDuration" />
		<result column="opt_duration" jdbcType="INTEGER" property="optDuration" />
        <result column="qc_confirm_user" jdbcType="VARCHAR" property="qcConfirmUser" />
        <result column="qc_confirm_date" jdbcType="TIMESTAMP" property="qcConfirmDate" />
        <result column="BOM_DIRECTION" jdbcType="VARCHAR" property="bomDirection" />
        <result column="ITEM_DIRECTION" jdbcType="VARCHAR" property="itemDirection" />
    </resultMap>

    <resultMap id="BaseResultDtoMap" type="com.zte.interfaces.dto.SmtMachineMTLHistoryLDTO">
        <id column="LINE_ID" jdbcType="VARCHAR" property="lineId" />
        <result column="HEADER_ID" jdbcType="VARCHAR" property="headerId" />
        <result column="LINE_CODE" jdbcType="VARCHAR" property="lineCode" />
        <result column="WORK_ORDER" jdbcType="VARCHAR" property="workOrder" />
        <result column="ITEM_CODE" jdbcType="VARCHAR" property="itemCode" />
        <result column="ITEM_NAME" jdbcType="VARCHAR" property="itemName" />
        <result column="MACHINE_NO" jdbcType="VARCHAR" property="machineNo" />
        <result column="MODULE_NO" jdbcType="VARCHAR" property="moduleNo" />
        <result column="LOCATION_NO" jdbcType="VARCHAR" property="locationNo" />
        <result column="TRACK_NO" jdbcType="VARCHAR" property="trackNo" />
        <result column="FEEDER_NO" jdbcType="VARCHAR" property="feederNo" />
        <result column="OBJECT_ID" jdbcType="VARCHAR" property="objectId" />
        <result column="QTY" jdbcType="DECIMAL" property="qty" />
        <result column="ENABLED_FLAG" jdbcType="VARCHAR" property="enabledFlag" />
        <result column="PICK_STATUS" jdbcType="VARCHAR" property="pickStatus" />
        <result column="MOUNT_TYPE" jdbcType="VARCHAR" property="mountType" />
        <result column="OPERATE_MSG" jdbcType="VARCHAR" property="operateMsg" />
        <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
        <result column="CREATE_USER" jdbcType="VARCHAR" property="createUser" />
        <result column="MAC_ADDR" jdbcType="VARCHAR" property="macAddr" />
        <result column="ORG_ID" jdbcType="DECIMAL" property="orgId" />
        <result column="FACTORY_ID" jdbcType="DECIMAL" property="factoryId" />
        <result column="ENTITY_ID" jdbcType="DECIMAL" property="entityId" />
        <result column="LAST_UPDATED_DATE" jdbcType="TIMESTAMP" property="lastUpdatedDate" />
        <result column="LAST_UPDATED_BY" jdbcType="VARCHAR" property="lastUpdatedBy" />
        <result column="ATTR1" jdbcType="VARCHAR" property="attr1" />
        <result column="ATTR2" jdbcType="VARCHAR" property="attr2" />
        <result column="ATTR3" jdbcType="VARCHAR" property="attr3" />
        <result column="CFG_HEADER_ID" jdbcType="VARCHAR" property="cfgHeaderId" />
        <result column="IS_LEAD" jdbcType="VARCHAR" property="isLead" />
        <result column="AVL" jdbcType="VARCHAR" property="avl" />
        <result column="POLAR_INFO" jdbcType="VARCHAR" property="polarInfo" />
        <result column="SOURCE_BATCH_CODE" jdbcType="VARCHAR" property="sourceBatchCode" />
        <result column="RE_CHECK_DATE" jdbcType="TIMESTAMP" property="reCheckDate" />
        <result column="TRACING_QTY" jdbcType="DECIMAL" property="tracingQty" />
        <result column="COMBINATION" jdbcType="VARCHAR" property="combination" />
        <result column="LPN" jdbcType="VARCHAR" property="lpn" />
    </resultMap>

    <resultMap id="workLoadResultMap" type="com.zte.interfaces.dto.SmtMachineMTLHistoryWorkLoadDTO">
        <result column="CREATE_USER" jdbcType="VARCHAR" property="createUser" />
        <result column="LINE_CODE" jdbcType="VARCHAR" property="lineCode" />
        <result column="WORK_ORDER" jdbcType="VARCHAR" property="workOrder" />
        <result column="MOUNT_TYPE" jdbcType="VARCHAR" property="mountType" />
        <result column="SUCCESS_CNT" jdbcType="DECIMAL" property="successCnt" />
        <result column="FAIL_CNT" jdbcType="DECIMAL" property="failCnt" />
        <result column="ORG_ID" jdbcType="DECIMAL" property="orgId" />
        <result column="FACTORY_ID" jdbcType="DECIMAL" property="factoryId" />
        <result column="ENTITY_ID" jdbcType="DECIMAL" property="entityId" />
    </resultMap>

    <sql id="Base_Column_List">
        LINE_ID,HEADER_ID,LINE_CODE,WORK_ORDER,ITEM_CODE,ITEM_NAME,MACHINE_NO,MODULE_NO,LOCATION_NO,
        TRACK_NO,FEEDER_NO,OBJECT_ID,QTY,ENABLED_FLAG,PICK_STATUS,MOUNT_TYPE,OPERATE_MSG,CREATE_DATE,
        CREATE_USER,MAC_ADDR,ORG_ID,FACTORY_ID,ENTITY_ID, ATTR1, ATTR2, ATTR3, LAST_UPDATED_DATE,
        LAST_UPDATED_BY, CFG_HEADER_ID,IS_LEAD,AVL,POLAR_INFO,SOURCE_BATCH_CODE,TRACING_QTY,COMBINATION,LPN
    </sql>

    <!-- 有选择地新增SmtMachineMTLHistoryL -->
    <insert id="insertSmtMachineMTLHistoryLSelective" parameterType="com.zte.domain.model.SmtMachineMTLHistoryL">
        insert into SMT_MACHINE_MTL_SCAN_HISTORY_L
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="lineId != null">
                LINE_ID,
            </if>
            <if test="headerId != null">
                HEADER_ID,
            </if>
            <if test="lineCode != null">
                LINE_CODE,
            </if>
            <if test="workOrder != null">
                WORK_ORDER,
            </if>
            <if test="itemCode != null">
                ITEM_CODE,
            </if>
            <if test="itemName != null">
                ITEM_NAME,
            </if>
            <if test="machineNo != null">
                MODULE_NO,
            </if>
            <if test="locationNo != null">
                LOCATION_NO,
            </if>
            <if test="trackNo != null">
                TRACK_NO,
            </if>
            <if test="feederNo != null">
                FEEDER_NO,
            </if>
            <if test="objectId != null">
                OBJECT_ID,
            </if>
            <if test="qty != null">
                QTY,
            </if>
            <if test="enabledFlag != null">
                ENABLED_FLAG,
            </if>
            <if test="pickStatus != null">
                PICK_STATUS,
            </if>
            <if test="mountType != null">
                MOUNT_TYPE,
            </if>
            <if test="operateMsg != null">
                OPERATE_MSG,
            </if>
            <if test="createDate != null">
                CREATE_DATE,
            </if>
            <if test="createUser != null">
                CREATE_USER,
            </if>
            <if test="macAddr != null">
                MAC_ADDR,
            </if>
            <if test="orgId != null">
                ORG_ID,
            </if>
            <if test="factoryId != null">
                FACTORY_ID,
            </if>
            <if test="entityId != null">
                ENTITY_ID,
            </if>
            <if test="lastUpdatedDate != null">
                LAST_UPDATED_DATE,
            </if>
            <if test="lastUpdatedBy != null">
                LAST_UPDATED_BY,
            </if>
            <if test="attr1 != null">
                ATTR1,
            </if>
            <if test="attr2 != null">
                ATTR2,
            </if>
            <if test="attr3 != null">
                ATTR3,
            </if>
            <if test="cfgHeaderId != null">
                CFG_HEADER_ID,
            </if>
            <if test="isLead != null">
                IS_LEAD,
            </if>
            <if test="avl != null">
                AVL,
            </if>
            <if test="polarInfo != null">
                POLAR_INFO,
            </if>
            <if test="sourceBatchCode != null">
                SOURCE_BATCH_CODE,
            </if>
            <if test="tracingQty != null">
                TRACING_QTY,
            </if>
        </trim>

        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="lineId != null">
                #{lineId,jdbcType=VARCHAR},
            </if>
            <if test="headerId != null">
                #{headerId,jdbcType=VARCHAR},
            </if>
            <if test="lineCode != null">
                #{lineCode,jdbcType=VARCHAR},
            </if>
            <if test="workOrder != null">
                #{workOrder,jdbcType=VARCHAR},
            </if>
            <if test="itemCode != null">
                #{itemCode,jdbcType=VARCHAR},
            </if>
            <if test="itemName != null">
                #{itemName,jdbcType=VARCHAR},
            </if>
            <if test="machineNo != null">
                #{machineNo,jdbcType=VARCHAR},
            </if>
            <if test="moduleNo != null">
                #{moduleNo,jdbcType=VARCHAR},
            </if>
            <if test="locationNo != null">
                #{locationNo,jdbcType=VARCHAR},
            </if>
            <if test="trackNo != null">
                #{trackNo,jdbcType=VARCHAR},
            </if>
            <if test="feederNo != null">
                #{feederNo,jdbcType=VARCHAR},
            </if>
            <if test="objectId != null">
                #{objectId,jdbcType=VARCHAR},
            </if>
            <if test="qty != null">
                #{qty,jdbcType=DECIMAL},
            </if>
            <if test="enabledFlag != null">
                #{enabledFlag,jdbcType=VARCHAR},
            </if>
            <if test="pickStatus != null">
                #{pickStatus,jdbcType=VARCHAR},
            </if>
            <if test="mountType != null">
                #{mountType,jdbcType=VARCHAR},
            </if>
            <if test="operateMsg != null">
                #{operateMsg,jdbcType=VARCHAR},
            </if>
            <if test="createDate != null">
                #{createDate,jdbcType=TIMESTAMP},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="macAddr != null">
                #{macAddr,jdbcType=VARCHAR},
            </if>
            <if test="orgId != null">
                #{orgId,jdbcType=DECIMAL},
            </if>
            <if test="factoryId != null">
                #{factoryId,jdbcType=DECIMAL},
            </if>
            <if test="entityId != null">
                #{entityId,jdbcType=DECIMAL},
            </if>
            <if test="lastUpdatedDate != null">
                #{lastUpdatedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdatedBy != null">
                #{lastUpdatedBy,jdbcType=VARCHAR},
            </if>
            <if test="attr1 != null">
                #{attr1,jdbcType=VARCHAR},
            </if>
            <if test="attr2 != null">
                #{attr2,jdbcType=VARCHAR},
            </if>
            <if test="attr3 != null">
                #{attr3,jdbcType=VARCHAR},
            </if>
            <if test="cfgHeaderId != null">
                #{cfgHeaderId,jdbcType=VARCHAR},
            </if>
            <if test="isLead != null">
                #{isLead,jdbcType=VARCHAR},
            </if>
            <if test="avl != null">
                #{avl,jdbcType=VARCHAR},
            </if>
            <if test="polarInfo != null">
                #{polarInfo,jdbcType=VARCHAR},
            </if>
            <if test="sourceBatchCode != null">
                #{sourceBatchCode,jdbcType=VARCHAR},
            </if>
            <if test="tracingQty != null">
                #{tracingQty,jdbcType=DECIMAL},
            </if>
        </trim>
    </insert>

    <!-- 新增BsItemInfo -->
    <insert id="insertSmtMachineMTLHistoryL" parameterType="com.zte.domain.model.SmtMachineMTLHistoryL">
        insert into SMT_MACHINE_MTL_SCAN_HISTORY_L (LINE_ID,HEADER_ID,LINE_CODE,WORK_ORDER,ITEM_CODE,ITEM_NAME,MACHINE_NO,MODULE_NO,
        LOCATION_NO,TRACK_NO,FEEDER_NO,OBJECT_ID,QTY,ENABLED_FLAG,PICK_STATUS,MOUNT_TYPE,OPERATE_MSG,CREATE_DATE,CREATE_USER,
        MAC_ADDR,ORG_ID,FACTORY_ID,ENTITY_ID, ATTR1, ATTR2, ATTR3, LAST_UPDATED_DATE, LAST_UPDATED_BY, CFG_HEADER_ID,IS_LEAD,AVL,
        POLAR_INFO,SOURCE_BATCH_CODE, TRACING_QTY,COMBINATION,LPN,unbind_reel_id,unbind_feeder_no,material_rack,comp_duration,
        opt_duration,collecting_line_id,BOM_DIRECTION,ITEM_DIRECTION)
        values (#{lineId,jdbcType=VARCHAR}, #{headerId,jdbcType=VARCHAR}, #{lineCode,jdbcType=VARCHAR}, #{workOrder,jdbcType=VARCHAR},
        #{itemCode,jdbcType=VARCHAR}, #{itemName,jdbcType=VARCHAR}, #{machineNo,jdbcType=VARCHAR}, #{moduleNo,jdbcType=VARCHAR},
        #{locationNo,jdbcType=VARCHAR}, #{trackNo,jdbcType=VARCHAR}, #{feederNo,jdbcType=VARCHAR}, #{objectId,jdbcType=VARCHAR},
        #{qty,jdbcType=DECIMAL}, #{enabledFlag,jdbcType=VARCHAR}, #{pickStatus,jdbcType=VARCHAR}, #{mountType,jdbcType=VARCHAR},
        #{operateMsg,jdbcType=VARCHAR},SYSDATE, #{createUser,jdbcType=VARCHAR}, #{macAddr,jdbcType=VARCHAR},
        #{orgId,jdbcType=DECIMAL}, #{factoryId,jdbcType=DECIMAL}, #{entityId,jdbcType=DECIMAL}, #{attr1,jdbcType=VARCHAR},
        #{attr2,jdbcType=VARCHAR}, #{attr3,jdbcType=VARCHAR}, SYSDATE, #{lastUpdatedBy,jdbcType=VARCHAR},
        #{cfgHeaderId,jdbcType=VARCHAR},#{isLead,jdbcType=VARCHAR},#{avl,jdbcType=VARCHAR},#{polarInfo,jdbcType=VARCHAR},
        #{sourceBatchCode,jdbcType=VARCHAR},#{tracingQty,jdbcType=DECIMAL},
        #{combination,jdbcType=VARCHAR},#{lpn,jdbcType=VARCHAR},
        #{unbindReelId,jdbcType=VARCHAR},#{unbindFeederNo,jdbcType=VARCHAR},#{materialRack,jdbcType=VARCHAR},#{compDuration,jdbcType=INTEGER},
        #{optDuration,jdbcType=INTEGER},#{collectingLineId,jdbcType=VARCHAR},
        #{bomDirection,jdbcType=VARCHAR},#{itemDirection,jdbcType=VARCHAR})
    </insert>

    <!-- 根据lineId选择SmtMachineMTLHistoryL -->
    <select id="selectSmtMachineMTLHistoryLById" parameterType="com.zte.domain.model.SmtMachineMTLHistoryL" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from SMT_MACHINE_MTL_SCAN_HISTORY_L
        where LINE_ID = #{lineId,jdbcType=VARCHAR}
    </select>

    <select id="getHistoryQty" parameterType="com.zte.domain.model.SmtMachineMTLHistoryL" resultMap="BaseResultMap">
        select t.item_code,t.machine_no,t.module_no,t.location_no,sum(t.qty) qty
        from smt_machine_mtl_scan_history_l t where
        t.header_id in
        (select a.header_id from smt_machine_mtl_scan_history_h a where 1=1 and a.enabled_flag='Y'
        <if test="workOrder != null and workOrder != ''"> and a.work_order = #{workOrder,jdbcType=VARCHAR}</if>
        <if test="mountType != null and mountType != ''"> and a.mount_type in (${mountType})</if>
        <if test="(workOrder == null or workOrder == '') and (mountType == null or mountType == '') "> and 1=2</if>)
        and t.enabled_flag='Y'
        and t.operate_msg is null
        group by t.item_code,t.machine_no,t.module_no,t.location_no
    </select>

    <!-- 删除BsItemInfo：根据Id -->
    <delete id="deleteSmtMachineMTLHistoryLById" parameterType="com.zte.domain.model.SmtMachineMTLHistoryL">
        delete from SMT_MACHINE_MTL_SCAN_HISTORY_L
        where LINE_ID = #{lineId,jdbcType=VARCHAR}
    </delete>

    <!-- 更新BsItemInfo：根据Id -->
    <update id="updateSmtMachineMTLHistoryLById" parameterType="com.zte.domain.model.SmtMachineMTLHistoryL">
        update SMT_MACHINE_MTL_SCAN_HISTORY_L
        set
        HEADER_ID = #{headerId,jdbcType=VARCHAR},
        LINE_CODE = #{lineCode,jdbcType=VARCHAR},
        WORK_ORDER = #{workOrder,jdbcType=VARCHAR},
        ITEM_CODE = #{itemCode,jdbcType=VARCHAR},
        ITEM_NAME = #{itemName,jdbcType=VARCHAR},
        MACHINE_NO = #{machineNo,jdbcType=VARCHAR},
        MODULE_NO = #{moduleNo,jdbcType=VARCHAR},
        LOCATION_NO = #{locationNo,jdbcType=VARCHAR},
        TRACK_NO = #{trackNo,jdbcType=VARCHAR},
        FEEDER_NO = #{feederNo,jdbcType=VARCHAR},
        OBJECT_ID = #{objectId,jdbcType=VARCHAR},
        QTY = #{qty,jdbcType=DECIMAL},
        ENABLED_FLAG = #{enabledFlag,jdbcType=VARCHAR},
        PICK_STATUS = #{pickStatus,jdbcType=VARCHAR},
        MOUNT_TYPE = #{mountType,jdbcType=VARCHAR},
        OPERATE_MSG = #{operateMsg,jdbcType=VARCHAR},
        CREATE_DATE = #{createDate,jdbcType=TIMESTAMP},
        CREATE_USER = #{createUser,jdbcType=VARCHAR},
        MAC_ADDR = #{macAddr,jdbcType=VARCHAR},
        ORG_ID = #{orgId,jdbcType=DECIMAL},
        FACTORY_ID = #{factoryId,jdbcType=DECIMAL},
        ENTITY_ID = #{entityId,jdbcType=DECIMAL},
        ATTR1 = #{attr1,jdbcType=VARCHAR},
        ATTR2 = #{attr2,jdbcType=VARCHAR},
        ATTR3 = #{attr3,jdbcType=VARCHAR},
        LAST_UPDATED_DATE = #{lastUpdatedDate,jdbcType=TIMESTAMP},
        LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR},
        CFG_HEADER_ID = #{cfgHeaderId,jdbcType=VARCHAR},
        IS_LEAD = #{isLead,jdbcType=VARCHAR},
        AVL = #{avl,jdbcType=VARCHAR},
        POLAR_INFO = #{polarInfo,jdbcType=VARCHAR},
        SOURCE_BATCH_CODE = #{sourceBatchCode,jdbcType=VARCHAR},
        TRACING_QTY = #{tracingQty,jdbcType=DECIMAL}
        where LINE_ID = #{lineId,jdbcType=VARCHAR}
    </update>

    <!-- 更新BsItemInfo：根据IdSelective -->
    <update id="updateSmtMachineMTLHistoryLByIdSelective" parameterType="com.zte.domain.model.SmtMachineMTLHistoryL">
        update SMT_MACHINE_MTL_SCAN_HISTORY_L
        <set>
            <if test="headerId != null">
                HEADER_ID = #{headerId,jdbcType=VARCHAR},
            </if>
            <if test="lineCode != null">
                LINE_CODE = #{lineCode,jdbcType=VARCHAR},
            </if>
            <if test="workOrder != null">
                WORK_ORDER = #{workOrder,jdbcType=VARCHAR},
            </if>
            <if test="itemCode != null">
                ITEM_CODE = #{itemCode,jdbcType=VARCHAR},
            </if>
            <if test="itemName != null">
                ITEM_NAME = #{itemName,jdbcType=VARCHAR},
            </if>
            <if test="machineNo != null">
                MACHINE_NO = #{machineNo,jdbcType=VARCHAR},
            </if>
            <if test="moduleNo != null">
                MODULE_NO = #{moduleNo,jdbcType=VARCHAR},
            </if>
            <if test="locationNo != null">
                LOCATION_NO = #{locationNo,jdbcType=VARCHAR},
            </if>
            <if test="trackNo != null">
                TRACK_NO = #{trackNo,jdbcType=VARCHAR},
            </if>
            <if test="feederNo != null">
                FEEDER_NO = #{feederNo,jdbcType=VARCHAR},
            </if>
            <if test="objectId != null">
                OBJECT_ID = #{objectId,jdbcType=VARCHAR},
            </if>
            <if test="qty != null">
                QTY = #{qty,jdbcType=DECIMAL},
            </if>
            <if test="enabledFlag != null">
                ENABLED_FLAG = #{enabledFlag,jdbcType=DECIMAL},
            </if>
            <if test="pickStatus != null">
                PICK_STATUS = #{pickStatus,jdbcType=VARCHAR},
            </if>
            <if test="mountType != null">
                MOUNT_TYPE = #{mountType,jdbcType=VARCHAR},
            </if>
            <if test="operateMsg != null">
                OPERATE_MSG = #{operateMsg,jdbcType=VARCHAR},
            </if>
            <if test="createDate != null">
                CREATE_DATE = #{createDate,jdbcType=TIMESTAMP},
            </if>
            <if test="createUser != null">
                CREATE_USER = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="macAddr != null">
                MAC_ADDR = #{macAddr,jdbcType=VARCHAR},
            </if>
            <if test="orgId != null">
                ORG_ID = #{orgId,jdbcType=DECIMAL},
            </if>
            <if test="factoryId != null">
                FACTORY_ID = #{factoryId,jdbcType=DECIMAL},
            </if>
            <if test="entityId != null">
                ENTITY_ID = #{entityId,jdbcType=DECIMAL},
            </if>
            <if test="attr1 != null">
                ATTR1 = #{attr1,jdbcType=VARCHAR},
            </if>
            <if test="attr2 != null">
                ATTR2 = #{attr2,jdbcType=VARCHAR},
            </if>
            <if test="attr3 != null">
                ATTR3 = #{attr3,jdbcType=VARCHAR},
            </if>
            <if test="lastUpdatedBy != null">
                LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR},
            </if>
            <if test="cfgHeaderId != null">
                CFG_HEADER_ID = #{cfgHeaderId,jdbcType=VARCHAR},
            </if>
            <if test="isLead != null">
                IS_LEAD = #{isLead,jdbcType=VARCHAR},
            </if>
            <if test="avl != null">
                AVL = #{avl,jdbcType=VARCHAR},
            </if>
            <if test="polarInfo != null">
                POLAR_INFO = #{polarInfo,jdbcType=VARCHAR},
            </if>
            <if test="sourceBatchCode != null">
                SOURCE_BATCH_CODE = #{sourceBatchCode,jdbcType=VARCHAR},
            </if>
            <if test="tracingQty != null">
                TRACING_QTY = #{tracingQty,jdbcType=DECIMAL},
            </if>
            LAST_UPDATED_DATE = SYSDATE
        </set>
        where LINE_ID = #{lineId,jdbcType=VARCHAR}
    </update>

    <!-- 获取符合条件的记录列表 -->
    <select id="getList" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT * FROM
        (
        SELECT U.*, ROWNUM RN
        FROM(
        select
        t.LINE_ID,t.HEADER_ID,t.LINE_CODE,t.WORK_ORDER,t.ITEM_CODE,t.ITEM_NAME,t.MACHINE_NO,t.MODULE_NO,nvl(t.LOCATION_NO,'') LOCATION_NO,
        t.TRACK_NO,t.FEEDER_NO,t.OBJECT_ID,t.QTY,t.ENABLED_FLAG,t.PICK_STATUS,t.MOUNT_TYPE,t.OPERATE_MSG,t.CREATE_DATE,
        t.CREATE_USER,t.MAC_ADDR,t.ORG_ID,t.FACTORY_ID,t.ENTITY_ID, t.ATTR1, t.ATTR2, t.ATTR3, t.LAST_UPDATED_DATE,
        t.LAST_UPDATED_BY, t.CFG_HEADER_ID, t.IS_LEAD, t.AVL, t.POLAR_INFO, t.SOURCE_BATCH_CODE, t.TRACING_QTY,t.COMBINATION,t.LPN
        from SMT_MACHINE_MTL_SCAN_HISTORY_L t, SMT_MACHINE_MTL_SCAN_HISTORY_H h
        where h.HEADER_ID = t.HEADER_ID and h.ENABLED_FLAG='Y'
        <if test="enabledFlag != null and enabledFlag != ''"> and t.ENABLED_FLAG = #{enabledFlag}</if>
        <if test="headerId != null and headerId != ''"> and t.HEADER_ID = #{headerId}</if>
        <if test="lineId != null and lineId != ''"> and t.LINE_ID = #{lineId}</if>
        <if test="lineCode != null and lineCode != ''"> and t.LINE_CODE = #{lineCode}</if>
        <if test="workOrder != null and workOrder != ''"> and t.WORK_ORDER = #{workOrder}</if>
        <if test="createUser != null and createUser != ''"> and t.CREATE_USER = #{createUser}</if>
        <if test="mountType != null and mountType != ''"> and t.MOUNT_TYPE = #{mountType}</if>
        <if test="cfgHeaderId != null and cfgHeaderId != ''"> and t.CFG_HEADER_ID = #{cfgHeaderId}</if>
        <if test="moduleNo != null and moduleNo != ''"> and t.module_no = #{moduleNo}</if>
      	<if test="itemCode != null and itemCode != ''"> and t.item_code= #{itemCode}</if>
      	<if test="objectId != null and objectId != ''"> and t.OBJECT_ID= #{objectId}</if>
        <if test="enabledOnly != null and enabledOnly == 'Y'.toString()"> and h.ENABLED_FLAG = 'Y'</if>
        <if test="operateMsgNull != null and operateMsgNull == 'Y'.toString()"> and t.OPERATE_MSG IS NULL</if>
        <if test="attr1 != null and attr1 == 'N'.toString()"><![CDATA[ AND NVL(t.ATTR1,'N')<>'AB']]></if>
        <if test="inMountType != null and inMountType != ''"> and t.MOUNT_TYPE in (${inMountType})</if>
        <if test="notMachine != null and notMachine != ''"> and t.MACHINE_NO!=#{notMachine}</if>
        <if test="actualStartDate != null"> and t.CREATE_DATE > #{actualStartDate}</if>
        <if test="factoryId != null"> and t.FACTORY_ID = cast(#{factoryId} as numeric)</if>
        <if test="orgId != null"> and t.ORG_ID = cast(#{orgId} as numeric)</if>
        <if test="entityId != null"> and t.ENTITY_ID = cast(#{entityId} as numeric)</if>
        <if test="attr3 != null"> and t.attr3 = #{attr3}</if>
        <if test="locationNo != null and locationNo != ''">
            and t.LOCATION_NO = #{locationNo,jdbcType=VARCHAR}
        </if>
        <if test="orderField != null">
            <choose>
                <when test="orderField=='lastUpdatedDate'"> order by t.LAST_UPDATED_DATE <if test="order != null and order == 'desc'"> desc </if> </when>
                <when test="orderField=='lineCode'"> order by t.LINE_CODE <if test="order != null and order == 'desc'"> desc </if> </when>
                <when test="orderField=='workOrder'"> order by t.WORK_ORDER <if test="order != null and order == 'desc'"> desc </if> </when>
                <when test="orderField=='createDate'"> order by t.CREATE_DATE <if test="order != null and order == 'desc'"> desc </if> </when>
                <when test="orderField=='createUser'"> order by t.CREATE_USER <if test="order != null and order == 'desc'"> desc </if> </when>
            </choose>
        </if>
        ) U )
        where RN BETWEEN #{startRow}::numeric AND #{endRow}::numeric
    </select>

    <select id="getListByHeaderId" parameterType="java.util.Map" resultMap="BaseResultMap">
        select U.*, smm.create_user as qc_confirm_user, smm.create_date as qc_confirm_date
        from
        (select t.LINE_ID,t.HEADER_ID,t.LINE_CODE,t.WORK_ORDER,t.ITEM_CODE,t.ITEM_NAME,t.MACHINE_NO,t.MODULE_NO,nvl(t.LOCATION_NO,'') LOCATION_NO,
        t.TRACK_NO,t.FEEDER_NO,t.OBJECT_ID,t.QTY,t.ENABLED_FLAG,t.PICK_STATUS,t.MOUNT_TYPE,t.OPERATE_MSG,t.CREATE_DATE,
        t.CREATE_USER,t.MAC_ADDR,t.ORG_ID,t.FACTORY_ID,t.ENTITY_ID, t.ATTR1, t.ATTR2, t.ATTR3, t.LAST_UPDATED_DATE,
        t.LAST_UPDATED_BY, t.CFG_HEADER_ID, t.IS_LEAD, t.AVL, t.POLAR_INFO, t.SOURCE_BATCH_CODE, t.TRACING_QTY,t.COMBINATION,t.LPN,h.PROCESS_STATUS,
		t.material_rack,t.comp_duration,t.opt_duration,t.BOM_DIRECTION,t.ITEM_DIRECTION
        from SMT_MACHINE_MTL_SCAN_HISTORY_L t, SMT_MACHINE_MTL_SCAN_HISTORY_H h
        where h.HEADER_ID = t.HEADER_ID
        <if test="null == onlyEnable or onlyEnable"> and t.ENABLED_FLAG='Y'</if>
		<if test="moduleNo != null and moduleNo != ''"> and t.module_no = #{moduleNo}</if>
		<if test="itemCode != null and itemCode != ''"> and t.item_code= #{itemCode}</if>
		<if test="objectId != null and objectId != ''"> and t.OBJECT_ID= #{objectId}</if>
        <if test="differentDirectionFlag != null and differentDirectionFlag == 'Y'.toString()"> and t.BOM_DIRECTION != replace(t.ITEM_DIRECTION,'度','')</if>
		and t.HEADER_ID = #{headerId} ) U
        left join  SMT_MACHINE_MTL_SCAN_HISTORY_L smm on U.LINE_ID = smm.collecting_line_id and smm.enabled_flag = 'Y'
        and smm.work_order = u.WORK_ORDER
    </select>

    <!-- 上料历史查询-查全部明细 -->
    <select id="getSmtMachineHisDetailAll" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT U.*
        <if test='mountType != null and mountType == "2"'>
            , smm.create_user as qc_confirm_user, smm.create_date as qc_confirm_date
        </if>
        FROM(
        select
        t.LINE_ID,t.HEADER_ID,t.LINE_CODE,t.WORK_ORDER,t.ITEM_CODE,t.ITEM_NAME,t.MACHINE_NO,t.MODULE_NO,nvl(t.LOCATION_NO,'') LOCATION_NO,
        t.TRACK_NO,t.FEEDER_NO,t.OBJECT_ID,t.QTY,t.ENABLED_FLAG,t.PICK_STATUS,t.MOUNT_TYPE,t.OPERATE_MSG,t.CREATE_DATE,
        t.CREATE_USER,t.MAC_ADDR,t.ORG_ID,t.FACTORY_ID,t.ENTITY_ID, t.ATTR1, t.ATTR2, t.ATTR3, t.LAST_UPDATED_DATE,
        t.LAST_UPDATED_BY, t.CFG_HEADER_ID, t.IS_LEAD, t.AVL, t.POLAR_INFO, t.SOURCE_BATCH_CODE, t.TRACING_QTY,t.COMBINATION,t.LPN,t.material_rack,t.comp_duration,t.opt_duration,
        t.BOM_DIRECTION,t.ITEM_DIRECTION
        from SMT_MACHINE_MTL_SCAN_HISTORY_L t, SMT_MACHINE_MTL_SCAN_HISTORY_H h
        where h.HEADER_ID = t.HEADER_ID and h.ENABLED_FLAG='Y'
        <if test="headerId != null and headerId != ''"> and t.HEADER_ID = #{headerId}</if>
        <if test="lineId != null and lineId != ''"> and t.LINE_ID = #{lineId}</if>
        <if test="lineCode != null and lineCode != ''"> and t.LINE_CODE = #{lineCode}</if>
        <if test="workOrder != null and workOrder != ''"> and t.WORK_ORDER = #{workOrder}</if>
        <if test="createUser != null and createUser != ''"> and t.CREATE_USER = #{createUser}</if>
        <if test="mountType != null and mountType != ''"> and t.MOUNT_TYPE = #{mountType}</if>
        <if test="cfgHeaderId != null and cfgHeaderId != ''"> and t.CFG_HEADER_ID = #{cfgHeaderId}</if>
        <if test="moduleNo != null and moduleNo != ''"> and t.module_no = #{moduleNo}</if>
        <if test="itemCode != null and itemCode != ''"> and t.item_code= #{itemCode}</if>
        <if test="objectId != null and objectId != ''"> and t.OBJECT_ID= #{objectId}</if>
        <if test="enabledOnly != null and enabledOnly == 'Y'.toString()"> and h.ENABLED_FLAG = 'Y'</if>
        <if test="operateMsgNull != null and operateMsgNull == 'Y'.toString()"> and t.OPERATE_MSG IS NULL</if>
        <if test="attr1 != null and attr1 == 'N'.toString()"><![CDATA[ AND NVL(t.ATTR1,'N')<>'AB']]></if>
        <if test="inMountType != null and inMountType != ''"> and t.MOUNT_TYPE in (${inMountType})</if>
        <if test="notMachine != null and notMachine != ''"> and t.MACHINE_NO!=#{notMachine}</if>
        <if test="actualStartDate != null"> and t.CREATE_DATE > #{actualStartDate}</if>
        <if test="factoryId != null"> and t.FACTORY_ID = cast(#{factoryId} as numeric)</if>
        <if test="orgId != null"> and t.ORG_ID = cast(#{orgId} as numeric)</if>
        <if test="entityId != null"> and t.ENTITY_ID = cast(#{entityId} as numeric)</if>
        <if test="attr3 != null"> and t.attr3 = #{attr3}</if>
        <if test="locationNo != null and locationNo != ''">
            and t.LOCATION_NO = #{locationNo,jdbcType=VARCHAR}
        </if>
        <if test="differentDirectionFlag != null and differentDirectionFlag == 'Y'.toString()">
            and t.BOM_DIRECTION != replace(t.ITEM_DIRECTION,'度','')
        </if>
        <if test="orderField != null">
            <choose>
                <when test="orderField=='lastUpdatedDate'"> order by t.LAST_UPDATED_DATE <if test="order != null and order == 'desc'"> desc </if> </when>
                <when test="orderField=='lineCode'"> order by t.LINE_CODE <if test="order != null and order == 'desc'"> desc </if> </when>
                <when test="orderField=='workOrder'"> order by t.WORK_ORDER <if test="order != null and order == 'desc'"> desc </if> </when>
                <when test="orderField=='createDate'"> order by t.CREATE_DATE <if test="order != null and order == 'desc'"> desc </if> </when>
                <when test="orderField=='createUser'"> order by t.CREATE_USER <if test="order != null and order == 'desc'"> desc </if> </when>
            </choose>
        </if>
        ) U
        <if test='mountType != null and mountType == "2"'>
        left join SMT_MACHINE_MTL_SCAN_HISTORY_L  smm on U.LINE_ID = smm.collecting_line_id and smm.enabled_flag = 'Y'
            and smm.work_order = u.WORK_ORDER
        </if>
        where 1=1
        limit #{pageSize}::numeric offset (#{curPage}::numeric - 1) * #{pageSize}::numeric
    </select>

	<select id="getAnyTypeSmtMachineHisDetail" parameterType="com.zte.springbootframe.common.model.Page" resultMap="BaseResultMap">
		SELECT U.*
		<if test='params.mountType != null and params.mountType == "2"'>
			, smm.create_user as qc_confirm_user, smm.create_date as qc_confirm_date
		</if>
		FROM(
		select
		t.LINE_ID,t.HEADER_ID,t.LINE_CODE,t.WORK_ORDER,t.ITEM_CODE,t.ITEM_NAME,t.MACHINE_NO,t.MODULE_NO,nvl(t.LOCATION_NO,'') LOCATION_NO,
		t.TRACK_NO,t.FEEDER_NO,t.OBJECT_ID,t.QTY,t.ENABLED_FLAG,t.PICK_STATUS,t.MOUNT_TYPE,t.OPERATE_MSG,t.CREATE_DATE,
		t.CREATE_USER,t.MAC_ADDR,t.ORG_ID,t.FACTORY_ID,t.ENTITY_ID, t.ATTR1, t.ATTR2, t.ATTR3, t.LAST_UPDATED_DATE,
		t.LAST_UPDATED_BY, t.CFG_HEADER_ID, t.IS_LEAD, t.AVL, t.POLAR_INFO, t.SOURCE_BATCH_CODE, t.TRACING_QTY,t.COMBINATION,t.LPN,t.material_rack,t.comp_duration,t.opt_duration
		from SMT_MACHINE_MTL_SCAN_HISTORY_L t, SMT_MACHINE_MTL_SCAN_HISTORY_H h
		where h.HEADER_ID = t.HEADER_ID and h.ENABLED_FLAG='Y'
		and t.LAST_UPDATED_DATE &gt;= to_timestamp(#{params.lastUpdatedDateStart},'yyyy-MM-dd hh24:mi:ss')
		and t.LAST_UPDATED_DATE &lt;= to_timestamp(#{params.lastUpdatedDateEnd},'yyyy-MM-dd hh24:mi:ss')
		<if test="params.mountType != null and params.mountType != ''"> and t.MOUNT_TYPE = #{params.mountType}</if>
		order by t.LAST_UPDATED_DATE desc, t.LINE_ID asc
		) U
		<if test='params.mountType != null and params.mountType == "2"'>
			left join SMT_MACHINE_MTL_SCAN_HISTORY_L  smm on U.LINE_ID = smm.collecting_line_id and smm.enabled_flag = 'Y'
			and smm.work_order = u.WORK_ORDER
		</if>
	</select>

    <!-- 上料历史查询-批量查明细 -->
    <select id="getSmtMachineHisDetailBatch" parameterType="java.util.Map" resultMap="BaseResultMap">
        select * from
        (
        select
        rownum as rn,
        t.LINE_ID,t.HEADER_ID,t.LINE_CODE,t.WORK_ORDER,t.ITEM_CODE,t.ITEM_NAME,t.MACHINE_NO,t.MODULE_NO,nvl(t.LOCATION_NO,'') LOCATION_NO,
        t.TRACK_NO,t.FEEDER_NO,t.OBJECT_ID,t.QTY,t.ENABLED_FLAG,t.PICK_STATUS,t.MOUNT_TYPE,t.OPERATE_MSG,t.CREATE_DATE,
        t.CREATE_USER,t.MAC_ADDR,t.ORG_ID,t.FACTORY_ID,t.ENTITY_ID, t.ATTR1, t.ATTR2, t.ATTR3, t.LAST_UPDATED_DATE,
        t.LAST_UPDATED_BY, t.CFG_HEADER_ID, t.IS_LEAD, t.AVL, t.POLAR_INFO, t.SOURCE_BATCH_CODE, t.TRACING_QTY,t.COMBINATION,t.LPN,t.material_rack,t.comp_duration,t.opt_duration,
        t.BOM_DIRECTION,t.ITEM_DIRECTION
        from SMT_MACHINE_MTL_SCAN_HISTORY_L t, SMT_MACHINE_MTL_SCAN_HISTORY_H h
        where h.HEADER_ID = t.HEADER_ID and h.ENABLED_FLAG='Y'
        <if test="headerIdList != null ">
            AND h.HEADER_ID IN
            <foreach item="headerId" collection="headerIdList" separator="," open="(" close=")">
                #{headerId}
            </foreach>
        </if>
        <if test="lineCode != null and lineCode != ''"> and h.LINE_CODE = #{lineCode}</if>
        <if test="workOrder != null and workOrder != ''"> and h.WORK_ORDER = #{workOrder}</if>
        <if test="startTime != null">AND h.CREATE_DATE &gt; to_timestamp(#{startTime},'yyyy-MM-dd hh24:mi:ss')</if>
        <if test="endTime != null">AND h.CREATE_DATE &lt; to_timestamp(#{endTime},'yyyy-MM-dd hh24:mi:ss')</if>
        <if test="createUser != null and createUser != ''"> and h.CREATE_USER = #{createUser}</if>
        <if test="mountType != null and mountType != ''"> and h.MOUNT_TYPE = #{mountType}</if>
        <if test="sourceBatch != null and sourceBatch != ''"> and h.WORK_ORDER like '${sourceBatch}%'</if>
        <if test="moduleNo != null and moduleNo != ''"> and t.module_no = #{moduleNo}</if>
        <if test="itemCode != null and itemCode != ''"> and t.item_code= #{itemCode}</if>
        <if test="objectId != null and objectId != ''"> and t.OBJECT_ID= #{objectId}</if>
        <if test="differentDirectionFlag != null and differentDirectionFlag == 'Y'.toString()">
            and t.BOM_DIRECTION != replace(t.ITEM_DIRECTION,'度','')
        </if>
        order by t.CREATE_DATE asc
        )
        where RN BETWEEN (#{curPage}::numeric - 1) * #{pageSize}::numeric + 1 AND #{curPage}::numeric * #{pageSize}::numeric
    </select>

    <select id="getListByHeaderIdBatch" parameterType="java.util.Map" resultMap="BaseResultMap">
        select U.*, smm.create_user as qc_confirm_user, smm.create_date as qc_confirm_date
        from
        (select t.LINE_ID,t.HEADER_ID,t.LINE_CODE,t.WORK_ORDER,t.ITEM_CODE,t.ITEM_NAME,t.MACHINE_NO,t.MODULE_NO,nvl(t.LOCATION_NO,'') LOCATION_NO,
        t.TRACK_NO,t.FEEDER_NO,t.OBJECT_ID,t.QTY,t.ENABLED_FLAG,t.PICK_STATUS,t.MOUNT_TYPE,t.OPERATE_MSG,t.CREATE_DATE,
        t.CREATE_USER,t.MAC_ADDR,t.ORG_ID,t.FACTORY_ID,t.ENTITY_ID, t.ATTR1, t.ATTR2, t.ATTR3, t.LAST_UPDATED_DATE,
        t.LAST_UPDATED_BY, t.CFG_HEADER_ID, t.IS_LEAD, t.AVL, t.POLAR_INFO, t.SOURCE_BATCH_CODE, t.TRACING_QTY,t.COMBINATION,t.LPN,t.material_rack,t.comp_duration,t.opt_duration
        from SMT_MACHINE_MTL_SCAN_HISTORY_L t, SMT_MACHINE_MTL_SCAN_HISTORY_H h
        where h.HEADER_ID = t.HEADER_ID and h.ENABLED_FLAG='Y'
        AND t.HEADER_ID IN
            <foreach item="headerId" collection="headerIdList" separator="," open="(" close=")">
                #{headerId}
            </foreach>
        ) U
        left join  SMT_MACHINE_MTL_SCAN_HISTORY_L smm on U.LINE_ID = smm.collecting_line_id and smm.enabled_flag = 'Y'
        and smm.work_order = u.WORK_ORDER
        limit 50000
    </select>

    <!--获取符合条件的记录数 -->
    <select id="getCountBatch" parameterType="java.util.Map" resultType="java.lang.Long">
        select count(*)
        from SMT_MACHINE_MTL_SCAN_HISTORY_L t, SMT_MACHINE_MTL_SCAN_HISTORY_H h
        WHERE h.HEADER_ID = t.HEADER_ID and t.ENABLED_FLAG='Y'
        <if test="headerIdList != null ">
            AND h.HEADER_ID IN
            <foreach item="headerId" collection="headerIdList" separator="," open="(" close=")">
                #{headerId}
            </foreach>
        </if>
        <if test="differentDirectionFlag != null and differentDirectionFlag == 'Y'.toString()">
            and t.BOM_DIRECTION != replace(t.ITEM_DIRECTION,'度','')
        </if>
    </select>

    <select id="getSmtMachineHisList" parameterType="com.zte.interfaces.dto.SmtMachineMTLHistoryLDTO" resultMap="BaseResultMap">
        select
        t.LINE_ID,t.HEADER_ID,t.LINE_CODE,t.WORK_ORDER,t.ITEM_CODE,t.ITEM_NAME,t.MACHINE_NO,t.MODULE_NO,nvl(t.LOCATION_NO,'') LOCATION_NO,
        t.TRACK_NO,t.FEEDER_NO,t.OBJECT_ID,t.QTY,t.ENABLED_FLAG,t.PICK_STATUS,t.MOUNT_TYPE,t.OPERATE_MSG,t.CREATE_DATE,
        t.CREATE_USER,t.MAC_ADDR,t.ORG_ID,t.FACTORY_ID,t.ENTITY_ID, t.ATTR1, t.ATTR2, t.ATTR3, t.LAST_UPDATED_DATE,
        t.LAST_UPDATED_BY, t.CFG_HEADER_ID, t.IS_LEAD, t.AVL, t.POLAR_INFO, t.SOURCE_BATCH_CODE, t.TRACING_QTY,t.COMBINATION,t.LPN
        from SMT_MACHINE_MTL_SCAN_HISTORY_L t, SMT_MACHINE_MTL_SCAN_HISTORY_H h
        where h.HEADER_ID = t.HEADER_ID and t.ENABLED_FLAG='Y' and h.ENABLED_FLAG='Y'
        <if test="workOrder != null and workOrder != ''"> and t.WORK_ORDER = #{workOrder}</if>
        <if test="objectId != null and objectId != ''"> and t.OBJECT_ID= #{objectId}</if>
        <if test="inMountType != null and inMountType != ''"> and t.MOUNT_TYPE in (${inMountType})</if>
        <if test="(workOrder == null or workOrder == '')
                and (objectId == null or objectId == '')
                and (inMountType == null or inMountType == '')"> and 1=2</if>
    </select>

    <select id="getSmtMachineHisInfo" parameterType="com.zte.interfaces.dto.SmtMachineMTLHistoryLDTO" resultMap="BaseResultMap">
        select
        t.OBJECT_ID, t.WORK_ORDER , t.CREATE_DATE
        from SMT_MACHINE_MTL_SCAN_HISTORY_L t
        where t.ENABLED_FLAG='Y'
        and t.WORK_ORDER in
        <foreach item="workOrder" collection="workOrderList" separator="," open="(" close=")">
            #{workOrder}
        </foreach>
        and t.MOUNT_TYPE = #{inMountType}
    </select>

    <!-- 翻页函数:获取符合条件的记录数 -->
    <select id="getCount" parameterType="java.util.Map" resultType="java.lang.Long">
        select count(*)
        from SMT_MACHINE_MTL_SCAN_HISTORY_L t, SMT_MACHINE_MTL_SCAN_HISTORY_H h
        WHERE h.HEADER_ID = t.HEADER_ID and t.ENABLED_FLAG='Y'
        <if test="headerId != null and headerId != ''"> and t.HEADER_ID = #{headerId}</if>
        <if test="lineId != null and lineId != ''"> and t.LINE_ID = #{lineId}</if>
        <if test="lineCode != null and lineCode != ''"> and t.LINE_CODE = #{lineCode}</if>
        <if test="workOrder != null and workOrder != ''"> and t.WORK_ORDER = #{workOrder}</if>
        <if test="createUser != null and createUser != ''"> and t.CREATE_USER = #{createUser}</if>
        <if test="mountType != null and mountType != ''"> and t.MOUNT_TYPE = #{mountType}</if>
        <if test="cfgHeaderId != null and cfgHeaderId != ''"> and t.CFG_HEADER_ID = #{cfgHeaderId}</if>
        <if test="moduleNo != null and moduleNo != ''"> and t.module_no = #{moduleNo}</if>
      	<if test="itemCode != null and itemCode != ''"> and t.item_code= #{itemCode}</if>
      	<if test="objectId != null and objectId != ''"> and t.OBJECT_ID= #{objectId}</if>
        <if test="factoryId != null"> and t.FACTORY_ID = cast(#{factoryId} as numeric)</if>
        <if test="orgId != null"> and t.ORG_ID = cast(#{orgId} as numeric)</if>
        <if test="entityId != null"> and t.ENTITY_ID = cast(#{entityId} as numeric)</if>
    </select>

    <!-- 上料历史查询-查全部明细 -->
    <select id="getSmtMachineHisDetailAllCount" parameterType="java.util.Map" resultType="java.lang.Long">
        select count(*)
        from SMT_MACHINE_MTL_SCAN_HISTORY_L t, SMT_MACHINE_MTL_SCAN_HISTORY_H h
        WHERE h.HEADER_ID = t.HEADER_ID and h.ENABLED_FLAG='Y'
        <if test="headerId != null and headerId != ''"> and t.HEADER_ID = #{headerId}</if>
        <if test="lineId != null and lineId != ''"> and t.LINE_ID = #{lineId}</if>
        <if test="lineCode != null and lineCode != ''"> and t.LINE_CODE = #{lineCode}</if>
        <if test="workOrder != null and workOrder != ''"> and t.WORK_ORDER = #{workOrder}</if>
        <if test="createUser != null and createUser != ''"> and t.CREATE_USER = #{createUser}</if>
        <if test="mountType != null and mountType != ''"> and t.MOUNT_TYPE = #{mountType}</if>
        <if test="cfgHeaderId != null and cfgHeaderId != ''"> and t.CFG_HEADER_ID = #{cfgHeaderId}</if>
        <if test="moduleNo != null and moduleNo != ''"> and t.module_no = #{moduleNo}</if>
        <if test="itemCode != null and itemCode != ''"> and t.item_code= #{itemCode}</if>
        <if test="objectId != null and objectId != ''"> and t.OBJECT_ID= #{objectId}</if>
        <if test="factoryId != null"> and t.FACTORY_ID = cast(#{factoryId} as numeric)</if>
        <if test="orgId != null"> and t.ORG_ID = cast(#{orgId} as numeric)</if>
        <if test="entityId != null"> and t.ENTITY_ID = cast(#{entityId} as numeric)</if>
        <if test="differentDirectionFlag != null and differentDirectionFlag == 'Y'.toString()">
            and t.BOM_DIRECTION != replace(t.ITEM_DIRECTION,'度','')
        </if>
    </select>

    <update id="updateByCond" parameterType="java.util.Map" >
        update SMT_MACHINE_MTL_SCAN_HISTORY_L t
        set t.ENABLED_FLAG='N',
        LAST_UPDATED_DATE = sysdate
        where 1=1
        and t.LINE_CODE = #{lineCode,jdbcType=VARCHAR}
        and t.MODULE_NO in (${inModuleNo})
        and t.WORK_ORDER = #{workOrder,jdbcType=VARCHAR}
        and t.MOUNT_TYPE in (${inMountType})

    </update>

    <!-- 翻页函数:获取符合条件的记录数 -->
    <select id="getWorkloadCount" parameterType="java.util.Map" resultType="java.lang.Long">
        select count(*)
        from (select t.create_user, t.line_code, t.work_order, t.mount_type
            from SMT_MACHINE_MTL_SCAN_HISTORY_L t
            where 1=1
            <if test="lineCode != null and lineCode != ''"> and t.LINE_CODE = #{lineCode}</if>
            <if test="workOrder != null and workOrder != ''"> and t.WORK_ORDER = #{workOrder}</if>
            <if test="createUser != null and createUser != ''"> and t.CREATE_USER = #{createUser}</if>
            <if test="factoryId != null"> and t.FACTORY_ID = cast(#{factoryId} as numeric)</if>
            <if test="orgId != null"> and t.ORG_ID = cast(#{orgId} as numeric)</if>
            <if test="entityId != null"> and t.ENTITY_ID = cast(#{entityId} as numeric)</if>
            <if test="startTime != null and endTime != null">
                and (t.CREATE_DATE between #{startTime} and #{endTime})</if>
            <if test="mountType != null and mountType != ''"> and t.MOUNT_TYPE = #{mountType}</if>
            group by t.create_user, t.line_code, t.work_order, t.mount_type)
    </select>

    <!-- 获取符合条件的记录列表 -->
    <select id="getWorkloadList" parameterType="java.util.Map" resultMap="workLoadResultMap">
        SELECT * FROM
        (
        SELECT U.*, ROWNUM RN
        FROM(
        select t.create_user, t.line_code, t.work_order, t.mount_type,
            sum(case when t.operate_msg is null then 1 else 0 end) as success_cnt,
            sum(case when t.operate_msg is not null then 1 else 0 end) as fail_cnt
        from smt_machine_mtl_scan_history_l t where 1=1
            <if test="lineCode != null and lineCode != ''"> and t.LINE_CODE = #{lineCode}</if>
            <if test="workOrder != null and workOrder != ''"> and t.WORK_ORDER = #{workOrder}</if>
            <if test="createUser != null and createUser != ''"> and t.CREATE_USER = #{createUser}</if>
            <if test="factoryId != null"> and t.FACTORY_ID = cast(#{factoryId} as numeric)</if>
            <if test="orgId != null"> and t.ORG_ID = cast(#{orgId} as numeric)</if>
            <if test="entityId != null"> and t.ENTITY_ID = cast(#{entityId} as numeric)</if>
            <if test="startTime != null">
                and t.CREATE_DATE &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and t.CREATE_DATE &lt;= #{endTime}
            </if>
            <if test="mountType != null and mountType != ''"> and t.MOUNT_TYPE = #{mountType}</if>
        group by t.create_user, t.line_code, t.work_order, t.mount_type
        <if test="orderField != null">
            <choose>
                <when test="orderField=='lineCode'"> order by t.LINE_CODE <if test="order != null and order == 'desc'"> desc </if> </when>
                <when test="orderField=='workOrder'"> order by t.WORK_ORDER <if test="order != null and order == 'desc'"> desc </if> </when>
                <when test="orderField=='createUser'"> order by t.CREATE_USER <if test="order != null and order == 'desc'"> desc </if> </when>
            </choose>
        </if>
        ) U ) where RN BETWEEN #{startRow}::numeric AND #{endRow}::numeric
    </select>

    <!-- 获取符合条件reelId、mount_type(1/2)条件的创建时间 -->
    <select id="getMinAndMaxCreateDate" parameterType="com.zte.interfaces.dto.SmtMachineMTLHistoryLDTO" resultMap="BaseResultMap">
    	SELECT
    		MIN(l.CREATE_DATE) AS CREATE_DATE,
    		MAX(l.CREATE_DATE) AS LAST_UPDATED_DATE
    	FROM SMT_MACHINE_MTL_SCAN_HISTORY_L l
    	JOIN SMT_MACHINE_MTL_SCAN_HISTORY_H h ON h.HEADER_ID = l.HEADER_ID
    	WHERE l.ENABLED_FLAG='Y'
    		AND h.ENABLED_FLAG='Y'
    		<!-- 1:转机,2:接料,10:DIP上料扫描,11:DIP接料扫描 -->
    		AND h.MOUNT_TYPE IN ('1','2','10','11','14')
    		<if test="factoryId != null"> AND l.FACTORY_ID = cast(#{factoryId} as numeric)</if>
    		<if test="objectId != null and objectId != ''"> and l.OBJECT_ID= #{objectId}</if>
            <if test="objectIdList != null ">
                AND l.OBJECT_ID IN
                <foreach item="item" collection="objectIdList" separator=","
                         open="(" close=")">
                    #{item}
                </foreach>
            </if>
    </select>
    <select id="getCanQcReCheckItem" parameterType="com.zte.interfaces.dto.SmtMachineMTLHistoryLDTO" resultMap="BaseResultDtoMap">
        WITH TB1 AS (
        SELECT T.*
        FROM SMT_MACHINE_MTL_SCAN_HISTORY_L T, SMT_MACHINE_MTL_SCAN_HISTORY_H H
        WHERE H.HEADER_ID = T.HEADER_ID AND T.ENABLED_FLAG='Y'
        AND H.MOUNT_TYPE = '2'
        <if test="lineCode != null and lineCode != ''"> and t.LINE_CODE = #{lineCode}</if>
        <if test="workOrder != null and workOrder != ''"> and t.WORK_ORDER = #{workOrder}</if>
        <if test="moduleNo != null and moduleNo != ''"> and t.module_no = #{moduleNo}</if>
        <if test="factoryId != null"> and t.FACTORY_ID = cast(#{factoryId} as numeric)</if>
        <if test="entityId != null"> and t.ENTITY_ID = cast(#{entityId} as numeric)</if>
        <if test="cfgHeaderId != null and cfgHeaderId != ''"> and t.CFG_HEADER_ID = #{cfgHeaderId}</if>
        <if test="itemCode != null and itemCode != ''"> and t.item_code= #{itemCode}</if>
        <if test="objectId != null and objectId != ''"> and t.OBJECT_ID= #{objectId}</if>
        AND T.OPERATE_MSG IS NULL)
        ,TB2 AS (
        SELECT T.*
        FROM SMT_MACHINE_MTL_SCAN_HISTORY_L T, SMT_MACHINE_MTL_SCAN_HISTORY_H H
        WHERE H.HEADER_ID = T.HEADER_ID AND T.ENABLED_FLAG='Y'
        AND H.MOUNT_TYPE = '9'
        <if test="lineCode != null and lineCode != ''"> and t.LINE_CODE = #{lineCode}</if>
        <if test="workOrder != null and workOrder != ''"> and t.WORK_ORDER = #{workOrder}</if>
        <if test="moduleNo != null and moduleNo != ''"> and t.module_no = #{moduleNo}</if>
        <if test="factoryId != null"> and t.FACTORY_ID = cast(#{factoryId} as numeric)</if>
        <if test="entityId != null"> and t.ENTITY_ID = cast(#{entityId} as numeric)</if>
        <if test="cfgHeaderId != null and cfgHeaderId != ''"> and t.CFG_HEADER_ID = #{cfgHeaderId}</if>
        <if test="itemCode != null and itemCode != ''"> and t.item_code= #{itemCode}</if>
        <if test="objectId != null and objectId != ''"> and t.OBJECT_ID= #{objectId}</if>
        AND T.OPERATE_MSG IS NULL)
        SELECT B1.*,(SELECT MAX(CREATE_DATE) FROM TB2 B3 WHERE B3.OBJECT_ID = B1.OBJECT_ID AND B3.MODULE_NO=B3.MODULE_NO)RE_CHECK_DATE
        FROM TB1 B1 WHERE NOT EXISTS(SELECT * FROM TB2 B2 WHERE B2.OBJECT_ID = B1.OBJECT_ID AND B2.MODULE_NO=B1.MODULE_NO)
    </select>
    <select id="getReelIdHistory" parameterType="com.zte.interfaces.dto.SmtMachineMTLHistoryLDTO" resultMap="BaseResultMap" >
        select
        <include refid="Base_Column_List" />
        from SMT_MACHINE_MTL_SCAN_HISTORY_L t
        where t.operate_msg is null
        <if test="(objectId == null or objectId == '') and (mountType == null or mountType == '')
        and (lineCode == null or lineCode == '') and (workOrder == null or workOrder == '')"> and 1=2</if>
        <if test="objectId != null and objectId != ''"> and t.OBJECT_ID = #{objectId}</if>
        <if test="mountType != null and mountType != ''"> and t.MOUNT_TYPE = #{mountType}</if>
        <if test="lineCode != null and lineCode != ''"> and t.LINE_CODE = #{lineCode}</if>
        <if test="workOrder != null and workOrder != ''"> and t.WORK_ORDER = #{workOrder}</if>
        <if test="factoryId != null"> AND t.FACTORY_ID = cast(#{factoryId} as numeric)</if>
    </select>

    <!-- 翻页函数:获取符合条件的记录数 -->
    <select id="getLineCount" parameterType="com.zte.interfaces.dto.SmtMachineMTLHistoryLDTO" resultType="java.lang.Long">
        SELECT count(1) from smt_machine_mtl_scan_history_l t where t.ENABLED_FLAG='Y'
        <if test="lineCode != null and lineCode != ''"> and t.LINE_CODE = #{lineCode}</if>
        <if test="itemCode != null and itemCode != ''"> and t.item_code = #{itemCode}</if>
        <if test="workOrder != null and workOrder != ''"> and t.work_order = #{workOrder}</if>
        <if test="moduleNo != null and moduleNo != ''"> and t.module_no = #{moduleNo}</if>
        <if test="mountType != null and mountType != ''"> and t.MOUNT_TYPE = #{mountType}</if>
        <if test="machineNo != null and machineNo != ''"> and t.MACHINE_NO = #{machineNo}</if>
        <if test="locationNo != null and locationNo != ''"> and t.location_no = #{locationNo}</if>
        <if test="objectId != null and objectId != ''"> and t.object_id = #{objectId}</if>

    </select>

    <!--校验上料历史明细与上料表（基于物料代码、模组、占位且上料表需移除在上料放错异常跳过中的数据）对比（粒度比对到物料代码行， 两边的物料代码相等）-->
    <select id="equalMaterialExceptErrorSkip" parameterType="java.util.Map" resultType="java.lang.Long">
        select count(1)
          from (SELECT T1.item_code, t1.module_no, t1.location_no
                  from B_SMT_BOM_DETAIL t1
                 where t1.enabled_flag = 'Y'
                   and t1.cfg_header_id in
                       (SELECT b.cfg_header_id
                          from b_Smt_Bom_Header b
                         where b.line_code = #{lineCode}
                           and b.attr1 = #{taskNo}
                           and b.craft_section = #{craftSection})
                   and not exists
                 (select 1
                          from SMT_FEED_ERROR_SKIP a
                         where a.line_code = #{lineCode}
                         and a.enabled_flag =  'Y'
                         and a.WORK_ORDER_NO = #{workOrder}
                        and a.module_no = t1.module_no
                           and (a.location_no = t1.location_no or a.location_no is null ))
                EXCEPT
                SELECT  T.item_code, t.module_no, t.location_no
                          from smt_machine_mtl_scan_history_l t
                         where t.enabled_flag = 'Y'
                           and t.line_code = #{lineCode}
                           and t.work_order = #{workOrder}
                           and t.MOUNT_TYPE = '3'
                           )
    </select>

    <!-- 根据创建时间和批次统计上料历史行表中某批次的ReelId个数 -->
    <select id="countReelIdCountBySourceBatchAndTime" parameterType="com.zte.interfaces.dto.QuerySmtMachineMTLHistoryLDTO" resultType="com.zte.interfaces.dto.QuerySmtMachineMTLHistoryLDTO">
        select substr(t.WORK_ORDER,0,7) as productBatchCode, count(t.object_id) as reelIdCount
        from SMT_MACHINE_MTL_SCAN_HISTORY_L t, SMT_MACHINE_MTL_SCAN_HISTORY_H h
        where h.HEADER_ID = t.HEADER_ID and t.ENABLED_FLAG='Y' AND h.ENABLED_FLAG='Y' AND OPERATE_MSG IS NULL
        <if test="factoryId != null and factoryId != ''"> and t.FACTORY_ID = cast(#{factoryId} as numeric) </if>
        <if test="productBatchCode != null and productBatchCode != ''"> and t.WORK_ORDER like '${productBatchCode}%'</if>
        <if test="queryStartTime != null">
            <![CDATA[
	     AND t.CREATE_DATE >= #{queryStartTime}
	    ]]>
        </if>
        <if test="queryEndTime != null">
            <![CDATA[
	      AND t.CREATE_DATE <= #{queryEndTime}
	    ]]>
        </if>
        <if test="mountTypeList != null">
            <foreach item="mountType" collection="mountTypeList" separator=","
                     open="AND t.MOUNT_TYPE IN (" close=")">
                #{mountType}
            </foreach>
        </if>
        <if test="productBatchCodeList != null">
        <foreach item="productBatch" collection="productBatchCodeList" separator=","
                 open="AND substr(t.WORK_ORDER,0,7) IN (" close=")">
            #{productBatch}
        </foreach>
    </if>
        group by substr(t.WORK_ORDER,0,7)
    </select>
  <select id="getByNextReelId" resultMap="BaseResultMap">
      select * from (select qty, object_id,
      row_number() over(partition by object_id order by create_date desc) rw
      from SMT_MACHINE_MTL_SCAN_HISTORY_L
      where object_id in (
        <foreach collection="list" item="id" separator=",">
            #{id}
        </foreach>
      )
      and enabled_flag = 'Y'
      and mount_type = '2'
      and (operate_msg is null or operate_msg = ''))
      where rw = 1
  </select>

  <update id="deleteMTLLByCond" parameterType="java.util.Map" >
        update SMT_MACHINE_MTL_SCAN_HISTORY_L t
        set t.ENABLED_FLAG='N', t.LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR}, t.LAST_UPDATED_DATE = SYSDATE
        where t.ENABLED_FLAG='Y'
        and t.LINE_CODE = #{lineCode,jdbcType=VARCHAR}
        and t.WORK_ORDER = #{workOrder,jdbcType=VARCHAR}
        and t.MODULE_NO = #{moduleNo,jdbcType=VARCHAR}
        and t.MOUNT_TYPE = #{mountType,jdbcType=VARCHAR}
        <if test="locationNo != null and locationNo != ''">
            and t.LOCATION_NO = #{locationNo,jdbcType=VARCHAR}
        </if>
        <if test="objectId != null and objectId != ''">
            and t.OBJECT_ID = #{objectId,jdbcType=VARCHAR}
        </if>
        <if test="feederNo != null and feederNo != ''">
            and t.FEEDER_NO = #{feederNo,jdbcType=VARCHAR}
        </if>
    </update>


    <insert id="insertSmtMachineMTLHistoryLBatch" parameterType="java.util.List">
    insert into SMT_MACHINE_MTL_SCAN_HISTORY_L
    (LINE_ID,HEADER_ID,LINE_CODE,WORK_ORDER,ITEM_CODE,ITEM_NAME,MACHINE_NO,MODULE_NO,
        LOCATION_NO,TRACK_NO,FEEDER_NO,OBJECT_ID,QTY,ENABLED_FLAG,PICK_STATUS,MOUNT_TYPE,OPERATE_MSG,CREATE_DATE,CREATE_USER,
        MAC_ADDR,ORG_ID,FACTORY_ID,ENTITY_ID, ATTR1, ATTR2, ATTR3, LAST_UPDATED_DATE, LAST_UPDATED_BY, CFG_HEADER_ID,IS_LEAD,
        AVL,POLAR_INFO,SOURCE_BATCH_CODE, TRACING_QTY,COMBINATION,LPN,BOM_DIRECTION,ITEM_DIRECTION
     )
    select LINE_ID,HEADER_ID,LINE_CODE,WORK_ORDER,ITEM_CODE,ITEM_NAME,MACHINE_NO,MODULE_NO,
        LOCATION_NO,TRACK_NO,FEEDER_NO,OBJECT_ID,QTY,ENABLED_FLAG,PICK_STATUS,MOUNT_TYPE,OPERATE_MSG,CREATE_DATE,CREATE_USER,
        MAC_ADDR,ORG_ID,FACTORY_ID,ENTITY_ID, ATTR1, ATTR2, ATTR3, LAST_UPDATED_DATE, LAST_UPDATED_BY, CFG_HEADER_ID,IS_LEAD,
        AVL,POLAR_INFO,SOURCE_BATCH_CODE, TRACING_QTY,COMBINATION,LPN,BOM_DIRECTION,ITEM_DIRECTION
    from (
    <foreach collection ="list" item="item" index= "index" separator ="UNION ALL">
    select
    #{item.lineId,jdbcType=VARCHAR} LINE_ID, #{item.headerId,jdbcType=VARCHAR} HEADER_ID, #{item.lineCode,jdbcType=VARCHAR} LINE_CODE, #{item.workOrder,jdbcType=VARCHAR} WORK_ORDER,
        #{item.itemCode,jdbcType=VARCHAR} ITEM_CODE, #{item.itemName,jdbcType=VARCHAR} ITEM_NAME, #{item.machineNo,jdbcType=VARCHAR} MACHINE_NO, #{item.moduleNo,jdbcType=VARCHAR} MODULE_NO,
        #{item.locationNo,jdbcType=VARCHAR} LOCATION_NO, #{item.trackNo,jdbcType=VARCHAR} TRACK_NO, #{item.feederNo,jdbcType=VARCHAR} FEEDER_NO, #{item.objectId,jdbcType=VARCHAR} OBJECT_ID,
        #{item.qty,jdbcType=DECIMAL} QTY, #{item.enabledFlag,jdbcType=VARCHAR} ENABLED_FLAG, #{item.pickStatus,jdbcType=VARCHAR} PICK_STATUS, #{item.mountType,jdbcType=VARCHAR} MOUNT_TYPE,
        #{item.operateMsg,jdbcType=VARCHAR} OPERATE_MSG,SYSDATE CREATE_DATE, #{item.createUser,jdbcType=VARCHAR} CREATE_USER, #{item.macAddr,jdbcType=VARCHAR} MAC_ADDR,
        #{item.orgId,jdbcType=DECIMAL} ORG_ID, #{item.factoryId,jdbcType=DECIMAL} FACTORY_ID, #{item.entityId,jdbcType=DECIMAL} ENTITY_ID, #{item.attr1,jdbcType=VARCHAR} ATTR1,
        #{item.attr2,jdbcType=VARCHAR} ATTR2, #{item.attr3,jdbcType=VARCHAR} ATTR3, SYSDATE LAST_UPDATED_DATE, #{item.lastUpdatedBy,jdbcType=VARCHAR} LAST_UPDATED_BY,
        #{item.cfgHeaderId,jdbcType=VARCHAR} CFG_HEADER_ID,#{item.isLead,jdbcType=VARCHAR} IS_LEAD,#{item.avl,jdbcType=VARCHAR} AVL,
        #{item.polarInfo,jdbcType=VARCHAR} POLAR_INFO,#{item.sourceBatchCode,jdbcType=VARCHAR} SOURCE_BATCH_CODE,
        #{item.tracingQty,jdbcType=DECIMAL} TRACING_QTY,#{item.combination,jdbcType=VARCHAR} COMBINATION,
        #{item.lpn,jdbcType=VARCHAR} LPN,
        #{item.bomDirection,jdbcType=VARCHAR} BOM_DIRECTION,#{item.itemDirection,jdbcType=VARCHAR} ITEM_DIRECTION

    </foreach>
    ) a
  </insert>

    <insert id="insetFromPrepare">
        INSERT INTO smt_machine_mtl_scan_history_l
        (header_id, line_id ,work_order,line_code,item_code,
        machine_no,module_no,location_no,track_no,feeder_no,object_id,
        qty,enabled_flag,mount_type,create_date ,create_user,org_id,entity_id,
        factory_id,pick_status, last_updated_date,last_updated_by,cfg_header_id,
        is_lead,avl,polar_info,source_batch_code,tracing_qty)
        select #{dto.hisHeadId} header_id, gen_random_uuid() line_id ,work_order,line_code,item_code,
        machine_no,module_no,location_no,track_no,feeder_no,object_id,
        qty,'Y','14',sysdate ,#{dto.empNo},org_id,entity_id,
        factory_id,'1', sysdate,#{dto.empNo},#{dto.cfgHeaderId},
        is_lead,avl,polar_info,source_batch_code,0
        from smt_machine_material_prepare
        where mtl_prepare_id in
        <foreach collection="prepareIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </insert>

    <insert id="insetFromMouting">
        INSERT INTO smt_machine_mtl_scan_history_l
        (header_id, line_id ,work_order,line_code,item_code,item_name,
        machine_no,module_no,location_no,track_no,feeder_no,object_id,
        qty,enabled_flag,mount_type,create_date ,create_user,org_id,entity_id,
        factory_id,pick_status, last_updated_date,last_updated_by,cfg_header_id,
        is_lead,avl,polar_info,source_batch_code,tracing_qty,combination,lpn, attr1)
        select #{dto.hisHeadId} header_id, gen_random_uuid() line_id ,work_order,line_code,item_code,item_name,
        machine_no,module_no,location_no,track_no,feeder_no,object_id,
        qty,'Y','14',sysdate ,#{dto.empNo},org_id,entity_id,
        factory_id,'1', sysdate,#{dto.empNo},#{dto.cfgHeaderId},
        is_lead,avl,polar_info,source_batch_code,0,combination,lpn, 'AB'
        from smt_machine_material_mouting
        where machine_material_mouting_id in
        <foreach collection="moutingIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </insert>

    <!-- 删除BsItemInfo：根据Id -->
    <delete id="deleteHistoryDetailByLineCodeAndWorkOrderNo" parameterType="com.zte.domain.model.SmtMachineMTLHistoryL">
        delete from SMT_MACHINE_MTL_SCAN_HISTORY_L
        where ENABLED_FLAG='Y'
        and LINE_CODE = #{lineCode,jdbcType=VARCHAR}
        and WORK_ORDER = #{workOrder,jdbcType=VARCHAR}
        <if test="locationNo != null and locationNo != ''">
            and LOCATION_NO = #{locationNo,jdbcType=VARCHAR}
        </if>
        <if test="moduleNo != null and moduleNo != ''">
            and MODULE_NO = #{moduleNo,jdbcType=VARCHAR}
        </if>
        <if test="machineNo != null and machineNo != ''">
         and MACHINE_NO = #{machineNo,jdbcType=VARCHAR}</if>
    </delete>

    <update id="batchUpdateWithTime" parameterType="java.util.List">
        <foreach collection="list" item="item" >
            update SMT_MACHINE_MTL_SCAN_HISTORY_L
            <set>
                TRACING_QTY = #{item.tracingQty,jdbcType=DECIMAL},
                LAST_UPDATED_DATE = #{item.lastUpdatedDate,jdbcType=TIMESTAMP}
            </set>
            where LINE_ID = #{item.lineId,jdbcType=VARCHAR}
            AND ENABLED_FLAG = 'Y';
        </foreach>
    </update>


    <insert id="insetFromPrepareByWorkOrder" parameterType="com.zte.interfaces.dto.SmtMachineMTLHistoryLDTO">
        INSERT INTO smt_machine_mtl_scan_history_l
        (header_id, line_id ,work_order,line_code,item_code,
        machine_no,module_no,location_no,track_no,feeder_no,object_id,
        qty,enabled_flag,mount_type,create_date ,create_user,org_id,entity_id,
        factory_id,pick_status, last_updated_date,last_updated_by,cfg_header_id,
        is_lead,avl,polar_info,source_batch_code,tracing_qty)
        select #{headerId} header_id, gen_random_uuid() line_id ,work_order,line_code,item_code,
        machine_no,module_no,location_no,track_no,feeder_no,object_id,
        qty,'Y',#{mountType},sysdate ,#{empNo},org_id,entity_id,
        factory_id,'1', sysdate,#{empNo},#{cfgHeaderId},
        is_lead,avl,polar_info,source_batch_code,0
        from smt_machine_material_prepare
        where  ENABLED_FLAG = 'Y' and work_order = #{workOrder} and feeder_no is not null  and feeder_no !=''
    </insert>

    <update id="invalidLoadingHistory" parameterType="com.zte.interfaces.dto.SmtMachineMTLHistoryLDTO">
        update SMT_MACHINE_MTL_SCAN_HISTORY_L t
        set t.ENABLED_FLAG='N',
        LAST_UPDATED_DATE = sysdate,
        LAST_UPDATED_BY =  #{empNo,jdbcType=VARCHAR}
        where  ENABLED_FLAG='Y'
        and t.WORK_ORDER = #{workOrder,jdbcType=VARCHAR}
        and t.MOUNT_TYPE = #{mountType,jdbcType=VARCHAR}

    </update>

    <select id="countHistoryByWorkOrder" parameterType="com.zte.domain.model.SmtMachineMTLHistoryL" resultType="int">
        select count(t.HEADER_ID)
        from SMT_MACHINE_MTL_SCAN_HISTORY_L t
        where t.enabled_flag ='Y'
        and t.work_order = #{workOrder,jdbcType=VARCHAR}
        and t.line_code = #{lineCode,jdbcType=VARCHAR}
        and t.module_no = #{moduleNo,jdbcType=VARCHAR}
        <if test="mountTypeList != null and mountTypeList.size != 0">
            AND t.mount_type in
            <foreach collection="mountTypeList" item="mountType" index="index" open="("  separator="," close=")">
                #{mountType,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>

    <select id="lowLevelGetSmtMachineL" parameterType="com.zte.domain.model.SmtMachineMTLHistoryL"
            resultType="com.zte.domain.model.SmtMachineMTLHistoryL">
        select t.create_date, t.work_order, t.line_code, t.object_id
        from SMT_MACHINE_MTL_SCAN_HISTORY_L t
        where t.enabled_flag ='Y'
        and t.mount_type = '2'
        and (t.operate_msg is null
        or t.operate_msg = '')
        and t.line_code = #{lineCode,jdbcType=VARCHAR}
        AND t.work_order in
        <foreach collection="workOrderList" item="item" index="index" open="("  separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        AND t.object_id in
        <foreach collection="objectIdList" item="item" index="index" open="("  separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
</mapper>
