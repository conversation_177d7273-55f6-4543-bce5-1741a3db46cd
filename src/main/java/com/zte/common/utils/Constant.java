package com.zte.common.utils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 常量
 *
 * <AUTHOR>
 */
public interface Constant {
    String DATE_FORMAT_YYYYMMDD = "yyyy-MM-dd";
    String TECH_FILE_TYPE_SET = ".xlsx.xls.pdf.doc.docx.png.jpeg.jpg.bmp";
    String SOURCE_FACTORY = "源工厂：";
    String DIS_FACTORY = "需同步工厂：";
    String PROD = "批次：";

    String ITEM_TYPE_IS_A = "A";
    String SYNCHRONIZE_SPM_DATA_TO_CORRECT_FACTORY = "同步错工厂批次到正确工厂邮件提示";
    String ERROR_MSG_NOT_HAVE_DATA = "该数据在本地工厂不存在";
    String UPDATE_SPM_TECH_STATUS = "全量更新SPM同步过来的详情表状态";
    String DEFAULT_SYNCHRONIZE_TIME = "1900-01-01 00:00:00";
    Long SECOND_30 = 30L * 1000L;
    String SPECIAL_END_WITH = "-w";
    Long THREE_YEARS_TIMES = 3L * 365L * 24L * 3600L * 1000L;
    String BAR_CHANGE_MEMO_DEFAULT = "SPM数据迁移";
    String SYSTEM_SPM = "SPM";

    String STR_Y = "Y";
    String REGEX_NUMBER = "[^(0-9)]";
    String REGEX_NUMBER_ONLY = "\\d*";
    String NOT_HAVE_CRAFT_SMT = "工艺路径不包含任何SMT类(SMT-A、SMT-B、城堡子卡贴装、手贴)主工序;";
    String NOT_HAVE_CRAFT_TEST = "工艺路径不包含任何测试类(FT、ICT、Test、盘纤、在线测试)主工序;";
    String NOT_HAVE_CRAFT_POWER = "工艺路径不包含任何电源模块类(电源模块、定制电源)主工序;";
    String NOT_HAVE_CRAFT_ASSEMBLY = "工艺路径不包含任何装焊类(DIP、背板、装配、清洗、涂覆、铣板)主工序";
    String NOT_HAVE_ANY_TC = "SPM数据不包含任何TC技改";
    Integer INTEGER_1 = 1;
    String NOT_HAVE_CRAFT = "批次无工艺信息！";
    int INT_256 = 256;
    int INT_95 = 95;
    String CRAFT_SMT = "SMT";
    String CRAFT_TEST = "测试";
    String CRAFT_POWER = "电源模块";
    String CRAFT_ASSEMBLY = "装焊";
    String HAND_STICKERS_CODE = "Q";
    String LOOK_UP_TYPE_LEAD = "1036";
    String LOOKUP_TYPE_20220428 = "20220428";

    String LOOKUP_TYPE_1116 = "1116";
    String LEAD_FLAG = "leadFlag";
    String PRODUCT_LINE_CODE = "productLineCode";
    String STRING_STATISTIC_DATE = "statisticDate";
    String CONCISE_DAILY_SELECT_COL_CACHE = "conciseDailySelectColCacheKey";
    long CACHE_EXPIRED_TIME_30_DAY = 30 * 24 * 60 * 60L;
    String CONCISE_DAILY_EXPORT_FILE_NAME = "-简明日报查询.xlsx";

    String[] CONCISE_DAILY_EXPORT_HEADER = {"提取日期", "批次", "计划跟踪单号",
            "制造料单代码", "单板名称", "单板版本", "环保属性", "工艺路径", "计划数量", "释放日期", "备料区结存",
            "首指令开工时间", "SMT上线时间", "DIP上线时间", "TEST上线时间", "提入库单数量", "入库接收数量",
            "在线数量", "产品大类", "产品小类", "产品类型", "外协标志", "单板代码"};

    String[] CONCISE_DAILY_EXPORT_PROP = {"statisticDate", "prodplanId", "taskNo",
            "mBom", "itemName", "verNo", "leadFlag", "sourceSysNo", "taskQty", "releaseDate", "prepareAreaOnhand",
            "firstOrderStartTime", "smtStartTime", "dipStartTime", "testStartTime", "submitQty", "inboundQty",
            "onlineQty", "externalType", "internalType", "productTypeName", "outerFlag", "itemNo"};

    String CM_DAILY_EXPORT_FILE_NAME = "-整机高级日报查询.xlsx";


    String[] CM_DAILY_EXPORT_HEADER = {
            "提取日期", "批次", "任务号", "料单代码",
            "料单名称", "工艺路径", "环保属性", "任务数量",
            "排产线体", "ERP状态", "ERP完工数", "产品大类",
            "计划组"};

    String[] CM_DAILY_EXPORT_PROP = {
            "statisticDate", "prodplanId", "taskNo", "itemNo",
            "itemName", "sourceSysNo", "leadFlag", "taskQty",
            "productLineCode", "erpStatus", "erpCompletedQty", "externalType",
            "planGroup"};

    String CONCISE_DAILY_EXPORT_TASKQTY = "计划数量";
    String CONCISE_DAILY_EXPORT_PREPAREAREAONHAND = "备料区结存";
    String CONCISE_DAILY_EXPORT_SUBMITQTY = "提入库单数量";
    String CONCISE_DAILY_EXPORT_INBOUNDQTY = "入库接收数量";
    String CONCISE_DAILY_EXPORT_ONLINEQTY = "在线数量";

    // 简明日报，前端映射字段后缀，A表示昨存，B表示转入，C表示转出，D表示结存,E表示线体
    String SUFFIX_A = "A";

    String SUFFIX_B = "B";

    String SUFFIX_C = "C";

    String SUFFIX_D = "D";

    String SUFFIX_E = "E";

    String YESTERDAY_ON_HAND_QTY = "昨存";

    String TURN_IN = "转入";

    String TURN_OUT = "转出";

    String ON_HAND_QTY = "结存";

    String LINE_NAME = "线体";

    String STR_ADD = "\\+";

    String EMAIL = "email";

    String LOOKUP_TYPE_1003010 = "1003010";

    String LOOKUP_TYPE_1004095 = "1004095";
    // 技改解锁类型
    String LOOKUP_TYPE_1004096 = "1004096";
    // 技改解锁状态
    String LOOKUP_TYPE_1004102 = "1004102";
    // 技改单状态
    String LOOKUP_TYPE_1004103 = "1004103";

    int INT_128 = 128;

    String DEFAULT_ITEM_CHECK_PRINT_MAX_LEN = "50";

    String REGEX_BLANK = "\\s*";

    String STR_$ = "$";

    String STR_SPLIT_$ = "\\$";

    String MAX_PRINT_LEN = "maxPrintLen";

    String PRINTER = "printer";

    String TEMPLATE_NAME = "templateName";

    String SCRAP_BILL_STATUS_FINISH = "审批完成";

    String STR_UPPERCASE_A = "A";

    String STR_UPPERCASE_B = "B";

    String STR_LEFT_SQUARE_BRACKETS = "[";

    BigDecimal ITEM_CHECK_DEFAULT_THRESHOLD = new BigDecimal("500");

    String LOOKUP_TYPE_ITEM_CHECK = "1003002";

    String LOOKUP_TYPE_PK_CODE_DELIVERY_ADDRESS = "1003003";

    String LOOKUP_CODE_PRINT_MULTI_LOCATION = "1003002003";

    String LOOKUP_CODE_PRINT_FIRST_LOCATION = "1003002007";

    String LOOKUP_CODE_DISTRIBUTE_THRESHOLD = "1003002002";

    String LOOKUP_CODE_PRINT_TEMPLATE = "1003002004";

    String LOOKUP_CODE_PRINT_MACHINE = "1003002005";

    String LOOKUP_CODE_PRINT_MAX_LENGTH = "1003002006";

    String LOOKUP_CODE_PRINT_REEL_ID_TEMPLATE = "1003002008";

    String LOOKUP_CODE_PRE_ALLOCATION = "1003002001";

    String REELID_FORBIDDEN_CHECK_STATUS = "不可清点";

    String REELID_CAN_CHECK_STATUS = "可清点";

    String REELID_FINISH_CHECK_STATUS = "已清点";

    String ITEM_CHECK_STATUS_FORBID = "-1";

    String ITEM_CHECK_STATUS_CAN = "0";

    String ITEM_CHECK_STATUS_FINISH = "1";

    String INPUT = "input";

    String OUTPUT = "output";

    String HEADER = "header";

    String POINT = ".";

    String POINT_P = "\\.";

    public static final String RECENT_DAY = "recentDay";

    char ZERO_CHAR = '0';

    Long LONG_ZERO = 0L;
    //erp数字字典目录代码
    String ERP_DICTIONARY_CATALOGUE = "1019";
    //标模通用扫描是否去查询erp物料清单
    String ERP_DICTIONARY_SUBITEM_MATERIAL_LIST = "10190015";

    int INT_90 = 90;

    long DAY_MILLISECOND = 3600L * 24L * 1000L;

    int FOUR_ZERO_FOUR = 404;

    int FOUR_ZERO_ZERO = 400;
    int ONE_10000 = 10000;


    int INT_500 = 500;

    int INT_200 = 200;
    int INT_1000 = 1000;

    int INT_2000 = 2000;

    int INT_5000 = 5000;

    int ROW_5000 = 5000;

    int INT_50000 = 50000;

    int INT_40000 = 40000;

    int TWO_ZERO_ONE = 201;

    String SEPARATED_COMMA_COMMA = "','";
    String COLON = ":";

    //USER_ID
    String USER_ID_10241694 = "10241694";
    //来源系统id
    String SOURCE_SYSTEM_ID_100000108713 = "100000108713";
    int INT_20 = 20;

    //标模立库入库返回异步补偿开关
    String MAIN_SN = "mainSnSys";

    String LOOKUP_TYPE_1019 = "1019";
    String LOOKUP_TYPE_1017 = "1017";
    String OVENFLOW_TYPE = "回流炉";
    String LOOKUP_CODE_10190014 = "10190014";

    //批量过站扫描一次提交数量限制
    String LOOKUP_CODE_2040 = "2040";
    String LOOKUP_CODE_2040001 = "2040001";

    //子卡提交入库异步推送ERP开关
    String LOOKUP_CODE_2044 = "2044";
    String LOOKUP_CODE_2044001 = "2044001";
    String LOOKUP_CODE_2044002 = "2044002";
    String SUB_CARD_ERP_ERROR_EMAIL_SUB = "子卡入库单推送异常，收件人数据字典配置：2044002";
    String SUB_CARD_ERP_ERROR_ERP_ID = "异常数据ERP_ID:";
    String SUB_CARD_ERP_ERROR_LOG_NAME = "子卡入库单推送异常日志";
    String CONFIRM_UPDATE = "业务确认更新";
    String TIMING_COMPENSATION = "定时补偿";

    String LOOKUP_TYPE_2754 = "2754";
    String LOOKUP_TYPE_2755 = "2755";
    String LOOKUP_TYPE_1289 = "1289";
    String LOOKUP_CODE_1289003 = "1289003";

    /**
     * 整机工序数据字典
     */
    String LOOKUP_VALUE_221103 = "221103";


    //上料表查询虚拟站位更改权限
    String DATA_DICTIONARY_QTY_MODIFY_PERMISSON = "6108";

    //手补料
    String VIRTUAL_ITEM_SUPPLEMENT = "VIRTUAL_ITEM_SUPPLEMENT";

    //虚拟站位物料机台
    String VIRTUAL_ITEM_MACHINE_NO = "XNJT01";

    //虚拟站位物料模组
    String VIRTUAL_ITEM_MODULE_NO = "XNMZ01";

    String LIST_TYPE_MES = "mes+"; // 系统类型

    String SYS_TYPE_MES = "MES"; // 系统类型 MES

    String SYS_TYPE_STEP = "STEP"; // 系统类型 STEP

    String FLAG_E = "E";

    String HASHTAG = "#";

    String ERP_LOG = "erpLog";

    String FLAG_Y = "Y";
    String k1Stock = "半成品K1库";
    String zjStock = "整机车间库";

    String FLAG_N = "N";
    String FLAG_ALL = "ALL";

    String ON = "ON";

    String OFF = "OFF";

    String POINTLOC = "pointLoc";

    String BOARD_ASSEMBER = "单板装配"; // 单板装配 工艺段

    String BOARD_ASSEMBER_INPUT = "P0001"; // 单板装配投入 工序代码

    String BOARD_TEST = "单板测试"; // 单板测试 工艺段

    String BOARD_TEST_INPUT = "P0002"; // 单板测试投入 工序代码

    String REPAIR_ING = "P0003"; // 维修中

    String REPAIR_COMPLETE = "P0004"; // 维修完毕

    String WAREHOUSE_ENTRY_CODE = "P0006";// 入库

    String STORE_IN = "P0006"; // 入库

    String IS_SUBMITTED = "已提交"; // 状态为已提交

    String IS_START = "已开工"; // 状态为已开工

    String DONE = "已完工"; // 状态为已开工

    String IS_HANG_UP = "挂起"; // 状态为挂起

    String ASTERISK = "*";

    String IS_DRAW_UP = "拟制中"; // 状态为挂起

    String WORK_FINISH_BREAK_STATUS = "零星板挂起";// 指令挂起状态 "零星板挂起"

    String IS_SCHEDULING = "已排产"; // 状态为挂起

    Integer HOUR_SECOND = 3600 * 1000;

    String IN_STATUS_HAS_HANG_UP = "'已提交', '已开工', '挂起'"; // in字符串包含挂起
    String SCAN_STATUS = "'已提交', '已开工', '零星板挂起'"; // in字符串包含挂起

    String IN_STATUS_HAS_NO_HANG_UP = "'已提交', '已开工'"; // in字符串不包含挂起

    String IN_STATUS_SUBMITTED = "'已提交'"; // in字符串不包含挂起

    String PCB_SN = "pcbSn"; // 分板板号

    String WORK_ORDER_NO = "workOrderNo"; // 指令编号

    String USER_ID = "userId"; // 人事ID

    String SCAN_FULL = "FULL"; // 分板扫描时用作清空暂存表

    String WRITE_BACK_FLAG = "1008"; // 数据字典中回写step标志
    // 数据字典中 入库提交回写开关数据字典
    int RK_WRITE_BACK_LOOKUP_CODE = 10080002;
    // 数据字典中 报废入库提交回写开关数据字典
    int SCRAP_BILL_WRITE_BACK_LOOKUP_CODE = 10080008;

    // 数据字典中配置的物料代码对应任务在条码打印完成后无需注册（不在imes生产）
    String SYS_LOOK_UP_1290 = "1290";

    // 转机扫描备料校验开关
    String LOOK_UP_TRANSFER_SCAN_CHECK = "1095";

    String LOOK_UP_TRANSFER_SCAN_CHECK_MODE = "1095001";

    String LOOKUP_TYPE_220825 = "220825";
    String HTTP_HEADER_X_TENANT_ID_VALUE = "10001";

    String REDIS_KEY_SYNC_PSN_AND_SN = "REDIS_KEY_SYNC_PSN_AND_SN";
    // 下划线
    String UNDER_LINE = "_";

    /**
     * 数据字典中 入库提交类型（bill_type);
     * BILL_TYPE_LOOKUP_TYPE="1048"
     */
    String BILL_TYPE_LOOKUP_TYPE = "1048";
    int BILL_TYPE_LOOKUP_CODE_ZJ = 10480001;
    int BILL_TYPE_LOOKUP_CODE_DB = 10480002;
    int BILL_TYPE_LOOKUP_CODE_SCRAP = 10480006;
    int BILL_TYPE_LOOKUP_CODE_DBFX = 1048009;
    String STOCK_TYPE_K2 = "半成品K2库";
    String STOCK_TYPE_MODE_WORKSTATION = "整机车间库";
    String STOCK_TYPE_HOME_BOARD = "家端单板库";
    String BILL_TYPE_SINGLE_CARD = "2";
    String BILL_TYPE_SON_CARD = "4";

    String BILL_TYPE_OUT_CARD = "10";
    String BILL_TYPE_ZJ = "3";

    String LOOKUP_CODE_1004037003 = "1004037003";
    String LOOKUP_CODE_1004037052 = "1004037052";
    String BILL_TYPE_XBCK_SB = "XBCK_SB";
    String BILL_TYPE_XBCK_YB = "XBCK_YB";
    String MOUNT_TYPE_TWO = "'2'";
    // 用于保存追溯导出数据文档云文件id
    public static final String TRACING_DATA_INFO_REDIS_KEY = "INFO:TRC_DT:";

    String COMMA = ",";

    String BAR = "-";

    String SINGLE_QUOTE = "'";

    String SN = "SN";
    String BILL_NO = "billNo";
    String IN_SNS = "inSns";

    String WORKORDER = "WORKORDER";

    String INSERT = "INSERT";

    String UPDATE = "UPDATE";

    String GLUE_INJECTION = "注胶";

    String WAREHOUSE_ENTRY = "入库";

    String ALREADY_WAREHOUSE_ENTRY = "已入库";
    String LEFT_WAREHOUSE_ENTRY = "待投数量";
    String SMT_DELIVER_QTY = "SMT待转交数量";
    String DIP_DELIVER_QTY = "DIP待转交数量";
    String ASS_DELIVER_QTY = "ASSY待转交数量";
    String CRAFTSECTION_WAREHOUSE_ENTRY = "SMT-A,SMT-B,DIP,ASSEMBLY,维修,装配,高温,调试,包装,流水线";
    String CRAFTSECTION_SMT_A = "SMT-A";
    String CRAFTSECTION_SMT_B = "SMT-B";
    String CRAFTSECTION_DIP = "DIP";
    String CRAFTSECTION_ASSEMBLY = "ASSEMBLY";
    String CRAFTSECTION_REPAIR = "维修";
    String CRAFTSECTION_CLEAN = "清洗";
    String CRAFTSECTION_BACKPLANE = "背板";
    String CRAFTSECTION_INSERTION = "INSERTION";
    String CRAFTSECTION_COATING = "COATING";
    String CRAFTSECTION_CHECK_WELDING = "CHECK_WELDING";
    String CRAFTSECTION_MILLING_PLATE = "MILLING_PLATE";
    String NOT_SCAN = "漏打";
    String EXPORT_TAG = "exportExecl";
    String DIP_FLAG = "DIP";

    String FAIL = "FAIL";
    String LPN = "lpn";
    String SYSTEM = "system"; // 系统
    String EMP_00000000 = "00000000"; // 系统

    String WORK_STATION_SMT_DOWN = "SMT下线扫描点";
    String WORK_STATION_DIP_DOWN = "DIP下线扫描";
    String WORK_STATION_ASSY_BIND = "ASSY绑定";
    String WORK_STATION_ASSY_UP = "ASSY上线";
    String WORK_STATION_ASSY_DOWN = "ASSY下线";
    String WORK_STATION_REPORT = "'SMT下线扫描点','DIP下线扫描','ASSY下线','ASSY上线','ASSY绑定'";

    /**
     * 上料占位唯一约束值定义
     */
    String UNIQUE_KEY_BSBD_LOCATION = "UK_BSBD_LOCATION_UNIQUE_KEY";

    /**
     * 测试通过
     */
    String TEST_Y = "0";

    /**
     * 测试不通过
     */
    String TEST_N = "1";

    String PROCESS_TEST = "自动测试";

    /**
     * 长沙工厂ID
     */
    String FACTORY_ID_CENTER = "51";
    String FACTORY_ID_CS = "52";
    String FACTORY_ID_XA = "56";
    String FACTORY_ID_SZ = "53";
    String FACTORY_ID_HY = "55";
    String FACTORY_ID_NJ = "58";

    String SN_LIST_KEY = "snList";// 条码集合map键

    String ERP_WSDL_MSG_STATUS_SUCCESS = "S";

    String ERP_WSDL_MSG_STATUS_ERROR = "E";

    String ERP_DOING_STATUS = "9";
    String ERP_TODO_STATUS = "0";
    String ERP_DONE_STATUS = "1";
    String ERP_ERROR_STATUS = "5";
    // erp调用未知异常,该状态不允许拒收
    String ERP_UNKNOWN_ERROR_STATUS = "6";
    // 1:待处理,5:erp业务异常,6:erp未知异常(如超时)
    String ERP_ALLTODO_STATUS = "'0','5','6'";
    String REPAIR_TODO_STATUS = "'0','5'";
    String STR_3 = "3";
    String STR_4 = "4";
    String STR_5 = "5";
    String STR_0 = "0";
    String STR_1 = "1";
    String STR_2 = "2";
    String STR_10 = "10";
    String STR_11 = "11";

    String PROD_ADDRESS = "http://server.hemans.zte.com.cn:32610/api/bll/dip/scan";

    String STATIC_VALUES = "单板_调测_单板下线检查";

    String SMT = "SMT";

    String DIP = "DIP";

    String TEST = "Test";
    String ICT = "ICT";

    String DIP_GB = "DIP_GB";

    String STATION_BIND = "工装绑定";

    String MSG_OK = "OK";

    /**
     * 长沙工厂ReelId前缀
     */
    String CS_PKCODE_PREFIX = "PKCS";

    /**
     * PKCode长度
     */
    int PK_CODE_LENGTH = 16;

    public static final int INT_64 = 64;


    /**
     * 逗号分隔符
     */
    public static String SEPARATED_COMMA = ",";

    String REPAIR_SCRAP = "P0005"; // 报废

    String REPAIR_WAREHOUSE_CODE = "P0243"; // 入库（bs_process表数据）

    String STR_NUMBER_ZERO = "0";
    String STR_NUMBER_ONE = "1";
    String STR_NUMBER_1000 = "1000";
    String STR_NUMBER_TWO_THOUSAND = "2000";

    String REPAIR_TWO = "S1053";  //二维 （bs_process表数据）

    String REPAIR_ONE = "S1054";  //一维 （bs_process表数据）

    //二维
    int REPAIR_STATION_TWO = 2;
    //一维
    int REPAIR_STATION_ONE = 1;

    String REPAIR_FROM_STATION = "单板生产";

    String REPAIR_FROM_ZJ_DB = "整机单板送修";

    String FROM_STATION_MACHINE = "整机";

    String STEP = "单板";

    String FROM_STATION_SEMIS = "半成品库";
    String WAREHOUSE_OUT = "出库";

    String MAC_ADDRESS = "1055";

    String FLAG_ON = "ON";

    String FLAG_ON_LOWERCASE = "on";

    String PROCESS_CODE_WH = "N";

    String FEW_BIND = "零星工装绑定";

    //SMT上料扫描类型
    String LOOK_UP_MOUNT_STATUS = "1035";

    String LOOK_UP_REPAIR_STATUS = "1056";  //维修状况字典
    String REPAIR_STATUS_WAIT = "10560001";  //待维修
    String REPAIR_STATUS_COMPLETE = "10560002";  //维修完成
    String REPAIR_STATUS_RESTORE = "10560003";  //维修返还
    String REPAIR_STATUS_SCRAP = "10560004";  //维修报废
    String REPAIR_STATUS_DISPLACE = "10560007";//维修置换


    String REPAIR_STATUS_WAIT_MSG = "待维修";
    String REPAIR_STATUS_COMPLETE_MSG = "维修完成";
    String REPAIR_STATUS_RESTORE_MSG = "维修返还";
    String REPAIR_STATUS_SCRAP_MSG = "维修报废";
    String REPAIR_STATUS_FICTION_MSG = "拟制中";
    String REPAIR_STATUS_TO_BE_RECEIVED_MSG = "待接收";
    String REPAIR_STATUS_DISPLACE_MSG = "维修置换";
    String REPAIR_STATUS_UNKNOWN_MSG = "维修状态未知";

    // 维修状态数据字典
    String LOOK_UP_TYPE_REPAIR_STATUS = "1056";
    // 楼栋状态数据字典
    String LOOK_UP_TYPE_BUILDING_STATUS = "1149";
    // 返修来源数据字典
    String LOOK_UP_TYPE_1146 = "1146";
    // 申请部门数据字典
    String LOOK_UP_TYPE_DEPT_STATUS = "2001";
    // 申请班组数据字典
    String LOOK_UP_TYPE_GROUP_STATUS = "2002";
    // 拟制中
    String REPAIR_STATUS_FICTION = "10560005";
    // 待接收
    String REPAIR_STATUS_TO_BE_RECEIVED = "10560006";

    String AGING_IN = "AGING上线"; //进老化工站
    String AGING_OUT = "AGING下线"; //出老化工站

    String AGING_INPUT = "AGING_INPUT"; //进老化子工序
    String AGING_OUTPUT = "AGING_OUTPUT";//出老化子工序

    String AGING_TYPE_IN = "in";

    String AGING_TYPE_OUT = "out";

    String API_RESULT_CODE_SUCCESS = "0000";
    String API_RESULT_CODE_FAIL = "0001";

    String SUCCESS = "0000";

    String BUS_ERROR = "0005";

    String WORKFINISH_STARTWITHSMT = "SMT";// 指令工艺段

    String DIP_CRAFT = "装焊";

    String DIP_CRAFT_STR = "DIP";

    String TEST_CRAFT = "测试";

    String TEST_CRAFT_STR = "FT";

    String PS_MODULE_CRAFT = "部件整机";

    String PARTS_CRAFT = "电源模块";

    int SPLITSIZE_HUNDRED = 100;

    String PROCESS_CODE_OB = "P1063";

    /*
     * 批量处理每批次大小
     */
    int BATCH_SIZE = 100;

    int BATCH_SIZE_30 = 30;

    int BATCH_SIZE_NINE_HUNDRED = 900;

    int BATCH_SIZE_499 = 499;

    int IN_MAX_BATCH_SIZE = 900;

    int SELECT_BATCH_SIZE = 20;
    String DAILY_REPORT_SIZE = "分批查询每批数量";
    String DAILY_REPORT_LOOKUPTYPE = "1152";

    String LOG_BEGINMSG_DAILYREPORT = "高级日报日志：";
    String LOG_BEGINMSG_WAREHOUSEINFO = "提交入库单日志：";

    /**
     * 整机可投入子工序
     */
    String LOOKUP_TYPE_ZJ_PROCESS = "1068";

    String TRACE_SOURCE_SPI = "SPI";
    String TRACE_SOURCE_PMJ = "PMJ";

    int REPAIR_STATUS_SUBMIT = 20;


    String FINISH_WORK = "已完工";
    String LXBGQ = "零星板挂起";

    String PRINTED = "已打印";
    String PROCESS = "子工序";
    String CHECK_SUCCESS = "success";

    String STR_EMPTY = "";
    String SPACE = "\\s";
    String TYPE_A = "A";
    String SHARED_MATERIALS = "2";

    String CODE_SUCCESS_STR = "\"0000\"";


    String COMPOSITE_PREPARE = "综合备料扫描";

    String FEEDER_BINDED = "Feeder绑定";

    String OBJECT_TYPE_REEL_ID = "ReelId";
    String SCLLJJ = "生产领料交接";
    String SCLLJJFROMPDA = "SCLLJJ";


    long EXPIRE_TIME = 12 * 30 * 24 * 60 * 60;

    //字典  字典代码字段名
    String FIELD_LOOKUP_CODE = "lookupCode";

    String FIELD_LOOKUP_TYPES = "lookupTypes";

    //字典  字典代码字段名
    String FIELD_LOOKUP_TYPE = "lookupType";

    //字典含义字段名
    String FIELD_LOOKUP_MEANING = "lookupMeaning";

    String FIELD_LOOKUP_ATTRIBUTE1 = "attribute1";

    //字典英文名
    String FIELD_DESCRIPTION_ENG = "descriptionEng";

    //字典英文名
    String FIELD_DESCRIPTION_CHIN_V = "descriptionChinV";
    String FIELD_DESCRIPTION_CHIN = "descriptionChin";

    //时分秒分割符号:f
    String SYMBOL_COLON = ":";

    String SYMBOL_COMMA = ",";

    int INT_0 = 0;

    int INT_1 = 1;

    int INT_2 = 2;

    int INT_3 = 3;
    int INT_4 = 4;

    int INT_5 = 5;
    int INT_6 = 6;

    int INT_7 = 7;
    int INT_8 = 8;

    int EDITABLE = 7;

    int INT_12 = 12;
    int INT_30 = 30;
    String CONTAINER_SPLIT = "容器拆分";

    int INT_60 = 60;
    int INT_600 = 600;

    int INT_23 = 23;

    int INT_15 = 15;

    int INT_50 = 50;

    int INT_59 = 59;

    int INT_400 = 400;

    int INT_100 = 100;

    int INT_3000 = 3000;
    int INT_4000 = 4000;

    int INT_47 = 47;

    int INT_58 = 58;

    int INT_300001 = 30000 * 1;
    int INT_300002 = 30000 * 2;
    int INT_300003 = 30000 * 3;
    int INT_300004 = 30000 * 4;
    int INT_300005 = 30000 * 5;
    int INT_300006 = 30000 * 6;
    int INT_300007 = 30000 * 7;
    int INT_300008 = 30000 * 8;

    String STRINGT_00001 = "00001";

    int INT_NEGATIVE_1 = -1;

    String STRING_BUS_TYPE = "busType";

    String STRING_GROUP_TYPE = "groupType";

    int EMAIL_BUS_TYPE_OUTPUT = 1;

    int EMAIL_BUS_TYPE_OUTPUT_FAILURE = 3;

    int EMAIL_GROUP_TYPE_MAIL_TO = 0;

    String LINE_TYPE = "SMT";// 线体工序类型

    String AIMEX_MACHILE = "AIMEX";
    String LOCATION_COLUMN = "站位";
    String ITEMCODE_COLUMN = "物料代码";
    String AM_COLUMN = "Am";
    String BM_COLUMN = "Bm";
    String LEFT_BRACKET = "(";
    String RIGHT_BRACKET = ")";

    String STATE_STRING = "state";

    String LAST_UPDATED_DATE = "lastUpdatedDate";
    String ASC = "asc";
    String DESC = "desc";

    String STYLE = "style";
    String SUPPLIER_NAME = "supplierName";
    String PRODUCT_DATE = "productDate";
    String BG_BRAND_NO = "bgBrandNo";
    String PRODUCTCODE = "productCode";


    String LOG_ERROR = "点对点出错:";
    String LOG_DICTIONARY_ERROR = "点对点出错:";
    String LOG_PROCESS_ERROR = "点对点调用getProcessName失败";
    String LOG_QTY_ERROR = "更新QTY数量失败";
    String REPAIR = "维修";
    String REPAIR_MACHINE_ORDER = "整机送修单";
    String REPAIR_MACHINE_RCV_ORDER = "整机返还单";
    String REPAIR_SEMIS_RCV_ORDER = "半成品返还单";
    String REPAIR_THREE = "3";
    String REPAIR_FOUR = "4";

    long DAY_EXPIRE_TIME = 24 * 60 * 60;

    String DIP_STR = "dip";
    String ASSY_OUT_PUT_STR = "assyOutput";
    String CRAFT_SECTION_STR = "craftSection";
    String RC_CRAFT_SECTION = "rcCraftSection";
    String DIP_OFFLINE_SCAN = "dipOfflineScan";
    String ASSY_BINDING = "assyBinding";

    String OUT_BOUND = "CK";

    String PHONE_SUB_CARD_TYPE = "M";
    String SUB_CARD_TYPE_CONTENT = "子卡入库单";
    String MAIN_SN_SCAN_SUCCESS = "1";
    String BINDED_AND_PASSWORKSTATION_COMPLETE = "2";
    String MAINSN_BINDED_COMPLETE = "3";
    String SUB_SN_SCAN_SUCCESS = "4";

    String SYS_TYPE_WMES = "WMES"; // 系统类型 WMES
    String SEQUENCE_CODE = "序列码";
    String ASSEMBLY_WRITE_BACK = "1073";

    String PICKS_TATUS_ING = "对比中";
    String PICKS_TATUS_END = "对比完毕";

    String PICKS_TATUS_ING_NUM = "1";
    String PICKS_TATUS_END_NUM = "2";

    String CYC_RESULT_STATUS_ONE = "未开始";
    String CYC_RESULT_STATUS_TWO = "进行中";
    String CYC_RESULT_STATUS_THREE = "已完成";
    String WMS_STATUS_NAME_0030 = "执行中";
    String WMS_STATUS_NAME_0040 = "已关闭";

    String PRE_MATERIAL_LOOKUP_CODE = "7580023001";

    String STR_AB = "AB";
    String ABLISH = "已废止";

    String DONE_STR = "done";

    String OM_REPAIR_RETURN = "维修返还";

    String ERP_STATUS_SUC = "1";

    String OM_ERP = "维修ERP入库";

    String RETURN_FINISH = "完工退料";

    String STRING_6 = "6";

    String STRING_7 = "7";

    String STRING_0 = "0";

    String FILEDOWNLOADURL ="fileDownloadUrl";

    int NUMBER_ONE = 1;

    String UNBIND_BY_MAIN_SN = "按主条码解绑";

    String UNBIND_BY_SUB_SN = "按子条码解绑";

    String UNBIND_ERROR = "解绑失败";

    String WRITE_SEMI_ERROR = "写交易失败";

    String NO_BIND_INFO = "没有绑定信息，请确认！";

    String LOCAL_RESOLVER = "localeResolverMes";

    String LOOKUP_TYPE_BIND_TYPE = "1210";

    //入库单提交，指令上线，通用扫描，过站装箱扫描是否更新任务交期日期
    String LOOKUP_TYPE_6031_TYPE = "6031";

    String LOOKUP_TYPE_6031001_TYPE = "6031001";

    int EXPORT_DATA_INTERVAL_MONTH = -2;

    String ASSEMBLER_RELA_FILE_NAME = "组装关系查询.xlsx";

    String PRODUCTION_SEND_A_SINGLE = "生产送修单";

    String ASSEMBLER_RELA_MODEL_NAME = "wipExtendIdentification.xlsx";

    String X_EMP_NO = "X-Emp-No";
    String X_EMP_NO_SMALL = "x-emp-no";

    String X_ORIGIN_SERVICENAME = "X-Origin-ServiceName";

    String X_ORG_ID = "X-Org-Id";
    String X_LANG_ID = "X-Lang-Id";

    String X_AUTH_VALUE = "X-Auth-Value";

    String STR_CODE = "code";

    String STR_BO = "bo";

    String STR_MSG = "msg";

    String STR_SUCCESS = "SUCCESS";

    String STR_ENCRYPTED = "encrypted";

    String STR_RIGHTS = "rights";

    int CHUNK_SIZE = 2 * 1024 * 1024;

    String REQUSET_ERROR = "0001";

    String BUSINESS_ERROR = "0002";

    String INVALID_PERMISSION = "0003";

    int STATUS_CODE = 200;

    String SUCCESS_CODE = "0000";

    String ITEM_NO = "itemCode";

    String WORK_ORDER = "workOrder";

    String MODULE_NO = "moduleNo";

    String MACHINENO_NO = "machineNo";

    String MOUNT_TYPE = "mountType";

    String OBJECT_ID = "objectId";

    String LOCATION_NO = "locationNo";

    String NO_LOCATION = "noLocation";

    String LINE_CODE = "lineCode";

    String IN_LINE_LIST = "inLineList";

    String PICK_STATUS = "pickStatus";

    String COMPARE_FINISH = "2";

    //物料防錯管控字典
    String LOOKUP_TYPE_ITEM_ERROR_CTRL_TYPE = "1211";

    String LOOKUP_VALUE_QC_TRANSFORM = "1211001";

    String LOOKUP_VALUE_QC_RE_CHECK = "1211002";

    String MOUNT_TYPE_QC_TRANSFORM = "3";

    String MOUNT_TYPE_QC = "9";

    String MOUNT_TYPE_FEEDER_INSERT = "12";

    String MOUNT_TYPE_FEEDER_EXTRACTION = "13";

    int MOUNT_TYPE_DISTRIBUTION_MANYY_TIMES = 11;

    String MOUNT_TYPE_RECEIVE = "2";

    int CONSTAN_INT_10 = 10;

    public static final String TASK_FINISHED = "已完工";
    String TASK_NO = "taskNo";
    String MAX_CNT = "maxCnt";
    String FACTORY_ID = "factoryId";
    String ITEM_NO_STR = "itemNo";
    String PRODPLAN_ID = "prodplanId";
    String PRODPLAN_NO = "prodplanNo";
    String WAREHOUSE_ENTRY_INFO = "warehouseEntryInfo";

    Long MAX_SN_CNT = 500000L;

    String SN_LOW_CASE = "sn";

    String MM = "mm"; // 分板板号

    String UPP_CASE_C = "C"; // 分板板号

    String LOW_CASE_C = "c"; // 分板板号

    String SYS_LOOK_CHECK_ATTR = "1078"; //环保属性校验数据字典

    String ENV_LOOK_UP_TYPES = "1036"; //环保属性校验数据字典

    String SYS_LOOK_6927 = "6927"; //测真值预警偏差
    String SYS_LOOK_6927001 = "6927001"; //测真值预警偏差下限
    String SYS_LOOK_6927002 = "6927002"; //测真值预警偏差上限

    String SYS_LOOK_CHECK_TECHINAL = "1079"; //技改校验数据字典

    String FLAG_OFF = "OFF";

    String SYS_LOOK_CRAFTSECTION = "1004";//主工序数据字典值

    String SYS_LOOK_1092 = "1092";//手动过站装箱扫描高温切换开关

    String SYS_LOOK_1092001 = "1092001";//手动过站装箱扫描高温切换开关

    String SYS_LOOK_PARAM = "1081"; //标签规则数据字典

    String SYS_LOOK_PARAM_ZJ_SN = "1081001"; //整机生成标签规则

    String SYS_LOOK_PARAM_WL_ID = "1081004"; //

    String SYS_LOOK_TYPE_CURR_PROCESS = "2000000000";//数据字典--工序数据所对应的字典编号

    String SYS_LOOK_TYPE_EMP = "2000000001";//数据字典--当前用户数据所对应的字典编号

    String SYS_LOOK_TYPE_ORDER_REJECT_EMP_IN_RECEIVED = "2000000003";//数据字典--"待接收"状态下具有送修单驳回权限的用户数据所对应的字典编号
    String SYS_LOOK_TYPE_ORDER_REJECT_EMP_IN_MAINTENANCE = "2000000004";//数据字典--待维修"状态下具有送修单驳回权限的用户数据所对应的字典编号

    String BARCODE_FORM_ZJ = "整机";

    String SYS_LOOK_DQAS = "1080";

    // 是否启用条码中心
    String SYS_LOOK_2344 = "2344";
    // 是否启用条码中心
    String SYS_LOOK_2344001 = "2344001";
    // 条码中心白名单
    String SYS_LOOK_2344002 = "2344002";

    // 记录同步最后执行时间数据字典

    String SYS_LOOK_6928 = "6928";
    String SYS_LOOK_6928001 = "6928001";
    String SYS_LOOK_7000 = "7000";
    // 记录大板条码和小板条码最后同步时间(作为下次同步开始时间)
    String SYS_LOOK_7000001 = "7000001";
    // 记录发料顺序计算的最后成功时间
    String SYS_LOOK_7000003 = "7000003";
    // 装配关系推送SPM最后同步时间
    String SYS_LOOK_7000005 = "7000005";
    // 最后一次同步lms单据状态至imes的时间
    String SYS_LOOK_7000006 = "7000006";
    // 最后一次同步lms条码IMU至imes的时间
    String SYS_LOOK_7000007 = "7000007";
    // 定时任务_生产执行_入库数据统计接口最后同步时间(作为下次同步开始时间)
    String SYS_LOOK_7000004 = "7000004";
    // 取本地工厂技改单数据最后同步时间
    String SYS_LOOK_7000008 = "7000008";
    // 获取扫描切换imes启用时间数据字典
    String SYS_LOOK_7000010 = "7000010";

    // 需要同步lms条码状态的imu值配置
    String SYS_LOOK_2211 = "2211";
    // 功能开关统一数据字典
    String SYS_LOOK_7001 = "7001";
    // 追溯查询是否使用旧逻辑开关(不配置使用新逻辑。配置为Y则使用旧逻辑)
    String SYS_LOOK_7001001 = "7001001";

    // 条码中心地址
    String MP_CODE_KEY_1004052 = "1004052";
    int BARCODE_BASIC_URL = 1004052001;
    int BARCODE_TENANT_ID = 1004052002;
    int BARCODE_WHITE_EMP = 1004052003;
    int BARCODE_AUTH_VALUE = 1004052004;
    int BARCODE_UPDATE_URI = 1004052005;
    int BARCODE_EXPAND_QUERY_URI = 1004052006;
    int BARCODE_QUERY_URI = 1004052007;
    int BARCODE_REGISTER_UIR = 1004052008;
    int BARCODE_GENERATE_UIR = 1004052009;
    int BARCODE_BLANK_GENERATE_URI = 1004052010;
    int BARCODE_X_AUTH_ACCESSKEY = 1004052012;
    int BARCODE_APPID = 1004052011;

    String X_AUTH_ACCESSKEY = "X-Auth-AccessKey";

    // APS服务地址
    String MP_CODE_KEY_1005052 = "1005052";
    String MP_CODE_KEY_1005052001 = "1005052001";

    String X_TYPE = "xType";

    String JSON_CODE = "code";
    String JSON_BO = "bo";
    String JSON_LIST = "list";

    String BO = "bo";
    String OTHER = "other";
    String TOTAL_COUNT = "totalCount";

    String STRING_EMPTY = "";

    Short TINT_ZERO = 0;

    String ZERO_PERCENT = "0%";

    String STRAGETY_ONE = "1";// 转产策略1

    String STRAGETY_TWO = "2";// 转产策略2

    String KEY_MACHINENO_MODULENO_LOCATIONNO = "%s;%s;%s";
    String KEY_LOCATIONNO_ITEMCODE = "%s,%s";
    //标模中式 对接   维修送修  条码校验
    String LOOKUP_TYPE_DQAS_REPAIR_CHECK = "1080002";

    //标模中式 对接   维修送修  数据推送
    String LOOKUP_TYPE_DQAS_REPAIR_POST = "1080003";

    //送修扫描中式校验开关
    String LOOKUP_DQAS_SEND_CHECK_SWITCH_ = "1080004";
    //送修保存数据推送中式开关
    String LOOKUP_DQAS_SEND_SAVE_SWITCH_ = "1080005";
    //维修返还条码扫描中式校验开关
    String LOOKUP_DQAS_REPAIR_CHECK_SWITCH_ = "1080006";
    //维修返还保存数据推送中式开关
    String LOOKUP_DQAS_REPAIR_SAVE_SWITCH_ = "1080007";
    //维修返还设置
    String LOOKUP_REPAIR = "2015";
    //维修返还  删除FIS数据  值Y/N表示是否删除，属性1为FIS接口地址。
    String LOOKUP_REPAIR_FIS_SWITCH_URL = "2015001";
    //维修返还 修改MES二维交易数据 值Y/N表示是否修改。
    String LOOKUP_REPAIR_MES_SWITCH = "2015002";
    //维修返还 修改MES二维交易数据 重试次数。
    String LOOKUP_REPAIR_MES_RETRY_THRESHOLD = "2015003";
    //维修录入 iFIS维修物料管理销账数量（物料可用数量）更新接口
    String LOOKUP_REPAIR_IFIS_CONSUME = "2015005";
    //维修录入 获取iFIS维修人员领料信息
    String LOOKUP_REPAIR_IFIS_CONSUME_PROVIDE = "2015006";
    //维修录入 幂等控制
    String REDIS_KEY_REPAIR_INPUT_IDEMPOTENT = "REPAIR_INPUT:";
    String REDIS_KEY_COLLECTION_CODE_SCAN = "COLLECTION_CODE_SCAN:";
    //维修返还 修改MES二维交易数据 默认重试次数。
    int MES_RETRY_THRESHOLD_DEFAULT = 3;
    //维修返还 修改MES二维交易数据 重试的redis锁
    String REDIS_KEY_MES_RETRY = "REDIS_KEY_MES_RETRY";
    //维修返还  FIS删除类型
    String LOOKUP_FIS_DELTYPE = "2016";
    //维修返还 来源  组装段
    String FROM_STATION_ASSEMBLE = "组装段";
    //维修返还 来源  单板维修库
    String FROM_STATION_REPAIR_WAREHOUSE = "单板维修库";
    //维修返还 来源  组装单板维修库
    String FROM_STATION_ASSEMBLE_REPAIR_WAREHOUSE = "组装单板维修库";
    //维修返还 来源  调拨
    String FROM_STATION_ALLOCATION = "调拨";
    //SMT扫描率日报数据字典
    String SINGLE_BATCH_REPOET_LOOKEUP_TYPE = "1085";
    // 条码与任务解绑工序工站配置
    String UNLINK_SN_AND_TASK_LOOKEUP_TYPE = "1087";
    //深圳工厂邮件标题
    String SZ_EMAIL_TITLE = "1085001";
    //深圳工厂邮件收件人
    String SZ_EMAIL_LIST = "1085002";
    //深圳工厂邮件抄送人
    String SZ_EMAIL_CC_LIST = "1085003";
    //河源工厂邮件标题
    String HY_EMAIL_TITLE = "1085004";
    //河源工厂邮件收件人
    String HY_EMAIL_LIST = "1085005";
    //河源工厂邮件抄送人
    String HY_EMAIL_CC_LIST = "1085006";
    String CS_EMAIL_TITLE = "1085007";
    String CS_EMAIL_LIST = "1085008";
    String CS_EMAIL_CC_LIST = "1085009";
    String XA_EMAIL_TITLE = "1085010";
    String XA_EMAIL_LIST = "1085011";
    String XA_EMAIL_CC_LIST = "1085012";
    String NJ_EMAIL_TITLE = "1085013";
    String NJ_EMAIL_LIST = "1085014";
    String NJ_EMAIL_CC_LIST = "1085015";
    String GET_INFORMATION_FAILED = "信息统计失败";
    String CODE = "code";
    String MSG = "msg";
    String RECEIVE_MESSAGE = "kafka接收到消息 " + ":{}";
    String IN_STORE = "入库";
    String CODE_0000 = "0000";
    String CODE_00000 = "00000";
    String AVL_ERROR = "AVL信息查询错误";
    String STATUS = "status";
    String S = "S";
    String E = "E";
    String YES = "是";
    String NO = "否";
    String DELETE = "delete";
    String ERROR = "error";
    String DIP_MATERIAL_SCAN = "DIP上料扫描";

    String BOM_CODE = "bomCode";
    String DEVICE_PRODUCTION_MAINTENANCE = "整机生产维修";
    String DEVICE_VENEER_MAINTENANCE = "整机单板送修";
    String DWX = "待维修";
    String WXZH = "维修置换";
    String DWX_CODE = "10560001";
    String WXZH_CODE = "10560007";
    String WXFH_CODE = "10560003";
    String WXWC_CODE = "10560002";
    String GANG = "_";
    String SN_CODE = "sn";
    String CRAFT_SECTION = "craft_section";

    String RECEPTION_DETAIL_ID = "reception_detail_id";
    String PRE_PROCESS = "pre_process";
    String ATTRIBUTE = "attr";
    String PROBLEMS = "problems";
    String STATE = "state";


    String FORM_SN = "formSn";
    String BO_VALUE = "bo为空";
    String FORM_SN_LIST = "formSnList";
    // 正斜杠
    String FORWARD_SLASH = "/";
    String FLOW_CODE_PARM = "?flowCode=";
    String DELIVERY_PROCESS = "裝焊配送";
    //工序转交接口数据字典目录值
    String TRANSFER_CODE = "1223";
    String TOKEN = "Token";
    String STRFACOTY = "strFactory";
    String STROPERATOR = "strOperator";
    String STRSTORGEFLAG = "strStorgeFlag";
    String LISTPARTCODE = "listPartcode";
    String STRPARTCODE = "strPartcode";
    String STRITEMCODE = "strItemcode";
    String IMES = "iMES";
    String FALSE = "false";
    String TRUE = "true";

    String STR_TRUE = "true";

    String STR_FALSE = "false";
    //楼栋
    String LOOK_UP_BUILD = "1149";
    //报废原因
    String LOOK_UP_SCRAP = "1196";

    // 追溯数据插入配置
    String LOOK_UP_5555001 = "5555001";

    String USR_LOCAL_TOMCAT_LOGS_PATH = "/usr/local/tomcat/logs/";

    //来源系统(1、MES老系统；2、MES微服务系统；3、infor)
    String SOURCE_SYS_ONE = "1";
    String SOURCE_SYS_TWO = "2";
    String SOURCE_SYS_THREE = "3";
    String CONFIRMED = "已确认";
    String REFUSED = "已驳回";
    //上料类型（0:配送绑定，1:转机，2:接料，3:QC）
    String MOUTH_TYPE_ONE = "1";
    String MOUTH_TYPE_FIVE = "5";
    String ENABLED = "已生效";
    String NOT_ENABLED = "未生效";
    String UPDATE_NUNMER = "更新投入数量，更新前投入数量：";
    String UPDATED_NUNMER = ",更新后投入数量：";
    String WIP_CURRENT_CRAFTSECTION = "请去WIP当前工序(";
    String NEXT_CRAFT_SECTION = ")下工序";
    String SAVE_INSERT_BARCODE = "批量在制保存插入条码：";
    String INSERT_LOG_INFO_OF_UPDATE = "6 updateWipInfo_保存数据 begin :";
    String INSERT_LOG_INFO_OF_OUT_PUT = "7 saveWipOutput_时段产出 begin :";

    // 已确认
    BigDecimal MATERIAL_RETURN_STATUS_ISCONFIRMED = (new BigDecimal("1"));
    // 已回收
    BigDecimal MATERIAL_RETURN_STATUS_RECYCLED = (new BigDecimal("2"));
    // 未确认
    BigDecimal MATERIAL_RETURN_STATUS_UNCONFIRMED = (new BigDecimal("0"));
    BigDecimal TIME_INTERVAL_FOR_EXPORT = (new BigDecimal("7"));
    BigDecimal TIME_INTERVAL = (new BigDecimal("31"));
    BigDecimal CONFIRM_TIME_INTERVAL = (new BigDecimal("30"));
	BigDecimal TIME_INTERVAL_ONE_YEAR = (new BigDecimal("367"));
    // 已确认
    String STATUS_IS_CONFIRMED = "已确认";
    // 已回收
    String STATUS_IS_RECYCLED = "已回收";
    // 未确认
    String STATUS_IS_UNCONFIRMED = "未确认";
    // 状态不明
    String STATUS_IS_UNKNOWN = "未知状态";

    String ZJ_CATEGORY_NAME = "整机条码（219）";

    String ZJ_CATEGORY_CODE = "219_DEVICE_SNID";
    //物料条码类型
    //1.管控获取物料代码
    //2.PKCODEINFO获取物料代码
    //3.回写获取物料代码
    String ITEM_SN_1 = "1";
    String ITEM_SN_2 = "2";
    String ITEM_SN_3 = "3";

    //子工序
    String X_TYPE_PROCESS = "子工序";

    String X_TYPE_WORKSTATION = "工站";
    String ZS_NULL_FLAG = "{}";

    String INVENTORY_REWORK = "库存返工";

    String ENTITY_TEMPLATE_XLSX = "处理结果.xlsx";
    String SN_STR = "条码:";
    String WIPINFO_NOT_EXIST = ",在制表中不存在!";
    String ONLY_CAN_STR = "只能对【";
    String TRANSFER_SN_STR = "】的单板进行转交，请确认！";
    String MASTER_PROCESS_NOT_STR = "存在主工序不是【";
    String SN_CONFIRM_STR = "】的单板，请确认！";
    String SERIAL_TD = "<TD>序号</TD>";
    String SOURCE_TASK_TD = "<TD>批次</TD>";
    String SN_SCAN_PERCENT_TD = "<TD>条码扫描率</TD>";
    String REEL_ID_PERCENT_TD = "<TD>Reel ID追溯率</TD>";
    String REEL_ID_SCAN_PERCENT_TD = "<TD>Reel ID扫描执行率</TD>";
    String NO_DATA_TD = "<TD>无数据</TD>";
    String UNLOCKED_STR = "未锁定";
    String LOCKED_STR = "已锁定";
    String TERMINAL_SCAN_NOT_COMPLETED = "转机扫描未完成";
    String FACTORY_ID_MAST_STR = "工厂ID(必须):由请求头(-H X-Factory-Id: ${param})传入";
    String DATE_FORMAT_STR = "时间,字符串格式必须为(yyyy-MM-dd HH:mm:ss),例: 1999-01-01 00:00:00";
    String WIPINFO_MAINTAIN_STR = "在制信息维护";
    String SN_STATUS_INFO_STR = "条码当前状态信息";
    String STATISTICS_BY_TASK_FAILED = "根据任务号或批次统计结存失败:";
    String CLIENT_STR = "客户端:";
    String POINT_STR = "端口";
    String CONNECTED_STR = " 已连接";
    String PRINTER_DISCONNECTED_STR = "喷码机断开连接;";

    String CLOSED_ZH = "已关闭";
    // 仓储中心接口，数据字典
    String LOOKUP_TYPE_1004037 = "1004037";
    // 仓储中心交易接口(批次)
    String LOOKUP_CODE_1004037008 = "1004037008";
    String LOOKUP_CODE_1004037016 = "1004037016";
    String IS_WAREHOUSE_CODE = "isWarehouseCode";
    String IN_LINE_CODE = "inLineCode";
    String WORK_ORDER_STATUS = "workOrderStatus";
    String LOOKUP_CODE_6926 = "6926";
    String LOOKUP_CODE_6926001 = "6926001";
    String LOOKUP_CODE_6926002 = "6926002";


    /**
     * DIP转交入库 - 入库 - 子工序
     */
    String IN_WAREHOUSE_PROCESS_CODE = "1218003";
    /**
     * 在库 - 工站
     */
    String WORK_STATION_IN_WAREHOUSE = "1218005";
    String DIP_TRANSFER_WAREHOUSE_SYS_TYPE = "1218";
    String LOOK_UP_TYPE = "lookupType";

    String REJECT_RIGHT = "rejectRight";
    String RCV_RIGHT = "rcvRight";
    String SCANRECEIVE_RIGHT = "scanReceiveRight";
    String LOOK_UP_TYPE_STOCK_RCV_RIGHT = "1298";
    String LOOK_UP_CODE_STOCK_RCV_RIGHT = "1298001";
    String LOOK_UP_CODE_STOCK_ERP_TIME = "1298002";
    String LOOK_UP_CODE_REQ_VALID_SWITCH = "1298003";
    String LOOK_UP_CODE_STOCK_REJECT_RIGHT = "1298004";
    String LOOK_UP_CODE_STOCK_REJECT_DATABACK = "1298005";
    String LOOK_UP_CODE_WAREHOUSE_UPDATE_CONTAINER = "1298006";
    String LOOK_UP_CODE_STOCK_SCANRECEIVE_RIGHT = "1298007";
    String LOOK_UP_CODE_STOCK_CLOSE = "1298008";
    String WIP_DAILY_SYS_TYPE = "6001";
    String WIP_DAILY_OUTER = "6007";
    String SM_DAILY_SYS_TYPE = "6010";
    String DIP_DAILY_SYS_TYPE = "6040";
    String DAILY_STAT_SETTING = "6041";
    String CM_DAILY_SYS_TYPE = "6008";
    // 工厂对应infor仓库数据字典
    String LOOK_UP_TYPE_6609 = "6609";
    BigDecimal DAILY_STAT_TRANS_MES = BigDecimal.valueOf(6041001);

    String SYS_LOOK_TYPES_NOT_TASKNO_SUFFIX = "6006";
    String SYS_LOOK_TYPES_ZJ_PROCESS_CODE = "221103";
    /**
     * 同位号维修次数管控
     */
    String SYS_LOOK_TYPES_TAGS_REPAIR_RECORDS = "23000";
    /**
     * 部件生产在线
     */
    String PARTS_ONLINE_QTY = "partsOnlineQty";
    /**
     * 在线总数
     */
    String ONLINE_TOTAL_QTY = "onlineTotalQty";

    String ATTR_PRODPLAN_ID = "prodplan_id";

    String ATTR_SUBMIT_QTY = "submit_qty";

    String ATTR_INBOUND_QTY = "inbound_qty";

    String ATTR_STOCK_QTY = "stock_qty";

    /**
     * 高级日报-单板统计定时任务：批量单次最大行数，防止内存溢出
     */
    int WIP_DAILY_MAX_BATCH_CNT = 500;

    String WIP_DAILY_EMP_NO_LOCK = "WIP_DAILY_EMP_NO_LOCK_";

    String CM_DAILY_EMP_NO_LOCK = "CM_DAILY_EMP_NO_LOCK_";

    int WIP_DAILY_EMP_NO_LOCK_TIMEOUT = 60 * 5;

    int CM_DAILY_EMP_NO_LOCK_TIMEOUT = 60 * 5;

    /**
     * 高级日报-单板统计导出：导出最大行数
     */
    int WIP_DAILY_MAX_EXPORT_ROW = 500;
    /**
     * 高级日报-单板统计导出：导出最大总数，防止内存溢出
     */
    int WIP_DAILY_MAX_EXPORT_CNT = 50000;

    /**
     * 高级日报-单板统计导出：导出最大行数
     */
    int CM_DAILY_MAX_EXPORT_ROW = 500;
    /**
     * 高级日报-单板统计导出：导出最大总数，防止内存溢出
     */
    int CM_DAILY_MAX_EXPORT_CNT = 50000;

    long ONE_DAY_MILLIS = 24 * 60 * 60 * 1000L;
    String LINE_RETURNED_WAREHOUSING = "XBTK";
    String LINESIDE_STOCK_OUT = "XBCK";
    String LINESIDE_STOCK_OUT_IN = "XBCK_SB,XBCK_YB,XBTK_YD,XBTK_WD";

    String OUT_TYPE01 = "按箱出库";

    String OUT_TYPE02 = "按物料代码+数量出库";
    String SN_IS_NULL = "条码不能为空";

    String EXIST_SUBMIT_SN = "存在已提交的条码";

    String SYS_LOOK_TYPE_1252 = "1252";

    /**
     * 字母卡组合解绑状态
     */
    String ENABLED_FLAG_FALSE = "N";
    /**
     * 单板需求单号前缀
     */
    String BILL_NO_PREFIX = "RI";

    String PARENT_SN = "parentSn";

    String APPLICATION = "application/octet-stream;charset=ISO8859-1";
    String CONTENT = "Content-Disposition";
    String ATTACHMENT = "attachment;filename=";
    String PARGAM = "Pargam";
    String NO_CACHE = "no-cache";
    String CACHE_CONTROL = "Cache-Control";
    String DATA_ERROR = "数据有误";
    String SN_IS_REPARING = "条码已送修:";
    String STRING_CASTLE = "城堡子卡贴装";
    String WIP_SCAN_HISTORY = "拼版关系绑定扫描";
    String PS_SCAN_HISTORY = "已扫描条码";

    String KEY_VALUE_PREFIX_WARESHOUSING = "PrimaryKey:WarehouseEntryInfo:";

    // 请求网关超时报错信息
    String READ_TIMED_OUT = "Read timed out";
    // 写erp交易表超时，不回滚。若实际未写erp交易表可将IS_TRANSLATED设为N重新回写
    String UPDATE_ERP_READ_TIMED_OUT = "写erp交易表超时";

    // 写erp交易表redis key 子库存转移
    String REDIS_KEY_TRANSFER_ZTE_LMS_MTL_TRANSACTIONS_INTER = "TRANSFER_ZTE_LMS_MTL_TRANSACTIONS_INTER";
    // 写erp交易表redis key 组织间转移
    String REDIS_KEY_TRANSFER_ZTE_LMS_ORG_TRANS_INTER = "TRANSFER_ZTE_LMS_ORG_TRANS_INTER";
    // 写MES WMES.WMES_MACHINE_ONLINE_INFO表redis key 高级日报数据回写MES
    String REDIS_KEY_TRANSFER_WMES_MACHINE_ONLINE_INFO = "TRANSFER_WMES_MACHINE_ONLINE_INFO";
    // 重新统计作业量redis key
    String REDIS_KEY_RESTATISTICSWORKLOAD = "REDIS_KEY_RESTATISTICSWORKLOAD:";
    // reelId数量同步失败计数key前缀
    String REDIS_REELID_QTY_SYNC_FAILED_COUNTER = "REELID_QTY_SYNC_FAILED_COUNTER";
    // 追溯计算防重复消费redisKey
    String REDIS_TRACE_CAL = "TRACE_CAL:%s:%s";
    // 追溯修复防重复消费redisKey
    String REDIS_TRACE_FIX = "TRACE_FIX:%s:%s";

    String REDIS_FIX_TRACE_DATA = "FIX_TRACE_DATA";

    String DATE_TIME_FORMATE_FULL = "yyyy-MM-dd HH:mm:ss";

    //送修免接收部门班组
    String LOOKUP_TYPE_IS_FREE_RECEIVING_TEAM = "1371";
    //可送修子工序
    String LOOKUP_TYPE_CAN_REPAIR_PROCESS_CODE = "1382";
    //已提交
    String WARHOUSE_DETAIL_STATUS_ZERO = "0";
    //入库单详情状态--已关闭
    String WARHOUSE_DETAIL_STATUS_ONE = "1";
    //入库单头表状态-已关闭
    String WARHOUSE_INFO_STATUS_THREE = "3";

    String WRITE_BACK_APS = "WRITE_BACK_APS";

    String RE_WRITE_BACK_APS = "RE_WRITE_BACK_APS";

    //在库
    String LOOKUP_TYPE_IN_STORE = "S1062";

    //交期默认周期
    String LOOKUP_TYPE_DEFAULT_PERIOD_DELIVERY_DATE = "6030";

    String IS_WARN = "预警";

    String SCANNED_SN_FILE_NAME = "scannedSnsTemplate.xlsx";

    String QUESTION_MARK = "?";
    String AND = "&";
    String EQUAL = "=";
    String CURRENTPAGE = "currentPage";
    String PAGESIZE = "pageSize";
    String PAGE_NO = "pageNo";

    String VIRTUAL_MACHINE_NO = "VIRTUAL_MACHINE_NO_";

    String LOOKUP_TYPE_1004042 = "1004042";
    String WIP_DAILY_STATISTIC_JOB = "WIP_DAILY_STATISTIC_JOB:";
    String TYPE_10560005 = "10560005";
    String LOOKUP_6104 = "6104";
    String VIRTUAL_PREFIX = "XN";
    String V_MACHINE_NO = "JT01";
    String V_MODULE_NO = "MZ01";
    String LOOKUP_6105 = "6105";
    String NO_VIRTUAL_MOUNT_TYPE = "6106";
    String STR_040 = "040";
    String STR_180 = "180";
    String[] PRE_IGNORE = {"endRow", "startRow", "page", "rows", "qty"};
    String LOCATION_HAS_MT = "站位存在有效机台在用";
    String PRE_TECH_CHG_TITLE = "iMES技改管控单：%s提前技改，提单人：%s";
    // 技改单号****第*次发放，批次有：***，提单人：***，请注意查收。
    String TECH_CHG_TITLE = "%s,技改单号：%s第%s次发放,提单人：%s,请注意查收";
    String TECH_CHG_CONTENT = "技改内容：";
    String TECH_CHG_ERROR_PROD = "技改单错误信息：";
    String LOCK_CONTENT = "锁定内容：";
    String CONTROL_MAIN_CRAFT = "管控主工序";
    long TIME_STAMP_184_DAY = 184 * 24 * 60 * 60 * 1000L;
    String OUTSOURCE = "外协";
    String OUTSOURCE_CODE = "P0240";
    String LOOKUP_CODE_1003011003 = "1003011003";
    String LOOKUP_TYPE_1003011 = "1003011";
    String CP = "CP";
    String POWER_MODULE = "模块电源";
    String SYSTEM_PRODUCTS = "系统产品";
    String MOBILE_PHONE = "手机";
    String WAREHOUSE_ENTRY_NORMAL = "正常入库";
    String PROD_NOT_HAVE_CRAFT_INFO = "批次无工艺信息！";
    String LOOKUP_TYPE_1003017 = "1003017";
    String LOOK_UP_CODE_1003017001 = "1003017001";
    String ASSEMBLY_SCAN_SELECT_SETTING_CACHE = "assemblyScanningSelectSettingCache";
    String ONLY_IMPORT_STANDARD_BARCODE_ALLOWED = "仅允许导入标模条码";
    String REPAIR_ENG = "repair";
    String STR_10000 = "10000";
    String STR_10000001 = "10000001";
    String LOOKUP_TYPE_7588001 = "7588001";
    String LOOKUP_VALUE_7580006 = "7580006";
    String LOOKUP_VALUE_7580007 = "7580007";
    String SYSTEM_LOGIC = "系统逻辑";
    String MANUAL_OPERATION = "人工维护";
    String SEQUENCE_MSG = "-展示顺序:";
    String PREPARE_ITEM_NAME_MSG = "-准备项名称:";
    long FIVE_MIN_TIME_STAMP = 5L * 60L * 1000L;
    String STR_8 = "8";
    String STENCIL_TO_BE_ISSUED_STATUS = "待发放";
    String STENCIL_ABNORMAL_CLOSURE_STATUS = "异常关闭";
    String STENCIL_CLOSE_STATUS = "关闭";
    String STENCIL_HAVE_BEEN_ISSUED_STATUS = "已发放";
    String BIND_NUM = "绑定数量";
    String BIND_ITEMCODE = "绑定物料";
    String STR_0040 = "0040";
    String STR_0030 = "0030";
    String LOOK_UP_7580021 = "7580021";
    String STR_9 = "9";
    String BUSINESS_CODE_COMMON_SCAN = "commonScan";
    String LAST_NUMBER_REGEX = "\\d+$";
    String NUMBER_REGEX = "\\d+";
    String DATA_WB = "/zte-mes-resourcewarehouse-datawb";
    String INFOR_SUPPLY_URL = "/inforStorageCenter/so/outDetail";
    String LOOKUP_CODE_7580023004 = "7580023004";
    String LOOKUP_CODE_7580023 = "7580023";
    String LOOKUP_CODE_7580023003 = "7580023003";
    String LOOKUP_CODE_7580023002 = "7580023002";
    String NO_DATA = "No Data";
    String LOOKUP_VALUE_7580027 = "7580027";
    String LOOKUP_VALUE_7580026 = "7580026";
    String NEED_BIND_ERROR_MESSAGE = "辅料代码%s，已绑定可用数量为%d，应绑定数量为%d";
    String MIX_NEED_BIND_ERROR_MESSAGE_FIRST = "任务%s下存在未绑完辅料的条码，具体数据为：";
    String LOOKUP_TYPE_7580026001 = "7580026001";
    String TASK_QTY_ZERO = "任务数量为0，请确认";
    String SFG_PKGID = "SFG_PKGID";
    int INT_18 = 18;
    String ENTITY_TYPE_REELID = "REELID";
    String PROGRAM_A_NAME = "A面程序";
    String PROGRAM_B_NAME = "B面程序";
    String EMP_NO_NULL = "未传入8位ID";
    String EMP_NO_NOT_EXIST = "8位ID不存在";
    String REPAIR_GROUP = "维修组";
    String OLD_CHANGE_NEW = "以旧换新";
    String GET_NEW = "新领";
    String STR_219 = "219";

    String STATUS_NO_RECYCLED = "不回收";
    String BLOCK_BY_BLOCK_REDIS = "BLOCK_BY_BLOCK_REDIS:";
    String INVENTEC_ZTE = "ZTE";
    String FACTORY_ID_CS_NAME = "长沙工厂";
    String FACTORY_ID_HY_NAME = "河源工厂";
    String FACTORY_ID_NJ_NAME = "南京工厂";
    String FACTORY_ID_XA_NAME = "西安工厂";
    String FACTORY_ID_SZ_NAME = "深圳工厂";
    String TEST_RESULT_P = "P";
    String TEST_RESULT_F = "F";
    String NONE = "无";
    String TENCENT = "Tencent";
    String MESSAGE_TYPE_TENCENT_L6_TEST_DATA = "ZTEiMes-Tencent-PushL6TestData";
    String LOOKUP_1003022 = "1003022";
    String LOOKUP_TYPE_1003022001 = "1003022001";
    BigDecimal LOOKUP_TYPE_1003022002 = new BigDecimal("1003022002");
    Object LOOKUP_TYPE_2868 = "2868";
    String ORG_TRANSFER_ORDER_TYPE = "2869";
    String INONE_ERROR = "INONE接口报错：";
    String REPAIR_APPROVAL_KAFKA_ERROR_MSG = "维修异常审批kafka异常监控";

    String AGREE = "同意";
    String REFUSE = "拒绝";
    String PENDING = "待审批";

    /**
     * 维修领料单创建 1.已提交 2.待确认，3.已确认，4.已收料，5.已发料，6.已作废，7.已退料，8.拟制中
     */
    interface Maintenance {
        String SUBMIT = "1";
        // 拟制中
        String PROPOSED = "8";
        //已发料
        String ISSUED = "5";
        // 已作废
        String INVALIDATED = "6";
        // 已退料
        String BACKED = "7";
        String RECIVE_OK = "9";
        // 待确认
        String PENDING_CONFIRMATION = "2";
        // 已确认
        String CONFIRMATION = "3";
        // 已领料
        String RECEIVE = "4";
        String IMESWXLL = "IMESWXLL";
        // 编辑权限
        String UPDATE = "update";
        // 删除按钮
        String DELETE = "delete";
        // 物料发放权限
        String MATERIAL_SEND = "send";
        // 审批权限
        String APPROVED = "approved";
        // 确认收料
        String CONFIRM = "confirm";
        // 失效
        String FAILURE = "failure";

    }

    //领料单导出EXCLE
    interface MaintenanceExport {
        String[] PROPS = new String[]{"createDate", "materialRequisitionBill",
                "teamGroup", "prodplanId", "itemNo", "itemName", "mainSn",
                "mainBomName", "subSn", "subBomName"
                , "locationNo", "brandName", "approvedBy", "approvedResult", "needQty", "changeType",
                "controlFlag", "oldMaterialStatus", "inforBillNo", "createUser",
                "wxSn", "wxSubItemSn", "wxItemCode", "wxLocationNo", "wxRepairProductType", "wxRepairProductStype", "wxRepairProductMstype", "wxRepairBy"};
        String[] HEADER_LIST = new String[]{"提单日期", "单据号", "班组", "生产批次", "物料代码", "物料名称", "主板条码", "主板名称", "子卡条码", "子卡名称"
                , "位号", "品牌", "审批人", "审批结果", "需求数量", "物料更换类型", "是否管控", "旧料状态", "infor领料单号", "领用人",
                "维修主板条码", "维修子卡条码", "维修物料代码", "维修位号", "维修大类", "维修小类", "维修次小类", "录单人"};

        String FILE_NAME = "-领料单创建.xlsx";
        String SHEETNAME = "导出清单";

    }

    String HAVE = "有";

    String GRADE_NINTH = "99";

    //扫描查询导出EXCEL
    interface ScanBill {
        String[] PROPS = new String[]{"barcode", "billNo", "rcvStatusDesc", "billTypeDesc", "itemNo", "itemName",
                "prodplanId", "qty", "createBy", "createDate"};
        String[] HEADER_LIST = new String[]{"条码", "单据号", "单据状态", "单据类型", "物料代码", "物料名称",
                "批次", "数量", "扫描人", "扫描时间"};
        //        String EMAIL_TITLE_ZH = "交易导出Excel 邮件通知";
//        String EMAIL_TITLE_EN = "Export Email notifications from the inventory";
        // 文件下载锁 工id + 工号id
        String TRAN_REDIS_FACTORY_LOCK = "transaction_down:%s:%s";
        String TRAN_REDIS_LOCK = "transaction_down:%s";
        String FILE_NAME = "扫描查询.xlsx";
        String SHEETNAME = "导出清单";

    }

    String SEMICOLON = ";";
    String STRING_A = "A";
    String WET_LEVEL_ONE = "一级";
    String WET_LEVEL_TWO = "二级";

    interface lookupType {
        String VALUE_6102 = "6102";
        String VALUE_6760 = "6760";
        String VALUE_6102001 = "6102001";
        String VALUE_2002 = "2002";
        String VALUE_1004041 = "1004041";
        String VALUE_1004041002 = "1004041002";
        String VALUE_1004041005 = "1004041005";
        String VALUE_1004063 = "1004063";
        String VALUE_1004065 = "1004065";

        String VALUE_1004067 = "1004067";
        String VALUE_1004067001 = "1004067001";
        String VALUE_1004070 = "1004070";
        String VALUE_22000 = "22000";
        String VALUE_1004070001 = "1004070001";
        String VALUE_1004070002 = "1004070002";
        String VALUE_1004074 = "1004074";
        // 指令开工状态校验
        String VALUE_1004076 = "1004076";
        // Spm 服务地址
        String VALUE_1004081 = "1004081";
        String VALUE_1004081001 = "1004081001";
        // 单据类型
        String VALUE_1048 = "1048";
        // 单据状态
        String VALUE_1114 = "1114";
        // erp 状态
        String VALUE_1004097 = "1004097";
        // 入库单明细状态
        String VALUE_1004098 = "1004098";

        String VALUE_6827 = "6827";

        String VALUE_7580030 = "7580030";
        String VALUE_7580030001 = "7580030001";
    }

    String BILL_NOS_KEY = "BILL_NOS_KEY";
    String LOOKUP_TYPE_6680 = "6680";
    String LOOKUP_TYPE_6685 = "6685";
    String LOOKUP_TYPE_6685001 = "6685001";
    String LOOKUP_TYPE_6685003 = "6685003";
    //入库单提交相关配置 10270446
    String LOOKUP_TYPE_6688 = "6688";
    String LOOKUP_TYPE_6688001 = "6688001";
    String LOOKUP_TYPE_6688002 = "6688002";
    String LOOKUP_TYPE_6688003 = "6688003";
    String LOOKUP_TYPE_6688004 = "6688004";
    String LOOKUP_TYPE_6688005 = "6688005";

    String LOOKUP_TYPE_6689 = "6689";
    String LOOKUP_TYPE_7303 = "7303";
    String LOOKUP_TYPE_7307 = "7307";
    String LOOKUP_TYPE_6817 = "6817";

    //
    String LOOKUP_TYPE_6672 = "6672";

    String LOOKUP_TYPE_6736 = "6736";

    String LOOKUP_TYPE_6736001 = "6736001";

    String LOOKUP_TYPE_10000001 = "10000001";
    String LOOKUP_TYPE_10000 = "10000";

    String LOOKUP_TYPE_6672008 = "6672008";

    String LOOKUP_TYPE_6672010 = "6672010";

    String LOOKUP_TYPE_6672011 = "6672011";

    BigDecimal LOOKUP_TYPE_6732008 = new BigDecimal("6732008");
    BigDecimal LOOKUP_TYPE_6732009 = new BigDecimal("6732009");
    String LOOKUP_TYPE_6732010 = "6732010";
    String LOOKUP_TYPE_6732011 = "6732011";
    String LOOKUP_TYPE_6732 = "6732";
    String LOOKUP_TYPE_1777 = "1777";
    String LOOKUP_TYPE_6917 = "6917";
    String LOOKUP_TYPE_6917001 = "6917001";
    String LOOKUP_TYPE_6917002 = "6917002";
    String LOOKUP_TYPE_6917003 = "6917003";
    String LOOKUP_TYPE_6918 = "6918";
    String LOOKUP_TYPE_6734 = "6734";
    String LOOKUP_TYPE_6735 = "6735";
    String LOOKUP_TYPE_6732001 = "6732001";
    String LOOKUP_TYPE_6732002 = "6732002";
    String LOOKUP_TYPE_6732003 = "6732003";
    String LOOKUP_TYPE_6732004 = "6732004";
    String LOOKUP_TYPE_6732005 = "6732005";
    String LOOKUP_TYPE_6732006 = "6732006";
    String LOOKUP_TYPE_6732012 = "6732012";

    String LOOKUP_TYPE_2020 = "2020";
    String LOOKUP_TYPE_2020010 = "2020010";
    String LOOKUP_TYPE_2020012 = "2020012";
    String COMPLETED = "已完成";
    String IN_PROGRESS = "进行中";

    //入库单提交相关配置 10270446
    String LOOKUP_TYPE_6667 = "6667";
    String LOOKUP_TYPE_1268 = "1268";
    String LOOKUP_VALUE_1268001 = "1268001";
    //可扫描状态
    String LOOKUP_VALUE_6691 = "6691";
    String LOOKUP_VALUE_6691001 = "6691001";
    //使用新旧写入入库单详情表sql
    String LOOKUP_TYPE_6667001 = "6667001";
    String LOOKUP_TYPE_6667003 = "6667003";
    String LOOKUP_TYPE_6667002 = "6667002";
    String LOOKUP_TYPE_5972 = "5972";
    String LOOKUP_TYPE_5976 = "5976";
    String LOOKUP_CODE_5972005 = "5972005";
    String LOOKUP_TYPE_2870 = "2870";
    String LOOKUP_CODE_2870001 = "2870001";
    String UPDATE_QTY = "updateQty";
    String UPDATE_QTY_EMAIL_KEY = "psWipInfo";

    String LOOKUP_TYPE_6702 = "6702";

    String LOOKUP_TYPE_6911 = "6911";

    String LOOKUP_CODE_1058 = "1058";
    String LOOKUP_TYPE_8889001 = "8889001";
    String LOOKUP_TYPE_8889002 = "8889002";

    String LOOKUP_TYPE_7502002 = "7502002";
    String QUO_MARK = "'";
    String COMMA_MARK = "',";

    String BOM_NO = "bomNo";
    String STR_LIST = "list";
    String ITEM_CODE = "itemCode";

    String STR_ITEM_CODE = "item_code";
    String ITEMNO = "itemNo";
    String ITEM_UUID = "itemUuid";

    interface LookUpKey {
        String LOOK_2221 = "2221";
        String LOOK_1004073 = "1004073";
        String LOOK_1004073001 = "1004073001";
        String LOOK_2002 = "2002";
        String LOOK_7300 = "7300";
        String LOOK_1890 = "1890";
        String LOOK_1890005 = "1890005";
        String LOOK_1890006 = "1890006";
        String LOOK_6718 = "6718";
        String LOOK_6502 = "6502";
        String LOOK_1048 = "1048";
        String LOOK_7000 = "7000";
        String LOOK_7000014 = "7000014";
        String LOOK_7000015 = "7000015";
        String LOOK_1032 = "1032";
    }

    String LOOKUP_TYPE_6745 = "6745";

    String LOOKUP_TYPE_6745001 = "6745001";

    String LOOKUP_TYPE_6745002 = "6745002";

    long LONG_1 = 1L;
    long LONG_5000 = 5000L;
    String AUTO_RUNNING = "Job auto Running";
    String LOCKED_FAIL = "locked fail";

    String STR_EMPTY_ONE = " ";
    String SEND_SUCCESS = "推送成功";

    String STATUS_PREPARATION = "首备中";
    String STATUS_FINISH_PREPARE = "首备完成";

    String NOT_INFO_STATUS = "notInfoStatus";
    String NOT_DETAIL_STATUS = "notDetailStatus";
    String STOCK_NAME_LIST = "stockNameList";
    String APPROVED = "已审批";
    String APPLICATION_TYPE_ZJ_TO_BM = "整机车间库-标模库";
    String APPLICATION_TYPE_BM_TO_BM = "标模库-标模库";
    /**
     * 立库数据字典
     */
    String STEREOSCOPIC_WAREHOUSE = "1116";
    String SEND_ERROR = "推送异常";
    String BATCH_CODE = "BATCH_CODE";
    String BYTE_DANCE = "ByteDance";
    //异步补偿常量配置
    interface AsynCompesation {
        String ASYN_COMPESATION = "ASYN_COMPESATION:";
        String HTTP_GET = "GET";
        String HTTP_PUT = "PUT";
        String HTTP_POST = "POST";
        String HTTP_DELETE = "DELETE";
        String HTTP_PATCH = "PATCH";
        String WEBSERVICE = "WEBSERVICE";
        String ASYN_STATUS_FAIL = "0";
        String ASYN_STATUS_SUCCESS = "1";
        String REQUEST_TYPE_ERRO = "不支持该请求方式";
        String LOOKUP_TYPE_2039 = "2039";
        String BU_CODE = "BU_CODE:";
        String LOG_ID = "LOG_ID:";
        String ERROR_INFO = "ID_AND_LOGID:%S:%S;";
    }

    interface RedisMigrateExport {
        String[] REDIS_MIGRATE_HEADER = new String[]{"redis键", "redis值", "redis值1", "redis值2", "redis值3", "redis值4", "redis值5", "redis值6", "redis值7", "过期时间"};
        String[] REDIS_MIGRATE_PROPS = new String[]{"key", "value", "value1", "value2", "value3", "value4", "value5", "value6", "value7", "expire"};
        String REDIS_MIGRATE_FILE_NAME = "redisMigrate.xlsx";
        String USER_DIR = "user.dir";
        String TEMP = "/temp";
        String REDIS_MIGRATE = "RedisMigrate";
    }

    String REMARK_SN = "条码中心不存在的条码";

    String REMARK_QTY = "计算标准用量";

    String TYPE_SEQUENCE_CODE = "序列码";
    String TYPE_BATCH_CODE = "批次码";
    String NULL = "null";

    interface RedisKey {
        // 扫描历史表头表 锁
        String SMT_MTL_H = "SmtMachineMTLHistoryH:%s:%s:%s:%s";
        // 物料接收扫描条码锁
        String MATERIAL_RECEIVING_SN = "MaterialReceiving:%s:%s";
        // 物料接收扫描表头锁
        String MATERIAL_RECEIVING_HEAD = "MaterialReceivingHead:%s";
        // 物料接收自动关单key
        String MATERIAL_RECEIVING_REFRESH = "MaterialReceivingRefresh:%s";
        // 上料表可导入锁 指令
        String B_SMT_BOM_UPLOAD_LOCK = "BSmtBomUpload:%s";
        // 指令转交锁 工厂id + 指令
        String SUBMIT_PC_PROCESS = "submitPcProcessTransfer:%s:%s";
        String SCHEDULE_TASK_UPDATE_TECH_DETAIL_STATUS = "scheduleTaskUpdateTechDetailStatus:%s";
        String SAVE_WAREHOUSE_BOARD_PACKAGE_LOCK = "saveWarehouseBoardPackage:%s";
        String WAREHOUSE_BOARD_PACKAGE_SUBMIT_ERP_JOB_LOCK = "warehouseBoardPackageSubmitErpJob:%s";
    }

    String SPLIT = ",|，|;|；";

    String LOCK_TYPE_SN = "条码锁定";
    String LOCK_TYPE_BATCH = "批次锁定";

    String MP = "量产";

    String SD = "SD";
    String XBZZZ = "XBZZZ";

    String SAVE = "save";
    String SUBMIT = "submit";

    String IN_PREPARATION = "拟制中";

    String SUBMITTED = "已提交";

    String LOCKING = "锁定中";

    String COMPLETION_UNLOCKING = "完工解锁";

    String UN_LOCK = "已解锁";

    String INDEX = " 序号";
    String BATCH_SN = " 批次/条码";
    String LOKC_PROCESS = " 锁定工序";
    String CURR_PROCESS = " 当前工序";
    String SN_LOCK_TITLE = "锁定单号：%s已生成，请查阅";
    String SN_LOCK_DETAIL = "锁定明细";

    String SUBMIT_WAREHOUSE_INFO_SUBJECT = "标模自动提交入库单失败，原因：获取不到货位名称对应货位信息，请知悉";
    String SUBMIT_WAREHOUSE_INFO_CONTENTA = "任务：%s 货位名称：%s";

    String CHINA = "zh_CN";

	String[] TRANSFER_ALLOWED_STATUS = {"已开工","已提交","挂起"};
    String BARCODE_UNLOCK_TITLE = "锁定单号：%s 已进行解锁操作，请查阅";
    String BARCODE_LOCK_BILL_NO = "锁定单号：";
    String BARCODE_LOCK_REASON = "锁定原因：";
    String BARCODE_LOCK_USER = "锁定人：";
    String BARCODE_LOCK_TYPE = "锁定类型：";
    String BARCODE_UNLOCK_REASON = "解锁原因：";
    String BARCODE_UNLOCK_USER = "解锁人：";
    String BARCODE_UNLOCK_DETAIL = "本次解锁明细如下：";
    String BARCODE_UNLOCK_BATCH = "批次";
    String BATCH_ITEM_NO = "批次_料单代码";
    String BARCODE_ITEM_NO = "单板名称";
    String ERROR_MSG = "错误信息";
    String BARCODE_ISSUANCE_CRAFT = "技改文件下发工序";
    String BARCODE_TEMP_BOM = "临时BOM:";
    String BARCODE_TECHNICAL_OPERATOR = "技改维护人:";
    String BARCODE_TECHNICAL_FILE = "技改文件:";
    String OTHER_FILE = "其他文件:";
    String BARCODE_REMAKE = "备注:";
    String BARCODE_UNLOCK_SN = "条码";
    String BARCODE_UNLOCK_CNT = "解锁数量";
    String BARCODE_UNLOCK_CRAFT = "解锁工序";

    //条码锁定查询-导出EXCLE
    interface BarCodeLockExport {
        String[] PROPS = new String[]{"billNo", "status",
                "type", "reason", "createBy", "schedulingLockFlag", "ccList", "createDate",
                "batchSn", "mainCardBatch", "lockProcessName"
                , "currProcessCodeName", "detailStatus", "unlockBy", "unlockDate", "detailCcList", "unlockReason"};
        String[] HEADER_LIST = new String[]{"锁定单号", "状态", "锁定类型", "锁定原因", "锁定人", "是否锁定排产", "抄送人", "锁定时间", "批次/条码", "主卡批次", "锁定工序"
                , "当前工序", "锁定状态", "解锁人", "解锁时间", "解锁抄送人", "解锁原因"};

        String FILE_NAME = "-条码锁定查询.xlsx";
        String SHEETNAME = "导出清单";

    }

    String URL = "url";
    String IS_WRITE_BACK_SCAN = "is_write_back_scan";

    interface BarCodeUnLockExport {
        String[] PROPS_PRODPLANID = new String[]{"prodPlanId", "lockedCnt",
                "lockedCraftNames"};
        String[] HEADER_LIST_PRODPLANID = new String[]{"批次", "锁定数量", "锁定工序"};

        String[] PROPS_SN = new String[]{"sn", "currProcessCodeName",
                "lockedCraftNames"};
        String[] HEADER_LIST_SN = new String[]{"条码", "当前工序", "锁定工序"};

        String FILE_NAME = "-条码解锁信息.xlsx";
        String SHEETNAME = "导出清单";

    }

    String RETURN_PRODUCTION = "返制上线";

    String RETURN_PRODUCTION_PREFIX = "RETURN_PRODUCTION";

    String DEVICE_SNID30 = "DEVICE_SNID30";

    interface BindingSettingTemplate {
        String FILE_NAME = "整机绑定关系导入模板.xlsx";
        String TEMPLATE_NAME = "prodBindSettingTemplate.xlsx";
    }


    // 技改导入EXCEL
    interface TechnicalExport {
        String[] PROPS = new String[]{"chgReqNo", "sn"};
        String[] HEADER_LIST = new String[]{"技改单号", "条码"};
        String FILE_NAME = "-iMes技改导入模板.xlsx";
        String SHEETNAME = "技改导入";
    }

    //报废单导出EXCLE
    interface BoardScrapExport {
        String[] PROPS = new String[]{"prodplanId", "sn",
                "scrapBigClass", "scrapSmallClass", "scrapDesc", "notQualifiedNo", "responsibleDept",
                "subStockLocation", "subSn", "applyer", "remark"};
        String[] HEADER_LIST = new String[]{"批次", "条码", "报废大类", "报废小类", "报废原因描述", "不合格品单", "责任部门", "子库位", "子卡条码", "申请人", "备注"};

        String FILE_NAME = "-iMes单板报废导入模板.xlsx";
        String SHEETNAME = "";

        String[] EXPORT_PROPS = new String[]{"prodplanId", "sn", "externalType", "internalType", "itemNo", "itemName", "taskNo", "releaseDate", "accountAge",
                "scrapBigClass", "scrapSmallClass", "scrapDesc", "notQualifiedNo", "responsibleDept", "applyWorkshop",
                "subStockLocation", "subSn", "applyerName", "createByName", "remark", "unitPrice"};
        String[] EXPORT_HEADER_LIST = new String[]{"批次", "条码", "产品大类", "产品小类", "料单代码", "料单名称", "计划跟踪单号", "释放日期", "帐龄(天)",
                "报废大类", "报废小类", "报废原因描述", "不合格品单", "责任部门", "申请车间",
                "子库位", "子卡条码", "申请人", "提单人", "备注", "单价"};

    }

    String LOOKUP_TYPE_6708 = "6708";
    String LOOKUP_TYPE_1245 = "1245";
    String LOOKUP_TYPE_6709 = "6709";
    String LOOKUP_TYPE_6710 = "6710";

    String LOOKUP_TYPE_6715 = "6715";

    String LOOKUP_TYPE_6718 = "6718";

    String MAIN_SEND = "mainSend";

    String CC_SEND = "ccSend";

    String REPAIR_PROUCT_MS_TYPE_SCRAP = "报废";

    String MONEY = "元";

    String IMESBF = "IMESBF";

    //报废单状态
    interface BoardScrapBillStatus {
        String IS_DRAW_UP = "拟制中";
        String PENDING_APPROVAL = "待审批";
        String CANCELED = "已取消";
        String UNDER_APPROVAL = "审批中";
        String APPROVAL_COMPLETED = "审批完成";
        String REJECTED = "已拒绝";
    }

    interface ApprovalStatus {
        String AGREE = "同意";
        String REFUSE = "拒绝";
        String APPROVAL_COMPLETED = "审批完成";
        String TRANSFER = "转交";
    }

    long DAY_TIME = 1000 * 60 * 60 * 24;
    String APPROVAL_VIEW_NAME = "_报废申请单条码详情";
    String SCRAP_TYPE_ONE = "材料问题";
    String SCRAP_TYPE_TWO = "非材料问题";
    //报废中（P0006）、已报废（P0007）、报废入库（P0008）
    String P0006 = "P0006";
    String P0007 = "P0007";
    String P0008 = "P0008";
    String SCRAP_IN_STOCK = "报废入库";
    String CIPHER_TEXT = "******";
    String RE_APPROVAL_PRE = "NODESTART-";

    enum BoardScrapBillNode {
        /**
         * 材料技术质量工程师
         */
        PRODUCTION_QUALITY_ENGINEER("qualitier", "材料技术质量工程师"),
        /**
         * 供应链财经部财务
         */
        SUPPLY_CHAIN_FINANCE_DEPARTMENT_FINANCE("financer", "供应链财经部财务"),
        /**
         * 生产部部长
         */
        MINISTER_OF_PRODUCTION_QUALITY("qualitiyMinister", "生产部部长"),
        /**
         * 制造总经理
         */
        DEPUTY_GENERAL_MANAGER_OF_PRODUCTION("productManager", "制造总经理"),
        /**
         * 生产部部长
         */
        HEAD_OF_PARTS_PRODUCTION_DEPARTMENT("productMinister", "生产部部长"),
        /**
         * 车间主任
         */
        WORKSHOP_DIRECTOR("workshoper", "车间主任");

        /**
         * 节点编码
         */
        private String code;
        /**
         * 节点名称
         */
        private String name;

        private BoardScrapBillNode(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }


    }


    Long LONG_1024 = 1024L;
    String MB_STR = "MB";

    String RD_OWN_MATERIALS_REGISTER = "研发自带料注册";
    String REEL_ID_BIND = "ReelId注册";

    String LATE_REEL_ID_BIND = "LateReelIdBind";

    interface TestType {
        //3、整机工序   4、普通单板工序  5、电源模块及ICT等特殊工序 6、通用
        List<String> TEST_TYPE_ALL = new ArrayList<String>() {{
            add(TEST_TYPE_ONE);
            add(TEST_TYPE_TWO);
            add(WHOLE_PROCESS);
            add(ORDINARY_VENEER);
            add(POWER_MODULE);
            add(UNIVERSAL);
        }};

        String TEST_TYPE_ONE = "1";
        String TEST_TYPE_TWO = "2";
        String WHOLE_PROCESS = "3";
        String ORDINARY_VENEER = "4";
        String POWER_MODULE = "5";
        String UNIVERSAL = "6";
    }

    enum MaintenanceType {
        /**
         * 条码
         */
        SN("0", "按条码"),
        /**
         * 批次
         */
        PRODPLAN_ID("1", "按批次"),
        /**
         * 料单
         */
        ITEM("2", "按料单");

        /**
         * 工厂
         */
        private String typeCode;

        /**
         * 来源
         */
        private String typeName;

        MaintenanceType(String typeCode, String typeName) {

            this.typeCode = typeCode;
            this.typeName = typeName;
        }

        public String getTypeCode() {
            return typeCode;
        }

        public String getTypeName() {
            return typeName;
        }
    }

    interface SnType {
        String SN = "sn";
        String EN_CODE = "enCode";
        String MAC = "mac";
    }

    String INVALID_STR = "失效";
    String VALID_STR = "有效";

    //测试结果0通过 1不通过-2023.05.18 更新测试结果:0成功 1测试失败 2或其他未测试
    String TEST_RESULT_TWO = "2";
    String TEST_RESULT_ONE = "1";
    String TEST_RESULT_ZERO = "0";
    String PERCENT_SIGN = "%";

    // 仓储中心返回行
    List<String> RETURN_COLUMNS = Arrays.asList(new String[]{"productionBatch",
            "createdDate",
            "billTypeCode",
            "srcBillNo",
            "qtyChange",
            "billInfoType",
            "warehouseId",
            "productBatch",
            "reelid",
            "itemNo",
            "itemBarcode",
            "actionCode",
            "fromSummaryId",
            "journalDate"});

    String COMMAS = ",|，";

    String TECHNICAL_EXPORT_NAME = "技改信息.xlsx";
    String WAREHOUSE_EXPORT_NAME = "入库单明细信息.xlsx";
    String WAREHOUSE_EXPORT_NAME_HEAD = "入库单头信息.xlsx";
    String TECHNICAL = "technical";
    String WAREHOUSE_EXPORT = "warehouse";
    String TECHNICAL_EXPORT_SHEET_NAME = "技改信息";
    String WAREHOUSE_EXPORT_SHEET_NAME = "入库单信息";

    //imes技改单状态  0待提交，1拟制中，2锁定中，3已解锁，4已作废
    interface TechnicalChangeStatus {
        // 待提交
        String TO_BE_SUBMITTED = "0";
        // 拟制中
        String IN_PREPARATION = "1";
        // 锁定中
        String LOCKING = "2";
        // 已解锁
        String UNLOCKED = "3";
        // 已作废
        String VOIDED = "4";
    }

    //PDM技改单状态
    interface PdmTechnicalChangeStatus {
        // 未发放
        String UNISSUED = "未发放";
        // 已发放
        String ISSUED = "已发放";
        // 已删除
        String DELETED = "已删除";
        // 已作废
        String VOIDED = "已作废";
    }

    String LOCK_REASON = "技改单号:%s 技改锁定，技改人：%s";
    String UNLOCK_REASON = "技改单号:%s PDM系统变更，变更人：%s";

    String UNLOCK_REASON_FOR_VOIDED = "技改单号:%s PDM系统发放，自动解锁批次锁定单。发放人：%s";
    String EXPORT_MAX_MSG = "数据超出最大导出最大行数%s,还有数据没有全部导出，请精确导出条件，再次导出";

    String F = "F";

    String SEND_EMAIL_NAME = "技改单发送邮件失败";

    String GET_TECHICAL_UPDATE_BY_NAME_ERROR = "获取技改单最后更新人姓名错误";

    String LOOKUP_VALUE_221104 = "221104";

    String BOARD_TEST_STAT_INFO = "单板测试合格率";
    String SOURCE_SYS_ZS = "中试";

    String SINGLE_QUOTATION_MARKS = "'";

    String ENTITY_TYPE_SN = "产品条码";
    String X_FACTORY_ID = "x-factory-id";

    String QC_RESULT_PASS = "PASS";
    String QC_RESULT_FAILURE = "FAILURE";
    String[] MATERIAL_RETURN_DATA_PROCESSING_PROPS = new String[]{"objectId", "returnedQuantity"};

    String CASTLE_MOUNTING_CODE = "λ";

    String CASTLE_VERSION_SUB_CARD_ERROR_MSG = "批次:%s是城堡版子卡";
    String SUB_CASTLE_VERSION_SUB_CARD_ERROR_MSG = "批次:%s的子卡批次:%s是城堡版子卡";

    String LOOKUP_TYPE_2222 = "2222";

    String DOWN_LOAD_TITLE = "技改文件下载";
    String DOWN_LOAD_OTHER_TITLE = "其他文件下载";
    String FILE_VIEW = "预览";
    String OUT_SOURCE = "外协";
    String KANG_XUN = "康讯半成品库";
    String SN_REGULAR = "^[0-9]{12}$";
    String DELETE_OLD_DAILY_REPORT = "DELETE_OLD_DAILY_REPORT";

    String WIP_DAILY_BALANCE = "部件装配结存,部件调试结存,高温标模结存,功放结存,高温配送结存";
    String SYS_LOOK_UP_28650006 = "28650006";
    String COMPLETE_MACHINE_MAINTENANCE = "整机生产维修";
    String PCB = "PCB";
    String LOOKUP_TYPE_6726 = "6726";

    String VOID_THE_TECHNICAL_TRANSFORMATION_SHEET = "%s,技改单号：%s,创建人：%s， 在PDM系统将技改单作废，系统已自动为您在iMES系统上作废此技改管控单。";
    String DELETE_THE_TECHNICAL_TRANSFORMATION_SHEET = "%s,技改单号：%s,创建人：%s， 在PDM系统将技改单删除，系统已自动为您在iMES系统上作废此技改管控单。";
    String TECHNICAL_TRANSFORMATION_SHEET_HAS_BEEN_SYNCHRONIZED = "%s,技改单号：%s,创建人：%s，PDM状态为: 未发放,IMES系统已同步，请注意执行！";
    String BATCH_AND_BOARD_NAME = "单板名称： %s,批次: %s";
    String SEE_THE_TEXT_FOR_DETAILS = "详情见正文";
    String BATCH_DOES_NOT_EXIST = "批次:%s在iMES不存在";
    String ROUTE_DOES_NOT_EXIST = "批次:%s对应工艺路径信息不存在";
    String SEND_EMAIL_FOR_ISSUED = "%s,技改单号：%s,创建人：%s，PDM状态为: 已发放,IMES系统已同步，请注意执行！";

    interface SolderPasteTraceabilityExport {
        String[] PROPS = new String[]{"sn", "prodplanId", "workOrderNo", "itemNo", "itemName", "materialTypeName", "lineName",
                "objectId", "sourceBatchCode", "createByName", "createDate", "lastUpdatedByName", "lastUpdatedDate"};
        String[] HEADER = new String[]{"条码", "生产批次", "指令", "物料代码", "物料名称", "辅料类型", "线体",
                "实物", "物料条码", "创建人", "创建时间", "最后更新人", "最后更新时间"};

        String FILE_NAME = "-锡膏追溯信息.xlsx";
        String SHEETNAME = "导出清单";

    }

    String SOLDER_PASTE_TRACING_ABNORMAL_DATA = "SPI上传写锡膏追溯异常数据";
    String SENT_WIP_EXT_TO_IMES_EXIST = "装配关系回写imes已存在装配关系";

    String DATA_REMARK = "spm数据迁移";
    String CONFIRMED_VALUE = "1";
    String RECEPTTION_VALUE = "2";
    String WRITE_BACK_TO_LOCAL_SENIOR_DAILY = "批次：%s 回写本地高级日报到中心工厂失败";

    interface AdvancedDailyReportExport extends EmailExportCommonReport {
        String FILE_NAME = "-单板高级日报信息.xlsx";
        String EMAIL_TITLE_ZH = "高级日报导出Excel 邮件通知";
        String EMAIL_TITLE_ZH_ERROR = "高级日报导出Excel异常";
        String EMAIL_TITLE_EN = "Advanced daily report export Excel email notification";
        String EMAIL_TITLE_EN_ERROR = "Excel export exception of advanced daily report";

    }

    interface EmailExportCommonReport {
        String SHEETNAME = "导出清单";
        String EMAIL_PREFIX = "<p style=\"font-family:arial;font-size:30px;\">";
        String EMAIL_PREFIX_A = "<a style=\"color:blue;font-size:25px;font-weight:bold;\" href=";
        String EMAIL_SUFFIX = "</a></p>";
        String EMAIL_SUFFIX_P = "</p>";
        String EMAIL_COLO = ">";
        String EMAIL_SPLIT = "_";
        String CLICK_DOWN = "请点击下载";
        String LEFT_BRACKET = "(";
        String RIGHT_BRACKET = ")";
        String VALID_FOR_SEVEN_DAYS = "下载有效期七天";
        String Z_MAIL_EXPORT_FAIL = "邮件导出失败";
    }


    interface ExportConciseDailyReport extends EmailExportCommonReport {
        String FILE_NAME = "-简明日报信息.xlsx";
        String EMAIL_TITLE_ZH = "简明日报导出Excel 邮件通知";
        String EMAIL_TITLE_ZH_ERROR = "简明日报导出Excel异常";
        String EMAIL_TITLE_EN = "Brief daily report export Excel email notification";
        String EMAIL_TITLE_EN_ERROR = "Exception in exporting concise daily report to Excel";

    }

    String STR_WRAP = "\n";

    String ERROR_INFO = "高级日报导出日志";
    String TECHNICAL_SCAN_REDIS_KEY = "pdm_technical_change_scan:%s:%s:%s";

    String P = "P";
    String DOING = "已开工";
    String PREFIX = "、";
    String SMT_A_ZH = "贴片A面";
    String SMT_B_ZH = "贴片B面";
    String SMT_A = "SMT-A";
    String SMT_B = "SMT-B";
    String PAGE = "page";
    String ROWS = "rows";
    String ONLY_ENABLE = "onlyEnable";
    String DIFFERENT_DIRECTION_FLAG = "differentDirectionFlag";

    interface snInfoExport {
        String[] PROPS = new String[]{"sn", "craftSection", "currProcessName", "lastUpdatedDate", "sourceSysName", "isPrintName", "workOrderNo",
                "lineName", "mBom", "itemNo", "parentSn", "lpn", "lastProcess", "errorCode", "lastUpdatedBy"};
        String[] HEADER = new String[]{"条码", "主工序", "当前工序", "扫描时间", "工站", "是否打印", "指令",
                "线体", "制造料单代码", "料单代码", "拼版条码", "容器代码", "最后工序", "不良代码", "操作员"};
        String FILE_NAME = "条码当前状态.xlsx";
        String EMAIL_EXPORT_TITLE = "imes条码当前状态邮件导出";
    }

    String LOOKUP_VALUE_1031201 = "1031201";
    String LOOKUP_VALUE_10312002 = "10312002";
    String LOOKUP_TYPE_10312 = "10312";

    BigDecimal LOOKUP_VALUE_6880001 = new BigDecimal("6880001");
    BigDecimal LOOKUP_VALUE_6880002 = new BigDecimal("6880002");
    String LOOKUP_TYPE_6880 = "6880";
    String HJM_SECTION = "HJM_SECTION";

    String AUTO_UPDATE_REELID_QTY = "点料机自动修改reelid数量";
    String USER_NAME = "username";
    String PASS_WORD = "password";
    String GRANT_TYPE = "grant_type";
    String Z_ACCESS_TOKEN = "Z-ACCESS-TOKEN";
    String PRE_PRODUCTION_WITHOUT_PO = "无PO提前生产";
    String CLJH_TEST = "test";
    String MESSAGE_TYPE_SINGLE_BOARD_FEEDBACK = "ZTEiMES-ByteDance-Board";
    String IS_NUMBER = "^[-\\+]?[\\d]*$";
    String MINUS_ONE = "-1";
    String ONE_HUNDRED = "100";
    String EXTERNAL_ORDER_KEY_2 = "externalorderkey2";
    String APPCode = "APPCode";
    String MATERIAL_RETURN_INFO_XLSX = "指令退料信息.xlsx";
    String MATERIAL_RETURN_INFO = "指令退料信息";
    String ADD_FLAG = "+";
    public static final String LOCK = "lock:";
    public static final String MATERIAL_RETURN_INFO_STR = "material_return_info";
    String REELID_NOT_EXISTS = "reelId不存在，请确认!";
    String UNABLE_TO_OBTAIN_WORK_ORDER = "获取不到reelId对应的指令";
    String WORK_ORDER_STATUS_INCORRECT = "reelId对应的指令未完工，不能修改退料数量";
    String REELID_IS_RETURN = "已进行退料确认，不允许修改退料数量";
    String REELID_IS_RECOVERY = "已进行退料回收，不允许修改退料数量";
    String STORAGE_CENTER_RETURN_NULL = "仓储中心数据返回为空";
    String RETURN_INFO_IS_NULL = "不存在退料信息";
    String RETURN_INFO_DIFFER_IN_TRANSACTION_RECORD = "退料信息和交易记录不一致";
    String QTY_MUST_BE_A_POSITIVE_INTEGER = "入参数量必须为正整数";
    String RETURN_QTY_EXCEED_ITEM_QTY = "退料数量超过发料数量";
    String REELID_ITEM_TYPE_IS_A = "A材目前不支持退线边仓，请直接退央仓！";
    String OPERATE_SUCCESS = "操作成功";
    String SALES_MAN = "业务代表";
    String MESSAGE_TYPE_TEST_FEEDBACK = "ZTEiMES-ByteDance-BoardStationLog";
    String LOOKUP_TYPE_3411 = "3411";
    String LOOKUP_TYPE_3412 = "3412";
    String LOOKUP_TYPE_3412001 = "3412001";
    String ADD_FLAG_FOR_SPLIT = "[+]";
    String DOUBLE_ADD = "[+][+]";
    String REPLACE_ADD = "+加号替换";
    String REPLACE_ADD_VALUE = "加号替换";
    String ROUTE_POINTER = " -> ";
    String LOOKUP_TYPE_3412002 = "3412002";
    String EMPTY_STRING = "";
    String MDS_FT_STATION_ID = "FT测试工序";
    String PART_CODE = "partcode";
    String LOOKUP_TYPE_1003018 = "1003018";
    String LOOK_UP_CODE_1003018001 = "1003018001";
    String INVENTORY_ITEM_CODE = "inventoryItemCode";
    String RELATED_ITEM_CODE = "relatedItemCode";

    String ZTE = "ZTE-";
    //跳过校验上料表及转机扫描的子工序
    String LOOKUP_28650009 = "28650009";
    String LOOKUP_28650009001 = "28650009001";

    String SN_BATCH_BINDING_SUB_LOCK = "snBatchBinding:%s:sub:%s";
    String REPAIR_SN = "repairSn";
    String RETURN_SN = "returnSn";
    String LAST_UPDATED_BY = "lastUpdatedBy";

    //1:绑定 2:解绑 3 置换
    String OPT_TYPE_1 = "绑定";
    String OPT_TYPE_2 = "解绑";
    String OPT_TYPE_3 = "置换";

    String LOCK_REASON_ADD = "(从%s迁移)";

    String CANCEL_DISTRIBUTION_LOG_NO_TECHNICAL_MODIFICATION_ORDER = "取消发放日志-目的工厂已解锁锁定单无技改单";
    String OPERATE_FAILED = "操作失败";
    String PARAMETER_VERIFICATION_FAILED = "参数检验失败";
    String NOT_LOGGED_IN_OR_TOKEN_HAS_EXPIRED = "未登录或令牌已经过期";
    String NO_RELEVANT_PERMISSIONS = "没有相关权限";
    String CANNOT_BE_EXECUTED_SIMULTANEOUSLY = "不可同时执行...";
    String BARCODE_IS_UNDERGOING_MAINTENANCE_ENTRY = "当前条码正在进行维修录入，请稍后再试";

    String OPERATE_NAME_BATCH_PASS = "批量过站扫描";
    String OPERATE_NAME_BATCH_CONTINUS_PASS = "批量连续过站";
    String OPERATE_NAME_PM_SCAN = "通用扫描";
    String LOCK_FAIL_MSG_ZH = "当前指令正在转交，请稍后再试";
    String SN_LOCK_FAIL_MSG_ZH = "当前条码正在进行通用扫描处理，请稍后再试";

    String ONE_FIVE_TEN_THOUSAND = "1万~5万";
    String FIVE_TEN_TEN_THOUSAND = "5万~10万";
    String TEN_FIFTY_TEN_THOUSAND = "10万~50万";
    String MORE_THAN_FIFTY_TEN_THOUSAND = ">50万";
    String SPM_DATE_SYNCHRONIZE = "SMT-A,SMT-B,城堡子卡贴装,手贴,FT,ICT,Test,盘纤,在线测试,电源模块,定制电源,DIP,背板,装配,清洗,涂覆,铣板";
    String NUMS_EXCEED_100 = "传入条码数量不能超过100";
    String PARAMS_IS_NULL = "入参不能为空";
    String TYPE_IS_NULL = "条码类型不能为空";
    String SNLIST_IS_NULL = "条码列表不能为空";

    String COMPLETE_MACHINE_SUB_CARD = "整机子卡";
    String COMPONENT_SUB_CARD = "部件子卡";
    //推送装配关系到MES的相关数据字典配置

    String LOOKUP_TYPE_286516 = "286516";
    String LOOKUP_CODE_286516001 = "286516001";
    String ASSEMBLY_SEND_MES_LOCK = "assembly_send_mes_lock_";
    String ASSEMBLY_SEND_EMAIL_TITLE = "装配关系推送MES定时任务本次执行异常数据，请查收!";
    String ASSEMBLY_SEND_EMAIL_CN_PREFIX = "以下数据执行推送异常可能是12位物料代码在MES无匹配信息或其他数据缺失导致，请尽快确认处理，谢谢！(";
    String ASSEMBLY_SEND_EMAIL_CN_SUFFIX = ")。";
    String ASSEMBLY_SEND_EMAIL_CN_FOR_EXIST_PREFIX = "以下数据执行推送异常是由于装配关系在MES中已存在，不执行重复推送，请确认，谢谢！(";
    String ASSEMBLY_TASK_NO = "任务号";
    String ASSEMBLY_MAIN_SN = "主条码";
    String ASSEMBLY_SUB_SN = "子条码";
    String ASSEMBLY_MAIN_SN_ITEM = "主条码物料代码";
    String ASSEMBLY_SUB_SN_ITEM = "子条码物料代码";
    String SUB_SN_CREATE_BY = "绑定人";
    String MAILBOX_SUFFIX = "@zte.com.cn";
    String SENT_ASSEMBLY_TO_MES_EXIST = "装配关系在mes已存在";
    String LOOKUP_TYPE_286517 = "286517";
    String LOOKUP_CODE_286517001 = "286517001";

    String LOOKUP_TYPE_7580006 = "7580006";
    String LOOKUP_TYPE_7580006001 = "7580006001";
    String LOOKUP_TYPE_7580007 = "7580007";
    String ERROR_MSG_FOR_UNBINDING_PRE = "在子工序";
    String ERROR_MSG_FOR_UNBINDING_MIDDLE = "没有绑定完成";
    // 测试合格率保留sourceSys数据字典
    String LOOKUP_TYPE_7310 = "7310";
    String DIP_BOARD_DELETION_BOX_CONTENT = "DIP过板删除箱内容";
    String AUTO_BATCH_PASS = "测试自动过站";
    String PREPARE_COMPLETED_FLAG = "(OK)";
    String STR_102 = "102";
    String STR_XBRK = "XBRK";
    String BILL_TYPE_XBRK_YA = "XBRK_YA";
    String QUERY_OPTION_ALL = "allStatus";
    String QUERY_OPTION_FINISH = "onlyFinish";
    String DESTINATION_WAREHOUSE = "目的仓库";
    String LOOKUP_TYPE_6823 = "6823";
    String LOOKUP_CODE_6823002 = "6823002";
    String LOOKUP_TYPE_6826 = "6826";
    String LOOKUP_CODE_6826001 = "6826001";
    // 指令开工校验生产资源准备总开关
    String LOOKUP_TYPE_286520 = "286520";
    String LOOKUP_CODE_286520001 = "286520001";
    // 指令开工校验生产资源准备线体级开关
    String LOOKUP_TYPE_286521 = "286521";

    //生产准备项配置
    interface ProdPreConfig {
        String LOOKUP_286518 = "286518";
        String[] PRO_PRE_STATUS = {"已提交", "挂起"};
        // 准备状态(0:未准备完成;1:准备完成;2:准备中,3不涉及)
        String PRE_STATUS_UNFINISHED = "0";
        String PRE_STATUS_FINISHED = "1";
        String PRE_STATUS_PREPARING = "2";
        String PRE_STATUS_NOT_NEED = "3";
        // 子项准备OK状态
        String[] ITEM_PRE_STATUS_OK = {"1", "3"};
        // 准备项是否启用。Y:是,N:否
        String ACTIVITY_Y = "Y";
        String ACTIVITY_N = "N";
        // 准备项数据获取方式。0:表达式;1:人工维护
        Integer ACCESS_WAY_SYSTEM = 0;
        Integer ACCESS_WAY_USER = 1;
        // 准备完成方式(0:自动完成;1:手动完成)
        String PRE_COMPLETE_SYSTEM = "0";
        String PRE_COMPLETE_USER = "1";
        // 系统维护的准备项
        String PRE_ITEM_ALLOCATE_BILL = "ALLOCATE_BILL";
        String PRE_ITEM_LINESIDE_RECEIVE = "LINESIDE_RECEIVE";
        String PRE_ITEM_PROGRAM = "PROGRAM";
        String PRE_ITEM_FIRST_PREPARETION = "FIRST_PREPARETION";
        String PRE_ITEM_COMP_MATERIAL_PREPARATION = "COMP_MATERIAL_PREPARATION";
        String PRE_ITEM_STOVE_TEMP = "STOVE_TEMP";
        String PRE_ITEM_FIXTURE = "FIXTURE";
        String PRE_ITEM_STEEL_MESH = "STEEL_MESH";
        String PRE_ITEM_SOLDER_PASTE = "SOLDER_PASTE";
        String EQP_MAINTENANCE = "EQP_MAINTENANCE";
        String SMT_BOM_STATUS = "SMT_BOM_STATUS";
        String LABEL_PCBs = "LABEL_PCBs";
        // 加锁
        String PRE_CHECK_LOCK = "PRE_CHECK_LOCK_";
    }

    String COMMAND_LINE_CHANGE_WRITING = "指令换线写入";

    String HIGH_TEMP_SN_INFO = "高温条码信息";
    String HIGH_TEMP_SN_INFO_XLSX = "高温条码信息.xlsx";
    String HIGH_TEMP_SN_INFO_EXPORT = "high.temp.sn.info.export";

    // 作业时长和滞留时长校验
    String RE_SCAN = "回流重扫";

    // QC抽检规则相关
    // 需检
    String QC_INSPECTION = "INSPECTION";
    // 免检
    String QC_EXEMPTION = "EXEMPTION";
    // 维度 主工序
    String QC_CRAFT_SECTION = "CRAFT_SECTION";
    // 维度 批次
    String QC_PRODPLAN_ID = "PRODPLAN_ID";
    // 维度 线体
    String QC_LINE = "LINE";
    // 维度 料单
    String QC_BOM_NO = "BOM_NO";

    String HIGH_TEMP_RULE = "highTempRule";

    String IMU_ID = "imuId";
    String WORK_STATION = "workStation";

    // 简明日报工艺段结存
    String LOOKUP_TYPE_CRAFT_BALANCE = "286524";

    String[] PROCESS_SCRAP_OR_REPAIR = {"P0003", "P0006", "P0007"};

    String CONCISE_DAILY_EXPORT_CRAFT_BALANCE_SMT = "SMT结存";

    String KEY_WORKORDER_CURRPROCESS_STATION = "%s-%s-%s";

    String SOURCE_TASK = "sourceTask";

    String IN_CRAFT_SECTION = "inCraftSection";

    String CRAFT_SECTIONS = "craftSections";

    String WORK_ORDER_STATUS_LIST_NEW = "workOrderStatusListNew";

    String CAN_NOT_SKIP_HIGH_PROCESS = "该条码不能跳过高温工序";

    String DATA_FORMAT_STR_YYMMDD = "yyMMdd";
    String STR_DEFAULT = "default";
    String FIELD_SORT = "sort";
    String FIELD_SORT_SEQ = "sortSeq";
    String FIELD_ORDER = "order";
    String QC_SAMPLING_BILL_NO = "RI";
    String QC_SAMPLING_DATE_FORMAT = "yyMMdd";
    String QUERYINFO_TYPE = "partcode";
    String CUSTOM = "custom";
    String FORM_TYPE = "formType";
    // AQL-全检
    String QC_AQL_COMPLETE = "COMPLETE";
    // AQL-自定义
    String QC_AQL_CUSTOM = "CUSTOM";
    String GET_WIP_OPERATIONS = "getwipOperations";
    String DHOME = "DHOME";
    String INVOKE_KEY = "34";
    String ERP_PRODUCT_TYPE = "IMES";
    String SERVICE_CODE = "zte-imes-manage";
    String ONLINE_ROUTE_CHANGE = "工艺路径变更引入";
    // 辅料绑定扫描校验环保属性开关
    String LEAD_CHECK_FLAG_652303 = "652303";
    BigDecimal LEAD_CHECK_FLAG_652303001 = new BigDecimal("652303001");
    String LOOKUP_VALUE_10320001 = "10320001";

    String SOURCE_BATCH_CODE = "SOURCE_BATCH_CODE";

    String POWER_MODULES_TO_RAW_MATERIALS = "电源模块转材料";
    String BARCODE = "Barcode";

    String ONE_STR = "1";

    String STATUS_OK = "OK";
    String STATUS_HOLD = "HOLD";

    String PROD_ADDRESS_ONE = "SZ";
    String PROD_ADDRESS_SIX = "HY";
    String PROD_ADDRESS_SEVEN = "NJ";
    String SMT_TRACE_PRODEND = "PRODEND";
    String SMT_TRACE_PRODSTART = "PRODSTART";

    BigDecimal TIME_A_YEAR = new BigDecimal("366");

    String OBTAIN_ERP_START_PROCESS = "获取ERP起始工序";

    String LOOKUP_VALUE_2038 = "2038";

    String[] PROCESS_NOT_SYNC = {"P0007", "P0008"};

    String CONTAINER_TYPE_0 = "车";

    String CONTAINER_TYPE_1 = "箱";
    String STR_44 = "44";
    String STR_31 = "31";
    String BOARD_PACKAGE_ERP_ERROR="单板包装扫描推送ERP catch Exception：";
    String BOARD_PACKAGE_INSERT_IN_STOCK_ERROR="单板包装扫描写入入库单 catch Exception：";
    Integer FIVE_MINUTES = 300;
    BigDecimal IMU_ID_42 = new BigDecimal(42);
    BigDecimal IMU_ID_613 = new BigDecimal(613);
    String BOARD_PACKAGE_BOARD_CODE_ERROR="单板包装扫描条码入库 catch Exception：";
    Integer HALF_AN_HOUR = 1800;
    String BOARD_PACKAGE_JOB_ERP_ERROR="单板包装扫描JOB推送ERP catch Exception：";
    String UPDATE_FIRST_WAREHOUSE_DATE = "updateFirstWarehouseDate";

    //写ERP子库存转移任务，ICenter通知收件人
    String LOOKUP_CODE_3413 = "3413";
    String LOOKUP_CODE_3413001 = "3413001";
    String TRANSFER_ERP_TRANSFERNUM_ERROR = "写ERP子卡库转移定时任务，写入ERP的数量%d与实际数量%d不一致，请关注！";

    String SUPPLIER = "供应商";
    String BARCODE_SUBCLASS = "条码小类";
    String MATERIAL_CODE = "物料代码";
    String ENVIRONMENTAL_ATTRIBUTES = "环保属性";
    String SPECIFICATION_MODEL = "规格型号";
    String BRAND_NAME = "品牌名称";

    String BOARD_SNID = "BOARD_SNID";
    String BOARD_SN = "board_sn";
    String STATION_TYPE = "station_type";
    String LOOKUP_TYPE_5980 = "5980";
    String LOOKUP_TYPE_5974 = "5974";
    String LOOKUP_TYPE_5979 = "5979";

    String ACCOUNT_NAME_OUTBOUND_ERROR = "account_name_outbound_error";
    String STR_200 = "200";

    String TOTAL_QTY = "totalQty";

    String[] TYPE_AC = {"A", "C"};

    String REGEX_SN = "^(?:[A-Za-z\\d]{16}|\\d{12}|P\\d{12})$";
    String LOOKUP_CODE_********* = "*********";
    String LOOKUP_CODE_1777001 = "1777001";
    String LOOKUP_TYPE_INSTOCK_PRINT = "5973,1036";
    String LOOKUP_TYPE_5973 = "5973";

    String LOOKUP_TYPE_5870 = "5870";
    String LOOKUP_CODE_5870001 = "5870001";
    String LOOKUP_CODE_5870002 = "5870002";
    String LOOKUP_CODE_5870003 = "5870003";
    String LOOKUP_CODE_5870004 = "5870004";
    String LOOKUP_CODE_5870005 = "5870005";

    // 特定客户名称(用于判断是否阿里任务)
    String LOOKUP_TYPE_7500 = "7500";
    String LOOKUP_TYPE_7501 = "7501";
    String LOOKUP_TYPE_7502 = "7502";
    String LOOKU_VALUE_7503001 = "7503001";

    String CAL_IN_OUT_QTY = "CAL_IN_OUT_QTY";
    String CHECK_TYPE_INCOME = "来料清单";
    String CHECK_TYPE_REGISTER = "注册清单";

    String ISSUANCE_INFO_EXCEL = "批次发料顺序查询信息.xls";
    String VIRTUAL_PARENT_SN_NO = "VPSN";
    String STR_15="15";

    String PROD_RES_PREP_HEAD = "资源准备看板";
    String NONEED_LABELING = "NoNeed_Labeling";//类型：不需要单板贴标
    String ITEM_NO_LIST = "itemNos";
    String TASK_NO_LIST = "taskNos";
    String STR_17 = "17";
    String PRE_COMPLETE = "准备完成";
    String PRE_NOT_COMPLETE = "未准备完成";
    String PRE_NOT_NEED = "不涉及";
    String RECEIVING_MATERIALS = "收料中";
    String PRIMED = "首备中";
    String TO_BE_ISSUED = "待发放";
    String ALREADY_RECEIVED = "已接收";
    String PREPARING = "准备中";
    String ISSUED = "已发放";
    String UNDER_RELEASE = "发放中";

    String CFG_STATUS="cfgStatus";
    String SOURCE_TASK_LIST="sourceTaskList";
    String HAS_IMPORT="已导入";
    String NOT_IMPORT="未导入";
    String B_SMT_BOM_IMPORT_OK="上料表导入完成";
    String NULL_ANGLE="<null>度";
    String COLOR_RED  = "红色";
    String COLOR_YELLOW  = "黄色";

    String BOX_SCAN_LOCK_FAIL_MSG_ZH = "当前箱码正在打印，请稍后再试";

    String BOX_SCAN_LOCK_FAIL_MSG_EN = "The current box code is being printed, please try again later";

    String BOX_SCAN_SN_LOCK_FAIL_MSG_ZH = "当前条码正在进行装箱扫描处理，请稍后再试";

    String BOX_SCAN_SN_LOCK_FAIL_MSG_EN = "The current barcode is undergoing container scanning processing. Please try again later";

    String REGEX_FOUR = "^\\\\d+(\\\\.\\\\d{1,4})?$";
    // 改配
    String REWORK_2  = "REWORK_2";
    // 拆解
    String FG_DISAS_2  = "FG_DISAS_2";
    //无需获取质量码白名单
    String LOOKUP_TYPE_WHITE_BILL = "2025051521";
    //测试工序
    String LOOKUP_TYPE_TEST_PROCESS = "2025051531";
    String AOI = "AOI";

    /**
     * 阿里配置客户编码
     */
    String LOOKUP_TYPE_1004115 = "1004115";
    String ITEM_TYPE_FINISH = "成品料";

    String MIX = "MIX";

    /**
     * 维修结果映射
     */
    String LOOKUP_TYPE_1004120 = "1004120";
    String REPAIR_SUCCESS = "正常维修完成";

    String PASS_RESULT = "Pass";
    String FAIL_RESULT = "Fail";
    // 8.1.1主板生产质量
    String MES_ALIBABA_BOARD_PRODUCT_INFO = "ZTEiMES-Alibaba-BoardBoardproductinfo";
    // 8.1.3站位错误分析结果
    String ZTEI_MES_ALIBABA_CAPTURE_ERROR_PROCESS = "ZTEiMES-Alibaba-CaptureErrorProcess";

    String REPAIR_SN_PUSH = "alibaba过站推送异常";


    /**
     * 良品库的lookup_code编码
     */
    String LOOKUP_CODE_GOOD_WAREHOUSE = "7502001";

    /**
     * 不良品库的lookup_code编码
     */
    String LOOKUP_CODE_BAD_WAREHOUSE = "7502003";

    /**
     * buffer库的lookup_code编码
     */
    String LOOKUP_CODE_BUFFER_WAREHOUSE = "7502002";

    //任务类型为buffer
    String TASK_TYPE_BUFFER = "2";

    //任务状态为转正中  转正状态（1：待转正，2：转正中，3：已转正，4：已取消）
    String TASK_STATUS_B_TRANSFER = "2";

    //任务状态为待转正
    String TASK_STATUS_P_TRANSFER = "1";

    //入库单明细状态 0-已提交/1-已关闭/2-已接收/3-已拒绝
    //入库单状态 0已提交1已确认2已拒绝3已关闭4部分接收部分关闭 数据字典1114
    String STATUS_SUBMITTED = "0";

    //2-已接收
    String DETAIL_STATUS_RECEIVED = "2";

    // 单据类型为整装入库
    String BILL_TYPE_PACKAGE_STORAGE = "3";

    // 单据类型为转正入库单
    String BILL_TYPE_TRANSFERRED = "11";

    /**
     * 库房lookUp value
     */
    String LOOKUP_VALUE_WAREHOUSE = "7502";

}
