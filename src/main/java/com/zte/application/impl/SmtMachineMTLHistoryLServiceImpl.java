/**
 * 项目名称 : ${project_name}
 * 创建日期 : ${date}
 * 修改历史 :
 * 1. [${date}] 创建文件 by ${user}
 **/
package com.zte.application.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Maps;
import com.zte.application.BSmtBomDetailService;
import com.zte.application.ImesPDACommonService;
import com.zte.application.PkCodeInfoService;
import com.zte.application.SmtMachineDistributeScanService;
import com.zte.application.SmtMachineMTLHistoryHService;
import com.zte.application.SmtMachineMTLHistoryLService;
import com.zte.application.SmtMachineMaterialMoutingService;
import com.zte.application.SmtMachineMaterialPrepareService;
import com.zte.common.CommonUtils;
import com.zte.common.DateUtil;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.common.utils.NumConstant;
import com.zte.common.utils.ObtainRemoteServiceDataUtil;
import com.zte.common.utils.RedisKeyConstant;
import com.zte.common.utils.SymbolConstant;
import com.zte.domain.model.BSmtBomDetail;
import com.zte.domain.model.CFLine;
import com.zte.domain.model.PkCodeInfo;
import com.zte.domain.model.PkCodeInfoRepository;
import com.zte.domain.model.PsTask;
import com.zte.domain.model.PsWorkOrderBasic;
import com.zte.domain.model.SmtMachineMTLHistoryH;
import com.zte.domain.model.SmtMachineMTLHistoryHRepository;
import com.zte.domain.model.SmtMachineMTLHistoryL;
import com.zte.domain.model.SmtMachineMTLHistoryLRepository;
import com.zte.domain.model.SmtMachineMaterialMouting;
import com.zte.domain.model.SmtMachineMaterialMoutingRepository;
import com.zte.domain.model.SmtMachineMaterialPrepare;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.infrastructure.remote.ProductionDeliveryRemoteService;
import com.zte.interfaces.assembler.SmtMachineMTLHistoryLAssembler;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.itp.msa.util.string.StringHelper;
import com.zte.springbootframe.common.consts.SysConst;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.locale.LocaleMessageSourceBean;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.springbootframe.common.redis.RedisLock;
import com.zte.springbootframe.util.MESHttpHelper;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.zte.common.utils.Constant.LOCATION_HAS_MT;
import static com.zte.common.utils.Constant.UNDER_LINE;

/**
 * //
 *
 * <AUTHOR>
 **/
@Service
public class SmtMachineMTLHistoryLServiceImpl implements SmtMachineMTLHistoryLService{

    /**
     * 上料明细Service
     */
    @Autowired
    private BSmtBomDetailService bSmtBomDetailService;

    @Autowired
    private SmtMachineMTLHistoryLRepository smtMachineMTLHistoryLRepository;

    @Autowired
    private SmtMachineMTLHistoryHRepository smtMachineMTLHistoryHRepository;

    @Autowired
    private SmtMachineMTLHistoryHService smtMachineMTLHistoryHService;

    @Autowired
    private SmtMachineMTLHistoryLService smtMachineMTLHistoryLService;
    @Autowired
    private PkCodeInfoRepository pkCodeInfoRepository;
    @Autowired
    private SmtMachineMaterialMoutingService smtMachineMaterialMoutingService;
    @Autowired
    private SmtMachineMaterialMoutingRepository smtMachineMaterialMoutingRepository;

    @Autowired
    private SmtMachineDistributeScanService smtMachineDistributeScanService;

    @Autowired
    private ImesPDACommonService imesPDACommonService;
    @Autowired
    private RedisTemplate<String,Object> redisTemplate;

    @Autowired
    private SmtMachineMaterialPrepareService smtMachineMaterialPrepareService;

    @Autowired
    private StItemBarcodeServiceImpl stItemBarcodeService;

    @Autowired
    private PkCodeInfoService pkCodeInfoService;

    @Autowired
    private DatawbRemoteService datawbRemoteService;


    private static final Logger LOG = LoggerFactory.getLogger(SmtMachineMTLHistoryLServiceImpl.class);

    /**
     * 有选择性的增加实体数据
     *
     * @param record
     **/
    @Override
    public void insertSmtMachineMTLHistoryLSelective(SmtMachineMTLHistoryL record) {
        smtMachineMTLHistoryLRepository.insertSmtMachineMTLHistoryLSelective(record);
    }

    /**
     * 根据主键查询实体信息
     *
     * @param record
     * @return BsItemInfo
     **/
    @Override
    public SmtMachineMTLHistoryL selectSmtMachineMTLHistoryLById(SmtMachineMTLHistoryL record) {
        return smtMachineMTLHistoryLRepository.selectSmtMachineMTLHistoryLById(record);
    }

    /**
     * 增加实体数据
     * @param record
     **/
    @Override
    public int insertSmtMachineMTLHistoryL(SmtMachineMTLHistoryL record) {
        if (StringHelper.isEmpty(record.getHeaderId())){
            record.setHeaderId(UUID.randomUUID().toString());
            record.setEnabledFlag(Constant.FLAG_Y);
        }
        return smtMachineMTLHistoryLRepository.insertSmtMachineMTLHistoryL(record);
    }

    /**
     * 根据主键删除实体数据
     * @param record
     **/
    @Override
    public void deleteSmtMachineMTLHistoryLById(SmtMachineMTLHistoryL record) {
        smtMachineMTLHistoryLRepository.deleteSmtMachineMTLHistoryLById(record);
    }

    /**
     * 根据主键更新实体数据
     * @param record
     **/
    @Override
    public int updateSmtMachineMTLHistoryLById(SmtMachineMTLHistoryL record) {
        return smtMachineMTLHistoryLRepository.updateSmtMachineMTLHistoryLById(record);
    }

    /**
     * 有选择性的更新实体数据
     * @param record
     **/
    @Override
    public void updateSmtMachineMTLHistoryLByIdSelective(SmtMachineMTLHistoryL record) {
        smtMachineMTLHistoryLRepository.updateSmtMachineMTLHistoryLByIdSelective(record);
    }

    /**
     * 获取物料信息getlist方法
     * @param map 参数集
     * @param orderField 排序
     * @param order order
     * @param curPage 当前页
     * @param pageSize 页值
     * @return list
     * @throws Exception 异常
     */
    @Override
    public List<SmtMachineMTLHistoryL> getList(Map<String, Object> map, String orderField, String order, Long curPage,
                                               Long pageSize) throws Exception {
        List<SmtMachineMTLHistoryL> list = null;
        map.put("orderField", orderField);
        map.put("order", order);
        curPage = curPage < 1 ? 1 : curPage;
        pageSize = pageSize < 1 ? Constant.INT_5000 : pageSize;
        map.put("startRow", (curPage - 1) * pageSize + 1);
        map.put("endRow", curPage * pageSize);
        map.put("enabledFlag",Constant.FLAG_Y);
        list = smtMachineMTLHistoryLRepository.getList(map);
        return list;
    }

    /**
     * 上料历史查询-查全部明细
     * @param map 参数集
     * @param orderField 排序
     * @param order order
     * @param curPage 当前页
     * @param pageSize 页值
     * @return list
     * @throws Exception 异常
     */
    @Override
    public List<SmtMachineMTLHistoryL> getSmtMachineHisDetailAll(Map<String, Object> map, String orderField, String order, Long curPage,
                                               Long pageSize) throws Exception {
        List<SmtMachineMTLHistoryL> list = null;
        map.put("orderField", orderField);
        map.put("order", order);
        curPage = curPage < 1 ? 1 : curPage;
        pageSize = pageSize < 1 ? Constant.INT_5000 : pageSize;
        map.put("curPage", curPage);
        map.put("pageSize", pageSize);
        list = smtMachineMTLHistoryLRepository.getSmtMachineHisDetailAll(map);
        if (!CollectionUtils.isEmpty(list)) {
            // 添加物料规格型号
            addItemModel(list);
            //添加极性相关字段
            addIssueSeqRelation(list);
        }
        return list;
    }
	/**
	 * 获取指定类型上料历史记录
	 */
	@Override
	public Page<SmtMachineMTLHistoryL> getAnyTypeSmtMachineHisDetail(SmtMachineMTLHistoryL dto)throws Exception {
		//先校验分页参数--没分页报错--一页内容超过500条报错
		if(dto.getPage() == null || dto.getRows() == null){
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PAGING_QUERY_PARAMETERS_IS_EMPTY);
		}
		if(dto.getRows() > Constant.INT_500){
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.ROWS_MORE_THAN_FIVE_HUNDRED);
		}
		//限制时间必填，且不超过30天
		if(StringUtils.isBlank(dto.getLastUpdatedDateStart()) || StringUtils.isBlank(dto.getLastUpdatedDateEnd())){
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.TIME_CAN_NOT_BE_NULL);
		}else{
			Date startDate = DateUtil.convertStringToDate(dto.getLastUpdatedDateStart(), DateUtil.DATE_FORMATE_FULL);
			Date endDate = DateUtil.convertStringToDate(dto.getLastUpdatedDateEnd(), DateUtil.DATE_FORMATE_FULL);
            if (startDate == null || endDate == null) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.TIME_CAN_NOT_BE_NULL);
            }
			if(startDate.getTime() > endDate.getTime()){
				throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.START_TIME_IS_LATER);
			}
			if(DateUtil.caldaysByDate(endDate,startDate,Constant.INT_0).compareTo(Constant.TIME_INTERVAL) == Constant.INT_1) {
				throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SPAN_WHITIN_30DAYS);
			}
		}
		Page<SmtMachineMTLHistoryL> pageInfo = new Page<>(dto.getPage(),dto.getRows());
		pageInfo.setParams(dto);
		List<SmtMachineMTLHistoryL> list = smtMachineMTLHistoryLRepository.getAnyTypeSmtMachineHisDetail(pageInfo);
		// 添加物料规格型号
		if (!CollectionUtils.isEmpty(list)) {
			addItemModel(list);
		}
		pageInfo.setRows(list);
		return pageInfo;
	}

    /**
     * 上料历史查询-批量查明细
     * @param dto
     * @return list
     * @throws Exception 异常
     */
    @Override
    public PageRows<SmtMachineMTLHistoryL> getSmtMachineHisDetailBatch(SmtMachineMTLHistoryLDTO dto) throws Exception {
        PageRows<SmtMachineMTLHistoryL> vPage = new PageRows<>();
        Map<String, Object> map = new HashMap<>();
        List<String> headerIdList = Arrays.asList(dto.getHeaderId().split(","));
        long curPage = Long.parseLong(dto.getPage());
        long pageSize = Long.parseLong(dto.getRows());
        map.put("headerIdList", headerIdList);
        map.put("curPage", curPage);
        map.put("pageSize", pageSize);
        map.put("lineCode", dto.getLineCode());
        map.put("workOrder", dto.getWorkOrder());
        map.put("createUser", dto.getCreateUser());
        map.put("mountType", dto.getMountType());
        map.put("sourceBatch", dto.getSourceBatch());
        map.put("objectId", dto.getObjectId());
        map.put("itemCode", dto.getItemCode());
        map.put("moduleNo", dto.getModuleNo());
        map.put("differentDirectionFlag", dto.getDifferentDirectionFlag());
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(MpConstant.DATE_FORMAT_YYYYMMDDHHMMSS);
        if (StringUtils.isNotBlank(dto.getStartTime())) {
            Date starTime = simpleDateFormat.parse(dto.getStartTime());
            map.put("startTime", starTime);
        }
        if (StringUtils.isNotBlank(dto.getEndTime())) {
            Date endTime = simpleDateFormat.parse(dto.getEndTime());
            map.put("endTime", endTime);
        }
        //查询总记录
        long total = smtMachineMTLHistoryLRepository.getCountBatch(map);
        if (total > 0) {
            //批量查明细
            List<SmtMachineMTLHistoryL> list = smtMachineMTLHistoryLRepository.getSmtMachineHisDetailBatch(map);
            if (!CollectionUtils.isEmpty(list)) {
                //物料规格信息
                addItemModel(list);
                //增加操作类型名称
                addMountTypeName(list);
                //增加线体名称
                addLineName(list);
                //操作类型为2增加qc确认信息
                addQcConfirmInfo(list);
                //添加极性相关字段
                addIssueSeqRelation(list);
            }
            vPage.setCurrent(curPage);
            vPage.setTotal(total);
            vPage.setRows(list);
        }
        return vPage;
    }

    private void addMountTypeName(List<SmtMachineMTLHistoryL> list) throws Exception {
        String dicGetKey = MpConstant.DIC_GET_KEY;
        Map<String, Map<String, String>> mountTypeMap = ObtainRemoteServiceDataUtil.getLookupTypeByType(Constant.LOOK_UP_MOUNT_STATUS, Constant.FIELD_LOOKUP_MEANING);
        if (null != mountTypeMap && mountTypeMap.size() > 0) {
            for (SmtMachineMTLHistoryL item : list) {
                String mountTypeName = "";
                Map<String, String> dataMap = mountTypeMap.get(item.getMountType());
                if (dataMap != null) {
                    Object getValue = dataMap.get(dicGetKey);
                    if (getValue != null) {
                        mountTypeName = getValue.toString();
                    }
                }
                item.setMountTypeName(mountTypeName);
            }
        }
    }

    private void addLineName(List<SmtMachineMTLHistoryL> list)  throws Exception{
        List<String> lineCodeList = list.stream().map(t -> t.getLineCode()).distinct().collect(Collectors.toList());
        // 查询线体名称
        Map<String, String> lineMap = BasicsettingRemoteService.getLineNameByCodeList(lineCodeList);
        if (lineMap == null) {
            return;
        }
        for (SmtMachineMTLHistoryL item : list) {
            item.setLineName(lineMap.get(item.getLineCode()));
        }
    }

    private void addQcConfirmInfo(List<SmtMachineMTLHistoryL> list) throws Exception{
        List<String> headerIdList = list.stream().filter(t -> NumConstant.STR_TWO.equals(t.getMountType())).map(t -> t.getHeaderId()).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(headerIdList)){
            List<SmtMachineMTLHistoryL> listByHeaderIdBatch = smtMachineMTLHistoryLRepository.getListByHeaderIdBatch(headerIdList);
            Map<String, SmtMachineMTLHistoryL> map = listByHeaderIdBatch.stream().collect(Collectors.toMap(k -> k.getHeaderId(), v -> v, (oldValue, newValue) -> newValue));
            for (SmtMachineMTLHistoryL item : list) {
                SmtMachineMTLHistoryL historyL = map.get(item.getHeaderId());
                if (Objects.isNull(historyL)) {
                    continue;
                }
                item.setQcConfirmUser(historyL.getQcConfirmUser());
                item.setQcConfirmDate(historyL.getQcConfirmDate());
            }
        }
    }
    private void addItemModel(List<SmtMachineMTLHistoryL> list) {

        StringBuilder itemCode = new StringBuilder();
        Set<String> sourceBatchCodeSet = new HashSet<>();
        for (SmtMachineMTLHistoryL mtlHistoryL : list) {
            String sourceBatchCode = mtlHistoryL.getSourceBatchCode();
            if (!sourceBatchCodeSet.contains(sourceBatchCode)) {
                sourceBatchCodeSet.add(sourceBatchCode);
                itemCode.append(SymbolConstant.SINGLE_MARK).append(sourceBatchCode).append(SymbolConstant.SINGLE_MARK);
                itemCode.append(SymbolConstant.DOUHAO_CONSTANT);
            }
        }

        try {
            if (itemCode.length() > 0) {
                Map<String, Map<String, String>> colMap = stItemBarcodeService.getItemSupName(itemCode);
                for (SmtMachineMTLHistoryL mtlHistoryL : list) {
                    String sourceBatchCode = mtlHistoryL.getSourceBatchCode();
                    if (null != colMap && null != sourceBatchCode) {
                        Map<String, String> sourceBatchCodeMap = colMap.get(sourceBatchCode);
                        if (null != sourceBatchCodeMap) {
                            mtlHistoryL.setStyle(sourceBatchCodeMap.get(Constant.STYLE));
                            mtlHistoryL.setBgBrandNo(sourceBatchCodeMap.get(Constant.BG_BRAND_NO));
                        }
                    }
                }
            }
        } catch (Exception e) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.GET_ITEM_MODEL_INFO_ERROR);
        }
    }

    public void addIssueSeqRelation(List<SmtMachineMTLHistoryL> list) throws Exception {
        List<String> pkCodeList = list.stream().map(t -> t.getObjectId()).collect(Collectors.toList());
        // 查询pkCodeInfo
        List<PkCodeInfo> pkCodeInfoList = pkCodeInfoService.getListByPkCodes(pkCodeList);
        if (CollectionUtils.isEmpty(pkCodeInfoList)) {
            return;
        }
        // 查询发料信息
        List<TaskMaterialIssueSeqEntityDTO> packSpecList = datawbRemoteService.getIsHasDirFlag(pkCodeInfoList);
        if (CollectionUtils.isEmpty(packSpecList)) {
            return;
        }
        // key为uuid+供应商编码, value为dto
        Map<String, TaskMaterialIssueSeqEntityDTO> uuidSupToDtoMap = packSpecList.stream().collect(
                Collectors.toMap(k -> k.getSysLotCode() + k.getSupplerCode(), v -> v, (oldValue, newValue) -> newValue));
        for (PkCodeInfo entity : pkCodeInfoList) {
            TaskMaterialIssueSeqEntityDTO tempDTO = uuidSupToDtoMap.get(entity.getSysLotCode() + entity.getSupplerCode());
            if (tempDTO == null) {
                continue;
            }
            entity.setIsDir(tempDTO.getIsDir());
            entity.setBraidDirection(tempDTO.getBraidDirection());
        }
        //添加极性相关字段
        Map<String, PkCodeInfo> pkCodeInfoMap = pkCodeInfoList.stream().collect(Collectors.toMap(k -> k.getPkCode(), v -> v, (oldValue, newValue) -> newValue));
        for (SmtMachineMTLHistoryL smtMachineMTLHistoryL : list) {
            PkCodeInfo pkCodeInfo = pkCodeInfoMap.get(smtMachineMTLHistoryL.getObjectId());
            if (Objects.isNull(pkCodeInfo)) {
                continue;
            }
            smtMachineMTLHistoryL.setIsDir(pkCodeInfo.getIsDir());
            smtMachineMTLHistoryL.setBraidDirection(pkCodeInfo.getBraidDirection());
        }
    }


    /**
     * 查询上料头以及明细
     * @return
     * @throws Exception
     */
    @Override
    public List<SmtMachineMTLHistoryL> getSmtMachineHisList(SmtMachineMTLHistoryLDTO dto)throws Exception{
        return smtMachineMTLHistoryLRepository.getSmtMachineHisList(dto);
    }

    /**
     * 查询上料明细
     * @return
     * @throws Exception
     */
    @Override
    public List<SmtMachineMTLHistoryL> getSmtMachineHisInfo(List<String> workOrders,String inMountType)throws Exception{
        return smtMachineMTLHistoryLRepository.getSmtMachineHisInfo(workOrders,inMountType);
    }

    /**
     * getCount方法
     * @param map 参数局
     * @return long
     * @throws Exception 异常
     */
    @Override
    public long getCount(Map<String, Object> map) throws Exception {
        long count = smtMachineMTLHistoryLRepository.getCount(map);
        return count;
    }

    /**
     * getCount方法
     * @param map 参数局
     * @return long
     * @throws Exception 异常
     */
    @Override
    public long getSmtMachineHisDetailAllCount(Map<String, Object> map) throws Exception {
        long count = smtMachineMTLHistoryLRepository.getSmtMachineHisDetailAllCount(map);
        return count;
    }

    /**
     * 新增上料历史明细信息（没有表头信息时需要新增表头信息）
     * @param record 历史明细信息
     **/
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String insertSmtMachineMTLHistoryAll(SmtMachineMTLHistoryL record) throws MesBusinessException {
        String headerId = record.getHeaderId();
        if(StringHelper.isEmpty(headerId)) {
            SmtMachineMTLHistoryH head = new SmtMachineMTLHistoryH();
            head.setLineCode(record.getLineCode());
            head.setWorkOrder(record.getWorkOrder());
            head.setMountType(record.getMountType());
            head.setPickStatus(NumConstant.STR_ONE);
            SmtMachineMTLHistoryH retResult = smtMachineMTLHistoryHRepository.selectSmtMachineMTLHistoryH(head);
            if(retResult == null) {
                // 获取redis 锁
                String smtRedisKey = String.format(Constant.RedisKey.SMT_MTL_H,head.getLineCode(),head.getWorkOrder()
                        ,head.getMountType(),head.getPickStatus());
                Boolean locked = redisTemplate.opsForValue().setIfAbsent(smtRedisKey, Constant.LOCKED_STR, Constant.INT_1,
                        TimeUnit.MINUTES);
                if(locked){
                    try {
                        // 再查一次数据
                        retResult = smtMachineMTLHistoryHRepository.selectSmtMachineMTLHistoryH(head);
                        if( retResult == null){
                            head.setHeaderId(UUID.randomUUID().toString());
                            head.setPickStatus(NumConstant.STR_ONE);
                            head.setEnabledFlag(Constant.FLAG_Y);
                            head.setModuleCounts(record.getModuleCounts());
                            head.setCreateUser(record.getCreateUser());
                            head.setCreateDate(record.getCreateDate());
                            head.setLastUpdatedBy(record.getCreateUser());
                            head.setLastUpdatedDate(record.getCreateDate());
                            head.setOrgId(record.getOrgId());
                            head.setEntityId(record.getEntityId());
                            head.setFactoryId(record.getFactoryId());
                            head.setCfgHeaderId(record.getCfgHeaderId());
                            head.setAttr1(record.getAttr1());
                            smtMachineMTLHistoryHService.insertSmtMachineMTLHistoryH(head);
                            headerId = head.getHeaderId();
                        }else {
                            headerId = retResult.getHeaderId();
                        }
                    }finally {
                        redisTemplate.delete(smtRedisKey);
                    }
                }else {
                    throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SMT_MACHINE_H_RUNNING);
                }
            } else {
                headerId = retResult.getHeaderId();
            }
        }
        this.insertSmtMachineMTLHistoryL(record, headerId);

        if(record.getLastScanFlag()) {
            insertSmtMachineMTLHistoryAllAfter(record,headerId);
        }
        return headerId;
    }

    private void insertSmtMachineMTLHistoryL(SmtMachineMTLHistoryL record, String headerId) throws MesBusinessException {
        if (null != record.getPkCodeList() && MpConstant.NUM_0 < record.getPkCodeList().size()) {
            this.checkPkCodeInfo(record.getPkCodeList().stream().map(PkCodeInfo::getPkCode).collect(Collectors.toList()));
        	//多个pkCode插入多条历史记录
        	dealBatchPkCode(record, headerId);
        } else {
            this.checkPkCodeInfo(new LinkedList<String>(){{add(record.getObjectId());}});
            // QC复检添加对应的接料行ID
            if (Constant.MOUNT_TYPE_QC.equals(record.getMountType())) {
                record.setCollectingLineId(record.getLineId());
            }
        	record.setLineId(UUID.randomUUID().toString());
            record.setHeaderId(headerId);
            record.setEnabledFlag(Constant.FLAG_Y);
            record.setLastUpdatedBy(record.getCreateUser());
            record.setLastUpdatedDate(record.getCreateDate());
            record.setAttr1(record.getAttr1());
            record.setTracingQty(record.getQty());
			record.setOptDuration(record.getOptDuration());
			record.setCompDuration(record.getCompDuration());
			record.setMaterialRack(record.getMaterialRack());
            smtMachineMTLHistoryLRepository.insertSmtMachineMTLHistoryL(record);
        }
    }

    /**
     * 校验reelid 在本地工厂是否存在
     * @param reelIdList
     * @throws MesBusinessException
     */
    private void checkPkCodeInfo(List<String> reelIdList) throws MesBusinessException{
        PkCodeInfoDTO dto = new PkCodeInfoDTO();
        List<PkCodeInfo> pkList = new LinkedList<>();
        for (List<String> list : CommonUtils.splitList(reelIdList, Constant.FOUR_ZERO_ZERO)) {
            String pkCodesStr = list.stream()
                    .collect(Collectors.joining(Constant.SEPARATED_COMMA_COMMA, Constant.SINGLE_QUOTE, Constant.SINGLE_QUOTE));
            dto.setInPkCode(pkCodesStr);
            List<PkCodeInfo> pkCodeList = pkCodeInfoRepository.getList(dto);
            if(CollectionUtils.isEmpty(pkCodeList)){
                continue;
            }
            pkList.addAll(pkCodeList);
        }
        List<String> reelIds = pkList.stream().map(PkCodeInfo::getPkCode).collect(Collectors.toList());
        reelIdList.removeAll(reelIds);
        if (CollectionUtils.isEmpty(reelIdList)) {
            return;
        }
        throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,MessageId.REELID_NOT_EXIST,reelIdList.toArray());

    }

	/**
	 * 处理多个pkCode情况，分批插入历史表
	 * @param record
	 * @param headerId
	 */
    private void dealBatchPkCode(SmtMachineMTLHistoryL record, String headerId) {
		List<SmtMachineMTLHistoryL> historyList = new ArrayList<SmtMachineMTLHistoryL>();
		for (PkCodeInfo pkCodeInfo : record.getPkCodeList()) {
			SmtMachineMTLHistoryL history = new SmtMachineMTLHistoryL();
			BeanUtils.copyProperties(record, history);
			history.setLineId(UUID.randomUUID().toString());
			history.setHeaderId(headerId);
			history.setEnabledFlag(Constant.FLAG_Y);
			history.setObjectId(pkCodeInfo.getPkCode());
			history.setQty(pkCodeInfo.getItemQty());
			history.setItemCode(pkCodeInfo.getItemCode());
			history.setSourceBatchCode(pkCodeInfo.getSourceBatchCode());
            history.setTracingQty(record.getQty());
            history.setCombination(record.getCombination());
            history.setLpn(record.getLpn());
            history.setBomDirection(record.getBomDirection());
            history.setItemDirection(record.getItemDirection());
		    historyList.add(history);
		}
		List<List<SmtMachineMTLHistoryL>> bigHistoryList = CommonUtils.splitList(historyList,Constant.BATCH_SIZE);
		//分批插入历史表
		for (List<SmtMachineMTLHistoryL> tempList : bigHistoryList) {
			smtMachineMTLHistoryLRepository.insertSmtMachineMTLHistoryLBatch(tempList);
		}
	}

	@Override
    public void insertSmtMachineMTLHistoryAllAfter(SmtMachineMTLHistoryL record,String headerId){
        BSmtBomDetailDTO queryDTO = new BSmtBomDetailDTO();
        queryDTO.setCfgHeaderId(record.getCfgHeaderId());
        queryDTO.setOrgId(record.getOrgId());
        queryDTO.setFactoryId(record.getFactoryId());
        queryDTO.setEntityId(record.getEntityId());
        queryDTO.setAttr1(record.getAttr1());
        List<BSmtBomDetail> dList = null;
        // 读取配置：不需要校验虚拟站位的MountType
        setNoVirtualMountType(queryDTO, record);
        if(NumConstant.STR_FIVE.equals(record.getMountType())) {
            queryDTO.setWorkOrder(record.getWorkOrder());
            dList = bSmtBomDetailService.getBSmtBomDetailBindFeederList(queryDTO);
        }else{
            queryDTO.setMountType(record.getMountType());
            queryDTO.setWorkOrder(record.getWorkOrder());
            queryDTO.setLineCode(record.getLineCode());
            queryDTO.setPickStatusString(Constant.SINGLE_QUOTE+NumConstant.STR_ONE+Constant.SINGLE_QUOTE);
            dList = bSmtBomDetailService.getModuleBSmtBomDetailList(queryDTO);
        }
        if (CollectionUtils.isEmpty(dList)) {
            return;
        }
        boolean notScanFlag = false;
        List<String> trayList =  this.getListOfPalletStations(queryDTO.getLineCode());
        notScanFlag = this.checkScanFinish(dList, notScanFlag, record, trayList);
        if(!notScanFlag) {
            SmtMachineMTLHistoryH head = new SmtMachineMTLHistoryH();
            head.setHeaderId(headerId);
            head.setPickStatus(NumConstant.STR_TWO);
            head.setLastUpdatedBy(record.getCreateUser());
            head.setLastUpdatedDate(record.getCreateDate());
            smtMachineMTLHistoryHRepository.updateSmtMachineMTLHistoryHByIdSelective(head);
        }else {
            //如果没对比完成，进一步判断是否是合并备料场景，进行对比完成计算
            checkIsScanFinishWhenPreTogether(dList,record,trayList,headerId);
        }
    }

    private boolean checkScanFinish(List<BSmtBomDetail> dList ,Boolean notScanFlag, SmtMachineMTLHistoryL record,  List<String> trayList) {
        for(BSmtBomDetail detail : dList) {
            if(Constant.MOUTH_TYPE_FIVE.equals(record.getMountType())) {
                if(StringHelper.isEmpty(detail.getHisFeederNo()) &&
                        null != record.getLocationNo() &&
                        !record.getLocationNo().equals(detail.getLocationNo())) {
                    notScanFlag = true;
                    break;
                }
            }else if (Constant.STR_8.equals(record.getMountType())) {
                if(StringUtils.isEmpty(detail.getMaterialTray()) && !trayList.contains(detail.getMachineNo()+ UNDER_LINE +detail.getModuleNo()+ UNDER_LINE +detail.getLocationNo())) {
                    notScanFlag = true;
                    break;
                }
            }else if(StringHelper.isEmpty(detail.getMaterialTray())) {
                    // && !record.getLocationNo().equals(detail.getLocationNo())
                    notScanFlag = true;
                    break;
            }
        }
        return notScanFlag;
    }

    /**
     * 区分是否合并备料，是合并备料进一步进行判断是否对比完成
     * @return
     * @throws Exception
     */
    public void checkIsScanFinishWhenPreTogether(List<BSmtBomDetail> dList,SmtMachineMTLHistoryL record,List<String> trayList,String headerId){
        if(!Constant.STR_8.equals(record.getMountType())){
            return;
        }
        //判断是否有合并备料
        if(!record.getmIsBothSides()){
            return;
        }
        if (StringUtils.isEmpty(record.getWorkOrder())) {
            return;
        }
        //查询当前指令对应批次的指令数据
        PsWorkOrderBasicDTO paramDto = new PsWorkOrderBasicDTO();
        paramDto.setSourceTask(record.getWorkOrder().substring(Constant.INT_0, NumConstant.NUM_SEVEN));
        List<PsWorkOrderBasicDTO> orderList = PlanscheduleRemoteService.queryWorkOrderList(paramDto);
        //筛选出SMT-A,SMT-B指令，如果结果=2，继续判断
        List<PsWorkOrderBasicDTO> smtOrderList = orderList.stream().filter(orderDto -> orderDto.getCraftSection().equals(Constant.SMT_A)
                || orderDto.getCraftSection().equals(Constant.SMT_B)).collect(Collectors.toList());
        //一个指令一下，直接返回
        if (smtOrderList.size()<= NumConstant.NUM_ONE) {
            return;
        }
        //匹配得到另一面指令
        List<PsWorkOrderBasicDTO> otherOrderList =smtOrderList.stream().filter(smtOrderDto->!smtOrderDto.getWorkOrderNo().equals(record.getWorkOrder())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(otherOrderList)){
            return;
        }
        //根据线体+另一面指令查询另一面的上料历史
        BSmtBomDetailDTO paramsDto=new BSmtBomDetailDTO();
        paramsDto.setCfgHeaderId(otherOrderList.get(0).getCfgHeaderId());
        paramsDto.setMountType(record.getMountType());
        paramsDto.setWorkOrder(otherOrderList.get(0).getWorkOrderNo());
        paramsDto.setLineCode(record.getLineCode());
        paramsDto.setPickStatusString(Constant.SINGLE_QUOTE+NumConstant.STR_ONE+Constant.SINGLE_QUOTE);
        List<BSmtBomDetail> otherBomList= bSmtBomDetailService.getModuleBSmtBomDetailList(paramsDto);
        if(CollectionUtils.isEmpty(otherBomList)){
            return;
        }
        //循环当前面别的上料历史，在另一面上料历史，对同物料+机台模组站位进行匹配
        for(BSmtBomDetail e:dList){
            //如果有同机台模组站位并且当前站位无REELID，将另一面的REELID赋值到当前面别的上料历史
            String location=e.getItemCode()+e.getMachineNo()+e.getModuleNo()+e.getLocationNo();
            List<BSmtBomDetail> sameBomList=otherBomList.stream().filter(
                    otherBomDto->(otherBomDto.getItemCode()+otherBomDto.getMachineNo()+otherBomDto.getModuleNo()+otherBomDto.getLocationNo())
                            .equals(location)).collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(sameBomList)&&StringUtils.isEmpty(e.getMaterialTray())){
                e.setMaterialTray(sameBomList.get(0).getMaterialTray());
            }
        }
        //再次进行对比完成判断
        Boolean notScanFlag =checkScanFinish(dList,false,record,trayList);
        if(!notScanFlag){
            SmtMachineMTLHistoryH head = new SmtMachineMTLHistoryH();
            head.setHeaderId(headerId);
            head.setPickStatus(NumConstant.STR_TWO);
            head.setLastUpdatedBy(record.getCreateUser());
            head.setLastUpdatedDate(record.getCreateDate());
            smtMachineMTLHistoryHRepository.updateSmtMachineMTLHistoryHByIdSelective(head);
        }
    }

    /**
     * 获取托盘站位list
     * @param lineCode
     */
    private List<String> getListOfPalletStations(String lineCode) {
        List<SmtLocationInfoDTO> smtLocationInfoDTOList = BasicsettingRemoteService.getSmtLocationByLineCodeAndLocationType(lineCode,NumConstant.STR_TWO);
        return CollectionUtils.isEmpty(smtLocationInfoDTOList) ? new ArrayList<>() :
                smtLocationInfoDTOList.stream().map(e->e.getMachineNo()+ UNDER_LINE +e.getModuleNo()+ UNDER_LINE +e.getLocationNo()).distinct().collect(Collectors.toList());
    }

    public void setNoVirtualMountType(BSmtBomDetailDTO queryDTO, SmtMachineMTLHistoryL record) {
        try {
            List<SysLookupTypesDTO> lookups = BasicsettingRemoteService.getSysLookUpValue(Constant.NO_VIRTUAL_MOUNT_TYPE);
            boolean emptyLookup =  CollectionUtils.isEmpty(lookups) || lookups.get(Constant.INT_0) == null
                    || StringUtils.isBlank(lookups.get(Constant.INT_0).getLookupMeaning());
            if (emptyLookup) {
                return;
            }
            List<String> mts = Arrays.asList(lookups.get(Constant.INT_0).getLookupMeaning().split(Constant.COMMA));
            if(mts.contains(record.getMountType())) {
                queryDTO.setVirtualFlag(Constant.FLAG_N);
            }
        } catch (Exception e) {
            return;
        }
    }


    /**
     * getCount方法
     * @param map 参数局
     * @return long
     * @throws Exception 异常
     */
    @Override
    public long getWorkloadCount(Map<String, Object> map) throws Exception {
        long count = smtMachineMTLHistoryLRepository.getWorkloadCount(map);
        return count;
    }

    /**
     * 获取物料信息getlist方法
     * @param map 参数集
     * @param orderField 排序
     * @param order order
     * @param curPage 当前页
     * @param pageSize 页值
     * @return list
     * @throws Exception 异常
     */
    @Override
    public List<SmtMachineMTLHistoryWorkLoadDTO> getWorkloadList(Map<String, Object> map, String orderField, String order, Long curPage,
                                                                 Long pageSize) throws Exception {
        List<SmtMachineMTLHistoryWorkLoadDTO> list;
        map.put("orderField", orderField);
        map.put("order", order);
        curPage = curPage < 1 ? 1 : curPage;
        pageSize = pageSize < 1 ? Constant.INT_5000 : pageSize;
        map.put("startRow", (curPage - 1) * pageSize + 1);
        map.put("endRow", curPage * pageSize);
        list = smtMachineMTLHistoryLRepository.getWorkloadList(map);

        String factoryId = null;
        Object facId = map.get("factoryId");
        if (null != facId) {
            factoryId  = facId.toString();
        }
        Map<String, String> lineNameMap = ObtainRemoteServiceDataUtil.getLineByFactory(factoryId);
        for(SmtMachineMTLHistoryWorkLoadDTO dto: list){
            if(!StringUtils.isEmpty(dto.getLineCode())){
                dto.setLineName(lineNameMap.get(dto.getLineCode()));
            }
        }
        return list;
    }

    /**
     * 根据ReelId,MOUNT_TYPE(1/2)查询创建时间前后加3天的时间段
     */
	@Override
	public SmtMachineMTLHistoryL getTimeRange(SmtMachineMTLHistoryLDTO dto) throws Exception {

		// 根据查询条件查询时间范围
		SmtMachineMTLHistoryL entity = smtMachineMTLHistoryLRepository.getMinAndMaxCreateDate(dto);
		if (entity == null || entity.getCreateDate() == null || entity.getLastUpdatedDate() == null) {
			return null;
		}
		//原有逻辑要求前后3天。
        Date timeLimitLeft  = DateUtil.addDay(entity.getCreateDate(), NumConstant.NUM_NEGATIVE_THREE);
        Date timeLimitRight = DateUtil.addDay(entity.getLastUpdatedDate(), NumConstant.NUM_THREE);
        //验证是否超过90天，防止查询过多表。
        checkTimeInterval(timeLimitLeft, timeLimitRight, Constant.INT_90);
		entity.setTimeLimitLeftStr(DateUtil.convertDateToString(DateUtil.addDay(entity.getCreateDate(), NumConstant.NUM_NEGATIVE_THREE), DateUtil.DATE_FORMATE_DAY));
		entity.setTimeLimitRightStr(DateUtil.convertDateToString(DateUtil.addDay(entity.getLastUpdatedDate(), NumConstant.NUM_THREE), DateUtil.DATE_FORMATE_DAY));
		return entity;
	}

	private void checkTimeInterval(Date begin, Date end, long interval) throws Exception {
        if(end.getTime() - begin.getTime() < 0 || (end.getTime() - begin.getTime())/Constant.DAY_MILLISECOND > interval) {
            throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, MessageId.TIME_INTERVAL_ERROR);
        }
    }
    /**
     * 获取可QC接料复检记录
     * @param dto
     * @return
     */
    @Override
    public List<SmtMachineMTLHistoryLDTO> getCanQcReCheckItem(SmtMachineMTLHistoryLDTO dto){
        return smtMachineMTLHistoryLRepository.getCanQcReCheckItem(dto);
    }

    @Override
    public ServiceData checkReelId(SmtMachineMTLHistoryL record) {
        ServiceData ret = new ServiceData();
        RetCode retCode = new RetCode(RetCode.SUCCESS_CODE,RetCode.SUCCESS_MSGID);
        List<SmtMachineMTLHistoryL> hitoryList = smtMachineMTLHistoryLRepository.getReelIdHistory(record);
        if (!CollectionUtils.isEmpty(hitoryList)) {
            retCode = new RetCode(RetCode.BUSINESSERROR_CODE,MessageId.MATRIAL_HAS_USED);
            ret.setBo(hitoryList.get(Constant.INT_0));
            ret.setCode(retCode);
            return ret;
        }
        ret.setCode(retCode);
        return ret;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String saveDistributionInfo(SmtMachineMTLHistoryLDTO hisRecord) throws Exception {
    	SmtMachineMTLHistoryL entity = SmtMachineMTLHistoryLAssembler.toEntity(hisRecord);
        String headId = "";
    	if(StringUtils.isEmpty(hisRecord.getIsFirst()) ||  Constant.STR_TRUE.equals(CommonUtils.getStrTransNull(hisRecord.getIsFirst()).toLowerCase())){//
            headId = insertSmtMachineMTLHistoryAll(entity);
            //AB同时分料存在共用物料时
            if (StringHelper.isNotEmpty(hisRecord.getOtherWorkOrder()) && StringHelper.isNotEmpty(hisRecord.getLineCode())) {
                entity.setWorkOrder(hisRecord.getOtherWorkOrder());
                entity.setLineCode(hisRecord.getLineCode());
                entity.setCfgHeaderId(hisRecord.getOtherCfgHeaderId());
                entity.setHeaderId("");
                headId = insertSmtMachineMTLHistoryAll(entity);
            }
            //双 轨场景，关联指令与源线体指令公用物料时
            if (StringHelper.isNotEmpty(hisRecord.getRelatedWorkOrder()) && StringHelper.isNotEmpty(hisRecord.getRelatedLine())) {
                entity.setWorkOrder(hisRecord.getRelatedWorkOrder());
                entity.setLineCode(hisRecord.getRelatedLine());
                entity.setCfgHeaderId(hisRecord.getRelatedCfgHeaderId());
                entity.setHeaderId("");
                headId = insertSmtMachineMTLHistoryAll(entity);

            }
        }
        //插入分料历史数据
        SmtMachineDistributeScanEntityDTO smtMachineDistributeScanEntityDTO = new SmtMachineDistributeScanEntityDTO();
        BeanUtils.copyProperties(hisRecord, smtMachineDistributeScanEntityDTO);
        PsWorkOrderBasic  planBasic = PlanscheduleRemoteService.findWorkOrder(hisRecord.getWorkOrder());
        smtMachineDistributeScanEntityDTO.setProdplanId(planBasic==null?"":planBasic.getSourceTask());
        smtMachineDistributeScanService.save(smtMachineDistributeScanEntityDTO);
        return headId;
    }

    /**
     * 查询上料历史明细表中是否有数据（基于物料代码、线体、模组、站位、指令）
     * @param
     * @return
     */
    @Override
    public boolean isReceive(SmtMachineMTLHistoryLDTO record) {
        long count = smtMachineMTLHistoryLRepository.getLineCount(record);
        return count > 0 ? true:false;
    }

    @Override
    public boolean isQcReCheck(String itemNo, String moduleNo, String locationNo,String workOrder,String objectId) {
        SmtMachineMTLHistoryLDTO record = new SmtMachineMTLHistoryLDTO();
        record.setObjectId(objectId);
        record.setModuleNo(moduleNo);
        record.setWorkOrder(workOrder);
        record.setLocationNo(locationNo);
        record.setItemCode(itemNo);
        record.setMountType(Constant.MOUNT_TYPE_QC);
        long count = smtMachineMTLHistoryLRepository.getLineCount(record);
        return count > 0 ? true:false;
    }

    /**
     * 校验上料历史明细与上料表（基于物料代码、模组、占位且上料表需移除在上料放错异常跳过中的数据）对比（粒度比对到物料代码行， 两边的物料代码相等）
     * @param
     * @return
     */
    @Override
    public boolean equalMaterialExceptErrorSkip(String lineCode, String workOrder) throws  Exception{
        PsWorkOrderBasic  planBasic = PlanscheduleRemoteService.findWorkOrder(workOrder);
        LOG.info("点对点调用根据指令获取实体数据{}",workOrder);
        if(null == planBasic){
            return  false;
        }
        // TODO: 2019/8/12  参数校验及方法提取
        Map param  = Maps.newHashMap();
        param.put("lineCode",lineCode);
        param.put("taskNo", CommonUtils.getStrTransNull(planBasic.getSourceTask()));
        param.put("craftSection",CommonUtils.getStrTransNull(planBasic.getCraftSection()));
        param.put("workOrder",workOrder);
        return smtMachineMTLHistoryLRepository.equalMaterialExceptErrorSkip(param) != 0 ? false: true;
    }

    /**
     * 根据创建时间和批次统计上料历史行表中某批次的ReelId个数
     *
     * @param queryDto 入参
     * @return list
     */
    @Override
    public List<QuerySmtMachineMTLHistoryLDTO> countReelIdCountBySourceBatchAndTime(QuerySmtMachineMTLHistoryLDTO queryDto) {
        return smtMachineMTLHistoryLRepository.countReelIdCountBySourceBatchAndTime(queryDto);
    }

    /**
     * 有选择性的批量更新实体数据
     *
     * @param recordList
     **/
    @Override
    public int batchUpdateWithTime(List<SmtMachineMTLHistoryL> recordList) {
        return smtMachineMTLHistoryLRepository.batchUpdateWithTime(recordList);
    }

    @Override
    public BigDecimal getItemQty(Map<String, Object> map, String itemCode) throws Exception {
        BigDecimal itemQtySum = new BigDecimal(NumConstant.NUM_ZERO);
        List<SmtMachineMTLHistoryL> historyLS = getList(map,null,null,0L,0L);
        for(SmtMachineMTLHistoryL smtMachineMTLHistoryL:historyLS){
            itemQtySum = itemQtySum.add(smtMachineMTLHistoryL.getQty());
        }
        return itemQtySum;
    }
    /**
     * d点对点调用箱内容
     *
     * @param map object_id
     *
     **/

    private List<ContainerContentInfoDTO> getContainerContentInfo(Map map)  {
        JsonNode json = ProductionDeliveryRemoteService.getContainerContentInfo(map);
        if (json!= null&&json.get(MpConstant.JSON_BO) != null){
            String bo = json.get(MpConstant.JSON_BO).toString();
            List<ContainerContentInfoDTO> list = (List<ContainerContentInfoDTO>) JacksonJsonConverUtil.jsonToListBean(bo,
                    new TypeReference<ArrayList<ContainerContentInfoDTO>>() {
                    });
            return list;
        }
        return new ArrayList<ContainerContentInfoDTO>();
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertSmtMachineMTLHistoryHeadAndDetail(SmtMachineMTLHistoryL record)throws Exception {
//        计算旧容器的数量分组合物料和非组合物料场景
//        组合物料
       BigDecimal totalQty= new BigDecimal(NumConstant.NUM_ZERO);
        if (!StringHelper.isEmpty(record.getCombination()) && Constant.FLAG_Y.equals(record.getCombination())) {
//        数量取箱内容表
            Map map =new HashMap<>();
            map.put("lpn",record.getLpn());
            List<ContainerContentInfoDTO> containerContentInfoDTOList = getContainerContentInfo(map);
            for(int i=0;i<containerContentInfoDTOList.size();i++){
                if(containerContentInfoDTOList.get(i).getContentQty() != null) {
                    totalQty=totalQty.add(containerContentInfoDTOList.get(i).getContentQty());
                }
            }

        } else {
//            非组合物料
            PkCodeInfoDTO pkCode = new PkCodeInfoDTO();
            pkCode.setInPkCode(record.getInOldPkCode());
            List<PkCodeInfo> pkCodeInfoList =  pkCodeInfoRepository.getPickCodeList(pkCode);
            for (int i=0 ;i < pkCodeInfoList.size(); i++) {

                if (pkCodeInfoList.get(i).getItemQty() != null) {
                    totalQty = totalQty.add(pkCodeInfoList.get(i).getItemQty());
                }
            }
        }
        extractedUpdateOrDel(record, totalQty);
        return addSmtMachineHistory(record);
    }

    public void extractedUpdateOrDel(SmtMachineMTLHistoryL record, BigDecimal totalQty) throws Exception {
        if (totalQty.compareTo(new BigDecimal(NumConstant.NUM_ZERO)) > 0){
            SmtMachineMaterialMouting smt = new SmtMachineMaterialMouting ();
            smt.setLpn(record.getLpn());
            smt.setNextReelRowid(record.getReelRowid());
            smtMachineMaterialMoutingRepository.updateSmtMachineMaterialMoutingByOthers(smt);
        }else{
            if (StringHelper.isEmpty(record.getNextReelRowid())){
                SmtMachineMaterialMouting smt = new SmtMachineMaterialMouting ();
                smt.setLpn(record.getLpn());
                smt.setNewLpn(record.getNewLpn());
                smt.setInPkCode(record.getInPkCode());
                smtMachineMaterialMoutingService.deleteAndInsertSmtMachineMaterialMouting(smt);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public int addSmtMachineHistory(SmtMachineMTLHistoryL record) {
        record.setHeaderId(UUID.randomUUID().toString());
        record.setLpn(record.getNewLpn());
        SmtMachineMTLHistoryH smt = setSmtHistoryInfo(record);
        smtMachineMTLHistoryHRepository.insertSmtMachineMTLHistoryHSelective(smt);
        PkCodeInfoDTO pkCode = new PkCodeInfoDTO();
        pkCode.setInPkCode(record.getInPkCode());
        List<PkCodeInfo> pkCodeInfoList =  pkCodeInfoRepository.getPickCodeList(pkCode);
        List<SmtMachineMTLHistoryL> list = new ArrayList<>();
        for(PkCodeInfo info:pkCodeInfoList){
            SmtMachineMTLHistoryL historyL = new SmtMachineMTLHistoryL();
            BeanUtils.copyProperties(record, historyL);
            historyL.setLineId(UUID.randomUUID().toString());
            historyL.setItemCode(info.getItemCode());
            historyL.setItemName(info.getItemName());
            historyL.setQty(info.getItemQty());
            historyL.setObjectId(info.getPkCode());
            list.add(historyL);
        }
        int count= smtMachineMTLHistoryLRepository.insertSmtMachineMTLHistoryLBatch(list);
        return count;
    }
    @Transactional(rollbackFor = Exception.class)
    public SmtMachineMTLHistoryH setSmtHistoryInfo(SmtMachineMTLHistoryL record) {
        SmtMachineMTLHistoryH smt = new SmtMachineMTLHistoryH();
        smt.setHeaderId(record.getHeaderId());
        smt.setLineCode(record.getLineCode());
        smt.setWorkOrder(record.getWorkOrder());
        smt.setMountType(record.getMountType());
        smt.setCreateUser(record.getCreateUser());
        smt.setOrgId(record.getOrgId());
        smt.setEnabledFlag(Constant.FLAG_Y);
        smt.setEntityId(record.getEntityId());
        smt.setFactoryId(record.getFactoryId());
        smt.setCfgHeaderId(record.getCfgHeaderId());
        smt.setPickStatus(record.getPickStatus());
        return smt;
    }

    /**
     * 产线物料消耗查询物料上料历史
     * @param dto
     * @return
     * @throws Exception
     */
    @Override
    public List<SmtMachineMTLHistoryL> getHistoryQty(SmtMachineMTLHistoryL dto)throws Exception{
        return smtMachineMTLHistoryLRepository.getHistoryQty(dto);
    }

    /**
    * feeder拔出接口
    *@Author: 10307315陈俊熙
    *@date 2022/5/13 下午2:13
    *@param dto
    *@return void
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleFeederExtractionOperation(FeederInsertExtractionDTO dto) throws Exception {
        // 参数必填校验
        validateFeederInseAndExtrDTO(dto);
        // 查询并验证线体是否支持智能feeder(并设置dto线体编码)
        validateIntelligentFeederFlag(dto);
        // 自旋锁(使用线体编码、模组、站位)
        String key = RedisKeyConstant.FEEDER_INSERTION_OR_EXTRACTION + dto.getLineCode() + Constant.AND +
                dto.getModuleNo() + Constant.AND + dto.getLocationNo();
        RedisLock redisLock = new RedisLock(key);
        // 自选3000ms
        if (!redisLock.lock(Constant.INT_3000)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.FAILED_TO_GET_REDIS_LOCK);
        }
        try {
            // 根据线体、模组、站位、feeder查询机台在用enabled_flag为Y数据
            Map<String, Object> map = new HashMap<>(Constant.INT_4);
            map.put("lineCode", dto.getLineCode());
            map.put("moduleNo", dto.getModuleNo());
            map.put("locationNo", dto.getLocationNo());
            map.put("feederNo", dto.getFeederNo());
            List<SmtMachineMaterialMouting> matMoutingList =
                    smtMachineMaterialMoutingService.getList(map, null, null);
            if (CollectionUtils.isEmpty(matMoutingList)) {
                throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, MessageId.FEEDER_NOT_EXIST_MOUTING,
                        new String[]{dto.getFeederNo()});
            }
            // 失效Mouting记录
            Set<String> moutingIdSet = matMoutingList.stream().map(e -> e.getMachineMaterialMoutingId())
                    .collect(Collectors.toSet());
            Map<String, String> headerMap = MESHttpHelper.getHttpRequestHeader();
            String lastUpdatedBy = headerMap.get(Constant.X_EMP_NO_SMALL);
            smtMachineMaterialMoutingService.setMaterialMoutingDisenabledbyIdSet(moutingIdSet, lastUpdatedBy);
            // 正常业务场景为一条，但考虑异常出现多条为Y的数据，但数量也很少，因此循环插入处理
            for (SmtMachineMaterialMouting entity : matMoutingList) {
                // 写头表
                String workOrderNo = entity.getWorkOrder();
                String headerId = smtMachineMTLHistoryLService.dealMTLHistoryH(workOrderNo, dto.getLineCode(), Constant.MOUNT_TYPE_FEEDER_EXTRACTION);
                // 写行表
                insertMTLHistoryLAdapter(headerId, entity, Constant.MOUNT_TYPE_FEEDER_EXTRACTION);
            }
        }finally {
            redisLock.unlock();
        }
    }

    /**
    * feeder插入接口
    *@Author: 10307315陈俊熙
    *@date 2022/5/16 上午9:22
    *@param
     * @param dto
    *@return void
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleFeederInsertOperation(FeederInsertExtractionDTO dto) throws Exception {
        // 参数必填校验
        validateFeederInseAndExtrDTO(dto);
        // 查询并验证线体是否支持智能feeder(并设置dto线体编码)
        validateIntelligentFeederFlag(dto);
        // 自旋锁(使用线体编码、模组、站位)
        String key = RedisKeyConstant.FEEDER_INSERTION_OR_EXTRACTION + dto.getLineCode() + Constant.AND +
                dto.getModuleNo() + Constant.AND + dto.getLocationNo();
        RedisLock redisLock = new RedisLock(key);
        // 自选3000ms
        if (!redisLock.lock(Constant.INT_3000)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.FAILED_TO_GET_REDIS_LOCK);
        }
        try {
            // 根据线体、模组、站位查询机台在用
            SmtMachineMaterialMouting entity = new SmtMachineMaterialMouting();
            entity.setModuleNo(dto.getModuleNo());
            entity.setLocationNo(dto.getLocationNo());
            entity.setLineCode(dto.getLineCode());
            List<SmtMachineMaterialMouting> matMoutingList =
                    smtMachineMaterialMoutingService.getAllByParamsOfFeederInsertion(entity);
            // 根据feederNo查询提前备料表
            List<SmtMachineMaterialPrepare> matPrepareList =
                    smtMachineMaterialPrepareService.selectSmtMachineMaterialPrepareByFeederNo(dto.getFeederNo());
            // 默认为0，0表示正常备料(提前备料不为空)；
            // 1表示临时拔插(无提前备料，但是存在为N的机台在用数据)
            // 2表示临时拔插，但是拔出未正常调用拔出接口(无提前备料，但传入feeder在机台在用有enabledFlag为Y数据)
            // 3表示临时更换feeder，(提前备料有数据，根据参数查询的机台在用最新数据是否和和提前备料记录中指令、模组、站位、机台、reelId一致的)
            int saveDateType = 0;
            SmtMachineMaterialMouting matMoutingY = new SmtMachineMaterialMouting();
            SmtMachineMaterialMouting matMoutingN = new SmtMachineMaterialMouting();
            if (CollectionUtils.isEmpty(matMoutingList)) {
                // 无机台信息和提前备料信息，报错， 若有提前备料，则继续
                if (CollectionUtils.isEmpty(matPrepareList)) {
                    throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE,
                            MessageId.PREPARE_INFO_OF_FEEDER_NOT_EXIST, new String[]{dto.getFeederNo()});
                }
            } else {
                // 根据enabledFlag为Y或N拆分成2个列表
                Map<Boolean, List<SmtMachineMaterialMouting>> tempMap = matMoutingList.stream()
                        .collect(Collectors.groupingBy(e -> Constant.FLAG_Y.equals(e.getEnabledFlag())));
                if(tempMap==null){
                    return;
                }
                // 取最新数据。
                List<SmtMachineMaterialMouting> trueList=tempMap.get(true);
                List<SmtMachineMaterialMouting> falseList=tempMap.get(false);

                matMoutingY = CollectionUtils.isEmpty(trueList) ? null : trueList.stream().max(Comparator.comparing
                        (SmtMachineMaterialMouting::getLastUpdatedDate)).get();
                matMoutingN = CollectionUtils.isEmpty(falseList)? null : falseList.stream().max(Comparator.comparing
                        (SmtMachineMaterialMouting::getLastUpdatedDate)).get();
                if (CollectionUtils.isEmpty(matPrepareList)) { // 只有提前备料表为空，才要判断是否是临时插拔
                    saveDateType = confirmTemporaryExtraction(matMoutingY, matMoutingN, dto);
                }
                else {
                    // 判定是否是临时更换feeder
                    saveDateType = confirmTemChangeFeeder(matMoutingList, matPrepareList);
                }
                // 将不是传入feeder的Y记录，enabledFlag改为N
                dealListMaterialMoutingY(tempMap.get(true), dto);
            }
            // 分支逻辑处理完后，准备保存数据。
            saveData(saveDateType, matMoutingN, matMoutingY, matPrepareList, dto);
        } finally {
            redisLock.unlock();
        }
    }

    /**
    * 处理不同插入类型
    *@Author: 10307315陈俊熙
    *@date 2022/5/16 上午9:22
    *@param
     * @param saveDateType
     * @param matMoutingN
     * @param matMoutingY
     * @param matPrepareList
     * @param dto
    *@return void
    */
    private void saveData(int saveDateType, SmtMachineMaterialMouting matMoutingN,
                          SmtMachineMaterialMouting matMoutingY,
                          List<SmtMachineMaterialPrepare> matPrepareList, FeederInsertExtractionDTO dto) throws Exception{
        String workOrderNo = Constant.STR_EMPTY;
        SmtMachineMaterialMouting matMouting = null;
        SmtMachineMaterialPrepare prepare = null;
        Map<String, String> headerMap = MESHttpHelper.getHttpRequestHeader();
        // 用于写上料历史行表
        // 如果是临时拔插，则无需校验reelid，
        if (saveDateType == Constant.INT_1&& matMoutingN != null) {
            // 将feeder对应的记录设置为Y
            matMoutingN.setLastUpdatedBy(headerMap.get(Constant.X_EMP_NO_SMALL));
            smtMachineMaterialMoutingService.setMaterialMoutingEnabled(matMoutingN);
            workOrderNo = matMoutingN.getWorkOrder();
            matMouting = matMoutingN;
        }
        if (saveDateType == Constant.INT_2 && matMoutingY != null) {
            matMoutingY.setLastUpdatedBy(headerMap.get(Constant.X_EMP_NO_SMALL));
            smtMachineMaterialMoutingService.setMaterialMoutingEnabled(matMoutingY);
            workOrderNo = matMoutingY.getWorkOrder();
            matMouting = matMoutingY;
        }
        // 正常逻辑，需校验prepare表及reelid相关性校验
        if (saveDateType == Constant.INT_0) {
            // 校验matPrepareList是否满足约束条件
            validateMaterialPrepare(matPrepareList, dto);
            prepare = matPrepareList.get(Constant.INT_0);
            String reelId = prepare.getObjectId();
            workOrderNo = prepare.getWorkOrder();
            // 校验reelid是否满足约束条件;并返回pkCode表中查到的实体。
            valiedateReelId(reelId, workOrderNo, dto);
        }
        // 临时更改feeder，处理。
        if (saveDateType == Constant.INT_3) {
            // 取为Y，为N最新数据。更新mouting表数据为Y，feederNo为提前备料表的feeder。
            SmtMachineMaterialMouting entiy = getLastMoutingEntity(matMoutingY, matMoutingN);
            entiy.setLastUpdatedBy(headerMap.get(Constant.X_EMP_NO_SMALL));
            entiy.setFeederNo(matPrepareList.get(Constant.INT_0).getFeederNo());
            smtMachineMaterialMoutingService.updateFeederAndSetEnabled(entiy);
            // 删除prepare记录。
            smtMachineMaterialPrepareService.deleteSmtMachineMaterialPrepareById(matPrepareList.get(Constant.INT_0));
            workOrderNo = entiy.getWorkOrder();
            matMouting = entiy;
        }
        // 写上料历史；处理头表数据
        String headId = smtMachineMTLHistoryLService.dealMTLHistoryH(workOrderNo, dto.getLineCode(), Constant.MOUNT_TYPE_FEEDER_INSERT);
        // 为0情况使用prepare写行表
        if (saveDateType == Constant.INT_0) {
            insertMTLHistoryLAdapter(headId, prepare, Constant.MOUNT_TYPE_FEEDER_INSERT);
            return;
        }
        // 其他不需要校验reelid的情况 使用mouting实体。
        insertMTLHistoryLAdapter(headId, matMouting, Constant.MOUNT_TYPE_FEEDER_INSERT);
    }

    /**
     *  得到最新的mouting数据
     * @param matMoutingY
     * @param matMoutingN
     * @return
     */
    private SmtMachineMaterialMouting getLastMoutingEntity(SmtMachineMaterialMouting matMoutingY, SmtMachineMaterialMouting matMoutingN) {
        SmtMachineMaterialMouting entity = new SmtMachineMaterialMouting();
        if (matMoutingN == null && matMoutingY == null) {
            return entity;
        }
        if (matMoutingY == null) {
            return matMoutingN;
        }
        if (matMoutingN == null) {
            return matMoutingY;
        }
        if (matMoutingN.getLastUpdatedDate() != null && matMoutingY.getLastUpdatedDate() != null) {
            if (matMoutingN.getLastUpdatedDate().compareTo(matMoutingY.getLastUpdatedDate()) > 0) {
                return matMoutingN;
            }
            return matMoutingY;
        }
        return entity;
    }

    /**
    *  数据插入行表的适配器
    *@Author: 10307315陈俊熙
    *@date 2022/5/13 下午3:30
    *@param
     * @param headId
     * @param entity
     * @param mountType
    *@return void
    */
    private void insertMTLHistoryLAdapter(String headId, Object entity, String mountType) {
        if (entity instanceof SmtMachineMaterialMouting) {
            SmtMachineMaterialMouting matMouting = (SmtMachineMaterialMouting) entity;
            dealMTLHistoryLByMouting(headId, matMouting, mountType);
        }
        if (entity instanceof SmtMachineMaterialPrepare) {
            SmtMachineMaterialPrepare prepare = (SmtMachineMaterialPrepare) entity;
            dealMTLHistoryLByPrepare(headId, prepare, mountType);
        }
    }

    /**
    * 使用mouting实体创建上料历史行表
    *@Author: 10307315陈俊熙
    *@date 2022/5/16 上午9:24
    *@param
     * @param headId
     * @param matMouting
     * @param mountType
    *@return void
    */
    private void dealMTLHistoryLByMouting(String headId, SmtMachineMaterialMouting matMouting, String mountType) {
        SmtMachineMTLHistoryL mtlHistoryL = new SmtMachineMTLHistoryL();
        mtlHistoryL.setHeaderId(headId);
        mtlHistoryL.setWorkOrder(matMouting.getWorkOrder());
        mtlHistoryL.setLineCode(matMouting.getLineCode());
        mtlHistoryL.setItemCode(matMouting.getItemCode());
        mtlHistoryL.setItemName(matMouting.getItemName());
        mtlHistoryL.setLocationNo(matMouting.getLocationNo());
        mtlHistoryL.setObjectId(matMouting.getObjectId());
        mtlHistoryL.setQty(matMouting.getQty());
        mtlHistoryL.setEnabledFlag(Constant.FLAG_Y);
        mtlHistoryL.setMountType(mountType);
        mtlHistoryL.setLineId(UUID.randomUUID().toString());
        mtlHistoryL.setMachineNo(matMouting.getMachineNo());
        mtlHistoryL.setSourceBatchCode(matMouting.getSourceBatchCode());
        mtlHistoryL.setModuleNo(matMouting.getModuleNo());
        mtlHistoryL.setFeederNo(matMouting.getFeederNo());
        Map<String, String> headerMap = MESHttpHelper.getHttpRequestHeader();
        mtlHistoryL.setCreateUser(headerMap.get(Constant.X_EMP_NO_SMALL));
        mtlHistoryL.setEntityId(new BigDecimal(Constant.INT_2));
        String factoryId = headerMap.get(SysConst.HTTP_HEADER_X_FACTORY_ID_LOW_CASE);
        mtlHistoryL.setFactoryId(new BigDecimal(factoryId));
        mtlHistoryL.setLastUpdatedBy(headerMap.get(Constant.X_EMP_NO_SMALL));
        mtlHistoryL.setTracingQty(matMouting.getQty());
        smtMachineMTLHistoryLRepository.insertSmtMachineMTLHistoryL(mtlHistoryL);
    }

    /**
    * 验证备料数据是否满足条件
    *@Author: 10307315陈俊熙
    *@date 2022/5/16 上午9:38
    *@param
     * @param matPrepareList
     * @param dto
    *@return void
    */
    private void validateMaterialPrepare(List<SmtMachineMaterialPrepare> matPrepareList,
                                         FeederInsertExtractionDTO dto) throws Exception {
        if (CollectionUtils.isEmpty(matPrepareList)) {
            return;
        }
        SmtMachineMaterialPrepare materialPrepare = matPrepareList.get(Constant.INT_0);
        if (matPrepareList.size() > Constant.INT_1) {
            String[] message = new String[]{materialPrepare.getFeederNo()};
            throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, MessageId.FEEDER_HAS_MULTIPLE_RECORD, message);
        }
        if (!dto.getLineCode().equals(materialPrepare.getLineCode()) || !dto.getLocationNo().equals(materialPrepare.getLocationNo())
                || !dto.getModuleNo().equals(materialPrepare.getModuleNo())) {
            String[] message = new String[]{materialPrepare.getFeederNo()};
            throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, MessageId.PARAMS_NOT_MATCH_PREPARE_DATA, message);
        }
    }

    /**
    * 使用提前备料数据处理上料历史行表
    *@Author: 10307315陈俊熙
    *@date 2022/5/16 上午9:40
    *@param
     * @param headId
     * @param prepare
     * @param mountType
    *@return void
    */
    private void dealMTLHistoryLByPrepare(String headId, SmtMachineMaterialPrepare prepare, String mountType) {
        SmtMachineMTLHistoryL mtlHistoryL = new SmtMachineMTLHistoryL();
        mtlHistoryL.setHeaderId(headId);
        mtlHistoryL.setWorkOrder(prepare.getWorkOrder());
        mtlHistoryL.setLineCode(prepare.getLineCode());
        mtlHistoryL.setItemCode(prepare.getItemCode());
        mtlHistoryL.setLocationNo(prepare.getLocationNo());
        mtlHistoryL.setObjectId(prepare.getObjectId());
        mtlHistoryL.setQty(prepare.getQty());
        mtlHistoryL.setEnabledFlag(Constant.FLAG_Y);
        mtlHistoryL.setMountType(mountType);
        mtlHistoryL.setLineId(UUID.randomUUID().toString());
        mtlHistoryL.setMachineNo(prepare.getMachineNo());
        mtlHistoryL.setSourceBatchCode(prepare.getSourceBatchCode());
        mtlHistoryL.setModuleNo(prepare.getModuleNo());
        mtlHistoryL.setFeederNo(prepare.getFeederNo());
        Map<String, String> headerMap = MESHttpHelper.getHttpRequestHeader();
        mtlHistoryL.setCreateUser(headerMap.get(Constant.X_EMP_NO_SMALL));
        mtlHistoryL.setEntityId(new BigDecimal(Constant.INT_2));
        String factoryId = headerMap.get(SysConst.HTTP_HEADER_X_FACTORY_ID_LOW_CASE);
        mtlHistoryL.setFactoryId(new BigDecimal(factoryId));
        mtlHistoryL.setLastUpdatedBy(headerMap.get(Constant.X_EMP_NO_SMALL));
        mtlHistoryL.setTracingQty(prepare.getQty());
        smtMachineMTLHistoryLRepository.insertSmtMachineMTLHistoryL(mtlHistoryL);
    }

    /**
    *  处理上料历史头表数据
    *@Author: 10307315陈俊熙
    *@date 2022/5/13 下午3:19
    *@param 
     * @param workOrderNo
     * @param lineCode
     * @param mountType
    *@return java.lang.String
    */
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public String dealMTLHistoryH(String workOrderNo,String lineCode, String mountType) throws Exception {
        String headId = Constant.STR_EMPTY;
        // 使用指令和上料历史类型查询头表
        SmtMachineMTLHistoryH record = new SmtMachineMTLHistoryH();
        record.setWorkOrder(workOrderNo);
        record.setMountType(mountType);
        SmtMachineMTLHistoryH result = smtMachineMTLHistoryHRepository.selectSmtMachineMTLHistoryH(record);
        // 双重校验锁
        if(result == null) {
            String redisKey = String.format(RedisKeyConstant.FEEDER_INSERTION_MTL_HISTORY_H, record.getWorkOrder(),
                    record.getMountType());
            RedisLock redisLock = new RedisLock(redisKey);
            if (!redisLock.lock()) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.FAILED_TO_GET_REDIS_LOCK);
            }
            try {
                // 再查一次数据
                result = smtMachineMTLHistoryHRepository.selectSmtMachineMTLHistoryH(record);
                if (result == null) {
                    SmtMachineMTLHistoryH entity = buildMTLHistoryH(workOrderNo, lineCode, mountType);
                    smtMachineMTLHistoryHRepository.insertSmtMachineMTLHistoryHSelective(entity);
                    headId = entity.getHeaderId();
                    return headId;
                }
            } finally {
                redisLock.unlock();
            }
        }
        return result.getHeaderId();
    }

    /**
    * 构造头表参数
    *@Author: 10307315陈俊熙
    *@date 2022/5/13 下午3:20
    *@param 
     * @param workOrderNo
     * @param lineCode
     * @param mountType
    *@return com.zte.domain.model.SmtMachineMTLHistoryH
    */
    private SmtMachineMTLHistoryH buildMTLHistoryH(String workOrderNo, String lineCode, String mountType) {
        SmtMachineMTLHistoryH mtlHistoryH = new SmtMachineMTLHistoryH();
        mtlHistoryH.setHeaderId(UUID.randomUUID().toString());
        mtlHistoryH.setLineCode(lineCode);
        mtlHistoryH.setWorkOrder(workOrderNo);
        mtlHistoryH.setMountType(mountType);
        mtlHistoryH.setPickStatus(Constant.STR_NUMBER_ONE);
        Map<String, String> headerMap = MESHttpHelper.getHttpRequestHeader();
        mtlHistoryH.setCreateUser(headerMap.get(Constant.X_EMP_NO_SMALL));
        mtlHistoryH.setEntityId(new BigDecimal(Constant.INT_2));
        // MESHttpHelper 会自动转为小写
        String factoryId = headerMap.get(SysConst.HTTP_HEADER_X_FACTORY_ID_LOW_CASE);
        mtlHistoryH.setFactoryId(new BigDecimal(factoryId));
        mtlHistoryH.setLastUpdatedBy(headerMap.get(Constant.X_EMP_NO_SMALL));
        mtlHistoryH.setEnabledFlag(Constant.FLAG_Y);
        return mtlHistoryH;
    }

    /**
    * reelId校验
    *@Author: 10307315陈俊熙
    *@date 2022/5/16 上午9:41
    *@param
     * @param reelId
     * @param workOrderNo
     * @param dto
    *@return void
    */
    private void valiedateReelId(String reelId, String workOrderNo, FeederInsertExtractionDTO dto) throws Exception {
        // 1、校验料盘在pk_code_info表是否存在
        PkCodeInfoDTO pkCodeInfoDTO = new PkCodeInfoDTO();
        pkCodeInfoDTO.setPkCode(reelId);
        List<PkCodeInfo> pkCodeList = pkCodeInfoRepository.getList(pkCodeInfoDTO);
        if (CollectionUtils.isEmpty(pkCodeList)) {
            throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE,MessageId.REEL_ID_NOT_REGISTER);
        }
        PkCodeInfo pkCode = pkCodeList.get(NumConstant.NUM_ZERO);
        // 2.ReelId批次与mouting/prepare表指令是否同批次。
        String productTask = pkCode.getProductTask();
        if (StringUtils.isEmpty(productTask)) {
            throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE,MessageId.REELID_PRODPLANID_IS_NULL);
        }
        if (StringUtils.isEmpty(workOrderNo) || workOrderNo.length() < Constant.INT_7) {
            throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE,MessageId.WORK_ORDER_NO_OF_PREPARE_ERROR);
        }
        if (!productTask.equals(workOrderNo.trim().substring(Constant.INT_0,Constant.INT_7))) {
            throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE,MessageId.PRODPLANID_OF_REELID_NOT_MATCH_PREPARE, new String[]{reelId});
        }
        // 3.avl校验
        PDATransferScanDTO pdaTransferScanDTO = new PDATransferScanDTO();
        Map<String, Object> map = new HashMap<>(Constant.INT_2);
        map.put(Constant.PRODPLAN_ID, productTask);
        List<PsTask> listPsTask = PlanscheduleRemoteService.getPsTask(map);
        if (CollectionUtils.isEmpty(listPsTask)) {
            throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE,MessageId.REELID_OF_PRODPLANID_IS_NULL, new String[]{productTask});
        }
        pdaTransferScanDTO.setBomNo(listPsTask.get(Constant.INT_0).getItemNo());
        pdaTransferScanDTO.setLineCode(dto.getLineCode());
        pdaTransferScanDTO.setDrLocationNo(dto.getLocationNo());
        pdaTransferScanDTO.setWorkOrder(workOrderNo);
        // 拼接feeder插入停机消息
        String[] params = new String[]{dto.getLineCode() + ":" + dto.getModuleNo() + "-" + dto.getLocationNo(),
                pkCode.getItemCode()};
        LocaleMessageSourceBean lmb = (LocaleMessageSourceBean) SpringContextUtil.getBean(MpConstant.RESOURCE_SERVICE_NAME);
        String remark = lmb.getMessage(MessageId.FEEDER_INSERT_AVL_ERROR, params);
        String returnMsg = imesPDACommonService.avlCheckEvent(pdaTransferScanDTO, pkCode, remark);
        if (StringHelper.isNotEmpty(returnMsg)) {
            throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, MessageId.FEEDER_INSERT_AVL_CHECK_FAILED, new String[]{returnMsg});
        }
    }

    /**
    * 处理为Y的数据
    *@Author: 10307315陈俊熙
    *@date 2022/5/16 上午9:41
    *@param
     * @param listMaterialMoutingY
     * @param dto
    *@return void
    */
    private void dealListMaterialMoutingY(List<SmtMachineMaterialMouting> listMaterialMoutingY,
                                          FeederInsertExtractionDTO dto) throws Exception {
        if (CollectionUtils.isEmpty(listMaterialMoutingY)) {
            return;
        }
        for(SmtMachineMaterialMouting entity: listMaterialMoutingY) {
            // 非传入的feeder才做拔出操作
            if (!dto.getFeederNo().equals(entity.getFeederNo())) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, LOCATION_HAS_MT);
            }
        }
    }

    /**
    * 判断是否是临时插拔
    *@Author: 10307315陈俊熙
    *@date 2022/5/16 上午9:42
    *@param
     * @param matMoutingY
     * @param matMoutingN
     * @param dto
    *@return int
    */
    private int confirmTemporaryExtraction(SmtMachineMaterialMouting matMoutingY, SmtMachineMaterialMouting matMoutingN,
                                           FeederInsertExtractionDTO dto) throws Exception {
        // 如果存在为Y的最新数据是和传入feeder相同，判定是快速拔插，拔出为正确调用接口
        if (matMoutingY != null && dto.getFeederNo().equals(matMoutingY.getFeederNo())) {
            return Constant.INT_2;
        }
        if (matMoutingN == null || !dto.getFeederNo().equals(matMoutingN.getFeederNo())) {
            throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE,
                    MessageId.PREPARE_INFO_OF_FEEDER_NOT_EXIST, new String[]{dto.getFeederNo()});
        }
        // 为N的最新数据和传入feeder相同，则为快速拔插。
        return Constant.INT_1;
    }

    /**
    * 判断是否是更换feeder
    *@Author: 10307315陈俊熙
    *@date 2022/5/16 上午9:42
    *@param
     * @param matMoutingList
     * @param matPrepareList
    *@return int
    */
    private int confirmTemChangeFeeder(List<SmtMachineMaterialMouting> matMoutingList,
                                       List<SmtMachineMaterialPrepare> matPrepareList) {
        // 根据最后更新时间降序排序，取出最新的数据
        SmtMachineMaterialMouting matMouting = matMoutingList.stream().max(Comparator.comparing
                (SmtMachineMaterialMouting::getLastUpdatedDate)).get();
        SmtMachineMaterialPrepare prepare = matPrepareList.get(Constant.INT_0);
        // 机台在用最新数据是否和和提前备料记录中指令、模组、站位、机台、reelId一致
        boolean notChangeFeederFlag = StringUtils.isEmpty(matMouting.getWorkOrder())
                || StringUtils.isEmpty(matMouting.getModuleNo()) || StringUtils.isEmpty(matMouting.getLocationNo())
                || StringUtils.isEmpty(matMouting.getMachineNo()) || StringUtils.isEmpty(matMouting.getObjectId())
                || !matMouting.getWorkOrder().equals(prepare.getWorkOrder())
                || !matMouting.getModuleNo().equals(prepare.getModuleNo())
                || !matMouting.getLocationNo().equals(prepare.getLocationNo())
                || !matMouting.getMachineNo().equals(prepare.getMachineNo())
                || !matMouting.getObjectId().equals(prepare.getObjectId());
        if (notChangeFeederFlag) {
            // 如果指令、模组、站位、机台、reelId有一个不同，就为正常情况0场景
            return Constant.INT_0;
        }
        // 3表示临时更换feeder，(提前备料有数据，根据参数查询的机台在用最新数据是否和和提前备料记录中指令、模组、站位、机台、reelId一致的)
        return Constant.INT_3;
    }

    /**
    * 校验线体是否支持智能feeder
    *@Author: 10307315陈俊熙
    *@date 2022/5/16 上午9:43
    *@param
     * @param dto
    *@return void
    */
    private void validateIntelligentFeederFlag(FeederInsertExtractionDTO dto) throws Exception {
        CFLine cfLineEntity = BasicsettingRemoteService.getLineByName(dto.getLineName());
        if (cfLineEntity == null) {
            throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, MessageId.LINE_NAME_NOT_EXISTING);
        }
        if (cfLineEntity == null || !Constant.FLAG_Y.equals(cfLineEntity.getIntelligentFeederFlag())) {
            throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, MessageId.LINE_NOT_SUPPORT_INTELLIGENT_FEEDER);
        }
        dto.setLineCode(cfLineEntity.getLineCode());
    }

    /**
    * 插入拔出的校验参数必填
    *@Author: 10307315陈俊熙
    *@date 2022/5/13 下午3:01
    *@param dto
    *@return void
    */
    private void validateFeederInseAndExtrDTO(FeederInsertExtractionDTO dto) throws Exception {
        if (StringUtils.isEmpty(dto.getLineName())) {
            throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, MessageId.LINE_NAME_NOT_NULL);
        }
        if (StringUtils.isEmpty(dto.getFeederNo())) {
            throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, MessageId.FEEDER_NO_IS_NULL);
        }
        if (StringUtils.isEmpty(dto.getModuleNo())) {
            throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, MessageId.MODULE_NO_EMPTY);
        }
        if (StringUtils.isEmpty(dto.getLocationNo())) {
            throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, MessageId.LOCATION_NO_EMPTY);
        }
    }

    /**
    * feeder插入失败时记录错误的原因写入上料历史
    *@Author: 10307315陈俊熙
    *@date 2022/8/25 下午3:14
    *@param
     * @param dto
     * @param opsResult
    *@return void
    */
    public void handleFeederInsertErrorInfo(FeederInsertExtractionDTO dto, String opsResult) throws Exception {
        CFLine cfLineEntity = BasicsettingRemoteService.getLineByName(dto.getLineName());
        if (cfLineEntity == null || !Constant.FLAG_Y.equals(cfLineEntity.getIntelligentFeederFlag())) {
            return;
        }
        dto.setLineCode(cfLineEntity.getLineCode());
        // 获取指令，和BA对过，直接取mouting最新时效数据
        // 根据线体、模组、站位查询机台在用
        SmtMachineMaterialMouting smtMounting = new SmtMachineMaterialMouting();
        smtMounting.setModuleNo(dto.getModuleNo());
        smtMounting.setLocationNo(dto.getLocationNo());
        smtMounting.setLineCode(dto.getLineCode());
        List<SmtMachineMaterialMouting> matMountingList =
                smtMachineMaterialMoutingService.getAllByParamsOfFeederInsertion(smtMounting);
        if (CollectionUtils.isEmpty(matMountingList)) {
            return;
        }
        Optional<SmtMachineMaterialMouting> matMountingListN =  matMountingList.stream().filter(e -> Constant.FLAG_N.equals
                (e.getEnabledFlag())).max(Comparator.comparing
                (SmtMachineMaterialMouting::getLastUpdatedDate));
        SmtMachineMaterialMouting smtMachineMaterialMouting = null;
        if (matMountingListN != null && matMountingListN.isPresent()) {
            smtMachineMaterialMouting = matMountingListN.get();
        }
        if (smtMachineMaterialMouting == null) {
            return;
        }
        String workOrderNo = smtMachineMaterialMouting.getWorkOrder();
        // 写上料历史；处理头表数据
        String headId = smtMachineMTLHistoryLService.dealMTLHistoryH(workOrderNo, dto.getLineCode(), Constant.MOUNT_TYPE_FEEDER_INSERT);
        // 处理 行数据
        insertMTLHistoryLErrorInfo(headId, smtMachineMaterialMouting, Constant.MOUNT_TYPE_FEEDER_INSERT, opsResult);
    }

    @Override
    public void finishMaterialHistoryByWo(String workOrderNo) {
        smtMachineMTLHistoryHRepository.finishMaterialHistoryByWo(workOrderNo);
    }

    private void insertMTLHistoryLErrorInfo(String headId, SmtMachineMaterialMouting smtMachineMaterialMouting,
                                            String mountTypeFeederInsert, String opsResult) {
        SmtMachineMTLHistoryL mtlHistoryErrorInfo = new SmtMachineMTLHistoryL();
        mtlHistoryErrorInfo.setHeaderId(headId);
        mtlHistoryErrorInfo.setWorkOrder(smtMachineMaterialMouting.getWorkOrder());
        mtlHistoryErrorInfo.setLineCode(smtMachineMaterialMouting.getLineCode());
        mtlHistoryErrorInfo.setItemCode(smtMachineMaterialMouting.getItemCode());
        mtlHistoryErrorInfo.setItemName(smtMachineMaterialMouting.getItemName());
        mtlHistoryErrorInfo.setLocationNo(smtMachineMaterialMouting.getLocationNo());
        mtlHistoryErrorInfo.setObjectId(smtMachineMaterialMouting.getObjectId());
        mtlHistoryErrorInfo.setQty(smtMachineMaterialMouting.getQty());
        mtlHistoryErrorInfo.setEnabledFlag(Constant.FLAG_Y);
        mtlHistoryErrorInfo.setMountType(mountTypeFeederInsert);
        mtlHistoryErrorInfo.setLineId(UUID.randomUUID().toString());
        mtlHistoryErrorInfo.setMachineNo(smtMachineMaterialMouting.getMachineNo());
        mtlHistoryErrorInfo.setSourceBatchCode(smtMachineMaterialMouting.getSourceBatchCode());
        mtlHistoryErrorInfo.setModuleNo(smtMachineMaterialMouting.getModuleNo());
        mtlHistoryErrorInfo.setFeederNo(smtMachineMaterialMouting.getFeederNo());
        Map<String, String> headerMap = MESHttpHelper.getHttpRequestHeader();
        mtlHistoryErrorInfo.setCreateUser(headerMap.get(Constant.X_EMP_NO_SMALL));
        mtlHistoryErrorInfo.setEntityId(new BigDecimal(Constant.INT_2));
        String factoryId = headerMap.get(SysConst.HTTP_HEADER_X_FACTORY_ID_LOW_CASE);
        mtlHistoryErrorInfo.setFactoryId(new BigDecimal(factoryId));
        mtlHistoryErrorInfo.setLastUpdatedBy(headerMap.get(Constant.X_EMP_NO_SMALL));
        mtlHistoryErrorInfo.setTracingQty(smtMachineMaterialMouting.getQty());
        mtlHistoryErrorInfo.setOperateMsg(opsResult);
        smtMachineMTLHistoryLRepository.insertSmtMachineMTLHistoryL(mtlHistoryErrorInfo);
    }

    public void addItemModelInfo(List<SmtMachineMTLHistoryL> itemList) {
        if (CollectionUtils.isEmpty(itemList)) {
            return;
        }
        this.addItemModel(itemList);
    }


    @Override
    public List<SmtMachineMTLHistoryL> lowLevelGetSmtMachineL(SmtMachineMTLHistoryL record){
        return smtMachineMTLHistoryLRepository.lowLevelGetSmtMachineL(record);
    }
}
