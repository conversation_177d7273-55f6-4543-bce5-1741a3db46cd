/**
 * 项目名称 : ${project_name}
 * 创建日期 : ${date}
 * 修改历史 :
 * 1. [${date}] 创建文件 by ${user}
 **/
package com.zte.interfaces;

import com.alibaba.druid.util.StringUtils;
import com.zte.application.WipDailyReportInfoService;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.ExcelName;
import com.zte.common.utils.MpConstant;
import com.zte.domain.model.WipDailyReportInfo;
import com.zte.interfaces.assembler.WipDailyReportInfoAssembler;
import com.zte.interfaces.dto.WipDailyReportInfoDTO;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.common.annotation.Export;
import com.zte.springbootframe.common.annotation.TransmittableHeader;
import com.zte.springbootframe.common.consts.SysConst;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.util.RequestHeadValidationUtil;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.springbootframe.util.ThreadUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Callable;

/**
 * // TODO 添加类/接口功能描述
 *
 * <AUTHOR>
 **/
@RestController
@RequestMapping("/wipDailyReport")
@Api(description = "高级日报api")
public class WipDailyReportInfoController {
    @Autowired
    private WipDailyReportInfoService wipDailyReportInfoService;
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    /**
     * // TODO 添加方法功能描述
     *
     * @param request
     * @param dto
     * @return ServiceData
     **/
    @RequestMapping(value = "/wipDailyReportInfo", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ServiceData insert(HttpServletRequest request, @RequestBody WipDailyReportInfoDTO dto) {
        ServiceData ret = new ServiceData();
        WipDailyReportInfo entity = WipDailyReportInfoAssembler.toEntity(dto);
        wipDailyReportInfoService.insertWipDailyReportInfo(entity);
        return ret;
    }

    /**
     * // TODO 添加方法功能描述
     *
     * @param request
     * @param dto
     * @return ServiceData
     **/
    @RequestMapping(value = "/wipDailyReportInfo", method = RequestMethod.PUT, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ServiceData update(HttpServletRequest request, @RequestBody WipDailyReportInfoDTO dto) {
        ServiceData ret = new ServiceData();
        WipDailyReportInfo entity = WipDailyReportInfoAssembler.toEntity(dto);

        wipDailyReportInfoService.updateWipDailyReportInfoById(entity);
        return ret;
    }

    @ApiOperation("高级日报统计数据对应清单")
    @RequestMapping(value = "/detailList", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Callable<ServiceData> detailList(HttpServletRequest request, WipDailyReportInfoDTO record) throws Exception {
        //System.out.println("主线程开始");
        Callable<ServiceData> ret = new Callable<ServiceData>() {
            @Override
            public ServiceData call() throws Exception {
                //System.out.println("副线程开始");
                ServiceData ret = new ServiceData();

                String factoryId = request.getHeader(SysConst.HTTP_HEADER_X_FACTORY_ID);
                if (StringUtils.isEmpty(factoryId)) {
                    ret.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, RetCode.BUSINESSERROR_MSGID));
                    ret.setBo(CommonUtils.getLmbMessage(MessageId.FACTORY_ID_IS_NULL));
                    return ret;
                }

                record.setFactoryId(new BigDecimal(factoryId));
                Page<WipDailyReportInfoDTO> pageRow = wipDailyReportInfoService.detailList(record);
                ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
                ret.setBo(pageRow);
                //System.out.println("副线程结束");
                return ret;
            }
        };

        return ret;
    }

    /**
     * //定时任务统计
     *
     * @param request
     * @return ServiceData
     **/
    @ApiOperation("高级日报定时任务统计")
    @TransmittableHeader
    @RequestMapping(value = "/timeStatistics", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ServiceData timeStatistics(HttpServletRequest request, @RequestParam(name = "asyncJob", required = false) String isAsync) throws Exception {
        ServiceData ret = new ServiceData();
        String factoryId = request.getParameter(SysConst.HTTP_HEADER_X_FACTORY_ID);
        if (StringUtils.isEmpty(factoryId)) {
            ret.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, RetCode.BUSINESSERROR_MSGID));
            ret.setBo(CommonUtils.getLmbMessage(MessageId.FACTORY_ID_IS_NULL));
            return ret;
        }
        WipDailyReportInfoDTO record = new WipDailyReportInfoDTO();
        record.setFactoryId(new BigDecimal(factoryId));
        if (!StringUtils.equals(isAsync, Constant.FLAG_N)) {
            ThreadUtil.EXECUTOR.execute(() -> {
                try {
                    wipDailyReportInfoService.selectGroupByWd(record);
                } catch (Exception e) {
                    logger.error("高级日报定时任务统计：" + e.getMessage());
                }
            });
        } else {
            List<WipDailyReportInfoDTO> list = wipDailyReportInfoService.selectGroupByWd(record);
            ret.setBo(list);
        }
        return ret;
    }

    /**
     * 实时查询统计
     * @return
     * @throws Exception
     */
    @ApiOperation("高级日报查询统计")
    @RequestMapping(value = "/getListByCondition", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ServiceData getListByCondition(WipDailyReportInfoDTO record) throws Exception{
        RequestHeadValidationUtil.validaFactoryId();
        return ServiceDataBuilderUtil.success(wipDailyReportInfoService.getListByCondition(record));
    }

    /**
     * 导出EXCEL
     * @param request
     * @return
     * @throws Exception
     */
    @ApiOperation("导出EXCEL")
    @Export
    @RequestMapping(value = "/exportModel", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ServiceData<?> exportModel(HttpServletRequest request, HttpServletResponse response, WipDailyReportInfoDTO record) throws Exception {
        record.setAttribute1(Constant.EXPORT_TAG);
        Page<WipDailyReportInfoDTO> pageRow = wipDailyReportInfoService.prepareExportData(record);
        SimpleDateFormat sdf = new SimpleDateFormat(MpConstant.DATE_FORMAT_YYYYMMDD);
        String fileName = sdf.format(new Date()) + ExcelName.WIPDAILYREPORTINFO_EXPORTMODEL_FILENAME;
        wipDailyReportInfoService.exportModel(fileName, record.getTitle(), pageRow.getRows(), record.getProps());
        return ServiceDataBuilderUtil.success();
    }

    /**
     * 导出详情EXCEL
     * @return
     * @throws Exception
     */
    @ApiOperation("导出详情EXCEL")
    @Export
    @RequestMapping(value = "/exportModelDetail", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ServiceData exportModelDetail(HttpServletResponse response, WipDailyReportInfoDTO record) throws Exception {
        record.setAttribute1(Constant.EXPORT_TAG);
        Page<WipDailyReportInfoDTO> pageRow = wipDailyReportInfoService.detailList(record);
        SimpleDateFormat sdf = new SimpleDateFormat(MpConstant.DATE_FORMAT_YYYYMMDDHHMMSS);
        String fileName = sdf.format(new Date()) + ExcelName.WIPDAILYREPORTINFO_EXPORTMODELDETAIL_FILENAME;
        if (null != pageRow) {
            wipDailyReportInfoService.exportModel(fileName, record.getTitle(), pageRow.getRows(), record.getProps(), response);
        }
        return ServiceDataBuilderUtil.success();
    }

    /**
     * 实时查询统计
     * @param record
     * @return
     * @throws Exception
     */
    @ApiOperation("标模高级日报查询统计")
    @RequestMapping(value = "/getBuildListByCond", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ServiceData<?> getBuildListByCond(WipDailyReportInfoDTO record) throws Exception {
        record.setFactoryId(new BigDecimal(RequestHeadValidationUtil.validaFactoryId()));
        return ServiceDataBuilderUtil.success(wipDailyReportInfoService.getBuildListByCond(record));
    }

    /**
     * 导出EXCEL
     * @param request
     * @param record
     * @return
     * @throws Exception
     */
    @ApiOperation("导出标模EXCEL")
    @Export
    @RequestMapping(value = "/exportBuildModel", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ServiceData exportBuildModel(HttpServletRequest request, HttpServletResponse response, WipDailyReportInfoDTO record) throws Exception {
        record.setAttribute1(Constant.EXPORT_TAG);
        //不分页，全额导出
        record.setPage(1);
        record.setRows(10000);
        Page<WipDailyReportInfoDTO> pageRow = wipDailyReportInfoService.getBuildListByCond(record);

        SimpleDateFormat sdf = new SimpleDateFormat(MpConstant.DATE_FORMAT_YYYYMMDDHHMMSS);
        String fileName = sdf.format(new Date()) + ExcelName.WIPDAILYREPORTINFO_EXPORTBUILDMODEL_FILENAME;
        wipDailyReportInfoService.exportModel(fileName, record.getTitle(), pageRow.getRows(), record.getProps(), response);
        return null;
    }
}