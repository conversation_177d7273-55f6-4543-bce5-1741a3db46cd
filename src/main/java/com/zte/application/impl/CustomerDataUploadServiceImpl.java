/**
 * 项目名称 : OEM产品发货信息查询分析
 * 创建日期 : 2018-10-26
 * 修改历史 :
 * 1. [${date}] 创建文件 by 6055000032
 **/

package com.zte.application.impl;

import com.alibaba.fastjson.JSON;
import com.zte.application.CustomerDataUploadService;
import com.zte.application.PsWipInfoService;
import com.zte.application.WipExtendIdentificationService;
import com.zte.application.WipScanHistoryService;
import com.zte.common.CommonUtils;
import com.zte.common.DateUtil;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.BSProcess;
import com.zte.domain.model.BsItemInfo;
import com.zte.domain.model.PmRepairRcvDetailRepository;
import com.zte.domain.model.PmRepairRcvRepository;
import com.zte.domain.model.PsWipInfo;
import com.zte.domain.model.PsWipInfoRepository;
import com.zte.domain.model.PsWorkOrderBasic;
import com.zte.domain.model.WipExtendIdentification;
import com.zte.domain.model.WipScanHistory;
import com.zte.domain.model.WipScanHistoryRepository;
import com.zte.infrastructure.remote.BarcodeCenterRemoteService;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.CrafttechRemoteService;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.infrastructure.remote.DigitalPlatformRemoteService;
import com.zte.infrastructure.remote.ICenterRemoteService;
import com.zte.infrastructure.remote.MdsRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.BarcodeExpandDTO;
import com.zte.interfaces.dto.BarcodeExpandQueryDTO;
import com.zte.interfaces.dto.BarcodePriceApiDTO;
import com.zte.interfaces.dto.CompleteMachineHeadDTO;
import com.zte.interfaces.dto.CtBasicRouteDTO;
import com.zte.interfaces.dto.CustomerDataLogDTO;
import com.zte.interfaces.dto.CustomerDataUploadDTO;
import com.zte.interfaces.dto.CustomerDataUploadParamDTO;
import com.zte.interfaces.dto.CustomerItemsDTO;
import com.zte.interfaces.dto.CustomerTestDataUploadDTO;
import com.zte.interfaces.dto.MaterialsDTO;
import com.zte.interfaces.dto.PmRepairDetailDTO;
import com.zte.interfaces.dto.PsTaskDTO;
import com.zte.interfaces.dto.PushBoardDataProcessDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.interfaces.dto.SysLookupValuesDTO;
import com.zte.interfaces.dto.alibaba.BoardRecordDTO;
import com.zte.interfaces.dto.alibaba.ProblemRequestDTO;
import com.zte.interfaces.dto.mds.MdsWriteVersionInformationDTO;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.annotation.AlarmAnnotation;
import com.zte.springbootframe.common.annotation.RecordLogAnnotation;
import com.zte.springbootframe.common.annotation.RedisDistributedLockAnnotation;
import com.zte.springbootframe.common.annotation.RedisLockParamAnnotation;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.springbootframe.util.IdGenerator;
import com.zte.springbootframe.util.RequestHeadValidationUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.TimeZone;
import java.util.TreeSet;
import java.util.UUID;
import java.util.stream.Collectors;

import static com.zte.common.utils.Constant.COMMA;
import static com.zte.common.utils.Constant.CRAFT_SECTION;
import static com.zte.common.utils.Constant.SN_CODE;
import static com.zte.common.utils.NumConstant.NUM_TWELVE;

/**
 * OEM产品发货信息
 *
 * <AUTHOR>
 **/
@Service
public class CustomerDataUploadServiceImpl implements CustomerDataUploadService {

    private static final Logger log = LoggerFactory.getLogger(CustomerDataUploadServiceImpl.class);
    @Autowired
    private MdsRemoteService mdsRemoteService;
    @Autowired
    private DigitalPlatformRemoteService digitalPlatformRemoteService;
    @Autowired
    private PsWipInfoService wipInfoService;
    @Autowired
    private WipExtendIdentificationService wipExtendIdentificationService;
    @Autowired
    private WipScanHistoryService wipScanHistoryService;
    @Autowired
    private CenterfactoryRemoteService centerfactoryRemoteService;
    @Autowired
    private BarcodeCenterRemoteService barcodeCenterRemoteService;
    @Autowired
    private DatawbRemoteService datawbRemoteService;
    @Autowired
    private WipScanHistoryRepository wipScanHistoryRepository;
    @Autowired
    private PmRepairRcvDetailRepository pmRepairRcvDetailRepository;
    @Autowired
    private PmRepairRcvRepository pmRepairRcvRepository;
    @Autowired
    private PsWipInfoRepository psWipInfoRepository;
    @Value("#{${station.name.map:{'ICT':'ICT','Test':'FCT'}}}")
    private Map<String,String> stationNameMap;
    @Value("${alibaba.manufacturer.name:ZTE101}")
    private String alibabaManufacturerName;
    @Autowired
    private IdGenerator idGenerator;
    @Autowired
    private ICenterRemoteService iCenterRemoteService;
    @Value("${call.external.interface.icenter.mailer:0668000851}")
    private String centerReceiver;

    /**
     * 单板信息回传
     *
     * @param dto
     * @throws Exception
     */
    @Override
    public void customerDataFeedback(CustomerDataUploadParamDTO dto) throws Exception {
        if (StringUtils.equals(Constant.STR_0, dto.getSchedulingType())) {
            //手动推送
            manualPush(dto);
            return;
        } else if (StringUtils.equals(Constant.STR_2, dto.getSchedulingType())) {
            //MES调度
            List<CustomerDataUploadDTO> customerDataUploadDTOList = dto.getCustomerDataUploadDTOList();
            if (CollectionUtils.isEmpty(customerDataUploadDTOList)) {
                return;
            }
            List<String> boardSnList = customerDataUploadDTOList.stream().filter(e->StringUtils.isNotEmpty(e.getBoardSn())).map(e -> e.getBoardSn()).distinct().collect(Collectors.toList());
            dto.setBoardSnList(boardSnList);
            assembleAndReturnInformationBasedOnBarcode(dto);
            return;
        }

        //处理历史失败数据
        processingHistoricalFailureData(dto);

        //定时任务处理
        Date nowDate = new Date();
        //定时任务调度获取上次执行最后条码时间
        SysLookupTypesDTO sysLookupTypesDTO = BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_6732, Constant.LOOKUP_TYPE_6732006);
        if (sysLookupTypesDTO == null) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SYS_LOOK_NOT_CONFIG, new Object[]{Constant.LOOKUP_TYPE_6732006});
        }
        String lastExecutionTime = sysLookupTypesDTO.getLookupMeaning();
        //上次处理时间
        Date lastExecDate = StringUtils.isEmpty(lastExecutionTime) ? null : DateUtils.parseDate(lastExecutionTime, Constant.DATE_TIME_FORMATE_FULL);
        customerDataFeedbackForImes(dto, lastExecDate);
        //最后更新执行时间
        BasicsettingRemoteService.updateSysLookupValuesMeaning(new BigDecimal(Constant.LOOKUP_TYPE_6732006), this.getSimpleDateFormat().format(nowDate));
    }

    /**
     * 处理历史失败数据
     * @throws Exception
     */
    private void processingHistoricalFailureData(CustomerDataUploadParamDTO dto) throws Exception {
        int current = NumConstant.NUM_ONE;
        CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
        customerDataLogDTO.setFactoryId(dto.getFactoryId());
        customerDataLogDTO.setMessageType(Constant.MESSAGE_TYPE_SINGLE_BOARD_FEEDBACK);
        customerDataLogDTO.setStartDate(DateUtil.addDay(new Date(),NumConstant.NUM_NEGATIVE_ONE));
        customerDataLogDTO.setEndDate(new Date());
        customerDataLogDTO.setCustomerName(dto.getCustomerName());
        while (current <= NumConstant.NUM_1000) {
            //历史错误数据
            List<CustomerDataLogDTO> customerDataLogDTOList = centerfactoryRemoteService.getPushErrorData(customerDataLogDTO);
            if (CollectionUtils.isEmpty(customerDataLogDTOList)) {
               return;
            }
            customerDataLogDTO.setId(customerDataLogDTOList.get(customerDataLogDTOList.size()-NumConstant.NUM_ONE).getId());
            CustomerDataUploadParamDTO customerDataUploadParamDTO = new CustomerDataUploadParamDTO();
            customerDataUploadParamDTO.setCustomerDataLogDTOList(customerDataLogDTOList);
            customerDataUploadParamDTO.setFactoryId(dto.getFactoryId());
            customerDataUploadParamDTO.setEmpNo(dto.getEmpNo());
            customerDataUploadParamDTO.setCustomerName(dto.getCustomerName());
            manualPush(customerDataUploadParamDTO);
            current++;
        }
    }

    /**
     * 手动推送
     * @param dto
     * @throws Exception
     */
    private void manualPush(CustomerDataUploadParamDTO dto) throws Exception {
        List<CustomerDataLogDTO> customerDataLogDTOList = dto.getCustomerDataLogDTOList();
        if (CollectionUtils.isEmpty(customerDataLogDTOList)) {
            return;
        }
        List<String> boardSnList = customerDataLogDTOList.stream().map(e -> e.getSn()).distinct().collect(Collectors.toList());
        dto.setBoardSnList(boardSnList);
        assembleAndReturnInformationBasedOnBarcode(dto);
    }

    /**
     * imes定时任务调度
     *
     * @param dto
     * @param lastExecDate
     * @throws Exception
     */
    private void customerDataFeedbackForImes(CustomerDataUploadParamDTO dto, Date lastExecDate) throws Exception {
        //获取中心工厂类型为自研主板、自研子卡、自研背板的料单
        List<CustomerItemsDTO> customerItemsDTOList = centerfactoryRemoteService.getListByItemNoList(new ArrayList<>(),dto.getCustomerName());
        if (CollectionUtils.isEmpty(customerItemsDTOList)) {
            return;
        }
        //设置项目类型名称
        this.setProjectTypeName(customerItemsDTOList);
        this.setBoardTypeName(customerItemsDTOList);
        Map<String, CustomerItemsDTO> customerItemsDTOMap = customerItemsDTOList.stream().filter(e -> StringUtils.isNotEmpty(e.getZteCode())).collect(Collectors.toMap(CustomerItemsDTO::getZteCode, a -> a, (k1, k2) -> k1));
        if (customerItemsDTOMap.isEmpty()) {
            return;
        }
        List<String> productCodeList = new ArrayList<>(customerItemsDTOMap.keySet());
        //获取料单对应批次  需要确认是否需要分批  取中心工厂
        List<PsTaskDTO> psTaskList = centerfactoryRemoteService.getPsTaskByItemNoList(productCodeList);
        if (CollectionUtils.isEmpty(psTaskList)) {
            return;
        }
        //所有料单对应批次
        List<String> prodPlanIdList = psTaskList.stream().map(e -> e.getProdplanId()).distinct().collect(Collectors.toList());
        //先取最大时间
        Date maxDate = wipInfoService.getMaxSnDate(lastExecDate,prodPlanIdList);
        Map<String, PsTaskDTO> psTaskMap = psTaskList.stream().collect(Collectors.toMap(PsTaskDTO::getProdplanId, a -> a, (k1, k2) -> k1));
        lastExecDate = this.beforeLastExecuteDate(lastExecDate);
        //10个批次分批
        for (List<String> tempProdPlanIdList : CommonUtils.splitList(prodPlanIdList, NumConstant.NUM_TEN)) {
            //获取wip_info料单下对应条码的
            int current = NumConstant.NUM_ONE;
            boolean execFlag = true;
            while (execFlag) {
                List<PsWipInfo> psWipInfoList = wipInfoService.getWipInfoByProdPlanIdAndLastUpdateDate(tempProdPlanIdList, lastExecDate,maxDate, current);
                if (CollectionUtils.isEmpty(psWipInfoList)) {
                    execFlag = false;
                }else {
                    assemblyReturnData(dto, psTaskMap, tempProdPlanIdList, psWipInfoList, customerItemsDTOMap);
                    current++;
                }
            }
        }
    }

  /* Started by AICoder, pid:y3df5cbecf37ddb1406f0ad38081dc00fbb977c9 */
    private Date beforeLastExecuteDate(Date lastExecDate) {
        if (Objects.nonNull(lastExecDate)) {
            Calendar cal = Calendar.getInstance();
            cal.setTime(lastExecDate);
            cal.add(Calendar.MINUTE, -5);
            return cal.getTime();
        }
        return lastExecDate;
    }
    /* Ended by AICoder, pid:y3df5cbecf37ddb1406f0ad38081dc00fbb977c9 */

    /**
     * 设置项目类型名称
     *
     * @param customerItemsDTOList
     * @throws Exception
     */
    private void setProjectTypeName(List<CustomerItemsDTO> customerItemsDTOList) throws Exception {
        List<SysLookupTypesDTO> sysLookupTypesDTOList = BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_6817);
        Map<String, SysLookupTypesDTO> sysLookupTypesDTOMap = CollectionUtils.isEmpty(sysLookupTypesDTOList) ?
                new HashMap<>() : sysLookupTypesDTOList.stream().filter(e -> StringUtils.isNotEmpty(e.getLookupMeaning())).collect(Collectors.toMap(SysLookupTypesDTO::getLookupMeaning, a -> a, (k1, k2) -> k1));
        for (CustomerItemsDTO customerItemsDTO : customerItemsDTOList) {
            SysLookupTypesDTO sysLookupTypesDTO = sysLookupTypesDTOMap.get(customerItemsDTO.getProjType());
            customerItemsDTO.setProjectTypeName(customerItemsDTO.getProjType());
            if (sysLookupTypesDTO != null) {
                customerItemsDTO.setProjectTypeName(sysLookupTypesDTO.getDescriptionChinV());
            }
        }
    }

    /**
     * 设置项目类型名称
     *
     * @param customerItemsDTOList
     * @throws Exception
     */
    private void setBoardTypeName(List<CustomerItemsDTO> customerItemsDTOList) throws Exception {
        List<SysLookupTypesDTO> sysLookupTypesDTOList = BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_7307);
        Map<String, SysLookupTypesDTO> sysLookupTypesDTOMap = CollectionUtils.isEmpty(sysLookupTypesDTOList) ?
                new HashMap<>() : sysLookupTypesDTOList.stream().filter(e -> StringUtils.isNotEmpty(e.getLookupMeaning())).collect(Collectors.toMap(SysLookupTypesDTO::getLookupMeaning, a -> a, (k1, k2) -> k1));
        for (CustomerItemsDTO customerItemsDTO : customerItemsDTOList) {
            SysLookupTypesDTO sysLookupTypesDTO = sysLookupTypesDTOMap.get(customerItemsDTO.getBoardType());
            customerItemsDTO.setBoardTypeName(customerItemsDTO.getBoardType());
            if (sysLookupTypesDTO != null) {
                customerItemsDTO.setBoardTypeName(sysLookupTypesDTO.getDescriptionChinV());
            }
        }
    }

    //日期格式化
    private SimpleDateFormat getSimpleDateFormat() {
        SimpleDateFormat sdf = new SimpleDateFormat(Constant.DATE_TIME_FORMATE_FULL);
        TimeZone gmt = TimeZone.getTimeZone("GMT+8");
        sdf.setTimeZone(gmt);
        sdf.setLenient(true);
        return sdf;
    }

    /**
     * 根据条码组装客户回传信息
     *
     * @param dto
     * @throws Exception
     */
    public void assembleAndReturnInformationBasedOnBarcode(CustomerDataUploadParamDTO dto) throws Exception {
        List<String> boardSnList = dto.getBoardSnList();
        if (CollectionUtils.isEmpty(boardSnList)) {
            return;
        }
        List<PsWipInfo> psWipInfoList = wipInfoService.getListByBatchSn(boardSnList);
        if (CollectionUtils.isEmpty(psWipInfoList)) {
            return;
        }
        List<String> prodPlanIdList = psWipInfoList.stream().filter(e -> StringUtils.isNotEmpty(e.getAttribute1())).map(e -> e.getAttribute1()).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(prodPlanIdList)) {
            return;
        }
        List<PsTaskDTO> psTaskList = centerfactoryRemoteService.getPsTaskByProdplanIdList(prodPlanIdList);
        if (CollectionUtils.isEmpty(psTaskList)) {
            return;
        }
        Map<String, PsTaskDTO> psTaskMap = psTaskList.stream().collect(Collectors.toMap(PsTaskDTO::getProdplanId, a -> a, (k1, k2) -> k1));
        //根据料单获取基础信息
        List<String> itemNoList = psTaskList.stream().filter(e->StringUtils.isNotEmpty(e.getItemNo())).map(e -> e.getItemNo().substring(NumConstant.NUM_ZERO,NUM_TWELVE)).distinct().collect(Collectors.toList());
        List<CustomerItemsDTO> customerItemsDTOList = centerfactoryRemoteService.getListByItemNoList(itemNoList,dto.getCustomerName());
        if (CollectionUtils.isEmpty(customerItemsDTOList)) {
            return;
        }
        this.setProjectTypeName(customerItemsDTOList);
        this.setBoardTypeName(customerItemsDTOList);
        Map<String, CustomerItemsDTO> customerItemsDTOMap = customerItemsDTOList.stream().filter(e -> StringUtils.isNotEmpty(e.getZteCode())).collect(Collectors.toMap(CustomerItemsDTO::getZteCode, a -> a, (k1, k2) -> k1));
        if (customerItemsDTOMap.isEmpty()) {
            return;
        }

        //10个批次分批
        for (List<String> tempProdPlanIdList : CommonUtils.splitList(prodPlanIdList, NumConstant.NUM_TEN)) {
            assemblyReturnData(dto, psTaskMap, tempProdPlanIdList, psWipInfoList.stream().filter(e->tempProdPlanIdList.contains(e.getAttribute1())).collect(Collectors.toList()), customerItemsDTOMap);
        }

    }

    /**
     * 组装回传数据
     *
     * @param dto
     * @param psTaskMap
     * @param tempProdPlanIdList
     * @param psWipInfoList
     * @throws Exception
     */
    public void assemblyReturnData(CustomerDataUploadParamDTO dto, Map<String, PsTaskDTO> psTaskMap, List<String> tempProdPlanIdList,
                                    List<PsWipInfo> psWipInfoList, Map<String, CustomerItemsDTO> customerItemsDTOMap) throws Exception {
        if (CollectionUtils.isEmpty(psWipInfoList)) {
            return;
        }
        //调材料-计划接口获取物料信息
        List<BarcodePriceApiDTO> barcodePriceApiDTOList = digitalPlatformRemoteService.getMaterialInformation("",psWipInfoList.stream().map(e->e.getAttribute1()).distinct().collect(Collectors.toList()));
        Map<String, List<BarcodePriceApiDTO>> barcodeMap = barcodePriceApiDTOList.stream().collect(Collectors.groupingBy(BarcodePriceApiDTO::getProdPlanID));
        //获取物料条码对应条码中心扩展信息
        Map<String, BarcodeExpandDTO> barcodeExpandDTOMap = getBarcodeExpandDTOMap(barcodePriceApiDTOList);

        //调mds接口获取程序版本
        List<MdsWriteVersionInformationDTO> mdsWriteVersionInformationDTOList = mdsRemoteService.queryFirmwareVersionBatch(tempProdPlanIdList);
        Map<String, MdsWriteVersionInformationDTO> mdsMap = mdsWriteVersionInformationDTOList.stream().collect(Collectors.toMap(MdsWriteVersionInformationDTO::getBatchNo, a -> a, (k1, k2) -> k1));

        List<String> snList = psWipInfoList.stream().map(e -> e.getSn()).distinct().collect(Collectors.toList());
        //获取子条码信息
        Map<String, List<String>> wipMap = this.getWipMap(psWipInfoList, snList);
        //获取条码中心

        //入库的条码
        List<PsWipInfo> warehousingList = psWipInfoList.stream().filter(e -> StringUtils.equals(e.getCraftSection(), Constant.WAREHOUSE_ENTRY)).collect(Collectors.toList());
        List<String> warehouseSnList = CollectionUtils.isEmpty(warehousingList) ? new ArrayList<>() : warehousingList.stream().map(e -> e.getSn()).distinct().collect(Collectors.toList());
        List<WipScanHistory> wipScanHistoryList = new ArrayList<>();
        //入库扫描时间
        queryInventoryHistory(wipScanHistoryList, warehouseSnList);
        //手动推送需要查询MES系统合同号信息
        Map<String, CompleteMachineHeadDTO> completeMachineHeadDTOMap = getCompleteMachineHeadDTOMap(dto, warehousingList, warehouseSnList);
        Map<String, WipScanHistory> wipScanHistoryMap = wipScanHistoryList.stream().collect(Collectors.toMap(WipScanHistory::getSn, a -> a, (k1, k2) -> k1));

        List<CustomerDataUploadDTO> customerDataUploadDTOList = new ArrayList<>();
        List<CustomerDataUploadDTO> customerDataUploadDTOS = dto.getCustomerDataUploadDTOList();
        Map<String, CustomerDataUploadDTO> customerDataUploadDTOMap = CollectionUtils.isEmpty(customerDataUploadDTOS) ? new HashMap<>() : customerDataUploadDTOS.stream().collect(Collectors.toMap(CustomerDataUploadDTO::getBoardSn, a -> a, (k1, k2) -> k1));
        //手动推送
        List<CustomerDataLogDTO> customerDataLogDTOList = dto.getCustomerDataLogDTOList();
        Map<String, CustomerDataLogDTO> customerDataLogDTOMap = CollectionUtils.isEmpty(customerDataLogDTOList) ? new HashMap<>() : customerDataLogDTOList.stream().collect(Collectors.toMap(CustomerDataLogDTO::getSn, a -> a, (k1, k2) -> k1));
        List<CustomerDataLogDTO> dataList = new ArrayList<>();
        for (PsWipInfo psWipInfo : psWipInfoList) {
            CustomerDataUploadDTO customerDataUploadDTO = new CustomerDataUploadDTO();
            this.setWipInfo(wipMap, psWipInfo, customerDataUploadDTO);
            //基础信息
            String itemNo = psWipInfo.getItemNo();
            if(StringUtils.length(itemNo)> NUM_TWELVE){
                itemNo = itemNo.substring(NumConstant.NUM_ZERO,NUM_TWELVE);
            }
            CustomerItemsDTO customerItemsDTO = customerItemsDTOMap.get(itemNo);
            this.setCustomerItemsInfo(customerDataUploadDTO, customerItemsDTO);

            PsTaskDTO psTask = psTaskMap.get(psWipInfo.getAttribute1());
            this.setPsTaskInfo(customerDataUploadDTO, psTask);
            MdsWriteVersionInformationDTO mdsWriteVersionInformationDTO = mdsMap.get(psWipInfo.getAttribute1());
            if (mdsWriteVersionInformationDTO != null) {
                customerDataUploadDTO.setVersion(mdsWriteVersionInformationDTO.getSoftVersion());
            }
            //入库扫描信息
            WipScanHistory wipScanHistory = wipScanHistoryMap.get(psWipInfo.getSn());
            this.setInboundInformation(customerDataUploadDTO, wipScanHistory);

            //整机入库记账完成时间 取MES 如果是MES调度来的，会带这些信息,直接取就好
            this.setManufacturerTimeAndOrderTime(dto, customerDataUploadDTOMap, psWipInfo, customerDataUploadDTO,completeMachineHeadDTOMap);

            //物料信息
            List<BarcodePriceApiDTO> barcodePriceApiDTOS = barcodeMap.get(psWipInfo.getAttribute1());
            List<MaterialsDTO> materialsDTOList = this.getMaterialsDTOList(barcodePriceApiDTOS, barcodeExpandDTOMap);
            customerDataUploadDTO.setMaterials(materialsDTOList);
            customerDataUploadDTOList.add(customerDataUploadDTO);

            CustomerDataLogDTO pushCustomerDataLogDTO = this.generateCustomerDataLogDTO(dto, customerDataLogDTOMap, psWipInfo, customerDataUploadDTO, customerItemsDTO);
            setTaskInfo(psTask, pushCustomerDataLogDTO);
            dataList.add(pushCustomerDataLogDTO);
        }
        //调中心工厂通用接口推送B2B
        centerfactoryRemoteService.pushDataToB2B(dataList);
    }

    private Map<String, CompleteMachineHeadDTO> getCompleteMachineHeadDTOMap(CustomerDataUploadParamDTO dto, List<PsWipInfo> warehousingList, List<String> warehouseSnList) {
        Map<String, CompleteMachineHeadDTO> completeMachineHeadDTOMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(warehousingList) && StringUtils.equals(Constant.STR_0, dto.getSchedulingType())) {
            //查询合同信息以及入库记账完成时间
            List<CompleteMachineHeadDTO> completeMachineHeadDTOList = datawbRemoteService.getMainBoardInfo(warehouseSnList);
            completeMachineHeadDTOMap =  completeMachineHeadDTOList.stream().collect(Collectors.toMap(CompleteMachineHeadDTO::getSn, a -> a, (k1, k2) -> k1));
        }
        return completeMachineHeadDTOMap;
    }

    /**
     * 组装调中心工厂实体类
     * @param dto
     * @param customerDataLogDTOMap
     * @param psWipInfo
     * @param customerDataUploadDTO
     * @param customerItemsDTO
     * @return
     */
    private CustomerDataLogDTO generateCustomerDataLogDTO(CustomerDataUploadParamDTO dto, Map<String, CustomerDataLogDTO> customerDataLogDTOMap, PsWipInfo psWipInfo,
                                                          CustomerDataUploadDTO customerDataUploadDTO, CustomerItemsDTO customerItemsDTO) {
        CustomerDataLogDTO pushCustomerDataLogDTO = new CustomerDataLogDTO();
        //重推原始id
        CustomerDataLogDTO customerDataLogDTO = customerDataLogDTOMap.get(psWipInfo.getSn());
        if (customerDataLogDTO != null) {
            pushCustomerDataLogDTO.setTid(customerDataLogDTO.getId());
        }
        pushCustomerDataLogDTO.setId(UUID.randomUUID().toString());
        pushCustomerDataLogDTO.setOrigin(Constant.IMES);
        setBasicInformation(customerItemsDTO, pushCustomerDataLogDTO);
        pushCustomerDataLogDTO.setCraftSection(psWipInfo.getCraftSection());
        pushCustomerDataLogDTO.setSn(psWipInfo.getSn());
        pushCustomerDataLogDTO.setFactoryId(dto.getFactoryId());
        pushCustomerDataLogDTO.setMessageType(Constant.MESSAGE_TYPE_SINGLE_BOARD_FEEDBACK);
        pushCustomerDataLogDTO.setCreateBy(dto.getEmpNo());
        pushCustomerDataLogDTO.setLastUpdatedBy(dto.getEmpNo());
        pushCustomerDataLogDTO.setJsonData(JSON.toJSONString(customerDataUploadDTO));
        return pushCustomerDataLogDTO;
    }

    /**
     * 设置合同信息以及入库记账完成时间
     * @param dto
     * @param customerDataUploadDTOMap
     * @param psWipInfo
     * @param customerDataUploadDTO
     */
    public void setManufacturerTimeAndOrderTime(CustomerDataUploadParamDTO dto, Map<String, CustomerDataUploadDTO> customerDataUploadDTOMap, PsWipInfo psWipInfo,
                                CustomerDataUploadDTO customerDataUploadDTO,Map<String, CompleteMachineHeadDTO> completeMachineHeadDTOMap)
    {
        customerDataUploadDTO.setManufacturerTime(0L);
        customerDataUploadDTO.setOrderId(Constant.PRE_PRODUCTION_WITHOUT_PO);
        customerDataUploadDTO.setOrderTime(0L);
        if (StringUtils.equals(Constant.STR_2, dto.getSchedulingType())) {
            setManufacturerTimeAndOrderTime(customerDataUploadDTOMap, psWipInfo, customerDataUploadDTO);
        } else if (StringUtils.equals(Constant.STR_0, dto.getSchedulingType())) {
            setManufacturerTimeAndOrderTime(psWipInfo, customerDataUploadDTO, completeMachineHeadDTOMap);
        }
    }

    private void setManufacturerTimeAndOrderTime(PsWipInfo psWipInfo, CustomerDataUploadDTO customerDataUploadDTO, Map<String, CompleteMachineHeadDTO> completeMachineHeadDTOMap) {
        CompleteMachineHeadDTO completeMachineHeadDTO = completeMachineHeadDTOMap.get(psWipInfo.getSn());
        if (completeMachineHeadDTO != null) {
            Date manufacturerTime = completeMachineHeadDTO.getManufacturerTime();
            if(manufacturerTime != null) {
                customerDataUploadDTO.setManufacturerTime(manufacturerTime.getTime()/NumConstant.NUM_1000);
            }
            customerDataUploadDTO.setOrderId(completeMachineHeadDTO.getOrderId());
            Date orderTime = completeMachineHeadDTO.getOrderTime();
            if(orderTime != null) {
                customerDataUploadDTO.setOrderTime(orderTime.getTime()/NumConstant.NUM_1000);
            }
        }
    }

    /**
     * 设置条码信息
     * @param wipMap
     * @param psWipInfo
     * @param customerDataUploadDTO
     */
    private void setWipInfo(Map<String, List<String>> wipMap, PsWipInfo psWipInfo, CustomerDataUploadDTO customerDataUploadDTO) {
        customerDataUploadDTO.setBoardName(psWipInfo.getItemName());
        customerDataUploadDTO.setBoardPn(psWipInfo.getAttribute1());
        customerDataUploadDTO.setProductionBatch(psWipInfo.getAttribute1());
        customerDataUploadDTO.setBoardSn(psWipInfo.getSn());
        List<String> relateSnList = wipMap.get(psWipInfo.getSn());
        customerDataUploadDTO.setRelatedSn(relateSnList);
    }

    /**
     * 条码中心扩展信息
     * @param barcodePriceApiDTOList
     * @return
     * @throws Exception
     */
    private Map<String, BarcodeExpandDTO> getBarcodeExpandDTOMap(List<BarcodePriceApiDTO> barcodePriceApiDTOList) throws Exception {
        List<String> itemBarcodeList = barcodePriceApiDTOList.stream().filter(e -> StringUtils.isNotEmpty(e.getItemBarcode())).map(e -> e.getItemBarcode()).distinct().collect(Collectors.toList());
        BarcodeExpandQueryDTO barcodeExpandQueryDTO = new BarcodeExpandQueryDTO();
        barcodeExpandQueryDTO.setBarcodeList(itemBarcodeList);
        List<BarcodeExpandDTO> barcodeExpandDTOList = barcodeCenterRemoteService.expandQuery(barcodeExpandQueryDTO);
        Map<String, BarcodeExpandDTO> barcodeExpandDTOMap = barcodeExpandDTOList.stream().collect(Collectors.toMap(BarcodeExpandDTO::getBarcode, a -> a, (k1, k2) -> k1));
        return barcodeExpandDTOMap;
    }

    private void setTaskInfo(PsTaskDTO psTask, CustomerDataLogDTO pushCustomerDataLogDTO) {
        if (psTask != null) {
            pushCustomerDataLogDTO.setTaskNo(psTask.getTaskNo());
            pushCustomerDataLogDTO.setItemNo(psTask.getItemNo());
        }
    }

    /**
     * 设置客户基础信息
     *
     * @param customerItemsDTO
     * @param pushCustomerDataLogDTO
     */
    private void setBasicInformation(CustomerItemsDTO customerItemsDTO, CustomerDataLogDTO pushCustomerDataLogDTO) {
        if (customerItemsDTO != null) {
            pushCustomerDataLogDTO.setProjectName(customerItemsDTO.getProjectName());
            pushCustomerDataLogDTO.setCustomerName(customerItemsDTO.getCustomerName());
            pushCustomerDataLogDTO.setProjectPhase(customerItemsDTO.getProjectPhase());
            pushCustomerDataLogDTO.setCooperationMode(customerItemsDTO.getCooperationMode());
        }
    }

    /**
     * 入库信息
     *
     * @param customerDataUploadDTO
     * @param wipScanHistory
     */
    private void setInboundInformation(CustomerDataUploadDTO customerDataUploadDTO, WipScanHistory wipScanHistory) {
        if (wipScanHistory == null ) {
            return;
        }
        Date createDate = wipScanHistory.getCreateDate();
        if (createDate != null) {
            customerDataUploadDTO.setPackageTime(createDate.getTime()/NumConstant.NUM_1000);
            customerDataUploadDTO.setAcceptedTime(createDate.getTime()/NumConstant.NUM_1000);
        }
    }

    /**
     * 设置任务信息
     *
     * @param customerDataUploadDTO
     * @param psTask
     */
    private void setPsTaskInfo(CustomerDataUploadDTO customerDataUploadDTO, PsTaskDTO psTask) {
        if (psTask != null) {
            customerDataUploadDTO.setTicketId(psTask.getProdplanNo());
            Date releaseDate = psTask.getReleaseDate();
            customerDataUploadDTO.setTicketCreateTime(releaseDate == null ? 0L : releaseDate.getTime()/NumConstant.NUM_1000);
            customerDataUploadDTO.setFactoryLocation(Constant.ZTE+psTask.getFactoryName());
            customerDataUploadDTO.setShippingAddress(psTask.getFactoryName());
        }
    }

    /**
     * 设置基础信息
     *
     * @param customerDataUploadDTO
     * @param customerItemsDTO
     */
    private void setCustomerItemsInfo(CustomerDataUploadDTO customerDataUploadDTO, CustomerItemsDTO customerItemsDTO) {
        if (customerItemsDTO != null) {
            customerDataUploadDTO.setProjectName(customerItemsDTO.getProjectName());
            customerDataUploadDTO.setProjectType(customerItemsDTO.getProjectTypeName());
            customerDataUploadDTO.setProjectStage(customerItemsDTO.getProjectPhase());
            customerDataUploadDTO.setBoardPnType(customerItemsDTO.getBoardTypeName());
        }
    }

    /**
     * 入库扫描时间
     *
     * @param wipScanHistoryList
     * @param warehouseSnList
     * @throws Exception
     */
    private void queryInventoryHistory(List<WipScanHistory> wipScanHistoryList, List<String> warehouseSnList) throws Exception {
        if (!CollectionUtils.isEmpty(warehouseSnList)) {
            wipScanHistoryList.addAll(wipScanHistoryService.getTheEarliestInboundScanningTime(warehouseSnList));
        }
    }

    /**
     * 批量获取子条码
     *
     * @param psWipInfoList
     * @param snList
     * @return
     * @throws Exception
     */
    public Map<String, List<String>> getWipMap(List<PsWipInfo> psWipInfoList, List<String> snList) throws Exception {
        List<WipExtendIdentification> wipExtendIdentificationList = wipExtendIdentificationService.getAllChildSn(snList);
        Map<String, List<String>> wipMap = new HashMap<>();
        if(CollectionUtils.isEmpty(wipExtendIdentificationList)){
            return wipMap;
        }
        List<String> itemNoList = wipExtendIdentificationList.stream().filter(e->StringUtils.isNotEmpty(e.getItemNo())).map(e->e.getItemNo()).distinct().collect(Collectors.toList());
        if(CollectionUtils.isEmpty(itemNoList)){
            return wipMap;
        }
        List<BsItemInfo> bsItemInfoList = BasicsettingRemoteService.getItemType(itemNoList);
        if(CollectionUtils.isEmpty(bsItemInfoList)){
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,MessageId.ITEM_INFO_FIND_ERROR);
        }
        //过滤出半成品的物料代码
        List<String> semiFinishedProductItemNoList = bsItemInfoList.stream().filter(e->StringUtils.equals(Constant.STR_NUMBER_ONE,e.getItemType())).map(e->e.getItemNo()).collect(Collectors.toList());
        for (PsWipInfo psWipInfo : psWipInfoList) {
            List<String> tempList = wipExtendIdentificationList.stream().filter(e -> this.isBarcodeSemiFinishedProducts(semiFinishedProductItemNoList, psWipInfo.getSn(), e)).map(e -> e.getSn()).distinct().collect(Collectors.toList());
            wipMap.put(psWipInfo.getSn(), tempList);
        }
        return wipMap;
    }

    /**
     * 判断是当前条码的，并且为半成品
     */
    private boolean isBarcodeSemiFinishedProducts(List<String> semiFinishedProductItemNoList, String sn, WipExtendIdentification e) {
        return semiFinishedProductItemNoList.contains(e.getItemNo()) && StringUtils.endsWith(e.getBloodKinship(), sn);
    }

    /**
     * 设置MES相关合同信息
     *
     * @param customerDataUploadDTOMap
     * @param psWipInfo
     * @param customerDataUploadDTO
     */
    private void setManufacturerTimeAndOrderTime(Map<String, CustomerDataUploadDTO> customerDataUploadDTOMap, PsWipInfo psWipInfo, CustomerDataUploadDTO customerDataUploadDTO) {
        CustomerDataUploadDTO customerDTO = customerDataUploadDTOMap.get(psWipInfo.getSn());
        if (customerDTO != null) {
            customerDataUploadDTO.setManufacturerTime(customerDTO.getManufacturerTime());
            customerDataUploadDTO.setOrderId(customerDTO.getOrderId());
            customerDataUploadDTO.setOrderTime(customerDTO.getOrderTime());
        }
    }

    /**
     * 组装物料信息
     *
     * @param barcodePriceApiDTOS
     * @param barcodeExpandDTOMap
     * @return
     */
    public List<MaterialsDTO> getMaterialsDTOList(List<BarcodePriceApiDTO> barcodePriceApiDTOS, Map<String, BarcodeExpandDTO> barcodeExpandDTOMap) {
        List<MaterialsDTO> materialsDTOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(barcodePriceApiDTOS)) {
            return materialsDTOList;
        }
        for (BarcodePriceApiDTO barcodePriceApiDTO : barcodePriceApiDTOS) {
            //已发数量小于等于0的忽略
            if(StringUtils.isEmpty(barcodePriceApiDTO.getQty()) || new BigDecimal(barcodePriceApiDTO.getQty()).compareTo(BigDecimal.ZERO) <= NumConstant.NUM_ZERO){
                continue;
            }
            MaterialsDTO materialsDTO = new MaterialsDTO();
            materialsDTO.setMaterialsPn(barcodePriceApiDTO.getBrandStyle());
            List<String> locationList = getLocationList(barcodePriceApiDTO);
            materialsDTO.setLocation(StringUtils.join(locationList, Constant.COMMA));
            materialsDTO.setMaterialsType(barcodePriceApiDTO.getItemName());
            materialsDTO.setSpecifications(barcodePriceApiDTO.getBrandStyle());
            materialsDTO.setManufacturer(barcodePriceApiDTO.getSupplierName());
            materialsDTO.setLotCode(barcodePriceApiDTO.getProductNo());
            materialsDTO.setIntegratedBarcode(barcodePriceApiDTO.getReelid());
            //reelid为空用220
            if(StringUtils.isEmpty(materialsDTO.getIntegratedBarcode())){
                materialsDTO.setIntegratedBarcode(barcodePriceApiDTO.getItemBarcode());
            }
            materialsDTO.setMaterialsSn(StringUtils.EMPTY);
            materialsDTO.setVersion(
                    StringUtils.EMPTY);
            //设置生产日期
            BarcodeExpandDTO barcodeExpandDTO = barcodeExpandDTOMap.get(barcodePriceApiDTO.getItemBarcode());
            if (barcodeExpandDTO != null) {
                materialsDTO.setDateCode(barcodeExpandDTO.getProdDate());
            }
            materialsDTOList.add(materialsDTO);
        }
        if(CollectionUtils.isEmpty(materialsDTOList)){
            return materialsDTOList;
        }
        //过滤reelid重复的
        List<MaterialsDTO> newList = materialsDTOList.stream().filter(e->StringUtils.isNotEmpty(e.getIntegratedBarcode())).collect(Collectors.collectingAndThen(
                Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(MaterialsDTO::getIntegratedBarcode))),ArrayList::new));
        Map<String,Integer> sortMap = new HashMap<>();
        for (MaterialsDTO materialsDTO : newList) {
            //位号为空用物料名称
            setLocation(sortMap, materialsDTO);
        }
        return newList;
    }

    /**
     * 位号
     * @param sortMap
     * @param materialsDTO
     */
    public void setLocation(Map<String, Integer> sortMap,MaterialsDTO materialsDTO) {
        if(StringUtils.isEmpty(materialsDTO.getLocation())){
            Integer seq = sortMap.get(materialsDTO.getMaterialsType());
            if(seq == null){
                seq = NumConstant.NUM_ONE;
            }else{
                seq ++;
            }
            materialsDTO.setLocation(materialsDTO.getMaterialsType()+seq);
            sortMap.put(materialsDTO.getMaterialsType(),seq);
        }
    }

    private List<String> getLocationList(BarcodePriceApiDTO barcodePriceApiDTO) {
        List<String> locationList = new ArrayList<>();
        if (StringUtils.isNotEmpty(barcodePriceApiDTO.getPositionExt())) {
            locationList.add(barcodePriceApiDTO.getPositionExt());
        }
        if (StringUtils.isNotEmpty(barcodePriceApiDTO.getPositionExt1())) {
            locationList.add(barcodePriceApiDTO.getPositionExt1());
        }
        if (StringUtils.isNotEmpty(barcodePriceApiDTO.getPositionExt2())) {
            locationList.add(barcodePriceApiDTO.getPositionExt2());
        }
        if (StringUtils.isNotEmpty(barcodePriceApiDTO.getPositionExt3())) {
            locationList.add(barcodePriceApiDTO.getPositionExt3());
        }
        if (StringUtils.isNotEmpty(barcodePriceApiDTO.getPositionExt5())) {
            locationList.add(barcodePriceApiDTO.getPositionExt5());
        }
        if (StringUtils.isNotEmpty(barcodePriceApiDTO.getPositionExt4())) {
            locationList.add(barcodePriceApiDTO.getPositionExt4());
        }
        if (StringUtils.isNotEmpty(barcodePriceApiDTO.getPositionExt6())) {
            locationList.add(barcodePriceApiDTO.getPositionExt6());
        }
        return locationList;
    }

    /**
     * 测试日志回传方法
     *
     * @param dto
     */
    @Override
    public void customerTestDataFeedback(CustomerDataUploadParamDTO dto) throws Exception {
        if (StringUtils.equals(Constant.STR_0, dto.getSchedulingType())) {
            //手动推送
            manualPushTestData(dto);
            return;
        }
        //处理历史失败数据
        dealHistoryFailData(dto);
        //定时任务处理
        Date nowDate = new Date();
        //定时任务调度获取上次执行最后条码时间
        SysLookupTypesDTO sysLookupTypesDTO = BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_3412, Constant.LOOKUP_TYPE_3412001);
        if (sysLookupTypesDTO == null) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SYS_LOOK_NOT_CONFIG, new Object[]{Constant.LOOKUP_TYPE_3412});
        }
        String lastExecutionTime = sysLookupTypesDTO.getLookupMeaning();
        //冗余一个小时处理数据
        Date lastExecDate = StringUtils.isEmpty(lastExecutionTime) ? null : DateUtils.addHours(DateUtils.parseDate(lastExecutionTime, Constant.DATE_TIME_FORMATE_FULL), NumConstant.NUM_NEGATIVE_ONE);

        customerTestDataFeedbackForImes(dto, lastExecDate);
        BasicsettingRemoteService.updateSysLookupValuesMeaning(new BigDecimal(Constant.LOOKUP_TYPE_3412001), this.getSimpleDateFormat().format(nowDate));
    }
    /**
     * 手动推送测试日志数据
     *
     * @param dto
     */
    private void manualPushTestData(CustomerDataUploadParamDTO dto) throws Exception {
        List<CustomerDataLogDTO> customerDataLogDTOList = dto.getCustomerDataLogDTOList();
        if (CollectionUtils.isEmpty(customerDataLogDTOList)) {
            return;
        }
        List<String> boardSnList = customerDataLogDTOList.stream().map(CustomerDataLogDTO::getSn).distinct().collect(Collectors.toList());
        dto.setBoardSnList(boardSnList);
        getItemNoForPushTestData(dto);
    }

    /**
     * 获取手动推送测试日志数据条码对应的料单
     *
     * @param dto
     */
    private void getItemNoForPushTestData(CustomerDataUploadParamDTO dto) throws Exception {
        List<String> boardSnList = dto.getBoardSnList();
        if (CollectionUtils.isEmpty(boardSnList)) {
            return;
        }
        List<PsWipInfo> psWipInfoList = wipInfoService.getListByBatchSn(boardSnList);
        if (CollectionUtils.isEmpty(psWipInfoList)) {
            return;
        }
        List<String> prodPlanIdList = psWipInfoList.stream().filter(e -> StringUtils.isNotEmpty(e.getAttribute1())).map(e -> e.getAttribute1()).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(prodPlanIdList)) {
            return;
        }
        List<PsTaskDTO> psTaskList = centerfactoryRemoteService.getPsTaskByProdplanIdList(prodPlanIdList);
        if (CollectionUtils.isEmpty(psTaskList)) {
            return;
        }
        Map<String, PsTaskDTO> psTaskMap = psTaskList.stream().collect(Collectors.toMap(PsTaskDTO::getProdplanId, a -> a, (k1, k2) -> k1));
        //根据料单获取基础信息
        List<String> itemNoList = psTaskList.stream().filter(e->StringUtils.isNotEmpty(e.getItemNo())).map(e -> e.getItemNo().substring(NumConstant.NUM_ZERO,NUM_TWELVE)).distinct().collect(Collectors.toList());

        List<CustomerItemsDTO> customerItemsDTOList = centerfactoryRemoteService.getListByItemNoList(itemNoList, dto.getCustomerName());
        if (CollectionUtils.isEmpty(customerItemsDTOList)) {
            return;
        }
        Map<String, CustomerItemsDTO> customerItemsDTOMap = customerItemsDTOList.stream().filter(e -> StringUtils.isNotEmpty(e.getZteCode())).collect(Collectors.toMap(CustomerItemsDTO::getZteCode, a -> a, (k1, k2) -> k1));
        if (customerItemsDTOMap.isEmpty()) {
            return;
        }
        assemblyReturnTestData(dto, psWipInfoList, customerItemsDTOMap, psTaskMap);
    }
    /**
     * 处理历史推送失败数据
     *
     */
    private void dealHistoryFailData(CustomerDataUploadParamDTO dto) throws Exception {
        int current = NumConstant.NUM_ONE;
        CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
        customerDataLogDTO.setFactoryId(dto.getFactoryId());
        customerDataLogDTO.setMessageType(Constant.MESSAGE_TYPE_TEST_FEEDBACK);
        customerDataLogDTO.setStartDate(DateUtil.addDay(new Date(),NumConstant.NUM_NEGATIVE_ONE));
        customerDataLogDTO.setEndDate(new Date());
        customerDataLogDTO.setCustomerName(dto.getCustomerName());
        while (current <= NumConstant.NUM_1000) {
            //历史错误数据
            List<CustomerDataLogDTO> customerDataLogDTOList = centerfactoryRemoteService.getPushErrorData(customerDataLogDTO);
            if (CollectionUtils.isEmpty(customerDataLogDTOList)) {
                return;
            }
            customerDataLogDTO.setId(customerDataLogDTOList.get(customerDataLogDTOList.size()-NumConstant.NUM_ONE).getId());
            CustomerDataUploadParamDTO customerDataUploadParamDTO = new CustomerDataUploadParamDTO();
            customerDataUploadParamDTO.setCustomerDataLogDTOList(customerDataLogDTOList);
            customerDataUploadParamDTO.setEmpNo(dto.getEmpNo());
            customerDataUploadParamDTO.setFactoryId(dto.getFactoryId());
            customerDataUploadParamDTO.setCustomerName(dto.getCustomerName());
            manualPushTestData(customerDataUploadParamDTO);
            current++;
        }
    }
    /**
     * 定时任务推送时获取未推送的数据，根据批次分批推送
     *
     * @param dto
     */
    private void customerTestDataFeedbackForImes(CustomerDataUploadParamDTO dto, Date lastExecDate) throws Exception {
        //获取中心工厂类型为自研主板、自研子卡、自研背板的料单
        List<CustomerItemsDTO> customerItemsDTOList = centerfactoryRemoteService.getListByItemNoList(new ArrayList<>(), dto.getCustomerName());
        if (CollectionUtils.isEmpty(customerItemsDTOList)) {
            return;
        }
        //设置项目类型名称
        Map<String, CustomerItemsDTO> customerItemsDTOMap = customerItemsDTOList.stream().filter(e -> StringUtils.isNotEmpty(e.getZteCode())).collect(Collectors.toMap(CustomerItemsDTO::getZteCode, a -> a, (k1, k2) -> k1));
        if (customerItemsDTOMap.isEmpty()) {
            return;
        }
        List<String> productCodeList = new ArrayList<>(customerItemsDTOMap.keySet());
        //获取料单对应批次  需要确认是否需要分批  取中心工厂
        List<PsTaskDTO> psTaskList = centerfactoryRemoteService.getPsTaskByItemNoList(productCodeList);
        if (CollectionUtils.isEmpty(psTaskList)) {
            return;
        }

        //所有料单对应批次
        List<String> prodPlanIdList = psTaskList.stream().map(PsTaskDTO::getProdplanId).distinct().collect(Collectors.toList());
        Map<String, PsTaskDTO> psTaskMap = psTaskList.stream().collect(Collectors.toMap(PsTaskDTO::getProdplanId, a -> a, (k1, k2) -> k1));
        Date maxDate = wipInfoService.getMaxSnDate(lastExecDate,prodPlanIdList);

        //10个批次分批
        for (List<String> tempProdPlanIdList : CommonUtils.splitList(prodPlanIdList, NumConstant.NUM_TEN)) {
            //获取wip_info料单下对应条码的
            int current = NumConstant.NUM_ONE;
            boolean execFlag = true;
            while (execFlag) {
                List<PsWipInfo> psWipInfoList = wipInfoService.getWipInfoByProdPlanIdAndLastUpdateDate(tempProdPlanIdList, lastExecDate, maxDate, current);
                if (CollectionUtils.isEmpty(psWipInfoList)) {
                    execFlag = false;
                }else {
                    assemblyReturnTestData(dto, psWipInfoList, customerItemsDTOMap, psTaskMap);
                    current++;
                }
            }
        }
    }
    /**
     * 组装测试工序条码扫描历史数据并触发B2B推送
     *
     */
    private void assemblyReturnTestData(CustomerDataUploadParamDTO dto,  List<PsWipInfo> psWipInfoList, Map<String, CustomerItemsDTO> customerItemsDTOMap,  Map<String, PsTaskDTO> psTaskMap) throws Exception {
        // psWipInfoList已在调用方法中校验，去掉该校验降低圈复杂度
        List<SysLookupTypesDTO> sysLookupTypesDTOList = BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_3411);
        if (CollectionUtils.isEmpty(sysLookupTypesDTOList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SYS_LOOK_NOT_CONFIG, new Object[]{Constant.LOOKUP_TYPE_3411});
        }
        // 获取批次对应条码信息
        List<String> testList = sysLookupTypesDTOList.stream().map(SysLookupTypesDTO::getLookupMeaning).collect(Collectors.toList());

        // 获取条码对应工艺路径信息
        List<WipScanHistory> wipScanHistoryList = dealSnRoute(psWipInfoList, dto, testList);
        // 获取已维修条码
        List<WipScanHistory> repairDataList = this.pushRepairDataActually(psWipInfoList, testList);
        // 获取条码对应测试工序扫描信息
        if(CollectionUtils.isEmpty(wipScanHistoryList) && CollectionUtils.isEmpty(repairDataList)) {
            return;
        }
        List<WipScanHistory> pushList = transferPushList(wipScanHistoryList, repairDataList, dto);
        Map<String, PsWipInfo> psWipInfoMap = psWipInfoList.stream().collect(Collectors.toMap(i -> i.getSn(), a -> a, (k1, k2) -> k1));
        List<CustomerDataLogDTO> customerDataLogDTOList = dto.getCustomerDataLogDTOList();
        Map<String, CustomerDataLogDTO> customerDataLogDTOMap = CollectionUtils.isEmpty(customerDataLogDTOList) ? new HashMap<>() : customerDataLogDTOList.stream().collect(Collectors.toMap(CustomerDataLogDTO::getSn, a -> a, (k1, k2) -> k1));
        List<CustomerDataLogDTO> dataList = new ArrayList<>();
        String accessToken = this.getMdsAccessToken();
        for(WipScanHistory wipScanHistory: pushList) {
            CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
            CustomerTestDataUploadDTO customerTestDataUploadDTO = new CustomerTestDataUploadDTO();
            // wip_info中料单为15位，客户维护料单为前12位，保留wip_info料单的前12位用于匹配客户数据
            PsWipInfo snScanDto=psWipInfoMap.get(wipScanHistory.getSn());
            if(snScanDto==null||snScanDto.getItemNo()==null){
                continue;
            }
            CustomerItemsDTO customerItemsDTO = customerItemsDTOMap.get(snScanDto.getItemNo().substring(0, 12));
            CustomerDataLogDTO tidDTO = customerDataLogDTOMap.get(wipScanHistory.getSn());
            // 为重推数据
            if (tidDTO != null) {
                customerDataLogDTO.setTid(tidDTO.getId());
            }
            // 组装jsonData数据
            customerTestDataUploadDTO.setBoardSn(wipScanHistory.getSn());
            customerTestDataUploadDTO.setStationId(wipScanHistory.getCraftSection());
            String stationLog = mdsRemoteService.getStationLogFromMDS(wipScanHistory.getSn(), accessToken);
            if(wipScanHistory.getCraftSection().contains(Constant.REPAIR_ENG) && StringUtils.isBlank(stationLog)) {
                continue;
            }
            customerTestDataUploadDTO.setStationLog(stationLog);
            customerTestDataUploadDTO.setFailLog("");
            // 组装客户日志回传数据
            customerDataLogDTO.setId(UUID.randomUUID().toString());
            customerDataLogDTO.setOrigin(Constant.IMES);
            setBasicInformation(customerItemsDTO, customerDataLogDTO);
            customerDataLogDTO.setCraftSection(customerTestDataUploadDTO.getStationId());
            customerDataLogDTO.setSn(wipScanHistory.getSn());
            customerDataLogDTO.setFactoryId(dto.getFactoryId());
            customerDataLogDTO.setMessageType(Constant.MESSAGE_TYPE_TEST_FEEDBACK);
            customerDataLogDTO.setCreateBy(dto.getEmpNo());
            customerDataLogDTO.setLastUpdatedBy(dto.getEmpNo());
            PsWipInfo snDto=psWipInfoMap.get(wipScanHistory.getSn());
            if(snDto == null){
                continue;
            }
            PsTaskDTO psTask = psTaskMap.get(snDto.getAttribute1());
            setTaskInfo(psTask, customerDataLogDTO);
            customerDataLogDTO.setJsonData(JSON.toJSONString(customerTestDataUploadDTO));
            dataList.add(customerDataLogDTO);
        }
        this.pushTestDataToB2B(dataList);
    }

    private String getMdsAccessToken() {
        String accessToken = mdsRemoteService.getAccessToken();
        if(StringUtils.isBlank(accessToken)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.GET_MDS_TOKEN_ERROR);
        }
        return accessToken;
    }

    /**
     *  分批推送字节数据到B2B
     */
    private void pushTestDataToB2B(List<CustomerDataLogDTO> dataList) {
        if(CollectionUtils.isEmpty(dataList)) {
            return;
        }
        // 获取数据字典10000的10000001配置字节分批推送数据量
        SysLookupTypesDTO lookupTypesDTO = BasicsettingRemoteService.getSysLookUpValue(Constant.STR_10000, Constant.STR_10000001);
        if (null == lookupTypesDTO || StringUtils.isBlank(lookupTypesDTO.getLookupMeaning())) {
            centerfactoryRemoteService.pushDataToB2B(dataList);
        } else {
            int limitNum = Integer.parseInt(lookupTypesDTO.getLookupMeaning());
            for (List<CustomerDataLogDTO> splitList : CommonUtils.splitList(dataList, limitNum)) {
                centerfactoryRemoteService.pushDataToB2B(splitList);
            }
        }
    }

    /**
     * 处理条码对应工艺路径，并根据工艺路径获取存在后工序扫描历史的测试工序扫描历史
     */
    private List<WipScanHistory> dealSnRoute(List<PsWipInfo> psWipInfoList, CustomerDataUploadParamDTO dto, List<String> testList) {
        String testSection = String.join(",", testList);
        List<String> itemNoList = psWipInfoList.stream().map(PsWipInfo::getItemNo).distinct().collect(Collectors.toList());
        // 获取测试工序的基本信息
        List<BSProcess> bsProcessList = CrafttechRemoteService.getBsProcessListByTestCraft(testSection);
        // 获取料单对应工艺路径
        List<CtBasicRouteDTO> ctBasicRouteList = CrafttechRemoteService.getRouteAndSeqByItemNos(itemNoList);
        if(CollectionUtils.isEmpty(ctBasicRouteList) || CollectionUtils.isEmpty(bsProcessList)) {
            return new ArrayList<>();
        }
        Map<String, BSProcess> testSectionMap = bsProcessList.stream().collect(Collectors.toMap(BSProcess::getProcessName, a -> a, (k1, k2) -> k1));
        List<WipScanHistory> verifyDataList = new ArrayList<>();
        Map<String, CtBasicRouteDTO> routeMap = ctBasicRouteList.stream().collect(Collectors.toMap(CtBasicRouteDTO::getItemNo, a -> a, (k1, k2) -> k1));
        for(PsWipInfo psWipInfo : psWipInfoList) {
            if (routeMap.containsKey(psWipInfo.getItemNo())) {
                // 根据料单代码获取对应工艺路径并根据 -> 分割
                String[] route = routeMap.get(psWipInfo.getItemNo()).getRouteDetail().split(Constant.ROUTE_POINTER);
                // 根据料单代码获取对应工艺路径代码并根据加号分割
                String routePathCode =  routeMap.get(psWipInfo.getItemNo()).getRoutePathCode();
                // 处理code为+的情况，将"++"替换成"+加号替换"--查询sql改成按照$拼接,这里也改成按照$拆分
                String[] routeCode = routePathCode.split(Constant.STR_SPLIT_$);
                // 组装工序名称和工序Code对应关系map
                Map<String, String> codeWithNameMap = generateCodeWithNameMap(route, routeCode);
                // 关联工艺路径测试工序和下工序
                Map<String, String> nextProcessMap = findNextProcess(testSectionMap, route);
                if (CollectionUtils.isEmpty(nextProcessMap)) {
                    continue;
                }
                // 将条码和对应测试工序的下工序数据组合
                setDataForQryWipScanHis(verifyDataList, nextProcessMap, codeWithNameMap, psWipInfo);
            }
        }
        // 查询存在下工序扫描历史的对应测试工序扫描历史
        return wipScanHistoryService.getWipScanHistoryForTestCraftSection(verifyDataList);
    }
    /**
     * 组装条码对应工艺路径和工艺路径CODE的映射关系
     *
     */
    private Map<String, String> generateCodeWithNameMap(String[] route, String[] routeCode) {
        Map<String, String> codeWithNameMap = new HashMap<>();
        for (int i = 0; i < route.length; i++) {
            if(routeCode[i].equals(Constant.REPLACE_ADD_VALUE)) {
                routeCode[i] = Constant.ADD_FLAG;
            }
            codeWithNameMap.put(route[i], routeCode[i]);
        }
        return codeWithNameMap;
    }


    /**
     * 组装用于筛选查询数据的实体类表
     *
     */
    private void setDataForQryWipScanHis(List<WipScanHistory> verifyDataList, Map<String, String> nextProcessMap, Map<String, String> codeWithNameMap, PsWipInfo psWipInfo) {
        for (String key : nextProcessMap.keySet()) {
            WipScanHistory wipScanHistory = new WipScanHistory();
            wipScanHistory.setItemNo(psWipInfo.getItemNo());
            String nextProcessName =nextProcessMap.get(key);
            wipScanHistory.setNextProcess(codeWithNameMap.get(nextProcessName));
            wipScanHistory.setSn(psWipInfo.getSn());
            wipScanHistory.setCurrProcessCode(codeWithNameMap.get(key));
            verifyDataList.add(wipScanHistory);
        }
    }
    /**
     * 组装测试工序和下工序的映射关系
     *
     */
    private Map<String, String> findNextProcess(Map<String, BSProcess> testSectionMap, String[] route) {
        Map<String, String> nextProcessMap = new HashMap<>();
        for (int i = 0 ; i < route.length; i++) {
            if (testSectionMap.containsKey(route[i])) {
                nextProcessMap.put(route[i], route[i+1]);
            }
        }
        return nextProcessMap;
    }
    /**
     * 筛选已推送测试工序的条码
     *
     */
    private List<WipScanHistory> filterPushedTestProcessSn(List<WipScanHistory> verifyDataList) throws Exception {
        List<CustomerDataLogDTO> pushedSn = centerfactoryRemoteService.getPushedTestProcessSnScanData(verifyDataList);
        if (CollectionUtils.isEmpty(pushedSn)) {
            return verifyDataList;
        }
        Map<String, CustomerDataLogDTO> pushedSnMap = pushedSn.stream().collect(Collectors.toMap(item -> item.getSn() + item.getCraftSection(), a -> a, (k1, k2) -> k1));
        // 筛选与返回的已推送条码+测试工序不同的数据
        return verifyDataList.stream().filter(i -> !pushedSnMap.containsKey(i.getSn() + i.getCraftSection())).collect(Collectors.toList());
    }

    /**
     * 查询维修记录实时推送对应数据
     */
    private List<WipScanHistory> pushRepairDataActually(List<PsWipInfo> psWipInfoList, List<String> testList) {
        List<String> snList = psWipInfoList.stream().map(PsWipInfo::getSn).distinct().collect(Collectors.toList());
        List<WipScanHistory> repairDataList = pmRepairRcvDetailRepository.getTestRepairDataForPush(snList, testList);
        if(CollectionUtils.isEmpty(repairDataList)) {
            return new ArrayList<>();
        }
        return repairDataList;
    }

    /**
     * 合并扫描数据和维修条码数据
     */
    private List<WipScanHistory> transferPushList(List<WipScanHistory> wipScanHistoryList, List<WipScanHistory> repairDataList, CustomerDataUploadParamDTO dto) throws Exception {
        if (CollectionUtils.isEmpty(repairDataList)) {
            return wipScanHistoryList;
        }
        if (CollectionUtils.isEmpty(wipScanHistoryList)) {
            return repairDataList;
        }
        // 过滤已经转交下工序条码数据的维修数据
        for (WipScanHistory repairData : repairDataList) {
            if (null != repairData.getSn() && wipScanHistoryList.stream().noneMatch(i -> Objects.equals(i.getSn(), repairData.getSn()))) {
                wipScanHistoryList.add(repairData);
            }
        }
        // 如果是定时任务推送（不是手动重推），筛选未推送测试工序条码
        if(!Constant.STR_0.equals(dto.getSchedulingType())) {
            this.filterPushedTestProcessSn(wipScanHistoryList);
        }
        return wipScanHistoryList;
    }


    /**
     * 推送条码站记录到alibaba
     *
     * @param dto 参数
     */
    @Override
    @RecordLogAnnotation("客户测过站数据回传alibaba")
    @RedisDistributedLockAnnotation(redisPrefix = "customerTestAlibabaFeedback", factoryId = true, redisLockTime = 36000,
            redisLockParam = {@RedisLockParamAnnotation(paramName = "dto", propertiesString = "customerName")})
    @AlarmAnnotation(alarmName = "push_data_alibaba_error", alarmKey = "8000", alarmTitle = "客户测过站数据回传alibaba")
    public void customerTestAlibabaFeedback(PushBoardDataProcessDTO dto) {
        // 参数校验
        this.checkRequestHeadThenSetProperties(dto);
        List<PushBoardDataProcessDTO> list = null;
        do {
            // 获取中心工厂 push_board_data_detail,push_board_data_process 本工厂没有推送的条码
            list = centerfactoryRemoteService.queryPushDataList(dto);
            if (CollectionUtils.isEmpty(list)) {
                // 没有需要处理的数据
                break;
            }
            // 设置下次查询的参数 条码 加时间
            PushBoardDataProcessDTO pushBoardDataProcessDTO = list.get(list.size() - 1);
            dto.setId(pushBoardDataProcessDTO.getId());
            dto.setStartTime(pushBoardDataProcessDTO.getCreateDate());

            // 调用计划服务获取 获取批次的工信息
            Map<String, List<PushBoardDataProcessDTO>> pushGroup = list.stream()
                    .collect(Collectors.groupingBy(PushBoardDataProcessDTO::getProdplanId));
            List<String> sourceList = new LinkedList<>(pushGroup.keySet());
            List<PsWorkOrderBasic> workBasicByTask = PlanscheduleRemoteService.getWorkBasicByTask(sourceList, null, Constant.FLAG_Y);
            if (CollectionUtils.isEmpty(workBasicByTask)) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.WORKORDER_NOT_FIND);
            }
            Map<String, List<PsWorkOrderBasic>> sourceGroup = workBasicByTask.stream().collect(Collectors.groupingBy(PsWorkOrderBasic::getSourceTask));
            this.executeDataPush(dto, pushGroup, sourceGroup);
        } while (list.size() == dto.getRows());
    }

    /**
     * 校验并配置相关参数
     * @param dto 请求参数
     */
    private void checkRequestHeadThenSetProperties(PushBoardDataProcessDTO dto) {
        Pair<String, String> pair = RequestHeadValidationUtil.validaFactoryIdAndEmpno();
        dto.setFactoryId(Integer.valueOf(pair.getFirst()));
        dto.setCreateBy(pair.getSecond());
        // 获取维修结果映射数据字典
        List<SysLookupValuesDTO> sysLookupValues = BasicsettingRemoteService.getSysLookupValues(Constant.LOOKUP_TYPE_1004120);
        Map<String, String> resultMap = sysLookupValues.stream()
                .collect(Collectors.toMap(SysLookupValuesDTO::getAttribute1,
                SysLookupValuesDTO::getLookupMeaning, (k1, k2) -> k1));
        dto.setResultMap(resultMap);
    }

    /**
     * 推送数据组装
     *
     * @param dto         参数
     * @param pushGroup   待推送数据
     * @param sourceGroup 批次信息
     */
    private void executeDataPush(PushBoardDataProcessDTO dto, Map<String, List<PushBoardDataProcessDTO>> pushGroup
            , Map<String, List<PsWorkOrderBasic>> sourceGroup) {
        // 遍历单个批次条码处理数据
        for (Map.Entry<String, List<PushBoardDataProcessDTO>> entry : pushGroup.entrySet()) {
            // 如果批次工艺路径没有包含ICT/Test 工序，push_board_data_process 表新增推送成功记录，流程结束
            List<PsWorkOrderBasic> psWorkOrderBasics = sourceGroup.getOrDefault(entry.getKey(), new LinkedList<>());
            List<PushBoardDataProcessDTO> needNextList = new LinkedList<>();
            List<PushBoardDataProcessDTO> updateList = new ArrayList<>();
            // 获取需要后续操作的数据集合，工艺路径ICT/Test 工序
            this.addNeedDataList(entry, psWorkOrderBasics, needNextList, updateList);
            List<CustomerDataLogDTO> pushDataList = new ArrayList<>();
            // 存在ICT/Test工序集合
            if (!CollectionUtils.isEmpty(needNextList)) {
                // 获取入库的条码集合
                List<String> existSnList = this.checkInBoundSnList(needNextList);
                // 不入库的条吗不处理 的条码
                needNextList.removeIf(item -> !existSnList.contains(item.getSn()));
                if(!CollectionUtils.isEmpty(needNextList)){
                    // 获取扫描历史
                    dto.setSnScanHisMap(this.querySnScaHistory(dto, existSnList));
                    // 直接根据12位料单+客户名称获取客户部件类型
                    dto.setCustomerItemsDTO(this.queryCustomerItemInfo(dto, psWorkOrderBasics));
                    dto.setWorkMap(psWorkOrderBasics.stream()
                            .collect(Collectors.toMap(PsWorkOrderBasic::getCraftSection, v -> v, (k1, k2) -> k1)));
                    // 获取中试测试版本
                    dto.setSnAndCraftSectionToTestProgramVersion(this.queryMdsVersion(needNextList));
                    // 查询条码送修信息
                    dto.setRePairMap(this.querySnRepairMap(dto, existSnList));
                    // 组装参数
                    this.buildPushData(dto, needNextList, updateList, pushDataList);
                }
            }
            // 更新推送数据到中心工厂
            this.pushDataToCenterAndUpdateStatus(dto, updateList, pushDataList);
            // 失败发送通知
            this.sendCenterMsg(updateList);
        }
    }

    private void sendCenterMsg(List<PushBoardDataProcessDTO> updateList) {
        String msg = updateList.stream().map(PushBoardDataProcessDTO::getErrorMsg)
                .filter(StringUtils::isNotBlank)
                .distinct().collect(Collectors.joining(","));
        if (StringUtils.isNotBlank(msg)) {
            try {
                iCenterRemoteService.sendMessage(Constant.REPAIR_SN_PUSH, msg, centerReceiver);
            } catch (Exception e) {
                log.error("sendEmail",e);
            }
        }
    }

    /* Started by AICoder, pid:b3c34y466du1eed1487e0b21e0ff666bdad79819 */
    /**
     * 组装推送参数
     * @param dto 参数
     * @param needNextList needNextList
     * @param updateList updateList
     * @param pushDataList pushDataList
     */
    private void buildPushData(PushBoardDataProcessDTO dto, List<PushBoardDataProcessDTO> needNextList ,
                           List<PushBoardDataProcessDTO> updateList, List<CustomerDataLogDTO> pushDataList) {
        Map<String, PsWorkOrderBasic> workMap = dto.getWorkMap();
        CustomerItemsDTO customerItemsDTO1 = dto.getCustomerItemsDTO();
        Map<String, List<WipScanHistory>> snScanHisMap = dto.getSnScanHisMap();
        Map<String, String> snAndCraftSectionToTestProgramVersion = dto.getSnAndCraftSectionToTestProgramVersion();
        Map<String, PmRepairDetailDTO> rePairMap = dto.getRePairMap();
        for (PushBoardDataProcessDTO item : needNextList) {
            // 过站记录
            PsWorkOrderBasic orDefault = workMap.getOrDefault(item.getBusinessType(), new PsWorkOrderBasic());
            // 过站记录推送信息
            BoardRecordDTO tempSuccess = this.buildBoardData(item, customerItemsDTO1, orDefault, snScanHisMap, snAndCraftSectionToTestProgramVersion);
            // 组装推送数据
            CustomerDataLogDTO snSuccess = this.buildPushData(item, tempSuccess, orDefault, customerItemsDTO1);
            // 条码维修记录，存在则新增失败记录
            PmRepairDetailDTO repairDetailDTO = rePairMap.get(item.getSn() + item.getBusinessType());
            item.setPushDate(new Date());
            item.setPushStatus(Constant.INT_1);
            item.setErrorMsg(StringUtils.EMPTY);
            updateList.add(item);
            if (Objects.isNull(repairDetailDTO)) {
                // 不存在维修记录
                pushDataList.add(snSuccess);
                continue;
            }
            // 组装维修失败记录
            BoardRecordDTO tempFail = new BoardRecordDTO();
            BeanUtils.copyProperties(tempSuccess, tempFail);
            tempFail.setRequestId(idGenerator.snowFlakeIdStr());
            tempFail.setResult(Constant.FAIL_RESULT);
            // 站位开始时间 --上工序转交到本工序时间,若存在维修记录，
            // 传失败记录时取上工序转交本工序时间，
            tempFail.setStartedTime(tempSuccess.getStartedTime());
            // 成功记录取最新一次送修的维修返还时间
            tempSuccess.setStartedTime(repairDetailDTO.getReturnedDate());
            // 站位完成时间 本工序最后工站扫描时间，存存在维修记录，
            // 则传失败记录时结束时间取最新一次送修记录的维修返还时间，传成功记录时取本工序最后工站扫描时间
            tempFail.setFinishedTime(repairDetailDTO.getReturnedDate());
            snSuccess.setJsonData(JacksonJsonConverUtil.beanToJson(tempSuccess));

            // 工序站位检查结果失败时的原因说明 --维修小类-维修次小类（取本工序最新一次送修纪录中最新一条维修数据）
            tempFail.setMessage(repairDetailDTO.getRepairProductMstype());
            pushDataList.add(snSuccess);

            CustomerDataLogDTO snFailData = this.buildPushData(item, tempFail, workMap.getOrDefault(item.getBusinessType(), new PsWorkOrderBasic()), customerItemsDTO1);
            pushDataList.add(snFailData);

            // 组装维修记录
            ProblemRequestDTO problemRequestDTO = this.getProblemRequestDTO(tempFail, repairDetailDTO);
            //  采用数据字典配置维修录入的处理结果与编码关系，将处理结果转化为编码，若处理结果为空则默认“正常维修完成”
            // （取本工序最新一次送修纪录中最新一条维修数据），若找不到编码则记录异常，待下次推送
            String code = dto.getResultMap().get(problemRequestDTO.getActionMsg());
            if (StringUtils.isBlank(code)) {
                // 删除推送数据
                pushDataList.remove(snSuccess);
                pushDataList.remove(snFailData);
                item.setPushStatus(Constant.INT_8);
                // 记录报错原因
                item.setErrorMsg(CommonUtils.getLmbMessage(MessageId.REPAIR_RESULT_CODE_LOST,
                        new String[]{Constant.LOOKUP_TYPE_1004120, problemRequestDTO.getActionMsg()}));
                continue;
            }
            // 推送仨条记录
            item.setPushNumber(Constant.INT_3);
            problemRequestDTO.setActionCode(code);
            // 送修时间，取最新一条送修时间
            problemRequestDTO.setReworkTime(repairDetailDTO.getCreateDate());
            // 返还时间，取最新一条送修记录对应的返还时间
            problemRequestDTO.setFinishReworkTime(repairDetailDTO.getReturnedDate());
            CustomerDataLogDTO pushData = this.getCustomerDataLogDTO(item, customerItemsDTO1, problemRequestDTO,
                    orDefault, tempSuccess);
            pushDataList.add(pushData);
        }
    }
    /* Ended by AICoder, pid:b3c34y466du1eed1487e0b21e0ff666bdad79819 */

    private CustomerDataLogDTO getCustomerDataLogDTO(PushBoardDataProcessDTO item, CustomerItemsDTO customerItemsDTO1,
                                                     ProblemRequestDTO problemRequestDTO,
                                                     PsWorkOrderBasic orDefault, BoardRecordDTO tempSuccess) {
        CustomerDataLogDTO pushData = new CustomerDataLogDTO();
        pushData.setCustomerName(customerItemsDTO1.getCustomerName());
        pushData.setJsonData(JacksonJsonConverUtil.beanToJson(problemRequestDTO));
        pushData.setMessageType(Constant.ZTEI_MES_ALIBABA_CAPTURE_ERROR_PROCESS);
        pushData.setCraftSection(item.getBusinessType());
        pushData.setTaskNo(orDefault.getTaskNo());
        pushData.setSn(tempSuccess.getBoardSn());
        pushData.setCooperationMode(customerItemsDTO1.getCooperationMode());
        pushData.setProjectName(customerItemsDTO1.getProjectName());
        pushData.setProjectPhase(item.getBusinessType());
        pushData.setId(idGenerator.snowFlakeIdStr());
        pushData.setKeywords(item.getSn());
        pushData.setOrigin(Constant.IMES);
        return pushData;
    }

    /**
     * 获取维修问题擦书
     * @param tempFail 失败记录
     * @param repairDetailDTO 问题记录
     * @return 问题实体
     */
    private ProblemRequestDTO getProblemRequestDTO(BoardRecordDTO tempFail, PmRepairDetailDTO repairDetailDTO) {
        ProblemRequestDTO problemRequestDTO = new ProblemRequestDTO();
        problemRequestDTO.setProblemRequestId(tempFail.getRequestId());
        problemRequestDTO.setType("BOARD_CAPTURE");
        // 失败的站位名
        problemRequestDTO.setStationName(tempFail.getStationName());
        problemRequestDTO.setSn(tempFail.getBoardSn());
        problemRequestDTO.setBrand(Constant.INVENTEC_ZTE);
        // 失败定位后的一阶错误码; 维修大类（取本工序最新一次送修纪录中最新一条维修数据）
        problemRequestDTO.setErrorCode(repairDetailDTO.getRepairProductType());

        //  处理结果，若处理结果为空则默认“正常维修完成”（取本工序最新一次送修纪录中最新一条维修数据）
        String result = repairDetailDTO.getResult();
        if (StringUtils.isBlank(result)) {
            result = Constant.REPAIR_SUCCESS;
        }
        problemRequestDTO.setActionMsg(result);
        return problemRequestDTO;
    }

    /**
     * 获取入库条码
     * @param needNextList 条码信息
     * @return 入库条码
     */
    private List<String> checkInBoundSnList(List<PushBoardDataProcessDTO> needNextList) {
        List<String> existSnList = new LinkedList<>();
        List<String> snList = needNextList.stream().map(PushBoardDataProcessDTO::getSn)
                .distinct().collect(Collectors.toList());
        // 查询条码状态是否为入库,不入库不处理
        List<List<String>> splitList = CommonUtils.splitList(snList, Constant.BATCH_SIZE);
        for (List<String> sns : splitList) {
            List<String> boundList = psWipInfoRepository.selectInboundSnListBatch(sns);
            if (!CollectionUtils.isEmpty(boundList)) {
                existSnList.addAll(boundList);
            }
        }
        return existSnList;
    }

    /* Started by AICoder, pid:if6aai6358zf9ab140a70b1a20e70e4e547359aa */
    /**
     * 推送数据到中心工厂更新操作
     * @param dto 参数
     * @param updateList 更新数据集合
     * @param pushDataList 推送数据
     */
    private void pushDataToCenterAndUpdateStatus(PushBoardDataProcessDTO dto, List<PushBoardDataProcessDTO> updateList,
                                                 List<CustomerDataLogDTO> pushDataList) {
        pushDataList.forEach(item -> {
            // 设置通用属性
            item.setCreateBy(dto.getCreateBy());
            item.setLastUpdatedBy(dto.getCreateBy());
            item.setCreateDate(new Date());
            item.setLastUpdatedDate(new Date());
            item.setFactoryId(dto.getFactoryId());
            item.setCustomerName(dto.getCustomerName());
            item.setFactoryId(dto.getFactoryId());
        });
        // 先推送失败的记录
        List<List<CustomerDataLogDTO>> lists = CommonUtils.splitList(pushDataList, Constant.INT_400);
        // 推送B2B
        lists.forEach(item -> centerfactoryRemoteService.pushDataToB2BByKafka(item));
        // 更新推送状态
        List<List<PushBoardDataProcessDTO>> splitList = CommonUtils.splitList(updateList, Constant.INT_400);
        splitList.forEach(item -> centerfactoryRemoteService.updateBoardDataProcessBatch(item));
    }
    /* Ended by AICoder, pid:if6aai6358zf9ab140a70b1a20e70e4e547359aa */

    /**
     * 查询条码送修信息
     *
     * @param dto         参数
     * @param existSnList 条吗集合
     * @return 送修信息
     */
    private Map<String, PmRepairDetailDTO> querySnRepairMap(PushBoardDataProcessDTO dto, List<String> existSnList) {
        List<PmRepairDetailDTO> snRepairList = new LinkedList<>();
        List<List<String>> splitList1 = CommonUtils.splitList(existSnList, Constant.INT_50);
        splitList1.forEach(item -> {
            List<PmRepairDetailDTO> repairList = pmRepairRcvRepository.querySnRepairMaxBatch(item, dto.getBusinessTypeList());
            if (!CollectionUtils.isEmpty(repairList)) {
                snRepairList.addAll(repairList);
            }
        });
        return snRepairList.stream()
                .collect(Collectors.toMap(item -> item.getSn() + item.getCraftSection(), v -> v, (k1
                        , k2) -> k1));
    }

    /**
     * 查询中试测试版本
     *
     * @param needNextList 条码集合
     * @return 条码工序信息
     */
    private Map<String, String> queryMdsVersion(List<PushBoardDataProcessDTO> needNextList) {
        List<Map> snScanList = new ArrayList<>();
        needNextList.forEach(item -> {
            Map<String, String> tempMap = new HashMap<>();
            tempMap.put(SN_CODE, item.getSn());
            tempMap.put(CRAFT_SECTION, item.getBusinessType());
            snScanList.add(tempMap);
        });
        Map<String, String> snAndCraftSectionToTestProgramVersion = new HashMap<>();
        List<Map> maps = mdsRemoteService.queryCrossInfo(snScanList);
        for (Map<String, String> map : maps) {
            String snAndCraftSection = map.get("partcode") + COMMA + map.get("procedure");
            snAndCraftSectionToTestProgramVersion.put(snAndCraftSection, map.get("testProgramName"));
        }
        return snAndCraftSectionToTestProgramVersion;
    }

    /**
     * u获取辽大客户物料信息
     *
     * @param dto               dto
     * @param psWorkOrderBasics 批次信息
     * @return 客户物料维护
     */
    private CustomerItemsDTO queryCustomerItemInfo(PushBoardDataProcessDTO dto, List<PsWorkOrderBasic> psWorkOrderBasics) {
        String itemNo = psWorkOrderBasics.get(0).getItemNo().substring(0, Constant.INT_12);
        CustomerItemsDTO customerItemsDTO = new CustomerItemsDTO();
        customerItemsDTO.setCustomerNameList(Collections.singletonList(dto.getCustomerName()));
        customerItemsDTO.setItemNoList(Collections.singletonList(itemNo));
        List<CustomerItemsDTO> itemsDTOList = centerfactoryRemoteService.queryCustomerItemsInfo(customerItemsDTO);
        if (CollectionUtils.isEmpty(itemsDTOList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.CUSTOMER_INFO_LOST,
                    new Object[]{dto.getCustomerName(), itemNo});
        }
        return itemsDTOList.get(0);
    }

    /**
     * 获取条码扫描历史
     *
     * @param dto         dto
     * @param existSnList existSnList
     * @return 条码扫描历史
     */
    private Map<String, List<WipScanHistory>> querySnScaHistory(PushBoardDataProcessDTO dto, List<String> existSnList) {
        List<WipScanHistory> historyResultList = new LinkedList<>();
        List<List<String>> splitList1 = CommonUtils.splitList(existSnList, Constant.INT_50);
        for (List<String> snArr : splitList1) {
            List<WipScanHistory> historyList = wipScanHistoryRepository.getWipScanHistoryListBySnAndCraft(snArr, dto.getBusinessTypeList());
            if (!CollectionUtils.isEmpty(historyList)) {
                historyResultList.addAll(historyList);
            }
        }
        return historyResultList.stream().collect(Collectors.groupingBy(WipScanHistory::getSn));
    }

    /**
     * @param entry             entry
     * @param psWorkOrderBasics psWorkOrderBasics
     * @param needNextList      needNextList
     * @param updateList        updateList
     */
    private void addNeedDataList(Map.Entry<String, List<PushBoardDataProcessDTO>> entry, List<PsWorkOrderBasic> psWorkOrderBasics,
                                 List<PushBoardDataProcessDTO> needNextList, List<PushBoardDataProcessDTO> updateList) {
        Map<String, List<PushBoardDataProcessDTO>> businessGroup = entry.getValue().stream()
                .collect(Collectors.groupingBy(PushBoardDataProcessDTO::getBusinessType));
        // 过滤出待Test Ict工序数据
        List<String> craftList = psWorkOrderBasics.stream().map(PsWorkOrderBasic::getCraftSection)
                .collect(Collectors.toList());
        businessGroup.forEach((k, v) -> {
            if (craftList.contains(k)) {
                // 包含工序
                needNextList.addAll(businessGroup.getOrDefault(k, new LinkedList<>()));
            } else {
                // 没有包含工序
                this.buildNoCraftSection(businessGroup, k, updateList);
            }
        });
    }

    /* Started by AICoder, pid:g9ee5eb7e49ca7f144db0b3e406fe579188340ac */
    /**
     * 组装过站记录
     *
     * @param item                                  参数
     * @param customerItemsDTO1                     客户信息
     * @param orDefault                             orDefault
     * @param snScanHisMap                          snScanHisMap
     * @param snAndCraftSectionToTestProgramVersion 中试测试程序
     * @return 过站信息
     */
    private BoardRecordDTO buildBoardData(PushBoardDataProcessDTO item, CustomerItemsDTO customerItemsDTO1, PsWorkOrderBasic orDefault,
                                          Map<String, List<WipScanHistory>> snScanHisMap, Map<String, String> snAndCraftSectionToTestProgramVersion) {
        BoardRecordDTO temp = new BoardRecordDTO();
        // 请求唯一id
        temp.setRequestId(idGenerator.snowFlakeIdStr());
        // 设备类型
        temp.setType(customerItemsDTO1.getCustomerComponentType());
        // 计划跟踪单号
        temp.setWorkorderId(orDefault.getTaskNo());
        // 主板SN
        temp.setBoardSn(item.getSn());
        // 品名
        temp.setBrand(Constant.INVENTEC_ZTE);
        // 站位名称
        temp.setStationName(stationNameMap.get(item.getBusinessType()));
        // 扫描历史信息
        List<WipScanHistory> snScanList = snScanHisMap.getOrDefault(item.getSn(), new LinkedList<>());
        List<WipScanHistory> collect = snScanList.stream()
                .filter(history -> StringUtils.equals(item.getBusinessType(), history.getCraftSection()))
                .sorted(Comparator.comparing(WipScanHistory::getCreateDate)).collect(Collectors.toList());
        // 站位开始时间 --上工序转交到本工序时间,若存在维修记录，
        // 传失败记录时取上工序转交本工序时间，成功记录取最新一次送修的维修返还时间
        temp.setStartedTime(collect.get(0).getCreateDate());
        // 站位完成时间 本工序最后工站扫描时间，存存在维修记录，
        // 则传失败记录时结束时间取最新一次送修记录的维修返还时间，传成功记录时取本工序最后工站扫描时间
        temp.setFinishedTime(collect.get(collect.size()-1).getCreateDate());
        // 本工序存在送修则Fail，扫描完成传一条Pass
        temp.setResult(Constant.PASS_RESULT);
        // 工序版本信息 {"main_version":"001"} --中试接口获取
        Map<String,String> versionMap = new HashMap<>();
        versionMap.put("main_version", snAndCraftSectionToTestProgramVersion.get(item.getSn() + COMMA + item.getBusinessType()));
        temp.setVersion(JSON.toJSONString(versionMap));
        // 工厂名称
        temp.setManufacturerName(alibabaManufacturerName);
        // 主板MPN
        temp.setBoardMpn(customerItemsDTO1.getCustomerMaterialType());
        // 线别
        temp.setLine(orDefault.getLineName());
        return temp;
    }
    /* Ended by AICoder, pid:g9ee5eb7e49ca7f144db0b3e406fe579188340ac */


     /* Started by AICoder, pid:v8880f1647gca0d140fb0a0c30cf3021c3477ed0 */
    /**
     * 组装推送alibaba 数据实体
     * @param item 参数
     * @param temp 参数
     * @param orDefault 的
     * @param customerItemsDTO1 的
     * @return 参数
     */
    private CustomerDataLogDTO buildPushData(PushBoardDataProcessDTO item, BoardRecordDTO temp,
                                           PsWorkOrderBasic orDefault, CustomerItemsDTO customerItemsDTO1) {
        CustomerDataLogDTO pushData = new CustomerDataLogDTO();
        pushData.setJsonData(JacksonJsonConverUtil.beanToJson(temp));
        pushData.setCustomerName(customerItemsDTO1.getCustomerName());
        pushData.setMessageType(Constant.MES_ALIBABA_BOARD_PRODUCT_INFO);
        pushData.setCraftSection(item.getBusinessType());
        pushData.setTaskNo(orDefault.getTaskNo());
        pushData.setSn(temp.getBoardSn());
        pushData.setCooperationMode(customerItemsDTO1.getCooperationMode());
        pushData.setProjectName(customerItemsDTO1.getProjectName());
        pushData.setProjectPhase(item.getBusinessType());
        pushData.setId(idGenerator.snowFlakeIdStr());
        pushData.setKeywords(item.getSn());
        pushData.setOrigin(Constant.IMES);
        return pushData;
    }
    /* Ended by AICoder, pid:v8880f1647gca0d140fb0a0c30cf3021c3477ed0 */

    private void buildNoCraftSection(Map<String, List<PushBoardDataProcessDTO>> businessGroup, String ict, List<PushBoardDataProcessDTO> updateList) {
        businessGroup.getOrDefault(ict, new LinkedList<>())
                .forEach(item -> {
            // 回调成功
            item.setPushStatus(Constant.INT_2);
            item.setPushDate(new Date());
            updateList.add(item);
        });
    }

}
