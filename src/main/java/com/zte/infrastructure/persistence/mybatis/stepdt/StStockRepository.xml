<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.stepdt.StStockRepository">
  <resultMap id="BaseResultMap" type="com.zte.domain.model.stepdt.StStock">
    <id column="STOCK_NO" jdbcType="VARCHAR" property="stockNo" />
    <result column="STOCK_NAME" jdbcType="VARCHAR" property="stockName" />
    <result column="ATTRIBUTE" jdbcType="DECIMAL" property="attribute" />
    <result column="ENTP_NO" jdbcType="VARCHAR" property="entpNo" />
    <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy" />
    <result column="CREATION_DATE" jdbcType="TIMESTAMP" property="creationDate" />
    <result column="LAST_UPDATED_BY" jdbcType="VARCHAR" property="lastUpdatedBy" />
    <result column="LAST_UPDATE_DATE" jdbcType="TIMESTAMP" property="lastUpdateDate" />
    <result column="ENABLED_FLAG" jdbcType="DECIMAL" property="enabledFlag" />
    <result column="STOCK_ATTR" jdbcType="VARCHAR" property="stockAttr" />
    <result column="STOCK_ATTR2" jdbcType="VARCHAR" property="stockAttr2" />
    <result column="IS_FACTOBJECT" jdbcType="DECIMAL" property="isFactobject" />
    <result column="IS_MRP" jdbcType="DECIMAL" property="isMrp" />
    <result column="MRP_PERCENT" jdbcType="DECIMAL" property="mrpPercent" />
    <result column="IS_G" jdbcType="DECIMAL" property="isG" />
    <result column="IS_REMAIN" jdbcType="DECIMAL" property="isRemain" />
    <result column="IS_ENTP_AVAILABLE" jdbcType="DECIMAL" property="isEntpAvailable" />
    <result column="IS_MNGEDBYPROD" jdbcType="DECIMAL" property="isMngedbyprod" />
    <result column="ORGANIZATION_ID" jdbcType="VARCHAR" property="organizationId" />
    <result column="SUBINV_CODE" jdbcType="VARCHAR" property="subinvCode" />
    <result column="IS_MNGEDBYBAR" jdbcType="DECIMAL" property="isMngedbybar" />
    <result column="IS_POSITION" jdbcType="DECIMAL" property="isPosition" />
    <result column="IS_INFOR" jdbcType="DECIMAL" property="isInfor" />
    <result column="STOCK_USE" jdbcType="DECIMAL" property="stockUse" />
    <result column="IS_MNGBYAREA" jdbcType="DECIMAL" property="isMngbyarea" />
  </resultMap>

  <sql id="Base_Column_List">
    STOCK_NO, STOCK_NAME, ATTRIBUTE, ENTP_NO, CREATED_BY, CREATION_DATE, LAST_UPDATED_BY, 
    LAST_UPDATE_DATE, ENABLED_FLAG, STOCK_ATTR, STOCK_ATTR2, IS_FACTOBJECT, IS_MRP, MRP_PERCENT, 
    IS_G, IS_REMAIN, IS_ENTP_AVAILABLE, IS_MNGEDBYPROD, ORGANIZATION_ID, SUBINV_CODE, 
    IS_MNGEDBYBAR, IS_POSITION, IS_INFOR, STOCK_USE, IS_MNGBYAREA
  </sql>

  <select id="selectStStockAll" parameterType="com.zte.interfaces.stepdt.dto.StStockDTO" resultMap="BaseResultMap">
    select STOCK_NO, STOCK_NAME, ATTRIBUTE, ENTP_NO, CREATED_BY, CREATION_DATE, LAST_UPDATED_BY, 
    LAST_UPDATE_DATE, ENABLED_FLAG, STOCK_ATTR, STOCK_ATTR2, IS_FACTOBJECT, IS_MRP, MRP_PERCENT, 
    IS_G, IS_REMAIN, IS_ENTP_AVAILABLE, IS_MNGEDBYPROD, ORGANIZATION_ID, SUBINV_CODE, 
    IS_MNGEDBYBAR, IS_POSITION, IS_INFOR, STOCK_USE, IS_MNGBYAREA
    from ST_STOCK
    where IS_INFOR = 1 AND ENABLED_FLAG = 1
    <if test="stockNo != null">
        and STOCK_NO = #{stockNo,jdbcType=VARCHAR}
      </if>
  </select>

  <select id="selectStStockById" parameterType="com.zte.interfaces.stepdt.dto.StStockDTO" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ST_STOCK
    where IS_INFOR = 1 AND ENABLED_FLAG = 1 and STOCK_NO = #{stockNo,jdbcType=VARCHAR}
  </select> 
  
</mapper>
