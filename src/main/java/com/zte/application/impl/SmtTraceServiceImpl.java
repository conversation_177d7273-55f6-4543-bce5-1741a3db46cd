package com.zte.application.impl;

import com.alibaba.fastjson.JSONObject;
import com.zte.application.BSmtBomDetailService;
import com.zte.application.ParentsnAndSnService;
import com.zte.application.PkCodeInfoService;
import com.zte.application.PsScanHistoryService;
import com.zte.application.SmtMachineMaterialMoutingService;
import com.zte.application.SmtSnMtlTracingTService;
import com.zte.application.SmtTraceService;
import com.zte.application.WipScanHistoryService;
import com.zte.common.CommonUtils;
import com.zte.common.DateUtil;
import com.zte.common.utils.Constant;
import com.zte.common.utils.GenerateUUIDUtil;
import com.zte.common.utils.MpConstant;
import com.zte.common.utils.NumConstant;
import com.zte.common.utils.ObtainRemoteServiceDataUtil;
import com.zte.consts.CommonConst;
import com.zte.domain.model.BSmtBomDetail;
import com.zte.domain.model.PkCodeInfo;
import com.zte.domain.model.SmtMachineMaterialMouting;
import com.zte.domain.model.SmtSnMtlTracingT;
import com.zte.domain.model.SmtSnMtlTracingTRepository;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.EqpmgmtsRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.EmEqpInteractiveDTO;
import com.zte.interfaces.dto.EmEqpPdcountDTO;
import com.zte.interfaces.dto.EmPcbItemInfoDetailDTO;
import com.zte.interfaces.dto.PkCodeInfoDTO;
import com.zte.interfaces.dto.PsWorkOrderDTO;
import com.zte.interfaces.dto.SmtLocationInfoDTO;
import com.zte.interfaces.dto.SysLookupValuesDTO;
import com.zte.interfaces.dto.TraceCalDTO;
import com.zte.interfaces.dto.TraceFixDTO;
import com.zte.interfaces.dto.TraceQtyDTO;
import com.zte.interfaces.dto.UpdatePkCodeQtyDTO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
public class SmtTraceServiceImpl implements SmtTraceService {

    @Autowired
    private ParentsnAndSnService parentsnAndSnService;

    @Autowired
    private SmtMachineMaterialMoutingService smtMachineMaterialMoutingService;

    @Autowired
    private BSmtBomDetailService bSmtBomDetailService;

    @Autowired
    private PkCodeInfoService pkCodeInfoService;

    @Autowired
    private SmtSnMtlTracingTService smtSnMtlTracingTService;

    @Autowired
    private SmtSnMtlTracingTRepository smtSnMtlTracingTRepository;

    @Autowired
    private PsScanHistoryService psScanHistoryService;

    @Autowired
    private EqpmgmtsRemoteService eqpmgmtsRemoteService;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private WipScanHistoryService wipScanHistoryService;

    // 是否正在切换
    @Value("${trace.new.changing:Y}")
    private String changing;

    private static final Logger LOG = LoggerFactory.getLogger(SmtTraceServiceImpl.class);

    private static String XNSN_QUEUE_PREFIX = "QUEUE:XNSN:";
    private static String SPI_QUEUE_PREFIX = "QUEUE:SPI:";

    @Override
    public void traceCal(TraceCalDTO traceCalDTO) {
        if (StringUtils.isEmpty(traceCalDTO.getSn()) && Constant.FLAG_Y.equals(changing)) {
            // 条码为空,且正在进行追溯切换时。查询扫描历史数据获取条码。切换一段时间后作废
            this.fixDataByScanHistory(traceCalDTO);
        }
        String virtualMachineNo = this.getVirtualMachineNo(traceCalDTO.getFactoryId());
        // 获取机台在用
        List<SmtMachineMaterialMouting> mountingList = this.getMountingList(traceCalDTO, virtualMachineNo);
        if (CollectionUtils.isEmpty(mountingList)) {
            return;
        }
        // 获取指令信息
        PsWorkOrderDTO workOrder = PlanscheduleRemoteService.getBasicWorkerOrderInfo(traceCalDTO.getWorkOrderNo());
        if (workOrder == null || StringUtils.isEmpty(workOrder.getCfgHeaderId())) {
            return;
        }
        traceCalDTO.setWorkOrder(workOrder);
        traceCalDTO.setProdplanId(workOrder.getSourceTask());
        traceCalDTO.setBomNo(workOrder.getItemNo());
        // 根据isSubSn设置条码
        this.setSn(traceCalDTO);
        // 获取上料表数据
        List<BSmtBomDetail> bomDetailList = this.getBomDetailList(workOrder.getCfgHeaderId(), traceCalDTO, virtualMachineNo);
        if (CollectionUtils.isEmpty(bomDetailList)) {
            return;
        }
        // 获取上料表用量数据map
        Map<String, Integer> locQtyMap = this.getFullLocQtyMap(bomDetailList);
        // 筛选出所有虚拟模组站位
        Set<String> virtualLocationNoSet = this.getVirtualLocationNo(bomDetailList, traceCalDTO.getLineCode());
        // 根据线体、指令、机台获取一小时内未计算的抛料数据
        List<EmEqpPdcountDTO> pdCountList = this.getPdCountList(traceCalDTO);
        // 待更新pkCode数量
        List<UpdatePkCodeQtyDTO> updatePkCodeQtyList = new ArrayList<>();
        // 待新增追溯数据
        List<SmtSnMtlTracingT> tracingTList = new ArrayList<>();
        // 待更新抛料数据
        List<EmEqpPdcountDTO> updatePdCountList = new ArrayList<>();
        // 预警信息
        List<EmEqpInteractiveDTO> eqpInteractiveList = new ArrayList<>();
        // 遍历计算追溯
        for (SmtMachineMaterialMouting mounting : mountingList) {
            if (!this.checkData(mounting)) {
                continue;
            }
            // 机台 + 工站在上料表唯一
            String fullLocation = mounting.getMachineNo() + "--" + mounting.getLocationNo();
            // 是否虚拟站位
            boolean virtualFlag = virtualLocationNoSet.contains(fullLocation);
            // 获取上料表用量
            Integer bomQty = locQtyMap.get(fullLocation);
            if (bomQty == null) { continue; }
            mounting.setBomQty(bomQty);
            // 抛料数量
            int pdCount = this.getEqpCount(mounting, pdCountList, updatePdCountList);
            // 是否需要更新pk_code数量及在数量不足时进行接料操作
            boolean needUpdatePkCode = this.needUpdatePkcodeQty(traceCalDTO.isGetEqpQty(), virtualFlag);
            if (needUpdatePkCode) {
                updatePkCodeQtyList.addAll(this.calQtyAndDealTrace(traceCalDTO, mounting, pdCount, tracingTList, eqpInteractiveList));
            } else {
                // 创建追溯数据
                TraceQtyDTO traceQtyDTO = new TraceQtyDTO(bomQty, pdCount, bomQty, mounting.getQty().intValue());
                // 组装追溯数据
                tracingTList.add(this.packTracingInfo(traceCalDTO, traceQtyDTO, mounting));
            }
        }
        // 插入追溯数据
        smtSnMtlTracingTService.insertBatch(tracingTList);
        // 批量更新pkCode数量
        pkCodeInfoService.batchUpdateQty(updatePkCodeQtyList);
        // 更新抛料数据状态
        this.updatePdCount(updatePdCountList);
        // 写预警信息
        this.insertEqpInteractive(eqpInteractiveList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int smtTraceCal(TraceCalDTO traceCalDTO) {
        if (Constant.SMT_TRACE_PRODSTART.equals(traceCalDTO.getEventType())) {
            return startSmtTraceCal(traceCalDTO);
        } else if (Constant.SMT_TRACE_PRODEND.equals(traceCalDTO.getEventType())) {
            return endSmtTraceCal(traceCalDTO);
        }
        return Constant.INT_0;
    }

    /**
     * 单板出贴片机事件后处理追溯
     * 如果进贴片机时物料不够。出贴片机时从临时追溯表取出追溯记录补充完整写入追溯表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int endSmtTraceCal(TraceCalDTO traceCalDTO) {
        // 物料详情
        List<EmPcbItemInfoDetailDTO> pcbTtemDetails = traceCalDTO.getPcbTtemDetails();
        if (CollectionUtils.isEmpty(pcbTtemDetails)) {
            return Constant.INT_0;
        }
        String parentsn = getParentsn(traceCalDTO);
        List<SmtSnMtlTracingT> endTempList = smtSnMtlTracingTRepository.getTracingProdend(parentsn);
        Map<String, SmtSnMtlTracingT> endTempListMap = endTempList.stream().filter(p -> StringUtils.isNotEmpty(p.getLocationNo()))
                .collect(Collectors.toMap(SmtSnMtlTracingT::getLocationNo, v -> v, (oldVal, newVal) -> newVal));
        // 待新增追溯数据
        List<SmtSnMtlTracingT> tracingTList = new ArrayList<>();
        for (EmPcbItemInfoDetailDTO pcbDetail : pcbTtemDetails) {
            if (!checkPcbDetail(pcbDetail)) {
                continue;
            }
            SmtSnMtlTracingT endTemp = endTempListMap.get(pcbDetail.getLocationNo());
            if (endTemp == null) {
                continue;
            }
            endTemp.setObjectId(pcbDetail.getReelId());
            endTemp.setSnScanDate(pcbDetail.getCreateDate());
            endTemp.setItemCode(pcbDetail.getItemNo());
            endTemp.setFeederNo(pcbDetail.getFeederNo());
            endTemp.setMaterialDate(pcbDetail.getCreateDate());
            endTemp.setCreateDate(new Date());
            endTemp.setMaterialBy(pcbDetail.getCreateBy());
            endTemp.setEnabledFlag(CommonConst.ENABLE_FLAG_Y);
            endTemp.setHandleType(CommonConst.HANDLE_TYPE_SCAN);
            // pk_code剩余数量
            endTemp.setAttribute4(String.valueOf(pcbDetail.getQty()));
            tracingTList.add(endTemp);
            // 从临时表删除
            smtSnMtlTracingTRepository.deleteSmtSnMtlTracingTById(Constant.SMT_TRACE_PRODEND, endTemp);
        }
        //设置220条码
        setSourceBatchCode(tracingTList, pcbTtemDetails);
        // 插入追溯数据
        return smtSnMtlTracingTService.insertBatch(tracingTList);
    }

    private void setSourceBatchCode(List<SmtSnMtlTracingT> tracingTList, List<EmPcbItemInfoDetailDTO> pcbTtemDetails) {
        //根据reelId批量查询料盘信息
        String reelIdsString = pcbTtemDetails.stream()
                .map(EmPcbItemInfoDetailDTO::getReelId)
                .filter(Objects::nonNull) // 排除null的reelId
                .map(reelId -> Constant.QUO_MARK + reelId + Constant.QUO_MARK)
                .collect(Collectors.joining(Constant.SYMBOL_COMMA));
        PkCodeInfoDTO pkCodeInfoDTO = new PkCodeInfoDTO();
        pkCodeInfoDTO.setInPkCode(reelIdsString);
        List<PkCodeInfo> pkCodeInfoList = pkCodeInfoService.getList(pkCodeInfoDTO);
        if (CollectionUtils.isEmpty(pkCodeInfoList)) {
            return;
        }
        //循环更新220条码
        Map<String, PkCodeInfo> pkCodeInfoMap = pkCodeInfoList.stream()
                .collect(Collectors.toMap(
                        PkCodeInfo::getPkCode,
                        pkCodeInfo -> pkCodeInfo
                ));
        for (SmtSnMtlTracingT tracingT : tracingTList) {
            String objectId = tracingT.getObjectId();
            PkCodeInfo pkCodeInfo = pkCodeInfoMap.get(objectId);
            if (pkCodeInfo != null) {
                tracingT.setSourceBatchCode(pkCodeInfo.getSourceBatchCode());
            }
        }

    }
    /**
     * 单板进入贴片机事件后处理追溯
     * 如果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int startSmtTraceCal(TraceCalDTO traceCalDTO) {
        int res = Constant.INT_0;
        // 物料详情
        List<EmPcbItemInfoDetailDTO> pcbTtemDetails = traceCalDTO.getPcbTtemDetails();
        if (CollectionUtils.isEmpty(pcbTtemDetails)) {
            return res;
        }
        // 获取指令信息
        PsWorkOrderDTO workOrder = PlanscheduleRemoteService.getBasicWorkerOrderInfo(traceCalDTO.getWorkOrderNo());
        if (workOrder == null || StringUtils.isEmpty(workOrder.getCfgHeaderId())) {
            return res;
        }
        traceCalDTO.setWorkOrder(workOrder);
        traceCalDTO.setProdplanId(workOrder.getSourceTask());
        traceCalDTO.setBomNo(workOrder.getItemNo());
        // 获取大板码
        getParentsn(traceCalDTO);
        // 获取上料表数据
        List<BSmtBomDetail> bomDetailList = bSmtBomDetailService.getLocationAndQty(workOrder.getCfgHeaderId(), null);
        // 获取上料表用量数据map LocationNo -> Qty
        Map<String, Integer> locQtyMap = bomDetailList.stream().filter(p -> p.getQty() != null)
                .collect(Collectors.toMap(BSmtBomDetail::getLocationNo, v -> v.getQty().intValue(), (oldVal, newVal) -> newVal));
        if (CollectionUtils.isEmpty(locQtyMap)) {
            return res;
        }
        // 待新增追溯数据
        List<SmtSnMtlTracingT> tracingTList = new ArrayList<>();
        // 续料盘的追溯数据，等出贴片机事件补充完整reelId信息
        List<SmtSnMtlTracingT> tracingTEndList = new ArrayList<>();
        // 遍历计算追溯
        Date now = new Date();
        for (EmPcbItemInfoDetailDTO pcbDetail : pcbTtemDetails) {
            if (!checkPcbDetail(pcbDetail)) {
                continue;
            }
            // 获取上料表用量
            Integer bomQty = locQtyMap.get(pcbDetail.getLocationNo());
            if (bomQty == null) {
                continue;
            }
            // 组装追溯数据
            SmtSnMtlTracingT tracing = buildTracingInfo(traceCalDTO, pcbDetail);
            tracing.setCfgHeaderId(workOrder.getCfgHeaderId());
            tracing.setCreateDate(now);
            long leftQty = pcbDetail.getQty();
            long actualQty = bomQty;
            // bom用量
            tracing.setAttribute3(String.valueOf(bomQty));
            // pk_code剩余数量
            tracing.setAttribute4(String.valueOf(leftQty));
            if (leftQty < bomQty) {
                actualQty = leftQty;
                SmtSnMtlTracingT tracingEnd = new SmtSnMtlTracingT();
                BeanUtils.copyProperties(tracing, tracingEnd);
                tracingEnd.setRecordId(GenerateUUIDUtil.generateUUIDBySuffix(DateUtil.getCurYearQuarter()));
                tracingEnd.setAttribute1(String.valueOf(bomQty - leftQty));
                tracingTEndList.add(tracingEnd);
            }
            // 实际用量
            tracing.setAttribute1(String.valueOf(actualQty));
            tracingTList.add(tracing);
        }
        //设置220条码
        setSourceBatchCode(tracingTList, pcbTtemDetails);
        setSourceBatchCode(tracingTEndList, pcbTtemDetails);

        smtSnMtlTracingTRepository.insertBatch(Constant.SMT_TRACE_PRODEND, tracingTEndList);
        // 插入追溯数据
        return smtSnMtlTracingTService.insertBatch(tracingTList);
    }

    /**
     * 获取并设置大板条码
     */
    private String getParentsn(TraceCalDTO traceCalDTO) {
        // 获取大板码
        String parentsn = parentsnAndSnService.getParentsn(traceCalDTO.getSn());
        if (StringUtils.isNotEmpty(parentsn)) {
            traceCalDTO.setSn(parentsn);
        }
        return traceCalDTO.getSn();
    }

    /**
     * 校验并修改LocationNo格式
     */
    private boolean checkPcbDetail(EmPcbItemInfoDetailDTO pcbDetail) {
        if (StringUtils.isEmpty(pcbDetail.getModuleNo()) || StringUtils.isEmpty(pcbDetail.getLocationNo()) || pcbDetail.getQty() == null) {
            return false;
        }
        pcbDetail.setLocationNo(pcbDetail.getLocationNo());
        return true;
    }

    private SmtSnMtlTracingT buildTracingInfo(TraceCalDTO traceCalDTO, EmPcbItemInfoDetailDTO pcbDetail) {
        SmtSnMtlTracingT tracing = new SmtSnMtlTracingT();
        tracing.setRecordId(GenerateUUIDUtil.generateUUIDBySuffix(DateUtil.getCurYearQuarter()));
        tracing.setLineCode(traceCalDTO.getLineCode());
        tracing.setWorkOrder(traceCalDTO.getWorkOrderNo());
        tracing.setProductBatchCode(traceCalDTO.getProdplanId());
        tracing.setProductCode(traceCalDTO.getBomNo());
        tracing.setSn(traceCalDTO.getSn());
        tracing.setSnScanDate(pcbDetail.getCreateDate());
        tracing.setObjectId(pcbDetail.getReelId());
        tracing.setItemCode(pcbDetail.getItemNo());
        tracing.setLocationNo(pcbDetail.getLocationNo());
        tracing.setFeederNo(pcbDetail.getFeederNo());
        tracing.setSourceBatchCode(pcbDetail.getSourceBatchCode());
        tracing.setMaterialDate(pcbDetail.getCreateDate());
        tracing.setMaterialBy(pcbDetail.getCreateBy());
        tracing.setEnabledFlag(CommonConst.ENABLE_FLAG_Y);
        tracing.setHandleType(CommonConst.HANDLE_TYPE_SCAN);
        tracing.setFactoryId(Long.parseLong(traceCalDTO.getFactoryId()));
        return tracing;
    }

    /**
     * 追溯表对应的sn为大板条码
     * 当前条码为小板条码时需通过小板条码找到对应大板条码
     * @param traceCalDTO
     */
    private void setSn(TraceCalDTO traceCalDTO) {
        if (!traceCalDTO.isSubSn()) {
            // 若sn不是小板无需修改
            return;
        }
        if (StringUtils.isNotBlank(traceCalDTO.getSn()) && StringUtils.isNotBlank(traceCalDTO.getTraceSource())) {
            String traceSource = traceCalDTO.getTraceSource();
            if (traceCalDTO.isSpiWithPmj()) {
                traceSource = Constant.TRACE_SOURCE_PMJ;
            }
            String parentSn = psScanHistoryService.getParentSnBySnAndSourceSys(traceCalDTO.getSn(), traceSource);
            if (StringUtils.isNotBlank(parentSn)) {
                traceCalDTO.setSn(parentSn);
            }
        }
    }

    private boolean checkData(SmtMachineMaterialMouting mounting) {
        return StringUtils.isNotEmpty(mounting.getMachineNo()) && StringUtils.isNotEmpty(mounting.getLocationNo()) && StringUtils.isNotEmpty(mounting.getObjectId()) && mounting.getQty() != null;
    }

    private void insertEqpInteractive(List<EmEqpInteractiveDTO> eqpInteractiveList) {
        if (CollectionUtils.isEmpty(eqpInteractiveList)) {
            return;
        }
        List<List<EmEqpInteractiveDTO>> listOfList = CommonUtils.splitList(eqpInteractiveList);
        for (List<EmEqpInteractiveDTO> insertList : listOfList) {
            ObtainRemoteServiceDataUtil.insertEmEqpInteractiveBatch(insertList);
        }
    }

    /**
     * 分批更新抛料数据状态
     * @param updatePdCountList
     */
    private void updatePdCount(List<EmEqpPdcountDTO> updatePdCountList) {
        if (CollectionUtils.isEmpty(updatePdCountList)) {
            return;
        }
        List<List<EmEqpPdcountDTO>> listOfList = CommonUtils.splitList(updatePdCountList);
        for (List<EmEqpPdcountDTO> list : listOfList) {
            EqpmgmtsRemoteService.batchUpdateEqpPdcountStatus(list);
        }
    }

    /**
     * 获取站位、物料对应抛料数
     * @param mounting
     * @param emEqpPdcountDTOList
     * @param updatePdCountList
     * @return 抛料数量
     */
    private int getEqpCount(SmtMachineMaterialMouting mounting,
                            List<EmEqpPdcountDTO> emEqpPdcountDTOList, List<EmEqpPdcountDTO> updatePdCountList) {
        if (CollectionUtils.isEmpty(emEqpPdcountDTOList)) {
            return NumConstant.NUM_ZERO;
        }
        String fullLocation = mounting.getMachineNo() + mounting.getLocationNo();
        // 获得抛料表中实体
        List<EmEqpPdcountDTO> pdCountListAtPosition = emEqpPdcountDTOList.stream().filter(tempEmEqpPdcount ->
                        fullLocation.equals(tempEmEqpPdcount.getFullStationPosition())
                                && mounting.getItemCode().equalsIgnoreCase(tempEmEqpPdcount.getMaterialCode()))
                .collect(Collectors.toList());
        int eqpCount = NumConstant.NUM_ZERO;
        for (EmEqpPdcountDTO item : pdCountListAtPosition) {
            eqpCount += item.getThrowsNumber().intValue();
            item.setStatus(Constant.FLAG_Y);
            updatePdCountList.add(item);
        }
        return eqpCount;
    }

    /**
     * 查询抛料数据
     * @param traceCalDTO
     * @return
     */
    private List<EmEqpPdcountDTO> getPdCountList(TraceCalDTO traceCalDTO) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date());
        cal.add(Calendar.HOUR_OF_DAY, -1);
        Date startQueryDate = cal.getTime();
        String startTimeStr = DateUtil.convertDateToString(startQueryDate, DateUtil.DATE_FORMATE_FULL);

        Map<String, Object> paramMapE = new HashMap<String, Object>();
        paramMapE.put("lineCode", traceCalDTO.getLineCode());
        paramMapE.put("workOrderNo", traceCalDTO.getWorkOrderNo());
        paramMapE.put("startTimeStr", startTimeStr);
        if (traceCalDTO.getCalInMachine()) {
            paramMapE.put("eqpModule", traceCalDTO.getMachineNo());
        }
        paramMapE.put("status", Constant.FLAG_N);
        List<EmEqpPdcountDTO> pdCountList = new ArrayList<>();
        try {
            pdCountList = eqpmgmtsRemoteService.getEpqPdcountInfo(paramMapE);
        } catch (Exception e) {
            // 查询抛料数据异常不应影响追溯计算
            LOG.error("查询抛料数据异常:" + JSONObject.toJSONString(traceCalDTO), e);
        }
        return pdCountList;
    }

    /**
     * 计算数量并创建追溯数据
     * @param mounting 机台在用数据
     * @param pdCount 抛料数量
     * @param tracingTList 新增追溯数据
     * @return 需更新数量的pkCode数据
     */
    private List<UpdatePkCodeQtyDTO> calQtyAndDealTrace(TraceCalDTO traceCalDTO, SmtMachineMaterialMouting mounting, int pdCount, List<SmtSnMtlTracingT> tracingTList, List<EmEqpInteractiveDTO> eqpInteractiveList) {
        List<UpdatePkCodeQtyDTO> updatePkCodeList = new ArrayList<>();
        // 上料表用量
        int bomQty = mounting.getBomQty();
        // 扣数
        int finalQty = mounting.getQty().intValue() - bomQty - pdCount;

        if (finalQty <= NumConstant.NUM_ZERO) {
            // 当前reelId已使用完
            updatePkCodeList.add(new UpdatePkCodeQtyDTO(mounting.getObjectId(), NumConstant.NUM_ZERO));
            // 取reelId当前数量和bom用量中较小的值作为实际用量
            int actualQty = getActualQty(mounting, bomQty);
            // 计算到当前reelId的抛料数
            int pdCountOfReelId = getPdCountOfReelId(mounting, bomQty);
            // 组装追溯数量数据
            TraceQtyDTO traceQtyDTO = new TraceQtyDTO(actualQty, pdCountOfReelId, bomQty, NumConstant.NUM_ZERO);
            // 组装追溯数据
            tracingTList.add(this.packTracingInfo(traceCalDTO, traceQtyDTO, mounting));

            // 扣数后数量不足且NextReelId不为空时执行接料操作
            if (StringUtils.isNotBlank(mounting.getNextReelRowid())) {
                PkCodeInfo nextPkCodeInfo = pkCodeInfoService.getPkCodeInfoByCode(mounting.getNextReelRowid());
                if (nextPkCodeInfo == null || nextPkCodeInfo.getItemQty() == null) {
                    return updatePkCodeList;
                }
                int nextReelIdQty = nextPkCodeInfo.getItemQty().intValue();

                if (finalQty < NumConstant.NUM_ZERO) {
                    // 上盘reelId数量不足，需继续扣减续料盘数量,并再写一份追溯。若数量刚好足够则无需再写追溯及扣续料盘数量
                    nextReelIdQty = getNextReelIdQty(finalQty, nextReelIdQty);
                    nextPkCodeInfo.setItemQty(new BigDecimal(nextReelIdQty));
                    updatePkCodeList.add(new UpdatePkCodeQtyDTO(nextPkCodeInfo.getPkCode(), nextReelIdQty));
                    // 续料盘实际在单板上消耗的数量(若续料盘数量仍不足为异常场景,不再额外考虑)
                    actualQty = getNextReelIdActualQty(bomQty, actualQty);
                    pdCountOfReelId = pdCount - pdCountOfReelId;
                    traceQtyDTO = new TraceQtyDTO(actualQty, pdCountOfReelId, bomQty, nextReelIdQty);
                    // 组装追溯数据
                    tracingTList.add(this.packTracingInfo(traceCalDTO, traceQtyDTO, mounting));
                }
                // 接料，更新机台在用并删除备料
                smtMachineMaterialMoutingService.receiveReelId(mounting, nextPkCodeInfo);
            } else {
                // 数量小于等于0且续料盘为空时写预警信息
                eqpInteractiveList.add(this.packEmEqpInteractiveDTO(traceCalDTO, mounting));
            }
        } else {
            // 数量足够且有剩余,扣数 + 写追溯
            updatePkCodeList.add(new UpdatePkCodeQtyDTO(mounting.getObjectId(), finalQty));
            // 组装追溯数量数据
            TraceQtyDTO traceQtyDTO = new TraceQtyDTO(bomQty, pdCount, bomQty, finalQty);
            // 组装追溯数据
            tracingTList.add(this.packTracingInfo(traceCalDTO, traceQtyDTO, mounting));
        }
        return updatePkCodeList;
    }

    /**
     * 计算到当前reelId的抛料数
     * @param mounting
     * @param bomQty
     * @return
     */
    private int getPdCountOfReelId(SmtMachineMaterialMouting mounting, int bomQty) {
        return mounting.getQty().intValue() > bomQty ? mounting.getQty().intValue() - bomQty : 0;
    }

    /**
     * 料盘实际用量
     * @param mounting
     * @param bomQty
     * @return
     */
    private int getActualQty(SmtMachineMaterialMouting mounting, int bomQty) {
        return mounting.getQty().intValue() > bomQty ? bomQty : mounting.getQty().intValue();
    }

    /**
     * 获取续料盘实际扣数数量
     * @param bomQty 上料表用量
     * @param actualQty 原料盘实际用量
     * @return
     */
    private int getNextReelIdActualQty(int bomQty, int actualQty) {
        return actualQty >= bomQty ? NumConstant.NUM_ZERO : bomQty - actualQty;
    }

    /**
     * 获取续料盘扣数后数量
     * @param finalQty
     * @param nextReelIdQty
     * @return
     */
    private int getNextReelIdQty(int finalQty, int nextReelIdQty) {
        return nextReelIdQty + finalQty > 0 ? nextReelIdQty + finalQty : NumConstant.NUM_ZERO;
    }

    private EmEqpInteractiveDTO packEmEqpInteractiveDTO(TraceCalDTO dto, SmtMachineMaterialMouting mounting)  {
        PsWorkOrderDTO workOrder = dto.getWorkOrder();
        EmEqpInteractiveDTO interactiveDTO = new EmEqpInteractiveDTO();
        interactiveDTO.setLineCode(dto.getLineCode());
        interactiveDTO.setWorkOrderNo(dto.getWorkOrderNo());
        interactiveDTO.setFactoryId(new BigDecimal(dto.getFactoryId()));
        interactiveDTO.setWorkshopCode(workOrder.getWorkshopCode());
        interactiveDTO.setProcessCode(workOrder.getProcessCode());
        String workStation = workOrder.getProcessCode() + MpConstant.SCAN_HISTORY_REMARK_ONE;
        interactiveDTO.setWorkStation(workStation);
        interactiveDTO.setLocationNo(mounting.getLocationNo());
        interactiveDTO.setMsgType(MpConstant.EM_INTERIVE_MSGTYPE_TWO);
        interactiveDTO.setSendProgram(MpConstant.EM_INTERIVE_SEND_PROGRAM_LOW);
        interactiveDTO.setRemark(mounting.getMachineNo() + mounting.getLocationNo() + " " + mounting.getItemCode());
        interactiveDTO.setOrgId(mounting.getOrgId());
        interactiveDTO.setCreateDate(new Date());
        interactiveDTO.setUpdatedDate(new Date());
        interactiveDTO.setEnabledFlag(Constant.FLAG_Y);
        // 指令 停机0， 开机1 告警 2
        interactiveDTO.setCommander(MpConstant.EM_INTERIVE_COMMAND_ZERO);
        interactiveDTO.setRemark(interactiveDTO.getRemark() + " " + MpConstant.REMARK_SMT_SCAN_MATERIAL_DOWN);
        return interactiveDTO;
    }


    private SmtSnMtlTracingT packTracingInfo(TraceCalDTO traceCalDTO, TraceQtyDTO traceQtyDTO, SmtMachineMaterialMouting mounting) {
        SmtSnMtlTracingT tracing = new SmtSnMtlTracingT();
        tracing.setRecordId(GenerateUUIDUtil.generateUUIDBySuffix(DateUtil.getCurYearQuarter()));
        tracing.setLineCode(traceCalDTO.getLineCode());
        tracing.setWorkOrder(traceCalDTO.getWorkOrderNo());
        // 生产批次
        tracing.setProductBatchCode(traceCalDTO.getProdplanId());
        tracing.setProductCode(traceCalDTO.getBomNo());
        tracing.setSn(traceCalDTO.getSn());
        tracing.setSnScanDate(new Date());
        tracing.setObjectId(mounting.getObjectId());
        tracing.setItemCode(mounting.getItemCode());
        tracing.setLocationNo(mounting.getLocationNo());
        tracing.setFeederNo(mounting.getFeederNo());
        // 物料批次 220
        tracing.setSourceBatchCode(mounting.getSourceBatchCode());
        // 上料时间
        tracing.setMaterialDate(mounting.getCreateDate());
        tracing.setMaterialBy(mounting.getCreateUser());
        tracing.setCfgHeaderId(mounting.getCfgHeaderId());
        tracing.setCreateDate(new Date());
        tracing.setEnabledFlag(CommonConst.ENABLE_FLAG_Y);
        tracing.setHandleType(CommonConst.HANDLE_TYPE_SCAN);
        tracing.setFactoryId(Long.parseLong(traceCalDTO.getFactoryId()));
        // 实际用量
        tracing.setAttribute1(String.valueOf(traceQtyDTO.getActualQty()));
        // 抛料数量
        tracing.setAttribute2(String.valueOf(traceQtyDTO.getPdCount()));
        // bom用量
        tracing.setAttribute3(String.valueOf(traceQtyDTO.getBomQty()));
        // pk_code剩余数量
        tracing.setAttribute4(String.valueOf(traceQtyDTO.getPkCodeQty()));
        if (StringUtils.isBlank(traceCalDTO.getSn()) && traceCalDTO.getSeqId() != null) {
            // 追溯修复key(SPI延迟上传修复场景使用)
            tracing.setAttribute5(String.valueOf(traceCalDTO.getSeqId()));
        }
        return tracing;
    }

    /**
     * 校验是否需要更新pkCode数量
     * @param getEqpQty 线体reelID数量同步标识(获取设备物料消耗为true)
     * @param virtualFlag 是否虚拟站位
     * @return
     */
    private boolean needUpdatePkcodeQty(boolean getEqpQty, boolean virtualFlag) {
        return virtualFlag || !getEqpQty;
    }

    /**
     * 筛选得到所有虚拟站位
     *
     * @param bomDetailList
     * @param lineCode
     * @return
     */
    public Set<String> getVirtualLocationNo(List<BSmtBomDetail> bomDetailList, String lineCode) {
        Set<String> set = new HashSet<>();
        List<SmtLocationInfoDTO> locationList = new ArrayList<>();
        try {
            for (BSmtBomDetail bSmtBomDetail : bomDetailList) {
                if (Constant.FLAG_Y.equalsIgnoreCase(bSmtBomDetail.getVirtualFlag())) {
                    String locationStr = bSmtBomDetail.getMachineNo() + "--" + bSmtBomDetail.getLocationNo();
                    set.add(locationStr);
                    continue;
                }
                SmtLocationInfoDTO location = new SmtLocationInfoDTO();
                BeanUtils.copyProperties(bSmtBomDetail, location);
                location.setLineCode(lineCode);
                location.setLocationType(NumConstant.STR_TWO);
                locationList.add(location);
            }

            // 获取托盘类型站位(站位类型 LocationType=2), 处理逻辑同虚拟站位
            List<String> trayLocations = BasicsettingRemoteService.getListByLocationType(locationList);
            if (!CollectionUtils.isEmpty(trayLocations)) {
                set.addAll(trayLocations);
            }
        } catch (Exception e) {
            LOG.error("查询虚拟站位异常:" + JSONObject.toJSONString(locationList), e);
        }
        return set;
    }

    /**
     * 获取上料表数据
     * @param cfgHeaderId
     * @param traceCalDTO
     * @param virtualMachineNo
     * @return
     */
    private List<BSmtBomDetail> getBomDetailList(String cfgHeaderId, TraceCalDTO traceCalDTO, String virtualMachineNo) {
        List<String> machineNoList = this.getMachineNoList(traceCalDTO, virtualMachineNo);
        return bSmtBomDetailService.getLocationAndQty(cfgHeaderId, machineNoList);
    }

    /**
     * 获取站位用量map
     * @param bomDetailList
     * @return
     */
    private Map<String, Integer> getFullLocQtyMap(List<BSmtBomDetail> bomDetailList) {
        Map<String, Integer> map = new HashMap<>();
        for (BSmtBomDetail bSmtBomDetail : bomDetailList) {
            if (bSmtBomDetail.getQty() == null) {
                continue;
            }
            String fullLocationStr = bSmtBomDetail.getMachineNo() + "--" + bSmtBomDetail.getLocationNo();
            int qty = bSmtBomDetail.getQty().intValue();
            map.put(fullLocationStr, qty);
        }
        return map;
    }

    /**
     * 组装机台参数
     * @param traceCalDTO
     * @param virtualMachineNo
     * @return
     */
    private List<String> getMachineNoList(TraceCalDTO traceCalDTO, String virtualMachineNo) {
        List<String> machineNoList = new ArrayList<>();
        if (traceCalDTO.getCalInMachine()) {
            // 若是最后机台,需一起获取虚拟机台的机台在用数据
            machineNoList.add(traceCalDTO.getMachineNo());
            if (traceCalDTO.getIsEndBoard() && StringUtils.isNotEmpty(virtualMachineNo)) {
                machineNoList.add(virtualMachineNo);
            }
        }
        return machineNoList;
    }

    /**
     * 查询机台在用数据
     * 按机台精确扣数时加上机台进行筛选,且是最后模组时加上虚拟模组一起筛选
     * 不是按机台精确扣数时直接查询线体、指令下所有机台在用数据
     * @param traceCalDTO
     * @param virtualMachineNo
     * @return
     */
    private List<SmtMachineMaterialMouting> getMountingList(TraceCalDTO traceCalDTO, String virtualMachineNo) {
        List<String> machineNoList = this.getMachineNoList(traceCalDTO, virtualMachineNo);
        return smtMachineMaterialMoutingService.getMoutingWithPkCode(traceCalDTO.getLineCode(), traceCalDTO.getWorkOrderNo(), machineNoList);
    }

    /**
     * 获取虚拟模组
     * @param factoryId
     * @return
     */
    private String getVirtualMachineNo(String factoryId) {
        String virtualMachineNo = Constant.STR_EMPTY;
        String prefix = Constant.STR_EMPTY;
        // 首先查询redis获取虚拟模组。
        String redisKey = Constant.VIRTUAL_MACHINE_NO + factoryId;
        try {
            virtualMachineNo = redisTemplate.opsForValue().get(redisKey);
            if (StringUtils.isNotBlank(virtualMachineNo)) {
                return virtualMachineNo;
            }
        } catch (Exception e) {
            LOG.error("连接redis异常");
        }

        // redis不存在查询数据字典
        List<SysLookupValuesDTO> values = BasicsettingRemoteService.getLookupValueByTypeCodes(Constant.LOOKUP_6104);
        if (CollectionUtils.isEmpty(values)) {
            prefix = Constant.VIRTUAL_PREFIX;
        } else {
            prefix = StringUtils.defaultIfBlank(values.get(Constant.INT_0).getLookupMeaning(), Constant.VIRTUAL_PREFIX);
        }
        virtualMachineNo = prefix + Constant.V_MACHINE_NO;
        // 存入redis。过期时间10分钟
        try {
            redisTemplate.opsForValue().setIfAbsent(redisKey, virtualMachineNo, NumConstant.NUM_TEN, TimeUnit.MINUTES);
        } catch (Exception e) {
            LOG.error("连接redis异常");
        }

        return virtualMachineNo;
    }

    /**
     * 条码为空,且正在进行追溯切换时。
     * 查询扫描历史数据获取条码数据
     * 获取成功后更新扫描历史并更新设备服务SPI扫描顺序信息
     * 待切换一段时间后可删除
     * 预计20230527上线
     * @param traceCalDTO
     */
    public void fixDataByScanHistory(TraceCalDTO traceCalDTO) {
        try {
            // 查询指令下source_sys为SPI的最早一条还未计算追溯的parentSn
            String parentSn = psScanHistoryService.getFirstNotCalParentSn(traceCalDTO.getWorkOrderNo(), Constant.TRACE_SOURCE_SPI);
            if (StringUtils.isNotEmpty(parentSn)) {
                traceCalDTO.setSn(parentSn);
                // 更新扫描历史IS_TRACE_CALCULATE为Y
                psScanHistoryService.updateIsTraceCalculate(Constant.FLAG_Y, traceCalDTO.getWorkOrderNo(), parentSn, Constant.TRACE_SOURCE_SPI);
                // 更新SPI检测顺序表,补全sn数据及更新assignFlag为Y
                EqpmgmtsRemoteService.updateSpiSeq(traceCalDTO.getSeqId(), traceCalDTO.getSn(), Constant.FLAG_Y);
            }
        } catch (Exception e) {
            LOG.error("处理切换过程数据异常：" + JSONObject.toJSONString(traceCalDTO), e);
        }
    }

    @Override
    public void traceFix(TraceFixDTO traceFixDTO) {
        if (StringUtils.isEmpty(traceFixDTO.getSn()) || traceFixDTO.getSeqId() == null) {
            return;
        }

        if (traceFixDTO.isSubSn()) {
            String traceSource = traceFixDTO.getTraceSource();
            if (traceFixDTO.isSpiWithPmj()) {
                traceSource = Constant.TRACE_SOURCE_PMJ;
            }
            String parentSn = psScanHistoryService.getParentSnBySnAndSourceSys(traceFixDTO.getSn(), traceSource);
            traceFixDTO.setSn(parentSn);
        }

        // 按分区更新追溯数据
        this.updateTraceData(traceFixDTO.getSeqId(), traceFixDTO.getWorkOrderNo(), traceFixDTO.getSn());

    }

    /**
     * 按分区更新追溯数据
     * 最多更新30天内数据(可配置)
     * @param seqId
     * @param workOrderNo
     * @param sn
     */
    private void updateTraceData(Long seqId, String workOrderNo, String sn) {
        Set<String> yearQuaters = this.getYearQuaters(NumConstant.NUM_THIRTY);
        // 循环更新追溯数据
        for (String yearQuater : yearQuaters) {
            smtSnMtlTracingTService.updateSnByWorkOrderAndAttr5(yearQuater, sn, workOrderNo, seqId.toString());
        }
    }

    /**
     * 获取指定天数前到当前时间包含的年+季度
     * 示例1：当前时间为20230522,days为30则返回["20232"]
     * 示例2：当前时间为20230405,days为30则返回["20232","20231"]
     * @param days
     * @return
     */
    private Set<String> getYearQuaters(int days) {
        Set<String> yearQuaters = new HashSet<>();
        Calendar cal = Calendar.getInstance();
        int quarter = DateUtil.getCurQuarter(cal);
        int year = cal.get(Calendar.YEAR);
        yearQuaters.add(year + Constant.STR_EMPTY + quarter);

        cal.add(Calendar.DATE,  days);
        quarter = DateUtil.getCurQuarter(cal);
        year = cal.get(Calendar.YEAR);
        yearQuaters.add(year + Constant.STR_EMPTY + quarter);
        return yearQuaters;
    }
}
