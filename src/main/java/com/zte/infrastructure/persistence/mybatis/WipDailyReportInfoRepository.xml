<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.WipDailyReportInfoRepository">
  <resultMap id="BaseResultMap" type="com.zte.domain.model.WipDailyReportInfo">
    <id column="DR_ID" jdbcType="VARCHAR" property="drId" />
    <result column="WORK_DAY" jdbcType="TIMESTAMP" property="workDay" />
    <result column="PRODPLAN_NO" jdbcType="VARCHAR" property="prodplanNo" />
    <result column="PRODPLAN_ID" jdbcType="VARCHAR" property="prodplanId" />
    <result column="ITEM_CODE" jdbcType="VARCHAR" property="itemCode" />
    <result column="CRAFT_SECTION" jdbcType="VARCHAR" property="craftSection" />
    <result column="PROCESS_CODE" jdbcType="VARCHAR" property="processCode" />
    <result column="WORK_STATION" jdbcType="VARCHAR" property="workStation" />
    <result column="PASS_QTY" jdbcType="DECIMAL" property="passQty" />
    <result column="NG_QTY" jdbcType="DECIMAL" property="ngQty" />
    <result column="PROCESS_SEQ" jdbcType="DECIMAL" property="processSeq" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
    <result column="LAST_UPDATED_BY" jdbcType="VARCHAR" property="lastUpdatedBy" />
    <result column="LAST_UPDATED_DATE" jdbcType="TIMESTAMP" property="lastUpdatedDate" />
    <result column="ENABLED_FLAG" jdbcType="VARCHAR" property="enabledFlag" />
    <result column="ORG_ID" jdbcType="DECIMAL" property="orgId" />
    <result column="FACTORY_ID" jdbcType="DECIMAL" property="factoryId" />
    <result column="ENTITY_ID" jdbcType="DECIMAL" property="entityId" />
    <result column="ATTRIBUTE1" jdbcType="VARCHAR" property="attribute1" />
    <result column="ATTRIBUTE2" jdbcType="VARCHAR" property="attribute2" />
    <result column="ATTRIBUTE3" jdbcType="VARCHAR" property="attribute3" />
    
    <result column="sn" jdbcType="VARCHAR" property="sn" />
    <result column="bill_no" jdbcType="VARCHAR" property="billNo" />
    <result column="line_code" jdbcType="VARCHAR" property="lineCode" />
    <result column="SMT_A" jdbcType="VARCHAR" property="smtA" />
    <result column="SMT_B" jdbcType="VARCHAR" property="smtB" />
    <result column="SMT" jdbcType="VARCHAR" property="smt" />
    <result column="DIP" jdbcType="VARCHAR" property="dip" />
    <result column="DBCS" jdbcType="VARCHAR" property="dbcs" />
    <result column="task_Qty" jdbcType="DECIMAL" property="taskQty" />
    <result column="leftQty" jdbcType="DECIMAL" property="leftQty" />
    <result column="onlineQty" jdbcType="DECIMAL" property="onlineQty" />
    <result column="repairQty" jdbcType="DECIMAL" property="repairQty" />
    <result column="in_qty" jdbcType="VARCHAR" property="inQty" />
    <result column="left_in_qty" jdbcType="VARCHAR" property="leftInQty" />
    <result column="work_Order_No" jdbcType="VARCHAR" property="workOrderNo" />
    <result column="ITEM_NAME" jdbcType="VARCHAR" property="itemName" />    
    <result column="SMT_DELIVER_QTY" jdbcType="VARCHAR" property="smtDeliverCount" />
    <result column="DIP_DELIVER_QTY" jdbcType="VARCHAR" property="dipDeliverCount" />
    <result column="ASS_DELIVER_QTY" jdbcType="VARCHAR" property="assDeliverCount" />
    <result column="EXTERNAL_TYPE" jdbcType="VARCHAR" property="externalType" />
    <result column="assembleQty" jdbcType="DECIMAL" property="assembleQty" />
    <result column="highTemperQty" jdbcType="DECIMAL" property="highTemperQty" />
    <result column="debugQty" jdbcType="DECIMAL" property="debugQty" />
    <result column="packQty" jdbcType="DECIMAL" property="packQty" />
    <result column="lsLineQty" jdbcType="DECIMAL" property="lsLineQty" /> 
    <result column="countWipPerProdplanId" jdbcType="DECIMAL" property="countWipPerProdplanId" />    
  </resultMap>
  
    <resultMap id="ResultMap" type="com.zte.interfaces.dto.WipDailyReportInfoDTO">
    <id column="DR_ID" jdbcType="VARCHAR" property="drId" />
    <result column="WORK_DAY" jdbcType="TIMESTAMP" property="workDay" />
    <result column="PRODPLAN_NO" jdbcType="VARCHAR" property="prodplanNo" />
    <result column="PRODPLAN_ID" jdbcType="VARCHAR" property="prodplanId" />
    <result column="ITEM_CODE" jdbcType="VARCHAR" property="itemCode" />
    <result column="CRAFT_SECTION" jdbcType="VARCHAR" property="craftSection" />
    <result column="PROCESS_CODE" jdbcType="VARCHAR" property="processCode" />
    <result column="WORK_STATION" jdbcType="VARCHAR" property="workStation" />
    <result column="PASS_QTY" jdbcType="DECIMAL" property="passQty" />
    <result column="NG_QTY" jdbcType="DECIMAL" property="ngQty" />
    <result column="PROCESS_SEQ" jdbcType="DECIMAL" property="processSeq" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
    <result column="LAST_UPDATED_BY" jdbcType="VARCHAR" property="lastUpdatedBy" />
    <result column="LAST_UPDATED_DATE" jdbcType="TIMESTAMP" property="lastUpdatedDate" />
    <result column="ENABLED_FLAG" jdbcType="VARCHAR" property="enabledFlag" />
    <result column="ORG_ID" jdbcType="DECIMAL" property="orgId" />
    <result column="FACTORY_ID" jdbcType="DECIMAL" property="factoryId" />
    <result column="ENTITY_ID" jdbcType="DECIMAL" property="entityId" />
    <result column="ATTRIBUTE1" jdbcType="VARCHAR" property="attribute1" />
    <result column="ATTRIBUTE2" jdbcType="VARCHAR" property="attribute2" />
    <result column="ATTRIBUTE3" jdbcType="VARCHAR" property="attribute3" />
    <result column="ITEM_NAME" jdbcType="VARCHAR" property="itemName" />
    
    <result column="work_Order_No" jdbcType="VARCHAR" property="workOrderNo" />
    <result column="SMT_A" jdbcType="VARCHAR" property="SMT_A" />
    <result column="SMT_B" jdbcType="VARCHAR" property="SMT_B" />
    <result column="SMT" jdbcType="VARCHAR" property="SMT" />
    <result column="DIP" jdbcType="VARCHAR" property="DIP" />
    <result column="DBCS" jdbcType="VARCHAR" property="DBCS" />
    <result column="task_Qty" jdbcType="DECIMAL" property="taskQty" />
    <result column="leftQty" jdbcType="DECIMAL" property="leftQty" />
    <result column="onlineQty" jdbcType="DECIMAL" property="onlineQty" />
    <result column="repairQty" jdbcType="DECIMAL" property="repairQty" />
    <result column="in_qty" jdbcType="VARCHAR" property="inQty" />
    <result column="left_in_qty" jdbcType="VARCHAR" property="leftInQty" />
    <result column="SMT_DELIVER_QTY" jdbcType="VARCHAR" property="smtDeliverCount" />
    <result column="DIP_DELIVER_QTY" jdbcType="VARCHAR" property="dipDeliverCount" />
    <result column="EXTERNAL_TYPE" jdbcType="VARCHAR" property="externalType" />
    <result column="ASS_DELIVER_QTY" jdbcType="VARCHAR" property="assDeliverCount" />
  </resultMap>
  
  <resultMap id="BaseResultDTOMap" type="com.zte.interfaces.dto.ContainerContentInfoDTO">
    <result column="PRODPLAN_ID" jdbcType="VARCHAR" property="prodPlanId" />
    <result column="SMT" jdbcType="VARCHAR" property="smt" />
    <result column="DIP" jdbcType="VARCHAR" property="dip" />
    <result column="ASS" jdbcType="VARCHAR" property="ass" />
  </resultMap>

  <sql id="Base_Column_List">
    DR_ID, WORK_DAY, PRODPLAN_NO, PRODPLAN_ID, ITEM_CODE, CRAFT_SECTION, PROCESS_CODE, 
    WORK_STATION, PASS_QTY, NG_QTY, PROCESS_SEQ, REMARK, CREATE_BY, CREATE_DATE, LAST_UPDATED_BY, 
    LAST_UPDATED_DATE, ENABLED_FLAG, ORG_ID, FACTORY_ID, ENTITY_ID, ATTRIBUTE1, ATTRIBUTE2, 
    ATTRIBUTE3
  </sql>

  <select id="getTodayFinishList" parameterType="com.zte.interfaces.dto.WipDailyReportInfoDTO" resultMap="ResultMap">
        select distinct t.prodplan_id from wip_daily_report_info t where t.prodplan_id is not null and t.task_qty = t.in_qty
        and to_char(t.work_day,'yyyymmdd') = to_char(sysdate,'yyyymmdd')
  </select>

    <select id="getListByCondition" parameterType="com.zte.springbootframe.common.model.Page" resultMap="BaseResultMap">
        select
        t.work_day,
        t.task_qty,
        t.prodplan_no,
        t.prodplan_id,
        t.item_code,
        t.item_name,
        t.EXTERNAL_TYPE,
        t.task_qty-sum(decode(t.Craft_Section, 'SMT-A', t.pass_qty, 0))-sum(decode(t.Craft_Section, 'SMT-B', t.pass_qty,
        0))
        -sum(decode(t.Craft_Section, 'DIP', t.pass_qty, 0))-sum(decode(t.Craft_Section, 'ASSEMBLY', t.pass_qty, 0))
        -sum(decode(t.Craft_Section, '入库', t.pass_qty, 0))-sum(decode(t.Craft_Section, '维修', t.pass_qty, 0)) as
        leftQty,
        t.in_qty,
        t.left_in_qty,
        t.smt_deliver_qty,t.dip_deliver_qty,t.ass_deliver_qty,
        sum(decode(t.Craft_Section, 'SMT-A', t.pass_qty, 0)) as SMT_A,
        sum(decode(t.Craft_Section, 'SMT-B', t.pass_qty, 0)) as SMT_B,
        sum(decode(t.Craft_Section, 'DIP', t.pass_qty, 0)) as DIP,
        sum(decode(t.Craft_Section, 'ASSEMBLY', t.pass_qty, 0)) as DBCS,
        sum(decode(t.Craft_Section, '维修', t.pass_qty, 0)) as repairQty
        from wip_daily_report_info t where 1=1
        <if test="params != null and params.prodplanId != null and params.prodplanId != ''">
            and t.prodplan_id=#{params.prodplanId}
        </if>
        <if test="params != null and params.startTime != null">
            <![CDATA[and t.work_day >= to_date(#{params.startTime},'yyyy-MM-dd hh24:mi:ss') and t.work_day < to_date(#{params.endTime},'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="params != null and params.prodplanNo != null and params.prodplanNo != ''">
            and t.prodplan_no=#{params.prodplanNo}
        </if>
        <if test="params != null and params.itemCode != null and params.itemCode != '' and params.itemCode.length() == 15">
            and t.item_code=#{params.itemCode}
        </if>
        <if test="params != null and params.itemCode != null and params.itemCode != '' and params.itemCode.length() == 12">
            and t.item_code like #{params.itemCode} || '%'
        </if>
        group by t.prodplan_no, t.prodplan_id,
        t.item_code,t.item_name,t.task_qty,t.work_day,t.in_qty,t.left_in_qty,t.EXTERNAL_TYPE,t.smt_deliver_qty,t.dip_deliver_qty,t.ass_deliver_qty
        order by t.work_day desc
    </select>
  
   <select id="selectGroupByWd" parameterType="java.util.Map" resultMap="ResultMap">
	select count(1) as pass_qty,
       sysdate as work_day,
       t.craft_section,
       t.attribute1
	  from wip_info t
	 where 1 = 1
	   <if test="factoryId != null">
	       and t.factory_id = #{factoryId,jdbcType=DECIMAL}::numeric
       </if>
       <if test="inProdplanIds != null and inProdplanIds.size > 0">
	       and t.attribute1 in       
	       <foreach collection="inProdplanIds" index="index" item="prodplanId" open="(" separator="," close=")">  
		       #{prodplanId}  
		   </foreach> 
	   </if>
	   and t.craft_section is not null
	 group by t.craft_section, t.attribute1
  </select>
  
  <select id="leftInList" parameterType="com.zte.springbootframe.common.model.Page" resultMap="BaseResultMap">
          select ttt.attribute1 prodplan_id,
           #{params.prodplanNo} prodplan_no,
	       ttt.sn,
	       t4.bill_no,
	       ttt.Line_Code,       
	       ttt.last_updated_date,
	       ttt.work_order_no
          from
			(select t1.*
			  from wip_info t1
			 where 1=1
		 	   <if test="params.factoryId != null">
		       	and t1.factory_id = #{params.factoryId,jdbcType=DECIMAL}::numeric
		       </if>
			   <if test="params.prodplanId != null and params.prodplanId != ''">
		       	and t1.attribute1=#{params.prodplanId}
	   	  	   </if>
			   <if test="params.craftSection == '入库' or params.craftSection == '已入库'">
		       	and t1.CRAFT_SECTION='入库'
	   	 	   </if>
	   	 	   <if test="params.craftSection == '入库'">
		       	and not exists
	   	 	   </if>
			   <if test="params.craftSection == '已入库'">
		       	and exists
	   	 	   </if>
			       (select 1
			          from WAREHOUSEHM_ENTRY_INFO t2, warehousehm_entry_detail t3
			         where 1 = 1
			           and t2.status = '1'
			           and t2.erp_done_status = '1'
			           and t2.bill_no = t3.bill_no
			           <if test="params.prodplanId != null and params.prodplanId != ''">
				       	and t2.prodplan_id=#{params.prodplanId}
			   	  	   </if>
			           and t1.sn=t3.sn))ttt, warehousehm_entry_detail t4
			           where ttt.sn=t4.sn
			           order by ttt.sn
  </select>
  
  <select id="leftInList4Export" parameterType="com.zte.interfaces.dto.WipDailyReportInfoDTO" resultMap="BaseResultMap">
          select ttt.attribute1 prodplan_id,
           #{prodplanNo} prodplan_no,
	       ttt.sn,
	       t4.bill_no,
	       ttt.Line_Code,       
	       ttt.last_updated_date,
	       ttt.work_order_no
          from
			(select t1.*
			  from wip_info t1
			 where 1=1
              <if test=" (prodplanId == null or prodplanId == '' ) and (craftSection == null or  craftSection == '' ) ">
                  and 1=2 and exists
              </if>
			   <if test="factoryId != null">
		       	and t1.factory_id = #{factoryId,jdbcType=DECIMAL}::numeric
		       </if>
			   <if test="prodplanId != null and prodplanId != ''">
		       	and t1.attribute1=#{prodplanId}
	   	  	   </if>
			   <if test="craftSection == '入库' or craftSection == '已入库'">
		       	and t1.CRAFT_SECTION='入库'
	   	 	   </if>
	   	 	   <if test="craftSection == '入库'">
		       	and not exists
	   	 	   </if>
			   <if test="craftSection == '已入库'">
		       	and exists
	   	 	   </if>
			       (select 1
			          from WAREHOUSEHM_ENTRY_INFO t2, warehousehm_entry_detail t3
			         where 1 = 1
			           and t2.status = '1'
			           and t2.erp_done_status = '1'
			           and t2.bill_no = t3.bill_no
			           <if test="prodplanId != null and prodplanId != ''">
				       	and t2.prodplan_id=#{prodplanId}
			   	  	   </if>
			           and t1.sn=t3.sn))ttt, warehousehm_entry_detail t4
			           where ttt.sn=t4.sn
			           order by ttt.sn
  </select>  
  
  <select id="craftSectionList" parameterType="com.zte.springbootframe.common.model.Page" resultMap="BaseResultMap">
          select t1.attribute1 prodplan_id,
           #{params.prodplanNo} prodplan_no,
	       t1.sn,
	       '' bill_no,
	       t1.Line_Code,       
	       t1.last_updated_date,
	       t1.work_order_no
          from wip_info t1
		 where 1 = 1
		   <if test="params.factoryId != null">
	       	and t1.factory_id = #{params.factoryId,jdbcType=DECIMAL}::numeric
	       </if>
		   <if test="params.prodplanId != null and params.prodplanId != ''">
	       	and t1.attribute1=#{params.prodplanId}
   	  	   </if>
		   <if test="params.craftSection != null and params.craftSection != ''">
	       	and t1.CRAFT_SECTION=#{params.craftSection}
   	 	   </if>
   	 	   order by t1.sn
  </select>
  
  <select id="craftSectionList4Export" parameterType="com.zte.interfaces.dto.WipDailyReportInfoDTO" resultMap="BaseResultMap">
         select t1.attribute1 prodplan_id,
           #{prodplanNo} prodplan_no,
	       t1.sn,
	       '' bill_no,
	       t1.Line_Code,       
	       t1.last_updated_date,
	       t1.work_order_no
          from wip_info t1
		 where 1 = 1
		   <if test="factoryId != null">
	       	and t1.factory_id = #{factoryId,jdbcType=DECIMAL}::numeric
	       </if>
		   <if test="prodplanId != null and prodplanId != ''">
	       	and t1.attribute1=#{prodplanId}
   	  	   </if>
		   <if test="craftSection != null and craftSection != ''">
	       	and t1.CRAFT_SECTION=#{craftSection}
   	 	   </if>
          <if test=" (prodplanId == null or  prodplanId == '' ) and ( craftSection == null or  craftSection == '' ) ">
              and 1=2
          </if>
   	 	   order by t1.sn
  </select>
  
  <select id="maxSn" parameterType="java.util.Map" resultMap="BaseResultMap">
	 select max(to_number(t.sn)) sn
	  from wip_info t
	 where t.sn <![CDATA[ >= ]]> #{startSn}
	   and t.sn <![CDATA[ <= ]]> #{endSn}
	   and t.attribute1 = #{prodplanId}  
	   <if test="factoryId != null">
       	and t.factory_id = #{factoryId,jdbcType=DECIMAL}::numeric
       </if>
  </select>   

  <delete id="deleteByDay" parameterType="com.zte.interfaces.dto.WipDailyReportInfoDTO">
    delete from WIP_DAILY_REPORT_INFO t where t.work_day >= #{deleteDateStart,jdbcType=TIMESTAMP} and t.work_day
      <![CDATA[<]]>  #{deleteDateEnd,jdbcType=TIMESTAMP}
    <if test="factoryId != null"> and t.FACTORY_ID = cast(#{factoryId} as numeric)</if>
  </delete>
  
  <insert id="batchInsert" parameterType="java.util.List">
		insert into WIP_DAILY_REPORT_INFO (DR_ID, WORK_DAY, PRODPLAN_NO, 
	      PRODPLAN_ID, ITEM_CODE,ITEM_NAME, CRAFT_SECTION, 
	      PROCESS_CODE, WORK_STATION, PASS_QTY, 
	      NG_QTY, PROCESS_SEQ, REMARK, 
	      CREATE_BY, CREATE_DATE, LAST_UPDATED_BY, 
	      LAST_UPDATED_DATE, ENABLED_FLAG, ORG_ID, 
	      FACTORY_ID, ENTITY_ID, ATTRIBUTE1, 
	      ATTRIBUTE2, ATTRIBUTE3,TASK_QTY,in_qty,left_qty,left_in_qty,SMT_DELIVER_QTY,DIP_DELIVER_QTY,EXTERNAL_TYPE,ASS_DELIVER_QTY)
	  values
	  <foreach collection="list" item="item" index="index" separator=",">
		  (#{item.drId,jdbcType=VARCHAR}, #{item.workDay,jdbcType=TIMESTAMP}, #{item.prodplanNo,jdbcType=VARCHAR},
	      #{item.prodplanId,jdbcType=VARCHAR}, #{item.itemCode,jdbcType=VARCHAR},#{item.itemName,jdbcType=VARCHAR}, #{item.craftSection,jdbcType=VARCHAR}, 
	      #{item.processCode,jdbcType=VARCHAR}, #{item.workStation,jdbcType=VARCHAR}, #{item.passQty,jdbcType=DECIMAL}, 
	      #{item.ngQty,jdbcType=DECIMAL}, #{item.processSeq,jdbcType=DECIMAL}, #{item.remark,jdbcType=VARCHAR}, 
	      #{item.createBy,jdbcType=VARCHAR}, #{item.createDate,jdbcType=TIMESTAMP}, #{item.lastUpdatedBy,jdbcType=VARCHAR}, 
	      #{item.lastUpdatedDate,jdbcType=TIMESTAMP}, #{item.enabledFlag,jdbcType=VARCHAR}, #{item.orgId,jdbcType=DECIMAL}, 
	      #{item.factoryId,jdbcType=DECIMAL}, #{item.entityId,jdbcType=DECIMAL}, #{item.attribute1,jdbcType=VARCHAR}, 
	      #{item.attribute2,jdbcType=VARCHAR}, #{item.attribute3,jdbcType=VARCHAR},#{item.taskQty,jdbcType=VARCHAR},
	       #{item.inQty,jdbcType=VARCHAR},#{item.leftQty},#{item.leftInQty,jdbcType=VARCHAR},#{item.smtDeliverCount},
	       #{item.dipDeliverCount},#{item.externalType,jdbcType=VARCHAR},#{item.assDeliverCount})
	</foreach>
  </insert>

  <insert id="insertWipDailyReportInfo" parameterType="com.zte.domain.model.WipDailyReportInfo">
    insert into WIP_DAILY_REPORT_INFO (DR_ID, WORK_DAY, PRODPLAN_NO, 
      PRODPLAN_ID, ITEM_CODE, CRAFT_SECTION, 
      PROCESS_CODE, WORK_STATION, PASS_QTY, 
      NG_QTY, PROCESS_SEQ, REMARK, 
      CREATE_BY, CREATE_DATE, LAST_UPDATED_BY, 
      LAST_UPDATED_DATE, ENABLED_FLAG, ORG_ID, 
      FACTORY_ID, ENTITY_ID, ATTRIBUTE1, 
      ATTRIBUTE2, ATTRIBUTE3)
    values (#{drId,jdbcType=VARCHAR}, #{workDay,jdbcType=TIMESTAMP}, #{prodplanNo,jdbcType=VARCHAR}, 
      #{prodplanId,jdbcType=VARCHAR}, #{itemCode,jdbcType=VARCHAR}, #{craftSection,jdbcType=VARCHAR}, 
      #{processCode,jdbcType=VARCHAR}, #{workStation,jdbcType=VARCHAR}, #{passQty,jdbcType=DECIMAL}, 
      #{ngQty,jdbcType=DECIMAL}, #{processSeq,jdbcType=DECIMAL}, #{remark,jdbcType=VARCHAR}, 
      #{createBy,jdbcType=VARCHAR}, #{createDate,jdbcType=TIMESTAMP}, #{lastUpdatedBy,jdbcType=VARCHAR}, 
      #{lastUpdatedDate,jdbcType=TIMESTAMP}, #{enabledFlag,jdbcType=VARCHAR}, #{orgId,jdbcType=DECIMAL}, 
      #{factoryId,jdbcType=DECIMAL}, #{entityId,jdbcType=DECIMAL}, #{attribute1,jdbcType=VARCHAR}, 
      #{attribute2,jdbcType=VARCHAR}, #{attribute3,jdbcType=VARCHAR})
  </insert>


  <update id="updateWipDailyReportInfoById" parameterType="com.zte.domain.model.WipDailyReportInfo">
    update WIP_DAILY_REPORT_INFO
    set WORK_DAY = #{workDay,jdbcType=TIMESTAMP},
      PRODPLAN_NO = #{prodplanNo,jdbcType=VARCHAR},
      PRODPLAN_ID = #{prodplanId,jdbcType=VARCHAR},
      ITEM_CODE = #{itemCode,jdbcType=VARCHAR},
      CRAFT_SECTION = #{craftSection,jdbcType=VARCHAR},
      PROCESS_CODE = #{processCode,jdbcType=VARCHAR},
      WORK_STATION = #{workStation,jdbcType=VARCHAR},
      PASS_QTY = #{passQty,jdbcType=DECIMAL},
      NG_QTY = #{ngQty,jdbcType=DECIMAL},
      PROCESS_SEQ = #{processSeq,jdbcType=DECIMAL},
      REMARK = #{remark,jdbcType=VARCHAR},
      CREATE_BY = #{createBy,jdbcType=VARCHAR},
      CREATE_DATE = #{createDate,jdbcType=TIMESTAMP},
      LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR},
      LAST_UPDATED_DATE = #{lastUpdatedDate,jdbcType=TIMESTAMP},
      ENABLED_FLAG = #{enabledFlag,jdbcType=VARCHAR},
      ORG_ID = #{orgId,jdbcType=DECIMAL},
      FACTORY_ID = #{factoryId,jdbcType=DECIMAL},
      ENTITY_ID = #{entityId,jdbcType=DECIMAL},
      ATTRIBUTE1 = #{attribute1,jdbcType=VARCHAR},
      ATTRIBUTE2 = #{attribute2,jdbcType=VARCHAR},
      ATTRIBUTE3 = #{attribute3,jdbcType=VARCHAR}
    where DR_ID = #{drId,jdbcType=VARCHAR}
  </update>
  
  <select id="getDeliverList" parameterType="java.util.Map" resultMap="BaseResultDTOMap">
  	select attribute1 prodplan_id,
       sum(decode(work_station, #{assy1}, qty, 0)) +
       sum(decode(work_station, #{assy2}, qty, 0)) ASS,
       sum(decode(work_station, #{dip1}, qty, 0)) +
       sum(decode(work_station, #{dip2}, qty, 0)) dip,
       sum(decode(work_station, #{smt}, qty, 0)) smt
	  from (select count(*) qty, t.attribute1, t.work_station
	          from wip_info t
	         where 1=1
	               <if test="workStations != null and workStations.size > 0">
				       and t.work_station in			       
				       <foreach collection="workStations" index="index" item="workStation" open="(" separator="," close=")">  
					       #{workStation}  
					   </foreach>
				   </if>
	               <if test="factoryId != null">
			        and t.factory_id= #{factoryId,jdbcType=DECIMAL}::numeric
			       </if>
	               <if test="inProdplanIds != null and inProdplanIds.size > 0">
				       and t.attribute1 in			       
				       <foreach collection="inProdplanIds" index="index" item="prodplanId" open="(" separator="," close=")">  
					       #{prodplanId}  
					   </foreach>
				   </if>
                  <if test="( workStations == null or workStations.size() == 0) and ( inProdplanIds == null or inProdplanIds.size() == 0) ">
                      and 1=2
                  </if>
	         group by t.attribute1, t.work_station)
	 group by attribute1
  </select>
  
  <select id="deliverList4Export" parameterType="com.zte.interfaces.dto.WipDailyReportInfoDTO" resultMap="BaseResultMap">
          select t1.attribute1 prodplan_id,
           #{prodplanNo} prodplan_no,
	       t1.sn,
	       '' bill_no,
	       t1.Line_Code,       
	       t1.last_updated_date,
	       t1.work_order_no
          from wip_info t1
		 where 1 = 1
		   <if test="factoryId != null">
	       	and t1.factory_id = #{factoryId,jdbcType=DECIMAL}::numeric
	       </if>
		   <if test="prodplanId != null and prodplanId != ''">
	       	and t1.attribute1=#{prodplanId}
   	  	   </if>
		   <if test="workStation != null and workStation != ''">
	       	and t1.work_station in(${workStation})
   	 	   </if>
          <if test=" (prodplanId == null or  prodplanId == '' ) and ( workStation == null or  workStation == '' ) ">
              and 1=2
          </if>
   	 	   order by t1.sn
  </select>  
  
  <select id="deliverList" parameterType="com.zte.springbootframe.common.model.Page" resultMap="BaseResultMap">
          select t1.attribute1 prodplan_id,
           #{params.prodplanNo} prodplan_no,
	       t1.sn,
	       '' bill_no,
	       t1.Line_Code,       
	       t1.last_updated_date,
	       t1.work_order_no
          from wip_info t1
		 where 1 = 1
		   <if test="params.factoryId != null">
	       	and t1.factory_id = #{params.factoryId,jdbcType=DECIMAL}::numeric
	       </if>
		   <if test="params.prodplanId != null and params.prodplanId != ''">
	       	and t1.attribute1=#{params.prodplanId}
   	  	   </if>
		   <if test="params.workStation != null and params.workStation != ''">
	       	and t1.work_station in(${params.workStation})
   	 	   </if>
   	 	   order by t1.sn
  </select>

  <select id="getBuildListByCond" parameterType="com.zte.springbootframe.common.model.Page" resultMap="BaseResultMap">
        select
        t.work_day,
        t.task_qty,
        t.prodplan_no,
        t.prodplan_id,
        t.item_code,
        t.item_name,
        t.task_qty-sum(decode(t.Craft_Section, '装配', t.pass_qty, 0))-sum(decode(t.Craft_Section, '高温', t.pass_qty, 0))
        -sum(decode(t.Craft_Section, '调试', t.pass_qty, 0))-sum(decode(t.Craft_Section, '包装', t.pass_qty, 0))
        -sum(decode(t.Craft_Section, '流水线', t.pass_qty, 0))-sum(decode(t.Craft_Section, '维修', t.pass_qty, 0)) as leftQty,
        t.in_qty,
        t.left_in_qty,
        sum(decode(t.Craft_Section, '装配', t.pass_qty, 0)) as assembleQty,
        sum(decode(t.Craft_Section, '高温', t.pass_qty, 0)) as highTemperQty,
        sum(decode(t.Craft_Section, '调试', t.pass_qty, 0)) as debugQty,
        sum(decode(t.Craft_Section, '包装', t.pass_qty, 0)) as packQty,
        sum(decode(t.Craft_Section, '维修', t.pass_qty, 0)) as repairQty,
        sum(decode(t.Craft_Section, '流水线', t.pass_qty, 0)) as lsLineQty
        from wip_daily_report_info t where 1=1
        <if test="params != null and params.prodplanId != null and params.prodplanId != ''">
            and t.prodplan_id=#{params.prodplanId}
        </if>
        <if test="params != null and params.startTime != null">
            <![CDATA[and to_date(to_char(t.work_day, 'yyyy-mm-dd'),'yyyy-mm-dd') between to_date(#{params.startTime},'yyyy-MM-dd') and to_date(#{params.endTime},'yyyy-MM-dd')]]>
        </if>
        <if test="params != null and params.prodplanNo != null and params.prodplanNo != ''">
            and t.prodplan_no=#{params.prodplanNo}
        </if>
        <if test="params != null and params.itemCode != null and params.itemCode != '' and params.itemCode.length() == 15">
            and t.item_code=#{params.itemCode}
        </if>
        <if test="params != null and params.itemCode != null and params.itemCode != '' and params.itemCode.length() == 12">
            and t.item_code like #{params.itemCode} || '%'
        </if>
        group by t.prodplan_no, t.prodplan_id, t.item_code,t.item_name,t.task_qty,t.work_day,t.in_qty,t.left_in_qty
        order by t.work_day desc
  </select>

    <select id="getCountWipPerProdplanId" parameterType="com.zte.interfaces.dto.WipDailyReportInfoDTO"
            resultMap="BaseResultMap">
        select count(1) countWipPerProdplanId, t.attribute1 prodplan_id
        from wip_info t where 1=1
        <if test="inProdplanIds != null and inProdplanIds.size > 0">
            and t.attribute1 in
            <foreach collection="inProdplanIds" index="index" item="prodplanId" open="(" separator="," close=")">
                #{prodplanId}
            </foreach>
        </if>
        group by t.attribute1
    </select>
</mapper>
