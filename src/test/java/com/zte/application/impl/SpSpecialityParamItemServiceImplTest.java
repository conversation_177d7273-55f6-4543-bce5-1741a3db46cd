package com.zte.application.impl;

import com.google.common.collect.Lists;
import com.zte.application.OfflineExportService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.*;
import com.zte.interfaces.dto.SpRecoverImportDTO;
import com.zte.interfaces.dto.SpSpecialityParamItemPageQueryDTO;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.springbootframe.common.redis.RedisLock;
import com.zte.springbootframe.util.CloudDiskHelper;
import com.zte.springbootframe.util.FileUtils;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/10/20 10:06
 * @Description
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({FileUtils.class, SpSpecialityParamItemServiceImpl.class})
public class SpSpecialityParamItemServiceImplTest extends BaseTestCase {

    @InjectMocks
    private SpSpecialityParamItemServiceImpl service;
    @Mock
    private SpSpecialityParamItemRepository repository;
    @Mock
    private SpRecoveryRepository spRecoveryRepository;
    @Mock
    private SpTemplateItemRepository spTemplateItemRepository;
    @Mock
    private SpSpecialityParamRepository spSpecialityParamRepository;
    @Mock
    private RedisTemplate<String, String> redisTemplate;
    @Mock
    private ValueOperations<String, String> valueOperations;
    @Mock
    private RedisLock redisLock;
    @Mock
    private OfflineExportService offlineExportService;
    @Mock
    private CloudDiskHelper cloudDiskHelper;

    @Test
    public void testGetMaxBarcode() {
        PowerMockito.when(repository.selectMaxBarcode(Mockito.any())).thenReturn(1L);
        Assert.assertNotNull(service.getMaxBarcode("test"));
    }

    @Test
    public void testQueryPage() {
        PowerMockito.when(repository.countPage(Mockito.any())).thenReturn(1L);
        PowerMockito.when(repository.queryPage(Mockito.any())).thenReturn(new ArrayList<SpSpecialityParamItem>());
        PageRows<SpSpecialityParamItem> pageRows = service.queryPage(new SpSpecialityParamItemPageQueryDTO());
        Assert.assertTrue(pageRows.getRows().size() >= 0);
    }

    @Test
    public void testAddBatch() {
        PowerMockito.when(repository.insertBatch(Mockito.any())).thenReturn(1L);
        service.addBatch(new ArrayList<SpSpecialityParamItem>());
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

    @Test
    public void testGetImportMessage() {
        PowerMockito.when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        PowerMockito.when(valueOperations.get(Mockito.any())).thenReturn("");
        Assert.assertNotNull(service.getImportMessage("test"));
    }

    @Test
    public void testCommitRecover() {
        PowerMockito.when(spSpecialityParamRepository.selectById(Mockito.any())).thenReturn(new SpSpecialityParam() {{
            setTaskId("test");
            setIsRecovery(0);
        }});
        PowerMockito.when(spSpecialityParamRepository.updateById(Mockito.any())).thenReturn(1L);
        PowerMockito.when(spRecoveryRepository.recover(Mockito.any())).thenReturn(1L);
        service.commitRecover("test");

        PowerMockito.when(spSpecialityParamRepository.selectById(Mockito.any())).thenReturn(new SpSpecialityParam() {{
            setTaskId("test");
            setIsRecovery(1);
        }});
        try {
            service.commitRecover("test");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.RECOVERY_FINISHED,e.getMessage());
        }
    }


    @Test
    public void testExportExcel() throws Exception {
        HttpServletResponse response = Mockito.mock(HttpServletResponse.class);
        PowerMockito.when(repository.countPage(Mockito.any())).thenReturn(1L);
        PowerMockito.when(spSpecialityParamRepository.selectById(Mockito.any())).thenReturn(new SpSpecialityParam() {{
            setTaskId("test");
            setIsRecovery(0);
        }});

        List<SpTemplateItem> list = new ArrayList<>();
        list.add(new SpTemplateItem() {{
            setItemId("");
            setParamName("test");
        }});
        PowerMockito.when(spTemplateItemRepository.queryList(Mockito.any())).thenReturn(list);
        List<SpSpecialityParamItem> list2 = new ArrayList<SpSpecialityParamItem>();
        list2.add(new SpSpecialityParamItem() {{
            setItemData("{}");
        }});
        PowerMockito.when(repository.queryPage(Mockito.any())).thenReturn(list2);

        PowerMockito.whenNew(RedisLock.class).withAnyArguments().thenReturn(redisLock);
        PowerMockito.when(redisLock.lock()).thenReturn(true);

        PowerMockito.doNothing().when(offlineExportService).add(Mockito.any());

        service.exportExcel(response, "");
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

    @Test
    public void testExportExcel2() throws Exception {
        HttpServletResponse response = Mockito.mock(HttpServletResponse.class);
        PowerMockito.when(repository.countPage(Mockito.any())).thenReturn(NumConstant.LONG_ZERO);

        try {
            service.exportExcel(response, "");
        } catch (Exception er) {
            Assert.assertEquals( MessageId.DATA_IS_EMPTY, er.getMessage());
        }
        PowerMockito.when(repository.countPage(Mockito.any())).thenReturn(1L);
        PowerMockito.when(spSpecialityParamRepository.selectById(Mockito.any())).thenReturn(new SpSpecialityParam() {{
            setTaskId("test");
            setIsRecovery(0);
        }});

        List<SpTemplateItem> list = new ArrayList<>();
        list.add(new SpTemplateItem() {{
            setItemId("");
            setParamName("test");
        }});
        PowerMockito.when(spTemplateItemRepository.queryList(Mockito.any())).thenReturn(list);
        List<SpSpecialityParamItem> list2 = new ArrayList<SpSpecialityParamItem>();
        list2.add(new SpSpecialityParamItem() {{
            setItemData("{}");
        }});
        PowerMockito.when(repository.queryPage(Mockito.any())).thenReturn(list2);

        PowerMockito.whenNew(RedisLock.class).withAnyArguments().thenReturn(redisLock);
        PowerMockito.when(redisLock.lock()).thenReturn(false);

        PowerMockito.doNothing().when(offlineExportService).add(Mockito.any());
        try {
            service.exportExcel(response, "");
        } catch (Exception er) {
            Assert.assertEquals(er.getMessage(), er.getMessage());
        }
    }


    @Test
    public void epxortThread() throws Exception {
        PowerMockito.mockStatic(FileUtils.class);
        PowerMockito.when(FileUtils.createFilePathAndCheck(Mockito.any())).thenReturn("/usr/xx.xlsx");

        PowerMockito.doNothing().when(offlineExportService).updateById(Mockito.any());
        PowerMockito.doReturn(new SpSpecialityParam()).when(spSpecialityParamRepository).selectById(Mockito.any());
        List<SpTemplateItem> list = new ArrayList<>();
        list.add(new SpTemplateItem() {{
            setItemId("");
            setParamName("test");
        }});
        PowerMockito.when(spTemplateItemRepository.queryList(Mockito.any())).thenReturn(list);
        List<SpSpecialityParamItem> list2 = new ArrayList<SpSpecialityParamItem>();
        list2.add(new SpSpecialityParamItem() {{
            setItemData("{}");
        }});
        PowerMockito.when(repository.queryPage(Mockito.any())).thenReturn(list2);
        PowerMockito.doReturn("").when(cloudDiskHelper).fileUpload(Mockito.anyString(), Mockito.anyString(), Mockito.anyInt());
        Assert.assertNotNull(list);
    }

    @Test
    public void epxortThread2() throws Exception {
        PowerMockito.doThrow(new MesBusinessException("", "")).when(spSpecialityParamRepository).selectById(Mockito.any());
        try {
            Whitebox.invokeMethod(service, "exportThread", "test", new SpSpecialityParamItemPageQueryDTO(), 10L, "10", redisLock, new OfflineExport());
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), e.getMessage());
        }
    }

    @Test
    public void checkAndResetSpItem() throws Exception {
        PowerMockito.when(repository.selectOneBySpecialityParamId(Mockito.any())).thenReturn(null);
        try {
            Whitebox.invokeMethod(service, "checkAndResetSpItem", new SpRecoverImportDTO(){{setSpecialityParamId("test");}});
        } catch (Exception e) {
            Assert.assertEquals(null,e.getMessage());
        }
    }

    @Test
    public void getData() throws Exception {
        try {
            Whitebox.invokeMethod(service, "getData", Lists.newArrayList(), Lists.newArrayList());
        } catch (Exception e) {
            Assert.assertEquals("RetCode.ServerError", e.getMessage());
        }
    }

    @Test
    public void importRecoverExcel() throws Exception {
        SpRecoverImportDTO spRecoverImportDTO = new SpRecoverImportDTO();
        try {
            service.importRecoverExcel(spRecoverImportDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.IMPORT_DATA_IS_EMPTY, e.getMessage());
        }

        try {
            MultipartFile multipartFile = new MockMultipartFile("test", "test", Constant.STRING_EMPTY, "fsdf".getBytes());
            spRecoverImportDTO.setFile(multipartFile);
            service.importRecoverExcel(spRecoverImportDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.DATA_IS_EMPTY, e.getMessage());
        }

        try {
            MultipartFile multipartFile2 = new MockMultipartFile("test", "tes", "aaaa", "".getBytes());
            spRecoverImportDTO.setFile(multipartFile2);
            service.importRecoverExcel(spRecoverImportDTO);
        } catch (Exception e) {
            Assert.assertEquals(null, e.getMessage());
        }
    }
}
