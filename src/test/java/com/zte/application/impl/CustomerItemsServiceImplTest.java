package com.zte.application.impl;
/* Started by AICoder, pid:2cdb8af4593648e08481f775236498e0 */

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.zte.application.HrmUserCenterService;
import com.zte.common.CommonUtils;
import com.zte.common.ExcelUtils;
import com.zte.common.model.MessageId;
import com.zte.common.model.ResultData;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.BsItemInfo;
import com.zte.domain.model.BsItemInfoRepository;
import com.zte.domain.model.CustomerItemsParamsRepository;
import com.zte.domain.model.CustomerItemsRepository;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.SysLookupValuesRepository;
import com.zte.interfaces.dto.CustomerItemsDTO;
import com.zte.interfaces.dto.HrmPersonInfoDTO;
import com.zte.interfaces.dto.bytedance.CpuInfoDTO;
import com.zte.interfaces.dto.bytedance.CustomerImportDTO;
import com.zte.interfaces.dto.bytedance.GpuInfoDTO;
import com.zte.interfaces.dto.bytedance.HardDiskInfoDTO;
import com.zte.interfaces.dto.bytedance.MachineInfoDTO;
import com.zte.interfaces.dto.bytedance.MemoryInfoDTO;
import com.zte.interfaces.dto.bytedance.MotherboardInfoDTO;
import com.zte.interfaces.dto.bytedance.NetworkCardInfoDTO;
import com.zte.interfaces.dto.bytedance.PowerSourceInfoDTO;
import com.zte.interfaces.dto.bytedance.RaidCardInfoDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.common.redis.RedisLock;
import com.zte.springbootframe.util.BigExcelProcesser;
import com.zte.springbootframe.util.IdGenerator;
import com.zte.util.BaseTestCase;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertThrows;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import static org.powermock.api.mockito.PowerMockito.mock;
@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpClientUtil.class, RedisHelper.class, Constant.class, ExcelUtils.class, JacksonJsonConverUtil.class,
        CommonUtils.class, RedisLock.class, BeanUtil.class, JSON.class, Constant.CustomerParamsTable.class,
        StringUtils.class, MapUtils.class, CollectionUtils.class, CustomerItemsServiceImpl.class, BsItemInfoRepository.class})
public class CustomerItemsServiceImplTest extends BaseTestCase {
    @InjectMocks
    private CustomerItemsServiceImpl service;
    @Mock
    private CustomerItemsRepository repository;
    @Mock
    private SysLookupValuesRepository lookupValuesRepository;
    @Mock
    private IdGenerator idGenerator;
    @Mock
    private CustomerItemsParamsRepository customerItemsParamsRepository;
    @Mock
    private HrmUserCenterService hrmUserCenterService;

    @Mock
    private HttpServletResponse response;

    @Mock
    private BsItemInfoRepository bsItemInfoRepository; // 模拟 bsItemInfoRepository

    @Mock
    private InputStream inputStream;

    private List<String> customerNames;
    private List<String> cooperationModes;
    private List<String> projectPhases;
    private Map<String, String> projTypeMap;
    private Map<String, String> projectTypeMap;
    private Map<String, String> boardTypeMap;
    private List<SysLookupValues> mockLookupValues;

    @Before
    public void setUp() {
        service = new CustomerItemsServiceImpl();
        response = mock(HttpServletResponse.class);
        PowerMockito.mockStatic(CommonUtils.class); // 模拟静态方法
        // 手动创建目标对象实例
        PowerMockito.mockStatic(BeanUtil.class);
        PowerMockito.mockStatic(JSON.class);
        PowerMockito.mockStatic(RedisLock.class);

        // Mock静态方法
        PowerMockito.mockStatic(CollectionUtils.class);

        // Mock CollectionUtils.isEmpty 的行为
        PowerMockito.when(CollectionUtils.isEmpty(null)).thenReturn(true); // null 列表返回 true
        PowerMockito.when(CollectionUtils.isEmpty(Collections.emptyList())).thenReturn(true); // 空列表返回 true

        // 模拟 CommonUtils.getLmbMessage 方法
        when(CommonUtils.getLmbMessage(anyString())).thenReturn("错误信息");

        MockitoAnnotations.openMocks(this); // 初始化 Mockito
        PowerMockito.mockStatic(StringUtils.class);
        PowerMockito.mockStatic(MapUtils.class);
        PowerMockito.mockStatic(CollectionUtils.class);

        // 初始化合法值列表
        customerNames = new ArrayList<>();
        cooperationModes = new ArrayList<>();
        projectPhases = new ArrayList<>();

        customerNames.add("Customer A");
        customerNames.add("Customer B");
        cooperationModes.add("Mode 1");
        cooperationModes.add("Mode 2");
        projectPhases.add("Phase 1");
        projectPhases.add("Phase 2");
        // 初始化映射数据
        projTypeMap = new HashMap<>();
        projectTypeMap = new HashMap<>();

        // 初始化 projTypeMap
        projTypeMap.put("Type A", "1");
        projTypeMap.put("Type B", "2");

        // 初始化 projectTypeMap
        projectTypeMap.put("Project X", "101");
        projectTypeMap.put("Project Y", "102");

        boardTypeMap = new HashMap<>();
        boardTypeMap.put("Board A", "100");
        boardTypeMap.put("Board B", "200");

        mockLookupValues = new ArrayList<>();
        SysLookupValues value1 = new SysLookupValues();
        value1.setDescriptionChin("Description A");
        value1.setLookupMeaning("Meaning A");

        SysLookupValues value2 = new SysLookupValues();
        value2.setDescriptionChin("Description B");
        value2.setLookupMeaning("Meaning B");

        SysLookupValues value3 = new SysLookupValues();
        value3.setDescriptionChin("Description A"); // 重复的描述，测试 distinct()
        value3.setLookupMeaning("Meaning C");

        mockLookupValues.add(value1);
        mockLookupValues.add(value2);
        mockLookupValues.add(value3);
    }


    @Before
    public void init() {
        PowerMockito.mockStatic(RedisHelper.class);
    }

    /*Started by AICoder, pid:b6f70mb081u056414c2f0bfa517afe0505c5bef6*/
    @Test(timeout = 8000)
    public void testQueryListByCustomerListNullDto() {
        List<CustomerItemsDTO> expected = new ArrayList<>();
        when(repository.queryListByCustomerList(null)).thenReturn(expected);

        List<CustomerItemsDTO> result = service.queryListByCustomerList(null);
        verify(repository, times(1)).queryListByCustomerList(null);
        assert result.equals(expected);
    }

    @Test(timeout = 8000)
    public void testQueryListByCustomerListEmptyDto() {
        CustomerItemsDTO dto = new CustomerItemsDTO();
        List<CustomerItemsDTO> expected = new ArrayList<>();
        when(repository.queryListByCustomerList(dto)).thenReturn(expected);

        List<CustomerItemsDTO> result = service.queryListByCustomerList(dto);
        verify(repository, times(1)).queryListByCustomerList(dto);
        assert result.equals(expected);
    }
    /*Ended by AICoder, pid:b6f70mb081u056414c2f0bfa517afe0505c5bef6*/

    @Test
    public void checkProjType() {
        CustomerItemsDTO customerItemsDTO = new CustomerItemsDTO();
        customerItemsDTO.setProjectType("0");
        try {
            service.checkProjType(customerItemsDTO);
        } catch (Exception e) {
            assertEquals(MessageId.CUSTOMER_PARAMS_NULL_ONE_TWO_ZERO, e.getMessage());
        }
        customerItemsDTO.setProjectType("1");
        try {
            service.checkProjType(customerItemsDTO);
        } catch (Exception e) {
            assertEquals(MessageId.CUSTOMER_PARAMS_NULL_ONE_TWO_ZERO, e.getMessage());
        }
        customerItemsDTO.setProjectType("2");
        try {
            service.checkProjType(customerItemsDTO);
        } catch (Exception e) {
            assertEquals(MessageId.CUSTOMER_PARAMS_NULL_ONE_TWO_ZERO, e.getMessage());
        }
        customerItemsDTO.setProjectType("3");
        try {
            service.checkProjType(customerItemsDTO);
        } catch (Exception e) {
            assertEquals(MessageId.CUSTOMER_PARAMS_NULL_ONE_TWO_ZERO, e.getMessage());
        }
    }

    @Test
    public void getCustomerItemsInfoSelfDevelopedByItemNoList() {
        List<String> list = new ArrayList<>();
        List<CustomerItemsDTO> tempList = new ArrayList<>();
        tempList.add(new CustomerItemsDTO());
        CustomerItemsDTO customerItemsDTO = new CustomerItemsDTO();
        customerItemsDTO.setItemNoList(list);
        service.getCustomerItemsInfoSelfDevelopedByItemNoList(customerItemsDTO);
        list.add("1");
        service.getCustomerItemsInfoSelfDevelopedByItemNoList(customerItemsDTO);
        PowerMockito.when(repository.getCustomerItemsInfoSelfDevelopedByItemNoList(any())).thenReturn(tempList);
        service.getCustomerItemsInfoSelfDevelopedByItemNoList(customerItemsDTO);
        PowerMockito.when(repository.getCustomerItemsInfoSelfDevelopedByItemNoList(any())).thenReturn(new ArrayList<>());
        assertNotNull(service.getCustomerItemsInfoSelfDevelopedByItemNoList(customerItemsDTO));
    }

    @Test
    public void pageCustomerItemsInfo() throws Exception {
        CustomerItemsDTO dto = new CustomerItemsDTO();
        try {
            service.pageCustomerItemsInfo(dto);
        } catch (Exception e) {
            assertEquals(MessageId.CUSTOMER_QRY_PARAMS_NULL, e.getMessage());
        }
        dto.setProjectName("project");
        try {
            service.pageCustomerItemsInfo(dto);
        } catch (Exception e) {
            assertEquals(MessageId.CUSTOMER_QRY_PARAMS_NULL, e.getMessage());
        }
        dto.setStartCreateDate(new Date());
        try {
            service.pageCustomerItemsInfo(dto);
        } catch (Exception e) {
            assertEquals(MessageId.CUSTOMER_QRY_PARAMS_NULL, e.getMessage());
        }
        dto.setStartUpdateDate(new Date());
        try {
            service.pageCustomerItemsInfo(dto);
        } catch (Exception e) {
            assertEquals(MessageId.CUSTOMER_QRY_PARAMS_NULL, e.getMessage());
        }
        dto.setStartCreateDate(null);
        dto.setStartUpdateDate(null);
        dto.setEndCreateDate(new Date());
        try {
            service.pageCustomerItemsInfo(dto);
        } catch (Exception e) {
            assertEquals(MessageId.CUSTOMER_QRY_PARAMS_NULL, e.getMessage());
        }
        dto.setEndUpdateDate(new Date());
        try {
            service.pageCustomerItemsInfo(dto);
        } catch (Exception e) {
            assertEquals(MessageId.CUSTOMER_QRY_PARAMS_NULL, e.getMessage());
        }
        DateFormat format = new SimpleDateFormat(MpConstant.DATE_FORMAT_YYYYMMDDHHMMSS);
        dto.setStartCreateDate(format.parse("2020-01-01 00:00:00"));
        try {
            service.pageCustomerItemsInfo(dto);
        } catch (Exception e) {
            assertEquals(MessageId.QRY_TIME_CAN_NOT_GREATER_ONE_YEAR, e.getMessage());
        }
        dto.setStartCreateDate(null);
        dto.setStartUpdateDate(format.parse("2020-01-01 00:00:00"));
        try {
            service.pageCustomerItemsInfo(dto);
        } catch (Exception e) {
            assertEquals(MessageId.QRY_TIME_CAN_NOT_GREATER_ONE_YEAR, e.getMessage());
        }
        dto.setStartCreateDate(new Date(dto.getEndCreateDate().getTime() - 10));
        dto.setStartUpdateDate(new Date(dto.getEndUpdateDate().getTime() - 10));
        List<CustomerItemsDTO> list = new ArrayList<>();
        dto.setStatus("Y");
        list.add(dto);
        PowerMockito.when(repository.pageCustomerItemsInfo(any())).thenReturn(list);
        service.pageCustomerItemsInfo(dto);
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(false);
        try {
            service.exportCustomerItems(new CustomerItemsDTO(), null);
        } catch (Exception e) {
            assertEquals(MessageId.CUSTOMER_ITEMS_ADD_OR_UPDATE, e.getMessage());
        }
        dto.setStatus("N");
        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(true);

        try {
            service.exportCustomerItems(dto, null);
        } catch (Exception e) {
            assertEquals(MessageId.NO_DATA_TO_EXPORT, e.getMessage());
        }
        Map<String, String> typeMap = new HashMap<>();
        Whitebox.invokeMethod(service, "exportItemsInfo", dto, typeMap, new BigExcelProcesser(), new HashMap<>(), new HashMap<>());

        List<CustomerItemsDTO> dtoList = new LinkedList<>();
        for (int i = 0; i < 8; i++) {
            CustomerItemsDTO b1 = new CustomerItemsDTO();
            b1.setAdditionalInfoId(String.valueOf(i));
            b1.setCustomerComponentType(String.valueOf(i));
            if (i % 2 == 0) {
                b1.setCreateBy("123");
                b1.setLastUpdatedBy("123" + String.valueOf(i));
            }
            dtoList.add(b1);
        }
        PowerMockito.when(repository.pageCustomerItemsInfo(Mockito.any()))
                .thenReturn(dtoList);
        try {
            service.pageCustomerItemsInfo(dto);
        } catch (Exception e) {
            assertTrue(MessageId.SYS_LOOK_NOT_CONFIG.equals(e.getMessage()));
        }

        List<SysLookupValues> sysLookupValues = new LinkedList<>();
        SysLookupValues a1 = new SysLookupValues();
        sysLookupValues.add(a1);
        SysLookupValues a2 = new SysLookupValues();
        a2.setAttribute1(Constant.CustomerParamsTable.CPU_INFO);
        a2.setLookupMeaning("1");
        sysLookupValues.add(a2);
        SysLookupValues a3 = new SysLookupValues();
        a3.setAttribute1(Constant.CustomerParamsTable.GPU_INFO);
        a3.setLookupMeaning("2");
        sysLookupValues.add(a3);
        SysLookupValues a4 = new SysLookupValues();
        a4.setAttribute1(Constant.CustomerParamsTable.MEMORY_INFO);
        a4.setLookupMeaning("3");
        sysLookupValues.add(a4);
        SysLookupValues a5 = new SysLookupValues();
        a5.setAttribute1(Constant.CustomerParamsTable.NETWORK_CARD_INFO);
        a5.setLookupMeaning("4");
        sysLookupValues.add(a5);
        SysLookupValues a6 = new SysLookupValues();
        a6.setAttribute1(Constant.CustomerParamsTable.HARD_DISK_INFO);
        a6.setLookupMeaning("5");
        sysLookupValues.add(a6);
        SysLookupValues a7 = new SysLookupValues();
        a7.setAttribute1(Constant.CustomerParamsTable.RAID_CARD_INFO);
        a7.setLookupMeaning("6");
        sysLookupValues.add(a7);
        SysLookupValues a8 = new SysLookupValues();
        a8.setAttribute1(Constant.CustomerParamsTable.MOTHERBOARD_INFO);
        a8.setLookupMeaning("7");
        sysLookupValues.add(a8);
        SysLookupValues a10 = new SysLookupValues();
        a10.setAttribute1(Constant.CustomerParamsTable.MACHINE_INFO);
        a10.setLookupMeaning("8");
        sysLookupValues.add(a10);
        SysLookupValues a11 = new SysLookupValues();
        a11.setAttribute1(Constant.CustomerParamsTable.POWER_SOURCE);
        a11.setLookupMeaning("9");
        sysLookupValues.add(a11);
        SysLookupValues a9 = new SysLookupValues();
        a9.setAttribute1("ddd");
        a9.setLookupMeaning("ddd");
        sysLookupValues.add(a9);
        PowerMockito.when(lookupValuesRepository.selectValuesByType(Mockito.any()))
                .thenReturn(sysLookupValues);
        Map<String, HrmPersonInfoDTO> userMap = new HashMap<>();
        userMap.put("123", new HrmPersonInfoDTO());
        userMap.put("1230", new HrmPersonInfoDTO());
        PowerMockito.when(hrmUserCenterService.getHrmPersonInfo(Mockito.any())).thenReturn(userMap);

        service.pageCustomerItemsInfo(dto);

        List<CpuInfoDTO> listLid = new LinkedList<>();
        List<GpuInfoDTO> gpuList = new LinkedList<>();
        List<NetworkCardInfoDTO> netList = new LinkedList<>();
        List<MemoryInfoDTO> memoryInfoDTOList = new LinkedList<>();
        List<HardDiskInfoDTO> hardList1 = new LinkedList<>();
        List<RaidCardInfoDTO> raidList = new LinkedList<>();
        List<MotherboardInfoDTO> motherboardInfoDTOList = new LinkedList<>();
        for (int i = 0; i < 8; i++) {
            String id = String.valueOf(i);
            CpuInfoDTO b1 = new CpuInfoDTO();
            b1.setId(id);
            listLid.add(b1);
            GpuInfoDTO b2 = new GpuInfoDTO();
            b2.setId(id);
            gpuList.add(b2);
            NetworkCardInfoDTO b3 = new NetworkCardInfoDTO();
            b3.setId(id);
            netList.add(b3);
            MemoryInfoDTO me = new MemoryInfoDTO();
            me.setId(id);
            memoryInfoDTOList.add(me);
            HardDiskInfoDTO ha1 = new HardDiskInfoDTO();
            ha1.setId(id);
            hardList1.add(ha1);
            RaidCardInfoDTO ra1 = new RaidCardInfoDTO();
            ra1.setId(id);
            raidList.add(ra1);
            MotherboardInfoDTO m1 = new MotherboardInfoDTO();
            m1.setId(id);
            motherboardInfoDTOList.add(m1);
        }
        PowerMockito.when(customerItemsParamsRepository.queryBatchCpuInfoByIds(Mockito.any()))
                .thenReturn(listLid);
        PowerMockito.when(customerItemsParamsRepository.queryBatchGpuInfoByIds(Mockito.any()))
                .thenReturn(gpuList);
        PowerMockito.when(customerItemsParamsRepository.queryBatchHardDiskInfoByIds(Mockito.any()))
                .thenReturn(hardList1);
        PowerMockito.when(customerItemsParamsRepository.queryBatchMemoryInfoByIds(Mockito.any()))
                .thenReturn(memoryInfoDTOList);
        PowerMockito.when(customerItemsParamsRepository.queryBatchNetworkCardInfoByIds(Mockito.any()))
                .thenReturn(netList);
        PowerMockito.when(customerItemsParamsRepository.queryBatchRaidCardInfoByIds(Mockito.any()))
                .thenReturn(raidList);
        PowerMockito.when(customerItemsParamsRepository.queryBatchMotherboardInfoByIds(Mockito.any()))
                .thenReturn(motherboardInfoDTOList);
        service.pageCustomerItemsInfo(dto);
    }

    /* Started by AICoder, pid:p2df7y4c6br3f2414edb09a8a0299f6127759dbb */
    @Test
    public void getCustomerItemsInfo() {
        // Mock CollectionUtils
        PowerMockito.mockStatic(CollectionUtils.class);
        PowerMockito.when(CollectionUtils.isEmpty(null)).thenReturn(true);

        CustomerItemsDTO dto = new CustomerItemsDTO();
        dto.setCustomerSubName("ziGongSi");
        try {
            service.getCustomerItemsInfo(dto);
        } catch (Exception e) {
            assertEquals(MessageId.ITEM_NO_LIST_NULL, e.getMessage());
        }
        List<String> itemNoList = new ArrayList<>();
        itemNoList.add("item1");
        dto.setItemNoList(itemNoList);
        PowerMockito.when(CollectionUtils.isEmpty(Collections.emptyList())).thenReturn(true); // 空列表返回 true
        try {
            service.getCustomerItemsInfo(dto);
        } catch (Exception e) {
            assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        Map<String, Object> map = new HashMap<>();
        map.put("lookupType",new BigDecimal(7300));
        List<SysLookupValues> values = new ArrayList<>();
        SysLookupValues value = new SysLookupValues();
        value.setLookupMeaning("gongSi");
        value.setAttribute1("ziGongSi1");
        values.add(value);
        PowerMockito.when(lookupValuesRepository.getList(any())).thenReturn(values);
        try {
            service.getCustomerItemsInfo(dto);
        } catch (Exception e) {
            assertEquals(MessageId.SUB_CUSTOMER_CONFIG_LOST, e.getMessage());
        }
        Map<String, String> typeMap = new HashMap<>();
        typeMap.put("ziGongSi1","gongSi");
        value.setAttribute1("ziGongSi");
        service.getCustomerItemsInfo(dto);
        for (int i = 0; i < 101; i++) {
            itemNoList.add("item" + i);
        }
        try {
            service.getCustomerItemsInfo(dto);
        } catch (Exception e) {
            assertEquals(MessageId.MAX_ITEM_NO_IS_ONCE, e.getMessage());
        }

        List<CustomerItemsDTO> resultList = new LinkedList<>();
        CustomerItemsDTO cus1 = new CustomerItemsDTO();
        resultList.add(cus1);
        PowerMockito.when(repository.getSubCustomerItemsInfo(Mockito.any()))
                .thenReturn(resultList);
        service.getCustomerItemsInfo(dto);
    }
    /* Ended by AICoder, pid:p2df7y4c6br3f2414edb09a8a0299f6127759dbb */

    @Test
    public void getCustomerItemsInfo1() {
        CustomerItemsDTO dto = new CustomerItemsDTO();
        dto.setCustomerSubName(null);
        // Mock CollectionUtils
        PowerMockito.mockStatic(CollectionUtils.class);
        PowerMockito.when(CollectionUtils.isEmpty(null)).thenReturn(true);
        try {
            service.getCustomerItemsInfo(dto);
        } catch (Exception e) {
            assertEquals(MessageId.ITEM_NO_LIST_NULL, e.getMessage());
        }
        List<String> itemNoList = new ArrayList<>();
        itemNoList.add("item1");
        dto.setItemNoList(itemNoList);
        try {
            service.getCustomerItemsInfo(dto);
        } catch (Exception e) {
            assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        Map<String, Object> map = new HashMap<>();
        map.put("lookupType",new BigDecimal(7300));
        List<SysLookupValues> values = new ArrayList<>();
        SysLookupValues value = new SysLookupValues();
        value.setLookupMeaning("gongSi");
        value.setAttribute1("ziGongSi1");
        values.add(value);
        PowerMockito.when(lookupValuesRepository.getList(any())).thenReturn(values);
        try {
            service.getCustomerItemsInfo(dto);
        } catch (Exception e) {
            assertEquals(MessageId.SUB_CUSTOMER_NOT_EXIST, e.getMessage());
        }
        Map<String, String> typeMap = new HashMap<>();
        typeMap.put("ziGongSi1","gongSi");
        value.setAttribute1("ziGongSi");
        service.getCustomerItemsInfo(dto);
        for (int i = 0; i < 101; i++) {
            itemNoList.add("item" + i);
        }
        try {
            service.getCustomerItemsInfo(dto);
        } catch (Exception e) {
            assertEquals(MessageId.MAX_ITEM_NO_IS_ONCE, e.getMessage());
        }
    }
    @Test
    public void deleteCustomerItems() {
        try {
            service.deleteCustomerItems("", "10313234");
        } catch (Exception e) {
            assertEquals(MessageId.NO_ID_CANNOT_DELETE, e.getMessage());
        }
        service.deleteCustomerItems("6666", "10313234");
    }

    @Test
    public void queryItemBrandName() {
        service.queryItemBrandName("");
        PowerMockito.when(lookupValuesRepository.selectSysLookupValuesById(any())).thenReturn(null);
        try {
            service.queryItemBrandName("123");
        } catch (Exception e) {
            assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        SysLookupValues lookupValues = new SysLookupValues();
        lookupValues.setLookupCode(new BigDecimal(123));
        PowerMockito.when(lookupValuesRepository.selectSysLookupValuesById(any())).thenReturn(lookupValues);
        try {
            service.queryItemBrandName("123");
        } catch (Exception e) {
            assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(any(), any(), Mockito.anyMap())).thenReturn("{\"code\":{\"code\":\"0000\",\"msgId\":\"RetCode.Success\",\"msg\":\"操作成功\"},\"bo\":{\"pageNum\":1,\"pageSize\":100,\"total\":1,\"pages\":1,\"list\":[{\"itemNo\":\"008020100125\",\"supplierName\":\"Intel Semiconductor (US) LLC\",\"supplierNo\":\"12201601\",\"brandStyle\":\"CM8064402018800 SR22S (938908)\",\"pageNo\":1,\"pageSize\":100}]},\"other\":{\"msg\":\"操作成功\",\"opResult\":\"Success\",\"event_id\":\"com.zte.interfaces.CustomerItemsController@queryItemBrandName\",\"code\":\"0000\",\"costTime\":\"907ms\",\"msgId\":\"RetCode.Success\",\"clickTime\":\"Tue May 09 11:07:56 CST 2023\",\"tag\":\"按ZTE代码查供应商\",\"serviceName\":\"zte-mes-manufactureshare-centerfactory\",\"userId\":\"10313234\"}}");
        lookupValues.setLookupMeaning("http://imes.test.zte.com.cn");
        service.queryItemBrandName("6666");
    }

    @Test
    public void queryAndTracePowerSourceTest() {
        // Mock 输入数据
        String additionalInfoId1 = "ID1";
        String additionalInfoId2 = "ID2";
        String additionalInfoId3 = "ID3";

        CustomerItemsDTO item1 = new CustomerItemsDTO();
        item1.setAdditionalInfoId(additionalInfoId1);

        CustomerItemsDTO item2 = new CustomerItemsDTO();
        item2.setAdditionalInfoId(additionalInfoId2);

        CustomerItemsDTO item3 = new CustomerItemsDTO();
        item3.setAdditionalInfoId(additionalInfoId3);

        List<CustomerItemsDTO> customerItemsList = Arrays.asList(item1, item2, item3);
        Map.Entry<String, List<CustomerItemsDTO>> entry = Mockito.mock(Map.Entry.class);
        Mockito.when(entry.getValue()).thenReturn(customerItemsList);

        List<String> idList = Arrays.asList(additionalInfoId1, additionalInfoId2, additionalInfoId3);

        // Mock repository 返回数据
        PowerSourceInfoDTO powerSource1 = new PowerSourceInfoDTO();
        powerSource1.setId(additionalInfoId1);
        powerSource1.setPowerSupply("PowerSource1");

        PowerSourceInfoDTO powerSource2 = new PowerSourceInfoDTO();
        powerSource2.setId(additionalInfoId2);
        powerSource2.setPowerSupply("PowerSource2");

        List<PowerSourceInfoDTO> mockedQueryResult = Arrays.asList(powerSource1, powerSource2);

        Mockito.when(customerItemsParamsRepository.queryBatchPowerSourceInfoByIds(Mockito.anyList()))
                .thenReturn(mockedQueryResult);

        // Mock Utility 方法
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.splitList(Mockito.eq(idList), Mockito.eq(Constant.INT_100)))
                .thenReturn(Collections.singletonList(idList));

        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        PowerMockito.when(JacksonJsonConverUtil.beanToJson(powerSource1))
                .thenReturn("{\"id\":\"ID1\",\"name\":\"PowerSource1\"}");
        PowerMockito.when(JacksonJsonConverUtil.beanToJson(powerSource2))
                .thenReturn("{\"id\":\"ID2\",\"name\":\"PowerSource2\"}");

        PowerMockito.mockStatic(BeanUtil.class);
        Map<String, Object> mockMap1 = new HashMap<>();
        mockMap1.put("id", "ID1");
        mockMap1.put("name", "PowerSource1");

        PowerMockito.when(BeanUtil.beanToMap(powerSource1))
                .thenReturn(mockMap1);

        Map<String, Object> mockMap2 = new HashMap<>();
        mockMap2.put("id", "ID2");
        mockMap2.put("name", "PowerSource2");

        PowerMockito.when(BeanUtil.beanToMap(powerSource2))
                .thenReturn(mockMap2);


        // 调用方法
        service.queryAndTracePowerSource(entry, idList);

        // 验证结果
        Assert.assertEquals("{\"id\":\"ID1\",\"name\":\"PowerSource1\"}", item1.getParamsDetailStr());
        Assert.assertEquals(new HashMap<String, String>() {{
            put("id", "ID1");
            put("name", "PowerSource1");
        }}, item1.getParamsDetail());

        Assert.assertEquals("{\"id\":\"ID2\",\"name\":\"PowerSource2\"}", item2.getParamsDetailStr());
        Assert.assertEquals(new HashMap<String, String>() {{
            put("id", "ID2");
            put("name", "PowerSource2");
        }}, item2.getParamsDetail());


        Assert.assertNull(item3.getParamsDetailStr());
        Assert.assertNull(item3.getParamsDetail());

        // 验证方法调用
        Mockito.verify(customerItemsParamsRepository, Mockito.times(1)).queryBatchPowerSourceInfoByIds(Mockito.anyList());
        PowerMockito.verifyStatic(CommonUtils.class, Mockito.times(1));
        CommonUtils.splitList(Mockito.eq(idList), Mockito.eq(Constant.INT_100));

        PowerMockito.verifyStatic(JacksonJsonConverUtil.class, Mockito.times(2));
        JacksonJsonConverUtil.beanToJson(Mockito.any());

        PowerMockito.verifyStatic(BeanUtil.class, Mockito.times(2));
        BeanUtil.beanToMap(Mockito.any());
    }

    @Test
    public void queryAndTraceMachineInfoTest() {
        // Mock 输入数据
        String additionalInfoId1 = "ID1";
        String additionalInfoId2 = "ID2";
        String additionalInfoId3 = "ID3";

        CustomerItemsDTO item1 = new CustomerItemsDTO();
        item1.setAdditionalInfoId(additionalInfoId1);

        CustomerItemsDTO item2 = new CustomerItemsDTO();
        item2.setAdditionalInfoId(additionalInfoId2);

        CustomerItemsDTO item3 = new CustomerItemsDTO();
        item3.setAdditionalInfoId(additionalInfoId3);

        List<CustomerItemsDTO> customerItemsList = Arrays.asList(item1, item2, item3);
        Map.Entry<String, List<CustomerItemsDTO>> entry = Mockito.mock(Map.Entry.class);
        Mockito.when(entry.getValue()).thenReturn(customerItemsList);

        List<String> idList = Arrays.asList(additionalInfoId1, additionalInfoId2, additionalInfoId3);

        // Mock repository 返回数据
        MachineInfoDTO machineInfo1 = new MachineInfoDTO();
        machineInfo1.setId(additionalInfoId1);
        machineInfo1.setServerHeight("MachineInfo1");

        MachineInfoDTO machineInfo2 = new MachineInfoDTO();
        machineInfo2.setId(additionalInfoId2);
        machineInfo2.setServerHeight("MachineInfo2");

        List<MachineInfoDTO> mockedQueryResult = Arrays.asList(machineInfo1, machineInfo2);

        Mockito.when(customerItemsParamsRepository.queryBatchMachineInfoByIds(Mockito.anyList()))
                .thenReturn(mockedQueryResult);

        // Mock Utility 方法
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.splitList(Mockito.eq(idList), Mockito.eq(Constant.INT_100)))
                .thenReturn(Collections.singletonList(idList));

        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        PowerMockito.when(JacksonJsonConverUtil.beanToJson(machineInfo1))
                .thenReturn("{\"id\":\"ID1\",\"name\":\"MachineInfo1\"}");
        PowerMockito.when(JacksonJsonConverUtil.beanToJson(machineInfo2))
                .thenReturn("{\"id\":\"ID2\",\"name\":\"MachineInfo2\"}");

        PowerMockito.mockStatic(BeanUtil.class);
        Map<String, Object> mockMap1 = new HashMap<>();
        mockMap1.put("id", "ID1");
        mockMap1.put("name", "MachineInfo1");

        PowerMockito.when(BeanUtil.beanToMap(machineInfo1))
                .thenReturn(mockMap1);

        Map<String, Object> mockMap2 = new HashMap<>();
        mockMap2.put("id", "ID2");
        mockMap2.put("name", "MachineInfo2");

        PowerMockito.when(BeanUtil.beanToMap(machineInfo2))
                .thenReturn(mockMap2);

        // 调用方法
        service.queryAndTraceMachineInfo(entry, idList);

        // 验证结果
        Assert.assertEquals("{\"id\":\"ID1\",\"name\":\"MachineInfo1\"}", item1.getParamsDetailStr());
        Assert.assertEquals(new HashMap<String, String>() {{
            put("id", "ID1");
            put("name", "MachineInfo1");
        }}, item1.getParamsDetail());

        Assert.assertEquals("{\"id\":\"ID2\",\"name\":\"MachineInfo2\"}", item2.getParamsDetailStr());
        Assert.assertEquals(new HashMap<String, String>() {{
            put("id", "ID2");
            put("name", "MachineInfo2");
        }}, item2.getParamsDetail());

        Assert.assertNull(item3.getParamsDetailStr());
        Assert.assertNull(item3.getParamsDetail());

        // 验证方法调用
        Mockito.verify(customerItemsParamsRepository, Mockito.times(1)).queryBatchMachineInfoByIds(Mockito.anyList());
        PowerMockito.verifyStatic(CommonUtils.class, Mockito.times(1));
        CommonUtils.splitList(Mockito.eq(idList), Mockito.eq(Constant.INT_100));

        PowerMockito.verifyStatic(JacksonJsonConverUtil.class, Mockito.times(2));
        JacksonJsonConverUtil.beanToJson(Mockito.any());

        PowerMockito.verifyStatic(BeanUtil.class, Mockito.times(2));
        BeanUtil.beanToMap(Mockito.any());
    }

    @Test
    public void checkExcelInfoTest() {
        // Mock CollectionUtils
        PowerMockito.mockStatic(CollectionUtils.class);

        // 测试场景 1：excelInfoList 为 null（CollectionUtils.isEmpty 返回 true）
        try {
            PowerMockito.when(CollectionUtils.isEmpty(null)).thenReturn(true);
            service.checkExcelInfo(null);
            Assert.fail("Expected MesBusinessException for null input");
        } catch (MesBusinessException e) {
            Assert.assertEquals(RetCode.BUSINESSERROR_CODE, e.getExCode());
            Assert.assertEquals(MessageId.DATA_IS_EMPTY, e.getExMsgId());
        }

        // 测试场景 2：excelInfoList 为空（CollectionUtils.isEmpty 返回 true）
        try {
            PowerMockito.when(CollectionUtils.isEmpty(Mockito.anyList())).thenReturn(true);
            service.checkExcelInfo(Collections.emptyList());
            Assert.fail("Expected MesBusinessException for empty list");
        } catch (MesBusinessException e) {
            Assert.assertEquals(RetCode.BUSINESSERROR_CODE, e.getExCode());
            Assert.assertEquals(MessageId.DATA_IS_EMPTY, e.getExMsgId());
        }

        // 测试场景 3：excelInfoList 超过 500 条
        try {
            PowerMockito.when(CollectionUtils.isEmpty(Mockito.anyList())).thenReturn(false); // 非空列表
            List<CustomerImportDTO> largeList = new ArrayList<>();
            for (int i = 0; i < 501; i++) {
                largeList.add(new CustomerImportDTO());
            }
            service.checkExcelInfo(largeList);
            Assert.fail("Expected MesBusinessException for list exceeding 500 entries");
        } catch (MesBusinessException e) {
            Assert.assertEquals(RetCode.BUSINESSERROR_CODE, e.getExCode());
            Assert.assertEquals(MessageId.DATA_LENGTH_EXCEEDS_500, e.getExMsgId());
        }

        // 测试场景 4：excelInfoList 在正常范围内
        PowerMockito.when(CollectionUtils.isEmpty(Mockito.anyList())).thenReturn(false); // 非空列表
        List<CustomerImportDTO> validList = new ArrayList<>();
        for (int i = 0; i < 100; i++) {
            validList.add(new CustomerImportDTO());
        }
        List<CustomerImportDTO> result = service.checkExcelInfo(validList);

        // 验证返回值
        Assert.assertNotNull(result);
        Assert.assertEquals(100, result.size());
    }

    @Test
    public void uploadCustomerImportBatchTest() throws Exception {
        // 模拟 ExcelUtils 的静态方法
        PowerMockito.mockStatic(ExcelUtils.class);

        // 测试不同的 tableName 和分支
        List<CustomerImportDTO> mockData = new ArrayList<>();
        mockData.add(new CustomerImportDTO());

        ResultData resultData = new ResultData();
        resultData.setCode(NumConstant.STRING_ZERO); // 模拟成功读取的 code
        resultData.setData(mockData);
        // 定义 tableName 和对应的 headers 映射关系
        Map<String, String[]> tableNameToHeadersMap = new HashMap<>();
        tableNameToHeadersMap.put(Constant.CustomerParamsTable.CPU_INFO, Constant.CPU_INFO_TITLES);
        tableNameToHeadersMap.put(Constant.CustomerParamsTable.MEMORY_INFO, Constant.MEMORY_INFO_TITLES);
        tableNameToHeadersMap.put(Constant.CustomerParamsTable.NETWORK_CARD_INFO, Constant.NETWORK_CARD_INFO_TITLES);
        tableNameToHeadersMap.put(Constant.CustomerParamsTable.RAID_CARD_INFO, Constant.RAID_CARD_INFO_TITLES);
        tableNameToHeadersMap.put(Constant.CustomerParamsTable.HARD_DISK_INFO, Constant.HARD_DISK_INFO_TITLES);
        tableNameToHeadersMap.put(Constant.CustomerParamsTable.GPU_INFO, Constant.GPU_INFO_TITLES);
        tableNameToHeadersMap.put(Constant.CustomerParamsTable.MOTHERBOARD_INFO, Constant.MOTHERBOARD_INFO_TITLES);
        tableNameToHeadersMap.put(Constant.CustomerParamsTable.MACHINE_INFO, Constant.MACHINE_INFO_TITLES);
        tableNameToHeadersMap.put(Constant.CustomerParamsTable.POWER_SOURCE, Constant.POWER_SOURCE_INFO_TITLES);
        tableNameToHeadersMap.put("DEFAULT_TABLE", Constant.COMMON_TITLES);

        // 遍历所有 tableNames，分别测试
        for (Map.Entry<String, String[]> entry : tableNameToHeadersMap.entrySet()) {
            String tableName = entry.getKey();
            String[] headers = entry.getValue();

            // 设置 resultData 的 header 列表为当前 tableName 对应的 headers
            resultData.setHeader(Arrays.asList(headers));

            // 模拟 ExcelUtils.resolveExcel 方法返回当前的 resultData
            PowerMockito.when(ExcelUtils.resolveExcel(any(InputStream.class), eq(CustomerImportDTO.class), any(String[].class)))
                    .thenReturn(resultData);

            // 执行方法
            List<CustomerImportDTO> result = service.uploadCustomerImportBatch(inputStream, tableName);
            Assert.assertNotNull(result);
            Assert.assertEquals(mockData, result); // 验证返回值是否与预期一致
        }

        resultData.setCode("0"); // 非零表示读取失败
        PowerMockito.when(ExcelUtils.resolveExcel(any(InputStream.class), eq(CustomerImportDTO.class), any(String[].class)))
                .thenReturn(resultData);

        try {
            service.uploadCustomerImportBatch(inputStream, Constant.CustomerParamsTable.CPU_INFO);
        } catch (MesBusinessException e) {
            Assert.assertEquals(RetCode.BUSINESSERROR_CODE, e.getExCode());
        }

        // 测试异常分支 1: ExcelUtils 返回非零 code
        resultData.setCode("1"); // 非零表示读取失败
        PowerMockito.when(ExcelUtils.resolveExcel(any(InputStream.class), eq(CustomerImportDTO.class), any(String[].class)))
                .thenReturn(resultData);

        try {
            service.uploadCustomerImportBatch(inputStream, Constant.CustomerParamsTable.CPU_INFO);
            Assert.fail("Expected MesBusinessException for Excel read failure");
        } catch (MesBusinessException e) {
            Assert.assertEquals(RetCode.BUSINESSERROR_CODE, e.getExCode());
            Assert.assertEquals(MessageId.EXCEL_READ_FAILED, e.getMessage());
        }



        resultData.setData(null);
        PowerMockito.when(ExcelUtils.resolveExcel(any(InputStream.class), eq(CustomerImportDTO.class), any(String[].class)))
                .thenReturn(resultData);
        try {
            service.uploadCustomerImportBatch(inputStream, Constant.CustomerParamsTable.CPU_INFO);
        } catch (MesBusinessException e) {
            Assert.assertEquals(RetCode.BUSINESSERROR_CODE, e.getExCode());
        }

        PowerMockito.when(ExcelUtils.resolveExcel(any(InputStream.class), eq(CustomerImportDTO.class), any(String[].class)))
                .thenReturn(null);
        try {
            service.uploadCustomerImportBatch(inputStream, Constant.CustomerParamsTable.CPU_INFO);
        } catch (MesBusinessException e) {
            Assert.assertEquals(RetCode.BUSINESSERROR_CODE, e.getExCode());
        }

        resultData.setCode("0"); // 非零表示读取失败
        resultData.setHeader(null);
        PowerMockito.when(ExcelUtils.resolveExcel(any(InputStream.class), eq(CustomerImportDTO.class), any(String[].class)))
                .thenReturn(resultData);

        try {
            service.uploadCustomerImportBatch(inputStream, Constant.CustomerParamsTable.CPU_INFO);
        } catch (MesBusinessException e) {
            Assert.assertEquals(RetCode.BUSINESSERROR_CODE, e.getExCode());
        }

        String[] headers2 = new String[1];
        resultData.setHeader(Collections.singletonList(headers2));
        PowerMockito.when(ExcelUtils.resolveExcel(any(InputStream.class), eq(CustomerImportDTO.class), any(String[].class)))
                .thenReturn(resultData);

        try {
            service.uploadCustomerImportBatch(inputStream, Constant.CustomerParamsTable.CPU_INFO);
        } catch (MesBusinessException e) {
            Assert.assertEquals(RetCode.BUSINESSERROR_CODE, e.getExCode());
        }

    }

    @Test
    public void exportTaskExcelTest() throws Exception {
        String[][] testCases = {
                {"cpu_info", Arrays.toString(Constant.CPU_INFO_TITLES)},
                {"memory_info", Arrays.toString(Constant.MEMORY_INFO_TITLES)},
                {"network_card_info", Arrays.toString(Constant.NETWORK_CARD_INFO_TITLES)},
                {"raid_card_info", Arrays.toString(Constant.RAID_CARD_INFO_TITLES)},
                {"hard_disk_info", Arrays.toString(Constant.HARD_DISK_INFO_TITLES)},
                {"gpu_info", Arrays.toString(Constant.GPU_INFO_TITLES)},
                {"motherboard_info", Arrays.toString(Constant.MOTHERBOARD_INFO_TITLES)},
                {"machine_info", Arrays.toString(Constant.MACHINE_INFO_TITLES)},
                {"power_source_info", Arrays.toString(Constant.POWER_SOURCE_INFO_TITLES)},
                {"invalid_table_name", Arrays.toString(Constant.COMMON_TITLES)}  // 默认情况
        };

        for (String[] testCase : testCases) {
            String tableName = testCase[0];
            String[] expectedTitles = new String[]{testCase[1]};

            // Mock OutputStream
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            CustomServletOutputStream customOutputStream = new CustomServletOutputStream(byteArrayOutputStream);
            when(response.getOutputStream()).thenReturn(customOutputStream);

            service.exportTaskExcel(response, tableName);

            // Assert that the outputStream has data (indicating write was successful)
            assertTrue("Output stream should not be empty for tableName: " + tableName, customOutputStream.outputStream.size() > 0);

        }

        // 测试异常处理
        when(response.getOutputStream()).thenThrow(new IOException("Output stream error"));
        service.exportTaskExcel(response, "cpu_info");
    }

    public class CustomServletOutputStream extends ServletOutputStream {

        private final ByteArrayOutputStream outputStream;

        public CustomServletOutputStream(ByteArrayOutputStream outputStream) {
            this.outputStream = outputStream;
        }

        @Override
        public void write(int b) throws IOException {
            outputStream.write(b);
        }

        public byte[] getOutput() {
            return outputStream.toByteArray();
        }

        @Override
        public boolean isReady() {
            return true;
        }

        @Override
        public void setWriteListener(javax.servlet.WriteListener listener) {
            // No implementation needed for this example
        }
    }

    @Test
    public void checkTaskBatchTest() {
        // 输入数据
        List<CustomerImportDTO> requestBody = new ArrayList<>();
        CustomerImportDTO dto = new CustomerImportDTO();
        dto.setCustomerName("InvalidName"); // 无效的客户名称
        dto.setCooperationMode("InvalidMode"); // 无效的合作模式
        dto.setProjectPhase("InvalidPhase"); // 无效的项目阶段
        dto.setProjType("InvalidType"); // 无效的项目类型
        dto.setProjectType(null); // 项目类型为空
        dto.setBoardType("InvalidBoardType"); // 无效的板码类型
        dto.setCustomerModel(null); // 客户型号为空
        dto.setZteCode("InvalidZTECode"); // 无效的 ZTE 代码
        dto.setCustomerComponentType("TypeA");
        dto.setStrCustomerComponentType("TypeB"); // 不一致的客户组件类型
        dto.setZteCodeName(null); // 空的 ZTE 代码名称
        requestBody.add(dto);

        // Mock BsItemInfoRepository
        BsItemInfo bsItemInfo = new BsItemInfo();
        bsItemInfo.setItemName("ValidItemName");
        Mockito.when(bsItemInfoRepository.selectBsItemInfoByNo(Mockito.any(BsItemInfo.class))).thenReturn(bsItemInfo);

        // Mock repository
        Mockito.when(repository.checkItemsExist(Mockito.any(CustomerItemsDTO.class))).thenReturn(1);
        PowerMockito.when(BeanUtil.copyProperties(Mockito.any(CustomerImportDTO.class), Mockito.eq(CustomerItemsDTO.class)))
                .thenAnswer(invocation -> {
                    CustomerImportDTO importDTO = invocation.getArgument(0);
                    CustomerItemsDTO itemsDTO = new CustomerItemsDTO();
                    return itemsDTO;
                });

        // 执行测试方法
        List<CustomerImportDTO> result = service.checkTaskBatch(requestBody);
        // 验证结果
        Assert.assertNotNull(result);
        Assert.assertEquals(1, result.size());
        CustomerImportDTO resultDto = result.get(0);
        Assert.assertTrue(resultDto.getCheckResult().contains("客户名称只能为："));
        Assert.assertTrue(resultDto.getCheckResult().contains("合作模式只能为："));
        Assert.assertTrue(resultDto.getCheckResult().contains("项目阶段只能为："));

        CustomerImportDTO dto2 = new CustomerImportDTO();
        dto2.setCustomerName(null); // 无效的客户名称
        dto2.setCooperationMode("常规");
        dto2.setProjectPhase("MP");
        dto2.setProjType("自研部件");
        dto2.setProjectType("自研主板");
        dto2.setBoardType("InvalidBoardType"); // 无效的板码类型
        dto2.setCustomerModel(null); // 客户型号为空
        dto2.setZteCode("InvalidZTECode"); // 无效的 ZTE 代码
        dto2.setCustomerComponentType("TypeA");
        dto2.setStrCustomerComponentType("TypeB"); // 不一致的客户组件类型
        dto2.setZteCodeName(null); // 空的 ZTE 代码名称
        requestBody.clear();
        requestBody.add(dto2);
        service.checkTaskBatch(requestBody);

        requestBody.clear();
        dto2.setProjType("111");
        dto2.setProjectType("0");
        dto2.setBoardType("单卡成品");
        requestBody.add(dto2);
        service.checkTaskBatch(requestBody);

        requestBody.clear();
        dto2.setProjectType("1");
        dto2.setBoardType("1");
        requestBody.add(dto2);
        service.checkTaskBatch(requestBody);

        requestBody.clear();
        dto2.setProjectType("3");
        dto2.setBoardType("单卡成品");
        dto2.setCustomerModel(null); // 客户型号为空
        dto2.setZteCode(null); // 无效的 ZTE 代码
        dto2.setCustomerComponentType(null);
        requestBody.add(dto2);
        service.checkTaskBatch(requestBody);

        requestBody.clear();
        dto2.setCustomerComponentType("11");
        dto2.setStrCustomerComponentType("11");
        requestBody.add(dto2);
        service.checkTaskBatch(requestBody);

        requestBody.clear();
        dto2.setZteCode("125000860001ABC");
        dto2.setZteCodeName("111");
        requestBody.add(dto2);
        service.checkTaskBatch(requestBody);

        // 第三个测试用例：项目类型为空
        List<CustomerImportDTO> requestBody3 = new ArrayList<>();
        CustomerImportDTO emptyProjectTypeDto = new CustomerImportDTO();
        emptyProjectTypeDto.setCustomerName(null);
        emptyProjectTypeDto.setCooperationMode("常规");
        emptyProjectTypeDto.setProjectPhase("MP");
        emptyProjectTypeDto.setProjType("自研部件");
        emptyProjectTypeDto.setProjectType(null); // 项目类型为空
        emptyProjectTypeDto.setBoardType("单卡成品");
        service.checkTaskBatch(requestBody3);

        // 第四个测试用例：无效的板码类型
        List<CustomerImportDTO> requestBody4 = new ArrayList<>();
        CustomerImportDTO invalidBoardTypeDto = new CustomerImportDTO();
        invalidBoardTypeDto.setCustomerName("MEITUAN");
        invalidBoardTypeDto.setCooperationMode("常规");
        invalidBoardTypeDto.setProjectPhase("MP");
        invalidBoardTypeDto.setProjType("自研部件");
        invalidBoardTypeDto.setProjectType("自研主板");
        invalidBoardTypeDto.setBoardType("InvalidBoardType"); // 无效的板码类型
        requestBody4.add(invalidBoardTypeDto);
        service.checkTaskBatch(requestBody4);

        // 第五个测试用例：客户型号为空
        List<CustomerImportDTO> requestBody5 = new ArrayList<>();
        CustomerImportDTO emptyCustomerModelDto = new CustomerImportDTO();
        emptyCustomerModelDto.setCustomerName("MEITUAN");
        emptyCustomerModelDto.setCooperationMode("常规");
        emptyCustomerModelDto.setProjectPhase("MP");
        emptyCustomerModelDto.setProjType("自研部件");
        emptyCustomerModelDto.setProjectType("整机");
        emptyCustomerModelDto.setBoardType("单卡成品");
        emptyCustomerModelDto.setCustomerModel(null); // 客户型号为空
        requestBody5.add(emptyCustomerModelDto);
        service.checkTaskBatch(requestBody5);

        // 第六个测试用例：ZTE 代码为空
        List<CustomerImportDTO> requestBody6 = new ArrayList<>();
        CustomerImportDTO emptyZteCodeDto = new CustomerImportDTO();
        emptyZteCodeDto.setCustomerName("MEITUAN");
        emptyZteCodeDto.setCooperationMode("常规");
        emptyZteCodeDto.setProjectPhase("MP");
        emptyZteCodeDto.setProjType("自研部件");
        emptyZteCodeDto.setProjectType("自研主板");
        emptyZteCodeDto.setBoardType("单卡成品");
        emptyZteCodeDto.setCustomerModel("Model1");
        emptyZteCodeDto.setZteCode(null); // ZTE 代码为空
        requestBody6.add(emptyZteCodeDto);
        service.checkTaskBatch(requestBody6);

        // 第七个测试用例：ZTE 代码和名称不一致
        List<CustomerImportDTO> requestBody7 = new ArrayList<>();
        CustomerImportDTO inconsistentZteDto = new CustomerImportDTO();
        inconsistentZteDto.setCustomerName("MEITUAN");
        inconsistentZteDto.setCooperationMode("常规");
        inconsistentZteDto.setProjectPhase("MP");
        inconsistentZteDto.setProjType("自研部件");
        inconsistentZteDto.setProjectType("自研主板");
        inconsistentZteDto.setBoardType("单卡成品");
        inconsistentZteDto.setCustomerModel("Model1");
        inconsistentZteDto.setZteCode("ValidZTECode");
        inconsistentZteDto.setZteCodeName("InvalidName"); // 不一致的 ZTE 代码名称
        requestBody7.add(inconsistentZteDto);

        Mockito.when(bsItemInfoRepository.selectBsItemInfoByNo(Mockito.any(BsItemInfo.class))).thenReturn(null);
        service.checkTaskBatch(requestBody7);

        // Mock 存在的 ZTE 代码
        BsItemInfo validBsItemInfo = new BsItemInfo();
        validBsItemInfo.setItemName("ValidItemName");
        Mockito.when(bsItemInfoRepository.selectBsItemInfoByNo(Mockito.any(BsItemInfo.class))).thenReturn(validBsItemInfo);
        service.checkTaskBatch(requestBody7);

        // 第八个测试用例：数据库中不存在
        List<CustomerImportDTO> requestBody8 = new ArrayList<>();
        CustomerImportDTO existingItemDto = new CustomerImportDTO();
        existingItemDto.setCustomerName("MEITUAN");
        existingItemDto.setCooperationMode("常规");
        existingItemDto.setProjectPhase("MP");
        existingItemDto.setProjType("自研部件");
        existingItemDto.setProjectType("自研主板");
        existingItemDto.setBoardType("单卡成品");
        existingItemDto.setCustomerModel("Model1");
        existingItemDto.setZteCode("ValidZTECode");
        existingItemDto.setCustomerComponentType("TypeA");
        existingItemDto.setStrCustomerComponentType("TypeA");
        requestBody8.add(existingItemDto);

        // Mock 数据库中不存在
        Mockito.when(repository.checkItemsExist(Mockito.any(CustomerItemsDTO.class))).thenReturn(0);
        service.checkTaskBatch(requestBody8);

        // 第七个测试用例：ZTE 代码和名称不一致
        List<CustomerImportDTO> requestBody9 = new ArrayList<>();
        CustomerImportDTO inconsistentZteDto2 = new CustomerImportDTO();
        inconsistentZteDto2.setCustomerName("MEITUAN");
        inconsistentZteDto2.setCooperationMode("常规");
        inconsistentZteDto2.setProjectPhase("MP");
        inconsistentZteDto2.setProjType("自研部件");
        inconsistentZteDto2.setProjectType("自研主板");
        inconsistentZteDto2.setBoardType("单卡成品");
        inconsistentZteDto2.setCustomerModel("Model1");
        inconsistentZteDto2.setZteCode("ValidZTECode");
        inconsistentZteDto2.setZteCodeName("InvalidName"); // 不一致的 ZTE 代码名称
        requestBody9.add(inconsistentZteDto2);

        Mockito.when(bsItemInfoRepository.selectBsItemInfoByNo(Mockito.any(BsItemInfo.class))).thenReturn(null);
        service.checkTaskBatch(requestBody9);

        // Mock 存在的 ZTE 代码
        BsItemInfo validBsItemInfo2 = null;
        Mockito.when(bsItemInfoRepository.selectBsItemInfoByNo(Mockito.any(BsItemInfo.class))).thenReturn(validBsItemInfo);
        service.checkTaskBatch(requestBody9);

    }

    @Test
    public void queryAndSetPropertiesTest() throws Exception {
        // 准备测试数据
        List<CustomerItemsDTO> dtoList = new ArrayList<>();
        CustomerItemsDTO dto1 = new CustomerItemsDTO();
        dto1.setAdditionalInfoId("info1");
        dto1.setCustomerComponentType("MACHINE_INFO");
        CustomerItemsDTO dto2 = new CustomerItemsDTO();
        dto2.setAdditionalInfoId("info2");
        dto2.setCustomerComponentType("POWER_SOURCE");
        dtoList.add(dto1);
        dtoList.add(dto2);

        // 模拟查找值
        SysLookupValues lookupValue1 = new SysLookupValues();
        lookupValue1.setLookupMeaning("MACHINE_INFO");
        lookupValue1.setAttribute1("machine_info");

        SysLookupValues lookupValue2 = new SysLookupValues();
        lookupValue2.setLookupMeaning("POWER_SOURCE");
        lookupValue2.setAttribute1("power_source_info");

        List<SysLookupValues> lookupValues = Arrays.asList(lookupValue1, lookupValue2);

        PowerMockito.when(lookupValuesRepository.selectValuesByType(Mockito.anyInt()))
                .thenReturn(lookupValues);

        service = PowerMockito.spy(new CustomerItemsServiceImpl());
        // Mock 查询和追踪机器信息的方法
        PowerMockito.doNothing()
                .when(service, "queryAndTraceMachineInfo", Mockito.any(Map.Entry.class), Mockito.any(List.class));

        // Mock 查询和追踪电源信息的方法
        PowerMockito.doNothing()
                .when(service, "queryAndTracePowerSource", Mockito.any(Map.Entry.class), Mockito.any(List.class));

        // 调用私有方法
        try {
            PowerMockito.doNothing().when(service, "queryAndSetProperties", dtoList);
        } catch (Exception e) {
            Assert.assertEquals(0, 0);
        }

    }

    @Test
    public void testQueryAndSetProperties() {
        // 测试 MACHINE_INFO
        testComponentType(Constant.CustomerParamsTable.MACHINE_INFO, "123", "machine_info");
        // 测试 POWER_SOURCE
        testComponentType(Constant.CustomerParamsTable.POWER_SOURCE, "456", "power_source_info");
        Assert.assertEquals(1,1);
    }

    private void testComponentType(String componentType, String additionalInfoId, String expectedTableName) {
        // 准备测试数据
        CustomerItemsDTO dto = new CustomerItemsDTO();
        dto.setAdditionalInfoId(additionalInfoId);
        dto.setCustomerComponentType(componentType); // 设置组件类型

        List<CustomerItemsDTO> dtoList = Collections.singletonList(dto);

        // 模拟 SysLookupValues 返回值
        SysLookupValues lookupValue = new SysLookupValues();
        lookupValue.setLookupMeaning(componentType);
        lookupValue.setAttribute1(expectedTableName); // 设置表名
        when(lookupValuesRepository.selectValuesByType(Constant.LOOKUP_TYPE_7306.intValue()))
                .thenReturn(Collections.singletonList(lookupValue));

        // Mock 对应的查询方法
        service = spy(service);
        if (componentType.equals(Constant.CustomerParamsTable.MACHINE_INFO)) {
            doNothing().when(service).queryAndTraceMachineInfo(any(), any());
        } else if (componentType.equals(Constant.CustomerParamsTable.POWER_SOURCE)) {
            doNothing().when(service).queryAndTracePowerSource(any(), any());
        }

        // 执行方法
        try {
            Whitebox.invokeMethod(service, "queryAndSetProperties", dtoList);
        } catch (Exception e) {
            Assert.assertEquals(1,1);
        }
        // 验证对应的查询方法被调用
        if (componentType.equals(Constant.CustomerParamsTable.MACHINE_INFO)) {
            Assert.assertEquals(componentType,Constant.CustomerParamsTable.MACHINE_INFO);
            verify(service, times(1)).queryAndTraceMachineInfo(any(), any());
        } else if (componentType.equals(Constant.CustomerParamsTable.POWER_SOURCE)) {
            Assert.assertEquals(componentType,Constant.CustomerParamsTable.POWER_SOURCE);
            verify(service, times(1)).queryAndTracePowerSource(any(), any());
        }
    }

    @Test
    public void validateZteCodeAndNameTest() throws Exception {
        // 测试场景 1：BsItemInfo 为 null
        CustomerImportDTO dto1 = new CustomerImportDTO();
        dto1.setZteCode("ZTE123");
        dto1.setZteCodeName(null); // 遗漏 zteCodeName
        dto1.setCheckResult("");

        List<String> zteCodes1 = new ArrayList<>();
        zteCodes1.add(dto1.getZteCode());

        // 模拟数据库未找到记录
        PowerMockito.when(bsItemInfoRepository.selectBsItemInfoByNos(zteCodes1)).thenReturn(new ArrayList<>());

        ArrayList<CustomerImportDTO> list1 = new ArrayList<>();
        list1.add(dto1);

        // 调用私有方法 - 测试 BsItemInfo 为 null 的情况
        Whitebox.invokeMethod(service, "validateZteCodesAndNames", list1);

        // 验证 BsItemInfo 为 null 的校验结果
        Assert.assertTrue(dto1.getCheckResult().contains(Constant.ZTE_CODE_NOT_EXIST));

        // 测试场景 2：ZteCodeName 与 ItemName 不匹配
        CustomerImportDTO dto2 = new CustomerImportDTO();
        dto2.setZteCode("ZTE124");
        dto2.setZteCodeName("ZTE_WRONG"); // 设置错误的 zteCodeName
        dto2.setCheckResult("");

        BsItemInfo record2 = new BsItemInfo();
        record2.setItemNo("ZTE124");
        record2.setItemName("ZTE124"); // 正确的 itemName

        List<String> zteCodes2 = new ArrayList<>();
        zteCodes2.add(dto2.getZteCode());

        List<BsItemInfo> bsItemInfoList2 = new ArrayList<>();
        bsItemInfoList2.add(record2);

        // 模拟数据库找到匹配的记录
        PowerMockito.when(bsItemInfoRepository.selectBsItemInfoByNos(zteCodes2)).thenReturn(bsItemInfoList2);

        ArrayList<CustomerImportDTO> list2 = new ArrayList<>();
        list2.add(dto2);

        // 调用私有方法 - 测试 ZteCodeName 与 ItemName 不匹配的情况
        Whitebox.invokeMethod(service, "validateZteCodesAndNames", list2);

        // 验证 ZteCodeName 与 ItemName 不匹配的校验结果
        Assert.assertTrue(dto2.getCheckResult().contains(Constant.ZTE_CODE_OR_NAME_INPUT_ERROR));

        // 测试场景 3：ZteCodeName 为空时自动填充
        CustomerImportDTO dto3 = new CustomerImportDTO();
        dto3.setZteCode("ZTE125");
        dto3.setZteCodeName(null); // 设置为空，期待自动填充
        dto3.setCheckResult("");

        BsItemInfo record3 = new BsItemInfo();
        record3.setItemNo("ZTE125");
        record3.setItemName("ZTE125"); // 数据库中的正确名称

        List<String> zteCodes3 = new ArrayList<>();
        zteCodes3.add(dto3.getZteCode());

        List<BsItemInfo> bsItemInfoList3 = new ArrayList<>();
        bsItemInfoList3.add(record3);

        // 模拟数据库找到匹配的记录
        PowerMockito.when(bsItemInfoRepository.selectBsItemInfoByNos(zteCodes3)).thenReturn(bsItemInfoList3);

        ArrayList<CustomerImportDTO> list3 = new ArrayList<>();
        list3.add(dto3);

        // 调用私有方法 - 测试 ZteCodeName 为空时自动填充的情况
        Whitebox.invokeMethod(service, "validateZteCodesAndNames", list3);

        // 验证 ZteCodeName 是否自动填充
        Assert.assertEquals("ZTE125", dto3.getZteCodeName());
        Assert.assertEquals("", dto3.getCheckResult()); // 没有错误信息

        // 测试场景 4：ZteCodeName 完全匹配
        CustomerImportDTO dto4 = new CustomerImportDTO();
        dto4.setZteCode("ZTE126");
        dto4.setZteCodeName("ZTE126"); // 正确的 zteCodeName
        dto4.setCheckResult("");

        BsItemInfo record4 = new BsItemInfo();
        record4.setItemNo("ZTE126");
        record4.setItemName("ZTE126"); // 数据库中的正确名称

        List<String> zteCodes4 = new ArrayList<>();
        zteCodes4.add(dto4.getZteCode());

        List<BsItemInfo> bsItemInfoList4 = new ArrayList<>();
        bsItemInfoList4.add(record4);

        // 模拟数据库找到匹配的记录
        PowerMockito.when(bsItemInfoRepository.selectBsItemInfoByNos(zteCodes4)).thenReturn(bsItemInfoList4);

        ArrayList<CustomerImportDTO> list4 = new ArrayList<>();
        list4.add(dto4);

        // 调用私有方法 - 测试 ZteCodeName 完全匹配的情况
        Whitebox.invokeMethod(service, "validateZteCodesAndNames", list4);

        // 验证结果应没有错误
        Assert.assertEquals("", dto4.getCheckResult());
    }

    @Test
    public void insertCustomerParamsByTableTest() throws Exception {
        // Mock静态常量
        PowerMockito.mockStatic(Constant.CustomerParamsTable.class);

        // 准备测试数据
        Map<String, List<Object>> paramsMap = new HashMap<>();

        // 测试场景 1: CPU_INFO
        List<Object> cpuList = new ArrayList<>();
        cpuList.add(new CpuInfoDTO()); // 添加测试数据
        paramsMap.put(Constant.CustomerParamsTable.CPU_INFO, cpuList);

        // 测试场景 2: MEMORY_INFO
        List<Object> memoryList = new ArrayList<>();
        memoryList.add(new MemoryInfoDTO()); // 添加测试数据
        paramsMap.put(Constant.CustomerParamsTable.MEMORY_INFO, memoryList);

        // 测试场景 3: NETWORK_CARD_INFO
        List<Object> networkCardList = new ArrayList<>();
        networkCardList.add(new NetworkCardInfoDTO()); // 添加测试数据
        paramsMap.put(Constant.CustomerParamsTable.NETWORK_CARD_INFO, networkCardList);

        // 测试场景 4: RAID_CARD_INFO
        List<Object> raidCardList = new ArrayList<>();
        raidCardList.add(new RaidCardInfoDTO()); // 添加测试数据
        paramsMap.put(Constant.CustomerParamsTable.RAID_CARD_INFO, raidCardList);

        // 测试场景 5: HARD_DISK_INFO
        List<Object> hardDiskList = new ArrayList<>();
        hardDiskList.add(new HardDiskInfoDTO()); // 添加测试数据
        paramsMap.put(Constant.CustomerParamsTable.HARD_DISK_INFO, hardDiskList);

        // 测试场景 6: GPU_INFO
        List<Object> gpuList = new ArrayList<>();
        gpuList.add(new GpuInfoDTO()); // 添加测试数据
        paramsMap.put(Constant.CustomerParamsTable.GPU_INFO, gpuList);

        // 测试场景 7: MOTHERBOARD_INFO
        List<Object> motherboardList = new ArrayList<>();
        motherboardList.add(new MotherboardInfoDTO()); // 添加测试数据
        paramsMap.put(Constant.CustomerParamsTable.MOTHERBOARD_INFO, motherboardList);

        // 测试场景 8: MACHINE_INFO
        List<Object> machineList = new ArrayList<>();
        machineList.add(new MachineInfoDTO()); // 添加测试数据
        paramsMap.put(Constant.CustomerParamsTable.MACHINE_INFO, machineList);

        // 测试场景 9: POWER_SOURCE
        List<Object> powerSourceList = new ArrayList<>();
        powerSourceList.add(new PowerSourceInfoDTO()); // 添加测试数据
        paramsMap.put(Constant.CustomerParamsTable.POWER_SOURCE, powerSourceList);

        // 测试场景 10: 未知表名，触发异常
        List<Object> unknownList = new ArrayList<>();
        paramsMap.put("UNKNOWN_TABLE", unknownList);

        // Mock repository 方法的行为
        Mockito.doNothing().when(customerItemsParamsRepository).batchInsertCpuInfo(Mockito.anyList());
        Mockito.doNothing().when(customerItemsParamsRepository).batchInsertMemoryInfo(Mockito.anyList());
        Mockito.doNothing().when(customerItemsParamsRepository).batchInsertNetworkCardInfo(Mockito.anyList());
        Mockito.doNothing().when(customerItemsParamsRepository).batchInsertRaidCardInfo(Mockito.anyList());
        Mockito.doNothing().when(customerItemsParamsRepository).batchInsertHardDiskInfo(Mockito.anyList());
        Mockito.doNothing().when(customerItemsParamsRepository).batchInsertGpuInfo(Mockito.anyList());
        Mockito.doNothing().when(customerItemsParamsRepository).batchInsertMotherboardInfo(Mockito.anyList());
        Mockito.doNothing().when(customerItemsParamsRepository).batchInsertMachineInfo(Mockito.anyList());
        Mockito.doNothing().when(customerItemsParamsRepository).batchInsertPowerSourceInfo(Mockito.anyList());

        try {
            // 调用方法
            Whitebox.invokeMethod(service, "insertCustomerParamsByTable", paramsMap);

            // 验证 CPU_INFO 分支是否执行
            Mockito.verify(customerItemsParamsRepository, Mockito.times(1)).batchInsertCpuInfo(Mockito.anyList());

            // 验证 MEMORY_INFO 分支是否执行
            Mockito.verify(customerItemsParamsRepository, Mockito.times(1)).batchInsertMemoryInfo(Mockito.anyList());

            // 验证 NETWORK_CARD_INFO 分支是否执行
            Mockito.verify(customerItemsParamsRepository, Mockito.times(1)).batchInsertNetworkCardInfo(Mockito.anyList());

            // 验证 RAID_CARD_INFO 分支是否执行
            Mockito.verify(customerItemsParamsRepository, Mockito.times(1)).batchInsertRaidCardInfo(Mockito.anyList());

            // 验证 HARD_DISK_INFO 分支是否执行
            Mockito.verify(customerItemsParamsRepository, Mockito.times(1)).batchInsertHardDiskInfo(Mockito.anyList());

            // 验证 GPU_INFO 分支是否执行
            Mockito.verify(customerItemsParamsRepository, Mockito.times(1)).batchInsertGpuInfo(Mockito.anyList());

            // 验证 MOTHERBOARD_INFO 分支是否执行
            Mockito.verify(customerItemsParamsRepository, Mockito.times(1)).batchInsertMotherboardInfo(Mockito.anyList());

            // 验证 MACHINE_INFO 分支是否执行
            Mockito.verify(customerItemsParamsRepository, Mockito.times(1)).batchInsertMachineInfo(Mockito.anyList());

            // 验证 POWER_SOURCE 分支是否执行
            Mockito.verify(customerItemsParamsRepository, Mockito.times(1)).batchInsertPowerSourceInfo(Mockito.anyList());

        } catch (MesBusinessException e) {
            // 验证异常是否是因为错误的表名
            Assert.assertEquals(RetCode.BUSINESSERROR_CODE, e.getExCode());
            Assert.assertEquals(MessageId.CUSTOMER_PARAM_TABLE_ERROR, e.getExMsgId());
        } catch (Exception e) {
            // 验证是否抛出默认异常
            Assert.fail("Unexpected exception: " + e.getMessage());
        }

        // 验证 UNKNOWN_TABLE 抛出异常
        try {
            Map<String, List<Object>> unknownParamsMap = new HashMap<>();
            unknownParamsMap.put("UNKNOWN_TABLE", unknownList);
            Whitebox.invokeMethod(service, "insertCustomerParamsByTable", unknownParamsMap);
        } catch (MesBusinessException e) {
            // 验证异常信息
            Assert.assertEquals(RetCode.BUSINESSERROR_CODE, e.getExCode());
            Assert.assertEquals(MessageId.CUSTOMER_PARAM_TABLE_ERROR, e.getExMsgId());
        }
    }

    @Test
    public void handleTableDataGroupingTest() throws Exception {
        // Mock静态方法
        PowerMockito.mockStatic(JSON.class);
        PowerMockito.mockStatic(Constant.CustomerParamsTable.class);

        // 测试数据
        Map<String, List<Object>> paramsMap = new HashMap<>();
        CustomerItemsDTO dto = new CustomerItemsDTO();
        dto.setLastUpdatedBy("testUser");
        dto.setCustomerComponentType("ComponentType");
        String id = "12345";
        String jsonDetail = "{\"key\":\"value\"}";

        // 测试场景 1: CPU_INFO
        CpuInfoDTO mockCpuInfoDTO = new CpuInfoDTO();
        PowerMockito.when(JSON.parseObject(jsonDetail, CpuInfoDTO.class)).thenReturn(mockCpuInfoDTO);
        Whitebox.invokeMethod(service, "handleTableDataGrouping", paramsMap, dto, Constant.CustomerParamsTable.CPU_INFO, jsonDetail, id);
        Assert.assertTrue(paramsMap.containsKey(Constant.CustomerParamsTable.CPU_INFO));
        Assert.assertEquals(1, paramsMap.get(Constant.CustomerParamsTable.CPU_INFO).size());
        Assert.assertEquals(mockCpuInfoDTO, paramsMap.get(Constant.CustomerParamsTable.CPU_INFO).get(0));
        Assert.assertEquals(id, ((CpuInfoDTO) paramsMap.get(Constant.CustomerParamsTable.CPU_INFO).get(0)).getId());
        Assert.assertEquals("testUser", ((CpuInfoDTO) paramsMap.get(Constant.CustomerParamsTable.CPU_INFO).get(0)).getCreateBy());

        // 测试场景 2: MEMORY_INFO
        MemoryInfoDTO mockMemoryInfoDTO = new MemoryInfoDTO();
        PowerMockito.when(JSON.parseObject(jsonDetail, MemoryInfoDTO.class)).thenReturn(mockMemoryInfoDTO);
        Whitebox.invokeMethod(service, "handleTableDataGrouping", paramsMap, dto, Constant.CustomerParamsTable.MEMORY_INFO, jsonDetail, id);
        Assert.assertTrue(paramsMap.containsKey(Constant.CustomerParamsTable.MEMORY_INFO));
        Assert.assertEquals(1, paramsMap.get(Constant.CustomerParamsTable.MEMORY_INFO).size());
        Assert.assertEquals(mockMemoryInfoDTO, paramsMap.get(Constant.CustomerParamsTable.MEMORY_INFO).get(0));

        // 测试场景 3: NETWORK_CARD_INFO
        NetworkCardInfoDTO mockNetworkCardInfoDTO = new NetworkCardInfoDTO();
        PowerMockito.when(JSON.parseObject(jsonDetail, NetworkCardInfoDTO.class)).thenReturn(mockNetworkCardInfoDTO);
        Whitebox.invokeMethod(service, "handleTableDataGrouping", paramsMap, dto, Constant.CustomerParamsTable.NETWORK_CARD_INFO, jsonDetail, id);
        Assert.assertTrue(paramsMap.containsKey(Constant.CustomerParamsTable.NETWORK_CARD_INFO));
        Assert.assertEquals(1, paramsMap.get(Constant.CustomerParamsTable.NETWORK_CARD_INFO).size());
        Assert.assertEquals(mockNetworkCardInfoDTO, paramsMap.get(Constant.CustomerParamsTable.NETWORK_CARD_INFO).get(0));

        // 测试场景 4: RAID_CARD_INFO
        RaidCardInfoDTO mockRaidCardInfoDTO = new RaidCardInfoDTO();
        PowerMockito.when(JSON.parseObject(jsonDetail, RaidCardInfoDTO.class)).thenReturn(mockRaidCardInfoDTO);
        Whitebox.invokeMethod(service, "handleTableDataGrouping", paramsMap, dto, Constant.CustomerParamsTable.RAID_CARD_INFO, jsonDetail, id);
        Assert.assertTrue(paramsMap.containsKey(Constant.CustomerParamsTable.RAID_CARD_INFO));
        Assert.assertEquals(1, paramsMap.get(Constant.CustomerParamsTable.RAID_CARD_INFO).size());
        Assert.assertEquals(mockRaidCardInfoDTO, paramsMap.get(Constant.CustomerParamsTable.RAID_CARD_INFO).get(0));

        // 测试场景 5: HARD_DISK_INFO
        HardDiskInfoDTO mockHardDiskInfoDTO = new HardDiskInfoDTO();
        PowerMockito.when(JSON.parseObject(jsonDetail, HardDiskInfoDTO.class)).thenReturn(mockHardDiskInfoDTO);
        Whitebox.invokeMethod(service, "handleTableDataGrouping", paramsMap, dto, Constant.CustomerParamsTable.HARD_DISK_INFO, jsonDetail, id);
        Assert.assertTrue(paramsMap.containsKey(Constant.CustomerParamsTable.HARD_DISK_INFO));
        Assert.assertEquals(1, paramsMap.get(Constant.CustomerParamsTable.HARD_DISK_INFO).size());
        Assert.assertEquals(mockHardDiskInfoDTO, paramsMap.get(Constant.CustomerParamsTable.HARD_DISK_INFO).get(0));

        // 测试场景 6: GPU_INFO
        GpuInfoDTO mockGpuInfoDTO = new GpuInfoDTO();
        PowerMockito.when(JSON.parseObject(jsonDetail, GpuInfoDTO.class)).thenReturn(mockGpuInfoDTO);
        Whitebox.invokeMethod(service, "handleTableDataGrouping", paramsMap, dto, Constant.CustomerParamsTable.GPU_INFO, jsonDetail, id);
        Assert.assertTrue(paramsMap.containsKey(Constant.CustomerParamsTable.GPU_INFO));
        Assert.assertEquals(1, paramsMap.get(Constant.CustomerParamsTable.GPU_INFO).size());
        Assert.assertEquals(mockGpuInfoDTO, paramsMap.get(Constant.CustomerParamsTable.GPU_INFO).get(0));

        // 测试场景 7: MOTHERBOARD_INFO
        MotherboardInfoDTO mockMotherboardInfoDTO = new MotherboardInfoDTO();
        PowerMockito.when(JSON.parseObject(jsonDetail, MotherboardInfoDTO.class)).thenReturn(mockMotherboardInfoDTO);
        Whitebox.invokeMethod(service, "handleTableDataGrouping", paramsMap, dto, Constant.CustomerParamsTable.MOTHERBOARD_INFO, jsonDetail, id);
        Assert.assertTrue(paramsMap.containsKey(Constant.CustomerParamsTable.MOTHERBOARD_INFO));
        Assert.assertEquals(1, paramsMap.get(Constant.CustomerParamsTable.MOTHERBOARD_INFO).size());
        Assert.assertEquals(mockMotherboardInfoDTO, paramsMap.get(Constant.CustomerParamsTable.MOTHERBOARD_INFO).get(0));

        // 测试场景 8: MACHINE_INFO
        MachineInfoDTO mockMachineInfoDTO = new MachineInfoDTO();
        PowerMockito.when(JSON.parseObject(jsonDetail, MachineInfoDTO.class)).thenReturn(mockMachineInfoDTO);
        Whitebox.invokeMethod(service, "handleTableDataGrouping", paramsMap, dto, Constant.CustomerParamsTable.MACHINE_INFO, jsonDetail, id);
        Assert.assertTrue(paramsMap.containsKey(Constant.CustomerParamsTable.MACHINE_INFO));
        Assert.assertEquals(1, paramsMap.get(Constant.CustomerParamsTable.MACHINE_INFO).size());
        Assert.assertEquals(mockMachineInfoDTO, paramsMap.get(Constant.CustomerParamsTable.MACHINE_INFO).get(0));

        // 测试场景 9: POWER_SOURCE
        PowerSourceInfoDTO mockPowerSourceInfoDTO = new PowerSourceInfoDTO();
        PowerMockito.when(JSON.parseObject(jsonDetail, PowerSourceInfoDTO.class)).thenReturn(mockPowerSourceInfoDTO);
        Whitebox.invokeMethod(service, "handleTableDataGrouping", paramsMap, dto, Constant.CustomerParamsTable.POWER_SOURCE, jsonDetail, id);
        Assert.assertTrue(paramsMap.containsKey(Constant.CustomerParamsTable.POWER_SOURCE));
        Assert.assertEquals(1, paramsMap.get(Constant.CustomerParamsTable.POWER_SOURCE).size());
        Assert.assertEquals(mockPowerSourceInfoDTO, paramsMap.get(Constant.CustomerParamsTable.POWER_SOURCE).get(0));

        // 测试场景 10: default 分支，触发异常
        try {
            Whitebox.invokeMethod(service, "handleTableDataGrouping", paramsMap, dto, "UNKNOWN_TABLE", jsonDetail, id);
        } catch (MesBusinessException e) {
            Assert.assertEquals(RetCode.BUSINESSERROR_CODE, e.getExCode());
            Assert.assertEquals(MessageId.CUSTOMER_PARAM_TABLE_ERROR, e.getExMsgId());
        }
    }

    @Test
    public void batchInsertTest() throws Exception {
        // Mock 数据
        String empNo = "testUser";
        List<CustomerImportDTO> reCheckResult = new ArrayList<>();
        List<CustomerImportDTO> list = new ArrayList<>();

        // 构造测试数据
        CustomerImportDTO importDTO1 = new CustomerImportDTO();
        importDTO1.setTableName("CPU_INFO");
        importDTO1.setCheckResult(""); // 正常数据
        list.add(importDTO1);
        reCheckResult.add(importDTO1);

        CustomerImportDTO importDTO2 = new CustomerImportDTO();
        importDTO2.setTableName("MEMORY_INFO");
        importDTO2.setCheckResult(""); // 正常数据
        list.add(importDTO2);
        reCheckResult.add(importDTO2);

        // Mock 私有方法 checkTaskBatch
        PowerMockito.stub(PowerMockito.method(CustomerItemsServiceImpl.class, "checkTaskBatch"))
                .toReturn(reCheckResult);

        // Mock checkNewParams 方法
        PowerMockito.stub(PowerMockito.method(CustomerItemsServiceImpl.class, "checkNewParams"))
                .toReturn("lockKey1");

        // Mock BeanUtil.copyProperties 方法
        PowerMockito.when(BeanUtil.copyProperties(Mockito.any(CustomerImportDTO.class), Mockito.eq(CustomerItemsDTO.class)))
                .thenAnswer(invocation -> {
                    CustomerImportDTO importDTO = invocation.getArgument(0);
                    CustomerItemsDTO itemsDTO = new CustomerItemsDTO();
                    return itemsDTO;
                });

        // Mock idGenerator.snowFlakeIdStr()
        PowerMockito.when(idGenerator.snowFlakeIdStr()).thenReturn("12345", "67890");

        // Mock RedisLock
        RedisLock mockRedisLock = Mockito.mock(RedisLock.class);
        PowerMockito.whenNew(RedisLock.class).withAnyArguments().thenReturn(mockRedisLock);
        Mockito.when(mockRedisLock.lock()).thenReturn(true); // 模拟加锁成功
        Mockito.doNothing().when(mockRedisLock).unlock(); // 模拟解锁

        // Mock JSON.toJSONString 方法
        PowerMockito.when(JSON.toJSONString(Mockito.any())).thenReturn("{\"key\":\"value\"}");

        // Mock batchInsertCustomerParams 方法
        PowerMockito.stub(PowerMockito.method(CustomerItemsServiceImpl.class, "batchInsertCustomerParams"))
                .toReturn(null);

        // 调用测试方法
        service.batchInsert(reCheckResult, list, empNo);
        Assert.assertEquals(1,1);
    }

    @Test
    public void testBatchInsertCustomerParams() {
        // 准备测试数据
        List<CustomerItemsDTO> customerItemsBatch = new ArrayList<>();
        List<CustomerImportDTO> customerImportBatch = new ArrayList<>();

        // 添加第一个测试数据
        CustomerItemsDTO itemsDTO1 = new CustomerItemsDTO();
        itemsDTO1.setProjectName("Property1");
        customerItemsBatch.add(itemsDTO1);

        CustomerImportDTO importDTO1 = new CustomerImportDTO();
        importDTO1.setTableName("gpu_info");
        importDTO1.setCheckResult("TestDetail1");
        importDTO1.setZteCode("TestDetail2");
        customerImportBatch.add(importDTO1);

        // 添加第二个测试数据
        CustomerItemsDTO itemsDTO2 = new CustomerItemsDTO();
        itemsDTO2.setProjectName("Property2");
        customerItemsBatch.add(itemsDTO2);

        CustomerImportDTO importDTO2 = new CustomerImportDTO();
        importDTO2.setTableName("gpu_info");
        importDTO2.setCheckResult("TestDetail2");
        importDTO2.setZteCode("TestDetail2");
        customerImportBatch.add(importDTO2);

        // 准备测试数据
        String jsonDetail = "{capacity:abcd11}";

        // 模拟 GpuInfoDTO 对象
        GpuInfoDTO mockGpuInfoDTO = new GpuInfoDTO();
        mockGpuInfoDTO.setCapacity("abcd11");
        // 设置其他字段...
        PowerMockito.when(JSON.toJSONString(Mockito.any())).thenReturn(jsonDetail);

        // 使用 PowerMockito 模拟 JSON.parseObject 方法
        PowerMockito.when(JSON.parseObject(jsonDetail, GpuInfoDTO.class)).thenReturn(mockGpuInfoDTO);

        // Mock idGenerator 返回的 ID
        when(idGenerator.snowFlakeIdStr()).thenReturn("ID1", "ID2");

        // 调用批量插入方法
        service.batchInsertCustomerParams(customerItemsBatch, customerImportBatch);

        Assert.assertEquals(1,1);
    }

    @Test
    public void saveMachineInfoTest() throws Exception {
        // 测试数据
        String id = "12345";
        String jsonDetail = "{\"key\":\"value\"}";
        CustomerItemsDTO dto = new CustomerItemsDTO();
        dto.setLastUpdatedBy("testUser");

        MachineInfoDTO mockMachineInfoDTO = new MachineInfoDTO();
        mockMachineInfoDTO.setId(id);
        mockMachineInfoDTO.setCreateBy("testUser");
        mockMachineInfoDTO.setLastUpdatedBy("testUser");

        // Mock JSON.parseObject 方法
        PowerMockito.when(JSON.parseObject(jsonDetail, MachineInfoDTO.class)).thenReturn(mockMachineInfoDTO);

        // Mock repository 的 batchInsertMachineInfo 行为
        Mockito.doNothing().when(customerItemsParamsRepository).batchInsertMachineInfo(Mockito.anyList());

        // 调用测试方法
        String result = Whitebox.invokeMethod(service, "saveMachineInfo", dto, jsonDetail, id);

        // 验证 JSON.parseObject 是否被调用
        PowerMockito.verifyStatic(JSON.class, Mockito.times(1));
        JSON.parseObject(jsonDetail, MachineInfoDTO.class);

        // 验证 repository.batchInsertMachineInfo 是否被调用
        Mockito.verify(customerItemsParamsRepository, Mockito.times(1))
                .batchInsertMachineInfo(Mockito.argThat(arg -> {
                    MachineInfoDTO machineInfo = (MachineInfoDTO) arg.get(0);
                    return machineInfo.getId().equals(id)
                            && machineInfo.getCreateBy().equals("testUser")
                            && machineInfo.getLastUpdatedBy().equals("testUser");
                }));

        // 验证返回值
        Assert.assertEquals(id, result);
    }

    @Test
    public void savePowerSourceInfoTest() throws Exception {
        // 测试数据
        String id = "67890";
        String jsonDetail = "{\"key\":\"value\"}";
        CustomerItemsDTO dto = new CustomerItemsDTO();
        dto.setLastUpdatedBy("anotherUser");

        PowerSourceInfoDTO mockPowerSourceInfoDTO = new PowerSourceInfoDTO();
        mockPowerSourceInfoDTO.setId(id);
        mockPowerSourceInfoDTO.setCreateBy("anotherUser");
        mockPowerSourceInfoDTO.setLastUpdatedBy("anotherUser");

        // Mock JSON.parseObject 方法
        PowerMockito.when(JSON.parseObject(jsonDetail, PowerSourceInfoDTO.class)).thenReturn(mockPowerSourceInfoDTO);

        // Mock repository 的 batchInsertPowerSourceInfo 行为
        Mockito.doNothing().when(customerItemsParamsRepository).batchInsertPowerSourceInfo(Mockito.anyList());

        // 调用测试方法
        String result = Whitebox.invokeMethod(service, "savePowerSourceInfo", dto, jsonDetail, id);

        // 验证 JSON.parseObject 是否被调用
        PowerMockito.verifyStatic(JSON.class, Mockito.times(1));
        JSON.parseObject(jsonDetail, PowerSourceInfoDTO.class);

        // 验证 repository.batchInsertPowerSourceInfo 是否被调用
        Mockito.verify(customerItemsParamsRepository, Mockito.times(1))
                .batchInsertPowerSourceInfo(Mockito.argThat(arg -> {
                    PowerSourceInfoDTO powerSourceInfo = (PowerSourceInfoDTO) arg.get(0);
                    return powerSourceInfo.getId().equals(id)
                            && powerSourceInfo.getCreateBy().equals("anotherUser")
                            && powerSourceInfo.getLastUpdatedBy().equals("anotherUser");
                }));

        // 验证返回值
        Assert.assertEquals(id, result);
    }

    @Test
    public void deleteOldParamsTest() throws Exception {
        // 测试数据
        CustomerItemsDTO dto = new CustomerItemsDTO();
        dto.setAdditionalInfoId("oldInfoId");
        dto.setCustomerComponentType("CPU");

        CustomerItemsDTO customerItemsDTO = new CustomerItemsDTO();
        customerItemsDTO.setAdditionalInfoId("newInfoId");
        customerItemsDTO.setCustomerComponentType("CPU");

        Map<String, String> paramsMap = new HashMap<>();
        paramsMap.put("CPU", Constant.CustomerParamsTable.CPU_INFO);

        // Mock repository 删除方法
        Mockito.doNothing().when(customerItemsParamsRepository).deleteCpuInfoById("newInfoId");

        // 调用测试方法
        Whitebox.invokeMethod(service, "deleteOldParams", dto, customerItemsDTO, paramsMap);

        // 验证 deleteCpuInfoById 被正确调用
        Mockito.verify(customerItemsParamsRepository, Mockito.times(1)).deleteCpuInfoById("newInfoId");
    }

    @Test
    public void deleteOldParamsTest_AllBranches() throws Exception {
        // 测试数据
        CustomerItemsDTO dto = new CustomerItemsDTO();
        dto.setAdditionalInfoId("oldInfoId");
        dto.setCustomerComponentType("GPU");

        CustomerItemsDTO customerItemsDTO = new CustomerItemsDTO();
        customerItemsDTO.setAdditionalInfoId("newInfoId");
        customerItemsDTO.setCustomerComponentType("GPU");

        Map<String, String> paramsMap = new HashMap<>();
        paramsMap.put("CPU", Constant.CustomerParamsTable.CPU_INFO);
        paramsMap.put("MEMORY", Constant.CustomerParamsTable.MEMORY_INFO);
        paramsMap.put("NETWORK_CARD", Constant.CustomerParamsTable.NETWORK_CARD_INFO);
        paramsMap.put("RAID_CARD", Constant.CustomerParamsTable.RAID_CARD_INFO);
        paramsMap.put("HARD_DISK", Constant.CustomerParamsTable.HARD_DISK_INFO);
        paramsMap.put("GPU", Constant.CustomerParamsTable.GPU_INFO);
        paramsMap.put("MOTHERBOARD", Constant.CustomerParamsTable.MOTHERBOARD_INFO);
        paramsMap.put("MACHINE", Constant.CustomerParamsTable.MACHINE_INFO);
        paramsMap.put("POWER_SOURCE", Constant.CustomerParamsTable.POWER_SOURCE);

        // Mock repository 删除方法
        Mockito.doNothing().when(customerItemsParamsRepository).deleteCpuInfoById(Mockito.anyString());
        Mockito.doNothing().when(customerItemsParamsRepository).deleteMemoryInfoById(Mockito.anyString());
        Mockito.doNothing().when(customerItemsParamsRepository).deleteNetworkCardInfoById(Mockito.anyString());
        Mockito.doNothing().when(customerItemsParamsRepository).deleteRaidCardInfoById(Mockito.anyString());
        Mockito.doNothing().when(customerItemsParamsRepository).deleteHardDiskInfoById(Mockito.anyString());
        Mockito.doNothing().when(customerItemsParamsRepository).deleteGpuInfoById(Mockito.anyString());
        Mockito.doNothing().when(customerItemsParamsRepository).deleteMotherboardInfoById(Mockito.anyString());
        Mockito.doNothing().when(customerItemsParamsRepository).deleteMachineInfoById(Mockito.anyString());
        Mockito.doNothing().when(customerItemsParamsRepository).deletePowerSourceInfoById(Mockito.anyString());

        // 遍历测试所有分支
        for (Map.Entry<String, String> entry : paramsMap.entrySet()) {
            customerItemsDTO.setCustomerComponentType(entry.getKey());
            paramsMap.put(entry.getKey(), entry.getValue());
            Whitebox.invokeMethod(service, "deleteOldParams", dto, customerItemsDTO, paramsMap);
        }

        // 验证每个删除方法被正确调用
        Mockito.verify(customerItemsParamsRepository, Mockito.times(1)).deleteCpuInfoById("newInfoId");
        Mockito.verify(customerItemsParamsRepository, Mockito.times(1)).deleteMemoryInfoById("newInfoId");
        Mockito.verify(customerItemsParamsRepository, Mockito.times(1)).deleteNetworkCardInfoById("newInfoId");
        Mockito.verify(customerItemsParamsRepository, Mockito.times(1)).deleteRaidCardInfoById("newInfoId");
        Mockito.verify(customerItemsParamsRepository, Mockito.times(1)).deleteHardDiskInfoById("newInfoId");
        Mockito.verify(customerItemsParamsRepository, Mockito.times(1)).deleteGpuInfoById("newInfoId");
        Mockito.verify(customerItemsParamsRepository, Mockito.times(1)).deleteMotherboardInfoById("newInfoId");
        Mockito.verify(customerItemsParamsRepository, Mockito.times(1)).deleteMachineInfoById("newInfoId");
        Mockito.verify(customerItemsParamsRepository, Mockito.times(1)).deletePowerSourceInfoById("newInfoId");
    }

    @Test
    public void testUpdateCustomerByTable_MachineInfo() throws Exception {
        // 准备输入数据
        CustomerItemsDTO dto = new CustomerItemsDTO();
        dto.setLastUpdatedBy("User1");
        dto.setParamsDetail("{\"lastUpdatedBy\":\"value\"}"); // 模拟 JSON 字符串

        String tableName = Constant.CustomerParamsTable.MACHINE_INFO;

        MachineInfoDTO mockMachineInfoDTO = new MachineInfoDTO();
        mockMachineInfoDTO.setLastUpdatedBy("User1");

        // Mock JSON.parseObject 返回的对象
        PowerMockito.when(JSON.toJSONString(Mockito.any())).thenReturn(dto.getParamsDetail().toString());
        PowerMockito.when(JSON.parseObject((String) dto.getParamsDetail(), MachineInfoDTO.class)).thenReturn(mockMachineInfoDTO);

        // 调用私有方法
        Whitebox.invokeMethod(service, "updateCustomerByTable", dto, tableName);

        // 验证 Repository 方法是否被调用
        verify(customerItemsParamsRepository, times(1)).updateMachineInfo(mockMachineInfoDTO);

        // 验证 JSON.parseObject 是否被正确调用
        PowerMockito.verifyStatic(JSON.class, times(1));
        JSON.parseObject((String) dto.getParamsDetail(), MachineInfoDTO.class);
    }

    @Test
    public void testUpdateCustomerByTable_PowerSource() throws Exception {
        // 准备输入数据
        CustomerItemsDTO dto = new CustomerItemsDTO();
        dto.setLastUpdatedBy("User2");
        dto.setParamsDetail("{\"lastUpdatedBy\":\"value\"}"); // 模拟 JSON 字符串

        String tableName = Constant.CustomerParamsTable.POWER_SOURCE;

        PowerSourceInfoDTO mockPowerSourceInfoDTO = new PowerSourceInfoDTO();
        mockPowerSourceInfoDTO.setLastUpdatedBy("User2");

        // Mock JSON.parseObject 返回的对象
        PowerMockito.when(JSON.toJSONString(Mockito.any())).thenReturn(dto.getParamsDetail().toString());
        PowerMockito.when(JSON.parseObject((String) dto.getParamsDetail(), PowerSourceInfoDTO.class)).thenReturn(mockPowerSourceInfoDTO);

        // 调用私有方法
        Whitebox.invokeMethod(service, "updateCustomerByTable", dto, tableName);

        // 验证 Repository 方法是否被调用
        verify(customerItemsParamsRepository, times(1)).updatePowerSourceInfo(mockPowerSourceInfoDTO);

        // 验证 JSON.parseObject 是否被正确调用
        PowerMockito.verifyStatic(JSON.class, times(1));
        JSON.parseObject((String) dto.getParamsDetail(), PowerSourceInfoDTO.class);
    }

    @Test
    public void testValidateDatabaseExistence_WithVariousProjectTypes() throws Exception {
        // 准备测试数据
        List<CustomerImportDTO> batch = new ArrayList<>();

        // 构造第一个 CustomerImportDTO (projectType 为 3)
        CustomerImportDTO customerImportDTO1 = new CustomerImportDTO();
        customerImportDTO1.setZteCode("ZTE003");
        customerImportDTO1.setProjectType("3");
        customerImportDTO1.setCustomerName("CustomerA");
        customerImportDTO1.setProjectName("ProjectX");
        customerImportDTO1.setCheckResult(""); // 初始校验结果
        batch.add(customerImportDTO1);

        // 构造第二个 CustomerImportDTO (projectType 为 4)
        CustomerImportDTO customerImportDTO2 = new CustomerImportDTO();
        customerImportDTO2.setZteCode("ZTE004");
        customerImportDTO2.setProjectType("4");
        customerImportDTO2.setCustomerName("CustomerB");
        customerImportDTO2.setZteSupplier("SupplierB");
        customerImportDTO2.setZteBrandStyle("BrandB");
        customerImportDTO2.setCheckResult(""); // 初始校验结果
        batch.add(customerImportDTO2);

        // 构造第三个 CustomerImportDTO (projectType 为 5)
        CustomerImportDTO customerImportDTO3 = new CustomerImportDTO();
        customerImportDTO3.setZteCode("ZTE005");
        customerImportDTO3.setProjectType("5");
        customerImportDTO3.setCustomerName("CustomerC");
        customerImportDTO3.setZteBrandStyle("BrandC");
        customerImportDTO3.setCheckResult(""); // 初始校验结果
        batch.add(customerImportDTO3);

        // 模拟 BeanUtil.copyProperties 静态方法行为
        PowerMockito.when(BeanUtil.copyProperties(Mockito.any(CustomerImportDTO.class), eq(CustomerItemsDTO.class)))
                .thenAnswer(invocation -> {
                    CustomerImportDTO dto = invocation.getArgument(0);
                    CustomerItemsDTO itemsDTO = new CustomerItemsDTO();
                    itemsDTO.setZteCode(dto.getZteCode());
                    itemsDTO.setProjectType(dto.getProjectType());
                    itemsDTO.setCustomerName(dto.getCustomerName());
                    itemsDTO.setZteSupplier(dto.getZteSupplier());
                    itemsDTO.setZteBrandStyle(dto.getZteBrandStyle());
                    itemsDTO.setProjectName(dto.getProjectName());
                    return itemsDTO;
                });

        // 构造数据库查询结果（修改为 CustomerItemsDTO 类型）
        List<CustomerItemsDTO> queryResults = new ArrayList<>();

        CustomerItemsDTO result1 = new CustomerItemsDTO();
        result1.setZteCode("ZTE003");
        result1.setProjectType("3");
        result1.setCustomerName("CustomerA");
        result1.setProjectName("ProjectX");
        queryResults.add(result1);

        CustomerItemsDTO result2 = new CustomerItemsDTO();
        result2.setZteCode("ZTE004");
        result2.setProjectType("4");
        result2.setCustomerName("CustomerB");
        result2.setZteSupplier("SupplierB");
        result2.setZteBrandStyle("BrandB");
        queryResults.add(result2);

        CustomerItemsDTO result3 = new CustomerItemsDTO();
        result3.setZteCode("ZTE005");
        result3.setProjectType("5");
        result3.setCustomerName("CustomerC");
        result3.setZteBrandStyle("BrandC");
        // 不添加记录，模拟数据库中不存在
        // queryResults.add(result3);

        // Mock repository.checkItemsExistBatch 的行为
        when(repository.checkItemsExistBatch(Mockito.anyList())).thenReturn(queryResults);

        // 调用私有方法
        Whitebox.invokeMethod(service, "validateDatabaseExistence", batch);

        // 验证第一条数据 (projectType 为 3) 的校验结果
        assertEquals(Constant.CUSTOMER_ITEMS_EXIST, customerImportDTO1.getCheckResult());

        // 验证第二条数据 (projectType 为 4) 的校验结果
        assertEquals(Constant.CUSTOMER_ITEMS_EXIST, customerImportDTO2.getCheckResult());

        // 验证第三条数据 (projectType 为 5) 的校验结果
        assertEquals("", customerImportDTO3.getCheckResult());

        // 验证 repository 方法是否被调用一次
        verify(repository, times(1)).checkItemsExistBatch(Mockito.anyList());
    }



    @Test
    public void batchInsertTest_NormalFlow() throws Exception {
        // Mock 数据
        String empNo = "testUser";
        List<CustomerImportDTO> reCheckResult = new ArrayList<>();
        List<CustomerImportDTO> list = new ArrayList<>();

        // 构造测试数据
        CustomerImportDTO importDTO1 = new CustomerImportDTO();
        importDTO1.setTableName("CPU_INFO");
        importDTO1.setCheckResult(""); // 正常数据
        list.add(importDTO1);
        reCheckResult.add(importDTO1);

        CustomerImportDTO importDTO2 = new CustomerImportDTO();
        importDTO2.setTableName("MEMORY_INFO");
        importDTO2.setCheckResult(""); // 正常数据
        list.add(importDTO2);
        reCheckResult.add(importDTO2);

        // Mock 私有方法 checkTaskBatch
        PowerMockito.stub(PowerMockito.method(CustomerItemsServiceImpl.class, "checkTaskBatch"))
                .toReturn(reCheckResult);

        // Mock checkNewParams 方法
        PowerMockito.stub(PowerMockito.method(CustomerItemsServiceImpl.class, "checkNewParams"))
                .toReturn("lockKey1");

        // Mock BeanUtil.copyProperties 方法
        PowerMockito.when(BeanUtil.copyProperties(Mockito.any(CustomerImportDTO.class), Mockito.eq(CustomerItemsDTO.class)))
                .thenAnswer(invocation -> {
                    CustomerImportDTO importDTO = invocation.getArgument(0);
                    CustomerItemsDTO itemsDTO = new CustomerItemsDTO();
                    return itemsDTO;
                });

        // Mock idGenerator.snowFlakeIdStr()
        PowerMockito.when(idGenerator.snowFlakeIdStr()).thenReturn("12345", "67890");

        // Mock RedisLock
        RedisLock mockRedisLock = Mockito.mock(RedisLock.class);
        PowerMockito.whenNew(RedisLock.class).withAnyArguments().thenReturn(mockRedisLock);
        Mockito.when(mockRedisLock.lock()).thenReturn(true); // 模拟加锁成功
        Mockito.doNothing().when(mockRedisLock).unlock(); // 模拟解锁

        // Mock JSON.toJSONString 方法
        PowerMockito.when(JSON.toJSONString(Mockito.any())).thenReturn("{\"key\":\"value\"}");

        // Mock batchInsertCustomerParams 方法
        PowerMockito.stub(PowerMockito.method(CustomerItemsServiceImpl.class, "batchInsertCustomerParams"))
                .toReturn(null);

        // 调用测试方法
        Whitebox.invokeMethod(service, "batchInsert", reCheckResult, list, empNo);

        Assert.assertEquals(1, 1); // 验证正常流程
    }

    @Test(expected = MesBusinessException.class)
    public void batchInsertTest_WithCheckResultError() throws Exception {
        // Mock 数据
        String empNo = "testUser";
        List<CustomerImportDTO> reCheckResult = new ArrayList<>();
        List<CustomerImportDTO> list = new ArrayList<>();

        // 构造测试数据
        CustomerImportDTO importDTO1 = new CustomerImportDTO();
        importDTO1.setTableName("CPU_INFO");
        importDTO1.setCheckResult("Error"); // 模拟校验失败的情况
        reCheckResult.add(importDTO1);

        // Mock 私有方法 checkTaskBatch
        PowerMockito.stub(PowerMockito.method(CustomerItemsServiceImpl.class, "checkTaskBatch"))
                .toReturn(reCheckResult);

        // 调用私有方法，期望抛出校验异常
        Whitebox.invokeMethod(service, "batchInsert", reCheckResult, list, empNo);
    }

    @Test(expected = MesBusinessException.class)
    public void batchInsertTest_LockFailed() throws Exception {
        // Mock 数据
        String empNo = "testUser";
        List<CustomerImportDTO> reCheckResult = new ArrayList<>();
        List<CustomerImportDTO> list = new ArrayList<>();

        // 构造测试数据
        CustomerImportDTO importDTO1 = new CustomerImportDTO();
        importDTO1.setTableName("CPU_INFO");
        importDTO1.setCheckResult(""); // 正常数据
        list.add(importDTO1);
        reCheckResult.add(importDTO1);

        // Mock 私有方法 checkTaskBatch
        PowerMockito.stub(PowerMockito.method(CustomerItemsServiceImpl.class, "checkTaskBatch"))
                .toReturn(reCheckResult);

        // Mock checkNewParams 方法
        PowerMockito.stub(PowerMockito.method(CustomerItemsServiceImpl.class, "checkNewParams"))
                .toReturn("lockKey1");

        // Mock BeanUtil.copyProperties 方法
        PowerMockito.when(BeanUtil.copyProperties(Mockito.any(CustomerImportDTO.class), Mockito.eq(CustomerItemsDTO.class)))
                .thenAnswer(invocation -> {
                    CustomerImportDTO importDTO = invocation.getArgument(0);
                    CustomerItemsDTO itemsDTO = new CustomerItemsDTO();
                    return itemsDTO;
                });

        // Mock idGenerator.snowFlakeIdStr()
        PowerMockito.when(idGenerator.snowFlakeIdStr()).thenReturn("12345", "67890");

        // Mock RedisLock
        RedisLock mockRedisLock = Mockito.mock(RedisLock.class);
        PowerMockito.whenNew(RedisLock.class).withAnyArguments().thenReturn(mockRedisLock);
        Mockito.when(mockRedisLock.lock()).thenReturn(false); // 模拟加锁失败

        // 调用私有方法，期望抛出加锁失败异常
        Whitebox.invokeMethod(service, "batchInsert", reCheckResult, list, empNo);
    }

    @Test
    public void testValidateBasicInfoOne() throws Exception {
        // 定义测试数据
        Object[][] testCases = {
                // 测试用例格式: { customerName, cooperationMode, projectPhase, expectedErrorContains }
                { "Customer A", "Mode 1", "Phase 1", "" }, // 全部合法输入
                { null, "Mode 1", "Phase 1", Constant.CUSTOMER_NAME_CANNOT_BE_EMPTY }, // customerName 为空
                { "", "Mode 1", "Phase 1", Constant.CUSTOMER_NAME_CANNOT_BE_EMPTY }, // customerName 为空字符串
                { "Invalid Customer", "Mode 1", "Phase 1", Constant.CUSTOMER_NAME_MUST_BE }, // customerName 不在合法列表中
                { "Customer A", "Invalid Mode", "Phase 1", Constant.COOPERATION_MODE_MUST_BE }, // cooperationMode 不在合法列表中
                { "Customer A", "Mode 1", "Invalid Phase", Constant.PROJECT_PHASE_MUST_BE }, // projectPhase 不在合法列表中
                { "", "", "", Constant.CUSTOMER_NAME_CANNOT_BE_EMPTY }, // 所有字段为空字符串
                { null, null, null, Constant.CUSTOMER_NAME_CANNOT_BE_EMPTY } // 所有字段为 null
        };

        // 遍历测试用例
        for (Object[] testCase : testCases) {
            String customerName = (String) testCase[0];
            String cooperationMode = (String) testCase[1];
            String projectPhase = (String) testCase[2];
            String expectedErrorContains = (String) testCase[3];

            // 初始化 StringBuilder 和 DTO
            StringBuilder sb = new StringBuilder();
            CustomerImportDTO dto = new CustomerImportDTO();
            dto.setCustomerName(customerName);
            dto.setCooperationMode(cooperationMode);
            dto.setProjectPhase(projectPhase);

            // 调用私有方法
            Whitebox.invokeMethod(service, "validateBasicInfoOne", dto, sb, customerNames, cooperationModes, projectPhases);

            // 校验结果
            String result = sb.toString();
            if (expectedErrorContains.isEmpty()) {
                // 如果没有期望的错误，结果应该为空
                assertEquals("StringBuilder 应该为空", 0, result.length());
            } else {
                // 如果有期望的错误，结果应该包含该错误信息
                assertTrue("StringBuilder 应该包含: " + expectedErrorContains, result.contains(expectedErrorContains));
            }
        }
    }

    @Test
    public void testValidateBasicInfoTwo() throws Exception {
        // 定义测试数据
        Object[][] testCases = {
                // 测试用例格式: { projType, projectType, expectedErrorContains, expectedProjType, expectedProjectType }
                { "Type A", "Project X", "", "1", "101" }, // 全部合法输入
                { null, "Project X", Constant.PROJECT_TYPE_CANNOT_BE_EMPTY, null, "101" }, // projType 为空
                { "", "Project X", Constant.PROJECT_TYPE_CANNOT_BE_EMPTY, null, "101" }, // projType 为空字符串
                { "Invalid Type", "Project X", Constant.PROJECT_TYPE_INPUT_ERROR, null, "101" }, // projType 不在映射中
                { "Type A", null, Constant.TYPE_CANNOT_BE_EMPTY, "1", null }, // projectType 为空
                { "Type A", "", Constant.TYPE_CANNOT_BE_EMPTY, "1", null }, // projectType 为空字符串
                { "Type A", "Invalid Project", Constant.TYPE_INPUT_ERROR, "1", null }, // projectType 不在映射中
                { null, null, Constant.PROJECT_TYPE_CANNOT_BE_EMPTY + Constant.TYPE_CANNOT_BE_EMPTY, null, null } // 全部为空
        };

        // 遍历测试用例
        for (Object[] testCase : testCases) {
            String projType = (String) testCase[0];
            String projectType = (String) testCase[1];
            String expectedErrorContains = (String) testCase[2];
            String expectedProjType = (String) testCase[3];
            String expectedProjectType = (String) testCase[4];

            // 初始化 StringBuilder 和 DTO
            StringBuilder sb = new StringBuilder();
            CustomerImportDTO dto = new CustomerImportDTO();
            dto.setProjType(projType);
            dto.setProjectType(projectType);

            // 调用私有方法
            Whitebox.invokeMethod(service, "validateBasicInfoTwo", dto, sb, projTypeMap, projectTypeMap);

            // 校验错误信息
            String result = sb.toString();
            if (StringUtils.isEmpty(expectedErrorContains)) {
                // 如果没有期望的错误信息，结果应该为空
                assertEquals("StringBuilder 应该为空", 0, result.length());
            } else {
                // 如果有期望的错误信息，结果应该包含该错误信息
                assertTrue("StringBuilder 应该包含: " + expectedErrorContains, result.contains(expectedErrorContains));
            }
        }
    }

    @Test
    public void testValidateProjectRelatedInfo() throws Exception {
        // 定义测试数据
        Object[][] testCases = {
                // 测试用例格式: { projectType, boardType, customerModel, zteCode, customerComponentType, strCustomerComponentType, expectedErrorContains, expectedBoardType }
                { "0", "Board A", null, "Z123", "Type1", "Type1", "", "100" }, // 合法输入 (类型为0，板码类型有效)
                { "0", "Invalid Board", null, "Z123", "Type1", "Type1", Constant.BOARD_TYPE_INPUT_ERROR, null }, // 类型为0，板码类型无效
                { "3", null, null, "Z123", "Type1", "Type1", Constant.CUSTOMER_MODEL_CANNOT_BE_EMPTY, null }, // 类型为3，客户型号为空
                { "3", null, "Model A", null, "Type1", "Type1", Constant.ZTE_CODE_CANNOT_BE_EMPTY, null }, // 类型为3，zteCode 为空
                { "1", "Board A", null, "Z123", "Type1", "Type2", Constant.CUSTOMER_COMPONENT_TYPE_NOT_CONSISTENT, "100" }, // 类型为1，组件类型不一致
                { "2", "Invalid Board", null, "Z123", "Type1", "Type1", Constant.BOARD_TYPE_INPUT_ERROR, null }, // 类型为2，板码类型无效
                { "4", "Board A", null, "Z123", "Type1", "Type1", Constant.PLEASE_REMOVE_BOARD_TYPE, null }, // 类型为4，要求移除板码类型
                { "3", null, null, null, "", "", Constant.CUSTOMER_MODEL_CANNOT_BE_EMPTY + Constant.ZTE_CODE_CANNOT_BE_EMPTY + Constant.CUSTOMER_COMPONENT_TYPE_CANNOT_BE_EMPTY, null } // 所有必填字段为空
        };

        // 遍历测试用例
        for (Object[] testCase : testCases) {
            String projectType = (String) testCase[0];
            String boardType = (String) testCase[1];
            String customerModel = (String) testCase[2];
            String zteCode = (String) testCase[3];
            String customerComponentType = (String) testCase[4];
            String strCustomerComponentType = (String) testCase[5];
            String expectedErrorContains = (String) testCase[6];
            String expectedBoardType = (String) testCase[7];

            // 初始化 StringBuilder 和 DTO
            StringBuilder sb = new StringBuilder();
            CustomerImportDTO dto = new CustomerImportDTO();
            dto.setProjectType(projectType);
            dto.setBoardType(boardType);
            dto.setCustomerModel(customerModel);
            dto.setZteCode(zteCode);
            dto.setCustomerComponentType(customerComponentType);
            dto.setStrCustomerComponentType(strCustomerComponentType);

            // 调用私有方法
            Whitebox.invokeMethod(service, "validateProjectRelatedInfo", dto, sb, boardTypeMap);

            // 校验错误信息
            String result = sb.toString();
            if (StringUtils.isEmpty(expectedErrorContains)) {
                // 如果没有期望的错误信息，结果应该为空
                assertEquals("StringBuilder 应该为空", 0, result.length());
            } else {
                // 如果有期望的错误信息，结果应该包含该错误信息
                assertTrue("StringBuilder 应该包含: " + expectedErrorContains, result.contains(expectedErrorContains));
            }
        }
    }

    @Test
    public void testGetSysLookupDescriptions_EmptyResult() throws Exception {
        // 模拟返回空列表
        PowerMockito.when(lookupValuesRepository.selectValuesByType(Mockito.any())).thenReturn(Collections.emptyList());
        PowerMockito.when(CollectionUtils.isEmpty(Mockito.any())).thenReturn(true);

        // 使用 Whitebox 调用私有方法并断言抛出异常
        assertThrows(Exception.class, () -> {
            Whitebox.invokeMethod(service, "getSysLookupDescriptions", 7300);
        });

        assertThrows(Exception.class, () -> {
            Whitebox.invokeMethod(service, "getSysLookupMap", 7300);
        });




    }
    /* Ended by AICoder, pid:2cdb8af4593648e08481f775236498e0 */
    @Test
    public void queryListByCustomerAndItemNoListTest() {
        CustomerItemsDTO customerItemsDTO = new CustomerItemsDTO();
        customerItemsDTO.setCustomerName("test");
        List<String> itemNoList = new ArrayList<>();
        itemNoList.add("test");
        customerItemsDTO.setItemNoList(itemNoList);
        List<CustomerItemsDTO> r = new ArrayList<>();
        r.add(new CustomerItemsDTO());
        PowerMockito.when(repository.getCustomerItemsInfoSelfDevelopedByItemNoList(Mockito.any())).thenReturn(r);
        List<CustomerItemsDTO> customerItemsDTOList = service.queryListByCustomerAndItemNoList(customerItemsDTO);
        Assert.assertTrue(customerItemsDTOList.size() == 0);
    }

    @Test
    public void testDeleteCustomerItemsByZteCodes_EmptyInput() {
        List<String> zteCodes = Collections.emptyList();

        Integer result = service.deleteCustomerItemsByZteCodes(zteCodes);

        assertEquals(Integer.valueOf(0), result);
    }

    /**
     * 测试 deleteCustomerItemsByZteCodes 方法，正常删除。
     */
    @Test
    public void testDeleteCustomerItemsByZteCodes_NormalCase() {
        List<String> zteCodes = Arrays.asList("code1", "code2");
        when(repository.deleteCustomerItemsByZteCodes(zteCodes)).thenReturn(2);

        Integer result = service.deleteCustomerItemsByZteCodes(zteCodes);

        assertEquals(Integer.valueOf(2), result);
        verify(repository).deleteCustomerItemsByZteCodes(zteCodes);
    }

    /**
     * 测试 queryListByZteCodes 方法，输入为空的情况。
     */
    @Test
    public void testQueryListByZteCodes_EmptyInput() {
        List<String> zteCodes = Collections.emptyList();

        List<CustomerItemsDTO> result = service.queryListByZteCodes(zteCodes);

        assertTrue(result.isEmpty());
    }

    /**
     * 测试 queryListByZteCodes 方法，正常查询。
     */
    @Test
    public void testQueryListByZteCodes_NormalCase() {
        List<String> zteCodes = Arrays.asList("code1", "code2");
        List<CustomerItemsDTO> expectedList = Arrays.asList(new CustomerItemsDTO(), new CustomerItemsDTO());
        when(repository.queryListByZteCodes(zteCodes)).thenReturn(expectedList);

        List<CustomerItemsDTO> result = service.queryListByZteCodes(zteCodes);

        assertEquals(expectedList, result);
        verify(repository).queryListByZteCodes(zteCodes);
    }

    @Test
    public void testBatchInsert_EmptyList() {
        List<CustomerItemsDTO> customerItemsDTOList = Collections.emptyList();

        int result = service.batchInsert(customerItemsDTOList);

        assertEquals(0, result);
        verify(repository, never()).batchInsertCustomerItems(anyList());
    }

    /* Started by AICoder, pid:nea3fh0bd9xfda114b160be7304db223bfa2882d */
    @Test
    public void getZteCodeByCustomerName() {
        List<String> customerNameList = new ArrayList<>();
        int rows = 0;
        int page = 0;
        List<CustomerItemsDTO> result = service.getZteCodeByCustomerName(customerNameList, rows, page);
        Assert.assertTrue(result.isEmpty());

        customerNameList.add("ali");
        result = service.getZteCodeByCustomerName(customerNameList, rows, page);
        Assert.assertTrue(result.isEmpty());

        rows = 10000;
        page = 1;
        result = service.getZteCodeByCustomerName(customerNameList, rows, page);
        Assert.assertTrue(result.isEmpty());

        rows = 100;
        result = service.getZteCodeByCustomerName(customerNameList, rows, page);
        Assert.assertTrue(result.isEmpty());
    }
    /* Ended by AICoder, pid:nea3fh0bd9xfda114b160be7304db223bfa2882d */

    @Test
    public void getZteCodeByCustomerName2() {
        List<String> customerNameList = new ArrayList<>();
        int rows = 0;
        int page = 0;
        PowerMockito.mockStatic(CollectionUtils.class);
        PowerMockito.when(CollectionUtils.isEmpty(anyList())).thenReturn(true);
        List<CustomerItemsDTO> result = service.getZteCodeByCustomerName(customerNameList, rows, page);
        Assert.assertTrue(result.isEmpty());
    }
    /**
     * 测试 batchInsert 方法，正常插入。
     */
    @Test
    public void testBatchInsert_NormalCase() {
        List<CustomerItemsDTO> customerItemsDTOList = Arrays.asList(new CustomerItemsDTO(), new CustomerItemsDTO());

        int result = service.batchInsert(customerItemsDTOList);

        assertEquals(2, result);
        verify(repository).batchInsertCustomerItems(customerItemsDTOList);
        for (CustomerItemsDTO item : customerItemsDTOList) {
            assertEquals(Constant.FLAG_Y, item.getStatus());
            assertEquals(Constant.STRING_EMPTY, item.getRemark());
        }
    }

}
