package com.zte.application.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.zte.application.AuxMaterialMountingService;
import com.zte.application.AuxMaterialTracingService;
import com.zte.application.BProdBomService;
import com.zte.application.BSmtBomDetailService;
import com.zte.application.IMESLogService;
import com.zte.application.PkCodeInfoService;
import com.zte.application.PsScanHistoryService;
import com.zte.application.PsWipInfoService;
import com.zte.application.SmtItemAvailableTimeService;
import com.zte.application.SmtMachineMTLHistoryLService;
import com.zte.application.SmtMachineMaterialMoutingService;
import com.zte.application.SmtSnMtlTracingTService;
import com.zte.application.WorkorderOnlineService;
import com.zte.common.CommonUtils;
import com.zte.common.DateUtil;
import com.zte.common.NetUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.BusinessConstant;
import com.zte.common.utils.Constant;
import com.zte.common.utils.CraftConstant;
import com.zte.common.utils.ExcelName;
import com.zte.common.utils.GenerateUUIDUtil;
import com.zte.common.utils.MpConstant;
import com.zte.common.utils.NumConstant;
import com.zte.common.utils.ObtainRemoteServiceDataUtil;
import com.zte.common.utils.RedisKeyConstant;
import com.zte.common.utils.SqlUtils;
import com.zte.consts.CommonConst;
import com.zte.domain.model.AuxMaterialMouting;
import com.zte.domain.model.BSProcess;
import com.zte.domain.model.BSmtBomDetail;
import com.zte.domain.model.BsItemInfo;
import com.zte.domain.model.BsPubHrvOrgId;
import com.zte.domain.model.CFLine;
import com.zte.domain.model.PkCodeInfo;
import com.zte.domain.model.PsEntityPlanBasic;
import com.zte.domain.model.PsScanHistory;
import com.zte.domain.model.PsScanHistoryCollect;
import com.zte.domain.model.PsScanHistoryRepository;
import com.zte.domain.model.PsTask;
import com.zte.domain.model.PsWipInfo;
import com.zte.domain.model.PsWorkOrderBasic;
import com.zte.domain.model.SmtItemAvailableTime;
import com.zte.domain.model.SmtMachineMTLHistoryL;
import com.zte.domain.model.SmtMachineMaterialMouting;
import com.zte.domain.model.SmtMachineMaterialPrepare;
import com.zte.domain.model.SmtMachineMaterialPrepareRepository;
import com.zte.domain.model.SmtSnMtlTracingT;
import com.zte.domain.model.WipBindTaskInfo;
import com.zte.domain.model.WipScanHisExtraInfo;
import com.zte.domain.model.WipScanHisExtraRepository;
import com.zte.domain.model.WorkorderOnline;
import com.zte.infrastructure.feign.CenterFactoryFeignService;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.CrafttechRemoteService;
import com.zte.infrastructure.remote.EqpmgmtsRemoteService;
import com.zte.infrastructure.remote.HrmUserInfoService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.infrastructure.remote.ProductionDeliveryRemoteService;
import com.zte.interfaces.assembler.PsScanHistoryAssembler;
import com.zte.interfaces.dto.AuxMaterialTracingEntityDTO;
import com.zte.interfaces.dto.BManufactureCapacityDTO;
import com.zte.interfaces.dto.BProdBomHeaderDTO;
import com.zte.interfaces.dto.BSProcessDTO;
import com.zte.interfaces.dto.BindingBarcodeDTO;
import com.zte.interfaces.dto.BindingBarcodeDataForProdplanIdDto;
import com.zte.interfaces.dto.CtWorkStationsDTO;
import com.zte.interfaces.dto.EmEqpInteractiveDTO;
import com.zte.interfaces.dto.EmEqpPdcountDTO;
import com.zte.interfaces.dto.EmEqpSpiBoardDTO;
import com.zte.interfaces.dto.FlowControlInfoDTO;
import com.zte.interfaces.dto.MakeupRelationshipDTO;
import com.zte.interfaces.dto.OfflineExportDTO;
import com.zte.interfaces.dto.PmScanConditionDTO;
import com.zte.interfaces.dto.ProductInfoQueryIntegrateRequest;
import com.zte.interfaces.dto.ProductInfoQueryIntegrateResponse;
import com.zte.interfaces.dto.PsScanHistoryDTO;
import com.zte.interfaces.dto.PsWipInfoDTO;
import com.zte.interfaces.dto.PsWorkOrderDTO;
import com.zte.interfaces.dto.SMTScanDTO;
import com.zte.interfaces.dto.SMTScanParamDTO;
import com.zte.interfaces.dto.SmtLocationInfoDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.interfaces.dto.SysLookupValuesDTO;
import com.zte.interfaces.dto.WorkOrderDateDTO;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.exception.BusiException;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.itp.msa.util.string.DateHelper;
import com.zte.itp.msa.util.string.StringHelper;
import com.zte.microservice.name.MicroServiceNameEum;
import com.zte.springbootframe.common.annotation.Idempotnal;
import com.zte.springbootframe.common.annotation.IdempotnalKey;
import com.zte.springbootframe.common.annotation.RecordLogAnnotation;
import com.zte.springbootframe.common.annotation.RedisDistributedLockAnnotation;
import com.zte.springbootframe.common.annotation.RedisLockParamAnnotation;
import com.zte.springbootframe.common.consts.SysConst;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.FactoryConfig;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.redis.RedisLock;
import com.zte.springbootframe.util.CloudDiskHelper;
import com.zte.springbootframe.util.EmailUtils;
import com.zte.springbootframe.util.FileUtils;
import com.zte.springbootframe.util.ImesExcelUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.ThreadUtil;
import com.zte.ums.nfv.eco.hsif.msb.common.RouteException;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeSet;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 扫描历史表service实现类
 *
 * <AUTHOR>
 **/
@Service
public class PsScanHistoryServiceImpl implements PsScanHistoryService {
    private static final String REGEX_PARENT_SN = "P[0-9]{12}";

    // 日志对象
    private static final Logger LOG = LoggerFactory.getLogger(PsScanHistoryServiceImpl.class);
    @Autowired
    private PsScanHistoryRepository psScanHistoryRepository;

    @Autowired
    private CenterfactoryRemoteService centerfactoryRemoteService;
    @Autowired
    private PkCodeInfoService pkCodeInfoService;
    @Autowired
    private SmtMachineMaterialMoutingService smtMachineMaterialMoutingService;
    @Autowired
    private BSmtBomDetailService bSmtBomDetailService;
    @Autowired
    private SmtMachineMaterialPrepareRepository smtMachineMaterialPrepareRepository;
    @Autowired
    private PsWipInfoService psWipInfoService;

    @Autowired
    private HrmUserInfoService hrmUserInfoService;

    /**
     * 条码物料追溯Service
     */
    @Autowired
    private SmtSnMtlTracingTService smtSnMtlTracingTService;

    @Autowired
    private SmtItemAvailableTimeService smtItemAvailableTimeService;

    @Autowired
    private WorkorderOnlineService workorderOnlineService;

    @Autowired
    private SmtMachineMTLHistoryLService smtMachineMTLHistoryLService;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private FactoryConfig factoryConfig;

    @Autowired
    private IMESLogService imesLogService;

    @Autowired
    private EmailUtils emailUtils;
    @Autowired
    private RedisTemplate<String, Object> redisTemplateExport;
    @Autowired
    private CenterFactoryFeignService centerFactoryFeignService;
    @Autowired
    private CloudDiskHelper cloudDiskHelper;
    @Autowired
    private AuxMaterialMountingService auxMaterialMountingService;
    @Autowired
    private AuxMaterialTracingService auxMaterialTracingService;
    @Autowired
    private WipScanHisExtraRepository wipScanHisExtraRepository;
    @Autowired
    private BProdBomService bProdBomService;
    private Map lineCodeMap = new HashMap<String, String>();


    private Map workStationMap = new HashMap<String, String>();


    private static final String SPI = "SPI";
    private static final int SEVEN = 7;


    public void setPsScanHistoryRepository(PsScanHistoryRepository psScanHistoryRepository) {

        this.psScanHistoryRepository = psScanHistoryRepository;
    }

    public void setPsWipInfoService(PsWipInfoService psWipInfoService) {

        this.psWipInfoService = psWipInfoService;
    }

    /**
     * 增加实体数据
     *
     * @param record FlowControlInfoDTO实体
     * @return 插入数量
     **/
    @Override
    public int insertPsScanHistoryByScan(FlowControlInfoDTO record) {

        PsScanHistory scanHistory = createScanHistoryEntity(record);
        // 多选子工序插入多条记录
        List<PsScanHistory> historiesList = new LinkedList<>();
        // 扫描历史附加信息表新增数据
        List<WipScanHisExtraInfo> historiesExtraList = new LinkedList<>();
        if (!CollectionUtils.isEmpty(record.getProcessInfoList())) {
            List<FlowControlInfoDTO> processInfoList = record.getProcessInfoList();
            processInfoList.sort(Comparator.comparing(FlowControlInfoDTO::getSeqSort));
            PsScanHistory scanHistoryTemp;
            Calendar instance = Calendar.getInstance();
            for (int i = 0; i < processInfoList.size(); i++) {
                instance.add(Calendar.SECOND, 5);
                FlowControlInfoDTO flowControlInfoDTO = processInfoList.get(i);
                scanHistoryTemp = new PsScanHistory();
                BeanUtils.copyProperties(scanHistory, scanHistoryTemp);
                scanHistoryTemp.setWorkStation(flowControlInfoDTO.getWorkStation());
                scanHistoryTemp.setSmtScanId(java.util.UUID.randomUUID().toString());
                scanHistoryTemp.setSourceImu(flowControlInfoDTO.getaSourceImu());
                scanHistoryTemp.setSourceBimu(flowControlInfoDTO.getaSourceBimu());
                scanHistoryTemp.setColName(flowControlInfoDTO.getaColName());
                scanHistoryTemp.setSourceSysName(flowControlInfoDTO.getaSourceSysName());
                scanHistoryTemp.setCurrProcessCode(flowControlInfoDTO.getCurrProcessCode());
                scanHistoryTemp.setStationName(flowControlInfoDTO.getStationName());
                scanHistoryTemp.setLastProcess(flowControlInfoDTO.getIsLastProcess());
                scanHistoryTemp.setNextProcess(flowControlInfoDTO.getWorkStation());
                historiesList.add(scanHistoryTemp);
            }
        } else {
            historiesList.add(scanHistory);
        }
        this.setDefaultValue(historiesList);
        this.addList(historiesList,historiesExtraList);
        // isWriteBackScan = 'N'的数据写入扫描历史附加信息表
        if (historiesExtraList.size() > Constant.INT_0){
            wipScanHisExtraRepository.batchInsert(historiesExtraList);
        }
        psScanHistoryRepository.insertPsScanHistoryBatch(historiesList);
        return Constant.INT_1;
    }

    public PsScanHistory createScanHistoryEntity(FlowControlInfoDTO record) {
        PsScanHistory scanHistory = new PsScanHistory();
        // 设置主键
        scanHistory.setSmtScanId(UUID.randomUUID().toString());
        // sn号
        scanHistory.setSn(record.getSn());
        // 指令号
        scanHistory.setWorkOrderNo(record.getEntityPlanBasic().getWorkOrderNo());
        // 物料编码
        scanHistory.setItemNo(record.getEntityPlanBasic().getItemNo());
        // 物料名称
        scanHistory.setItemName(record.getEntityPlanBasic().getItemName());
        scanHistory.setCurrProcessCode(StringHelper.isNotEmpty(record.getaProcessCode()) ? record.getaProcessCode() : record.getCurrProcessCode());
        // 工艺路径
        scanHistory.setRouteId(record.getEntityPlanBasic().getRouteId());
        // 扫描人
        scanHistory.setWorker(record.getWorker());
        if (record.getErrorCode() == null) {
            scanHistory.setStatus(Constant.FLAG_Y);
        } else {
            scanHistory.setStatus(Constant.FLAG_N);
        }
        // scanHistory.setInTime(inTime); // 入站时间
        // scanHistory.setOutTime(outTime); // 出站时间
        // 父SN
        scanHistory.setParentSn(record.getParentSn() == null ? "" : record.getParentSn());
        // 故障代码
        scanHistory.setErrorCode(record.getErrorCode());
        // 线体代码
        scanHistory.setLineCode(
                StringUtils.isEmpty(record.getLineCode()) ? record.getEntityPlanBasic().getLineCode()
                        : record.getLineCode());
        // 工艺段
        scanHistory.setCraftSection(record.getEntityPlanBasic().getCraftSection());
        // 车间代码
        scanHistory.setWorkshopCode(record.getEntityPlanBasic().getWorkshopCode());
        scanHistory.setWorkStation(StringHelper.isNotEmpty(record.getaWorkStation()) ? record.getaWorkStation() : record.getWorkStation());
        scanHistory.setLastProcess(record.getIsLastProcess());
        scanHistory.setLastUpdatedBy(record.getLastUpdatedBy());
        scanHistory.setCreateBy(record.getLastUpdatedBy());
        // 工厂ID
        scanHistory.setFactoryId(record.getFactoryId());
        // 实体ID
        scanHistory.setEntityId(record.getEntityId());
        // 批次号
        scanHistory.setAttribute1(record.getEntityPlanBasic().getSourceTask());
        setOtherProperties(record, scanHistory);
        scanHistory.setAttribute2(record.getAttribute5());
        if (record.getWipInfo() != null) {
            scanHistory.setNextProcess(MpConstant.PROCESS_CODE_ZERO.equals(record.getWipInfo().getWorkStation()) ? record.getWipInfo().getNextProcess() : record.getWipInfo().getWorkStation());
        }
        scanHistory.setIsWriteBackScan(record.getIsWriteBackScan());
        scanHistory.setIsWriteBackUser(record.getIsWriteBackUser());
        scanHistory.setIsTraceCalculate(record.getIsTraceCalculate());
        scanHistory.setStationName(record.getStationName());
        if (record.isReScanFlag()){
            scanHistory.setSourceSys(Constant.RE_SCAN);
        }
        return scanHistory;
    }

    private void setDefaultValue(List<PsScanHistory> historiesList) {
        // 处理默认值
        historiesList.forEach(item->{
            if(StringUtils.isBlank(item.getIsTraceCalculate())){
                item.setIsTraceCalculate(Constant.FLAG_N);
            }
            if(StringUtils.isBlank(item.getIsWriteBackScan())){
                item.setIsWriteBackScan(Constant.FLAG_N);
            }
            if(StringUtils.isBlank(item.getIsWriteBackUser())){
                item.setIsWriteBackUser(Constant.FLAG_N);
            }
        });
    }

    public void addList(List<PsScanHistory> historiesList,List<WipScanHisExtraInfo> historiesExtraList) {
        // 新增数据
        historiesList.forEach(item->{
            // 工站为0不回写
            if(Constant.FLAG_N.equals(item.getIsWriteBackScan()) && !StringUtils.equals(Constant.STR_0,item.getWorkStation())){
                LOG.info(String.valueOf(item),"写入isWriteBackScan='N'");
                WipScanHisExtraInfo wipScanHisExtraInfo = new WipScanHisExtraInfo();
                // 批次、条码信息json格式写入
                Map<String, Object> objectMap = new HashMap<>();
                objectMap.put("prodplanId", item.getAttribute1());
                objectMap.put("sn", item.getSn());
                objectMap.put("imuId", item.getSourceImu());
                objectMap.put("workStation", item.getWorkStation());
                String params = JacksonJsonConverUtil.beanToJson(objectMap);
                wipScanHisExtraInfo.setExtraData(params);
                if (StringUtils.isBlank(item.getSmtScanId())){
                    wipScanHisExtraInfo.setId(java.util.UUID.randomUUID().toString());
                } else {
                    wipScanHisExtraInfo.setId(item.getSmtScanId());
                }
                wipScanHisExtraInfo.setRemark(item.getRemark());
                wipScanHisExtraInfo.setCreateBy(item.getCreateBy());
                wipScanHisExtraInfo.setLastUpdatedBy(item.getLastUpdatedBy());
                historiesExtraList.add(wipScanHisExtraInfo);
            }
        });
    }

    private void setOtherProperties(FlowControlInfoDTO record, PsScanHistory scanHistory) {
        if (StringHelper.isNotEmpty(record.getaSourceSysName())) {
            scanHistory.setColName(record.getaColName());
            scanHistory.setSourceImu(record.getaSourceImu());
            scanHistory.setSourceSysName(record.getaSourceSysName());
            scanHistory.setSourceBimu(record.getaSourceBimu());
        } else {
            scanHistory.setSourceBimu(record.getSourceBimu());
            scanHistory.setSourceSysName(record.getSourceSysName());
            scanHistory.setColName(record.getColName());
            scanHistory.setSourceImu(record.getSourceImu());
        }
    }

    /**
     * 批量增加实体数据
     *
     * @param listFlow
     * @return 插入数量
     * <AUTHOR>
     **/
    @Override
    public int insertPsScanHistoryByScanBatch(List<FlowControlInfoDTO> listFlow, int num) {

        int count = NumConstant.NUM_ZERO;

        List<List<FlowControlInfoDTO>> listOfList = CommonUtils.splitList(listFlow, num);
        if (CollectionUtils.isEmpty(listOfList)) {
            return count;
        }
        for (List<FlowControlInfoDTO> list : listOfList) {
            // 扫描历史附加信息表新增数据
            List<WipScanHisExtraInfo> historiesExtraList = new LinkedList<>();
            List<PsScanHistory> listHis = new ArrayList<PsScanHistory>();
            for (FlowControlInfoDTO record : list) {
                getPsScanHistoryInfo(listHis, record);
            }
            this.addList(listHis,historiesExtraList);
            if (historiesExtraList.size() > Constant.INT_0){
                wipScanHisExtraRepository.batchInsert(historiesExtraList);
            }
            count += psScanHistoryRepository.insertPsScanHistoryBatch(listHis);
        }
        return count;
    }

    /**
     * 获取线体名称
     *
     * @param record
     * @return
     */
    private String getLineCode(FlowControlInfoDTO record) {
        return StringUtils.isEmpty(record.getLineCode())
                ? record.getEntityPlanBasic()
                .getLineCode()
                : record.getLineCode();
    }

    /**
     * 获取子工序
     *
     * @param record
     * @return
     */
    private String getProcessCode(FlowControlInfoDTO record) {
        return StringHelper.isNotEmpty(record.getaProcessCode()) ?
                record.getaProcessCode() : record.getCurrProcessCode();
    }

    /**
     * @param listHis
     * @param record
     */
    public void getPsScanHistoryInfo(List<PsScanHistory> listHis, FlowControlInfoDTO record) {
        PsScanHistory scanHistory = new PsScanHistory();
        scanHistory.setSmtScanId(java.util.UUID.randomUUID().toString()); // 设置主键
        scanHistory.setSn(record.getSn()); // sn号
        scanHistory.setWorkOrderNo(record.getEntityPlanBasic().getWorkOrderNo()); // 指令号
        scanHistory.setItemNo(record.getEntityPlanBasic().getItemNo()); // 物料编码
        scanHistory.setItemName(record.getEntityPlanBasic().getItemName()); // 物料名称
        scanHistory.setRouteId(record.getEntityPlanBasic().getRouteId()); // 工艺路径
        // scanHistory.setCurrProcessCode(record.getCurrProcessCode()); // 当前工序代码
        scanHistory.setCurrProcessCode(getProcessCode(record));
        scanHistory.setStatus(Constant.FLAG_Y);
        if (StringUtils.isNotEmpty(record.getErrorCode())) {
            scanHistory.setStatus(Constant.FLAG_N);
        }
        scanHistory.setWorker(record.getWorker()); // 扫描人
        // scanHistory.setInTime(inTime); // 入站时间
        // scanHistory.setOutTime(outTime); // 出站时间
        scanHistory.setParentSn(StringUtils.defaultString(record.getParentSn(), "")); // 父SN
        scanHistory.setErrorCode(record.getErrorCode()); // 故障代码
        scanHistory.setLineCode(getLineCode(record)); // 线体代码
        scanHistory.setWorkshopCode(record.getEntityPlanBasic().getWorkshopCode()); // 车间代码
        // scanHistory.setOpeTimes(opeTimes); // 操作次数
        scanHistory.setCraftSection(record.getEntityPlanBasic().getCraftSection()); // 工艺段
        scanHistory.setWorkStation(StringHelper.isNotEmpty(record.getaWorkStation()) ? record.getaWorkStation() : record.getWorkStation());
        scanHistory.setLastProcess(StringUtils.isEmpty(record.getIsLastProcess()) ? Constant.FLAG_N : record.getIsLastProcess());
        scanHistory.setLastUpdatedBy(record.getLastUpdatedBy());
        scanHistory.setCreateBy(StringUtils.isEmpty(record.getCreateBy()) ? record.getLastUpdatedBy() : record.getCreateBy());
        scanHistory.setFactoryId(record.getFactoryId()); // 工厂ID
        scanHistory.setEntityId(record.getEntityId()); // 实体ID
        scanHistory.setAttribute1(record.getEntityPlanBasic().getSourceTask()); // 批次号
        scanHistory.setAttribute2(record.getLpn());
        setOtherProperties(record, scanHistory);
        if (record.getWipInfo() != null) {
            scanHistory.setNextProcess((MpConstant.PROCESS_CODE_ZERO).equals(record.getWipInfo().getWorkStation()) ? record.getWipInfo().getNextProcess() : record.getWipInfo().getWorkStation());
        }
        //isWriteBackScan默认值为N
        String isWriteBackScan = StringUtils.defaultIfBlank(record.getIsWriteBackScan(), Constant.FLAG_N);
        String isWriteBackUser = StringUtils.defaultIfBlank(record.getIsWriteBackUser(), Constant.FLAG_N);
        String isTraceCalculate = StringUtils.defaultIfBlank(record.getIsTraceCalculate(), Constant.FLAG_N);
        scanHistory.setIsWriteBackScan(isWriteBackScan);
        scanHistory.setSourceSys(record.getSourceSys());
        scanHistory.setIsWriteBackUser(isWriteBackUser);
        scanHistory.setIsTraceCalculate(isTraceCalculate);
        if (null != record.getPcbQty()) {
            scanHistory.setPcbQty(record.getPcbQty());
        }
        scanHistory.setStationName(record.getStationName());
        listHis.add(scanHistory);
    }

    /**
     * 设置来源系统
     *
     * @param record
     * @param scanHistory
     */
    public void setSourceSys(FlowControlInfoDTO record, PsScanHistory scanHistory) {
        if (record == null || scanHistory == null) {
            return;
        }
        scanHistory.setSourceSys(record.getScanSource());
    }

    /**
     * 批量更新扫描历史信息
     *
     * @param listFlow
     * @return 更新数量
     * <AUTHOR>
     **/
    @Override
    public int updatePsScanHistoryByScanBatch(List<FlowControlInfoDTO> listFlow, int num) {

        int count = NumConstant.NUM_ZERO;
        List<List<FlowControlInfoDTO>> listOfList = CommonUtils.splitList(listFlow, num);
        if (CollectionUtils.isEmpty(listOfList)) {
            return count;
        }
        for (List<FlowControlInfoDTO> list : listOfList) {
            List<PsScanHistory> listHis = new ArrayList<PsScanHistory>();
            for (FlowControlInfoDTO record : list) {
                addHistoryListInfo(listHis, record);
            }
            count += psScanHistoryRepository.updatePsScanHistoryByScanBatch(listHis);
        }
        return count;
    }

    /**
     * 增加历史信息
     *
     * @param listHis
     * @param record
     */
    private void addHistoryListInfo(List<PsScanHistory> listHis, FlowControlInfoDTO record) {
        PsScanHistory scanHistory = new PsScanHistory();
        scanHistory.setSn(record.getSn()); // sn号
        scanHistory.setItemNo(record.getEntityPlanBasic().getItemNo()); // 物料编码
        scanHistory.setItemName(record.getEntityPlanBasic().getItemName()); // 物料名称
        scanHistory.setWorkOrderNo(record.getEntityPlanBasic().getWorkOrderNo()); // 指令号
        scanHistory.setRouteId(record.getEntityPlanBasic().getRouteId()); // 工艺路径
        scanHistory.setCurrProcessCode(StringHelper.isNotEmpty(record.getaProcessCode()) ? record.getaProcessCode() : record.getCurrProcessCode());
        if (record.getErrorCode() == null) {
            scanHistory.setStatus(Constant.FLAG_Y);
        } else {
            scanHistory.setStatus(Constant.FLAG_N);
        }
        scanHistory.setWorker(record.getWorker()); // 扫描人
        scanHistory.setErrorCode(record.getErrorCode()); // 故障代码
        scanHistory.setParentSn(record.getParentSn() == null ? "" : record.getParentSn()); // 父SN
        scanHistory.setLineCode(StringUtils.isEmpty(record.getLineCode())
                ? record.getEntityPlanBasic()
                .getLineCode()
                : record.getLineCode()); // 线体代码
        scanHistory.setWorkshopCode(record.getEntityPlanBasic().getWorkshopCode()); // 车间代码
        // scanHistory.setOpeTimes(opeTimes); // 操作次数
        scanHistory.setCraftSection(record.getEntityPlanBasic().getCraftSection()); // 工艺段
        scanHistory.setWorkStation(StringHelper.isNotEmpty(record.getaWorkStation()) ? record.getaWorkStation() : record.getWorkStation());
        scanHistory.setCreateBy(record.getLastUpdatedBy());
        scanHistory.setLastProcess(record.getIsLastProcess());
        scanHistory.setLastUpdatedBy(record.getLastUpdatedBy());
        scanHistory.setEntityId(record.getEntityId()); // 实体ID
        scanHistory.setFactoryId(record.getFactoryId()); // 工厂ID
        scanHistory.setAttribute1(record.getEntityPlanBasic().getSourceTask()); // 批次号
        scanHistory.setAttribute2(record.getLpn());
        setOtherProperties(record, scanHistory);
        if (record.getWipInfo() != null) {
            scanHistory.setNextProcess((MpConstant.PROCESS_CODE_ZERO).equals(record.getWipInfo().getWorkStation()) ? record.getWipInfo().getNextProcess() : record.getWipInfo().getWorkStation());
        }
        scanHistory.setSourceSys(record.getSourceSys());
        scanHistory.setIsWriteBackUser(record.getIsWriteBackUser());
        scanHistory.setIsTraceCalculate(record.getIsTraceCalculate());
        scanHistory.setIsWriteBackScan(record.getIsWriteBackScan());
        scanHistory.setStationName(record.getStationName());
        listHis.add(scanHistory);
    }

    /**
     * 有选择性的增加实体数据
     *
     * @param record PsScanHistory实体
     * @return 插入数量
     * <AUTHOR>
     **/
    @Override
    public int insertPsScanHistorySelective(PsScanHistory record) {

        return psScanHistoryRepository.insertPsScanHistorySelective(record);
    }

    /**
     * 根据主键删除实体数据
     *
     * @param record PsScanHistory实体
     * @return 删除数量
     * <AUTHOR>
     **/
    @Override
    public int deletePsScanHistoryById(PsScanHistory record) {

        return psScanHistoryRepository.deletePsScanHistoryById(record);
    }

    /**
     * 有选择性的更新实体数据
     *
     * @param record PsScanHistory实体
     * @return 更新数量
     * <AUTHOR>
     **/
    @Override
    public int updatePsScanHistoryByIdSelective(PsScanHistory record) {

        return psScanHistoryRepository.updatePsScanHistoryByIdSelective(record);
    }

    /**
     * 更新扫描历史表实体数据
     *
     * @param record PsScanHistory实体
     * @return 更新数量
     **/
    @Override
    public int updatePsScanHistoryByScan(FlowControlInfoDTO record) {

        PsScanHistory scanHistory = new PsScanHistory();
        scanHistory.setSn(record.getSn()); // sn号
        scanHistory.setWorkOrderNo(record.getEntityPlanBasic().getWorkOrderNo()); // 指令号
        scanHistory.setItemNo(record.getEntityPlanBasic().getItemNo()); // 物料编码
        scanHistory.setItemName(record.getEntityPlanBasic().getItemName()); // 物料名称
        scanHistory.setRouteId(record.getEntityPlanBasic().getRouteId()); // 工艺路径
        scanHistory.setCurrProcessCode(StringHelper.isNotEmpty(record.getaProcessCode()) ? record.getaProcessCode() : record.getCurrProcessCode());
        if (MpConstant.RESULT_TYPE_OK.equals(record.getResultType())) {
            scanHistory.setStatus(Constant.FLAG_Y);
        } else {
            scanHistory.setStatus(Constant.FLAG_N);
        }
        scanHistory.setWorker(record.getWorker()); // 扫描人
        // scanHistory.setInTime(inTime); // 入站时间
        // scanHistory.setOutTime(outTime); // 出站时间
        scanHistory.setParentSn(record.getParentSn() == null ? "" : record.getParentSn()); // 父SN
        scanHistory.setErrorCode(record.getErrorCode() == null ? "" : record.getErrorCode()); // 故障代码
        // scanHistory.setLineCode(record.getEntityPlanBasic().getLineCode()); // 线体代码
        scanHistory.setLineCode(
                StringUtils.isEmpty(record.getLineCode()) ? record.getEntityPlanBasic().getLineCode()
                        : record.getLineCode()); // 线体代码
        scanHistory.setWorkshopCode(record.getEntityPlanBasic().getWorkshopCode()); // 车间代码
        // scanHistory.setOpeTimes(opeTimes); // 操作次数
        scanHistory.setCraftSection(record.getEntityPlanBasic().getCraftSection()); // 工艺段
        scanHistory.setWorkStation(StringHelper.isNotEmpty(record.getaWorkStation()) ? record.getaWorkStation() : record.getWorkStation());
        scanHistory.setLastUpdatedBy(record.getLastUpdatedBy());
        scanHistory.setFactoryId(record.getFactoryId()); // 工厂ID
        scanHistory.setEntityId(record.getEntityId()); // 实体ID
        scanHistory.setAttribute1(record.getEntityPlanBasic().getSourceTask()); // 批次号
        setOtherProperties(record, scanHistory);
        scanHistory.setAttribute2(record.getAttribute5());
        if (record.getWipInfo() != null) {
            scanHistory.setNextProcess(MpConstant.PROCESS_CODE_ZERO.equals(record.getWipInfo().getWorkStation()) ? record.getWipInfo().getNextProcess() : record.getWipInfo().getWorkStation());
        }
        scanHistory.setIsWriteBackScan(record.getIsWriteBackScan());
        scanHistory.setIsTraceCalculate(record.getIsTraceCalculate());
        scanHistory.setStationName(record.getStationName());
        scanHistory.setIsWriteBackUser(record.getIsWriteBackUser());
        return psScanHistoryRepository.updatePsScanHistoryByScan(scanHistory);
    }

    /**
     * 根据主键更新实体数据
     *
     * @param record PsScanHistory实体
     * @return 更新数量
     * <AUTHOR>
     **/
    @Override
    public int updatePsScanHistoryById(PsScanHistory record) {

        return psScanHistoryRepository.updatePsScanHistoryById(record);
    }

    /**
     * 根据主键查询实体信息
     *
     * @param record PsScanHistory实体
     * @return PsScanHistory
     * <AUTHOR>
     **/
    @Override
    public PsScanHistory selectPsScanHistoryById(PsScanHistory record) {

        return psScanHistoryRepository.selectPsScanHistoryById(record);
    }

    //查询条码拼版绑定数据
    @Override
    public List<PsScanHistory> selectPsScanHistoryBySn(PsScanHistory record) {

        return psScanHistoryRepository.selectPsScanHistoryBySn(record);
    }


    /**
     * getList
     *
     * @param record HashMap参数列表
     * @return PsScanHistory实体List
     * <AUTHOR>
     **/
    @Override
    public java.util.List<PsScanHistory> getList(Map<String, Object> record) {
        List<PsScanHistory> list = psScanHistoryRepository.getList(record);
        return bindProcess(list);
    }

    /**
     * 分页获取历史扫描条码
     *
     * @param dto
     * @return
     * @throws Exception
     * <AUTHOR>
     */
    @Override
    public List<PsScanHistory> getPageScanHistoryList(PsScanHistoryDTO dto) {
        this.setPartitionUpdateDate(dto);
        List<PsScanHistory> list = psScanHistoryRepository.getPageScanHistoryList(dto);
        return bindProcess(list);
    }

    /**
     * 获取历史扫描条码
     *
     * @param dto
     * @return
     * @throws Exception
     * <AUTHOR>
     */
    @Override
    public List<PsScanHistory> getScanHistoryList(PsScanHistoryDTO dto) throws Exception {
        this.setPartitionUpdateDate(dto);
        List<PsScanHistory> list = psScanHistoryRepository.getScanHistoryList(dto);
        return bindProcess(list);
    }

    /**
     * 长沙分区表，需要带分区字段查询否则查询极慢,获取ps-task 的创建时间往前推 1天
     * @param dto 查询参数
     */
    private void setPartitionUpdateDate(PsScanHistoryDTO dto) {
        if (StringUtils.isNotBlank(dto.getWorkOrderNo()) && Objects.isNull(dto.getLastUpdatedDate())) {
            String workOrderNo = dto.getWorkOrderNo();
            String[] split = workOrderNo.split(Constant.BAR);
            List<PsTask> taskList = PlanscheduleRemoteService.getPsTaskByProdplanIdList(Collections.singletonList(split[0]));
            if (!CollectionUtils.isEmpty(taskList)) {
                PsTask psTask = taskList.get(0);
                if (Objects.nonNull(psTask.getCreateDate())) {
                    Date createDate = psTask.getCreateDate();
                    dto.setLastUpdatedDate(new Date(createDate.getTime() - Constant.DAY_MILLISECOND));
                }
            }
        }
    }

    /**
     * 获取扫描历史记录根据主工序，子工序，工站
     *
     * @param dto
     * @return
     * <AUTHOR>
     */
    @Override
    public List<PsScanHistory> getScanHistoryListForZs(PsScanHistoryDTO dto) {
        return psScanHistoryRepository.getScanHistoryListForZs(dto);
    }


    @Override
    public Long getCountScanHistoryList(PsScanHistoryDTO record) {
        this.setPartitionUpdateDate(record);
        return psScanHistoryRepository.getCountScanHistoryList(record);
    }


    /**
     * 10241694
     * 把process由子工序编号新增子工序名称
     *
     * @param list
     * @return
     * @throws Exception
     */
    private List<PsScanHistory> bindProcess(List<PsScanHistory> list) {
        List<BSProcess> listWorkStation = CrafttechRemoteService.getProcessAll();
        if (Objects.isNull(listWorkStation)) {
            listWorkStation = new LinkedList<>();
        }
        Map<String, String> processMap = listWorkStation.stream()
                .filter(item->StringUtils.isNotBlank(item.getProcessCode())&& StringUtils.isNotBlank(item.getProcessName()))
                .collect(Collectors.toMap(BSProcess::getProcessCode, BSProcess::getProcessName, (k1, k2) -> k1));
        for (PsScanHistory item : list) {
            item.setProcessName(processMap.get(item.getCurrProcessCode()));
        }
        return list;
    }


    /**
     * 10241694
     * 点对对获取工站信息
     *
     * @param workStation
     * @param factoryId
     * @return
     * @throws Exception
     */
    public List<CtWorkStationsDTO> getWorkStationInfo(String workStation, String factoryId)
            throws Exception {

        Map<String, String> map = new HashMap<String, String>();
        if (StringHelper.isNotEmpty(factoryId)) {
            map.put("factoryId", factoryId);
        }
        if (StringHelper.isNotEmpty(workStation)) {
            map.put("stationId", workStation);
        }
        String params = JacksonJsonConverUtil.beanToJson(map);


        // 点对点调用服务
        String version = MicroServiceNameEum.VERSION;
        String serviceName = MicroServiceNameEum.CRAFTTECH;
        String getUrl = "/CT/ctWorkStations";
        String sendType = MicroServiceNameEum.SENDTYPEGET;

        String getresult = MicroServiceRestUtil.invokeService(serviceName, version, sendType, getUrl, params,
                MESHttpHelper.getHttpRequestHeader());
        JsonNode json = JacksonJsonConverUtil.getMapperInstance().readTree(getresult);
        String bo = json.get(MpConstant.JSON_BO).toString();
        List<CtWorkStationsDTO> listWorkStation = JacksonJsonConverUtil.jsonToListBean(bo,
                new TypeReference<ArrayList<CtWorkStationsDTO>>() {
                });

        return listWorkStation;
    }

    /**
     * getCount
     *
     * @param record HashMap参数列表
     * @return 数量
     * <AUTHOR>
     */
    @Override
    public Long getCount(Map<String, Object> record) {

        return psScanHistoryRepository.getCount(record);
    }

    /**
     * getPage分页查询服务
     *
     * @param record HashMap参数列表
     * @return PsScanHistory实体list
     * <AUTHOR>
     */
    @Override
    public java.util.List<PsScanHistory> getPage(Map<String, Object> record) throws Exception {

        List<PsScanHistory> list = psScanHistoryRepository.getPage(record);
        List<PsScanHistory> listAddProcess = bindProcess(list);
        return listAddProcess;
    }

    /**
     * snScanHistoryList
     *
     * @param record HashMap参数列表
     * @return PsScanHistoryDTO实体List
     * @throws IOException
     * @throws RouteException
     * @throws JsonProcessingException
     * <AUTHOR>
     **/
    @Override
    public List<PsScanHistoryDTO> snScanHistoryList(Map<String, Object> record)
            throws JsonProcessingException, RouteException, IOException {

        List<PsScanHistory> list = psScanHistoryRepository.getList(record);
        List<PsScanHistoryDTO> listDTO = new ArrayList<PsScanHistoryDTO>();
        if (!CollectionUtils.isEmpty(list)) {
            listDTO = setCurrProcessName(list);
        }
        setLineName(listDTO);
        setMBom(listDTO);
        return listDTO;
    }

    /**
     * snScanHistoryPage分页
     *
     * @param record HashMap参数列表
     * @return PsScanHistoryDTO实体list
     * @throws IOException
     * @throws RouteException
     * @throws JsonProcessingException
     * <AUTHOR>
     */
    @Override
    public List<PsScanHistoryDTO> snScanHistoryPage(Map<String, Object> record)
            throws JsonProcessingException, RouteException, IOException {

        List<PsScanHistory> list = psScanHistoryRepository.getPage(record);
        List<PsScanHistoryDTO> listDTO = new ArrayList<PsScanHistoryDTO>();
        if (!CollectionUtils.isEmpty(list)) {
            listDTO = setCurrProcessName(list);
        }
        setLineName(listDTO);
        setCreateByName(listDTO);
        setMBom(listDTO);
        return listDTO;
    }

    private void setMBom(List<PsScanHistoryDTO> list) {
        if(CollectionUtils.isEmpty(list)){
            return;
        }
        List<String> prodPlanIdList = list.stream().map(PsScanHistoryDTO::getAttribute1).distinct().collect(Collectors.toList());
        List<BProdBomHeaderDTO> bProdBomHeaderDTOList = centerfactoryRemoteService.queryProductCodeByProdPlanIdList(prodPlanIdList);
        Map<String,String> prodToMBomMap = bProdBomHeaderDTOList.stream().collect(Collectors.toMap(k -> k.getProdplanId(), v -> v.getProductCode(), (k1, k2) -> k1));
        for (PsScanHistoryDTO entity : list) {
            entity.setMbom(entity.getItemNo());
            String mBom = prodToMBomMap.get(entity.getAttribute1());
            if (StringUtils.isNotBlank(mBom)) {
                entity.setMbom(mBom);
            }
        }
    }

    /**
     * 将扫描历史信息中的当前工序代码转为工序名称 将扫描历史信息中的当前工站代码转为工站名称
     *
     * @return PsBarcodeControlInfo实体list
     * @throws IOException
     * @throws RouteException
     * @throws JsonProcessingException
     * <AUTHOR>
     */
    public java.util.List<PsScanHistoryDTO> setCurrProcessName(List<PsScanHistory> list)
            throws JsonProcessingException, RouteException, IOException {

        List<PsScanHistoryDTO> listDTO = PsScanHistoryAssembler.toPsScanHistoryDTOList(list);
        List<BSProcessDTO> listProcess = psWipInfoService.getProcessInfo("", null, null);
        if (listProcess == null || listProcess.size() == 0) {
            return listDTO;
        }

        List<BSProcessDTO> currProcess = null;
        for (PsScanHistoryDTO his : listDTO) {
            // 找到工序名称
            if (StringUtils.isNotBlank(his.getCurrProcessCode())) {
                currProcess = listProcess.stream()
                        .filter(pro -> his.getCurrProcessCode().equals(pro.getProcessCode()))
                        .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(currProcess)) {
                    his.setCurrProcessName(currProcess.get(NumConstant.NUM_ZERO).getProcessName());
                }
            }

            // 找到工站名称
            if (StringUtils.isNotBlank(his.getWorkStation())) {
                currProcess =
                        listProcess.stream().filter(pro -> his.getWorkStation().equals(pro.getProcessCode()))
                                .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(currProcess)) {
                    his.setWorkStationName(currProcess.get(NumConstant.NUM_ZERO).getProcessName());
                }
            }

        }
        setMBom(listDTO);
        return listDTO;
    }

    /**
     * 将在制表信息中的当前线体代码转为线体名称
     *
     * @param listDTO
     * @throws IOException
     * @throws RouteException
     */
    private void setLineName(List<PsScanHistoryDTO> listDTO) throws IOException, RouteException {

        if (!CollectionUtils.isEmpty(listDTO)) {

            List<CFLine> cfLineListAll = psWipInfoService.getLineInfo("");
            List<CFLine> searchCFLineList = null;
            for (PsScanHistoryDTO psScanHistoryDTO : listDTO) {
                if (StringUtils.isBlank(psScanHistoryDTO.getLineCode())) {
                    continue;
                }
                searchCFLineList = cfLineListAll.stream()
                        .filter(line -> psScanHistoryDTO.getLineCode().equals(line.getLineCode()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(searchCFLineList)) {
                    continue;
                }
                psScanHistoryDTO.setLineName(searchCFLineList.get(NumConstant.NUM_ZERO).getLineName());
            }

        }

    }

    /**
     * 根据指令编号物理删除扫描历史表数据
     *
     * @param workOrderNo 指令编号
     * @return 删除数量
     * <AUTHOR>
     */
    @Override
    public int deleteHistoryInfoByWorkOrderNo(String workOrderNo) {

        PsScanHistory scanHistory = new PsScanHistory();
        scanHistory.setWorkOrderNo(workOrderNo);
        return psScanHistoryRepository.deleteHistoryInfoByWorkOrderNo(scanHistory);
    }

    /**
     * 根据在制表信息查询扫描历史信息
     *
     * @param record 指令编号
     * @return 实体集合
     * <AUTHOR>
     */
    @Override
    public java.util.List<PsScanHistory> selectHistoryInfoByWipSn(PsScanHistory record) {

        return psScanHistoryRepository.selectHistoryInfoByWipSn(record);
    }

    /**
     * 从WIPINFO中批量新增扫描历史数据
     *
     * @param record HashMap参数列表
     * @return PsScanHistory实体list
     */
    @Override
    public int insertPsScanHistoryByWipInfo(Map record) {

        return psScanHistoryRepository.insertPsScanHistoryByWipInfo(record);
    }

    private boolean isEmptyString(Object str) {

        if (null == str) {
            return true;
        }
        if (String.class == str.getClass()) {
            if (null == str.toString().trim()) {
                return true;
            }
            if ("".equals(str.toString().trim())) {
                return true;
            }
        }

        return false;
    }

    /**
     * @return
     * @throws IOException
     * @throws RouteException
     * @throws JsonProcessingException
     * @throws ParseException
     */
    @Override
    public PageRows<ProductInfoQueryIntegrateResponse> getListRangelastUpDate(ProductInfoQueryIntegrateRequest busiData) throws Exception {
        init(busiData);
        PageRows<ProductInfoQueryIntegrateResponse> inteRsp =
                new PageRows<ProductInfoQueryIntegrateResponse>();
        String taskNo = null;
        taskNo = getTaskNo(busiData, taskNo);
        List<PsScanHistory> outQryList = this.getPsScanHistories(busiData, taskNo);
        if (CollectionUtils.isEmpty(outQryList)) {
            return null;
        }
        Set<String> itemNoSet = new HashSet<String>();
        outQryList.forEach(p -> itemNoSet.add(p.getItemNo()));
        List<String> itemNoList = new ArrayList<String>(itemNoSet);
        Map<String, PsTask> psTaskMap = batchQryPsTask(itemNoList);
        Map<String, String> craftMap = queryCraftType(itemNoList);
        List<ProductInfoQueryIntegrateResponse> proInfoList =
                new ArrayList<ProductInfoQueryIntegrateResponse>();
        List<String> bomList = new ArrayList<>();
        for (PsScanHistory psScanHistory : outQryList) {
            String tempTaskNo = psScanHistory.getAttribute1();
            ProductInfoQueryIntegrateResponse temp = new ProductInfoQueryIntegrateResponse();
            temp.setAircraftType(craftMap.get(psScanHistory.getItemNo()));// 机型
            temp.setOutQty(psScanHistory.getCnt());
            // 查询ps_work_order_basic
            temp.setEndTime(busiData.getEndTime());
            temp.setStartTime(busiData.getStartTime());
            temp.setItemName(psScanHistory.getItemName());
            temp.setItemNo(psScanHistory.getItemNo());
            temp.setLineCode(
                    null == lineCodeMap.get(psScanHistory.getLineCode()) ? null : (String) lineCodeMap
                            .get(psScanHistory.getLineCode()));
            PsTask psTask = psTaskMap.get(psScanHistory.getAttribute1());
            temp.setProdType(null == psTask ? null : psTask.getExternalType());// 产品大类
            temp.setTaskNo(tempTaskNo);
            temp.setWorkStation(
                    null == workStationMap.get(busiData.getWorkStation()) ? null : (String) workStationMap
                            .get(busiData.getWorkStation()));
            temp.setProdProductCode(psScanHistory.getItemNo());
            bomList.add(tempTaskNo);
            proInfoList.add(temp);
        }
        if (proInfoList.isEmpty()) {
            return null;
        }
        setProdProductCode(bomList, proInfoList);
        int page = Math.max(busiData.getPage(), 1);
        int rows = Math.max(busiData.getCurrentSize(), 1);
        int startRow = (page - 1) * rows;
        int endRow = page * rows > proInfoList.size() ? proInfoList.size() : page * rows - 1;
        List<ProductInfoQueryIntegrateResponse> resultList = proInfoList.subList(startRow, endRow);

        // inteRsp.setCurrent(page);
        inteRsp.setTotal(proInfoList.size());
        inteRsp.setRows(resultList);
        return inteRsp;

    }

    /**
     *<AUTHOR>
     * 赋值制造MBOM
     *@Date 2025/1/22 15:53
     *@Param [java.util.List<java.lang.String>, java.util.List<com.zte.interfaces.dto.ProductInfoQueryIntegrateResponse>]
     *@return
     **/
    private void setProdProductCode(List<String> bomList, List<ProductInfoQueryIntegrateResponse> proInfoList) throws Exception{
        if (CollectionUtils.isEmpty(bomList)) {
            return;
        }
        // 获取制造MBOM信息
        Map<String, String> bomMap = bProdBomService.getBProdBomListBatch(bomList);
        if (MapUtils.isEmpty(bomMap)) {
            return;
        }
        for (ProductInfoQueryIntegrateResponse info : proInfoList) {
            String prodProductCode = bomMap.get(info.getTaskNo());
            if (StringUtils.isNotEmpty(prodProductCode)) {
                info.setProdProductCode(prodProductCode);
            }
        }
    }

    private List<PsScanHistory> getPsScanHistories(ProductInfoQueryIntegrateRequest busiData, String taskNo) throws ParseException {
        Map<String, Object> record = new HashMap<String, Object>();
        record.put("taskNo", taskNo);
        record.put("workStation", busiData.getWorkStation());
        record.put("itemNoLike", busiData.getItemNoLike());
        record.put("lineCodes",
                CollectionUtils.isEmpty(busiData.getLineCodes()) ? null : busiData.getLineCodes());
        DateFormat df = new SimpleDateFormat(MpConstant.DATE_FORMAT_YYYYMMDDHHMMSS);
        Date start = df.parse(busiData.getStartTime());
        Date end = df.parse(busiData.getEndTime());
        record.put("startTime", start);
        record.put("endTime", end);
        List<PsScanHistory> outQryList = psScanHistoryRepository.getTaskNoOut(record);
        return outQryList;
    }

    /**
     * 获取taskNo
     *
     * @param busiData
     * @param taskNo
     * @return
     */
    private String getTaskNo(ProductInfoQueryIntegrateRequest busiData, String taskNo) {
        if (!StringHelper.isEmpty(busiData.getTaskNo())) {
            taskNo = busiData.getTaskNo();
        } else if (!StringHelper.isEmpty(busiData.getPlanNo()))// 计划跟踪单
        {
            // 转化为批次,查pstask
            PsTask psTask = queryPstask4taskNo(busiData.getPlanNo(), null);
            taskNo = (null == psTask ? null : psTask.getProdplanId());
        }
        return taskNo;
    }

    private Map<String, PsTask> batchQryPsTask(List<String> itemNoList)
            throws RouteException, JsonProcessingException, IOException {
        // TODO Auto-generated method stub

        String params = JacksonJsonConverUtil.beanToJson(itemNoList);
        Map<String, String> headerParamsMap = MESHttpHelper.getHttpRequestHeader();
        // 点对点调用服务
        String serviceName = MicroServiceNameEum.PLANSCHEDULE;
        String version = MicroServiceNameEum.VERSION;
        String sendType = MicroServiceNameEum.SENDTYPEPOST;
        String getUrl = "/PS/bstaskByItemNo";

        String getresult = MicroServiceRestUtil.invokeService(serviceName, version, sendType, getUrl, params,
                headerParamsMap);
        JsonNode json = JacksonJsonConverUtil.getMapperInstance().readTree(getresult);
        String bo = json.get(MpConstant.JSON_BO).toString();
        List<PsTask> itemInfoList = (List<PsTask>) JacksonJsonConverUtil.jsonToListBean(bo,
                new TypeReference<ArrayList<PsTask>>() {
                });
        Map<String, PsTask> pstaskMap = new HashMap<String, PsTask>();
        if (CollectionUtils.isEmpty(itemInfoList)) {
            return pstaskMap;
        }
        for (PsTask psTask : itemInfoList) {
            pstaskMap.put(psTask.getProdplanId(), psTask);
        }
        return pstaskMap;
    }

    private PsTask queryPstask4taskNo(String planNo, String planId) {
        Map<String, String> paramsMap = new HashMap<String, String>();
        paramsMap.put("prodplanId", planId);// 批次
        paramsMap.put("prodplanNo", planNo);// 计划跟踪单
        try {
            String params = JacksonJsonConverUtil.beanToJson(paramsMap);
            Map<String, String> headerParamsMap = MESHttpHelper.getHttpRequestHeader();
            // 点对点调用服务
            String getResult = MicroServiceRestUtil.invokeService(MicroServiceNameEum.PLANSCHEDULE, MicroServiceNameEum.VERSION,
                    MicroServiceNameEum.SENDTYPEGET, "/PS/psTask",
                    params, headerParamsMap);
            JsonNode json = JacksonJsonConverUtil.getMapperInstance().readTree(getResult);
            String bo = json.get(MpConstant.JSON_BO).toString();
            List<PsTask> psTaskList = (List<PsTask>) JacksonJsonConverUtil.jsonToListBean(bo,
                    new TypeReference<ArrayList<PsTask>>() {
                    });
            if (!CollectionUtils.isEmpty(psTaskList)) {
                // 将结果缓存起来，避免下次重复查询
                return psTaskList.get(NumConstant.NUM_ZERO);
            }
        } catch (Exception e) {
            LOG.error("context", e);
            return null;
        }
        return null;
    }

    private Map<String, String> queryCraftType(List<String> itemNoList)
            throws JsonProcessingException, IOException, RouteException {

        // 查询机型
        Map<String, String> result = new HashMap<String, String>();
        if (null == itemNoList || CollectionUtils.isEmpty(itemNoList)) {
            return result;
        }
        List<BsItemInfo> itemList = new ArrayList<>();
        for (String itemNo : itemNoList) {
            BsItemInfo temp = new BsItemInfo();
            temp.setItemNo(itemNo);
            itemList.add(temp);
        }
        Map<String, String> headerParamsMap = MESHttpHelper.getHttpRequestHeader();
        String params = JacksonJsonConverUtil.beanToJson(itemList);

        // 点对点调用服务
        String serviceName = MicroServiceNameEum.BASICSETTING;
        String version = MicroServiceNameEum.VERSION;
        String sendType = MicroServiceNameEum.SENDTYPEPOST;
        String getUrl = "/bsItemInfoCtrl/bsItemInfoBatch";

        String getresult = MicroServiceRestUtil.invokeService(serviceName, version, sendType, getUrl, params,
                headerParamsMap);
        JsonNode json = JacksonJsonConverUtil.getMapperInstance().readTree(getresult);
        String bo = json.get(MpConstant.JSON_BO).toString();
        List<BsItemInfo> itemInfoList = (List<BsItemInfo>) JacksonJsonConverUtil.jsonToListBean(bo,
                new TypeReference<ArrayList<BsItemInfo>>() {
                });
        if (CollectionUtils.isEmpty(itemInfoList)) {
            return result;
        }
        for (BsItemInfo bsItemInfo : itemInfoList) {
            result.put(bsItemInfo.getItemNo(), bsItemInfo.getStyle());
        }
        return result;
    }

    private void init(ProductInfoQueryIntegrateRequest busiData) {
        queryLineCode(busiData);
        queryWorkStation();
    }

    private void queryWorkStation() {

        Map<String, String> paramsMap = new HashMap<String, String>();
        paramsMap.put("xType", MpConstant.PROCESS_X_TYPE_S);
        try {
            String params = JacksonJsonConverUtil.beanToJson(paramsMap);
            Map<String, String> headerParamsMap = MESHttpHelper.getHttpRequestHeader();
            // 点对点调用服务
            String serviceName = MicroServiceNameEum.CRAFTTECH;
            String version = MicroServiceNameEum.VERSION;
            String sendType = MicroServiceNameEum.SENDTYPEGET;
            String getUrl = "/BS/BSProcessDTO";
            String getResult = MicroServiceRestUtil.invokeService(serviceName, version, sendType, getUrl,
                    params, headerParamsMap);
            JsonNode json = JacksonJsonConverUtil.getMapperInstance().readTree(getResult);
            String bo = json.get(MpConstant.JSON_BO).toString();
            List<BSProcessDTO> listLine = (List<BSProcessDTO>) JacksonJsonConverUtil.jsonToListBean(bo,
                    new TypeReference<ArrayList<BSProcessDTO>>() {
                    });
            if(CollectionUtils.isEmpty(listLine)){
                return;
            }
            for (BSProcessDTO pro : listLine) {
                workStationMap.put(pro.getProcessCode(), pro.getProcessName());
            }
        } catch (Exception e) {
            LOG.error("queryWorkStation", e);
        }
    }

    private void queryLineCode(ProductInfoQueryIntegrateRequest busiData) {

        Map<String, String> paramsMap = new HashMap<String, String>();
        paramsMap.put("factoryId", busiData.getFactoryId());
        try {
            String params = JacksonJsonConverUtil.beanToJson(paramsMap);
            Map<String, String> headerParamsMap = MESHttpHelper.getHttpRequestHeader();
            // 点对点调用服务
            String getResult = MicroServiceRestUtil.invokeService(MicroServiceNameEum.BASICSETTING,
                    MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEGET, "/CF/CFLine",
                    params, headerParamsMap);
            JsonNode json = JacksonJsonConverUtil.getMapperInstance().readTree(getResult);
            String bo = json.get(MpConstant.JSON_BO).get(MpConstant.JSON_ROWS).toString();
            List<CFLine> listLine = (List<CFLine>) JacksonJsonConverUtil.jsonToListBean(bo,
                    new TypeReference<ArrayList<CFLine>>() {
                    });
            if (null == listLine || listLine.isEmpty()) {
                return;
            }
            for (CFLine cfLine : listLine) {
                lineCodeMap.put(cfLine.getLineCode(), cfLine.getLineName());
            }
        } catch (Exception e) {
            LOG.error("queryLineCode", e);
            return;
        }
    }

    @Override
    public ServiceData<Map<String, Object>>
        queryProdDetail(ProductInfoQueryIntegrateRequest busiData) throws Exception {
        ServiceData<Map<String, Object>> serviceData = new ServiceData<>();
        // TODO Auto-generated method stub
        this.checkDetailParams(busiData);
        DateFormat df = new SimpleDateFormat(MpConstant.DATE_FORMAT_YYYYMMDDHHMMSS);
        Date start = df.parse(busiData.getStartTime());
        Date end = df.parse(busiData.getEndTime());
        if (DateUtil.caldaysByDate(end,start,Constant.INT_0).compareTo(Constant.TIME_INTERVAL_FOR_EXPORT) == Constant.INT_1) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SPAN_WHITIN_7DAYS);
        }
        init(busiData);
        String lineCode = transToCode(busiData.getLineCode());
        busiData.setLineCode(lineCode);
        busiData.setStartDate(start);
        busiData.setEndDate(end);
        busiData.setOrder(Constant.DESC);
        busiData.setOrderField(BusinessConstant.SN);
        int currentSize = busiData.getCurrentSize() < Constant.INT_1 ? Constant.INT_5000 : busiData.getCurrentSize();
        int currentPage = Math.max(busiData.getPage(), Constant.INT_1);
        Page<ProductInfoQueryIntegrateRequest> page = new Page<>(currentPage, currentSize);
        page.setParams(busiData);
        List<PsScanHistory> hisList = psScanHistoryRepository.getListRangelastUpDate(page);
        if (CollectionUtils.isEmpty(hisList)) {
            serviceData.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
            serviceData.getCode().setMsg(CommonUtils.getLmbMessage(MessageId.DATA_NOT_FOUND));
            serviceData.setBo(null);
            return serviceData;
        }
        String attribute1 = hisList.get(0).getAttribute1();
        String prodProductCode = hisList.get(0).getItemNo();
        if (StringUtils.isNotEmpty(attribute1)) {
            List<String> bomList = new ArrayList(){{add(attribute1);}};
            Map<String, String> bomMap = bProdBomService.getBProdBomListBatch(bomList);
            if (!MapUtils.isEmpty(bomMap)) {
                String tempItem = bomMap.get(attribute1);
                if (StringUtils.isNotEmpty(tempItem)) {
                    prodProductCode = tempItem;
                }
            }
        }
        List<ProductInfoQueryIntegrateResponse> resList = new ArrayList<>();
        DateFormat format = new SimpleDateFormat(MpConstant.DATE_FORMAT_YYYYMMDDHHMMSS);
        String taskNo = busiData.getTaskNo();
        for (PsScanHistory psScanHistory : hisList) {
            resList.add(this.getDetailRows(taskNo, format, psScanHistory, prodProductCode));
        }
        Map<String, Object> result = new HashMap<>();
        result.put("current", page.getCurrent());
        result.put("rows", resList);
        result.put("total", page.getTotal());
        Map<String, Object> otherMap = new HashMap<>();
        busiData.setPageSize(1);
        PsScanHistory lastInfo = psScanHistoryRepository.getFirstOrLastProdInfo(busiData);
        busiData.setOrder(Constant.ASC);
        PsScanHistory firstInfo = psScanHistoryRepository.getFirstOrLastProdInfo(busiData);
        otherMap.put("firstInfo", this.getDetailRows(taskNo, format, firstInfo, prodProductCode));
        otherMap.put("lastInfo", this.getDetailRows(taskNo, format, lastInfo, prodProductCode));
        result.put("other", otherMap);
        serviceData.setBo(result);
        return serviceData;
    }

    /**
     *<AUTHOR>
     * 校验入参
     *@Date 2023/7/20 9:42
     *@Param [com.zte.interfaces.dto.ProductInfoQueryIntegrateRequest]
     *@return
     **/
    private void checkDetailParams (ProductInfoQueryIntegrateRequest busiData) throws Exception {
        if (StringUtils.isEmpty(busiData.getLineCode()) || StringUtils.isEmpty(busiData.getWorkStation()) || StringUtils.isEmpty(busiData.getTaskNo())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PRODUCT_QUERY_PARAMS_CAN_NOT_BE_NULL);
        }
        if (StringUtils.isEmpty(busiData.getStartTime()) ||StringUtils.isEmpty(busiData.getStartTime())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.TIME_CAN_NOT_BE_NULL);
        }
    }

    /**
     *<AUTHOR>
     * 组合返回数据
     *@Date 2023/7/20 9:42
     *@Param [java.lang.String, java.text.DateFormat, com.zte.domain.model.PsScanHistory]
     *@return
     **/
    private ProductInfoQueryIntegrateResponse getDetailRows(String taskNo, DateFormat format, PsScanHistory psScanHistory, String prodProductCode) {
        ProductInfoQueryIntegrateResponse temp = new ProductInfoQueryIntegrateResponse();
        temp.setSn(psScanHistory.getSn());
        temp.setTaskNo(taskNo);
        temp.setLineCode(
                null == lineCodeMap.get(psScanHistory.getLineCode()) ? null : (String) lineCodeMap
                        .get(psScanHistory.getLineCode()));
        temp.setItemNo(psScanHistory.getItemNo());
        temp.setItemName(psScanHistory.getItemName());
        temp.setPriCraft(psScanHistory.getCraftSection());
        temp.setWorkStation(null == workStationMap
                .get(psScanHistory.getWorkStation()) ? null
                : (String) workStationMap
                .get(psScanHistory.getWorkStation()));
        temp.setScanTime(format.format(psScanHistory.getLastUpdatedDate()));
        temp.setProdProductCode(prodProductCode);
        return temp;
    }

    private String transToCode(String lineCode) {

        if (StringHelper.isEmpty(lineCode)) {
            return null;
        }
        Iterator<Map.Entry<String, String>> it = lineCodeMap.entrySet().iterator();
        while (it.hasNext()) {
            Map.Entry<String, String> entry = it.next();
            if (lineCode.equals(entry.getValue())) {
                return entry.getKey();
            }
        }
        return null;
    }

    @Override
    public RetCode exportDetail(ProductInfoQueryIntegrateRequest busiData, HttpServletResponse response)
            throws ParseException, JsonProcessingException, IOException, RouteException {

        RetCode retCode = new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID);
        init(busiData);
        String lineCode = busiData.getLineCode();
        String[] lineCodes = StringHelper.isEmpty(lineCode) ? null : lineCode.split("\\.");
        String workStation = busiData.getWorkStation();
        String taskNo = busiData.getTaskNo();
        if (!isEmptyString(busiData.getPlanNo()) && isEmptyString(taskNo)) {
            // 转化为批次,查pstask
            PsTask psTask = queryPstask4taskNo(busiData.getPlanNo(), null);
            taskNo = (null == psTask ? null : psTask.getProdplanId());
        }
        Map<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("lineCodes", lineCodes);
        paramMap.put("taskNo", taskNo);
        paramMap.put("itemNoLike", busiData.getItemNoLike());
        paramMap.put("workStation", workStation);
        DateFormat df = new SimpleDateFormat(MpConstant.DATE_FORMAT_YYYYMMDDHHMMSS);
        Date start = df.parse(busiData.getStartTime());
        Date end = df.parse(busiData.getEndTime());
        paramMap.put("startTime", start);
        paramMap.put("endTime", end);

        paramMap.put("orderField", BusinessConstant.SN);
        paramMap.put("order", Constant.DESC);
        List<PsScanHistory> hisList = psScanHistoryRepository.getList(paramMap);
        if (null == hisList || hisList.isEmpty()) {
            retCode.setMsg(CommonUtils.getLmbMessage(MessageId.SELECT_ERROR));
            return retCode;
        }
        List<String> itemNoList = psScanHistoryRepository.getItemNoList(paramMap);

        if (null == itemNoList || itemNoList.isEmpty()) {
            itemNoList = new LinkedList<>();
        }

        Map<String, PsTask> psTaskMap = batchQryPsTask(itemNoList);
        Map<String, String> craftMap = queryCraftType(itemNoList);
        return getRetCode(response, hisList, psTaskMap, craftMap);
    }

    private RetCode getRetCode(HttpServletResponse response, List<PsScanHistory> hisList, Map<String, PsTask> psTaskMap, Map<String, String> craftMap) {
        // excel标题
        String[] title = ExcelName.PSSCANHISTORY_EXPORT_TITLE;
        // sheet名
        String sheetName = ExcelName.PSSCANHISTORY_EXPORT_SHEETNAME;
        // 第一步，创建一个HSSFWorkbook，对应一个Excel文件
        HSSFWorkbook wb = new HSSFWorkbook();
        OutputStream os = null;
        try {
            DateFormat dateFormat = new SimpleDateFormat(MpConstant.DATE_FORMAT_YYYYMMDDHHMMSS);
            // 第四步，创建单元格，并设置值表头 设置表头居中
            HSSFCellStyle hssfCellStyle = wb.createCellStyle();
            hssfCellStyle.setAlignment(HorizontalAlignment.CENTER); // 创建一个居中格式
            BigDecimal hisListSize = new BigDecimal(hisList.size());
            int distance = NumConstant.NUM_60000;
            BigDecimal sheetNum = hisListSize.divide(new BigDecimal(distance), BigDecimal.ROUND_UP);
            for (int j = 0; j < sheetNum.intValue(); j++) {
                // 第二步，在workbook中添加一个sheet,对应Excel文件中的sheet
                HSSFSheet hssfSheet = wb.createSheet(sheetName + j);
                // 第三步，在sheet中添加表头第0行,注意老版本poi对Excel的行数列数有限制
                HSSFRow hssfRow = hssfSheet.createRow(NumConstant.NUM_ZERO);
                // 创建标题
                createTitleForSheet(title, hssfCellStyle, hssfRow);
                // { "单板条码", "批次", "线体", "料单代码",
                // "料单名称","产品大类","机型","主工序","工站","扫描时间"};
                // 创建内容
                int entRow = (j + 1) * distance > hisList.size() ? hisList.size() : (j + 1) * distance;
                int rowNum = 1;
                for (int i = j * distance; i < entRow; i++) {
                    PsScanHistory psScanHistoryTemp = hisList.get(i);
                    hssfRow = hssfSheet.createRow(rowNum++);
                    hssfRow.createCell(NumConstant.NUM_ZERO).setCellValue(psScanHistoryTemp.getSn());
                    hssfRow.createCell(NumConstant.NUM_ONE).setCellValue(psScanHistoryTemp.getAttribute1());
                    hssfRow.createCell(NumConstant.NUM_TWO).setCellValue(null == lineCodeMap
                            .get(psScanHistoryTemp.getLineCode()) ? null : (String) lineCodeMap.get(psScanHistoryTemp.getLineCode()));
                    hssfRow.createCell(NumConstant.NUM_THREE).setCellValue(psScanHistoryTemp.getItemNo());
                    hssfRow.createCell(NumConstant.NUM_FOUR).setCellValue(psScanHistoryTemp.getItemName());
                    /*
                     * PsTask pstask = queryPstask4taskNo(null,
                     * temp.getSn().substring(0, 7));
                     */
                    PsTask pstask = psTaskMap.get(psScanHistoryTemp.getAttribute1());
                    hssfRow.createCell(NumConstant.NUM_FIVE).setCellValue(null == pstask ? null : pstask.getExternalType());
                    hssfRow.createCell(NumConstant.NUM_SIX).setCellValue(craftMap.get(psScanHistoryTemp.getItemNo()));//
                    // 机型
                    hssfRow.createCell(NumConstant.NUM_SEVEN).setCellValue(psScanHistoryTemp.getCraftSection());
                    hssfRow.createCell(NumConstant.NUM_EIGHT)
                            .setCellValue(null == workStationMap.get(
                                    psScanHistoryTemp.getWorkStation()) ? null
                                    : (String) workStationMap.get(psScanHistoryTemp.getWorkStation()));
                    hssfRow.createCell(NumConstant.NUM_NINE).setCellValue(dateFormat.format(psScanHistoryTemp.getLastUpdatedDate()));
                }
            }

            DateFormat f = new SimpleDateFormat(MpConstant.DATE_FORMAT_YYYYMMDDHHMMSS_OTHER);
            String nowStr = f.format(new Date());
            String fileName = nowStr.replaceAll("\\+", "") + ExcelName.PSSCANHISTORY_EXPORT_FILE_NAME;
            // 响应到客户端
            os = response.getOutputStream();
            this.setResponseHeader(response, fileName);
            wb.write(os);
            os.flush();
            os.close();
        } catch (Exception e) {
            // TODO: handle exception
            LOG.error("getHSSFWorkbook:", e);
        } finally {
            closeResource(wb, os);
        }
        return new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID);
    }

    /**
     * 关闭资源
     *
     * @param wb
     * @param os
     */
    private void closeResource(HSSFWorkbook wb, OutputStream os) {
        if (null != os) {
            try {
                os.close();
            } catch (Exception e) {
                LOG.error("getHSSFWorkbook:", e);
            }
        }
        if (null != wb) {
            try {
                wb.close();
            } catch (Exception e) {
                LOG.error("getHSSFWorkbook:", e);
            }
        }
    }

    // 发送响应流方法
    private void setResponseHeader(HttpServletResponse response, String fileName) {

        try {
            try {
                fileName = new String(fileName.getBytes(), MpConstant.BYTE_CODE_ISO8859);
            } catch (UnsupportedEncodingException e) {
                LOG.error("setResponseHeader:", e);
            }
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.addHeader("Pargam", "no-cache");
            response.addHeader("Cache-Control", "no-cache");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
        } catch (Exception ex) {
            LOG.error("setResponseHeader:", ex);
        }
    }

    /**
     * 创建标题
     *
     * @param title
     * @param style
     * @param row
     */
    private void createTitleForSheet(String[] title, HSSFCellStyle style, HSSFRow row) {
        HSSFCell cell;
        for (int i = 0; i < title.length; i++) {
            cell = row.createCell(i);
            cell.setCellValue(title[i]);
            cell.setCellStyle(style);
        }
    }

    /**
     * SPI数据处理
     *
     * @param dto
     * @return
     * @throws Exception
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public RetCode spiDataAnalysis(SMTScanDTO dto) throws Exception {
        RetCode retCode = new RetCode(RetCode.BUSINESSERROR_CODE, RetCode.BUSINESSERROR_MSGID);
        //条码、指令、线体、是否良品
        String sn = dto.getSn();
        String workOrderNo = dto.getWorkOrderNo();
        String lineCode = dto.getLineCode();
        BigDecimal entityId = dto.getEntityId();
        BigDecimal factoryId = dto.getFactoryId();
        //写锡膏追溯
        writeSolderPasteTracing(dto);
        // 参数校验
        String errorMsg = spiDataCheck(lineCode, sn, workOrderNo);
        if (StringUtils.isNotEmpty(errorMsg)) {
            retCode.setMsg(errorMsg);
            return retCode;
        }
        // 校验线体追溯来源是否为SPI
        List<CFLine> cfLineList = psWipInfoService.getLineInfo(lineCode);
        String error = checkLineList(lineCode, cfLineList);
        if (StringUtils.isNotEmpty(error)) {
            retCode.setMsg(error);
            return retCode;
        }
        PsWorkOrderDTO psWorkOrder = ObtainRemoteServiceDataUtil.getBasicWorkerOrderInfo(workOrderNo, lineCode);
        if (null == psWorkOrder) {
            LOG.error("查询不到相应的指令信息");
            retCode.setMsg(CommonUtils.getLmbMessage(MessageId.WORKORDER_NOT_FIND));
            return retCode;
        }
        // 如果传过来的拼板数为0或者空，获取指令拼板数
        setPcbQtyForDto(dto, psWorkOrder);
        // 根据条码+指令+source_sys=SPI去历史表查询最后一次记录 取大板号
        Map<String, Object> mp = this.getParamMap(sn, workOrderNo);
        List<PsScanHistory> hisList = psScanHistoryRepository.getList(mp);
        // 用大板号+指令+source_sys=SPI查询历史表，获取小板（批次开头）数量，如果小板（批次开头）数量=拼板数，则不做处理
        Boolean returnFlag = checkQtyWithListSize(dto, workOrderNo, hisList);
        if (returnFlag) {
            retCode = new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID);
            return retCode;
        }
        // 根据条码去设备SPI表查询出该条码的记录
        List<EmEqpSpiBoardDTO> spiList = new ArrayList<>();
        List<EmEqpSpiBoardDTO> boardBarcodeList = getBoardBarcodeList(sn, Integer.toString(dto.getPcbQty()));
        // 获取SPI数据（去重）
        spiList = getSpiListByBoardBarcodeList(sn, spiList, boardBarcodeList);
        // SPI扫描历史数据根据喷码机数据补全(多拼版,适用于长沙使用富士设备过板无法取到大板条码场景)
        // 按小板获取同一块大板所有小板条码数据(根据喷码机扫描历史数据获取)。
        boolean spiWithPmj = this.getIfSpiWithPmj(cfLineList);
        List<PsScanHistory> pmjList = null;
        if (spiWithPmj && (dto.getPcbQty() > NumConstant.NUM_ONE || (psWorkOrder.getPcbQty() != null && psWorkOrder.getPcbQty().compareTo(BigDecimal.ONE) > 0))) {
            List<PsScanHistory> pmjScanhisList = psScanHistoryRepository.getListOfSameBoard(this.getStringObjectMap(sn));
            // pmj小板条码数据去重
            if (!CollectionUtils.isEmpty(pmjScanhisList)) {
                TreeSet<PsScanHistory> pmjTreeSet = new TreeSet<>(Comparator.comparing(PsScanHistory::getSn));
                pmjTreeSet.addAll(pmjScanhisList);
                pmjList = new ArrayList<>(pmjTreeSet);
            }
        }
        // 1 条码数=拼板数
        Boolean excuteFlag = excuteSpiInfo(dto, psWorkOrder, spiList, pmjList);
        if (excuteFlag) {
            retCode = new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID);
            return retCode;
        }
        try {
            spiCallScanNum(cfLineList, dto);
        } catch (Exception e) {
            LOG.info("调用过板扣数失败,lineCode-{},workOrder-{},sn-{}", lineCode, workOrderNo, sn);
        }

        retCode = new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID);
        retCode.setMsg(CommonUtils.getLmbMessage(MessageId.SPI_DATA_PROCESS_SUCCESS));
        return retCode;
    }

    public Map<String, Object> getStringObjectMap(String sn) {
        Map<String, Object> mp = new HashMap<>();
        mp.put("sn", sn);
        mp.put("sourceSys", MpConstant.SOURCE_SYS_PMJ);
        mp.put("orderField", MpConstant.CREATE_DATE);
        mp.put("order", Constant.DESC);
        return mp;
    }

    public Map<String, Object> getParamMap(String sn, String workOrderNo) {
        Map<String, Object> mp = new HashMap<>();
        mp.put("sn", sn);
        mp.put("workOrderNo", workOrderNo);
        mp.put("sourceSys", MpConstant.SOURCE_SYS_SPI);
        mp.put("orderField", MpConstant.CREATE_DATE);
        mp.put("order", Constant.DESC);
        return mp;
    }

    /**
     * 锡膏追溯
     *
     * @param dto
     * @throws Exception
     */
    private void writeSolderPasteTracing(SMTScanDTO dto) throws Exception {
        if(StringUtils.isEmpty(dto.getLineCode()) ||StringUtils.isEmpty(dto.getWorkOrderNo()) ||StringUtils.isEmpty(dto.getSn())){
            imesLogService.log(dto,Constant.SOLDER_PASTE_TRACING_ABNORMAL_DATA);
            return;
        }
        String empNo = StringUtils.isEmpty(dto.getCreateBy())?Constant.SYSTEM:dto.getCreateBy();
        List<AuxMaterialMouting> auxMaterialMoutingList = auxMaterialMountingService.getListByLineAndWorkOrder(dto.getLineCode(), dto.getWorkOrderNo());
        if(!CollectionUtils.isEmpty(auxMaterialMoutingList)){
            List<AuxMaterialTracingEntityDTO> auxMaterialTracingEntityDTOList = new ArrayList<>();
            for (AuxMaterialMouting auxMaterialMouting : auxMaterialMoutingList) {
                AuxMaterialTracingEntityDTO auxMaterialTracingEntityDTO = new AuxMaterialTracingEntityDTO();
                auxMaterialTracingEntityDTO.setId(UUID.randomUUID().toString());
                auxMaterialTracingEntityDTO.setCreateBy(empNo);
                auxMaterialTracingEntityDTO.setLastUpdatedBy(empNo);
                auxMaterialTracingEntityDTO.setSn(dto.getSn());
                auxMaterialTracingEntityDTO.setMaterialType(NumConstant.STRING_ONE);
                auxMaterialTracingEntityDTO.setObjectId(auxMaterialMouting.getObjectId());
                auxMaterialTracingEntityDTO.setProdplanId(auxMaterialMouting.getProdplanId());
                auxMaterialTracingEntityDTO.setWorkOrderNo(auxMaterialMouting.getWorkOrderNo());
                auxMaterialTracingEntityDTO.setLineCode(auxMaterialMouting.getLineCode());
                auxMaterialTracingEntityDTO.setItemNo(auxMaterialMouting.getItemNo());
                auxMaterialTracingEntityDTO.setItemName(auxMaterialMouting.getItemName());
                auxMaterialTracingEntityDTOList.add(auxMaterialTracingEntityDTO);
            }
            auxMaterialTracingService.batchInsert(auxMaterialTracingEntityDTOList);
        }
    }

    /**
     * @param dto
     * @param psWorkOrder
     * @param spiList
     * @param pmjList
     * @return
     */
    public Boolean excuteSpiInfo(SMTScanDTO dto, PsWorkOrderDTO psWorkOrder,
                                  List<EmEqpSpiBoardDTO> spiList, List<PsScanHistory> pmjList) {

        Boolean excuteFlag = false;
        if (!CollectionUtils.isEmpty(pmjList)) {
            // 借用PMJ数据生成SPI扫描历史记录。（适用于长沙工厂SPI采集不全且富士设备过板扫描无法采集大板条码场景）
            insertScanHistoryInfoByPmjList(dto, psWorkOrder, pmjList);
        } else if (!CollectionUtils.isEmpty(spiList)) {
            // 大板条码不为null，SPI存在数据，当面指令按SPI写入历史表
            insertScanHistoryInfo(dto, psWorkOrder, spiList);

            // 按输入条码查询历史表，获取非当面指令的历史数据
            Pair<String, String> pairMap = getOtherWorkOrder(dto.getSn(), dto.getWorkOrderNo(), spiList);
            String hisWorkOrder = pairMap.getFirst();
            String hisParentSn = pairMap.getSecond();

            // 历史数据中条码（当前批次开头）的个数小于spi中条码个数据时补全（历史表没数据或条码个数等于拼板数据时不做处理）
            dealWhenWorkOrderIsNotEmpty(dto, spiList,
                    hisWorkOrder, hisParentSn);
        } else {
            // 2 条码数!=拼板数
            // 条码保存历史表
            excuteFlag = dealWithOtherConditionForHisInfo(dto,
                    psWorkOrder);
        }
        return excuteFlag;
    }

    /**
     * 获取另一面指令
     * @param sn
     * @param workOrderNo
     * @param spiList
     * @return
     */
    private Pair<String, String> getOtherWorkOrder(String sn, String workOrderNo, List<EmEqpSpiBoardDTO> spiList) {
        Map<String, Object> queryMap = new HashMap<>();
        List<String> snList = new ArrayList<>();
        snList.add(sn);
        //取所有小板条码 避免第一面不完整，第二面完整时取不到另一面指令
        if(!CollectionUtils.isEmpty(spiList)){
            snList.addAll(spiList.stream().filter(e->StringUtils.isNotEmpty(e.getBarcode())).map(e->e.getBarcode()).distinct().collect(Collectors.toList()));
        }
        queryMap.put("snList", snList);
        queryMap.put("sourceSys", MpConstant.SOURCE_SYS_SPI);
        queryMap.put("taskNo", sn.substring(0, MpConstant.SN_MIN_LENGTH));
        queryMap.put("orderField", MpConstant.CREATE_DATE);
        List<PsScanHistory> scanList = psScanHistoryRepository.getList(queryMap);
        Pair<String, String> pairMap = getWorkOrderAndParentSn(scanList, workOrderNo);
        return pairMap;
    }

    /**
     * @param dto
     * @param psWorkOrder
     * @return
     */
    public Boolean dealWithOtherConditionForHisInfo(SMTScanDTO dto, PsWorkOrderDTO psWorkOrder) {
        String sn = dto.getSn();
        String workOrderNo = dto.getWorkOrderNo();
        String lineCode = dto.getLineCode();
        Integer isPass = dto.getIsPass();
        BigDecimal entityId = dto.getEntityId();
        BigDecimal factoryId = dto.getFactoryId();
        String empNo = dto.getCreateBy();
        Boolean retFlag = false;
        Map<String, Object> queryMap = new HashMap<String, Object>();
        queryMap.put("sn", sn);
        queryMap.put("crafSection", psWorkOrder.getCraftSection());
        queryMap.put("workOrderNo", workOrderNo);
        queryMap.put("status", Constant.FLAG_Y);
        queryMap.put("sourceSys", MpConstant.SOURCE_SYS_SPI);
        List<PsScanHistory> list = psScanHistoryRepository.getList(queryMap);

        if (!CollectionUtils.isEmpty(list)) {
            // 如果存在历史信息则更新时间
            for (int i = 0; i < list.size(); i++) {
                psScanHistoryRepository.updatePsScanHistoryById(list.get(i));
            }
            retFlag = true;
        } else {
            // 如果不存在就新增扫描历史表
            String processCode = psWorkOrder.getProcessGroup();
            if (processCode.indexOf(MpConstant.SPLIT_CHAR) > 0) {
                processCode = processCode.substring(0, processCode.indexOf(MpConstant.SPLIT_CHAR));
            }
            String workStation = processCode + MpConstant.SCAN_HISTORY_REMARK_ONE;
            String craftSection = psWorkOrder.getCraftSection();

            PsScanHistory psScanHistory = new PsScanHistory();
            psScanHistory.setIsTraceCalculate(Constant.FLAG_N);
            psScanHistory.setItemNo(psWorkOrder.getItemNo());
            psScanHistory.setCraftSection(craftSection);
            psScanHistory.setSmtScanId(UUID.randomUUID().toString());
            psScanHistory.setItemName(psWorkOrder.getItemName());
            psScanHistory.setCurrProcessCode(processCode);
            psScanHistory.setWorkStation(workStation);
            psScanHistory.setRemark(MpConstant.SCAN_HISTORY_REMARK_ONE);
            psScanHistory.setSourceSys(MpConstant.SOURCE_SYS_SPI);
            psScanHistory.setStatus(Constant.FLAG_Y);
            psScanHistory.setLineCode(lineCode);
            psScanHistory.setSn(sn);
            psScanHistory.setCreateBy(empNo);
            psScanHistory.setEnabledFlag(Constant.FLAG_Y);
            psScanHistory.setEntityId(entityId);
            psScanHistory.setWorkOrderNo(workOrderNo);
            psScanHistory.setFactoryId(factoryId);

            // 0:机器检测合格; 2:人工检测合格; 4:warning 接近不合格
            if (isPass != NumConstant.NUM_ZERO && isPass != NumConstant.NUM_TWO && isPass != NumConstant.NUM_FOUR) {
                psScanHistory.setIsTraceCalculate(Constant.FLAG_Y);
                psScanHistory.setStatus(Constant.FLAG_N);
            }
            psScanHistory.setSourceSysName(craftSection + MpConstant.SCAN_HISTORY_REMARK_ONE);
            psScanHistory.setParentSn(dto.getParentSn());
            psScanHistory.setPcbQty(BigDecimal.valueOf(dto.getPcbQty()));
            psScanHistory.setAttribute1(workOrderNo.substring(0, MpConstant.SN_MIN_LENGTH));
            psScanHistoryRepository.insertPsScanHistory(psScanHistory);
        }
        return retFlag;
    }

    private void dealWhenWorkOrderIsNotEmpty(SMTScanDTO dto, List<EmEqpSpiBoardDTO> spiList,
                                             String hisWorkOrder, String hisParentSn) {
        String sn = dto.getSn();
        String workOrderNo = dto.getWorkOrderNo();
        String lineCode = dto.getLineCode();
        Integer isPass = dto.getIsPass();
        BigDecimal entityId = dto.getEntityId();
        BigDecimal factoryId = dto.getFactoryId();
        String empNo = dto.getCreateBy();
        Map<String, Object> queryMap;
        List<PsScanHistory> scanList;
        if (!StringHelper.isNotEmpty(hisWorkOrder)) {
            return;
        }
        queryMap = new HashMap<String, Object>();
        queryMap.put("workOrderNo", hisWorkOrder);
        queryMap.put("sourceSys", MpConstant.SOURCE_SYS_SPI);
        queryMap.put("taskNo", sn.substring(0, MpConstant.SN_MIN_LENGTH));
        queryMap.put("parentSn", hisParentSn);
        scanList = psScanHistoryRepository.getList(queryMap);
        if (CollectionUtils.isEmpty(scanList)) {
            return;
        }
        if (scanList.size() != 0 && scanList.size() < dto.getPcbQty()) {
            PsScanHistory historyObj = scanList.get(NumConstant.NUM_ZERO);
            for (EmEqpSpiBoardDTO hisObj : spiList) {
                boolean isExists = false;
                isExists = checkSnExists(scanList, hisObj, isExists);
                if (!isExists) {
                    PsScanHistory psScanHistory = new PsScanHistory();
                    String processCode = historyObj.getCurrProcessCode();
                    psScanHistory.setSmtScanId(UUID.randomUUID().toString());
                    psScanHistory.setIsTraceCalculate(Constant.FLAG_N);
                    psScanHistory.setItemName(historyObj.getItemName());
                    psScanHistory.setCraftSection(historyObj.getCraftSection());
                    psScanHistory.setItemNo(historyObj.getItemNo());
                    psScanHistory.setSourceSys(MpConstant.SOURCE_SYS_SPI);
                    psScanHistory.setWorkStation(historyObj.getWorkStation());
                    psScanHistory.setCurrProcessCode(processCode);
                    psScanHistory.setStatus(Constant.FLAG_Y);
                    psScanHistory.setRemark(MpConstant.SCAN_HISTORY_REMARK_ONE);
                    psScanHistory.setEnabledFlag(Constant.FLAG_Y);
                    psScanHistory.setLineCode(lineCode);
                    psScanHistory.setSn(hisObj.getBarcode());
                    psScanHistory.setCreateBy(empNo);
                    psScanHistory.setFactoryId(factoryId);
                    psScanHistory.setWorkOrderNo(hisWorkOrder);
                    psScanHistory.setEntityId(entityId);
                    // 0:机器检测合格; 2:人工检测合格; 4:warning 接近不合格
                    if (isPass != NumConstant.NUM_ZERO && isPass != NumConstant.NUM_TWO && isPass != NumConstant.NUM_FOUR) {
                        psScanHistory.setIsTraceCalculate(Constant.FLAG_Y);
                        psScanHistory.setStatus(Constant.FLAG_N);
                    }
                    psScanHistory.setSourceSysName(historyObj.getCraftSection() + MpConstant.SCAN_HISTORY_REMARK_ONE);
                    psScanHistory.setParentSn(hisParentSn);
                    psScanHistory.setPcbQty(BigDecimal.valueOf(dto.getPcbQty()));
                    psScanHistory.setAttribute1(workOrderNo.substring(0, MpConstant.SN_MIN_LENGTH));
                    psScanHistoryRepository.insertPsScanHistory(psScanHistory);
                }
            }
        }
    }

    /**
     * @param scanList
     * @param hisObj
     * @param isExists
     * @return
     */
    private boolean checkSnExists(List<PsScanHistory> scanList, EmEqpSpiBoardDTO hisObj, boolean isExists) {
        for (PsScanHistory hisObj2 : scanList) {
            if (hisObj.getBarcode().equals(hisObj2.getSn())) {
                isExists = true;
                break;
            }
        }
        return isExists;
    }

    /**
     * 获取指令和父SN
     *
     * @param scanList
     * @param workOrderNo
     * @return
     */
    private Pair<String, String> getWorkOrderAndParentSn(List<PsScanHistory> scanList, String workOrderNo) {

        String hisWorkOrder = "";
        String hisParentSn = "";
        if (!CollectionUtils.isEmpty(scanList)) {
            if (scanList.size() > 0) {
                for (PsScanHistory hisObj : scanList) {
                    if (!hisObj.getWorkOrderNo().equals(workOrderNo)) {
                        hisWorkOrder = hisObj.getWorkOrderNo();
                        hisParentSn = hisObj.getParentSn();
                        break;
                    }
                }
            }
        }
        return Pair.of(hisWorkOrder, hisParentSn);
    }

    /**
     * 获取spiList信息
     *
     * @param sn
     * @param spiList
     * @param boardBarcodeList
     * @return
     * @throws Exception
     */
    private List<EmEqpSpiBoardDTO> getSpiListByBoardBarcodeList(String sn, List<EmEqpSpiBoardDTO> spiList,
                                                                List<EmEqpSpiBoardDTO> boardBarcodeList) throws Exception {
        if (!CollectionUtils.isEmpty(boardBarcodeList)) {
            if (boardBarcodeList.size() > 0) {
                String boardBarcode = boardBarcodeList.get(NumConstant.NUM_ZERO).getBoardBarcode();
                String qryTime = boardBarcodeList.get(NumConstant.NUM_ZERO).getCreateDate();
                // 按大板分组时间升序，获取最早时间的条码（批次开头）数量
                spiList = getEmEqpSpiBoardList(boardBarcode, sn.substring(0, 7), qryTime);
            }
        }

        // spi小板条码数据去重
        if (spiList != null && !CollectionUtils.isEmpty(spiList)) {
            TreeSet<EmEqpSpiBoardDTO> spiTreeSet = new TreeSet<>(Comparator.comparing(EmEqpSpiBoardDTO::getBarcode));
            spiTreeSet.addAll(spiList);
            spiList.clear();
            spiList.addAll(spiTreeSet);
        }
        return spiList;
    }

    /**
     * 校验数量
     *
     * @param dto
     * @param workOrderNo
     * @param hisList
     * @return
     */
    private Boolean checkQtyWithListSize(SMTScanDTO dto, String workOrderNo, List<PsScanHistory> hisList) {
        Map<String, Object> mp;
        Boolean returnFlag = false;
        if (!CollectionUtils.isEmpty(hisList) && StringUtils.isNotBlank(hisList.get(NumConstant.NUM_ZERO).getParentSn())) {
            String parentSn = hisList.get(NumConstant.NUM_ZERO).getParentSn();

            mp = new HashMap<String, Object>();
            mp.put("parentSn", parentSn);
            mp.put("workOrderNo", workOrderNo);
            mp.put("sourceSys", MpConstant.SOURCE_SYS_SPI);
            // 查询良品板的扫描历史表
            mp.put("status", Constant.FLAG_Y);
            hisList = psScanHistoryRepository.getList(mp);
            if (!CollectionUtils.isEmpty(hisList)) {
                // 小板条码去重
                List<PsScanHistory> distinctList = new ArrayList<>();
                Set<String> snSet = new HashSet<>();
                for (PsScanHistory item : hisList) {
                    if (!snSet.contains(item.getSn())) {
                        snSet.add(item.getSn());
                        distinctList.add(item);
                    }
                }
                if (distinctList.size() >= dto.getPcbQty()) {
                    returnFlag = true;
                }
            }
        }
        return returnFlag;
    }

    /**
     * @param psWorkOrder
     * @param spiList
     */
    private void insertScanHistoryInfo(SMTScanDTO dto, PsWorkOrderDTO psWorkOrder, List<EmEqpSpiBoardDTO> spiList) {
        String workOrderNo = dto.getWorkOrderNo();
        String lineCode = dto.getLineCode();
        Integer isPass = dto.getIsPass();
        BigDecimal entityId = dto.getEntityId();
        BigDecimal factoryId = dto.getFactoryId();
        String empNo = dto.getCreateBy();
        this.excludeExistOfSpi(spiList, psWorkOrder.getWorkOrderNo(), psWorkOrder.getCraftSection());
        for (EmEqpSpiBoardDTO obj : spiList) {
            String craftSection = psWorkOrder.getCraftSection();
            String processCode = psWorkOrder.getProcessGroup();
            if (processCode.indexOf(MpConstant.SPLIT_CHAR) > 0) {
                processCode = processCode.substring(0, processCode.indexOf(MpConstant.SPLIT_CHAR));
            }
            String workStation = processCode + MpConstant.SCAN_HISTORY_REMARK_ONE;

            PsScanHistory psScanHistory = new PsScanHistory();
            psScanHistory.setIsTraceCalculate(Constant.FLAG_N);
            psScanHistory.setSmtScanId(UUID.randomUUID().toString());
            psScanHistory.setItemName(psWorkOrder.getItemName());
            psScanHistory.setItemNo(psWorkOrder.getItemNo());
            psScanHistory.setCurrProcessCode(processCode);
            psScanHistory.setCraftSection(craftSection);
            psScanHistory.setWorkStation(workStation);
            psScanHistory.setRemark(MpConstant.SCAN_HISTORY_REMARK_ONE);
            psScanHistory.setSourceSys(MpConstant.SOURCE_SYS_SPI);
            psScanHistory.setStatus(Constant.FLAG_Y);
            psScanHistory.setLineCode(lineCode);
            psScanHistory.setEnabledFlag(Constant.FLAG_Y);
            psScanHistory.setWorkOrderNo(workOrderNo);
            psScanHistory.setSn(obj.getBarcode());
            psScanHistory.setFactoryId(factoryId);
            psScanHistory.setCreateBy(empNo);
            psScanHistory.setEntityId(entityId);
            // 0:机器检测合格; 2:人工检测合格; 4:warning 接近不合格
            if (isPass != NumConstant.NUM_ZERO && isPass != NumConstant.NUM_TWO && isPass != NumConstant.NUM_FOUR) {
                psScanHistory.setIsTraceCalculate(Constant.FLAG_Y);
                psScanHistory.setStatus(Constant.FLAG_N);
            }
            psScanHistory.setSourceSysName(craftSection + MpConstant.SCAN_HISTORY_REMARK_ONE);
            psScanHistory.setParentSn(obj.getBoardBarcode());
            psScanHistory.setPcbQty(BigDecimal.valueOf(obj.getArrayCount()));
            psScanHistory.setAttribute1(workOrderNo.substring(0, MpConstant.SN_MIN_LENGTH));
            psScanHistoryRepository.insertPsScanHistory(psScanHistory);
        }
    }

    /**
     * 借用PMJ数据生成SPI扫描历史记录
     *
     * @param dto
     * @param psWorkOrder
     * @param pmjList
     */
    private void insertScanHistoryInfoByPmjList(SMTScanDTO dto, PsWorkOrderDTO psWorkOrder, List<PsScanHistory> pmjList) {
        String workOrderNo = dto.getWorkOrderNo();
        String lineCode = dto.getLineCode();
        Integer isPass = dto.getIsPass();
        BigDecimal entityId = dto.getEntityId();
        BigDecimal factoryId = dto.getFactoryId();
        String empNo = dto.getCreateBy();
        // 排除已经存在有效SPI历史记录的数据，不再插入，防止追溯计算到相同条码上
        this.excludeExistOfPmj(pmjList, psWorkOrder.getWorkOrderNo(), psWorkOrder.getCraftSection());
        for (PsScanHistory obj : pmjList) {
            String processCode = psWorkOrder.getProcessGroup();
            if (processCode.indexOf(MpConstant.SPLIT_CHAR) > 0) {
                processCode = processCode.substring(0, processCode.indexOf(MpConstant.SPLIT_CHAR));
            }
            String workStation = processCode + MpConstant.SCAN_HISTORY_REMARK_ONE;
            String craftSection = psWorkOrder.getCraftSection();

            PsScanHistory psScanHistory = new PsScanHistory();
            psScanHistory.setIsTraceCalculate(Constant.FLAG_N);
            psScanHistory.setSmtScanId(UUID.randomUUID().toString());
            psScanHistory.setItemName(psWorkOrder.getItemName());
            psScanHistory.setItemNo(psWorkOrder.getItemNo());
            psScanHistory.setCurrProcessCode(processCode);
            psScanHistory.setWorkStation(workStation);
            psScanHistory.setCraftSection(craftSection);
            psScanHistory.setStatus(Constant.FLAG_Y);
            psScanHistory.setRemark(MpConstant.SCAN_HISTORY_REMARK_ONE);
            psScanHistory.setSourceSys(MpConstant.SOURCE_SYS_SPI);
            psScanHistory.setLineCode(lineCode);
            psScanHistory.setEnabledFlag(Constant.FLAG_Y);
            psScanHistory.setSn(obj.getSn());
            psScanHistory.setWorkOrderNo(workOrderNo);
            psScanHistory.setCreateBy(empNo);
            psScanHistory.setFactoryId(factoryId);
            psScanHistory.setEntityId(entityId);
            // 0:机器检测合格; 2:人工检测合格; 4:warning 接近不合格
            if (isPass != NumConstant.NUM_ZERO && isPass != NumConstant.NUM_TWO && isPass != NumConstant.NUM_FOUR) {
                psScanHistory.setIsTraceCalculate(Constant.FLAG_Y);
                psScanHistory.setStatus(Constant.FLAG_N);
            }
            psScanHistory.setSourceSysName(craftSection + MpConstant.SCAN_HISTORY_REMARK_ONE);
            psScanHistory.setParentSn(obj.getParentSn());
            psScanHistory.setPcbQty(new BigDecimal(dto.getPcbQty()));
            psScanHistory.setAttribute1(workOrderNo.substring(0, MpConstant.SN_MIN_LENGTH));
            psScanHistoryRepository.insertPsScanHistory(psScanHistory);
        }
    }

    @Override
    public void insertScanHistoryBySpi(String factoryId, List<EmEqpSpiBoardDTO> spiList) {
        if (CollectionUtils.isEmpty(spiList)) {
            return;
        }
        String workOrderNo = spiList.get(0).getWorkOrderNo();
        PsEntityPlanBasic psWorkOrder = PlanscheduleRemoteService.getWorkOrderInfoByWorkOrderNo(workOrderNo);
        if (psWorkOrder == null) {
            LOG.error("指令信息不存在：" + psWorkOrder);
            return;
        }

        // 排除已经存在有效SPI扫描历史记录的数据
        this.excludeExistOfSpi(spiList, psWorkOrder.getWorkOrderNo(), psWorkOrder.getCraftSection());
        for (EmEqpSpiBoardDTO obj : spiList) {
            String craftSection = psWorkOrder.getCraftSection();
            String processCode = psWorkOrder.getProcessGroup();
            if (StringUtils.isNotEmpty(processCode) && processCode.indexOf(MpConstant.SPLIT_CHAR) > 0) {
                processCode = processCode.substring(0, processCode.indexOf(MpConstant.SPLIT_CHAR));
            }
            String workStation = MpConstant.SOURCE_SYS_SPI;

            PsScanHistory psScanHistory = new PsScanHistory();
            // 不再写入N,不再作为是否计算追溯标识
            psScanHistory.setIsTraceCalculate(null);
            psScanHistory.setSmtScanId(UUID.randomUUID().toString());
            psScanHistory.setItemName(psWorkOrder.getItemName());
            psScanHistory.setItemNo(psWorkOrder.getItemNo());
            psScanHistory.setCurrProcessCode(processCode);
            psScanHistory.setCraftSection(craftSection);
            psScanHistory.setWorkStation(workStation);
            psScanHistory.setRemark(MpConstant.SOURCE_SYS_SPI);
            psScanHistory.setSourceSys(MpConstant.SOURCE_SYS_SPI);
            psScanHistory.setLineCode(obj.getLineCode());
            psScanHistory.setEnabledFlag(Constant.FLAG_Y);
            psScanHistory.setWorkOrderNo(workOrderNo);
            psScanHistory.setSn(obj.getBarcode());
            psScanHistory.setFactoryId(new BigDecimal(factoryId));
            psScanHistory.setCreateBy(obj.getCreateBy());
            if (!this.checkIsPass(obj.getIsPass())) {
                psScanHistory.setStatus(Constant.FLAG_N);
            } else {
                psScanHistory.setStatus(Constant.FLAG_Y);
            }
            psScanHistory.setSourceSysName(MpConstant.SOURCE_SYS_SPI);
            psScanHistory.setParentSn(obj.getBoardBarcode());
            int arrayCount = obj.getArrayCount() == null ? NumConstant.NUM_ZERO : obj.getArrayCount();
            psScanHistory.setPcbQty(BigDecimal.valueOf(arrayCount));
            psScanHistory.setAttribute1(psWorkOrder.getSourceTask());
            psScanHistoryRepository.insertPsScanHistory(psScanHistory);
        }
    }

    private boolean checkIsPass(BigDecimal isPass) {
        if (isPass == null) {
            return false;
        }
        int status = isPass.intValue();
        // 0:机器检测合格; 2:人工检测合格; 4:warning 接近不合格
        if (status == NumConstant.NUM_ZERO || status == NumConstant.NUM_TWO || status == NumConstant.NUM_FOUR) {
            return true;
        }
        return false;
    }

    /**
     * 排除已经存在有效SPI扫描历史记录的数据
     * @param pmjList
     * @param workOrderNo
     * @param craftSection
     */
    public void excludeExistOfPmj(List<PsScanHistory> pmjList, String workOrderNo, String craftSection) {
        if (CollectionUtils.isEmpty(pmjList) || StringUtils.isEmpty(workOrderNo) || StringUtils.isEmpty(craftSection)) {
            return;
        }
        Set<String> snSet = pmjList.stream().map(PsScanHistory::getSn).collect(Collectors.toSet());
        Set<String> parentSnSet = pmjList.stream().map(PsScanHistory::getParentSn).collect(Collectors.toSet());
        List<String> snAndParentSnList = this.getPassSpiScanHistory(snSet, parentSnSet, workOrderNo, craftSection);
        if (CollectionUtils.isEmpty(snAndParentSnList)) {
            return;
        }
        for (Iterator<PsScanHistory> iterator = pmjList.iterator(); iterator.hasNext(); ) {
            PsScanHistory psScanHistory = iterator.next();
            if (snAndParentSnList.contains(psScanHistory.getSn() + psScanHistory.getParentSn())) {
                // 排除已经存在有效SPI扫描历史记录的数据
                iterator.remove();
            }
        }
    }

    /**
     * 排除已经存在有效SPI扫描历史记录的数据
     * @param spiList
     * @param workOrderNo
     * @param craftSection
     */
    public void excludeExistOfSpi(List<EmEqpSpiBoardDTO> spiList, String workOrderNo, String craftSection) {
        if (StringUtils.isEmpty(workOrderNo) || StringUtils.isEmpty(craftSection)) {
            return;
        }
        Set<String> snSet = spiList.stream().map(EmEqpSpiBoardDTO::getBarcode).collect(Collectors.toSet());
        Set<String> parentSnSet = spiList.stream().map(EmEqpSpiBoardDTO::getBoardBarcode).collect(Collectors.toSet());
        List<String> snAndParentSnList = this.getPassSpiScanHistory(snSet, parentSnSet, workOrderNo, craftSection);
        if (CollectionUtils.isEmpty(snAndParentSnList)) {
            return;
        }
        for (Iterator<EmEqpSpiBoardDTO> iterator = spiList.iterator(); iterator.hasNext(); ) {
            EmEqpSpiBoardDTO emEqpSpiBoardDTO = iterator.next();
            if (snAndParentSnList.contains(emEqpSpiBoardDTO.getBarcode() + emEqpSpiBoardDTO.getBoardBarcode())) {
                // 排除已经存在有效SPI扫描历史记录的数据
                iterator.remove();
            }
        }
    }

    private List<String> getPassSpiScanHistory(Collection<String> snSet, Collection<String> parentSnSet, String workOrderNo, String craftSection) {
        if (CollectionUtils.isEmpty(snSet) || CollectionUtils.isEmpty(parentSnSet)
                || snSet.size() > NumConstant.NUM_1000 || parentSnSet.size() > NumConstant.NUM_1000) {
            return new ArrayList<>();
        }
        return psScanHistoryRepository.getPassSpiScanHistory(snSet, parentSnSet, workOrderNo, craftSection);
    }

    /**
     * @param dto
     * @param psWorkOrder
     * @throws Exception
     */
    private void setPcbQtyForDto(SMTScanDTO dto, PsWorkOrderDTO psWorkOrder) throws Exception {
        if (dto.getPcbQty() == null || dto.getPcbQty() <= 0 || psWorkOrder.getPcbQty() != null) {
            BigDecimal pcbQty = psWorkOrder.getPcbQty();
            if (pcbQty == null) {
                dto.setPcbQty(NumConstant.NUM_ONE);
                // throw new Exception(CommonUtils.getLmbMessage(MessageId.SPI_AND_INSTRUCTION_IS_EMPTY));
            }else {
                dto.setPcbQty(pcbQty.intValue());
            }
        }
    }

    /**
     * @param lineCode
     * @param cfLineList
     */
    private String checkLineList(String lineCode, List<CFLine> cfLineList) {
        String error = StringUtils.EMPTY;
        if (!CollectionUtils.isEmpty(cfLineList)) {
            String traceSource = cfLineList.get(NumConstant.NUM_ZERO).getTraceSource();
            if (StringHelper.isNotEmpty(traceSource)) {
                if (!SPI.equals(traceSource)) {
                    LOG.error("线体：" + lineCode + "，追溯来源错误：" + traceSource);
                    error = CommonUtils.getLmbMessage(MessageId.LINECODE_TRACESOURCE_ERROR, lineCode, traceSource);
                }
            }
        }
        return error;
    }

    /**
     * 获取线体是否采用SPI辅以喷码机追溯
     *
     * @param cfLineList
     */
    private boolean getIfSpiWithPmj(List<CFLine> cfLineList) {
        boolean spiTraceWithPmj = false;
        if (!CollectionUtils.isEmpty(cfLineList)) {
            String traceSource = cfLineList.get(NumConstant.NUM_ZERO).getTraceSource();
            String spiWithPmj = cfLineList.get(NumConstant.NUM_ZERO).getSpiWithPmj();
            if (SPI.equals(traceSource) && Constant.FLAG_Y.equals(spiWithPmj)) {
                spiTraceWithPmj = true;
            }
        }
        return spiTraceWithPmj;
    }

    /**
     * SPI数据处理参数校验
     *
     * @param lineCode
     * @param sn
     * @param workOrderNo
     * @return
     * @throws Exception
     */
    private String spiDataCheck(String lineCode, String sn, String workOrderNo) throws Exception {
        String errorMsg = StringUtils.EMPTY;
        // 校验
        if (StringHelper.isEmpty(lineCode)) {
            errorMsg = CommonUtils.getLmbMessage(MessageId.LINE_CODE_IS_NULL);
            return errorMsg;
        }
        if (StringHelper.isEmpty(sn)) {
            errorMsg = CommonUtils.getLmbMessage(MessageId.SN_INPUT_ERROR);
            return errorMsg;
        }
        if (StringHelper.isEmpty(workOrderNo) || workOrderNo.length() < SEVEN) {
            errorMsg = CommonUtils.getLmbMessage(MessageId.WORKORDER_IS_NULL_OR_LESS_THAN_SEVEN);
            return errorMsg;
        }
        if (sn.length() == NumConstant.NUM_TWELVE) {
            if (!sn.substring(0, SEVEN).equals(workOrderNo.substring(0, SEVEN))) {
                errorMsg = CommonUtils.getLmbMessage(MessageId.BARCODE_PRODPLANID_ERROR);
                return errorMsg;
            }
        } else if (sn.startsWith(BusinessConstant.FLAG_P) && sn.length() == NumConstant.NUM_THIRTEEN) {
            if (!sn.substring(NumConstant.NUM_ONE, NumConstant.NUM_EIGHT).equals(workOrderNo.substring(0, SEVEN))) {
                errorMsg = CommonUtils.getLmbMessage(MessageId.BARCODE_PRODPLANID_ERROR);
                return errorMsg;
            }
        } else {
            errorMsg = CommonUtils.getLmbMessage(MessageId.SN_INPUT_ERROR);
            return errorMsg;
        }
        return errorMsg;
    }

    public void spiCallScanNum(List<CFLine> cfLineList, SMTScanDTO dto) throws Exception {
        String workOrderNo = dto.getWorkOrderNo();
        String lineCode = dto.getLineCode();
        BigDecimal entityId = dto.getEntityId();
        BigDecimal factoryId = dto.getFactoryId();
        //新增逻辑：如果线体设置扣数及追溯点为SPI，调用smtSnScanNum()方法扣数
        if (!CollectionUtils.isEmpty(cfLineList)) {
            String tracingCalPoint = cfLineList.get(NumConstant.NUM_ZERO).getTracingCalPoint();
            if (SPI.equals(tracingCalPoint)) {
                SMTScanDTO snScanDto = new SMTScanDTO();
                snScanDto.setLineCode(lineCode);
                snScanDto.setSn(dto.getSn());
                snScanDto.setPassNum(1);
                snScanDto.setWorkOrderNo(workOrderNo);
                snScanDto.setCreateBy(BusinessConstant.SYS_EMP_NO);
                snScanDto.setIsEndBoard(NumConstant.STR_ONE);
                snScanDto.setFactoryId(factoryId);
                snScanDto.setEntityId(entityId);
                snScanDto.setCalInSPI(NumConstant.STR_ONE);
                smtSnScanNum(snScanDto);
            }
        } else {
            throw new Exception(CommonUtils.getLmbMessage(MessageId.LINE_INFO_NOT_MAINTAINED, lineCode));
        }
    }

    /**
     * @param sn
     * <AUTHOR>
     * 按小板获取大板号
     */
    public List<EmEqpSpiBoardDTO> getBoardBarcodeList(String sn, String pcbQty) throws Exception {
        // 点对点调用指令查询服务
        Map<String, String> mapW = new HashMap<String, String>();
        mapW.put("barcode", sn);
        mapW.put("lotNo", sn.substring(0, MpConstant.SN_MIN_LENGTH));
        mapW.put("rw", pcbQty);
        Map<String, String> headerParamsMapW = MESHttpHelper.getHttpRequestHeader();
        String paramsW = JacksonJsonConverUtil.beanToJson(mapW);
        String serviceNameW = MicroServiceNameEum.EQPMGMT;
        String versionW = MicroServiceNameEum.VERSION;
        String sendTypeW = MicroServiceNameEum.SENDTYPEGET;
        String getUrlW = "/eqp/getBoardBarcodeList";

        String getresultW = MicroServiceRestUtil.invokeService(serviceNameW, versionW, sendTypeW, getUrlW,
                paramsW, headerParamsMapW);
        JsonNode json = JacksonJsonConverUtil.getMapperInstance().readTree(getresultW);
        String bo = json.get(MpConstant.JSON_BO).toString();
        List<EmEqpSpiBoardDTO> boardBarcodeList =
                JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<EmEqpSpiBoardDTO>>() {
                });
        return boardBarcodeList;
    }

    /**
     * @param boardBarcode
     * @param prodplanId
     * <AUTHOR>
     * 查询spi扫描数据
     */
    public List<EmEqpSpiBoardDTO> getEmEqpSpiBoardList(String boardBarcode, String prodplanId, String qryTime) throws Exception {
        // 点对点调用指令查询服务
        Map<String, String> mapW = new HashMap<String, String>();
        mapW.put("boardBarcode", boardBarcode);
        mapW.put("lotNo", prodplanId);
        mapW.put(MpConstant.CREATE_DATE, qryTime);
        Map<String, String> headerParamsMapW = MESHttpHelper.getHttpRequestHeader();
        String paramsW = JacksonJsonConverUtil.beanToJson(mapW);
        String serviceNameW = MicroServiceNameEum.EQPMGMT;
        String versionW = MicroServiceNameEum.VERSION;
        String sendTypeW = MicroServiceNameEum.SENDTYPEGET;
        String getUrlW = "/eqp/getEmEqpSpiBoardList";

        String getresultW = MicroServiceRestUtil.invokeService(serviceNameW, versionW, sendTypeW, getUrlW,
                paramsW, headerParamsMapW);
        JsonNode json = JacksonJsonConverUtil.getMapperInstance().readTree(getresultW);
        String bo = json.get(MpConstant.JSON_BO).toString();
        List<EmEqpSpiBoardDTO> list =
                JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<EmEqpSpiBoardDTO>>() {
                });
        return list;
    }

    /**
     * 过板数扫描（新）
     * 2020/1/16更新:按模组扣数写追溯重构
     *
     * @param dto
     * @return
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    @Idempotnal(prefix = "smtSnScanNum")
    public RetCode smtSnScanNum(@IdempotnalKey(field = "requestKey") SMTScanDTO dto) throws Exception {

        // 指令、线体、过板数、统一时间、条码、是否最后模组
        String workOrderNo = dto.getWorkOrderNo();
        String lineCode = dto.getLineCode();
        int passNum = dto.getPassNum();
        String sn = dto.getSn();
        boolean isEndBoard = NumConstant.STR_ONE.equals(dto.getIsEndBoard());
         Date curDate = new Date();
         dto.setCurDate(curDate);
        // 校验
        checkLineCodeAndWorkOrderNo(workOrderNo, lineCode);

        // 获取线体信息:追溯来源、使用SMT计算
        CFLine cfLine = new CFLine();
        cfLine.setLineCode(lineCode);
        List<CFLine> listLine = ObtainRemoteServiceDataUtil.getLine(cfLine);
        if (null == listLine || CollectionUtils.isEmpty(listLine)) {
            LOG.info("smtTracingError: no line info:{}", lineCode);
            throw new Exception(CommonUtils.getLmbMessage(MessageId.LINE_NOT_FOUND) + lineCode);
        }
        cfLine = listLine.get(NumConstant.NUM_ZERO);
        String traceSource = cfLine.getTraceSource();
        // 按条码计算追溯
        String calInSmt = cfLine.getCalInSmt();
        String calInModule = cfLine.getCalInModule();
        // 设备物料消耗标识
        dto.setMarker(cfLine.getMaterielMarker());
        checkModuleInfo(dto, workOrderNo, isEndBoard, calInModule);
        dto.setCalInModule(calInModule);
        // 未维护线体追溯属性，则按SPI计算
        if (StringUtils.isEmpty(traceSource)) {
            traceSource = Constant.TRACE_SOURCE_SPI;
            calInSmt = Constant.FLAG_N;
        } else {
            if (StringUtils.isEmpty(calInSmt)) {
                if (Constant.TRACE_SOURCE_SPI.equals(traceSource)) {
                    calInSmt = Constant.FLAG_N;
                }
                if (Constant.TRACE_SOURCE_PMJ.equals(traceSource)) {
                    calInSmt = Constant.FLAG_Y;
                }
            }
        }

        //追溯计算点校验
        String tracingCalPoint = cfLine.getTracingCalPoint();
        //获取指令信息
        PsWorkOrderDTO psWorkOrder = ObtainRemoteServiceDataUtil.getBasicWorkerOrderInfo(workOrderNo, lineCode);
        checkTracingInfo(dto, tracingCalPoint, psWorkOrder);
        // 工序、指令ID
        String processCode = psWorkOrder.getProcessGroup();
        String workOrderId = psWorkOrder.getWorkOrderId();
        checkWorkOrderInfo(processCode, workOrderId);
        BigDecimal smtPcbQtyBD = psWorkOrder.getPcbQty();
        int smtPcbQty = 1;
        if (smtPcbQtyBD != null && smtPcbQtyBD.intValue() > 0) {
            smtPcbQty = smtPcbQtyBD.intValue();
        }

        List<PsScanHistory> list = new ArrayList<>();
        list = getScanHistoryListByCondition(dto, traceSource, calInSmt, list);
        dealWithScanHistoryList(dto, psWorkOrder, smtPcbQty, list);

        RetCode ret = new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID);
        ret.setMsg(CommonUtils.getLmbMessage(MessageId.SMT_INVESTMENT_SCAN_SUCCESS));
        return ret;
    }

    /**
     * @param dto
     * @param tracingCalPoint
     * @param psWorkOrder
     * @throws Exception
     */
    private void checkTracingInfo(SMTScanDTO dto, String tracingCalPoint, PsWorkOrderDTO psWorkOrder) throws Exception {
        if (StringUtils.isNotEmpty(tracingCalPoint)) {
            if (Constant.TRACE_SOURCE_SPI.equals(tracingCalPoint) && !NumConstant.STR_ONE.equals(dto.getCalInSPI())) {
                throw new Exception(CommonUtils.getLmbMessage(MessageId.SPI_DEDUCTIONS_AND_RETROACTIVE_EXCEPTION));
            }
        }
        if (psWorkOrder == null) {
            throw new Exception(CommonUtils.getLmbMessage(MessageId.LINE_NOT_FOUND));
        }
    }

    /**
     * @param processCode
     * @param workOrderId
     * @throws Exception
     */
    private void checkWorkOrderInfo(String processCode, String workOrderId) throws Exception {
        if (StringUtils.isEmpty(workOrderId)) {
            throw new Exception(CommonUtils.getLmbMessage(MessageId.WORKORDER_ID_IS_NULL));
        }
        if (StringUtils.isEmpty(processCode)) {
            throw new Exception(CommonUtils.getLmbMessage(MessageId.PROCESS_IS_NULL));
        }
    }

    /**
     * @param dto
     * @param workOrderNo
     * @param isEndBoard
     * @param calInModule
     * @throws Exception
     */
    private void checkModuleInfo(SMTScanDTO dto, String workOrderNo, boolean isEndBoard, String calInModule)
            throws Exception {
        if (Constant.FLAG_Y.equals(calInModule) && StringUtils.isEmpty(dto.getModuleNo())) {
            throw new Exception(CommonUtils.getLmbMessage(MessageId.MODULENO_IS_NULL) + workOrderNo);
        }
        if (!Constant.FLAG_Y.equals(calInModule) && !isEndBoard) {
            throw new Exception(CommonUtils.getLmbMessage(MessageId.LAST_MODULE_DATA_IS_PASSED_IN));
        }
    }

    /**
     * @param workOrderNo
     * @param lineCode
     * @throws Exception
     */
    private void checkLineCodeAndWorkOrderNo(String workOrderNo, String lineCode) throws Exception {
        if (StringHelper.isEmpty(lineCode)) {
            LOG.info("smtTracingError: no line info:{}", workOrderNo);
            throw new Exception(CommonUtils.getLmbMessage(MessageId.LINE_CODE_IS_NULL));
        }
        if (StringHelper.isEmpty(workOrderNo)) {
            LOG.info("smtTracingError: no workOrderNo:{}", lineCode);
            throw new Exception(CommonUtils.getLmbMessage(MessageId.WORK_ORDER_EMPTY));
        }
    }

    /**
     * @param dto
     * @param psWorkOrder
     * @param smtPcbQty
     * @param list
     * @throws Exception
     */
    public void dealWithScanHistoryList(SMTScanDTO dto, PsWorkOrderDTO psWorkOrder,
                                         int smtPcbQty, List<PsScanHistory> list) throws Exception {

        if (CollectionUtils.isEmpty(list)) {
            for (int i = 0; i < dto.getPassNum(); i++) {
                handleSnInfo(dto, psWorkOrder, Constant.STR_EMPTY, smtPcbQty, smtPcbQty);
            }
        } else {
            for (int i = 0; i < list.size(); i++) {
                PsScanHistory psScanHistory = list.get(i);
                String parentSn = psScanHistory.getParentSn();
                BigDecimal pcbQtyBD = psScanHistory.getPcbQty();
                int pcbQty = smtPcbQty;
                if (pcbQtyBD != null && pcbQtyBD.intValue() > 0) {
                    pcbQty = pcbQtyBD.intValue();
                }
                handleSnInfo(dto, psWorkOrder, parentSn, smtPcbQty, pcbQty);
                // 根据大板条码更新扫描历史表为已追溯
                if (StringUtils.isNotEmpty(parentSn)) {
                    PsScanHistory updatePsScanHistory = new PsScanHistory();
                    updatePsScanHistory.setParentSn(parentSn);
                    updatePsScanHistory.setIsTraceCalculate(Constant.FLAG_Y);
                    updatePsScanHistory.setWorkOrderNo(dto.getWorkOrderNo());
                    psScanHistoryRepository.updateTracingByParentSn(updatePsScanHistory);
                }
            }
            for (int i = list.size(); i < dto.getPassNum(); i++) {
                handleSnInfo(dto, psWorkOrder, Constant.STR_EMPTY, smtPcbQty, smtPcbQty);
            }
        }
    }

    /**
     * @param dto
     * @param traceSource
     * @param calInSmt
     * @param list
     * @return
     * @throws Exception
     */
    public List<PsScanHistory> getScanHistoryListByCondition(SMTScanDTO dto, String traceSource, String calInSmt, List<PsScanHistory> list)
            throws Exception {
        String workOrderNo = dto.getWorkOrderNo();
        String lineCode = dto.getLineCode();
        int passNum = dto.getPassNum();
        String sn = dto.getSn();
        boolean isEndBoard = NumConstant.STR_ONE.equals(dto.getIsEndBoard());
        Date curDate = new Date();
        if (isEndBoard) {
            // 查询扫描历史
            Map<String, Object> map = new HashMap<>();
            map.put("sourceSys", traceSource);
            map.put("orderField", MpConstant.CREATE_DATE);
            map.put("startRow", NumConstant.STR_ONE);
            map.put("endRow", String.valueOf(passNum));
            if (Constant.FLAG_Y.equals(calInSmt)) {
                if (StringUtils.isEmpty(sn)) {
                    throw new Exception(CommonUtils.getLmbMessage(MessageId.SN_IS_NULL));
                }
                map.put("sn", sn);
            } else {
                map.put("lineCode", lineCode);
                map.put("workOrderNo", workOrderNo);
                map.put("isTraceCalculate", Constant.FLAG_N);
            }
            //根据大板条码不重复整理一个新的list
            list = psScanHistoryRepository.getListBySmt(map);
        }
        return list;
    }

    public void handleSnInfo(SMTScanDTO dto, PsWorkOrderDTO psWorkOrder, String parentSn, int smtQty,
                             int pcbQty) throws Exception {
        // 线体、指令、操作员(00000000)、按机台扣数、最后过板、机台
        String lineCode = dto.getLineCode();
        boolean calInModule = Constant.FLAG_Y.equals(dto.getCalInModule());
        boolean isEndBoard = NumConstant.STR_ONE.equals(dto.getIsEndBoard());
        // 数据传来的 moduleNo 实际为 machineNo
        String eqpMachineNo = dto.getModuleNo();
        Date curDate = dto.getCurDate();
        // 指令信息基础数据校验
        checkBasicInfoForHandleSn(psWorkOrder);
        String processCode = psWorkOrder.getProcessGroup();
        if (processCode.indexOf(MpConstant.SPLIT_CHAR) > 0) {
            processCode = processCode.substring(0, processCode.indexOf(MpConstant.SPLIT_CHAR));
        }
        String workStation = processCode + MpConstant.SCAN_HISTORY_REMARK_ONE;
        String cfgHeaderId = psWorkOrder.getCfgHeaderId();
        String workshopCode = psWorkOrder.getWorkshopCode();
        // 根据线体、指令获取一小时内未计算的抛料数据
        List<EmEqpPdcountDTO> emEqpPdcountDTOList =
                getEmEqpPdcount(curDate, dto, calInModule, eqpMachineNo, MpConstant.FALSE);
        // 获取虚拟模组
        String virtualMachineNo = this.getVirtualMachineNo(dto.getFactoryId().toString());
        // 根据线体、指令取得机台在用表中信息,计算扣数后更新PK表中的qty字段(objectId 即pkCode)  机台在用表直接关联pk_code_info,关联字段objectId -- pkCode  qtyFinal = qty - 上料表标准用量 - 未用过的抛料数据
        List<SmtMachineMaterialMouting> mountingList = this.getMountingListInfo(dto, calInModule, eqpMachineNo, isEndBoard, virtualMachineNo);
        // 获得上料表中qty
        List<BSmtBomDetail> bomDetailList = getBomMap(calInModule, eqpMachineNo, cfgHeaderId, isEndBoard, virtualMachineNo);
        Map<String, BigDecimal> bomResultMap = new HashMap<>();
        setBomResultMapInfo(bomDetailList, bomResultMap);
        // 筛选出所有虚拟模组站位
        Set<String> virtualLocationNoSet = this.getVirtualLocationNo(bomDetailList, lineCode);
        List<SmtSnMtlTracingT> tracingTList = new ArrayList<>();
        List<EmEqpPdcountDTO> updateEmList = new ArrayList<>();
        List<PkCodeInfo> updatePkCodeList = new ArrayList<>();
        List<EmEqpInteractiveDTO> updateEqpInteractiveList = new ArrayList<>();
        List<SmtMachineMTLHistoryL> hisLUpdateList = new ArrayList<>();
        for (SmtMachineMaterialMouting mounting : mountingList) {
            String locationStr = mounting.getMachineNo() + "--" + mounting.getLocationNo();
            BigDecimal bomQty = bomResultMap.get(locationStr);
            this.checkMountingInfoForHandleSn(cfgHeaderId, mounting, bomQty);
            int eqpCount = getEqpCount(mounting.getItemCode(), mounting.getMachineNo(), mounting.getLocationNo(), emEqpPdcountDTOList, updateEmList);
            //计算扣减的上料表数量 = 上料表用量/指令拼板数*SPI拼板数
            // 线体标记为设备物料消耗标识不进行扣数
            BigDecimal calBomQty = bomQty.divide(BigDecimal.valueOf(smtQty), 2, BigDecimal.ROUND_FLOOR)
                    .multiply(BigDecimal.valueOf(pcbQty));
            int qtyFinal = mounting.getQty().intValue() - calBomQty.intValue() - eqpCount;
            // 是否虚拟站位
            boolean virtualFlag = virtualLocationNoSet.contains(locationStr);
            // 更新PKCodeInfo
            SMTScanParamDTO smtScanParamDTO = this.getSmtScanParamDTO(dto, parentSn, isEndBoard, curDate, virtualFlag);
            this.setSMTScanParamDTO(tracingTList, updatePkCodeList, mounting, smtScanParamDTO);
            this.setSMTScanParamDTO(psWorkOrder, bomQty, eqpCount, qtyFinal, smtScanParamDTO);
            qtyFinal = dealListByQtyFinal(smtScanParamDTO);
            if (this.needUpdatePkcodeQty(dto.getMarker(), virtualFlag)) {
                PkCodeInfo pkCodeInfo = this.getPkCodeInfo(curDate, mounting, qtyFinal);
                updatePkCodeList.add(pkCodeInfo);
            }
            smtScanParamDTO = this.getSmtScanParamDTO(dto, curDate, processCode, mounting, qtyFinal);
            this.setSMTScanParamDTO(workStation, workshopCode, updateEqpInteractiveList, smtScanParamDTO);
            this.eqpInteractiveInfoProcess(smtScanParamDTO);
        }
        if (calInModule && StringUtils.isNotEmpty(eqpMachineNo) && StringUtils.isNotEmpty(parentSn) && isEndBoard) {
            // 查追溯切换开关
            SysLookupTypesDTO sysLookupType = BasicsettingRemoteService.getSysLookUpValue(BusinessConstant.TRACING_NEW_METHOD_SWITCH_TYPE,
                    BusinessConstant.TRACING_NEW_METHOD_SWITCH_VALUE);
            if (sysLookupType != null && Constant.FLAG_Y.equals(sysLookupType.getLookupMeaning())) {
                SMTScanParamDTO smtScanParamDTO = this.getSmtScanParamDTO(dto, psWorkOrder, parentSn, curDate, tracingTList);
                this.setSMTScanParamDTO(smtQty, pcbQty, updateEmList, hisLUpdateList, smtScanParamDTO);
                this.tracingNotLastNew(eqpMachineNo, smtScanParamDTO);
            } else {
                SMTScanParamDTO smtScanParamDTO = this.getSmtScanParamDTO(dto, curDate, tracingTList);
                this.tracingNotLastPre(eqpMachineNo, virtualMachineNo, psWorkOrder, parentSn, smtScanParamDTO);
            }
        }
        this.updateOrInsertListInfo(tracingTList, updateEmList, updatePkCodeList, updateEqpInteractiveList, hisLUpdateList);
    }

    public void setSMTScanParamDTO(int smtQty, int pcbQty, List<EmEqpPdcountDTO> updateEmList, List<SmtMachineMTLHistoryL> hisLUpdateList, SMTScanParamDTO smtScanParamDTO) {
        smtScanParamDTO.setSmtQty(smtQty);
        smtScanParamDTO.setPcbQty(pcbQty);
        smtScanParamDTO.setHisLUpdateList(hisLUpdateList);
        smtScanParamDTO.setUpdateEmList(updateEmList);
    }

    public SMTScanParamDTO getSmtScanParamDTO(SMTScanDTO dto, PsWorkOrderDTO psWorkOrder, String parentSn, Date curDate, List<SmtSnMtlTracingT> tracingTList) {
        SMTScanParamDTO smtScanParamDTO = new SMTScanParamDTO();
        smtScanParamDTO.setCurDate(curDate);
        smtScanParamDTO.setDto(dto);
        smtScanParamDTO.setTracingTList(tracingTList);
        smtScanParamDTO.setPsWorkOrder(psWorkOrder);
        smtScanParamDTO.setParentSn(parentSn);
        return smtScanParamDTO;
    }

    public SMTScanParamDTO getSmtScanParamDTO(SMTScanDTO dto, Date curDate, List<SmtSnMtlTracingT> tracingTList) {
        SMTScanParamDTO smtScanParamDTO = new SMTScanParamDTO();
        smtScanParamDTO.setCurDate(curDate);
        smtScanParamDTO.setDto(dto);
        smtScanParamDTO.setTracingTList(tracingTList);
        return smtScanParamDTO;
    }

    private PkCodeInfo getPkCodeInfo(Date curDate, SmtMachineMaterialMouting mounting, int qtyFinal) {
        PkCodeInfo pkCodeInfo = new PkCodeInfo();
        pkCodeInfo.setPkCode(mounting.getObjectId());
        pkCodeInfo.setItemQty(new BigDecimal(qtyFinal));
        pkCodeInfo.setLastUpdatedDate(curDate);
        return pkCodeInfo;
    }

    private void setSMTScanParamDTO(PsWorkOrderDTO psWorkOrder, BigDecimal bomQty, int eqpCount, int qtyFinal, SMTScanParamDTO smtScanParamDTO) {
        smtScanParamDTO.setBomQty(bomQty);
        smtScanParamDTO.setEqpCount(eqpCount);
        smtScanParamDTO.setQtyFinal(qtyFinal);
        smtScanParamDTO.setPsWorkOrder(psWorkOrder);
    }

    private void setSMTScanParamDTO(List<SmtSnMtlTracingT> tracingTList, List<PkCodeInfo> updatePkCodeList, SmtMachineMaterialMouting mounting, SMTScanParamDTO smtScanParamDTO) {
        smtScanParamDTO.setTracingTList(tracingTList);
        smtScanParamDTO.setUpdatePkCodeList(updatePkCodeList);
        smtScanParamDTO.setMounting(mounting);
        smtScanParamDTO.setQty(mounting.getQty());
    }

    private SMTScanParamDTO getSmtScanParamDTO(SMTScanDTO dto, String parentSn, boolean isEndBoard, Date curDate, boolean virtualFlag) {
        SMTScanParamDTO smtScanParamDTO = new SMTScanParamDTO();
        smtScanParamDTO.setMarker(dto.getMarker());
        smtScanParamDTO.setVirtualFlag(virtualFlag);
        smtScanParamDTO.setParentSn(parentSn);
        smtScanParamDTO.setCurDate(curDate);
        smtScanParamDTO.setDto(dto);
        smtScanParamDTO.setEndBoard(isEndBoard);
        return smtScanParamDTO;
    }

    private void setSMTScanParamDTO(String workStation, String workshopCode, List<EmEqpInteractiveDTO> updateEqpInteractiveList, SMTScanParamDTO smtScanParamDTO) {
        smtScanParamDTO.setWorkStation(workStation);
        smtScanParamDTO.setWorkshopCode(workshopCode);
        smtScanParamDTO.setUpdateEqpInteractiveList(updateEqpInteractiveList);
    }

    private SMTScanParamDTO getSmtScanParamDTO(SMTScanDTO dto, Date curDate, String processCode, SmtMachineMaterialMouting mounting, int qtyFinal) {
        SMTScanParamDTO smtScanParamDTO = new SMTScanParamDTO();
        smtScanParamDTO.setMounting(mounting);
        smtScanParamDTO.setNextReel(mounting.getNextReelRowid());
        smtScanParamDTO.setQtyFinal(qtyFinal);
        smtScanParamDTO.setCurDate(curDate);
        smtScanParamDTO.setDto(dto);
        smtScanParamDTO.setProcessCode(processCode);
        return smtScanParamDTO;
    }

    /**
     * 筛选得到所有虚拟站位
     *
     * @param bomDetailList
     * @param lineCode
     * @return
     */
    public Set<String> getVirtualLocationNo(List<BSmtBomDetail> bomDetailList, String lineCode) {
        Set<String> set = new HashSet<>();
        List<SmtLocationInfoDTO> locationList = new ArrayList<>();
        for (BSmtBomDetail bSmtBomDetail : bomDetailList) {
            if (Constant.FLAG_Y.equalsIgnoreCase(bSmtBomDetail.getVirtualFlag())) {
                String locationStr = bSmtBomDetail.getMachineNo() + "--" + bSmtBomDetail.getLocationNo();
                set.add(locationStr);
            }
            SmtLocationInfoDTO location = new SmtLocationInfoDTO();
            BeanUtils.copyProperties(bSmtBomDetail, location);
            location.setLineCode(lineCode);
            location.setLocationType(NumConstant.STR_TWO);
            locationList.add(location);
        }

        // 获取托盘类型站位(站位类型 LocationType=2), 处理逻辑同虚拟站位
        List<String> trayLocations = BasicsettingRemoteService.getListByLocationType(locationList);
        if (!CollectionUtils.isEmpty(trayLocations)) {
            set.addAll(trayLocations);
        }
        return set;
    }

    private int getEqpCount(String itemCode, String machineNo, String locationNo,
                            List<EmEqpPdcountDTO> emEqpPdcountDTOList, List<EmEqpPdcountDTO> updateEmList) throws Exception {
        // 获得抛料表中实体
        List<EmEqpPdcountDTO> emDTOAtPosition = new ArrayList<>();
        emDTOAtPosition = dealWithEqpPdCountDtoList(emEqpPdcountDTOList, itemCode, machineNo, locationNo,
                emDTOAtPosition);
        int eqpCount = NumConstant.NUM_ZERO;
        for (EmEqpPdcountDTO item : emDTOAtPosition) {
            eqpCount += item.getThrowsNumber().intValue();
            item.setStatus(Constant.FLAG_Y);
            updateEmList.add(item);
        }
        return eqpCount;
    }

    private void eqpInteractiveInfoProcess(SMTScanParamDTO smtScanParamDTO) throws Exception {
        String nextReel = smtScanParamDTO.getNextReel();
        int qtyFinal = smtScanParamDTO.getQtyFinal();
        List<EmEqpInteractiveDTO> updateEqpInteractiveList = smtScanParamDTO.getUpdateEqpInteractiveList();
        if (StringUtils.isEmpty(nextReel) && qtyFinal <= 0) {
            EmEqpInteractiveDTO interactiveDTO = this.generateEmEqpInteractiveDTO(smtScanParamDTO);
            updateEqpInteractiveList.add(interactiveDTO);
        }
    }

    private List<EmEqpPdcountDTO> getEmEqpPdcount(Date curDate, SMTScanDTO dto, boolean calInModule,
                                                  String eqpMachineNo, boolean isTracing) throws Exception {

        EmEqpPdcountDTO emEqpPdcount = new EmEqpPdcountDTO();
        emEqpPdcount.setLineCode(dto.getLineCode());
        emEqpPdcount.setWorkOrderNo(dto.getWorkOrderNo());
        // 获取当前时间的前一个小时
        Calendar cal = Calendar.getInstance();
        cal.setTime(curDate);
        cal.add(Calendar.HOUR_OF_DAY, -1);
        Date startQueryDate = cal.getTime();
        emEqpPdcount.setStartTimeStr(DateHelper.convertDateToString(startQueryDate, DateUtil.DATE_FORMATE_FULL));
        if (isTracing) {
            emEqpPdcount.setNotEqpModule(eqpMachineNo);
        } else {
            emEqpPdcount.setStatus(Constant.FLAG_N);
            if (calInModule && StringUtils.isNotEmpty(eqpMachineNo)) {
                emEqpPdcount.setEqpModule(eqpMachineNo);
            }
        }

        // 点对点调用服务
        Map<String, Object> paramMapE = new HashMap<String, Object>();
        paramMapE.put("lineCode", emEqpPdcount.getLineCode());
        paramMapE.put("workOrderNo", emEqpPdcount.getWorkOrderNo());
        paramMapE.put("startTimeStr", emEqpPdcount.getStartTimeStr());
        paramMapE.put("notEqpModule", emEqpPdcount.getNotEqpModule());
        paramMapE.put("eqpModule", emEqpPdcount.getEqpModule());
        paramMapE.put("status", emEqpPdcount.getStatus());
        String paramsE = JacksonJsonConverUtil.beanToJson(paramMapE);
        Map<String, String> headerParamsMapE = MESHttpHelper.getHttpRequestHeader();
        String serviceNameE = MicroServiceNameEum.EQPMGMTSYS;
        String versionE = MicroServiceNameEum.VERSION;
        String sendTypeE = MicroServiceNameEum.SENDTYPEGET;
        String getUrlE = "/EmEqp/getEpqPdcountInfo";
        String getresultE = MicroServiceRestUtil.invokeService(serviceNameE, versionE, sendTypeE, getUrlE,
                paramsE, headerParamsMapE);
        JsonNode jsonE = JacksonJsonConverUtil.getMapperInstance().readTree(getresultE);
        String bo = jsonE.get(MpConstant.JSON_BO).toString();
        List<EmEqpPdcountDTO> emEqpPdcountList = JacksonJsonConverUtil.jsonToListBean(bo,
                new TypeReference<ArrayList<EmEqpPdcountDTO>>() {
                });
        return emEqpPdcountList;
    }

    /**
     * @param smtScanParamDTO
     * @return
     * @throws Exception
     */
    private int dealListByQtyFinal(SMTScanParamDTO smtScanParamDTO) throws Exception {
        String parentSn = smtScanParamDTO.getParentSn();
        boolean isEndBoard = smtScanParamDTO.isEndBoard();
        List<SmtSnMtlTracingT> tracingTList= smtScanParamDTO.getTracingTList();
        int qtyFinal = smtScanParamDTO.getQtyFinal();


        if (qtyFinal <= 0) {
            // 如果数量被扣成0
            this.dealWithQtyFinal(smtScanParamDTO);
            qtyFinal = 0;
        } else {
            if (StringUtils.isNotEmpty(parentSn) && isEndBoard) {
                tracingTList.add(this.generateOneSmtSnMtlTracingInfo(MpConstant.TRACE_TYPE_TWO, smtScanParamDTO));
            }
        }
        return qtyFinal;
    }

    /**
     * @param calInModule
     * @param eqpMachineNo
     * @param cfgHeaderId
     * @param isEndBoard
     * @param virtualMachineNo
     * @return
     * @throws Exception
     */
    private List<BSmtBomDetail> getBomMap(boolean calInModule, String eqpMachineNo, String cfgHeaderId, boolean isEndBoard, String virtualMachineNo) throws Exception {
        Map<String, Object> bomMap = new HashMap<>();
        bomMap.put(MpConstant.CFG_HEADERID, cfgHeaderId);
        if (calInModule && StringUtils.isNotEmpty(eqpMachineNo)) {
            if (isEndBoard) {
                // 按模组精确扣数。在最后模组对虚拟站位进行扣数和写追溯
                List<String> machineNoList = new ArrayList<>(2);
                machineNoList.add(eqpMachineNo);
                machineNoList.add(virtualMachineNo);
                bomMap.put("inMachineNo", SqlUtils.convertStrCollectionToSqlType(machineNoList));
            } else {
                bomMap.put("machineNo", eqpMachineNo);
            }
        }
        List<BSmtBomDetail> bomDetailList = bSmtBomDetailService.getList(bomMap, MpConstant.CFG_HEADERID, Constant.DESC);
        if (CollectionUtils.isEmpty(bomDetailList)) {
            if (calInModule && StringUtils.isNotEmpty(eqpMachineNo)) {
                // 最后模组可能没有上料信息。返回空列表防止最后模组没有上料信息时无追溯数据
                if (bomDetailList == null) {
                    bomDetailList = new ArrayList<>();
                }
            } else {
                LOG.info("smtTracingError: 无上料表信息 {}", cfgHeaderId);
                throw new MesBusinessException(MessageId.NO_BOM_DETAIL, RetCode.BUSINESSERROR_MSGID);
            }
        }
        return bomDetailList;
    }

    // 获取虚拟模组
    private String getVirtualMachineNo(String factoryId) {
        String virtualMachineNo = Constant.STR_EMPTY;
        String prefix = Constant.STR_EMPTY;
        // 首先查询redis获取虚拟模组。
        String redisKey = Constant.VIRTUAL_MACHINE_NO + factoryId;
        try {
            virtualMachineNo = redisTemplate.opsForValue().get(redisKey);
            if (StringUtils.isNotBlank(virtualMachineNo)) {
                return virtualMachineNo;
            }
        } catch (Exception e) {
            LOG.error("连接redis异常");
        }

        // redis不存在查询数据字典
        List<SysLookupValuesDTO> values = BasicsettingRemoteService.getLookupValueByTypeCodes(Constant.LOOKUP_6104);
        if (CollectionUtils.isEmpty(values)) {
            prefix = Constant.VIRTUAL_PREFIX;
        } else {
            prefix = StringUtils.defaultIfBlank(values.get(Constant.INT_0).getLookupMeaning(), Constant.VIRTUAL_PREFIX);
        }
        virtualMachineNo = prefix + Constant.V_MACHINE_NO;
        // 存入redis。过期时间10分钟
        try {
            redisTemplate.opsForValue().setIfAbsent(redisKey, virtualMachineNo, NumConstant.NUM_TEN, TimeUnit.MINUTES);
        } catch (Exception e) {
            LOG.error("连接redis异常");
        }

        return virtualMachineNo;
    }

    /**
     * @param dto
     * @param calInModule
     * @param eqpMachineNo
     * @param virtualMachineNo
     * @return
     * @throws Exception
     */
    private List<SmtMachineMaterialMouting> getMountingListInfo(SMTScanDTO dto,
                                                                boolean calInModule, String eqpMachineNo, boolean isEndBoard, String virtualMachineNo) throws Exception {
        Map<String, Object> moutingMap = new HashMap<>();
        moutingMap.put("workOrder", dto.getWorkOrderNo());
        moutingMap.put("lineCode", dto.getLineCode());
//        需求：追溯扣数获取机台在用数据时因当前模组可能已经切换至下一个指令，故不加enable_flag=Y条件,查全部
        moutingMap.put("enabledFlag", Constant.FLAG_ALL);
        if (calInModule && StringUtils.isNotEmpty(eqpMachineNo)) {
            if (isEndBoard) {
                // 按模组精确扣数。在最后模组对虚拟站位进行扣数和写追溯
                List<String> machineNoList = new ArrayList<>(2);
                machineNoList.add(eqpMachineNo);
                machineNoList.add(virtualMachineNo);
                moutingMap.put("inMachineNo", SqlUtils.convertStrCollectionToSqlType(machineNoList));
            } else {
                moutingMap.put("machineNo", eqpMachineNo);
            }
        }
        List<SmtMachineMaterialMouting> mountingList =
                smtMachineMaterialMoutingService.selectMoutingWithPkCodeInfo(moutingMap);
        if (CollectionUtils.isEmpty(mountingList)) {
            if (calInModule && StringUtils.isNotEmpty(eqpMachineNo)) {
                // 最后模组可能没有上料信息。返回空列表防止最后模组没有上料信息时无追溯数据
                if (mountingList == null) {
                    mountingList = new ArrayList<>();
                }
            } else {
                LOG.info("smtTracingError: 无机台在用物料表中对应信息 {} {}", dto.getLineCode(), dto.getWorkOrderNo());
                throw new MesBusinessException(MessageId.NO_MOUNTING_DETAIL, RetCode.BUSINESSERROR_MSGID);
            }
        }

        return mountingList;
    }

    /**
     * @throws Exception
     */
    private void dealWithQtyFinal(SMTScanParamDTO smtScanParamDTO) throws Exception {
        String marker = smtScanParamDTO.getMarker();
        boolean virtualFlag = smtScanParamDTO.isVirtualFlag();
        SMTScanDTO dto = smtScanParamDTO.getDto();
        Date curDate = smtScanParamDTO.getCurDate();
        String parentSn = smtScanParamDTO.getParentSn();
        boolean isEndBoard = smtScanParamDTO.isEndBoard();
        List<SmtSnMtlTracingT> tracingTList= smtScanParamDTO.getTracingTList();
        List<PkCodeInfo> updatePkCodeList = smtScanParamDTO.getUpdatePkCodeList();
        SmtMachineMaterialMouting mounting = smtScanParamDTO.getMounting();
        BigDecimal qty = smtScanParamDTO.getQty();
        BigDecimal bomQty = smtScanParamDTO.getBomQty();
        int eqpCount = smtScanParamDTO.getEqpCount();
        int qtyFinal = smtScanParamDTO.getQtyFinal();
        PsWorkOrderDTO psWorkOrder = smtScanParamDTO.getPsWorkOrder();
        if (StringUtils.isNotEmpty(parentSn) && isEndBoard) {
            tracingTList.add(this.generateOneSmtSnMtlTracingInfo(MpConstant.TRACE_TYPE_ONE, smtScanParamDTO));
        }

        String reelId = mounting.getObjectId();
        String nextReelRowId = mounting.getNextReelRowid();
        // 存在续料盘且(是虚拟站位或线体未配置reelId数量同步)
        if (StringUtils.isNotEmpty(nextReelRowId) && needUpdatePkcodeQty(marker, virtualFlag)) {
            PkCodeInfo pkCodeInfoNew = new PkCodeInfo();
            pkCodeInfoNew.setPkCode(nextReelRowId);
            pkCodeInfoNew = pkCodeInfoService.selectPkCodeInfoById(pkCodeInfoNew);
            if (null == pkCodeInfoNew) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,MessageId.PK_CODE_INFO_NOT_FOUND,
                        new Object[]{nextReelRowId});
            }
            if (null == pkCodeInfoNew.getItemQty()) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,
                        MessageId.ITEMQTY_IS_NULL, new Object[]{nextReelRowId});
            }
            int qtyNew = pkCodeInfoNew.getItemQty().intValue() + qtyFinal;
            pkCodeInfoNew.setItemQty(new BigDecimal(qtyNew));
            updatePkCodeList.add(pkCodeInfoNew);
            pkCodeInfoNew.setLastUpdatedDate(curDate);

            // 接料，并删除备料
            SmtMachineMaterialPrepare smtPrepare = new SmtMachineMaterialPrepare();
            smtPrepare.setObjectId(nextReelRowId);
            SmtMachineMaterialMouting mountingNew = smtMachineMaterialMoutingService.updateSmtMachineMaterialMoutingFromPrepare(mounting, pkCodeInfoNew, curDate, reelId);
            smtMachineMaterialPrepareRepository.deleteSmtMachineMaterialPrepareById(smtPrepare);
            if (StringUtils.isNotEmpty(parentSn) && isEndBoard) {
                tracingTList.add(this.generateOneSmtSnMtlTracingInfo(MpConstant.TRACE_TYPE_THREE, smtScanParamDTO));
            }
        }
    }

    /**
     * 校验是否需要更新pkCode数量
     *
     * @param marker      线体reelID数量同步标识
     * @param virtualFlag 是否虚拟站位
     * @return
     */
    private boolean needUpdatePkcodeQty(String marker, boolean virtualFlag) {
        return virtualFlag || !Constant.FLAG_Y.equals(marker);
    }

    /**
     * @param emEqpPdcountDTOList
     * @param itemCode
     * @param machineNo
     * @param locationNo
     * @param emDTOAtPosition
     * @return
     */
    private List<EmEqpPdcountDTO> dealWithEqpPdCountDtoList(List<EmEqpPdcountDTO> emEqpPdcountDTOList,
                                                            String itemCode, String machineNo, String locationNo, List<EmEqpPdcountDTO> emDTOAtPosition) {

        if (!CollectionUtils.isEmpty(emEqpPdcountDTOList)) {
            if (StringUtils.isEmpty(machineNo)) {
                emDTOAtPosition = emEqpPdcountDTOList.stream().filter(tempEmEqpPdcount ->
                                locationNo.equals(tempEmEqpPdcount.getFullStationPosition())
                                        && itemCode.equalsIgnoreCase(tempEmEqpPdcount.getMaterialCode()))
                        .collect(Collectors.toList());
            } else {
                emDTOAtPosition = emEqpPdcountDTOList.stream().filter(tempEmEqpPdcount ->
                                (machineNo + locationNo).equals(tempEmEqpPdcount.getFullStationPosition())
                                        && itemCode.equalsIgnoreCase(tempEmEqpPdcount.getMaterialCode()))
                        .collect(Collectors.toList());
            }
        }
        return emDTOAtPosition;
    }

    /**
     * @param cfgHeaderId
     * @param mounting
     * @param bomQty
     * @throws Exception
     */
    private void checkMountingInfoForHandleSn(String cfgHeaderId, SmtMachineMaterialMouting mounting, BigDecimal bomQty) throws Exception {
        String mountingId = mounting.getMachineMaterialMoutingId();
        String reelId = mounting.getObjectId();
        String locationNo = mounting.getLocationNo();
        BigDecimal qty = mounting.getQty();
        if (StringUtils.isEmpty(reelId)) {
            LOG.info("smtTracingError: reelId为空 mountingId: {}", mountingId);
            throw new MesBusinessException(MessageId.NO_REEL_ID_IN_MOUNTING, RetCode.BUSINESSERROR_MSGID);
        }
        if (StringUtils.isEmpty(locationNo)) {
            LOG.info("smtTracingError: locationNo为空 mountingId: {}", mountingId);
            throw new MesBusinessException(MessageId.NO_LOCATION_NO_IN_HIS, RetCode.BUSINESSERROR_MSGID);
        }
        if (null == qty) {
            throw new Exception(CommonUtils.getLmbMessage(MessageId.QTY_IS_NULL_OF_REELID));
        }
        if (null == bomQty) {
            throw new Exception(CommonUtils.getLmbMessage(MessageId.BOMQTY_IS_NULL, cfgHeaderId, locationNo));
        }
    }

    /**
     * @param tracingTList
     * @param updateEmList
     * @param updatePkCodeList
     * @param updateEqpInteractiveList
     * @throws Exception
     */
    private void updateOrInsertListInfo(List<SmtSnMtlTracingT> tracingTList, List<EmEqpPdcountDTO> updateEmList,
                                        List<PkCodeInfo> updatePkCodeList, List<EmEqpInteractiveDTO> updateEqpInteractiveList, List<SmtMachineMTLHistoryL> hisLUpdateList)
            throws Exception {

        if (!CollectionUtils.isEmpty(tracingTList)) {
            List<List<SmtSnMtlTracingT>> tracingTSplitList = CommonUtils.splitList(tracingTList, Constant.BATCH_SIZE);
            for (List<SmtSnMtlTracingT> insertList : tracingTSplitList) {
                smtSnMtlTracingTService.insertBatch(insertList);
            }
        }

        if (!CollectionUtils.isEmpty(updateEmList)) {
            List<List<EmEqpPdcountDTO>> updateEmSplitList = CommonUtils.splitList(updateEmList, Constant.BATCH_SIZE);
            for (List<EmEqpPdcountDTO> updateList : updateEmSplitList) {
                EqpmgmtsRemoteService.batchUpdateEqpPdcountStatus(updateList);
            }
        }

        if (!CollectionUtils.isEmpty(updatePkCodeList)) {
            List<List<PkCodeInfo>> updatePkCodeSplitList = CommonUtils.splitList(updatePkCodeList, Constant.BATCH_SIZE);
            for (List<PkCodeInfo> updateList : updatePkCodeSplitList) {
                pkCodeInfoService.updatePkCodeInfoByIdSelectWithTime(updateList);
            }
        }

        updateHisAndInteractive(updateEqpInteractiveList, hisLUpdateList);
    }

    private void updateHisAndInteractive(List<EmEqpInteractiveDTO> updateEqpInteractiveList, List<SmtMachineMTLHistoryL> hisLUpdateList) throws Exception {
        if (!CollectionUtils.isEmpty(hisLUpdateList)) {
            List<List<SmtMachineMTLHistoryL>> hisLUpdateSplitList = CommonUtils.splitList(hisLUpdateList, Constant.BATCH_SIZE);
            for (List<SmtMachineMTLHistoryL> updateList : hisLUpdateSplitList) {
                smtMachineMTLHistoryLService.batchUpdateWithTime(updateList);
            }
        }

        if (!CollectionUtils.isEmpty(updateEqpInteractiveList)) {
            List<List<EmEqpInteractiveDTO>> updateEqpInterSplitList = CommonUtils.splitList(updateEqpInteractiveList, Constant.BATCH_SIZE);
            for (List<EmEqpInteractiveDTO> insertList : updateEqpInterSplitList) {
                RetCode retCode = ObtainRemoteServiceDataUtil.insertEmEqpInteractiveBatch(insertList);
                if (!RetCode.SUCCESS_CODE.equals(retCode.getCode())) {
                    throw new Exception(retCode.getMsg());
                }
            }
        }
    }

    /**
     * @param psWorkOrder
     * @throws Exception
     */
    private void checkBasicInfoForHandleSn(PsWorkOrderDTO psWorkOrder) throws Exception {

        //上料表头表ID、物料代码、生产批次、车间
        String cfgHeaderId = psWorkOrder.getCfgHeaderId();
        String itemCode = psWorkOrder.getItemNo();
        String productBatchCode = psWorkOrder.getSourceTask();
        String workshopCode = psWorkOrder.getWorkshopCode();
        // 工序
        String processCode = psWorkOrder.getProcessGroup();

        if (StringUtils.isEmpty(cfgHeaderId)) {
            throw new Exception(CommonUtils.getLmbMessage(MessageId.SMT_TRACINGT_CFGHEADERID_IS_NULL));
        }
        if (StringUtils.isEmpty(itemCode)) {
            throw new Exception(CommonUtils.getLmbMessage(MessageId.ITEM_CODE_EMPTY));
        }
        if (StringUtils.isEmpty(productBatchCode)) {
            throw new Exception(CommonUtils.getLmbMessage(MessageId.PRODPLANID_ID_NULL));
        }
        if (StringUtils.isEmpty(workshopCode)) {
            throw new Exception(CommonUtils.getLmbMessage(MessageId.WORKSHOP_IS_NULL));
        }
        if (StringUtils.isEmpty(processCode)) {
            throw new Exception(CommonUtils.getLmbMessage(MessageId.PROCESS_IS_NULL));
        }
    }

    private void tracingNotLastPre(String eqpMachineNo, String virtualMachineNo, PsWorkOrderDTO psWorkOrderDTO, String parentSn,SMTScanParamDTO smtScanParamDTO) throws Exception {
        SMTScanDTO dto = smtScanParamDTO.getDto();
        Date curDate = smtScanParamDTO.getCurDate();
        List<SmtSnMtlTracingT> tracingTList = smtScanParamDTO.getTracingTList();

        String lineCode = dto.getLineCode();
        String workOrderNo = dto.getWorkOrderNo();
        String cfgHeaderId = psWorkOrderDTO.getCfgHeaderId();

        Map<String, Object> moutingMap = new HashMap<>();
        moutingMap.put("workOrder", workOrderNo);
        moutingMap.put("lineCode", lineCode);
        moutingMap.put("enabledFlag", Constant.FLAG_Y);
        List<String> notInMachineNoList = new ArrayList<>(2);
        notInMachineNoList.add(eqpMachineNo);
        notInMachineNoList.add(virtualMachineNo);
        // 虚拟模组站位已在最后模组扣数+写追溯。无需再次写追溯
        moutingMap.put("notInMachineNo", SqlUtils.convertStrCollectionToSqlType(notInMachineNoList));
        List<SmtMachineMaterialMouting> mountingListNotLast =
                smtMachineMaterialMoutingService.selectMoutingWithPkCodeInfo(moutingMap);

        // 获得上料表中qty
        Map<String, Object> bomMap = new HashMap<>();
        bomMap.put(MpConstant.CFG_HEADERID, cfgHeaderId);
        // 虚拟模组站位已在最后模组扣数+写追溯。无需再次写追溯
        bomMap.put("notInMachineNo", SqlUtils.convertStrCollectionToSqlType(notInMachineNoList));
        List<BSmtBomDetail> bomDetailListNotLast = bSmtBomDetailService.getList(bomMap, MpConstant.CFG_HEADERID, Constant.DESC);
        Map<String, BigDecimal> bomResultMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(bomDetailListNotLast)) {
            setBomResultMapInfo(bomDetailListNotLast, bomResultMap);
        }
        for (SmtMachineMaterialMouting mounting : mountingListNotLast) {

            String reelId = mounting.getObjectId();
            String machineNo = mounting.getMachineNo();
            String locationNo = mounting.getLocationNo();

            if (StringUtils.isEmpty(reelId)) {
                LOG.info("smtTracingError: reelId为空 mountingId: {}", mounting.getMachineMaterialMoutingId());
                throw new MesBusinessException(MessageId.NO_REEL_ID_IN_MOUNTING, RetCode.BUSINESSERROR_MSGID);
            }
            if (StringUtils.isEmpty(locationNo)) {
                LOG.info("smtTracingError: locationNo为空 mountingId: {}", mounting.getMachineMaterialMoutingId());
                throw new MesBusinessException(MessageId.NO_LOCATION_NO_IN_HIS, RetCode.BUSINESSERROR_MSGID);
            }

            BigDecimal qty = mounting.getQty();
            BigDecimal bomQty = bomResultMap.get(machineNo + "--" + locationNo);
            if (null == bomQty) {
                LOG.info("smtTracingError: 上料表bomQty为空 cfgHeaderId: {} locationNo: {}", cfgHeaderId, locationNo);
                throw new MesBusinessException(MessageId.NO_BOM_DETAIL, RetCode.BUSINESSERROR_MSGID);
            }
            SMTScanParamDTO scanParamDTO = this.getSmtScanParamDTO(parentSn, dto, curDate, qty, bomQty);
            scanParamDTO.setMounting(mounting);
            scanParamDTO.setPsWorkOrder(psWorkOrderDTO);
            tracingTList.add(generateOneSmtSnMtlTracingInfo(NumConstant.NUM_TWO, scanParamDTO));
        }
    }

    private SMTScanParamDTO getSmtScanParamDTO(String parentSn, SMTScanDTO dto, Date curDate, BigDecimal qty, BigDecimal bomQty) {
        SMTScanParamDTO scanParamDTO = new SMTScanParamDTO();
        scanParamDTO.setCurDate(curDate);
        scanParamDTO.setDto(dto);
        scanParamDTO.setEqpCount(NumConstant.NUM_ZERO);
        scanParamDTO.setQty(qty);
        scanParamDTO.setParentSn(parentSn);
        scanParamDTO.setBomQty(bomQty);
        return scanParamDTO;
    }

    private void tracingNotLastNew(String eqpMachineNo, SMTScanParamDTO smtScanParamDTO) throws Exception {
        PsWorkOrderDTO psWorkOrderDTO = smtScanParamDTO.getPsWorkOrder();
        SMTScanDTO dto = smtScanParamDTO.getDto();
        Date curDate = smtScanParamDTO.getCurDate();
        String lineCode = dto.getLineCode();
        String workOrderNo = dto.getWorkOrderNo();
        String productBatchCode = psWorkOrderDTO.getSourceTask();
        String productCode = psWorkOrderDTO.getItemNo();
        String cfgHeaderId = psWorkOrderDTO.getCfgHeaderId();
        Date actualStartDate = psWorkOrderDTO.getActualStartDate();

        // 查询转机上料历史记录
        Map hisMap = new HashMap<>();
        hisMap.put("workOrder", workOrderNo);
        hisMap.put("lineCode", lineCode);
        hisMap.put("enabledOnly", Constant.FLAG_Y);
        hisMap.put("notMachine", eqpMachineNo);
        hisMap.put("operateMsgNull", Constant.FLAG_Y);
        hisMap.put("attr1", Constant.FLAG_N);
        hisMap.put("mountType", NumConstant.STR_ONE);
        List<SmtMachineMTLHistoryL> transferMTLHistoryLList
                = smtMachineMTLHistoryLService.getList(hisMap, BusinessConstant.ORDER_FIELD_CREATE_DATE,
                BusinessConstant.ORDER_ASC, 0L, 0L);
        Map<String, List<SmtMachineMTLHistoryL>> hisResultMap = new HashMap();
        if (CollectionUtils.isEmpty(transferMTLHistoryLList)) {
            return;
        }
        setTransferHisMap(transferMTLHistoryLList, hisResultMap);

        // 查询开工后的接料扫描记录
        hisMap.put("mountType", NumConstant.STR_TWO);
        hisMap.put("actualStartDate", actualStartDate);
        List<SmtMachineMTLHistoryL> receiveMTLHistoryLList
                = smtMachineMTLHistoryLService.getList(hisMap, BusinessConstant.ORDER_FIELD_CREATE_DATE,
                BusinessConstant.ORDER_ASC, 0L, 0L);
        if (!CollectionUtils.isEmpty(receiveMTLHistoryLList)) {
            setReceiveHisMap(receiveMTLHistoryLList, hisResultMap);
        }

        // 根据线体、指令获取一小时内未计算追溯的抛料数据
        List<EmEqpPdcountDTO> emEqpPdcountDTOList =
                getEmEqpPdcount(curDate, dto, true, eqpMachineNo, true);

        // 获得上料表中qty
        Map bomMap = new HashMap();
        bomMap.put(MpConstant.CFG_HEADERID, cfgHeaderId);
        bomMap.put("notMachine", eqpMachineNo);
        List<BSmtBomDetail> bomDetailListNotLast = bSmtBomDetailService.getList(bomMap, MpConstant.CFG_HEADERID, Constant.DESC);
        Map<String, BigDecimal> bomResultMap = new HashMap();
        if (CollectionUtils.isEmpty(bomDetailListNotLast)) {
            return;
        }
        setBomResultMapInfo(bomDetailListNotLast, bomResultMap);

        for (String fullLocationNo : bomResultMap.keySet()) {

            BigDecimal bomQty = bomResultMap.get(fullLocationNo);
            if (null == bomQty) {
                LOG.info("smtTracingError: 上料表bomQty为空 cfgHeaderId: {} locationNo: {}", cfgHeaderId, fullLocationNo);
                throw new MesBusinessException(MessageId.NO_BOM_DETAIL, RetCode.BUSINESSERROR_MSGID);
            }
            smtScanParamDTO.setProductBatchCode(productBatchCode);
            smtScanParamDTO.setProductCode(productCode);
            this.trcingByHisForBom(smtScanParamDTO, hisResultMap, emEqpPdcountDTOList, bomResultMap, fullLocationNo);
        }
    }

    private void trcingByHisForBom(SMTScanParamDTO smtScanParamDTO, Map<String, List<SmtMachineMTLHistoryL>> hisResultMap,
                                   List<EmEqpPdcountDTO> emEqpPdcountDTOList, Map<String, BigDecimal> bomResultMap, String fullLocationNo) throws Exception {
        Date curDate = smtScanParamDTO.getCurDate();
        int smtQty = smtScanParamDTO.getSmtQty();
        int pcbQty = smtScanParamDTO.getPcbQty();
        List<SmtSnMtlTracingT> tracingTList = smtScanParamDTO.getTracingTList();
        List<SmtMachineMTLHistoryL> hisLUpdateList = smtScanParamDTO.getHisLUpdateList();
        List<EmEqpPdcountDTO> updateEmList = smtScanParamDTO.getUpdateEmList();
        SmtMachineMTLHistoryL preHis;
        SmtMachineMTLHistoryL nextHis = null;
        List<SmtMachineMTLHistoryL> mapHisList = hisResultMap.get(fullLocationNo);
        if (CollectionUtils.isEmpty(mapHisList)) {
            return;
        }
        List<SmtMachineMTLHistoryL> mapHisCalList = mapHisList.stream().filter(his -> (null != his.getTracingQty()
                && his.getTracingQty().compareTo(BigDecimal.ZERO) > 0)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(mapHisCalList)) {
            preHis = mapHisList.get(mapHisList.size() - 1);
            preHis.setTracingQty(BigDecimal.ZERO);
        } else {
            mapHisCalList.sort(Comparator.comparing(SmtMachineMTLHistoryL::getCreateDate));
            preHis = mapHisCalList.get(NumConstant.NUM_ZERO);
            if (mapHisCalList.size() >= NumConstant.NUM_TWO) {
                nextHis = mapHisCalList.get(NumConstant.NUM_ZERO);
            }
        }

        String reelId = preHis.getObjectId();
        String machineNo = preHis.getMachineNo();
        String locationNo = preHis.getLocationNo();
        String itemCode = preHis.getItemCode();
        checkReelIdAndBomQty(preHis, reelId, locationNo);

        BigDecimal qty = preHis.getTracingQty();
        BigDecimal bomQty = bomResultMap.get(fullLocationNo);
        if (bomQty == null) {
            return;
        }

        // 获得抛料表中实体
        List<EmEqpPdcountDTO> emDTOAtPosition = new ArrayList<>();
        emDTOAtPosition = dealWithEqpPdCountDtoList(emEqpPdcountDTOList, itemCode, machineNo, locationNo,
                emDTOAtPosition);
        int eqpCount = NumConstant.NUM_ZERO;
        for (EmEqpPdcountDTO item : emDTOAtPosition) {
            eqpCount += item.getTracingQty().intValue();
            item.setTracingQty(BigDecimal.ZERO);
            updateEmList.add(item);
        }

        // 计算扣减的上料表数量 = 上料表用量/指令拼板数*SPI拼板数
        BigDecimal calBomQty = bomQty.divide(BigDecimal.valueOf(smtQty), 2, BigDecimal.ROUND_FLOOR)
                .multiply(BigDecimal.valueOf(pcbQty));
        int qtyFinal = qty.intValue() - calBomQty.intValue() - eqpCount;
        if (qtyFinal > 0) {
            smtScanParamDTO.setSmtMachineMTLHistoryL(preHis);
            tracingTList.add(this.generateOneSmtSnMtlTracingInfoByHis(smtScanParamDTO, qty, eqpCount, NumConstant.NUM_TWO, bomQty));
            preHis.setTracingQty(BigDecimal.valueOf(qtyFinal));
            preHis.setLastUpdatedDate(curDate);
            hisLUpdateList.add(preHis);
        } else {
            smtScanParamDTO.setSmtMachineMTLHistoryL(preHis);
            tracingTList.add(this.generateOneSmtSnMtlTracingInfoByHis(smtScanParamDTO,qty, eqpCount, NumConstant.NUM_ONE, bomQty));
            preHis.setTracingQty(BigDecimal.ZERO);
            preHis.setLastUpdatedDate(curDate);
            hisLUpdateList.add(preHis);
            if (nextHis != null) {
                smtScanParamDTO.setSmtMachineMTLHistoryL(nextHis);
                tracingTList.add(this.generateOneSmtSnMtlTracingInfoByHis(smtScanParamDTO,qty, NumConstant.NUM_ZERO, NumConstant.NUM_THREE, bomQty));
                BigDecimal remainedQty = nextHis.getTracingQty().add(BigDecimal.valueOf(qtyFinal));
                nextHis.setTracingQty(BigDecimal.ZERO.compareTo(remainedQty) > 0 ? BigDecimal.ZERO : remainedQty);
                preHis.setLastUpdatedDate(curDate);
                hisLUpdateList.add(nextHis);
            }
        }
    }

    private void checkReelIdAndBomQty(SmtMachineMTLHistoryL preHis, String reelId, String locationNo) throws Exception {

        if (StringUtils.isEmpty(reelId)) {
            LOG.info("smtTracingError: reelId为空 lineId: {}", preHis.getLineId());
            throw new MesBusinessException(MessageId.NO_REEL_ID_IN_MOUNTING, RetCode.BUSINESSERROR_MSGID);
        }
        if (StringUtils.isEmpty(locationNo)) {
            LOG.info("smtTracingError: locationNo为空 lineId: {}", preHis.getLineId());
            throw new MesBusinessException(MessageId.NO_LOCATION_NO_IN_HIS, RetCode.BUSINESSERROR_MSGID);
        }
    }

    /**
     * 低位预警计算
     *
     * @param dto
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public RetCode calSMTMaterialQty(SMTScanDTO dto) throws Exception {

        String workOrderNo = dto.getWorkOrderNo();
        String lineCode = dto.getLineCode();
        // 统一时间
        Date curDate = new Date();
        dto.setCurDate(curDate);
        LOG.info("calSMTMaterialQty params -- workOrderNo:{}, lineCode:{}", workOrderNo, lineCode);

        checkLineCodeAndWorkOrderNo(workOrderNo, lineCode);

        PsWorkOrderDTO psWorkOrder = ObtainRemoteServiceDataUtil.getBasicWorkerOrderInfo(workOrderNo, lineCode);
        this.calReturn(dto, psWorkOrder, curDate);

        RetCode ret = new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID);
        ret.setMsg(CommonUtils.getLmbMessage(MessageId.SMT_INVESTMENT_SCAN_SUCCESS));
        return ret;
    }

    public void calReturn(SMTScanDTO dto, PsWorkOrderDTO psWorkOrder, Date curDate) throws Exception {
        // 指令、线体、实体ID、工厂ID、操作员（默认00000000）
        String workOrderNo = dto.getWorkOrderNo();
        String lineCode = dto.getLineCode();

        //上料表头表ID、物料代码、生产批次、车间
        String cfgHeaderId = psWorkOrder.getCfgHeaderId();
        String workshopCode = psWorkOrder.getWorkshopCode();
        LOG.info(" workOrderId: {} ", psWorkOrder.getWorkOrderId());
        // 工序
        String processCode = psWorkOrder.getProcessGroup();
        checkBasicInfoForHandleSn(psWorkOrder);
        if (processCode.indexOf(MpConstant.SPLIT_CHAR) > 0) {
            processCode = processCode.substring(0, processCode.indexOf(MpConstant.SPLIT_CHAR));
        }
        // 工站
        String workStation = processCode + BusinessConstant.RECEIVE_SCAN;

        // 获得上料表中qty
        Map<String, Object> bomMap = new HashMap<>();
        bomMap.put(MpConstant.CFG_HEADERID, cfgHeaderId);
        List<BSmtBomDetail> bomDetailList = bSmtBomDetailService.getList(bomMap, MpConstant.CFG_HEADERID, Constant.DESC);
        if (CollectionUtils.isEmpty(bomDetailList)) {
            LOG.info("smtTracingError: 无上料表信息 {}", cfgHeaderId);
            throw new MesBusinessException(MessageId.NO_BOM_DETAIL, RetCode.BUSINESSERROR_MSGID);
        }
        Map<String, BigDecimal> bomResultMap = new HashMap<>();
        setBomResultMapInfo(bomDetailList, bomResultMap);

        // 根据线体、指令取得在制表中的reelId,并更新PK表中的qty字段：objectId--reelId
        // qty=qty-上料表标准用量
        Map<String, Object> moutingMap = new HashMap<>();
        moutingMap.put("workOrder", workOrderNo);
        moutingMap.put("lineCode", lineCode);
        moutingMap.put("enabledFlag", Constant.FLAG_Y);
        List<SmtMachineMaterialMouting> mountingList =
                smtMachineMaterialMoutingService.selectMoutingWithPkCodeInfo(moutingMap);
        if (CollectionUtils.isEmpty(mountingList)) {
            LOG.info("smtTracingError: 无机台在用物料表中对应信息 {} {}", lineCode, workOrderNo);
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.NO_MOUNTING_DETAIL);
        }
        for (SmtMachineMaterialMouting mounting : mountingList) {
            String reelId = mounting.getObjectId();
            String machineNo = mounting.getMachineNo();
            String locationNo = mounting.getLocationNo();
            String nextReel = mounting.getNextReelRowid();
            BigDecimal bomQty = bomResultMap.get(machineNo + "--" + locationNo);
            if (StringUtils.isEmpty(reelId)) {
                LOG.info("smtTracingError: reelId为空 mountingId: {}", mounting.getMachineMaterialMoutingId());
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,MessageId.NO_REEL_ID_IN_MOUNTING);
            }
            if (StringUtils.isEmpty(locationNo)) {
                LOG.info("smtTracingError: locationNo为空 mountingId: {}", mounting.getMachineMaterialMoutingId());
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,MessageId.NO_LOCATION_NO_IN_HIS);
            }

            BigDecimal qty = mounting.getQty();
            if (null == qty) {
                throw new Exception(CommonUtils.getLmbMessage(MessageId.PK_CODE_INFO_QTY_IS_NULL));
            }


            if (null == bomQty) {
                LOG.info("smtTracingError: 上料表bomQty为空 cfgHeaderId: {} locationNo: {}", cfgHeaderId, locationNo);
                imesLogService.log(mounting, "smtTracingBomQtyError");
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,MessageId.NO_BOM_DETAIL);
            }

            int qtyFinal = qty.intValue();
            if (StringUtils.isEmpty(nextReel) && qtyFinal <= 0) {
                return;
            }
        }

        this.generateEqpInteractiveStartInfo(dto, workshopCode, processCode, workStation);
    }

    /**
     * @param bomDetailList
     * @param bomResultMap
     */
    private void setBomResultMapInfo(List<BSmtBomDetail> bomDetailList, Map<String, BigDecimal> bomResultMap) {
        for (BSmtBomDetail bSmtBomDetail : bomDetailList) {
            String locationStr = bSmtBomDetail.getMachineNo() + "--" + bSmtBomDetail.getLocationNo();
            bomResultMap.put(locationStr, bSmtBomDetail.getQty());
        }
    }

    /**
     * @param smtMachineMTLHistoryLList
     * @param hisResultMap
     */
    private void setTransferHisMap(List<SmtMachineMTLHistoryL> smtMachineMTLHistoryLList,
                                   Map<String, List<SmtMachineMTLHistoryL>> hisResultMap) {
        for (SmtMachineMTLHistoryL smtMachineMTLHistoryL : smtMachineMTLHistoryLList) {
            String locationStr = smtMachineMTLHistoryL.getMachineNo() + "--" + smtMachineMTLHistoryL.getLocationNo();
            List<SmtMachineMTLHistoryL> mapList = new ArrayList<>();
            mapList.add(smtMachineMTLHistoryL);
            hisResultMap.put(locationStr, mapList);
        }
    }

    /**
     * @param smtMachineMTLHistoryLList
     * @param hisResultMap
     */
    private void setReceiveHisMap(List<SmtMachineMTLHistoryL> smtMachineMTLHistoryLList,
                                  Map<String, List<SmtMachineMTLHistoryL>> hisResultMap) {
        for (SmtMachineMTLHistoryL smtMachineMTLHistoryL : smtMachineMTLHistoryLList) {
            String locationStr = smtMachineMTLHistoryL.getMachineNo() + "--" + smtMachineMTLHistoryL.getLocationNo();
            if (hisResultMap.containsKey(locationStr) && hisResultMap.get(locationStr) != null) {
                hisResultMap.get(locationStr).add(smtMachineMTLHistoryL);
            }
        }
    }

    /**
     * 生成机台在用物料低位预警开机信息
     *
     * @param dto
     * @param workshopCode
     * @param processCode
     * @param workStation
     */
    public void generateEqpInteractiveStartInfo(SMTScanDTO dto, String workshopCode, String processCode,
                                                String workStation) throws Exception {
        String workOrderNo = dto.getWorkOrderNo();
        String lineCode = dto.getLineCode();
        BigDecimal entityId = dto.getEntityId();
        BigDecimal factoryId = dto.getFactoryId();
        String empNo = dto.getCreateBy();
        Date curDate = dto.getCurDate();

        EmEqpInteractiveDTO interactiveDTO = new EmEqpInteractiveDTO();
        interactiveDTO.setWorkshopCode(workshopCode);
        interactiveDTO.setWorkOrderNo(workOrderNo);
        interactiveDTO.setProcessCode(processCode);
        interactiveDTO.setLineCode(lineCode);
        interactiveDTO.setWorkStation(workStation);
        // 消息类型
        interactiveDTO.setMsgType(MpConstant.EM_INTERIVE_MSGTYPE_TWO);
        // 发送方
        interactiveDTO.setSendProgram(MpConstant.EM_INTERIVE_SEND_PROGRAM_LOW);
        // 指令 停机0， 开机1
        interactiveDTO.setCommander(MpConstant.EM_INTERIVE_COMMAND_ONE);
        interactiveDTO.setCreateDate(curDate);
        interactiveDTO.setUpdatedDate(curDate);
        interactiveDTO.setCreateBy(empNo);
        interactiveDTO.setEntityId(entityId);
        interactiveDTO.setFactoryId(factoryId);
        interactiveDTO.setEnabledFlag(Constant.FLAG_Y);
        interactiveDTO.setUpdatedBy(empNo);
        interactiveDTO.setRemark(MpConstant.REMARK_LOW);

        RetCode retCode = ObtainRemoteServiceDataUtil.insertEmEqpInteractive(interactiveDTO);
        if (!RetCode.SUCCESS_CODE.equals(retCode.getCode())) {
            throw new Exception(retCode.getMsg());
        }
    }


    /**
     * 设备交互信息处理（包括停机、预警）
     *
     * @param smtScanParamDTO
     * @return
     * @throws Exception
     */
    private EmEqpInteractiveDTO generateEmEqpInteractiveDTO(SMTScanParamDTO smtScanParamDTO) throws Exception {
        SMTScanDTO dto = smtScanParamDTO.getDto();
        Date curDate = smtScanParamDTO.getCurDate();
        String processCode = smtScanParamDTO.getProcessCode();
        String workStation = smtScanParamDTO.getWorkStation();
        String workshopCode = smtScanParamDTO.getWorkshopCode();
        SmtMachineMaterialMouting mounting = smtScanParamDTO.getMounting();
        EmEqpInteractiveDTO interactiveDTO = new EmEqpInteractiveDTO();
        interactiveDTO.setLineCode(dto.getLineCode());
        interactiveDTO.setWorkOrderNo(dto.getWorkOrderNo());
        interactiveDTO.setFactoryId(dto.getFactoryId());
        interactiveDTO.setEntityId(dto.getEntityId());
        interactiveDTO.setCreateBy(dto.getCreateBy());
        interactiveDTO.setUpdatedBy(dto.getCreateBy());
        interactiveDTO.setWorkshopCode(workshopCode);
        interactiveDTO.setProcessCode(processCode);
        interactiveDTO.setWorkStation(workStation);
        interactiveDTO.setLocationNo(mounting.getLocationNo());
        interactiveDTO.setMsgType(MpConstant.EM_INTERIVE_MSGTYPE_TWO);
        interactiveDTO.setSendProgram(MpConstant.EM_INTERIVE_SEND_PROGRAM_LOW);
        interactiveDTO.setRemark(mounting.getMachineNo() + mounting.getLocationNo() + " " + mounting.getItemCode());
        interactiveDTO.setOrgId(mounting.getOrgId());
        interactiveDTO.setCreateDate(curDate);
        interactiveDTO.setUpdatedDate(curDate);
        interactiveDTO.setEnabledFlag(Constant.FLAG_Y);
        // 指令 停机0， 开机1 告警 2
        interactiveDTO.setCommander(MpConstant.EM_INTERIVE_COMMAND_ZERO);
        interactiveDTO.setRemark(interactiveDTO.getRemark() + " " + MpConstant.REMARK_SMT_SCAN_MATERIAL_DOWN);
        return interactiveDTO;
    }

    /**
     * 根据上料历史生成物料追溯信息
     *
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    public SmtSnMtlTracingT generateOneSmtSnMtlTracingInfoByHis(SMTScanParamDTO smtScanParamDTO, BigDecimal qty, int eqpCount, int type,
                                                                BigDecimal bomQty) throws Exception {
        SmtMachineMTLHistoryL historyL = smtScanParamDTO.getSmtMachineMTLHistoryL();
        String productBatchCode = smtScanParamDTO.getProductBatchCode();
        String productCode = smtScanParamDTO.getProductCode();
        String sn = smtScanParamDTO.getParentSn();
        SMTScanDTO dto = smtScanParamDTO.getDto();
        Date curDate = smtScanParamDTO.getCurDate();

        SmtSnMtlTracingT tracing = new SmtSnMtlTracingT();
        tracing.setRecordId(GenerateUUIDUtil.generateUUIDBySuffix(DateUtil.getCurYearQuarter()));
        tracing.setLineCode(historyL.getLineCode());
        tracing.setWorkOrder(historyL.getWorkOrder());
        // 生产批次
        tracing.setProductBatchCode(productBatchCode);
        tracing.setProductCode(productCode);
        tracing.setSn(sn);
        tracing.setLocationNo(historyL.getLocationNo());
        tracing.setItemCode(historyL.getItemCode());
        tracing.setFeederNo(historyL.getFeederNo());
        tracing.setObjectId(historyL.getObjectId());
        tracing.setSnScanDate(curDate);
        tracing.setSnScanBy(dto.getCreateBy());
        //物料批次
        tracing.setSourceBatchCode(historyL.getSourceBatchCode());
        //上料时间
        tracing.setMaterialDate(historyL.getCreateDate());
        tracing.setMaterialBy(historyL.getCreateUser());
        tracing.setCfgHeaderId(historyL.getCfgHeaderId());
        tracing.setCreateDate(curDate);
        tracing.setEnabledFlag(CommonConst.ENABLE_FLAG_Y);
        tracing.setHandleType(CommonConst.HANDLE_TYPE_SCAN);
        tracing.setEntityId(dto.getEntityId().longValue());
        tracing.setAttribute3(bomQty.toString());
        tracing.setFactoryId(dto.getFactoryId().longValue());
        if (type == MpConstant.TRACE_TYPE_ONE) {
            // qtyFinal<=0
            tracing.setAttribute2(new Integer(eqpCount).toString());
            tracing.setAttribute1(qty.toString());
        } else if (type == MpConstant.TRACE_TYPE_TWO) {
            // qtyFinal>0
            tracing.setAttribute2(new Integer(eqpCount).toString());
            tracing.setAttribute1(bomQty.toString());
        } else if (type == MpConstant.TRACE_TYPE_THREE) {
            //qtyFinal<=0,nextReel不为空
            tracing.setAttribute1(new Integer(bomQty.intValue() - qty.intValue()).toString());
        } else {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,MessageId.INCORRECT_TRACEBACK_TYPE_PARAMETER);
        }
        return tracing;
    }

    /**
     * 生成物料追溯信息
     *
     * @param type
     * @param smtScanParamDTO
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    public SmtSnMtlTracingT generateOneSmtSnMtlTracingInfo(int type, SMTScanParamDTO smtScanParamDTO) throws Exception {
        SMTScanDTO dto = smtScanParamDTO.getDto();
        Date curDate = smtScanParamDTO.getCurDate();
        SmtMachineMaterialMouting mounting = smtScanParamDTO.getMounting();
        BigDecimal qty = smtScanParamDTO.getQty();
        BigDecimal bomQty = smtScanParamDTO.getBomQty();
        int eqpCount = smtScanParamDTO.getEqpCount();
        PsWorkOrderDTO psWorkOrder = smtScanParamDTO.getPsWorkOrder();

        SmtSnMtlTracingT tracing = new SmtSnMtlTracingT();
        tracing.setRecordId(GenerateUUIDUtil.generateUUIDBySuffix(DateUtil.getCurYearQuarter()));
        tracing.setLineCode(dto.getLineCode());
        tracing.setWorkOrder(dto.getWorkOrderNo());
        // 生产批次
        tracing.setProductBatchCode(psWorkOrder.getSourceTask());
        tracing.setProductCode(psWorkOrder.getItemNo());
        tracing.setSn(smtScanParamDTO.getParentSn());
        tracing.setSnScanDate(curDate);
        tracing.setSnScanBy(dto.getCreateBy());
        tracing.setObjectId(mounting.getObjectId());
        tracing.setItemCode(mounting.getItemCode());
        tracing.setLocationNo(mounting.getLocationNo());
        tracing.setFeederNo(mounting.getFeederNo());
        // 物料批次 220
        tracing.setSourceBatchCode(mounting.getSourceBatchCode());
        // 上料时间
        tracing.setMaterialDate(mounting.getCreateDate());
        tracing.setMaterialBy(mounting.getCreateUser());
        tracing.setCfgHeaderId(mounting.getCfgHeaderId());
        tracing.setCreateDate(curDate);
        tracing.setEnabledFlag(CommonConst.ENABLE_FLAG_Y);
        tracing.setHandleType(CommonConst.HANDLE_TYPE_SCAN);
        tracing.setFactoryId(dto.getFactoryId().longValue());
        tracing.setEntityId(dto.getEntityId().longValue());
        tracing.setAttribute3(bomQty.toString());
        switch (type){
            case  MpConstant.TRACE_TYPE_ONE:
                tracing.setAttribute1(qty.toString());
                tracing.setAttribute2(String.valueOf(eqpCount));
                break;
            case MpConstant.TRACE_TYPE_TWO:
                tracing.setAttribute1(bomQty.toString());
                tracing.setAttribute2(String.valueOf(eqpCount));
                break;
            case MpConstant.TRACE_TYPE_THREE:
                tracing.setAttribute1(String.valueOf(bomQty.intValue() - qty.intValue()));
                break;
            default:
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.INCORRECT_TRACEBACK_TYPE_PARAMETER);
        }
        return tracing;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertPsScanHistoryBatch(List<PsScanHistory> lists, int num) {
        int count = NumConstant.NUM_ZERO;
        List<List<PsScanHistory>> listOfList = CommonUtils.splitList(lists, num);
        if (!CollectionUtils.isEmpty(listOfList)) {
            for (List<PsScanHistory> list : listOfList) {
                count += psScanHistoryRepository.insertPsScanHistoryBatch(list);
            }
        }
        return count;
    }

    @Override
    public List<PsScanHistoryCollect> collectPsScanHistoryBySmt(PsScanHistory record) {
        return psScanHistoryRepository.collectPsScanHistoryBySmt(record);
    }

    @Override
    public Long collectFinishContByLineCodes(PsScanHistoryDTO record) {
        //计算真实开始时间范围为早上八点到次日八点
        //如果在0点到8点，为昨天八点到今天八点
        //如果在8点到24点，为今天八点到次日八点
        Calendar newDate = Calendar.getInstance();
        SimpleDateFormat df1 = new SimpleDateFormat(MpConstant.DATE_FORMAT_YYYYMMDD_ONE);
        newDate.setTime(new Date());
        int hh = newDate.get(Calendar.HOUR_OF_DAY);
        String nowDay = df1.format(newDate.getTime()) + MpConstant.EIGHT_CLOCK;
        if (hh <= MpConstant.BEFORE_DAYS && hh >= 0) {
            newDate.add(Calendar.DATE, -1);
            record.setBeginTime(df1.format(newDate.getTime()) + MpConstant.EIGHT_CLOCK);
            record.setEndTime(nowDay);
        } else {
            newDate.add(Calendar.DATE, 1);
            record.setBeginTime(nowDay);
            record.setEndTime(df1.format(newDate.getTime()) + MpConstant.EIGHT_CLOCK);
        }
        Long cnt = psScanHistoryRepository.collectFinishContByLineCodes(record);
        return cnt;
    }

    @Override
    public List<PsScanHistoryCollect> finishContByLineCodes(PsScanHistoryDTO record) {
        //计算真实开始时间范围为早上八点到次日八点
        //如果在0点到8点，为昨天八点到今天八点
        //如果在8点到24点，为今天八点到次日八点
        SimpleDateFormat df1 = new SimpleDateFormat(MpConstant.DATE_FORMAT_YYYYMMDD_ONE);
        Calendar newDate = Calendar.getInstance();
        newDate.setTime(new Date());
        int hh = newDate.get(Calendar.HOUR_OF_DAY);
        String nowDay = df1.format(newDate.getTime()) + MpConstant.EIGHT_CLOCK;
        if (hh <= MpConstant.BEFORE_DAYS && hh >= 0) {
            record.setEndTime(nowDay);
            newDate.add(Calendar.DATE, -1);
            record.setBeginTime(df1.format(newDate.getTime()) + MpConstant.EIGHT_CLOCK);
        } else {
            record.setBeginTime(nowDay);
            newDate.add(Calendar.DATE, 1);
            record.setEndTime(df1.format(newDate.getTime()) + MpConstant.EIGHT_CLOCK);
        }
        return psScanHistoryRepository.finishContByLineCodes(record);
    }

    /**
     * 各线体近7日产出
     * 实际达成:根据时间(近7日)范围（8：00到次日8:00）工位等于（SMT下线扫描、DIP下线扫描），MAC对应线体计数（count）汇总
     * 近7日： 当前日期前7天，当前日期算1天
     *
     * @param record 条件
     * @return 统计记过
     * <AUTHOR>
     */
    @Override
    public List<PsScanHistory> collect7DayFinishContByLineCodes(PsScanHistoryDTO record) {

        Map<String, String> map = new HashMap<String, String>();
        Map<String, String> workStationMap = new HashMap<String, String>();
        map.put("xType", MpConstant.PROCESS_X_TYPE_S);
        map.put("factoryId", record.getFactoryId().toString());
        map.put("processName", record.getWorkStation());
        try {
            List<BSProcessDTO> bsProcess = ObtainRemoteServiceDataUtil.getProcessName(map);
            if (null != bsProcess && !CollectionUtils.isEmpty(bsProcess)) {
                for (BSProcessDTO pro : bsProcess) {
                    workStationMap.put(pro.getProcessName(), pro.getProcessCode());
                }
            }
        } catch (Exception e) {
            LOG.error("点对点调用getProcessName失败：", e);
        }
        if (record.getWorkStation() != null) {
            record.setWorkStation(workStationMap.get(record.getWorkStation()));
        }

        List<PsScanHistory> list = psScanHistoryRepository.collect7DayFinishContByLineCodes(record);
        if (!CollectionUtils.isEmpty(list)) {
            setLineCodeForList(record, list);
        }
        return list;
    }

    /**
     * @param record
     * @param list
     */
    private void setLineCodeForList(PsScanHistoryDTO record, List<PsScanHistory> list) {
        Map<String, String> mapGetLine = new HashMap<String, String>();
        try {
            mapGetLine = ObtainRemoteServiceDataUtil.getLineAll(record.getFactoryId());
        } catch (Exception e1) {
            LOG.error("点对点调用失败   getLineAll ：" + e1);
        }
        for (PsScanHistory dto : list) {
            if (null != mapGetLine && mapGetLine.size() > 0) {
                if (StringUtils.isNotBlank(dto.getLineCode())) {
                    dto.setLineCode(mapGetLine.get(dto.getLineCode()));
                }
            }
        }
    }

    /**
     * 扫描设置时绑定批次信息
     */
    @Override
    public RetCode bingTaskInfo(PmScanConditionDTO dto, HttpServletRequest request) throws Exception {
        RetCode retCode = new RetCode(RetCode.BUSINESSERROR_CODE, RetCode.BUSINESSERROR_MSGID);
        //String ip = NetUtils.getIpAddr(request);
        String mac = NetUtils.getIpAddr(request);
        // String macR = NetUtils.getMACAddress(mac);

        if (StringUtils.isEmpty(mac)) {
            retCode.setMsg(CommonUtils.getLmbMessage(MessageId.MAC_ADDRESS_NOT_OBTAINED));
            return retCode;
        }
        // 数据字典读取mac地址
        Map<String, String> map = new HashMap<String, String>();
        map.put("lookupType", Constant.MAC_ADDRESS);
        // Map<String, String> headerParamsMap = new HashMap<String, String>();

        String params = JacksonJsonConverUtil.beanToJson(map);
        Map<String, String> headerParamsMap = MESHttpHelper.getHttpRequestHeader();
        // 点对点调用服务
        String serviceName = MicroServiceNameEum.BASICSETTING;
        String version = MicroServiceNameEum.VERSION;
        String sendType = MicroServiceNameEum.SENDTYPEGET;
        String getUrl = "/BS/sysLookupTypesAll";

        String getresult = MicroServiceRestUtil.invokeService(serviceName, version, sendType, getUrl, params,
                headerParamsMap);
        JsonNode json = JacksonJsonConverUtil.getMapperInstance().readTree(getresult);

        String bo = json.get(MpConstant.JSON_BO).toString();
        List<SysLookupTypesDTO> types = (List<SysLookupTypesDTO>) JacksonJsonConverUtil.jsonToListBean(bo,
                new TypeReference<List<SysLookupTypesDTO>>() {
                });
        boolean macCheck = false;
        for (SysLookupTypesDTO macInfo : types) {
            if (mac.equals(macInfo.getDescriptionChinV())) {
                macCheck = true;
            }
        }
        if (macCheck) {
            String prodPlanId = "";
            if (!StringUtils.isEmpty(dto.getWorkOrderNo())) {
                prodPlanId = dto.getWorkOrderNo().substring(0, 7);
            }
            // 绑定任务信息
            WipBindTaskInfo selectInfo = new WipBindTaskInfo();
            selectInfo.setBindMac(mac);
            WipBindTaskInfo queryInfo = psScanHistoryRepository.selectBindInfoByMac(selectInfo);
            int count = NumConstant.NUM_ZERO;
            WipBindTaskInfo bindInfo = new WipBindTaskInfo();
            bindInfo.setBindId(java.util.UUID.randomUUID().toString());
            bindInfo.setBindTask(prodPlanId);
            bindInfo.setBindLine(dto.getLineCode());
            bindInfo.setBindMac(mac);
            bindInfo.setWorkOrderNo(dto.getWorkOrderNo());
            bindInfo.setItemCode(dto.getItemCode());
            bindInfo.setProcessCode(dto.getCurrProcessCode());
            bindInfo.setWorkStation(dto.getWorkStation());
            bindInfo.setCreateBy(dto.getCreateBy());
            bindInfo.setLastUpdatedBy(dto.getLastUpdatedBy());
            bindInfo.setFactoryId(dto.getFactoryId());
            bindInfo.setEntityId(dto.getEntityId());

            if (null == queryInfo) {
                // 没有该mac地址对应的信息，则新增
                count = psScanHistoryRepository.insertWipBindTaskInfoSelective(bindInfo);
            } else {
                // 更新该mac地址信息
                count = psScanHistoryRepository.updateWipBindTaskInfoSelective(bindInfo);
            }
            if (count != 1) {
                retCode.setMsg(CommonUtils.getLmbMessage(MessageId.FAILED_TO_INSERT_OR_UPDATE_BINDING_INFORMATION));
                return retCode;
            }
        }
        retCode = new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID);
        retCode.setMsg(MpConstant.RESULT_TYPE_OK);
        return retCode;
    }

    /**
     * 获取创建人名字存到dto的createByName字段
     *
     * @param listDTO
     * @throws IOException
     * @throws RouteException
     * @throws JsonProcessingException
     * @throws Exception
     */
    private void setCreateByName(List<PsScanHistoryDTO> listDTO) throws JsonProcessingException, RouteException, IOException {
        if (CollectionUtils.isEmpty(listDTO)) {
            return;
        }
        List<List<PsScanHistoryDTO>> listOfList = CommonUtils.splitList(listDTO, Constant.BATCH_SIZE);
        List<BsPubHrvOrgId> bsPubHrvOrgIdListAll = new ArrayList<BsPubHrvOrgId>();
        getBsPubHrvOrgIdListAllInfo(listOfList, bsPubHrvOrgIdListAll);
        if (CollectionUtils.isEmpty(bsPubHrvOrgIdListAll)) {
            return;
        }
        List<BsPubHrvOrgId> bsPubHrvOrgIdListFilter = null;
        for (PsScanHistoryDTO psScanHistoryDTO : listDTO) {
            if (StringUtils.isBlank(psScanHistoryDTO.getCreateBy())) {
                continue;
            }
            bsPubHrvOrgIdListFilter = bsPubHrvOrgIdListAll.stream()
                    .filter(item -> (psScanHistoryDTO.getCreateBy().equals(item.getUserId()) || psScanHistoryDTO.getCreateBy().equals(item.getUserFullId())))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(bsPubHrvOrgIdListFilter)) {
                continue;
            }
            psScanHistoryDTO.setCreateByName(bsPubHrvOrgIdListFilter.get(NumConstant.NUM_ZERO).getUserName());
        }
    }

    /**
     * @param listOfList
     * @param bsPubHrvOrgIdListAll
     * @throws RouteException
     * @throws JsonProcessingException
     * @throws IOException
     */
    private void getBsPubHrvOrgIdListAllInfo(List<List<PsScanHistoryDTO>> listOfList,
                                             List<BsPubHrvOrgId> bsPubHrvOrgIdListAll) throws RouteException, JsonProcessingException, IOException {
        for (List<PsScanHistoryDTO> list : listOfList) {
            StringBuffer inUserId = new StringBuffer();
            for (int i = 0; i < list.size(); i++) {
                PsScanHistoryDTO scanHistory = list.get(i);
                if (StringHelper.isNotEmpty(scanHistory.getCreateBy()) && !inUserId.toString().contains(scanHistory.getCreateBy())) {
                    inUserId.append("'").append(scanHistory.getCreateBy()).append("'");
                    if (i + 1 < list.size()) {
                        inUserId.append(",");
                    }
                }
            }
            if (StringHelper.isNotEmpty(inUserId.toString())) {
                // 调用点对点获取人员信息
                addBsPubHrvOrgIdInfo(bsPubHrvOrgIdListAll, inUserId);
            }

        }
    }

    /**
     * @param bsPubHrvOrgIdListAll
     * @param inUserId
     * @throws RouteException
     * @throws JsonProcessingException
     * @throws IOException
     */
    private void addBsPubHrvOrgIdInfo(List<BsPubHrvOrgId> bsPubHrvOrgIdListAll, StringBuffer inUserId)
            throws RouteException, JsonProcessingException, IOException {
        List<BsPubHrvOrgId> tempList = hrmUserInfoService.getBsPubHrvOrgIdInfo(inUserId.toString());
        if (null != tempList && !CollectionUtils.isEmpty(tempList)) {
            bsPubHrvOrgIdListAll.addAll(tempList);
        }
    }

    /**
     * .
     * 获取实际产能
     *
     * @param record 订单编号集合
     * @return 实际产能36
     */
    @Override
    public List<PsScanHistory> collectFinishContByWorkOrderNos(PsScanHistoryDTO record) {
        return psScanHistoryRepository.collectFinishContByWorkOrders(record);
    }

    /**
     * .
     * 批量统计 各个订单的实际产出 - 分小时汇总 - 12小时内
     *
     * @param record 订单编号集合
     * @return 实际产能
     */
    @Override
    public List<PsScanHistory> collectFinishContByWorkOrderNoTime(PsScanHistoryDTO record) {
        return psScanHistoryRepository.collectFinishContByWorkOrderNoTime(record);
    }

    /**
     * 物料可用时间计算定时任务
     *
     * @param dto      传参：lineCode线体、workOrderNo指令、factoryId工厂ID（lineCode/factoryId 必须传）
     * @param lineCode 线体
     * @throws Exception
     */
    @Override
    public void availableTimeCalJob(SMTScanDTO dto, String lineCode) throws Exception {

        if (BusinessConstant.ALL.equals(lineCode)) {
            CFLine cfLine = new CFLine();
            cfLine.setFactoryId(new Integer(dto.getFactoryId().toString()));
            cfLine.setProcessSection(Constant.LINE_TYPE);
            List<CFLine> smtLineList = ObtainRemoteServiceDataUtil.getLine(cfLine);
            if (!CollectionUtils.isEmpty(smtLineList)) {
                for (CFLine line : smtLineList) {
                    dto.setLineCode(line.getLineCode());
                    try {
                        availableTimeCal(dto);
                    } catch (Exception e) {
                        LOG.info("{}物料可用时间计算失败", line.getLineName());
                    }
                }
            }
        } else {
            dto.setLineCode(lineCode);
            availableTimeCal(dto);
        }
    }

    /**
     * 物料可用时间计算通用接口
     *
     * @param dto 传参：lineCode线体、workOrderNo指令、factoryId工厂ID（lineCode/factoryId 必须传）
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    public void availableTimeCal(SMTScanDTO dto) throws Exception {

        // lineCode/factoryId 必须传
        String lineCode = dto.getLineCode();
        BigDecimal factoryId = dto.getFactoryId();
        if (StringUtils.isEmpty(lineCode)) {
            throw new BusiException(RetCode.BUSINESSERROR_CODE, MessageId.LINE_CODE_IS_NULL);
        }
        if (factoryId == null) {
            throw new BusiException(RetCode.BUSINESSERROR_CODE, MessageId.FACTORY_ID_IS_NULL);
        }
        Date curDate = new Date();
        calculateAvailableTime(dto, curDate);
    }


    /**
     * 根据大条码取得其已扫描的数量
     */

    @Override
    public int getCountByParentSn(String parentSn) {
        return psScanHistoryRepository.countByPsn(parentSn);
    }

    /**
     * 大板条码扫描后更新 scanHistory
     *
     * @param list
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addOrUpdateParentSn(List<PsScanHistory> list) throws Exception {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        PsScanHistory queryPsScanHistory = list.get(NumConstant.NUM_ZERO);
        PsWorkOrderBasic workOrderBasic =
                PlanscheduleRemoteService.findWorkOrder(queryPsScanHistory.getWorkOrderNo());
        if (null == workOrderBasic) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.WORKORDER_NOT_FIND);
        }
        List<PsScanHistory> addList = new ArrayList<PsScanHistory>();
        List<PsScanHistory> updatList = new ArrayList<PsScanHistory>();
        for (PsScanHistory psScanHistory : list) {
            psScanHistory.setRouteId(workOrderBasic.getRouteId());
            psScanHistory.setItemNo(workOrderBasic.getItemNo());
            psScanHistory.setWorkshopCode(workOrderBasic.getWorkshopCode());
            psScanHistory.setLineCode(workOrderBasic.getLineCode());
            psScanHistory.setFactoryId(workOrderBasic.getFactoryId());
            if (StringHelper.isEmpty(psScanHistory.getSourceSys())) {
                psScanHistory.setSourceSys(SPI);
            }
            if (StringHelper.isEmpty(psScanHistory.getSmtScanId())) {
                psScanHistory.setSmtScanId(UUID.randomUUID().toString());
                addList.add(psScanHistory);
            } else {
                updatList.add(psScanHistory);
            }
        }
        addParentSn(addList);
        updateParentSn(updatList);
    }

    /**
     * 更新大板条码
     *
     * @param updatList
     */
    private void updateParentSn(List<PsScanHistory> updatList) {
        if (!CollectionUtils.isEmpty(updatList)) {
            List<List<PsScanHistory>> listOfupdates =
                    CommonUtils.splitList(updatList, Constant.SPLITSIZE_HUNDRED);
            for (List<PsScanHistory> listOfupdate : listOfupdates) {
                psScanHistoryRepository.updatePsScanHistoryBatch(listOfupdate);
            }
        }
    }

    /**
     * 新增大板条码
     *
     * @param addList
     * @throws Exception
     */
    private void addParentSn(List<PsScanHistory> addList) throws Exception {
        if ((null != addList) && (!CollectionUtils.isEmpty(addList))) {
            List<List<PsScanHistory>> listOfAdds = CommonUtils.splitList(addList, Constant.SPLITSIZE_HUNDRED);
            for (List<PsScanHistory> listOfAdd : listOfAdds) {
                psScanHistoryRepository.insertPsScanHistoryBatch(listOfAdd);
            }
        }
    }

    /**
     * 物料可用时间计算通用接口
     *
     * @param dto     传参：lineCode线体、workOrderNo指令、factoryId工厂ID（lineCode/factoryId 必须传）
     * @param curDate 统一时间，生成或修改的数据会按照统一时间更新
     * @throws Exception
     */
    private void calculateAvailableTime(SMTScanDTO dto, Date curDate) throws Exception {
        BigDecimal factoryId = dto.getFactoryId();
        String lineCode = dto.getLineCode();
        // 指令编号，如果为空的话，取workOrderOnline表开工的指令
        String workOrderNo = dto.getWorkOrderNo();
        PsWorkOrderDTO psWorkOrder = getOnlineWorkOrder(lineCode, workOrderNo);
        workOrderNo = psWorkOrder.getWorkOrderNo();
        dto.setWorkOrderNo(workOrderNo);
        //上料表头表ID、物料代码、生产批次、车间
        String itemCode = psWorkOrder.getItemNo();
        String cfgHeaderId = psWorkOrder.getCfgHeaderId();
        String workshopCode = psWorkOrder.getWorkshopCode();
        // 子工序、工艺段
        String craftSection = psWorkOrder.getCraftSection();
        String processCode = psWorkOrder.getProcessGroup();
        this.validatePsWorkOrder(cfgHeaderId, itemCode, processCode);
        if (processCode.indexOf(MpConstant.SPLIT_CHAR) > 0) {
            processCode = processCode.substring(0, processCode.indexOf(MpConstant.SPLIT_CHAR));
        }
        // 工站
        String workStation = processCode + BusinessConstant.RECEIVE_SCAN;

        // 指令拼板数，获取不到默认1
        BigDecimal pcbQtyBD = psWorkOrder.getPcbQty();
        int pcbQty = 1;
        if (pcbQtyBD != null && pcbQtyBD.intValue() > 0) {
            pcbQty = pcbQtyBD.intValue();
        }
        // 获得上料表中qty
        List<BSmtBomDetail> bomDetailList = this.getbSmtBomDetails(cfgHeaderId);
        Map<String, BigDecimal> bomResultMap = new HashMap<>();
        this.setBomResultMapInfo(bomDetailList, bomResultMap);

        // 根据线体、指令取得在制表中的reelId
        // 可用时间=剩余数量/标准用量（根据上料表，条件是模组+站位+物料得到）/UPH（产能）/指令的拼板数
        List<SmtMachineMaterialMouting> mountingList = this.getMachineMaterialMoutings(lineCode, workOrderNo);
        // UPH
        BigDecimal uphBd = getUPH(lineCode, itemCode, processCode, factoryId);
        List<SmtItemAvailableTime> insertList = new ArrayList<>();
        for (SmtMachineMaterialMouting mounting : mountingList) {
            String reelId = mounting.getObjectId();
            String moutingId = mounting.getMachineMaterialMoutingId();
            String locationNo = mounting.getLocationNo();
            String machineNo = mounting.getMachineNo();
            String nextReel = mounting.getNextReelRowid();
            BigDecimal qty = mounting.getQty();
            validateMouting(reelId, locationNo, qty, moutingId);
            if (StringUtils.isNotBlank(nextReel)) {
                continue;
            }
            BigDecimal bomQty = bomResultMap.get(machineNo + "--" + locationNo);
            if (null == bomQty) {
                throw new MesBusinessException(MessageId.NO_BOM_DETAIL, RetCode.BUSINESSERROR_MSGID);
            }
            int qtyFinal = qty.intValue();
            // 计算可用时间
            BigDecimal availableTime = calAvailableTime(uphBd, bomQty, qtyFinal, pcbQty);
            // 生成插入数据
            SMTScanParamDTO scanParamDTO = new SMTScanParamDTO();
            scanParamDTO.setDto(dto);
            scanParamDTO.setPcbQty(pcbQty);
            scanParamDTO.setMounting(mounting);
            scanParamDTO.setBomQty(bomQty);
            scanParamDTO.setProcessCode(processCode);
            scanParamDTO.setWorkStation(workStation);
            scanParamDTO.setWorkshopCode(workshopCode);
            scanParamDTO.setCurDate(curDate);
            SmtItemAvailableTime smtItemAvailableTime = this.generateAvailableTimedData(uphBd, scanParamDTO, availableTime, cfgHeaderId,craftSection);
            insertList.add(smtItemAvailableTime);
        }
        if (CollectionUtils.isEmpty(insertList)) {
            return;
        }
        List<List<SmtItemAvailableTime>> listOfList = CommonUtils.splitList(insertList, Constant.SPLITSIZE_HUNDRED);
        for (List<SmtItemAvailableTime> list : listOfList) {
            smtItemAvailableTimeService.batchInsertSmtItemAvailableTime(list);
        }
    }

    private List<BSmtBomDetail> getbSmtBomDetails(String cfgHeaderId) {
        Map<String, Object> bomMap = new HashMap<>();
        bomMap.put(MpConstant.CFG_HEADERID, cfgHeaderId);
        List<BSmtBomDetail> bomDetailList = bSmtBomDetailService.getList(bomMap, MpConstant.CFG_HEADERID, Constant.DESC);
        if (CollectionUtils.isEmpty(bomDetailList)) {
            throw new MesBusinessException(MessageId.NO_BOM_DETAIL, RetCode.BUSINESSERROR_MSGID);
        }
        return bomDetailList;
    }

    private List<SmtMachineMaterialMouting> getMachineMaterialMoutings(String lineCode, String workOrderNo) {
        Map<String, Object> moutingMap = new HashMap<>();
        moutingMap.put("lineCode", lineCode);
        moutingMap.put("enabledFlag", Constant.FLAG_Y);
        moutingMap.put("workOrder", workOrderNo);
        List<SmtMachineMaterialMouting> mountingList =
                smtMachineMaterialMoutingService.selectMoutingWithPkCodeInfo(moutingMap);
        if (CollectionUtils.isEmpty(mountingList)) {
            throw new MesBusinessException(MessageId.NO_MOUNTING_DETAIL, RetCode.BUSINESSERROR_MSGID);
        }
        return mountingList;
    }

    /**
     * 获得UPH
     *
     * @param lineCode    线体
     * @param itemCode    料单代码
     * @param processCode 子工序
     * @param factoryId   工厂ID
     * @return UPH
     * @throws Exception
     */
    public BigDecimal getUPH(String lineCode, String itemCode, String processCode, BigDecimal factoryId) throws Exception {

        List<BManufactureCapacityDTO> capacityList = ObtainRemoteServiceDataUtil.getBManufactureCapacityInfo(lineCode, itemCode,
                processCode, factoryId.toString());
        BigDecimal uph = new BigDecimal("1");
        if (!CollectionUtils.isEmpty(capacityList)) {
            BManufactureCapacityDTO capacityDTO = capacityList.get(NumConstant.NUM_ZERO);
            if (StringUtils.isNotEmpty(capacityDTO.getUph())) {
                uph = new BigDecimal(capacityDTO.getUph()); // 小时
            }
        }
        if (uph.compareTo(BigDecimal.ZERO) <= 0) {
            uph = BigDecimal.valueOf(1);
        }
        return uph;
    }

    /**
     * 获取开工的指令信息
     * @param lineCode
     * @param workOrderNo
     * @return
     * @throws Exception
     */
    private PsWorkOrderDTO getOnlineWorkOrder(String lineCode, String workOrderNo) throws Exception {
        if (StringUtils.isEmpty(workOrderNo)) {
            // 根据线体查找开工的工单
            WorkorderOnline online = new WorkorderOnline();
            online.setLineCode(lineCode);
            List<WorkorderOnline> onlineList = workorderOnlineService.getWorkorderOnline(online);
            if (CollectionUtils.isEmpty(onlineList)) {
                throw new BusiException(RetCode.BUSINESSERROR_CODE,MessageId.NO_STARTED_WORK_ORDER);
            }
            workOrderNo = onlineList.get(NumConstant.NUM_ZERO).getWorkOrder();
        }
        if (StringUtils.isEmpty(workOrderNo)) {
            throw new BusiException(RetCode.BUSINESSERROR_CODE, MessageId.WORK_ORDER_NO_IS_NULL);
        }
        return ObtainRemoteServiceDataUtil.getBasicWorkerOrderInfo(workOrderNo, lineCode);
    }

    /**
     * mouting表的数据校验
     *
     * @param objectId
     * @param locationNo
     * @param itemQty
     * @param moutingId
     * @throws Exception
     */
    private void validateMouting(String objectId, String locationNo, BigDecimal itemQty, String moutingId) throws Exception {

        if (StringUtils.isEmpty(objectId)) {
            throw new MesBusinessException(MessageId.NO_REEL_ID_IN_MOUNTING, RetCode.BUSINESSERROR_MSGID);
        }
        if (StringUtils.isEmpty(locationNo)) {
            throw new MesBusinessException(MessageId.NO_LOCATION_NO_IN_HIS, RetCode.BUSINESSERROR_MSGID);
        }
        if (null == itemQty) {
            throw new Exception(CommonUtils.getLmbMessage(MessageId.PK_CODE_INFO_QTY_IS_NULL));
        }
    }


    /**
     * 校验workOrder数据
     *
     * @param cfgHeaderId
     * @param itemCode
     * @param processCode
     * @throws Exception
     */
    private void validatePsWorkOrder(String cfgHeaderId, String itemCode, String processCode) throws Exception {

        if (StringUtils.isEmpty(cfgHeaderId)) {
            throw new Exception(CommonUtils.getLmbMessage(MessageId.SMT_TRACINGT_CFGHEADERID_IS_NULL));
        }
        if (StringUtils.isEmpty(itemCode)) {
            throw new Exception(CommonUtils.getLmbMessage(MessageId.ITEM_CODE_EMPTY));
        }
        if (StringUtils.isEmpty(processCode)) {
            throw new Exception(CommonUtils.getLmbMessage(MessageId.PROCESS_IS_NULL));
        }
    }

    /**
     * 计算可用时间
     *
     * @param uph      产能
     * @param bomQty   标准用量
     * @param qtyFinal 物料数量
     * @param pcbQty   指令拼板数
     * @return availableTime 可用时间
     * @throws Exception
     */
    public BigDecimal calAvailableTime(BigDecimal uph, BigDecimal bomQty,
                                       int qtyFinal, int pcbQty) throws Exception {

        if (bomQty.compareTo(BigDecimal.ZERO) <= 0) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,MessageId.STANDARD_DOSAGE_CANNOT_BE_ZERO);
        }
        BigDecimal availableTime =
                (new BigDecimal(qtyFinal).divide(bomQty, 2, BigDecimal.ROUND_FLOOR)).multiply(BigDecimal.valueOf(60))
                        .divide(uph, 2, BigDecimal.ROUND_FLOOR).multiply(BigDecimal.valueOf(pcbQty));

        return availableTime;
    }

    /**
     * 物料可用时间表数据生成
     *
     * @param uph           产能
     * @param cfgHeaderId   上料表ID
     * @return SmtItemAvailableTime 物料可用时间对象
     * @throws Exception
     */
    public SmtItemAvailableTime generateAvailableTimedData(BigDecimal uph, SMTScanParamDTO scanParamDTO,
                                                           BigDecimal availableTime,String cfgHeaderId, String craftSection) throws Exception {
        SMTScanDTO dto = scanParamDTO.getDto();
        int pcbQty = scanParamDTO.getPcbQty();
        SmtMachineMaterialMouting mouting = scanParamDTO.getMounting();
        BigDecimal bomQty =scanParamDTO.getBomQty();
        String processCode = scanParamDTO.getProcessCode();
        String workStation = scanParamDTO.getWorkStation();
        String workshopCode = scanParamDTO.getWorkshopCode();
        Date curDate = scanParamDTO.getCurDate();
        String empNo = dto.getCreateBy();
        if (StringUtils.isEmpty(empNo)) {
            empNo = BusinessConstant.SYS_EMP_NO;
        }

        SmtItemAvailableTime smtItemAvailableTime = new SmtItemAvailableTime();
        smtItemAvailableTime.setLineCode(dto.getLineCode());
        smtItemAvailableTime.setSiatId(UUID.randomUUID().toString());
        smtItemAvailableTime.setProcessCode(processCode);
        smtItemAvailableTime.setWorkOrderNo(dto.getWorkOrderNo());
        smtItemAvailableTime.setWorkshopCode(workshopCode);
        smtItemAvailableTime.setCraftSection(craftSection);
        smtItemAvailableTime.setWorkStation(workStation);
        smtItemAvailableTime.setMachineNo(mouting.getMachineNo());
        smtItemAvailableTime.setObjectId(mouting.getObjectId());
        smtItemAvailableTime.setModuleNo(mouting.getModuleNo());
        smtItemAvailableTime.setTrackNo(mouting.getTrackNo());
        smtItemAvailableTime.setLocationNo(mouting.getLocationNo());
        smtItemAvailableTime.setAvailableQty(mouting.getQty());
        smtItemAvailableTime.setItemCode(mouting.getItemCode());
        smtItemAvailableTime.setItemName(mouting.getItemName());
        smtItemAvailableTime.setBomQty(bomQty);
        smtItemAvailableTime.setPcbQty(BigDecimal.valueOf(pcbQty));
        smtItemAvailableTime.setCfgHeaderId(cfgHeaderId);
        smtItemAvailableTime.setUph(uph);
        smtItemAvailableTime.setFactoryId(dto.getFactoryId());
        smtItemAvailableTime.setEntityId(dto.getEntityId());
        smtItemAvailableTime.setAvailableTime(availableTime);
        smtItemAvailableTime.setLastUpdatedBy(empNo);
        smtItemAvailableTime.setLastUpdatedDate(curDate);
        smtItemAvailableTime.setCreateBy(empNo);
        smtItemAvailableTime.setCreateDate(curDate);
        smtItemAvailableTime.setEnabledFlag(Constant.FLAG_Y);
        return smtItemAvailableTime;
    }

    /**
     * 检查DTO合法性
     * @param codeDto
     * @throws Exception
     */
    @Override
    public List<String> checkBindCodeDTO(BindingBarcodeDTO codeDto) throws Exception {
        if ((null == codeDto.getSourceTask())
                || (null == codeDto.getLineCode())
                || (null == codeDto.getWorkOrderNo())
                || (null == codeDto.getParentSn())
                || (null == codeDto.getEmpNo())
                || StringHelper.isEmpty(codeDto.getSourceTask())
                || StringHelper.isEmpty(codeDto.getLineCode())
                || StringHelper.isEmpty(codeDto.getWorkOrderNo())
                || StringHelper.isEmpty(codeDto.getParentSn())
                || StringHelper.isEmpty(codeDto.getEmpNo())
                || (codeDto.getQty() < 1)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PARAM_MISSING);
        }
        String regParent13 = "^[P,p][0-9]{12}$";
        String regParent12 = "^[0-9]{12}$";
        String regSourceTask = "^[0-9]{7}$";
        String regSn = "^[0-9]{12}$";
        // 验证 parentSN
        if (!Pattern.matches(regSourceTask, codeDto.getSourceTask())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SOURCE_TASK_ERROR);
        }
        if (!Pattern.matches(regParent13, codeDto.getParentSn())
                && !Pattern.matches(regParent12, codeDto.getParentSn())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PARENTSN_ERROR);
        }
        int parentCodeLength =
                codeDto.getParentSn().length();
        if (!codeDto.getParentSn().substring(
                parentCodeLength - Constant.INT_12,
                parentCodeLength - Constant.INT_12 + Constant.INT_7).equals(codeDto.getSourceTask())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PARENTSN_ERROR);
        }
        // 验证 SN
        String[] sns = codeDto.getSn().split(",");
        if (sns.length < Constant.INT_1) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SN_ERROR);
        }
        List<String> listSn = Arrays.asList(sns).stream().distinct().collect(Collectors.toList());
        for (String s : listSn) {
            if (!Pattern.matches(regSn, s) || (!s.substring(0, 7).equals(codeDto.getSourceTask()))) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SN_ERROR);
            }
        }
        return listSn;
    }

    /**
     * 写入拼板关系
     *
     * @param codeDto
     * @param listSn
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveBindCode(BindingBarcodeDTO codeDto, List<String> listSn) throws Exception {
        // 查询Sn 扫描情况
        List<PsScanHistory> historys = psScanHistoryRepository.selectBySns(listSn);
        if (!CollectionUtils.isEmpty(historys)) {
            saveWithList(codeDto, historys, listSn);
        } else {
            checkQty(codeDto, Constant.INT_0, listSn.size());
            List<PsScanHistory> addList = makeAdd(codeDto, listSn);
            addParentSn(addList);
        }
    }

    /**
     * 组成 PscanHistory 新增数据
     *
     * @param codeDto 绑定DTO
     * @param listSn  小板条码
     * @return
     * @throws Exception
     */
    private List<PsScanHistory> makeAdd(BindingBarcodeDTO codeDto, List<String> listSn)
            throws Exception {
        List<PsScanHistory> addList = new ArrayList<>();
        PsWorkOrderBasic workOrderBasic =
                PlanscheduleRemoteService.findWorkOrder(codeDto.getWorkOrderNo());
        if (null == workOrderBasic) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.WORKORDER_INFO_IS_NULL);
        }
        for (String s : listSn) {
            PsScanHistory psScanHistory = new PsScanHistory();
            psScanHistory.setSn(s);
            psScanHistory.setWorkOrderNo(codeDto.getWorkOrderNo());
            psScanHistory.setParentSn(codeDto.getParentSn());
            psScanHistory.setRouteId(workOrderBasic.getRouteId());
            psScanHistory.setItemNo(workOrderBasic.getItemNo());
            psScanHistory.setWorkshopCode(workOrderBasic.getWorkshopCode());
            psScanHistory.setSourceSys(SPI);
            psScanHistory.setSmtScanId(UUID.randomUUID().toString());
            psScanHistory.setCreateBy(codeDto.getEmpNo());
            psScanHistory.setAttribute1(codeDto.getSourceTask());
            psScanHistory.setLineCode(codeDto.getLineCode());
            psScanHistory.setFactoryId(workOrderBasic.getFactoryId());
            addList.add(psScanHistory);
        }
        return addList;
    }

    /**
     * 数据库中已存小板条码时存盘处理
     *
     * @param codeDto
     * @param historys
     * @param listSn
     * @throws Exception
     */
    private void saveWithList(BindingBarcodeDTO codeDto, List<PsScanHistory> historys,
                              List<String> listSn) throws Exception {
        List<PsScanHistory> updates = new ArrayList<>();
        List<String> adds = new ArrayList<>();
        int irepeat = 0;
        for (PsScanHistory history : historys) {
            if ((null != history.getParentSn())
                    && (!StringHelper.isEmpty(history.getParentSn()))) {
                // 小板条码存且大板条与当前大板一致只计数不更改数据
                if (history.getParentSn().equals(codeDto.getParentSn())) {
                    irepeat++;
                } else {
                    throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SN_PARENT);
                }
            } else {
                //如小板条码存在，但大板条码不存时， 更新列
                history.setCreateBy(codeDto.getEmpNo());
                history.setParentSn(codeDto.getParentSn());
                updates.add(history);
            }
        }
        addSnForList(historys, listSn, adds);
        checkQty(codeDto, irepeat, listSn.size());
        if (!CollectionUtils.isEmpty(adds)) {
            List<PsScanHistory> addList = makeAdd(codeDto, adds);
            addParentSn(addList);
        }
        if (!CollectionUtils.isEmpty(updates)) {
            updateParentSn(updates);
        }
    }

    /**
     * @param historys
     * @param listSn
     * @param adds
     */
    private void addSnForList(List<PsScanHistory> historys, List<String> listSn, List<String> adds) {
        if (listSn.size() > historys.size()) {
            for (String s : listSn) {
                if (!checkhistory(s, historys)) {
                    adds.add(s);
                }
            }
        }
    }

    /**
     * 比较输入SN是否已存在数据库中
     *
     * @param s
     * @param historys
     * @return
     */
    private boolean checkhistory(String s, List<PsScanHistory> historys) {
        for (PsScanHistory history : historys) {
            if (history.getSn().equals(s)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查
     *
     * @param codeDto
     * @param irepeat
     * @param listSize
     */
    private void checkQty(BindingBarcodeDTO codeDto, int irepeat, int listSize) throws Exception {
        int count = this.getCountByParentSn(codeDto.getParentSn());
        if (codeDto.getQty() != count + listSize - irepeat) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.QTY_ERROR);
        }
        if (listSize == irepeat) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.NO_SN_BINDED);
        }
    }

    @Override
    public Integer countByWorkDate(WorkOrderDateDTO querDto) {
        return psScanHistoryRepository.countByWorkDate(querDto);
    }

    //根据批次获取绑定数据
    @Override
    public BindingBarcodeDataForProdplanIdDto getBindingBarcodeDataByProdplanId(String prodplanId) throws Exception {
        if (StringUtils.isEmpty(prodplanId)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PRODPLANID_ID_NULL);
        }
        return psScanHistoryRepository.getBindingBarcodeDataByProdplanId(prodplanId);
    }

    /**
     *
     *@Author: 10307315陈俊熙
     *@date 2022/4/12 下午3:34
     *@param dto
     *@return void
     */
    @Override
    @RecordLogAnnotation("中试系统直接调用生成拼板关系")
    public void saveMakeupRelationship(MakeupRelationshipDTO dto) throws Exception {
        // 校验必填参数
        checkParam(dto);
        // 校验大板条码是否符合规则。
        checkFormatOfParentSn(dto.getParentSn());
        // 校验大板条码是否已经存在绑定记录，如果返回true，则不处理本次请求
        if (checkParentSn(dto)) {
            return;
        }
        // 校验小板条码是否在wip_info存在
        List<PsWipInfo> listChildSnWipInfo = checkChildSn(dto);
        // 全部通过，插入绑定关系表。
        insertMakeupRelationship(listChildSnWipInfo, dto);
    }

    /**
     * 构造数据并插入保存
     *@Author: 10307315陈俊熙
     *@date 2022/4/13 下午5:15
     *@param
     *@return void
     */
    private void insertMakeupRelationship(List<PsWipInfo> listChildSnWipInfo, MakeupRelationshipDTO dto) throws Exception {
        Map<String, String> headerParamsMap = MESHttpHelper.getHttpRequestHeader();
        List<PsScanHistory> listPsScanHistory = new ArrayList<>();
        // 构造数据
        for (PsWipInfo psWipInfo : listChildSnWipInfo) {
            PsScanHistory psScanHistory = new PsScanHistory();
            psScanHistory.setAttribute1(psWipInfo.getAttribute1());
            psScanHistory.setCreateBy(dto.getOperator());
            psScanHistory.setLineCode(psWipInfo.getLineCode());
            psScanHistory.setParentSn(dto.getParentSn());
            psScanHistory.setPcbQty(new BigDecimal(dto.getListChildSn().size()));
            psScanHistory.setSn(psWipInfo.getSn());
            psScanHistory.setSourceSys(Constant.SOURCE_SYS_ZS);
            psScanHistory.setWorkOrderNo(psWipInfo.getWorkOrderNo());
            psScanHistory.setLastUpdatedBy(dto.getOperator());
            String factory = headerParamsMap.get(SysConst.HTTP_HEADER_X_FACTORY_ID);
            factory = StringUtils.isEmpty(factory) ? headerParamsMap.get(SysConst.HTTP_HEADER_X_FACTORY_ID_LOW_CASE) : factory;
            psScanHistory.setFactoryId(new BigDecimal(factory));
            psScanHistory.setEntityId(new BigDecimal(factoryConfig.getCommonEntityId()));
            listPsScanHistory.add(psScanHistory);
        }
        // 调用原来的接口实现保存数据
        addOrUpdateParentSn(listPsScanHistory);
    }

    /**
     *  校验小板条码是否在wip_info存在，存在没有扫描的条码会报错，如果全部通过，返回查到的实体列表(经过sn排序)用于插入
     *@Author: 10307315陈俊熙
     *@date 2022/4/13 下午4:25
     *@param
     *@return java.util.List<com.zte.domain.model.PsWipInfo> 返回wipinfo实体用于插入数据
     */
    private List<PsWipInfo> checkChildSn(MakeupRelationshipDTO dto) throws Exception {
        // 校验小板条码是否在wip_info存在，将不存在的条码提示出。
        List<String> listChildSn = dto.getListChildSn();
        List<PsWipInfo> listWipInfo = psWipInfoService.getListByBatchSn(listChildSn);
        if (listWipInfo == null || listWipInfo.size() == Constant.INT_0) {
            String[] msg = new String[]{listChildSn.toString()};
            throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, MessageId.CHILD_SN_IS_NOT_SCANNED, msg);
        }
        // 存在扫描记录的子卡集合。
        Set<String> setSn = listWipInfo.stream().map(PsWipInfo::getSn).collect(Collectors.toSet());
        // 移除存在的子卡集合。
        listChildSn.removeAll(setSn);
        // 判断如果存在未扫描，则保错。
        if (!CollectionUtils.isEmpty(listChildSn)) {
            String[] msg = new String[]{listChildSn.toString()};
            throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, MessageId.CHILD_SN_IS_NOT_SCANNED, msg);
        }
        return listWipInfo;
    }
    /**
     * 校验大板条码是否已经存在绑定记录，存在则不处理会返回true，
     *@Author: 10307315陈俊熙
     *@date 2022/4/12 下午4:31
     *@param
     *@return void
     */
    private boolean checkParentSn(MakeupRelationshipDTO dto) throws Exception {
        Map<String, Object> record = new HashMap<>(Constant.INT_2);
        List<PsScanHistory> psScanHistoryList = psScanHistoryRepository.getMakeupRelationshipByParentSn(dto.getParentSn());
        // 不为空，则说明存在拼板记录，直接返回，不处理逻辑
        if (!CollectionUtils.isEmpty(psScanHistoryList)) {
            return true;
        }
        return false;
    }

    /**
     *  大板条码需要符合P+12位数字规则。
     *@Author: 10307315陈俊熙
     *@date 2022/4/12 下午4:02
     *@param
     *@return void
     */
    private void checkFormatOfParentSn(String parentSn) throws Exception {
        if(!parentSn.matches(REGEX_PARENT_SN)) {
            throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, MessageId.PARENT_SN_FORMAT_ERROR, new String[] {parentSn});
        }
    }

    /**
     * 必填参数校验
     *@Author: 10307315陈俊熙
     *@date 2022/4/12 下午4:02
     *@param
     *@return void
     */
    private void checkParam(MakeupRelationshipDTO dto) throws Exception{
        if (dto == null) {
            throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE,RetCode.VALIDATIONERROR_MSGID);
        }
        if (StringUtils.isEmpty(dto.getParentSn())) {
            throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE,MessageId.PARENT_SN_CAN_NOT_BE_NULL);
        }
        if (dto.getListChildSn() == null || dto.getListChildSn().size() == Constant.INT_0) {
            throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE,MessageId.LIST_CHILD_SN_CAN_NOT_BE_NULL);
        }
        if (StringUtils.isEmpty(dto.getOperator())) {
            throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE,MessageId.OPERATOR_CAN_NOT_BE_NULL);
        }
    }

    /**
     * 条码扫描历史导出EXCEL email
     *
     * @param dto
     * @return
     */
    @Override
    public Object exportScanHistoryEmail(HttpServletResponse response, PsWipInfoDTO dto) throws Exception {
        checkDataEmpty(dto);
        ThreadUtil.EXECUTOR.execute(() -> {
            OfflineExportDTO offlineExportDTO = new OfflineExportDTO();
            offlineExportDTO.setId(UUID.randomUUID().toString());
            offlineExportDTO.setFunctionName(MpConstant.ScanHistoryExport.FUNCTION_NAME);
            offlineExportDTO.setStatus(NumConstant.STR_ONE);
            offlineExportDTO.setRecipients(dto.getEmpNo());
            offlineExportDTO.setCreateBy(dto.getEmpNo());
            offlineExportDTO.setLastUpdatedBy(dto.getEmpNo());
            // 获取最大导出数量
            Pair<Integer, Integer> maxNumberSys = this.getMaxNumberFromSys();
            // 最大行数
            int maxNumber = NumConstant.NUM_100000;
            // 单个任务最大同时导出
            int maxDownNumber = maxNumberSys.getFirst();
            String empNoDownKey = String.format(MpConstant.ScanHistoryExport.SCAN_HISTORY_EXPORT_REDIS_LOCK, dto.getEmpNo());
            // 校验同时任务个数
            this.verifyQuantityExported(maxDownNumber, empNoDownKey);
            String filePath;
            try {
                centerFactoryFeignService.offlineExportSave(offlineExportDTO);
                filePath = buildScanHistoryData(response, dto, maxNumber, offlineExportDTO);
                offlineExportDTO.setFileDownloadUrl(filePath);
                offlineExportDTO.setStatus(NumConstant.STR_TWO);
            } catch (Exception e) {
                offlineExportDTO.setStatus(NumConstant.STR_THREE);
                emailUtils.sendMail(dto.getEmail(), MpConstant.ScanHistoryExport.EMAIL_TITLE_ZH,
                        MpConstant.ScanHistoryExport.EMAIL_TITLE_EN,
                        MpConstant.ScanHistoryExport.EXCEPTION + getErrorMsg(e), StringUtils.EMPTY);
            } finally {
                redisTemplateExport.delete(empNoDownKey);
                centerFactoryFeignService.offlineExportUpdate(offlineExportDTO);
            }
        });
        return null;
    }

    /**
     * 直接导出
     * @param request
     * @param response
     * @param dto
     * @throws Exception
     */
    @RedisDistributedLockAnnotation(redisPrefix = "exportScanHistoryExcel", redisLockTime = 1800, lockFailMsgZh =
            "正在导出请稍后再试", lockFailMsgEn = "Exporting. Please try again later.", redisLockParam = {
            @RedisLockParamAnnotation(paramName = "dto", propertiesString = "empNo")})
    public void exportScanHistory(HttpServletRequest request, HttpServletResponse response, PsWipInfoDTO dto) throws Exception {
        checkDataEmpty(dto);
        SimpleDateFormat sdf = new SimpleDateFormat(MpConstant.DATE_FORMAT_YYYYMMDD);
        String fileName = sdf.format(new Date()) + MpConstant.ScanHistoryExport.EXPORT_FILE_NAME;
        //easy导出
        ImesExcelUtil.setResponseHeader(response, fileName);
        WriteSheet build = EasyExcelFactory.writerSheet(0, fileName).build();
        ExcelWriter excelWriter = EasyExcelFactory.write(response.getOutputStream(), PsScanHistoryDTO.class)
                .excelType(ExcelTypeEnum.XLSX).build();
        long page = new Long(Constant.STR_NUMBER_ONE);
        long rows = new Long(MpConstant.NUM_2000);
        int maxNumber = NumConstant.NUM_100000;
        Map<String, Object> record = setParamsMap(dto, page, rows);
        long count = psScanHistoryRepository.getCount(record);
        if (count > maxNumber) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.THE_EXPORTED_DATA_CANNOT_BE_LARGER_THAN_100000);
        }
        boolean isHasNextPage = true;
        List<PsScanHistoryDTO> psScanHistorySort = new ArrayList<>();
        if (StringUtils.isNotEmpty(dto.getLpn())) {
            psScanHistorySort = getPsScanHistoryDTOSWithLpn(dto, page, rows, isHasNextPage, maxNumber);
            if (CollectionUtils.isEmpty(psScanHistorySort) && count == 0) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SEARCH_SCAN_HISTORY_INFO_IS_NULL);
            }
            // 按照条码顺序+最后更新时间倒序
            psScanHistorySort = psScanHistorySort.stream()
                    .sorted(Comparator.comparing(PsScanHistoryDTO::getSn)
                            .thenComparing(PsScanHistoryDTO::getLastUpdatedDate, Comparator.reverseOrder()))
                    .collect(Collectors.toList());
            List<List<PsScanHistoryDTO>> splitScanHisList = CommonUtils.splitList(psScanHistorySort, NumConstant.NUM_2000);
            for (List<PsScanHistoryDTO> scanHisList : splitScanHisList) {
                excelWriter.write(scanHisList , build);
            }
        } else {
            int totalCnt = NumConstant.NUM_ZERO;
            while (goOn(maxNumber, isHasNextPage, totalCnt)) {
                Map<String, Object> recordLpn = setParamsMap(dto, page, rows);
                recordLpn.put("orderField", "getScanHis");
                List<PsScanHistory> snHisList = psScanHistoryRepository.getPage(recordLpn);
                if (CollectionUtils.isEmpty(snHisList)) {
                    break;
                }
                isHasNextPage = this.hasNextPage(snHisList.size(), rows);
                totalCnt += snHisList.size();
                page++;
                List<PsScanHistoryDTO> psScanHisList = setCurrProcessName(snHisList);
                setLineName(psScanHisList);
                setCreateByName(psScanHisList);
                setMBomProductCode(psScanHisList);
                excelWriter.write(psScanHisList, build);
            }
        }
        excelWriter.finish();
    }

    private boolean hasNextPage(int size, long rows) {
        if (size < rows) {
            return false;
        }
        return true;
    }

    /**
     * 判断是否继续导出下一页
     * @param maxNumber
     * @param isHasNextPage
     * @param totalCnt
     * @return
     */
    private boolean goOn(int maxNumber, boolean isHasNextPage, int totalCnt) {
        return isHasNextPage && totalCnt < maxNumber;
    }

    private static void checkDataEmpty(PsWipInfoDTO dto) throws Exception {
        if (StringUtils.isEmpty(dto.getSn()) && StringUtils.isEmpty(dto.getWorkOrderNo()) && StringUtils.isEmpty(dto.getAttribute1())) {
            throw new Exception(CommonUtils.getLmbMessage(MessageId.EXPORT_SCAN_HISTORY_MISS_PARAMS));
        }
    }

    /**
     * 组装条码扫描历史导出数据
     *
     * @param dto
     * @param maxNumber
     * @return
     * @throws Exception
     */
    private String buildScanHistoryData(HttpServletResponse response, PsWipInfoDTO dto, int maxNumber, OfflineExportDTO offlineExportDTO) throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat(MpConstant.DATE_FORMAT_YYYYMMDD);
        String fileName = sdf.format(new Date()) + MpConstant.ScanHistoryExport.FILE_NAME;
        offlineExportDTO.setFileName(fileName);
        int totalPage = 0;
        // 1.查询条码信息wip_info,如果条件中有容器代码，调用配送服务/getPageContainerContentPage
        // 2.根据条码信息分批查询wip_scan_history
        // 3.查询工艺路径信息匹配每一个扫描历史记录的子工序顺序,匹配人员姓名工号,线体名称等信息
        // 4.将查询数据根据条码+子工序顺序排列
        long page = new Long(Constant.STR_NUMBER_ONE);
        long rows = new Long(Constant.ONE_10000);
        boolean isHasNextPage = true;
//        long sheetNo = NumConstant.NUM_ONE;
        //easy导出
        String tempfilePath = FileUtils.createFilePathAndCheck(fileName);
        ImesExcelUtil.setResponseHeader(response, fileName);
        ExcelWriter excelWriter = EasyExcel.write(tempfilePath).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).head(PsScanHistoryDTO.class).build();
        WriteSheet writeSheet = EasyExcel.writerSheet(0, fileName).build();
        List<PsScanHistoryDTO> psScanHistorySort = new ArrayList<>();
        if (StringUtils.isNotEmpty(dto.getLpn())) {
            psScanHistorySort = getPsScanHistoryDTOSWithLpn(dto, page, rows, isHasNextPage, maxNumber);
            checkPsScanHistorySort(psScanHistorySort);
            // 按照条码顺序+最后更新时间倒序
            psScanHistorySort = psScanHistorySort.stream()
                    .sorted(Comparator.comparing(PsScanHistoryDTO::getSn)
                            .thenComparing(PsScanHistoryDTO::getLastUpdatedDate, Comparator.reverseOrder()))
                    .collect(Collectors.toList());

            List<List<PsScanHistoryDTO>> splitScanHisList = CommonUtils.splitList(psScanHistorySort, NumConstant.NUM_2000);
            for (List<PsScanHistoryDTO> scanHisList : splitScanHisList) {
                excelWriter.write(scanHisList , writeSheet);
            }
        } else{
            List<PsScanHistory> snHisList;
            int totalCnt = NumConstant.NUM_ZERO;
            while (isHasNextPage && totalCnt < maxNumber) {
                Map<String, Object> map = setParamsMap(dto, page, rows);
                map.put("orderField", "getScanHis");
                snHisList = psScanHistoryRepository.getPage(map);
                if (CollectionUtils.isEmpty(snHisList)) {
                    isHasNextPage = false;
                    break;
                }
                totalCnt += snHisList.size();
                page++;
                List<PsScanHistoryDTO> psScanHisList = setCurrProcessName(snHisList);
                setLineName(psScanHisList);
                setCreateByName(psScanHisList);
                setMBomProductCode(psScanHisList);
                // 写excel
                excelWriter.write(psScanHisList, writeSheet);
            }
        }
        excelWriter.finish();
        //上传文档云
        String fileKey = cloudDiskHelper.fileUpload(tempfilePath, dto.getEmpNo(), CloudDiskHelper.MAX_RETRY_TIMES);
        String downloadURL = cloudDiskHelper.getFileDownloadUrl(fileKey, fileName, dto.getEmpNo());
        FileUtils.deleteFile(tempfilePath);
        offlineExportDTO.setFileName(fileName);
        offlineExportDTO.setFileKey(fileKey);
        offlineExportDTO.setStatus(NumConstant.STRING_TWO);
        // 消息内容
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append(MpConstant.ScanHistoryExport.EMAIL_PREFIX)
                .append(fileName)
                .append(MpConstant.ScanHistoryExport.EMAIL_PREFIX_A)
                .append(downloadURL)
                .append(MpConstant.ScanHistoryExport.EMAIL_COLO)
                .append(MpConstant.ScanHistoryExport.CLICK_DOWN)
                .append(MpConstant.ScanHistoryExport.EMAIL_SUFFIX);
        // 发送邮件通知
        emailUtils.sendMail(dto.getEmail(), MpConstant.ScanHistoryExport.EMAIL_TITLE_ZH, MpConstant.ScanHistoryExport.EMAIL_TITLE_EN,
                stringBuffer.toString(), StringUtils.EMPTY);
        return downloadURL;

    }

    private static void checkPsScanHistorySort(List<PsScanHistoryDTO> psScanHistorySort) {
        if (CollectionUtils.isEmpty(psScanHistorySort)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SEARCH_SCAN_HISTORY_INFO_IS_NULL);
        }
    }

    private Map<String, Object> setParamsMap(PsWipInfoDTO dto, long page, long rows) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("startRow", (page - 1) * rows + 1);
        map.put("endRow", page * rows);
        map.put("page", page);
        map.put("rows", rows);
        map.put("attribute1", dto.getAttribute1());
        map.put("sn", dto.getSn());
        map.put("isPrint", dto.getIsPrint());
        map.put("workOrderNo", dto.getWorkOrderNo());
        map.put("factoryId", dto.getFactoryId());
        map.put("workStation", dto.getWorkStation());
        map.put("currProcessCode", dto.getCurrProcessCode());
        if (CraftConstant.CURR_PROCESS_NAME.equals(dto.getSort())) {
            dto.setSort("currProcessCode");
        }
        map.put("orderField", dto.getSort());
        map.put("order", dto.getOrder());
        return map;
    }

    private List<PsScanHistoryDTO> getPsScanHistoryDTOSWithLpn(PsWipInfoDTO dto, long page, long rows, boolean isHasNextPage, int maxNumber) throws Exception {
        List<PsScanHistoryDTO> psScanHistorySort;
        List<PsWipInfoDTO> allSnList = new ArrayList<>();
        while (isHasNextPage && allSnList.size() < maxNumber) {
            PageRows<PsWipInfoDTO> psWipInfoDTOPageRows = new PageRows<>();
            Map<String,Object> map =new HashMap<>();
            map.put("page",page);
            map.put("rows",rows);
            map.put("entityIdentification",dto.getSn());
            map.put("inTaskBelongsTo",dto.getWorkOrderNo());
            map.put("lpn",dto.getLpn());
            map.put("workStation",dto.getWorkStation());
            map.put("curProcessCode",dto.getCurrProcessCode());
            psWipInfoDTOPageRows = ProductionDeliveryRemoteService.getPageContainerContent(map);
            if (null == psWipInfoDTOPageRows || CollectionUtils.isEmpty(psWipInfoDTOPageRows.getRows())) {
                isHasNextPage = false;
                break;
            } else {
                page++;
                allSnList.addAll(psWipInfoDTOPageRows.getRows());
            }
        }
        if (CollectionUtils.isEmpty(allSnList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SEARCH_SCAN_HISTORY_INFO_IS_NULL);
        }
        psScanHistorySort = getScanHisListBySnList(dto, allSnList);
        setMBom(psScanHistorySort);
        return psScanHistorySort;
    }

    /**
     * 获取条码的扫描历史信息
     * @param dto
     * @param allSnList
     * @return
     * @throws Exception
     */
    private List<PsScanHistoryDTO> getScanHisListBySnList(PsWipInfoDTO dto, List<PsWipInfoDTO> allSnList) throws Exception {
        List<String> snStrList = allSnList.stream().map(PsWipInfoDTO::getSn).distinct().collect(Collectors.toList());
        List<List<String>> splitSnStrList = CommonUtils.splitList(snStrList, NumConstant.NUM_1000);
        List<PsScanHistoryDTO> psScanHistoryDTOS = new ArrayList<>();
        for (List<String> strListSn : splitSnStrList) {
            Map<String, Object> record = new HashMap<String, Object>();
            record.put("factoryId", dto.getFactoryId());
            record.put("snList", strListSn);
            List<PsScanHistory> list = psScanHistoryRepository.getList(record);
            List<PsScanHistoryDTO> listDTO = new ArrayList<PsScanHistoryDTO>();
            if (!CollectionUtils.isEmpty(list)) {
                listDTO = setCurrProcessName(list);
            }
            setLineName(listDTO);
            setCreateByName(listDTO);
            setMBomProductCode(listDTO);
            psScanHistoryDTOS.addAll(listDTO);
        }
        return psScanHistoryDTOS;
    }

    private void setMBomProductCode(List<PsScanHistoryDTO> list) {
        List<String> prodplanIds = list.stream().map(i -> i.getSn().substring(NumConstant.NUM_ZERO, NumConstant.NUM_SEVEN)).collect(Collectors.toList());
        List<BProdBomHeaderDTO> mBomList = centerfactoryRemoteService.queryProductCodeByProdPlanIdList(prodplanIds);
        Map<String, String> mBomProductCodeByProdplanIdMap = mBomList.stream().collect(Collectors.toMap(BProdBomHeaderDTO::getProdplanId, BProdBomHeaderDTO::getProductCode, (v1, v2) -> v1));
        for(PsScanHistoryDTO dto : list) {
            String mBomProductCode = mBomProductCodeByProdplanIdMap.get(dto.getSn().substring(NumConstant.NUM_ZERO, NumConstant.NUM_SEVEN));
            dto.setMBomProductCode(mBomProductCode == null ? dto.getItemNo() : mBomProductCode);
        }
    }
    /**
     * 校验导出最大任务数
     *
     * @param maxDownNumber
     * @param empNoDownKey
     * @throws MesBusinessException
     */
    private void verifyQuantityExported(int maxDownNumber, String empNoDownKey) throws MesBusinessException {
        Boolean ifAbsent = redisTemplateExport.opsForValue()
                .setIfAbsent(empNoDownKey, new Date(), Constant.INT_1, TimeUnit.DAYS);
        // 上锁成功
        if (Boolean.TRUE.equals(ifAbsent)) {
            return;
        }
        throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.EXPORT_DOWN_MAX_LIMIT);
    }

    /**
     * 获取email 最大导出数量,与并发导出数
     *
     * @return
     */
    private Pair<Integer, Integer> getMaxNumberFromSys() {
        // 获取最大可导出数量,和任务数
        Map<String, Object> lookupTypeMap = new HashMap<>(1);
        lookupTypeMap.put(Constant.FIELD_LOOKUP_TYPE, Constant.lookupType.VALUE_1004041);
        List<SysLookupValuesDTO> lookupValuesDTOList =
                BasicsettingRemoteService.getLookupValueByTypeCodes(lookupTypeMap);
        if (CollectionUtils.isEmpty(lookupValuesDTOList)) {
            return Pair.of(Constant.INT_5, NumConstant.NUM_100000);
        }
        SysLookupValuesDTO sysLookupValuesDTO1 = lookupValuesDTOList.stream()
                .filter(item -> Constant.lookupType.VALUE_1004041005.equals(item.getLookupCode().toString()))
                .findFirst()
                .orElse(null);
        return sysLookupValuesDTO1 == null ? Pair.of(Constant.INT_5, NumConstant.NUM_100000) :
                Pair.of(Integer.valueOf(sysLookupValuesDTO1.getAttribute1()),
                        Integer.valueOf(sysLookupValuesDTO1.getLookupMeaning()));
    }

    private String getErrorMsg(Exception e) {
        String errorMsg = e.getMessage();
        if (e instanceof MesBusinessException) {
            Object[] paramsArr = ((MesBusinessException) e).getParams();
            String[] paArr = new String[Constant.INT_1];
            if (paramsArr != null) {
                paArr = new String[paramsArr.length];
                for (int i = 0; i < paramsArr.length; i++) {
                    paArr[i] = String.valueOf(paramsArr[i]);
                }
            }
            String exMsgId = ((MesBusinessException) e).getExMsgId();
            errorMsg = CommonUtils.getLmbMessage(exMsgId, paArr);
        }
        return errorMsg;
    }

    @Override
    public Integer getScanCountByGroup(PsScanHistory dto) throws Exception {
        if (StringUtils.isEmpty(dto.getProcessName()) || StringUtils.isEmpty(dto.getAttribute1())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PARAMS_MISSING);
        }
        BSProcess bsProcess = getBsProcess(dto);
        dto.setCurrProcessCode(bsProcess.getProcessCode());
        List<PsScanHistory> psScanHistoryList = psScanHistoryRepository.getScanCountByGroup(dto);
        if(CollectionUtils.isEmpty(psScanHistoryList) || StringUtils.isEmpty(psScanHistoryList.get(NumConstant.NUM_ZERO).getCnt())){
            return NumConstant.NUM_ZERO;
        }
        int result = Integer.parseInt(psScanHistoryList.get(NumConstant.NUM_ZERO).getCnt());
        return result;
    }

    @Override
    public String getParentSnBySnAndSourceSys(String sn, String sourceSys) {
        return psScanHistoryRepository.getParentSnBySnAndSourceSys(sn, sourceSys);
    }

    @Override
    public String getFirstNotCalParentSn(String workOrderNo, String sourceSys) {
        return psScanHistoryRepository.getFirstNotCalParentSn(workOrderNo, sourceSys);
    }

    @Override
    public void updateIsTraceCalculate(String isTraceCalculate, String workOrderNo, String parentSn, String sourceSys) {
        psScanHistoryRepository.updateIsTraceCalculate(isTraceCalculate, workOrderNo, parentSn, sourceSys);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer insertScanHisByAuxScan(PsScanHistory dto) throws Exception {
        if (StringUtils.isEmpty(dto.getProcessName()) || StringUtils.isEmpty(dto.getAttribute1()) || StringUtils.isEmpty(dto.getLineCode())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PARAMS_MISSING);
        }
        RedisLock redisLock = new RedisLock(RedisKeyConstant.AUX_PROCESS_SCAN + dto.getProcessName() + dto.getAttribute1(), NumConstant.NUM_300);
        if (!redisLock.lock()) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.THE_TASK_IS_AUX_SCANNING);
        }
        try{
            List<PsTask> psTaskList=PlanscheduleRemoteService.getPsTaskByProdPlanId(dto.getAttribute1());
            if(CollectionUtils.isEmpty(psTaskList) || null == psTaskList.get(NumConstant.NUM_ZERO) || null == psTaskList.get(NumConstant.NUM_ZERO).getTaskQty()){
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.TASK_OF_PRDOPLANID_NOT_EXIST);
            }
            int taskQty = psTaskList.get(NumConstant.NUM_ZERO).getTaskQty().intValue();
            int count = NumConstant.NUM_ZERO;
            List<PsScanHistory> psList = psScanHistoryRepository.getScanCountByGroup(dto);
            if(!CollectionUtils.isEmpty(psList) && !StringUtils.isEmpty(psList.get(NumConstant.NUM_ZERO).getCnt())){
                count = Integer.parseInt(psList.get(NumConstant.NUM_ZERO).getCnt());
            }
            if(taskQty < count + dto.getAuxScanAmount()){
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.AUX_QTY_IS_EXCEED);
            }
            BSProcess bsProcess = getBsProcess(dto);
            dto.setCurrProcessCode(bsProcess.getProcessCode());
            int anCreateBegin = NumConstant.NUM_ZERO;
            PsScanHistory psScanHistory = psScanHistoryRepository.getLastSnByAuxScan(dto);
            if(null != psScanHistory && StringUtils.isNotEmpty(psScanHistory.getSn())){
                anCreateBegin = Integer.parseInt(psScanHistory.getSn().substring(7,12));
            }
            Map<String, SysLookupValuesDTO> sysMap = getSysMap();
            int snTemp =NumConstant.NUM_ZERO;
            List<PsScanHistory> batchInsertList =new ArrayList<>();
            for(int i = 0; i < dto.getAuxScanAmount();i++){
                StringBuffer sb = new StringBuffer(dto.getAttribute1());
                snTemp= anCreateBegin + i + NumConstant.NUM_ONE;
                sb.append(StringUtils.leftPad(String.valueOf(snTemp), 5, "0"));
                SysLookupValuesDTO sysTemp = sysMap.get(dto.getLineCode());
                if(null == sysTemp || StringUtils.isEmpty(sysTemp.getAttribute1())){
                    throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.GET_AUX_SYS_FAILED);
                };
                PsScanHistory psScanHistoryTemp = new PsScanHistory();
                psScanHistoryTemp.setSmtScanId(UUID.randomUUID().toString());
                psScanHistoryTemp.setSn(sb.toString());
                psScanHistoryTemp.setAttribute1(dto.getAttribute1());
                psScanHistoryTemp.setLineCode(sysTemp.getAttribute1());
                psScanHistoryTemp.setCurrProcessCode(dto.getCurrProcessCode());
                psScanHistoryTemp.setWorkStation(dto.getProcessName());
                psScanHistoryTemp.setSourceSysName(dto.getProcessName());
                psScanHistoryTemp.setCreateBy(dto.getCreateBy());
                psScanHistoryTemp.setLastUpdatedBy(dto.getLastUpdatedBy());
                psScanHistoryTemp.setItemNo(psTaskList.get(NumConstant.NUM_ZERO).getItemNo());
                psScanHistoryTemp.setItemName(psTaskList.get(NumConstant.NUM_ZERO).getItemName());
                psScanHistoryTemp.setFactoryId(dto.getFactoryId());

                batchInsertList.add(psScanHistoryTemp);
            }
            addParentSn(batchInsertList);
        } finally {
            if (redisLock != null) {
                redisLock.unlock();
            }
        }
        return dto.getAuxScanAmount();
    }

    private Map<String, SysLookupValuesDTO> getSysMap() throws MesBusinessException {
        List<SysLookupValuesDTO> listSys  = BasicsettingRemoteService.getLookupValueByTypeCodes(Constant.SYS_LOOK_UP_28650006);
        if(CollectionUtils.isEmpty(listSys)){
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.GET_AUX_SYS_FAILED);
        }
        return listSys.stream()
                .collect(Collectors.toMap(SysLookupValuesDTO::getDescriptionChin, a -> a, (k1, k2) -> k1));
    }

    private BSProcess getBsProcess(PsScanHistory dto) throws Exception {
        BSProcess process = new BSProcess();
        process.setxType(MpConstant.PROCESS_X_TYPE_P);
        List<BSProcess> queryProcessList = CrafttechRemoteService.getProcess(process);
        BSProcess bsProcess = queryProcessList.stream()
                .filter(item -> StringUtils.equals(dto.getProcessName(), item.getProcessName()))
                .findFirst().orElse(null);
        if (CollectionUtils.isEmpty(queryProcessList) || null == bsProcess || StringUtils.isEmpty(bsProcess.getProcessCode())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.NOT_FOUND_AUX_PROCESS);
        }
        return bsProcess;
    }
}
