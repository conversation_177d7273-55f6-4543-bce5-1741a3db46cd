package com.zte.application.impl;

import com.zte.application.BarcodeLockDetailService;
import com.zte.application.WipScanHistoryService;
import com.zte.common.CommonUtils;
import com.zte.common.DateUtil;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MesCollectionUtil;
import com.zte.common.utils.MpConstant;
import com.zte.common.utils.NumConstant;
import com.zte.common.utils.SqlUtils;
import com.zte.domain.model.CFLine;
import com.zte.domain.model.ParentsnAndSn;
import com.zte.domain.model.PsWorkOrderBasic;
import com.zte.domain.model.WipInfoDelLogRepository;
import com.zte.domain.model.WipScanHistory;
import com.zte.domain.model.WipScanHistoryRepository;
import com.zte.domain.vo.WorkOrderNoQtyVO;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.BoardInstructionCycleDataCreateDTO;
import com.zte.interfaces.dto.BoardInstructionCycleInfoDTO;
import com.zte.interfaces.dto.CraftSectionDataDTO;
import com.zte.interfaces.dto.CtRouteDetailDTO;
import com.zte.interfaces.dto.LineCurrProcessCodeParamDTO;
import com.zte.interfaces.dto.LineProduceInParamDTO;
import com.zte.interfaces.dto.LineProduceOutParamDTO;
import com.zte.interfaces.dto.ProcessProducesParamsDTO;
import com.zte.interfaces.dto.ProcessProducessDataDTO;
import com.zte.interfaces.dto.PsWorkOrderBasicOutDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.interfaces.dto.TimeProduceDTO;
import com.zte.interfaces.dto.WipInfoDelLogDTO;
import com.zte.interfaces.dto.WipScanHistoryDTO;
import com.zte.interfaces.dto.WipScanParamsDTO;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.string.StringHelper;
import com.zte.springbootframe.common.annotation.RecordLogAnnotation;
import com.zte.springbootframe.common.annotation.RedisDistributedLockAnnotation;
import com.zte.springbootframe.common.annotation.RedisLockParamAnnotation;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

/**
 * WipScanHistoryServiceImpl
 * <AUTHOR>
 *
 */
@Service
public class WipScanHistoryServiceImpl implements WipScanHistoryService {

	private static final Logger logger = LoggerFactory.getLogger(WipScanHistoryServiceImpl.class);
	@Autowired
	private WipScanHistoryRepository wipScanHistoryRepository;

	@Autowired
	private WipInfoDelLogRepository wipInfoDelLogRepository;

	@Autowired
	BarcodeLockDetailService barcodeLockDetailService;

	@Override
	public Long getBoardCount(WipScanHistoryDTO dto) {
		return wipScanHistoryRepository.getBoardCount(dto);
	}

	@Override
	public List<WipScanHistory> getOverBoardList(WipScanHistoryDTO dto) {
		return wipScanHistoryRepository.getOverBoardList(dto);
	}
	@Override
	public Integer insert(WipScanHistoryDTO dto) {
		return wipScanHistoryRepository.insert(dto);
	}




	/**
	 * 产品物料追溯查询根据产品sn和大板条码查询时间段(createDate前后加1天)
	 */
	@Override
	public WipScanHistory getTimeRange(WipScanHistoryDTO dto) throws Exception {
		// 根据你大板条码和产品sn查询，create最小值/最大值，左右增加一天
		WipScanHistory createDateBo = wipScanHistoryRepository.queryMinCreateDate(dto);
		if (createDateBo == null || createDateBo.getCreateDate() == null) {
			return null;
		}
		WipScanHistory entity = new WipScanHistory();
		entity.setCreateDate(createDateBo.getCreateDate());
		createDateBo = wipScanHistoryRepository.queryMaxCreateDate(dto);
		if (createDateBo == null || createDateBo.getLastUpdatedDate() == null) {
			return null;
		}
		entity.setLastUpdatedDate(createDateBo.getLastUpdatedDate());
		entity.setTimeLimitLeftStr(DateUtil.convertDateToString(DateUtil.addDay(entity.getCreateDate(), NumConstant.NUM_NEGATIVE_ONE), MpConstant.DATE_FORMAT_YYYYMMDDHHMMSS));
		entity.setTimeLimitRightStr(DateUtil.convertDateToString(DateUtil.addDay(entity.getLastUpdatedDate(), NumConstant.NUM_ONE), MpConstant.DATE_FORMAT_YYYYMMDDHHMMSS));
		return entity;
	}

	/**
	 * 线体产出统计
	 * @param dto 入参
	 * @return 业务数据
	 */
	@Override
	public ServiceData getLineProduceInfo(LineProduceInParamDTO dto) throws Exception {

		ServiceData ret = new ServiceData();
		List<LineProduceOutParamDTO> result = new ArrayList<>();

		if(!MesCollectionUtil.isEmpty(dto.getInLineArray())){
			dto.setInLineList(StringUtils.join(dto.getInLineArray(),","));
		}
		// 1.0 (点对点调用) 获取车间产线今天生产的指令集
		List<PsWorkOrderBasicOutDTO> workshopProductionInstructionSet = PlanscheduleRemoteService.getWorkshopProductionInstructionSet(dto);
		// 2.0 计算线体产出
		/*
		 *  2.2 依次处理指令集A记录，计算指令产出
		 *         2.2.1 以“$”字符为条件解析工序组字段PROCESS_GROUP提取最后一个工序代码Code（如P0220$P0221$P0224$P0222，提取得到代码为P0222）
		 *         2.2.2 以指令、线体编号、匹配WIP_SCAN_HISTORY表查找Code=CURR_PROCESS_CODE并且修改时间为指定时间内的记录数量为指令产出数量。
		 *  2.3 该线体的所有指令产出数量之和即输出产线的指定的产出。
		 *
		 */
		if (!MesCollectionUtil.isEmpty(workshopProductionInstructionSet)){

			for (PsWorkOrderBasicOutDTO psWorkOrderBasicOutDTO : workshopProductionInstructionSet) {

				// 封装查询数据
				Map<String, String> queryData = this.queryData(dto, psWorkOrderBasicOutDTO);

				// 统计产出数量
				Integer outputQty = wipScanHistoryRepository.countOutputQty(queryData);

				// 结果封装
				LineProduceOutParamDTO lineProduceOutParamDTO = new LineProduceOutParamDTO();
				lineProduceOutParamDTO.setLineCode(psWorkOrderBasicOutDTO.getLineCode());

				// 点对点调用服务，获取线体名称
				CFLine line = BasicsettingRemoteService.getLine(psWorkOrderBasicOutDTO.getLineCode());
				if (null != line){
					lineProduceOutParamDTO.setLineName(line.getLineName());
				}

				lineProduceOutParamDTO.setStartDate(dto.getStartDate());
				lineProduceOutParamDTO.setEndDate(dto.getEndDate());
				lineProduceOutParamDTO.setOutputQty(String.valueOf(outputQty));

				result.add(lineProduceOutParamDTO);
			}
		}
		// 匹配不存在的结果线体，进行设置
		if(null != workshopProductionInstructionSet){
			this.isNotExistByResult(dto, workshopProductionInstructionSet, result);
		}

		// 相同线体合并
		Map<String, LineProduceOutParamDTO> map = new HashMap<>();
		for (LineProduceOutParamDTO entity : result) {
			if(map.get(entity.getLineCode()) != null) {
				LineProduceOutParamDTO lineProduceOutParamDTO = map.get(entity.getLineCode());
				if(null != lineProduceOutParamDTO){
					lineProduceOutParamDTO.setOutputQty(this.countLineCodeQty(entity, lineProduceOutParamDTO));
					map.put(entity.getLineCode(),lineProduceOutParamDTO);
				}
			} else {
				map.put(entity.getLineCode(),entity);
			}
		}
		// 结果转换
		List<LineProduceOutParamDTO> resultOut = new ArrayList<>();
		resultOut.addAll(map.values());

		ret.setBo(resultOut);
		ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
		return ret;
	}

	/**
	 * 匹配不存在的结果线体，进行设置
	 * @param dto
	 * @param workshopProductionInstructionSet
	 * @param result
	 * @throws Exception
	 * <AUTHOR>
	 */
	private void isNotExistByResult(LineProduceInParamDTO dto, List<PsWorkOrderBasicOutDTO> workshopProductionInstructionSet,
									List<LineProduceOutParamDTO> result) throws Exception{
		if(!StringUtils.isEmpty(dto.getLineCode())){
			List<PsWorkOrderBasicOutDTO> lps = workshopProductionInstructionSet
					.stream().filter(entity -> entity.getLineCode().equals(dto.getLineCode())).collect(Collectors.toList());
			if(MesCollectionUtil.isEmpty(lps)){
				LineProduceOutParamDTO lineProduceOutParamDTO = this.setLineProduceOutParamDtoByEmpty(dto, dto.getLineCode());
				result.add(lineProduceOutParamDTO);
			}
		}
		if(!MesCollectionUtil.isEmpty(dto.getInLineArray())){
			for (String lineCode : dto.getInLineArray()) {
				List<PsWorkOrderBasicOutDTO> lps = workshopProductionInstructionSet
						.stream().filter(entity -> entity.getLineCode().equals(lineCode)).collect(Collectors.toList());
				if(MesCollectionUtil.isEmpty(lps)){
					LineProduceOutParamDTO lineProduceOutParamDTO = this.setLineProduceOutParamDtoByEmpty(dto, lineCode);
					result.add(lineProduceOutParamDTO);
				}
			}
		}
	}

	/**
	 * 封装空结果集
	 * <AUTHOR>
	 */
	private LineProduceOutParamDTO setLineProduceOutParamDtoByEmpty(LineProduceInParamDTO dto, String lineCode) throws Exception{
		// 结果封装
		LineProduceOutParamDTO lineProduceOutParamDTO = new LineProduceOutParamDTO();
		lineProduceOutParamDTO.setLineCode(lineCode);
		// 点对点调用服务，获取线体名称
		CFLine line = BasicsettingRemoteService.getLine(lineCode);
		if (null != line){
			lineProduceOutParamDTO.setLineName(line.getLineName());
		}else{
			lineProduceOutParamDTO.setLineName(Constant.STR_EMPTY);
		}

		lineProduceOutParamDTO.setStartDate(dto.getStartDate());
		lineProduceOutParamDTO.setEndDate(dto.getEndDate());
		lineProduceOutParamDTO.setOutputQty(Constant.STR_NUMBER_ZERO);
		return lineProduceOutParamDTO;
	}


	private String countLineCodeQty(LineProduceOutParamDTO entity, LineProduceOutParamDTO lineProduceOutParamDTO) {
		if (null == entity || lineProduceOutParamDTO == null){
			return "0";
		}
		return String.valueOf(Integer.parseInt(entity.getOutputQty())+Integer.parseInt(lineProduceOutParamDTO.getOutputQty()));
	}


	/**
	 *  封装查询参数
	 * @param dto 前台传入条件
	 * @param psWorkOrderBasicOutDTO 遍历的条件
	 */
	private Map<String,String> queryData(LineProduceInParamDTO dto, PsWorkOrderBasicOutDTO psWorkOrderBasicOutDTO) {

		// 		工序组
		String processCode = this.handleProcessCode(psWorkOrderBasicOutDTO.getProcessGroup());

		//		工单号
		String workOrderNo = psWorkOrderBasicOutDTO.getWorkOrderNo();
		if (null == workOrderNo) {
			workOrderNo = "";
		}

		//      线体编码
		String lineCode = psWorkOrderBasicOutDTO.getLineCode();
		if (null == lineCode) {
			lineCode = "";
		}

		//		开始时间
		String startDate = dto.getStartDate();

		//		结束时间
		String endDate = dto.getEndDate();

		// 工厂ID
		String factoryId = dto.getFactoryId();

		Map<String,String> queryData = new HashMap<>();
		queryData.put("processCode", processCode);
		queryData.put("workOrderNo", workOrderNo);
		queryData.put("lineCode", lineCode);
		queryData.put("startDate", startDate);
		queryData.put("endDate", endDate);
		queryData.put("factoryId", factoryId);
		return queryData;
	}

	/**
	 * 如P0220$P0221$P0224$P0222，提取得到代码为P0222
	 * @param processCode 工序组
	 * @return 工序组字段信息
	 */
	private String handleProcessCode(String processCode){

		if(null == processCode){
			return "";
		}
		String[] sz = processCode.split("\\$");
		return sz[sz.length - 1];
	}

	@Override
	public List<WipScanHistory>  getWipScanHistorySelective(WipScanHistoryDTO dto){
		return wipScanHistoryRepository.getWipScanHistorySelective(dto);
	}

	/**
	 * 线体产出统计2
	 * <AUTHOR>
	 * @param dto 入参
	 * @return 业务数据
	 */
	@Override
	public ServiceData getLineProduceInfo(WipScanParamsDTO dto) throws Exception {
		ServiceData ret = new ServiceData();
		List<LineProduceOutParamDTO> result = new ArrayList<>();
		// 查询结果集
		List<LineProduceOutParamDTO> list = new ArrayList<>();
		for (LineCurrProcessCodeParamDTO lcp :dto.getLineProcessList()) {
			if(StringUtils.isEmpty(lcp.getLineCode())){
				ret.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, RetCode.BUSINESSERROR_MSGID));
				ret.setBo(MessageId.PARAM_IS_NULL);
				return ret;
			}
		}
		for (LineCurrProcessCodeParamDTO lcp : dto.getLineProcessList()) {
			dto.setLineCode(lcp.getLineCode());
			dto.setCurrProcessCodeList(SqlUtils.convertSqlTypeToStrCollection(SqlUtils.parseInForPG(lcp.getCurrProcessCode())));
			list = wipScanHistoryRepository.getLineCurrProcessCodeAndQty(dto);
			result.addAll(list);
		}

		// 产出结果封装
		if(!MesCollectionUtil.isEmpty(result)){
			result.forEach(item ->{
				item.setStartDate(dto.getStartDate());
				item.setEndDate(dto.getEndDate());
			});
		}

		// 匹配不存在的线体
		this.isNotExistByResult(dto, result);

		// 相同线体合并
		Map<String, LineProduceOutParamDTO> map = new HashMap<>();
		for (LineProduceOutParamDTO entity : result) {
			if(map.get(entity.getLineCode()) != null) {
				LineProduceOutParamDTO lineProduceOutParamDTO = map.get(entity.getLineCode());
				if(null != lineProduceOutParamDTO){
					lineProduceOutParamDTO.setOutputQty(this.countLineCodeQty(entity, lineProduceOutParamDTO));
					map.put(entity.getLineCode(),lineProduceOutParamDTO);
				}
			} else {
				map.put(entity.getLineCode(),entity);
			}
		}
		// 结果转换
		List<LineProduceOutParamDTO> resultOut = new ArrayList<>();
		resultOut.addAll(map.values());
		for (LineProduceOutParamDTO lpo : resultOut) {
			// 点对点调用服务，获取线体名称
			CFLine line = BasicsettingRemoteService.getLine(lpo.getLineCode());
			if (null != line){
				lpo.setLineName(line.getLineName());
			}
		}

		ret.setBo(resultOut);
		ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
		return ret;
	}

	/**
	 * 工序时段产出数据接口2
	 *
	 * @param dto
	 * @return
	 */
	@Override
	@RecordLogAnnotation("时段产出")
	public ServiceData getProductQtyByCraftAndLineCode(ProcessProducesParamsDTO dto) throws Exception {
		ServiceData ret = new ServiceData();
		for (LineCurrProcessCodeParamDTO lcp:dto.getLineProcessList()) {
			if(StringHelper.isEmpty(lcp.getLineCode())){
				ret.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, RetCode.BUSINESSERROR_MSGID));
				ret.setBo(MessageId.PARAM_IS_NULL);
				return ret;
			}
		}
		// 最终结果容器
		// 全部线体产出量
		Integer totalOutQty = 0;
		// 1. 获取全部线体总产出量
		for (LineCurrProcessCodeParamDTO lcp: dto.getLineProcessList()) {
			dto.setLineCode(lcp.getLineCode());
			dto.setCurrProcessCodeList(SqlUtils.convertSqlTypeToStrCollection(SqlUtils.parseInForPG(lcp.getCurrProcessCode())));
			// 计算产出量
			totalOutQty = totalOutQty + wipScanHistoryRepository.getTotalLineQty(dto);
		}
		// 时间段工艺段数据容器
		List<CraftSectionDataDTO> craftSectionDataDtoList = new ArrayList<>();
		Map<String, Set<TimeProduceDTO>> resultMap = new HashMap<>();

		// 获取线体列表
		String[]craftSections = dto.getCraftSection().split(",|，");
		for (String craftSection : craftSections) {
			craftSection = craftSection.replace("\'", "").trim();
			Set<TimeProduceDTO> timeProduceDtoSet = new HashSet<>();
			resultMap.put(craftSection, timeProduceDtoSet);
		}
		// 获取时间段
		List<ProcessProducessDataDTO> getTime = this.timeSplit(dto.getInterval(), dto.getStartDate(), dto.getEndDate());
		for (ProcessProducessDataDTO paramsDto : getTime) {
			paramsDto.setFactoryId(dto.getFactoryId());
			Map<String, Integer> map1 = new HashMap<>();
			// 根据线体和产出工序查询工艺段数据
			this.getCraftSectionQtyQueryData(dto, paramsDto, map1);
			List<Map<String, String>> craftSectionOut = new ArrayList<>();
			// 设置无值工艺段
			for (String craftSection : craftSections) {
				craftSection = craftSection.replace("\'", "").trim();
				if(!map1.containsKey(craftSection)){
					addCraftSectionMap(craftSectionOut, craftSection, Constant.STR_0);
					Set<TimeProduceDTO> timeProduceDtoSet = resultMap.get(craftSection);
					TimeProduceDTO tpd = this.getTimeProduceDTO(dto, paramsDto, craftSection, Constant.STR_0);
					timeProduceDtoSet.add(tpd);
					resultMap.put(craftSection, timeProduceDtoSet);
				}
			}
			// 设置工艺段的值
			for (String key: map1.keySet()) {
				addCraftSectionMap(craftSectionOut, key, map1.get(key) + Constant.STRING_EMPTY);
				Set<TimeProduceDTO> timeProduceDtoSet = resultMap.get(key);
				TimeProduceDTO tpd = getTimeProduceDTO(dto, paramsDto, key, map1.get(key) + Constant.STRING_EMPTY);
				timeProduceDtoSet.add(tpd);
				resultMap.put(key, timeProduceDtoSet);
			}
			// 单个时间数据容器
			CraftSectionDataDTO craftSectionDataDto = this.getCraftSectionDataDTO(dto, paramsDto, craftSectionOut);
			craftSectionDataDtoList.add(craftSectionDataDto);
		}
		//工艺段数据
		List<Map<String, Object>> craftList = new ArrayList<>();
		this.getCraftSectionData(resultMap, craftList);

		Map<String, Object> result = new HashMap<>();
		result.put("tatalOutQty", totalOutQty);
		result.put("craftSectionData", craftList);

		ret.setBo(result);
		ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
		return ret;
	}

	private CraftSectionDataDTO getCraftSectionDataDTO(ProcessProducesParamsDTO dto, ProcessProducessDataDTO paramsDto, List<Map<String, String>> craftSectionOut) {
		CraftSectionDataDTO craftSectionDataDto = new CraftSectionDataDTO();
		craftSectionDataDto.setStartTime(paramsDto.getStartDate());
		craftSectionDataDto.setEndTime(paramsDto.getEndDate());
		craftSectionDataDto.setInterval(dto.getInterval());
		craftSectionDataDto.setCraftSectionOut(craftSectionOut);
		return craftSectionDataDto;
	}

	private TimeProduceDTO getTimeProduceDTO(ProcessProducesParamsDTO dto, ProcessProducessDataDTO paramsDto, String craftSection, String str0) {
		TimeProduceDTO tpd = new TimeProduceDTO();
		tpd.setCraft(craftSection);
		tpd.setStartDate(paramsDto.getStartDate());
		tpd.setEndDate(paramsDto.getEndDate());
		tpd.setInterval(dto.getInterval() + Constant.STRING_EMPTY);
		tpd.setQty(str0);
		return tpd;
	}

	private void addCraftSectionMap(List<Map<String, String>> craftSectionOut, String craftSection, String str0) {
		Map<String, String> map = new HashMap<>();
		map.put("craftSection", craftSection);
		map.put("outQty", str0);
		craftSectionOut.add(map);
	}

	/**
	 * 设置工艺段时间数据
	 * @param resultMap
	 * @param craftList
	 */
	private void getCraftSectionData(Map<String, Set<TimeProduceDTO>> resultMap, List<Map<String, Object>> craftList) {
		if(null != resultMap){
			for (String craft: resultMap.keySet()) {
				Map<String, Object> map = new HashMap<>();
				map.put("craftSection", craft);
				map.put("data", resultMap.get(craft));
				craftList.add(map);
			}
		}
	}

	/**
	 * 当前任务产出
	 *
	 * @param dto
	 * @return ServiceData
	 */
	@Override
	public ServiceData getCurrentTaskOutPut(WipScanParamsDTO dto) throws Exception {
		ServiceData ret = new ServiceData();

		/**
		 * --线体、产出工序、输入开始时间、输入结束时间
		 * --1、以线体编号为条件搜索WIP_SCAN_HISTORY表匹配对应线体
		 * --并且修改时间在开始时间及结束时间范围内并以修改时间排序，取第一条记录的WORK_ORDER_NO。
		 * -- 2、以WORK_ORDER_NO查询WIP_SCAN_HISTORY表，
		 * --产出工序=CURR_PROCESS_CODE（当产出工序为空默认所有，同时支持多工序，工序间用逗号隔开）
		 * --并且LAST_PROCESS=Y,（CURR_PROCESS_CODE+SN）不重复的记录数量为产出统计数
		 * --3、以WORK_ORDER_NO查询PS_WORK_ORDER_BASIC表输出任务产出数count+WORK_ORDER_NO+workOrderQty+outputQty+inputQty
		 * --  当前任务产出（新增）
		 */
		// 查询结果集
		List<WorkOrderNoQtyVO> result = new ArrayList<>();
		for (LineCurrProcessCodeParamDTO lcp :dto.getLineProcessList()) {
			if(StringUtils.isEmpty(lcp.getLineCode())){
				ret.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, RetCode.BUSINESSERROR_MSGID));
				ret.setBo(MessageId.PARAM_IS_NULL);
				return ret;
			}
		}
		for (LineCurrProcessCodeParamDTO lcp : dto.getLineProcessList()) {
			WorkOrderNoQtyVO wonq = new WorkOrderNoQtyVO();
			dto.setLineCode(lcp.getLineCode());
			dto.setCurrProcessCodeList(SqlUtils.convertSqlTypeToStrCollection(SqlUtils.parseInForPG(lcp.getCurrProcessCode())));
			String workOrderNo = wipScanHistoryRepository.getCurrentTaskWorkOrderNo(dto);


			wonq.setLineCode(lcp.getLineCode());
			wonq.setCount(Constant.INT_0);
			wonq.setInputQty(Constant.INT_0);
			wonq.setWorkOrderQty(Constant.INT_0);
			wonq.setOutputQty(Constant.INT_0);
			wonq.setWorkOrderNo(Constant.STRING_EMPTY);
			if(!StringUtils.isEmpty(workOrderNo)){
				dto.setWorkOrderNo(workOrderNo);
				String qty = wipScanHistoryRepository.countCurrentTaskQty(dto);
				wonq.setWorkOrderNo(workOrderNo);
				if(!StringUtils.isEmpty(qty)){
					wonq.setCount(Integer.parseInt(qty));
				}

				// 以WORK_ORDER_NO查询PS_WORK_ORDER_BASIC表输出任务产出数count+WORK_ORDER_NO+workOrderQty+outputQty+inputQty
				PsWorkOrderBasic workOrderBasic = PlanscheduleRemoteService.findWorkOrder(workOrderNo);
				if(null != workOrderBasic){
					wonq.setInputQty(workOrderBasic.getInputQty().intValue());
					wonq.setWorkOrderQty(workOrderBasic.getWorkOrderQty().intValue());
					wonq.setOutputQty(workOrderBasic.getOutputQty().intValue());
				}
			}
			result.add(wonq);
		}
		ret.setBo(result);
		ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
		return ret;
	}


	/**
	 * 根据线体和工艺阶段查询数据
	 * @param dto
	 * @param paramsDto
	 * @param map1
	 */
	private void getCraftSectionQtyQueryData(ProcessProducesParamsDTO dto, ProcessProducessDataDTO paramsDto, Map<String, Integer> map1) {
		for (LineCurrProcessCodeParamDTO lcp: dto.getLineProcessList()) {
			paramsDto.setLineCode(lcp.getLineCode());
			paramsDto.setCurrProcessCodeList(SqlUtils.convertSqlTypeToStrCollection(SqlUtils.parseInForPG(lcp.getCurrProcessCode())));
			List<ProcessProducesParamsDTO> list = wipScanHistoryRepository.getCraftSectionQty(paramsDto);
			if(!MesCollectionUtil.isEmpty(list)){
				for (ProcessProducesParamsDTO p: list) {
					if(map1.containsKey(p.getCraftSection())){
						Integer qty = map1.get(p.getCraftSection()) + p.getOutQty();
						map1.put(p.getCraftSection(), qty);
					}else{
						map1.put(p.getCraftSection(), p.getOutQty());
					}
				}
			}
		}
	}


	/**
	 * 匹配不存在的结果线体，进行设置
	 * @param dto
	 * @param result
	 * @throws Exception
	 * <AUTHOR>
	 */
	private void isNotExistByResult(WipScanParamsDTO dto, List<LineProduceOutParamDTO> result) throws Exception{
		if(!StringUtils.isEmpty(dto.getLineCode())){
			List<LineProduceOutParamDTO> lps = result.stream()
					.filter(entity -> entity.getLineCode().equals(dto.getLineCode())).collect(Collectors.toList());
			if(MesCollectionUtil.isEmpty(lps)){
				LineProduceOutParamDTO lineProduceOutParamDTO = this.setLineProduceOutParamDtoByEmpty(dto, dto.getLineCode());
				result.add(lineProduceOutParamDTO);
			}
		}
		if(!MesCollectionUtil.isEmpty(dto.getLineProcessList())){
			for (LineCurrProcessCodeParamDTO lcp : dto.getLineProcessList()) {
				List<LineProduceOutParamDTO> lps = result
						.stream().filter(entity -> entity.getLineCode().equals(lcp.getLineCode())).collect(Collectors.toList());
				if(MesCollectionUtil.isEmpty(lps)){
					LineProduceOutParamDTO lineProduceOutParamDTO = this.setLineProduceOutParamDtoByEmpty(dto, lcp.getLineCode());
					result.add(lineProduceOutParamDTO);
				}
			}
		}
	}

	/**
	 * 封装空结果集
	 * <AUTHOR>
	 */
	private LineProduceOutParamDTO setLineProduceOutParamDtoByEmpty(WipScanParamsDTO  dto, String lineCode) throws Exception{
		// 结果封装
		LineProduceOutParamDTO lineProduceOutParamDTO = new LineProduceOutParamDTO();
		lineProduceOutParamDTO.setLineCode(lineCode);
		lineProduceOutParamDTO.setStartDate(dto.getStartDate());
		lineProduceOutParamDTO.setEndDate(dto.getEndDate());
		lineProduceOutParamDTO.setOutputQty(Constant.STR_NUMBER_ZERO);
		return lineProduceOutParamDTO;
	}

	/**
	 * 时间段切割时间
	 *
	 * @param interval     间隔时长(1-N)小时,例: 2
	 * @param startDateStr 开始时间 ：字符串格式必须为(yyyy-MM-dd HH:mm:ss),例: 1999-01-01 00:00:00
	 * @param endDateStr   结束时间 ：字符串格式必须为(yyyy-MM-dd HH:mm:ss),例: 1999-01-01 00:00:00
	 * @return Map 时间片段
	 */
	private List<ProcessProducessDataDTO> timeSplit(Integer interval, String startDateStr, String endDateStr) {


		List<ProcessProducessDataDTO> processProducessDateList = new ArrayList<>();

		DateFormat format = new SimpleDateFormat(MpConstant.DATE_FORMAT_YYYYMMDDHHMMSS);
		Date startDate = null;
		Date endDate = null;
		Instant startInstant = null;
		Instant endInstant = null;
		int hours = 0;
		try {
			startDate = format.parse(startDateStr);
			endDate = format.parse(endDateStr);
			startInstant = startDate.toInstant();
			endInstant = endDate.toInstant();
			hours = (int) Duration.between(startInstant, endInstant).toHours();
		} catch (ParseException e) {
			e.printStackTrace();
		}

		this.getHours(startDate, endDate, hours);
		int eay = hours % interval;
		if (eay != 0) {
			eay = hours / interval + 1;
		} else {
			eay = hours / interval;
		}
		if(null != endDate){
			this.timeListAssignment(interval, processProducessDateList, endDate, startInstant, eay);
		}
		return processProducessDateList;
	}

	private void timeListAssignment(Integer interval, List<ProcessProducessDataDTO> processProducessDateList, Date endDate, Instant startInstant, int eay) {
		DateFormat format = new SimpleDateFormat(MpConstant.DATE_FORMAT_YYYYMMDDHHMMSS);
		for (int i = 1; i <= eay; i++) {

			ProcessProducessDataDTO processProducessDataDto = new ProcessProducessDataDTO();

			Date startDateTemp = null;
			Date endDateTemp = null;
			if (null != startInstant) {
				startDateTemp = new Date(startInstant.plus(Duration.ofHours((i - 1) * interval)).toEpochMilli());
				endDateTemp = new Date(startInstant.plus(Duration.ofHours((i) * interval)).toEpochMilli());
			}
			String startStrTemp = null;
			String endStrTemp = null;
			if (null != startDateTemp) {
				startStrTemp = format.format(startDateTemp);
			}
			if (null != endDateTemp) {
				endStrTemp = format.format(endDateTemp);
			}

			if (i == eay) {
				processProducessDataDto.setKey(i);
				processProducessDataDto.setStartDate(startStrTemp);
				processProducessDataDto.setEndDate(format.format(endDate));
			} else {
				processProducessDataDto.setKey(i);
				processProducessDataDto.setStartDate(startStrTemp);
				processProducessDataDto.setEndDate(endStrTemp);
			}
			processProducessDateList.add(processProducessDataDto);
		}
	}

	private void getHours(Date startDate, Date endDate, int hours) {
		String[] startSplit = null;
		String[] endSplit = null;
		if (null != startDate && null != endDate) {
			startSplit = new SimpleDateFormat(MpConstant.DATE_FORMAT_HHMMSS).format(startDate).split(":");
			endSplit = new SimpleDateFormat(MpConstant.DATE_FORMAT_HHMMSS).format(endDate).split(":");
		}
		if (null != startSplit && startSplit.length >= NumConstant.NUM_THREE && null != endSplit && endSplit.length >= NumConstant.NUM_THREE) {
			int startMin = Integer.parseInt(startSplit[1]);
			int endMin = Integer.parseInt(endSplit[1]);
			int startSecond = Integer.parseInt(startSplit[NumConstant.NUM_TWO]);
			int endSecond = Integer.parseInt(endSplit[NumConstant.NUM_TWO]);
			if (startMin > endMin) {
				hours++;
			} else if (startMin == endMin && startSecond > endSecond) {
				hours++;
			}
		}
	}

	/**
	 * 更新WipScanHistory
	 * @param condition
	 * @return
	 */
	@Override
	public int updateWipScanHistory(WipScanHistoryDTO condition) throws Exception{
		return wipScanHistoryRepository.updateWipScanHistory(condition);
	}

	/**
	 * 更新parentSn
	 * @param condition
	 * @return
	 */
	@Override
	@RedisDistributedLockAnnotation(redisPrefix = "updateParentSnBySmtScanID", factoryId = true, redisLockTime = 1200, redisLockParam =
			{@RedisLockParamAnnotation(paramName = "condition", propertiesString = "smtScanId")}, lockFailMsgZh = MessageId.UNBINDING_PLEASE_WAIT,
			lockFailMsgEn = "The bill No is being update. Please try again later.")
	public int updateParentSn(WipScanHistoryDTO condition) throws Exception{
		return wipScanHistoryRepository.updateParentSn(condition);
	}


	/**
	 * 根据条件查询wip_scan_history表中parent_sn不为空且sn不以P开头的数据（需对sn进行去重处理)
	 * @param condition
	 * @return
	 */
	@Override
	public List<WipScanHistory> queryWipScanHistoryList(WipScanHistory condition) throws Exception{
		return wipScanHistoryRepository.queryWipScanHistoryList(condition);
	}

	/**
	 * 获取条码入库最早的时间
	 * @param snList
	 * @return
	 * @throws Exception
	 */
	@Override
	public List<WipScanHistory> getTheEarliestInboundScanningTime(List<String> snList) throws Exception{
		List<WipScanHistory> wipScanHistoryList = new ArrayList<>();
		if (CollectionUtils.isEmpty(snList)) {
			return wipScanHistoryList;
		}
		for (List<String> snTempList : CommonUtils.splitList(snList, NumConstant.NUM_999)) {
			List<WipScanHistory> tempList = wipScanHistoryRepository.getTheEarliestInboundScanningTime(snTempList);
			if(!CollectionUtils.isEmpty(tempList)){
				wipScanHistoryList.addAll(tempList);
			}
		}
		return wipScanHistoryList;
	}


	/**
	 * 根据条件查询wip_scan_history表中parent_sn不为空且sn不以P开头的数据（需对sn进行去重处理)
	 * @param condition
	 * @return
	 */
	@Override
	public PageRows<WipScanHistoryDTO> queryWipScanHistoryListPage(WipScanHistoryDTO condition) throws Exception{
		setStartDateAndEndDate(condition);
		PageRows<WipScanHistoryDTO> page = new PageRows<>();
		long total = wipScanHistoryRepository.queryWipScanHistoryListCount(condition);
		page.setTotal(total);
		condition.setStartRow((condition.getPageNo() - Constant.INT_1)
				* condition.getPageSize() + Constant.INT_1);
		condition.setEndRow(condition.getPageNo() * condition.getPageSize());
		List<WipScanHistoryDTO> list = wipScanHistoryRepository.queryWipScanHistoryListPage(condition);
		page.setRows(list);
		return page;
	}

	/**
	 * 拼版绑定关系查询
	 * @param dto
	 * @return
	 */
	@Override
	public Page<WipScanHistoryDTO> getWipScanHistoryListPage(WipScanHistoryDTO dto){
		Page<WipScanHistoryDTO> pageInfo = new Page<>(dto.getPage(), dto.getRows());
		pageInfo.setParams(dto);
		// 获取分页
		List<WipScanHistoryDTO> list = wipScanHistoryRepository.getWipScanHistoryListPage(pageInfo);
		pageInfo.setRows(list);
		return pageInfo;
	}

	//设置时间范围
	private void setStartDateAndEndDate(WipScanHistoryDTO condition) throws ParseException {
		String dateStr = condition.getLastUpdatedDateRange();
		if (StringHelper.isNotEmpty(dateStr)) {
			List<Date> dataList = CommonUtils.parseDateFromStr(dateStr);
			if (dataList != null && dataList.size() == NumConstant.NUM_ONE) {
				condition.setStart(dataList.get(NumConstant.NUM_ZERO));
			} else if (dataList != null && dataList.size() > NumConstant.NUM_ONE) {
				condition.setStart(dataList.get(NumConstant.NUM_ZERO));
				//将时间后缀拼由默认的00:00:00接至23:59:59
				Calendar c = Calendar.getInstance();
				c.setTime(dataList.get(NumConstant.NUM_ONE));
				c.add(Calendar.DATE, 1);
				c.add(Calendar.SECOND,-1);
				condition.setEnd(c.getTime());
			}
		}
	}

	@Override
	public List<BoardInstructionCycleDataCreateDTO> getFirstDateOfProdplanId(List<String> prodplanIdList) {
		List<BoardInstructionCycleDataCreateDTO> list = new ArrayList<>();
		if (CollectionUtils.isEmpty(prodplanIdList)) {
			return list;
		}
		// 分批
		List<List<String>> listOfProdplanIdList = CommonUtils.splitList(prodplanIdList,Constant.INT_20);
		for (List<String> listOfList : listOfProdplanIdList) {
			List<BoardInstructionCycleDataCreateDTO> tempList = wipScanHistoryRepository.getFirstDateOfProdplanId(listOfList);
			if (!CollectionUtils.isEmpty(tempList)) {
				list.addAll(tempList);
			}
		}
		return list;
	}

	/**
	 * 获取批次各主工序完成日期
	 */
	@Override
	public List<BoardInstructionCycleDataCreateDTO> getLastDateOfProdplanId(List<String> prodplanIdList) {
		List<BoardInstructionCycleDataCreateDTO> res = new LinkedList<>();
		if (CollectionUtils.isEmpty(prodplanIdList)) {
			return res;
		}
		// 分批
		List<List<String>> listOfProdplanIdList = CommonUtils.splitList(prodplanIdList,NumConstant.NUM_TEN);
		for (List<String> listOfList : listOfProdplanIdList) {
			List<BoardInstructionCycleDataCreateDTO> tempList = wipScanHistoryRepository.getLastDateOfProdplanId(listOfList);
			res.addAll(tempList);
		}
		return res;
	}

	@Override
	public List<BoardInstructionCycleDataCreateDTO> getDateOfProdplanId(List<String> prodplanIdList) {
		List<BoardInstructionCycleDataCreateDTO> dtoList = new ArrayList<>();
		// 分批查询
		List<List<String>> listList = CommonUtils.splitList(prodplanIdList);
		for (List<String> stringList : listList) {
			List<BoardInstructionCycleDataCreateDTO> tempList = wipScanHistoryRepository.getDateOfProdplanId(stringList);
			if (!CollectionUtils.isEmpty(tempList)) {
				dtoList.addAll(tempList);
			}
		}
		return dtoList;
	}

	@Override
	public List<BoardInstructionCycleInfoDTO> getPartData(List<String> prodplanIdList) throws Exception {
		if(CollectionUtils.isEmpty(prodplanIdList)){
			return new ArrayList<>();
		}
		Map<String, List<CtRouteDetailDTO>> routeMap = barcodeLockDetailService.getRouteInfoByProdPlanIdList(prodplanIdList);
		if (routeMap == null) {
			return new ArrayList<>();
		}
		Map<String, Object> lookupType = new HashMap<>();
		lookupType.put(Constant.FIELD_LOOKUP_TYPE, Constant.LOOKUP_VALUE_221103);
		// 获取整机工序数据字典
		List<SysLookupTypesDTO> types = BasicsettingRemoteService.getSysLookUpValue(lookupType);
		if (CollectionUtils.isEmpty(types)) {
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SYS_LOOK_UP_TYPE_NOT_CONFIGURE);
		}
		List<String> lookUpTypes = new ArrayList<>();
		for (SysLookupTypesDTO type : types) {
			lookUpTypes.add(type.getLookupMeaning());
		}
		List<String> needList = this.getNeedList(prodplanIdList, routeMap, lookUpTypes);
		if(CollectionUtils.isEmpty(needList)){
			return new ArrayList<>();
		}
		List<BoardInstructionCycleInfoDTO> infoDTOList = new ArrayList<>();
		// 分批查询
		List<List<String>> splitList = CommonUtils.splitList(needList,Constant.INT_20);
		for (List<String> list : splitList) {
			List<BoardInstructionCycleInfoDTO> tempList = wipScanHistoryRepository.getPartData(list,lookUpTypes);
			if (!CollectionUtils.isEmpty(tempList)) {
				infoDTOList.addAll(tempList);
			}
		}
		return infoDTOList;
	}

	private  List<String> getNeedList(List<String> prodplanIdList, Map<String, List<CtRouteDetailDTO>> routeMap, List<String> lookUpTypes) {
		List<String> needList = new ArrayList<>();
		for (String prodplanId : prodplanIdList) {
			List<CtRouteDetailDTO> ctRouteDetailDTOList = routeMap.get(prodplanId);
			if(CollectionUtils.isEmpty(ctRouteDetailDTOList)){
				continue;
			}
			//按工序序号排序
			ctRouteDetailDTOList = ctRouteDetailDTOList.stream().sorted(Comparator.comparing(CtRouteDetailDTO::getProcessSeq)).collect(Collectors.toList());
			List<String> processCodeList = ctRouteDetailDTOList.stream().filter(e->StringUtils.isNotEmpty(e.getNextProcess())).map(e->e.getNextProcess()).collect(Collectors.toList());
			if(CollectionUtils.isEmpty(processCodeList)){
				continue;
			}
			//不是整机工序也不是入库的是部件工序
			List<String> partProcessCodeList =  processCodeList.stream().filter(e->!lookUpTypes.contains(e) && !StringUtils.equals(Constant.PROCESS_CODE_WH,e)).distinct().collect(Collectors.toList());
			if(CollectionUtils.isEmpty(partProcessCodeList)){
				continue;
			}
			for (int i = NumConstant.NUM_ZERO; i < ctRouteDetailDTOList.size(); i++) {
				//部件工序-》整机工序
				if(partProcessCodeList.contains(ctRouteDetailDTOList.get(i).getNextProcess()) && (i+NumConstant.NUM_ONE)<ctRouteDetailDTOList.size() && lookUpTypes.contains(ctRouteDetailDTOList.get(i+NumConstant.NUM_ONE).getNextProcess())){
					needList.add(prodplanId);
				}
			}
		}
		return needList;
	}

	@Override
	public Date getMinCreateDate() {
		return wipScanHistoryRepository.getMinCreateDate();
	}

	@Override
	public Date getMaxCreateDate() {
		return wipScanHistoryRepository.getMaxCreateDate();
	}
	
	@Override
	public List<WipScanHistory> statPsnAndSn(String startDate, String endDate, int startRow, int endRow) {
		return wipScanHistoryRepository.statPsnAndSn(startDate, endDate, startRow, endRow);
	}

	/**
	 * 累积扫描数量 指令、子工序、工站 去重
	 */
	@Override
	public Long getCumulativeScans(String workOrderNo, String currProcessCode, String workStation) {
		Long cumulativeScans = wipScanHistoryRepository.getCumulativeScans(workOrderNo, currProcessCode, workStation);
		// 查询已解绑的
		List<WipInfoDelLogDTO> delLastScans = wipInfoDelLogRepository.getLastScan(workOrderNo, currProcessCode, workStation);
		if (CollectionUtils.isEmpty(delLastScans)) {
			return cumulativeScans;
		}
		// 查询解绑条码的最后扫描时间
		List<WipScanHistory> lastScanBySn = wipScanHistoryRepository.getLastScanBySnWorkOrder(workOrderNo, currProcessCode, workStation);
		// 比对最终是扫描还是解绑
		Set<String> delSnFinal = new HashSet<>();
		for (WipInfoDelLogDTO del : delLastScans) {
			Optional<WipScanHistory> first = lastScanBySn.stream().filter(p -> del.getSn().equals(p.getSn())).findFirst();
			if (first.isPresent() && first.get().getCreateDate().before(del.getCreateDate())) {
				delSnFinal.add(del.getSn());
			}
		}
		return cumulativeScans - delSnFinal.size();
	}

	/**
	 * 更新回写标识
	 *
	 * @param prodList
	 */
	@Override
	public void updateIsWriteScanBack(List<String> prodList) {
		prodList = prodList.stream().filter(StringUtils::isNotBlank)
				.filter(item->item.length()>=Constant.INT_7)
				.collect(Collectors.toList());
		if (CollectionUtils.isEmpty(prodList)) {
			return;
		}
		for (String prodId : prodList) {
			wipScanHistoryRepository.updateIsWriteScanBack(prodId);
		}
	}

	@Override
	public List<WipScanHistoryDTO> findOnLineTimeOfProcess(List<String> prodPlanIdList) {
		return wipScanHistoryRepository.findOnLineTimeOfProcess(prodPlanIdList);
	}

	@Override
	public List<WipScanHistoryDTO> countQtyGroupByProdAndProcess(Date yesterday, List<String> prodPlanIdList, boolean stationFlag) {
		return wipScanHistoryRepository.countQtyGroupByProdAndProcess(yesterday, prodPlanIdList, stationFlag);
	}

	/**
	 * 根据传入的条码和主工序获取对应工序扫描数据
	 *
	 * @param wipScanHistoryList
	 */
	@Override
	public List<WipScanHistory> getWipScanHistoryForTestCraftSection(List<WipScanHistory> wipScanHistoryList) {
		List<WipScanHistory> resultList = new ArrayList<>();
		List<WipScanHistory> nextProcessList = new ArrayList<>();

		if (CollectionUtils.isEmpty(wipScanHistoryList)){
			return resultList;
		}
		for (List<WipScanHistory> splitList : CommonUtils.splitList(wipScanHistoryList , NumConstant.NUM_500)) {
			// 获取条码对应下工序的扫描信息
			List<WipScanHistory> tempList = wipScanHistoryRepository.getWipScanHistoryForTestCraftSection(splitList);
			if(!CollectionUtils.isEmpty(tempList)){
				nextProcessList.addAll(tempList);
			}
		}
		if (CollectionUtils.isEmpty(nextProcessList)){
			return resultList;
		}
		Map<String, WipScanHistory> hasNextProcessMap = nextProcessList.stream().collect(Collectors.toMap(i -> i.getSn() + Constant.ADD_FLAG + i.getCurrProcessCode(), a -> a, (k1, k2) -> k1));
		List<WipScanHistory> hasNextSns = new ArrayList<>();
		for (WipScanHistory wipScanHistory : wipScanHistoryList) {
			if(!hasNextProcessMap.containsKey(wipScanHistory.getSn() + Constant.ADD_FLAG + wipScanHistory.getNextProcess())) {
				continue;
			}
			wipScanHistory.setNextProcess(wipScanHistory.getCurrProcessCode());
			hasNextSns.add(wipScanHistory);
		}
		for (List<WipScanHistory> splitHasNextSns : CommonUtils.splitList(hasNextSns , NumConstant.NUM_500)) {
			// 获取存在下工序扫描记录条码对应测试工序的扫描信息
			List<WipScanHistory> tempHasNextList = wipScanHistoryRepository.getWipScanHistoryForTestCraftSection(splitHasNextSns);
			if(!CollectionUtils.isEmpty(tempHasNextList)){
				resultList.addAll(tempHasNextList);
			}
		}
			return resultList;
	}

	@Override
	public List<ParentsnAndSn> getParentSnBySnAndProdplanId(List<String> snList, String prodplanId) {
		return wipScanHistoryRepository.getParentSnBySnAndProdplanId(snList, prodplanId);
	}

	@Override
	public List<WipScanHistoryDTO> countQtyForScrapOrMaintence(Date yesterday, List<String> prodPlanIdList, boolean preProcessCodeFlag) {
		return wipScanHistoryRepository.countQtyForScrapOrMaintence(yesterday, prodPlanIdList, preProcessCodeFlag);
	}
}
