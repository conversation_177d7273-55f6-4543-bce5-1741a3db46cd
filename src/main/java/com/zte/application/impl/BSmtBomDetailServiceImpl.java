/**
 * 项目名称 : ${project_name}
 * 创建日期 : ${date}
 * 修改历史 :
 *   1. [${date}] 创建文件 by ${user}
 **/
package com.zte.application.impl;

import com.google.common.collect.Maps;
import com.zte.application.BSmtBomDetailService;
import com.zte.application.BSmtBomHeaderService;
import com.zte.application.PkCodeInfoService;
import com.zte.application.SmtMachineDistributeScanService;
import com.zte.application.SmtMachineMTLHistoryLService;
import com.zte.application.SmtMachineMaterialMoutingService;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MesCollectionUtil;
import com.zte.common.utils.MpConstant;
import com.zte.common.utils.NumConstant;
import com.zte.common.utils.ObtainRemoteServiceDataUtil;
import com.zte.consts.CommonConst;
import com.zte.domain.model.BPcbLocationDetail;
import com.zte.domain.model.BSmtBomDetail;
import com.zte.domain.model.BSmtBomDetailRepository;
import com.zte.domain.model.BSmtBomHeader;
import com.zte.domain.model.BSmtBomHeaderRepository;
import com.zte.domain.model.BsItemInfo;
import com.zte.domain.model.PsWorkOrderBasic;
import com.zte.domain.model.PsWorkOrderSmt;
import com.zte.domain.model.SmtLocationInfo;
import com.zte.domain.model.SmtMachineMTLHistoryL;
import com.zte.domain.model.SmtMachineMaterialMouting;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.infrastructure.remote.ProductionDeliveryRemoteService;
import com.zte.interfaces.assembler.BSmtBomDetailAssembler;
import com.zte.interfaces.dto.AgeingInfoFencePointToPointQueryItemInfoDTO;
import com.zte.interfaces.dto.BBomDetailDTO;
import com.zte.interfaces.dto.BPcbLocationDetailDTO;
import com.zte.interfaces.dto.BSmtBomDetailChildDTO;
import com.zte.interfaces.dto.BSmtBomDetailDTO;
import com.zte.interfaces.dto.BSmtBomDetailVirtualDTO;
import com.zte.interfaces.dto.EmEqpPdCountsDTO;
import com.zte.interfaces.dto.ItemCheckRelatedInfoDTO;
import com.zte.interfaces.dto.PdaFeederBindDTO;
import com.zte.interfaces.dto.PkCodeInfoDTO;
import com.zte.interfaces.dto.PsWorkOrderBasicDTO;
import com.zte.interfaces.dto.PsWorkOrderDTO;
import com.zte.interfaces.dto.SmtLocationInfoDTO;
import com.zte.interfaces.dto.SmtMachineDistributeScanEntityDTO;
import com.zte.interfaces.dto.SysLookupValuesDTO;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.util.string.StringHelper;
import com.zte.springbootframe.common.exception.MesBusinessException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.zte.common.utils.NumConstant.DOUBLE_1;


/**
 * // TODO 添加类/接口功能描述
 *
 * <AUTHOR>
 **/
@Service
public class BSmtBomDetailServiceImpl implements BSmtBomDetailService {

	@Autowired
	private BSmtBomDetailRepository bSmtBomDetailRepository;

	@Autowired
	private BSmtBomHeaderService bSmtBomHeaderService;

	@Autowired
	private SmtMachineMaterialMoutingService smtMachineMaterialMoutingService;
	@Autowired
	private BSmtBomHeaderRepository bSmtBomHeaderRepository;

	@Autowired
	private SmtMachineMTLHistoryLService smtMachineMTLHistoryLService;

	@Autowired
	private PkCodeInfoService pkCodeInfoService;

	@Autowired
	private SmtMachineDistributeScanService smtMachineDistributeScanService;

	@Autowired
	private CenterfactoryRemoteService centerfactoryRemoteService;
	public void setbSmtBomDetailRepository(BSmtBomDetailRepository bSmtBomDetailRepository) {
		this.bSmtBomDetailRepository = bSmtBomDetailRepository;
	}


	public void setBSmtBomHeaderService(BSmtBomHeaderService bSmtBomHeaderService) {
		this.bSmtBomHeaderService = bSmtBomHeaderService;
	}
	/**
	 * 增加实体数据
	 *
	 * @param record
	 **/
	@Override
	public int insertBSmtBomDetail(BSmtBomDetail record) {

		return bSmtBomDetailRepository.insertBSmtBomDetail(record);
	}

	/**
	 * 有选择性的增加实体数据
	 *
	 * @param record
	 **/
	@Override
	public int insertBSmtBomDetailSelective(BSmtBomDetail record) {
		return bSmtBomDetailRepository.insertBSmtBomDetailSelective(record);
	}

	/**
	 * 根据主键删除实体数据
	 *
	 * @param record
	 **/
	@Override
	public int deleteBSmtBomDetailById(BSmtBomDetail record) {
		return bSmtBomDetailRepository.deleteBSmtBomDetailById(record);
	}

	/**
	 * 有选择性的更新实体数据
	 *
	 * @param record
	 **/
	@Override
	public int updateBSmtBomDetailByIdSelective(BSmtBomDetail record) {
		return bSmtBomDetailRepository.updateBSmtBomDetailByIdSelective(record);
	}

	/**
	 * 根据主键更新实体数据
	 *
	 * @param record
	 **/
	@Override
	public int updateBSmtBomDetailById(BSmtBomDetail record) {
		return bSmtBomDetailRepository.updateBSmtBomDetailById(record);
	}

	/**
	 * 根据主键查询实体信息
	 *
	 * @param record
	 * @return BSmtBomDetail
	 **/
	@Override
	public BSmtBomDetail selectBSmtBomDetailById(BSmtBomDetail record) {
		return bSmtBomDetailRepository.selectBSmtBomDetailById(record);
	}

	/**
	 * 根据条件查询实体信息
	 *
	 * @param record
	 * @return list
	 **/
	@Override
	public List<BSmtBomDetail> getList(Map<String, Object> record, String orderField, String order) {
		record.put("enabledFlag", CommonConst.ENABLE_FLAG_Y);
		return bSmtBomDetailRepository.getList(record);
	}

	/**
	 * 获取指令对应虚拟站位
	 *
	 * @param dto
	 * @return list
	 **/
	@Override
	public List<BSmtBomDetail> getVirtualLocationNoData(BSmtBomDetailDTO dto)throws Exception {
		if(StringUtils.isEmpty(dto.getCfgHeaderId()) || StringUtils.isEmpty(dto.getVirtualFlag())|| StringUtils.isEmpty(dto.getWorkOrder())){
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,MessageId.PARAM_MISSING);
		}
		List<BSmtBomDetail> bSmtBomDetailList=bSmtBomDetailRepository.getVirtualLocationNoData(dto);
		if(CollectionUtils.isEmpty(bSmtBomDetailList)){
			return bSmtBomDetailList;
		}
		//获取对应指令信息
		PsWorkOrderBasic psWorkOrderBasic=PlanscheduleRemoteService.findWorkOrder(dto.getWorkOrder());
		//设置位号
		BPcbLocationDetailDTO bPcbLocationDetail=new BPcbLocationDetailDTO();
		bPcbLocationDetail.setPage(NumConstant.LONG_ONE);
		bPcbLocationDetail.setRows(NumConstant.LONG_5000);
		if(Objects.nonNull(psWorkOrderBasic)){
			bPcbLocationDetail.setCraftSection(psWorkOrderBasic.getCraftSection());
			bPcbLocationDetail.setProductCode(psWorkOrderBasic.getItemNo());
		}
		List<BPcbLocationDetail> bPcbLocationDetailList=new ArrayList<>();
		PageRows<BPcbLocationDetail> bPcbLocationDetailListPage=centerfactoryRemoteService.getBPcbLocPage(bPcbLocationDetail);
		if(bPcbLocationDetailListPage == null || bPcbLocationDetailListPage.getTotal()<=NumConstant.LONG_ZERO){
			return bSmtBomDetailList;
		}
		bPcbLocationDetailList.addAll(bPcbLocationDetailListPage.getRows());
		long total=bPcbLocationDetailListPage.getTotal();
		//超过5000的
		forTotalMoreQuery(psWorkOrderBasic, bPcbLocationDetailList, total);

		Map<String, List<BPcbLocationDetail>> map = bPcbLocationDetailList.stream().collect(Collectors.groupingBy(BPcbLocationDetail::getItemCode));
		if(map == null ||map.size() <=NumConstant.NUM_ZERO){
			return bSmtBomDetailList;
		}
		bSmtBomDetailList.forEach(p->{
			setPointLoc(map, p);
		});
		return bSmtBomDetailList;
	}

	private void setPointLoc(Map<String, List<BPcbLocationDetail>> map, BSmtBomDetail p) {
		List<BPcbLocationDetail> bPcbLocationDetails= map.get(p.getItemCode());
		if(!CollectionUtils.isEmpty(bPcbLocationDetails)) {
			List<String> pointLocList = bPcbLocationDetails.stream().map(BPcbLocationDetail::getPointLoc).collect(Collectors.toList());
			if(StringUtils.isNotEmpty(p.getObjectId())) {
				p.setPointLocs(String.join(Constant.COMMA, pointLocList));
			}
		}
	}

	//超过5000的
	private void forTotalMoreQuery(PsWorkOrderBasic psWorkOrderBasic, List<BPcbLocationDetail> bPcbLocationDetailList, long total) throws Exception {
		if(total >NumConstant.LONG_5000){
			int pages=(int)Math.ceil(total *DOUBLE_1/NumConstant.LONG_5000);
			for(int i=NumConstant.NUM_TWO;i<=pages;i++){
				BPcbLocationDetailDTO tempDto=new BPcbLocationDetailDTO();
				tempDto.setPage(new Long(i));
				tempDto.setRows(NumConstant.LONG_5000);
				if (Objects.nonNull(psWorkOrderBasic)) {
					tempDto.setCraftSection(psWorkOrderBasic.getCraftSection());
					tempDto.setProductCode(psWorkOrderBasic.getItemNo());
				}
				PageRows<BPcbLocationDetail> tempListPage=centerfactoryRemoteService.getBPcbLocPage(tempDto);
				if(tempListPage == null || tempListPage.getTotal()<=NumConstant.LONG_ZERO){
					continue;
				}
				bPcbLocationDetailList.addAll(tempListPage.getRows());
			}
		}
	}

	/**
	 * 根据条件查询实体信息
	 *
	 * @param record
	 * @return list
	 **/
	@Override
	public List<BSmtBomDetail> getDetailList(Map<String, Object> record, String cfgHeaderId) throws Exception {
		List<BSmtBomDetail> list = bSmtBomDetailRepository.getPage(record);
		if (CollectionUtils.isEmpty(list)) {
			return  list;
		}
		BSmtBomHeader header = new BSmtBomHeader();
		header.setCfgHeaderId(cfgHeaderId);
		//获得主工序
		BSmtBomHeader productList = bSmtBomHeaderService.selectBSmtBomHeaderById(header);
		//获得规格型号
		Map<String, Object> stylequery = new HashMap<String, Object>();
		StringBuilder itemNo = new StringBuilder();
		//料单代码去重
		for (BSmtBomDetail planInfo : list) {
			stylequery.put(planInfo.getItemCode(), planInfo.
					getItemName());
		}
		for (String key : stylequery.keySet()) {
			itemNo.append(Constant.SINGLE_QUOTE).append(key).append(Constant.SINGLE_QUOTE).append(Constant.COMMA);
		}
		Map<String, Object> mapItemNo = new HashMap<String, Object>();
		mapItemNo.put("inItemNo", itemNo.substring(0, itemNo.length() - 1));
		List<BsItemInfo> style = BasicsettingRemoteService.getStyleInfo(mapItemNo);
		//添加规格型号
		this.addStyleDetail(list,style);
		return list;
	}

	/**
	 * 添加规格型号
	 * @param list
	 * @param style
	 * <AUTHOR>
	 */
	public void addStyleDetail(List<BSmtBomDetail> list,List<BsItemInfo> style){
		if (CollectionUtils.isEmpty(style)) {
			return;
		}
		// itemInfo集合转成<itemNo, itemInfo>map
		Map<String, BsItemInfo> itemInfoMap = style.stream()
				.filter(item -> item != null && StringUtils.isNotBlank(item.getItemNo()))
				.collect(Collectors.toMap(BsItemInfo::getItemNo, item -> item, (a,b) -> a));
		for (BSmtBomDetail bomDetail : list) {
			if (bomDetail == null) {
				continue;
			}
			BsItemInfo item = itemInfoMap.get(bomDetail.getItemCode());
			if (item == null) {
				// 默认A材
				bomDetail.setAbcType(Constant.TYPE_A);
				continue;
			}
			bomDetail.setStyle(item.getStyle());
			// 默认A材
			bomDetail.setAbcType(StringUtils.isBlank(item.getAbcType()) ? Constant.TYPE_A : item.getAbcType());
		}
	}



	@Override
    public Long getCount(Map<String,Object>record,String orderField,String order){
        Long count = bSmtBomDetailRepository.getCount(record);
        return count;
    }

	@Override
	public List<BSmtBomDetail> getPage(Map<String, Object> record, String orderField, String order, Long page,
			Long rows) {
		record.put("orderField", orderField);
		record.put("order", order);
		record.put("startRow", (page - 1) * rows + 1);
		record.put("endRow", page * rows);
		List<BSmtBomDetail> list = bSmtBomDetailRepository.getPage(record);
		return list;
	}

	@Override
	public int updateMountingByBomline(List<BSmtBomDetail> list) {
		int transferstrategy=0;
		String worknoC="";
		String worknoN="";


		if (CollectionUtils.isEmpty(list)||list.size() != NumConstant.NUM_TWO) {
			return Constant.INT_0;
		}
		List<BSmtBomDetail> returnlist0 = new ArrayList<>();// 中间变量
		List<BSmtBomDetail> returnlist00 = new ArrayList<>();// 中间变量
		List<BSmtBomDetail> returnlist1 = new ArrayList<>();// 物料相同，站位相同
		List<BSmtBomDetail> returnlist2 = new ArrayList<>();// 物料相同，站位不相同 在其他两个字段里，防止了N的 机器号和位号
		List<BSmtBomDetail> neslist = null;

		//借用的字段：表的qty：transferstrategy，CREATE_USER ：当前指令C
		BSmtBomDetail bbdC = list.get(NumConstant.NUM_ZERO);
		transferstrategy=bbdC.getQty().intValue();
		worknoC=bbdC.getCreateUser();

		List<BSmtBomDetail> listC = bSmtBomDetailRepository.selectBSmtBomDetailByHeadId(bbdC);// 当前指令C的上料表明细
		List<BSmtBomDetailDTO> listCDTO = BSmtBomDetailAssembler.toBSmtBomDetailDTOList(listC);

		BSmtBomDetail bbdN = list.get(1);
		worknoN=bbdN.getCreateUser();
		List<BSmtBomDetail> listN = bSmtBomDetailRepository.selectBSmtBomDetailByHeadId(bbdN);// 下条指令N的上料表明细
		List<BSmtBomDetailDTO> listNDTO = BSmtBomDetailAssembler.toBSmtBomDetailDTOList(listN);
		ItemCheckRelatedInfoDTO itemCheckRelatedInfoDTO = new ItemCheckRelatedInfoDTO();
		itemCheckRelatedInfoDTO.setBSmtBomDetailList(listN);
		itemCheckRelatedInfoDTO.setbSmtBomDetailDTOList(listNDTO);
		this.dealListCAndNFirst(returnlist0, returnlist1, listC, listCDTO,itemCheckRelatedInfoDTO);

		dealListCAndNSecond(returnlist00, returnlist2, listC, listN);
		// 更新机台在用物料表指令：1，更新物料相同，站位相同的，2，更新物料相同站位不同的，3，全部不同的（此时分转产策略是2还是3）。


		dealReturnlist1(worknoC, worknoN, returnlist1);

		if (!CollectionUtils.isEmpty(returnlist2 )) {
			// 情况2 ，更新物料相同站位不同的
			List<SmtMachineMaterialMouting> listmx = new ArrayList<SmtMachineMaterialMouting>();
			for (BSmtBomDetail bbb : returnlist2) {
				SmtMachineMaterialMouting entity = new SmtMachineMaterialMouting();
				entity.setWorkOrder(worknoC);
				entity.setItemCode(bbb.getItemCode());
				entity.setLocationNo(bbb.getLocationNo());
				entity.setLastUpdatedBy(worknoN);  //jieyong

				entity.setCreateUser(bbb.getCreateUser());//借用字段//存放getMachineNo机器好
				entity.setItemName(bbb.getItemName());    //借用字段//存放位号
				listmx.add(entity);
			}

			//更新机台在用物料表指令：1，更新物料相同，站位相同的， ,2,更新物料相同，站位不同
			updateMountingTheSame(listmx, MpConstant.UPDATEMOUNTINGTHESAME_TWO);

		}

		if (!CollectionUtils.isEmpty(listC)) {
			// 情况3更新 全部不同的（此时分转产策略是2还是3
			List<SmtMachineMaterialMouting> listm = new ArrayList<SmtMachineMaterialMouting>();

			for (BSmtBomDetail bbb : listC) {
				SmtMachineMaterialMouting entity = new SmtMachineMaterialMouting();
				entity.setWorkOrder(worknoC);
				entity.setItemCode(bbb.getItemCode());
				entity.setLocationNo(bbb.getLocationNo());
				// entity.setForward("to挂起");
				listm.add(entity);
			}

			updateMountingDiff(listm, transferstrategy);

		}
		return Constant.INT_1;


	}

	/**
	 * @param worknoC
	 * @param worknoN
	 * @param returnlist1
	 */
	private void dealReturnlist1(String worknoC, String worknoN, List<BSmtBomDetail> returnlist1) {
		if (!CollectionUtils.isEmpty(returnlist1)) {
			//情况1 ，更新物料相同，站位相同的
			if (returnlist1 != null && returnlist1.size() > 0) {
				List<SmtMachineMaterialMouting> listm0 = new ArrayList<SmtMachineMaterialMouting>();
				for (BSmtBomDetail bbb : returnlist1) {
					SmtMachineMaterialMouting entity = new SmtMachineMaterialMouting();
					entity.setWorkOrder(worknoC);
					entity.setItemCode(bbb.getItemCode());
					entity.setLocationNo(bbb.getLocationNo());
					entity.setLastUpdatedBy(worknoN);//借用该字段，存放下条指令N号
					listm0.add(entity);
				}

				updateMountingTheSame(listm0, MpConstant.UPDATEMOUNTINGTHESAME_ONE);
				/*if(!listm0.isEmpty()&&listm0.size()>0){
					if(updateMountingTheSame(listm0, 1)<0)
						returnMessage="updateMountingTheSame failed";
				}*/

			}
		}
	}

	/**
	 * @param returnlist00
	 * @param returnlist2
	 * @param listC
	 * @param listN
	 */
	private void dealListCAndNSecond(List<BSmtBomDetail> returnlist00, List<BSmtBomDetail> returnlist2,
			List<BSmtBomDetail> listC, List<BSmtBomDetail> listN) {
		List<BSmtBomDetail> neslist;
		if (!CollectionUtils.isEmpty(listC)&&!CollectionUtils.isEmpty(listN)) {
			//在剩下的listC和listN中，选取物料相同，站位不同的物料，放入returnlist2，再从listC中删除，
			//之后，returnlist2存放物料相同，站位不同的记录，listC中存放的是物料不相同，站位也不同的记录
			List<BSmtBomDetailDTO> list3 = BSmtBomDetailAssembler.toBSmtBomDetailDTOList(listN);
			for (BSmtBomDetailDTO wip : list3) {// 站位相同，物料相同
				neslist = listC.stream().filter(pro -> wip.getItemCode().equals(pro.getItemCode())
						&& !wip.getLocationNo().equals(pro.getLocationNo())).collect(Collectors.toList());
				if (!CollectionUtils.isEmpty(neslist)) {

					BSmtBomDetail bb = neslist.get(NumConstant.NUM_ZERO);

					bb.setCreateUser(wip.getMachineNo());//机器号
					bb.setItemName(wip.getLocationNo());//位号
					returnlist2.add(bb);
					returnlist00.add(neslist.get(NumConstant.NUM_ZERO));

				}
			}
			listC.removeAll(returnlist00); // 更新listC，listc剩下的是全部不同的
		}
	}

	/**
	 * @param returnlist0
	 * @param returnlist1
	 * @param listC
	 * @param listCDTO
	 * @param listN
	 * @param listNDTO
	 */
	private void dealListCAndNFirst(List<BSmtBomDetail> returnlist0, List<BSmtBomDetail> returnlist1,
			List<BSmtBomDetail> listC, List<BSmtBomDetailDTO> listCDTO, ItemCheckRelatedInfoDTO itemCheckRelatedInfoDTO) {
		List<BSmtBomDetail> listN = itemCheckRelatedInfoDTO.getBSmtBomDetailList();
		List<BSmtBomDetailDTO> listNDTO = itemCheckRelatedInfoDTO.getbSmtBomDetailDTOList();
		List<BSmtBomDetail> neslist;
		if (!CollectionUtils.isEmpty(listC) && !CollectionUtils.isEmpty(listN) ) {
			//获得两个上料表明细中，站位相同，物料也相同 的数据记录，放在returnlist1中，并将其从listC和listN中去除
			//之后，returnlist1存放物料相同，站位也相同的记录
			for (BSmtBomDetailDTO wip : listNDTO) {// 站位相同，物料相同
				neslist = listC.stream().filter(pro -> wip.getItemCode().equals(pro.getItemCode())
						&& wip.getLocationNo().equals(pro.getLocationNo())).collect(Collectors.toList());
				if (!CollectionUtils.isEmpty(neslist)) {
					returnlist1.add(neslist.get(NumConstant.NUM_ZERO));
				}
			}
			listC.removeAll(returnlist1); // 更新listC

			for (BSmtBomDetailDTO wip : listCDTO) {// 站位相同，物料相同
				neslist = listN.stream().filter(pro -> wip.getItemCode().equals(pro.getItemCode())
						&& wip.getLocationNo().equals(pro.getLocationNo())).collect(Collectors.toList());
				if (!CollectionUtils.isEmpty(neslist)) {

					returnlist0.add(neslist.get(NumConstant.NUM_ZERO));
				}
			}
			listN.removeAll(returnlist0);// 更新listN

		}
	}


	/*
	 * 更新机台在用物料表指令：1，更新物料相同，站位相同的， ,2,更新物料相同，站位不同
	 * wl
	 * param:获取的满足条件的C的上料表line行
	 *
	 */
	public int updateMountingTheSame(List<SmtMachineMaterialMouting> returnlist,int flag)
			{
		int je = 1;

		smtMachineMaterialMoutingService.updateMountingTheSameByline1tr(returnlist,flag);
		return je;
	}


	/*
	 * 更新机台在用物料表指令：3，全部不同的（分转产策略是2还是3）。
	 * wl
	 * param:获取的满足条件的C的上料表line行
	 *
	 */
	public int updateMountingDiff(List<SmtMachineMaterialMouting> returnlist,int transstra){
		int je = 1;
		if(transstra==NumConstant.NUM_TWO)
		{
			smtMachineMaterialMoutingService.updateMountingDiffByline2tr(returnlist);
		}
		if(transstra==NumConstant.NUM_THREE){
			smtMachineMaterialMoutingService.updateMountingDiffByline3tr(returnlist);
		}

		return je;
	}

	/**
	 * 查询模组上料详情信息
	 * @param entity
	 * @return
	 */
	@Override
    public List<BSmtBomDetail> getModuleBSmtBomDetailList(BSmtBomDetailDTO entity) {
        return bSmtBomDetailRepository.getModuleBSmtBomDetailList(entity);
    }


    /**
     * 查询备料模组上料详情信息
     * @param entity
     * @return
     * @throws Exception
     */
	@Override
    public List<BSmtBomDetail> getPrepareBSmtBomDetailList(BSmtBomDetailDTO entity) throws Exception {
        List<BSmtBomDetail> allList = this.getModuleBSmtBomDetailList(entity);
        if (!CollectionUtils.isEmpty(allList)) {
            Set<String> locationSet = new HashSet<String>();
            List<List<BSmtBomDetail>> listOfList = CommonUtils.splitList(allList, CommonConst.BATCH_SIZE_1000);
            for (List<BSmtBomDetail> batchList : listOfList) {
                //去查询指令状态为2的
                StringBuilder locSb = new StringBuilder();
                for(BSmtBomDetail item : batchList) {
                    locSb.append(Constant.SINGLE_QUOTE).append(item.getLocationNo()).append(Constant.SINGLE_QUOTE).append(Constant.COMMA);
                }
                String location = locSb.toString();
                SmtLocationInfoDTO dto = new SmtLocationInfoDTO();
                dto.setLineCode(entity.getLineCode());
                dto.setModuleNo(entity.getModuleNo());
                dto.setInLocationNo(location.substring(0,location.length()-1));
                dto.setLocationType(CommonConst.LOCATION_TYPE_TWO);
                List<SmtLocationInfo> retList = ObtainRemoteServiceDataUtil.getSmtLocationInfoList(dto);
				this.addLocationSetByRetList(retList,locationSet);
            }
            if(!CollectionUtils.isEmpty(locationSet)) {
                List<BSmtBomDetail> removeList = new ArrayList<BSmtBomDetail>();
                for(BSmtBomDetail item : allList) {
                    if(locationSet.contains(item.getLocationNo())) {
                        removeList.add(item);
                    }
                }
                allList.removeAll(removeList);
            }
        }
        return allList;
    }

	/**
	 * 添加LocationSet
	 * @param retList
	 * @param locationSet
	 * <AUTHOR>
	 */
	private void addLocationSetByRetList(List<SmtLocationInfo> retList,Set<String> locationSet){
		if(!CollectionUtils.isEmpty(retList)) {
			for(SmtLocationInfo info : retList) {
				locationSet.add(info.getLocationNo());
			}
		}
	}

    /**
     * 查询物料Feeder绑定信息
     * @param entity
     * @return
     */
	@Override
    public List<BSmtBomDetail> getBSmtBomDetailBindFeederList(BSmtBomDetailDTO entity) {
        return bSmtBomDetailRepository.getBSmtBomDetailBindFeederList(entity);
    }

	@Override
	public boolean hasSameItemCode(String prodplanId,String moduleNo,String locationNo) {
		Map  param = Maps.newHashMap();
		param.put("prodplanId",prodplanId);
		param.put(Constant.MODULE_NO,moduleNo);
		param.put(Constant.LOCATION_NO,locationNo);
		Long count = bSmtBomDetailRepository.getListSameItemCode(param);
		return count > 1 ?true:false;
	}

	/**
     * 获取智能机柜上料表信息
     * @param entity
     * @return
     */
    @Override
    public List<BSmtBomDetail> getIntelligentCabinetBSmtBomDetailList(BSmtBomDetailDTO entity) {
        return bSmtBomDetailRepository.getIntelligentCabinetBSmtBomDetailList(entity);
    }

    @Override
    public BSmtBomHeader getbsmtBomDetailInfoByWorkOrderNo(BSmtBomDetailDTO entity) throws Exception{
    	// 根据指令编码获取headid
		List<PsWorkOrderSmt>  smtInfoList = PlanscheduleRemoteService.getList(entity.getWorkOrderNo());
		if (smtInfoList==null||CollectionUtils.isEmpty(smtInfoList)|| org.springframework.util.StringUtils.isEmpty(smtInfoList.get(	Constant.INT_0).getCfgHeaderId())){
			return null;
		}
		PsWorkOrderSmt smtInfo = new PsWorkOrderSmt();
		smtInfo.setCfgHeaderId(smtInfoList.get(	Constant.INT_0).getCfgHeaderId());
		// 根据headid获取头和详情信息
		BSmtBomHeader head = new BSmtBomHeader();
		head.setCfgHeaderId(smtInfo.getCfgHeaderId());
		head.setLineCode(entity.getLineCode());
		BSmtBomHeader headerInfo = bSmtBomHeaderRepository.selectBSmtBomHeaderById(head);
		if (null==headerInfo){
			return null;
		}
		BSmtBomDetail detail = new BSmtBomDetail();
		detail.setCfgHeaderId(smtInfo.getCfgHeaderId());
		detail.setMachineNo(entity.getStationName());
		detail.setPreManue(entity.getPreManue());
		detail.setProcessCode(entity.getProcessCode());
		detail.setLineCode(entity.getLineCode());
		List<BSmtBomDetail> detailList = bSmtBomDetailRepository.selectBSmtBomDetailListById(detail);
		if (CollectionUtils.isEmpty(detailList)){
			return null;
		}
		StringBuilder itemCodeBuilder = new StringBuilder();
		//料单代码去重
		Map<String,Object> itemMap = new HashMap<>();
		for (BSmtBomDetail item :detailList){
			itemMap.put(item.getItemCode(),item.getItemName());
		}
		//料单代码设置为带双引号、逗号的格式
		for (String key : itemMap.keySet()) {
			itemCodeBuilder.append(Constant.SINGLE_QUOTE).append(key).append(Constant.SINGLE_QUOTE).append(Constant.COMMA);
		}
		String inItemNo = itemCodeBuilder.toString().substring(0,itemCodeBuilder.length()-1);
		// 查询物料基础信息
		List<BsItemInfo> itemList =BasicsettingRemoteService.getStyleInfo(inItemNo);
		if(!CollectionUtils.isEmpty(itemList)) {
			Map<String, String> itemCodeMap = new HashMap<>();
			for(BsItemInfo item : itemList) {
				itemCodeMap.put(item.getItemNo(), item.getItemName());
			}
			for (BSmtBomDetail item :detailList){
				item.setItemName(itemCodeMap.get(item.getItemCode()));
			}
		}
		headerInfo.setTransferList(detailList);
		return headerInfo;
	}

	public BSmtBomHeader getStationList(BSmtBomDetailDTO entity) throws Exception{
		// 根据头id获取头和详情信息
		BSmtBomHeader head = new BSmtBomHeader();
		head.setProductCode(entity.getProductCode());
		head.setStatus(entity.getStatus());
		BSmtBomHeader station= bSmtBomHeaderService.getStationList(head);
		if (null ==station){
			return null;
		}
		return station;
	}

	@Override
	public void setItemRemainAndScanHistory(BSmtBomDetail bSmtBomDetail,BSmtBomDetailDTO entity) throws  Exception{
    	if(bSmtBomDetail == null || entity == null){
    		return ;
		}
		Map<String,Object> param = Maps.newHashMap();
		param.put("mountType",Constant.MOUNT_TYPE_DISTRIBUTION_MANYY_TIMES);
		param.put("itemCode",bSmtBomDetail.getItemCode());
		PsWorkOrderBasic workOrder = PlanscheduleRemoteService.getProductCodeByWorkNo(entity.getWorkOrder());
		if (null == workOrder) {
			return ;
		}
		//查询批次下 对应物料扫描的数量
		param.put("attr3",workOrder.getSourceTask());
		BigDecimal itemQtySumed = smtMachineMTLHistoryLService.getItemQty(param,bSmtBomDetail.getItemCode());
		// 取站位 物料代码  reelid扫描记录 需带上 模组 站位；
		param.put("moduleNo",bSmtBomDetail.getModuleNo());
		param.put(Constant.LOCATION_NO,bSmtBomDetail.getLocationNo());
		bSmtBomDetail.setDistributionScanHistorys(smtMachineMTLHistoryLService.getList(param,null,null,0L,0L));
		//  2020/2/10  获取批次物料总数量
		BigDecimal itemQtySum = pkCodeInfoService.getItemQtySum(bSmtBomDetail.getItemCode(),workOrder.getSourceTask());
		if(itemQtySum != null){
			bSmtBomDetail.setItemRemain(itemQtySum);
			if(itemQtySumed != null){
				bSmtBomDetail.setItemRemain(itemQtySum.subtract(itemQtySumed));
			}
		}else{
			bSmtBomDetail.setItemRemain(new BigDecimal(NumConstant.NUM_ZERO));
		}
	}

	public BSmtBomHeader getBomHeaderAndDetail(BSmtBomDetailDTO entity, List<PsWorkOrderSmt> headIdList) throws Exception{
		// 根据headid获取头和详情信息
		BSmtBomHeader head = new BSmtBomHeader();
		head.setCfgHeaderId(headIdList.get(NumConstant.NUM_ZERO).getCfgHeaderId());
		if(StringHelper.isNotEmpty(entity.getLineCode())){
			head.setLineCode(entity.getLineCode());
		}
		BSmtBomHeader headerInfo = bSmtBomHeaderRepository.selectBSmtBomHeaderById(head);
		if (null==headerInfo){
			return null;
		}
		BSmtBomDetail detail = new BSmtBomDetail();
		detail.setCfgHeaderId(headIdList.get(NumConstant.NUM_ZERO).getCfgHeaderId());
		detail.setProcessCode(entity.getProcessCode());
		if(StringHelper.isNotEmpty(entity.getLineCode())){
			detail.setLineCode(entity.getLineCode());
		}
		List<BSmtBomDetail> detailList = bSmtBomDetailRepository.selectBSmtBomDetailListById(detail);
		if (CollectionUtils.isEmpty(detailList)){
			return null;
		}
		StringBuilder itemCodeBuilder = new StringBuilder();
		//料单代码去重
		Map<String,Object> itemMap = new HashMap<>();
		for (BSmtBomDetail item :detailList){
			itemMap.put(item.getItemCode(),item.getItemName());
		}
		//料单代码设置为带双引号、逗号的格式
		for (String key : itemMap.keySet()) {
			itemCodeBuilder.append(Constant.SINGLE_QUOTE).append(key).append(Constant.SINGLE_QUOTE).append(Constant.COMMA);
		}
		String inItemNo = itemCodeBuilder.toString().substring(0,itemCodeBuilder.length()-1);
		// 查询物料基础信息
		List<BsItemInfo> itemList =BasicsettingRemoteService.getStyleInfo(inItemNo);
		if(!CollectionUtils.isEmpty(itemList)) {
			Map<String, String> itemCodeMap = new HashMap<>();
			for(BsItemInfo item : itemList) {
				itemCodeMap.put(item.getItemNo(), item.getItemName());
			}
			for (BSmtBomDetail item :detailList){
				item.setItemName(itemCodeMap.get(item.getItemCode()));
			}
		}
		headerInfo.setTransferList(detailList);
		return headerInfo;
	}
	@Override
	public void setItemRemainAndScanHistory(List<BSmtBomDetail> bSmtBomDetails,BSmtBomDetailDTO entity) throws  Exception{
		if(CollectionUtils.isEmpty(bSmtBomDetails) || entity == null){
			return ;
		}
		// 获取指令备用
		PsWorkOrderBasic workOrder = PlanscheduleRemoteService.getProductCodeByWorkNo(entity.getWorkOrder());
		if (null == workOrder) {
			return ;
		}
		//获取批次的所有物料的总数量，itemQty 表示的是批次下物料的总数量！！！  返回值[{"itemCode":"040002032","itemQty":"222"},{"itemCode":"040002033","itemQty":"333"}]
		List<PkCodeInfoDTO> itemQtys = pkCodeInfoService.getItemSumByProductTask(workOrder.getSourceTask());
		//获取批次下所有扫描的历史记录
		List<SmtMachineDistributeScanEntityDTO> distributeScans = smtMachineDistributeScanService.getListByProdplanId(workOrder.getSourceTask());
		// 处理剩余数量及扫描历史
		setItemRemainAndScanHistory(bSmtBomDetails,distributeScans,itemQtys);
	}

	/**
	 * 获取用户单板数 数量QTY
	 *
	 * @param list
	 * @return
	 */
	@Override
	public List<EmEqpPdCountsDTO> getCount(List<EmEqpPdCountsDTO> list) {
		if(!MesCollectionUtil.isEmpty(list)){
			for (EmEqpPdCountsDTO dto: list) {
				// 站位用料数
				String qty = bSmtBomDetailRepository.getQtyByDto(dto);
				// 指令用料数
				Long orderQty = bSmtBomDetailRepository.getOrderQtyByDto(dto);
				if(StringUtils.isNotEmpty(qty)){
					dto.setQty(BigDecimal.valueOf(Long.valueOf(qty)));
				}else{
					dto.setQty(BigDecimal.ZERO);
				}
				dto.setOrderQty(BigDecimal.valueOf(orderQty));
			}
		}
		return list;
	}

	/**
	 * 获取物料
	 * @param bSmtBomDetails
	 * @param distributeScans
	 * @return
	 */
	private void setItemRemainAndScanHistory(List<BSmtBomDetail> bSmtBomDetails
			,List<SmtMachineDistributeScanEntityDTO> distributeScans,List<PkCodeInfoDTO> itemQtys){
		//处理分料扫描历史数据
		Map<String,Integer> itemRemains = Maps.newHashMap();
		Map<String,Integer> itemScaneds = Maps.newHashMap();
		Map<String,List<SmtMachineMTLHistoryL>> scanHistoryMap = Maps.newHashMap();
		parseDistributionScanHistoryData(distributeScans,itemScaneds,scanHistoryMap);
		//处理剩余数量
		for(PkCodeInfoDTO pkCodeInfo:itemQtys){
			if(pkCodeInfo != null && pkCodeInfo.getItemCode() != null && itemScaneds.containsKey(pkCodeInfo.getItemCode())){
				// 物料总数减去 已扫描数量
				itemRemains.put(pkCodeInfo.getItemCode(), pkCodeInfo.getItemQty().intValue() - itemScaneds.get(pkCodeInfo.getItemCode()));
			}
		}
		//处理列表数据
		for(BSmtBomDetail bSmtBomDetail:bSmtBomDetails){
			if(null != bSmtBomDetail.getItemCode() && null != itemRemains.get(bSmtBomDetail.getItemCode())){
				bSmtBomDetail.setItemRemain(new BigDecimal(itemRemains.get(bSmtBomDetail.getItemCode())));
			}
			bSmtBomDetail.setDistributionScanHistorys(scanHistoryMap.get(getKey(bSmtBomDetail)));
		}
	}

	/**
	 * 处理分料扫描历史数据
	 */
	private void parseDistributionScanHistoryData(List<SmtMachineDistributeScanEntityDTO> distributeScans,
												  Map<String,Integer> itemScaneds,Map<String,List<SmtMachineMTLHistoryL>> scanHistoryMap){
		for(SmtMachineDistributeScanEntityDTO distributeScan:distributeScans){
			if(!isItemCodeNotNull(distributeScan)){
				continue;
			}
			//处理物料总数
			dealItemCodeSum(itemScaneds,distributeScan.getItemCode(),distributeScan.getQty() == null ? 0 :distributeScan.getQty().intValue());
			//处理扫描历史
			dealScanHistory(scanHistoryMap,distributeScan);
		}
	}

	// 处理总数
	private void dealItemCodeSum(Map<String,Integer> itemScaneds,String itemCode,int qty){
		if(itemScaneds.containsKey(itemCode)){
			itemScaneds.put(itemCode, itemScaneds.get(itemCode) + qty);
		}else{
			itemScaneds.put(itemCode,qty);
		}
	}

	// 处理历史记录
	private void dealScanHistory(Map<String,List<SmtMachineMTLHistoryL>> scanHistoryMap,
								 SmtMachineDistributeScanEntityDTO distributeScan){
		//如果不是相同的物料代码、模组、站位 则返回
		String key = getKey(distributeScan);
		if(scanHistoryMap.containsKey(key)){
			SmtMachineMTLHistoryL smtMachineMTLHistoryL = new SmtMachineMTLHistoryL();
			BeanUtils.copyProperties(distributeScan, smtMachineMTLHistoryL);
			scanHistoryMap.get(key).add(smtMachineMTLHistoryL);
		}else{
			List<SmtMachineMTLHistoryL> smtMachineMTLHistoryLs = new ArrayList<SmtMachineMTLHistoryL>();
			SmtMachineMTLHistoryL smtMachineMTLHistoryL = new SmtMachineMTLHistoryL();
			BeanUtils.copyProperties(distributeScan, smtMachineMTLHistoryL);
			smtMachineMTLHistoryLs.add(smtMachineMTLHistoryL);
			scanHistoryMap.put(key,smtMachineMTLHistoryLs);
		}
	}

	private String getKey(SmtMachineDistributeScanEntityDTO distributeScan){
		return distributeScan.getItemCode()+Constant.GANG+distributeScan.getModuleNo()+Constant.GANG+distributeScan.getLocationNo();
	}

	private String getKey(BSmtBomDetail bSmtBomDetail){
		return bSmtBomDetail.getItemCode()+Constant.GANG+bSmtBomDetail.getModuleNo()+Constant.GANG+bSmtBomDetail.getLocationNo();
	}

	private boolean isItemCodeNotNull(SmtMachineDistributeScanEntityDTO distributeScan){
		return distributeScan != null && StringUtils.isNotEmpty(distributeScan.getItemCode());
	}

	/**
	 * 查询最新上料历史数据
	 * @param entity
	 * @return
	 * @throws Exception
	 */
	@Override
	public List<BSmtBomDetail> getBSmtBomDetailList (BSmtBomDetailDTO entity)throws Exception{
		veifyParams(entity);
		Map<String, Object> map = new HashMap<String, Object>();
		//设置查询条件
		map.put("productCode", entity.getProductCode());
		map.put("lineCode", entity.getLineCode());
		map.put("craftSection", entity.getCraftSection());
		map.put("orderField", "lastUpdatedDate");
		map.put("order", "desc");
		map.put("enabledFlag", Constant.FLAG_Y);
		map.put("factoryId", entity.getFactoryId());
		map.put("preManue", entity.getPreManue());//是否前加工
		map.put("processCode", entity.getProcessCode());//子工序
		map.put(Constant.LOCATION_NO, entity.getLocationNo());//工位
		List<BSmtBomHeader> headerList=bSmtBomHeaderRepository.getListLinkBomDetail(map);
		if(CollectionUtils.isEmpty(headerList)){
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.B_SMT_BOM_DETAIL_OF_WORKORDER_IS_NULL);
		}
		BSmtBomHeader bSmtBomHeader=headerList.get(0);
		Map<String, Object> mapDetail = new HashMap<String, Object>();
		//设置查询条件
		mapDetail.put(MpConstant.CFG_HEADERID, bSmtBomHeader.getCfgHeaderId());
		mapDetail.put("preManue", entity.getPreManue());
		mapDetail.put("processCode", entity.getProcessCode());
		mapDetail.put(Constant.LOCATION_NO, entity.getLocationNo());
		mapDetail.put("factoryId", entity.getFactoryId());
		mapDetail.put("enabledFlag", CommonConst.ENABLE_FLAG_Y);
		List<BSmtBomDetail> detailList=bSmtBomDetailRepository.getList(mapDetail);
		setItemName(detailList);
		if(CollectionUtils.isEmpty(detailList)){
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.B_SMT_BOM_DETAIL_OF_WORKORDER_IS_NULL);
		}
		return detailList;
	}

	//设置物料名称
	private void setItemName(List<BSmtBomDetail> detailList) throws Exception {
		if(CollectionUtils.isEmpty(detailList)){
			return;
		}
		List<AgeingInfoFencePointToPointQueryItemInfoDTO> itemList=new ArrayList<>();
		for(BSmtBomDetail detail:detailList){
			AgeingInfoFencePointToPointQueryItemInfoDTO dto=new AgeingInfoFencePointToPointQueryItemInfoDTO();
			dto.setItemNo(detail.getItemCode());
			itemList.add(dto);
		}
		if(CollectionUtils.isEmpty(itemList)){
			return;
		}
		List<AgeingInfoFencePointToPointQueryItemInfoDTO> ageList= BasicsettingRemoteService.getItemInfo(itemList);
		if(!CollectionUtils.isEmpty(ageList)) {
			Map<String, AgeingInfoFencePointToPointQueryItemInfoDTO> ageListMap = ageList.stream().collect(Collectors.toMap(AgeingInfoFencePointToPointQueryItemInfoDTO::getItemNo, a -> a, (k1, k2) -> k1));
			for (BSmtBomDetail detail : detailList) {
				AgeingInfoFencePointToPointQueryItemInfoDTO ageDto = ageListMap.get(detail.getItemCode());
				if (ageDto != null && StringUtils.isNotEmpty(ageDto.getItemName())) {
					detail.setItemName(ageDto.getItemName());
				}
			}
		}
	}

	//参数校验
	private void veifyParams(BSmtBomDetailDTO entity) throws MesBusinessException {
		if(!StringUtils.isNotEmpty(entity.getProductCode())){
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PRODUCTCODE_IS_NULL);
		}
		if(!StringUtils.isNotEmpty(entity.getLineCode())){
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.LINE_CODE_IS_NULL);
		}
		if(!StringUtils.isNotEmpty(entity.getCraftSection())){
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.CRAFTSECTION_IS_NULL);
		}
		if(!StringUtils.isNotEmpty(entity.getFactoryId()+"")){
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.FACTORY_ID_IS_NULL);
		}
		if(!StringUtils.isNotEmpty(entity.getPreManue())){
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PARAM_IS_NULL);
		}
		if(!StringUtils.isNotEmpty(entity.getProcessCode())){
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PROCESS_IS_NULL);
		}
		if(!StringUtils.isNotEmpty(entity.getLocationNo())){
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.LOCATION_NO_EMPTY);
		}
	}

	/**
	 * 首备看板--根据cfg_headId批量查询上料明细
	 */
	@Override
	public List<BSmtBomDetailDTO> getBomDetail(BSmtBomDetailDTO dto)throws  Exception{
		return bSmtBomDetailRepository.getBomDetail(dto);
	}

	/**
	 * 产线物料消耗,获取cfg_header_id、占位、物料代码在B_SMT_BOM_DETAIL的标准用量qty
	 */
	@Override
	public List<BSmtBomDetailDTO> getStandardQtyByWorkOrder(BSmtBomDetailDTO dto) {
		return bSmtBomDetailRepository.getStandardQtyByWorkOrder(dto);
	}

	/**
	 * 根据配置编号查询拼板数,根据批次和主工序获取指令
	 * @param
	 * @return
	 */
	@Override
	public PsWorkOrderSmt queryQtyAndWorkOrderByCfgHeaderId(BSmtBomDetailDTO dto) throws Exception {
		PsWorkOrderSmt psWorkOrderSmt = new PsWorkOrderSmt();
		//获取指令
		PsWorkOrderBasicDTO workOrderNoList = PlanscheduleRemoteService.queryWorkOrderNo(dto.getProdplanId(),dto.getCraftSection());
		if(workOrderNoList == null){
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,MessageId.WORKORDER_NOT_FIND);
		}
		String workOrderNo = workOrderNoList.getWorkOrderNo();
		psWorkOrderSmt.setWorkOrderNo(workOrderNo);
		//获取拼板数
		PsWorkOrderSmt smtOrderList = PlanscheduleRemoteService.getPcbQty(dto.getCfgHeaderId());
		if(smtOrderList == null){
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,MessageId.CFGHEADERID_INFO_IS_NULL);
		}
		BigDecimal pieceQty = smtOrderList.getPcbQty();
		if(pieceQty == null){
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,MessageId.PCB_QTY_IS_NULL, new Object[]{workOrderNo});
		}
		psWorkOrderSmt.setPcbQty(pieceQty);
		return psWorkOrderSmt;
	}

	/**
	 * 获取虚拟站位物料数量校验
	 * @param dto
	 */
	@Override
	public void checkQty(BSmtBomDetailDTO dto) throws Exception {
		//先校验是否输入了负数
		if(dto.getQty().signum() == Constant.INT_NEGATIVE_1 || dto.getQty().signum() == Constant.INT_0){
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,MessageId.QTY_CANNOT_BE_ZERO_OR_NEGATIVE);
		}
		//获取指令和拼板数
		PsWorkOrderSmt psWorkOrderSmt = this.queryQtyAndWorkOrderByCfgHeaderId(dto);
		//获取拼板数
		BigDecimal pieceQty = psWorkOrderSmt.getPcbQty();
		// 查询城堡主板子板指令信息
		List<PsWorkOrderBasicDTO> orderListAll = ProductionDeliveryRemoteService.getChildPsTask(psWorkOrderSmt.getWorkOrderNo());
		if(CollectionUtils.isEmpty(orderListAll)){
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,MessageId.WORKORDER_NOT_FIND);
		}
		// 获取用量信息用于校验
		BSmtBomDetailVirtualDTO bSmtBomDetailVirtualDTO = this.allBomQty(orderListAll,dto.getItemCode());
		bSmtBomDetailVirtualDTO.setPcbQty(pieceQty);
		//校验数量是否大于bom用量*拼板数
		BigDecimal result = this.checkBomQty(bSmtBomDetailVirtualDTO);
		int flag = dto.getQty().compareTo(result);
		if(flag == Constant.INT_1) {
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,MessageId.QTY_EXCEEDS_THE_UPPER_LIMIT, new Object[]{result});
		}
	}

	/**
	 * 校验数量
	 * @param dto
	 * @return
	 */
	private BigDecimal checkBomQty(BSmtBomDetailVirtualDTO dto) throws Exception {
		BigDecimal multiplySum = new BigDecimal(Constant.INT_0);
		if(dto == null){
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,MessageId.AMOUNT_EXCEPTION);
		}
		//校验数量是否大于bom用量*拼板数
		for (int i = 0; i < dto.getChildQtyCollectionList().size(); i++) {
			BigDecimal temp = dto.getChildQtyCollectionList().get(i).getChildBomQty()
					.multiply(dto.getChildQtyCollectionList().get(i).getStandardBomQty());
			multiplySum = temp.add(multiplySum);
		}
		BigDecimal bomCount = dto.getUsageCountMain().add(multiplySum).multiply(dto.getPcbQty());
		return bomCount;
	}

	/**
	 * 城堡板获取物料代码用量
	 * @param orderListAll,itemCode
	 * @return
	 */
	private BSmtBomDetailVirtualDTO allBomQty(List<PsWorkOrderBasicDTO> orderListAll,String itemCode) throws Exception {
		// 设定一个实体类接所有的用量
		BSmtBomDetailVirtualDTO bSmtBomDetailVirtualDTO = new BSmtBomDetailVirtualDTO();
        //新建一个参数集合用于查询用量
		List<BBomDetailDTO> queryBomParamList = new ArrayList<>();
		// 主卡指令集合
		List<PsWorkOrderBasicDTO> mainOrder = orderListAll.stream().filter(e -> e.getChildCardFlag() != null && e.getChildCardFlag().equals(false)).collect(Collectors.toList());
		if(CollectionUtils.isEmpty(mainOrder)){
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,MessageId.WORKORDER_NOT_FIND);
		}
		List<BBomDetailDTO> mainBomParamList = new ArrayList<>();
		BBomDetailDTO bBomDetailDTO = new BBomDetailDTO();
		bBomDetailDTO.setProductCode(mainOrder.get(0).getItemNo());
		bBomDetailDTO.setItemCode(itemCode);
		mainBomParamList.add(bBomDetailDTO);
		// 子卡指令集合
		orderListAll.removeAll(mainOrder);
        //子卡料单代码+物料代码条件拼接
		List<BBomDetailDTO> childBomParamList = new ArrayList<>();
		for (int i = 0; i < orderListAll.size(); i++) {
			BBomDetailDTO bBomDetailDTO1 = new BBomDetailDTO();
			bBomDetailDTO1.setProductCode(orderListAll.get(i).getItemNo());
			bBomDetailDTO1.setItemCode(itemCode);
			childBomParamList.add(bBomDetailDTO1);
		}
		//主卡料单代码+子卡料单代码拼接
		List<BBomDetailDTO> standardBomParamList = new ArrayList<>();
		for (int i = 0; i < orderListAll.size(); i++) {
			BBomDetailDTO bBomDetailDTO2 = new BBomDetailDTO();
			// 料单代码为主卡料单代码
			bBomDetailDTO2.setProductCode(mainOrder.get(0).getItemNo());
			// 此时的子卡料单代码作为物料代码在主卡的料单代码下查标准用量
			bBomDetailDTO2.setItemCode(orderListAll.get(i).getItemNo());
			standardBomParamList.add(bBomDetailDTO2);
		}
		//所有的查询条件拼接在一起
		queryBomParamList.addAll(mainBomParamList);
		queryBomParamList.addAll(childBomParamList);
		queryBomParamList.addAll(standardBomParamList);
        //调用接口一次性返回所有的BOM用量
		List<BBomDetailDTO> allBomQtyList = CenterfactoryRemoteService.getNumByProductCodeAndItemCode(queryBomParamList);
		//主卡+物料代码匹配BOM结果,获取物料代码在主板用量
		List<BBomDetailDTO> mainBomQty = allBomQtyList.stream().filter(e -> !StringUtils.isEmpty(e.getProductCode()) && !StringUtils.isEmpty(e.getItemCode())
						&& e.getProductCode().equals(bBomDetailDTO.getProductCode()) && e.getItemCode().equals(bBomDetailDTO.getItemCode())).collect(Collectors.toList());
		if(CollectionUtils.isEmpty(mainBomQty)){
			//如果匹配不到令主板数量为0
			bSmtBomDetailVirtualDTO.setUsageCountMain(new BigDecimal(Constant.INT_0));
		}else{
			//设定物料代码在主板的用量
			bSmtBomDetailVirtualDTO.setUsageCountMain(mainBomQty.get(0).getUsageCount());
		}
		List<BSmtBomDetailChildDTO> childBomQtyCollection = new ArrayList<>();
		//子卡+物料代码匹配BOM结果,获取物料代码在子板用量
		for (int i = 0; i < childBomParamList.size(); i++) {
			//设定一个集合来统计子板用量标准用量
			BSmtBomDetailChildDTO dto = new BSmtBomDetailChildDTO();
			int finalI = i;
			//子卡+物料代码匹配BOM结果,获取物料代码在子板用量
			List<BBomDetailDTO> childBomList = allBomQtyList.stream().filter(e -> !StringUtils.isEmpty(e.getProductCode()) && !StringUtils.isEmpty(e.getItemCode())
							&& e.getProductCode().equals(childBomParamList.get(finalI).getProductCode()) && e.getItemCode().equals(bBomDetailDTO.getItemCode())).collect(Collectors.toList());
			if(childBomList.size() == Constant.INT_0){
				dto.setStandardBomQty(new BigDecimal(Constant.INT_0));
				dto.setChildBomQty(new BigDecimal(Constant.INT_0));
				childBomQtyCollection.add(dto);
				continue;
			}
			//主卡料单代码+主卡料单代码匹配BOM结果,获取子板标准用量
			List<BBomDetailDTO> standardBomList = allBomQtyList.stream().filter(e -> !StringUtils.isEmpty(e.getProductCode()) && !StringUtils.isEmpty(e.getItemCode())
							&& e.getProductCode().equals(bBomDetailDTO.getProductCode()) && e.getItemCode().equals(childBomParamList.get(finalI).getProductCode())).collect(Collectors.toList());
			if(standardBomList.size() == Constant.INT_0){
				throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,MessageId.STANDARD_QTY_IS_NULL, new Object[]{childBomList.get(i).getProductCode(),itemCode});
			}
			dto.setChildBomQty(childBomList.get(0).getUsageCount());
			dto.setStandardBomQty(standardBomList.get(0).getUsageCount());
			childBomQtyCollection.add(dto);
		}
		bSmtBomDetailVirtualDTO.setChildQtyCollectionList(childBomQtyCollection);
		return bSmtBomDetailVirtualDTO;
	}

	/**
	 * 根据ItemCode更新虚拟站位物料数量
	 * @param dto
	 * @return
	 */
	@Override
	public int updateQtyByItemCode(BSmtBomDetailDTO dto) throws Exception {
		this.checkQty(dto);
		return bSmtBomDetailRepository.updateQtyByItemCode(dto);
	}


	/**
	 * 根据ItemCode新增虚拟站位物料
	 * @param dto
	 * @return
	 */
	@Override
	public int insertItemCode(BSmtBomDetailDTO dto) throws Exception {
		// 查询拼板数
		PsWorkOrderSmt psWorkOrderSmt = this.queryQtyAndWorkOrderByCfgHeaderId(dto);
		// 获得拼板数
		BigDecimal pieceQty = psWorkOrderSmt.getPcbQty();
		// 获得指令
		String workOrders = psWorkOrderSmt.getWorkOrderNo();
		// 校验在上料表中是否已经存在
		List<BSmtBomDetail> checkItemCode = bSmtBomDetailRepository.checkByItemCode(dto);
		if (!CollectionUtils.isEmpty(checkItemCode)) {
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,MessageId.ITEM_CODE_IS_EXISTS_IN_TABLE, new Object[]{dto.getItemCode()});
		}
		// 查询当前物料代码在主卡料单代码的BOM表中是否存在的条件拼装
		List<BBomDetailDTO> checkList = new ArrayList<>();
		BBomDetailDTO dto1 = new BBomDetailDTO();
		dto1.setItemCode(dto.getItemCode());
		dto1.setProductCode(dto.getProductCode());
		checkList.add(dto1);
		// 查询当前物料代码在子卡料单代码的BOM表中是否存在条件拼装
		List<PsWorkOrderBasicDTO> orderListAll = ProductionDeliveryRemoteService.getChildPsTask(psWorkOrderSmt.getWorkOrderNo());
		if(CollectionUtils.isEmpty(orderListAll)){
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,MessageId.WORKORDER_NOT_FIND);
		}
		List<PsWorkOrderBasicDTO> childOrder = orderListAll.stream().filter(e -> e.getChildCardFlag() != null && e.getChildCardFlag().equals(true)).collect(Collectors.toList());
		List<BBomDetailDTO> checkChlidList = new ArrayList<>();
		if(!CollectionUtils.isEmpty(childOrder)){
			for (int i = 0; i < childOrder.size(); i++){
				BBomDetailDTO dto2 = new BBomDetailDTO();
				dto2.setItemCode(dto.getItemCode());
				dto2.setProductCode(childOrder.get(i).getItemNo());
				checkChlidList.add(dto2);
			}
		}
		checkList.addAll(checkChlidList);
		List<BBomDetailDTO> checkBom = CenterfactoryRemoteService.getNumByProductCodeAndItemCode(checkList);
		if (CollectionUtils.isEmpty(checkBom)) {
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,MessageId.ITEM_CODE_IS_NOT_EXISTS_IN_TABLE, new Object[]{dto.getItemCode()});
		}
		// 数量校验
		this.checkQty(dto);
		// 自动分配下一个未使用的虚拟站位
		// 取配置：手补料虚拟前缀
		String prefix = getVirtualPrefix();
        // 获取虚拟位号起始值
		AtomicInteger atomicInt = getStartVirtualNo(dto);
		// 更新数据至上料表中
		dto.setMachineNo(Constant.VIRTUAL_ITEM_MACHINE_NO);
		dto.setModuleNo(Constant.VIRTUAL_ITEM_MODULE_NO);
		dto.setLocationNo(assembleLocationNo(prefix, atomicInt.incrementAndGet()));
		int insertCnt = bSmtBomDetailRepository.insertMaterialRecord(dto);
		return insertCnt;
	}

	/**
	 * 获取前缀
	 */
	private String getVirtualPrefix() {
		List<SysLookupValuesDTO> values = BasicsettingRemoteService.getLookupValueByTypeCodes(Constant.LOOKUP_6104);
		if (org.apache.commons.collections.CollectionUtils.isEmpty(values)) {
			return Constant.VIRTUAL_PREFIX;
		}
		String prefix = values.get(Constant.INT_0).getLookupMeaning();
		return StringUtils.isNotBlank(prefix) ? prefix : Constant.VIRTUAL_PREFIX;
	}

	/**
	 * 获取起始值
	 */
	private AtomicInteger getStartVirtualNo(BSmtBomDetailDTO dto) {
		PsWorkOrderDTO dto1 = new PsWorkOrderDTO();
		dto1.setCfgHeaderId(dto.getCfgHeaderId());
		dto1.setLineCode(dto.getLineCode());
		dto1.setItemNo(dto.getProductCode());
		dto1.setSourceTask(dto.getProdplanId());
		// 单面导入时，获取另一面有效虚拟位号的最大值otherMaxNo，otherMaxNo+1开始
		List<String> otherVirtualNos = bSmtBomDetailRepository.queryVirtualNo(dto1);
		if (org.apache.commons.collections.CollectionUtils.isEmpty(otherVirtualNos)) {
			return new AtomicInteger(Constant.INT_0);
		}
		int otherMaxNo = otherVirtualNos.stream().mapToInt(vn -> {
			try {
				return Integer.parseInt(vn.split(Constant.BAR)[Constant.INT_1]);
			} catch (Exception e) {
				return Constant.INT_0;
			}
		}).max().orElse(Constant.INT_0);
		return new AtomicInteger(otherMaxNo);
	}

	/**
	 * 拼装得到下一个未使用的虚拟站位
	 */
	private String assembleLocationNo(String prefix, int i) {
		// 查询按排序LocationNo，需要补0
		return prefix + Constant.V_MODULE_NO + Constant.BAR +
				(i < NumConstant.NUM_100 ? Constant.STR_0 +
						(i < NumConstant.NUM_TEN ? Constant.STR_0 + i : i) : i);
	}
	/**
	 * 根据ItemCode删除虚拟站位物料
	 * @param record
	 * @return
	 */
	@Override
	public int deleteVirtualBomDetailByItemCode(BSmtBomDetailDTO record) {
		return bSmtBomDetailRepository.deleteVirtualBomDetailByItemCode(record);
	}

	@Override
	public List<BSmtBomDetail> selectBSmtBomDetailByIdSetAndItemNo(Set<String> cfgHeadIdSet, String itemNo) {
		return bSmtBomDetailRepository.selectBSmtBomDetailByIdSetAndItemNo(cfgHeadIdSet, itemNo);
	}

	@Override
	public int getBomQtyByMtInfo(SmtMachineMaterialMouting mtInfo) {
		Integer bomQty = bSmtBomDetailRepository.getBomQtyByMtInfo(mtInfo);
		return bomQty == null ? Constant.INT_0 : bomQty;
	}

	@Override
	public List<BSmtBomDetail> getListForOneSide(Map<String, Object> record) throws Exception {
		return bSmtBomDetailRepository.getListForOneSide(record);
	}

	@Override
	public List<BSmtBomDetail> getBomDetailByCondition(BSmtBomDetailDTO record) throws Exception {
		return bSmtBomDetailRepository.getBomDetailByCondition(record);
	}

	@Override
	public String getBomDetailByMouting(BSmtBomDetailDTO record) throws Exception {
		return bSmtBomDetailRepository.getBomDetailByMouting(record);
	}

	@Override
	public List<BSmtBomDetail> getLocationAndQty(String cfgHeaderId, List<String> machineNoList) {
		return bSmtBomDetailRepository.getLocationAndQty(cfgHeaderId, machineNoList);
	}

	@Override
	public String getEndMachineNo(String cgfHeaderId){
		return bSmtBomDetailRepository.getEndMachineNo(cgfHeaderId);
	}

	@Override
	public String checkCommonItemByReelId(BSmtBomDetailDTO dto) {
		// 查询是否共用料
		int count = bSmtBomDetailRepository.countBomDetailByReelId(dto);
		if(count < Constant.INT_2){
			return StringUtils.EMPTY;
		}
		Map<String, Object> map = new HashMap<>();
		map.put(Constant.LINE_CODE, dto.getLineCode());
		map.put(Constant.PRODPLAN_ID, dto.getProdplanId());
		map.put(Constant.WORK_ORDER_STATUS_LIST_NEW,new ArrayList<String>(){{add(Constant.IS_HANG_UP);}});
		map.put(Constant.CRAFT_SECTIONS,new ArrayList<String>(){{add(Constant.SMT_A);add(Constant.SMT_B);}});
		// 查询是否有挂起指令
		int hangUpWorkOrderCount = PlanscheduleRemoteService.getWorkOrderInfoCount(map);
		if(hangUpWorkOrderCount > Constant.INT_0){
			// 此物料为共用物料，请选择需要切换的指令
			return Constant.FLAG_Y;
		}
		return StringUtils.EMPTY;
	}

	@Override
	public List<BSmtBomDetail> getBSmtBomDetailBindFeederListNew(PdaFeederBindDTO dto) {
		List<BSmtBomDetail> bSmtBomDetailBindFeederList = new ArrayList<>();
		if(StringUtils.isBlank(dto.getCfgHeaderId())){
			return bSmtBomDetailBindFeederList;
		}

		// 查询指令上料表信息
		BSmtBomDetailDTO entity = new BSmtBomDetailDTO();
		entity.setCfgHeaderId(dto.getCfgHeaderId());
		entity.setWorkOrder(dto.getWorkOrder());
		entity.setLineCode(dto.getLineCode());
		entity.setVirtualFlag(Constant.FLAG_N);
		entity.setFactoryId(dto.getFactoryId());
		entity.setEntityId(dto.getEntityId());
		bSmtBomDetailBindFeederList = bSmtBomDetailRepository.getBSmtBomDetailBindFeederList(entity);

		// 当面没有物料
		if(CollectionUtils.isEmpty(bSmtBomDetailBindFeederList)){
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.CURRENT_NO_ITEMS);
		}
		// 设置指令
		bSmtBomDetailBindFeederList.stream().forEach(x -> x.setWorkOrder(dto.getWorkOrder()));
		List<BSmtBomDetail> otherBomDetails = new ArrayList<>();
		// AB面同时绑定，且AB面指令上料表ID不为空
		if(dto.getmIsBothSides() && StringUtils.isNotBlank(dto.getmOtherSideCfgHeaderId())){
			entity.setCfgHeaderId(dto.getmOtherSideCfgHeaderId());
			entity.setWorkOrder(dto.getmOtherSideWorkOrderNo());
			otherBomDetails = bSmtBomDetailRepository.getBSmtBomDetailBindFeederList(entity);
		}
		// 根据机台和站位去重
		if(!CollectionUtils.isEmpty(otherBomDetails)){
			List<String> collect = bSmtBomDetailBindFeederList.stream().map(e -> e.getMachineNo() + e.getLocationNo()).collect(Collectors.toList());
			List<BSmtBomDetail> filterOtherDetails = otherBomDetails.stream().filter(x -> !collect.contains(x.getMachineNo() + x.getLocationNo())).collect(Collectors.toList());
			if(!CollectionUtils.isEmpty(filterOtherDetails)){
				// 设置指令
				filterOtherDetails.stream().forEach(x -> x.setWorkOrder(dto.getmOtherSideWorkOrderNo()));
				bSmtBomDetailBindFeederList.addAll(filterOtherDetails);
			}
		}
		bSmtBomDetailBindFeederList.sort(Comparator.comparing(BSmtBomDetail::getModuleNo));
		return bSmtBomDetailBindFeederList;
	}
}
