/*Started by AICoder, pid:a85596df635566d14e07096dd0b1e055f695cbeb*/
package com.zte.application.datawb.impl;

import com.alibaba.fastjson.JSON;
import com.zte.application.datawb.WsmAssembleLinesService;
import com.zte.common.CommonUtils;
import com.zte.common.utils.DateUtil;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.interfaces.dto.ZmsByteDanceMREntityDTO;
import com.zte.springbootframe.util.json.JacksonJsonConverUtil;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.List;

@RunWith(PowerMockRunner.class)
@PrepareForTest({CenterfactoryRemoteService.class,
        WsmAssembleLinesService.class, JacksonJsonConverUtil.class,
        JSON.class, CommonUtils.class, DateUtil.class})
@PowerMockIgnore({"javax.net.ssl.*", "javax.security.*"})
public class ZmsByteDanceMRServiceImpl_pushDataToB2B_4_Test {

    @InjectMocks
    private ZmsByteDanceMRServiceImpl zmsByteDanceMRService;

    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Before
    public void setUp() throws Exception {
        ReflectionTestUtils.setField(zmsByteDanceMRService, "b2bSize", 100);
    }

    @Test(timeout = 8000)
    public void testPushDataToB2B_EmptyList() throws Exception {
        List<ZmsByteDanceMREntityDTO> list = new ArrayList<>();
        ZmsByteDanceMREntityDTO zmsByteDanceMREntityDTO = new ZmsByteDanceMREntityDTO();
        zmsByteDanceMREntityDTO.setDataDate("20240101");
        zmsByteDanceMREntityDTO.setDataTransferBatchNo("123");
        list.add(zmsByteDanceMREntityDTO);
        zmsByteDanceMRService.pushDataToB2B("test", list);
        Assert.assertNull(null);
    }
}
/*Ended by AICoder, pid:a85596df635566d14e07096dd0b1e055f695cbeb*/