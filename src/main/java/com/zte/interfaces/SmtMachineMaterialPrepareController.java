/**
 * 项目名称 : ${project_name}
 * 创建日期 : ${date}
 * 修改历史 :
 *   1. [${date}] 创建文件 by ${user}
 **/
package com.zte.interfaces;

import com.zte.application.*;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.NumConstant;
import com.zte.common.utils.ServiceDataUtil;
import com.zte.domain.model.PkCodeInfo;
import com.zte.domain.model.PsEntityPlanBasic;
import com.zte.domain.model.SmtMachineMaterialPrepare;
import com.zte.interfaces.assembler.SmtMachineMaterialPrepareAssembler;
import com.zte.interfaces.dto.SmtMachineMaterialPrepareDTO;
import com.zte.interfaces.dto.PdaCompositePrepareDTO;
import com.zte.itp.msa.core.constant.SysGlobalConst;
import com.zte.itp.msa.core.exception.ValidationException;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.string.StringHelper;
import com.zte.springbootframe.common.annotation.OpenApi;
import com.zte.springbootframe.common.annotation.RecordLogAnnotation;
import com.zte.springbootframe.common.consts.SysConst;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.FactoryConfig;
import com.zte.springbootframe.util.RequestHeadValidationUtil;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import io.swagger.annotations.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.util.Pair;
import org.springframework.http.MediaType;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * // TODO 添加类/接口功能描述
 * 
 * <AUTHOR>
 **/
@RestController
@RequestMapping("/SmtMachineMaterialPrepareCtrl")
@Api(description="SMT防错料-SMT提前备料")
public class SmtMachineMaterialPrepareController {
    @Autowired
    private SmtMachineMaterialPrepareService smtMachineMaterialPrepareService;
    @Autowired
    private SmtMachineMaterialMoutingService smtMachineMaterialMoutingService;
    @Autowired
    private SmtMachineMtlOnlineStandbyService smtMachineMtlOnlineStandbyService;
    @Autowired
    private BSmtBomDetailService bSmtBomDetailService;
    @Autowired
    private SmtMachineMaterialReturnService smtMachineMaterialReturnService;

    @Autowired
    private PkCodeInfoService pkCodeInfoService;

    // 日志对象
    private static final Logger LOG = LoggerFactory.getLogger(SmtMachineMaterialPrepareController.class);

    @Autowired
    private FactoryConfig factoryConfig;

    
    /**
     * // TODO 添加方法功能描述
     * @param request
     * @param dto
     * @return ServiceData
     **/
    @ApiOperation("新增一条SMT提前备料信息")
    @RequestMapping(value = "/smtMachineMaterialPrepare",method = RequestMethod.POST,consumes = MediaType.APPLICATION_JSON_VALUE,produces = MediaType.APPLICATION_JSON_VALUE)
    public ServiceData insert(HttpServletRequest request, @RequestBody SmtMachineMaterialPrepareDTO dto) {
        ServiceData ret = new ServiceData();
        SmtMachineMaterialPrepare entity = SmtMachineMaterialPrepareAssembler.toEntity(dto);
        String tempId = java.util.UUID.randomUUID().toString();
        String empNo = request.getHeader(SysGlobalConst.HTTP_HEADER_X_EMP_NO);
        if(org.apache.commons.lang3.StringUtils.isBlank(entity.getMtlPrepareId())){
            entity.setMtlPrepareId(tempId);
        }
        entity.setCreateUser(empNo);
        entity.setLastUpdatedBy(empNo);
        String factoryId = request.getHeader(SysConst.HTTP_HEADER_X_FACTORY_ID);
        if (StringHelper.isNotEmpty(factoryId)) {
            entity.setFactoryId(new BigDecimal(factoryId));
        }
        if (StringHelper.isNotEmpty(factoryConfig.getCommonEntityId())) {
            entity.setEntityId(new BigDecimal(factoryConfig.getCommonEntityId()));
        }
        int count =smtMachineMaterialPrepareService.insertSmtMachineMaterialPrepare(entity);
        ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        ret.setBo(count);
        return ret;
    }

    /**
     * // 删除SMT提前备料信息
     * @param request
     * @param dto
     * @return ServiceData
     **/
    @ApiOperation("删除SMT提前备料信息")
    @RequestMapping(value = "/smtMachineMaterialPrepare",method = RequestMethod.DELETE,produces = MediaType.APPLICATION_JSON_VALUE)
    public ServiceData delete(HttpServletRequest request, @RequestBody SmtMachineMaterialPrepareDTO dto) {
        ServiceData ret = new ServiceData();
        SmtMachineMaterialPrepare entity = new SmtMachineMaterialPrepare();
        entity.setMtlPrepareId(dto.getMtlPrepareId());
        entity.setWorkOrder(dto.getWorkOrder());
        entity.setLineCode(dto.getLineCode());
        entity.setLocationNo(dto.getLocationNo());
        entity.setObjectId(dto.getObjectId());
        // String empNo = request.getHeader(SysGlobalConst.HTTP_HEADER_X_EMP_NO);
        // entity.setLastUpdatedBy(empNo);
        smtMachineMaterialPrepareService.deleteSmtMachineMaterialPrepareById(entity);
        return ret;
    }

    /**
     * // 更新SMT提前备料信息
     * @param request
     * @param dto
     * @return ServiceData
     **/
    @ApiOperation("更新SMT提前备料信息")
    @RequestMapping(value = "/smtMachineMaterialPrepare",method = RequestMethod.PUT,consumes = MediaType.APPLICATION_JSON_VALUE,produces = MediaType.APPLICATION_JSON_VALUE)
    public ServiceData update(HttpServletRequest request, @RequestBody SmtMachineMaterialPrepareDTO dto) {
        ServiceData ret = new ServiceData();
        SmtMachineMaterialPrepare entity = SmtMachineMaterialPrepareAssembler.toEntity(dto);
        String empNo = request.getHeader(SysGlobalConst.HTTP_HEADER_X_EMP_NO);
        entity.setLastUpdatedBy(empNo);
        //smtMachineMaterialPrepareService.updateSmtMachineMaterialPrepareById(entity);
		 int count=smtMachineMaterialPrepareService.updateSmtMachineMaterialPrepareByIdSelective(entity);
       
        ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        ret.setBo(count);
        return ret;
    }

    /**
     * // 逻辑删除SMT提前备料信息
     * <AUTHOR>
     * @param request
     * @param dto
     * @return ServiceData
     **/
    @ApiOperation("逻辑删除SMT提前备料信息")
    @RecordLogAnnotation("逻辑删除SMT提前备料信息")
    @RequestMapping(value = "/deleteSmtMachineMaterialPrepare",method = RequestMethod.PUT,consumes = MediaType.APPLICATION_JSON_VALUE,produces = MediaType.APPLICATION_JSON_VALUE)
    public ServiceData deleteSmtMachineMaterialPrepare(HttpServletRequest request, @RequestBody SmtMachineMaterialPrepareDTO dto) {
        ServiceData ret = new ServiceData();
        SmtMachineMaterialPrepare entity = SmtMachineMaterialPrepareAssembler.toEntity(dto);
        String empNo = request.getHeader(SysGlobalConst.HTTP_HEADER_X_EMP_NO);
        entity.setLastUpdatedBy(empNo);
        int count=smtMachineMaterialPrepareService.deleteSmtMachineMaterialPrepareBySelective(entity);

        ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        ret.setBo(count);
        return ret;
    }

    @ApiOperation("按ID查询一条SMT提前备料信息")
    @RequestMapping(value = "/smtMachineMaterialPrepare/{id}",method = RequestMethod.GET,produces = MediaType
            .APPLICATION_JSON_VALUE)
    public ServiceData get(HttpServletRequest request, @PathVariable String id) {
        ServiceData ret = new ServiceData();
        SmtMachineMaterialPrepare entity =  new SmtMachineMaterialPrepare();
        entity.setMtlPrepareId(id);
        String empNo = request.getHeader(SysGlobalConst.HTTP_HEADER_X_EMP_NO);
        entity.setLastUpdatedBy(empNo);
        smtMachineMaterialPrepareService.selectSmtMachineMaterialPrepareById(entity);
        return ret;
    }
    
    /**
     * 获取feeder相关SMT提前备料信息
     * <AUTHOR>
     * @param request
     * @param
     * @return ServiceData
     **/
    @ApiOperation("获取feeder相关SMT提前备料信息")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "workOrder", dataType = "java.lang.String",
                    required = false, value = "指令", defaultValue = ""),
            @ApiImplicitParam(paramType = "query", name = "moduleNo", dataType = "java.lang.String",
                    required = false, value = "模组编码", defaultValue = ""),
            @ApiImplicitParam(paramType = "query", name = "feederNo", dataType = "java.lang.String",
                    required = false, value = "Feeder编码", defaultValue = ""),
            @ApiImplicitParam(paramType = "query", name = "locationNo", dataType = "java.lang.String",
            required = false, value = "站位编码", defaultValue = ""),
            @ApiImplicitParam(paramType = "query", name = "lineCode", dataType = "java.lang.String",
            required = false, value = "线体编码", defaultValue = ""),
            @ApiImplicitParam(paramType = "query", name = "orgId", dataType = "java.lang.Number",
            required = false, value = "组织id", defaultValue = ""),
            @ApiImplicitParam(paramType = "query", name = "factoryId", dataType = "java.lang.Number",
            required = false, value = "工厂id", defaultValue = ""),
            @ApiImplicitParam(paramType = "query", name = "entityId", dataType = "java.lang.Number",
            required = false, value = "实体id", defaultValue = "")
    })
    @ApiResponses({ @ApiResponse(code = 400, message = "请求参数没填好"),
        @ApiResponse(code = 404, message = "请求路径没有或页面跳转路径不对") })
    @RequestMapping(value = "/querySmtMachineMaterialPrepareByFeederRef",method = RequestMethod.GET,produces = MediaType.APPLICATION_JSON_VALUE)
    public ServiceData<SmtMachineMaterialPrepare> querySmtMachineMaterialPrepareByFeederRef(HttpServletRequest request, SmtMachineMaterialPrepareDTO dto) {
        ServiceData<SmtMachineMaterialPrepare> ret = new ServiceData<SmtMachineMaterialPrepare>();
        SmtMachineMaterialPrepare retItem = smtMachineMaterialPrepareService.selectSmtMachineMaterialPrepareByFeederRef(dto);
        ret.setBo(retItem);
        return ret;
    }

    /**
     * 批量新增SMT提前备料信息
     * @param request
     * @param listDto
     * @return
     */
    @ApiOperation("批量新增SMT提前备料信息")
    @RequestMapping(value = "/smtMachineMaterialPrepareBatch",method = RequestMethod.POST,consumes = MediaType.APPLICATION_JSON_VALUE,produces = MediaType.APPLICATION_JSON_VALUE)
    public ServiceData insertSmtMachineMaterialPrepareBatch(HttpServletRequest request, @RequestBody List<SmtMachineMaterialPrepareDTO> listDto) {
        ServiceData ret = new ServiceData();
        if(CollectionUtils.isEmpty(listDto)) {
            ret.setCode(new RetCode(RetCode.VALIDATIONERROR_CODE, RetCode.VALIDATIONERROR_MSGID));
            ret.setBo(CommonUtils.getLmbMessage(MessageId.DATA_NOT_FOUND));
            return ret;
        }

        String empNo = request.getHeader(SysGlobalConst.HTTP_HEADER_X_EMP_NO);
        String factoryId = request.getHeader(SysConst.HTTP_HEADER_X_FACTORY_ID);
        String entityId = factoryConfig.getCommonEntityId();
        for(SmtMachineMaterialPrepareDTO wip : listDto) {
            wip.setLastUpdatedBy(empNo);
            String tempId = java.util.UUID.randomUUID().toString();
            wip.setMtlPrepareId(tempId);
            wip.setCreateUser(empNo);
            if (StringHelper.isNotEmpty(factoryId)) {
                wip.setFactoryId(new BigDecimal(factoryId));
            }
            if (StringHelper.isNotEmpty(entityId)) {
                wip.setEntityId(new BigDecimal(entityId));
            }
        }
        smtMachineMaterialPrepareService.insertSmtMachineMaterialPrepareBatch(listDto);
        ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        ret.setBo(NumConstant.NUM_ONE);
        return ret;
    }

    /**
     * 批量保存SMT提前备料信息
     * @param request
     * @param listDto
     * @return
     */
    @ApiOperation("批量保存SMT提前备料信息")
    @RequestMapping(value = "/pdaBatchSaveSmtMachineMaterialPrepare",method = RequestMethod.POST,consumes = MediaType.APPLICATION_JSON_VALUE,produces = MediaType.APPLICATION_JSON_VALUE)
    public ServiceData pdaBatchSaveSmtMachineMaterialPrepare(HttpServletRequest request, @RequestBody List<SmtMachineMaterialPrepareDTO> listDto) {
        ServiceData ret = new ServiceData();
        if(CollectionUtils.isEmpty(listDto)) {
            ret.setCode(new RetCode(RetCode.VALIDATIONERROR_CODE, RetCode.VALIDATIONERROR_MSGID));
            ret.setBo(CommonUtils.getLmbMessage(MessageId.DATA_NOT_FOUND));
            return ret;
        }
        List<SmtMachineMaterialPrepareDTO> listAddDTO = new ArrayList<SmtMachineMaterialPrepareDTO>();
        List<SmtMachineMaterialPrepareDTO> listModifyDTO = new ArrayList<SmtMachineMaterialPrepareDTO>();
        String empNo = request.getHeader(SysGlobalConst.HTTP_HEADER_X_EMP_NO);
        String factoryId = request.getHeader(SysConst.HTTP_HEADER_X_FACTORY_ID);
        String entityId = factoryConfig.getCommonEntityId();
        for(SmtMachineMaterialPrepareDTO wip : listDto){
            wip.setLastUpdatedBy(empNo);
            long count = smtMachineMaterialPrepareService.pdaSelectCountSmtMachineMaterialPrepare(wip);
            if(count>0){
                listModifyDTO.add(wip);
            }
            else{
                String tempId = java.util.UUID.randomUUID().toString();
                wip.setMtlPrepareId(tempId);
                wip.setCreateUser(empNo);
                if (StringHelper.isNotEmpty(factoryId)) {
                    wip.setFactoryId(new BigDecimal(factoryId));
                }
                if (StringHelper.isNotEmpty(entityId)) {
                    wip.setEntityId(new BigDecimal(entityId));
                }
                listAddDTO.add(wip);
            }
        }
        if(!CollectionUtils.isEmpty(listAddDTO)){
            smtMachineMaterialPrepareService.insertSmtMachineMaterialPrepareBatch(listAddDTO);
        }
        if(!CollectionUtils.isEmpty(listModifyDTO)){
            smtMachineMaterialPrepareService.pdaBatchUpdateSmtMachineMaterialPrepare(listModifyDTO);
        }
        ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        ret.setBo(NumConstant.NUM_ONE);
        return ret;
    }
    
    /**
     * 新增或更新备料Feeder绑定信息
     * <AUTHOR>
     * @param request
     * @param dto
     * @return ServiceData
     **/
    @ApiOperation("新增或更新备料Feeder绑定信息")
    @ApiResponses({ @ApiResponse(code = 400, message = "请求参数没填好"),
        @ApiResponse(code = 404, message = "请求路径没有或页面跳转路径不对") })
    @RequestMapping(value = "/saveOrUpdateSmtMachineMaterialPrepareByFeederRef",method = RequestMethod.POST,
        consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ServiceData<String> saveOrUpdateSmtMachineMaterialPrepareByFeederRef(HttpServletRequest request, @Validated @RequestBody SmtMachineMaterialPrepareDTO dto,
            BindingResult result) throws Exception {
        if(null != result && result.hasErrors()) {
            throw new ValidationException(result);
        }
        ServiceData<String> ret = new ServiceData<String>();
        RetCode retCode = new RetCode(RetCode.BUSINESSERROR_CODE,RetCode.BUSINESSERROR_MSGID);
        String empNo = request.getHeader(SysGlobalConst.HTTP_HEADER_X_EMP_NO);
        dto.setCreateUser(empNo);
        dto.setCreateDate(new Date());
        BigDecimal entityId = dto.getEntityId();
        if(entityId != null) {
            dto.setEntityId(entityId);
        } else {
            dto.setEntityId(new BigDecimal(factoryConfig.getCommonEntityId()));
        }
        String factoryId = request.getHeader(SysConst.HTTP_HEADER_X_FACTORY_ID);
        if(StringHelper.isEmpty(factoryId)) {
            BigDecimal factoryIdBd = dto.getFactoryId();
            if(factoryIdBd == null || factoryIdBd.intValue() < 1) {
                retCode.setMsg(CommonUtils.getLmbMessage(MessageId.FACTORY_ID_IS_NULL));
                ret.setCode(retCode);
                return ret;
            } else {
                factoryId = factoryIdBd.toString();
            }
        }
        dto.setFactoryId(new BigDecimal(factoryId));
        String operateMsg = dto.getOperateMsg();
        if(operateMsg != null) {
            operateMsg = CommonUtils.getFromBase64(operateMsg);
            dto.setOperateMsg(operateMsg);
        }
        smtMachineMaterialPrepareService.saveOrUpdateSmtMachineMaterialPrepareByFeederRef(dto);
        retCode = new RetCode(RetCode.SUCCESS_CODE,RetCode.SUCCESS_MSGID);
        ret.setCode(retCode);
        return ret;
    }
    
    /**
     * PDA Feeder解绑（实体id、工厂id由界面传递过来）
     * @param request
     * @param dto
     * @return ServiceData
     **/
    @ApiOperation("PDA Feeder解绑")
    @RequestMapping(value = "/unbundlingFeeder",method = RequestMethod.POST,consumes = MediaType.APPLICATION_JSON_VALUE,produces = MediaType.APPLICATION_JSON_VALUE)
    public ServiceData<Integer> unbundlingFeeder(HttpServletRequest request, @RequestBody SmtMachineMaterialPrepareDTO dto) throws Exception {
        ServiceData<Integer> ret = new ServiceData<Integer>();
        RequestHeadValidationUtil.validaFactoryIdAndEmpno(request);
        RetCode retCode = new RetCode(RetCode.BUSINESSERROR_CODE,RetCode.BUSINESSERROR_MSGID);
        SmtMachineMaterialPrepare entity = SmtMachineMaterialPrepareAssembler.toEntity(dto);
        entity.setLastUpdatedBy(request.getHeader(Constant.X_EMP_NO));
        int count = NumConstant.NUM_ZERO;
        try {
            count=smtMachineMaterialPrepareService.unbundlingFeeder(entity);
        } catch (Exception e) {
            String errTip = CommonUtils.getLmbMessage(MessageId.DEAL_FEEDER_BIND_INFO_ERROR);
            ret.setCode(retCode);
            retCode.setMsg(errTip);
            return ret;
        }
        ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        ret.setBo(count);
        return ret;
    }

    @ApiOperation("上料表id相同，删除SMT机台在用物料表mounting，更新SMT提前备料Prepare与SMT机台备用物料Standby表")
    @RequestMapping(value = "/refreshByWorkorder",method = RequestMethod.POST,consumes = MediaType.APPLICATION_JSON_VALUE,produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ServiceData refresh(HttpServletRequest request, @RequestBody PsEntityPlanBasic record){
        ServiceData ret = new ServiceData();
        smtMachineMaterialPrepareService.refreshByWorkorder(record);
        ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        return ret;
    }

    @ApiOperation("上料表id不同，根据物料SMT机台在用物料mounting，更新SMT提前备料Prepare与SMT机台备用物料Standby表")
    @RequestMapping(value = "/refreshByMaterial",method = RequestMethod.POST,consumes = MediaType.APPLICATION_JSON_VALUE,produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ServiceData refreshbyMaterial(HttpServletRequest request,@RequestBody List<PsEntityPlanBasic> list) throws Exception {
        ServiceData ret=new ServiceData();
        RetCode retCode = new RetCode(RetCode.BUSINESSERROR_CODE,RetCode.BUSINESSERROR_MSGID);
        try {
            smtMachineMaterialPrepareService.refreshbyMaterial(list);
        }catch (Exception e){
            String error = CommonUtils.getLmbMessage(MessageId.UPDATE_DATA_FAILED_CONVERSION_STRATEGY_THREE);
            LOG.error(error,e);
            ret.setCode(retCode);
            retCode.setMsg(error);
            return  ret;
        }
        ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        return ret;
    }

    @ApiOperation("根据批次间转产策略处理物料")
    @RequestMapping(value = "/transferMaterial", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ServiceData transferMaterial(HttpServletRequest request, @RequestBody List<PsEntityPlanBasic> planBasics) {
        ServiceData ret = new ServiceData();
        smtMachineMaterialPrepareService.transferMaterial(planBasics);
        ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        return ret;
    }

    /**
     * 通过条件查询信息
     * <AUTHOR> 600008583
     * @param request 请求
     * @param dto 实体
     * @return ret
     * @throws Exception 异常
     */
    @ApiOperation("SMT提前备料查询")
    @ApiResponses({ @ApiResponse(code = 400, message = "请求参数没填好"),
            @ApiResponse(code = 404, message = "请求路径没有或页面跳转路径不对") })
    @RequestMapping(value = "/getSmtAdvanceStockList", method = RequestMethod.GET,
    		produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
	public ServiceData<?> getRelList(HttpServletRequest request, SmtMachineMaterialPrepareDTO dto)
			throws Exception {
		String factoryId = request.getHeader(SysConst.HTTP_HEADER_X_FACTORY_ID);
		ServiceData<List<SmtMachineMaterialPrepare>> ret = new ServiceData<List<SmtMachineMaterialPrepare>>();
		if (factoryId != "" && factoryId != null) {
			dto.setFactoryId(new BigDecimal(factoryId));
		} else {
			throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, MessageId.FACTORY_ID_IS_NULL);
		}
		if (StringHelper.isNotEmpty(dto.getPage()) && StringHelper.isNotEmpty(dto.getRows()) && 0L != dto.getPage()
				&& 0L != dto.getRows()) {
			Long startRow = (dto.getPage() - 1) * dto.getRows() + 1;
			Long endRow = dto.getPage() * dto.getRows();
			dto.setStartRow(startRow);
			dto.setEndRow(endRow);
			return getPage(request, dto);
		}
		List<SmtMachineMaterialPrepare> itemList = smtMachineMaterialPrepareService.getRelOneList(dto);
		ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
		ret.setBo(itemList);
		return ret;
	}
    /**
     * 
     * 分页获取数据
     * <AUTHOR>
     * @param request 请求
     * @param dto 实体传输
     * @return ret
     * @throws Exception 异常
     */
	private ServiceData<?> getPage(HttpServletRequest request, SmtMachineMaterialPrepareDTO dto) throws Exception {
		// 设置查询条件
	    long total = smtMachineMaterialPrepareService.getRelCount(dto);
	    List<SmtMachineMaterialPrepare> list = smtMachineMaterialPrepareService.getRelOnePage(dto);
	    
	    PageRows<SmtMachineMaterialPrepare> page = new PageRows<SmtMachineMaterialPrepare>();
	    page.setCurrent(dto.getPage());
	    page.setTotal(total);
	    page.setRows(list);
	    // 返回统一的服务端数据
	    ServiceData<PageRows<SmtMachineMaterialPrepare>> ret = new ServiceData<PageRows<SmtMachineMaterialPrepare>>();
	    ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
	    ret.setBo(page);
	    return ret;
	}
	
    /**
     * 获取feeder相关SMT提前备料信息
     * <AUTHOR>
     * @param request
     * @param
     * @return ServiceData
     **/
    @ApiOperation("获取feeder相关SMT提前备料信息")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "workOrder", dataType = "java.lang.String",
                    required = false, value = "指令", defaultValue = ""),
            @ApiImplicitParam(paramType = "query", name = "moduleNo", dataType = "java.lang.String",
                    required = false, value = "模组编码", defaultValue = ""),
            @ApiImplicitParam(paramType = "query", name = "feederNo", dataType = "java.lang.String",
                    required = false, value = "Feeder编码", defaultValue = ""),
            @ApiImplicitParam(paramType = "query", name = "locationNo", dataType = "java.lang.String",
            required = false, value = "站位编码", defaultValue = ""),
            @ApiImplicitParam(paramType = "query", name = "lineCode", dataType = "java.lang.String",
            required = false, value = "线体编码", defaultValue = ""),
            @ApiImplicitParam(paramType = "query", name = "orgId", dataType = "java.lang.Number",
            required = false, value = "组织id", defaultValue = ""),
            @ApiImplicitParam(paramType = "query", name = "factoryId", dataType = "java.lang.Number",
            required = false, value = "工厂id", defaultValue = ""),
            @ApiImplicitParam(paramType = "query", name = "entityId", dataType = "java.lang.Number",
            required = false, value = "实体id", defaultValue = "")
    })
    @ApiResponses({ @ApiResponse(code = 400, message = "请求参数没填好"),
        @ApiResponse(code = 404, message = "请求路径没有或页面跳转路径不对") })
    @RequestMapping(value = "/checkFeeder",method = RequestMethod.GET,produces = MediaType.APPLICATION_JSON_VALUE)
    public ServiceData<SmtMachineMaterialPrepare> checkFeeder(HttpServletRequest request, SmtMachineMaterialPrepareDTO dto) {
        ServiceData<SmtMachineMaterialPrepare> ret = new ServiceData<SmtMachineMaterialPrepare>();
        RetCode retCode = smtMachineMaterialPrepareService.checkFeeder(dto);
        ret.setCode(retCode);
        return ret;
    }
    
    /**
     * 运维-退料操作
     * @param request
     * @param dto
     * @return ServiceData
     * @throws Exception 
     **/
    @ApiOperation("运维-退料操作")
    @RecordLogAnnotation("运维-退料操作")
    @RequestMapping(value = "/returnMaterialInfo",method = RequestMethod.POST,consumes = MediaType.APPLICATION_JSON_VALUE,produces = MediaType.APPLICATION_JSON_VALUE)
    public ServiceData returnMaterialInfo(HttpServletRequest request, @RequestBody SmtMachineMaterialPrepareDTO dto) throws Exception {
        ServiceData ret = new ServiceData();
        RetCode retCode = new RetCode(RetCode.BUSINESSERROR_CODE,RetCode.BUSINESSERROR_MSGID);
        String empNo = request.getHeader(SysGlobalConst.HTTP_HEADER_X_EMP_NO);
        String factoryId = request.getHeader(SysConst.HTTP_HEADER_X_FACTORY_ID);
        dto.setCreateUser(empNo);
        dto.setFactoryId(new BigDecimal(factoryId));
        retCode = smtMachineMaterialPrepareService.returnMaterialInfo(dto,request);
        
        ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        ret.setBo(Constant.MSG_OK);
        return ret;
    }

    /**
     * PDA Feeder解绑(新方法),删除prepare表数据,更新上料历史头为对比中,失效上料明细
     * @param request
     * @param dto
     * @return ServiceData
     **/
    @ApiOperation("PDA Feeder解绑(新方法)")
    @RecordLogAnnotation("PDA Feeder解绑(新方法)")
    @RequestMapping(value = "/unbindFeeder",method = RequestMethod.POST,consumes = MediaType.APPLICATION_JSON_VALUE,produces = MediaType.APPLICATION_JSON_VALUE)
    public ServiceData<Integer> unbindFeeder(HttpServletRequest request, @RequestBody SmtMachineMaterialPrepareDTO dto) {
        ServiceData<Integer> ret = new ServiceData<Integer>();
        RetCode retCode = new RetCode(RetCode.BUSINESSERROR_CODE,RetCode.BUSINESSERROR_MSGID);
        SmtMachineMaterialPrepare entity = SmtMachineMaterialPrepareAssembler.toEntity(dto);
        int count = NumConstant.NUM_ZERO;
        try {
            smtMachineMaterialPrepareService.unbindFeeder(entity);
        } catch (Exception e) {
            String errTip = CommonUtils.getLmbMessage(MessageId.DEAL_FEEDER_BIND_INFO_ERROR);
            ret.setCode(retCode);
            retCode.setMsg(errTip);
            return ret;
        }
        ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        ret.setBo(count);
        return ret;
    }

    @ApiOperation("查询SMT备料表")
    @RequestMapping(value = "/getSmtMachineMaterialPrepareList", method = RequestMethod.POST,produces = MediaType.APPLICATION_JSON_VALUE)
    public ServiceData<List<SmtMachineMaterialPrepare>> getSmtMachineMaterialPrepareList(HttpServletRequest request, @Validated @RequestBody SmtMachineMaterialPrepareDTO dto) throws Exception {
        ServiceData<List<SmtMachineMaterialPrepare>> ret = new ServiceData<List<SmtMachineMaterialPrepare>>();
        List<SmtMachineMaterialPrepare> list = smtMachineMaterialPrepareService.getSmtMachineMaterialPrepareList(dto);
        ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        ret.setBo(list);
        return ret;
    }

    @ApiOperation("生产领料交接-查询SMT备料表")
    @RequestMapping(value = "/getPrepareList", method = RequestMethod.GET,produces = MediaType.APPLICATION_JSON_VALUE)
    public ServiceData<List<SmtMachineMaterialPrepare>> getPrepareList(HttpServletRequest request, SmtMachineMaterialPrepareDTO dto) throws Exception {
        ServiceData<List<SmtMachineMaterialPrepare>> ret = new ServiceData<List<SmtMachineMaterialPrepare>>();
        List<SmtMachineMaterialPrepare> list = smtMachineMaterialPrepareService.getSmtMachineMaterialPrepareList(dto);
        ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        ret.setBo(list);
        return ret;
    }

    @ApiOperation("生产领料交接-扫描REELID")
    @RequestMapping(value = "/pickkingReelid", method = RequestMethod.GET,produces = MediaType.APPLICATION_JSON_VALUE)
    public ServiceData<List<SmtMachineMaterialPrepare>> pickkingReelid(HttpServletRequest request, SmtMachineMaterialPrepareDTO dto) throws Exception{
        ServiceData<List<SmtMachineMaterialPrepare>> ret = new ServiceData<List<SmtMachineMaterialPrepare>>();
        String factoryId=request.getHeader(SysConst.HTTP_HEADER_X_FACTORY_ID);
        if(StringUtils.isEmpty(factoryId)){
            ret.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.FACTORY_ID_IS_NULL));
            return ret;
        }
        dto.setFactoryId(new BigDecimal(factoryId));
        String empNo=request.getHeader(SysConst.HTTP_HEADER_X_EMP_NO);
        dto.setCreateUser(empNo);
        dto.setLastUpdatedBy(empNo);
        try {
            smtMachineMaterialPrepareService.pickkingReelid(dto);
        }catch (Exception e){
            e.printStackTrace();
            ret.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, e.getMessage()));
            return ret;
        }
        ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        return ret;
    }

    @ApiOperation("生产领料交接-查询SMT备料表")
    @RequestMapping(value = "/findPrepareList", method = RequestMethod.GET,produces = MediaType.APPLICATION_JSON_VALUE)
    public ServiceData<List<SmtMachineMaterialPrepare>> findPrepareList(HttpServletRequest request, SmtMachineMaterialPrepareDTO dto)throws Exception {
        ServiceData<List<SmtMachineMaterialPrepare>> ret = new ServiceData<List<SmtMachineMaterialPrepare>>();
        List<SmtMachineMaterialPrepare> list = smtMachineMaterialPrepareService.findSmtMachineMaterialPrepareList(dto);
        ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        ret.setBo(list);
        return ret;
    }

    @PostMapping("/unbindFeederByNo")
    public ServiceData unbindFeederByNo(@RequestBody SmtMachineMaterialPrepareDTO dto) throws MesBusinessException {
        return ServiceDataUtil.getSuccess(smtMachineMaterialPrepareService.unbindFeederByNo(dto));
    }

    @PostMapping("/unbindFeederByReelId")
    public ServiceData unbindFeederByReelId(@RequestBody SmtMachineMaterialPrepareDTO dto) throws MesBusinessException {
        return ServiceDataUtil.getSuccess(smtMachineMaterialPrepareService.unbindFeederByReelId(dto));
    }

    @PostMapping("/checkFeederAndReelId")
    public ServiceData checkFeederAndReelId(@RequestBody SmtMachineMaterialPrepareDTO dto) throws MesBusinessException {
        return ServiceDataUtil.getSuccess(smtMachineMaterialPrepareService.checkFeederAndReelId(dto));
    }

    @PostMapping("/bindFeeder")
    public ServiceData bindFeeder(@RequestBody SmtMachineMaterialPrepareDTO dto) throws MesBusinessException {
        // 查询pk_code 信息用于替换mouting表数据。
        if (dto != null && org.apache.commons.lang3.StringUtils.isNotBlank(dto.getObjectId())) {
            PkCodeInfo pkCodeInfo = pkCodeInfoService.getPkCodeInfoByCode(dto.getObjectId());
            if (pkCodeInfo != null) {
                dto.setPkCodeInfo(pkCodeInfo);
            }
        }
        return ServiceDataUtil.getSuccess(smtMachineMaterialPrepareService.bindFeeder(dto));
    }

    @GetMapping("/checkFeederNo")
    public ServiceData checkFeeder(@RequestParam("feederNo") String feederNo) throws Exception {
        return ServiceDataUtil.getSuccess(smtMachineMaterialPrepareService.checkFeederNo(feederNo));
    }

    @ApiOperation("PDA 查询模组下拉数据")
    @PostMapping("/getModuleSelectData")
    public ServiceData<List<PdaCompositePrepareDTO>> getModuleSelectData(HttpServletRequest request, @RequestBody PdaCompositePrepareDTO dto) throws Exception {
        // 校验工号和工厂ID
        RequestHeadValidationUtil.validaFactoryIdAndEmpno(request);
        String factoryId=request.getHeader(SysConst.HTTP_HEADER_X_FACTORY_ID);
        dto.setFactoryId(new BigDecimal(factoryId));
        ServiceData<List<PdaCompositePrepareDTO>> ret = new ServiceData<>();
        List<PdaCompositePrepareDTO> list = smtMachineMaterialPrepareService.getModuleSelectData(dto);
        ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        ret.setBo(list);
        return ret;
    }

    @ApiOperation("PDA 查询模组提前备料信息")
    @PostMapping("/getModulePrepareList")
    public ServiceData<List<SmtMachineMaterialPrepareDTO>> getModulePrepareList(HttpServletRequest request, @RequestBody PdaCompositePrepareDTO dto)throws Exception {
        // 校验工号和工厂ID
        RequestHeadValidationUtil.validaFactoryIdAndEmpno(request);
        String factoryId=request.getHeader(SysConst.HTTP_HEADER_X_FACTORY_ID);
        dto.setFactoryId(new BigDecimal(factoryId));
        return smtMachineMaterialPrepareService.getModulePrepareList(dto);
    }

    @ApiOperation("PDA 校验料盘信息")
    @PostMapping("/checkMaterialTray")
    public ServiceData checkMaterialTray(HttpServletRequest request, @RequestBody PdaCompositePrepareDTO dto)throws Exception {
        // 校验工号和工厂ID
        RequestHeadValidationUtil.validaFactoryIdAndEmpno(request);
        return ServiceDataUtil.getSuccess(smtMachineMaterialPrepareService.checkMaterialTray(dto));
    }

    @ApiOperation("PDA 保存综合备料信息")
    @PostMapping("/saveCompositePrepareInfo")
    public ServiceData saveCompositePrepareInfo(HttpServletRequest request, @RequestBody SmtMachineMaterialPrepareDTO dto)throws Exception {
        // 校验工号和工厂ID
        RequestHeadValidationUtil.validaFactoryIdAndEmpno(request);
        return smtMachineMaterialPrepareService.saveCompositePrepareInfo(dto);
    }

    @ApiOperation("获取提前备料指令")
    @PostMapping("/getPrepareOrderByWorkOrderStr")
    public ServiceData getPrepareOrderByWorkOrderStr(HttpServletRequest request, @RequestBody SmtMachineMaterialPrepareDTO dto)throws Exception {
        // 校验工号和工厂ID
        RequestHeadValidationUtil.validaFactoryIdAndEmpno(request);
        return ServiceDataBuilderUtil.success(smtMachineMaterialPrepareService.getPrepareOrderByWorkOrderStr(dto));
    }

    @ApiOperation("生产领料交接接口")
    @PostMapping("/pickkingReelidAndConfirm")
    @OpenApi(name = "生产领料交接确认接口", consumer = {"部件（陈军）"})
    public ServiceData pickkingReelidAndConfirm(HttpServletRequest request, @RequestBody SmtMachineMaterialPrepareDTO dto)throws Exception {
        Pair<String, String> pair = RequestHeadValidationUtil.validaFactoryIdAndEmpno(request);
        dto.setCreateUser(pair.getSecond());
        dto.setLastUpdatedBy(pair.getSecond());
        dto.setFactoryId(new BigDecimal(pair.getFirst()));
        smtMachineMaterialPrepareService.pickkingReelidAndConfirm(dto);
        return ServiceDataBuilderUtil.success();
    }

    @ApiOperation("通过指令、线体获取提前备料记录")
    @PostMapping("/selectByWorkOrderAndLineCode")
    public ServiceData selectByWorkOrderAndLineCode(HttpServletRequest request, @RequestBody SmtMachineMaterialPrepare record)throws Exception {
        RequestHeadValidationUtil.validaFactoryIdAndEmpno(request);
        return ServiceDataBuilderUtil.success(smtMachineMaterialPrepareService.selectByWorkOrderAndLineCode(record));
    }

    @ApiOperation("Feeder绑定(新)")
    @PostMapping("/bindFeederNew")
    public ServiceData bindFeederNew(HttpServletRequest request, @RequestBody SmtMachineMaterialPrepareDTO dto) throws Exception {
        // 校验工号和工厂ID
        RequestHeadValidationUtil.validaFactoryIdAndEmpno(request);
        String factoryId = request.getHeader(SysConst.HTTP_HEADER_X_FACTORY_ID);
        dto.setFactoryId(new BigDecimal(factoryId));
        BigDecimal entityId = dto.getEntityId();
        if(entityId != null) {
            dto.setEntityId(entityId);
        } else {
            dto.setEntityId(new BigDecimal(factoryConfig.getCommonEntityId()));
        }
        String empNo=request.getHeader(SysConst.HTTP_HEADER_X_EMP_NO);
        dto.setCreateUser(empNo);
        dto.setLastUpdatedBy(empNo);
        return ServiceDataUtil.getSuccess(smtMachineMaterialPrepareService.bindFeederNew(dto));
    }

}