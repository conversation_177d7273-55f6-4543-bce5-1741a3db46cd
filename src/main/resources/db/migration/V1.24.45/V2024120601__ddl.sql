set lock_timeout='2000ms';
ALTER TABLE scrap_bill_detail ADD if not exists calculated_input varchar(1) NULL;
COMMENT ON COLUMN scrap_bill_detail.calculated_input IS '已计算投入 1未计算 0 已计算';
ALTER TABLE scrap_bill_detail ADD if not exists calculated_output varchar(1) NULL;
COMMENT ON COLUMN scrap_bill_detail.calculated_output IS '已计算产出 1未计算 0 已计算';
ALTER TABLE scrap_bill_detail ADD if not exists work_order_no varchar(200) NULL;
COMMENT ON COLUMN scrap_bill_detail.work_order_no IS '报废前指令';