/*Started by AICoder, pid:t35f0z9b7e2f89e14dfa09024009b97f5fb855c7*/
package com.zte.application.datawb.impl;

import com.alibaba.fastjson.JSON;
import com.zte.application.datawb.WsmAssembleLinesService;
import com.zte.common.CommonUtils;
import com.zte.common.utils.Constant;
import com.zte.common.utils.DateUtil;
import com.zte.domain.model.SysLookupValues;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.interfaces.dto.ZmsByteDanceMRDetailDTO;
import com.zte.springbootframe.util.json.JacksonJsonConverUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({CenterfactoryRemoteService.class,
        WsmAssembleLinesService.class, JacksonJsonConverUtil.class,
        JSON.class, CommonUtils.class, DateUtil.class})
@PowerMockIgnore({"javax.net.ssl.*", "javax.security.*"})
public class ZmsByteDanceMRServiceImpl_getLookupMeanList_2_Test {
    @Mock
    private WsmAssembleLinesService wsmAssembleLinesService;

    @InjectMocks
    private ZmsByteDanceMRServiceImpl zmsByteDanceMRService;

    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);
    }

    @Test(timeout = 8000)
    public void testGetLookupMeanList_WithData() {
        ZmsByteDanceMRDetailDTO dto = new ZmsByteDanceMRDetailDTO();
        List<SysLookupValues> sysLookupValuesList = new ArrayList<>();
        when(wsmAssembleLinesService.getSysLookupValues(Constant.LOOKUP_TYPES_8240042))
                .thenReturn(sysLookupValuesList);
        try {
            zmsByteDanceMRService.getLookupMeanList(dto);
        } catch (Exception e) {
            assertNull(dto.getUnit());
        }

        SysLookupValues sysLookupValues1 = new SysLookupValues();
        sysLookupValues1.setLookupCode(new BigDecimal("824004200020"));
        sysLookupValues1.setDescription("ODMPlantCode");
        sysLookupValuesList.add(sysLookupValues1);
        SysLookupValues sysLookupValues2 = new SysLookupValues();
        sysLookupValues2.setLookupCode(new BigDecimal("824004200021"));
        sysLookupValues2.setDescription("Unit");
        sysLookupValuesList.add(sysLookupValues2);
        SysLookupValues sysLookupValues3 = new SysLookupValues();
        sysLookupValues3.setLookupCode(new BigDecimal("824004200022"));
        sysLookupValues3.setDescription("ODMStorageLoc");
        sysLookupValuesList.add(sysLookupValues3);
        SysLookupValues sysLookupValues4 = new SysLookupValues();
        sysLookupValues4.setLookupCode(new BigDecimal("824004200023"));
        sysLookupValues4.setDescription("MovementType");
        sysLookupValuesList.add(sysLookupValues4);
        SysLookupValues sysLookupValues5 = new SysLookupValues();
        sysLookupValues5.setLookupCode(new BigDecimal("824004200013"));
        sysLookupValues5.setDescription("MovementType2");
        sysLookupValuesList.add(sysLookupValues5);
        when(wsmAssembleLinesService.getSysLookupValues(Constant.LOOKUP_TYPES_8240042))
                .thenReturn(sysLookupValuesList);

        zmsByteDanceMRService.getLookupMeanList(dto);
        assertEquals("ODMPlantCode", dto.getOdmPlantCode());
        assertEquals("Unit", dto.getUnit());
        assertEquals("ODMStorageLoc", dto.getOdmStorageLoc());
        assertEquals("MovementType", dto.getMovementType());

        SysLookupValues sysLookupValues6 = new SysLookupValues();
        sysLookupValues6.setLookupCode(new BigDecimal("111"));
        sysLookupValues6.setDescription("MovementType2");
        sysLookupValuesList.clear();
        sysLookupValuesList.add(sysLookupValues6);
        when(wsmAssembleLinesService.getSysLookupValues(Constant.LOOKUP_TYPES_8240042))
                .thenReturn(sysLookupValuesList);
        zmsByteDanceMRService.getLookupMeanList(dto);
        assertEquals("ODMPlantCode", dto.getOdmPlantCode());
    }
}
/*Ended by AICoder, pid:t35f0z9b7e2f89e14dfa09024009b97f5fb855c7*/