package com.zte.autoTest.unitTest;

import com.zte.application.datawb.impl.DefectsServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.domain.model.DefectsDTO;
import com.zte.domain.model.DefectsPlanNumberInfo;
import com.zte.domain.model.DefectsRegulation;
import com.zte.domain.model.DefectsRepository;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.interfaces.dto.centerfactory.PsTask;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.List;

@RunWith(PowerMockRunner.class)
@PrepareForTest({CenterfactoryRemoteService.class})
@PowerMockIgnore({"javax.management.*", "javax.script.*"})
/**
 * <AUTHOR>
 * @Date 2020/12/22 23
 * @description:
 */
public class DefectsServiceImplTest {
    @InjectMocks
    private DefectsServiceImpl defectsService;
    @Mock
    private DefectsRepository defectsRepository;
    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Test
    public void getPlanNumberInfo() throws Exception {
        List<DefectsPlanNumberInfo> list = defectsService.getPlanNumberInfo("");
        Assert.assertEquals(new ArrayList<>(), list);
        PowerMockito.when(defectsRepository.getPlanNumberInfo(Mockito.any())).thenReturn(new ArrayList<>());
        List<DefectsPlanNumberInfo> planNumberInfo = defectsService.getPlanNumberInfo("1");
        Assert.assertEquals(0, planNumberInfo.size());
    }

    @Test
    public void checkTaskNo() throws Exception {
        DefectsPlanNumberInfo res = defectsService.checkTaskNo(null);
        Assert.assertNull(res);

        List<PsTask> psTasks = new ArrayList<>();
        PsTask psTask = new PsTask();
        psTask.setExternalType("ProductType");
        psTasks.add(psTask);
        PowerMockito.when(centerfactoryRemoteService.getPsTask(Mockito.anyList())).thenReturn(psTasks);
        res = defectsService.checkTaskNo("111");
        Assert.assertEquals("ProductType", res.getProductType());

        psTask.setItemNo("ItemNo");
        PowerMockito.when(centerfactoryRemoteService.getPsTask(Mockito.anyList())).thenReturn(psTasks);
        res = defectsService.checkTaskNo("111");
        Assert.assertEquals("ItemNo", res.getItemCode());

        psTask.setItemNo("012345678901ABC");
        PowerMockito.when(centerfactoryRemoteService.getPsTask(Mockito.anyList())).thenReturn(psTasks);
        res = defectsService.checkTaskNo("111");
        Assert.assertEquals("012345678901", res.getItemCode());

        PowerMockito.when(centerfactoryRemoteService.getPsTask(Mockito.anyList())).thenReturn(null);
        PowerMockito.when(defectsRepository.getTaskNoList(Mockito.anyList())).thenReturn(null);
        try {
            res = defectsService.checkTaskNo("111");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.TASK_NO_NOT_EXIST, e.getMessage());
        }

        List<String> taskNoList = new ArrayList<>();
        taskNoList.add("222");
        PowerMockito.when(defectsRepository.getTaskNoList(Mockito.anyList())).thenReturn(taskNoList);
        PowerMockito.when(defectsRepository.getProductType(Mockito.any())).thenReturn("ProductType1");
        res = defectsService.checkTaskNo("111");
        Assert.assertEquals("ProductType1", res.getProductType());
    }

    @Test
    public void getDefectsMaterialProperty() throws Exception {
        DefectsDTO dto = new DefectsDTO();
        try {
            defectsService.getDefectsMaterialProperty(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.DEFECTS_QTY_IS_NULL, e.getMessage());
        }
        dto.setDefectsQty(15);
        dto.setQtyType("1");
        try {
            defectsService.getDefectsMaterialProperty(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.MASTER_TYPE_IS_NULL, e.getMessage());
        }
        dto.setMasterType("master");
        try {
            defectsService.getDefectsMaterialProperty(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SMALL_TYPE_IS_NULL, e.getMessage());
        }
        dto.setSmallType("small");
        try {
            defectsService.getDefectsMaterialProperty(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SUB_TYPE_IS_NULL, e.getMessage());
        }
        dto.setSubType("sub");
        try {
            defectsService.getDefectsMaterialProperty(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CATEGORY_IS_NULL, e.getMessage());
        }
        dto.setCategory("部件");
        PowerMockito.when(defectsRepository.getTypeNo(Mockito.any())).thenReturn(null);
        try {
            defectsService.getDefectsMaterialProperty(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.TYPE_NO_IS_NULL, e.getMessage());
        }
        PowerMockito.when(defectsRepository.getTypeNo(Mockito.any())).thenReturn("A1");
        List<DefectsRegulation> regulationList = new ArrayList<>();
        PowerMockito.when(defectsRepository.getRegulation(Mockito.any())).thenReturn(regulationList);
        try {
            defectsService.getDefectsMaterialProperty(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.REGULATION_IS_NULL, e.getMessage());
        }
        regulationList.add(new DefectsRegulation() {{
            setBatch("1<=X&&15<=Y");
            setGeneral("X<1&&15<=Y");
            setScatter("Y<15");
        }});
        dto.setDefectsRate(1.0);
        String result = defectsService.getDefectsMaterialProperty(dto);
        Assert.assertEquals("batch", result);

        DefectsDTO dto1 = new DefectsDTO();
        dto1.setDefectsQty(16);
        try {
            defectsService.getDefectsMaterialProperty(dto1);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CATEGORY_IS_NULL, e.getMessage());
        }
        dto1.setCategory("整机");
        try {
            defectsService.getDefectsMaterialProperty(dto1);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.IS_ITEM_IS_NULL, e.getMessage());
        }
        dto1.setIsItem(1);
        List<DefectsRegulation> regulationList1 = new ArrayList<>();
        PowerMockito.when(defectsRepository.getRegulation(Mockito.any())).thenReturn(regulationList1);
        try {
            defectsService.getDefectsMaterialProperty(dto1);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.REGULATION_IS_NULL, e.getMessage());
        }
        regulationList1.add(new DefectsRegulation() {{
            setBatch("15<=Y");
            setGeneral("10<=Y&&Y<15");
            setScatter("Y<6");
        }});
        String result1 = defectsService.getDefectsMaterialProperty(dto1);
        Assert.assertEquals("batch", result1);
        regulationList1.remove(0);
        regulationList1.add(new DefectsRegulation() {{
            setBatch("1<=X");
            setGeneral("0.5<=X&&X<1");
            setScatter("X<0.5");
        }});
        dto1.setDefectsRate(0.5);
        String result2 = defectsService.getDefectsMaterialProperty(dto1);
        Assert.assertEquals("general", result2);

        regulationList1.remove(0);
        regulationList1.add(new DefectsRegulation() {{
            setBatch("1<=X");
            setGeneral("0.5<=X&&X<1");
            setScatter("X<0.5");
        }});

        regulationList1.remove(0);
        regulationList1.add(new DefectsRegulation() {{
            setBatch("15<=Y");
            setGeneral("10<=Y&&Y<15");
            setScatter("Y<10");
        }});
        dto1.setDefectsQty(7);
        String result3 = defectsService.getDefectsMaterialProperty(dto1);
        Assert.assertEquals("scatter", result3);
    }
}
