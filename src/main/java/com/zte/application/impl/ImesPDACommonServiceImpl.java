package com.zte.application.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Maps;
import com.zte.application.AvlService;
import com.zte.application.BSmtBomDetailService;
import com.zte.application.ImesPDACommonService;
import com.zte.application.PkCodeHistoryService;
import com.zte.application.PkCodeInfoService;
import com.zte.application.PsScanHistoryService;
import com.zte.application.SmtFeedErrorSkipService;
import com.zte.application.SmtMachineMTLHistoryHService;
import com.zte.application.SmtMachineMTLHistoryLService;
import com.zte.application.SmtMachineMaterialMoutingService;
import com.zte.application.SmtMachineMaterialPrepareService;
import com.zte.application.TransferStrategyInfoService;
import com.zte.common.CommonUtils;
import com.zte.common.ConstantInterface;
import com.zte.common.enums.InterfaceEnum;
import com.zte.common.model.MessageId;
import com.zte.common.utils.BusinessConstant;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.common.utils.NumConstant;
import com.zte.common.utils.ObtainRemoteServiceDataUtil;
import com.zte.common.utils.RedisKeyConstant;
import com.zte.domain.model.BPcbLocationDetail;
import com.zte.domain.model.BSmtBomDetail;
import com.zte.domain.model.BSmtBomDetailRepository;
import com.zte.domain.model.BaItemSupplier;
import com.zte.domain.model.CFLine;
import com.zte.domain.model.CtRouteDetail;
import com.zte.domain.model.EmEqpFeederStatus;
import com.zte.domain.model.PDATransferScanRepository;
import com.zte.domain.model.PkCodeHistory;
import com.zte.domain.model.PkCodeInfo;
import com.zte.domain.model.PkCodeInfoRepository;
import com.zte.domain.model.PsEntityPlanBasic;
import com.zte.domain.model.PsTask;
import com.zte.domain.model.PsWipInfoRepository;
import com.zte.domain.model.PsWorkOrderBasic;
import com.zte.domain.model.PsWorkOrderSmt;
import com.zte.domain.model.SmtLocationInfo;
import com.zte.domain.model.SmtMachineMTLHistoryH;
import com.zte.domain.model.SmtMachineMTLHistoryHRepository;
import com.zte.domain.model.SmtMachineMTLHistoryL;
import com.zte.domain.model.SmtMachineMaterialMouting;
import com.zte.domain.model.SmtMachineMaterialMoutingRepository;
import com.zte.domain.model.SmtMachineMaterialPrepare;
import com.zte.domain.model.SmtMachineMaterialPrepareRepository;
import com.zte.domain.model.TransferStrategyInfo;
import com.zte.domain.model.WorkorderOnline;
import com.zte.domain.model.WorkorderOnlineRepository;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.infrastructure.remote.EqpmgmtsRemoteService;
import com.zte.infrastructure.remote.HttpRemoteService;
import com.zte.infrastructure.remote.IscpRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.infrastructure.remote.ProductionDeliveryRemoteService;
import com.zte.interfaces.assembler.PkCodeHistoryAssembler;
import com.zte.interfaces.assembler.SmtMachineMTLHistoryLAssembler;
import com.zte.interfaces.assembler.SmtMachineMaterialMoutingAssembler;
import com.zte.interfaces.dto.ASMTransferScanDTO;
import com.zte.interfaces.dto.AvlDTO;
import com.zte.interfaces.dto.BPcbLocationDetailDTO;
import com.zte.interfaces.dto.BSmtBomDetailDTO;
import com.zte.interfaces.dto.BSmtBomInfoDetailDTO;
import com.zte.interfaces.dto.ContainerContentInfoDTO;
import com.zte.interfaces.dto.CtRouteDetailParamDTO;
import com.zte.interfaces.dto.EmEqpInteractiveDTO;
import com.zte.interfaces.dto.EmEqpPdcountDTO;
import com.zte.interfaces.dto.ItemSplitInfoDTO;
import com.zte.interfaces.dto.OneKeySwitchMoutingDTO;
import com.zte.interfaces.dto.OneKeySwitchScanLocationDto;
import com.zte.interfaces.dto.PDAQCSpotCheckDTO;
import com.zte.interfaces.dto.PDAQCSpotCheckPkCodeDTO;
import com.zte.interfaces.dto.PDARcvOldPkCodeDto;
import com.zte.interfaces.dto.PDAReceiveCheckItemDTO;
import com.zte.interfaces.dto.PDAReceiveItemsScanDTO;
import com.zte.interfaces.dto.PDAReelIdQtyModifyDto;
import com.zte.interfaces.dto.PDAReelIdSplitDto;
import com.zte.interfaces.dto.PDATransferScanCommonDto;
import com.zte.interfaces.dto.PDATransferScanDTO;
import com.zte.interfaces.dto.PDATransferScanModuleDto;
import com.zte.interfaces.dto.PkCodeInfoDTO;
import com.zte.interfaces.dto.PsWorkOrderBasicDTO;
import com.zte.interfaces.dto.PsWorkOrderDTO;
import com.zte.interfaces.dto.SMTScanDTO;
import com.zte.interfaces.dto.SmtLocationInfoDTO;
import com.zte.interfaces.dto.SmtMachineMTLHistoryLDTO;
import com.zte.interfaces.dto.SmtMachineMaterialMoutingDTO;
import com.zte.interfaces.dto.SmtMachineMaterialPrepareDTO;
import com.zte.interfaces.dto.SplitReelIdDTO;
import com.zte.interfaces.dto.StCodeInfoDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.interfaces.dto.SysLookupValuesDTO;
import com.zte.interfaces.dto.TransferStrategyInfoDTO;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.constant.SysGlobalConst;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.itp.msa.util.string.StringHelper;
import com.zte.microservice.name.MicroServiceNameEum;
import com.zte.springbootframe.common.annotation.RecordLogAnnotation;
import com.zte.springbootframe.common.consts.SysConst;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.locale.LocaleMessageSourceBean;
import com.zte.springbootframe.common.redis.RedisLock;
import com.zte.springbootframe.util.JsonConvertUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.ums.nfv.eco.hsif.msb.common.RouteException;
import org.apache.commons.lang.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * PDA公共接口Service实现类
 *
 * <AUTHOR>
 **/
@Service
public class ImesPDACommonServiceImpl implements ImesPDACommonService {

    @Autowired
    private PsWipInfoRepository psWipInfoRepository;

    @Autowired
    private PkCodeInfoRepository pkCodeInfoRepository;

    @Autowired
    private SmtMachineMaterialMoutingRepository smtMachineMaterialMoutingRepository;

    @Autowired
    private SmtMachineMaterialMoutingService smtMachineMaterialMoutingService;

    @Autowired
    private SmtMachineMaterialPrepareRepository smtMachineMaterialPrepareRepository;

    @Autowired
    private SmtMachineMaterialPrepareService smtMachineMaterialPrepareService;

    @Autowired
    private SmtMachineMTLHistoryHService smtMachineMTLHistoryHService;

    @Autowired
    private SmtMachineMTLHistoryLService smtMachineMTLHistoryLService;

    @Autowired
    private PkCodeHistoryService pkCodeHistoryService;

    @Autowired
    private AvlService avlService;

    @Autowired
    private PsScanHistoryService psScanHistoryService;

    @Autowired
    private SmtFeedErrorSkipService smtFeedErrorSkipService;

    @Autowired
    private BSmtBomDetailService bSmtBomDetailService;

    @Autowired
    private PkCodeInfoService pkCodeInfoService;

    @Autowired
    private TransferStrategyInfoService transferStrategyInfoService;

    @Autowired
    private ConstantInterface constantInterface;

    @Autowired
    private SmtMachineMTLHistoryHRepository smtMachineMTLHistoryHRepository;

    @Autowired
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Autowired
    private BSmtBomDetailRepository bSmtBomDetailRepository;

    @Autowired
    private PDATransferScanRepository pdaTransferScanRepository;

    @Autowired
    private WorkorderOnlineRepository workorderOnlineRepository;
    @Autowired
    private IscpRemoteService iscpRemoteService;

    @Autowired
    private DatawbRemoteService datawbRemoteService;


    /**
     * 日志对象
     */
    private static final Logger LOG = LoggerFactory.getLogger(ImesPDACommonServiceImpl.class);

    private BaItemSupplier getBaItemSupplierInfo(String supplierCode, String itemUuid)
            throws RouteException, IOException {
        BaItemSupplier retObj = new BaItemSupplier();
        Map<String, String> map = new HashMap<String, String>();
        if (StringHelper.isNotEmpty(supplierCode)) {
            map.put("supplierNo", supplierCode);
        }
        if (StringHelper.isNotEmpty(itemUuid)) {
            map.put("itemUuid", itemUuid);
        }
        String params = JacksonJsonConverUtil.beanToJson(map);
        Map<String, String> headerParamsMap = MESHttpHelper.getHttpRequestHeader();
        // 点对点调用服务
        String getUrl = ConstantInterface.getUrlStatic(InterfaceEnum.bisFind);
        String result = HttpRemoteService.remoteExe(InterfaceEnum.bisFind, map, headerParamsMap, getUrl);
        JsonNode json = JacksonJsonConverUtil.getMapperInstance().readTree(result);
        String bo = json.get(MpConstant.JSON_BO).toString();
        BaItemSupplier obj = (BaItemSupplier) JacksonJsonConverUtil.jsonToBean(bo, BaItemSupplier.class);
        if (null != obj && StringHelper.isNotEmpty(obj.getItemUuid())) {
            retObj = obj;
        }
        return retObj;
    }

    /**
     * 获取feeder设备状态
     *
     * @param feederNo
     * @return
     * @throws RouteException
     * @throws IOException
     */
    private List<EmEqpFeederStatus> getEmEqpFeederStatus(String feederNo)
            throws RouteException, IOException {

        Map<String, String> map = new HashMap<String, String>();
        map.put("feederCode", feederNo);
        String params = JacksonJsonConverUtil.beanToJson(map);
        Map<String, String> headerParamsMap = MESHttpHelper.getHttpRequestHeader();

        // 点对点调用服务
        String serviceName = MicroServiceNameEum.EQPMGMT;
        String version = MicroServiceNameEum.VERSION;
        String sendType = MicroServiceNameEum.SENDTYPEGET;
        String getUrl = "/emEqpFeederStatus/getList";

        String resultString = MicroServiceRestUtil.invokeService(serviceName, version, sendType, getUrl, params,
                headerParamsMap);
        JsonNode json = JacksonJsonConverUtil.getMapperInstance().readTree(resultString);

        String bo = json.get(MpConstant.JSON_BO).toString();
        return JacksonJsonConverUtil.jsonToListBean(bo,
                new TypeReference<ArrayList<EmEqpFeederStatus>>() {
                });
    }

    /**
     * 新增设备交互信息
     *
     * @param dto
     * @return
     */
    private RetCode addEmEqpInteractive(EmEqpInteractiveDTO dto) {
        RetCode retCode = null;
        try {
            // 点对点调用服务
            String params = JacksonJsonConverUtil.beanToJson(dto);
            Map<String, String> headerParamsMap = MESHttpHelper.getHttpRequestHeader();
            String serviceName = MicroServiceNameEum.EQPMGMT;
            String version = MicroServiceNameEum.VERSION;
            String sendType = MicroServiceNameEum.SENDTYPEPOST;
            String getUrl = "/eqp/emEqpInteractive";

            String getresult = MicroServiceRestUtil.invokeService(serviceName, version, sendType, getUrl,
                    params, headerParamsMap);
            JsonNode json = JacksonJsonConverUtil.getMapperInstance().readTree(getresult);
            String code = json.get(MpConstant.JSON_CODE).toString();
            retCode = (RetCode) JacksonJsonConverUtil.jsonToBean(code, RetCode.class);
        } catch (Exception e) {
            String errorTip = CommonUtils.getLmbMessage(MessageId.DEVICE_INTERACTION_INFORMATION_INSERT_FAILED);
            LOG.info(errorTip, e);
            retCode = new RetCode(RetCode.BUSINESSERROR_CODE, RetCode.BUSINESSERROR_MSGID);
            retCode.setMsg(errorTip);
        }
        return retCode;
    }

    /**
     * PDA接料扫描
     *
     * @param entity
     * @return
     * @throws RouteException
     * @throws IOException
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public RetCode pdaReceiveItemsScan(List<PDAReceiveItemsScanDTO> entity) throws Exception {
        RetCode retCode = new RetCode(RetCode.BUSINESSERROR_CODE, RetCode.BUSINESSERROR_MSGID);
        // 校验新料盘在pk_code_info表是否存在
        PkCodeInfoDTO condObj = new PkCodeInfoDTO();
        String empNo = entity.get(NumConstant.NUM_ZERO).getEmpNo();
        condObj.setPkCode(entity.get(NumConstant.NUM_ZERO).getNewPkCode());
        condObj.setEnabledFlag(Constant.FLAG_Y);
        List<PkCodeInfo> pkCodeList = pkCodeInfoRepository.getList(condObj);
        if (CollectionUtils.isEmpty(pkCodeList)) {
            retCode = new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.REEL_ID_NOT_REGISTER);
            return retCode;
        }
        String sourceTask = entity.get(NumConstant.NUM_ZERO).getSourceTask();
        List<PkCodeInfo> currPkCodeList = pkCodeList.stream().filter(ft -> StringUtils.equals(sourceTask, ft.getProductTask()))
                .collect(Collectors.toList());
        // 是否城堡板
        Boolean checkFlag = false;
        // 是否允许跨批次挪用物料
        boolean receiveAppropriation = false;
        if (CollectionUtils.isEmpty(currPkCodeList)) {
            String productTask = pkCodeList.get(NumConstant.NUM_ZERO).getProductTask();
            if (StringHelper.isEmpty(productTask)) {
                retCode = new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.REELID_PRODPLANID_IS_NULL);
                return retCode;
            }
            // 查询是否城堡板
            checkFlag = pkCodeSourceTaskCheck(productTask, sourceTask);
            if (!checkFlag) {
                // 查询是否允许跨批次挪用物料
                receiveAppropriation = checkReceiveAppropriation(productTask, sourceTask,
                        pkCodeList.get(NumConstant.NUM_ZERO).getItemCode(), entity.get(NumConstant.NUM_ZERO).getLineCode(), empNo);
                if (!receiveAppropriation) {
                    retCode = new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.PRODPLAN_ID_NOT_MATCH_WORKORDER);
                    return retCode;
                }
            }
        }
        PkCodeInfo newPkCodeObj = getPkCodeInfo(pkCodeList, currPkCodeList, receiveAppropriation);
        if (newPkCodeObj.getItemQty().longValue() <= 0) {
            retCode = new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.NEW_PKCODE_COUNT_MUST_GREATER_THAN_ZERO);
            return retCode;
        }
        // 极性校验， 停机消息
        StringBuilder remark = new StringBuilder();
        remark.append(BusinessConstant.LOCATION_NO).append(entity.get(NumConstant.NUM_ZERO).getOldMachineNo()).append(BusinessConstant.PLUS_CHAR).append(entity.get(NumConstant.NUM_ZERO).getOldLocationNo())
                .append(BusinessConstant.ITEM_CODE).append(newPkCodeObj.getItemCode()).append(BusinessConstant.RECEIVE_SCAN_FAILED);
        if (!newPkCodeObj.getItemCode().equals(entity.get(NumConstant.NUM_ZERO).getOldItemCode())) {
            addEmEqpInfo(entity, remark.toString());
            String[] params = new String[]{newPkCodeObj.getItemCode(), entity.get(NumConstant.NUM_ZERO).getOldItemCode()};
            retCode = new com.zte.springbootframe.common.model.RetCode(RetCode.BUSINESSERROR_CODE, MessageId.ITEM_CODE_NOT_MATCH, params);
            return retCode;
        }
        // 新料盘是否存在mounting表，校验object id或者next reel id
        String checkMount = checkMountingByNewCode(entity);
        if (StringUtils.isNotEmpty(checkMount)) {
            retCode = new RetCode(RetCode.BUSINESSERROR_CODE, checkMount);
            return retCode;
        }
        // AVL校验
        String avlMsg = checkAvlInfo(entity, newPkCodeObj, remark.toString());
        if (StringUtils.isNotEmpty(avlMsg)) {
            retCode = new RetCode(RetCode.BUSINESSERROR_CODE, avlMsg);
            return retCode;
        }
        //校验通过后做数据处理
        excuteMoutingPrepareAndHistoryInfo(entity, newPkCodeObj);
        // 如果是挪料场景
        if (receiveAppropriation) {
            // 将reelid所属批次修改为当前接料批次
            newPkCodeObj.setProductTask(sourceTask);
            pkCodeInfoRepository.updatePkCodeInfoById(newPkCodeObj);
            // 标记已使用转产策略
            transferStrategyInfoService.markUsed(sourceTask, entity.get(NumConstant.NUM_ZERO).getLineCode());
        }
        retCode = new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID);
        return retCode;
    }

    private PkCodeInfo getPkCodeInfo(List<PkCodeInfo> pkCodeList, List<PkCodeInfo> currPkCodeList, boolean receiveAppropriation) {
        PkCodeInfo newPkCodeObj = !CollectionUtils.isEmpty(currPkCodeList) ? currPkCodeList.get(NumConstant.NUM_ZERO) : pkCodeList.get(NumConstant.NUM_ZERO);
        newPkCodeObj.setProgramName(receiveAppropriation ? BusinessConstant.RECEIVE_APPROPRIATION : BusinessConstant.RECEIVE_SCAN);
        return newPkCodeObj;
    }

    /**
     * 查询是否允许跨批次挪用物料
     */
    private boolean checkReceiveAppropriation(String prodPlanNoNext, String prodPlanNo, String itemCode, String lineCode, String empNo) throws Exception {
        // 查询转产策略
        TransferStrategyInfoDTO queryStrategy = new TransferStrategyInfoDTO();
        queryStrategy.setProdplanId(prodPlanNo);
        queryStrategy.setLineCode(lineCode);
        List<TransferStrategyInfo> strategies = transferStrategyInfoService.getList(queryStrategy);
        if (CollectionUtils.isEmpty(strategies)) {
            // 没有查询到转产策略
            return false;
        }
        if (!strategies.stream().anyMatch(p -> StringUtils.equals(prodPlanNoNext, p.getPreProdplanId()))) {
            // 物料批次不是该指令或预转产指令的，请确认
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PRODPLAN_ID_NOT_MATCH_WORKORDER_OR_NEXT);
        }
        // 当前批次这种物料是否已全部使用完
        PkCodeInfo query = new PkCodeInfo();
        query.setProductTask(prodPlanNo);
        query.setItemCode(itemCode);
        List<PkCodeInfo> pkCodeList = pkCodeInfoRepository.getNotUsed(query);
        List<SysLookupValuesDTO> lookupValueList = BasicsettingRemoteService.getLookupValueByTypeCodes(MpConstant.LOOKUP_TYPE_RECEIVE_APPROPRIATION);
        boolean isSpecialFlag = false;
        if (!CollectionUtils.isEmpty(lookupValueList) && StringUtils.isNotBlank(empNo)) {
            for (SysLookupValuesDTO lv : lookupValueList) {
                if (lv.getLookupMeaning().equals(empNo)) {
                    isSpecialFlag = true;
                    break;
                }
            }
        }
        if (!CollectionUtils.isEmpty(pkCodeList) && !isSpecialFlag) {
            // {0}批次存在未用完的物料{0}，reelid：{2}，不允许进行挪料操作
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.NO_MATERIAL_TRANSFER_OPERATION_ALLOWED,
                    new String[]{prodPlanNo, itemCode, pkCodeList.get(Constant.INT_0).getPkCode()});
        }
        return true;
    }

    /**
     * pda接料扫描极性校验
     *
     * @param entity
     * @return
     * @throws Exception
     */
    @Override
    @RecordLogAnnotation(value = "pda接料扫描极性校验")
    public String polarCheck(PDAReceiveItemsScanDTO entity) throws Exception {
        String retMessage = "";

        // 数据字典开关是否打开
        String switchFlag = getPolarSwitch();
        if (!Constant.FLAG_Y.equals(switchFlag)) {
            return "";
        }
        PkCodeInfo newPkCodeObj = getPkCodeInfo(entity.getNewPkCode(), "");
        if (null == newPkCodeObj || StringHelper.isEmpty(newPkCodeObj.getPkCode())) {
            return MpConstant.PDA_RS_MESSAGE_TWO;
        }
        // 校验ReelID在wms中是否存在--校验后移，这里是为了在报错前可以直接将组装好的参数调用上料历史记录写入方法
        boolean wmsHasReelId = ProductionDeliveryRemoteService.checkWmsHasReelId(entity.getNewPkCode());
        if (wmsHasReelId) {
            String msg = String.format(MpConstant.WMS_HAS_THE_NEW_REELID, entity.getNewPkCode());
            entity.setOperateMsg(msg);
            insertMtlHistoryForPda(entity, newPkCodeObj);
            return MpConstant.PDA_RS_MESSAGE_SIX;
        }
        // 增加料盘找站位reelID和新料盘一致性校验，不一致要写入上料历史
        if (StringHelper.isNotEmpty(entity.getNewPkCode()) && !entity.getNewPkCode().equals(entity.getCheckPkCode())) {
            String msg = String.format(MpConstant.CONSISTANT_CHECK_FAILED, entity.getNewPkCode(), entity.getCheckPkCode());
            entity.setOperateMsg(msg);
            insertMtlHistoryForPda(entity, newPkCodeObj);
            return MpConstant.PDA_RS_MESSAGE_FIVE;
        }
        if (Constant.FLAG_N.equals(entity.getContinueFlag())) {
            // 点击”否“
            LocaleMessageSourceBean lmb = (LocaleMessageSourceBean) SpringContextUtil.getBean(MpConstant.RESOURCE_SERVICE_NAME);
            String msg = lmb.getMessage(MessageId.FORBIDDEN_RECEIVE_ITEMS);
            entity.setOperateMsg(msg);
            insertMtlHistoryForPda(entity, newPkCodeObj);
            return MpConstant.PDA_RS_MESSAGE_FOUR;
        }
        PkCodeInfo oldPkCodeObj = getPkCodeInfo(entity.getOldPkCode(), "");
        if (null == oldPkCodeObj || StringHelper.isEmpty(oldPkCodeObj.getPkCode())) {
            return MpConstant.PDA_RS_MESSAGE_ONE;
        }
        String oldSupplierCode = oldPkCodeObj.getSupplerCode();
        String oldItemUuid = oldPkCodeObj.getSysLotCode();
        String newSupplierCode = newPkCodeObj.getSupplerCode();
        String newItemUuid = newPkCodeObj.getSysLotCode();
        if (StringHelper.isEmpty(oldSupplierCode) || StringHelper.isEmpty(oldItemUuid)
                || StringHelper.isEmpty(newSupplierCode) || StringHelper.isEmpty(newItemUuid)) {
            entity.setOperateMsg("oldSupplierCode" + oldSupplierCode + oldItemUuid + newSupplierCode + newItemUuid);
            insertMtlHistoryForPda(entity, newPkCodeObj);
            return "";
        }
        // 极性校验
        retMessage = execPolarCheckEvent(entity, oldPkCodeObj, newPkCodeObj);
        return retMessage;
    }

    private String execPolarCheckEvent(PDAReceiveItemsScanDTO entity, PkCodeInfo oldPkCodeObj, PkCodeInfo newPkCodeObj) throws Exception {
        String newSupplierCode = newPkCodeObj.getSupplerCode();
        String oldSupplierCode = oldPkCodeObj.getSupplerCode();
        String newItemUuid = newPkCodeObj.getSysLotCode();
        String oldItemUuid = oldPkCodeObj.getSysLotCode();
        String reMessage = Constant.STR_EMPTY;
        // 厂家校验
        String itemCodeCheckFlag = BasicsettingRemoteService.getLookupMeaningByCode(Constant.LOOKUP_TYPE_8889001);
        // 方向校验
        String braidDirectionCheckFlag =
                BasicsettingRemoteService.getLookupMeaningByCode(Constant.LOOKUP_TYPE_8889002);

        //校验新旧料盘的供应商编码是否一致
        if (entity.getItemCodeFlag()) {
            //  Y 打开校验 N 忽略校验
            if (StringUtils.equals(Constant.FLAG_Y, itemCodeCheckFlag)) {
                String msg = String.format(MpConstant.OLD_NEW_ITEM_CODE_DIFFERENT, entity.getNewPkCode(), entity.getCheckPkCode());
                entity.setOperateMsg(msg);
                insertMtlHistoryForPda(entity, newPkCodeObj);
                reMessage = MpConstant.PDA_RS_MESSAGE_SEVEN;
            }
        }
        if (StringUtils.equals(oldSupplierCode, newSupplierCode) && StringUtils.equals(oldItemUuid, newItemUuid)) {
            return Constant.STR_EMPTY + reMessage;
        }
        // 增加一个开关，开关打开的才会走方向校验
        String directionCheckFlag = BasicsettingRemoteService.getLookupMeaningByCode(Constant.LOOKUP_CODE_652310001);
        if (StringUtils.equals(Constant.FLAG_N, directionCheckFlag)) {
            return Constant.STR_EMPTY + reMessage;
        }
        // 新旧料盘方向校验修改为优先从imes的极性物料表中获取，获取不到再从采购获取数据，如新旧料盘的方向不一致时则弹出提示“新料盘XX的方向为XXX，旧料盘XXX的方向为XXX，方向不一致，请确认”
        String oldItemAngle = getItemBraidDirection(oldPkCodeObj);
        String newItemAngle = getItemBraidDirection(newPkCodeObj);
        // 旧、新料盘方向是否一致
        if (!StringUtils.equals(oldItemAngle, newItemAngle)) {
            String operateMsg = String.format(MpConstant.DIRECTION_CHECK_FAILED, newPkCodeObj.getPkCode(), newItemAngle, oldPkCodeObj.getPkCode(), oldItemAngle);
            entity.setOperateMsg(operateMsg);
            entity.setOldDirection(oldItemAngle);
            entity.setNewDirection(newItemAngle);
            insertMtlHistoryForPda(entity, newPkCodeObj);
            //  Y 打开校验 N 抛出异常
            if (!StringUtils.equals(Constant.FLAG_Y, braidDirectionCheckFlag)) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PDARSERROR_011,
                        new Object[]{entity.getNewPkCode(), entity.getNewDirection(), entity.getOldPkCode(), entity.getOldDirection()});
            }
            return MpConstant.PDA_RS_MESSAGE_THREE + reMessage;
        }
        return Constant.STR_EMPTY + reMessage;
    }

    public String getItemBraidDirection(PkCodeInfo pkCodeObj) throws Exception {
        String itemAngle = Constant.EMPTY_STRING;
        PkCodeInfo newPkCodeInfo = pkCodeInfoService.getItemDirectionByPkCode(pkCodeObj);
        if (StringUtils.isNotEmpty(newPkCodeInfo.getItemAngle())) {
            return newPkCodeInfo.getItemAngle();
        }
        // IMES没有维护，再查采购
        // 根据物料代码+uuid+供应商代码查询采购物料拆分信息
        List<ItemSplitInfoDTO> newItemSplitList = iscpRemoteService.getItemSplitInfo(pkCodeObj.getItemCode(), pkCodeObj.getSysLotCode(), pkCodeObj.getSupplerCode());
        if (!CollectionUtils.isEmpty(newItemSplitList)) {
            ItemSplitInfoDTO newTemp = newItemSplitList.get(0);
            itemAngle = newTemp.getBraidDirection();
            // 需要转换名称
            List<StCodeInfoDTO> stCodeInfoList = datawbRemoteService.getStCodeInfoList();
            if (!CollectionUtils.isEmpty(stCodeInfoList)) {
                Map<String, String> codeMap = stCodeInfoList.stream().collect(
                        Collectors.toMap(StCodeInfoDTO::getCode, StCodeInfoDTO::getCodeDesc, (k1, k2) -> k1));
                String codeMsg = codeMap.get(itemAngle);
                itemAngle = StringUtils.isEmpty(codeMsg) ? itemAngle : codeMsg;
            }
        }
        return itemAngle;
    }

    private PkCodeInfo getPkCodeInfo(String pkCode, String sourceTask) {
        PkCodeInfoDTO condObj = new PkCodeInfoDTO();
        condObj.setPkCode(pkCode);
        condObj.setEnabledFlag(Constant.FLAG_Y);
        List<PkCodeInfo> pkCodeList = pkCodeInfoRepository.getList(condObj);
        if (CollectionUtils.isEmpty(pkCodeList)) {
            return null;
        }
        if (StringHelper.isNotEmpty(sourceTask)) {
            List<PkCodeInfo> currPkCodeList = pkCodeList.stream().filter(ft -> sourceTask.equals(ft.getProductTask()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(currPkCodeList)) {
                return null;
            }
            return currPkCodeList.get(NumConstant.NUM_ZERO);
        }
        return pkCodeList.get(NumConstant.NUM_ZERO);
    }

    /**
     * @param entity
     * @param newPkCodeObj
     * @param remark
     * @return
     * @throws Exception
     */
    private String checkAvlInfo(List<PDAReceiveItemsScanDTO> entity, PkCodeInfo newPkCodeObj, String remark) throws Exception {
        String returnMsg = Constant.STR_EMPTY;
        AvlDTO avlParams = new AvlDTO();
        avlParams.setBomNo(entity.get(NumConstant.NUM_ZERO).getBomNo());
        avlParams.setItemNo(newPkCodeObj.getItemCode());
        avlParams.setCurrentPage(NumConstant.NUM_ONE);
        avlParams.setPageSize(NumConstant.NUM_1000);
        String resultString = avlService.getAvlDTOList(avlParams).getBo().toString();
        if (StringHelper.isEmpty(resultString)) {
            returnMsg = CommonUtils.getLmbMessage(MessageId.AVL_SERVER_FAILED);
            return returnMsg;
        }
        String uuidStr = BusinessConstant.ITEM_UUID + newPkCodeObj.getSysLotCode() + Constant.COMMA;
        Boolean avlCheckPassed = !resultString.contains(uuidStr);
        if (!avlCheckPassed) {
            // 发送停机命令
            returnMsg = MessageId.AVL_CHECK_FAILED;
            addEmEqpInfo(entity, remark);
            return returnMsg;
        }
        return returnMsg;
    }

    private String checkMountingByNewCode(List<PDAReceiveItemsScanDTO> entity) {
        String returnMsg = Constant.STR_EMPTY;
        SmtMachineMaterialMouting mountingCondObj = new SmtMachineMaterialMouting();
        mountingCondObj.setObjectId(entity.get(NumConstant.NUM_ZERO).getNewPkCode());
        mountingCondObj.setEnabledFlag(Constant.FLAG_Y);
        List<SmtMachineMaterialMouting> mountingList = smtMachineMaterialMoutingRepository.selectSmtMachineMaterialMoutingSelective(mountingCondObj);
        SmtMachineMaterialMouting mountingObj = new SmtMachineMaterialMouting();
        mountingObj.setNextReelRowid(entity.get(NumConstant.NUM_ZERO).getNewPkCode());
        mountingObj.setEnabledFlag(Constant.FLAG_Y);
        List<SmtMachineMaterialMouting> mountList = smtMachineMaterialMoutingRepository.selectSmtMachineMaterialMoutingSelective(mountingObj);
        if (!CollectionUtils.isEmpty(mountingList) || !CollectionUtils.isEmpty(mountList)) {
            returnMsg = MessageId.REEL_USING_PLEASE_CONFIRM;
        }
        return returnMsg;
    }

    /**
     * pda接料校验通过后做数据处理
     *
     * @param scanList
     * @param newPkCodeObj
     * @throws Exception
     */
    private void excuteMoutingPrepareAndHistoryInfo(List<PDAReceiveItemsScanDTO> scanList, PkCodeInfo newPkCodeObj) throws Exception {

        for (PDAReceiveItemsScanDTO entity : scanList) {
            // 如果旧料盘数量<=0且下个料盘为空
            if (entity.getOldQty().longValue() <= 0 && StringUtils.isEmpty(entity.getNextReelId())) {
                // 旧料盘数量已经消耗完，直接更新object_id为新料盘
                updateMountingInfoWithObjectId(entity, newPkCodeObj);
                // 按新reel id删除prepare表的数据
                deletePrepareInfoByObjectId(entity);
            } else {
                // 写入prepare表，pick_type=2(接料)
                insertPrepareInfoForPda(entity, newPkCodeObj);
                // 更新新料盘到续料盘字段next_reel_id
                updatePrepareInfoForPda(entity, newPkCodeObj);
            }
            // 写入上料历史
            insertMtlHistoryForPda(entity, newPkCodeObj);
            // 调用低位预警接口
            calSmtMaterialInfo(entity);
        }
        // 写入reel历史轨迹
        insertPkcodeHistoryForPda(scanList.get(NumConstant.NUM_ZERO), newPkCodeObj);
    }

    private void insertPkcodeHistoryForPda(PDAReceiveItemsScanDTO pdaReceiveItemsScanDTO, PkCodeInfo newPkCodeObj) {

        PkCodeHistory pkCodeHistoryParams = new PkCodeHistory();
        pkCodeHistoryParams.setHistoryId(UUID.randomUUID().toString());
        pkCodeHistoryParams.setEntityId(pdaReceiveItemsScanDTO.getEntityId());
        pkCodeHistoryParams.setFactoryId(pdaReceiveItemsScanDTO.getFactoryId());
        pkCodeHistoryParams.setCreateBy(pdaReceiveItemsScanDTO.getUserId());
        pkCodeHistoryParams.setLastUpdatedBy(pdaReceiveItemsScanDTO.getUserId());
        pkCodeHistoryParams.setObjectId(pdaReceiveItemsScanDTO.getNewPkCode());
        pkCodeHistoryParams.setObjectType(MpConstant.REEL_ID);
        pkCodeHistoryParams.setItemCode(newPkCodeObj.getItemCode());
        pkCodeHistoryParams.setProgramName(newPkCodeObj.getProgramName());
        pkCodeHistoryParams.setCurrentQty(newPkCodeObj.getItemQty());
        pkCodeHistoryParams.setSourceTask(pdaReceiveItemsScanDTO.getSourceTask());
        pkCodeHistoryParams.setLineCode(pdaReceiveItemsScanDTO.getLineCode());
        pkCodeHistoryParams.setWorkOrder(pdaReceiveItemsScanDTO.getWorkOrder());
        pkCodeHistoryParams.setLocationNo(pdaReceiveItemsScanDTO.getOldLocationNo());
        pkCodeHistoryParams.setMachineNo(pdaReceiveItemsScanDTO.getOldMachineNo());
        pkCodeHistoryParams.setSourceBatchCode(newPkCodeObj.getSourceBatchCode());
        pkCodeHistoryService.insertPkCodeHistory(pkCodeHistoryParams);
    }

    /**
     * PDA收料低位预警
     *
     * @param entity
     * @throws Exception
     */
    public void calSmtMaterialInfo(PDAReceiveItemsScanDTO entity) throws Exception {

        SMTScanDTO smtScanParams = new SMTScanDTO();
        smtScanParams.setEntityId(entity.getEntityId());
        smtScanParams.setFactoryId(entity.getFactoryId());
        smtScanParams.setCreateBy(entity.getUserId());
        smtScanParams.setLineCode(entity.getLineCode());
        smtScanParams.setWorkOrderNo(entity.getWorkOrder());
        psScanHistoryService.calSMTMaterialQty(smtScanParams);
    }

    /**
     * PDA接料写上料历史记录
     *
     * @param entity
     * @param newPkCodeObj
     */
    @Override
    public void insertMtlHistoryForPda(PDAReceiveItemsScanDTO entity, PkCodeInfo newPkCodeObj) throws MesBusinessException {

        SmtMachineMTLHistoryL historyLObj = new SmtMachineMTLHistoryL();
        historyLObj.setEntityId(entity.getEntityId());
        historyLObj.setFactoryId(entity.getFactoryId());
        historyLObj.setCreateUser(entity.getUserId());
        historyLObj.setLastUpdatedBy(entity.getUserId());
        historyLObj.setLineCode(entity.getLineCode());
        historyLObj.setWorkOrder(entity.getWorkOrder());
        historyLObj.setCfgHeaderId(entity.getCfgHeaderId());
        historyLObj.setMountType(NumConstant.STR_TWO);
        historyLObj.setItemCode(newPkCodeObj.getItemCode());
        historyLObj.setItemName(newPkCodeObj.getItemName());
        historyLObj.setLocationNo(entity.getOldLocationNo());
        historyLObj.setLastScanFlag(false);
        historyLObj.setMachineNo(entity.getOldMachineNo());
        historyLObj.setSourceBatchCode(newPkCodeObj.getSourceBatchCode());
        historyLObj.setIsLead(entity.getIsLead());
        historyLObj.setAvl(newPkCodeObj.getSysLotCode());
        historyLObj.setPolarInfo(Constant.STR_EMPTY);
        historyLObj.setObjectId(entity.getNewPkCode());
        historyLObj.setQty(newPkCodeObj.getItemQty());
        historyLObj.setOperateMsg(entity.getOperateMsg());
        historyLObj.setAttr2(entity.getOldPkCode());
        historyLObj.setAttr3(entity.getOldQty().toString());
        historyLObj.setModuleNo(entity.getModuleNo());
        //增加料架，操作时长，对比时长写入
        historyLObj.setMaterialRack(entity.getMaterialRack());
        historyLObj.setOptDuration(entity.getOptDuration());
        historyLObj.setCompDuration(entity.getCompDuration());
        smtMachineMTLHistoryLService.insertSmtMachineMTLHistoryAll(historyLObj);
    }

    /**
     * pda收料更新mounting表
     *
     * @param entity
     * @param newPkCodeObj
     */
    private void updatePrepareInfoForPda(PDAReceiveItemsScanDTO entity, PkCodeInfo newPkCodeObj) {

        SmtMachineMaterialMouting moutingDTO = new SmtMachineMaterialMouting();
        moutingDTO.setEntityId(entity.getEntityId());
        moutingDTO.setFactoryId(entity.getFactoryId());
        moutingDTO.setItemCode(newPkCodeObj.getItemCode());
        moutingDTO.setWorkOrder(entity.getWorkOrder());
        moutingDTO.setLineCode(entity.getLineCode());
        moutingDTO.setLocationNo(entity.getOldLocationNo());
        moutingDTO.setNextReelRowid(entity.getNewPkCode());
        // moutingDTO.setSourceBatchCode(newPkCodeObj.getSourceBatchCode());
        moutingDTO.setMachineMaterialMoutingId(entity.getOldMountingId());
        // 使用objectId更新记录
        smtMachineMaterialMoutingRepository.updateMountingInfoByObjectId(moutingDTO);
    }

    /**
     * pda接料新增prepare信息
     *
     * @param entity
     * @param newPkCodeObj
     */
    public void insertPrepareInfoForPda(PDAReceiveItemsScanDTO entity, PkCodeInfo newPkCodeObj) {

        List<SmtMachineMaterialPrepareDTO> listMmp = new ArrayList<>();
        SmtMachineMaterialPrepareDTO prepareDTO = new SmtMachineMaterialPrepareDTO();
        prepareDTO.setMtlPrepareId(UUID.randomUUID().toString());
        prepareDTO.setEntityId(entity.getEntityId());
        prepareDTO.setFactoryId(entity.getFactoryId());
        prepareDTO.setItemCode(newPkCodeObj.getItemCode());
        prepareDTO.setLineCode(entity.getLineCode());
        prepareDTO.setWorkOrder(entity.getWorkOrder());
        prepareDTO.setLocationNo(entity.getOldLocationNo());
        prepareDTO.setModuleNo(entity.getModuleNo());
        prepareDTO.setMachineNo(entity.getOldMachineNo());
        prepareDTO.setObjectId(entity.getNewPkCode());
        prepareDTO.setQty(newPkCodeObj.getItemQty());
        prepareDTO.setRemark(MpConstant.ANGLE_BRACKET_CHAR + entity.getWorkOrder());
        prepareDTO.setPickType(NumConstant.STR_TWO);
        prepareDTO.setCfgHeaderId(entity.getCfgHeaderId());
        prepareDTO.setIsLead(entity.getIsLead());
        prepareDTO.setAvl(newPkCodeObj.getSysLotCode());
        prepareDTO.setPolarInfo(Constant.STR_EMPTY);
        prepareDTO.setSourceBatchCode(newPkCodeObj.getSourceBatchCode());
        listMmp.add(prepareDTO);
        //4.30 考虑如果已经有提前备料信息，则更新这条数据
        SmtMachineMaterialPrepare queryDto = new SmtMachineMaterialPrepare();
        queryDto.setWorkOrder(entity.getWorkOrder());
        queryDto.setObjectId(entity.getNewPkCode());
        List<SmtMachineMaterialPrepare> preList = smtMachineMaterialPrepareRepository.getPrepareInfo(queryDto);
        if (!CollectionUtils.isEmpty(preList)) {
            SmtMachineMaterialPrepare record = new SmtMachineMaterialPrepare();
            record.setMtlPrepareId(preList.get(0).getMtlPrepareId());
            record.setLocationNo(entity.getOldLocationNo());
            record.setPickType(NumConstant.STR_TWO);
            record.setCfgHeaderId(entity.getCfgHeaderId());
            //PDA前端把机台在用模组设置到moduleNo
            record.setModuleNo(entity.getModuleNo());
            record.setMachineNo(entity.getOldMachineNo());
            record.setLineCode(entity.getLineCode());
            record.setWorkOrder(entity.getWorkOrder());
            smtMachineMaterialPrepareRepository.updateSmtMachineMaterialPrepareByOnlyId(record);

        } else {
            smtMachineMaterialPrepareRepository.insertSmtMachineMaterialPrepareBatch(listMmp);
        }
    }

    /**
     * PDA接料删除prepare表数据
     * 改成物理删除 zzc
     *
     * @param entity
     */
    private void deletePrepareInfoByObjectId(PDAReceiveItemsScanDTO entity) {
        if (StringHelper.isNotEmpty(entity.getNewPkCode())) {
            SmtMachineMaterialPrepare delPrepareParams = new SmtMachineMaterialPrepare();
            delPrepareParams.setEntityId(entity.getEntityId());
            delPrepareParams.setFactoryId(entity.getFactoryId());
            delPrepareParams.setObjectId(entity.getNewPkCode());
            smtMachineMaterialPrepareService.deleteSmtMachineMaterialPrepareById(delPrepareParams);
        }
    }

    /**
     * PDA接料更新mouting表
     *
     * @param entity
     */
    public void updateMountingInfoWithObjectId(PDAReceiveItemsScanDTO entity, PkCodeInfo newPkCodeObj) {
        SmtMachineMaterialMouting mtObj = new SmtMachineMaterialMouting();
        mtObj.setObjectId(entity.getNewPkCode());
        mtObj.setSourceBatchCode(newPkCodeObj.getSourceBatchCode());
        mtObj.setMachineMaterialMoutingId(entity.getOldMountingId());
        mtObj.setQty(newPkCodeObj.getItemQty());
        mtObj.setRawQty(newPkCodeObj.getRawQty());
        int i = smtMachineMaterialMoutingRepository.updateSmtMachineMaterialMoutingByOthers(mtObj);
        if (i < Constant.INT_1) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.UPDATE_MOUTING_FAILED);
        }
    }

    /**
     * 增加停开机信息
     */
    private void addEmEqpInfo(List<PDAReceiveItemsScanDTO> entity, String remark) {

        for (PDAReceiveItemsScanDTO pdaInfo : entity) {
            EmEqpInteractiveDTO eqpObj = new EmEqpInteractiveDTO();
            eqpObj.setLineCode(pdaInfo.getLineCode());
            eqpObj.setWorkOrderNo(pdaInfo.getWorkOrder());
            eqpObj.setSendProgram(MpConstant.PDA);
            //  消息类型0转机扫描 1接料扫描 2SMT低位预警
            eqpObj.setMsgType(NumConstant.STR_ONE);
            // 0 停机 1开机
            eqpObj.setCommander(NumConstant.STR_ZERO);
            String oldLocationNo = pdaInfo.getOldLocationNo();
            eqpObj.setLocationNo(oldLocationNo);
            eqpObj.setRemark(remark);
            addEmEqpInteractive(eqpObj);
        }
    }

    /**
     * PDA转机扫描
     *
     * @param entity
     * @return
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public String pdaTransferScan(PDATransferScanDTO entity) throws Exception {
        String returnMsg = "";
        LocaleMessageSourceBean lmb = (LocaleMessageSourceBean) SpringContextUtil.getBean(MpConstant.RESOURCE_SERVICE_NAME);
        String key = RedisKeyConstant.PDA_TRANSFER_SCAN_REELID_REDIS_LOCK + entity.getPkCode();
        RedisLock redisLock = new RedisLock(key);
        if (!redisLock.lock()) {
            returnMsg = lmb.getMessage(MessageId.PDA_TRANSFER_SCAN_REELID_REDIS_LOCKED, new Object[]{entity.getPkCode()});
            return returnMsg;
        }
        try {
            //增加校验--指令是否状态为'已开工','已提交','挂起'
            returnMsg = this.checkBothLineWorkOrderStatus(entity);
            if (StringHelper.isNotEmpty(returnMsg)) {
                return returnMsg;
            }
            // 1、校验料盘在pk_code_info表是否存在
            PkCodeInfoDTO condObj = new PkCodeInfoDTO();
            condObj.setPkCode(entity.getPkCode());
            condObj.setEnabledFlag(Constant.FLAG_Y);
            List<PkCodeInfo> pkCodeList = pkCodeInfoRepository.getList(condObj);
            if (CollectionUtils.isEmpty(pkCodeList)) {
                returnMsg = lmb.getMessage(MessageId.REEL_ID_NOT_REGISTER);
                return returnMsg;
            }
            List<PkCodeInfo> currPkCodeList = pkCodeList.stream().filter(ft -> entity.getSourceTask().equals(ft.getProductTask()))
                    .collect(Collectors.toList());
            Boolean checkFlag = false;
            if (CollectionUtils.isEmpty(currPkCodeList)) {
                String productTask = pkCodeList.get(NumConstant.NUM_ZERO).getProductTask();
                if (StringHelper.isEmpty(productTask)) {
                    return lmb.getMessage(MessageId.REELID_PRODPLANID_IS_NULL);
                }
                checkFlag = pkCodeSourceTaskCheck(productTask, entity.getSourceTask());
                if (!checkFlag) {
                    returnMsg = lmb.getMessage(MessageId.PRODPLAN_ID_NOT_MATCH_WORKORDER);
                    return returnMsg;
                }
            }
            PkCodeInfo newPkCodeObj = checkFlag ? pkCodeList.get(NumConstant.NUM_ZERO) :
                    currPkCodeList.get(NumConstant.NUM_ZERO);
            if (!newPkCodeObj.getItemCode().equals(entity.getDrItemCode())) {
                String[] params = new String[]{entity.getDrLocationNo()};
                returnMsg = lmb.getMessage(MessageId.ITEM_CODE_NOT_MATCH_BOM, params);
                return returnMsg;
            }
            if (StringHelper.isEmpty(entity.getBomNo())) {
                returnMsg = lmb.getMessage(MessageId.WORK_ORDER_ITEM_EMPTY);
                return returnMsg;
            }
            // 拼接转机扫描停机消息
            String[] params = new String[]{entity.getDrMachineNo() + "+" + entity.getDrLocationNo(), newPkCodeObj.getItemCode()};
            String remark = lmb.getMessage(MessageId.TRANSFER_SCAN_ERROR, params);

            // 2、avl校验
            // 只有转机扫描需要avl校验
            returnMsg = this.checkAvl(entity, newPkCodeObj, remark);
            if (StringHelper.isNotEmpty(returnMsg)) {
                return returnMsg;
            }
            List<SysLookupValuesDTO> lookupValueList = BasicsettingRemoteService.getLookupValueByTypeCodes(MpConstant.LOOKUP_TYPE_ISLEAD);
            // 3.料盘环保属性转换(2位数转1位数)
            String newIsLead = getIsLeadInfo(newPkCodeObj, lookupValueList);
            newIsLead = StringHelper.isNotEmpty(newIsLead) ? newIsLead : newPkCodeObj.getIsLead();

            returnMsg = pdaTransferMountingCheck(entity);
            if (StringHelper.isNotEmpty(returnMsg)) {
                return returnMsg;
            }

            // 转机扫描及双轨线处理
            return pdaTransferScanBothLine(entity, newPkCodeObj, remark, newIsLead);
        } finally {
            redisLock.unlock();
        }
    }

    private String checkBothLineWorkOrderStatus(PDATransferScanDTO entity) throws Exception {
        String returnMsg = "";
        List<String> allowedStatus = Arrays.asList(Constant.TRANSFER_ALLOWED_STATUS);
        if (StringHelper.isNotEmpty(entity.getWorkOrder())) {
            //获取对应指令信息
            PsWorkOrderBasic psWorkOrderBasic = PlanscheduleRemoteService.findWorkOrder(entity.getWorkOrder());
            if (null != psWorkOrderBasic && !allowedStatus.contains(psWorkOrderBasic.getWorkOrderStatus())) {
                return CommonUtils.getLmbMessage(MessageId.TRANSFER_WORK_ORDER_STATUS_ERROR, entity.getWorkOrder());
            }
        }
        if (StringHelper.isNotEmpty(entity.getWorkOrder2())) {
            //获取对应指令信息
            PsWorkOrderBasic workOrderBasic = PlanscheduleRemoteService.findWorkOrder(entity.getWorkOrder2());
            if (null != workOrderBasic && !allowedStatus.contains(workOrderBasic.getWorkOrderStatus())) {
                return CommonUtils.getLmbMessage(MessageId.TRANSFER_WORK_ORDER_STATUS_ERROR, entity.getWorkOrder2());
            }
        }
        return returnMsg;
    }

    //复杂度超了，抽方法
    private String checkAvl(PDATransferScanDTO entity, PkCodeInfo newPkCodeObj, String remark) throws Exception {
        String returnMsg = "";
        if (entity.getFormFlag().intValue() == NumConstant.NUM_ZERO) {
            returnMsg = avlCheckEvent(entity, newPkCodeObj, remark);
            if (StringHelper.isNotEmpty(returnMsg)) {
                return returnMsg;
            }
        }
        return returnMsg;
    }

    private String pdaTransferMountingCheck(PDATransferScanDTO entity) {
        if (entity.getFormFlag().longValue() == NumConstant.NUM_ZERO) {
            SmtMachineMaterialMouting mountingCondObj = new SmtMachineMaterialMouting();
            mountingCondObj.setObjectId(entity.getPkCode());
            mountingCondObj.setEnabledFlag(Constant.FLAG_Y);
            List<SmtMachineMaterialMouting> mountingList = smtMachineMaterialMoutingRepository.selectSmtMachineMaterialMoutingSelective(mountingCondObj);
            if (!CollectionUtils.isEmpty(mountingList)) {
                return CommonUtils.getLmbMessage(MessageId.MATRIAL_HAS_USED, mountingList.get(NumConstant.NUM_ZERO).getWorkOrder());
            }
        }
        return "";
    }

    /**
     * 转机扫描双轨线处理
     *
     * @param entity
     * @param newPkCodeObj
     * @param remark
     * @param newIsLead
     * @return
     * @throws Exception
     */
    private String pdaTransferScanBothLine(PDATransferScanDTO entity, PkCodeInfo newPkCodeObj, String remark, String newIsLead) throws Exception {
        String returnMsg = "";
        // 转机扫描处理
        if (StringHelper.isNotEmpty(entity.getWorkOrder()) && StringHelper.isNotEmpty(entity.getLineCode())) {
            String attr1 = entity.getWorkOrder().equals(entity.getmWorkOrder()) ? Constant.FLAG_N : MpConstant.STRING_AB;
            returnMsg = pdaTransferScanEvent(entity, newPkCodeObj, remark, newIsLead, Pair.of(attr1, MpConstant.FALSE));
            if (StringHelper.isNotEmpty(returnMsg)) {
                return returnMsg;
            }
        }
        // 双轨线指令执行转机扫描处理
        if (StringHelper.isNotEmpty(entity.getWorkOrder2()) && StringHelper.isNotEmpty(entity.getLineCode2())) {
            PDATransferScanDTO entity2 = new PDATransferScanDTO();
            BeanUtils.copyProperties(entity, entity2);
            entity2.setLineCode(entity.getLineCode2());
            entity2.setWorkOrder(entity.getWorkOrder2());
            entity2.setCfgHeaderId(entity.getCfgHeaderId2());
            returnMsg = pdaTransferScanEvent(entity2, newPkCodeObj, remark, newIsLead, Pair.of(Constant.FLAG_N, MpConstant.TRUE));
        }
        return returnMsg;
    }

    /**
     * 5G城堡单板子卡Reel ID批次校验
     *
     * @param newSourceTask
     * @param oldSourceTask
     * @return
     * @throws Exception
     * <AUTHOR>
     */
    @Override
    public Boolean pkCodeSourceTaskCheck(String newSourceTask, String oldSourceTask) throws Exception {
        Boolean checkResultFlag = false;

        List<PsTask> taskList = PlanscheduleRemoteService.getPsTaskByProdPlanId(newSourceTask);
        List<PsTask> oldTaskList = PlanscheduleRemoteService.getPsTaskByProdPlanId(oldSourceTask);
        if (CollectionUtils.isEmpty(taskList) || CollectionUtils.isEmpty(oldTaskList)) {
            return false;
        }
        // 新料盘部件计划跟踪单
        String partsPlanNo = null == taskList.get(NumConstant.NUM_ZERO).getPartsPlanno() ? "" : taskList.get(NumConstant.NUM_ZERO).getPartsPlanno();
        // 旧料盘计划跟踪单
        String prodPlanNo = null == oldTaskList.get(NumConstant.NUM_ZERO).getProdplanNo() ? "" : oldTaskList.get(NumConstant.NUM_ZERO).getProdplanNo();
        // 获取新批次工艺路径
        String routeId = getRouteId(taskList.get(NumConstant.NUM_ZERO));
        // 判断工艺路径中是否包含子工序代码为“λ“
        CtRouteDetailParamDTO paramObj = new CtRouteDetailParamDTO();
        paramObj.setRouteId(routeId);
        paramObj.setNextProcess(MpConstant.PROCESS_CODE_PARTS_CARDS);
        List<CtRouteDetail> dtlListOne = ObtainRemoteServiceDataUtil.getCtRouteDetailInfo(paramObj);
        if (!CollectionUtils.isEmpty(dtlListOne)) {
            // 取新料盘的部件计划于旧料盘批次对应的计划跟踪单对比，是否一致
            checkResultFlag = prodPlanNo.equals(partsPlanNo);
        }
        return checkResultFlag;
    }

    /**
     * 接料时如果批次不匹配时。查询是否时是5G城堡单板子卡情况、或者跨批次挪料情况。这两种情况允许批次不匹配。
     */
    @Override
    public Boolean pkCodeCastleOrAppropriation(String newSourceTask, String oldSourceTask, String itemCode, String lineCode, String empNo) throws Exception {
        // 判断是否城堡板情况
        Boolean castle = pkCodeSourceTaskCheck(newSourceTask, oldSourceTask);
        if (castle) {
            return true;
        }
        // 如果不是判断是否挪料场景
        return checkReceiveAppropriation(newSourceTask, oldSourceTask, itemCode, lineCode, empNo);
    }

    private String getRouteId(PsTask psTask) throws Exception {
        List<PsWorkOrderDTO> wos = PlanscheduleRemoteService.getBasicWorkOrderInfoByProdplanId(psTask.getProdplanId());
        if (CollectionUtils.isEmpty(wos) || wos.get(Constant.INT_0) == null) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.WORKORDER_NOT_FOUND_PRODPLANID,
                    new String[]{psTask.getProdplanId()});
        }
        String routeId = wos.get(Constant.INT_0).getRouteId();
        if (StringUtils.isBlank(routeId)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.NOT_FOUND_WORKORDER_ROUTE,
                    new String[]{wos.get(Constant.INT_0).getWorkOrderNo()});
        }
        return routeId;
    }

    /**
     * 环保属性
     *
     * @param newPkCodeObj
     * @return
     * @throws IOException
     * @throws RouteException
     */
    private String getIsLeadInfo(PkCodeInfo newPkCodeObj, List<SysLookupValuesDTO> lookupValueList) throws RouteException, IOException {
        String newIsLead = "";
        if (!CollectionUtils.isEmpty(lookupValueList)) {
            for (SysLookupValuesDTO lv : lookupValueList) {
                String lookupMeaning = lv.getLookupMeaning();
                String attribute1 = lv.getAttribute1();
                if (attribute1.equals(newPkCodeObj.getIsLead())) {
                    newIsLead = lookupMeaning;
                    break;
                }
            }
        }
        return newIsLead;
    }

    /**
     * 获取极性校验开关
     *
     * @return
     * @throws RouteException
     * @throws IOException
     */
    private String getPolarSwitch() throws RouteException, IOException {
        String flag = Constant.FLAG_N;
        List<SysLookupValuesDTO> lookupValueList = BasicsettingRemoteService.getLookupValueByTypeCodes(MpConstant.LOOKUP_TYPE_POLAR_SWITCH);
        if (!CollectionUtils.isEmpty(lookupValueList)) {
            for (SysLookupValuesDTO lv : lookupValueList) {
                if (MpConstant.LOOKUP_VALUE_POLAR_SWTICH_ONE.equals(lv.getLookupCode().toString())) {
                    flag = lv.getLookupMeaning();
                    break;
                }
            }
        }
        return flag;
    }

    /**
     * 转机扫描处理
     *
     * @param entity
     * @param newPkCodeObj
     * @param remark
     * @param newIsLead
     * @return
     * @throws Exception
     */
    public String pdaTransferScanEvent(PDATransferScanDTO entity, PkCodeInfo newPkCodeObj, String remark, String newIsLead, Pair<String, Boolean> pair) throws Exception {
        String attr1 = pair.getFirst();
        Boolean bothLineFlag = pair.getSecond();
        String returnMsg = Constant.STR_EMPTY;
        SysLookupValuesDTO sysLookupValuesDTO = this.getSysLookupValuesDTO();
        String feederNo = "";
        // 未启用feeder标识， smt_machine_material_prepare得到该(料盘、指令、线体)绑定的feederId， 找不到，则feederId为空
        List<SmtMachineMaterialPrepare> prepareList = getMachineMaterialPrepareList(entity);
        // 查询是不是托盘物料 locationType= 2
        List<SmtLocationInfoDTO> listSmtLocation =
                BasicsettingRemoteService.getListSmtLocation(entity.getLineCode(), entity.getDrLocationNo());
        boolean isFeeder = false;
        if (!CollectionUtils.isEmpty(listSmtLocation)) {
            SmtLocationInfoDTO smtLocationInfoDTO = listSmtLocation.stream()
                    .filter(item -> StringUtils.equals(entity.getModuleNo(), item.getModuleNo()))
                    .findFirst().orElse(new SmtLocationInfoDTO());
            // 托盘物料 不需要综合备料就能转机扫描
            isFeeder = Constant.STRAGETY_ONE.equals(smtLocationInfoDTO.getLocationType());
        }
        //转机扫描才校验
        String msg = this.verifyPrepareMachineMaterialForTransferScan(entity, isFeeder, prepareList);
        if (StringUtils.isNotEmpty(msg)) {
            return msg;
        }

        if (!CollectionUtils.isEmpty(prepareList)) {
            feederNo = prepareList.get(NumConstant.NUM_ZERO).getFeederNo();
            entity.setOldReelId(prepareList.get(NumConstant.NUM_ZERO).getOldReelId());
        }

        // 获取feeder启用转机扫描标识
        String startFeederFlag = sysLookupValuesDTO.getLookupMeaning();
        // 启用了feeder，校验feeder设备状态
        if (Constant.FLAG_Y.equals(startFeederFlag)) {
            returnMsg = feederCheckEvent(entity, feederNo, remark);
            if (StringHelper.isNotEmpty(returnMsg)) {
                return returnMsg;
            }
        }
        // 写入上料历史表
        String mountType = this.getMountType(entity);
        String headerId = getHeadId(entity, mountType);

        SmtMachineMTLHistoryL historyLObj = this.generateSmtMachineMTLHistoryL(entity, newPkCodeObj, newIsLead, mountType, headerId);
        historyLObj.setFeederNo(feederNo);
        historyLObj.setAttr1(attr1);
        //historyLObj.setSourceTask(entity.getSourceTask());
        smtMachineMTLHistoryLService.insertSmtMachineMTLHistoryAll(historyLObj);

        //不是转机扫描直接返回
        if (null == entity.getFormFlag() || entity.getFormFlag().longValue() != NumConstant.NUM_ZERO) {
            return "";
        }
        // 转机扫描
        if (this.isTransferScan(entity, bothLineFlag)) {
            // 上料历史中指令与选择指令一致时，写入mounting表
            smtMachineMaterialMoutingService.batchInsertSmtMachineMaterialMouting(this.generateSmtMachineMaterialMoutingDTO(entity, newPkCodeObj, feederNo, newIsLead));
            if (StringUtils.isNotBlank(entity.getOldReelId())) {
                // 是续盘清空提前备料，字段
                smtMachineMaterialPrepareRepository.upDateOldReelId(entity.getPkCode());
            } else {
                // 如果不是续料盘,删除prepare表
                deletePrepareInfo(entity);
            }
        }
        // 线体 + 指令 + 类型获取上料历史头表状态
        dealTransferCompleted(entity, mountType);
        // 写入reel历史轨迹
        PkCodeHistory pkCodeHistoryParams = generatePkCodeHistory(entity, newPkCodeObj);
        pkCodeHistoryService.insertPkCodeHistory(pkCodeHistoryParams);
        return returnMsg;
    }

    private SysLookupValuesDTO getSysLookupValuesDTO() {
        List<SysLookupValuesDTO> feederFlagList = BasicsettingRemoteService.getLookupValueByTypeCodes(MpConstant.LOOKUP_TYPE_FEEDER_SWITCH);
        if (CollectionUtils.isEmpty(feederFlagList)) {
            feederFlagList = new LinkedList<>();
        }
        SysLookupValuesDTO sysLookupValuesDTO = feederFlagList.stream()
                .filter(item -> MpConstant.LOOKUP_VALUE_FEEDER_SWTICH_ONE.equals(item.getLookupCode().toString()))
                .findFirst().orElse(new SysLookupValuesDTO() {{
                    setLookupMeaning(Constant.FLAG_N);
                    setAttribute1(Constant.FLAG_N);
                }});
        return sysLookupValuesDTO;
    }

    private List<SmtMachineMaterialPrepare> getMachineMaterialPrepareList(PDATransferScanDTO entity) {
        SmtMachineMaterialPrepareDTO prepareParamsDTO = new SmtMachineMaterialPrepareDTO();
        prepareParamsDTO.setLineCode(entity.getLineCode());
        prepareParamsDTO.setWorkOrder(entity.getWorkOrder());
        prepareParamsDTO.setObjectId(entity.getPkCode());
        List<SmtMachineMaterialPrepare> prepareList = smtMachineMaterialPrepareRepository.getRelOneList(prepareParamsDTO);
        return prepareList;
    }

    private void deletePrepareInfo(PDATransferScanDTO entity) {
        SmtMachineMaterialPrepare delPrepareParams = new SmtMachineMaterialPrepare();
        delPrepareParams.setEntityId(entity.getEntityId());
        delPrepareParams.setFactoryId(entity.getFactoryId());
        delPrepareParams.setObjectId(entity.getPkCode());
        delPrepareParams.setWorkOrder(entity.getWorkOrder());
        smtMachineMaterialPrepareService.deleteSmtMachineMaterialPrepareById(delPrepareParams);
    }


    private List<SmtMachineMaterialMoutingDTO> generateSmtMachineMaterialMoutingDTO(PDATransferScanDTO entity, PkCodeInfo newPkCodeObj, String feederNo, String newIsLead) {
        List<SmtMachineMaterialMoutingDTO> listAddDTO = new ArrayList<>();
        SmtMachineMaterialMoutingDTO moutingParamDTO = new SmtMachineMaterialMoutingDTO();
        moutingParamDTO.setMachineMaterialMoutingId(UUID.randomUUID().toString());
        moutingParamDTO.setEntityId(entity.getEntityId());
        moutingParamDTO.setFactoryId(entity.getFactoryId());
        moutingParamDTO.setCreateUser(entity.getUserId());
        moutingParamDTO.setLastUpdatedBy(entity.getUserId());
        moutingParamDTO.setFeederNo(feederNo);
        moutingParamDTO.setForward("");
        moutingParamDTO.setItemCode(newPkCodeObj.getItemCode());
        moutingParamDTO.setItemName(newPkCodeObj.getItemName());
        moutingParamDTO.setLineCode(entity.getLineCode());
        moutingParamDTO.setWorkOrder(entity.getWorkOrder());
        moutingParamDTO.setLocationNo(entity.getDrLocationNo());
        moutingParamDTO.setMachineNo(entity.getDrMachineNo());
        moutingParamDTO.setModuleNo(entity.getModuleNo());
        moutingParamDTO.setNextReelRowid("");
        moutingParamDTO.setObjectId(entity.getPkCode());
        moutingParamDTO.setQty(newPkCodeObj.getItemQty());
        moutingParamDTO.setRawQty(newPkCodeObj.getItemQty());
        moutingParamDTO.setRemark(MpConstant.ANGLE_BRACKET_CHAR + entity.getWorkOrder());
        moutingParamDTO.setTrackNo("");
        moutingParamDTO.setIsLead(newIsLead);
        moutingParamDTO.setAvl(newPkCodeObj.getSysLotCode());
        moutingParamDTO.setPolarInfo("");
        moutingParamDTO.setSourceBatchCode(newPkCodeObj.getSourceBatchCode());
        moutingParamDTO.setCfgHeaderId(entity.getCfgHeaderId());
        // 备料表原料盘不为空，将原料盘写入机台在用后再将用户扫描的reelid写入续料盘字段中
        if (StringUtils.isNotBlank(entity.getOldReelId())) {
            moutingParamDTO.setObjectId(entity.getOldReelId());
            moutingParamDTO.setNextReelRowid(entity.getPkCode());
        }
        listAddDTO.add(moutingParamDTO);
        return listAddDTO;
    }

    private PkCodeHistory generatePkCodeHistory(PDATransferScanDTO entity, PkCodeInfo newPkCodeObj) {
        PkCodeHistory pkCodeHistoryParams = new PkCodeHistory();
        pkCodeHistoryParams.setHistoryId(UUID.randomUUID().toString());
        pkCodeHistoryParams.setEntityId(entity.getEntityId());
        pkCodeHistoryParams.setFactoryId(entity.getFactoryId());
        pkCodeHistoryParams.setCreateBy(entity.getUserId());
        pkCodeHistoryParams.setLastUpdatedBy(entity.getUserId());
        pkCodeHistoryParams.setObjectId(entity.getPkCode());
        pkCodeHistoryParams.setObjectType(MpConstant.REEL_ID);
        pkCodeHistoryParams.setItemCode(newPkCodeObj.getItemCode());
        pkCodeHistoryParams.setProgramName(MpConstant.TRANSFER_SCAN);
        pkCodeHistoryParams.setCurrentQty(newPkCodeObj.getItemQty());
        pkCodeHistoryParams.setSourceTask(entity.getSourceTask());
        pkCodeHistoryParams.setLineCode(entity.getLineCode());
        pkCodeHistoryParams.setWorkOrder(entity.getWorkOrder());
        pkCodeHistoryParams.setLocationNo(entity.getDrLocationNo());
        pkCodeHistoryParams.setMachineNo(entity.getDrMachineNo());
        pkCodeHistoryParams.setModuleNo(entity.getModuleNo());
        pkCodeHistoryParams.setSourceBatchCode(newPkCodeObj.getSourceBatchCode());
        return pkCodeHistoryParams;
    }

    private SmtMachineMTLHistoryL generateSmtMachineMTLHistoryL(PDATransferScanDTO entity, PkCodeInfo newPkCodeObj, String newIsLead, String mountType, String headerId) {
        SmtMachineMTLHistoryL historyLObj = new SmtMachineMTLHistoryL();
        historyLObj.setHeaderId(headerId);
        historyLObj.setEntityId(entity.getEntityId());
        historyLObj.setFactoryId(entity.getFactoryId());
        historyLObj.setCreateUser(entity.getUserId());
        historyLObj.setLastUpdatedBy(entity.getUserId());
        historyLObj.setLineCode(entity.getLineCode());
        historyLObj.setWorkOrder(entity.getWorkOrder());
        historyLObj.setCfgHeaderId(entity.getCfgHeaderId());
        historyLObj.setMountType(mountType);
        historyLObj.setItemCode(newPkCodeObj.getItemCode());
        historyLObj.setItemName(newPkCodeObj.getItemName());
        historyLObj.setLocationNo(entity.getDrLocationNo());
        historyLObj.setModuleNo(entity.getModuleNo());
        historyLObj.setMachineNo(entity.getDrMachineNo());
        historyLObj.setSourceBatchCode(newPkCodeObj.getSourceBatchCode());
        historyLObj.setIsLead(newIsLead);
        historyLObj.setAvl(newPkCodeObj.getSysLotCode());
        historyLObj.setPolarInfo("");
        historyLObj.setObjectId(entity.getPkCode());
        historyLObj.setQty(newPkCodeObj.getItemQty());
        historyLObj.setLastScanFlag(entity.getLastScanFlag());
        return historyLObj;
    }

    /**
     * 转机扫描校验提前备料
     *
     * @param entity
     * @param isFeeder
     * @param prepareList
     * @return
     * @throws Exception
     */
    private String verifyPrepareMachineMaterialForTransferScan(PDATransferScanDTO entity,
                                                               boolean isFeeder, List<SmtMachineMaterialPrepare> prepareList) throws Exception {
        if (entity.getFormFlag() == null) {
            return "";
        }
        if (entity.getFormFlag().longValue() != NumConstant.NUM_ZERO) {
            return "";
        }
        SysLookupTypesDTO sysLookupValuesDTO = BasicsettingRemoteService.getSysLookUpValue(Constant.LookUpKey.LOOK_1890, Constant.LookUpKey.LOOK_1890006);
        if (sysLookupValuesDTO == null || !Constant.FLAG_Y.equals(sysLookupValuesDTO.getLookupMeaning())) {
            return "";
        }
        //首指令
        PsWorkOrderBasicDTO psWorkOrderBasicDTO = PlanscheduleRemoteService.queryFirstWorkOrderByProdplanId(entity.getSourceTask());
        if (psWorkOrderBasicDTO == null) {
            return CommonUtils.getLmbMessage(MessageId.FAILED_TO_GET_BATCH_FIRST_COMMAND);
        }
        //另一面指令
        PsWorkOrderDTO otherSidePsWorkOrderDTO = getOtherSidePsWorkOrderDTO(entity);
        //是否共用物料
        boolean isCommonMaterial = verifyIsCommonMaterial(entity, otherSidePsWorkOrderDTO);
        //1 当前站位是否存在有效机台在用数据，如果存在则报错“XX站位已存在有效机台在用数据，禁止离线转机操作”当前站位是否存在有效机台在用数据，如果存在则报错“XX站位已存在有效机台在用数据，禁止离线转机操作”
        if (smtMachineMaterialMoutingService.countLocationMt(entity) > Constant.INT_0) {
            return CommonUtils.getLmbMessage(MessageId.THERE_IS_ALREADY_VALID_MACHINE_DATA_IN_USE, new String[]{entity.getDrLocationNo()});
        }
        //站位类型为feeder才校验
        if (!isFeeder) {
            return "";
        }
        //1 判断是否共用物料 如果是共用物料就直接返回
        //  如存在feederno不为空的记录则需要再看该记录的指令是否与当前指令一致，如不一致则再看该物料代码是否为共用物料
        //  (同批次另一面指令与当前指令排在同一线体且上料表中存在站位与物料代码一致的记录),
        //共用物料并且不是首指令就不校验，直接返回
        if (isCommonMaterial && !StringUtils.equals(psWorkOrderBasicDTO.getWorkOrderNo(), entity.getWorkOrder())) {
            return "";
        }
        // 2 校验reelid存在当前指令feeder不为空的数据，如不存在则报错“XXreelid没有进行综合备料，不能进行转机扫描，请确认”
        List<SmtMachineMaterialPrepare> feederIsNotEmptyList = getFeederIsNotEmptyList(prepareList);
        if (CollectionUtils.isEmpty(feederIsNotEmptyList)) {
            return CommonUtils.getLmbMessage(MessageId.NO_COMPREHENSIVE_PREPARATION, new String[]{entity.getPkCode()});
        }
        return "";
    }

    /**
     * 判断是否共用物料
     *
     * @param entity
     * @return
     * @throws Exception
     */
    private boolean verifyIsCommonMaterial(PDATransferScanDTO entity, PsWorkOrderDTO otherSidePsWorkOrderDTO) throws Exception {
        if (otherSidePsWorkOrderDTO != null && StringUtils.equals(otherSidePsWorkOrderDTO.getLineCode(), entity.getLineCode())) {
            Map<String, Object> smtMap = new HashMap<>();
            smtMap.put("workOrderId", otherSidePsWorkOrderDTO.getWorkOrderId());
            PsWorkOrderSmt psWorkOrderSmt = PlanscheduleRemoteService.getWorkOrderSMTInfo(smtMap);
            if (psWorkOrderSmt == null || StringUtils.isBlank(psWorkOrderSmt.getCfgHeaderId())) {
                return false;
            }
            Map<String, Object> map = new HashMap<String, Object>();
            //料单代码+工艺段+线体
            map.put("cfgHeaderId", psWorkOrderSmt.getCfgHeaderId());
            List<BSmtBomInfoDetailDTO> detailList = bSmtBomDetailRepository.getListByCond(map);
            if (!CollectionUtils.isEmpty(detailList)) {
                //过滤出上料表中存在模组站位与物料代码一致的记录
                List<BSmtBomInfoDetailDTO> filterList = detailList.stream().filter(e ->
                        StringUtils.equals(e.getModuleNo(), entity.getModuleNo())
                                && StringUtils.equals(e.getLocationNo(), entity.getDrLocationNo())
                                && StringUtils.equals(e.getItemCode(), entity.getDrItemCode())).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(filterList)) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 获取批次SMT另一面指令
     *
     * @param entity
     * @return
     * @throws Exception
     */
    private PsWorkOrderDTO getOtherSidePsWorkOrderDTO(PDATransferScanDTO entity) throws Exception {
        List<PsWorkOrderDTO> psWorkOrderDTOList = PlanscheduleRemoteService.getWorkOrderBasicByProdPlanId(entity.getSourceTask(), null);
        if (CollectionUtils.isEmpty(psWorkOrderDTOList)) {
            return null;
        }
        return psWorkOrderDTOList.stream().filter(e -> StringUtils.startsWith(e.getCraftSection(), Constant.SMT)
                && !StringUtils.equals(e.getWorkOrderNo(), entity.getWorkOrder())).findFirst().orElse(null);
    }

    /**
     * 过滤是否存在feeder不为空的数据
     *
     * @param prepareList
     * @return
     */
    private List<SmtMachineMaterialPrepare> getFeederIsNotEmptyList(List<SmtMachineMaterialPrepare> prepareList) {
        if (!CollectionUtils.isEmpty(prepareList)) {
            return prepareList.stream().filter(e -> StringUtils.isNotEmpty(e.getFeederNo())).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    public boolean checkPrepare(boolean isTray, List<SmtMachineMaterialPrepare> prepareList, SysLookupValuesDTO sysLookupValuesDTO) {
        if (isTray) {
            return true;
        }
        if (CollectionUtils.isEmpty(prepareList)) {
            // 没有提前备料
            if (Constant.FLAG_Y.equals(sysLookupValuesDTO.getAttribute1())) {
                return false;
            }
        } else {
            String feederNo = prepareList.get(NumConstant.NUM_ZERO).getFeederNo();
            if (this.preparationMethod(sysLookupValuesDTO, feederNo)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 非托盘物料没有做
     *
     * @param sysLookupValuesDTO 数据字典
     * @param feederNo           feed
     * @return
     * @throws Exception 基础异常
     */
    private boolean preparationMethod(SysLookupValuesDTO sysLookupValuesDTO, String feederNo) {
        if (StringUtils.isBlank(feederNo)) {
            if (Constant.FLAG_Y.equals(sysLookupValuesDTO.getAttribute1())) {
                return true;
            }
        }
        return false;
    }

    private String getHeadId(PDATransferScanDTO entity, String mountType) throws Exception {
        Map<String, Object> headMap = new HashMap<>();
        headMap.put("lineCode", entity.getLineCode());
        headMap.put("workOrder", entity.getWorkOrder());
        headMap.put("mountType", mountType);
        headMap.put("enabledFlag", Constant.FLAG_Y);
        headMap.put("pickStatus", NumConstant.STR_ONE);
        String headerId = StringUtils.EMPTY;
        List<SmtMachineMTLHistoryH> headList = smtMachineMTLHistoryHService.getList(headMap, MpConstant.CREATE_DATE, "", NumConstant.LONG_ONE, NumConstant.LONG_9999);
        if (!CollectionUtils.isEmpty(headList)) {
            headerId = headList.get(NumConstant.NUM_ZERO).getHeaderId();
        }
        return headerId;
    }

    private String getMountType(PDATransferScanDTO entity) {
        String mountType = NumConstant.STR_ONE;
        if (null != entity.getFormFlag() && entity.getFormFlag().longValue() == NumConstant.NUM_ONE) {
            mountType = NumConstant.STR_THREE;
        }
        if (null != entity.getFormFlag() && entity.getFormFlag().longValue() == NumConstant.NUM_TWO) {
            mountType = NumConstant.STR_SEVEN;
        }
        if (null != entity.getFormFlag() && entity.getFormFlag().longValue() == NumConstant.NUM_THREE) {
            mountType = MpConstant.STRING_FIFTEEN;
        }
        return mountType;
    }

    /**
     * 是否转机扫描
     *
     * @param entity       实体
     * @param bothLineFlag 实线标识
     * @return 布尔值
     */
    private boolean isTransferScan(PDATransferScanDTO entity, Boolean bothLineFlag) {
        return entity.getWorkOrder().equals(entity.getmWorkOrder()) || bothLineFlag;
    }

    /**
     * avl校验
     *
     * @param entity
     * @param newPkCodeObj
     * @param remark
     * @return
     * @throws Exception
     */
    @Override
    public String avlCheckEvent(PDATransferScanDTO entity, PkCodeInfo newPkCodeObj, String remark) throws Exception {
        String returnMsg = "";
        // 2、avl校验
        AvlDTO avlParams = new AvlDTO();
        avlParams.setBomNo(entity.getBomNo());
        avlParams.setItemNo(newPkCodeObj.getItemCode());
        avlParams.setCurrentPage(NumConstant.NUM_ONE);
        avlParams.setPageSize(NumConstant.NUM_1000);
        String resultString = avlService.getAvlDTOList(avlParams).getBo().toString();
        if (StringHelper.isEmpty(resultString)) {
            returnMsg = BusinessConstant.AVL_SERVER_ERROR;
            return returnMsg;
        }
        String uuidStr = MpConstant.AVL_CHECK_ITEM_UUID + "=" + newPkCodeObj.getSysLotCode() + ",";
        Boolean avlCheckPassed = !resultString.contains(uuidStr);
        if (!avlCheckPassed) {
            // 发送停机命令
            returnMsg = BusinessConstant.AVL_CHECK_FAIL;
            EmEqpInteractiveDTO eqpObj = new EmEqpInteractiveDTO();
            eqpObj.setLineCode(entity.getLineCode());
            eqpObj.setWorkOrderNo(entity.getWorkOrder());
            eqpObj.setSendProgram(MpConstant.PDA);
            //  消息类型0转机扫描 1接料扫描 2SMT低位预警
            eqpObj.setMsgType(NumConstant.STR_ZERO);
            // 0 停机 1开机
            eqpObj.setCommander(NumConstant.STR_ZERO);
            eqpObj.setLocationNo(entity.getDrLocationNo());
            eqpObj.setRemark(remark);
            addEmEqpInteractive(eqpObj);
            return returnMsg;
        }
        return returnMsg;
    }

    /**
     * pda转机扫描feeder校验
     *
     * @param entity
     * @param feederNo
     * @param remark
     * @return
     * @throws Exception
     */
    private String feederCheckEvent(PDATransferScanDTO entity, String feederNo, String remark) throws Exception {
        String returnMsg = "";
        LocaleMessageSourceBean lmb = (LocaleMessageSourceBean) SpringContextUtil.getBean(MpConstant.RESOURCE_SERVICE_NAME);
        if (StringHelper.isEmpty(feederNo)) {
            String[] params = new String[]{entity.getDrLocationNo()};
            returnMsg = lmb.getMessage(MessageId.LOCATION_NOT_FEEDER_BOUND, params);
            return returnMsg;
        }
        // 按feederNo,em_eqp_feeder_status得到该feeder最后一次的站位信息，判断与上料表是否一致
        List<EmEqpFeederStatus> feederStatusList = getEmEqpFeederStatus(feederNo);
        if (CollectionUtils.isEmpty(feederStatusList)) {
            String[] params = new String[]{feederNo};
            returnMsg = lmb.getMessage(MessageId.FEEDER_NOT_FOUND_EQP_STATUS, params);
            return returnMsg;
        }
        String eqpLocationNo = feederStatusList.get(NumConstant.NUM_ZERO).getFullStationPosition();
        if (StringHelper.isEmpty(eqpLocationNo)) {
            returnMsg = lmb.getMessage(MessageId.FEEDER_FULL_STATION_POSITION_EMPTY);
            return returnMsg;
        }
        if (!eqpLocationNo.equals(entity.getDrLocationNo())) {
            String[] params = new String[]{eqpLocationNo, entity.getDrLocationNo()};
            returnMsg = lmb.getMessage(MessageId.FEEDER_LOCATION_NOT_MATCH_BOM, params);
            EmEqpInteractiveDTO eqpObj = new EmEqpInteractiveDTO();
            eqpObj.setLineCode(entity.getLineCode());
            eqpObj.setWorkOrderNo(entity.getWorkOrder());
            eqpObj.setSendProgram(MpConstant.PDA);
            //  消息类型0转机扫描 1接料扫描 2SMT低位预警
            eqpObj.setMsgType(NumConstant.STR_ZERO);
            // 0 停机 1开机
            eqpObj.setCommander(NumConstant.STR_ZERO);
            eqpObj.setLocationNo(entity.getDrLocationNo());
            eqpObj.setRemark(remark);
            addEmEqpInteractive(eqpObj);
            return returnMsg;
        }
        return returnMsg;
    }

    /**
     * pda转机扫描指令下上料表全部扫描完成后处理
     *
     * @param entity
     * @param mountType
     * @throws Exception
     * <AUTHOR>
     */
    private void dealTransferCompleted(PDATransferScanDTO entity, String mountType) throws Exception {
        String pickStatus = "";
        Map<String, Object> map = new HashMap<String, Object>(NumConstant.NUM_FIVE);
        String lineCode = entity.getLineCode();
        String workOrder = entity.getWorkOrder();
        map.put("lineCode", lineCode);
        map.put("workOrder", workOrder);
        map.put("mountType", mountType);
        map.put("entityId", entity.getEntityId());
        map.put("factoryId", entity.getFactoryId());
        map.put("enabledFlag", Constant.FLAG_Y);
        List<SmtMachineMTLHistoryH> historyHList = smtMachineMTLHistoryHService.getList(map, "", "", MpConstant.NUM_MINUS_1, MpConstant.NUM_MINUS_1);
        if (!CollectionUtils.isEmpty(historyHList)) {
            pickStatus = historyHList.get(NumConstant.NUM_ZERO).getPickStatus();
        }
        if (NumConstant.STR_TWO.equals(pickStatus)) {
            // 比对完毕，发送开机指令
            EmEqpInteractiveDTO eqpObj = new EmEqpInteractiveDTO();
            eqpObj.setLineCode(lineCode);
            eqpObj.setWorkOrderNo(workOrder);
            eqpObj.setSendProgram(MpConstant.PDA);
            //  消息类型0转机扫描 1接料扫描 2SMT低位预警
            eqpObj.setMsgType(NumConstant.STR_ZERO);
            // 0 停机 1开机
            eqpObj.setCommander(NumConstant.STR_ONE);
            eqpObj.setLocationNo(entity.getDrLocationNo());
            addEmEqpInteractive(eqpObj);
        }
    }


    @Override
    public ServiceData<SmtMachineMaterialMoutingDTO> pdaReceiveCheck(String factoryId, SmtMachineMaterialMoutingDTO dto) throws Exception {
        ServiceData<SmtMachineMaterialMoutingDTO> serviceData = new ServiceData<SmtMachineMaterialMoutingDTO>();
        serviceData.setBo(dto);
        //如果是接料扫描，判断旧料盘所在站位是否是虚拟站位，根据cfgHeaderId+站位去判断是否是虚拟站位
        serviceData.setCode(validateVirtualLocation(dto));
        if (!RetCode.SUCCESS_CODE.equals(serviceData.getCode().getCode())) {
            return serviceData;
        }
        //工厂ID校验
        serviceData.setCode(CommonUtils.validFactoryId(factoryId));
        if (!RetCode.SUCCESS_CODE.equals(serviceData.getCode().getCode())) {
            return serviceData;
        }
        //校验传入的：物料代码、线体、模组、站位、指令
        serviceData.setCode(validParam(dto.getItemCode(), dto.getWorkOrder(), dto.getLineCode(), dto.getModuleNo(), dto.getLocationNo()));
        if (!RetCode.SUCCESS_CODE.equals(serviceData.getCode().getCode())) {
            return serviceData;
        }
        //查询上料历史明细表中是否有数据（基于物料代码、线体、模组、站位、指令）
        SmtMachineMTLHistoryLDTO record = new SmtMachineMTLHistoryLDTO();
        record.setItemCode(dto.getItemCode());
        record.setLineCode(dto.getLineCode());
        record.setModuleNo(dto.getModuleNo());
        record.setLocationNo(dto.getLocationNo());
        record.setWorkOrder(dto.getWorkOrder());
        record.setMountType(Constant.MOUNT_TYPE_RECEIVE);
        record.setObjectId(dto.getObjectId());
        if (smtMachineMTLHistoryLService.isReceive(record)) {
            return receiveData(serviceData, dto);
        } else {//无接料记录
            return handleNoReceiveData(serviceData, dto);
        }
    }

    /**
     * 如果是接料扫描，判断旧料盘所在站位是否是虚拟站位，根据cfgHeaderId+站位去判断是否是虚拟站位
     *
     * @param dto
     * @throws MesBusinessException
     */
    public RetCode validateVirtualLocation(SmtMachineMaterialMoutingDTO dto) throws Exception {
        final SysLookupTypesDTO sysLookUpValue = BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_1268, Constant.LOOKUP_VALUE_1268001);
        RetCode retCode = new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID);
        if (sysLookUpValue == null || !sysLookUpValue.getLookupMeaning().equals(Constant.TRUE)) {
            return retCode;
        }
        if (!Constant.FLAG_Y.equals(dto.getOperateType())) {
            return retCode;
        }
        List<String> virtualLocationList = bSmtBomDetailRepository.getVirtualFlagByLocationNo(dto.getCfgHeaderId(), dto.getLocationNo());
        if (CollectionUtils.isEmpty(virtualLocationList)) {
            return retCode;
        }
        final boolean result = virtualLocationList.stream().filter(StringUtils::isNotBlank).anyMatch(virtual -> virtual.equals(Constant.FLAG_Y));
        if (result) {
            return new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.CURRENT_LOCATIONNO_IS_VIRTUAL);
        }
        return retCode;
    }

    /**
     * 物料代码、指令有接料记录时：
     *
     * @param dto
     * @return
     * @throws Exception
     */
    public ServiceData receiveData(ServiceData<SmtMachineMaterialMoutingDTO> serviceData, SmtMachineMaterialMoutingDTO dto) throws Exception {
        //查询数据字典是否有维护QC复检且状态为Y    物料防錯管控字典 1211
        if (!isMaintainQCReCheck()) {
            return serviceData;
        }
        //判断 上料防错异常跳过表（SMT_FEED_ERROR_SKIP）是否有记录，查询字段（线体、指令、模组、占位）
        if (smtFeedErrorSkipService.getErrorSkipCount(dto.getLineCode(), dto.getWorkOrder(), dto.getModuleNo(), dto.getLocationNo()) > NumConstant.NUM_ZERO
                || smtFeedErrorSkipService.getErrorSkipCountNoLocation(dto.getLineCode(), dto.getWorkOrder(), dto.getModuleNo(), Constant.FLAG_Y) > NumConstant.NUM_ZERO) {
            return serviceData;
        }
        //校验上料历史明细是否有该物料代码，类型“QC复检”、模组、站位记录
        if (smtMachineMTLHistoryLService.isQcReCheck(dto.getItemCode(), dto.getModuleNo(), dto.getLocationNo(), dto.getWorkOrder(), dto.getObjectId())) {
            LOG.error("接料管控--有接料记录");
            return serviceData;
        } else { //否  验证失败 feedar绑定完成才能备料
            LOG.error("接料管控--无接料记录");
            return feederBindValidFailure(MessageId.RECEIVE_MUST_AFTER_QC_RECHECK);
        }
    }

    public ServiceData handleNoReceiveData(ServiceData<SmtMachineMaterialMoutingDTO> serviceData, SmtMachineMaterialMoutingDTO dto) throws Exception {
        //查询数据字典是否有维护QC转机且状态为Y ？
        if (!isMaintainQC()) {
            LOG.error("未维护qc转机");
            return serviceData;
        }
        //  2019/8/9   根据线体、指令、上料类型（QC转机）， 查询上料历史头表对应的状态是否为“对比完成”
        if (smtMachineMTLHistoryHService.isCompareFinish(dto.getLineCode(), dto.getWorkOrder())) {
            LOG.error("接料管控--对比完成");
            return serviceData;
        }
        //判断 上料防错异常跳过表（SMT_FEED_ERROR_SKIP）是否有记录，查询字段（线体、指令、模组、占位）
        //  验证失败 feedar绑定完成才能备料
        if (smtFeedErrorSkipService.getErrorSkipCount(dto.getLineCode(), dto.getWorkOrder(), null) <= 0) {
            LOG.error("验证失败 feedar绑定完成才能备料");
            return feederBindValidFailure(MessageId.RECEIVE_MUST_AFTER_QC_TRANSFORM);
        }

        //step1: 查询线体表中 是否有关联线体
        CFLine cfLine = BasicsettingRemoteService.getLine(dto.getLineCode());
        if (null != cfLine && !ObjectUtils.isEmpty(cfLine) && StringUtils.isNotEmpty(cfLine.getRelatedLine())) {//有关联线体， 则为双轨线
            LOG.error("查询上料表中 是否有共用物料   查询上料表" + dto.getWorkOrder());
            //step2: 查询上料表中 是否有共用物料   查询上料表
            PsWorkOrderBasic planBasic = PlanscheduleRemoteService.findWorkOrder(dto.getWorkOrder());
            if (ObjectUtils.isEmpty(planBasic)) {
                LOG.error("接料管控点对点获取指令信息失败");
                return feederBindValidFailure(MessageId.RECEIVE_MUST_AFTER_QC_TRANSFORM);
            }
            //双轨线有共用物料
            LOG.error("是否有共用物料？" + dto.getWorkOrder());
            if (null != planBasic && bSmtBomDetailService.hasSameItemCode(planBasic.getSourceTask(), dto.getModuleNo(), dto.getLocationNo())) {
                LOG.error("有共用物料？" + dto.getWorkOrder());
                //step3:  根据关联线体 的共用物料  获取 模组、站位
                SmtMachineMaterialMouting relateSM = smtMachineMaterialMoutingService.selectSmtMachineMaterialMoutingSelective(dto.getObjectId(), cfLine.getRelatedLine());
                //step4:  根据线体、指令、 模组、站位  查找  管控跳过表中是否有记录,  若有则 继续走，  否则  报错；
                if (this.findSkipTableRecordExist(relateSM, dto)) {
                    return feederBindValidFailure(MessageId.RECEIVE_MUST_AFTER_QC_TRANSFORM);
                }
                /*
                if(!ObjectUtils.isEmpty(relateSM)){
                	if(smtFeedErrorSkipService.getErrorSkipCountNoLocation(relateSM.getLineCode(),relateSM.getWorkOrder(),dto.getModuleNo(),Constant.FLAG_Y) == 0
                			&& smtFeedErrorSkipService.getErrorSkipCount(relateSM.getLineCode(),relateSM.getWorkOrder(),dto.getModuleNo(),dto.getLocationNo()) == 0){
                		LOG.error("根据线体、指令、 模组、站位  查找  管控跳过表中是否有记录, 报错"+dto.getWorkOrder());
                		return feederBindValidFailure(MessageId.RECEIVE_MUST_AFTER_QC_TRANSFORM);
                	}
                }
                 */
            }
        }
        //校验上料历史明细与上料表（基于物料代码、模组、占位且上料表需移除在上料防错异常中的数据）对比 (粒度比对到物料代码行， 两边的物料代码相等)
        // 结果相等 正常结束    @@@ 待确认  是否只需要比对记录还是需要分记录比对数量？ an:只需要根据物料代码比对行即可
        if (smtMachineMTLHistoryLService.equalMaterialExceptErrorSkip(dto.getLineCode(), dto.getWorkOrder())) {
            LOG.error("验上料历史明细与上料表（基于物料代码、模组、占位且上料表需移除在上料防错异常中的数据）对比 (粒度比对到物料代码行， 两边的物料代码相等)" + dto.getWorkOrder());
            return serviceData;
        } else { // 验证失败 feedar绑定完成才能备料
            LOG.error("验上料历史明细与上料表（基于物料代码、模组、占位且上料表需移除在上料防错异常中的数据）对比 (结果不相等失败" + dto.getWorkOrder());
            return feederBindValidFailure(MessageId.RECEIVE_MUST_AFTER_QC_TRANSFORM);
        }
    }

    /**
     * step4:  根据线体、指令、 模组、站位  查找  管控跳过表中是否有记录
     *
     * @param relateSm sm的值
     * @param dto      参数
     * @return 布尔值
     * @throws Exception 异常
     */
    private boolean findSkipTableRecordExist(SmtMachineMaterialMouting relateSm, SmtMachineMaterialMoutingDTO dto) throws Exception {
        boolean flag = false;
        if (!ObjectUtils.isEmpty(relateSm)) {
            if (smtFeedErrorSkipService.getErrorSkipCountNoLocation(relateSm.getLineCode(), relateSm.getWorkOrder(), dto.getModuleNo(), Constant.FLAG_Y) == 0
                    && smtFeedErrorSkipService.getErrorSkipCount(relateSm.getLineCode(), relateSm.getWorkOrder(), dto.getModuleNo(), dto.getLocationNo()) == 0) {
                LOG.error("根据线体、指令、 模组、站位  查找  管控跳过表中是否有记录, 报错" + dto.getWorkOrder());
                flag = true;
            }
        }
        return flag;
    }

    /**
     * 验证失败 feedar绑定完成才能备料
     *
     * @return
     */
    public ServiceData feederBindValidFailure(String messageId) {
        ServiceData<String> serviceData = new ServiceData<String>();
        serviceData.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, messageId));
        return serviceData;
    }

    /**
     * 校验传入的：物料代码、线体、模组、站位
     *
     * @param itemNo
     * @param workOrder
     * @param lineCode
     * @param moduleNo
     * @param locationNo
     * @return
     */
    public RetCode validParam(String itemNo, String workOrder, String lineCode, String moduleNo, String locationNo) {
        if (StringUtils.isEmpty(itemNo)) {
            return new RetCode(RetCode.VALIDATIONERROR_CODE, MessageId.ITEM_CODE_EMPTY);
        }
        if (StringUtils.isEmpty(workOrder)) {
            return new RetCode(RetCode.VALIDATIONERROR_CODE, MessageId.WORK_ORDER_EMPTY);
        }
        if (StringUtils.isEmpty(lineCode)) {
            return new RetCode(RetCode.VALIDATIONERROR_CODE, MessageId.LINE_CODE_EMPTY);
        }
        if (StringUtils.isEmpty(moduleNo)) {
            return new RetCode(RetCode.VALIDATIONERROR_CODE, MessageId.MODULE_NO_EMPTY);
        }
        if (StringUtils.isEmpty(locationNo)) {
            return new RetCode(RetCode.VALIDATIONERROR_CODE, MessageId.LOCATION_NO_EMPTY);
        }
        return new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID);
    }

    /**
     * 查询数据字典是否有维护QC转机且状态为Y    物料防錯管控字典 1211
     *
     * @return
     */
    public boolean isMaintainQC() throws Exception {
        List<SysLookupTypesDTO> itemErrorCtrls = BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_ITEM_ERROR_CTRL_TYPE);
        for (SysLookupTypesDTO itemErrorCtrl : itemErrorCtrls) {
            if (Constant.LOOKUP_VALUE_QC_TRANSFORM.equals(itemErrorCtrl.getLookupCode().toString()) && Constant.FLAG_Y.equals(itemErrorCtrl.getLookupMeaning())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 查询数据字典是否有维护QC转机且状态为Y    物料防錯管控字典 1211
     *
     * @return
     */
    public boolean isMaintainQCReCheck() throws Exception {
        List<SysLookupTypesDTO> itemErrorCtrls = BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_ITEM_ERROR_CTRL_TYPE);
        for (SysLookupTypesDTO itemErrorCtrl : itemErrorCtrls) {
            if (Constant.LOOKUP_VALUE_QC_RE_CHECK.equals(itemErrorCtrl.getLookupCode().toString()) && Constant.FLAG_Y.equals(itemErrorCtrl.getLookupMeaning())) {
                return true;
            }
        }
        return false;
    }

    public void setSmtMachineMTLHistoryHService(SmtMachineMTLHistoryHService smtMachineMTLHistoryHService) {
        this.smtMachineMTLHistoryHService = smtMachineMTLHistoryHService;
    }

    public void setSmtMachineMTLHistoryLService(SmtMachineMTLHistoryLService smtMachineMTLHistoryLService) {
        this.smtMachineMTLHistoryLService = smtMachineMTLHistoryLService;
    }

    /**
     * PDA转机扫描
     *
     * @param entity
     * @return
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public RetCode asmTransferScan(ASMTransferScanDTO entity) throws Exception {
        RetCode retCode = new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID);
        // 1.根据西门子的设备ID、站位查询得到lineCode
        List<SmtLocationInfo> locationInfoList = this.getLocationInfos(entity.getSmtLocationNo());
        if (CollectionUtils.isEmpty(locationInfoList)) {
            String[] params = new String[]{entity.getSmtLocationNo()};
            return new com.zte.springbootframe.common.model.RetCode(RetCode.BUSINESSERROR_CODE, MessageId.LOCATION_INFO_LACKING, params);
        }
        String lineCode = locationInfoList.get(NumConstant.NUM_ZERO).getLineCode();
        String machineNo = locationInfoList.get(NumConstant.NUM_ZERO).getMachineNo();
        String moduleNo = locationInfoList.get(NumConstant.NUM_ZERO).getModuleNo();
        String locationNo = locationInfoList.get(NumConstant.NUM_ZERO).getLocationNo();
        if (this.currentItemIsBlank(lineCode, machineNo, moduleNo, locationNo)) {
            String[] params = new String[]{entity.getSmtLocationNo()};
            return new com.zte.springbootframe.common.model.RetCode(RetCode.BUSINESSERROR_CODE, MessageId.LOCATION_INFO_LACKING, params);
        }
        this.setEntityInfo(entity, lineCode, machineNo, moduleNo, locationNo);
        // 2.查询线体信息，判断是否启用设备对接、是否ASM线体
        CFLine cfLine = BasicsettingRemoteService.getLine(lineCode);
        if (cfLine == null) {
            String[] params = new String[]{lineCode};
            return new com.zte.springbootframe.common.model.RetCode(RetCode.BUSINESSERROR_CODE, MessageId.LINE_INFO_LACKING, params);
        }
        if (this.isNotDeviceType(cfLine.getSmtDeviceSupport(), cfLine.getSmtdevicetype())) {
            String[] params = new String[]{lineCode};
            return new com.zte.springbootframe.common.model.RetCode(RetCode.BUSINESSERROR_CODE, MessageId.LINE_NOT_SUPPORT_DEVICE_OR_NOT_ASM, params);
        }
        // 3.解析程序名，查询指令
        String[] programAnaData = programNameAnalysis(entity.getProgramName());
        String productCode = programAnaData[0];
        String craftSection = programAnaData[1];
        if (this.isNotProgram(productCode, craftSection)) {
            String[] params = new String[]{entity.getProgramName()};
            return new com.zte.springbootframe.common.model.RetCode(RetCode.BUSINESSERROR_CODE, MessageId.PROGRAM_NAME_ANALYSIS_ERROR, params);
        }
        List<PsWorkOrderDTO> workOrderList = getPsWorkOrderDTOS(lineCode, productCode, craftSection);
        PsWorkOrderDTO workOrder = null;
        if (null == workOrderList) {
            String[] params = new String[]{lineCode};
            return new com.zte.springbootframe.common.model.RetCode(RetCode.BUSINESSERROR_CODE, MessageId.NO_WORK_ORDER_ON_LINE, params);
        }
        workOrderList = workOrderList.stream().sorted(Comparator.comparing(PsWorkOrderDTO::getScheduleStartDate)).collect(Collectors.toList());
        workOrder = this.setCurrentWorkOrder(workOrderList, workOrder);
        if (workOrder == null) {
            String[] params = new String[]{lineCode};
            return new com.zte.springbootframe.common.model.RetCode(RetCode.BUSINESSERROR_CODE, MessageId.NO_WORK_ORDER_ON_LINE, params);
        }
        entity.setProductionPlanId(workOrder.getSourceTask());
        entity.setWorkOrderNo(workOrder.getWorkOrderNo());
        // 4.校验料盘在pk_code_info表是否存在
        List<PkCodeInfo> pkCodeList = this.getPkCodeInfos(entity.getReelId());
        if (CollectionUtils.isEmpty(pkCodeList)) {
            return new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.REEL_ID_NOT_REGISTER);
        }
        pkCodeList = pkCodeList.stream().filter(ft -> entity.getProductionPlanId().equals(ft.getProductTask())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(pkCodeList)) {
            return new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.PRODPLAN_ID_NOT_MATCH_WORKORDER);
        }
        pkCodeList = pkCodeList.stream().filter(ft -> entity.getItemCode().equals(ft.getItemCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(pkCodeList)) {
            return new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.ITEM_CODE_NOT_MATCH_BOM);
        }
        // 5.查上料表是否存在
        String cfgHeaderId = workOrder.getCfgHeaderId();
        if (StringUtils.isBlank(cfgHeaderId)) {
            return new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.WORK_ORDER_NO_CFG_ID);
        }
        entity.setCfgHeaderId(cfgHeaderId);
        List<BSmtBomDetail> detailList = getbSmtBomDetails(entity, moduleNo, locationNo, cfgHeaderId);
        if (CollectionUtils.isEmpty(detailList)) {
            return new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.NOT_MATCH_WITH_BOM_DETAIL);
        }
        return this.getRetCode(entity, retCode, pkCodeList);
    }

    private RetCode getRetCode(ASMTransferScanDTO entity, RetCode retCode, List<PkCodeInfo> pkCodeList) throws Exception {
        // 6.料盘环保属性转换(2位数转1位数)
        PkCodeInfo pkCodeInfo = pkCodeList.get(NumConstant.NUM_ZERO);
        List<SysLookupValuesDTO> lookupValueList = BasicsettingRemoteService.getLookupValueByTypeCodes(MpConstant.LOOKUP_TYPE_ISLEAD);
        String newIsLead = getIsLeadInfo(pkCodeInfo, lookupValueList);
        newIsLead = this.getNewIsLead(newIsLead, pkCodeInfo);
        // 7.校验该料盘是否已被占用
        String errorMsg = this.verificationOccupied(pkCodeInfo);
        if (StringHelper.isNotEmpty(errorMsg)) {
            retCode = new RetCode(RetCode.BUSINESSERROR_CODE, RetCode.BUSINESSERROR_MSGID);
            retCode.setMsg(errorMsg);
            return retCode;
        }
        List<SmtMachineMaterialMouting> mountingList = getSmtMachineMaterialMoutings(entity);
        if (!CollectionUtils.isEmpty(mountingList)) {
            String[] params = new String[]{mountingList.get(NumConstant.NUM_ZERO).getObjectId()};
            return new com.zte.springbootframe.common.model.RetCode(RetCode.BUSINESSERROR_CODE, MessageId.LOCATION_HAS_TRANSFER_SCAN, params);
        }
        // 转机扫描处理
        StringBuilder msg = new StringBuilder();
        if (this.isNotTransferScan(entity, pkCodeInfo, newIsLead, msg)) {
            retCode = new RetCode(RetCode.BUSINESSERROR_CODE, RetCode.BUSINESSERROR_MSGID);
            retCode.setMsg(msg.toString());
            return retCode;
        }
        return retCode;
    }

    public void setEntityInfo(ASMTransferScanDTO entity, String lineCode, String machineNo, String moduleNo, String locationNo) {
        entity.setLineCode(lineCode);
        entity.setMachineNo(machineNo);
        entity.setModuleNo(moduleNo);
        entity.setLocationNo(locationNo);
    }

    public List<SmtLocationInfo> getLocationInfos(String smtLocationNo) throws Exception {
        SmtLocationInfoDTO queryDTO = new SmtLocationInfoDTO();
        queryDTO.setSupplierLoc(smtLocationNo);
        List<SmtLocationInfo> locationInfoList = ObtainRemoteServiceDataUtil.getSmtLocationInfoList(queryDTO);
        return locationInfoList;
    }

    public String verificationOccupied(PkCodeInfo pkCodeInfo) {
        PDATransferScanDTO checkMoutingDTO = new PDATransferScanDTO();
        checkMoutingDTO.setFormFlag(BigDecimal.ZERO);
        checkMoutingDTO.setPkCode(pkCodeInfo.getPkCode());
        String errorMsg = pdaTransferMountingCheck(checkMoutingDTO);
        return errorMsg;
    }

    public List<PkCodeInfo> getPkCodeInfos(String reelId) {
        PkCodeInfoDTO condObj = new PkCodeInfoDTO();
        condObj.setPkCode(reelId);
        condObj.setEnabledFlag(Constant.FLAG_Y);
        return pkCodeInfoRepository.getList(condObj);
    }

    public List<PsWorkOrderDTO> getPsWorkOrderDTOS(String lineCode, String productCode, String craftSection) throws Exception {
        Map workOrderMap = Maps.newHashMap();
        workOrderMap.put("lineCode", lineCode);
        workOrderMap.put("craftSection", craftSection);
        workOrderMap.put("productCode", productCode);
        workOrderMap.put("workOrderStatus", MpConstant.WORK_ORDER_STATUS);
        List<PsWorkOrderDTO> workOrderList = getWorkOrderInfo(workOrderMap);
        return workOrderList;
    }

    public List<BSmtBomDetail> getbSmtBomDetails(ASMTransferScanDTO entity, String moduleNo, String locationNo, String cfgHeaderId) {
        Map bSmtBomMap = new HashMap();
        bSmtBomMap.put(MpConstant.CFG_HEADERID, cfgHeaderId);
        bSmtBomMap.put("moduleNo", moduleNo);
        bSmtBomMap.put(Constant.LOCATION_NO, locationNo);
        bSmtBomMap.put("itemCode", entity.getItemCode());
        List<BSmtBomDetail> detailList = bSmtBomDetailService.getList(bSmtBomMap, null, null);
        return detailList;
    }

    public List<SmtMachineMaterialMouting> getSmtMachineMaterialMoutings(ASMTransferScanDTO entity) {
        SmtMachineMaterialMouting mountingCondObj = new SmtMachineMaterialMouting();
        mountingCondObj.setLineCode(entity.getLineCode());
        mountingCondObj.setWorkOrder(entity.getWorkOrderNo());
        mountingCondObj.setModuleNo(entity.getModuleNo());
        mountingCondObj.setLocationNo(entity.getLocationNo());
        mountingCondObj.setEnabledFlag(Constant.FLAG_Y);
        List<SmtMachineMaterialMouting> mountingList = smtMachineMaterialMoutingRepository.selectSmtMachineMaterialMoutingSelective(mountingCondObj);
        return mountingList;
    }


    /**
     * 获取参数newIsLead
     *
     * @param newIsLead  参数newIsLead
     * @param pkCodeInfo 参数pkCodeInfo
     * @return String类型
     */
    private String getNewIsLead(String newIsLead, PkCodeInfo pkCodeInfo) {
        return StringHelper.isNotEmpty(newIsLead) ? newIsLead : pkCodeInfo.getIsLead();
    }

    /**
     * 判断是否转机扫描处理
     *
     * @param entity     实体
     * @param pkCodeInfo pkcode信息
     * @param newIsLead  新的newIsLead
     * @param msg        msg
     * @return 布尔值
     * @throws Exception 异常
     */
    private boolean isNotTransferScan(ASMTransferScanDTO entity, PkCodeInfo pkCodeInfo, String newIsLead, StringBuilder msg) throws Exception {
        boolean flag = false;
        if (StringHelper.isNotEmpty(entity.getWorkOrderNo()) && StringHelper.isNotEmpty(entity.getLineCode())) {
            String returnMsg = asmTransferScanEvent(entity, pkCodeInfo, newIsLead, Constant.FLAG_N);
            if (StringHelper.isNotEmpty(returnMsg)) {
                msg.append(returnMsg);
                flag = true;
            }
        }
        return flag;
    }

    /**
     * 判断程序名
     *
     * @param productCode  生产代码
     * @param craftSection 工艺
     * @return 布尔值
     */
    private boolean isNotProgram(String productCode, String craftSection) {
        return StringUtils.isBlank(productCode) || StringUtils.isBlank(craftSection);
    }

    /**
     * 判断设备
     *
     * @param smtDeviceSupport smt设备端口
     * @param deviceType       设备类型
     * @return 布尔值
     */
    private boolean isNotDeviceType(String smtDeviceSupport, String deviceType) {
        return !Constant.FLAG_Y.equals(smtDeviceSupport) || !BusinessConstant.DEVICE_TYPE_ASM.equals(deviceType);
    }

    /**
     * 设置WorkOrder
     *
     * @param workOrderList 工序集
     * @param workOrder     工序
     */
    private PsWorkOrderDTO setCurrentWorkOrder(List<PsWorkOrderDTO> workOrderList, PsWorkOrderDTO workOrder) {
        for (PsWorkOrderDTO psWorkOrderDTO : workOrderList) {
            if (Constant.IS_SUBMITTED.equals(psWorkOrderDTO.getWorkOrderStatus())
                    || Constant.IS_HANG_UP.equals(psWorkOrderDTO.getWorkOrderStatus())) {
                workOrder = psWorkOrderDTO;
                break;
            }
            if (Constant.IS_START.equals(psWorkOrderDTO.getWorkOrderStatus())) {
                workOrder = psWorkOrderDTO;
            }
        }
        return workOrder;
    }

    /**
     * 判断当前项是否为空
     * if 里边的原代码：
     * StringUtils.isBlank(lineCode) || StringUtils.isBlank(machineNo) || StringUtils.isBlank(moduleNo) ||
     * StringUtils.isBlank(locationNo)
     *
     * @param lineCode   线体
     * @param machineNo  料位
     * @param moduleNo   moduleN号
     * @param locationNo 站位
     * @return
     */
    private boolean currentItemIsBlank(String lineCode, String machineNo, String moduleNo, String locationNo) {
        return StringUtils.isBlank(lineCode) || StringUtils.isBlank(machineNo) || StringUtils.isBlank(moduleNo) ||
                StringUtils.isBlank(locationNo);
    }

    private String[] programNameAnalysis(String programName) throws Exception {

        String productCode = Constant.STR_EMPTY;
        String craftSection = Constant.STR_EMPTY;
        String[] strAry = StringUtils.split(programName, "_(-)-——（）");
        if (strAry.length >= NumConstant.NUM_THREE) {
            String itemNo = "_" + strAry[1];
            itemNo = StringUtils.replace(itemNo, MpConstant.ITEMNO_LOWERCASE_A, "");
            itemNo = StringUtils.replace(itemNo, MpConstant.ITEMNO_UPCASE_A, "");
            itemNo = StringUtils.replace(itemNo, MpConstant.ITEMNO_LOWERCASE_B, "");
            itemNo = StringUtils.replace(itemNo, MpConstant.ITEMNO_UPCASE_B, "");
            itemNo = StringUtils.replace(itemNo, "_", "");
            productCode = itemNo;

            if (strAry[1].toUpperCase().startsWith(BusinessConstant.STRING_A) || (strAry.length >= NumConstant.NUM_FOUR &&
                    strAry[NumConstant.NUM_THREE].toUpperCase().startsWith(BusinessConstant.STRING_T))) {
                craftSection = Constant.CRAFTSECTION_SMT_A;
            }

            if (strAry[1].toUpperCase().startsWith(BusinessConstant.STRING_B) || (strAry.length >= NumConstant.NUM_FOUR &&
                    strAry[NumConstant.NUM_THREE].toUpperCase().startsWith(BusinessConstant.STRING_B))) {
                craftSection = Constant.CRAFTSECTION_SMT_B;
            }
        }
        return new String[]{productCode, craftSection};
    }


    /**
     * 点对点调用获取指令信息
     *
     * @param map
     * @return
     * @throws Exception
     */
    public List<PsWorkOrderDTO> getWorkOrderInfo(Map map) throws Exception {

        JsonNode json = PlanscheduleRemoteService.getBasicWorkOrderInfo(map);
        if (null == json) {
            return null;
        }
        String retCode = json.get(MpConstant.JSON_CODE).get(MpConstant.JSON_CODE).asText();
        String bo = json.get(MpConstant.JSON_BO).toString();
        if (!RetCode.SUCCESS_CODE.equalsIgnoreCase(retCode)) {
            return null;
        }
        return JsonConvertUtil.jsonToBean(bo, List.class, PsWorkOrderDTO.class);
    }


    /**
     * ASM 西门子转机扫描处理
     *
     * @param entity
     * @param pkCodeInfo
     * @param newIsLead
     * @param attr1
     * @return
     * @throws Exception
     */
    public String asmTransferScanEvent(ASMTransferScanDTO entity, PkCodeInfo pkCodeInfo, String newIsLead,
                                       String attr1) throws Exception {
        String returnMsg = "";
        String feederNo = "";
        // 未启用feeder标识， smt_machine_material_prepare得到该(料盘、指令、线体)绑定的feederId， 找不到，则feederId为空
        SmtMachineMaterialPrepareDTO prepareParamsDTO = new SmtMachineMaterialPrepareDTO();
        prepareParamsDTO.setEntityId(entity.getEntityId());
        prepareParamsDTO.setFactoryId(entity.getFactoryId());
        prepareParamsDTO.setLineCode(entity.getLineCode());
        prepareParamsDTO.setWorkOrder(entity.getWorkOrderNo());
        prepareParamsDTO.setObjectId(entity.getReelId());
        List<SmtMachineMaterialPrepare> prepareList = smtMachineMaterialPrepareRepository.getRelOneList(prepareParamsDTO);
        if (!CollectionUtils.isEmpty(prepareList)) {
            feederNo = prepareList.get(NumConstant.NUM_ZERO).getFeederNo();
        }
        entity.setFeederNo(feederNo);

        // 写入上料历史表
        String mountType = NumConstant.STR_ONE;
        this.insertSmtMachineMTLHistoryAll(entity, pkCodeInfo, newIsLead, attr1, mountType);

        // 转机扫描
        // 上料历史中指令与选择指令一致时，写入mounting表
        List<SmtMachineMaterialMoutingDTO> listAddDTO = new ArrayList<>();
        SmtMachineMaterialMoutingDTO moutingParamDTO = this.getSmtMachineMaterialMoutingDTO(entity, pkCodeInfo, newIsLead);
        listAddDTO.add(moutingParamDTO);
        smtMachineMaterialMoutingService.batchInsertSmtMachineMaterialMouting(listAddDTO);

        // 删除prepare表
        SmtMachineMaterialPrepare delPrepareParams = new SmtMachineMaterialPrepare();
        delPrepareParams.setEntityId(entity.getEntityId());
        delPrepareParams.setFactoryId(entity.getFactoryId());
        delPrepareParams.setLineCode(entity.getLineCode());
        delPrepareParams.setObjectId(entity.getReelId());
        smtMachineMaterialPrepareService.deleteSmtMachineMaterialPrepareById(delPrepareParams);

        // 线体 + 指令 + 类型获取上料历史头表状态
        this.dealTransferCompleted(entity, mountType);

        // 写入reel历史轨迹
        this.insertPkCodeHistory(entity, pkCodeInfo);
        return returnMsg;
    }

    /**
     * 组装机台在用信息
     *
     * @param entity
     * @param pkCodeInfo
     * @param newIsLead
     * @return
     */
    private SmtMachineMaterialMoutingDTO getSmtMachineMaterialMoutingDTO(ASMTransferScanDTO entity, PkCodeInfo pkCodeInfo, String newIsLead) {
        SmtMachineMaterialMoutingDTO moutingParamDTO = new SmtMachineMaterialMoutingDTO();
        moutingParamDTO.setMachineMaterialMoutingId(UUID.randomUUID().toString());
        moutingParamDTO.setEntityId(entity.getEntityId());
        moutingParamDTO.setFactoryId(entity.getFactoryId());
        moutingParamDTO.setCreateUser(entity.getCreateBy());
        moutingParamDTO.setLastUpdatedBy(entity.getCreateBy());
        moutingParamDTO.setFeederNo(entity.getFeederNo());
        moutingParamDTO.setForward("");
        moutingParamDTO.setItemCode(pkCodeInfo.getItemCode());
        moutingParamDTO.setItemName(pkCodeInfo.getItemName());
        moutingParamDTO.setLineCode(entity.getLineCode());
        moutingParamDTO.setWorkOrder(entity.getWorkOrderNo());
        moutingParamDTO.setMachineNo(entity.getMachineNo());
        moutingParamDTO.setModuleNo(entity.getModuleNo());
        moutingParamDTO.setLocationNo(entity.getLocationNo());
        moutingParamDTO.setNextReelRowid("");
        moutingParamDTO.setObjectId(entity.getReelId());
        moutingParamDTO.setQty(pkCodeInfo.getItemQty());
        moutingParamDTO.setRawQty(pkCodeInfo.getItemQty());
        moutingParamDTO.setRemark(MpConstant.ANGLE_BRACKET_CHAR + entity.getWorkOrderNo());
        moutingParamDTO.setTrackNo("");
        moutingParamDTO.setIsLead(newIsLead);
        moutingParamDTO.setAvl(pkCodeInfo.getSysLotCode());
        moutingParamDTO.setPolarInfo("");
        moutingParamDTO.setSourceBatchCode(pkCodeInfo.getSourceBatchCode());
        moutingParamDTO.setCfgHeaderId(entity.getCfgHeaderId());
        return moutingParamDTO;
    }

    private void insertSmtMachineMTLHistoryAll(ASMTransferScanDTO entity, PkCodeInfo pkCodeInfo, String newIsLead, String attr1, String mountType) {
        SmtMachineMTLHistoryL historyLObj = new SmtMachineMTLHistoryL();
        historyLObj.setEntityId(entity.getEntityId());
        historyLObj.setFactoryId(entity.getFactoryId());
        historyLObj.setCreateUser(entity.getCreateBy());
        historyLObj.setLastUpdatedBy(entity.getLastUpdatedBy());
        historyLObj.setLineCode(entity.getLineCode());
        historyLObj.setWorkOrder(entity.getWorkOrderNo());
        historyLObj.setObjectId(entity.getReelId());
        historyLObj.setMachineNo(entity.getMachineNo());
        historyLObj.setModuleNo(entity.getModuleNo());
        historyLObj.setLocationNo(entity.getLocationNo());
        historyLObj.setMountType(mountType);
        historyLObj.setItemCode(pkCodeInfo.getItemCode());
        historyLObj.setItemName(pkCodeInfo.getItemName());
        historyLObj.setSourceBatchCode(pkCodeInfo.getSourceBatchCode());
        historyLObj.setAvl(pkCodeInfo.getSysLotCode());
        historyLObj.setQty(pkCodeInfo.getItemQty());
        historyLObj.setIsLead(newIsLead);
        historyLObj.setPolarInfo("");
        historyLObj.setAttr1(attr1);
        historyLObj.setCfgHeaderId(entity.getCfgHeaderId());
        historyLObj.setFeederNo(entity.getFeederNo());
        historyLObj.setLastScanFlag(true);
        smtMachineMTLHistoryLService.insertSmtMachineMTLHistoryAll(historyLObj);
    }

    private void dealTransferCompleted(ASMTransferScanDTO entity, String mountType) throws Exception {
        PDATransferScanDTO queryDTO = new PDATransferScanDTO();
        queryDTO.setLineCode(entity.getLineCode());
        queryDTO.setWorkOrder(entity.getWorkOrderNo());
        queryDTO.setEntityId(entity.getEntityId());
        queryDTO.setFactoryId(entity.getFactoryId());
        queryDTO.setDrLocationNo(entity.getLocationNo());
        dealTransferCompleted(queryDTO, mountType);
    }

    private void insertPkCodeHistory(ASMTransferScanDTO entity, PkCodeInfo pkCodeInfo) {
        PkCodeHistory pkCodeHistoryParams = new PkCodeHistory();
        pkCodeHistoryParams.setHistoryId(UUID.randomUUID().toString());
        pkCodeHistoryParams.setEntityId(entity.getEntityId());
        pkCodeHistoryParams.setFactoryId(entity.getFactoryId());
        pkCodeHistoryParams.setCreateBy(entity.getCreateBy());
        pkCodeHistoryParams.setLastUpdatedBy(entity.getCreateBy());
        pkCodeHistoryParams.setObjectId(entity.getReelId());
        pkCodeHistoryParams.setObjectType(MpConstant.REEL_ID);
        pkCodeHistoryParams.setItemCode(pkCodeInfo.getItemCode());
        pkCodeHistoryParams.setProgramName(MpConstant.TRANSFER_SCAN);
        pkCodeHistoryParams.setCurrentQty(pkCodeInfo.getItemQty());
        pkCodeHistoryParams.setSourceTask(entity.getProductionPlanId());
        pkCodeHistoryParams.setLineCode(entity.getLineCode());
        pkCodeHistoryParams.setWorkOrder(entity.getWorkOrderNo());
        pkCodeHistoryParams.setLocationNo(entity.getLocationNo());
        pkCodeHistoryParams.setMachineNo(entity.getMachineNo());
        pkCodeHistoryParams.setModuleNo(entity.getModuleNo());
        pkCodeHistoryParams.setSourceBatchCode(pkCodeInfo.getSourceBatchCode());
        pkCodeHistoryService.insertPkCodeHistory(pkCodeHistoryParams);
    }


    /**
     * dip上料扫描
     *
     * @param entity
     * @return
     * @throws Exception
     */
    @Override
    public String dipMaterialScan(SmtMachineMTLHistoryLDTO entity) throws Exception {
        String returnMsg = "";
        //设置pkCode信息
        setPkCodeList(entity);
        //判断是否组合物料
        List<PkCodeInfo> pkList = entity.getPkCodeList();
        String combination = Constant.FLAG_N;
        String firstItemCode = pkList.get(0).getItemCode();
        for (PkCodeInfo e : pkList) {
            if (!e.getItemCode().equals(firstItemCode)) {
                combination = Constant.FLAG_Y;
            }
        }
        entity.setCombination(combination);
        //插入上料历史表
        smtMachineMTLHistoryLService.insertSmtMachineMTLHistoryAll(SmtMachineMTLHistoryLAssembler.toEntity(entity));
        String operateMsg = entity.getOperateMsg();
        if (StringUtils.isEmpty(operateMsg)) {
            //分批获取pk码信息插入机台在用物料表
            if (null != entity.getPkCodeList() && MpConstant.NUM_0 != entity.getPkCodeList().size()) {
                List<SmtMachineMaterialMoutingDTO> moutingList = new ArrayList<SmtMachineMaterialMoutingDTO>();
                List<SysLookupValuesDTO> lookupValueList = BasicsettingRemoteService.getLookupValueByTypeCodes(MpConstant.LOOKUP_TYPE_ISLEAD);
                for (PkCodeInfo pkCodeInfo : entity.getPkCodeList()) {
                    SmtMachineMaterialMoutingDTO moutingParam = new SmtMachineMaterialMoutingDTO();
                    BeanUtils.copyProperties(entity, moutingParam);
                    moutingParam.setMachineMaterialMoutingId(UUID.randomUUID().toString());
                    moutingParam.setEnabledFlag(Constant.FLAG_Y);
                    moutingParam.setObjectId(pkCodeInfo.getPkCode());
                    moutingParam.setQty(pkCodeInfo.getItemQty());
                    moutingParam.setItemCode(pkCodeInfo.getItemCode());
                    moutingParam.setSourceBatchCode(pkCodeInfo.getSourceBatchCode());
                    moutingParam.setCombination(combination);
                    moutingParam.setLpn(entity.getLpn());
                    PkCodeInfo tempPkInfo = new PkCodeInfo();
                    tempPkInfo.setIsLead(pkCodeInfo.getIsLead());
                    String newIsLead = getIsLeadInfo(tempPkInfo, lookupValueList);
                    if (StringUtils.isNotEmpty(newIsLead)) {
                        moutingParam.setIsLead(newIsLead);
                    }
                    moutingList.add(moutingParam);
                }
                smtMachineMaterialMoutingService.batchInsertSmtMachineMaterialMouting(moutingList);
            }
        }
        savePkCodeHisoty(entity, entity.getPkCodeList(), Constant.DIP_MATERIAL_SCAN);
        return returnMsg;
    }

    /**
     * 获取pkCode信息
     *
     * @param entity
     */
    private void setPkCodeList(SmtMachineMTLHistoryLDTO entity) {
        if (null == entity || null == entity.getObjectIdList() || MpConstant.NUM_0 == entity.getObjectIdList().size()) {
            return;
        }
        List<PkCodeInfo> pkCodeList = new ArrayList<PkCodeInfo>();
        List<List<String>> bigObjectList = CommonUtils.splitList(entity.getObjectIdList(), Constant.BATCH_SIZE);
        for (List<String> tempObjectList : bigObjectList) {
            StringBuilder inPkCodeSb = new StringBuilder();
            for (String objectId : tempObjectList) {
                inPkCodeSb.append(Constant.SINGLE_QUOTE).append(objectId).append(Constant.SINGLE_QUOTE).append(Constant.COMMA);
            }
            String inPkCode = inPkCodeSb.toString();
            PkCodeInfoDTO quendCond = new PkCodeInfoDTO();
            quendCond.setInPkCode(inPkCode.substring(0, inPkCode.length() - 1));
            List<PkCodeInfo> resultList = pkCodeInfoService.getList(quendCond);
            pkCodeList.addAll(resultList);

        }
        entity.setPkCodeList(pkCodeList);
    }

    /**
     * 分批保存pkCode历史表
     *
     * @param dto
     * @param pkCodeList
     * @param programName
     */
    public void savePkCodeHisoty(SmtMachineMTLHistoryLDTO dto, List<PkCodeInfo> pkCodeList, String programName) {
        List<PkCodeHistory> pkcodeHistoryList = new ArrayList<PkCodeHistory>();
        for (PkCodeInfo pkCodeInfo : pkCodeList) {
            PkCodeHistory codeHistory = new PkCodeHistory();
            codeHistory.setLastUpdatedBy(dto.getCreateUser());
            codeHistory.setCreateDate(dto.getCreateDate());
            codeHistory.setEntityId(dto.getEntityId());
            codeHistory.setFactoryId(dto.getFactoryId());
            codeHistory.setEnabledFlag(Constant.FLAG_Y);
            codeHistory.setProgramName(programName);
            codeHistory.setHistoryId(UUID.randomUUID().toString());
            codeHistory.setObjectId(pkCodeInfo.getPkCode());
            codeHistory.setObjectType(Constant.OBJECT_TYPE_REEL_ID);
            codeHistory.setItemCode(dto.getItemCode());
            codeHistory.setCurrentQty(pkCodeInfo.getItemQty());
            codeHistory.setSourceTask(pkCodeInfo.getProductTask());
            codeHistory.setWorkOrder(dto.getWorkOrder());
            codeHistory.setLineCode(dto.getLineCode());
            codeHistory.setModuleNo(dto.getModuleNo());
            codeHistory.setLocationNo(dto.getLocationNo());
            codeHistory.setMachineNo(dto.getMachineNo());
            pkcodeHistoryList.add(codeHistory);
        }
        List<List<PkCodeHistory>> bigHistoryList = CommonUtils.splitList(pkcodeHistoryList, Constant.BATCH_SIZE);
        for (List<PkCodeHistory> tempList : bigHistoryList) {
            pkCodeHistoryService.insertPkCodeHistoryBatch(tempList);
        }
    }

    /**
     * AVL查询
     */
    public Map<String, String> getAvl(List<String> itemList) throws Exception {
        //拼接物料代码，批量查询
        StringBuilder bomNoSb = new StringBuilder();
        itemList.forEach(p -> {
            bomNoSb.append(Constant.QUO_MARK);
            bomNoSb.append(p);
            bomNoSb.append(Constant.COMMA_MARK);
        });

        bomNoSb.setLength(bomNoSb.length() - 1);
        Map<String, String> paramsMap = new HashMap<>();
        paramsMap.put(Constant.BOM_NO, bomNoSb.toString());
        LOG.info("远程调用同步的入参 :{}", paramsMap);
        Map<String, String> headParams = MESHttpHelper.getHttpRequestHeader();
        String url = constantInterface.getUrl(InterfaceEnum.dataWbSysGetAvlInfo);
        LOG.info("远程调用同步的URL :{}", url);
        String msg = HttpRemoteService.remoteExe(InterfaceEnum.dataWbSysGetAvlInfo, paramsMap, headParams, url);
        if (StringUtils.isBlank(msg)) {
            throw new Exception(CommonUtils.getLmbMessage(MessageId.AVL_SERVER_FAILED));
        }
        JsonNode json = JacksonJsonConverUtil.getMapperInstance().readTree(msg);
        LOG.info("远程调用同步的结果 :{}", json);
        String retCode = json.get(MpConstant.JSON_CODE).get(MpConstant.JSON_CODE).asText();
        String bo = json.get(MpConstant.JSON_BO).toString();
        if (!RetCode.SUCCESS_CODE.equalsIgnoreCase(retCode)) {
            throw new Exception(bo);
        }
        Map<String, Object> reMap = CommonUtils.jsonStringToMap(bo);
        //判断是否有AVL结果
        if (reMap.get(Constant.STATUS).equals(Constant.E)) {
            //未查询到结果，直接返回
            return null;
        }
        JSONArray avlJson = null;
        if (reMap != null && reMap.size() > 0 && reMap.get(Constant.STR_LIST) != null) {
            avlJson = (JSONArray) reMap.get(Constant.STR_LIST);
        }
        Map<String, String> avlMap = new HashMap<>();
        if (avlJson != null && avlJson.size() > NumConstant.NUM_ZERO) {
            for (int i = 0; i < avlJson.size(); i++) {
                addAvlMap(avlJson, avlMap, i);
            }
        }
        return avlMap;
    }

    //设置avlMap
    private void addAvlMap(JSONArray avlJson, Map<String, String> avlMap, int i) {
        try {
            JSONObject jsonTmp = ((JSONObject) avlJson.get(i));
            if (jsonTmp == null) {
                return;
            }
            Object itemUuidObj = jsonTmp.get(Constant.ITEM_UUID);
            Object itemNoObj = jsonTmp.get(Constant.ITEMNO);
            if (null != jsonTmp && null != itemUuidObj && null != itemNoObj) {
                avlMap.put(itemUuidObj.toString(),
                        itemNoObj.toString());
            }
        } catch (Exception e) {
            e.printStackTrace();
            ;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SmtMachineMaterialMoutingDTO handFeedingScan(SmtMachineMaterialMoutingDTO dto) throws Exception {
        String workOrderNo = dto.getWorkOrder();
        if (StringUtils.isBlank(dto.getLineCode()) || StringUtils.isBlank(workOrderNo) || StringUtils.isBlank(dto.getCfgHeaderId()) || StringUtils.isBlank(dto.getObjectId())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PARAM_MISSING);
        }

        //校验料盘在mouting是否存在（object id或者next reel id=料盘），存在报错“已经被XXX指令使用了”
        SmtMachineMaterialMoutingDTO params = new SmtMachineMaterialMoutingDTO();
        params.setObjectIdOrNext(dto.getObjectId());
        List<SmtMachineMaterialMouting> mountingList = smtMachineMaterialMoutingRepository.selectMoutingWithPkCodeInfoSelective(params);
        if (!CollectionUtils.isEmpty(mountingList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.REELID_IS_USED_BY_WORKORDER, new Object[]{mountingList.get(NumConstant.NUM_ZERO).getWorkOrder()});
        }
        if (mountingList.size() > NumConstant.NUM_ONE) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.MULTIPLE_DATA_FOR_THE_REELID);
        }
        //检验料盘是否存在pk_code_info中且product_task跟指令的source_task一致，报错“没有该条码信息或该条码不是这个指令的”
        PkCodeInfo pkCodeInfoParm = new PkCodeInfo();
        pkCodeInfoParm.setPkCode(dto.getObjectId());
        PkCodeInfo pkCodeInfo = pkCodeInfoRepository.getPkCodeInfoByCode(pkCodeInfoParm);
        //获取环保属性
        List<SysLookupValuesDTO> lookupValueList = BasicsettingRemoteService.getLookupValueByTypeCodes(MpConstant.LOOKUP_TYPE_ISLEAD);
        String newIsLead = getIsLeadInfo(pkCodeInfo, lookupValueList);
        newIsLead = StringHelper.isNotEmpty(newIsLead) ? newIsLead : pkCodeInfo.getIsLead();
        //获取对应指令信息
        PsWorkOrderBasic psWorkOrderBasic = PlanscheduleRemoteService.findWorkOrder(workOrderNo);
        if (psWorkOrderBasic == null) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.WORKORDER_NOT_FIND);
        }
        if (null == pkCodeInfo || !StringUtils.equals(pkCodeInfo.getProductTask(), psWorkOrderBasic.getSourceTask())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.REELID_IS_NOT_FOR_THIS_WORKORDER);
        }
        // avl校验 avl为黑名单
        List<String> itemList = new ArrayList<>();
        itemList.add(pkCodeInfo.getItemCode());
        Map<String, String> avlMap = getAvl(itemList);
        if (avlMap != null && avlMap.size() > NumConstant.NUM_ZERO) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.AVL_CHECK_FAILED);
        }
        //如果料盘的数量小于等于0，报错“料盘的数量不能为0”
        if (new BigDecimal(NumConstant.NUM_ZERO).compareTo(pkCodeInfo.getItemQty()) >= NumConstant.NUM_ZERO) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.THE_NUMBER_OF_TRAYS_CANNOT_BE_ZERO);
        }
        //获取指令上料表虚拟站位数据
        Map<String, Object> map = new HashMap<String, Object>();
        map.put(MpConstant.CFG_HEADERID, dto.getCfgHeaderId());
        //是否虚拟站位
        map.put("virtualFlag", Constant.FLAG_Y);
        List<BSmtBomDetail> bSmtBomDetailList = bSmtBomDetailService.getList(map, Constant.LAST_UPDATED_DATE, Constant.DESC);
        //上料数据为空报错
        if (CollectionUtils.isEmpty(bSmtBomDetailList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.B_SMT_BOM_DETAIL_OF_WORKORDER_IS_NULL);
        }
        //按物料代码分组,新增会保证物料代码维度下唯一性
        Map<String, BSmtBomDetail> bSmtBomDetailMap = bSmtBomDetailList.stream().collect(Collectors.toMap(BSmtBomDetail::getItemCode, a -> a, (k1, k2) -> k1));
        String itemCode = pkCodeInfo.getItemCode();
        BSmtBomDetail itemCodeBSmtBomDetail = bSmtBomDetailMap.get(itemCode);
        //根据reel id带出物料代码，跟上料表的虚拟站位比较，如果上料表不存在，则报错“上料表没这个物料”
        if (itemCodeBSmtBomDetail == null) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.NO_DATA_FOR_THE_ITEMCODE_IN_BOM_SMT, new Object[]{itemCode});
        }
        //站位
        String locationNo = itemCodeBSmtBomDetail.getLocationNo();
        SmtMachineMaterialMoutingDTO locationParm = new SmtMachineMaterialMoutingDTO();
        locationParm.setWorkOrder(dto.getWorkOrder());
        locationParm.setLocationNo(locationNo);
        List<SmtMachineMaterialMouting> smtMachineMaterialMoutingList = smtMachineMaterialMoutingRepository.selectMoutingWithPkCodeInfoSelective(locationParm);
        //如果存在，再看mouting表中是否有这个虚拟站位的数据，如果有判断该指令转机扫描是否完成（上料历史表中mouty_type=1 pick_status=2）
        if (!CollectionUtils.isEmpty(smtMachineMaterialMoutingList)) {
            forMoutingListNotEmpty(dto, pkCodeInfo, newIsLead, itemCodeBSmtBomDetail, smtMachineMaterialMoutingList);
        }
        //如果mouting没有，则插入mouting表并记录上料历史（mouty_type=1），如果整个上料表的物料上料完毕，则转机扫描状态改成转机完成，发送开机消息（参考转机扫描）
        else {
            forMoutingIsEmpty(dto, pkCodeInfo, newIsLead, psWorkOrderBasic, itemCodeBSmtBomDetail);
        }
        //处理完后将reelid及该物料在位号信息表中的位号拼接（以逗号分隔，根据指令料单代码、指令主工序查询位号信息表，无位号记录则不记录）填充到grid中
        return setPointLocs(dto, pkCodeInfo, psWorkOrderBasic, locationNo);
    }

    //机台在用不为空时
    private void forMoutingIsEmpty(SmtMachineMaterialMoutingDTO dto, PkCodeInfo pkCodeInfo, String newIsLead, PsWorkOrderBasic psWorkOrderBasic, BSmtBomDetail itemCodeBSmtBomDetail) throws Exception {
        //写机台在用
        insertSmtMachineMaterialMouting(dto, pkCodeInfo, newIsLead, psWorkOrderBasic, itemCodeBSmtBomDetail);

        // 写入上料历史表
        String mountType = NumConstant.STR_ONE;
        SmtMachineMTLHistoryL historyLObj = insertSmtMachineMTLHistoryL(dto, pkCodeInfo, itemCodeBSmtBomDetail, newIsLead, mountType);
        //最后一盘料  去掉最后一盘条件，考虑并发，多界面操作
        //判断是否比对完成
        smtMachineMTLHistoryLService.insertSmtMachineMTLHistoryAllAfter(historyLObj, historyLObj.getHeaderId());
        //发送开机消息(比对完成才发)
        PDATransferScanDTO pdaTransferScanDTO = new PDATransferScanDTO();
        pdaTransferScanDTO.setDrLocationNo(itemCodeBSmtBomDetail.getLocationNo());
        pdaTransferScanDTO.setEntityId(dto.getEntityId());
        pdaTransferScanDTO.setFactoryId(dto.getFactoryId());
        pdaTransferScanDTO.setLineCode(dto.getLineCode());
        pdaTransferScanDTO.setWorkOrder(dto.getWorkOrder());
        this.dealTransferCompleted(pdaTransferScanDTO, mountType);
    }

    //机台在用为空时
    private void forMoutingListNotEmpty(SmtMachineMaterialMoutingDTO dto, PkCodeInfo pkCodeInfo, String newIsLead, BSmtBomDetail itemCodeBSmtBomDetail, List<SmtMachineMaterialMouting> smtMachineMaterialMoutingList) throws Exception {
        //判断该指令转机扫描是否完成
        checkPickUp(dto);
        SmtMachineMaterialMouting smtMachineMaterialMouting = smtMachineMaterialMoutingList.get(NumConstant.NUM_ZERO);
        //续料盘
        String nextReelIdRowid = smtMachineMaterialMouting.getNextReelRowid();
        //如果旧料盘有续料盘记录有则报错“该站位已有上料记录”
        if (StringUtils.isNotEmpty(nextReelIdRowid)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.THE_STATION_HAS_A_LOADING_RECORD);
        }
        //无续料盘且旧料盘数量<=0则根据旧料盘更新mouting对应的料盘信息为料盘
        if (new BigDecimal(NumConstant.NUM_ZERO).compareTo(smtMachineMaterialMouting.getQty()) >= NumConstant.NUM_ZERO) {
            SmtMachineMaterialMouting smtMachineMaterialMoutingUpdate = new SmtMachineMaterialMouting();
            smtMachineMaterialMoutingUpdate.setMachineMaterialMoutingId(smtMachineMaterialMouting.getMachineMaterialMoutingId());
            smtMachineMaterialMoutingUpdate.setLastUpdatedBy(dto.getLastUpdatedBy());
            smtMachineMaterialMoutingUpdate.setObjectId(dto.getObjectId());
            smtMachineMaterialMoutingRepository.updateSmtMachineMaterialMoutingByIdSelective(smtMachineMaterialMoutingUpdate);
        }
        //无续料盘且旧料盘数量>0则将新的reelid写入续料盘中
        else {
            SmtMachineMaterialMouting smtMachineMaterialMoutingUpdate = new SmtMachineMaterialMouting();
            smtMachineMaterialMoutingUpdate.setMachineMaterialMoutingId(smtMachineMaterialMouting.getMachineMaterialMoutingId());
            smtMachineMaterialMoutingUpdate.setNextReelRowid(dto.getObjectId());
            smtMachineMaterialMoutingUpdate.setLastUpdatedBy(dto.getLastUpdatedBy());
            smtMachineMaterialMoutingRepository.updateSmtMachineMaterialMoutingByIdSelective(smtMachineMaterialMoutingUpdate);
        }
        //记录上料历史（mouty_type=2）
        String mountType = NumConstant.STR_TWO;
        SmtMachineMTLHistoryL historyLObj = insertSmtMachineMTLHistoryL(dto, pkCodeInfo, itemCodeBSmtBomDetail, newIsLead, mountType);
    }

    //判断该指令转机扫描是否完成
    public void checkPickUp(SmtMachineMaterialMoutingDTO dto) throws Exception {
        Map<String, Object> smtMachineMap = new HashMap<String, Object>(NumConstant.NUM_SEVEN);
        smtMachineMap.put("lineCode", dto.getLineCode());
        smtMachineMap.put("workOrder", dto.getWorkOrder());
        smtMachineMap.put("mountType", NumConstant.STR_ONE);
        smtMachineMap.put("pickStatus", NumConstant.STR_TWO);
        smtMachineMap.put("entityId", dto.getEntityId());
        smtMachineMap.put("factoryId", dto.getFactoryId());
        smtMachineMap.put("enabledFlag", Constant.FLAG_Y);
        List<SmtMachineMTLHistoryH> historyHList = smtMachineMTLHistoryHService.getList(smtMachineMap, "", "", MpConstant.NUM_MINUS_1, MpConstant.NUM_MINUS_1);
        //转机扫描未完成，报错
        if (CollectionUtils.isEmpty(historyHList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PDA_TRANSFER_SCAN_NOT_COMPLETED, new Object[]{dto.getWorkOrder()});
        }
    }

    //拼装返回dto
    private SmtMachineMaterialMoutingDTO setPointLocs(SmtMachineMaterialMoutingDTO dto, PkCodeInfo pkCodeInfo, PsWorkOrderBasic psWorkOrderBasic, String locationNo) throws Exception {
        BPcbLocationDetailDTO bPcbLocationDetail = new BPcbLocationDetailDTO();
        bPcbLocationDetail.setItemCode(pkCodeInfo.getItemCode());
        bPcbLocationDetail.setCraftSection(psWorkOrderBasic.getCraftSection());
        bPcbLocationDetail.setProductCode(psWorkOrderBasic.getItemNo());
        List<BPcbLocationDetail> bPcbLocationDetailList = centerfactoryRemoteService.getBPcbLocList(bPcbLocationDetail);
        SmtMachineMaterialMoutingDTO locationParm = new SmtMachineMaterialMoutingDTO();
        locationParm.setWorkOrder(dto.getWorkOrder());
        locationParm.setLocationNo(locationNo);
        List<SmtMachineMaterialMouting> smtMachineMaterialMoutingList = smtMachineMaterialMoutingRepository.selectMoutingWithPkCodeInfoSelective(locationParm);
        if (CollectionUtils.isEmpty(smtMachineMaterialMoutingList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SMT_MACHINE_MATERIAL_OF_WORKORDER_IS_NULL);
        }
        SmtMachineMaterialMouting machineMaterialMouting = smtMachineMaterialMoutingList.get(NumConstant.NUM_ZERO);
        SmtMachineMaterialMoutingDTO returnSmtMachineMaterialMoutingDTO = SmtMachineMaterialMoutingAssembler.toDTO(machineMaterialMouting);
        if (!CollectionUtils.isEmpty(bPcbLocationDetailList)) {
            List<String> pointLocList = bPcbLocationDetailList.stream().map(BPcbLocationDetail::getPointLoc).collect(Collectors.toList());
            returnSmtMachineMaterialMoutingDTO.setPointLocs(String.join(Constant.COMMA, pointLocList));
        }
        return returnSmtMachineMaterialMoutingDTO;
    }

    //写机台在用
    private void insertSmtMachineMaterialMouting(SmtMachineMaterialMoutingDTO dto, PkCodeInfo pkCodeInfo, String newIsLead, PsWorkOrderBasic psWorkOrderBasic, BSmtBomDetail itemCodeBSmtBomDetail) {
        SmtMachineMaterialMouting entity = SmtMachineMaterialMoutingAssembler.toEntity(dto);
        entity.setMachineMaterialMoutingId(UUID.randomUUID().toString());
        entity.setWorkOrder(psWorkOrderBasic.getWorkOrderNo());
        entity.setItemCode(pkCodeInfo.getItemCode());
        entity.setLineCode(psWorkOrderBasic.getLineCode());
        entity.setLocationNo(itemCodeBSmtBomDetail.getLocationNo());
        entity.setItemName(pkCodeInfo.getItemName());
        entity.setForward(StringUtils.EMPTY);
        entity.setMachineNo(itemCodeBSmtBomDetail.getMachineNo());
        entity.setTrackNo(StringUtils.EMPTY);
        entity.setFeederNo(StringUtils.EMPTY);
        entity.setObjectId(pkCodeInfo.getPkCode());
        entity.setNextReelRowid(StringUtils.EMPTY);
        entity.setQty(pkCodeInfo.getItemQty());
        entity.setRawQty(pkCodeInfo.getRawQty());
        entity.setModuleNo(itemCodeBSmtBomDetail.getModuleNo());
        entity.setCreateUser(dto.getLastUpdatedBy());
        entity.setLastUpdatedBy(dto.getLastUpdatedBy());
        entity.setOrgId(dto.getOrgId());
        entity.setFactoryId(dto.getFactoryId());
        entity.setEntityId(dto.getEntityId());
        entity.setEnabledFlag(Constant.FLAG_Y);
        entity.setIsLead(newIsLead);
        entity.setAvl(pkCodeInfo.getSysLotCode());
        entity.setPolarInfo(StringUtils.EMPTY);
        entity.setItemType(StringUtils.EMPTY);
        entity.setWetLevel(pkCodeInfo.getWetLevel());
        entity.setSourceBatchCode(pkCodeInfo.getSourceBatchCode());
        entity.setCfgHeaderId(dto.getCfgHeaderId());
        entity.setLpn(StringUtils.EMPTY);
        entity.setRemark(MpConstant.HAND_FEEDING_SCANNING);
        entity.setCombination(Constant.FLAG_N);
        smtMachineMaterialMoutingRepository.insertSmtMachineMaterialMouting(entity);
    }

    //写上料历史
    private SmtMachineMTLHistoryL insertSmtMachineMTLHistoryL(SmtMachineMaterialMoutingDTO dto, PkCodeInfo pkCodeInfo
            , BSmtBomDetail itemCodeBSmtBomDetail, String newIsLead, String mountType) throws MesBusinessException {
        SmtMachineMTLHistoryL historyLObj = new SmtMachineMTLHistoryL();
        historyLObj.setEntityId(dto.getEntityId());
        historyLObj.setFactoryId(dto.getFactoryId());
        historyLObj.setCreateUser(dto.getCreateUser());
        historyLObj.setLastUpdatedBy(dto.getLastUpdatedBy());
        historyLObj.setLineCode(dto.getLineCode());
        historyLObj.setWorkOrder(dto.getWorkOrder());
        historyLObj.setCfgHeaderId(dto.getCfgHeaderId());
        historyLObj.setMountType(mountType);
        historyLObj.setPickStatus(NumConstant.STR_ONE);
        historyLObj.setItemCode(pkCodeInfo.getItemCode());
        historyLObj.setItemName(pkCodeInfo.getItemName());
        historyLObj.setLocationNo(itemCodeBSmtBomDetail.getLocationNo());
        historyLObj.setModuleNo(itemCodeBSmtBomDetail.getModuleNo());
        historyLObj.setMachineNo(itemCodeBSmtBomDetail.getMachineNo());
        historyLObj.setSourceBatchCode(pkCodeInfo.getSourceBatchCode());
        historyLObj.setIsLead(newIsLead);
        historyLObj.setAvl(pkCodeInfo.getSysLotCode());
        historyLObj.setPolarInfo(StringUtils.EMPTY);
        historyLObj.setObjectId(pkCodeInfo.getPkCode());
        historyLObj.setQty(pkCodeInfo.getItemQty());
        historyLObj.setFeederNo(StringUtils.EMPTY);
        historyLObj.setAttr1(StringUtils.EMPTY);
        historyLObj.setLineId(UUID.randomUUID().toString());
        historyLObj.setEnabledFlag(Constant.FLAG_Y);
        historyLObj.setTracingQty(pkCodeInfo.getItemQty());
        String headerId = smtMachineMTLHistoryLService.insertSmtMachineMTLHistoryAll(historyLObj);
        historyLObj.setHeaderId(headerId);
        return historyLObj;
    }

    @Override
    public ServiceData<List<PDATransferScanModuleDto>> getModuleBSmtBomDetailInfo(PDATransferScanCommonDto dto) throws Exception {
        ServiceData<List<PDATransferScanModuleDto>> ret = new ServiceData<>();
        List<PDATransferScanModuleDto> list = new ArrayList<>();
        //根据“选择指令”查询模组上料信息
        PDATransferScanModuleDto pdaTransferScanModuleDto = new PDATransferScanModuleDto();
        pdaTransferScanModuleDto.setFactoryId(dto.getFactoryId());
        pdaTransferScanModuleDto.setCfgHeaderId(dto.getCfgHeaderId());
        pdaTransferScanModuleDto.setWorkOrder(dto.getWorkOrder());
        pdaTransferScanModuleDto.setLineCode(dto.getLineCode());
        pdaTransferScanModuleDto.setModuleNo(dto.getModuleNo());
        pdaTransferScanModuleDto.setAttr1(Constant.FLAG_N);
        pdaTransferScanModuleDto.setMountType(dto.getMountType());
        if (StringUtils.isNotBlank(dto.getPickStatusString())) {
            pdaTransferScanModuleDto.setPickStatusString(dto.getPickStatusString());
        }
        checkTransferScanModuleDto(pdaTransferScanModuleDto);
        if (StringUtils.isBlank(dto.getModuleNo())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.MODULE_NO_EMPTY);
        }
        List<PDATransferScanModuleDto> moduleList = pdaTransferScanRepository.getModuleBSmtBomDetailList(pdaTransferScanModuleDto);
        if (!CollectionUtils.isEmpty(moduleList)) {
            moduleList.forEach(t -> {
                t.setWorkOrder(dto.getWorkOrder());
                t.setLineCode(dto.getLineCode());
            });
            list.addAll(moduleList);
        }
        List<PDATransferScanModuleDto> otherModuleList = new ArrayList<>();
        //AB同时转机时 查另一面的上料信息
        Boolean mIsBothSides = dto.getmIsBothSides();
        if (mIsBothSides != null && mIsBothSides && StringUtils.isNotBlank(dto.getmOtherSideCfgHeaderId())) {
            otherSideModuleBSmtBomDetailInfo(dto, moduleList, otherModuleList);
        }
        //双轨线
        if (StringUtils.isNotBlank(dto.getRelatedCfgHeaderId()) && StringUtils.isNotBlank(dto.getRelatedWorkOrder()) && StringUtils.isNotBlank(dto.getmRelatedLine())) {
            relatedModuleBSmtBomDetailInfo(dto, moduleList, otherModuleList);
        }
        if (!CollectionUtils.isEmpty(otherModuleList)) {
            list.addAll(otherModuleList);
        }
        //按站位排序
        list = getListSort(list);
        ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        ret.setBo(list);
        if (CollectionUtils.isEmpty(list)) {
            //模组没有接料信息
            ret.getCode().setMsg(CommonUtils.getLmbMessage(MessageId.MODULE_NO_ITEMS));
        } else {
            //判断料盘为空的数据
            List<PDATransferScanModuleDto> emptyMaterialList = list.stream().filter(t -> StringUtils.isBlank(t.getMaterialTray())).collect(Collectors.toList());
            //不存在空数据则比对完成
            if (emptyMaterialList.size() == NumConstant.NUM_ZERO) {
                ret.getCode().setMsg(CommonUtils.getLmbMessage(MessageId.SCAN_COMPARISON_COMPLETE));
            }
        }
        return ret;
    }

    /**
     * 获取AB同时转机时另一面上料信息
     */
    private void otherSideModuleBSmtBomDetailInfo(PDATransferScanCommonDto dto, List<PDATransferScanModuleDto> moduleList, List<PDATransferScanModuleDto> otherModuleList) throws Exception {
        PDATransferScanModuleDto pdaTransferScanModuleDto = new PDATransferScanModuleDto();
        pdaTransferScanModuleDto.setFactoryId(dto.getFactoryId());
        pdaTransferScanModuleDto.setCfgHeaderId(dto.getmOtherSideCfgHeaderId());
        pdaTransferScanModuleDto.setWorkOrder(dto.getmOtherSideWorkOrderNo());
        pdaTransferScanModuleDto.setLineCode(dto.getLineCode());
        pdaTransferScanModuleDto.setAttr1(Constant.STR_AB);
        if (StringUtils.isNotBlank(dto.getModuleNo())) {
            pdaTransferScanModuleDto.setModuleNo(dto.getModuleNo());
        }
        if (StringUtils.isNotBlank(dto.getPickStatusString())) {
            pdaTransferScanModuleDto.setPickStatusString(dto.getPickStatusString());
        }
        //MountType：转机扫描 1; QC转机巡检 3; QC抽检 17
        pdaTransferScanModuleDto.setMountType(dto.getMountType());
        checkTransferScanModuleDto(pdaTransferScanModuleDto);
        List<PDATransferScanModuleDto> list = pdaTransferScanRepository.getModuleBSmtBomDetailList(pdaTransferScanModuleDto);
        if (!CollectionUtils.isEmpty(list)) {
            for (PDATransferScanModuleDto detail : list) {
                String locationNo = detail.getLocationNo();
                String machineNo = detail.getMachineNo();
                List<PDATransferScanModuleDto> moduleDetail = moduleList.stream().filter(t -> locationNo.equals(t.getLocationNo()) && machineNo.equals(t.getMachineNo())).collect(Collectors.toList());
                if (moduleDetail.size() == Constant.INT_0) {
                    detail.setLineCode(dto.getLineCode());
                    detail.setWorkOrder(dto.getmOtherSideWorkOrderNo());
                    otherModuleList.add(detail);
                }
            }
        }
    }

    /**
     * 获取双轨线另一面上料信息
     */
    private void relatedModuleBSmtBomDetailInfo(PDATransferScanCommonDto dto, List<PDATransferScanModuleDto> moduleList, List<PDATransferScanModuleDto> otherModuleList) throws Exception {
        PDATransferScanModuleDto pdaTransferScanModuleDto = new PDATransferScanModuleDto();
        pdaTransferScanModuleDto.setFactoryId(dto.getFactoryId());
        pdaTransferScanModuleDto.setCfgHeaderId(dto.getRelatedCfgHeaderId());
        pdaTransferScanModuleDto.setWorkOrder(dto.getRelatedWorkOrder());
        pdaTransferScanModuleDto.setLineCode(dto.getmRelatedLine());
        if (StringUtils.isNotBlank(dto.getModuleNo())) {
            pdaTransferScanModuleDto.setModuleNo(dto.getModuleNo());
        }
        if (StringUtils.isNotBlank(dto.getPickStatusString())) {
            pdaTransferScanModuleDto.setPickStatusString(dto.getPickStatusString());
        }
        pdaTransferScanModuleDto.setAttr1(Constant.FLAG_N);
        //MountType：转机扫描 1 QC转机巡检 3 生产物料巡检 15 QC抽检 17
        pdaTransferScanModuleDto.setMountType(dto.getMountType());
        checkTransferScanModuleDto(pdaTransferScanModuleDto);
        List<PDATransferScanModuleDto> list = pdaTransferScanRepository.getModuleBSmtBomDetailList(pdaTransferScanModuleDto);
        if (!CollectionUtils.isEmpty(list)) {
            for (PDATransferScanModuleDto detail : list) {
                String locationNo = detail.getLocationNo();
                String machineNo = detail.getMachineNo();
                List<PDATransferScanModuleDto> moduleDetail = moduleList.stream().filter(t -> locationNo.equals(t.getLocationNo()) && machineNo.equals(t.getMachineNo())).collect(Collectors.toList());
                if (moduleDetail.size() == Constant.INT_0) {
                    detail.setOtherLineCode(dto.getmRelatedLine());
                    detail.setOtherWorkOrder(dto.getRelatedWorkOrder());
                    otherModuleList.add(detail);
                } else {
                    moduleDetail.forEach(t -> {
                        t.setOtherWorkOrder(dto.getRelatedWorkOrder());
                        t.setOtherLineCode(dto.getmRelatedLine());
                    });
                }
            }
        }
    }

    /**
     * 查询详细信息-校验基础数据
     */
    private void checkTransferScanModuleDto(PDATransferScanModuleDto dto) throws Exception {
        if (StringUtils.isBlank(dto.getCfgHeaderId())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.CFGHEADERID_IS_EMPTY);
        }
        if (StringUtils.isBlank(dto.getLineCode())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.LINE_CODE_EMPTY);
        }
        if (StringUtils.isBlank(dto.getWorkOrder())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.WORK_ORDER_EMPTY);
        }
        if (StringUtils.isBlank(dto.getMountType())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.MOUNT_TYPE_IS_NULL);
        }
    }

    /**
     * 查询模组数据-校验基础数据
     */
    private void checkTransferScanModuleDto(PDATransferScanCommonDto dto) {
        if (StringUtils.isBlank(dto.getCfgHeaderId())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.CFGHEADERID_IS_EMPTY);
        }
        if (StringUtils.isBlank(dto.getLineCode())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.LINE_CODE_EMPTY);
        }
        if (StringUtils.isBlank(dto.getWorkOrder())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.WORK_ORDER_EMPTY);
        }
        if (StringUtils.isBlank(dto.getSourceTask())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PRODUCT_TASK_IS_NULL);
        }
        if (StringUtils.isBlank(dto.getMountType())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.MOUNT_TYPE_IS_NULL);
        }

    }

    private List<PDATransferScanModuleDto> getListSort(List<PDATransferScanModuleDto> list) {
        list = list.stream().sorted((a, b) -> {
            String[] location1 = a.getLocationNo().split("-");
            String[] location2 = b.getLocationNo().split("-");
            int i1 = 0;
            for (int i = 0; i < location1.length; i++) {
                if (NumberUtils.isNumber(location1[i])) {
                    i1 = Integer.valueOf(location1[i]).compareTo(Integer.valueOf(location2[i]));
                } else {
                    i1 = location1[i].compareTo(location2[i]);
                }
                if (i1 == 0) {
                    continue;
                }
                return i1;
            }
            return i1;
        }).collect(Collectors.toList());
        return list;
    }

    /**
     * 转机扫描/QC转机巡检/QC抽检-查询模组信息
     *
     * @param dto
     * @return
     * @throws Exception
     **/
    @Override
    public List<PDATransferScanCommonDto> getTransferScanModuleSelectData(PDATransferScanCommonDto dto) throws Exception {
        List<PDATransferScanCommonDto> moduleList = new ArrayList<>();
        //校验基础数据
        checkTransferScanModuleDto(dto);
        //校验指令备料或综合备料是否完成
        checkWorkOrderPrepare(dto);
        //校验指令能否转交
        checkWorkOrderTransfer(dto);

        List<String> cfgHeaderIds = new ArrayList<>();
        cfgHeaderIds.add(dto.getCfgHeaderId());
        //AB同时转机,查另一面的模组集合
        if (dto.getmIsBothSides() != null && dto.getmIsBothSides() && StringUtils.isNotBlank(dto.getmOtherSideCfgHeaderId())) {
            cfgHeaderIds.add(dto.getmOtherSideCfgHeaderId());
        }
        //双轨线，查关联指令的模组集合
        else if (StringUtils.isNotBlank(dto.getRelatedCfgHeaderId()) && StringUtils.isNotBlank(dto.getRelatedWorkOrder())) {
            cfgHeaderIds.add(dto.getRelatedCfgHeaderId());
        }

        //查询所有模组
        List<String> moduleNoList = pdaTransferScanRepository.getModuleNoList(cfgHeaderIds);
        if (CollectionUtils.isEmpty(moduleNoList)) {
            return moduleList;
        }

        dto.setCfgHeaderIds(cfgHeaderIds);
        //1、转机扫描-判断模组是否完成转机，并设置Flag
        if (dto.getMountType().equals(MpConstant.STRING_ONE)) {
            moduleList = getModuleForTransferScan(moduleNoList, dto);
        }
        //2、QC转机巡检/QC抽检/生产物料巡检 QC抽检不需要关联查询扫描历史,实际QC抽检mountType为7，但是传了17
        if (dto.getMountType().equals(MpConstant.STRING_THREE) || dto.getMountType().equals(MpConstant.STRING_SEVENTEEN) || dto.getMountType().equals(MpConstant.STRING_FIFTEEN)) {
            moduleList = getModuleForQCTransferInspection(moduleNoList, dto);
        }

        //排序
        moduleList = moduleList.stream()
                .sorted(Comparator.comparing(PDATransferScanCommonDto::getModuleNo))
                .collect(Collectors.toList());
        return moduleList;
    }

    /*
     * QC转机巡检/QC抽检
     * */
    private List<PDATransferScanCommonDto> getModuleForQCTransferInspection(List<String> moduleNoList, PDATransferScanCommonDto dto) throws Exception {
        List<PDATransferScanCommonDto> moduleList = new ArrayList<>();
        List<PDATransferScanModuleDto> list = new ArrayList<>();
        //根据“选择指令”查询模组上料信息
        PDATransferScanModuleDto pdaTransferScanModuleDto = new PDATransferScanModuleDto();
        pdaTransferScanModuleDto.setFactoryId(dto.getFactoryId());
        pdaTransferScanModuleDto.setCfgHeaderId(dto.getCfgHeaderId());
        pdaTransferScanModuleDto.setWorkOrder(dto.getWorkOrder());
        pdaTransferScanModuleDto.setLineCode(dto.getLineCode());
        pdaTransferScanModuleDto.setAttr1(Constant.FLAG_N);
        pdaTransferScanModuleDto.setMountType(dto.getMountType());
        if (StringUtils.isNotBlank(dto.getPickStatusString())) {
            pdaTransferScanModuleDto.setPickStatusString(dto.getPickStatusString());
        }
        List<PDATransferScanModuleDto> detailList = pdaTransferScanRepository.getModuleBSmtBomDetailList(pdaTransferScanModuleDto);
        if (!CollectionUtils.isEmpty(detailList)) {
            detailList.forEach(t -> {
                t.setWorkOrder(dto.getWorkOrder());
                t.setLineCode(dto.getLineCode());
            });
            list.addAll(detailList);
        }
        List<PDATransferScanModuleDto> otherDetailList = new ArrayList<>();
        //AB同时转机时 查另一面的上料信息
        Boolean mIsBothSides = dto.getmIsBothSides();
        if (mIsBothSides != null && mIsBothSides && StringUtils.isNotBlank(dto.getmOtherSideCfgHeaderId())) {
            otherSideModuleBSmtBomDetailInfo(dto, detailList, otherDetailList);
        }
        //双轨线
        if (StringUtils.isNotBlank(dto.getRelatedCfgHeaderId()) && StringUtils.isNotBlank(dto.getRelatedWorkOrder()) && StringUtils.isNotBlank(dto.getmRelatedLine())) {
            relatedModuleBSmtBomDetailInfo(dto, detailList, otherDetailList);
        }
        if (!CollectionUtils.isEmpty(otherDetailList)) {
            list.addAll(otherDetailList);
        }
        //校验模组是否完成转机巡检/QC抽检
        for (String moduleNo : moduleNoList) {
            PDATransferScanCommonDto moduleDto = new PDATransferScanCommonDto();
            int count = (int) list.stream()
                    .filter(one -> Objects.equals(one.getModuleNo(), moduleNo) && StringUtils.isBlank(one.getMaterialTray()))
                    .count();
            //存在料盘为空则未完成巡检
            if (count > 0) {
                moduleDto.setCompletedFlag(false);
            } else {
                moduleDto.setCompletedFlag(true);
            }
            moduleDto.setModuleNo(moduleNo);
            moduleList.add(moduleDto);
        }

        return moduleList;
    }

    /*
     * 转机扫描校验模组是否完成转机，并返回模组
     * */
    private List<PDATransferScanCommonDto> getModuleForTransferScan(List<String> moduleNoList, PDATransferScanCommonDto dto) {
        //查询未完成转机（料盘为空）的模组
        List<String> emptyMaterialModuleList = getEmptyMaterialModuleList(dto);
        List<PDATransferScanCommonDto> moduleList = new ArrayList<>();
        //校验模组是否完成转机
        for (String moduleNo : moduleNoList) {
            PDATransferScanCommonDto moduleDto = new PDATransferScanCommonDto();
            if (emptyMaterialModuleList.contains(moduleNo)) {
                moduleDto.setCompletedFlag(false);
            } else {
                moduleDto.setCompletedFlag(true);
            }
            moduleDto.setModuleNo(moduleNo);
            moduleList.add(moduleDto);
        }
        return moduleList;
    }


    /*
     * 获取所有模组上料详细信息
     * */
    private List<String> getEmptyMaterialModuleList(PDATransferScanCommonDto dto) {
        PDATransferScanCommonDto commonDto = new PDATransferScanCommonDto();
        List<String> lineCodeSet = new ArrayList<>();
        lineCodeSet.add(dto.getLineCode());
        commonDto.setCfgHeaderIds(dto.getCfgHeaderIds());
        commonDto.setLineCodeSet(lineCodeSet);
        //双轨线且指令2不为空,设置关联线体
        if (StringUtils.isNotBlank(dto.getRelatedCfgHeaderId()) && StringUtils.isNotBlank(dto.getmRelatedLine()) && StringUtils.isNotBlank(dto.getRelatedWorkOrder())) {
            lineCodeSet.add(dto.getmRelatedLine());
            commonDto.setLineCodeSet(lineCodeSet);
        }
        List<String> emptyMaterialModuleList = pdaTransferScanRepository.getEmptyMaterialModuleList(commonDto);
        if (CollectionUtils.isEmpty(emptyMaterialModuleList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.MODULE_DETAIL_NOT_FOUND);
        }
        return emptyMaterialModuleList;
    }


    /*
     * 校验指令备料或综合备料是否完成
     **/
    private void checkWorkOrderPrepare(PDATransferScanCommonDto dto) throws Exception {
        String checkCraftSection = "";
        String craftSection = dto.getCraftSection();
        if (craftSection.equals(Constant.CRAFTSECTION_SMT_A)) {
            checkCraftSection = Constant.CRAFTSECTION_SMT_B;
        }
        if (craftSection.equals(Constant.CRAFTSECTION_SMT_B)) {
            checkCraftSection = Constant.CRAFTSECTION_SMT_A;
        }
        //首指令时无需校验，非首指令需校验上个指令是否开工
        if (!StringUtils.equals(dto.getRemark(), NumConstant.STR_ZERO)
                && (craftSection.equals(Constant.CRAFTSECTION_SMT_A) || craftSection.equals(Constant.CRAFTSECTION_SMT_B))) {
            Map<String, Object> map = new HashMap<>();
            map.put("sourceTask", dto.getSourceTask());
            map.put("craftSection", checkCraftSection);
            List<PsWorkOrderDTO> workOrderList = getWorkOrderInfo(map);
            //指令为空不继续校验
            if (CollectionUtils.isEmpty(workOrderList)) {
                return;
            }
            //筛选出remark=0且状态为拟制中或已提交的指令，如存在则提示"上个指令未开工，不允许转机"
            List<String> collect = workOrderList.stream().filter(psWorkOrderDTO -> !StringUtils.isBlank(psWorkOrderDTO.getRemark()) &&
                            StringUtils.equals(psWorkOrderDTO.getRemark(), NumConstant.STR_ZERO) &&
                            (StringUtils.equals(psWorkOrderDTO.getWorkOrderStatus(), Constant.IS_SUBMITTED) ||
                                    StringUtils.equals(psWorkOrderDTO.getWorkOrderStatus(), Constant.IS_DRAW_UP)))
                    .map(PsWorkOrderDTO::getWorkOrderNo).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(collect)) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.NOT_ALLOWED_TRANSFER);
            }
        }
    }

    /*
     * 校验指令能否转交
     * */
    public void checkWorkOrderTransfer(PDATransferScanCommonDto dto) throws Exception {
        String workOrderNos = dto.getWorkOrder();
        if (StringUtils.isNotBlank(dto.getmOtherSideWorkOrderNo())) {
            workOrderNos += ',';
            workOrderNos += dto.getmOtherSideWorkOrderNo();
        }
        if (StringUtils.isNotBlank(dto.getRelatedWorkOrder())) {
            workOrderNos += ',';
            workOrderNos += dto.getRelatedWorkOrder();
        }
        RetCode retCode = smtMachineMTLHistoryHService.checkWorkOrderCanDeliver(workOrderNos);
        if (!StringUtils.equals(retCode.getCode(), RetCode.SUCCESS_CODE)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, retCode.getMsg());
        }
    }

    /**
     * 一键切换-扫描站位
     *
     * @param locationSn
     * @return
     **/
    @Override
    public OneKeySwitchScanLocationDto scanLocationSnOneKeySwitch(String locationSn, String factoryId) {
        if (StringUtils.isEmpty(locationSn)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.LOCATION_SN_ERROR);
        }
        OneKeySwitchMoutingDTO oneKeySwitchMoutingDTO = smtMachineMaterialMoutingService.checkSmtLocationLineInfo(locationSn);
        if (ObjectUtils.isEmpty(oneKeySwitchMoutingDTO)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.LOCATION_NOT_EXIST);
        }
        String lineCode = oneKeySwitchMoutingDTO.getLineCode();
        List<WorkorderOnline> workOrderByLineCode = workorderOnlineRepository.getWorkOrderByLineCode(lineCode);
        //开工指令数量不能大于2
        if (!CollectionUtils.isEmpty(workOrderByLineCode) && workOrderByLineCode.size() > MpConstant.QTY_TWO) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.LINE_NOT_ALLOW_MORE_THAN_TWO_WORK_ORDER);
        }
        Map<String, Object> map = new HashMap<>();
        map.put("inWorkOrderStatus", MpConstant.WOKRORDER_STATUS_TWO);
        map.put("lineCode", lineCode);
        map.put("factoryId", factoryId);
        map.put("sort", "scheduleStartDate");
        List<PsEntityPlanBasic> psEntityPlanBasicList = PlanscheduleRemoteService.getPsEntityPlanBasicList(map);
        List<String> workOrderlist = new ArrayList<>();
        List<PsEntityPlanBasic> basicWorkOrderlist = new ArrayList<>();
        //筛选开工的指令
        if (!CollectionUtils.isEmpty(workOrderByLineCode)) {
            workOrderlist = workOrderByLineCode.stream()
                    .map(item -> item.getWorkOrder())
                    .collect(Collectors.toList());
        }
        //筛选已提交、挂起指令的指令号、批次
        if (!CollectionUtils.isEmpty(psEntityPlanBasicList)) {
            basicWorkOrderlist = psEntityPlanBasicList.stream()
                    .map(item -> {
                        PsEntityPlanBasic psEntityPlanBasic = new PsEntityPlanBasic();
                        psEntityPlanBasic.setWorkOrderNo(item.getWorkOrderNo());
                        psEntityPlanBasic.setSourceTask(item.getSourceTask());
                        return psEntityPlanBasic;
                    })
                    .collect(Collectors.toList());
        }
        OneKeySwitchScanLocationDto oneKeySwitchScanLocationDto = new OneKeySwitchScanLocationDto(workOrderlist, basicWorkOrderlist,
                lineCode, oneKeySwitchMoutingDTO.getLineName(), oneKeySwitchMoutingDTO.getModuleNo());
        return oneKeySwitchScanLocationDto;

    }


    /*
     *  reelid拆分-扫描旧reelid
     * @param reelid
     * @return qty
     * */
    @Override
    public int scanOldReelIdForReelIdSplit(String pkCode, String factoryId) throws Exception {
        List<ContainerContentInfoDTO> containerContentInfoList = ProductionDeliveryRemoteService.getContainerContentInfoList(pkCode, factoryId);
        if (!CollectionUtils.isEmpty(containerContentInfoList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.REEL_ID_EXIST_IN_CONTAINER_NOT_ALLOW_SPLIT,
                    new String[]{containerContentInfoList.get(0).getLpn()});
        }
        PkCodeInfoDTO pkCodeInfoDTO = new PkCodeInfoDTO();
        pkCodeInfoDTO.setPkCode(pkCode);
        //查询pkCode信息
        List<PkCodeInfo> pkCodeInfoList = pkCodeInfoService.getList(pkCodeInfoDTO);
        if (CollectionUtils.isEmpty(pkCodeInfoList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.REELID_NOT_EXISTS);
        }
        int itemQty = pkCodeInfoList.get(0).getItemQty().intValue();

        //查询是否开启feeder
        SysLookupTypesDTO sysLookupValuesDTO = BasicsettingRemoteService.getSysLookUpValue(Constant.LookUpKey.LOOK_1032, Constant.LOOKUP_VALUE_10320001);
        if (sysLookupValuesDTO != null && sysLookupValuesDTO.getLookupMeaning().equals(Constant.FLAG_Y)) {
            //查询SMT在用表
            SmtMachineMaterialMouting mouting = getSmtMachineMaterialMouting(factoryId, pkCode);
            EmEqpPdcountDTO eqpPdcountDto = new EmEqpPdcountDTO();
            eqpPdcountDto.setLineCode(mouting.getLineCode());
            eqpPdcountDto.setMaterialCode(mouting.getItemCode());
            eqpPdcountDto.setFullStationPosition(mouting.getLocationNo());
            //查询抛料数据
            List<EmEqpPdcountDTO> inforOfPdcount = EqpmgmtsRemoteService.getInforOfPdcount(eqpPdcountDto);
            BigDecimal sumThrowsNumber = BigDecimal.valueOf(0);
            if (!CollectionUtils.isEmpty(inforOfPdcount)) {
                sumThrowsNumber = inforOfPdcount.stream()
                        .map(EmEqpPdcountDTO::getThrowsNumber)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
            }
            itemQty = itemQty - sumThrowsNumber.intValue();
        }
        itemQty = Math.max(itemQty, 0);
        return itemQty;
    }

    @Override
    public void scanNewQtyForReelIdSplit(PDAReelIdSplitDto dto) {
        String oldReelId = dto.getOldReelId();
        //查询旧reelid信息
        PkCodeInfoDTO pkCodeInfoDTO = new PkCodeInfoDTO();
        pkCodeInfoDTO.setPkCode(oldReelId);
        List<PkCodeInfo> pkCodeInfoList = pkCodeInfoService.getList(pkCodeInfoDTO);
        if (CollectionUtils.isEmpty(pkCodeInfoList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.REELID_NOT_EXISTS);
        }
        PkCodeInfo oldPkCodeInfo = pkCodeInfoList.get(Constant.INT_0);
        BigDecimal originQty = new BigDecimal(oldPkCodeInfo.getItemQty().toString());
        BigDecimal newQty = new BigDecimal(dto.getNewQty());
        //旧料盘现在的数量=原始数量-拆分出去的数量
        BigDecimal resultQty = originQty.subtract(newQty);
        SplitReelIdDTO splitReelIdDTO = new SplitReelIdDTO();
        //拆分时，oldQty更改为旧料盘的原始数量-新数量，不减抛料数量，前端展示数量为原始数量-抛料数量
        splitReelIdDTO.setOldPkInfo(setPkCodeInfoDTO(oldPkCodeInfo, dto, oldReelId, resultQty));
        splitReelIdDTO.setNewPkInfo(setPkCodeInfoDTO(oldPkCodeInfo, dto, dto.getNewReelId(), newQty));
        //中心工厂reelid拆分
        centerfactoryRemoteService.splitReelId(splitReelIdDTO);
        //记录拆分后新reelid轨迹
        insertPkcodeHistoryForPda(oldPkCodeInfo, dto);
    }

    private SmtMachineMaterialMouting getSmtMachineMaterialMouting(String factoryId, String pkCode) {
        SmtMachineMaterialMouting smtMachineMaterialMouting = new SmtMachineMaterialMouting();
        smtMachineMaterialMouting.setFactoryId(new BigDecimal(factoryId));
        smtMachineMaterialMouting.setObjectId(pkCode);
        List<SmtMachineMaterialMouting> smtMachineMaterialMoutings = smtMachineMaterialMoutingService.selectSmtMachineMaterialMoutingSelective(smtMachineMaterialMouting);
        if (CollectionUtils.isEmpty(smtMachineMaterialMoutings)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.MOUNTING_DATA_IS_NULL, new Object[]{pkCode});
        }
        SmtMachineMaterialMouting mouting = smtMachineMaterialMoutings.get(Constant.INT_0);
        mouting.setFactoryId(new BigDecimal(factoryId));
        if (StringUtils.isBlank(mouting.getLineCode()) || StringUtils.isBlank(mouting.getLocationNo()) || StringUtils.isBlank(mouting.getItemCode())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SMT_DATA_LINE_LOCATION_ITEM_CODE_IS_NULL);
        }
        return mouting;
    }

    private PkCodeInfoDTO setPkCodeInfoDTO(PkCodeInfo dto, PDAReelIdSplitDto pdaReelIdSplitDto, String reelId, BigDecimal qty) {
        PkCodeInfoDTO pkCodeInfoDTO = new PkCodeInfoDTO();
        pkCodeInfoDTO.setFactoryId(new BigDecimal(pdaReelIdSplitDto.getFactoryId()));
        pkCodeInfoDTO.setPkCode(reelId);
        pkCodeInfoDTO.setItemCode(dto.getItemCode());
        pkCodeInfoDTO.setItemVersion(dto.getItemVersion());
        pkCodeInfoDTO.setItemQty(qty);
        pkCodeInfoDTO.setRawQty(qty);
        pkCodeInfoDTO.setDatecode(dto.getDatecode());
        pkCodeInfoDTO.setLotcode(dto.getLotcode());
        pkCodeInfoDTO.setSupplerCode(dto.getSupplerCode());
        pkCodeInfoDTO.setSourceSys(MpConstant.STRING_TWO);
        pkCodeInfoDTO.setSysLotCode(dto.getSysLotCode());
        pkCodeInfoDTO.setAttribute1(dto.getAttribute1());
        pkCodeInfoDTO.setAttribute2(dto.getAttribute2());
        pkCodeInfoDTO.setAttribute3(MpConstant.STRING_ONE);
        pkCodeInfoDTO.setRemark(dto.getRemark());
        pkCodeInfoDTO.setIsSeparate(dto.getIsSeparate());
        pkCodeInfoDTO.setIsLead(dto.getIsLead());
        pkCodeInfoDTO.setProductTask(dto.getProductTask());
        pkCodeInfoDTO.setCreateBy(pdaReelIdSplitDto.getLoginUser());
        pkCodeInfoDTO.setLastUpdatedBy(pdaReelIdSplitDto.getLoginUser());
        if (StringUtils.isNotEmpty(dto.getItemName())) {
            pkCodeInfoDTO.setItemName(dto.getItemName());
        }
        return pkCodeInfoDTO;
    }

    //记录reelid轨迹
    private void insertPkcodeHistoryForPda(PkCodeInfo pkCodeInfo, PDAReelIdSplitDto pdaReelIdSplitDto) {
        PkCodeHistory pkCodeHistoryParams = new PkCodeHistory();
        pkCodeHistoryParams.setHistoryId(UUID.randomUUID().toString());
        pkCodeHistoryParams.setCreateBy(pdaReelIdSplitDto.getLoginUser());
        pkCodeHistoryParams.setFactoryId(pkCodeInfo.getFactoryId());
        pkCodeHistoryParams.setLastUpdatedBy(pdaReelIdSplitDto.getLoginUser());
        pkCodeHistoryParams.setObjectId(pdaReelIdSplitDto.getNewReelId());
        pkCodeHistoryParams.setObjectType(MpConstant.REEL_ID);
        pkCodeHistoryParams.setItemCode(pkCodeInfo.getItemCode());
        pkCodeHistoryParams.setProgramName(MpConstant.REEL_ID_SPLIT);
        pkCodeHistoryParams.setCurrentQty(new BigDecimal(pdaReelIdSplitDto.getNewQty()));
        pkCodeHistoryParams.setSourceTask(pkCodeInfo.getProductTask());
        pkCodeHistoryParams.setSourceBatchCode(pkCodeInfo.getSourceBatchCode());
        pkCodeHistoryService.insertPkCodeHistory(pkCodeHistoryParams);
    }

    public PDARcvOldPkCodeDto rcvScanOldPkCode(String oldPkCode, String newPkCode, String productTask, String factoryId, String empNo) throws Exception {
        if (StringUtils.isBlank(oldPkCode)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.OLD_AND_NEW_PK_CODE_CAN_NOT_BE_NULL);
        }
        if (StringUtils.isBlank(newPkCode)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.OLD_AND_NEW_PK_CODE_CAN_NOT_BE_NULL);
        }
        if (StringUtils.isBlank(productTask)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PRODPLANID_ID_NULL);
        }
        PDARcvOldPkCodeDto pdaRcvOldPkCodeDto = new PDARcvOldPkCodeDto();
        //校验新旧料盘物料代码是否一致
        PDAReceiveCheckItemDTO pdaReceiveCheckItemDTO = smtMachineMaterialMoutingService.pdaReceiveCheckItem(oldPkCode, newPkCode);
        if (pdaReceiveCheckItemDTO != null) {
            if (StringUtils.isNotBlank(pdaReceiveCheckItemDTO.getAttribute1())) {
                CFLine cfLine = ObtainRemoteServiceDataUtil.getLineCodeInfo(pdaReceiveCheckItemDTO.getLineCode());
                pdaRcvOldPkCodeDto.setLineName(cfLine.getLineName());
            }
            pdaRcvOldPkCodeDto.setPdaReceiveCheckItemDTO(pdaReceiveCheckItemDTO);
            return pdaRcvOldPkCodeDto;
        }

        //扫描旧料盘的校验，并获取机台在用数据
        List<SmtMachineMaterialMouting> smtMachineMaterialMoutings = smtMachineMaterialMoutingService.rcvScanOldPkCode(oldPkCode, factoryId);
        if (CollectionUtils.isEmpty(smtMachineMaterialMoutings)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PKCODE_NOT_USED_ERROR);
        }
        SmtMachineMaterialMouting mouting = smtMachineMaterialMoutings.get(NumConstant.NUM_ZERO);
        String oldProductTask = mouting.getSourceTask();
        if (!oldProductTask.equals(productTask)) {
            //校验5G城堡单板子卡ReelID批次校验和跨批次挪料
            Boolean result = pkCodeCastleOrAppropriation(productTask, oldProductTask, mouting.getItemCode(), mouting.getLineCode(), empNo);
            if (!result) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.OLD_AND_NEW_PKCODE_BATCH_INCONSISTENT);
            }
        }

        //根据旧料盘的机台在用信息获取上料详表料架信息
        BSmtBomDetailDTO bSmtBomDetailDTO = new BSmtBomDetailDTO();
        bSmtBomDetailDTO.setItemCode(mouting.getItemCode());
        bSmtBomDetailDTO.setAttr1(mouting.getSourceTask());
        bSmtBomDetailDTO.setModuleNo(mouting.getModuleNo());
        bSmtBomDetailDTO.setMachineNo(mouting.getMachineNo());
        bSmtBomDetailDTO.setLocationNo(mouting.getLocationNo());
        bSmtBomDetailDTO.setLineCode(mouting.getLineCode());
        String materialRackStr = bSmtBomDetailRepository.getBomDetailByMouting(bSmtBomDetailDTO);
        //查询旧料盘线体名称，与设备对接时使用
        CFLine cfLine = ObtainRemoteServiceDataUtil.getLineCodeInfo(mouting.getLineCode());

        pdaRcvOldPkCodeDto.setLineName(cfLine.getLineName());
        pdaRcvOldPkCodeDto.setMaterialRackStr(materialRackStr);
        pdaRcvOldPkCodeDto.setSmtMachineMaterialMoutingList(smtMachineMaterialMoutings);
        return pdaRcvOldPkCodeDto;
    }


    public PDAQCSpotCheckPkCodeDTO checkPkCodeQCSpotCheck(PDAQCSpotCheckDTO dto) throws Exception {
        List<SmtLocationInfoDTO> listSmtLocation = BasicsettingRemoteService.getListSmtLocation(dto.getLineCode(), dto.getLocationNo(), dto.getModuleNo());
        if (CollectionUtils.isEmpty(listSmtLocation)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.LOCATION_NOT_EXIST);
        }
        SmtLocationInfoDTO smtLocationInfoDTO = listSmtLocation.get(MpConstant.NUM_0);
        if (StringUtils.isBlank(smtLocationInfoDTO.getLocationSn()) || StringUtils.isBlank(smtLocationInfoDTO.getToSupplierMachine())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.LOCATION_NOT_EXIST);
        }

        Map<String, Object> params = new HashMap<>();
        params.put("workOrderNo", dto.getWorkOrderNo());
        PDAQCSpotCheckPkCodeDTO checkCfgIdChangedDto = checkCfgIdChanged(params, dto.getCfgHeaderId());
        if (StringUtils.isNotBlank(dto.getWorkOrderNo2()) && StringUtils.isNotBlank(dto.getCfgHeaderId2())) {
            params.put("workOrderNo", dto.getWorkOrderNo2());
            checkCfgIdChangedDto = checkCfgIdChanged(params, dto.getCfgHeaderId2());
        }
        return new PDAQCSpotCheckPkCodeDTO(smtLocationInfoDTO, checkCfgIdChangedDto.getCfgIdChangedFlag(), checkCfgIdChangedDto.getMsg());
    }

    private PDAQCSpotCheckPkCodeDTO checkCfgIdChanged(Map<String, Object> map, String cfgHeaderIdCheck) throws Exception {
        List<PsWorkOrderDTO> basicWorkOrder = PlanscheduleRemoteService.getBasicWorkOrder(map);
        if (CollectionUtils.isEmpty(basicWorkOrder)) {
            return new PDAQCSpotCheckPkCodeDTO(true, CommonUtils.getLmbMessage(MessageId.WORKORDER_NOT_FIND));
        }
        PsWorkOrderDTO psWorkOrderDTO = basicWorkOrder.get(MpConstant.NUM_0);
        if (StringUtils.isBlank(psWorkOrderDTO.getCfgHeaderId())) {
            return new PDAQCSpotCheckPkCodeDTO(true, CommonUtils.getLmbMessage(MessageId.WORK_ORDER_NO_CFG_ID));
        }
        if (!cfgHeaderIdCheck.equals(psWorkOrderDTO.getCfgHeaderId())) {
            return new PDAQCSpotCheckPkCodeDTO(true, CommonUtils.getLmbMessage(MessageId.WORK_ORDER_CFG_ID_CHANGED));
        }
        return new PDAQCSpotCheckPkCodeDTO(false, "");
    }

    public SmtLocationInfo scanLocationSnRcvScan(String locationSn, String pkCode) {
        //查询站位信息
        SmtLocationInfoDTO smtLocationInfo = BasicsettingRemoteService.getSmtLocationInfoByLocationSn(locationSn);
        if (smtLocationInfo == null) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.LOCATION_NOT_EXIST);
        }
        //查询机台在用信息
        SmtMachineMaterialMouting smtMachineMaterialMouting = new SmtMachineMaterialMouting();
        smtMachineMaterialMouting.setObjectId(pkCode);
        List<SmtMachineMaterialMouting> smtMachineMaterialMoutings = smtMachineMaterialMoutingService.selectSmtMachineMaterialMoutingSelective(smtMachineMaterialMouting);
        if (CollectionUtils.isEmpty(smtMachineMaterialMoutings)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.MOUNTING_DATA_IS_NULL);
        }
        SmtMachineMaterialMouting mouting = smtMachineMaterialMoutings.get(Constant.INT_0);
        // 获取站位信息
        SmtLocationInfo locationInfo = BasicsettingRemoteService.asmInfoQuery(smtLocationInfo.getLineCode(), smtLocationInfo.getModuleNo(),
                smtLocationInfo.getMachineNo(), smtLocationInfo.getLocationNo());
        if (locationInfo == null) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.LOCATION_NOT_EXIST);
        }
        if (!StringUtils.equals(smtLocationInfo.getLocationNo(), mouting.getLocationNo()) ||
                !StringUtils.equals(smtLocationInfo.getMachineNo(), mouting.getMachineNo()) ||
                !StringUtils.equals(smtLocationInfo.getModuleNo(), mouting.getModuleNo())) {
            return locationInfo;
        }
        return null;
    }

    @Override
    public ServiceData<List<PDATransferScanModuleDto>> getModuleBSmtBomDetailInfoForQCTransfer(PDATransferScanCommonDto dto) throws Exception {
        ServiceData<List<PDATransferScanModuleDto>> ret = new ServiceData<>();
        List<PDATransferScanModuleDto> list = new ArrayList<>();
        PDATransferScanModuleDto pdaTransferScanModuleDto = getTransferScanModuleDto(dto);
        checkTransferScanModuleDto(pdaTransferScanModuleDto);
        if (StringUtils.isBlank(dto.getModuleNo())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.MODULE_NO_EMPTY);
        }
        List<PDATransferScanModuleDto> moduleList = pdaTransferScanRepository.getModuleBSmtBomDetailList(pdaTransferScanModuleDto);
        if (!CollectionUtils.isEmpty(moduleList)) {
            moduleList.forEach(t -> {
                t.setWorkOrder(dto.getWorkOrder());
                t.setLineCode(dto.getLineCode());
            });
            list.addAll(moduleList);
        }
        List<PDATransferScanModuleDto> otherModuleList = new ArrayList<>();
        //AB同时转机时 查另一面的上料信息
        Boolean mIsBothSides = dto.getmIsBothSides();
        if (mIsBothSides != null && mIsBothSides && StringUtils.isNotBlank(dto.getmOtherSideCfgHeaderId())) {
            otherSideModuleBSmtBomDetailInfo(dto, moduleList, otherModuleList);
            //校验AB同时转机是否存在某个模组未完成转机
            if (checkABScanComplete(dto, moduleList, otherModuleList)){
                list.clear();
                list.addAll(moduleList);
            }
        }
        //双轨线
        if (StringUtils.isNotBlank(dto.getRelatedCfgHeaderId()) && StringUtils.isNotBlank(dto.getRelatedWorkOrder()) && StringUtils.isNotBlank(dto.getmRelatedLine())) {
            relatedModuleBSmtBomDetailInfo(dto, moduleList, otherModuleList);
            //校验双轨线是否存在某个模组未完成转机
            if (checkRelateScanComplete(dto, moduleList, otherModuleList)){
                list.clear();
                list.addAll(moduleList);
            }
        }
        if (!CollectionUtils.isEmpty(otherModuleList)) {
            list.addAll(otherModuleList);
        }
        //按站位排序
        list = getListSort(list);
        ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        ret.setBo(list);
        if (CollectionUtils.isEmpty(list)) {
            //模组没有接料信息
            ret.getCode().setMsg(CommonUtils.getLmbMessage(MessageId.MODULE_NO_ITEMS));
        } else {
            //判断料盘为空的数据
            List<PDATransferScanModuleDto> emptyMaterialList = list.stream().filter(t -> StringUtils.isBlank(t.getMaterialTray())).collect(Collectors.toList());
            //不存在空数据则比对完成
            if (emptyMaterialList.size() == NumConstant.NUM_ZERO) {
                ret.getCode().setMsg(CommonUtils.getLmbMessage(MessageId.SCAN_COMPARISON_COMPLETE));
            }
        }
        return ret;
    }

    private static PDATransferScanModuleDto getTransferScanModuleDto(PDATransferScanCommonDto dto) {
        PDATransferScanModuleDto pdaTransferScanModuleDto = new PDATransferScanModuleDto();
        pdaTransferScanModuleDto.setFactoryId(dto.getFactoryId());
        pdaTransferScanModuleDto.setCfgHeaderId(dto.getCfgHeaderId());
        pdaTransferScanModuleDto.setWorkOrder(dto.getWorkOrder());
        pdaTransferScanModuleDto.setLineCode(dto.getLineCode());
        pdaTransferScanModuleDto.setModuleNo(dto.getModuleNo());
        pdaTransferScanModuleDto.setAttr1(Constant.FLAG_N);
        pdaTransferScanModuleDto.setMountType(dto.getMountType());
        pdaTransferScanModuleDto.setPickStatusString(dto.getPickStatusString());
        return pdaTransferScanModuleDto;
    }

    /**
     * 更新 moduleList
     */
    private void updateModuleList(List<PDATransferScanModuleDto> moduleList, List<PDATransferScanModuleDto> newList) {
        moduleList.clear();
        if (!CollectionUtils.isEmpty(newList)){
            moduleList.addAll(newList);
        }
    }

    /*
     * 获取对比完成的上料历史记录
     * */
    private List<PDATransferScanModuleDto> getPickStatus2DetailInfo(PDATransferScanCommonDto dto,String attr1){
        //根据“选择指令”查询模组上料信息
        PDATransferScanModuleDto pdaTransferScanModuleDto = new PDATransferScanModuleDto();
        pdaTransferScanModuleDto.setFactoryId(dto.getFactoryId());
        pdaTransferScanModuleDto.setCfgHeaderId(dto.getCfgHeaderId());
        pdaTransferScanModuleDto.setWorkOrder(dto.getWorkOrder());
        pdaTransferScanModuleDto.setLineCode(dto.getLineCode());
        pdaTransferScanModuleDto.setAttr1(attr1);
        pdaTransferScanModuleDto.setPickStatusString(Constant.STR_2);
        pdaTransferScanModuleDto.setMountType(dto.getMountType());
        pdaTransferScanModuleDto.setModuleNo(dto.getModuleNo());

        List<PDATransferScanModuleDto> moduleBSmtBomDetailList = pdaTransferScanRepository.getModuleBSmtBomDetailList(pdaTransferScanModuleDto);
        if (CollectionUtils.isEmpty(moduleBSmtBomDetailList)) {
            return moduleBSmtBomDetailList;
        }
        moduleBSmtBomDetailList.forEach(t -> {
            t.setWorkOrder(dto.getWorkOrder());
            t.setLineCode(dto.getLineCode());
        });
        return moduleBSmtBomDetailList;
    }

    /*
     * 校验双轨线是否都完成QC转机巡检、生产物料巡检
     * */
    private boolean checkRelateScanComplete(PDATransferScanCommonDto dto,List<PDATransferScanModuleDto> moduleList,List<PDATransferScanModuleDto> otherModuleList) throws Exception {
        if (CollectionUtils.isEmpty(moduleList)){
            return false;
        }
        if ((!dto.getMountType().equals(Constant.STR_3) && !dto.getMountType().equals(Constant.STR_15))) {
            return false;
        }

        Map<String, Object> map = new HashMap<>();
        map.put("mountType",dto.getMountType());
        map.put("lineCode",dto.getLineCode());
        map.put("workOrder",dto.getWorkOrder());
        map.put("enabledFlag",Constant.FLAG_Y);
        map.put("pickStatus",Constant.STR_2);
        //分别查询对比完成的记录
        long headCount = smtMachineMTLHistoryHService.getCount(map);
        map.put("lineCode",dto.getmRelatedLine());
        map.put("workOrder", dto.getRelatedWorkOrder());
        long otherHeadCount = smtMachineMTLHistoryHService.getCount(map);

        if (headCount > otherHeadCount) {
            updateModuleList(moduleList, getPickStatus2DetailInfo(dto, Constant.FLAG_N));
            return true;
        }
        if (otherHeadCount > headCount) {
            dto.setLineCode(dto.getmRelatedLine());
            dto.setWorkOrder(dto.getRelatedWorkOrder());
            dto.setCfgHeaderId(dto.getRelatedCfgHeaderId());
            updateModuleList(otherModuleList, getPickStatus2DetailInfo(dto, Constant.FLAG_N));
            return true;
        }
        return false;
    }

    /*
     * 校验AB面是否都完成QC转机巡检、生产物料巡检
     * */
    private boolean checkABScanComplete(PDATransferScanCommonDto dto,List<PDATransferScanModuleDto> moduleList,List<PDATransferScanModuleDto> otherModuleList) throws Exception {
        if (CollectionUtils.isEmpty(moduleList)){
            return false;
        }
        if ((!dto.getMountType().equals(Constant.STR_3) && !dto.getMountType().equals(Constant.STR_15))) {
            return false;
        }
        Map<String, Object> map = new HashMap<>();
        map.put("mountType",dto.getMountType());
        map.put("lineCode",dto.getLineCode());
        map.put("workOrder",dto.getWorkOrder());
        map.put("enabledFlag",Constant.FLAG_Y);
        map.put("pickStatus",Constant.STR_2);
        //分别查询对比完成的记录
        long headCount = smtMachineMTLHistoryHService.getCount(map);
        map.put("workOrder", dto.getmOtherSideWorkOrderNo());
        long otherHeadCount = smtMachineMTLHistoryHService.getCount(map);

        //第一面扫描完成，查询扫描完成的记录
        if (headCount > otherHeadCount) {
            updateModuleList(moduleList, getPickStatus2DetailInfo(dto, Constant.FLAG_N));
            return true;
        }
        //第二面扫描完成，查询扫描完成的记录
        if (otherHeadCount > headCount) {
            dto.setLineCode(dto.getmRelatedLine());
            dto.setWorkOrder(dto.getmOtherSideWorkOrderNo());
            dto.setCfgHeaderId(dto.getmOtherSideCfgHeaderId());
            updateModuleList(otherModuleList, getPickStatus2DetailInfo(dto, Constant.STR_AB));
            return true;
        }
        return false;
    }


    public void updateReelIdQty(PDAReelIdQtyModifyDto dto) throws Exception {
        PkCodeInfo pkCodeInfo = new PkCodeInfo();
        pkCodeInfo.setPkCode(dto.getReelId());
        pkCodeInfo.setItemQty(dto.getNewQty());
        pkCodeInfo.setRawQty(dto.getRawQty());
        pkCodeInfo.setFactoryId(dto.getFactoryId());
        //更新料盘数量
        pkCodeInfoService.updatePkCodeInfoByIdSelective(pkCodeInfo);
        //查询机台在用
        SmtMachineMaterialMouting moutingDto = new SmtMachineMaterialMouting();
        moutingDto.setObjectId(dto.getReelId());
        moutingDto.setEnabledFlag(MpConstant.FLAG_Y);
        List<SmtMachineMaterialMouting> smtMachineMaterialMoutings = smtMachineMaterialMoutingService.selectSmtMachineMaterialMoutingSelective(moutingDto);
        if (Objects.equals(dto.getNewQty(), new BigDecimal(NumConstant.NUM_ZERO))){
            if(CollectionUtils.isEmpty(smtMachineMaterialMoutings)){
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.MOUNTING_DATA_IS_NULL);
            }
            SmtMachineMaterialMouting mouting = smtMachineMaterialMoutings.get(NumConstant.NUM_ZERO);
            String nextReelRowid = mouting.getNextReelRowid();
            boolean hasNextReelId = StringUtils.isNotBlank(nextReelRowid);
            if (!hasNextReelId){
                return;
            }
            // nextReelId清空，nextReelId写入objectId
            for (int i = 0; i < smtMachineMaterialMoutings.size(); i++) {
                SmtMachineMaterialMouting one = smtMachineMaterialMoutings.get(i);
                if (StringUtils.isBlank(one.getMachineMaterialMoutingId())){
                    break;
                }
                SmtMachineMaterialMouting updateMoutingDto = new SmtMachineMaterialMouting();
                updateMoutingDto.setMachineMaterialMoutingId(one.getMachineMaterialMoutingId());
                updateMoutingDto.setObjectId(nextReelRowid);
                smtMachineMaterialMoutingService.updateSmtMachineMaterialMoutingByOthers(updateMoutingDto);
            }
            SmtMachineMaterialPrepare prepare = new SmtMachineMaterialPrepare();
            prepare.setObjectId(nextReelRowid);
            //按objectId删除prepare表信息
            smtMachineMaterialPrepareService.deleteSmtMachineMaterialPrepareById(prepare);
        }
        boolean result2 = dto.getItemQty().compareTo(BigDecimal.ZERO) == NumConstant.NUM_ZERO && dto.getNewQty().compareTo(BigDecimal.ZERO) > NumConstant.NUM_ZERO;
        if (result2) {
            //设置低位预警参数
            SMTScanDTO smtScanDTO = getSmtScanDTO(dto, smtMachineMaterialMoutings);
            // 调用低位预警
            RetCode retCode = psScanHistoryService.calSMTMaterialQty(smtScanDTO);
            if (!retCode.getCode().equals(MpConstant.BUSINESS_SUCCESS)){
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.CALL_MATERIAL_QTY_API_FAILED);
            }
        }
        //记录reel历史轨迹
        PkCodeHistory entity = new PkCodeHistory();
        entity.setObjectId(dto.getReelId());
        entity.setObjectType(MpConstant.REEL_ID);
        entity.setItemCode(dto.getItemCode());
        entity.setProgramName(MpConstant.UPDATE_REEL_ID_QTY);
        entity.setSourceTask(dto.getSourceTask());
        entity.setSourceBatchCode(dto.getSourceBatchCode());
        entity.setCreateBy(dto.getLoginUser());
        entity.setLastUpdatedBy(dto.getLoginUser());
        entity.setHistoryId(java.util.UUID.randomUUID().toString());
        entity.setOldItemQty(dto.getItemQty());
        entity.setCurrentQty(dto.getNewQty());
        pkCodeHistoryService.insertPkCodeHistory(entity);

    }

    private static SMTScanDTO getSmtScanDTO(PDAReelIdQtyModifyDto dto, List<SmtMachineMaterialMouting> smtMachineMaterialMoutings) {
        if(CollectionUtils.isEmpty(smtMachineMaterialMoutings)){
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.MOUNTING_DATA_IS_NULL);
        }
        SmtMachineMaterialMouting mouting = smtMachineMaterialMoutings.get(NumConstant.NUM_ZERO);
        SMTScanDTO smtScanDTO = new SMTScanDTO();
        smtScanDTO.setLineCode(mouting.getLineCode());
        smtScanDTO.setWorkOrderNo(mouting.getWorkOrder());
        smtScanDTO.setCreateBy(dto.getLoginUser());
        return smtScanDTO;
    }
}
