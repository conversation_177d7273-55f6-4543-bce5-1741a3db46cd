package com.zte.application.stepdt.impl;

import com.zte.domain.model.stepdt.KxStepBarSubmit;
import com.zte.domain.model.stepdt.KxStepBarSubmitRepository;
import com.zte.domain.model.stepdt.KxStepBoardOnline;
import com.zte.domain.model.stepdt.KxStepBoardOnlineRepository;
import com.zte.interfaces.stepdt.dto.KxStepBarSubmitDTO;
import com.zte.interfaces.stepdt.dto.KxStepBoardOnlineDTO;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.*;

public class KxStepBarSubmitServiceImplTest  extends BaseTestCase {
    @InjectMocks
    private KxStepBarSubmitServiceImpl barSubmitService;
    @Mock
    private KxStepBarSubmitRepository kxStepBarSubmitRepository;
    @Test
    public void selectBarSubmitWithParam() {
        KxStepBarSubmitDTO kxStepBarSubmitDTO =new KxStepBarSubmitDTO();
        kxStepBarSubmitDTO.setBillNo("1233");
        List<KxStepBarSubmit> list = new ArrayList<>();
        KxStepBarSubmit kxStepBarSubmit =new KxStepBarSubmit();
        kxStepBarSubmit.setBillNo("1233");
        list.add(kxStepBarSubmit);
        PowerMockito.when(kxStepBarSubmitRepository.selectBarSubmitWithParam(Mockito.any())).thenReturn(list);
        Assert.assertNotNull(barSubmitService.selectBarSubmitWithParam(kxStepBarSubmitDTO));
    }

    @Test
    public void updateBarSubmitById() {
        KxStepBarSubmit kxStepBarSubmit =new KxStepBarSubmit();
        kxStepBarSubmit.setBillNo("1233");
        PowerMockito.when(kxStepBarSubmitRepository.updateBarSubmitById(Mockito.any())).thenReturn(1);
        Assert.assertNotNull(barSubmitService.updateBarSubmitById(kxStepBarSubmit));
    }
}