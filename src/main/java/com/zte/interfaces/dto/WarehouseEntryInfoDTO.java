/**
 * 项目名称 : ${project_name}
 * 创建日期 : ${date}
 * 修改历史 :
 * 1. [${date}] 创建文件 by ${user}
 **/
package com.zte.interfaces.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 入库单DTO
 *
 * <AUTHOR>
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
public class WarehouseEntryInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * // 主键ID
     **/
    private String warehouseEntryId;

    private String eqStatus;

    /**
     * 单据号（自动生成）
     */
    private String billNo;

    /**
     * Infor单据号
     */
    private String inforBillNo;

    /**
     * 入库申请单号（从step同步）
     */
    private String applyNo;

    /**
     * 批次号
     **/
    private String prodplanId;

    /**
     * 生产计划单号
     **/
    private String prodplanNo;

    private String currProcess;

    /**
     * 订单编号
     **/
    private String planId;

    /**
     * 产品代码
     **/
    private String itemNo;

    /**
     * 产品名称
     **/
    private String itemName;

    /**
     * 软件版本
     **/
    private String softwareVersion;

    /**
     * 计划总数量
     **/
    private BigDecimal taskQty;

    /**
     * 实收数量
     **/
    private BigDecimal commitedQty;

    /**
     * 环保属性
     **/
    private String isLead;

    /**
     * 单板包装扫描-目的仓编码
     **/
    private String destinationWarehouse;

    /**
     * 转材料后代码
     **/
    private String sku;

    /**
     * 小批量标识
     **/
    private String isSmall;

    /**
     * 重度维修
     **/
    private String isRepair;

    /**
     * 状态
     */
    private String status;

    /**
     * 备注
     **/
    private String remark;

    /**
     * 合同号
     */
    private String contractNo;

    /**
     * 规格型号
     */
    private String internalType;

    private List<PsWipInfoDTO> snList;

    private List<String> notInSnList;
    private int limit;

    private boolean scanReceiveFlag;

    private Long stockQty;

    public boolean isScanReceiveFlag() {
        return scanReceiveFlag;
    }

    public void setScanReceiveFlag(boolean scanReceiveFlag) {
        this.scanReceiveFlag = scanReceiveFlag;
    }

    public List<String> getNotInSnList() {
        return notInSnList;
    }

    public void setNotInSnList(List<String> notInSnList) {
        this.notInSnList = notInSnList;
    }

    /**
     * 当前时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String currDate;

    /**
     * // TODO remarks
     **/
    private String createBy;

    /**
     * // TODO remarks
     **/
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;

    /**
     * // TODO remarks
     **/
    private String lastUpdatedBy;

    /**
     * // TODO remarks
     **/
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastUpdatedDate;

    /**
     * // TODO remarks
     **/
    private String enabledFlag;

    /**
     * // TODO remarks
     **/
    private BigDecimal orgId;

    /**
     * // TODO remarks
     **/
    private BigDecimal factoryId;

    /**
     * // TODO remarks
     **/
    private BigDecimal entityId;

    /**
     * // TODO remarks
     **/
    private String attribute1;

    /**
     * // TODO remarks
     **/
    private String attribute2;

    /**
     * // TODO remarks
     **/
    private String attribute3;

    /**
     * // TODO remarks
     **/
    private String attribute4;

    /**
     * // TODO remarks
     **/
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date attribute5;

    private String createDateStart;

    private String createDateEnd;

    /**
     * erp错误信息
     **/
    private String erpErrorInfo;

    /**
     * erp状态
     **/
    private String erpStatus;

    /**
     * 任务号
     **/
    private String taskNo;

    /**
     * 子库存
     **/
    private String subStock;

    /**
     * 货位ID
     **/
    private BigDecimal locatorId;

    /**
     * 货位
     **/
    private String locatorName;

    /**
     * 单据类型：0-维修单，1-退库单，2-单板生产入库单，3-整机入库单
     **/
    private String billType;

    private String stockType;

    // 扫描方式
    private String scanType;

    private String startSn;

    private String endSn;

    private List<String> strSnList;

    private String craftSection;

    private String routeId;

    //别名
    private String carryAccountName;

    private String externalType;

    public String getExternalType() {
        return externalType;
    }

    public void setExternalType(String externalType) {
        this.externalType = externalType;
    }

    public String getCarryAccountName() {
        return carryAccountName;
    }

    public void setCarryAccountName(String carryAccountName) {
        this.carryAccountName = carryAccountName;
    }

    public String getCraftSection() {
        return craftSection;
    }

    public void setCraftSection(String craftSection) {
        this.craftSection = craftSection;
    }

    public String getRouteId() {
        return routeId;
    }

    public void setRouteId(String routeId) {
        this.routeId = routeId;
    }

    public List<String> getStrSnList() {
        return strSnList;
    }

    public void setStrSnList(List<String> strSnList) {
        this.strSnList = strSnList;
    }

    public String getStartSn() {
        return startSn;
    }

    public void setStartSn(String startSn) {
        this.startSn = startSn;
    }

    public String getEndSn() {
        return endSn;
    }

    public void setEndSn(String endSn) {
        this.endSn = endSn;
    }

    public String getScanType() {
        return scanType;
    }

    public void setScanType(String scanType) {
        this.scanType = scanType;
    }

    private String zjSubcardFlag;
    /**
     * 仓库名称
     */
    private String stockName;
    private List<WarehouseEntryFlowInfoDTO> listWarehouseEnrtyFlowInfoDTO;

    private String sort; // 排序字段(值areaId或者areaCode)

    private String order; // 排序方式(默认升序,设为desc时降序)

    private Long page = 0L; // 请求的页码

    private Long rows = 0L; // 每页条数

    private String warehouseEntryDetailId;

    private String workOrderNo;

    private String sns;

    private String warehouseEntryDetailIds;

    private String sourceSubStock;
    private BigDecimal sourceLocatorId;
    private String sourceLocatorName;
    private String taskType;
    private String notInfoStatus;
    private String notDetailStatus;
    private String lpn;
    private List<String> lpnList;
    private List<String> stockNameList;

    private String empNo;

    private String stock;

    private List<String> titleList;
    private List<String> propsList;

    private String warehouseCode;

    public List<String> getTitleList() {
        return titleList;
    }

    public void setTitleList(List<String> titleList) {
        this.titleList = titleList;
    }

    public List<String> getPropsList() {
        return propsList;
    }

    public void setPropsList(List<String> propsList) {
        this.propsList = propsList;
    }

    public String getStock() {
        return stock;
    }

    public void setStock(String stock) {
        this.stock = stock;
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public String getCurrProcess() {
        return currProcess;
    }

    public void setCurrProcess(String currProcess) {
        this.currProcess = currProcess;
    }

    public String getStockType() {
        return stockType;
    }

    public void setStockType(String stockType) {
        this.stockType = stockType;
    }

    public String getZjSubcardFlag() {
        return zjSubcardFlag;
    }

    public void setZjSubcardFlag(String zjSubcardFlag) {
        this.zjSubcardFlag = zjSubcardFlag;
    }

    public String getStockName() {
        return stockName;
    }

    public void setStockName(String stockName) {
        this.stockName = stockName;
    }

    public List<WarehouseEntryFlowInfoDTO> getListWarehouseEnrtyFlowInfoDTO() {
        return listWarehouseEnrtyFlowInfoDTO;
    }

    public String getEmpNo() {
        return empNo;
    }

    public void setEmpNo(String empNo) {
        this.empNo = empNo;
    }

    public BigDecimal getSourceLocatorId() {
        return sourceLocatorId;
    }

    public void setSourceLocatorId(BigDecimal sourceLocatorId) {
        this.sourceLocatorId = sourceLocatorId;
    }

    public String getSourceSubStock() {
        return sourceSubStock;
    }

    public void setSourceSubStock(String sourceSubStock) {
        this.sourceSubStock = sourceSubStock;
    }

    public String getSourceLocatorName() {
        return sourceLocatorName;
    }

    public void setSourceLocatorName(String sourceLocatorName) {
        this.sourceLocatorName = sourceLocatorName;
    }

    public String getTaskType() {
        return taskType;
    }

    public void setTaskType(String taskType) {
        this.taskType = taskType;
    }

    public void setListWarehouseEnrtyFlowInfoDTO(List<WarehouseEntryFlowInfoDTO> listWarehouseEnrtyFlowInfoDTO) {

        this.listWarehouseEnrtyFlowInfoDTO = listWarehouseEnrtyFlowInfoDTO;
    }

    public void setWarehouseEntryId(String warehouseEntryId) {

        this.warehouseEntryId = warehouseEntryId;
    }

    public String getWarehouseEntryId() {

        return warehouseEntryId;
    }

    public String getBillNo() {

        return billNo;
    }

    public void setBillNo(String billNo) {

        this.billNo = billNo;
    }

    public String getInforBillNo() {

        return inforBillNo;
    }

    public void setInforBillNo(String inforBillNo) {

        this.inforBillNo = inforBillNo;
    }

    public String getApplyNo() {

        return applyNo;
    }

    public void setApplyNo(String applyNo) {

        this.applyNo = applyNo;
    }

    public void setProdplanId(String prodplanId) {

        this.prodplanId = prodplanId;
    }

    public String getProdplanId() {

        return prodplanId;
    }

    public void setProdplanNo(String prodplanNo) {

        this.prodplanNo = prodplanNo;
    }

    public String getProdplanNo() {

        return prodplanNo;
    }

    public void setPlanId(String planId) {

        this.planId = planId;
    }

    public String getPlanId() {

        return planId;
    }

    public String getItemNo() {

        return itemNo;
    }

    public void setItemNo(String itemNo) {

        this.itemNo = itemNo;
    }

    public String getItemName() {

        return itemName;
    }

    public void setItemName(String itemName) {

        this.itemName = itemName;
    }

    public void setSoftwareVersion(String softwareVersion) {

        this.softwareVersion = softwareVersion;
    }

    public String getSoftwareVersion() {

        return softwareVersion;
    }

    public BigDecimal getTaskQty() {

        return taskQty;
    }

    public void setTaskQty(BigDecimal taskQty) {

        this.taskQty = taskQty;
    }

    public BigDecimal getCommitedQty() {

        return commitedQty;
    }

    public void setCommitedQty(BigDecimal commitedQty) {

        this.commitedQty = commitedQty;
    }

    public void setIsLead(String isLead) {

        this.isLead = isLead;
    }

    public String getIsLead() {

        return isLead;
    }

    public void setIsSmall(String isSmall) {

        this.isSmall = isSmall;
    }

    public String getIsSmall() {

        return isSmall;
    }

    public void setIsRepair(String isRepair) {

        this.isRepair = isRepair;
    }

    public String getIsRepair() {

        return isRepair;
    }

    public String getStatus() {

        return status;
    }

    public void setStatus(String status) {

        this.status = status;
    }

    public void setRemark(String remark) {

        this.remark = remark;
    }

    public String getRemark() {

        return remark;
    }

    public void setCreateBy(String createBy) {

        this.createBy = createBy;
    }

    public String getCreateBy() {

        return createBy;
    }

    public void setCreateDate(Date createDate) {

        this.createDate = createDate;
    }

    public Date getCreateDate() {

        return createDate;
    }

    public void setLastUpdatedBy(String lastUpdatedBy) {

        this.lastUpdatedBy = lastUpdatedBy;
    }

    public String getLastUpdatedBy() {

        return lastUpdatedBy;
    }

    public void setLastUpdatedDate(Date lastUpdatedDate) {

        this.lastUpdatedDate = lastUpdatedDate;
    }

    public Date getLastUpdatedDate() {

        return lastUpdatedDate;
    }

    public String getCurrDate() {

        return currDate;
    }

    public void setCurrDate(String currDate) {

        this.currDate = currDate;
    }

    public void setEnabledFlag(String enabledFlag) {

        this.enabledFlag = enabledFlag;
    }

    public String getEnabledFlag() {

        return enabledFlag;
    }

    public void setOrgId(BigDecimal orgId) {

        this.orgId = orgId;
    }

    public BigDecimal getOrgId() {

        return orgId;
    }

    public void setFactoryId(BigDecimal factoryId) {

        this.factoryId = factoryId;
    }

    public BigDecimal getFactoryId() {

        return factoryId;
    }

    public void setEntityId(BigDecimal entityId) {

        this.entityId = entityId;
    }

    public BigDecimal getEntityId() {

        return entityId;
    }

    public String getContractNo() {

        return contractNo;
    }

    public void setContractNo(String contractNo) {

        this.contractNo = contractNo;
    }

    public String getInternalType() {

        return internalType;
    }

    public void setInternalType(String internalType) {

        this.internalType = internalType;
    }

    public void setAttribute1(String attribute1) {

        this.attribute1 = attribute1;
    }

    public String getAttribute1() {

        return attribute1;
    }

    public void setAttribute2(String attribute2) {

        this.attribute2 = attribute2;
    }

    public String getAttribute2() {

        return attribute2;
    }

    public void setAttribute3(String attribute3) {

        this.attribute3 = attribute3;
    }

    public String getAttribute3() {

        return attribute3;
    }

    public void setAttribute4(String attribute4) {

        this.attribute4 = attribute4;
    }

    public String getAttribute4() {

        return attribute4;
    }

    public void setAttribute5(Date attribute5) {

        this.attribute5 = attribute5;
    }

    public Date getAttribute5() {

        return attribute5;
    }

    public String getSort() {

        return sort;
    }

    public void setSort(String sort) {

        this.sort = sort;
    }

    public String getOrder() {

        return order;
    }

    public void setOrder(String order) {

        this.order = order;
    }

    public Long getPage() {

        return page;
    }

    public void setPage(Long page) {

        this.page = page;
    }

    public Long getRows() {

        return rows;
    }

    public void setRows(Long rows) {

        this.rows = rows;
    }

    public String getCreateDateStart() {

        return createDateStart;
    }

    public void setCreateDateStart(String createDateStart) {

        this.createDateStart = createDateStart;
    }

    public String getCreateDateEnd() {

        return createDateEnd;
    }

    public void setCreateDateEnd(String createDateEnd) {

        this.createDateEnd = createDateEnd;
    }

    public String getErpErrorInfo() {

        return erpErrorInfo;
    }

    public void setErpErrorInfo(String erpErrorInfo) {

        this.erpErrorInfo = erpErrorInfo;
    }

    public String getErpStatus() {

        return erpStatus;
    }

    public void setErpStatus(String erpStatus) {

        this.erpStatus = erpStatus;
    }

    private String sn;//单板条码

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public String getTaskNo() {
        return taskNo;
    }

    public void setTaskNo(String taskNo) {
        this.taskNo = taskNo;
    }

    public String getSubStock() {
        return subStock;
    }

    public void setSubStock(String subStock) {
        this.subStock = subStock;
    }

    public BigDecimal getLocatorId() {
        return locatorId;
    }

    public void setLocatorId(BigDecimal locatorId) {
        this.locatorId = locatorId;
    }

    public String getLocatorName() {
        return locatorName;
    }

    public void setLocatorName(String locatorName) {
        this.locatorName = locatorName;
    }

    public String getBillType() {
        return billType;
    }

    public void setBillType(String billType) {
        this.billType = billType;
    }

    private String erpMoveStatus;
    private String erpDoneStatus;
    private String delQty;

    public String getErpMoveStatus() {
        return erpMoveStatus;
    }

    public void setErpMoveStatus(String erpMoveStatus) {
        this.erpMoveStatus = erpMoveStatus;
    }

    public String getErpDoneStatus() {
        return erpDoneStatus;
    }

    public void setErpDoneStatus(String erpDoneStatus) {
        this.erpDoneStatus = erpDoneStatus;
    }

    public String getDelQty() {
        return delQty;
    }

    public void setDelQty(String delQty) {
        this.delQty = delQty;
    }

    public String getWarehouseEntryDetailId() {
        return warehouseEntryDetailId;
    }

    public void setWarehouseEntryDetailId(String warehouseEntryDetailId) {
        this.warehouseEntryDetailId = warehouseEntryDetailId;
    }

    public String getWorkOrderNo() {
        return workOrderNo;
    }

    public void setWorkOrderNo(String workOrderNo) {
        this.workOrderNo = workOrderNo;
    }

    public String getSns() {
        return sns;
    }

    public void setSns(String sns) {
        this.sns = sns;
    }

    public String getWarehouseEntryDetailIds() {
        return warehouseEntryDetailIds;
    }

    public void setWarehouseEntryDetailIds(String warehouseEntryDetailIds) {
        this.warehouseEntryDetailIds = warehouseEntryDetailIds;
    }

    public String getEqStatus() {
        return eqStatus;
    }

    public void setEqStatus(String eqStatus) {
        this.eqStatus = eqStatus;
    }

    public List<PsWipInfoDTO> getSnList() {
        return snList;
    }

    public void setSnList(List<PsWipInfoDTO> snList) {
        this.snList = snList;
    }

    public String getNotInfoStatus() {
        return notInfoStatus;
    }

    public void setNotInfoStatus(String notInfoStatus) {
        this.notInfoStatus = notInfoStatus;
    }

    public String getNotDetailStatus() {
        return notDetailStatus;
    }

    public void setNotDetailStatus(String notDetailStatus) {
        this.notDetailStatus = notDetailStatus;
    }

    public String getLpn() {
        return lpn;
    }

    public void setLpn(String lpn) {
        this.lpn = lpn;
    }

    public List<String> getLpnList() {
        return lpnList;
    }

    public void setLpnList(List<String> lpnList) {
        this.lpnList = lpnList;
    }

    public List<String> getStockNameList() {
        return stockNameList;
    }

    public void setStockNameList(List<String> stockNameList) {
        this.stockNameList = stockNameList;
    }

    public void setLimit(int limit) {
        this.limit = limit;
    }

    public int getLimit() {
        return limit;
    }

    public String getDestinationWarehouse() {
        return destinationWarehouse;
    }

    public void setDestinationWarehouse(String destinationWarehouse) {
        this.destinationWarehouse = destinationWarehouse;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getWarehouseCode() {
        return warehouseCode;
    }

    public void setWarehouseCode(String warehouseCode) {
        this.warehouseCode = warehouseCode;
    }

    public Long getStockQty() {
        return stockQty;
    }

    public void setStockQty(Long stockQty) {
        this.stockQty = stockQty;
    }
}
