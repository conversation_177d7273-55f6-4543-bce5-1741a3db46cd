<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.PsWipScanHistoryRepository">
  <resultMap id="HistoryResultMap" type="com.zte.domain.model.PsWipScanHistory">
    <id column="SMT_SCAN_ID" jdbcType="VARCHAR" property="smtScanId" />
    <result column="SN" jdbcType="VARCHAR" property="sn" />
    <result column="WORK_ORDER_NO" jdbcType="VARCHAR" property="workOrderNo" />
    <result column="ITEM_NO" jdbcType="VARCHAR" property="itemNo" />
    <result column="ITEM_NAME" jdbcType="VARCHAR" property="itemName" />
    <result column="ROUTE_ID" jdbcType="VARCHAR" property="routeId" />
    <result column="CURR_PROCESS_CODE" jdbcType="VARCHAR" property="currProcessCode" />
    <result column="STATUS" jdbcType="VARCHAR" property="status" />
    <result column="WORKER" jdbcType="VARCHAR" property="worker" />
    <result column="IN_TIME" jdbcType="TIMESTAMP" property="inTime" />
    <result column="OUT_TIME" jdbcType="TIMESTAMP" property="outTime" />
    <result column="PARENT_SN" jdbcType="VARCHAR" property="parentSn" />
    <result column="ERROR_CODE" jdbcType="VARCHAR" property="errorCode" />
    <result column="LINE_CODE" jdbcType="VARCHAR" property="lineCode" />
    <result column="TIME_SPAN" jdbcType="TIMESTAMP" property="timeSpan" />
    <result column="WORKSHOP_CODE" jdbcType="VARCHAR" property="workshopCode" />
    <result column="CRAFT_SECTION" jdbcType="VARCHAR" property="craftSection" />
    <result column="WORK_STATION" jdbcType="VARCHAR" property="workStation" />
    <result column="OPE_TIMES" jdbcType="DECIMAL" property="opeTimes" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
    <result column="LAST_UPDATED_BY" jdbcType="VARCHAR" property="lastUpdatedBy" />
    <result column="LAST_UPDATED_DATE" jdbcType="TIMESTAMP" property="lastUpdatedDate" />
    <result column="ENABLED_FLAG" jdbcType="VARCHAR" property="enabledFlag" />
    <result column="ORG_ID" jdbcType="DECIMAL" property="orgId" />
    <result column="FACTORY_ID" jdbcType="DECIMAL" property="factoryId" />
    <result column="ENTITY_ID" jdbcType="DECIMAL" property="entityId" />
    <result column="ATTRIBUTE1" jdbcType="VARCHAR" property="attribute1" />
    <result column="ATTRIBUTE2" jdbcType="VARCHAR" property="attribute2" />
    <result column="ATTRIBUTE3" jdbcType="VARCHAR" property="attribute3" />
    <result column="ATTRIBUTE4" jdbcType="VARCHAR" property="attribute4" />
    <result column="ATTRIBUTE5" jdbcType="TIMESTAMP" property="attribute5" />
    <result column="LAST_PROCESS" jdbcType="VARCHAR" property="lastProcess" />
    <result column="SOURCE_SYS" jdbcType="VARCHAR" property="sourceSys" />
    <result column="NEXT_PROCESS" jdbcType="VARCHAR" property="nextProcess" />
    <result column="SOURCE_IMU" jdbcType="DECIMAL" property="sourceImu" />
    <result column="SOURCE_BIMU" jdbcType="DECIMAL" property="sourceBimu" />
    <result column="COL_NAME" jdbcType="VARCHAR" property="colName" />
    <result column="SOURCE_SYS_NAME" jdbcType="VARCHAR" property="sourceSysName" />
    <result column="IS_WRITE_BACK_SCAN" jdbcType="VARCHAR" property="isWriteBackScan" />
    <result column="IS_WRITE_BACK_USER" jdbcType="VARCHAR" property="isWriteBackUser" />
    <result column="IS_TRACE_CALCULATE" jdbcType="VARCHAR" property="isTraceCalculate" />
    <result column="PCB_QTY" jdbcType="DECIMAL" property="pcbQty" />
    <result column="FIX_ID" jdbcType="VARCHAR" property="fixId" />
    <result column="STATION_NAME" jdbcType="VARCHAR" property="stationName" />
  </resultMap>
  <select id="getPsWipScanHistoryList" parameterType="java.util.Map" resultMap="HistoryResultMap">
    select SMT_SCAN_ID,SN,WORK_ORDER_NO,ITEM_NO,ITEM_NAME,ROUTE_ID,CURR_PROCESS_CODE,STATUS,
    WORKER,IN_TIME,OUT_TIME,PARENT_SN,ERROR_CODE,LINE_CODE,TIME_SPAN,WORKSHOP_CODE,CRAFT_SECTION,
    WORK_STATION,OPE_TIMES,REMARK,CREATE_BY,CREATE_DATE,LAST_UPDATED_BY,LAST_UPDATED_DATE,ENABLED_FLAG,
    ORG_ID,FACTORY_ID,ENTITY_ID,ATTRIBUTE1,ATTRIBUTE2,ATTRIBUTE3,ATTRIBUTE4,ATTRIBUTE5,LAST_PROCESS,
    SOURCE_SYS,NEXT_PROCESS,SOURCE_IMU,SOURCE_BIMU,COL_NAME,SOURCE_SYS_NAME,IS_WRITE_BACK_SCAN,
    IS_WRITE_BACK_USER,IS_TRACE_CALCULATE,PCB_QTY,FIX_ID,STATION_NAME
    from WIP_SCAN_HISTORY
    where 1=1
    and ENABLED_FLAG = 'Y'
    <if test="sn != null and sn != ''"> and SN = #{sn}</if>
    <if test="currProcessCode != null and currProcessCode != ''"> and CURR_PROCESS_CODE = #{currProcessCode}</if>
    <if test="workStation != null and workStation != ''"> and WORK_STATION = #{workStation}</if>
    <if test="(sn ==null or sn == '') and
      (currProcessCode ==null or currProcessCode== '') and
      (workStation ==null or workStation =='') ">
      and 1=2
    </if>
  </select>

  <update id="updatePsWipScanHistoryBatch" parameterType="java.util.List">
    <foreach collection="list" separator=";" item="item" >
      update WIP_SCAN_HISTORY T
      <set>
        <if test="item.sn != null">
          T.SN = #{item.sn,jdbcType=VARCHAR},
        </if>
        <if test="item.workOrderNo != null">
          T.WORK_ORDER_NO = #{item.workOrderNo,jdbcType=VARCHAR},
        </if>
        <if test="item.itemNo != null">
          T.ITEM_NO = #{item.itemNo,jdbcType=VARCHAR},
        </if>
        <if test="item.itemName != null">
          T.ITEM_NAME = #{item.itemName,jdbcType=VARCHAR},
        </if>
        <if test="item.routeId != null">
          T.ROUTE_ID = #{item.routeId,jdbcType=VARCHAR},
        </if>
        <if test="item.currProcessCode != null">
          T.CURR_PROCESS_CODE = #{item.currProcessCode,jdbcType=VARCHAR},
        </if>
        <if test="item.status != null">
          T.STATUS = #{item.status,jdbcType=VARCHAR},
        </if>
        <if test="item.worker != null">
          T.WORKER = #{item.worker,jdbcType=VARCHAR},
        </if>
        <if test="item.inTime != null">
          T.IN_TIME = #{item.inTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.outTime != null">
          T.OUT_TIME = #{item.outTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.parentSn != null">
          T.PARENT_SN = #{item.parentSn,jdbcType=VARCHAR},
        </if>
        <if test="item.errorCode != null">
          T.ERROR_CODE = #{item.errorCode,jdbcType=VARCHAR},
        </if>
        <if test="item.lineCode != null">
          T.LINE_CODE = #{item.lineCode,jdbcType=VARCHAR},
        </if>
        <if test="item.timeSpan != null">
          T.TIME_SPAN = #{item.timeSpan,jdbcType=TIMESTAMP},
        </if>
        <if test="item.workshopCode != null">
          T.WORKSHOP_CODE = #{item.workshopCode,jdbcType=VARCHAR},
        </if>
        <if test="item.opeTimes != null">
          T.OPE_TIMES = #{item.opeTimes,jdbcType=DECIMAL},
        </if>
        <if test="item.craftSection != null">
          T.CRAFT_SECTION = #{item.craftSection,jdbcType=VARCHAR},
        </if>
        <if test="item.workStation != null">
          T.WORK_STATION = #{item.workStation,jdbcType=VARCHAR},
        </if>
        <if test="item.lastProcess != null">
          T.LAST_PROCESS = #{item.lastProcess,jdbcType=VARCHAR},
        </if>
        <if test="item.sourceSys != null">
          T.SOURCE_SYS = #{item.sourceSys,jdbcType=VARCHAR},
        </if>
        <if test="item.nextProcess != null">
          T.NEXT_PROCESS = #{item.nextProcess,jdbcType=VARCHAR},
        </if>
        <if test="item.remark != null">
          T.REMARK = #{item.remark,jdbcType=VARCHAR},
        </if>
        <if test="item.createBy != null">
          T.CREATE_BY = #{item.createBy,jdbcType=VARCHAR},
        </if>
        <if test="item.createDate != null">
          T.CREATE_DATE = #{item.createDate,jdbcType=TIMESTAMP},
        </if>
        <if test="item.lastUpdatedBy != null">
          T.LAST_UPDATED_BY = #{item.lastUpdatedBy,jdbcType=VARCHAR},
        </if>
        <if test="item.enabledFlag != null">
          T.ENABLED_FLAG = #{item.enabledFlag,jdbcType=VARCHAR},
        </if>
        <if test="item.orgId != null">
          T.ORG_ID = #{item.orgId,jdbcType=DECIMAL},
        </if>
        <if test="item.factoryId != null">
          T.FACTORY_ID = #{item.factoryId,jdbcType=DECIMAL},
        </if>
        <if test="item.entityId != null">
          T.ENTITY_ID = #{item.entityId,jdbcType=DECIMAL},
        </if>
        <if test="item.sourceImu != null">
          T.SOURCE_IMU = #{item.sourceImu,jdbcType=DECIMAL},
        </if>
        <if test="item.sourceBimu != null">
          T.SOURCE_BIMU = #{item.sourceBimu,jdbcType=DECIMAL},
        </if>
        <if test="item.colName != null">
          T.COL_NAME = #{item.colName,jdbcType=VARCHAR},
        </if>
        <if test="item.sourceSysName != null">
          T.SOURCE_SYS_NAME = #{item.sourceSysName,jdbcType=VARCHAR},
        </if>
        <if test="item.isWriteBackScan != null">
          T.IS_WRITE_BACK_SCAN = #{item.isWriteBackScan,jdbcType=VARCHAR},
        </if>
        <if test="item.isWriteBackUser != null">
          T.IS_WRITE_BACK_USER = #{item.isWriteBackUser,jdbcType=VARCHAR},
        </if>
        <if test="item.isTraceCalculate != null">
          T.IS_TRACE_CALCULATE = #{item.isTraceCalculate,jdbcType=VARCHAR},
        </if>
        <if test="item.pcbQty != null">
          T.PCB_QTY = #{item.pcbQty,jdbcType=VARCHAR},
        </if>
        <if test="item.fixId != null">
          T.FIX_ID = #{item.fixId,jdbcType=VARCHAR},
        </if>
        <if test="item.stationName != null">
          T.STATION_NAME = #{item.stationName,jdbcType=VARCHAR},
        </if>
        T.LAST_UPDATED_DATE = SYSDATE
      </set>
      where T.SMT_SCAN_ID = #{item.smtScanId,jdbcType=VARCHAR}
      and T.ENABLED_FLAG = 'Y'
    </foreach>
  </update>

  <select id="selectAccumulatePage" resultType="com.zte.domain.model.CmDailyStatisticReport">
    with ST as(
    <foreach item="lookup" collection="codeGroups" separator="union all" open="(" close=")">
      SELECT #{lookup.lookupMeaning,jdbcType=VARCHAR} CURR_PROCESS_CODE,
      #{lookup.attribute1,jdbcType=VARCHAR} WORK_STATION,
      #{lookup.attribute3,jdbcType=VARCHAR} IS_SHOW,
      #{lookup.attribute4,jdbcType=VARCHAR} FIELD_NAME
    </foreach>
    ), TT as(
    <foreach item="item" collection="psTasks" separator="union all" open="(" close=")">
      SELECT #{item.taskNo,jdbcType=VARCHAR} TASK_NO,
      #{item.prodplanId,jdbcType=VARCHAR} PRODPLAN_ID
    </foreach>)
    <!-- 可能存在多个子工序一起算累计 -->
    select TASK_NO,STAT_ITEM,SYSDATE STATISTIC_DATE,count(1) STAT_QTY from(
    <!-- 根据条码去重 -->
    SELECT TT.TASK_NO,T.SN,ST.FIELD_NAME STAT_ITEM
    FROM
    WIP_SCAN_HISTORY T, ST,TT
    WHERE T.ENABLED_FLAG = 'Y'
    AND T.ATTRIBUTE1 = TT.PRODPLAN_ID
    and ST.CURR_PROCESS_CODE = T.CURR_PROCESS_CODE
    AND ST.WORK_STATION = T.WORK_STATION
    <!-- 定时任务统计不排除隐藏字段，查询和导出需隐藏 -->
    <if test="isJob == false">
      AND ST.IS_SHOW = 'Y'
    </if>
    GROUP BY TT.TASK_NO,T.SN,ST.FIELD_NAME)
    GROUP BY TASK_NO,STAT_ITEM
  </select>

  <select id="getInfoByParentSn" parameterType="java.util.List" resultType="com.zte.interfaces.dto.ReturnAndOnlineResultDTO">
    with tt as(
    <foreach collection="parentSnList" item="item" separator="UNION ALL">
      select #{item} parent_sn
    </foreach>)
    select * from(
      select t.sn,t.parent_sn, t.create_by,t.create_date, t.attribute1,t.attribute2, t.source_sys_name,
      rank() over(partition by t.SN order by t.CREATE_DATE desc) mm
      from wip_scan_history t,tt
      where t.enabled_flag = 'Y'
      and t.sn = tt.parent_sn
      and t.source_sys_name = '返制上线'
    ) where mm =1
  </select>

  <insert id="insertWipScanHistoryByBatch">
    insert into wip_scan_history (smt_scan_id,sn, item_no, curr_process_code, parent_sn,craft_section, work_order_no, line_code,
    item_name, work_station, create_date, last_updated_date,attribute1,attribute2,factory_id,create_by,last_updated_by, status, last_process
    )
    values
    <foreach collection="list" item="item" separator=",">
      ( gen_random_uuid(), #{item.parentSn}, #{item.itemCode}, #{item.processCode}, #{item.formSn},#{item.craftSection},#{item.workOrderNo},
      #{item.lineCode}, #{item.itemName}, #{item.workStation}, sysdate,sysdate, #{item.attribute1}, #{item.taskNo}, cast(#{factoryId} as numeric),
      #{empNo},#{empNo}, #{item.status}, 'Y')
    </foreach>
  </insert>
</mapper>
