package com.zte.interfaces.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2020/12/14 23
 * @description:
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class BomDetailQueryDTO implements Serializable {
    /**
     * 配送工序
     */
    private String deliveryProcess;
    /**
     * 料单代码
     */
    private String bomCode;
    /**
     * 分阶来源
     */
    private String subLevels;
    @ApiModelProperty(value = "配送工序，AB 面拆分前")
    private String deliveryProcessSplitBefore;
    @ApiModelProperty(value = "前加工类型代码为空")
    private String typeCodeNull;

    public String getDeliveryProcessSplitBefore() {
        return deliveryProcessSplitBefore;
    }

    public void setDeliveryProcessSplitBefore(String deliveryProcessSplitBefore) {
        this.deliveryProcessSplitBefore = deliveryProcessSplitBefore;
    }

    public String getTypeCodeNull() {
        return typeCodeNull;
    }

    public void setTypeCodeNull(String typeCodeNull) {
        this.typeCodeNull = typeCodeNull;
    }

    public String getDeliveryProcess() {
        return deliveryProcess;
    }

    public void setDeliveryProcess(String deliveryProcess) {
        this.deliveryProcess = deliveryProcess;
    }

    public String getBomCode() {
        return bomCode;
    }

    public void setBomCode(String bomCode) {
        this.bomCode = bomCode;
    }

    public String getSubLevels() {
        return subLevels;
    }

    public void setSubLevels(String subLevels) {
        this.subLevels = subLevels;
    }
}
