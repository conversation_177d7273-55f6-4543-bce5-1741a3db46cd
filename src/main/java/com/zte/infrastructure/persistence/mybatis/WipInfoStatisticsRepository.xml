<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zte.domain.model.WipInfoStatisticsRepository">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.zte.interfaces.dto.WipInfoStatisticsDTO" id="wipInfoStatisticsMap">
        <result property="id" jdbcType="VARCHAR" column="ID" />
        <result property="taskNo" jdbcType="VARCHAR" column="TASK_NO" />
        <result property="prodplanId" jdbcType="VARCHAR" column="PRODPLAN_ID" />
        <result property="itemCode" jdbcType="VARCHAR" column="ITEM_CODE" />
        <result property="totalNumber" jdbcType="DECIMAL" column="TOTAL_NUMBER" />
        <result property="finishedQty" jdbcType="DECIMAL" column="FINISHED_QTY" />
        <result property="craftSection" jdbcType="VARCHAR" column="CRAFT_SECTION" />
        <result property="factoryId" jdbcType="DECIMAL" column="FACTORY_ID" />
        <result property="createDate" jdbcType="TIMESTAMP" column="CREATE_DATE" />
        <result property="itemName" jdbcType="VARCHAR" column="ITEM_NAME" />
        <result property="currProcessCode" jdbcType="VARCHAR" column="CURR_PROCESS_CODE" />
        <result property="lastProcess" jdbcType="VARCHAR" column="LAST_PROCESS" />
        <result property="taskStatus" jdbcType="VARCHAR" column="TASK_STATUS" />
        <result property="enabledFlag" jdbcType="VARCHAR" column="ENABLED_FLAG" />
        <result property="createBy" jdbcType="VARCHAR" column="CREATE_BY" />
        <result property="lastUpdatedBy" jdbcType="VARCHAR" column="LAST_UPDATED_BY" />
        <result property="lastUpdatedDate" jdbcType="TIMESTAMP" column="LAST_UPDATED_DATE" />
        <result property="entityId" jdbcType="DECIMAL" column="ENTITY_ID" />
        <result property="orgId" jdbcType="DECIMAL" column="ORG_ID" />
        <result property="planTeam" jdbcType="VARCHAR" column="PLAN_TEAM" />
        <result property="externalType" jdbcType="VARCHAR" column="EXTERNAL_TYPE" />
        <result property="draftedBy" jdbcType="VARCHAR" column="DRAFTED_BY" />
        <result property="retrievalDate" jdbcType="TIMESTAMP" column="RETRIEVAL_DATE" />
        <result property="reworkQty" jdbcType="DECIMAL" column="REWORK_QTY" />
        <result property="currentDayCompletedQty" jdbcType="DECIMAL" column="CURRENT_DAY_COMPLETED_QTY" />
        <result property="erpCompletedQty" jdbcType="DECIMAL" column="ERP_COMPLETED_QTY" />
        <result property="scheduledStartDate" jdbcType="TIMESTAMP" column="SCHEDULED_START_DATE" />
        <result property="sourceSys" jdbcType="VARCHAR" column="SOURCE_SYS" />
    </resultMap>
    <!-- 条码分布查询 -->
    <resultMap id="barcodeDistributionQueryMap" type="java.util.Map">
	    <result column="NO" jdbcType="VARCHAR" property="no" />
	    <result column="item_code" jdbcType="VARCHAR" property="itemCode" />
	    <result column="item_name" jdbcType="VARCHAR" property="itemName" />
	    <result column="task_no" jdbcType="VARCHAR" property="taskNo" />
	    <result column="prodplan_id" jdbcType="VARCHAR" property="prodplanId" />
        <result column="create_date" jdbcType="VARCHAR" property="createDate" />
  	</resultMap>

    <sql id="Base_Column_List">
      ID,TASK_NO,PRODPLAN_ID,ITEM_CODE,TOTAL_NUMBER,FINISHED_QTY,CRAFT_SECTION,
      FACTORY_ID,CREATE_DATE,ITEM_NAME,CURR_PROCESS_CODE,
      LAST_PROCESS,TASK_STATUS,ENABLED_FLAG,CREATE_BY,LAST_UPDATED_BY,
      LAST_UPDATED_DATE,ENTITY_ID,ORG_ID,PLAN_TEAM,EXTERNAL_TYPE,DRAFTED_BY,
      RETRIEVAL_DATE,REWORK_QTY,CURRENT_DAY_COMPLETED_QTY,ERP_COMPLETED_QTY,
      SCHEDULED_START_DATE,SOURCE_SYS
    </sql>

    <select id="pageList" parameterType="com.zte.springbootframe.common.model.Page" resultMap="wipInfoStatisticsMap">
      select
      <include refid="Base_Column_List" />
      from WIP_INFO_STATISTICS where 1=1
      <if test="params != null and params.id != null and params.id != ''">and ID = #{params.id}</if>
      <if test="params != null and params.taskNo != null and params.taskNo != ''">and TASK_NO = #{params.taskNo}</if>
      <if test="params != null and params.prodplanId != null and params.prodplanId != ''">and PRODPLAN_ID = #{params.prodplanId}</if>
      <if test="params != null and params.itemCode != null and params.itemCode != ''">and ITEM_CODE = #{params.itemCode}</if>
      <if test="params != null and params.totalNumber != null and params.totalNumber != ''">and TOTAL_NUMBER = #{params.totalNumber}::numeric</if>
      <if test="params != null and params.finishedQty != null and params.finishedQty != ''">and FINISHED_QTY = #{params.finishedQty}::numeric</if>
      <if test="params != null and params.craftSection != null and params.craftSection != ''">and CRAFT_SECTION = #{params.craftSection}</if>
      <if test="params != null and params.createDate != null">and CREATE_DATE = #{params.createDate}</if>
      <if test="params != null and params.itemName != null and params.itemName != ''">and ITEM_NAME = #{params.itemName}</if>
      <if test=" (params == null or params.id == null or params.id == '' ) and (params == null or params.taskNo == null or params.taskNo == '' ) and  (params == null or params.prodplanId == null or params.prodplanId == '' ) and
            (params == null or params.itemCode == null or params.itemCode == '' ) and (params == null or params.totalNumber == null or params.totalNumber == '' ) and  (params == null or params.finishedQty == null or params.finishedQty == '' ) and
            (params == null or params.finishedQty == null or params.finishedQty == '' ) and (params == null or params.craftSection == null or params.craftSection == '' ) and
            (params == null or params.itemName == null or params.itemName == '' ) and (params == null or params.createDate == null )">
        and 1=2
      </if>
    </select>

    <select id="getStatisticInfo" parameterType="java.util.Map" resultMap="wipInfoStatisticsMap">
        select
        <include refid="Base_Column_List" />
        from WIP_INFO_STATISTICS where 1=1
        <if test="id != null and id != ''">and ID = #{id}</if>
        <if test="taskNo != null and taskNo != ''">and TASK_NO = #{taskNo}</if>
        <if test="prodplanId != null and prodplanId != ''">and PRODPLAN_ID = #{prodplanId}</if>
        <if test="itemCode != null and itemCode != ''">and ITEM_CODE = #{itemCode}</if>
        <if test="totalNumber != null and totalNumber != ''">and TOTAL_NUMBER = #{totalNumber}::numeric</if>
        <if test="finishedQty != null and finishedQty != ''">and FINISHED_QTY = #{finishedQty}::numeric</if>
        <if test="craftSection != null and craftSection != ''">and CRAFT_SECTION = #{craftSection}</if>
        <if test="createDate != null ">and CREATE_DATE = #{createDate}</if>
        <if test="itemName != null and itemName != ''">and ITEM_NAME = #{itemName}</if>
        <if test=" (id == null or id == '' ) and (taskNo == null or taskNo == '' ) and (prodplanId == null or prodplanId == '' )
                   and (itemCode == null or itemCode == '' )  and (totalNumber == null or totalNumber == '' )   and (finishedQty == null or finishedQty == '' )
                    and (craftSection == null or craftSection == '' )   and (createDate == null )   and (itemName == null or itemName == '' ) ">
            and 1=2
        </if>
    </select>

    <select id="queryStatisticsSimpleByParam" resultType="com.zte.interfaces.dto.WipInfoStatisticsSimpleDTO">
        select
            distinct TASK_NO as taskNo,CREATE_DATE as createDate,to_char(LAST_UPDATED_DATE,'yyyy-mm-dd hh24:mi:ss') lastUpdatedDate,ITEM_CODE as itemCode , ITEM_NAME as itemName ,EXTERNAL_TYPE as externalType
                    ,PLAN_TEAM as planTeam ,DRAFTED_BY as draftedBy ,CURRENT_DAY_COMPLETED_QTY as currentDayCompletedQty,
                    ERP_COMPLETED_QTY as erpCompletedQty ,ORG_ID as orgId ,SCHEDULED_START_DATE as scheduledStartDate,REWORK_QTY as reworkQty
                   ,ERP_STATUS as erpStatus,ENV_ATTR as envAttr
        from WIP_INFO_STATISTICS where 1=1
        and TASK_NO in
        <if test="filterTaskNoList != null and filterTaskNoList.size > 0">
            <foreach collection="filterTaskNoList" index="index" item="taskNo" open="(" separator="," close=")">
                #{taskNo}
            </foreach>
        </if>
        <if test="startDate != null">and CREATE_DATE >= #{startDate}</if>
        <if test="endDate != null"><![CDATA[ and CREATE_DATE <= #{endDate} ]]></if>
        <if test="startDate == null or endDate == null">and 1=2 </if>
    </select>

    <select id="queryTaskNoByParam" parameterType="com.zte.interfaces.dto.BarcodeDistributionQueryDTO" resultType="java.lang.String">
        select
        distinct TASK_NO as taskNo
        from WIP_INFO_STATISTICS t where 1=1
        <if test="taskNoOrProdPlanId != null and taskNoOrProdPlanId != ''">and TASK_NO  like '%${taskNoOrProdPlanId}%' </if>
        <if test="planTeam != null and planTeam != ''">and PLAN_TEAM = #{planTeam}</if>
        <if test="itemNo != null and itemNo != ''">and ITEM_CODE = #{itemNo}</if>
        <if test="itemName != null and itemName != ''">and t.ITEM_NAME like '%${itemName}%'</if>
        <if test="externalType != null and externalType != ''">and EXTERNAL_TYPE = #{externalType}</if>
        <if test="planner != null and planner != ''">and (t.DRAFTED_BY = #{planner} or t.DRAFTED_BY =LTRIM(#{planner},'0'))</if>
        <if test="orgId != null and orgId != ''">and ORG_ID = cast(#{orgId} as numeric)</if>
        <if test="startDate != null">and CREATE_DATE >= #{startDate}</if>
        <if test="endDate != null"><![CDATA[ and CREATE_DATE <= #{endDate} ]]></if>
        <if test="sourceSys != null and sourceSys != ''">and SOURCE_SYS = #{sourceSys}</if>
        <if test="envAttr != null and envAttr != ''">and ENV_ATTR = #{envAttr}</if>
        <if test="erpStatus != null and erpStatus != ''">and ERP_STATUS = #{erpStatus}</if>
        <if test="startDate == null or endDate == null">and 1=2 </if>
    </select>

    <select id="queryAllWipInfoStatistics" parameterType="com.zte.interfaces.dto.BarcodeDistributionQueryDTO" resultType="com.zte.interfaces.dto.WipInfoStatisticsSimpleDTO">
        select
        distinct TASK_NO as taskNo,CREATE_DATE as createDate,to_char(LAST_UPDATED_DATE,'yyyy-mm-dd hh24:mi:ss') lastUpdatedDate,ITEM_NAME as itemName,ITEM_CODE as itemCode ,EXTERNAL_TYPE as externalType
        ,PLAN_TEAM as planTeam ,DRAFTED_BY as draftedBy ,CURRENT_DAY_COMPLETED_QTY as currentDayCompletedQty,
        ERP_COMPLETED_QTY as erpCompletedQty ,ORG_ID as orgId ,SCHEDULED_START_DATE as scheduledStartDate,ERP_STATUS as erpStatus,ENV_ATTR as envAttr
        from WIP_INFO_STATISTICS t where 1=1
        <if test="taskNoOrProdPlanId != null and taskNoOrProdPlanId != ''">and t.TASK_NO like '%${taskNoOrProdPlanId}%' </if>
        <if test="planTeam != null and planTeam != ''">and PLAN_TEAM = #{planTeam}</if>
        <if test="itemNo != null and itemNo != ''">and ITEM_CODE = #{itemNo}</if>
        <if test="itemName != null and itemName != ''">and t.ITEM_NAME like '%${itemName}%'</if>
        <if test="externalType != null and externalType != ''">and EXTERNAL_TYPE = #{externalType}</if>
        <if test="planner != null and planner != ''">and (t.DRAFTED_BY = #{planner} or t.DRAFTED_BY =LTRIM(#{planner},'0'))</if>
        <if test="orgId != null and orgId != ''">and ORG_ID = cast(#{orgId} as numeric)</if>
        <if test="startDate != null">and CREATE_DATE >= #{startDate}</if>
        <if test="endDate != null"><![CDATA[ and CREATE_DATE <= #{endDate} ]]></if>
        <if test="sourceSys != null and sourceSys != ''">and SOURCE_SYS = #{sourceSys}</if>
        <if test="envAttr != null and envAttr != ''">and ENV_ATTR = #{envAttr}</if>
        <if test="erpStatus != null and erpStatus != ''">and ERP_STATUS = #{erpStatus}</if>
        <if test="startDate == null or endDate == null">and 1=2 </if>
    </select>

    <insert id="batchInsert" parameterType="java.util.List">
        <foreach collection="list" separator=";" item="item" >
            insert into WIP_INFO_STATISTICS ( ID,TASK_NO,PRODPLAN_ID,ITEM_CODE,
            TOTAL_NUMBER,FINISHED_QTY,CRAFT_SECTION,CURR_PROCESS_CODE,LAST_PROCESS,TASK_STATUS,
            FACTORY_ID,ITEM_NAME,ENABLED_FLAG,CREATE_BY,CREATE_DATE,LAST_UPDATED_BY,LAST_UPDATED_DATE,
            ENTITY_ID,ORG_ID,PLAN_TEAM,EXTERNAL_TYPE,DRAFTED_BY,RETRIEVAL_DATE,REWORK_QTY,CURRENT_DAY_COMPLETED_QTY,
            ERP_COMPLETED_QTY,SCHEDULED_START_DATE,SOURCE_SYS)
            values (#{item.id,jdbcType=VARCHAR},#{item.taskNo,jdbcType=VARCHAR},#{item.prodplanId,jdbcType=VARCHAR},
            #{item.itemCode,jdbcType=VARCHAR},#{item.totalNumber,jdbcType=DECIMAL},#{item.finishedQty,jdbcType=DECIMAL},
            #{item.craftSection,jdbcType=VARCHAR},#{item.currProcessCode,jdbcType=VARCHAR},
            #{item.lastProcess,jdbcType=VARCHAR},#{item.taskStatus,jdbcType=VARCHAR},#{item.factoryId,jdbcType=DECIMAL},
            #{item.itemName,jdbcType=VARCHAR},'Y',#{item.createBy,jdbcType=VARCHAR},sysdate,#{item.lastUpdatedBy,jdbcType=VARCHAR},
            sysdate,#{item.entityId,jdbcType=DECIMAL},#{item.orgId,jdbcType=DECIMAL},#{item.planTeam,jdbcType=VARCHAR},
            #{item.externalType,jdbcType=VARCHAR},#{item.draftedBy,jdbcType=VARCHAR},sysdate,#{item.reworkQty,jdbcType=DECIMAL},
            #{item.currentDayCompletedQty,jdbcType=DECIMAL},#{item.erpCompletedQty,jdbcType=DECIMAL},
            #{item.scheduledStartDate,jdbcType=TIMESTAMP},#{item.sourceSys,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <update id="batchUpdate" parameterType="java.util.List">
        <foreach collection="list" separator=";" item="item" >
            update WIP_INFO_STATISTICS t set
                t.ID=#{item.id,jdbcType=VARCHAR},
                t.TASK_NO= #{item.taskNo,jdbcType=VARCHAR},
                t.PRODPLAN_ID= #{item.prodplanId,jdbcType=VARCHAR},
                t.ITEM_CODE= #{item.itemCode,jdbcType=VARCHAR},
                t.TOTAL_NUMBER= #{item.totalNumber,jdbcType=DECIMAL},
                t.FINISHED_QTY=#{item.finishedQty,jdbcType=DECIMAL},
                t.CRAFT_SECTION=#{item.craftSection,jdbcType=VARCHAR},
                t.CURR_PROCESS_CODE=#{item.currProcessCode,jdbcType=VARCHAR},
                t.LAST_PROCESS=#{item.lastProcess,jdbcType=VARCHAR},
                t.TASK_STATUS=#{item.taskStatus,jdbcType=VARCHAR},
                t.FACTORY_ID=#{item.factoryId,jdbcType=DECIMAL},
                t.ENABLED_FLAG='Y',
                t.CREATE_BY=#{item.createBy,jdbcType=VARCHAR},
                t.CREATE_DATE=sysdate,
                t.LAST_UPDATED_BY=#{item.lastUpdatedBy,jdbcType=VARCHAR},
                t.LAST_UPDATED_DATE=sysdate,
                t.ENTITY_ID=#{item.entityId,jdbcType=DECIMAL},
                t.ORG_ID=#{item.orgId,jdbcType=DECIMAL},
                t.PLAN_TEAM=#{item.planTeam,jdbcType=VARCHAR},
                t.EXTERNAL_TYPE=#{item.externalType,jdbcType=VARCHAR},
                t.DRAFTED_BY=#{item.draftedBy,jdbcType=VARCHAR},
                t.RETRIEVAL_DATE=sysdate,
                t.REWORK_QTY=#{item.reworkQty,jdbcType=DECIMAL},
                t.CURRENT_DAY_COMPLETED_QTY=#{item.currentDayCompletedQty,jdbcType=DECIMAL}),
                t.ERP_COMPLETED_QTY=#{item.erpCompletedQty,jdbcType=DECIMAL},
                t.SCHEDULED_START_DATE=#{item.scheduledStartDate,jdbcType=TIMESTAMP},
                t.SOURCE_SYS=#{item.sourceSys,jdbcType=VARCHAR}
              where t.ID = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    

  	
    <select id="barcodeDistributionQueryByParam" parameterType="com.zte.springbootframe.common.model.Page" resultMap="barcodeDistributionQueryMap">
        select task_no,task_no as no,create_date,
        <foreach item="item" index="index0" collection="params.craftSectionList" separator=",">
            max(case when CRAFT_SECTION = #{params.craftSectionList[${index0}]} then count else '' end) "${item}"
        </foreach>
        from (
        select t.CRAFT_SECTION,t.task_no, to_char(t.CREATE_DATE, 'yyyy-mm-dd') as create_date,(Concat(Concat(sum(t.Total_Number),'/'),sum(t.Finished_Qty))) AS COUNT
        from wip_info_statistics t
        where 1=1
        <if test="params.taskNoList != null and params.taskNoList.size>0">
            and t.TASK_NO in
            <foreach collection="params.taskNoList" index="indexT" open="(" separator="," close=")">
                #{params.taskNoList[${indexT}]}
            </foreach>
        </if>
        <if test="params.taskNoOrProdPlanId != null and params.taskNoOrProdPlanId != ''">and t.TASK_NO like '%${params.taskNoOrProdPlanId}%' </if>
        <if test="params.planTeam != null and params.planTeam != ''">and t.PLAN_TEAM = #{params.planTeam}</if>
        <if test="params.itemNo != null and params.itemNo != ''">and t.ITEM_CODE = #{params.itemNo}</if>
        <if test="params.itemName != null and params.itemName != ''">and t.ITEM_NAME like '%${params.itemName}%'</if>
        <if test="params.externalType != null and params.externalType != ''">and t.EXTERNAL_TYPE = #{params.externalType}</if>
        <if test="params.planner != null and params.planner != ''">and (t.DRAFTED_BY = #{params.planner} or t.DRAFTED_BY =LTRIM(#{params.planner},'0'))</if>
        <if test="params.orgId != null and params.orgId != ''">and t.ORG_ID = #{params.orgId}::numeric</if>
        <if test="params.startDate != null">and t.CREATE_DATE >= #{params.startDate}</if>
        <if test="params.endDate != null"><![CDATA[ and t.CREATE_DATE <= #{params.endDate} ]]></if>
        <if test="params.sourceSys != null and params.sourceSys != ''">and t.SOURCE_SYS = #{params.sourceSys}</if>
        <if test="params.startDate == null or params.endDate == null">and 1=2 </if>
        group by t.task_no,t.CRAFT_SECTION,to_char(t.CREATE_DATE, 'yyyy-mm-dd')
        ) group by task_no,create_date,
        <foreach item="item" index="index1" collection="params.craftSectionList" separator=",">
            #{params.craftSectionList[${index1}]}
        </foreach>
        order by create_date
    </select>

  	<select id="barcodeDistributionQueryAllByParam" parameterType="com.zte.interfaces.dto.BarcodeDistributionQueryDTO" resultMap="barcodeDistributionQueryMap">
        select task_no,task_no as no,create_date,
        <foreach item="item" index="index0" collection="craftSectionList" separator=",">
            max(case when CRAFT_SECTION = #{craftSectionList[${index0}]} then count else '' end) "${item}"
        </foreach>
        from (
        select t.CRAFT_SECTION,t.task_no, to_char(t.CREATE_DATE, 'yyyy-mm-dd') as create_date,(Concat(Concat(sum(t.Total_Number),'/'),sum(t.Finished_Qty))) AS COUNT
        from wip_info_statistics t
        where 1=1
        <if test="taskNoList != null and taskNoList.size>0">
            and t.TASK_NO in
            <foreach collection="taskNoList" index="indexT" open="(" separator="," close=")">
                #{taskNoList[${indexT}]}
            </foreach>
        </if>
        <if test="taskNoOrProdPlanId != null and taskNoOrProdPlanId != ''">and t.TASK_NO  like '%${taskNoOrProdPlanId}%' </if>
        <if test="planTeam != null and planTeam != ''">and t.PLAN_TEAM = #{planTeam}</if>
        <if test="itemNo != null and itemNo != ''">and t.ITEM_CODE = #{itemNo}</if>
        <if test="itemName != null and itemName != ''">and t.ITEM_NAME like '%${itemName}%'</if>
        <if test="externalType != null and externalType != ''">and t.EXTERNAL_TYPE = #{externalType}</if>
        <if test="planner != null and planner != ''">and (t.DRAFTED_BY = #{planner} or t.DRAFTED_BY =LTRIM(#{planner},'0'))</if>
        <if test="orgId != null and orgId != ''">and t.ORG_ID = cast(#{orgId} as numeric)</if>
        <if test="startDate != null">and t.CREATE_DATE >= #{startDate}</if>
        <if test="endDate != null"><![CDATA[ and t.CREATE_DATE <= #{endDate} ]]></if>
        <if test="sourceSys != null and sourceSys != ''">and t.SOURCE_SYS = #{sourceSys}</if>
        <if test="startDate == null or endDate == null">and 1=2 </if>
        group by t.task_no,t.CRAFT_SECTION,to_char(t.CREATE_DATE, 'yyyy-mm-dd')
        ) group by task_no,create_date,
        <foreach item="item" index="index1" collection="craftSectionList" separator=",">
            #{craftSectionList[${index1}]}
        </foreach>
        order by create_date
  	</select>

    <insert id="updateOrInsertWipInfoStatistics" parameterType="java.util.List">
        with T as(
        <foreach collection = "list" item = "item" index = "index" separator = "UNION ALL">
            select
            #{item.id,jdbcType=VARCHAR} ID,
            #{item.taskNo,jdbcType=VARCHAR} TASK_NO,
            #{item.prodplanId,jdbcType=VARCHAR} PRODPLAN_ID,
            #{item.itemCode,jdbcType=VARCHAR} ITEM_CODE,
            #{item.totalNumber,jdbcType=DECIMAL}::numeric TOTAL_NUMBER,
            #{item.finishedQty,jdbcType=DECIMAL}::numeric FINISHED_QTY,
            #{item.craftSection,jdbcType=VARCHAR} CRAFT_SECTION,
            #{item.currProcessCode,jdbcType=VARCHAR} CURR_PROCESS_CODE,
            #{item.lastProcess,jdbcType=VARCHAR} LAST_PROCESS,
            #{item.taskStatus,jdbcType=VARCHAR} TASK_STATUS,
            #{item.factoryId,jdbcType=DECIMAL}::numeric FACTORY_ID,
            #{item.itemName,jdbcType=VARCHAR} ITEM_NAME,
            'Y' ENABLED_FLAG,
            #{item.createBy,jdbcType=VARCHAR} CREATE_BY,
            sysdate CREATE_DATE,
            #{item.lastUpdatedBy,jdbcType=VARCHAR} LAST_UPDATED_BY,
            sysdate LAST_UPDATED_DATE,
            #{item.entityId,jdbcType=DECIMAL}::numeric ENTITY_ID,
            #{item.orgId,jdbcType=DECIMAL}::numeric ORG_ID,
            #{item.planTeam,jdbcType=VARCHAR} PLAN_TEAM,
            #{item.externalType,jdbcType=VARCHAR} EXTERNAL_TYPE,
            #{item.draftedBy,jdbcType=VARCHAR} DRAFTED_BY,
            sysdate RETRIEVAL_DATE,
            #{item.reworkQty,jdbcType=DECIMAL}::numeric REWORK_QTY,
            #{item.currentDayCompletedQty,jdbcType=DECIMAL}::numeric CURRENT_DAY_COMPLETED_QTY,
            #{item.erpCompletedQty,jdbcType=DECIMAL}::numeric ERP_COMPLETED_QTY,
            #{item.scheduledStartDate,jdbcType=TIMESTAMP}::TIMESTAMP SCHEDULED_START_DATE,
            #{item.sourceSys,jdbcType=VARCHAR} SOURCE_SYS,
            #{item.envAttr,jdbcType=VARCHAR} ENV_ATTR,
            #{item.erpStatus,jdbcType=VARCHAR} ERP_STATUS
        </foreach>
        ),
        TEM as( update WIP_INFO_STATISTICS BI
         set    BI.CURR_PROCESS_CODE=T.CURR_PROCESS_CODE,
            BI.TOTAL_NUMBER=T.TOTAL_NUMBER ,
            BI.FINISHED_QTY=T.FINISHED_QTY,
            BI.TASK_STATUS=T.TASK_STATUS,
            BI.LAST_UPDATED_BY=T.LAST_UPDATED_BY,
            BI.LAST_UPDATED_DATE=sysdate,
            BI.DRAFTED_BY=T.DRAFTED_BY,
            BI.RETRIEVAL_DATE=sysdate,
            BI.REWORK_QTY=T.REWORK_QTY,
            BI.CURRENT_DAY_COMPLETED_QTY=T.CURRENT_DAY_COMPLETED_QTY,
            BI.ERP_COMPLETED_QTY=T.ERP_COMPLETED_QTY,
            BI.SCHEDULED_START_DATE=T.SCHEDULED_START_DATE,
            BI.ENV_ATTR=T.ENV_ATTR,
            BI.ERP_STATUS=T.ERP_STATUS
        from  T  where  BI.PRODPLAN_ID = T.PRODPLAN_ID AND BI.CRAFT_SECTION = T.CRAFT_SECTION
                     AND (trunc(BI.CREATE_DATE) = trunc(sysdate))
                             returning BI.* )
        INSERT into WIP_INFO_STATISTICS
        (ID,TASK_NO,PRODPLAN_ID,ITEM_CODE,TOTAL_NUMBER,FINISHED_QTY,CRAFT_SECTION,
        CURR_PROCESS_CODE,LAST_PROCESS,TASK_STATUS,FACTORY_ID,ITEM_NAME,ENABLED_FLAG,
        CREATE_BY,CREATE_DATE,LAST_UPDATED_BY,LAST_UPDATED_DATE, ENTITY_ID,ORG_ID,
        PLAN_TEAM,EXTERNAL_TYPE,DRAFTED_BY,RETRIEVAL_DATE,REWORK_QTY,CURRENT_DAY_COMPLETED_QTY,
        ERP_COMPLETED_QTY,SCHEDULED_START_DATE,SOURCE_SYS,ENV_ATTR,ERP_STATUS)
        select T.ID,T.TASK_NO,T.PRODPLAN_ID,T.ITEM_CODE,T.TOTAL_NUMBER,T.FINISHED_QTY,T.CRAFT_SECTION,
        T.CURR_PROCESS_CODE,T.LAST_PROCESS,T.TASK_STATUS,T.FACTORY_ID,T.ITEM_NAME,'Y',T.CREATE_BY,
        sysdate,T.LAST_UPDATED_BY,sysdate,T.ENTITY_ID,T.ORG_ID,T.PLAN_TEAM,T.EXTERNAL_TYPE,T.DRAFTED_BY,
        sysdate,T.REWORK_QTY,T.CURRENT_DAY_COMPLETED_QTY,T.ERP_COMPLETED_QTY,T.SCHEDULED_START_DATE,T.SOURCE_SYS,T.ENV_ATTR,T.ERP_STATUS
        from T where
        not exists ( select 1 from TEM  where  PRODPLAN_ID = T.PRODPLAN_ID AND CRAFT_SECTION = T.CRAFT_SECTION
        AND (trunc(CREATE_DATE) = trunc(sysdate)))
    </insert>



</mapper>
