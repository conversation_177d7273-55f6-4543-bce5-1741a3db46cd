package com.zte.autoTest.unitTest;

import com.zte.application.datawb.OtherSysProdBarcodeListService;
import com.zte.application.erpdt.impl.ZteWipMoveMtlTxnServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.domain.model.MtlSystemItemsBRepository;
import com.zte.domain.model.WarehouseEntryInfo;
import com.zte.domain.model.erpdt.ZteMrpWipIssue;
import com.zte.domain.model.erpdt.ZteMrpWipIssueRepository;
import com.zte.domain.model.erpdt.ZteWipMoveMtlTxn;
import com.zte.domain.model.erpdt.ZteWipMoveMtlTxnRepository;
import com.zte.interfaces.assembler.ZteWipMoveMtlTxnAssembler;
import com.zte.interfaces.dto.ZteWipMoveMtlTxnDTO;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.string.StringHelper;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.springbootframe.transaction.TransactionManagerHelper;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.mockito.Matchers.*;
import static org.mockito.Mockito.doReturn;
import static org.powermock.api.mockito.PowerMockito.mock;

/**
 * erp入库查询单元测试
 * <AUTHOR> 陈昭君
 */
@RunWith(PowerMockRunner.class)
public class ZteWipMoveMtlTxnServiceImplTest extends BaseTestCase {

    @InjectMocks
    private ZteWipMoveMtlTxnServiceImpl zteWipMoveMtlTxnServiceImpl;
    @Mock
    private ZteWipMoveMtlTxnRepository zteWipMoveMtlTxnRepository;

    @Mock
    private ZteMrpWipIssueRepository zteMrpWipIssueRepository;

    @Mock
    private MtlSystemItemsBRepository mtlSystemItemsBRepository;

    private RetCode retCode;

    private ServiceData serviceData;

    @Mock
    private TransactionManagerHelper transactionManagerHelper;

    @Mock
    private OtherSysProdBarcodeListService otherSysProdBarcodeListService;


    @Test
    @PrepareForTest({ServiceData.class,RetCode.class,ZteWipMoveMtlTxnAssembler.class,StringHelper.class,ZteWipMoveMtlTxnServiceImpl.class})
    public void getWarehouseQueryList() throws Exception {
        ZteWipMoveMtlTxnServiceImpl serivce = PowerMockito.spy(new ZteWipMoveMtlTxnServiceImpl());
        serivce.setZteWipMoveMtlTxnRepository(zteWipMoveMtlTxnRepository);
        serviceData= mock(ServiceData.class);
        retCode= mock(RetCode.class);
        PowerMockito.mockStatic(StringHelper.class);
        PowerMockito.whenNew(ServiceData.class).withAnyArguments().thenReturn(serviceData);
        PowerMockito.whenNew(RetCode.class).withArguments(anyString(),anyString()).thenReturn(retCode);
        PowerMockito.when(StringHelper.isEmpty(anyObject())).thenReturn(false);
        doReturn("0000").when(retCode).getCode();
        List<ZteWipMoveMtlTxn> wipmove = new ArrayList<ZteWipMoveMtlTxn>();
        PowerMockito.when(zteWipMoveMtlTxnRepository.getWarehouseQueryList(anyObject())).thenReturn(wipmove);
        List<ZteWipMoveMtlTxnDTO> wip = new ArrayList<ZteWipMoveMtlTxnDTO>();
        PowerMockito.mockStatic(ZteWipMoveMtlTxnAssembler.class);
        PowerMockito.when(ZteWipMoveMtlTxnAssembler.toZteWipMoveMtlTxnDTOList(wipmove)).thenReturn(wip);
        ZteWipMoveMtlTxnDTO conditions = new ZteWipMoveMtlTxnDTO();
        conditions.setPage(1);
        conditions.setRows(10);
        Assert.assertNotNull(serivce.getWarehouseQueryList(conditions));
    }

    @Test
    public void writeErpTransaction() throws Exception {
        WarehouseEntryInfo warehouseEntryInfo = new WarehouseEntryInfo();
        warehouseEntryInfo.setItemNo("123654789654569");
        BigDecimal itemId = new BigDecimal("1");
        ZteWipMoveMtlTxnServiceImpl serivce = PowerMockito.spy(new ZteWipMoveMtlTxnServiceImpl());
        serivce.setZteWipMoveMtlTxnRepository(zteWipMoveMtlTxnRepository);
        serivce.setZteMrpWipIssueRepository(zteMrpWipIssueRepository);
        serivce.setMtlSystemItemsBRepository(mtlSystemItemsBRepository);
        PowerMockito.when(zteWipMoveMtlTxnRepository.getWipMoveMtlByBillNo(anyObject())).thenReturn(null);
        PowerMockito.when(zteMrpWipIssueRepository.getMrpWipByBillNo(anyObject())).thenReturn(null);
        PowerMockito.when(mtlSystemItemsBRepository.getItemIdBySegment1(anyObject())).thenReturn(itemId);
        doReturn(1).when(serivce).insertWipMoveMtl(warehouseEntryInfo,itemId);
        doReturn(1).when(serivce).insertMrpWipIssue(warehouseEntryInfo,itemId);
        Assert.assertNotNull(serivce.writeErpTransaction(warehouseEntryInfo));
    }

    @Test
    public void insertWipMoveMtl() {
        WarehouseEntryInfo warehouseEntryInfo = new WarehouseEntryInfo();
        BigDecimal itemId = new BigDecimal("1");
        warehouseEntryInfo.setItemNo("123654789654569");
        warehouseEntryInfo.setOrgId(new BigDecimal("11"));
        warehouseEntryInfo.setCommitedQty(new BigDecimal(11));
        warehouseEntryInfo.setProdplanId("123456");
        warehouseEntryInfo.setCreateBy("123456");
        warehouseEntryInfo.setBillNo("213654657898");
        ZteWipMoveMtlTxnServiceImpl serivce = PowerMockito.spy(new ZteWipMoveMtlTxnServiceImpl());
        serivce.setZteWipMoveMtlTxnRepository(zteWipMoveMtlTxnRepository);
        PowerMockito.when(zteWipMoveMtlTxnRepository.insertZteWipMoveMtlTxnSelective(anyObject())).thenReturn(1);
        Assert.assertNotNull(serivce.insertWipMoveMtl(warehouseEntryInfo, itemId));
    }

    @Test
    public void insertMrpWipIssue() {
        WarehouseEntryInfo warehouseEntryInfo = new WarehouseEntryInfo();
        BigDecimal itemId = new BigDecimal("1");
        warehouseEntryInfo.setItemNo("123654789654569");
        warehouseEntryInfo.setOrgId(new BigDecimal("11"));
        warehouseEntryInfo.setCommitedQty(new BigDecimal(11));
        warehouseEntryInfo.setProdplanId("123456");
        warehouseEntryInfo.setCreateBy("123456");
        warehouseEntryInfo.setBillNo("213654657898");
        ZteWipMoveMtlTxnServiceImpl serivce = PowerMockito.spy(new ZteWipMoveMtlTxnServiceImpl());
        serivce.setZteMrpWipIssueRepository(zteMrpWipIssueRepository);
        PowerMockito.when(zteMrpWipIssueRepository.insertZteMrpWipIssueSelective(anyObject())).thenReturn(1);
        Assert.assertNotNull(serivce.insertMrpWipIssue(warehouseEntryInfo, itemId));
    }

    @Test
    @PrepareForTest({ZteWipMoveMtlTxnServiceImpl.class})
    public void writeErpForSubCard()throws Exception {
        WarehouseEntryInfo warehouseEntryInfo = new WarehouseEntryInfo();
        BigDecimal itemId = new BigDecimal("1");
        warehouseEntryInfo.setItemNo("123654789654569");
        warehouseEntryInfo.setOrgId(new BigDecimal("11"));
        warehouseEntryInfo.setCommitedQty(new BigDecimal(11));
        warehouseEntryInfo.setProdplanId("123456");
        warehouseEntryInfo.setCreateBy("123456");
        warehouseEntryInfo.setBillNo("213654657898");
        ZteWipMoveMtlTxnServiceImpl serivce = PowerMockito.spy(new ZteWipMoveMtlTxnServiceImpl());
        serivce.setZteMrpWipIssueRepository(zteMrpWipIssueRepository);
        serivce.setZteWipMoveMtlTxnRepository(zteWipMoveMtlTxnRepository);
        serivce.setMtlSystemItemsBRepository(mtlSystemItemsBRepository);
        PowerMockito.whenNew(TransactionManagerHelper.class).withAnyArguments().thenReturn(transactionManagerHelper);
        PowerMockito.when(zteWipMoveMtlTxnRepository.getWipMoveMtlByBillNo(anyObject())).thenReturn(new ZteWipMoveMtlTxn());
        PowerMockito.when(zteMrpWipIssueRepository.getMrpWipByBillNo(anyObject())).thenReturn(new ZteMrpWipIssue());
        PowerMockito.when(mtlSystemItemsBRepository.getItemIdBySegment1(anyObject())).thenReturn(BigDecimal.ZERO);
        serivce.writeErpForSubCard(warehouseEntryInfo);
        Assert.assertNotNull(warehouseEntryInfo);
    }


    @Test
    public void onlyWriteErp() {
        try {
            zteWipMoveMtlTxnServiceImpl.onlyWriteErp(new WarehouseEntryInfo(){{setItemNo("1111111111111111");}});
        } catch (Exception e) {
            e.printStackTrace();
            Assert.assertEquals(MessageId.ITEM_ID_FOUND_ERROR,e.getMessage());
        }
        try {
            PowerMockito.when(mtlSystemItemsBRepository.getItemIdBySegment1(any())).thenReturn(BigDecimal.ONE);
            PowerMockito.when(zteWipMoveMtlTxnRepository.insertZteWipMoveMtlTxnSelective(any())).thenReturn(0);
            zteWipMoveMtlTxnServiceImpl.onlyWriteErp(new WarehouseEntryInfo(){{
                setItemNo("1111111111111111");
                setBillNo("12123123");
                setLastUpdatedBy("1");
                setCreateBy("1");
            }});
        } catch (Exception e) {
            Assert.assertEquals(MessageId.WIP_MOVE_MTL_INSERT_ERROR,e.getMessage());
        }
    }
}
