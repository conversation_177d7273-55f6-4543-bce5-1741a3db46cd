package com.zte.application.impl;

import com.alibaba.fastjson.JSONObject;
import com.zte.application.AssemblyOptRecordService;
import com.zte.common.CommonUtils;
import com.zte.common.DateUtil;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.NumConstant;
import com.zte.common.utils.SqlUtils;
import com.zte.domain.model.*;
import com.zte.infrastructure.remote.*;
import com.zte.interfaces.dto.*;
import com.zte.springbootframe.util.EmailUtils;
import org.springframework.data.util.Pair;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.springbootframe.common.redis.RedisLock;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.zte.common.utils.Constant.*;

@Slf4j
@Service("assemblyOptRecordService")
public class AssemblyOptRecordServiceImpl implements AssemblyOptRecordService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private AssemblyOptRecordRepository assemblyOptRecordrepository;

    @Autowired
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Autowired
    private WipEntityScanInfoServiceImpl wipEntityScanInfoService;

    @Autowired
    private EmailUtils emailUtils;

    @Autowired
    private WipExtendIdentificationServiceImpl wipExtendIdentificationService;
    @Autowired
    private WipExtendIdentificationRepository wipExtendIdentificationRepository;
    @Autowired
    private BarcodeCenterRemoteService barcodeCenterRemoteService;
    @Autowired
    private DatawbRemoteService datawbRemoteService;

    @Override
    public Page<AssemblyOptRecordEntityDTO> getPageListByParams(AssemblyRelationshipQueryDTO dto) throws Exception {
        wipExtendIdentificationService.parameterVerification(dto);
        Page<AssemblyOptRecordEntityDTO> page = new Page<>(dto.getPage(), dto.getRows());
        Page<AssemblyRelationshipQueryDTO> pageInfo = new Page<>(dto.getPage(), dto.getRows());
        page.setTotalPage(pageInfo.getTotalPage());
        page.setPageSize(pageInfo.getPageSize());
        page.setCurrent(pageInfo.getCurrent());
        pageInfo.setParams(dto);
        int total = assemblyOptRecordrepository.getPageListCountByParams(dto);
        if (total <= INT_0) {
            return page;
        }
        page.setTotal(total);
        List<AssemblyOptRecordEntityDTO> optRecordEntityDTOList = assemblyOptRecordrepository.getPageListByParams(dto);
        if (CollectionUtils.isEmpty(optRecordEntityDTOList)) {
            return page;
        }
        //设置绑定类型 子工序 工站名称
        this.setProcessNameAndWorkStationName(optRecordEntityDTOList);
        //设置创建人
        this.setEmpName(optRecordEntityDTOList);
        //操作类型 1:绑定 2:解绑 3 置换
        setOptTypeName(optRecordEntityDTOList);
        page.setRows(optRecordEntityDTOList);
        return page;
    }

    public List<AssemblyOptRecordEntityDTO> getListBySn(String sn) throws Exception {
        List<AssemblyOptRecordEntityDTO> assemblyOptRecordEntityDTOList = assemblyOptRecordrepository.getListBySn(sn);
        if (CollectionUtils.isEmpty(assemblyOptRecordEntityDTOList)) {
            return assemblyOptRecordEntityDTOList;
        }
        //设置绑定类型 子工序 工站名称
        this.setProcessNameAndWorkStationName(assemblyOptRecordEntityDTOList);
        //设置创建人
        this.setEmpName(assemblyOptRecordEntityDTOList);
        //操作类型 1:绑定 2:解绑 3 置换
        setOptTypeName(assemblyOptRecordEntityDTOList);
        return assemblyOptRecordEntityDTOList;
    }

    /**
     * 转换操作类型
     *
     * @param assemblyOptRecordEntityDTOList
     */
    private void setOptTypeName(List<AssemblyOptRecordEntityDTO> assemblyOptRecordEntityDTOList) {
        for (AssemblyOptRecordEntityDTO assemblyOptRecordEntityDTO : assemblyOptRecordEntityDTOList) {
            if (StringUtils.equals(assemblyOptRecordEntityDTO.getOptType(), Constant.STR_1)) {
                assemblyOptRecordEntityDTO.setOptTypeName(Constant.OPT_TYPE_1);
            } else if (StringUtils.equals(assemblyOptRecordEntityDTO.getOptType(), Constant.STR_2)) {
                assemblyOptRecordEntityDTO.setOptTypeName(Constant.OPT_TYPE_2);
            } else if (StringUtils.equals(assemblyOptRecordEntityDTO.getOptType(), Constant.STR_3)) {
                assemblyOptRecordEntityDTO.setOptTypeName(Constant.OPT_TYPE_3);
            }
        }
    }


    /**
     * 设置姓名
     *
     * @param assemblyOptRecordEntityDTOList
     * @throws Exception
     */
    public void setEmpName(List<AssemblyOptRecordEntityDTO> assemblyOptRecordEntityDTOList) throws Exception {
        //创建人
        List<String> createByList = assemblyOptRecordEntityDTOList.stream().filter(e -> StringUtils.isNotEmpty(e.getCreateBy())).map(e -> e.getCreateBy()).distinct().collect(Collectors.toList());
        Map<String, HrmPersonInfoDTO> hrmPersonInfoDTOMap = centerfactoryRemoteService.getHrmPersonInfo(createByList);
        for (AssemblyOptRecordEntityDTO assemblyOptRecordEntityDTO : assemblyOptRecordEntityDTOList) {
            HrmPersonInfoDTO hrmPersonInfoDTO = hrmPersonInfoDTOMap.get(assemblyOptRecordEntityDTO.getCreateBy());
            assemblyOptRecordEntityDTO.setCreateByName(assemblyOptRecordEntityDTO.getCreateBy());
            if (hrmPersonInfoDTO != null) {
                assemblyOptRecordEntityDTO.setCreateByName(hrmPersonInfoDTO.getEmpName() + assemblyOptRecordEntityDTO.getCreateBy());
            }
        }
    }

    /**
     * 设置工装 子工序 绑定类型
     *
     * @param assemblyOptRecordEntityDTOList
     * @throws Exception
     */
    public void setProcessNameAndWorkStationName(List<AssemblyOptRecordEntityDTO> assemblyOptRecordEntityDTOList) throws Exception {
        List<String> processAndWorkStationList = assemblyOptRecordEntityDTOList.stream().flatMap(p -> Stream.of(p.getProcessCode(), p.getWorkStation())).distinct().collect(Collectors.toList());
        List<BSProcessDTO> bsProcessDTOList = wipEntityScanInfoService.getProcessInfo(null, null, null, null, SqlUtils.convertStrCollectionToSqlType(processAndWorkStationList));
        //按主条码分组
        Map<String, BSProcessDTO> wipExtendIdentificationMap = CollectionUtils.isEmpty(bsProcessDTOList) ? new HashMap<>() : bsProcessDTOList.stream().collect(Collectors.toMap(k -> k.getProcessCode(), v -> v, (oldValue, newValue) -> newValue));

        List<SysLookupTypesDTO> sysLookupTypesDTOList = BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_BIND_TYPE);

        for (AssemblyOptRecordEntityDTO assemblyOptRecordEntityDTO : assemblyOptRecordEntityDTOList) {
            BSProcessDTO bsProcessDTO = wipExtendIdentificationMap.get(assemblyOptRecordEntityDTO.getProcessCode());
            if (bsProcessDTO != null) {
                assemblyOptRecordEntityDTO.setProcessName(bsProcessDTO.getProcessName());
            }
            bsProcessDTO = wipExtendIdentificationMap.get(assemblyOptRecordEntityDTO.getWorkStation());
            if (bsProcessDTO != null) {
                assemblyOptRecordEntityDTO.setWorkStationName(bsProcessDTO.getProcessName());
            }
            assemblyOptRecordEntityDTO.setFormTypeName(this.getFormType(sysLookupTypesDTOList, assemblyOptRecordEntityDTO.getFormType()));
        }
    }

    /**
     * 获取绑定类型
     *
     * @param sysLookupTypesDTOList
     * @param formType
     * @return
     */
    private String getFormType(List<SysLookupTypesDTO> sysLookupTypesDTOList, String formType) {
        for (SysLookupTypesDTO lookupTypesDTO : sysLookupTypesDTOList) {
            if (!StringUtils.isEmpty(lookupTypesDTO.getLookupMeaning()) && !StringUtils.isEmpty(formType)
                    && formType.equals(lookupTypesDTO.getLookupMeaning())) {
                return lookupTypesDTO.getDescriptionChinV();
            }
        }
        return "";
    }

    /**
     * 批量新增
     *
     * @param list
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchInsert(List<AssemblyOptRecordEntityDTO> list) {
        int result = NumConstant.NUM_ZERO;
        for (List<AssemblyOptRecordEntityDTO> assemblyOptRecordEntityDTOS : CommonUtils.splitList(list, Constant.INT_100)) {
            result += assemblyOptRecordrepository.batchInsert(assemblyOptRecordEntityDTOS);
        }
        return result;
    }

    /**
     * 定时任务-推送装配关系到MES
     * 1.获取数据字典上一次推送的时间
     * 2.加锁
     * 3.获取时间跨度内的装配关系操作记录
     * 4.根据操作类型分组（绑定/解绑/置换）
     * 5.批量查询MES是否存在条码装配关系的数据
     * 6.组装需要推送MES的数据
     * 7.执行删除/写入
     * 8.记录执行时间
     *
     * @param param
     * @return
     * @throws Exception
     */
    @Override
    public void sendAssemblyRelationToMes(AssemblyRelationshipQueryDTO param, String factoryId) throws Exception {
        String key = Constant.ASSEMBLY_SEND_MES_LOCK + factoryId;
        RedisLock redisLock = new RedisLock(key);
        if (!redisLock.lock()) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.FAILED_TO_GET_REDIS_LOCK);
        }
        try {
            List<WipExtendIdentification> errorBuildList = new ArrayList<>();
            AssemblyRelationshipQueryDTO dto = getQueryDTO(param);
            //整机组装绑定数据字典1210
            dto.setFormType(STR_2);
            dto.setPage(NumConstant.NUM_ONE);
            dto.setRows(NumConstant.NUM_500);
            AssemblyOptRecordEntityDTO assemblyTemp = new AssemblyOptRecordEntityDTO();
            Page<AssemblyRelationshipQueryDTO> pageInfo = new Page<>(dto.getPage(), dto.getRows());
            while (true) {
                pageInfo.setParams(dto);
                pageInfo.setSearchCount(false);
                List<AssemblyOptRecordEntityDTO> optPageList = assemblyOptRecordrepository.getListByParams(pageInfo);
                if (CollectionUtils.isEmpty(optPageList)) {
                    break;
                }
                List<AssemblyOptRecordEntityDTO> optList = new ArrayList<>();
                Map<String, List<AssemblyOptRecordEntityDTO>> optMap = optPageList.stream()
                        .collect(Collectors.groupingBy(item -> item.getFormSn() + item.getSn()));
                for (Map.Entry<String,List<AssemblyOptRecordEntityDTO>> optEntry: optMap.entrySet()) {
                    List<AssemblyOptRecordEntityDTO> tempList = optEntry.getValue();
                    AssemblyOptRecordEntityDTO assemblyOptRecordEntityDTO = tempList.stream()
                            .sorted(Comparator.comparing(AssemblyOptRecordEntityDTO::getCreateDate, Comparator.nullsLast(Comparator.naturalOrder()))
                                    .reversed()).collect(Collectors.toList()).get(NumConstant.NUM_ZERO);
                    optList.add(assemblyOptRecordEntityDTO);
                }
                List<PsTask> psTaskList = getTasksWithTaskNo(optList);
                // 分情况组装数据
                List<WipExtendIdentification> addWipList = new ArrayList<>();
                // 根据新增的绑定操作记录获得这些主子条码的装配关系
                getAddWipExtendList(addWipList, optList);
                // 置换的操作记录需要找到置换后的主子条码装配关系
                getReplaceExtendList(addWipList, optList);
                // 新增及置换后的装配关系需要推送MES，在此组装
                Pair<List<AssemblyWriteBackDto>, List<WipExtendIdentification>> pair = buildAddAssemblyToMES(psTaskList, addWipList);
                List<AssemblyWriteBackDto> writeDtoList = pair.getFirst();
                List<WipExtendIdentification> errorBuildTemp = pair.getSecond();
                // 组装需要解除装配关系的数据
                List<AssemblyWriteBackDto> removeDtoList = buildDeleteAssemblyToMES(optList);
                SendAssemblyToMesDTO sendAssemblyToMesDTO =new SendAssemblyToMesDTO();
                sendAssemblyToMesDTO.setInsertList(writeDtoList);
                sendAssemblyToMesDTO.setDeleteList(removeDtoList);
                datawbRemoteService.batchInsertOrDeleteAssemble(sendAssemblyToMesDTO);
                // 异常推送数据集中
                if (!CollectionUtils.isEmpty(errorBuildTemp)) {
                    log.info("装配关系推送MES-单次循环的异常数据：", JSONObject.toJSONString(errorBuildTemp));
                    if (errorBuildList.size() < NumConstant.NUM_1000) {
                        errorBuildList.addAll(errorBuildTemp);
                    }
                }
                assemblyTemp = optList.get(optList.size() - 1);
                String lastUpdatedTime = DateUtil.convertDateToString(assemblyTemp.getCreateDate(), DateUtil.DATE_FORMATE_FULL);
                BasicsettingRemoteService.updateSysLookupValuesMeaning(new BigDecimal(Constant.LOOKUP_CODE_286516001), lastUpdatedTime);
                dto.setStartTime(assemblyTemp.getCreateDate());
                dto.setId(assemblyTemp.getId());
            }
            // 异常推送数据发送邮件
            if (!CollectionUtils.isEmpty(errorBuildList)) {
                sendEmailForFail(errorBuildList);
            }
        } finally {
            redisLock.unlock();
        }
    }

    public AssemblyRelationshipQueryDTO getQueryDTO(AssemblyRelationshipQueryDTO dto) throws Exception {
        SysLookupTypesDTO sysLookupTypesDTO = BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_286516, Constant.LOOKUP_CODE_286516001);
        if (sysLookupTypesDTO == null || StringUtils.isEmpty(sysLookupTypesDTO.getLookupMeaning())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SYS_LOOK_NOT_CONFIG, new Object[]{Constant.LOOKUP_CODE_286516001});
        }
        int forwardMinus = StringUtils.isEmpty(sysLookupTypesDTO.getAttribute1())? NumConstant.NUM_MINUS_FIFTEEN : Integer.parseInt(sysLookupTypesDTO.getAttribute1());
        int timeRange = StringUtils.isEmpty(sysLookupTypesDTO.getAttribute2())? NumConstant.NUM_FIVE : Integer.parseInt(sysLookupTypesDTO.getAttribute2());
        if (StringUtils.isNotEmpty(dto.getTaskNo()) || (!Objects.isNull(dto.getStartTime()) && !Objects.isNull(dto.getEndTime()))) {
            Date forwardDateTime = dto.getStartTime();
            Date currDate = dto.getEndTime();
            // 如果时间跨度大于5天，限制单次执行最大5天
            if(DateUtil.caldaysByDate(currDate,forwardDateTime,Constant.INT_0).compareTo(new BigDecimal(timeRange)) > NumConstant.NUM_ZERO){
                currDate = DateUtil.addDay(forwardDateTime,timeRange);
            }
            dto.setStartTime(forwardDateTime);
            dto.setEndTime(currDate);
            return dto;
        } else {
            String forwardTimeStr = sysLookupTypesDTO.getLookupMeaning();
            Date forwardDateTime = DateUtil.convertStringToDate(forwardTimeStr, DateUtil.DATE_FORMATE_FULL);
            Date forwardDateTimeBack = DateUtil.addDateTimeSecond(forwardDateTime, forwardMinus);
            dto.setStartTime(forwardDateTimeBack);
        }
        return dto;
    }

    private List<PsTask> getTasksWithTaskNo(List<AssemblyOptRecordEntityDTO> optList) {
        List<PsTask> psTaskList = new ArrayList<>();
        List<String> taskNoList = optList.stream().map(AssemblyOptRecordEntityDTO::getTaskNo).distinct().collect(Collectors.toList());
        List<List<String>> splitList = CommonUtils.splitList(taskNoList, NumConstant.NUM_999);
        for (List<String> tempList : splitList) {
            List<PsTask> tempPaskList = PlanscheduleRemoteService.getPsTaskByTaskNoList(tempList);
            if (!CollectionUtils.isEmpty(tempPaskList)) {
                psTaskList.addAll(tempPaskList);
            }
        }
        return psTaskList;
    }

    public void sendEmailForFail(List<WipExtendIdentification> errorBuildList) throws Exception {
        List<WipExtendIdentification> errorForExisted = errorBuildList.stream().filter(w -> Constant.SENT_ASSEMBLY_TO_MES_EXIST.equals(w.getRemark())).collect(Collectors.toList());
        List<WipExtendIdentification> errorForOther = errorBuildList.stream().filter(w -> !Constant.SENT_ASSEMBLY_TO_MES_EXIST.equals(w.getRemark())).collect(Collectors.toList());
        StringBuilder contentCnForSendEmail = new StringBuilder("<br/><div>");
        if (!CollectionUtils.isEmpty(errorForOther)) {
            contentCnForSendEmail.append("<p style='margin:0;font-size:13pt'>" + Constant.ASSEMBLY_SEND_EMAIL_CN_PREFIX + "</p>");
            contentCnForSendEmail.append("<p style='margin:0;font-size:13pt'>" + EMPTY_STRING + "</p>");
            for (WipExtendIdentification wipExtendIdentification : errorForOther) {
                contentCnForSendEmail.append("<p style='margin:0;font-size:10pt'>" +
                        Constant.ASSEMBLY_TASK_NO + Constant.COLON + wipExtendIdentification.getTaskNo() + Constant.COMMA +
                        Constant.ASSEMBLY_MAIN_SN + Constant.COLON + wipExtendIdentification.getFormSn() + Constant.COMMA +
                        Constant.ASSEMBLY_MAIN_SN_ITEM + Constant.COLON + wipExtendIdentification.getFormItemNo() + Constant.COMMA +
                        Constant.ASSEMBLY_SUB_SN + Constant.COLON + wipExtendIdentification.getSn() + Constant.COMMA +
                        Constant.ASSEMBLY_SUB_SN_ITEM + Constant.COLON + wipExtendIdentification.getItemNo() + Constant.COMMA +
                        Constant.SUB_SN_CREATE_BY + Constant.COLON + wipExtendIdentification.getCreateBy() + Constant.SEMICOLON + "</p>");
            }
            contentCnForSendEmail.append("<p style='margin:0;font-size:13pt'>" + EMPTY_STRING + "</p>");
            contentCnForSendEmail.append(Constant.ASSEMBLY_SEND_EMAIL_CN_SUFFIX);
        }
        if (!CollectionUtils.isEmpty(errorForExisted)) {
            contentCnForSendEmail.append("<p style='margin:0;font-size:13pt'>" + Constant.ASSEMBLY_SEND_EMAIL_CN_FOR_EXIST_PREFIX + "</p>");
            contentCnForSendEmail.append("<p style='margin:0;font-size:13pt'>" + EMPTY_STRING + "</p>");
            for (WipExtendIdentification wipExtendIdentification : errorForExisted) {
                contentCnForSendEmail.append("<p style='margin:0;font-size:10pt'>" +
                        Constant.ASSEMBLY_TASK_NO + Constant.COLON + wipExtendIdentification.getTaskNo() + Constant.COMMA +
                        Constant.ASSEMBLY_MAIN_SN + Constant.COLON + wipExtendIdentification.getFormSn() + Constant.COMMA +
                        Constant.ASSEMBLY_MAIN_SN_ITEM + Constant.COLON + wipExtendIdentification.getFormItemNo() + Constant.COMMA +
                        Constant.ASSEMBLY_SUB_SN + Constant.COLON + wipExtendIdentification.getSn() + Constant.COMMA +
                        Constant.ASSEMBLY_SUB_SN_ITEM + Constant.COLON + wipExtendIdentification.getItemNo() + Constant.SEMICOLON + "</p>");
            }
            contentCnForSendEmail.append("<p style='margin:0;font-size:13pt'>" + EMPTY_STRING + "</p>");
            contentCnForSendEmail.append(Constant.ASSEMBLY_SEND_EMAIL_CN_SUFFIX);
        }
        //需要处理的人发送邮件
        StringBuilder currentHandlerSb = new StringBuilder();
        SysLookupTypesDTO sysLookupTypesDTO = BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_286517, Constant.LOOKUP_CODE_286517001);
        if (null != sysLookupTypesDTO && StringUtils.isNotEmpty(sysLookupTypesDTO.getLookupMeaning())) {
            String currentHandler = sysLookupTypesDTO.getLookupMeaning();
            String[] currentHandlerArr = currentHandler.split(Constant.COMMA);
            for (String currentHandlerStr : currentHandlerArr) {
                currentHandlerSb.append(currentHandlerStr).append(Constant.MAILBOX_SUFFIX).append(Constant.SEMICOLON);
            }
            emailUtils.sendMail(currentHandlerSb.toString(), Constant.ASSEMBLY_SEND_EMAIL_TITLE, "", contentCnForSendEmail.toString(), "");
        }
    }

    public void getReplaceExtendList(List<WipExtendIdentification> addWipList, List<AssemblyOptRecordEntityDTO> optList) {
        List<AssemblyOptRecordEntityDTO> replaceOptList = optList.stream().filter(o -> Constant.STR_3.equals(o.getOptType())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(replaceOptList)) {
            List<String> replaceSubItemList = replaceOptList.stream().map(AssemblyOptRecordEntityDTO::getSubItemNo).distinct().collect(Collectors.toList());
            List<String> replaceFormSnList = replaceOptList.stream().map(AssemblyOptRecordEntityDTO::getFormSn).distinct().collect(Collectors.toList());
            List<WipExtendIdentification> replaceWipList = wipExtendIdentificationRepository.getWipExtendBySubItemNoAndFormSn(replaceSubItemList, replaceFormSnList);
            for (AssemblyOptRecordEntityDTO assemblyOptRecordEntityDTO : replaceOptList) {
                WipExtendIdentification wipExTemp = replaceWipList.stream()
                        .filter(r -> assemblyOptRecordEntityDTO.getFormSn().equals(r.getFormSn()) && assemblyOptRecordEntityDTO.getSubItemNo().equals(r.getItemNo()))
                        .findFirst().orElse(null);
                if (null != wipExTemp) {
                    wipExTemp.setLastUpdatedBy(assemblyOptRecordEntityDTO.getCreateBy());
                    addWipList.add(wipExTemp);
                }
            }
        }
    }

    public void getAddWipExtendList(List<WipExtendIdentification> addWipList, List<AssemblyOptRecordEntityDTO> optList) {
        List<AssemblyOptRecordEntityDTO> addOptList = optList.stream().filter(o -> Constant.STR_1.equals(o.getOptType())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(addOptList)) {
            List<String> addFormSnList = addOptList.stream().map(AssemblyOptRecordEntityDTO::getFormSn).distinct().collect(Collectors.toList());
            List<String> addSnList = addOptList.stream().map(AssemblyOptRecordEntityDTO::getSn).distinct().collect(Collectors.toList());
            List<WipExtendIdentification> wipExtendListForAdd = wipExtendIdentificationRepository.getWipExtendBySnAndFormSn(addSnList, addFormSnList);
            for (AssemblyOptRecordEntityDTO assemblyOptRecordEntityDTO : addOptList) {
                WipExtendIdentification wipExTemp = wipExtendListForAdd.stream()
                        .filter(r -> assemblyOptRecordEntityDTO.getFormSn().equals(r.getFormSn()) && assemblyOptRecordEntityDTO.getSn().equals(r.getSn()))
                        .findFirst().orElse(null);
                if (null != wipExTemp) {
                    wipExTemp.setLastUpdatedBy(assemblyOptRecordEntityDTO.getCreateBy());
                    addWipList.add(wipExTemp);
                }
            }
        }
    }

    public List<AssemblyWriteBackDto> buildDeleteAssemblyToMES(List<AssemblyOptRecordEntityDTO> optList) {
        List<AssemblyOptRecordEntityDTO> removeOptList = optList.stream()
                .filter(o -> Constant.STR_2.equals(o.getOptType()) || Constant.STR_3.equals(o.getOptType()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(removeOptList)) {
            return new ArrayList<>();
        }
        Map<String, List<AssemblyOptRecordEntityDTO>> removeMap = removeOptList.stream().collect(Collectors.groupingBy(AssemblyOptRecordEntityDTO::getFormSn));
        List<AssemblyWriteBackDto> removeDtoList = new ArrayList<>();
        for (String removeFormSn : removeMap.keySet()) {
            AssemblyWriteBackDto removeWrite = new AssemblyWriteBackDto();
            WsmAssembleHeaders removeMainSn = new WsmAssembleHeaders();
            List<WsmAssembleLinesWriteBack> removeSubSnList = new ArrayList<>();
            removeMainSn.setItemBarcode(removeFormSn);
            List<AssemblyOptRecordEntityDTO> removeSnList = removeMap.get(removeFormSn);
            if (CollectionUtils.isEmpty(removeSnList)) {
                continue;
            }
            for (AssemblyOptRecordEntityDTO assemblyOptRecordEntityDTO : removeSnList) {
                WsmAssembleLinesWriteBack wsmAssembleLinesWriteBack = new WsmAssembleLinesWriteBack();
                wsmAssembleLinesWriteBack.setItemBarcode(assemblyOptRecordEntityDTO.getSn());
                removeSubSnList.add(wsmAssembleLinesWriteBack);
            }
            removeWrite.setMainSn(removeMainSn);
            removeWrite.setSubSnList(removeSubSnList);
            removeDtoList.add(removeWrite);
        }
        return removeDtoList;
    }

    /**
     * 在此组装需要推送MES的数据
     *
     * @param psTaskList
     * @param addWipList
     * @return
     * @throws Exception
     */
    public Pair<List<AssemblyWriteBackDto>, List<WipExtendIdentification>> buildAddAssemblyToMES(List<PsTask> psTaskList, List<WipExtendIdentification> addWipList) throws Exception {
        if (CollectionUtils.isEmpty(addWipList)) {
            return Pair.of(new ArrayList<>(), new ArrayList<>());
        }
        List<AssemblyWriteBackDto> writeDtoList = new ArrayList<>();
        setAttributeFromPsTask(psTaskList, addWipList);
        List<MtlSystemItemsDTO> mtlSystemItemsDTOS = getMtlItemsFromMES(addWipList);
        List<BsItemInfo> bsItemInfoList = getBsItemInfoList(addWipList);
        if (Objects.isNull(bsItemInfoList)) {
            bsItemInfoList = new LinkedList<>();
        }
        Pair<List<BarcodeExpandDTO>, List<WsmAssembleLinesDTO>> pairOfBarcode = getBarcodeInfo(addWipList);
        List<BarcodeExpandDTO> barcodeExpandDTOList = pairOfBarcode.getFirst();
        List<WsmAssembleLinesDTO> wsmAssembleLinesDTOS = pairOfBarcode.getSecond();
        Map<String, List<WipExtendIdentification>> buildMap = addWipList.stream().collect(Collectors.groupingBy(WipExtendIdentification::getFormSn));
        // 组装新增数据
        List<WipExtendIdentification> errorBuildInfo = new ArrayList<>();
        for (String mainBarcode : buildMap.keySet()) {
            AssemblyWriteBackDto writeDto = new AssemblyWriteBackDto();
            List<WipExtendIdentification> subBarcodeList = buildMap.get(mainBarcode).stream()
                    .sorted(Comparator.comparing(WipExtendIdentification::getLastUpdatedDate, Comparator.nullsLast(Comparator.naturalOrder())))
                    .collect(Collectors.toList());
            WipExtendIdentification wipMain = subBarcodeList.get(NumConstant.NUM_ZERO);
            WsmAssembleHeaders mainSn = buildMainSn(mtlSystemItemsDTOS, bsItemInfoList, barcodeExpandDTOList, mainBarcode, wipMain);
            if (null == mainSn.getItemId()) {
                errorBuildInfo.addAll(subBarcodeList);
                continue;
            }
            List<WsmAssembleLinesWriteBack> subSnList = new ArrayList<>();
            for (WipExtendIdentification subWip : subBarcodeList) {
                String subItemBarcode = subWip.getSn();
                if (existedInMES(wsmAssembleLinesDTOS, mainBarcode, subItemBarcode)) {
                    subWip.setRemark(Constant.SENT_ASSEMBLY_TO_MES_EXIST);
                    errorBuildInfo.add(subWip);
                    continue;
                }
                WsmAssembleLinesWriteBack subSn = buildAssembleLinesWriteBack(mtlSystemItemsDTOS, bsItemInfoList, barcodeExpandDTOList, subWip);
                if (null == subSn.getItemId() || null == subSn.getCreatedBy()) {
                    errorBuildInfo.add(subWip);
                    continue;
                }
                subSnList.add(subSn);
            }
            this.setSomeProperties(writeDtoList, writeDto, mainSn, subSnList);
        }
        return Pair.of(writeDtoList, errorBuildInfo);
    }

    public Pair<List<BarcodeExpandDTO>, List<WsmAssembleLinesDTO>> getBarcodeInfo(List<WipExtendIdentification> addWipList) throws Exception {
        if (CollectionUtils.isEmpty(addWipList)) {
            return Pair.of(new ArrayList<>(), new ArrayList<>());
        }
        List<BarcodeExpandDTO> barcodeExpandDTOList = new ArrayList<>();
        List<WsmAssembleLinesDTO> wsmAssembleLinesDTOS = new ArrayList<>();
        List<List<WipExtendIdentification>> splitAddWipList = CommonUtils.splitList(addWipList, NumConstant.NUM_100);
        for (List<WipExtendIdentification> wipList : splitAddWipList) {
            List<String> formSnList = new ArrayList<>();
            List<String> snList = new ArrayList<>();
            List<String> fullBarcodeList = new ArrayList<>();
            for (WipExtendIdentification wipTemp : wipList) {
                String formSn = wipTemp.getFormSn();
                String sn = wipTemp.getSn();
                String fullBarcode =formSn + Constant.UNDER_LINE + sn;
                formSnList.add(formSn);
                snList.add(sn);
                fullBarcodeList.add(fullBarcode);
            }
            List<BarcodeExpandDTO> barcodeExpandDTOTemp = getBarcodeExpandList(formSnList, snList);
            if (!CollectionUtils.isEmpty(barcodeExpandDTOTemp)) {
                barcodeExpandDTOList.addAll(barcodeExpandDTOTemp);
            }
            WsmAssembleLinesDTO paramDTO = new WsmAssembleLinesDTO();
            paramDTO.setMainItemBarcodeList(formSnList);
            paramDTO.setSubItemBarcodeList(snList);
            paramDTO.setFullBarcodeList(fullBarcodeList);
            List<WsmAssembleLinesDTO> wmsTemp = datawbRemoteService.getWsmAssemblyListByMainAndSub(paramDTO);
            if (!CollectionUtils.isEmpty(wmsTemp)) {
                wsmAssembleLinesDTOS.addAll(wmsTemp);
            }
        }
        return Pair.of(barcodeExpandDTOList, wsmAssembleLinesDTOS);
    }

    private void setSomeProperties(List<AssemblyWriteBackDto> writeDtoList, AssemblyWriteBackDto writeDto, WsmAssembleHeaders mainSn, List<WsmAssembleLinesWriteBack> subSnList) {
        if (!CollectionUtils.isEmpty(subSnList)) {
            writeDto.setMainSn(mainSn);
            writeDto.setSubSnList(subSnList);
            writeDtoList.add(writeDto);
        }
    }

    public boolean existedInMES(List<WsmAssembleLinesDTO> wsmAssembleLinesDTOS, String mainBarcode, String subItemBarcode) {
        WsmAssembleLinesDTO wsmExited = wsmAssembleLinesDTOS.stream()
                .filter(w -> mainBarcode.equals(w.getMainItemBarcode()) && subItemBarcode.equals(w.getSubItemBarcode()))
                .findFirst().orElse(null);
        if (null != wsmExited) {
            return true;
        }
        return false;
    }

    private void setAttributeFromPsTask(List<PsTask> psTaskList, List<WipExtendIdentification> addWipList) {
        for (WipExtendIdentification wipExtendIdentification : addWipList) {
            PsTask psTask = psTaskList.stream().filter(p -> wipExtendIdentification.getTaskNo().equals(p.getTaskNo())).findFirst().orElse(null);
            if (null != psTask) {
                wipExtendIdentification.setFormItemNo(psTask.getItemNo());
                wipExtendIdentification.setOrgId(psTask.getOrgId());
                wipExtendIdentification.setFormCatogary(psTask.getExternalType());
            }
        }
    }

    private List<BsItemInfo> getBsItemInfoList(List<WipExtendIdentification> addWipList) throws Exception {
        List<String> itemNoFullList = addWipList.stream().map(WipExtendIdentification::getFormItemNo).distinct().collect(Collectors.toList());
        itemNoFullList.addAll(addWipList.stream().map(WipExtendIdentification::getItemNo).distinct().collect(Collectors.toList()));
        itemNoFullList = itemNoFullList.stream().distinct().collect(Collectors.toList());
        List<List<String>> splitList = CommonUtils.splitList(itemNoFullList, NumConstant.NUM_100);
        List<BsItemInfo> bsItemInfoList = new ArrayList<>();
        for (List<String> itemNos : splitList) {
            List<BsItemInfo> tempList = BasicsettingRemoteService.getItemByAmbiguityNos(itemNos);
            if (!CollectionUtils.isEmpty(tempList)) {
                bsItemInfoList.addAll(tempList);
            }
        }
        return bsItemInfoList;
    }

    private List<MtlSystemItemsDTO> getMtlItemsFromMES(List<WipExtendIdentification> addWipList) {
        List<String> itemNoList = addWipList.stream().filter(a -> StringUtils.isNotEmpty(a.getFormItemNo()) && a.getFormItemNo().length() >= NumConstant.NUM_TWELVE)
                .map(a -> a.getFormItemNo().substring(INT_0, NumConstant.NUM_TWELVE)).distinct().collect(Collectors.toList());
        itemNoList.addAll(addWipList.stream().filter(a -> StringUtils.isNotEmpty(a.getItemNo()) && a.getItemNo().length() >= NumConstant.NUM_TWELVE)
                .map(a -> a.getItemNo().substring(INT_0, NumConstant.NUM_TWELVE)).distinct().collect(Collectors.toList()));
        itemNoList = itemNoList.stream().distinct().collect(Collectors.toList());
        return datawbRemoteService.getItemIdBySegment1List(itemNoList);
    }

    private List<BarcodeExpandDTO> getBarcodeExpandList(List<String> formSnList, List<String> snList) throws Exception {
        List<String> barcodeList = new ArrayList<>();
        barcodeList.addAll(formSnList);
        barcodeList.addAll(snList);
        barcodeList = barcodeList.stream().filter(b -> StringUtils.isNotEmpty(b)).distinct().collect(Collectors.toList());
        BarcodeExpandQueryDTO barcodeExpandQueryDTO = new BarcodeExpandQueryDTO();
        barcodeExpandQueryDTO.setBarcodeList(barcodeList);
        return barcodeCenterRemoteService.expandQuery(barcodeExpandQueryDTO);
    }

    public WsmAssembleLinesWriteBack buildAssembleLinesWriteBack(List<MtlSystemItemsDTO> mtlSystemItemsDTOS, List<BsItemInfo> bsItemInfoList, List<BarcodeExpandDTO> barcodeExpandDTOList, WipExtendIdentification subWip) {
        WsmAssembleLinesWriteBack subSn = new WsmAssembleLinesWriteBack();
        String subItemNo = subWip.getItemNo();
        subSn.setItemCode(subItemNo);
        if (StringUtils.isNotEmpty(subItemNo) && subItemNo.length() > NumConstant.NUM_TWELVE) {
            subSn.setItemCode(subItemNo.substring(NumConstant.NUM_ZERO, NumConstant.NUM_TWELVE));
        }
        String subItemCode = subSn.getItemCode();
        MtlSystemItemsDTO subMtl = mtlSystemItemsDTOS.stream()
                .filter(m ->StringUtils.isNotEmpty(subItemCode) && subItemCode.equals(m.getSegment1())).findFirst().orElse(null);
        if (null == subMtl || null == subMtl.getInventoryItemId()) {
            return subSn;
        }
        subSn.setItemId(new BigDecimal(subMtl.getInventoryItemId()));
        // 设置子物料名称和版本
        BsItemInfo subBsItemInfo = bsItemInfoList.stream()
                .filter(b ->StringUtils.isNotEmpty(subItemNo) && subItemNo.equals(b.getItemNo())).findFirst().orElse(null);
        if (null != subBsItemInfo) {
            subSn.setItemName(subBsItemInfo.getItemName());
//            subSn.setItemVersion(subBsItemInfo.getVersion());
        }
        BarcodeExpandDTO subBarcode = barcodeExpandDTOList.stream().filter(b -> subWip.getSn().equals(b.getBarcode())).findFirst().orElse(null);
        subSn.setScanType(Constant.SEQUENCE_CODE);
        if (null != subBarcode) {
            String subScanType = Constant.TYPE_BATCH_CODE.equals(subBarcode.getParentCategoryName()) ? Constant.TYPE_BATCH_CODE : Constant.SEQUENCE_CODE;
            subSn.setScanType(subScanType);
            subSn.setSupplierName(subBarcode.getSupplierName());
        }
        subSn.setEnabledFlag(Constant.FLAG_Y);
        subSn.setCreatedBy(new BigDecimal(subWip.getCreateBy()));
        subSn.setLastUpdatedBy(new BigDecimal(subWip.getLastUpdatedBy()));
        subSn.setCreationDate(subWip.getCreateDate());
        subSn.setLastUpdateDate(subWip.getLastUpdatedDate());
        subSn.setItemBarcode(subWip.getSn());
        subSn.setItemQty(subWip.getFormQty());
        subSn.setVerifyFlag(Constant.FLAG_Y);
        subSn.setSourceSys(Constant.IMES);
        return subSn;
    }

    public WsmAssembleHeaders buildMainSn(List<MtlSystemItemsDTO> mtlSystemItemsDTOS, List<BsItemInfo> bsItemInfoList, List<BarcodeExpandDTO> barcodeExpandDTOList, String mainBarcode, WipExtendIdentification wipMain) {
        WsmAssembleHeaders mainSn = new WsmAssembleHeaders();
        String scanType = Constant.SEQUENCE_CODE;
        BarcodeExpandDTO barcodeExpandDTO = barcodeExpandDTOList.stream()
                .filter(b -> mainBarcode.equals(b.getBarcode())).findFirst().orElse(null);
        if (null != barcodeExpandDTO && Constant.TYPE_BATCH_CODE.equals(barcodeExpandDTO.getParentCategoryName())) {
            scanType = barcodeExpandDTO.getParentCategoryName();
        }
        String formItemNo = wipMain.getFormItemNo();
        mainSn.setItemCode(formItemNo);
        if (StringUtils.isNotEmpty(formItemNo) && formItemNo.length() > NumConstant.NUM_TWELVE) {
            mainSn.setItemCode(formItemNo.substring(NumConstant.NUM_ZERO, NumConstant.NUM_TWELVE));
        }
        String itemCode = mainSn.getItemCode();
        MtlSystemItemsDTO mtlSystemItemsDTO = mtlSystemItemsDTOS.stream()
                .filter(m -> StringUtils.isNotEmpty(itemCode) && itemCode.equals(m.getSegment1())).findFirst().orElse(null);
        if (null == mtlSystemItemsDTO || null == mtlSystemItemsDTO.getInventoryItemId()) {
            return mainSn;
        }
        mainSn.setItemId(new BigDecimal(mtlSystemItemsDTO.getInventoryItemId()));
        mainSn.setEnabledFlag(Constant.FLAG_Y);
        mainSn.setCreatedBy(new BigDecimal(wipMain.getCreateBy()));
        mainSn.setLastUpdatedBy(new BigDecimal(wipMain.getLastUpdatedBy()));
        mainSn.setCreationDate(wipMain.getCreateDate());
        mainSn.setLastUpdateDate(wipMain.getLastUpdatedDate());
        mainSn.setSiteId(new BigDecimal(Constant.STR_NUMBER_ZERO));
        mainSn.setScanType(scanType);
        mainSn.setItemBarcode(mainBarcode);
        mainSn.setItemQty(new BigDecimal(NumConstant.STR_ONE));
        BsItemInfo bsItemInfo = bsItemInfoList.stream()
                .filter(b -> formItemNo.equals(b.getItemNo())).findFirst().orElse(null);
        if (null != bsItemInfo) {
            mainSn.setItemName(bsItemInfo.getItemName());
            mainSn.setItemVersion(bsItemInfo.getVersion());
        }
        mainSn.setOrgId(wipMain.getOrgId());
        mainSn.setProdBigcategory(wipMain.getFormCatogary());
        mainSn.setVerifyFlag(Constant.FLAG_Y);
        return mainSn;
    }
}