<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.datawb.CfgCodeRuleItemRepository">
  <resultMap id="BaseResultMap" type="com.zte.domain.model.datawb.CfgCodeRuleItem">
    <id column="RULE_ITEM_ID" jdbcType="DECIMAL" property="ruleItemId" />
    <result column="CODE_RULE_ID" jdbcType="DECIMAL" property="codeRuleId" />
    <result column="RULE_ITEM_TYPE" jdbcType="VARCHAR" property="ruleItemType" />
    <result column="SORT_NO" jdbcType="DECIMAL" property="sortNo" />
    <result column="ATTRIBUTE1" jdbcType="VARCHAR" property="attribute1" />
    <result column="ATTRIBUTE2" jdbcType="VARCHAR" property="attribute2" />
    <result column="ATTRIBUTE3" jdbcType="VARCHAR" property="attribute3" />
    <result column="ATTRIBUTE4" jdbcType="VARCHAR" property="attribute4" />
    <result column="ATTRIBUTE5" jdbcType="DECIMAL" property="attribute5" />
    <result column="ATTRIBUTE6" jdbcType="DECIMAL" property="attribute6" />
    <result column="ATTRIBUTE7" jdbcType="DECIMAL" property="attribute7" />
    <result column="ATTRIBUTE8" jdbcType="DECIMAL" property="attribute8" />
    <result column="RESET_RULE" jdbcType="DECIMAL" property="resetRule" />
    <result column="LAST_UPDATE_DATE" jdbcType="TIMESTAMP" property="lastUpdateDate" />
    <result column="LAST_UPDATED_BY" jdbcType="DECIMAL" property="lastUpdatedBy" />
    <result column="CREATION_DATE" jdbcType="TIMESTAMP" property="creationDate" />
    <result column="CREATED_BY" jdbcType="DECIMAL" property="createdBy" />
    <result column="LAST_UPDATE_LOGIN" jdbcType="DECIMAL" property="lastUpdateLogin" />
    <result column="ENABLED_FLAG" jdbcType="VARCHAR" property="enabledFlag" />
  </resultMap>

  <sql id="Base_Column_List">
    RULE_ITEM_ID, CODE_RULE_ID, RULE_ITEM_TYPE, SORT_NO, ATTRIBUTE1, ATTRIBUTE2, ATTRIBUTE3, 
    ATTRIBUTE4, ATTRIBUTE5, ATTRIBUTE6, ATTRIBUTE7, ATTRIBUTE8, RESET_RULE, LAST_UPDATE_DATE, 
    LAST_UPDATED_BY, CREATION_DATE, CREATED_BY, LAST_UPDATE_LOGIN, ENABLED_FLAG
  </sql>

  <select id="selectCfgCodeRuleItemById" parameterType="com.zte.domain.model.datawb.CfgCodeRuleItem" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from CFG_CODE_RULE_ITEM
    where RULE_ITEM_ID = #{ruleItemId,jdbcType=DECIMAL}
  </select>
  <select id="configurationCodeRuleDEGetRuleItems" parameterType="com.zte.domain.model.datawb.CfgCodeRuleItem" resultMap="BaseResultMap">
        
            SELECT 
                RULE_ITEM_ID ,
                CODE_RULE_ID ,
                RULE_ITEM_TYPE ,
                SORT_NO ,
                RESET_RULE ,
                ATTRIBUTE1 ,
                ATTRIBUTE2 ,
                ATTRIBUTE3 ,
                ATTRIBUTE4 ,
                ATTRIBUTE5 ,
                ATTRIBUTE6 , 
                ATTRIBUTE7 ,
                ATTRIBUTE8 ,
				        LAST_UPDATE_DATE ,
				        LAST_UPDATED_BY ,
				        CREATION_DATE ,
				        CREATED_BY ,
				        LAST_UPDATE_LOGIN ,
				        ENABLED_FLAG 
            FROM 
                MESSYS.CFG_CODE_RULE_ITEM 
            WHERE 
                ENABLED_FLAG = 'Y'
                AND CODE_RULE_ID  = #{codeRuleId,jdbcType=DECIMAL} 
            ORDER BY SORT_NO 
        </select>
  <delete id="deleteCfgCodeRuleItemById" parameterType="com.zte.domain.model.datawb.CfgCodeRuleItem">
    delete from CFG_CODE_RULE_ITEM
    where RULE_ITEM_ID = #{ruleItemId,jdbcType=DECIMAL}
  </delete>

  <insert id="insertCfgCodeRuleItem" parameterType="com.zte.domain.model.datawb.CfgCodeRuleItem">
    insert into CFG_CODE_RULE_ITEM (RULE_ITEM_ID, CODE_RULE_ID, RULE_ITEM_TYPE, 
      SORT_NO, ATTRIBUTE1, ATTRIBUTE2, 
      ATTRIBUTE3, ATTRIBUTE4, ATTRIBUTE5, 
      ATTRIBUTE6, ATTRIBUTE7, ATTRIBUTE8, 
      RESET_RULE, LAST_UPDATE_DATE, LAST_UPDATED_BY, 
      CREATION_DATE, CREATED_BY, LAST_UPDATE_LOGIN, 
      ENABLED_FLAG)
    values (#{ruleItemId,jdbcType=DECIMAL}, #{codeRuleId,jdbcType=DECIMAL}, #{ruleItemType,jdbcType=VARCHAR}, 
      #{sortNo,jdbcType=DECIMAL}, #{attribute1,jdbcType=VARCHAR}, #{attribute2,jdbcType=VARCHAR}, 
      #{attribute3,jdbcType=VARCHAR}, #{attribute4,jdbcType=VARCHAR}, #{attribute5,jdbcType=DECIMAL}, 
      #{attribute6,jdbcType=DECIMAL}, #{attribute7,jdbcType=DECIMAL}, #{attribute8,jdbcType=DECIMAL}, 
      #{resetRule,jdbcType=DECIMAL}, #{lastUpdateDate,jdbcType=TIMESTAMP}, #{lastUpdatedBy,jdbcType=DECIMAL}, 
      #{creationDate,jdbcType=TIMESTAMP}, #{createdBy,jdbcType=DECIMAL}, #{lastUpdateLogin,jdbcType=DECIMAL}, 
      #{enabledFlag,jdbcType=VARCHAR})
  </insert>

  <insert id="insertCfgCodeRuleItemSelective" parameterType="com.zte.domain.model.datawb.CfgCodeRuleItem">
    insert into CFG_CODE_RULE_ITEM
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="ruleItemId != null">
        RULE_ITEM_ID,
      </if>

      <if test="codeRuleId != null">
        CODE_RULE_ID,
      </if>

      <if test="ruleItemType != null">
        RULE_ITEM_TYPE,
      </if>

      <if test="sortNo != null">
        SORT_NO,
      </if>

      <if test="attribute1 != null">
        ATTRIBUTE1,
      </if>

      <if test="attribute2 != null">
        ATTRIBUTE2,
      </if>

      <if test="attribute3 != null">
        ATTRIBUTE3,
      </if>

      <if test="attribute4 != null">
        ATTRIBUTE4,
      </if>

      <if test="attribute5 != null">
        ATTRIBUTE5,
      </if>

      <if test="attribute6 != null">
        ATTRIBUTE6,
      </if>

      <if test="attribute7 != null">
        ATTRIBUTE7,
      </if>

      <if test="attribute8 != null">
        ATTRIBUTE8,
      </if>

      <if test="resetRule != null">
        RESET_RULE,
      </if>

      <if test="lastUpdateDate != null">
        LAST_UPDATE_DATE,
      </if>

      <if test="lastUpdatedBy != null">
        LAST_UPDATED_BY,
      </if>

      <if test="creationDate != null">
        CREATION_DATE,
      </if>

      <if test="createdBy != null">
        CREATED_BY,
      </if>

      <if test="lastUpdateLogin != null">
        LAST_UPDATE_LOGIN,
      </if>

      <if test="enabledFlag != null">
        ENABLED_FLAG,
      </if>

    </trim>

    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="ruleItemId != null">
        #{ruleItemId,jdbcType=DECIMAL},
      </if>

      <if test="codeRuleId != null">
        #{codeRuleId,jdbcType=DECIMAL},
      </if>

      <if test="ruleItemType != null">
        #{ruleItemType,jdbcType=VARCHAR},
      </if>

      <if test="sortNo != null">
        #{sortNo,jdbcType=DECIMAL},
      </if>

      <if test="attribute1 != null">
        #{attribute1,jdbcType=VARCHAR},
      </if>

      <if test="attribute2 != null">
        #{attribute2,jdbcType=VARCHAR},
      </if>

      <if test="attribute3 != null">
        #{attribute3,jdbcType=VARCHAR},
      </if>

      <if test="attribute4 != null">
        #{attribute4,jdbcType=VARCHAR},
      </if>

      <if test="attribute5 != null">
        #{attribute5,jdbcType=DECIMAL},
      </if>

      <if test="attribute6 != null">
        #{attribute6,jdbcType=DECIMAL},
      </if>

      <if test="attribute7 != null">
        #{attribute7,jdbcType=DECIMAL},
      </if>

      <if test="attribute8 != null">
        #{attribute8,jdbcType=DECIMAL},
      </if>

      <if test="resetRule != null">
        #{resetRule,jdbcType=DECIMAL},
      </if>

      <if test="lastUpdateDate != null">
        #{lastUpdateDate,jdbcType=TIMESTAMP},
      </if>

      <if test="lastUpdatedBy != null">
        #{lastUpdatedBy,jdbcType=DECIMAL},
      </if>

      <if test="creationDate != null">
        #{creationDate,jdbcType=TIMESTAMP},
      </if>

      <if test="createdBy != null">
        #{createdBy,jdbcType=DECIMAL},
      </if>

      <if test="lastUpdateLogin != null">
        #{lastUpdateLogin,jdbcType=DECIMAL},
      </if>

      <if test="enabledFlag != null">
        #{enabledFlag,jdbcType=VARCHAR},
      </if>

    </trim>

  </insert>
  <update id="configurationCodeRuleItemDEUpdate" parameterType="com.zte.domain.model.datawb.CfgCodeRuleItem">
     
          UPDATE
              MESSYS.CFG_CODE_RULE_ITEM
          <set>
              Attribute5 = #{attribute5,jdbcType=DECIMAL}
          </set>
          WHERE
              RULE_ITEM_ID = #{ruleItemId,jdbcType=DECIMAL}
     
    </update>
  <update id="updateCfgCodeRuleItemByIdSelective" parameterType="com.zte.domain.model.datawb.CfgCodeRuleItem">
    update CFG_CODE_RULE_ITEM
    <set>
      <if test="codeRuleId != null">
        CODE_RULE_ID = #{codeRuleId,jdbcType=DECIMAL},
      </if>

      <if test="ruleItemType != null">
        RULE_ITEM_TYPE = #{ruleItemType,jdbcType=VARCHAR},
      </if>

      <if test="sortNo != null">
        SORT_NO = #{sortNo,jdbcType=DECIMAL},
      </if>

      <if test="attribute1 != null">
        ATTRIBUTE1 = #{attribute1,jdbcType=VARCHAR},
      </if>

      <if test="attribute2 != null">
        ATTRIBUTE2 = #{attribute2,jdbcType=VARCHAR},
      </if>

      <if test="attribute3 != null">
        ATTRIBUTE3 = #{attribute3,jdbcType=VARCHAR},
      </if>

      <if test="attribute4 != null">
        ATTRIBUTE4 = #{attribute4,jdbcType=VARCHAR},
      </if>

      <if test="attribute5 != null">
        ATTRIBUTE5 = #{attribute5,jdbcType=DECIMAL},
      </if>

      <if test="attribute6 != null">
        ATTRIBUTE6 = #{attribute6,jdbcType=DECIMAL},
      </if>

      <if test="attribute7 != null">
        ATTRIBUTE7 = #{attribute7,jdbcType=DECIMAL},
      </if>

      <if test="attribute8 != null">
        ATTRIBUTE8 = #{attribute8,jdbcType=DECIMAL},
      </if>

      <if test="resetRule != null">
        RESET_RULE = #{resetRule,jdbcType=DECIMAL},
      </if>

      <if test="lastUpdateDate != null">
        LAST_UPDATE_DATE = #{lastUpdateDate,jdbcType=TIMESTAMP},
      </if>

      <if test="lastUpdatedBy != null">
        LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=DECIMAL},
      </if>

      <if test="creationDate != null">
        CREATION_DATE = #{creationDate,jdbcType=TIMESTAMP},
      </if>

      <if test="createdBy != null">
        CREATED_BY = #{createdBy,jdbcType=DECIMAL},
      </if>

      <if test="lastUpdateLogin != null">
        LAST_UPDATE_LOGIN = #{lastUpdateLogin,jdbcType=DECIMAL},
      </if>

      <if test="enabledFlag != null">
        ENABLED_FLAG = #{enabledFlag,jdbcType=VARCHAR},
      </if>

    </set>

    where RULE_ITEM_ID = #{ruleItemId,jdbcType=DECIMAL}
  </update>

  <update id="updateCfgCodeRuleItemById" parameterType="com.zte.domain.model.datawb.CfgCodeRuleItem">
    update CFG_CODE_RULE_ITEM
    set CODE_RULE_ID = #{codeRuleId,jdbcType=DECIMAL},
      RULE_ITEM_TYPE = #{ruleItemType,jdbcType=VARCHAR},
      SORT_NO = #{sortNo,jdbcType=DECIMAL},
      ATTRIBUTE1 = #{attribute1,jdbcType=VARCHAR},
      ATTRIBUTE2 = #{attribute2,jdbcType=VARCHAR},
      ATTRIBUTE3 = #{attribute3,jdbcType=VARCHAR},
      ATTRIBUTE4 = #{attribute4,jdbcType=VARCHAR},
      ATTRIBUTE5 = #{attribute5,jdbcType=DECIMAL},
      ATTRIBUTE6 = #{attribute6,jdbcType=DECIMAL},
      ATTRIBUTE7 = #{attribute7,jdbcType=DECIMAL},
      ATTRIBUTE8 = #{attribute8,jdbcType=DECIMAL},
      RESET_RULE = #{resetRule,jdbcType=DECIMAL},
      LAST_UPDATE_DATE = #{lastUpdateDate,jdbcType=TIMESTAMP},
      LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=DECIMAL},
      CREATION_DATE = #{creationDate,jdbcType=TIMESTAMP},
      CREATED_BY = #{createdBy,jdbcType=DECIMAL},
      LAST_UPDATE_LOGIN = #{lastUpdateLogin,jdbcType=DECIMAL},
      ENABLED_FLAG = #{enabledFlag,jdbcType=VARCHAR}
    where RULE_ITEM_ID = #{ruleItemId,jdbcType=DECIMAL}
  </update>
  <select id="getSmallBoxSize" parameterType="String" resultType="String">
     SELECT LOOKUP_MEANING 
     FROM messys.SYS_LOOKUP_VALUES a 
     where a.lookup_code=#{lookUpCode}
  </select>
  <select id="getDicDescription" parameterType="String" resultType="String">
    select description
    from messys.sys_lookup_values
    where lookup_code=#{lookUpCode}
  </select>
  <select id="getSequenceBox" resultType="String">
    select to_char(MESSYS.CFG_CODE_RULE_ITEM_S_BOX_S.NEXTVAL)
    from dual
  </select>


</mapper>
