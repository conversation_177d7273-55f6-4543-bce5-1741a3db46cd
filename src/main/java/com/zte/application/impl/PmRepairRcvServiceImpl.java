/**
 * 项目名称 : ${project_name}
 * 创建日期 : ${date}
 * 修改历史 :
 * 1. [${date}] 创建文件 by ${user}
 **/
package com.zte.application.impl;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Maps;
import com.zte.application.*;
import com.zte.common.CommonUtils;
import com.zte.common.DateUtil;
import com.zte.common.enums.InterfaceEnum;
import com.zte.common.enums.RepairApprovalStatusEnum;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.ExcelName;
import com.zte.common.utils.MpConstant;
import com.zte.common.utils.NumConstant;
import com.zte.common.utils.ObtainRemoteServiceDataUtil;
import com.zte.common.utils.RepairStatusUtil;
import com.zte.consts.CommonConst;
import com.zte.domain.model.*;
import com.zte.domain.vo.PmRepairDqasRcvVO;
import com.zte.domain.vo.PmRepairRcvVo;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.CrafttechRemoteService;
import com.zte.infrastructure.remote.HrmUserInfoService;
import com.zte.infrastructure.remote.MdsRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.infrastructure.remote.ProductionDeliveryRemoteService;
import com.zte.interfaces.assembler.PsWipInfoAssembler;
import com.zte.interfaces.dto.BProdBomHeaderDTO;
import com.zte.interfaces.dto.BSProcessDTO;
import com.zte.interfaces.dto.CtRouteDetailDTO;
import com.zte.interfaces.dto.DQASCheckDTO;
import com.zte.interfaces.dto.MaintenanceMaterialInfoDTO;
import com.zte.interfaces.dto.PmRepairDetailDTO;
import com.zte.interfaces.dto.PmRepairInfoDTO;
import com.zte.interfaces.dto.PmRepairRcvDTO;
import com.zte.interfaces.dto.PmRepairRcvDetailDTO;
import com.zte.interfaces.dto.PmRepairRcvDetailParamDTO;
import com.zte.interfaces.dto.PmRepairVirtualSnDTO;
import com.zte.interfaces.dto.PsScanHistoryDTO;
import com.zte.interfaces.dto.PsWipInfoDTO;
import com.zte.interfaces.dto.RepairRestoreDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.interfaces.dto.SysLookupValuesDTO;
import com.zte.interfaces.dto.*;
import com.zte.interfaces.dto.mds.MdsRepairRecordDTO;
import com.zte.interfaces.dto.middle.PmRepairCommonDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.itp.msa.util.string.StringHelper;
import com.zte.microservice.name.MicroServiceNameEum;
import com.zte.springbootframe.common.annotation.RecordLogAnnotation;
import com.zte.springbootframe.common.consts.SysConst;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.redis.RedisLock;
import com.zte.springbootframe.util.EasyExcelUtils;
import com.zte.springbootframe.util.ImesExcelUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.RequestHeadValidationUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;


/**
 * 送修/返还维护
 *
 * <AUTHOR>
 **/
@Service
public class PmRepairRcvServiceImpl implements PmRepairRcvService {
    @Autowired
    private PmRepairRcvRepository pmRepairRcvRepository;
    @Autowired
    private PsWipInfoService psWipInfoService;
    @Autowired
    private PmRepairRcvDetailService pmRepairRcvDetailService;
    @Autowired
    private PmRepairInfoService pmRepairInfoService;
    @Autowired
    private RepairRcvSequenceService repairRcvSequenceService;
    @Autowired
    private PsScanHistoryService psScanHistoryService;
    @Autowired
    private PmRepairRcvRecodeService pmRepairRcvRecodeService;

    @Autowired
    private PmRepairRcvServiceRecodeImpl pmRepairRcvServiceRecode;
    @Autowired
    private PsWipInfoServiceImpl psWipInfoServiceImpl;
    @Autowired
    private WipExtendIdentificationRepository  wipExtendIdentificationRepository;
    @Autowired
    private PsWipInfoRepository psWipInfoRepository;
    @Autowired
    private PmRepairRcvDetailRepository pmRepairRcvDetailRepository;
    @Autowired
    private PmRepairRcvService pmRepairRcvService;
    @Autowired
    private PmRepairDetailService pmRepairDetailService;

    @Autowired
    private MaintenanceMaterialLineRepository maintenanceMaterialLineRepository;

    @Autowired
    private FisService fisService;

    @Autowired
    private MesRepairService mesRepairService;
    @Autowired
    private BarcodeLockDetailRepository barcodeLockDetailRepository;
    @Autowired
    private BarcodeLockTempRepository barcodeLockTempRepository;
    @Autowired
    private HrmUserInfoService hrmUserInfoService;
	@Autowired
	private MdsRemoteService mdsRemoteService;
    @Autowired
    private PsScanHistoryServiceImpl scanHistoryServiceImpl;

    @Autowired
    private BProdBomService bProdBomService;

    @Autowired
    private CenterfactoryRemoteService centerfactoryRemoteService;
    @Autowired
    private RepairApprovalService repairApprovalService;

    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    /**
     * <AUTHOR>
     * 送修提交时增加扫描校验来源
     * @param fromStation
     * @param wipInfo
     * @throws Exception
     */
    public void checkValue(String fromStation,String sn, List<PsWipInfo> wipInfo) throws Exception {
        if(Constant.REPAIR_FROM_STATION.equals(fromStation)){
            if(Constant.WAREHOUSE_ENTRY.equals(wipInfo.get(Constant.INT_0).getCraftSection())){
                throw new Exception(sn + MpConstant.STATUS_WAREHOUSE_ENTRY);
            }
            if(Constant.WAREHOUSE_OUT.equals(wipInfo.get(Constant.INT_0).getCraftSection())){
                throw new Exception(sn + MpConstant.STATUS_WAREHOUSE_OUT);
            }
        } else if(Constant.FROM_STATION_SEMIS.equals(fromStation)) {
            if(!Constant.WAREHOUSE_ENTRY.equals(wipInfo.get(Constant.INT_0).getCraftSection())){
                throw new Exception(sn + CommonUtils.getLmbMessage(MessageId.FROM_STATION_NOT_MATCHED));
            }
        } else if(Constant.FROM_STATION_MACHINE.equals(fromStation)) {
            if(!Constant.WAREHOUSE_OUT.equals(wipInfo.get(Constant.INT_0).getCraftSection())){
                throw new Exception(sn + CommonUtils.getLmbMessage(MessageId.WHOLE_MACHINE_CAN_ONLY_BE_SENT_OUT_FOR_REPAIR));
            }
        }
    }
    /**
     * 根据主键删除实体数据
     *
     * @param record
     **/
    @Override
    public int deletePmRepairRcvByReason(PmRepairRcv record) {
        return pmRepairRcvRepository.deletePmRepairRcvByCode(record);
    }

    /**
     * 有选择性的更新实体数据
     *
     * @param record
     **/
    @Override
    public int updatePmRepairRcvByReasonSelective(PmRepairRcv record) {
        BigDecimal orgId = record.getOrgId() == null ? new BigDecimal(Constant.STR_NUMBER_ZERO) : record.getOrgId();
        record.setOrgId(orgId);
        return pmRepairRcvRepository.updatePmRepairRcvByCodeSelective(record);
    }

    /**
     * 根据主键查询实体信息
     *
     * @param  record 检索参数
     * @return PmRepairRcv 送修/返还维护数据
     **/
    @Override
    public PmRepairRcv selectPmRepairRcvById(PmRepairRcvDTO record) {
        return pmRepairRcvRepository.selectPmRepairRcvById(record);
    }

    /**
     * 根据条件查询实体信息并排序
     *
     * @param  record 条件结合
     * @return PmRepairRcv 送修/返还维护数据
     **/
    @Override
    public List<PmRepairRcv> getList(PmRepairRcvDTO record) {
        String orderField = record.getSort();
        if (orderField == null || orderField.isEmpty()) {
            record.setSort(MpConstant.ORDER_FIELD);
        }
        return pmRepairRcvRepository.getList(record);
    }

    /**
     * 根据条件查询实体信息并排序
     * 获取记录数量
     * @param  record 条件结合
     * @return 送修/返还维护数量
     **/
    @Override
    public Long getCount(PmRepairRcvDTO record) {
        return pmRepairRcvRepository.getCount(record);
    }

    /**
     * 根据条件查询实体信息并排序
     * 分页显示
     * @param  record 条件结合
     * @return PmRepairRcv 送修/返还维护数据
     **/
    @Override
    public List<PmRepairRcv> getPage(PmRepairRcvDTO record) {
        List<PmRepairRcv> list = pmRepairRcvRepository.getPage(record);
        return list;
    }

    /**
     * 根据条件查询实体信息并排序
     *
     * @param  record 条件结合
     * @return PmRepairRcv 送修/返还维护数据
     **/
    @Override
    public List<PmRepairRcv> getRelOneList(PmRepairRcvDTO record) {
        String orderField = record.getSort();
        if (orderField == null || orderField.isEmpty()) {
            record.setSort(MpConstant.ORDER_FIELD);
        }
        return pmRepairRcvRepository.getRelOneList(record);
    }

    /**
     * 根据条件查询实体信息并排序
     * 分页显示
     * @param  record 条件结合
     * @return PmRepairRcv 送修/返还维护数据
     **/
    @Override
    public List<PmRepairRcv> getRelOnePage(PmRepairRcvDTO record) {
        return pmRepairRcvRepository.getRelOnePage(record);
    }


    /**
     * 根据条件查询实体信息并排序
     * 获取记录数量
     * @param  record 条件结合
     * @return 送修/返还维护数量
     **/
    @Override
    public Long getRelOneCount(PmRepairRcvDTO record) {
        return pmRepairRcvRepository.getRelOneCount(record);
    }
    /**
     * 生成单号
     * <AUTHOR>
     * @return 单号
     */
    @Override
    public String newBillNo(String factoryId,String rp, int type) {
        RepairRcvSequence returnEntity = new RepairRcvSequence();
        RepairRcvSequence craftSequence = repairRcvSequenceService.selectSequenceAll(type);
        if (craftSequence == null) {
            craftSequence = new RepairRcvSequence();
            craftSequence.setSequence(Constant.INT_0);
            craftSequence.setLastDate(new Date());
            craftSequence.setType(type);
            repairRcvSequenceService.insertSequence(craftSequence);
            // 新增后再做查询操作
            craftSequence = repairRcvSequenceService.selectSequenceAll(type);
        }
        SimpleDateFormat sdf = new SimpleDateFormat(MpConstant.DATE_FORMAT_YYYYMMDD_TWO);

        if(StringUtils.isNotEmpty(factoryId)) {
            factoryId = lpad(factoryId, Constant.INT_2, Constant.STRING_0);
        }
        String craftNo = rp + factoryId + sdf.format(craftSequence.getCurrData());
        // 若存储时间不等于服务器当前时间（同一天），则返回序号为1，若为同一天，则在当前序号基础上+1
        if (craftSequence.getLastDays() != craftSequence.getCurrDays()) {
            returnEntity.setLastDate(craftSequence.getCurrData());
            returnEntity.setSequence(NumConstant.NUM_ONE);
            returnEntity.setBillNo(craftNo + Constant.STRINGT_00001);
        } else {
            returnEntity.setLastDate(craftSequence.getCurrData());
            int intSeq = craftSequence.getSequence() + NumConstant.NUM_ONE;
            returnEntity.setSequence(intSeq);
            String strSeq = Constant.STR_EMPTY + intSeq;
            strSeq = lpad(strSeq, Constant.INT_5, Constant.STR_NUMBER_ZERO);

            returnEntity.setBillNo(craftNo + strSeq);
        }
        returnEntity.setType(type);
        repairRcvSequenceService.updateSequence(returnEntity);
        return returnEntity.getBillNo();

    }
    /**
     * 通过条码查询送修信息
     * <AUTHOR>
     * @param record
     * @return
     */
    @Override
    public PmRepairRcvDTO selectPmRepairRcvDTOBySn(PmRepairRcv record,String empNo) {
        PmRepairRcvDTO pmRepairRcvDTO = new PmRepairRcvDTO();
        if(record.getSn() == null && record.getSn().isEmpty()) {
            pmRepairRcvDTO.setValiedMsg(CommonUtils.getLmbMessage(MessageId.NO_MESSAGE_OF_SN));
            return pmRepairRcvDTO;
        }
        if(record.getFromStation() == null && record.getFromStation().isEmpty()) {
            pmRepairRcvDTO.setValiedMsg(CommonUtils.getLmbMessage(MessageId.NO_FROMSTATION_OF_SN));
            return pmRepairRcvDTO;
        }
        pmRepairRcvDTO.setSn(record.getSn());
        PmRepairRcvDTO prrs = new PmRepairRcvDTO();
        prrs.setSn(record.getSn());
        prrs.setBillType(MpConstant.REPAIR_BILL_TYPE_TEN);
        prrs.setStatus(Constant.REPAIR_STATUS_SCRAP);
        Long o = pmRepairRcvRepository.getRelOneCount(prrs);
        if (o > Constant.INT_0 ){
            pmRepairRcvDTO.setValiedMsg(CommonUtils.getLmbMessage(MessageId.SN_IS_SCRAPPED));
            return pmRepairRcvDTO;
        }
        PmRepairRcv pmRepairRcv = pmRepairRcvRepository.getOnePmRepairRcvDetail(pmRepairRcvDTO);
        if(pmRepairRcv!=null) {
            if(pmRepairRcv.getReceptionId()!=null&&pmRepairRcv.getIsAccept().intValue()==0){
                pmRepairRcvDTO.setValiedMsg(CommonUtils.getLmbMessage(MessageId.SN_HAS_NOT_BEEN_REPAIR_RETURNED));
                return pmRepairRcvDTO;
            }
        }
        pmRepairRcvDTO.setValiedMsg(MpConstant.RESULT_TYPE_OK);

        String fromStation = record.getFromStation();
        List<PsWipInfo> wipInfo = psWipInfoService.getWipInfoJoinTestBySn(record.getSn());
        if(null!=wipInfo&&!wipInfo.isEmpty()){
            pmRepairRcvDTO.setItemCode(wipInfo.get(Constant.INT_0).getItemNo());
            pmRepairRcvDTO.setCraftSection(wipInfo.get(Constant.INT_0).getCraftSection());
            Map<String, String> mapGetLine = new HashMap<String, String>();

            this.changeLine(mapGetLine, record.getFactoryId(), wipInfo,pmRepairRcvDTO);

        }else {
            pmRepairRcvDTO.setValiedMsg(CommonUtils.getLmbMessage(MessageId.NO_WIP_INFO_RECORD_OF_SN));
            return pmRepairRcvDTO;
        }
        //校验
        this.valied(pmRepairRcvDTO,wipInfo,fromStation);

        // 中式校验
        if(Constant.FROM_STATION_MACHINE.equals(record.getFromStation())){
            checkDQAS(pmRepairRcvDTO,empNo);
        }
        return pmRepairRcvDTO;
    }

    /**
     * 维修送修条码校验
     * @param pmRepairRcvDTO
     * @param empNo
     */
    private void checkDQAS(PmRepairRcvDTO pmRepairRcvDTO,String empNo){
    	SysLookupTypesDTO sysLookupTypesDTO  = null;
        try{
            // 2019/10/31  调用中式接口, 待调试及联调\ 开关添加
            sysLookupTypesDTO = BasicsettingRemoteService.getSysLookUpValue(Constant.SYS_LOOK_DQAS,Constant.LOOKUP_DQAS_SEND_CHECK_SWITCH_);
        }catch(Exception e) {
        	String[] params = { Constant.LOOKUP_DQAS_SEND_CHECK_SWITCH_ };
            pmRepairRcvDTO.setValiedMsg(CommonUtils.getLmbMessage(MessageId.GET_LOOKUP_VALUE_ERROR, params));
        }
        if(sysLookupTypesDTO != null && Constant.FLAG_Y.equals(sysLookupTypesDTO.getLookupMeaning())){
            StringBuffer strInfo=new StringBuffer("");
            DQASCheckDTO dqasCheckDTO = new DQASCheckDTO(pmRepairRcvDTO.getSn(),CommonUtils.getStrTransNull(pmRepairRcvDTO.getFactoryId()),pmRepairRcvDTO.getItemCode(),MpConstant.STR_SEND_REPAIR,empNo);
            if(!repairDQASCheck(dqasCheckDTO,strInfo)){
                pmRepairRcvDTO.setValiedMsg(strInfo.toString());
            }
        }

    }

    /**
     * 封装条码校验 送修、返还维修参数
     * @return
     */
    private String packageParam(String baseUrl, DQASCheckDTO dqasCheckDTO) throws Exception{
        String url = baseUrl;
        if(StringUtils.isNotEmpty(url) && url.endsWith(MpConstant.SLASH)){
            url += dqasCheckDTO.getFactoryId();
        }else if(StringUtils.isNotEmpty(url) && !url.endsWith(MpConstant.SLASH)){
            url += MpConstant.SLASH ;
        }
        url += MpConstant.SLASH + dqasCheckDTO.getSn() + MpConstant.SLASH + URLEncoder.encode(dqasCheckDTO.getRepairStatus(), "UTF-8") + MpConstant.SLASH + dqasCheckDTO.getEmpNo() + MpConstant.SLASH + dqasCheckDTO.getItemCode();
        return url;
    }
    /**
     *  维修条码校验
     * @return
     */
    @Override
    @RecordLogAnnotation("中试校验")
    public boolean repairDQASCheck(DQASCheckDTO dqasCheckDTO,StringBuffer strInfo) {
        // 获取联机平台url地址
        try{
            SysLookupTypesDTO sysLookupTypesDTO = BasicsettingRemoteService.getSysLookUpValue(Constant.SYS_LOOK_DQAS,Constant.LOOKUP_TYPE_DQAS_REPAIR_CHECK);
            if (sysLookupTypesDTO == null) {
                throw new Exception(dqasCheckDTO.getSn()+CommonUtils.getLmbMessage(MessageId.REPAIR_ZS_VAILDATE_FAIL));
            }
            String url  = packageParam(sysLookupTypesDTO.getLookupMeaning(),dqasCheckDTO);
            String result = HttpClientUtil.httpGet(url, null,null);
            JsonNode json = JacksonJsonConverUtil.getMapperInstance().readTree(result);
            if(json == null){
                logger.error(dqasCheckDTO.getSn()+MpConstant.SN_DQAS_CHECK_FAILURE);
                throw new Exception(dqasCheckDTO.getSn()+CommonUtils.getLmbMessage(MessageId.REPAIR_ZS_VAILDATE_FAIL));
            }
            JsonNode bResultNode = json.get("bResult");
            if(bResultNode != null ){
                strInfo.append(json.get("strInfo"));
                return bResultNode.asBoolean();
            }
        }catch(Exception e){
            return false;
        }
        return false;
    }

    /**
     * 方法外提
     * <AUTHOR>
     * @param mapGetLine
     * @param factoryId
     * @param wipInfo
     * @param pmRepairRcvDTO
     */
    private void changeLine(Map<String, String> mapGetLine,BigDecimal factoryId,
                            List<PsWipInfo> wipInfo, PmRepairRcvDTO pmRepairRcvDTO) {
        try {
            mapGetLine = ObtainRemoteServiceDataUtil.getLineAll(factoryId);
        } catch (Exception e1) {
            logger.error(Constant.LOG_ERROR+e1.getMessage(),e1);
        }
        if(null != mapGetLine && mapGetLine.size() > 0
                && StringUtils.isNotBlank(wipInfo.get(Constant.INT_0).getLineCode())) {
            pmRepairRcvDTO.setLineName(mapGetLine.get(wipInfo.get(Constant.INT_0).getLineCode()));
        }
    }

    public void valied(PmRepairRcvDTO pmRepairRcvDTO,List<PsWipInfo> wipInfoList,String fromStation){
        // 2024.02.27 整机单板送修放开限制
        if((Constant.REPAIR_FROM_STATION).equals(fromStation)){
            if(wipInfoList!=null && wipInfoList.size()>Constant.INT_0 && (Constant.WAREHOUSE_ENTRY).equals(wipInfoList.get(Constant.INT_0).getCraftSection())){
                pmRepairRcvDTO.setValiedMsg(MpConstant.STATUS_WAREHOUSE_ENTRY);
            }
            if(wipInfoList!=null && wipInfoList.size()>Constant.INT_0 && (Constant.WAREHOUSE_OUT).equals(wipInfoList.get(Constant.INT_0).getCraftSection())){
                pmRepairRcvDTO.setValiedMsg(MpConstant.STATUS_WAREHOUSE_OUT);
            }
        } else if((Constant.FROM_STATION_SEMIS).equals(fromStation)) {
            if(!(Constant.WAREHOUSE_ENTRY).equals(wipInfoList.get(Constant.INT_0).getCraftSection())){
                pmRepairRcvDTO.setValiedMsg(CommonUtils.getLmbMessage(MessageId.FROM_STATION_NOT_MATCHED));
            }
        } else if((Constant.FROM_STATION_MACHINE).equals(fromStation)) {
            if(!(Constant.WAREHOUSE_OUT).equals(wipInfoList.get(Constant.INT_0).getCraftSection())){
                pmRepairRcvDTO.setValiedMsg(CommonUtils.getLmbMessage(MessageId.TOTAL_MACHINE_NEED_TO_OUT_WATEHOUSE_TO_TEPAIRE));
            }
        }
    }
    @Override
    public int insertPmRepairRcvSelective(PmRepairRcv record) {
        return pmRepairRcvRepository.insertPmRepairRcvSelective(record);
    }

    @Override
    public PmRepairInfo getRepairRcvListBySn(PmRepairInfoDTO dto) {
        PmRepairInfo pmRepairInfo = new PmRepairInfo();

        // 校验是否报废
        PmRepairRcvDTO prrs = new PmRepairRcvDTO();
        prrs.setSn(dto.getSn());
        prrs.setBillType(MpConstant.REPAIR_BILL_TYPE_TEN);
        prrs.setStatus(Constant.REPAIR_STATUS_SCRAP);
        Long o = pmRepairRcvRepository.getRelOneCount(prrs);
        if (o > Constant.INT_0 ){
            pmRepairInfo.setValidMsg(CommonUtils.getLmbMessage(MessageId.SN_IS_SCRAPPED,dto.getSn()));
            return pmRepairInfo;
        }

        // 校验返还条码或对应批次是否存在锁定记录,若存在则不允许返还
        if (checkSnInLock(dto, pmRepairInfo)){
            return pmRepairInfo;
        }

        // 查询维修记录
        dto.setPage(1L);
        dto.setRows(8L);
        dto.setSort(MpConstant.ORDER_FIELD);
        dto.setOrder(Constant.DESC);
        Long startRow = (dto.getPage() - Constant.INT_1) * dto.getRows() + Constant.INT_1;
        Long endRow = dto.getPage() * dto.getRows();
        dto.setStartRow(startRow);
        dto.setEndRow(endRow);

        // 不过滤送修来源
        PmRepairInfoDTO queryDto = new PmRepairInfoDTO();
        BeanUtils.copyProperties(dto, queryDto);
        queryDto.setFromStation(null);

        List<PmRepairInfo> mRepairRcv = pmRepairInfoService.getRelOnePageNoChange(queryDto);

        if(CollectionUtils.isNotEmpty(mRepairRcv) ){
            pmRepairInfo = mRepairRcv.get(Constant.INT_0);
            // 终端返还校验
            boolean terminalFlag = MpConstant.PAGE_TYPE_TERMINAL.equals(dto.getPageType());
            if (terminalFlag) {
                checkRcvListBySnTerminal(dto, pmRepairInfo);
                return pmRepairInfo;
            }
            if(dto.getRcvCraftSection().equals(pmRepairInfo.getRcvCraftSection())) {
                if(pmRepairInfo.getStatus()!=null) {
                    this.checkRcvListBySn(pmRepairInfo, dto.getSn());
                }else {
                    pmRepairInfo.setValidMsg(CommonUtils.getLmbMessage(MessageId.NOT_REPAIR_TWO,pmRepairInfo.getSn()));
                }
            }else {
                pmRepairInfo.setValidMsg(CommonUtils.getLmbMessage(MessageId.CRAFT_SECTION_INCONSISTENT_CAN_NOT_BE_RETURNED,pmRepairInfo.getSn(),pmRepairInfo.getRcvCraftSection()));
            }
        }else{
            pmRepairInfo.setValidMsg(CommonUtils.getLmbMessage(MessageId.INPUT_RECORD_NOT_FOUND,dto.getSn()));
        }
        // 处理MBOM
        List<PmRepairInfo> resultList = new ArrayList<>();
        resultList.add(pmRepairInfo);
        setMBomOfPmRepairInfoList(resultList);
        return pmRepairInfo;
    }

    private void setMBomOfPmRepairInfoList(List<PmRepairInfo> pmRepairInfoList) {
        if(CollectionUtils.isEmpty(pmRepairInfoList)){
            return;
        }
        List<String> prodPlanIdList = pmRepairInfoList.stream().map(PmRepairInfo::getProdplanId).distinct().collect(Collectors.toList());
        List<BProdBomHeaderDTO> bProdBomHeaderDTOS = centerfactoryRemoteService.queryProductCodeByProdPlanIdList(prodPlanIdList);
        Map<String,String> prodToMBomMap = bProdBomHeaderDTOS.stream().collect(Collectors.toMap(BProdBomHeaderDTO::getProdplanId, BProdBomHeaderDTO::getProductCode, (k1, k2) -> k1));
        for (PmRepairInfo pmRepairInfo : pmRepairInfoList) {
            pmRepairInfo.setMbom(pmRepairInfo.getItemCode());
            String mBom = prodToMBomMap.get(pmRepairInfo.getProdplanId());
            if (StringUtils.isNotBlank(mBom)) {
                pmRepairInfo.setMbom(mBom);
            }
        }
    }

    private boolean checkSnInLock(PmRepairInfoDTO dto, PmRepairInfo pmRepairInfo) {
        List<String> batchSnList = new ArrayList<>();
        PsWipInfo returnPsWipInfo = psWipInfoService.getWipInfoBySn(dto.getSn());
        batchSnList.add(dto.getSn());
        String attribute1 = Constant.STR_EMPTY;
        if(null != returnPsWipInfo && StringUtils.isNotEmpty(returnPsWipInfo.getAttribute1())){
            attribute1 = returnPsWipInfo.getAttribute1();
            batchSnList.add(attribute1);
        }
        List<BarcodeLockDetail> lockList = barcodeLockDetailRepository.queryLockDetail(batchSnList, null);
        if (CollectionUtils.isEmpty(lockList)) {
            return false;
        }
        return filterSn(dto, pmRepairInfo, batchSnList, attribute1, lockList);
    }

    private boolean filterSn(PmRepairInfoDTO dto, PmRepairInfo pmRepairInfo, List<String> batchSnList, String attribute1, List<BarcodeLockDetail> lockList) {
        List<String> billNoList = lockList.stream().map(BarcodeLockDetail::getBillNo).distinct().collect(Collectors.toList());
        // 单据下是否存在已解锁的条码
        List<BarcodeLockTemp> list = barcodeLockTempRepository.selectUnLockInfo(billNoList, batchSnList);
        if (!CollectionUtils.isEmpty(list)) {
            // 筛选掉该条码已解锁的单据
            Map<String, List<BarcodeLockTemp>> collectMap = list.stream()
                    .collect(Collectors.groupingBy(BarcodeLockTemp::getBillNo));
            Iterator<BarcodeLockDetail> iterator = lockList.iterator();
            List<String> tempList;
            while (iterator.hasNext()) {
                tempList = new LinkedList<>();
                tempList.addAll(batchSnList);
                BarcodeLockDetail next = iterator.next();
                List<BarcodeLockTemp> dtos = collectMap.get(next.getBillNo());
                if (CollectionUtils.isEmpty(dtos)) {
                    continue;
                }
                List<String> unLockSn = dtos.stream().map(BarcodeLockTemp::getSn)
                        .collect(Collectors.toList());
                // 当前条码已经解锁，移除锁定记录
                if (unLockSn.contains(dto.getSn())) {
                    iterator.remove();
                }
            }
            if(!CollectionUtils.isEmpty(lockList)){
                pmRepairInfo.setValidMsg(CommonUtils.getLmbMessage(MessageId.RETURN_SN_IS_ON_LOCK,dto.getSn(),attribute1));
                return true;
            }
        }else{
            pmRepairInfo.setValidMsg(CommonUtils.getLmbMessage(MessageId.RETURN_SN_IS_ON_LOCK,dto.getSn(),attribute1));
            return true;
        }
        return false;
    }

    /**
     * 终端返还校验
     */
    boolean checkRcvListBySnTerminal(PmRepairInfoDTO dto, PmRepairInfo pmRepairInfo) {
        // 校验是否为有条码送修
        if (MpConstant.SN_TYPE_NO_SN.equals(pmRepairInfo.getSnType())) {
            pmRepairInfo.setValidMsg(CommonUtils.getLmbMessage(MessageId.INPUT_TYPE_NOT_HAS_SN_CAN_NOT_BE_RETURNED, dto.getSn()));
            return false;
        }
        // 校验状态是否为维修完成
        if (!Constant.REPAIR_STATUS_COMPLETE.equals(pmRepairInfo.getRepairStatus())) {
            String repairStatus = pmRepairInfo.getRepairStatus();
            String repairStatusMsg = RepairStatusUtil.getRepairStatusMessage(repairStatus);
            pmRepairInfo.setValidMsg(CommonUtils.getLmbMessage(MessageId.REPAIR_STATUS_NOT_COMPLETE_CAN_NOT_BE_RETURNED, dto.getSn(), repairStatusMsg));
            return false;
        }
        // 校验线体
        if (!StringUtils.isEmpty(dto.getLineCode()) && !dto.getLineCode().equals(pmRepairInfo.getLineCode())) {
            pmRepairInfo.setValidMsg(CommonUtils.getLmbMessage(MessageId.LINE_CODE_INCONSISTENT_CAN_NOT_BE_RETURNED, dto.getSn(), pmRepairInfo.getLineCode()));
            return false;
        }
        // 校验来源（去向），查询时传入来源
        if (!StringUtils.isEmpty(dto.getFromStation()) && !dto.getFromStation().equals(pmRepairInfo.getFromStation())) {
            pmRepairInfo.setValidMsg(CommonUtils.getLmbMessage(MessageId.FROM_STATION_INCONSISTENT_CAN_NOT_BE_RETURNED, dto.getSn(), pmRepairInfo.getFromStation()));
            return false;
        }
        // 校验是否维修过
        PmRepairRcvDetailDTO pmRepairRcvDetailDTO = new PmRepairRcvDetailDTO();
        pmRepairRcvDetailDTO.setIsAccept(MpConstant.REPAIR_IS_ACCEPT);
        pmRepairRcvDetailDTO.setSn(dto.getSn());
        pmRepairRcvDetailDTO.setBillType(MpConstant.REPAIR_BILL_TYPE_TEN);
        pmRepairRcvDetailDTO.setEnabledFlag(Constant.FLAG_Y);
        List<PmRepairRcvDetail> list = pmRepairRcvDetailService.getRelOneList(pmRepairRcvDetailDTO);
        if (CollectionUtils.isEmpty(list)) {
            pmRepairInfo.setValidMsg(CommonUtils.getLmbMessage(MessageId.NEEDS_TO_BE_REPAIRED_FIRST, pmRepairInfo.getSn()));
            return false;
        }
        // 校验通过
        pmRepairInfo.setValidMsg(MpConstant.RESULT_TYPE_OK);
        return true;
    }

    @Override
    public List<PmRepairInfo> getRepairRcvListNoSn(PmRepairInfoDTO pmRepairInfoDTO) {
        // 校验批次号是否为空
        String prodplanId = pmRepairInfoDTO.getProdplanId();
        if (StringUtils.isEmpty(prodplanId)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PLAN_NO_IS_NULL);
        }
        // 校验批次号是否为7位数字
        if (!(prodplanId.length() == Constant.INT_7 && StringUtils.isNumeric(prodplanId))) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PRODPLANID_SHALL_BE_SEVEN_DIGITS);
        }
        // 校验返还数量 不能超过100
        Integer rows = pmRepairInfoDTO.getQty();
        if (rows == null) {
            rows = Constant.BATCH_SIZE;
        } else if (rows > Constant.BATCH_SIZE || rows < Constant.INT_1) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.REPAIR_RETURN_QTY_ERROR);
        }

        PmRepairInfoDTO queryDto = new PmRepairInfoDTO();
        BeanUtils.copyProperties(pmRepairInfoDTO, queryDto);
        queryDto.setSort(MpConstant.ORDER_FIELD);
        queryDto.setOrder(Constant.DESC);
        queryDto.setStartRow(1L);
        queryDto.setEndRow(Long.valueOf(rows));
        queryDto.setRepairStatus(Constant.REPAIR_STATUS_COMPLETE);
        queryDto.setOnlyNewRecord(true);
        List<PmRepairInfo> mRepairRcvList = pmRepairInfoService.getRelOnePageNoChange(queryDto);
        for (PmRepairInfo p : mRepairRcvList) {
            // 校验是否为无条码送修
            if (MpConstant.SN_TYPE_NO_SN.equals(p.getSnType())) {
                p.setValidMsg(MpConstant.RESULT_TYPE_OK);
            } else {
                p.setValidMsg(CommonUtils.getLmbMessage(MessageId.INPUT_TYPE_NOT_HAS_NO_SN_CAN_NOT_BE_RETURNED, p.getSn()));
            }
        }
        setMBomOfPmRepairInfoList(mRepairRcvList);
        return mRepairRcvList;
    }

    /* Started by AICoder, pid:n8c97cc5b041e2f145010ac0b0cacb55a1d9b8da */
    @Override
    public List<PmRepairInfo> getRepairRcvListBySnList(PmRepairInfoDTO dto) {
        List<PmRepairRcvDetailDTO> repairRcvDetailList = dto.getRepairRcvDetailList();
        // Early return if the list is empty
        if (CollectionUtils.isEmpty(repairRcvDetailList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.DATA_IS_EMPTY);
        }
        // Extract SNs into a list
        List<String> snList = repairRcvDetailList.stream()
                .map(PmRepairRcvDetailDTO::getSn)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
        // Validate input list
        if (CollectionUtils.isEmpty(snList) || snList.size() < repairRcvDetailList.size()) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SN_NULL, new Object[]{});
        }
        // 校验 已经报废代码报错
        this.validScrapped(snList);
        // 获取条码送修记录信息
        List<PmRepairInfo> mRepairRcvList = this.getPmRepairInfos(snList);
        // Initialize returnList and craftSectionMap
        List<PmRepairInfo> returnList = new ArrayList<>();
        Map<String, String> processCodeMap = new HashMap<>();
        // Set craft sections and validate them
        this.setProcessMap(returnList, mRepairRcvList, processCodeMap);
        this.validProcessCode(repairRcvDetailList, returnList, processCodeMap);
        // Validate status
        this.validStatus(dto, returnList);
        // Set MBom for PmRepairInfoList
        this.setMBomOfPmRepairInfoList(returnList);
        return returnList;
    }
    /* Ended by AICoder, pid:n8c97cc5b041e2f145010ac0b0cacb55a1d9b8da */

    /* Started by AICoder, pid:vc146k23599618214ab60a4720a43d2eda12f774 */

    /**
     * 获取送修记录信息
     * @param snList 条码列表
     * @return 参数
     */
    private List<PmRepairInfo> getPmRepairInfos(List<String> snList) {

        // Set up PmRepairInfoDTO
        PmRepairInfoDTO repairInfo = new PmRepairInfoDTO();
        repairInfo.setInSnList(snList);
        repairInfo.setIsAccept(Constant.STR_0);
        repairInfo.setSort(MpConstant.ORDER_FIELD);
        repairInfo.setOrder(Constant.DESC);

        // Fetch mRepairRcvList
        List<PmRepairInfo> mRepairRcvList = pmRepairInfoService.getRelOnePageNoChange(repairInfo);

        // Throw exception if the list is empty
        if (CollectionUtils.isEmpty(mRepairRcvList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.INPUT_RECORD_NOT_FOUND,
                    new Object[]{snList.toString()});
        }
        // MessageId.INPUT_RECORD_NOT_FOUND
        List<String> snReList = mRepairRcvList.stream().map(PmRepairInfo::getSn).distinct().collect(Collectors.toList());
        snList.removeIf(snReList::contains);
        if (CollectionUtils.isNotEmpty(snList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.INPUT_RECORD_NOT_FOUND,
                    new Object[]{snList.toString()});
        }
        return mRepairRcvList;
    }

    /* Ended by AICoder, pid:vc146k23599618214ab60a4720a43d2eda12f774 */

    /* Started by AICoder, pid:e432243633ra74214d1408a0c029ab35ac49e96e */
    /**
     * 验证是否已经报废
     *
     * @param snList 报废信息列表
     */
    private void validScrapped(List<String> snList) {
        // Set up PmRepairRcvDTO
        PmRepairRcvDTO prrs = new PmRepairRcvDTO();
        prrs.setBillType(MpConstant.REPAIR_BILL_TYPE_TEN);
        prrs.setStatus(Constant.REPAIR_STATUS_SCRAP);
        prrs.setSnList(snList);

        // Fetch repairRcvList
        List<PmRepairRcv> repairRcvList = pmRepairRcvRepository.getRelOneCountList(prrs);
        // 如果报废信息列表为空，直接返回
        if (CollectionUtils.isEmpty(repairRcvList)) {
            return;
        }

        // 提取所有报废信息的 SN 并拼接成逗号分隔的字符串
        String snStr = repairRcvList.stream()
                .map(PmRepairRcv::getSn)
                .collect(Collectors.joining(","));

        // 抛出业务异常，提示 SN 已报废
        throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SN_IS_SCRAPPED, new Object[]{snStr});
    }
    /* Ended by AICoder, pid:e432243633ra74214d1408a0c029ab35ac49e96e */

    /* Started by AICoder, pid:ze17cz2767119e61481c080470dd18261a23268f */
    private void validStatus(PmRepairInfoDTO dto, List<PmRepairInfo> returnList) {
        SysLookupTypesDTO sysLookupTypesDTO = BasicsettingRemoteService.getSysLookUpValue(Constant.SYS_LOOK_DQAS, Constant.LOOKUP_DQAS_REPAIR_CHECK_SWITCH_);
        boolean checkFlag = sysLookupTypesDTO != null && Constant.FLAG_Y.equals(sysLookupTypesDTO.getLookupMeaning());
        List<String> snList = new ArrayList<>();
        for (PmRepairInfo info : returnList) {
            if (info.getStatus() != null) {
                checkRcvListBySn(info, info.getSn());
            } else {
                snList.add(info.getSn());
            }
            returnByZs(dto, info, checkFlag);
        }

        // Check snList after the loop
        if (CollectionUtils.isNotEmpty(snList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.NOT_REPAIR_TWO, new Object[]{snList.toString()});
        }
    }

    /* Ended by AICoder, pid:ze17cz2767119e61481c080470dd18261a23268f */


    /**
     * 获取最新的送修记录信息
     * @param returnList 条码最新返修信息
     * @param mRepairRcvList 所有返修信息
     * @param snProcessMap 工序映射MAP
     */
    private void setProcessMap(List<PmRepairInfo> returnList, List<PmRepairInfo> mRepairRcvList, Map<String, String> snProcessMap) {
        for (PmRepairInfo info : mRepairRcvList) {
            // 相当于获取最新的维修，只取一条
            if (snProcessMap.containsKey(info.getSn())) {
                continue;
            }
            snProcessMap.put(info.getSn(), info.getProcessCode());
            returnList.add(info);
        }
    }

    /**
     * // 维修返还条码扫描中式校验
     * @param dto 参数
     * @param info 参数
     * @param checkFlag 中试校验开关
     */
    private void returnByZs(PmRepairInfoDTO dto, PmRepairInfo info, boolean checkFlag) {
        if(Constant.FROM_STATION_MACHINE.equals(info.getFromStation())){
            if(checkFlag){
                DQASCheckDTO dqasCheckDTO = new DQASCheckDTO(info.getSn(),dto.getFactoryId().toString(),info.getItemCode(), MpConstant.STR_RETURN_REPAIR,dto.getCreateBy());
                boolean dqasCheck = pmRepairRcvService.repairDQASCheck(dqasCheckDTO, new StringBuffer());
                if(!dqasCheck){
                    throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.REPAIR_ZS_VAILDATE_FAIL,
                            new Object[]{info.getSn()});
                }
            }
        }
    }

    /* Started by AICoder, pid:504eaz3c5ark94914d4e09b7f0ec0b2e36693fe8 */
    /**
     * 校验返还子工序
     * @param repairRcvDetailList 返还列表
     * @param returnList 子工序集合
     * @param processCodeMap 工序
     */
    private PmRepairCommonDTO validProcessCode(List<PmRepairRcvDetailDTO> repairRcvDetailList, List<PmRepairInfo> returnList,
                                               Map<String, String> processCodeMap) {
        // 校验返修规则
        Map<String, List<String>> snProcessMap = new HashMap<>();
        PmRepairCommonDTO commonDTO = this.buildSnMapReturnProcessMap(returnList, snProcessMap);
        List<BSProcessDTO> processList = commonDTO.getProcessList();
        Map<String, String> processNameCodeMap = processList.stream()
                .collect(Collectors.toMap(BSProcessDTO::getProcessName, BSProcessDTO::getProcessCode, (k1, k2) -> k1));
        Map<String, BSProcessDTO> processMap = processList.stream()
                .collect(Collectors.toMap(BSProcessDTO::getProcessCode, v -> v, (k1, k2) -> k1));
        StringBuilder errorMsgBuffer = new StringBuilder();
        Map<String, PmRepairInfo> pmMap = returnList.stream()
                .collect(Collectors.toMap(PmRepairInfo::getSn, item -> item, (k1, k2) -> k1));
        for (PmRepairRcvDetailDTO info : repairRcvDetailList) {
            // 非空前端单个
            if (StringUtils.isBlank(info.getProcessCode())) {
                info.setProcessCode(processNameCodeMap.get(info.getProcessName()));
            }
            String sn = info.getSn();
            // 返修记录一定会有
            PmRepairInfo pmRepairInfo = pmMap.getOrDefault(info.getSn(), new PmRepairInfo());
            BSProcessDTO processDTO = processMap.getOrDefault(info.getProcessCode(), new BSProcessDTO());
            // 返还到前工序条码集合
            if (snProcessMap.containsKey(sn)) {
                List<String> processCodeList = snProcessMap.getOrDefault(sn, new LinkedList<>());
                // 前工序没有包含返修子工序， 不能返还
                if (!processCodeList.contains(info.getProcessCode())) {
                    errorMsgBuffer.append(CommonUtils.getLmbMessage(MessageId.SN_REPAIR_PROCESS_FORBID,
                            new String[]{info.getSn(), info.getProcessName()}));
                    continue;
                }
            } else {
                String processCode = processCodeMap.get(info.getSn());
                if (!StringUtils.equals(info.getProcessCode(), processCode)) {
                    errorMsgBuffer.append(CommonUtils.getLmbMessage(MessageId.CRAFT_SECTION_INCONSISTENT_CAN_NOT_BE_RETURNED,
                            new String[]{info.getSn(), info.getProcessName()}));
                    continue;
                }
            }
            pmRepairInfo.setProcessCode(info.getProcessCode());
            pmRepairInfo.setReceptionBy(info.getReceptionBy());
            pmRepairInfo.setReturnedBy(info.getReturnedBy());
            pmRepairInfo.setProcessName(info.getProcessName());
            pmRepairInfo.setCraftSection(processDTO.getCraftSection());
        }
        String msgBufferString = errorMsgBuffer.toString();
        if (StringUtils.isNotBlank(msgBufferString)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.CUSTOMIZE_MSG, new Object[]{msgBufferString});
        }
        return commonDTO;
    }
    /* Ended by AICoder, pid:2d5dax8604ncbd914f6b0b85b0b3b7604c970b5f */

    /* Started by AICoder, pid:fb364va910ee1c414d2b099e40a51a60ba324b71 */

    /**
     * 获取需要校验前工序的条码map
     *
     * @param returnList   录入条码信息
     * @param snProcessMap sn
     * @return 子工序映射
     */
    private PmRepairCommonDTO buildSnMapReturnProcessMap(List<PmRepairInfo> returnList, Map<String, List<String>> snProcessMap) {
        PmRepairCommonDTO commonDTO = new PmRepairCommonDTO();
        commonDTO.setRouteDetailList(new LinkedList<>());
        commonDTO.setWorkOrderBasicList(new LinkedList<>());
        commonDTO.setSnList(new LinkedList<>());
        // 1. 获取送修来源配置数据字典 1146,扩展字段1 为Y 则可以当前工序前的任一工序，入库子工序除外
        List<SysLookupValuesDTO> sysLookupValues = BasicsettingRemoteService.getSysLookupValues(Constant.LOOK_UP_TYPE_1146);
        if (Objects.isNull(sysLookupValues)) {
            sysLookupValues = Collections.emptyList();
        }

        List<String> openList = sysLookupValues.stream()
                .filter(item -> Constant.STR_Y.equals(item.getAttribute1()))
                .map(SysLookupValuesDTO::getLookupMeaning)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());

        // 获取所有子工序
        Map<String, String> map = new HashMap<>();
        map.put("xType", MpConstant.PROCESS_X_TYPE_P);
        List<BSProcessDTO> bsProcessList = CrafttechRemoteService.queryProcessInfo(map);
        Map<String, String> processCodeMap = bsProcessList.stream()
                .collect(Collectors.toMap(BSProcessDTO::getProcessName, BSProcessDTO::getProcessCode, (k1, k2) -> k1));
        commonDTO.setProcessList(bsProcessList);
        String warehouseEntryProcessCode = processCodeMap.getOrDefault(Constant.WAREHOUSE_ENTRY, Constant.FLAG_N);
        // 维修录入信息
        List<PmRepairInfo> checkList = returnList.stream()
                .filter(item -> openList.contains(item.getFromStation())
                        && !StringUtils.equals(warehouseEntryProcessCode, item.getProcessCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(checkList)) {
            return commonDTO;
        }
        List<String> prodplanIdList =
                checkList.stream().map(PmRepairInfo::getRcvProdplanId).distinct().collect(Collectors.toList());
        List<PsWorkOrderBasic> workOrderBasicList = PlanscheduleRemoteService.getWorkBasicByTask(prodplanIdList, null, null);
        // 获取批次指令信息
        Map<String, String> prodIdRouteMap = workOrderBasicList.stream()
                .collect(Collectors.toMap(PsWorkOrderBasic::getSourceTask, PsWorkOrderBasic::getRouteId, (k1, k2) -> k1));
        // 获取指令工艺路径信息
        List<CtRouteDetailDTO> ctRouteDetailByRouteIds = CrafttechRemoteService.getCtRouteDetailByRouteIds(new LinkedList<>(prodIdRouteMap.values()));
        Map<String, List<CtRouteDetailDTO>> routeGroup = ctRouteDetailByRouteIds.stream()
                .collect(Collectors.groupingBy(CtRouteDetailDTO::getRouteId, Collectors.mapping(Function.identity(), Collectors.toList())));
        commonDTO.setRouteDetailList(ctRouteDetailByRouteIds);
        commonDTO.setWorkOrderBasicList(workOrderBasicList);
        // 排序
        routeGroup.forEach((k, v) -> v.sort(Comparator.comparing(CtRouteDetailDTO::getProcessSeq)));
        // 批次工艺路径
         for (PmRepairInfo pmRepairInfo : checkList) {
            String routeId = prodIdRouteMap.getOrDefault(pmRepairInfo.getRcvProdplanId(), StringUtils.EMPTY);
            List<CtRouteDetailDTO> routeDetail = routeGroup.getOrDefault(routeId, Collections.emptyList());
            List<String> beforeProcessCodeList = new LinkedList<>();
            for (CtRouteDetailDTO detailDTO : routeDetail) {
                beforeProcessCodeList.add(detailDTO.getNextProcess());
                if (StringUtils.equals(pmRepairInfo.getProcessCode(), detailDTO.getNextProcess())) {
                    break;
                }
            }
            snProcessMap.put(pmRepairInfo.getSn(), beforeProcessCodeList);
         }
         commonDTO.setSnList(new LinkedList<>(snProcessMap.keySet()));
        return commonDTO;
    }

    /* Ended by AICoder, pid:fb364va910ee1c414d2b099e40a51a60ba324b71 */

    private void checkRcvListBySn(PmRepairInfo pmRepairInfo,String sn) {
        if(pmRepairInfo.getStatus().intValue()==Constant.REPAIR_STATUS_SUBMIT&&(Constant.REPAIR_TWO).equals(pmRepairInfo.getWorkStation())){
            PmRepairRcvDetailDTO pmRepairRcvDetailDTO = new PmRepairRcvDetailDTO();
            pmRepairRcvDetailDTO.setIsAccept(MpConstant.REPAIR_IS_ACCEPT);
            pmRepairRcvDetailDTO.setSn(sn);
            pmRepairRcvDetailDTO.setBillType(MpConstant.REPAIR_BILL_TYPE_TEN);
            pmRepairRcvDetailDTO.setEnabledFlag(Constant.FLAG_Y);
            List<PmRepairRcvDetail> list = pmRepairRcvDetailService.getRelOneList(pmRepairRcvDetailDTO);
            PsWipInfo wipInfo = psWipInfoService.getWipInfoBySn(sn);
            if(wipInfo!=null){
                if(CollectionUtils.isEmpty(list)){
                    pmRepairInfo.setValidMsg(CommonUtils.getLmbMessage(MessageId.NEEDS_TO_BE_REPAIRED_FIRST,pmRepairInfo.getSn()));
                }else{
                    this.checkRcvWipInfoBySn(list, wipInfo, pmRepairInfo);
                }
            }else{
                pmRepairInfo.setValidMsg(CommonUtils.getLmbMessage(MessageId.NO_WIP_INFO_RECORD_OR_NOT_REPAIRING,pmRepairInfo.getSn()));
            }
        }
    }

    private void checkRcvWipInfoBySn(List<PmRepairRcvDetail> list, PsWipInfo wipInfo, PmRepairInfo pmRepairInfo) {
        if(list.get(Constant.INT_0).getFromStation().equals(Constant.REPAIR_FROM_STATION)){
            if((Constant.REPAIR_ING).equals(wipInfo.getCurrProcessCode())){
                pmRepairInfo.setValidMsg(MpConstant.RESULT_TYPE_OK);
            }else{
                pmRepairInfo.setValidMsg(CommonUtils.getLmbMessage(MessageId.NOT_BE_REPAIRING,pmRepairInfo.getSn()));
            }
        }else {
            pmRepairInfo.setValidMsg(MpConstant.RESULT_TYPE_OK);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public long updatePmRepairRcvDetailBatch(List<PmRepairRcvDetailDTO> record) {
        if (CollectionUtils.isEmpty(record)) {
            return 0;
        }
        Pair<String, String> pair = RequestHeadValidationUtil.validaFactoryIdAndEmpno();
        record.forEach(item->item.setLastUpdatedBy(pair.getSecond()));
        List<RedisLock> redisList = new ArrayList<>();
        try {
            // 批量加锁
            this.generateRedisLock(record, redisList);
            // 校验条码及批次是否在锁定中
            this.checkSnExistInLock(record);
            return this.updatePmRepairRcvDetailBatchOld(record);
        } finally {
            redisList.forEach(RedisLock::unlock);
        }
    }

    /**
     * 校验条码锁定
     * @param recordList 数据
     */
    private void checkSnExistInLock(List<PmRepairRcvDetailDTO> recordList) {
        List<String> lockCheckList = recordList.stream().map(PmRepairRcvDetailDTO::getSn)
                .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        List<PsWipInfo> listByBatchSn = psWipInfoRepository.getListByBatchSn(lockCheckList);
        List<String> batchList = listByBatchSn.stream().map(PsWipInfo::getAttribute1)
                .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        batchList.addAll(lockCheckList);
        List<BarcodeLockDetail> lockDetailList = barcodeLockDetailRepository.queryLockDetail(batchList, null);
        if (CollectionUtils.isNotEmpty(lockDetailList)) {
            List<String> billNoList = lockDetailList.stream().map(BarcodeLockDetail::getBillNo).distinct().collect(Collectors.toList());
            // 单据下是否存在已解锁的条码
            List<BarcodeLockTemp> list = barcodeLockTempRepository.selectUnLockInfo(billNoList, lockCheckList);
            if (!CollectionUtils.isEmpty(list)) {
                filterSnUnLock(lockCheckList, lockDetailList, list,listByBatchSn);
            } else {
                List<String> errorList = lockDetailList.stream().map(BarcodeLockDetail::getBatchSn).distinct().collect(Collectors.toList());
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.RETURN_INFO_IS_ON_LOCK, new Object[]{StringUtils.join(errorList, Constant.COMMA)});
            }
        }
    }

    private void filterSnUnLock(List<String> lockCheckList, List<BarcodeLockDetail> lockDetailList, List<BarcodeLockTemp> list,List<PsWipInfo> listByBatchSn) {
        List<String> errorList = new ArrayList<>();
        errorList.addAll(lockCheckList);
        Map<String, String> snMap = listByBatchSn.stream().collect(Collectors.toMap(PsWipInfo::getSn, PsWipInfo::getAttribute1, (k1, k2) -> k1));
        // 筛选掉已在所有锁定单据中解锁的条码
        for (String lockSn : lockCheckList) {
            // 获取条码或所属批次的锁定单
            String lockTemp = snMap.get(lockSn);
            String lockBatchSn = StringUtils.isEmpty(lockTemp)?Constant.EMPTY_STRING:lockTemp;
            List<String> lockBillNoTemp = lockDetailList.stream().filter(l->lockSn.equals(l.getBatchSn()) || lockBatchSn.equals(l.getBatchSn()))
                    .map(BarcodeLockDetail::getBillNo)
                    .distinct().collect(Collectors.toList());
            if(CollectionUtils.isEmpty(lockBillNoTemp)){
                errorList.remove(lockSn);
                continue;
            }
            // 是否所有锁定单下都有该条码的解锁记录
            List<String> unLockBillNoTemp = list.stream().filter(u->lockBillNoTemp.contains(u.getBillNo()) && lockSn.equals(u.getSn()))
                    .map(BarcodeLockTemp::getBillNo)
                    .distinct().collect(Collectors.toList());
            if(lockBillNoTemp.size() == unLockBillNoTemp.size()){
                errorList.remove(lockSn);
            }
        }
        if (!CollectionUtils.isEmpty(errorList)) {
            errorList = errorList.stream().filter(e -> StringUtils.isNotEmpty(e)).distinct().collect(Collectors.toList());
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.RETURN_INFO_IS_ON_LOCK, new Object[]{StringUtils.join(errorList, Constant.COMMA)});
        }
    }

    /* Started by AICoder, pid:fb364va910ee1c414d2b099e40a51a60ba324b71 */
    private long updatePmRepairRcvDetailBatchOld(List<PmRepairRcvDetailDTO> record) {
        // 终端返还校验
        if (MpConstant.PAGE_TYPE_TERMINAL.equals(record.get(0).getPageType())) {
            return updatePmRepairRcvDetailBatchTerminal(record);
        }
        // 校验返还
        List<String> snList = record.stream().map(PmRepairRcvDetailDTO::getSn).collect(Collectors.toList());
        // 获取条码送修记录信息
        List<PmRepairInfo> mRepairRcvList = this.getPmRepairInfos(snList);
        // Initialize returnList and craftSectionMap
        List<PmRepairInfo> returnList = new ArrayList<>();
        Map<String, String> processCodeMap = new HashMap<>();
        // Set craft sections and validate them
        this.setProcessMap(returnList, mRepairRcvList, processCodeMap);
        PmRepairCommonDTO commonDTO = this.validProcessCode(record, returnList, processCodeMap);
        // 增加开关控制是否需要返还到原送修工序（各送修来源分开控制，功能上线时默认关闭），
        // 开关打开即允许返还至该条码所做过的历史工序(送修子工序前任一子工序)，开关关闭时只允许返还至送修时子工序、工站。
//       // 如果返还指令以及送修指令状态为已完工，更新指令状态为 零星板挂起
        List<String> updateWorkNoList = this.buildUpdateDataByRule(record, commonDTO, returnList, processCodeMap);
        // 返还
        this.insert(MpConstant.REPAIR_VALID_TIP_RP, record);
        // 更新指令已完工状态为 零星板挂起
        PlanscheduleRemoteService.updateWorkOrderStatusBatch(updateWorkNoList, Constant.DONE, Constant.WORK_FINISH_BREAK_STATUS);
        List<SemiManufactureDealInfo> machineList = new ArrayList<>();
        List<SemiManufactureDealInfo> seimsList = new ArrayList<>();
        //更新维修信息状态为维修返还，组装交易数据数据
        this.updateRepairBuildTran(record, machineList, seimsList);
        //  2019/11/6  返还整机中式推送
        String productType = psWipInfoServiceImpl.getExternalType(record.get(NumConstant.NUM_ZERO).getSn());
        this.pushDataToDqs(record, productType);
        //整机 维修返还 批量插入交易表 6055000040
        this.handlerSemi(machineList, seimsList, productType);
        return Constant.INT_1;
    }
    /* Ended by AICoder, pid:f9e3726f2284a4914abc085e60465357e260325e */

    /* Started by AICoder, pid:h4b4570f25yfb2f14caa0af460b09690933097ad */
    /**
     * 获取需要更新的数据集合
     * @param record 返还记录信息
     * @param commonDTO 通用参数
     * @param returnList 返修记录信息
     * @param processCodeMap 返修记录工序
     * @return 需要更新状态的指令
     */
    private List<String> buildUpdateDataByRule(List<PmRepairRcvDetailDTO> record, PmRepairCommonDTO commonDTO, List<PmRepairInfo> returnList, Map<String, String> processCodeMap) {
        List<String> updateSnList = commonDTO.getSnList();
        Map<String, List<PsWorkOrderBasic>> sourceGroupMap = commonDTO.getWorkOrderBasicList().stream()
                .collect(Collectors.groupingBy(PsWorkOrderBasic::getSourceTask));
        Map<String, List<CtRouteDetailDTO>> routeDeatilGroupMap = commonDTO.getRouteDetailList().stream()
                .collect(Collectors.groupingBy(CtRouteDetailDTO::getRouteId));
        routeDeatilGroupMap.forEach((k, v) -> v.sort(Comparator.comparing(CtRouteDetailDTO::getProcessSeq)));
        List<String> updateWorkNoList = new LinkedList<>();
        for (PmRepairRcvDetailDTO rcvDetailDTO : record) {
            rcvDetailDTO.setUpdateProcess(false);
            // 需要更新指令，子工序等信息的条码
            if (updateSnList.contains(rcvDetailDTO.getSn())) {
                List<PsWorkOrderBasic> defaultList = sourceGroupMap.getOrDefault(rcvDetailDTO.getRcvProdplanId(), new LinkedList<>());
                PsWorkOrderBasic psWorkOrderBasic = defaultList.stream()
                        .filter(item -> StringUtils.isNotBlank(item.getProcessGroup()))
                        .filter(item -> {
                            String[] split = item.getProcessGroup().split(Constant.STR_SPLIT_$);
                            List<String> list = Arrays.asList(split);
                            return list.contains(rcvDetailDTO.getProcessCode());
                        }).findFirst().orElse(new PsWorkOrderBasic());
                // 指令表更新指令为当前指令
                rcvDetailDTO.setWorkOrderNo(psWorkOrderBasic.getWorkOrderNo());
                updateWorkNoList.add(psWorkOrderBasic.getWorkOrderNo());
                // 当前指令到返修工序指令，是已完工全部更新为零星板挂起
                List<CtRouteDetailDTO> routeDetail = routeDeatilGroupMap.getOrDefault(psWorkOrderBasic.getRouteId(), new LinkedList<>());
                // 更新状态子工序
                List<String> updateProcessList = this.getUpdateProcessList(processCodeMap, rcvDetailDTO, routeDetail);
                // 更新状态指令
                List<String> workUpdate = defaultList.stream()
                        .filter(item -> StringUtils.isNotBlank(item.getProcessGroup()))
                        .filter(item -> {
                            boolean containFlag = false;
                            String[] split = item.getProcessGroup().split(Constant.STR_SPLIT_$);
                            for (String process : split) {
                                if (updateProcessList.contains(process)) {
                                    containFlag = true;
                                    break;
                                }
                            }
                            return containFlag;
                        }).map(PsWorkOrderBasic::getWorkOrderNo).collect(Collectors.toList());
                updateWorkNoList.addAll(workUpdate);
                rcvDetailDTO.setUpdateProcess(Boolean.TRUE);
            }
        }
        return updateWorkNoList.stream().distinct().collect(Collectors.toList());
    }

    /**
     * 获取需要更新的指令集合
     * @param processCodeMap  processCodeMap
     * @param rcvDetailDTO rcvDetailDTO
     * @param routeDetail 工艺明细
     * @return
     */
    private List<String> getUpdateProcessList(Map<String, String> processCodeMap, PmRepairRcvDetailDTO rcvDetailDTO, List<CtRouteDetailDTO> routeDetail) {
        List<String> updateProcessList = new LinkedList<>();
        int startSeq = Integer.MAX_VALUE;
        int endSeq = Integer.MAX_VALUE;
        // 送修子工序
        String processChoose = processCodeMap.get(rcvDetailDTO.getSn());
        for (CtRouteDetailDTO detailDTO : routeDetail) {
            BigDecimal processSeq = detailDTO.getProcessSeq();
            if (Objects.isNull(processSeq)) {
                processSeq = BigDecimal.ZERO;
            }
            int intValue = processSeq.intValue();
            // 开始指令
            if (StringUtils.equals(detailDTO.getNextProcess(), rcvDetailDTO.getProcessCode())) {
                startSeq = intValue;
                updateProcessList.add(detailDTO.getNextProcess());
            }
            //结束指令
            if (StringUtils.equals(detailDTO.getNextProcess(), processChoose)) {
                updateProcessList.add(detailDTO.getNextProcess());
                endSeq = intValue;
            }
            if (startSeq <= intValue && endSeq >= intValue) {
                updateProcessList.add(detailDTO.getNextProcess());
            }
        }
        return updateProcessList.stream().distinct().collect(Collectors.toList());
    }
    /* Ended by AICoder, pid:h4b4570f25yfb2f14caa0af460b09690933097ad */

    /**
     * 推送返修信息到中试
     * @param record 返修信息
     * @param productType 产品大类
     */
    private void pushDataToDqs(List<PmRepairRcvDetailDTO> record, String productType) {
        SysLookupTypesDTO sysLookupTypesDTO = BasicsettingRemoteService.getSysLookUpValue(Constant.SYS_LOOK_DQAS, Constant.LOOKUP_DQAS_REPAIR_SAVE_SWITCH_);
        if (Objects.nonNull(sysLookupTypesDTO) && Constant.FLAG_Y.equals(sysLookupTypesDTO.getLookupMeaning())) {
            boolean dqasCheck = pmRepairRcvRecodeService.sendRepairDQAS(packageRcvPostData(record, productType));
            if (!dqasCheck) {// 抛异常   回滚事务
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.REPAIR_ZS_VAILDATE_FAIL);
            }
        }
    }

    /**
     * 终端返还
     */
    public long updatePmRepairRcvDetailBatchTerminal(List<PmRepairRcvDetailDTO> records) {
        // 校验批次数量
        if (records == null || records.size() <= 0 || records.size() > Constant.BATCH_SIZE) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.REPAIR_RETURN_QTY_ERROR);
        }
        // 需调用MES处理的
        List<RepairRestoreDTO> repairRestoreDTOS = new ArrayList<>();
        // 校验传入的各个条码
        for (PmRepairRcvDetailDTO dto : records) {
            PmRepairInfoDTO queryDto = new PmRepairInfoDTO();
            queryDto.setSn(dto.getSn());
            queryDto.setSort(MpConstant.ORDER_FIELD);
            queryDto.setOrder(Constant.DESC);
            List<PmRepairInfo> pmRepairRcvs = pmRepairInfoService.getRelOnePageNoChange(queryDto);
            // 校验录入记录
            if (pmRepairRcvs == null || pmRepairRcvs.size() == 0) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,
                        MessageId.INPUT_RECORD_NOT_FOUND, new Object[]{dto.getSn()});
            }
            PmRepairInfo pmRepairInfo = pmRepairRcvs.get(0);
            // 校验状态是否为维修完成
            if (!Constant.REPAIR_STATUS_COMPLETE.equals(pmRepairInfo.getRepairStatus())) {
                String repairStatus = pmRepairInfo.getRepairStatus();
                String repairStatusMsg = RepairStatusUtil.getRepairStatusMessage(repairStatus);
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,
                        MessageId.REPAIR_STATUS_NOT_COMPLETE_CAN_NOT_BE_RETURNED, new Object[]{dto.getSn(), repairStatusMsg});
            }
            // 去向为“组装段”(数据字典配置)时需要修改MES二维交易数据
            if (Constant.FROM_STATION_ASSEMBLE.equals(dto.getFromStation())) {
                RepairRestoreDTO repairRestoreDTO = new RepairRestoreDTO(dto.getSn(), pmRepairInfo.getResult());
                repairRestoreDTOS.add(repairRestoreDTO);
            }
        }

        // 修改维修状态
        Date date = new Date();
        for (PmRepairRcvDetailDTO dto : records) {
            //返还更新状态维修返还
            dto.setStatus(Constant.REPAIR_STATUS_RESTORE);
            dto.setIsAccept(MpConstant.REPAIR_IS_NOT_ACCEPT);
            dto.setRepairRcvDate(date);
        }
        pmRepairRcvRepository.updatePmRepairRcvDetailBatch(records);
        // 处理FIS删除数据
        deleteTestData(records);
        try {
            // 修改MES二维数据
            mesRepairService.repairRestore(repairRestoreDTOS);
        } catch (Exception e) {
            // 修改MES二维数据失败 也返回成功
            logger.error("修改MES二维数据失败", e);
            mesRepairService.logError(repairRestoreDTOS);
        }
        return Constant.INT_1;
    }

    /**
     * 处理FIS删除数据
     */
    private void deleteTestData(List<PmRepairRcvDetailDTO> records) {
        // 获取需删除FIS条码列表
        List<PmRepairRcvDetailDTO> delFisSns = records.stream()
                .filter(p -> p != null && !StringUtils.isEmpty(p.getSn()) && p.getDelTestData())
                .collect(Collectors.toList());
        // 处理FIS数据
        fisService.deleteTestData(delFisSns);
    }

    /**
     * 加锁
     * @param recordList 记录
     * @param redisList 锁集合
     */
    private void generateRedisLock(List<PmRepairRcvDetailDTO> recordList, List<RedisLock> redisList) {
        for (PmRepairRcvDetailDTO info : recordList) {
            RedisLock redisLock = new RedisLock(Constant.OM_REPAIR_RETURN + info.getSn());
            boolean lock = redisLock.lock();
            if (!lock) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SN_IS_NOT_UNLOCK, new Object[]{info.getSn()});
            }
            redisList.add(redisLock);
        }
    }

    private void handlerSemi(List<SemiManufactureDealInfo> machineList,List<SemiManufactureDealInfo> seimsList ,String productType){
        //整机 维修返还 批量插入交易表 6055000040
        if(CollectionUtils.isNotEmpty(machineList)){
            for(SemiManufactureDealInfo semiManufactureDealInfo:machineList){
                JSONObject remark = new JSONObject();
                remark.put("productType",productType);
                semiManufactureDealInfo.setRemark(remark.toJSONString());
            }
            pmRepairRcvRecodeService.insertMachine(machineList,true);
        }
        //半成品 维修返还 批量插入交易表 6055000040
        if(CollectionUtils.isNotEmpty(seimsList)){
            pmRepairRcvRecodeService.insertMachine(seimsList);
        }
    }

    /**
     * 封装接口参数
     * @param pmRepairRcvDetailDTOs
     * @return
     */
    private Map<String, Object> packageRcvPostData(List<PmRepairRcvDetailDTO> pmRepairRcvDetailDTOs,String productType) {
        Map<String, Object> postMap = Maps.newHashMap();
        List<String> barcodes = new ArrayList<String>();
        for(PmRepairRcvDetailDTO pmRepairRcvDTO:pmRepairRcvDetailDTOs){
            barcodes.add(pmRepairRcvDTO.getSn());
        }
        postMap.put("productType",productType);
        postMap.put("barcodes",barcodes);
        postMap.put("scanningTime", DateUtil.convertDateToString(new Date(),DateUtil.DATE_FORMATE_FULL));
        //  2019/11/6  物料代码已确认 删除
//                postMap.put("machineMaterialcode","sss");
        postMap.put("processName",Constant.CRAFTSECTION_REPAIR);
        postMap.put("station",Constant.CRAFTSECTION_REPAIR);
        postMap.put("barcodeFrom",Constant.FROM_STATION_MACHINE.equals(pmRepairRcvDetailDTOs.get(NumConstant.NUM_ZERO).getFromStation())?Constant.FROM_STATION_MACHINE:Constant.STEP);
        postMap.put("status",Constant.INT_2);
        postMap.put("userId",pmRepairRcvDetailDTOs.get(NumConstant.NUM_ZERO).getLastUpdatedBy());
        return postMap;
    }

    /**
     * 方法外提
     *
     * @param listRecord
     * @param machineList
     * @param seimsList
     * <AUTHOR>
     */
    private void updateRepairBuildTran(List<PmRepairRcvDetailDTO> listRecord, List<SemiManufactureDealInfo> machineList,
     List<SemiManufactureDealInfo> seimsList) {
        List<List<PmRepairRcvDetailDTO>> listOfList = CommonUtils.splitList(listRecord, Constant.INT_100);
        Date date = new Date();
        for (List<PmRepairRcvDetailDTO> list : listOfList) {
            for (PmRepairRcvDetailDTO dto : list) {
                //返还更新状态维修返还
                dto.setStatus(Constant.REPAIR_STATUS_RESTORE);
                dto.setRepairRcvDate(date);
                //整机维修返还 插入表 插入表 605500040
                this.machineRepairRcv(dto, machineList);
                //半成品 维修返还 插入表 插入表 605500040
                this.seimsRepairRcv(dto, seimsList);
            }
            pmRepairRcvRepository.updatePmRepairRcvDetailBatch(list);
        }

    }


    /**
     * 方法外提
     * <AUTHOR>
     * @param bsProcessForWorkStation
     */
    private Map<String, String> getWorkStation(List<BSProcessDTO> bsProcessForWorkStation) {
        Map<String, String> workStationMap = new HashMap<>();
        // 工站
        for (BSProcessDTO dto : bsProcessForWorkStation) {
            if (MpConstant.PROCESS_NAME_DIP_DOWNLINE.equals(dto.getProcessName())) {
                workStationMap.put(Constant.DIP_OFFLINE_SCAN, dto.getProcessCode());
            }
            if (MpConstant.PROCESS_NAME_ASSY_BIND.equals(dto.getProcessName())) {
                workStationMap.put(Constant.ASSY_BINDING, dto.getProcessCode());
            }
            if (MpConstant.PROCESS_NAME_ASSY_UPLINE.equals(dto.getProcessName())) {
                workStationMap.put("assyOnline", dto.getProcessCode());
            }
        }
        return workStationMap;
    }

    /**
     * 方法外提
     * <AUTHOR>
     * @param bsProcessList
     * @param processMap
     */
    private void getProcessName(List<BSProcessDTO> bsProcessList, Map<String, String> processMap) {
        for (BSProcessDTO dto : bsProcessList) {
            changeProcessName(dto, processMap);
        }
    }

    /**
     * 方法外提
     * <AUTHOR>
     * @param dto
     * @param processMap
     */
    private void changeProcessName(BSProcessDTO dto,Map<String, String> processMap) {
        if((Constant.REPAIR_COMPLETE).equals(dto.getProcessCode())) {
            processMap.put(Constant.RC_CRAFT_SECTION, dto.getCraftSection());
        }
        if((Constant.REPAIR_SCRAP).equals(dto.getProcessCode())) {
            processMap.put("rsCraftSection", dto.getCraftSection());
        }
        if (MpConstant.PROCESS_NAME_DIP.equals(dto.getProcessName())) {
            processMap.put(Constant.DIP_STR, dto.getProcessCode());
        }
        if (MpConstant.PROCESS_NAME_ASSY_INPUT.equals(dto.getProcessName())) {
            processMap.put("assyInput", dto.getProcessCode());
        }

        if (MpConstant.PROCESS_NAME_ASSY_OUTPUT.equals(dto.getProcessName())) {
            processMap.put(Constant.ASSY_OUT_PUT_STR, dto.getProcessCode());
        }
        if((Constant.WAREHOUSE_ENTRY).equals(dto.getProcessName())) {
            processMap.put("weProcessCode", dto.getProcessCode());
            processMap.put("weCrftSection", dto.getCraftSection());
        }
    }
    //校验主工序
    private void validWeProcessCode(List<PmRepairRcvDetail> list,PsWipInfo psWipInfo, String weProcessCode,String weCraftSection){
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        //        送修来源是单板生产或者整机单板送修
        if(Constant.REPAIR_FROM_STATION.equals(list.get(Constant.INT_0).getFromStation()) || Constant.REPAIR_FROM_ZJ_DB.equals(list.get(Constant.INT_0).getFromStation())) {
            psWipInfo.setWorkStation(" ");
            if(StringUtils.isBlank(weProcessCode)
                    || StringUtils.isBlank(weCraftSection)) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,MessageId.CRAFT_SECTION_NOT_LOADED);
            }
            psWipInfo.setCurrProcessCode(weProcessCode);
            psWipInfo.setCraftSection(weCraftSection);
        }else {
            psWipInfo.setWorkStation(list.get(Constant.INT_0).getWorkStation());
            psWipInfo.setCurrProcessCode(list.get(Constant.INT_0).getProcessCode());
            psWipInfo.setCraftSection(list.get(Constant.INT_0).getCraftSection());
        }
    }

    //拼接in查询字符串
    private String getInStrs(List<PmRepairRcvDetailDTO> detailIdList) {
        StringBuilder sb = new StringBuilder();
        for (PmRepairRcvDetailDTO detail : detailIdList) {
            sb.append(Constant.SINGLE_QUOTE).append(detail.getReceptionDetailId()).append(Constant.SINGLE_QUOTE).append(Constant.COMMA);
        }
        String sns = sb.toString();
        sns = sns.substring(0, sns.length() - 1);
        return sns;
    }

    //获取开关
    private String getSwitchStr(String lookupType,String lookupCode){
        String switchStr = Constant.FLAG_N;
        SysLookupTypesDTO sysLookupTypesDTO=BasicsettingRemoteService.getSysLookUpValue(lookupType,lookupCode);
        if(sysLookupTypesDTO != null){
            switchStr = sysLookupTypesDTO.getLookupMeaning();
        }
        return switchStr;
    }

    //维修领料校验
    public void checkMaintenanceMaterial(List<PmRepairRcvDetailDTO> list){
        // 开关
        String switchStr=getSwitchStr(MpConstant.LOOKUP_6672,MpConstant.LOOKUP_6672002);
        if(!StringUtils.equals(Constant.FLAG_Y,switchStr)){
            return ;
        }
        //查询领料记录
        List<MaintenanceMaterialInfoDTO> maintenanceMaterialInfoDTOS = getMaintenanceMaterialInfoDTOS(list);
        //如果没有领料记录则跳过该校验
        if(CollectionUtils.isEmpty(maintenanceMaterialInfoDTOS)){
            return;
        }
        //领料记录中是否包含物料状态为1  已提交  2 待确认  3 已确认   4 已领料   5 已发料   8 拟制中 记录，如果存在则报错
        List<MaintenanceMaterialInfoDTO> colletList = maintenanceMaterialInfoDTOS.stream().filter(p -> this.checkResourceStaus(p.getItemStaus()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(colletList)) {
            List<String> errorSnList=colletList.stream().map(MaintenanceMaterialInfoDTO::getMainSn).collect(Collectors.toList());
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.MATERIAL_REQUISITION_RECORD_NOT_BEEN_PROCESSED, new Object[]{StringUtils.join(errorSnList,Constant.COMMA)});
        }
        //取有效的领料记录（物料状态为“已收料”可能存在多个领料记录）
        List<MaintenanceMaterialInfoDTO> issuedList = maintenanceMaterialInfoDTOS.stream().filter(p -> MpConstant.RESOURCE_STATUS_RECEIVED.equals(p.getItemStaus()))
                .collect(Collectors.toList());
        List<String> receptionDetailIdList=issuedList.stream().map(MaintenanceMaterialInfoDTO::getReceptionDetailId).collect(Collectors.toList());
        //如果没有已发料领料记录则跳过该校验
        if(CollectionUtils.isEmpty(issuedList)){
            return;
        }
        //根据已发料记录的条码，送修单号获取维修记录（根据送修单号（未返还）获取维修记录）
        //获取维修记录（根据id获取维修记录）
        Map<String, List<PmRepairDetailDTO>> pmRepairDetailDTOListMap= getPmRepairDetailList(receptionDetailIdList);
        //循环校验领料记录
        checkMaintenaceMaterialInfo(issuedList, pmRepairDetailDTOListMap);
    }

    //查询领料记录
    private List<MaintenanceMaterialInfoDTO> getMaintenanceMaterialInfoDTOS(List<PmRepairRcvDetailDTO> list) {
        List<MaintenanceMaterialInfoDTO> returnList=new ArrayList<>();
        if(org.apache.commons.collections4.CollectionUtils.isEmpty(list)){
            return returnList;
        }
        List<List<PmRepairRcvDetailDTO>> splitList = CommonUtils.splitList(list, NumConstant.NUM_999);
        for(List<PmRepairRcvDetailDTO> tempList:splitList){
            MaintenanceMaterialInfoDTO maintenanceMaterialInfoDTO=new MaintenanceMaterialInfoDTO();
            maintenanceMaterialInfoDTO.setReceptionDetailIds(getInStrs(tempList));
            returnList.addAll(maintenanceMaterialLineRepository.getListByReceptionDetailIds(maintenanceMaterialInfoDTO));
        }
        return returnList;
    }

    //循环校验领料记录
    private void checkMaintenaceMaterialInfo(List<MaintenanceMaterialInfoDTO> maintenanceMaterialInfoDTOS, Map<String, List<PmRepairDetailDTO>> pmRepairDetailDTOListMap) {
        for (MaintenanceMaterialInfoDTO dto : maintenanceMaterialInfoDTOS) {
            List<PmRepairDetailDTO > tempList= pmRepairDetailDTOListMap == null?new ArrayList<>():pmRepairDetailDTOListMap.get(dto.getReceptionDetailId());
            //1  物料更换类型为“以旧换新”：判断该条码的维修记录中是否包含有输入更换后条码，且位号信息与维修领料中的位号信息是否一致，维修次小类为“更换器件”(数据字典配置6671)
            //不存在则 XX维修领料单中的XXX物料未使用，不允许返还
            if (StringUtils.equals(MpConstant.RESOURCE_CHANGE_TYPE_RENEW, dto.getChangeType())&&!checkSnAndLocactionNo(dto,tempList)) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.REPAIR_MATERIAL_REQUISITION_IS_NOT_USED, new Object[]{dto.getMaterialRequisitionBill(),dto.getItemNo()});
            // 物料更换类型为“新领”：判断该条码的维修记录中的位号信息是否与维修领料中的位号信息是否有一致
            }else  if (StringUtils.equals(MpConstant.RESOURCE_CHANGE_TYPE_RECEIVE_NEW, dto.getChangeType())&&!checkLocactionNo(dto,tempList)) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.REPAIR_MATERIAL_REQUISITION_IS_NOT_USED, new Object[]{dto.getMaterialRequisitionBill(),dto.getItemNo()});
            }
            //子条码不为空时 判断该条码的维修记录是否有定位为子部件故障且子部件条码等于领料记录子板条码,不存在则报错：未找到与领料记录子部件条码一致的维修记录，请确认！
            if(StringUtils.isNotEmpty(dto.getSubSn())&&!checkIsSub(dto,tempList)){
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.NO_MAINTENANCE_RECORD_SUB_SN_CONSISTENT, new Object[]{dto.getDeliveryNo(),dto.getSubSn()});
            }
        }
    }

    //判断该条码的维修记录（根据送修单号（未返还）获取维修记录）是否有定位为子部件故障且子部件条码等于领料记录子板条码
    public boolean checkIsSub(MaintenanceMaterialInfoDTO dto,List<PmRepairDetailDTO > tempList){
        if(CollectionUtils.isEmpty(tempList)){
            return false;
        }
        List<PmRepairDetailDTO > filterList=tempList.stream().filter(p -> StringUtils.equals(dto.getSubSn(),p.getSubItemSn())&&StringUtils.equals(Constant.FLAG_Y,p.getIsSub()))
                .collect(Collectors.toList());
        return CollectionUtils.isEmpty(filterList)?false:true;
    }

    //判断该条码的维修记录（根据送修单号（未返还）获取维修记录）中的位号信息是否与维修领料中的位号信息是否有一致
    public boolean checkLocactionNo(MaintenanceMaterialInfoDTO dto,List<PmRepairDetailDTO > tempList){
        if(CollectionUtils.isEmpty(tempList)){
            return false;
        }
        List<PmRepairDetailDTO > filterList=tempList.stream().filter(p -> StringUtils.equalsIgnoreCase(dto.getLocationNo(),p.getLocationNo()))
                .collect(Collectors.toList());
        return CollectionUtils.isEmpty(filterList)?false:true;
    }

    //判断该条码的维修记录（根据送修单号（未返还）获取维修记录）中是否包含有输入更换后条码，且位号信息与维修领料中的位号信息是否一致，维修次小类为“更换器件”(数据字典配置)
    public boolean checkSnAndLocactionNo(MaintenanceMaterialInfoDTO dto,List<PmRepairDetailDTO> tempList) {
        if(CollectionUtils.isEmpty(tempList)){
            return false;
        }
        // 维修状态数据字典
        List<SysLookupTypesDTO> sysLookupTypesDTOS=BasicsettingRemoteService.getSysLookUpValue(MpConstant.LOOKUP_6671);
        if(CollectionUtils.isEmpty(sysLookupTypesDTOS)){
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,MessageId.SYS_LOOKUPTYPE_6671_IS_NULL);
        }

        List<PmRepairDetailDTO> filterList=tempList.stream().filter(p -> StringUtils.isNotEmpty(p.getReplaceSn())&&checkSysLookUp(sysLookupTypesDTOS,p.getRepairProductMstype())&&
                StringUtils.equalsIgnoreCase(dto.getLocationNo(),p.getLocationNo()))
                .collect(Collectors.toList());
        return CollectionUtils.isEmpty(filterList)?false:true;
    }
    //校验维修次小类是否在配置中
    private boolean checkSysLookUp (List<SysLookupTypesDTO> sysLookupTypesDTOS,String repairProductMstype){
        for(SysLookupTypesDTO dto:sysLookupTypesDTOS){
            if(StringUtils.equals(dto.getLookupMeaning(),repairProductMstype)){
                return true;
            }
        }
        return false;
    }

    //获取维修记录（根据sn,送修单号（未返还）获取维修记录）
    private Map<String, List<PmRepairDetailDTO>> getPmRepairDetailList(List<String> receptionDetailIdList) {
        //去重
        removeDuplicate(receptionDetailIdList);
        List<PmRepairDetailDTO> ppddList=pmRepairDetailService.getListByReceptionDetailId(receptionDetailIdList);
        //没有维修记录则报错
        if(CollectionUtils.isEmpty(ppddList)){
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,MessageId.REPAIR_RECORD_NOT_FOUND);
        }
        //按送修单id分组排序
        return  ppddList.stream().collect(Collectors.groupingBy(PmRepairDetailDTO::getReceptionDetailId));
    }

    //物料状态为1  已提交  2 待确认  3 已确认   4 已领料   5 已发料   8 拟制中
    public boolean checkResourceStaus(String status){
        if(StringUtils.equals(MpConstant.RESOURCE_STATUS_UNDER_PREPARATION,status)||StringUtils.equals(MpConstant.RESOURCE_STATUS_CONFIRMED,status)||
                StringUtils.equals(MpConstant.RESOURCE_STATUS_SUBMITTED,status)||StringUtils.equals(MpConstant.RESOURCE_STATUS_ISSUED_MATERIALS,status)||
                StringUtils.equals(MpConstant.RESOURCE_STATUS_TO_CONFIRMED,status)||StringUtils.equals(MpConstant.RESOURCE_STATUS_MATERIAL_PICKED,status)){
            return true;
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insert(String valieTip, List<PmRepairRcvDetailDTO> record) {
        Map<String, String> processMap = this.getStationMap();
        //点对点查询子工序 6055000040
        Map<String, String> mapForProcessCode = new HashMap<>();
        mapForProcessCode.put("xType", MpConstant.PROCESS_X_TYPE_P);
        List<BSProcessDTO> bsProcessList = CrafttechRemoteService.queryProcessInfo(mapForProcessCode);
        this.getProcessName(bsProcessList, processMap);

        Map<String, Object> scanHistory = new HashMap<>();
        scanHistory.put("currProcessCode", processMap.get(Constant.DIP_STR));
        scanHistory.put("orderField", MpConstant.CREATE_DATE);
        PmRepairRcvDetailParamDTO pmRepairRcvDetailParamDTO = new PmRepairRcvDetailParamDTO();
        List<PsWipInfo> psWipInfosFromScanHistoryList = new ArrayList<>();
        pmRepairRcvDetailParamDTO.setPsWipInfosFromScanHistory(psWipInfosFromScanHistoryList);
        pmRepairRcvDetailParamDTO.setValieTip(valieTip);
        pmRepairRcvDetailParamDTO.setRecord(record);
        List<PsWipInfo> psWipInfos = new ArrayList<>();
        pmRepairRcvDetailParamDTO.setPsWipInfos(psWipInfos);
        List<PsScanHistory> psScanHistory = new ArrayList<>();
        pmRepairRcvDetailParamDTO.setPsScanHistorys(psScanHistory);
        StringBuilder sns = new StringBuilder();
        pmRepairRcvDetailParamDTO.setSns(sns);
        pmRepairRcvDetailParamDTO.setCurrProcessCode(Constant.STR_EMPTY);
        pmRepairRcvDetailParamDTO.setScanHistory(scanHistory);
        pmRepairRcvDetailParamDTO.setProcessMap(processMap);
        // 当前工序
        String currProcessCode = this.validAndInitReturnInfo(pmRepairRcvDetailParamDTO);
        //更新在制表，历史表
        pmRepairInfoService.oneDimensional(psWipInfos, psScanHistory);
        //中试的需更新在制表数据(从扫描历史表)，历史表为空字段也需要更新到在制表 10270446
        if (CollectionUtils.isNotEmpty(psWipInfosFromScanHistoryList)) {
            psWipInfoRepository.updatePsWipInfoFromPsScanHistoryBatch(psWipInfosFromScanHistoryList);
        }
        pmRepairInfoService.timeProduction(sns, currProcessCode, Constant.REPAIR_TWO);
    }

    /**
     * 获取特定工站map
     * @return 工站map
     */
    private Map<String, String> getStationMap() {
        Map<String, String> mapForWorkStation = new HashMap<>();
        mapForWorkStation.put("xType", MpConstant.PROCESS_X_TYPE_S);
        List<BSProcessDTO> bsProcessForWorkStation = CrafttechRemoteService.queryProcessInfo(mapForWorkStation);
        if (CollectionUtils.isEmpty(bsProcessForWorkStation)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.WORKSTATION_ERROR);
        }
        return this.getWorkStation(bsProcessForWorkStation);
    }

    public String validAndInitReturnInfo(PmRepairRcvDetailParamDTO pmRepairRcvDetailParamDTO) {
        List<PsWipInfo> psWipInfosFromScanHistory = pmRepairRcvDetailParamDTO.getPsWipInfosFromScanHistory();
        String valieTip = pmRepairRcvDetailParamDTO.getValieTip();
        List<PmRepairRcvDetailDTO> record = pmRepairRcvDetailParamDTO.getRecord();
        List<PsWipInfo> psWipInfos = pmRepairRcvDetailParamDTO.getPsWipInfos();
        List<PsScanHistory> psScanHistorys = pmRepairRcvDetailParamDTO.getPsScanHistorys();
        StringBuilder sns = pmRepairRcvDetailParamDTO.getSns();
        String currProcessCode = pmRepairRcvDetailParamDTO.getCurrProcessCode();
        Map<String, String> processMap = pmRepairRcvDetailParamDTO.getProcessMap();
        // 获取条码在制表信息
        List<String> snList = record.stream().map(PmRepairRcvDetailDTO::getSn).distinct().collect(Collectors.toList());
        List<PsWipInfo> psWipInfoList = psWipInfoService.getListByBatchSn(snList);
        Map<String, PsWipInfo> psWipInfoMap = psWipInfoList.stream()
                .collect(Collectors.toMap(PsWipInfo::getSn, v -> v, (oldValue, newValue) -> newValue));
        // 获取未返还的条码送修信息
        List<PmRepairRcvDetail> pmRepairRcvDetailList = this.getPmRepairRcvDetails(snList, valieTip);
        Map<String, List<PmRepairRcvDetail>> pmRepairMap = pmRepairRcvDetailList.stream()
                .collect(Collectors.groupingBy(PmRepairRcvDetail::getSn));
        // 返修来源是整机生产维修的数据
        List<String> detailIdList = record.stream()
                .filter(e -> Constant.DEVICE_PRODUCTION_MAINTENANCE.equals(e.getFromStation())
                && StringUtils.isNotEmpty(e.getReceptionDetailId()))
                .map(PmRepairRcvDetailDTO::getReceptionDetailId).distinct().collect(Collectors.toList());
        // 根据送修明细id 获取维修信息
        List<PmRepairRcvVo> pmRepairRcvVoList = this.getPmRepairRcvVos(detailIdList);
        Map<String, PmRepairRcvVo> pmRepairRcvVoMap = pmRepairRcvVoList.stream()
                .collect(Collectors.toMap(PmRepairRcvVo::getReceptionDetailId, v -> v, (oldValue, newValue) -> newValue));
        Map<String, String> pmRepairRcvDetailDTOMap = record.stream()
                .collect(Collectors.toMap(PmRepairRcvDetailDTO::getReceptionDetailId, PmRepairRcvDetailDTO::getSn, (k1, k2) -> k1));
        // 获取整机条码信息
        Map<String, PsWipInfo> psWipInfoMapForFormSn = this.getStringPsWipInfoMap(pmRepairRcvVoList, pmRepairRcvDetailDTOMap, psWipInfoMap);
        for (int i = 0; i < record.size(); i++) {
            PmRepairRcvDetailDTO rcvDetailDTO = record.get(i);
            String sn = rcvDetailDTO.getSn();
            // 拼接条码
            this.appendSn(sns, sn, i, record);
            PsWipInfo wipInfo = this.getPsWipInfo(psWipInfoMap, sn);
            PsWipInfo psWipInfo = new PsWipInfo();
            psWipInfo.setSn(sn);
            psWipInfo.setLastUpdatedBy(record.get(i).getLastUpdatedBy());
            PsScanHistory temp = pmRepairInfoService.creatPsScanHistory(wipInfo, record.get(i).getLastUpdatedBy());
            temp.setSn(sn);
            temp.setAttribute1(wipInfo.getAttribute1());
            List<PmRepairRcvDetail> list = pmRepairMap.getOrDefault(sn, new ArrayList<>());
            // 返还
            if (MpConstant.REPAIR_VALID_TIP_RP.equals(valieTip)) {
                currProcessCode = Constant.REPAIR_COMPLETE;
                temp.setCurrProcessCode(currProcessCode);
                this.setSomeProperties(psWipInfo, rcvDetailDTO, list);
                //维修领料校验
                this.checkMaintenanceMaterial(record);
                temp.setWorkStation(Constant.REPAIR_TWO);
                temp.setCraftSection(processMap.get(Constant.RC_CRAFT_SECTION));
            } else if ((MpConstant.REPAIR_VALID_TIP_SC).equals(valieTip)) {
                //                报废 //校验主工序
                this.validWeProcessCode(list, psWipInfo, processMap.get("weProcessCode"), processMap.get("weCrftSection"));
                currProcessCode = Constant.REPAIR_SCRAP;
                temp.setCurrProcessCode(currProcessCode);
                temp.setWorkStation(Constant.REPAIR_TWO);
                temp.setCraftSection(processMap.get("rsCraftSection"));
            }
            // 送修来源不等于整机
            addPsWipList(list, psWipInfos, psWipInfo);
            if (Constant.DEVICE_PRODUCTION_MAINTENANCE.equals(record.get(i).getFromStation())) {
                PmRepairRcvVo pmRepairRcvVo = this.getPmRepairRcvVo(record, pmRepairRcvVoMap, i);
                String formSn = getFormSn(wipInfo, pmRepairRcvVo);
                PsWipInfo wipInfoFormSn = getPsWipInfo(psWipInfoMapForFormSn, formSn);
                currProcessCode = Constant.REPAIR_COMPLETE;
                temp = pmRepairInfoService.creatPsScanHistory(wipInfoFormSn, record.get(i).getLastUpdatedBy());
                setTemp(currProcessCode, processMap, temp, formSn, wipInfoFormSn);
                pmRepairRcvVo.setPsWipInfo(psWipInfo);
                this.dealDeviceProductionMaintenance(wipInfoFormSn, psWipInfosFromScanHistory, wipInfo, psWipInfos, pmRepairRcvVo);
            }
            psScanHistorys.add(temp);
        }
        return currProcessCode;
    }

    private void addPsWipList(List<PmRepairRcvDetail> list, List<PsWipInfo> psWipInfos, PsWipInfo psWipInfo) {
        if (CollectionUtils.isNotEmpty(list) && !(Constant.FROM_STATION_MACHINE).equals(list.get(Constant.INT_0).getFromStation())) {
            psWipInfos.add(psWipInfo);
        }
    }

    /**
     * 设置部分指令属性
     * @param psWipInfo 条码信息
     * @param rcvDetailDTO 返还信息
     * @param list 送修信息
     */
    private void setSomeProperties(PsWipInfo psWipInfo, PmRepairRcvDetailDTO rcvDetailDTO, List<PmRepairRcvDetail> list) {
        psWipInfo.setCurrProcessCode(rcvDetailDTO.getProcessCode());
        psWipInfo.setCraftSection(rcvDetailDTO.getCraftSection());
        psWipInfo.setWorkStation(list.get(0).getWorkStation());
        psWipInfo.setStatus(Constant.FLAG_Y);
        // 工站为0 更新为指令
        if (Boolean.TRUE.equals(rcvDetailDTO.getUpdateProcess())) {
            psWipInfo.setWorkStation(Constant.STR_0);
            psWipInfo.setLastProcess(Constant.FLAG_N);
            psWipInfo.setWorkOrderNo(rcvDetailDTO.getWorkOrderNo());
        }
    }

    private Map<String, PsWipInfo> getStringPsWipInfoMap(List<PmRepairRcvVo> pmRepairRcvVoList, Map<String, String> pmRepairRcvDetailDTOMap, Map<String, PsWipInfo> psWipInfoMap) {
        List<String> formSnList = new ArrayList<>();
        for (PmRepairRcvVo pmRepairRcvVo : pmRepairRcvVoList) {
            String sn = pmRepairRcvDetailDTOMap.get(pmRepairRcvVo.getReceptionDetailId());
            formSnList.add(this.getFormSn(psWipInfoMap.get(sn), pmRepairRcvVo));
        }
        List<PsWipInfo> psWipInfoListForFormSn = psWipInfoService.getListByBatchSn(formSnList);
        return psWipInfoListForFormSn.stream()
                .collect(Collectors.toMap(PsWipInfo::getSn, v -> v, (oldValue, newValue) -> newValue));
    }

    private PmRepairRcvVo getPmRepairRcvVo(List<PmRepairRcvDetailDTO> record, Map<String, PmRepairRcvVo> pmRepairRcvVoMap, int i) {
        PmRepairRcvVo pmRepairRcvVo = pmRepairRcvVoMap.get(record.get(i).getReceptionDetailId());
        if (Objects.isNull(pmRepairRcvVo)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.REPAIR_RECORD_NOT_FOUND);
        }
        return pmRepairRcvVo;
    }

    private PsWipInfo getPsWipInfo(Map<String, PsWipInfo> psWipInfoMap, String sn) {
        PsWipInfo wipInfo = psWipInfoMap.get(sn);
        if(Objects.isNull(wipInfo)){
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,MessageId.SN_NULL,new Object[]{sn});
        }
        return wipInfo;
    }

    private void setTemp(String currProcessCode, Map<String, String> processMap, PsScanHistory temp, String formSn, PsWipInfo wipInfoFornSn) {
        temp.setSn(formSn);
        temp.setAttribute1(wipInfoFornSn.getAttribute1());
        temp.setAttribute2(wipInfoFornSn.getAttribute2());
        temp.setCurrProcessCode(currProcessCode);
        temp.setWorkStation(Constant.REPAIR_TWO);
        temp.setCraftSection(processMap.get(Constant.RC_CRAFT_SECTION));
    }

    private List<PmRepairRcvVo> getPmRepairRcvVos(List<String> detailIdList) {
        List<PmRepairRcvVo> pmRepairRcvVoList = new ArrayList<>();
        for (List<String> detailIdTempList : CommonUtils.splitList(detailIdList, Constant.IN_MAX_BATCH_SIZE)) {
            List<PmRepairRcvVo> pmRepairRcvVoTempList = pmRepairRcvRepository.getRepairPcvAndDeatilByDeatilIdList(detailIdTempList);
            if(CollectionUtils.isNotEmpty(pmRepairRcvVoTempList)){
                pmRepairRcvVoList.addAll(pmRepairRcvVoTempList);
            }
        }
        return pmRepairRcvVoList;
    }

    private List<PmRepairRcvDetail> getPmRepairRcvDetails(List<String> snList, String valieTip) {
        List<PmRepairRcvDetail> pmRepairRcvDetailList = new ArrayList<>();
        PmRepairRcvDetailDTO pmRepairRcvDetailDTO = new PmRepairRcvDetailDTO();
        pmRepairRcvDetailDTO.setIsAccept(MpConstant.REPAIR_IS_ACCEPT);
        pmRepairRcvDetailDTO.setBillType(MpConstant.REPAIR_BILL_TYPE_TEN);
        pmRepairRcvDetailDTO.setEnabledFlag(Constant.FLAG_Y);
        for (List<String> snTempList : CommonUtils.splitList(snList, Constant.IN_MAX_BATCH_SIZE)) {
            pmRepairRcvDetailDTO.setSnList(snTempList);
            List<PmRepairRcvDetail> tempList = pmRepairRcvDetailService.getRelOneList(pmRepairRcvDetailDTO);
            if(CollectionUtils.isNotEmpty(tempList)){
                pmRepairRcvDetailList.addAll(tempList);
            }
        }
        // 维修返还必须要有 待返还记录
        if (MpConstant.REPAIR_VALID_TIP_RP.equals(valieTip)) {
            List<String> existList = pmRepairRcvDetailList.stream().map(PmRepairRcvDetail::getSn).collect(Collectors.toList());
            snList.removeAll(existList);
            if (CollectionUtils.isNotEmpty(snList)) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SN_NOT_EXIST_ACCEPT,
                        new Object[]{snList.toString()});
            }
        }
        return pmRepairRcvDetailList;
    }

    private String getFormSn(PsWipInfo wipInfo,PmRepairRcvVo pmRepairRcvVo){
        if (checkDbSn(wipInfo)) {
            return pmRepairRcvVo.getDeviceBarCode();
        }else {
           return pmRepairRcvVo.getSn();
        }
    }
    private void dealDeviceProductionMaintenance(PsWipInfo wipInfoFornSn,List<PsWipInfo> psWipInfosFromScanHistory,PsWipInfo wipInfo,
                                                 List<PsWipInfo> psWipInfos,PmRepairRcvVo pmRepairRcvVo) {
        PsWipInfo psWipInfo = pmRepairRcvVo.getPsWipInfo();
        if (checkDbSn(wipInfo)) {
            //整机送修时单板条码更新对应整机条码工序状态,不更新单板条码
            psWipInfos.remove(psWipInfo);
        }
        if(!checkPmRepairRcvZsInfoIsEmpty(pmRepairRcvVo)){
            String formSn=getFormSn(wipInfo,pmRepairRcvVo);
            PsScanHistoryDTO dto=new PsScanHistoryDTO();
            dto.setSn(formSn);
            dto.setCurrProcessCode(pmRepairRcvVo.getProcessCodeZs());
            dto.setCraftSection(pmRepairRcvVo.getCraftSectionZs());
            dto.setWorkStation(pmRepairRcvVo.getWorkStationZs());
            List<PsScanHistory> scanhistoryList=psScanHistoryService.getScanHistoryListForZs(dto);
            if(CollectionUtils.isEmpty(scanhistoryList)){
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,MessageId.FAILED_TO_GET_SCAN_HISTORY,new Object[]{formSn});
            }
            PsWipInfo pwi= createPsWipInfo(scanhistoryList.get(NumConstant.NUM_ZERO));
            psWipInfosFromScanHistory.add(pwi);
        }else {
            if (checkDbSn(wipInfo)) {
                //整机送修时单板条码更新对应整机条码工序状态,不更新单板条码
                addWipInfo(pmRepairRcvVo, psWipInfos,wipInfoFornSn);
            }
        }
    }

    private PsWipInfo createPsWipInfo(PsScanHistory psScanHistory){
        PsWipInfo psWipInfo=new PsWipInfo();
        psWipInfo.setSn(psScanHistory.getSn());
        psWipInfo.setWorkOrderNo(psScanHistory.getWorkOrderNo());
        psWipInfo.setItemNo(psScanHistory.getItemNo());
        psWipInfo.setItemName(psScanHistory.getItemName());
        psWipInfo.setCurrProcessCode(psScanHistory.getCurrProcessCode());
        psWipInfo.setStatus(psScanHistory.getStatus());
        psWipInfo.setWorker(psScanHistory.getWorker());
        psWipInfo.setInTime(psScanHistory.getInTime());
        psWipInfo.setOutTime(psScanHistory.getOutTime());
        psWipInfo.setParentSn(psScanHistory.getParentSn());
        psWipInfo.setErrorCode(psScanHistory.getErrorCode());
        psWipInfo.setLineCode(psScanHistory.getLineCode());
        psWipInfo.setTimeSpan(psScanHistory.getTimeSpan());
        psWipInfo.setWorkshopCode(psScanHistory.getWorkshopCode());
        psWipInfo.setCraftSection(psScanHistory.getCraftSection());
        psWipInfo.setWorkStation(psScanHistory.getWorkStation());
        psWipInfo.setRemark(psScanHistory.getRemark());
        psWipInfo.setCreateBy(psScanHistory.getCreateBy());
        psWipInfo.setLastUpdatedBy(psScanHistory.getLastUpdatedBy());
        psWipInfo.setOrgId(psScanHistory.getOrgId());
        psWipInfo.setFactoryId(psScanHistory.getFactoryId());
        psWipInfo.setEntityId(psScanHistory.getEntityId());
        psWipInfo.setAttribute1(psScanHistory.getAttribute1());
        psWipInfo.setAttribute3(psScanHistory.getAttribute3());
        psWipInfo.setAttribute4(psScanHistory.getAttribute4());
        psWipInfo.setAttribute5(psScanHistory.getAttribute5());
        psWipInfo.setLastProcess(psScanHistory.getLastProcess());
        psWipInfo.setSourceSys(psScanHistory.getSourceSys());
        psWipInfo.setNextProcess(psScanHistory.getNextProcess());
        psWipInfo.setTaskNo(psScanHistory.getTaskNo());
        psWipInfo.setSourceBimu(psScanHistory.getSourceBimu());
        psWipInfo.setSourceImu(psScanHistory.getSourceImu());
        psWipInfo.setColName(psScanHistory.getColName());
        psWipInfo.setSourceSysName(psScanHistory.getSourceSysName());
        psWipInfo.setFixId(psScanHistory.getFixId());
        psWipInfo.setStationName(psScanHistory.getStationName());
        return psWipInfo;
    }

    //校验送修信息中试字段是否为空，全不为空才算不为空
    private boolean checkPmRepairRcvZsInfoIsEmpty(PmRepairRcvVo pmRepairRcv){
        if (StringUtils.isEmpty(pmRepairRcv.getCraftSectionZs()) || StringUtils.isEmpty(pmRepairRcv.getProcessCodeZs())
                || StringUtils.isEmpty(pmRepairRcv.getWorkStationZs())) {
          return true;
        }
        return false;
    }

    //校验是否单板条码
    private boolean checkDbSn(PsWipInfo wipInfo) {
        if(wipInfo==null||StringUtils.isEmpty(wipInfo.getItemNo())){
            return false;
        }
        if (wipInfo.getItemNo().length() == MpConstant.NUM_15) {
            return true;
        }
        return false;
    }

    // 拼接条码
    private void appendSn(StringBuilder sns, String sn, int i, List<PmRepairRcvDetailDTO> record) {
        if (i < record.size() - 1) {
            sns.append(Constant.SINGLE_QUOTE).append(sn).append(Constant.SINGLE_QUOTE)
                    .append(Constant.COMMA);
        } else {
            sns.append(Constant.SINGLE_QUOTE).append(sn).append(Constant.SINGLE_QUOTE);
        }
    }

    //方法外提
    private void addWipInfo(PmRepairRcvVo pmRepairRcv,List<PsWipInfo> psWipInfos,PsWipInfo psWipInfo ){
        //维修返还，整机生产维修 送修条码为单板条码 插入历史表（wip_scan_history）根据整机条码插入，而不是根据单板条码formSn
        if (checkPmRepairRcvZsInfoIsEmpty(pmRepairRcv)) {
            psWipInfo.setCraftSection(pmRepairRcv.getCraftSection());
            psWipInfo.setCurrProcessCode(pmRepairRcv.getProcessCode());
            psWipInfo.setWorkStation(pmRepairRcv.getWorkStation());
        } else {
            psWipInfo.setCraftSection(pmRepairRcv.getCraftSectionZs());
            psWipInfo.setCurrProcessCode(pmRepairRcv.getProcessCodeZs());
            psWipInfo.setWorkStation(pmRepairRcv.getWorkStationZs());
        }
        psWipInfos.add(psWipInfo);
    }


    /**
     * LPAD函数
     * @param str 待填充字符串
     * @param num 填充字符个数
     * @param pad 填充字符
     * @return 字符串
     * <AUTHOR>
     */
    public static String lpad(String str, int num, String pad) {

        String returnStr = str;
        if (str == null) {
            return " ";
        }
        for (int i = str.length(); i < num; i++) {
            returnStr = pad + returnStr;
        }
        return returnStr;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public long updateRcvDetailBatchOnly(List<PmRepairRcvDetailDTO> record) {
        // long num = pmRepairRcvRepository.updatePmRepairRcvDetailBatch(record);
        // zzc
        long num = 0;
        List<List<PmRepairRcvDetailDTO>> listOfList = CommonUtils.splitList(record, CommonConst.BATCH_SIZE);
        if (!CollectionUtils.isEmpty(listOfList)) {
            for (List<PmRepairRcvDetailDTO> paramsList : listOfList) {
                num +=  pmRepairRcvRepository.updatePmRepairRcvDetailBatch(paramsList);
            }
        }
        return num;
    }
    /**
     * 下载Excel数据
     * <AUTHOR>
     */
    @Override
    public HSSFWorkbook downExcel(PmRepairRcvDTO record) throws Exception {
        String[] title = ExcelName.PMREPAIRRCV_TITLE;
        String[] props = ExcelName.PMREPAIRRCV_PROPS;
        String sheetName = MpConstant.SHEET;
        List<PmRepairRcv> dataList = pmRepairRcvRepository.getRelOneList(record);
        dataList = change(dataList);
        return ImesExcelUtil.getHSSFWorkbook(sheetName, title, dataList, null, props);
    }

    @Override
    public List<PmRepairRcv> change (List<PmRepairRcv> list){

        if(CollectionUtils.isNotEmpty(list)) {
            Map<String, Map<String, String>> mountTypeMap = new HashMap<String, Map<String, String>>();
            try {
                mountTypeMap = ObtainRemoteServiceDataUtil.getLookupTypeByType(Constant.LOOK_UP_REPAIR_STATUS, Constant.FIELD_LOOKUP_CODE);
            } catch (Exception e) {
                logger.error(Constant.LOG_DICTIONARY_ERROR, e);
            }
            Map<String, Map<String, String>> buildingMap = new HashMap<String, Map<String, String>>();
            try {
                buildingMap = ObtainRemoteServiceDataUtil.getLookupTypeByType(Constant.LOOK_UP_BUILD, Constant.FIELD_LOOKUP_CODE);
            } catch (Exception e) {
                logger.error(Constant.LOG_DICTIONARY_ERROR, e);
            }
            for(PmRepairRcv pro : list) {
                this.setRepairStatus(pro, mountTypeMap);
                this.setBuildingName(pro, buildingMap);
            }
        }

        return list;
    }

    /**
     * 方法外提
     * <AUTHOR>
     * @param pro
     * @param mountTypeMap
     */
    private void setRepairStatus(PmRepairRcv pro, Map<String, Map<String, String>> mountTypeMap) {
        String dicGetKey = MpConstant.DIC_GET_KEY;
        if(null != mountTypeMap && mountTypeMap.size() > 0) {
            String mountTypeName = Constant.STR_EMPTY;
            Map<String, String> dataMap = mountTypeMap.get(pro.getStatus());
            if (dataMap != null) {
                Object getValue = dataMap.get(dicGetKey);
                if (getValue != null) {
                    mountTypeName = getValue.toString();
                }
            }
            pro.setRepairStatusName(mountTypeName);
        }
    }

    /**
     * 方法外提
     * <AUTHOR>
     * @param pro
     * @param buildingMap
     */
    private void setBuildingName(PmRepairRcv pro, Map<String, Map<String, String>> buildingMap) {
        String dicGetKey = MpConstant.DIC_GET_KEY;
        if(null != buildingMap && buildingMap.size() > 0) {
            String mountTypeName = Constant.STR_EMPTY;
            Map<String, String> dataMap = buildingMap.get(pro.getBuilding());
            if (dataMap != null) {
                Object getValue = dataMap.get(dicGetKey);
                if (getValue != null) {
                    mountTypeName = getValue.toString();
                }
            }
            pro.setBuildingName(mountTypeName);
        }
    }

    @Override
    public long getRepairSum(PmRepairRcvDTO pmRepairRcvDTO) {
        return pmRepairRcvRepository.getRepairSum(pmRepairRcvDTO);
    }

    /**
     * <AUTHOR>
     * 整机送修插入交易表
     * @param dto
     * @return
     */
    private void machineRepairRcv(PmRepairRcvDetailDTO dto,List<SemiManufactureDealInfo> machineList){
        if ((Constant.FROM_STATION_MACHINE).equals(dto.getFromStation())) {
            //整机维修返还 插入表 插入表 605500040
            String deliverNo = MpConstant.DELIVER_NO_FH + dto.getDeliveryNo().substring(2);
            SemiManufactureDealInfo semiManufactureDealInfo = new SemiManufactureDealInfo();
            semiManufactureDealInfo.setLpn(deliverNo);
            semiManufactureDealInfo.setProdplanId(dto.getRcvProdplanId());
            semiManufactureDealInfo.setSn(dto.getSn());
            semiManufactureDealInfo.setDealType(Constant.REPAIR_FOUR);
            semiManufactureDealInfo.setAttribute1(Constant.WAREHOUSE_OUT);
            semiManufactureDealInfo.setAttribute2(dto.getItemCode());
            semiManufactureDealInfo.setAttribute3(Constant.REPAIR_MACHINE_RCV_ORDER);
            semiManufactureDealInfo.setFactoryId(dto.getFactoryId());
            machineList.add(semiManufactureDealInfo);
        }
    }
    /**
     * <AUTHOR>
     * 半成品送修插入交易表
     * @param dto
     * @return
     */
    private void seimsRepairRcv(PmRepairRcvDetailDTO dto,List<SemiManufactureDealInfo> seimsList){
        if ((Constant.FROM_STATION_SEMIS).equals(dto.getFromStation())) {
            //半成品库维修返还 插入表 插入表 605500040
            String deliverNo = MpConstant.DELIVER_NO_FH + dto.getDeliveryNo().substring(2);
            SemiManufactureDealInfo semiManufactureDealInfo = new SemiManufactureDealInfo();
            semiManufactureDealInfo.setLpn(deliverNo);
            semiManufactureDealInfo.setProdplanId(dto.getRcvProdplanId());
            semiManufactureDealInfo.setSn(dto.getSn());
            semiManufactureDealInfo.setDealType(Constant.REPAIR_FOUR);
            semiManufactureDealInfo.setAttribute1(Constant.WAREHOUSE_ENTRY);
            semiManufactureDealInfo.setAttribute2(dto.getItemCode());
            semiManufactureDealInfo.setAttribute3(Constant.REPAIR_SEMIS_RCV_ORDER);
            semiManufactureDealInfo.setFactoryId(dto.getFactoryId());
            seimsList.add(semiManufactureDealInfo);
        }
    }
    /**
     * <AUTHOR>
     * 校验方法外提
     * @param bsProcessList
     * @param processMap
     */
    public void verification(List<BSProcessDTO> bsProcessList,Map<String, String> processMap){
        if (CollectionUtils.isNotEmpty(bsProcessList)) {
            for (BSProcessDTO dto : bsProcessList) {
                if (MpConstant.PROCESS_NAME_DIP.equals(dto.getProcessName())) {
                    processMap.put(Constant.DIP_STR, dto.getProcessCode());
                }
                if (MpConstant.PROCESS_NAME_ASSY_OUTPUT.equals(dto.getProcessName())) {
                    processMap.put(Constant.ASSY_OUT_PUT_STR, dto.getProcessCode());
                }
                if (Constant.REPAIR_ING.equals(dto.getProcessCode())) {
                    processMap.put(Constant.CRAFT_SECTION_STR, dto.getCraftSection());
                }
            }
        }
    }
    /**
     * <AUTHOR>
     * 校验再次外提
     * @param bsProcessForWorkStation
     * @param processMap
     */
    public void verificationDip(List<BSProcessDTO> bsProcessForWorkStation,Map<String, String> processMap){
        if(CollectionUtils.isNotEmpty(bsProcessForWorkStation)){
            for(BSProcessDTO dto : bsProcessForWorkStation){
                if(MpConstant.PROCESS_NAME_DIP_DOWNLINE.equals(dto.getProcessName())){
                    processMap.put(Constant.DIP_OFFLINE_SCAN, dto.getProcessCode());
                }
            }
        }
    }
    /**
     * <AUTHOR>
     * 批量修改
     * @param psWipInfos
     */
    public void updateBatch(List<PsWipInfo> psWipInfos){
        if (!CollectionUtils.isEmpty(psWipInfos)) {
            // 批量修改数据
            List<List<PsWipInfo>> listOfList = CommonUtils.splitList(psWipInfos, CommonConst.BATCH_SIZE);
            if (!CollectionUtils.isEmpty(listOfList)) {
                for (List<PsWipInfo> paramsList : listOfList) {
                    psWipInfoService.updatePsWipInfoByScanBatch(paramsList);
                }
            }
        }
    }

    /**
     * 逻辑代码外提
     * @param pmRepairRcvDetailDTO
     */
    public void logic(PmRepairRcv record,PmRepairRcvDetailDTO pmRepairRcvDetailDTO,List<PsWorkOrderBasic> listWorkOrder,
                      Map<String, String> processMap,String receptionId) {
        Map<String,Object> scanHistory = new HashMap<>();
        // 条码查重校验
        PsWipInfo wipInfo = psWipInfoService.getWipInfoBySn(pmRepairRcvDetailDTO.getSn());
        if(!ObjectUtils.isEmpty(wipInfo) && !wipInfo.getWipId().isEmpty()){
            pmRepairRcvDetailDTO.setItemCode(wipInfo.getItemNo());
            pmRepairRcvDetailDTO.setItemName(wipInfo.getItemName());
            pmRepairRcvDetailDTO.setWorkStation(wipInfo.getWorkStation());
            pmRepairRcvDetailDTO.setProcessCode(wipInfo.getCurrProcessCode());
            pmRepairRcvDetailDTO.setCraftSection(wipInfo.getCraftSection());
            pmRepairRcvDetailDTO.setRcvProdplanId(wipInfo.getAttribute1());
            pmRepairRcvDetailDTO.setSendFlag(MpConstant.REPAIR_SEND_FLAG_ZERO);
            pmRepairRcvDetailDTO.setRcvFlag(MpConstant.REPAIR_RCV_FLAG_ZERO);
            PsWorkOrderBasic workOrderIn = new PsWorkOrderBasic();
            PsWorkOrderBasic workOrderOut = new PsWorkOrderBasic();
            if(processMap.get(Constant.DIP_STR).equals(wipInfo.getCurrProcessCode()) && processMap.get(Constant.DIP_OFFLINE_SCAN).equals(wipInfo.getWorkStation())){
                workOrderOut.setWorkOrderNo(wipInfo.getWorkOrderNo());
                workOrderOut.setAttribute1(MpConstant.WORKORDER_ATTR1_OUTPUT);
                listWorkOrder.add(workOrderOut);
            }
            if(processMap.get(Constant.ASSY_OUT_PUT_STR).equals(wipInfo.getCurrProcessCode())){
                workOrderIn.setWorkOrderNo(wipInfo.getWorkOrderNo());
                workOrderIn.setAttribute1(MpConstant.WORKORDER_ATTR1_INPUT);
                listWorkOrder.add(workOrderIn);

                scanHistory.put("sn", pmRepairRcvDetailDTO.getSn());
                scanHistory.put("currProcessCode", processMap.get(Constant.DIP_STR));
                scanHistory.put("orderField", MpConstant.CREATE_DATE);
                // 取最早一条
                List<PsScanHistory> historyList = psScanHistoryService.getList(scanHistory);
                if(CollectionUtils.isNotEmpty(historyList)){
                    workOrderOut.setWorkOrderNo(historyList.get(Constant.INT_0).getWorkOrderNo());
                    workOrderOut.setAttribute1(MpConstant.WORKORDER_ATTR1_OUTPUT);
                    listWorkOrder.add(workOrderOut);
                }
            }
        }
        pmRepairRcvDetailDTO.setReceptionId(receptionId);
        pmRepairRcvDetailDTO.setIsAccept(MpConstant.REPAIR_IS_ACCEPT);
        pmRepairRcvDetailDTO.setReceptionDetailId(UUID.randomUUID().toString());
        pmRepairRcvDetailDTO.setFactoryId(record.getFactoryId());
        pmRepairRcvDetailDTO.setCreateBy(record.getCreateBy());
        pmRepairRcvDetailDTO.setLastUpdatedBy(record.getLastUpdatedBy());
        pmRepairRcvDetailDTO.setEntityId(record.getEntityId());
        pmRepairRcvDetailDTO.setStatus(Constant.REPAIR_STATUS_WAIT);
        //半成品插入交易表并更新QTY数量(点对点之后调用)
        this.insertSemis(record,pmRepairRcvDetailDTO);
        //整机插入交易表
        if(!ObjectUtils.isEmpty(wipInfo)) {
            this.insertMachine(wipInfo,record,pmRepairRcvDetailDTO);
        }
    }

    /**
     * <AUTHOR>
     * 半成品插入交易表并更新QTY数量(点对点)
     * @param record
     * @param pmRepairRcvDetailDTO
     * @throws Exception
     */
    public void insertSemis(PmRepairRcv record,PmRepairRcvDetailDTO pmRepairRcvDetailDTO) {
        StringBuilder sns = new StringBuilder();
        if ((Constant.FROM_STATION_SEMIS).equals(record.getFromStation())) {
            sns.append(pmRepairRcvDetailDTO.getSn()).append(",");
        }
        // 1、新增送修记录，判断送修来源，如果为半成品库，校验箱内容是否存在（container_content_info），
        // 如果存在，删除箱内容对应单板条码，更新箱库存数量（stock_onhand_info），
        // 如果箱库存不存在，校验箱内容是否存在，如果存在，删除箱内容；--装箱查询，单板条码条码查询，半成品库装箱查询
        // 2、1满足的情况下，写交易信息表，类型为out；--semi_manufacture_deal_info
        try {
            if (sns.length() > 0) {
                sns.deleteCharAt(sns.length() - 1);
                ObtainRemoteServiceDataUtil.getLpn(String.valueOf(sns),record);
            }
        } catch (Exception e) {
            logger.info(Constant.LOG_ERROR + e.getMessage());
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,MessageId.FAILED_TO_UPDATE_QTY);
        }
    }
    /**
     * <AUTHOR>
     * 整机插入交易表(点对点)
     * @param wipInfo
     * @param record
     * @param pmRepairRcvDetailDTO
     * @throws Exception
     */
    public void insertMachine(PsWipInfo wipInfo,PmRepairRcv record,PmRepairRcvDetailDTO pmRepairRcvDetailDTO) {
        List<SemiManufactureDealInfo> machineList = new ArrayList<>();
        if ((Constant.FROM_STATION_MACHINE).equals(record.getFromStation())) {
            //整机插入 set数据
            SemiManufactureDealInfo semiManufactureDealInfo = new SemiManufactureDealInfo();
            semiManufactureDealInfo.setLpn(record.getDeliveryNo());
            semiManufactureDealInfo.setProdplanId(wipInfo.getAttribute1());
            semiManufactureDealInfo.setSn(pmRepairRcvDetailDTO.getSn());
            semiManufactureDealInfo.setDealType(Constant.REPAIR_THREE);
            semiManufactureDealInfo.setAttribute1(Constant.REPAIR);
            semiManufactureDealInfo.setAttribute2(wipInfo.getItemNo());
            semiManufactureDealInfo.setAttribute3(Constant.REPAIR_MACHINE_ORDER);
            semiManufactureDealInfo.setFactoryId(pmRepairRcvDetailDTO.getFactoryId());
            machineList.add(semiManufactureDealInfo);
        }
        //点对点  整机插入交易表
        try{
            if(CollectionUtils.isNotEmpty(machineList)){
                ProductionDeliveryRemoteService.semiManufactureDealInfoInsertBatch(machineList);
            }
        }catch(Exception e){
            logger.info(Constant.LOG_ERROR + e.getMessage());
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,MessageId.BATCH_INSERT_TRANSACTION_TABLE_FAILED);
        }

    }

    /**
     * 通过条码查询送修信息
     * <AUTHOR>
     * @param record
     * @return
     */
    @Override
    public PmRepairRcvDTO selectPmRepairRcvDTOBySnNew(PmRepairRcv record, String empNo) throws Exception {
        PmRepairRcvDTO pmRepairRcvDTO = new PmRepairRcvDTO();
        pmRepairRcvDTO.setSn(record.getSn());
        if (validCommon(record, pmRepairRcvDTO)) {
            return pmRepairRcvDTO;
        }
        if (MpConstant.PAGE_TYPE_TERMINAL.equals(String.valueOf(record.getPageType()))) {
            if (validTerminal(record, pmRepairRcvDTO)) {
                return pmRepairRcvDTO;
            }
        } else {
            if (validSys(record, pmRepairRcvDTO, empNo)) {
                return pmRepairRcvDTO;
            }
        }
        // 计划维修次数
        PmRepairRcvDetailDTO queryDto = new PmRepairRcvDetailDTO();
        queryDto.setSn(record.getSn());
        pmRepairRcvDTO.setRepairCount(new BigDecimal(pmRepairRcvDetailRepository.getRepairCount(queryDto)));
        setMBomOfPmRepairRcvDTO(pmRepairRcvDTO);
        return pmRepairRcvDTO;
    }

    private void setMBomOfPmRepairRcvDTO(PmRepairRcvDTO result) {
        if(result == null){
            return;
        }
        List<String> prodPlanIdList = new ArrayList<>();
        prodPlanIdList.add(result.getRcvProdplanId());
        List<BProdBomHeaderDTO> bProdBomHeaderDTOList = centerfactoryRemoteService.queryProductCodeByProdPlanIdList(prodPlanIdList);
        Map<String,String> prodToMBomMap = bProdBomHeaderDTOList.stream().collect(Collectors.toMap(k -> k.getProdplanId(), v -> v.getProductCode(), (k1, k2) -> k1));
        result.setMbom(result.getItemCode());
        String mBom = prodToMBomMap.get(result.getRcvProdplanId());
        if (StringUtils.isNotBlank(mBom)) {
            result.setMbom(mBom);
        }
    }

    /**
     * 系统、终端共有校验
     *
     * @param record
     * @param pmRepairRcvDTO
     * @return
     */
    private boolean validCommon(PmRepairRcv record, PmRepairRcvDTO pmRepairRcvDTO) {
        //必传参数校验
        if (validInputParam(record, pmRepairRcvDTO)) {
            return true;
        }
        //返还校验
        pmRepairRcvDTO = returnValid(pmRepairRcvDTO);
        if (MpConstant.SN_IS_NOT_RETURNED.equals(pmRepairRcvDTO.getValiedMsg())) {
            return true;
        }
        return false;
    }

    /**
     * 系统产品校验
     *
     * @param record
     * @param pmRepairRcvDTO
     * @return
     */
    private boolean validSys(PmRepairRcv record, PmRepairRcvDTO pmRepairRcvDTO, String empNo) throws Exception {
        // 报废校验
        if (scapValid(record, pmRepairRcvDTO)) {
            return true;
        }
        List<PsWipInfo> wipInfoList = psWipInfoService.getWipInfoJoinTestBySn(record.getSn());
        if (CollectionUtils.isEmpty(wipInfoList)) {
            pmRepairRcvDTO.setValiedMsg(CommonUtils.getLmbMessage(MessageId.WIP_INFO_DATA_NOT_FOUND));
            return true;
        }
        //送修次数管控
        if(sentControl(record, pmRepairRcvDTO, wipInfoList)) {
            return true;
        }
        // 输入单板条码时，是否存在整机送修记录；  // 输入整机条码时，是否存在单板送修记录
        if (judgeReparieBordOrDevice(record, pmRepairRcvDTO, wipInfoList)) {
            return true;
        }
        // 获取对应的产品大类，开关打开时，调用中试接口获取故障描述
        if (this.checkSwitchAndGetDes(record, pmRepairRcvDTO, wipInfoList)) {
            return true;
        }
        pmRepairRcvDTO.setValiedMsg(MpConstant.RESULT_TYPE_OK);
        //获取传进来的条码对应料单代码的基础物料信息
        Map<String, Object> mapItemNo = new HashMap<String, Object>();
        mapItemNo.put(Constant.ITEM_NO_STR, wipInfoList.get(Constant.INT_0).getItemNo());
        List<BsItemInfo> itemInfoList = BasicsettingRemoteService.getStyleInfo(mapItemNo);
        if (CollectionUtils.isEmpty(itemInfoList)) {
            pmRepairRcvDTO.setValiedMsg(CommonUtils.getLmbMessage(MessageId.NO_INFO_OF_THIS_ITEMCODE));
            return true;
        }
        // 获取工艺路径
        List<BSProcess> craftList = CrafttechRemoteService.getProcessAll();
        if (CollectionUtils.isEmpty(craftList)) {
            pmRepairRcvDTO.setValiedMsg(MpConstant.NO_FIND_CRAFT_MESSAGE);
            return true;
        }
        Map<String, String> craftMap = new HashMap<>();
        for (BSProcess process : craftList) {
            craftMap.put(process.getProcessCode(), process.getProcessName());
        }
        // 设置线体名称、子工序名称，如果不是整机生产维修，初始化参数
        if (judgeWipInfo(record, pmRepairRcvDTO, wipInfoList, itemInfoList, craftMap)) {
            return true;
        }
        //校验来源是否一致
        String fromStation = record.getFromStation();
        this.valied(pmRepairRcvDTO, wipInfoList, fromStation);
        // 中式校验
        if (Constant.FROM_STATION_MACHINE.equals(record.getFromStation())) {
            checkDQAS(pmRepairRcvDTO, empNo);
        }
        // 整机生产维修
        pmRepairRcvDTO.setParamFromStation(fromStation);
        totalProductionRepaire(record, pmRepairRcvDTO, wipInfoList, itemInfoList, craftMap);
        return false;
    }

    /**
     * 送修次数管控
     * @param record
     * @param pmRepairRcvDTO
     * @param wipInfoList
     * @return
     */
    private boolean sentControl(PmRepairRcv record, PmRepairRcvDTO pmRepairRcvDTO, List<PsWipInfo> wipInfoList) throws Exception {
        //1.送修来源是单板生产、整机单板送修、整机生产维修才进行送修次数管控
        pmRepairRcvDTO.setFactoryId(record.getFactoryId());
        String fromStation = record.getFromStation();
        if (!Constant.REPAIR_FROM_STATION.equals(fromStation) &&
                !Constant.REPAIR_FROM_ZJ_DB.equals(fromStation) &&
                !Constant.DEVICE_PRODUCTION_MAINTENANCE.equals(fromStation)) {
            return false;
        }
        //2.根据条码所属12位料单获取客户名称,若客户名称获取不到或客户未维护送修次数限制则不进行校验。
        String itemNo = wipInfoList.get(Constant.INT_0).getItemNo();
        String zteCode = itemNo.substring(Constant.INT_0, Constant.INT_12);
        CustomerItemsDTO customerItemsDTO = new CustomerItemsDTO();
        customerItemsDTO.setItemNoList(Collections.singletonList(zteCode));
        List<CustomerItemsDTO> customerItemsDTOS = CenterfactoryRemoteService.getCustomerItemsInfo(customerItemsDTO);
        if(CollectionUtils.isEmpty(customerItemsDTOS)){
            return false;
        }
        String customerName = customerItemsDTOS.get(Constant.INT_0).getCustomerName();

        /* Started by AICoder, pid:s4f8ctf85c34c28145b3081e70ef2452b6208a4c */
        // 查询客户名称数据字典，7300
        List<SysLookupTypesDTO> customerLookupTypes = BasicsettingRemoteService.getSysLookUpValue(Constant.LookUpKey.LOOK_7300);

        // 过滤出符合条件的客户列表
        List<SysLookupTypesDTO> customerList = customerLookupTypes.stream()
                .filter(l -> StringUtils.isNotBlank(l.getAttribute2()) &&
                             org.apache.commons.lang.ObjectUtils.equals(customerName, l.getLookupMeaning()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(customerList)) {
            return false;
        }

        // 提取客户编号列表
        Set<String> customerNumberSet = customerList.stream()
                .map(SysLookupTypesDTO::getAttribute2)
                .collect(Collectors.toSet());

        // 查询另一个数据字典，2754
        List<SysLookupTypesDTO> lookupTypesDTOS = BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_2754);

        // 过滤出符合条件的记录
        List<SysLookupTypesDTO> filteredList = lookupTypesDTOS.stream()
                .filter(l -> StringUtils.isNotBlank(l.getLookupMeaning()) &&
                             customerNumberSet.contains(l.getAttribute1()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(filteredList)) {
            return false;
        }

        int customerSendCount = Integer.parseInt(filteredList.get(0).getLookupMeaning());
        /* Ended by AICoder, pid:s4f8ctf85c34c28145b3081e70ef2452b6208a4c */

        // 3.查询送修单审批次数（通过单板条码+审批单类型查询审批通过的单据数量）
        RepairApprovalDTO repairApprovalDTO = new RepairApprovalDTO();
        repairApprovalDTO.setBarcode(wipInfoList.get(Constant.INT_0).getSn());
        repairApprovalDTO.setApprovalType(Constant.STR_0);
        repairApprovalDTO.setStatus(String.valueOf(RepairApprovalStatusEnum.APPROVED));
        List<RepairApproval> approvalList = repairApprovalService.getByCondition(repairApprovalDTO);
        int approvalCount = approvalList.size();
        if(Constant.INT_15 == itemNo.length()){
            //4.单板条码校验，根据条码查询iMes送修次数+通过条码查询MDS（取最大的repair_count作为送修次数）是否大于数据字典配置的送修次数+送修单审批次数
            return boardSentValid(pmRepairRcvDTO, customerSendCount, approvalCount,wipInfoList);
        }else{
            //5.整机条码校验，根据条码子工序（数据字典配置）配置的iMes送修次数是否大于数据字典配置的送修次数+送修单审批次数
            return deviceSentValid(pmRepairRcvDTO, customerSendCount, approvalCount, wipInfoList);
        }
    }

    private static boolean deviceSentValid(PmRepairRcvDTO pmRepairRcvDTO, int customerSendCount, int approvalCount, List<PsWipInfo> wipInfoList) throws Exception {
        String sn = wipInfoList.get(Constant.INT_0).getSn();
        String currProcessCode = wipInfoList.get(Constant.INT_0).getCurrProcessCode();
        HashMap<String, Object> map = new HashMap<>(1);
        map.put(Constant.LOOK_UP_TYPE, Constant.LOOKUP_TYPE_2755);
        List<SysLookupTypesDTO> lookupTypesDTOList = BasicsettingRemoteService.getListByLookupType(map);
        Map<String, String> sendCountMap = lookupTypesDTOList.stream()
                .collect(Collectors.toMap(
                        SysLookupTypesDTO::getAttribute1,
                        SysLookupTypesDTO::getLookupMeaning,
                        (oldValue, newValue) -> oldValue
                ));
        if(!sendCountMap.containsKey(currProcessCode)){
            return false;
        }
        int sentCount = Integer.parseInt(sendCountMap.get(currProcessCode));
        if(sentCount + 1 > customerSendCount + approvalCount){
            pmRepairRcvDTO.setValiedMsg(CommonUtils.getLmbMessage(MessageId.SN_SENT_REPAIR_NOT_ALLOWED, sn));
            return true;
        }
        return false;
    }

    private boolean boardSentValid(PmRepairRcvDTO pmRepairRcvDTO, int customerSendCount, int approvalCount, List<PsWipInfo> wipInfoList) throws Exception {
        String sn = wipInfoList.get(Constant.INT_0).getSn();
        PmRepairInfoDTO pmRepairInfoDTO = new PmRepairInfoDTO();
        pmRepairInfoDTO.setInSns(sn);
        pmRepairInfoDTO.setRepairStatus(Constant.REPAIR_STATUS_RESTORE);
        pmRepairInfoDTO.setFactoryId(pmRepairRcvDTO.getFactoryId());
        List<PmRepairInfo> pmRepairInfoList = pmRepairInfoService.getRelOneDetailList(pmRepairInfoDTO);
        List<MdsRepairRecordDTO> records = Optional.ofNullable(mdsRemoteService.getRepairCountFromMDS(sn)).orElse(Collections.emptyList());
        int maxMdsSentCount = records.stream()
                .mapToInt(MdsRepairRecordDTO::getRepairCount)
                .max()
                .orElse(0);
        if((pmRepairInfoList.size() + maxMdsSentCount + 1) > customerSendCount + approvalCount){
            pmRepairRcvDTO.setValiedMsg(CommonUtils.getLmbMessage(MessageId.SN_SENT_REPAIR_NOT_ALLOWED, sn));
            return true;
        }
        return false;
    }

private boolean checkSwitchAndGetDes(PmRepairRcv record, PmRepairRcvDTO pmRepairRcvDTO, List<PsWipInfo> wipInfoList) throws Exception {
        return Constant.FLAG_Y.equals(record.getAutoGetErrDec()) && this.getErrorDesFromMds(record, pmRepairRcvDTO, wipInfoList);
    }

    private boolean getErrorDesFromMds(PmRepairRcv record, PmRepairRcvDTO pmRepairRcvDTO, List<PsWipInfo> wipInfoList) throws Exception {
        String itemNo = wipInfoList.get(Constant.INT_0).getItemNo();
        String errorMsg = "";
        //没有维护送修生产阶段报错
        if (StringUtils.isBlank(record.getProStageForRepair())) {
            pmRepairRcvDTO.setValiedMsg(CommonUtils.getLmbMessage(MessageId.CORESPONDING_STAGE_IS_NULL));
            return true;
        }
        if (MpConstant.PRO_STAGE_SN.equals(record.getProStageForRepair())) {
            // 单板时送修--物料代码15位时根据批次号去获取产品大类
            errorMsg = this.getErrorDesForStageSn(itemNo, pmRepairRcvDTO, wipInfoList,record.getSn());
        } else if (MpConstant.PRO_STAGE_ZJ.equals(record.getProStageForRepair())) {
            // 标模时送修--物料代码15位时根据装配关系获取整机条码，再根据任务号去获取产品大类
            if (Constant.INT_15 == itemNo.length()) {
                Map<String, Object> extendMap = new HashMap<>();
                extendMap.put(Constant.SN_CODE, record.getSn());
                extendMap.put(Constant.FACTORY_ID, record.getFactoryId());
                extendMap.put(Constant.FORM_TYPE, Constant.STR_2);
                List<WipExtendIdentification> extendList = wipExtendIdentificationRepository.getList(extendMap);
                errorMsg = this.getErrorDesForStageZj(pmRepairRcvDTO, extendList,record.getSn());
            } else {
                // 标模时送修--物料代码非15位时根据任务号去获取产品大类
                PsTask psTask = PlanscheduleRemoteService.getPsTaskByTaskNo(wipInfoList.get(Constant.INT_0).getAttribute2());
                if (null == psTask) {
                    pmRepairRcvDTO.setValiedMsg(CommonUtils.getLmbMessage(MessageId.FAILED_TO_GET_TASK_INFO_BY_TASKNO,
                            new String[]{wipInfoList.get(Constant.INT_0).getAttribute2()}));
                    return true;
                }
                errorMsg = mdsRemoteService.getMdsErrorMsg(psTask.getExternalType(),record.getSn());
            }
        }
        if (StringUtils.isNotBlank(pmRepairRcvDTO.getValiedMsg())) {
            return true;
        }
        if (StringUtils.equals(Constant.COLON,errorMsg)) {
            errorMsg = "";
        }
        pmRepairRcvDTO.setErrorDescription(errorMsg);
        return false;
    }

    private String getErrorDesForStageSn(String itemNo, PmRepairRcvDTO pmRepairRcvDTO, List<PsWipInfo> wipInfoList, String sn) throws Exception {
        String errorMsg = "";
        if (Constant.INT_15 != itemNo.length()) {
            pmRepairRcvDTO.setValiedMsg(CommonUtils.getLmbMessage(MessageId.THE_ENTERED_BARCODE_IS_NOT_A_BOARD_BARCODE));
            return errorMsg;
        }
        List<PsTask> psTaskList = PlanscheduleRemoteService.getPsTaskByProdPlanId(wipInfoList.get(Constant.INT_0).getAttribute1());
        if (CollectionUtils.isEmpty(psTaskList)) {
            pmRepairRcvDTO.setValiedMsg(CommonUtils.getLmbMessage(MessageId.FAILED_TO_GET_TASK_INFO_BY_PRODPLANID,
                    new String[]{wipInfoList.get(Constant.INT_0).getAttribute1()}));
            return errorMsg;
        }
        errorMsg = mdsRemoteService.getMdsErrorMsg(psTaskList.get(0).getExternalType(), sn);
        return errorMsg;
    }

    private String getErrorDesForStageZj(PmRepairRcvDTO pmRepairRcvDTO, List<WipExtendIdentification> extendList, String sn) throws Exception {
        String errorMsg = "";
        if (CollectionUtils.isEmpty(extendList)) {
            pmRepairRcvDTO.setValiedMsg(CommonUtils.getLmbMessage(MessageId.FAILED_TO_GET_ASSEMBLYRELATION_OF_SN));
            return errorMsg;
        }
        List<PsWipInfo> formSnWipInfo = psWipInfoService.getWipInfoJoinTestBySn(extendList.get(Constant.INT_0).getFormSn());
        if (CollectionUtils.isEmpty(formSnWipInfo)) {
            pmRepairRcvDTO.setValiedMsg(CommonUtils.getLmbMessage(MessageId.SN_NOT_EXIST_WIP));
            return errorMsg;
        }
        PsTask psTask = PlanscheduleRemoteService.getPsTaskByTaskNo(formSnWipInfo.get(Constant.INT_0).getAttribute2());
        if (null == psTask) {
            pmRepairRcvDTO.setValiedMsg(CommonUtils.getLmbMessage(MessageId.FAILED_TO_GET_TASK_INFO_BY_TASKNO,
                    new String[]{formSnWipInfo.get(Constant.INT_0).getAttribute2()}));
            return errorMsg;
        }
        errorMsg = mdsRemoteService.getMdsErrorMsg(psTask.getExternalType(), sn);
        return errorMsg;
    }


    public PmRepairRcvDTO returnValid (PmRepairRcvDTO pmRepairRcvDTO){
        // 维修返还校验
        pmRepairRcvDTO.setInAcceptStatus(Constant.INT_0 + Constant.COMMA + Constant.INT_2);
        List<PmRepairRcv> pmRepairRcvList = pmRepairRcvRepository.getPmRepairRcvDetail(pmRepairRcvDTO);
        if(CollectionUtils.isNotEmpty(pmRepairRcvList)) {
            pmRepairRcvDTO.setValiedMsg(MpConstant.SN_IS_NOT_RETURNED);
            return pmRepairRcvDTO;
        }
        return pmRepairRcvDTO;
    }

    private boolean scapValid(PmRepairRcv record, PmRepairRcvDTO pmRepairRcvDTO) {
        PmRepairRcvDTO prrs = new PmRepairRcvDTO();
        //条码报废校验
        prrs.setSn(record.getSn());
        prrs.setBillType(MpConstant.REPAIR_BILL_TYPE_TEN);
        prrs.setStatus(Constant.REPAIR_STATUS_SCRAP);
        Long o = pmRepairRcvRepository.getRelOneCount(prrs);
        if (o > Constant.INT_0) {
            pmRepairRcvDTO.setValiedMsg(CommonUtils.getLmbMessage(MessageId.SN_IS_SCRAPPED));
            return true;
        }
        return false;
    }

    private boolean judgeReparieBordOrDevice(PmRepairRcv record, PmRepairRcvDTO pmRepairRcvDTO, List<PsWipInfo> wipInfoList) {
        List<WipExtendIdentification>  wipExtendList = new ArrayList<>();
        if((Constant.DEVICE_PRODUCTION_MAINTENANCE).equals(record.getFromStation())){
            // 单板校验
            if (boardValid(record, pmRepairRcvDTO, wipInfoList)) {
                return true;
            }
            //整机校验
            if (deviceValid(record, pmRepairRcvDTO, wipInfoList)){
                return true;
            }
        }
        return false;
    }

    private boolean deviceValid(PmRepairRcv record, PmRepairRcvDTO pmRepairRcvDTO, List<PsWipInfo> wipInfoList) {
        List<WipExtendIdentification> wipExtendList;
        if (Constant.INT_15!=wipInfoList.get(Constant.INT_0).getItemNo().length()){
            // 根据整机条码查单板条码
            Map<String, Object> extendMap = new HashMap<>();
            extendMap.put(Constant.FORM_SN,record.getSn());
            extendMap.put(Constant.FACTORY_ID,record.getFactoryId());
            wipExtendList = wipExtendIdentificationRepository.getList(extendMap);
            //如果没有找到绑定记录，或者绑定类型不为2，就不进行对应的单板校验，如果有，并且绑定类型为2，就要进行校验
            if (CollectionUtils.isEmpty(wipExtendList)||!Constant.COMPARE_FINISH.equals(wipExtendList.get(Constant.INT_0).getFormType()) ){
                return false;
            }
            // 报废校验
            PmRepairRcv rec = new PmRepairRcv();
            rec.setSn(wipExtendList.get(Constant.INT_0).getSn());
            if (scapValid(rec, pmRepairRcvDTO)) {
                return true;
            }
            //返还校验
            String sn = pmRepairRcvDTO.getSn();
            pmRepairRcvDTO.setSn(wipExtendList.get(Constant.INT_0).getSn());
            pmRepairRcvDTO = returnValid (pmRepairRcvDTO);
            pmRepairRcvDTO.setSn(sn);
            if (MpConstant.SN_IS_NOT_RETURNED.equals(pmRepairRcvDTO.getValiedMsg())){
                pmRepairRcvDTO.setValiedMsg(CommonUtils.getLmbMessage(MessageId.STATUS_NO_RESTORE_DEVICE));
                return true;
            }
            pmRepairRcvDTO.setDeviceBarCode(wipExtendList.get(Constant.INT_0).getSn());
        }
        return false;
    }

    private boolean boardValid(PmRepairRcv record, PmRepairRcvDTO pmRepairRcvDTO, List<PsWipInfo> wipInfoList) {
        List<WipExtendIdentification> wipExtendList;
        if (Constant.INT_15==wipInfoList.get(Constant.INT_0).getItemNo().length()){
            Map<String, Object> extendMap = new HashMap<>();
            extendMap.put(Constant.SN_CODE,record.getSn());
            extendMap.put(Constant.FACTORY_ID,record.getFactoryId());
            wipExtendList = wipExtendIdentificationRepository.getList(extendMap);
            if (CollectionUtils.isEmpty(wipExtendList)||!Constant.COMPARE_FINISH.equals(wipExtendList.get(Constant.INT_0).getFormType()) ){
                pmRepairRcvDTO.setValiedMsg(CommonUtils.getLmbMessage(MessageId.NO_BINGDING_RECORD));
                return true;
            }
            // 返还、报废校验
            if (returnAndScrapValid(pmRepairRcvDTO, wipExtendList)) {
                return true;
            }
            pmRepairRcvDTO.setDeviceBarCode(wipExtendList.get(Constant.INT_0).getFormSn());
        }
        return false;
    }

    private boolean returnAndScrapValid(PmRepairRcvDTO pmRepairRcvDTO, List<WipExtendIdentification> wipExtendList) {
        // 报废校验
        PmRepairRcv rec=  new PmRepairRcv();
        rec.setSn(wipExtendList.get(Constant.INT_0).getFormSn());
        if (scapValid(rec, pmRepairRcvDTO)) {
            return true;
        }
        //返还校验
        String sn = pmRepairRcvDTO.getSn();
        pmRepairRcvDTO.setSn(wipExtendList.get(Constant.INT_0).getFormSn());
        pmRepairRcvDTO = returnValid (pmRepairRcvDTO);
        pmRepairRcvDTO.setSn(sn);
        if (MpConstant.SN_IS_NOT_RETURNED.equals(pmRepairRcvDTO.getValiedMsg())){
            pmRepairRcvDTO.setValiedMsg(CommonUtils.getLmbMessage(MessageId.STATUS_NO_RESTORE_DEVICE));
            return true;
        }
        return false;
    }

    private void totalProductionRepaire(PmRepairRcv record, PmRepairRcvDTO pmRepairRcvDTO, List<PsWipInfo> wipInfoList,List<BsItemInfo> itemInfoList, Map<String,String> craftMap) throws Exception {
        String fromStation = pmRepairRcvDTO.getParamFromStation();
        if((Constant.DEVICE_PRODUCTION_MAINTENANCE).equals(fromStation)){
            List<PsWipInfoDTO> wipInfoFormSnList = new ArrayList<>();
            List<WipExtendIdentification>  wipExtendList = new ArrayList<>();
            if (Constant.INT_15==wipInfoList.get(Constant.INT_0).getItemNo().length()){
                // 根据主条码查询wip_info表
                PsWipInfoDTO formInfo = new PsWipInfoDTO();
                formInfo.setSn(pmRepairRcvDTO.getDeviceBarCode());
                formInfo.setFactoryId(record.getFactoryId());
                wipInfoFormSnList = psWipInfoRepository.getWipInfoDTOList(formInfo);
                if (CollectionUtils.isEmpty(wipInfoFormSnList)){
                    pmRepairRcvDTO.setValiedMsg(CommonUtils.getLmbMessage(MessageId.SN_NOT_EXISTS));
                    return;
                }
            }
            // 初始化整机生产维修参数
            pmRepairRcvDTO.setWipInfoList(wipInfoList);
            setInfoDetailInfo(record, pmRepairRcvDTO, wipInfoFormSnList, itemInfoList,craftMap);


        }
    }

    private boolean validInputParam(PmRepairRcv record, PmRepairRcvDTO pmRepairRcvDTO) {
        if (StringUtils.isEmpty(record.getSn())) {
            pmRepairRcvDTO.setValiedMsg(CommonUtils.getLmbMessage(MessageId.NO_MESSAGE_OF_SN));
            return true;
        }
        if (StringUtils.isEmpty(record.getFromStation())) {
            pmRepairRcvDTO.setValiedMsg(CommonUtils.getLmbMessage(MessageId.NO_FROMSTATION_OF_SN));
            return true;
        }
        return false;
    }

    /**
     * 终端校验
     *
     * @param record
     * @param pmRepairRcvDTO
     * @return
     */
    private boolean validTerminal(PmRepairRcv record, PmRepairRcvDTO pmRepairRcvDTO) {
        //条码应为7开头12位数字 条码格式校验
        String sn = record.getSn();
        if (!isSnFormat(sn)) {
            pmRepairRcvDTO.setValiedMsg(CommonUtils.getLmbMessage(MessageId.SN_MUST_START_SEVEN));
            return true;
        }
        // 条码对应批次是否有无条码送修记录
        String prodplanId = sn.substring(0, 7);
        if (hasNoSnRecord(prodplanId)) {
            pmRepairRcvDTO.setValiedMsg(prodplanId + CommonUtils.getLmbMessage(MessageId.PRODPLAN_EXIST_NO_SN_RECORD));
            return true;
        }
        // 是否已经进行装配上线扫描过站
        Map<String, Object> wipInfoMap = new HashMap<>();
        wipInfoMap.put("parentSn", sn);
        List<PsWipInfo> psWipInfos = psWipInfoRepository.getList(wipInfoMap);
        if (CollectionUtils.isNotEmpty(psWipInfos)) {
            pmRepairRcvDTO.setValiedMsg(CommonUtils.getLmbMessage(MessageId.SN_HAS_BEEN_BOUND_UNBIND_FIRST));
            return true;
        }
        pmRepairRcvDTO.setValiedMsg(MpConstant.RESULT_TYPE_OK);
        pmRepairRcvDTO.setRcvProdplanId(prodplanId);
        return false;
    }

    /**
     * 校验sn格式
     */
    private boolean isSnFormat(String sn) {
        //7开头的12位数字
        return Pattern.matches(MpConstant.SN_PATTERN, sn);
    }

    /**
     * 是否有无条码维修记录
     */
    private boolean hasNoSnRecord(String prodplanId) {
        return pmRepairRcvDetailRepository.getProdplanSnRecord(prodplanId, MpConstant.SN_TYPE_NO_SN) > 0;
    }

    private boolean judgeWipInfo(PmRepairRcv record, PmRepairRcvDTO pmRepairRcvDTO, List<PsWipInfo> wipInfoList,List<BsItemInfo> itemInfoList,Map<String,String> craftMap) {
            if (null != wipInfoList && !wipInfoList.isEmpty()) {
                if (!Constant.DEVICE_PRODUCTION_MAINTENANCE.equals(record.getFromStation())) {
                    pmRepairRcvDTO.setItemCode(wipInfoList.get(Constant.INT_0).getItemNo());
                    pmRepairRcvDTO.setCraftSection(wipInfoList.get(Constant.INT_0).getCraftSection());
                    pmRepairRcvDTO.setProcessCode(wipInfoList.get(Constant.INT_0).getCurrProcessCode());
                    pmRepairRcvDTO.setWorkStation(wipInfoList.get(Constant.INT_0).getWorkStation());
                    pmRepairRcvDTO.setRcvProdplanId(wipInfoList.get(Constant.INT_0).getAttribute1());
                    pmRepairRcvDTO.setSn(record.getSn());
                    pmRepairRcvDTO.setTaskNo(wipInfoList.get(Constant.INT_0).getAttribute2());
                    pmRepairRcvDTO.setWorkOrderNo(wipInfoList.get(Constant.INT_0).getWorkOrderNo());
                    String itemNo = wipInfoList.get(Constant.INT_0).getItemNo();
                    pmRepairRcvDTO.setMaterialVersion(itemNo.substring(itemNo.length() - Constant.INT_3, itemNo.length()));
                    pmRepairRcvDTO.setProductClass(itemInfoList.get(Constant.INT_0).getProductClass());
                    pmRepairRcvDTO.setProductSmlclass(itemInfoList.get(Constant.INT_0).getProductSmlclass());
                    pmRepairRcvDTO.setItemCode(itemInfoList.get(Constant.INT_0).getItemNo());
                    pmRepairRcvDTO.setPcbVersion(itemInfoList.get(Constant.INT_0).getVersion());
                    pmRepairRcvDTO.setItemName(itemInfoList.get(Constant.INT_0).getItemName());
                    pmRepairRcvDTO.setStyle(itemInfoList.get(Constant.INT_0).getStyle());
                    setLineNameAndCraft(record, pmRepairRcvDTO, wipInfoList, craftMap);
                }
            } else {
                pmRepairRcvDTO.setValiedMsg(CommonUtils.getLmbMessage(MessageId.NOT_FIND_RECORD_OF_SN));
                return true;
            }
        return false;
    }

    private void setLineNameAndCraft(PmRepairRcv record, PmRepairRcvDTO pmRepairRcvDTO, List<PsWipInfo> wipInfoList, Map<String, String> craftMap) {
        Map<String, String> mapGetLine = new HashMap<String, String>();
        this.changeLine(mapGetLine, record.getFactoryId(), wipInfoList, pmRepairRcvDTO);
        //设置工艺名称
        if(null != craftMap && craftMap.size() > Constant.INT_0 && StringUtils.isNotBlank(wipInfoList.get(Constant.INT_0).getCurrProcessCode())) {
            pmRepairRcvDTO.setProcessName(craftMap.get(wipInfoList.get(Constant.INT_0).getCurrProcessCode()));
        }
    }

    private void setInfoDetailInfo(PmRepairRcv record, PmRepairRcvDTO pmRepairRcvDTO, List<PsWipInfoDTO> wipInfoFormSnList, List<BsItemInfo> itemInfoList, Map<String,String> craftMap) throws Exception {
        List<PsWipInfo> wipInfoList = pmRepairRcvDTO.getWipInfoList();
        if (Constant.INT_15==wipInfoList.get(Constant.INT_0).getItemNo().length()){
            Map<String,Object> mapItem =  new HashMap();
            mapItem.put(Constant.ITEM_NO_STR, wipInfoFormSnList.get(Constant.INT_0).getItemNo());
            List<BsItemInfo> itemInfoListTotal = BasicsettingRemoteService.getStyleInfo(mapItem);
            // 送修来源为整机
            // 主工序、子工序、工站、产品大类、产品小类取整机的，
            // 条码、物料代码、物料名称 、规格型号、物料版本、PCB版本 取单板的
            initBordInformation(record, pmRepairRcvDTO, wipInfoFormSnList, itemInfoList, itemInfoListTotal);
            List<PsWipInfo> formSnList=PsWipInfoAssembler.toPsWipInfoList(wipInfoFormSnList);
            //线体取整机条码的
            setLineNameAndCraft(record, pmRepairRcvDTO, formSnList, craftMap);
        }else {
            // 送修来源为整机生产维修且送修条码是整机时，所有信息均存整机的
            List<PsWipInfoDTO> wipInfoDtoList = PsWipInfoAssembler.toPsWipInfoDTOList(wipInfoList);
            setProcessMap(wipInfoDtoList, pmRepairRcvDTO);
            initTotalMachine(record, pmRepairRcvDTO, wipInfoList, itemInfoList);
            setLineNameAndCraft(record, pmRepairRcvDTO, wipInfoList, craftMap);
        }
    }

    private void initTotalMachine(PmRepairRcv record, PmRepairRcvDTO pmRepairRcvDTO, List<PsWipInfo> wipInfoList, List<BsItemInfo> itemInfoList) {
        pmRepairRcvDTO.setRcvProdplanId(wipInfoList.get(Constant.INT_0).getAttribute2());
        pmRepairRcvDTO.setSn(record.getSn());
        pmRepairRcvDTO.setTaskNo(wipInfoList.get(Constant.INT_0).getAttribute2());
        pmRepairRcvDTO.setWorkOrderNo(wipInfoList.get(Constant.INT_0).getWorkOrderNo());
        pmRepairRcvDTO.setProductClass(itemInfoList.get(Constant.INT_0).getProductClass());
        pmRepairRcvDTO.setProductSmlclass(itemInfoList.get(Constant.INT_0).getProductSmlclass());
        pmRepairRcvDTO.setItemCode(itemInfoList.get(Constant.INT_0).getItemNo());
        pmRepairRcvDTO.setPcbVersion(itemInfoList.get(Constant.INT_0).getVersion());
        pmRepairRcvDTO.setItemName(itemInfoList.get(Constant.INT_0).getItemName());
        pmRepairRcvDTO.setStyle(itemInfoList.get(Constant.INT_0).getStyle());
    }

    private void initBordInformation(PmRepairRcv record, PmRepairRcvDTO pmRepairRcvDTO, List<PsWipInfoDTO> wipInfoFormSnList,
                                     List<BsItemInfo> itemInfoList, List<BsItemInfo> itemInfoListTotal) {
        List<PsWipInfo> wipInfoList = pmRepairRcvDTO.getWipInfoList();
        setProcessMap(wipInfoFormSnList, pmRepairRcvDTO);
        pmRepairRcvDTO.setRcvProdplanId(wipInfoFormSnList.get(Constant.INT_0).getAttribute1());
        pmRepairRcvDTO.setSn(record.getSn());
        String itemNo = wipInfoList.get(Constant.INT_0).getItemNo();
        pmRepairRcvDTO.setMaterialVersion(itemNo.substring(itemNo.length()-Constant.INT_3,itemNo.length()));
        pmRepairRcvDTO.setTaskNo(wipInfoFormSnList.get(Constant.INT_0).getAttribute2());
        pmRepairRcvDTO.setWorkOrderNo(wipInfoFormSnList.get(Constant.INT_0).getWorkOrderNo());
        pmRepairRcvDTO.setProductClass(itemInfoListTotal.get(Constant.INT_0).getProductClass());
        pmRepairRcvDTO.setProductSmlclass(itemInfoListTotal.get(Constant.INT_0).getProductSmlclass());
        pmRepairRcvDTO.setItemCode(itemInfoList.get(Constant.INT_0).getItemNo());
        pmRepairRcvDTO.setPcbVersion(itemInfoList.get(Constant.INT_0).getVersion());
        pmRepairRcvDTO.setItemName(itemInfoList.get(Constant.INT_0).getItemName());
        pmRepairRcvDTO.setStyle(itemInfoList.get(Constant.INT_0).getStyle());
    }

    private void setProcessMap(List<PsWipInfoDTO> wipInfoFormSnList, PmRepairRcvDTO pmRepairRcv) {
        pmRepairRcv.setCraftSection(wipInfoFormSnList.get(Constant.INT_0).getCraftSection());
        pmRepairRcv.setProcessCode(wipInfoFormSnList.get(Constant.INT_0).getCurrProcessCode());
        pmRepairRcv.setWorkStation(wipInfoFormSnList.get(Constant.INT_0).getWorkStation());
    }

    @Override
    public List<PmRepairRcv> getPmRepairRcvByDeliveryNo(PmRepairRcvDTO dto)throws Exception{
        dto.setStatus(Constant.REPAIR_STATUS_FICTION);
        List<PmRepairRcv> list = pmRepairRcvRepository.getHeadAndDetailList(dto);
        if (CollectionUtils.isEmpty(list)){
            return null;
        }
        StringBuilder snSb = new StringBuilder();
        //获取线体代码
        Map<String, String> lineMap = getLineCode(list,snSb);
        // 获取工艺名称
        List<BSProcess> craftList= CrafttechRemoteService.getProcessAll();
        if (CollectionUtils.isEmpty(craftList)){
            throw new  Exception(MpConstant.NO_FIND_CRAFT_MESSAGE);
        }
        Map<String,String> craftMap= new HashMap<>();
        for (BSProcess process :craftList){
            craftMap.put(process.getProcessCode(),process.getProcessName());
        }
        StringBuilder deviceSn = new StringBuilder();
        List<PmRepairRcv> deviceList = list.stream()
                .filter(pro -> pro.getItemCode() != null && Constant.INT_15 != pro.getItemCode().length())
                .collect(Collectors.toList());
        Map<String, String> deviceMap = new HashMap<>();
        //获取整机对应单板条码
        getSnOfDevice(deviceSn, deviceList, deviceMap);
        // 获取线体名称
        Map<String,String> mapGetLine = new HashMap<>();
        try {
            mapGetLine = ObtainRemoteServiceDataUtil.getLineAll(dto.getFactoryId());
        } catch (Exception e1) {
            logger.error(Constant.LOG_ERROR+e1.getMessage(),e1);
        }
        //设置线体名称、条码
        setLineNameAndCraft(list, lineMap, craftMap, mapGetLine,deviceMap);
        // 设置MBOM
        setMBom(list);
        return list;
    }

    private void setMBom(List<PmRepairRcv> list) {
        if(CollectionUtils.isEmpty(list)){
            return;
        }
        List<String> prodPlanIdList = list.stream().map(PmRepairRcv::getRcvProdplanId).distinct().collect(Collectors.toList());
        List<BProdBomHeaderDTO> bProdBomHeaderDTOS = centerfactoryRemoteService.queryProductCodeByProdPlanIdList(prodPlanIdList);
        Map<String,String> prodToMBomMap = bProdBomHeaderDTOS.stream().collect(Collectors.toMap(k -> k.getProdplanId(), v -> v.getProductCode(), (k1, k2) -> k1));
        for (PmRepairRcv entity : list) {
            entity.setMBom(entity.getItemCode());
            String mBom = prodToMBomMap.get(entity.getRcvProdplanId());
            if (StringUtils.isNotBlank(mBom)) {
                entity.setMBom(mBom);
            }
        }
    }
    private void setMBomOfVo(List<PmRepairRcvVo> list) {
        if(CollectionUtils.isEmpty(list)){
            return;
        }
        List<String> prodPlanIdList = list.stream().map(PmRepairRcvVo::getRcvProdplanId).distinct().collect(Collectors.toList());
        List<BProdBomHeaderDTO> bProdBomHeaderDTOS = centerfactoryRemoteService.queryProductCodeByProdPlanIdList(prodPlanIdList);
        Map<String,String> prodToMBomMap = bProdBomHeaderDTOS.stream().collect(Collectors.toMap(k -> k.getProdplanId(), v -> v.getProductCode(), (k1, k2) -> k1));
        for (PmRepairRcvVo entity : list) {
            entity.setMBom(entity.getItemCode());
            String mBom = prodToMBomMap.get(entity.getRcvProdplanId());
            if (StringUtils.isNotBlank(mBom)) {
                entity.setMBom(mBom);
            }
        }
    }
    private void getSnOfDevice(StringBuilder deviceSn, List<PmRepairRcv> deviceList, Map<String, String> deviceMap) {
        if (CollectionUtils.isNotEmpty(deviceList)) {
            for (PmRepairRcv info : deviceList) {
                deviceSn.append(Constant.SINGLE_QUOTE).append(info.getSn()).append(Constant.SINGLE_QUOTE).append(Constant.COMMA);
            }
            if (null!=deviceSn) {
                deviceSn.replace(deviceSn.length() - 1, deviceSn.length(), "");
                Map<String, Object> snMap = new HashMap<>();
                List<String> formSnList= new ArrayList<>();
                formSnList.add(deviceSn.toString());
                snMap.put(Constant.FORM_SN_LIST, formSnList);
                List<WipExtendIdentification> wipExtendList = wipExtendIdentificationRepository.getList(snMap);
                if (CollectionUtils.isNotEmpty(wipExtendList)) {
                    for (WipExtendIdentification extend : wipExtendList) {
                        deviceMap.put(extend.getFormSn(), extend.getSn());
                    }
                }
            }
        }
    }

    private void setLineNameAndCraft(List<PmRepairRcv> list, Map<String, String> lineMap, Map<String, String> craftMap, Map<String, String> mapGetLine,Map<String,String> deviceMap) throws Exception{
        for (PmRepairRcv info : list){
            //设置线体名称
            if(null!=lineMap) {
                if (null != mapGetLine && mapGetLine.size() > Constant.INT_0 && StringUtils.isNotBlank((lineMap.get(info.getSn())))) {
                    info.setLineName(mapGetLine.get(lineMap.get(info.getSn())));
                }
            }
            //设置工艺名称
            if(null != craftMap && craftMap.size() > Constant.INT_0 && StringUtils.isNotBlank(info.getProcessCode())) {
                info.setProcessName(craftMap.get(info.getProcessCode()));
            }
            //设置对应单板条码
            if (null!=deviceMap) {
                if (null == info.getDeviceBarCode()) {
                    info.setDeviceBarCode(deviceMap.get(info.getSn()));
                }
            }
        }
    }

    private Map<String, String> getLineCode(List<PmRepairRcv> list,StringBuilder snSb) {
        Map<String, String> lineMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(list)) {
            for (PmRepairRcv info : list) {
                snSb.append(Constant.SINGLE_QUOTE).append(info.getSn()).append(Constant.SINGLE_QUOTE).append(Constant.COMMA);
            }
            if (null!=snSb) {
                snSb.replace(snSb.length() - 1, snSb.length(), "");
                PsWipInfoDTO lineInfo = new PsWipInfoDTO();
                lineInfo.setInSns(snSb.toString());
                List<PsWipInfoDTO> wipInfoList = psWipInfoRepository.getWipInfoDTOList(lineInfo);
                //将条码和线体代码放入map中
                for (PsWipInfoDTO wip : wipInfoList) {
                    lineMap.put(wip.getSn(), wip.getLineCode());
                }
            }
        }
        return lineMap;
    }

    @Override
    public ServiceData searchResult(PmRepairRcvDTO searchDTO) {
        //this.getSnByDto(searchDTO);
        if(searchDTO.getPage() > 0 && searchDTO.getRows() > 0){
            //分页查询
            return this.searchToPage(searchDTO);
        }
        return this.searchToList(searchDTO);
    }

    @Override
    public ServiceData<List<PmRepairDqasRcvVO>> searchResultBySn(String sn, String factoryId) {
        ServiceData<List<PmRepairDqasRcvVO>> ret = new ServiceData();
        //校验sn码
        if (StringUtils.isEmpty(sn)) {
            ret.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.SN_IS_NULL));
            return ret;
        }
        if (!StringUtils.isNumeric(sn)) {
            ret.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.SN_FORMAT_ERROR));
            return ret;
        }
        //校验工厂id
        if (StringUtils.isEmpty(factoryId) || !StringUtils.isNumeric(factoryId)) {
            ret.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.FACTORY_ID_IS_NULL));
            return ret;
        }
        PmRepairRcvDTO searchDTO = new PmRepairRcvDTO();
        searchDTO.setSn(String.format("'%s'", sn));
        searchDTO.setFactoryId(BigDecimal.valueOf(Long.valueOf(factoryId)));
        List<PmRepairRcvVo> pmRepairRcvVos = pmRepairRcvRepository.getListRepairPcvAndDeatilByDto(searchDTO);
        List<PmRepairDqasRcvVO> pmRepairDqasRcvVOs = new ArrayList<>();
        if (pmRepairRcvVos != null) {
            for (PmRepairRcvVo p : pmRepairRcvVos) {
                PmRepairDqasRcvVO o = new PmRepairDqasRcvVO();
                pmRepairDqasRcvVOs.add(o);
                o.setDeliveryTime(p.getCreateDate());
                o.setDeliveryBy(p.getDeliveryBy());
                o.setErrorDescription(p.getErrorDescription());
                o.setStatus(RepairStatusUtil.getRepairStatusMessage(p.getStatus()));
            }
        }
        ret.setBo(pmRepairDqasRcvVOs);
        ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        return ret;
    }

    /**
     * 分页输出
     * @param searchDTO
     * @return ServiceData
     */
    private ServiceData searchToPage(PmRepairRcvDTO searchDTO) {
        ServiceData ret = new ServiceData();
        PageRows<PmRepairRcvVo> page = new PageRows<PmRepairRcvVo>();
        long total = pmRepairRcvRepository.countByDto(searchDTO);
        page.setTotal(total);
        searchDTO.setStartRow((searchDTO.getPage() - Constant.INT_1)
                * searchDTO.getRows() + Constant.INT_1);
        searchDTO.setEndRow(searchDTO.getPage() * searchDTO.getRows());
        List<PmRepairRcvVo> list = pmRepairRcvRepository.getPageRepairPcvAndDeatilByDto(searchDTO);
        this.setProcess(list);
        this.setUserName(list);
        //this.setRepairCount(list);
        // 增加环保属性
        this.setEpAttr(list);
        // 设置MBOM
        setMBomOfVo(list);
        page.setRows(list);
        ret.setBo(page);
        return ret;
    }

    /**
     * <AUTHOR>
     * 方法优化
     * @param list
     * @return
     */
    public Map<String, String> getUserNew(List<PmRepairRcvVo> list) {
        Map<String, String> bsPubHrMap = new HashMap<>();
        Set<String> idsSet=duplicateIds(list);
        if(idsSet.size()< NumConstant.NUM_ONE){
            return bsPubHrMap;
        }
        List<String> idsList = new ArrayList<>(idsSet);
        List<List<String>> listOfList = CommonUtils.splitList(idsList, Constant.BATCH_SIZE);
        for (List<String> pmlist : listOfList) {
            //创建人
            StringBuilder userInfoStr=new StringBuilder().append(getInStrFromList(pmlist));
            List<BsPubHrvOrgId> userInfoList = getBsPubHrvInfo(userInfoStr);
            if (!CollectionUtils.isEmpty(userInfoList)) {
                for(BsPubHrvOrgId dto : userInfoList) {
                    bsPubHrMap.put(dto.getUserId(), dto.getUserName());
                }
            }
        }
        return bsPubHrMap;
    }

    /**
     * <AUTHOR>
     * 方法提取
     * @param userInfoStr
     * @return
     */
    public List<BsPubHrvOrgId> getBsPubHrvInfo(StringBuilder userInfoStr) {
        List<BsPubHrvOrgId> tempList = new ArrayList<>();
        if (StringHelper.isNotEmpty(userInfoStr.toString())) {
            try {
                tempList = hrmUserInfoService.getBsPubHrvOrgIdInfo(userInfoStr.toString());
            } catch (Exception e) {
                logger.error("点对点调用点对点获取人员信息失败：" + e.getMessage(), e);
            }
        }
        return tempList;
    }

    //把list拼接成in查询可用的字符串
    private String getInStrFromList(List<String> list){
        if(org.springframework.util.CollectionUtils.isEmpty(list)){
            return "";
        }
        StringBuilder inStr = new StringBuilder();
        for (int i = 0; i < list.size();i++) {
            inStr.append("\'").append(list.get(i)).append("\'").append(",");
        }
        inStr.deleteCharAt(inStr.length()-1);
        return inStr.toString();
    }

    //去重，返回set
    public Set<String> duplicateIds(List<PmRepairRcvVo> list) {
        //创建人 修改人
        Set<String>  set=new HashSet<String>();
        for(PmRepairRcvVo pmRepairInfo:list) {
            if (StringHelper.isNotEmpty(pmRepairInfo.getDeliveryBy())) {
                set.add(pmRepairInfo.getDeliveryBy());
            }
            if (StringHelper.isNotEmpty(pmRepairInfo.getReceptionBy()) ) {
                set.add(pmRepairInfo.getReceptionBy());
            }
            if (StringHelper.isNotEmpty(pmRepairInfo.getLastUpdatedBy()) ) {
                set.add(pmRepairInfo.getLastUpdatedBy());
            }
            if (StringHelper.isNotEmpty(pmRepairInfo.getReturnedBy()) ) {
                set.add(pmRepairInfo.getReturnedBy());
            }
            if (StringHelper.isNotEmpty(pmRepairInfo.getReturnedTo()) ) {
                set.add(pmRepairInfo.getReturnedTo());
            }
        }
        return set;
    }

    //设置人员姓名
    public void setUserName(List<PmRepairRcvVo> list){
        if(CollectionUtils.isNotEmpty(list)) {
            //获取人员姓名 6055000040
            //人员姓名
            // List<BsPubHrvOrgId> bsPubHrvOrgIdListAll = this.getPersonName(list);
            Map<String, String> bsPubHrMap = this.getUserNew(list);
            for(PmRepairRcvVo pro : list) {
                //设置人员信息 6055000031
                if (StringHelper.isNotEmpty(pro.getReturnedBy()) && StringHelper.isNotEmpty(bsPubHrMap.get(pro.getReturnedBy()))) {
                    pro.setReturnedByName(bsPubHrMap.get(pro.getReturnedBy()) + pro.getReturnedBy());
                }
                if (StringHelper.isNotEmpty(pro.getReturnedTo()) && StringHelper.isNotEmpty(bsPubHrMap.get(pro.getReturnedTo()))) {
                    pro.setReturnedToName(bsPubHrMap.get(pro.getReturnedTo()) + pro.getReturnedTo());
                }
                setDeliverAndReceptionName(bsPubHrMap, pro);
            }
        }
    }

    /* Started by AICoder, pid:m846c74e85aa34b142500840f0c23c1e9bb4bb7d */
    private void setDeliverAndReceptionName(Map<String, String> bsPubHrMap, PmRepairRcvVo pro) {
        setPersonName(bsPubHrMap, pro::getDeliveryBy, pro::setDeliveryByName);
        setPersonName(bsPubHrMap, pro::getReceptionBy, pro::setReceptionByName);
    }

    private void setPersonName(Map<String, String> bsPubHrMap, Supplier<String> getter, Consumer<String> setter) {
        String id = getter.get();
        if (StringHelper.isNotEmpty(id)) {
            String name = bsPubHrMap.get(id);
            if (StringHelper.isNotEmpty(name)) {
                setter.accept(name + id);
            }
        }
    }
    /* Ended by AICoder, pid:m846c74e85aa34b142500840f0c23c1e9bb4bb7d */

    /**
     * 列表输出
     * @param searchDTO
     * @return ServiceData
     */
    private ServiceData searchToList(PmRepairRcvDTO searchDTO) {
        ServiceData ret = new ServiceData();
        List<PmRepairRcvVo> list = pmRepairRcvRepository.getListRepairPcvAndDeatilByDto(searchDTO);
        this.setProcess(list);
        // 设置MBOM
        setMBomOfVo(list);
        ret.setBo(list);
        return ret;
    }

    /**
     * 批量驳回
     *
     * @param dto
     * @return ServiceData
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ServiceData batchReject(PmRepairRcvDTO dto) {
        ServiceData ret = new ServiceData();
        // 开关
        String switchStr=getSwitchStr(MpConstant.LOOKUP_6672,MpConstant.LOOKUP_6672002);
        if(StringUtils.equals(Constant.FLAG_Y,switchStr)){
            //如果有领料单，不能驳回
            MaintenanceMaterialInfoDTO maintenanceMaterialInfoDTO=new MaintenanceMaterialInfoDTO();
            maintenanceMaterialInfoDTO.setReceptionDetailIds(dto.getReceptionDetailId());
            maintenanceMaterialInfoDTO.setNoItemStaus(MpConstant.RESOURCE_STATUS_RETURNED_MATERIAL);
            maintenanceMaterialInfoDTO.setNoBillStatus(MpConstant.RESOURCE_STATUS_INVALID);
            List<MaintenanceMaterialInfoDTO> maintenanceMaterialInfoDTOS=maintenanceMaterialLineRepository.getListByReceptionDetailIds(maintenanceMaterialInfoDTO);
            if(CollectionUtils.isNotEmpty(maintenanceMaterialInfoDTOS)){
                ret.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.EXIST_MATERIAL_REQUISITION_CANNOT_BE_REJECTED));
                return ret;
            }
        }

        // 状态为待接收或者待维修可驳回，状态变为拟制中
        // 待接收状态或者待维修状态(用逗号拼接)
        dto.setStatus(Constant.SINGLE_QUOTE + Constant.REPAIR_STATUS_TO_BE_RECEIVED + Constant.SEPARATED_COMMA_COMMA + Constant.REPAIR_STATUS_WAIT + Constant.SINGLE_QUOTE);
        PmRepairRcvDTO queryDto = new PmRepairRcvDTO();
        queryDto.setStatus(dto.getStatus());
        queryDto.setReceptionDetailId(dto.getReceptionDetailId());
        List<PmRepairRcvVo> list = pmRepairRcvRepository.getListRepairPcvAndDeatilByDto(queryDto);
        if (getVoList(dto, ret, list)){
            return ret;
        }
        try {
            //驳回时，状态改为拟制中
            dto.setStatus(Constant.REPAIR_STATUS_FICTION);
            dto.setLastUpdatedDate(new Date());
            int count = pmRepairRcvRepository.updatePmRePairRcvDetailStatusByParams(dto);
            ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
            ret.getCode().setMsg(count + Constant.STRING_EMPTY);
            logger.info("更新数量：{}条！", count);
        }catch (Exception e){
            throw e;
        }
        return ret;
    }

    /**
     * 批量接收
     *
     * @param dto
     * @return ServiceData
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ServiceData batchReceived(PmRepairRcvDTO dto) {
        ServiceData ret = new ServiceData();
        // 状态为待接收可接收，状态变为待维修
        // 待接收状态
        dto.setStatus(Constant.SINGLE_QUOTE + Constant.REPAIR_STATUS_TO_BE_RECEIVED + Constant.SINGLE_QUOTE);
        PmRepairRcvDTO queryDto = new PmRepairRcvDTO();
        queryDto.setStatus(dto.getStatus());
        queryDto.setReceptionDetailId(dto.getReceptionDetailId());
        List<PmRepairRcvVo> list = pmRepairRcvRepository.getListRepairPcvAndDeatilByDto(queryDto);
        if (getVoList(dto, ret, list)){
            return ret;
        }
        try {
            //接收时，状态改为待维修
            dto.setStatus(Constant.REPAIR_STATUS_WAIT);
            dto.setLastUpdatedDate(new Date());
            dto.setReceivingTime(new Date());
            int count = pmRepairRcvRepository.updatePmRePairRcvDetailStatusByParams(dto);
            //更新条码主工序、子工序，写扫描历史
            updateSnAndInsertHistory(list);
            ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
            ret.getCode().setMsg(count + Constant.STRING_EMPTY);
            logger.info("更新数量：{}条！", count);
        }catch (Exception e){
            throw e;
        }
        return ret;
    }

    /**
     * 删除送修记录明细
     * 物理删除
     * @param dto
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ServiceData deletePmRepairRcvDeatil(PmRepairRcvDTO dto) {
        ServiceData ret = new ServiceData();
        PmRepairRcvVo repairRcvVo = pmRepairRcvRepository.getRepairPcvAndDeatilByDeatilId(dto.getReceptionDetailId());
        if(null == repairRcvVo || !Constant.REPAIR_STATUS_FICTION.equals(repairRcvVo.getStatus())){
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.DATA_REPAIR_STATUS_IS_NOT_FICTION);
        }
        /**
         * 状态为拟制中可修改；
         * --物理删除（送修来源为“整机生产维修"且送修详情表的整机条码不为空时将整机条码回到原主工序、子工序，
         * 其余场景将送修详情表条码回到原主工序、子工序，）
         */
        List<PsWipInfo> psWipInfosList = new ArrayList<PsWipInfo>();
        List<PsScanHistory> psScanHistorys = new ArrayList<>();
        PsWipInfo psWipInfo = new PsWipInfo();
        if(Constant.DEVICE_PRODUCTION_MAINTENANCE.equals(repairRcvVo.getFromStation())
                && StringUtils.isNotEmpty(repairRcvVo.getDeviceBarCode())){
            psWipInfo.setSn(repairRcvVo.getDeviceBarCode());
        }else{
            psWipInfo.setSn(repairRcvVo.getSn());
        }
        psWipInfo.setLastUpdatedBy(dto.getLastUpdatedBy());
        psWipInfo.setLastUpdatedDate(new Date());
        // 主工序
        psWipInfo.setCraftSection(repairRcvVo.getCraftSection());
        // 子工序
        psWipInfo.setCurrProcessCode(repairRcvVo.getProcessCode());
        //工站
        psWipInfo.setWorkStation(repairRcvVo.getWorkStation());
        //状态
        psWipInfo.setStatus(Constant.FLAG_Y);
        psWipInfosList.add(psWipInfo);
        PsWipInfo currWip = psWipInfoService.getWipInfoBySn(psWipInfo.getSn());
        try{
            // 批量更新在制表
            updateBatch(psWipInfosList);
            pmRepairRcvServiceRecode.addPsWipScanHistory(currWip, psScanHistorys, psWipInfo);
            scanHistoryServiceImpl.insertPsScanHistoryBatch(psScanHistorys,NumConstant.NUM_100);
            pmRepairRcvRepository.deletePmRePairRcvDetailByDetailId(dto.getReceptionDetailId());
            ret.setBo(null);
            ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        }catch(Exception e){
            logger.error("删除送修单明细失败！原因： {}",e.getMessage());
            throw e;
        }
        return ret;
    }

    private boolean getVoList(PmRepairRcvDTO dto, ServiceData ret, List<PmRepairRcvVo> list) {
        String status = dto.getStatus().replace(Constant.SINGLE_QUOTE, Constant.STR_EMPTY);
        int length = dto.getReceptionDetailId().split(Constant.COMMA).length;
        if (null == list || list.isEmpty() || length != list.size()) {
            if (status.equals(Constant.REPAIR_STATUS_TO_BE_RECEIVED + Constant.COMMA + Constant.REPAIR_STATUS_WAIT)) {
                //批量驳回时,数据不为"待接收"或"待维修"状态，则抛异常
                ret.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, MessageId. DATA_REPAIR_STATUS_IS_NOT_TO_BE_RECEIVED_OR_MAINTENANCE));
                return true;
            } else if (status.equals(Constant.REPAIR_STATUS_TO_BE_RECEIVED)) {
                //批量接收,数据不为"待接收"状态，则抛异常
                ret.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, MessageId. DATA_REPAIR_STATUS_IS_NOT_TO_BE_RECEIVED));
                return true;
            }
        } else if (length == list.size() && Constant.REPAIR_STATUS_TO_BE_RECEIVED.equals(status)) {
            //批量接收,数据为全"待接收"状态，则返回false
            return false;
        }

        //批量驳回时，作"待接收"、"待维修"状态下的驳回权限校验
        //获取用户
        String empNo = dto.getLastUpdatedBy();
        //从数据字典查询"待接收"状态下有驳回权限的用户
        List<SysLookupValuesDTO> lookUpList = BasicsettingRemoteService.getSysLookupValuesList(new SysLookupValuesDTO(){{setLookupType(new BigDecimal(Constant.SYS_LOOK_TYPE_ORDER_REJECT_EMP_IN_RECEIVED));}});
        List<String> receivedList = lookUpList.stream()
                .filter(e -> StringUtils.isNotBlank(e.getLookupMeaning()))
                .flatMap(e -> Arrays.stream(e.getLookupMeaning().split(Constant.COMMA)))
                .collect(Collectors.toList());
        //从数据字典查询"待维修"状态下有驳回权限的用户
        lookUpList = BasicsettingRemoteService.getSysLookupValuesList(new SysLookupValuesDTO(){{setLookupType(new BigDecimal(Constant.SYS_LOOK_TYPE_ORDER_REJECT_EMP_IN_MAINTENANCE));}});
        List<String> maintenanceList = lookUpList.stream()
                .filter(e -> StringUtils.isNotBlank(e.getLookupMeaning()))
                .flatMap(e -> Arrays.stream(e.getLookupMeaning().split(Constant.COMMA)))
                .collect(Collectors.toList());

        if (extractedGetVoList(ret, list, empNo, receivedList, maintenanceList)) {
            return true;
        }

        return false;
    }

    public boolean extractedGetVoList(ServiceData ret, List<PmRepairRcvVo> list, String empNo, List<String> receivedList, List<String> maintenanceList) {
        String voStatus;
        if(CollectionUtils.isNotEmpty(list)){
            for (PmRepairRcvVo vo : list){
                voStatus = vo.getStatus();
                if (!receivedList.contains(empNo) && voStatus.equals(Constant.REPAIR_STATUS_TO_BE_RECEIVED)) {
                    //如果数据状态为"待接收",且该状态下用户没有驳回操作权限，则抛异常
                    ret.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, MessageId. NO_PERMISSION_TO_REJECT_IN_RECEIVED_STATUS));
                    return true;
                } else if (!maintenanceList.contains(empNo) && voStatus.equals(Constant.REPAIR_STATUS_WAIT)) {
                    //如果数据状态为"待维修",且该状态下用户没有驳回操作权限，则抛异常
                    ret.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, MessageId. NO_PERMISSION_TO_REJECT_IN_MAINTENANCE_STATUS));
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 设置子工序 将子工序编码转换为中文
     */
    private void setProcess(List<PmRepairRcvVo> list){
        BSProcess queryProcess = new BSProcess();
        queryProcess.setxType(MpConstant.PROCESS_X_TYPE_P);
        try {
            List<BSProcess> processList = CrafttechRemoteService.getProcess(queryProcess);
            if(null != processList && !processList.isEmpty() && null != list && !list.isEmpty()){
                for (PmRepairRcvVo vo: list) {
                    processList.stream().forEach(item ->{
                        this.foreachSetProcess(vo, item);
                    });
                }
            }
        }catch (Exception e){
            e.printStackTrace();
            logger.error("获取子工序列表失败！原因： {}", e.getMessage());
        }
    }

    /**
     * 循环设置子工序的值
     * @param vo
     * @param item
     */
    private void foreachSetProcess(PmRepairRcvVo vo, BSProcess item) {
        if(StringUtils.isNotEmpty(vo.getProcessCode()) && StringUtils.isNotEmpty(item.getProcessCode())){
            // 子工序
            if(vo.getProcessCode().equals(item.getProcessCode())){
                vo.setProcessCode(item.getProcessName());
            }
        }

        if(StringUtils.isNotEmpty(vo.getProcessCodeZs()) && StringUtils.isNotEmpty(item.getProcessCode())){
            // 返还子工序
            if(vo.getProcessCodeZs().equals(item.getProcessCode())){
                vo.setProcessCodeZs(item.getProcessName());
            }
        }
    }

    /**
     * 导出excel
     */
    @Override
    public void exportExcel(HttpServletResponse resp) throws Exception {
        ExcelWriter excelWriter = null;
        try {
            String[] firstRow = ExcelName.TEMPALTE_DATA;
            List<PmRepairRcvDetailDTO> dataList = new ArrayList<>();
            PmRepairRcvDetailDTO example = new PmRepairRcvDetailDTO();
            example.setReceptionBy(firstRow[0]);
            example.setReturnedBy(firstRow[1]);
            example.setProcessName(firstRow[2]);
            example.setSn(firstRow[3]);
            dataList.add(example);
            String fileName = MpConstant.REPAIRRESTORE_NAME_XLS;
            ImesExcelUtil.setResponseHeader(resp, fileName);
            excelWriter = EasyExcelFactory.write(resp.getOutputStream(), PmRepairRcvDetailDTO.class)
                    .excelType(ExcelTypeEnum.XLSX).build();
            WriteSheet build = EasyExcelFactory.writerSheet(0, "0").build();
            excelWriter.write(dataList, build);
        }finally {
            if (Objects.nonNull(excelWriter)) {
                excelWriter.finish();
            }
        }
    }

    /**
     * Excel表格校验解析
     *
     * <AUTHOR>
     */
    @Override
    public List<PmRepairRcvDetailDTO> resolveExcel(InputStream inputStream) throws Exception {
        List<PmRepairRcvDetailDTO> excelInfoList = new LinkedList<>();
        EasyExcelUtils.read(inputStream, PmRepairRcvDetailDTO.class, new AnalysisEventListener() {
            @Override
            public void invoke(Object o, AnalysisContext analysisContext) {
                PmRepairRcvDetailDTO temp = (PmRepairRcvDetailDTO)o;
                excelInfoList.add(temp);
            }
            @Override
            public void doAfterAllAnalysed(AnalysisContext analysisContext) {

            }
        }).doReadAll();
        return checkReadData(excelInfoList);
    }

    /**
     * 校验读取数据
     * @param excelInfoList 读取表格数据
     * @return 参数
     * @throws Exception 业务异常
     */
    private List<PmRepairRcvDetailDTO> checkReadData(List<PmRepairRcvDetailDTO> excelInfoList) throws Exception{
        if (CollectionUtils.isEmpty(excelInfoList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.DATA_IS_EMPTY);
        }
        //校验是否存在空数据
        List<String> msgList = validateEmpty(excelInfoList);
        if (CollectionUtils.isNotEmpty(msgList)){
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.CUSTOMIZE_MSG, new Object[]{msgList.toString().substring(1,msgList.toString().length()-1)});
        }
        //校验重复条码
        this.validRepeatSn(excelInfoList);
        //校验员工号
        this.validEmpNo(excelInfoList);
        return excelInfoList;
    }

    /* Started by AICoder, pid:j54c9nbfd9c45ef14cae08dad0c8f419e3e52488 */

    /**
     * 校验重复SN
     *
     * @param excelInfoList 解析数据
     */
    private void validRepeatSn(List<PmRepairRcvDetailDTO> excelInfoList) {
        Map<String, Long> snCountMap = excelInfoList.stream()
                .collect(Collectors.toMap(PmRepairRcvDetailDTO::getSn, v -> 1L, Long::sum));

        for (Map.Entry<String, Long> entry : snCountMap.entrySet()) {
            if (entry.getValue() > 1) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SN_IS_REPEAT,
                        new Object[]{entry.getKey()});
            }
        }
    }

    /* Ended by AICoder, pid:j54c9nbfd9c45ef14cae08dad0c8f419e3e52488 */

    /* Started by AICoder, pid:z3b325db4ddd631147df0bac60499b447d22bfb3 */

    /**
     * 校验工号信息
     *
     * @param excelInfoList excel
     * @throws Exception 业务异常
     */
    private void validEmpNo(List<PmRepairRcvDetailDTO> excelInfoList) throws Exception {
        Set<String> userIdList = new HashSet<>();

        // Add only non-blank user IDs to the set
        excelInfoList.forEach(item -> {
            String returnedBy = item.getReturnedBy();
            String receptionBy = item.getReceptionBy();
                userIdList.add(returnedBy);
                userIdList.add(receptionBy);
        });

        // Fetch existing users
        List<BsPubHrvOrgId> existingUsers = hrmUserInfoService.getBsPubHrvOrgIdInfo(new LinkedList<>(userIdList));

        if (CollectionUtils.isEmpty(existingUsers)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.EMPLOYEE_IS_NOT_EXIST);
        }

        // Collect existing user IDs
        Set<String> existingUserIds = existingUsers.stream()
                .map(BsPubHrvOrgId::getUserId)
                .collect(Collectors.toSet());

        // Find non-existing users
        List<String> nonExistingUsers = userIdList.stream()
                .filter(userId -> !existingUserIds.contains(userId))
                .collect(Collectors.toList());

        if (!nonExistingUsers.isEmpty()) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.EMPLOYEE_IS_NOT_EXIST,
                    new Object[]{nonExistingUsers.toString()});
        }
    }

    /* Ended by AICoder, pid:z3b325db4ddd631147df0bac60499b447d22bfb3 */

    /**
     * 校验空数据
     *
     * @param excelInfoList 解析数据
     * @return
     * <AUTHOR>
     */
    private  List<String>  validateEmpty(List<PmRepairRcvDetailDTO> excelInfoList) {
        StringBuilder validReceptionByStr = new StringBuilder();
        long receptionCount = 0;
        validReceptionByStr.append(CommonUtils.getLmbMessage(MessageId.RECPETIONBY_IS_NULL));
        StringBuilder validReturnedByStr = new StringBuilder();
        long returnedCount = 0;
        validReturnedByStr.append(CommonUtils.getLmbMessage(MessageId.RETURNEDBY_IS_NULL));
        StringBuilder validCraftsectionStr = new StringBuilder();
        long craftsectionCount = 0;
        validCraftsectionStr.append(CommonUtils.getLmbMessage(MessageId.SUB_CRAFTSECTION_IS_NULL));
        StringBuilder validSnStr = new StringBuilder();
        long snCount = 0;
        // 校验空数据
        long count =0;
        for (PmRepairRcvDetailDTO info : excelInfoList) {
            if (StringUtils.isEmpty(info.getReceptionBy())) {
                validReceptionByStr.append("'").append(info.getSn()).append("'").append(",");
                receptionCount++;
            }
            if (StringUtils.isEmpty(info.getReturnedBy())) {
                validReturnedByStr.append("'").append(info.getSn()).append("'").append(",");
                returnedCount++;
            }
            if (StringUtils.isEmpty(info.getProcessName())) {
                validCraftsectionStr.append("'").append(info.getSn()).append("'").append(",");
                craftsectionCount++;
            }
            if (StringUtils.isEmpty(info.getSn())){
                validSnStr.append(count).append(",");
                snCount++;
            }
            count++;
        }
        List<String> msgList = new ArrayList<>();
        if (receptionCount!=0){
            msgList.add(validReceptionByStr.toString().substring(0,validReceptionByStr.toString().length()-1));
        }
        if (returnedCount!=0){
            msgList.add(validReturnedByStr.toString().substring(0,validReturnedByStr.toString().length()-1));
        }
        if (craftsectionCount!=0){
            msgList.add(validCraftsectionStr.toString().substring(0,validCraftsectionStr.toString().length()-1));
        }
        if (snCount!=0){
            msgList.add(CommonUtils.getLmbMessage(MessageId.SNS_IS_NOT_NULL,validSnStr.toString().substring(0,validSnStr.toString().length()-1)));
        }
        return msgList;
    }

    @Override
    public List<SysLookupValuesDTO> getListByLookupType(String typeCode) {
        List<SysLookupValuesDTO> list = BasicsettingRemoteService.getLookupValueByTypeCodes(typeCode);
        return list;
    }

    private void  getAllTaskNoOrPlanId(List<PmRepairRcvVo> list, List<String>  rcvTaskNos, List<String> rcvProdplanIds){
        for (PmRepairRcvVo pmRepairRcvVo : list) {
            //整机条码根据任务号查询环保属性  单板条码根据批次查询
            if (StringUtils.isEmpty(pmRepairRcvVo.getItemCode())) {
                continue;
            }
            if (checkZjSn(pmRepairRcvVo.getItemCode())) {
                rcvTaskNos.add(pmRepairRcvVo.getTaskNo());
            } else {
                rcvProdplanIds.add(pmRepairRcvVo.getRcvProdplanId());
            }
        }
    }

    /**
     * 设置环保属性
     * @param list 传入列表列表
     */
    private void setEpAttr(List<PmRepairRcvVo> list) {
        if (CollectionUtils.isNotEmpty(list)) {
            List<PsTask> taskNoPsTaskList = new ArrayList<PsTask>();
            List<PsTask> planIdPsTaskList = new ArrayList<PsTask>();
            List<String> rcvProdplanIds = new ArrayList<String>();
            List<String> rcvTaskNos = new ArrayList<String>();
            //获取所有任务号，批次
            getAllTaskNoOrPlanId(list,rcvTaskNos,rcvProdplanIds);
            //去重
            removeDuplicate(rcvTaskNos);
            removeDuplicate(rcvProdplanIds);

            List<List<String>> planIdSplitList = CommonUtils.splitList(rcvProdplanIds, Constant.BATCH_SIZE);
            for (List<String> splitList : planIdSplitList) {
                List<PsTask> psTaskList = PlanscheduleRemoteService.getEnvAttrBatchByIds(splitList);
                if (CollectionUtils.isNotEmpty(psTaskList)) {
                    planIdPsTaskList.addAll(psTaskList);
                }
            }
            List<List<String>> taskNoSplitList = CommonUtils.splitList(rcvTaskNos, Constant.BATCH_SIZE);
            for (List<String> splitList : taskNoSplitList) {
                List<PsTask> psTaskList = PlanscheduleRemoteService.getEnvAttrBatchByIds(splitList);
                if (CollectionUtils.isNotEmpty(psTaskList)) {
                    taskNoPsTaskList.addAll(psTaskList);
                }
            }

            Map<String, PsTask> taskNoEnvAttrMap = taskNoPsTaskList.stream().collect(Collectors.toMap(PsTask::getTaskNo, a -> a, (k1, k2) -> k1));
            Map<String, PsTask> planIdEnvAttrMap = planIdPsTaskList.stream().collect(Collectors.toMap(PsTask::getProdplanId, a -> a, (k1, k2) -> k1));

            //单板根据批次获取环保属性，整机条码根据任务号
            setEpAttrName(taskNoEnvAttrMap,planIdEnvAttrMap,list);
        }


    }

    private void setEpAttrName(Map<String, PsTask> taskNoEnvAttrMap,Map<String, PsTask> planIdEnvAttrMap,List<PmRepairRcvVo> list){
        for (PmRepairRcvVo pmRepairRcvVo : list) {
            if (StringUtils.isEmpty(pmRepairRcvVo.getItemCode())) {
                continue;
            }
            if (MapUtils.isNotEmpty(taskNoEnvAttrMap) && checkZjSn(pmRepairRcvVo.getItemCode()) && StringUtils.isNotEmpty(pmRepairRcvVo.getTaskNo())){
                PsTask psTask=taskNoEnvAttrMap.get(pmRepairRcvVo.getTaskNo());
                if(psTask!=null){
                    pmRepairRcvVo.setEpAttrName(psTask.getEnvAttrName());
                }
            }
            if(MapUtils.isNotEmpty(planIdEnvAttrMap)&&!checkZjSn(pmRepairRcvVo.getItemCode())&& StringUtils.isNotEmpty(pmRepairRcvVo.getRcvProdplanId())){
                PsTask psTask=planIdEnvAttrMap.get(pmRepairRcvVo.getRcvProdplanId());
                if(psTask!=null) {
                    pmRepairRcvVo.setEpAttrName(psTask.getEnvAttrName());
                }
            }
        }
    }

    //校验是否整机条码
    private boolean checkZjSn(String itemCode) {
        if (itemCode.length() == MpConstant.NUM_12) {
            return true;
        }
        return MpConstant.FALSE;
    }

    //去重
    private static void removeDuplicate(List list) {
        HashSet set = new HashSet(list);
        list.clear();
        list.addAll(set);
    }

    @Override
    public List<PmRepairRcvVo> getListRepairPcvAndDeatilByDto(PmRepairRcvDTO dto) {
        return pmRepairRcvRepository.getListRepairPcvAndDeatilByDto(dto);
    }

    /**
     * @param dto 检索参数
     * @return 送修单查询数据
     * @throws Exception
     */
    @Override
    public List<PmRepairRcvVo> exportPmRepairRcv(PmRepairRcvDTO dto,String factoryId) {
        List<PmRepairRcvVo> list = pmRepairRcvRepository.exportPmRepairRcv(dto);
        if(org.springframework.util.CollectionUtils.isEmpty(list)){
            return list;
        }
        this.setProcess(list);
        this.setUserName(list);
        this.setEpAttr(list);
        // 维修状态数据字典
        List<Map> lookupTypeRepair = this.getLookupTypesAll(Constant.LOOK_UP_TYPE_REPAIR_STATUS,factoryId);
        // 楼栋数据字典
        List<Map> lookupTypeBuilding = this.getLookupTypesAll(Constant.LOOK_UP_TYPE_BUILDING_STATUS,factoryId);
        // 申请部门数据字典
        List<Map> lookupTypeDert = this.getLookupTypesAll(Constant.LOOK_UP_TYPE_DEPT_STATUS,factoryId);
        // 申请班组数据字典
        List<Map> lookupTypeGroup = this.getLookupTypesAll(Constant.LOOK_UP_TYPE_GROUP_STATUS,factoryId);
        list.forEach(pmRepairRcvVo -> {
            setValues(lookupTypeRepair, lookupTypeBuilding, lookupTypeDert, lookupTypeGroup, pmRepairRcvVo);
            pmRepairRcvVo.setCreateDateStr(DateUtil.convertDateToString(pmRepairRcvVo.getCreateDate(),DateUtil.DATE_FORMATE_FULL));
            pmRepairRcvVo.setReceivingTimeStr(DateUtil.convertDateToString(pmRepairRcvVo.getReceivingTime(),DateUtil.DATE_FORMATE_FULL));
            pmRepairRcvVo.setRepairRcvDateStr(DateUtil.convertDateToString(pmRepairRcvVo.getReturnedDate(),DateUtil.DATE_FORMATE_FULL));
        });
        return list;
    }

    /**
     * 获取无条码送修的虚拟条码
     */
    @Override
    public List<String> getRepairSnList(PmRepairVirtualSnDTO dto) throws Exception {
        // 校验批次号、数量
        if (StringUtils.isEmpty(dto.getProdplanId())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PRODPLAN_IS_NULL);
        }
        if (dto.getQty() == null || dto.getQty() < MpConstant.PRODPLAN_QTY_MIN) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PRODPLAN_QTY_LESS);
        }
        if (dto.getQty() > MpConstant.PRODPLAN_QTY_MAX) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PRODPLAN_QTY_MORE);
        }
        // 校验此批次是否有输入类型为“有条码录入”送修记录，如果有则报错
        if (pmRepairRcvDetailRepository.getProdplanSnRecord(dto.getProdplanId(), MpConstant.SN_TYPE_HAS_SN) > 0) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PRODPLAN_EXIST_SN_RECORD);
        }
        return PlanscheduleRemoteService.getRepairSnListRemote(dto);
    }

    /**
     * 获取无条码送修批次待维修的最小条码
     *
     * @param dto
     */
    @Override
    public String getRepairMinSn(PmRepairVirtualSnDTO dto) throws MesBusinessException {
        // 校验此批次是否有输入类型为“有条码录入”送修记录，如果有则报错
        if (pmRepairRcvDetailRepository.getProdplanSnRecord(dto.getProdplanId(), MpConstant.SN_TYPE_HAS_SN) > 0) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PRODPLAN_EXIST_SN_RECORD);
        }
        return pmRepairRcvDetailRepository.getRepairMinSn(dto.getProdplanId());
    }


    /**
     * 获取无条码送修批次待维修的条码
     *
     * @param dto
     */
    @Override
    public List<String> getNoSnRepairSns(PmRepairVirtualSnDTO dto) throws MesBusinessException {
        if (pmRepairRcvDetailRepository.getProdplanSnRecord(dto.getProdplanId(), MpConstant.SN_TYPE_HAS_SN) > 0) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PRODPLAN_EXIST_SN_RECORD);
        }
        if (dto.getQty() == null || dto.getQty() < MpConstant.PRODPLAN_QTY_MIN) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PRODPLAN_QTY_LESS);
        }
        if (dto.getQty() > Constant.INT_100) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PRODPLAN_QTY_MORE);
        }
        if (StringUtils.isNotEmpty(dto.getStartSn()) && !dto.getStartSn().startsWith(dto.getProdplanId())) {
            dto.setStartSn(null);
        }
        return pmRepairRcvDetailRepository.getNoSnRepairSns(dto);
    }

    private void setValues(List<Map> lookupTypeRepair, List<Map> lookupTypeBuilding, List<Map> lookupTypeDert, List<Map> lookupTypeGroup, PmRepairRcvVo pmRepairRcvVo) {
        if(Objects.nonNull(lookupTypeRepair)&&Objects.nonNull(lookupTypeBuilding)
                &&Objects.nonNull(lookupTypeDert)&&Objects.nonNull(lookupTypeGroup)) {
            lookupTypeRepair.forEach(repair -> {
                if (repair.get(Constant.FIELD_LOOKUP_CODE).toString().equals(pmRepairRcvVo.getStatus())) {
                    pmRepairRcvVo.setStatus(repair.get(Constant.FIELD_DESCRIPTION_CHIN_V).toString());
                }
            });
            lookupTypeBuilding.forEach(building -> {
                if (building.get(Constant.FIELD_LOOKUP_CODE).toString().equals(pmRepairRcvVo.getBuilding())) {
                    pmRepairRcvVo.setBuilding(building.get(Constant.FIELD_DESCRIPTION_CHIN_V).toString());
                }
            });
            lookupTypeDert.forEach(dert -> {
                if (dert.get(Constant.FIELD_LOOKUP_CODE).toString().equals(pmRepairRcvVo.getApplicationDepartment())) {
                    pmRepairRcvVo.setApplicationDepartment(dert.get(Constant.FIELD_DESCRIPTION_CHIN_V).toString());
                }
            });
            lookupTypeGroup.forEach(group -> {
                if (group.get(Constant.FIELD_LOOKUP_CODE).toString().equals(pmRepairRcvVo.getApplicationSection())) {
                    pmRepairRcvVo.setApplicationSection(group.get(Constant.FIELD_DESCRIPTION_CHIN_V).toString());
                }
            });
        }
    }

    /**
     *
     * @param lookupType
     * @return
     * @throws Exception
     */
    public List<Map> getLookupTypesAll(String lookupType,String factoryId) {
        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("lookupType", lookupType);
        logger.info("远程调用同步的入参 :{}", paramsMap);
        Map<String, String> headerParamsMap = MESHttpHelper.getHttpRequestHeader();
        headerParamsMap.put(SysConst.HTTP_HEADER_X_FACTORY_ID, factoryId);
        try {
            String responseStr = MicroServiceRestUtil.invokeService(InterfaceEnum.basicSettingLookupTypesList.getServiceName(),
                    MicroServiceNameEum.VERSION, InterfaceEnum.basicSettingLookupTypesList.getReqType(),
                    InterfaceEnum.basicSettingLookupTypesList.getUrl(), JacksonJsonConverUtil.beanToJson(paramsMap), headerParamsMap);
            JsonNode json = JacksonJsonConverUtil.getMapperInstance().readTree(responseStr);
            logger.info("远程调用同步的结果 :{}", json);
            if (Objects.isNull(json)) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.QUERY_RESULT_IS_NULL);
            }
            String retCode = json.get(MpConstant.JSON_CODE).get(MpConstant.JSON_CODE).asText();
            String bo = json.get(MpConstant.JSON_BO).toString();
            if (!RetCode.SUCCESS_CODE.equalsIgnoreCase(retCode)) {
                throw new Exception(bo);
            }
            return JSONObject.parseArray(json.get(MpConstant.JSON_BO).toString(), Map.class);
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
        return null;
    }

    @Override
    public List<PmRepairRcv> postList(PmRepairRcvDTO record)  throws Exception{
        List<PmRepairRcv> resultList = new LinkedList<>();
        // 是子板不需要查送修记录
        if(!record.isChildCard()){
            Page<PmRepairRcv> page = new Page(Constant.INT_1,Constant.INT_1);
            page.setParams(record);
            List<PmRepairRcv> pmRepairRcvs = pmRepairRcvRepository.postList(page);
            if(CollectionUtils.isEmpty(pmRepairRcvs)){
                return new LinkedList<>();
            }
            resultList.add(pmRepairRcvs.get(Constant.INT_0));
        }else {
            PmRepairRcv dto =  new  PmRepairRcv();
            dto.setSn(record.getSn());
            resultList.add(dto);
        }
        Map<String,Object> map = new HashMap<>();
        map.put("sn", record.getSn());
        List<PsWipInfo> list = psWipInfoRepository.getList(map);
        if(CollectionUtils.isEmpty(list)){
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,MessageId.PLEASE_ENTER_THE_CORRECT_BARCODE);
        }
        List<String> snList = new ArrayList<>();
        snList.add(record.getSn());
        this.snScrap(snList);
        PsWipInfo psWipInfo = list.get(Constant.INT_0);
        String attribute1 = psWipInfo.getAttribute1();
        // 制造BOM料单替换 begin
        String mbomProductCode = psWipInfo.getItemNo();
        BProdBomHeaderDTO bProdBomHeaderDTO = new BProdBomHeaderDTO();
        bProdBomHeaderDTO.setProdplanId(attribute1);
        bProdBomHeaderDTO.setOriginalProductCode(psWipInfo.getItemNo());
        List<BProdBomHeaderDTO> bProdBomHeader = bProdBomService.getBProdBomHeader(bProdBomHeaderDTO);
        if (CollectionUtils.isNotEmpty(bProdBomHeader)) {
            mbomProductCode = bProdBomHeader.get(Constant.INT_0).getProductCode();
        }
        String finalMbomProductCode = mbomProductCode;
        resultList.forEach(item -> item.setMbomProductCode(finalMbomProductCode));
        // 制造BOM料单替换 end
        resultList.forEach(item->{
            item.setItemName(psWipInfo.getItemName());
            item.setItemCode(psWipInfo.getItemNo());
            if(StringUtils.isNotBlank(attribute1)){
                if(attribute1.length()>Constant.INT_7){
                    item.setRcvProdplanId(attribute1.substring(Constant.INT_0,Constant.INT_6));
                }else {
                    item.setRcvProdplanId(attribute1);
                }
            }
        });
        return resultList;
    }

    /**
     *<AUTHOR>
     * 校验条码是否录入维修次小类为报废的条码
     *@Date 2022/12/16 15:38
     *@Param [java.lang.String]
     *@return
     **/
    public void snScrap(List<String> snList) throws Exception {
        List<PmRepairDetailDTO> repairDetailDTOList = pmRepairDetailService.getRepairDetailBySnList(snList);
        if (CollectionUtils.isEmpty(repairDetailDTOList)) {
            return;
        }
        for (PmRepairDetailDTO pmRepairDetailDTO : repairDetailDTOList) {
            if (Constant.REPAIR_PROUCT_MS_TYPE_SCRAP.equals(pmRepairDetailDTO.getRepairProductMstype())) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SN_EXIST_SCRAP, new String[]{pmRepairDetailDTO.getSn()});
            }
        }
    }

    private void updateSnAndInsertHistory(List<PmRepairRcvVo> list) {
        if(CollectionUtils.isNotEmpty(list)){
            String fromStation = list.get(Constant.INT_0).getFromStation();
            if(!Constant.REPAIR_FROM_STATION.equals(fromStation) && !Constant.DEVICE_VENEER_MAINTENANCE.equals(fromStation)
                && !Constant.DEVICE_PRODUCTION_MAINTENANCE.equals(fromStation)){
                return;
            }
        }
        // 获取dip下限扫描的  getProcessCode
        Map<String, String> processMap = pmRepairRcvServiceRecode.getProcessMap();
        List<PsWipInfo> psWipInfos = new ArrayList<>();
        List<PsScanHistory> psScanHistory = new ArrayList<>();
        for (PmRepairRcvVo pmRepairRcvVo : list) {
            PsWipInfo newPsWipInfo = new PsWipInfo();
            newPsWipInfo.setSn(pmRepairRcvVo.getSn());
            newPsWipInfo.setCurrProcessCode(Constant.REPAIR_ING);
            newPsWipInfo.setCraftSection(StringUtils.isBlank(processMap.get(Constant.CRAFT_SECTION_STR)) ? Constant.CRAFTSECTION_REPAIR : processMap.get(Constant.CRAFT_SECTION_STR));
            newPsWipInfo.setLastUpdatedBy(pmRepairRcvVo.getLastUpdatedBy());
            psWipInfos.add(newPsWipInfo);
            PsWipInfo oldWipInfo = psWipInfoService.getWipInfoBySn(pmRepairRcvVo.getSn());
            pmRepairRcvServiceRecode.addPsWipScanHistory(oldWipInfo, psScanHistory, newPsWipInfo);
        }
        //批量修改条码主、子工序
        this.updateBatch(psWipInfos);
        // 批量写入扫描历史
        scanHistoryServiceImpl.insertPsScanHistoryBatch(psScanHistory,NumConstant.NUM_100);
    }
}
