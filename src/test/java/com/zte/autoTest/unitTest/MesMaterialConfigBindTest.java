package com.zte.autoTest.unitTest;


import com.zte.application.datawb.BomConfigCheckEnService;
import com.zte.application.datawb.ConfigBindInfoQueryService;
import com.zte.application.datawb.MaterialConfigBindService;
import com.zte.application.datawb.MesMaterialConfigBindServiceReplace;
import com.zte.application.datawb.impl.BomConfigBindInfoImpl;
import com.zte.application.datawb.impl.MesMaterialConfigBindServiceImpl;
import com.zte.application.datawb.impl.MesUpdateWholeSiteServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.utils.Constant;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.datawb.MaterialConfigBindRepository;
import com.zte.domain.model.datawb.MesMaterialConfigBindRepository;
import com.zte.domain.model.datawb.OemProduceSkipGoodsQueryRepository;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.interfaces.dto.*;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.httpclient.HttpClientUtil;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

import static org.mockito.Matchers.*;
import static org.powermock.api.mockito.PowerMockito.when;

@RunWith(PowerMockRunner.class)

@PrepareForTest({CenterfactoryRemoteService.class, MESHttpHelper.class, HttpClientUtil.class,
        CommonUtils.class})
public class MesMaterialConfigBindTest extends BaseTestCase {
    @InjectMocks
    MesMaterialConfigBindServiceImpl mesMaterialConfigBindService;
    @InjectMocks
    MesUpdateWholeSiteServiceImpl mesUpdateWholeSiteServiceImpl;
    @Mock
    OemProduceSkipGoodsQueryRepository oemProduceSkipGoodsQuery;
    @Mock
    MesMaterialConfigBindRepository mesMaterialConfigBindRepository;

    @Mock
    MaterialConfigBindRepository materialConfigBindRepository;
    @Mock
    CenterfactoryRemoteService centerfactoryRemoteService;
    @Mock
    BomConfigCheckEnService bomConfigCheckEnService;
    @Mock
    BomConfigBindInfoImpl bomConfigBindInfoService;
    @Mock
    MaterialConfigBindService materialConfigBindService;
    @Mock
    ConfigBindInfoQueryService configBindInfoQueryService;
    @Mock
    MesMaterialConfigBindServiceReplace mesMaterialConfigBindServiceReplaceXdt;

    @Before
    public void init() {
        PowerMockito.mockStatic(CenterfactoryRemoteService.class);
        PowerMockito.mockStatic(CommonUtils.class);
    }

    @Test
    public void maincheckin() throws Exception {
        try {
            PowerMockito.mockStatic(CommonUtils.class);

            Map mapcc = new HashMap();
            mapcc.put("entityId","9706327");
            mapcc.put("configDetailId","1152021604");
            mapcc.put("itemBarcode","T3279004959");
            materialConfigBindService.isMainBarcodeScaned(mapcc);

            mapcc = new HashMap();
            mapcc.put("entityId","97063271111");
            mapcc.put("configDetailId","1152021604");
            mapcc.put("itemBarcode","T3279004959");
            materialConfigBindService.isMainBarcodeScaned(mapcc);

            mesMaterialConfigBindService.validateToken("eCommerce300","EE2DC8FD77642E0CE0535896360A7F1B");
            MaterialConfigBindInDTO params1 = new MaterialConfigBindInDTO();
            params1.setEntityName("MSG-M20221200001");
            params1.setMainBarcode("219994102213");
            params1.setMainBarcodeCenterFlag("Y");
            params1.setMainConfigDetailID("1274926389");
            params1.setMainItemCode("112012");
            params1.setMfgSiteId("209331518");
            params1.setNoLinkedEntityName("N");
            params1.setMainBarcodeBigCategory("SN_CODE");
            params1.setOperationName("包装");
            params1.setOrgID("635");
            params1.setSubBarcode("219994102215");
            params1.setSubBarcodeBigCategory("");
            params1.setSubBarcodeCenterFlag("N");
            params1.setSubBarcodeSourceBatchCode("");
            params1.setSubConfigDetailID("1274926390");
            params1.setSubItemCode("");
            params1.setSubQty(2);
            when(mesMaterialConfigBindRepository.qryBarcodeType(any())).thenReturn("序列码");
            List<ConfigLevel> configLevelList = new ArrayList<>();
            ConfigLevel cccc = new ConfigLevel();
            cccc.setLevelNodt("123");
            cccc.setEntityId("33332");
            configLevelList.add(cccc);
            when(materialConfigBindService.getConfigDetailInfoListBOQBOM(any())).thenReturn(configLevelList);
            mesMaterialConfigBindService.mesMaterialConfigBind(params1, "eCommerce300");


            when(CommonUtils.getLmbMessage(anyString())).thenReturn("hi");
            MaterialConfigBindInDTO params = new MaterialConfigBindInDTO();
            //mesMaterialConfigBindService.mesMaterialConfigBind();
            //String uuid = "123456";
            Map map = new HashMap();
            StringBuilder mainCodeInfo = new StringBuilder();
            //(MaterialConfigBindInDTO params, Map map, StringBuilder mainCodeInfo, java.lang.String empNo)
            mesMaterialConfigBindService.paramCheckMain(null, map, mainCodeInfo, "empNo");


            params.setEntityName("123");
            mesMaterialConfigBindService.paramCheckMain(params, map, mainCodeInfo, "empNo");


            params.setMainBarcode("12");

            params.setMainBarcodeCenterFlag("Y");
            params.setMainConfigDetailID("123");
            params.setMainItemCode("123");
            //params.setMfgSiteId("123");
            params.setNoLinkedEntityName("N");
            params.setOperationName("调试");
            params.setOrgID("635");
            params.setSubBarcode("123");
            params.setSubBarcodeBigCategory("SN_CODE");
            params.setSubBarcodeCenterFlag("Y");
            params.setSubBarcodeSourceBatchCode("220");
            params.setSubConfigDetailID("12");
            params.setSubItemCode("123");
            params.setSubQty(1);
            mesMaterialConfigBindService.paramCheckMain(params, map, mainCodeInfo, "empNo");


            params.setMfgSiteId("123");
            mesMaterialConfigBindService.paramCheckMain(params, map, mainCodeInfo, "empNo");

            params.setMainBarcodeBigCategory("123");
            mesMaterialConfigBindService.paramCheckMain(params, map, mainCodeInfo, "empNo");

            params.setMainBarcodeBigCategory("SN_CODE");
            mesMaterialConfigBindService.paramCheckMain(params, map, mainCodeInfo, "empNo");


            params.setMainBarcodeCenterFlag("N");
            params.setMainBarcodeBigCategory("12SN_CODE");
            mesMaterialConfigBindService.paramCheckMain(params, map, mainCodeInfo, "empNo");


            params.setMainBarcodeCenterFlag("N");
            params.setMainBarcodeBigCategory("SN_CODE");

            mesMaterialConfigBindService.paramCheckMain(params, map, mainCodeInfo, "empNo");


        } catch (Exception e) {
            Assert.assertEquals(null, e.getMessage());
        }

    }

    @Test
    public void maincheckin2() throws Exception {
        try {
            PowerMockito.mockStatic(CommonUtils.class);
            when(CommonUtils.getLmbMessage(anyString())).thenReturn("hi");
            MaterialConfigBindInDTO params = new MaterialConfigBindInDTO();
            params.setEntityName("123");
            params.setMainBarcode("12");

            params.setMainBarcodeCenterFlag("Y");
            params.setMainConfigDetailID("123");
            params.setMainItemCode("123");
            //params.setMfgSiteId("123");
            params.setNoLinkedEntityName("N");
            params.setOperationName("调试");
            params.setOrgID("635");
            params.setSubBarcode("123");
            params.setSubBarcodeBigCategory("SN_CODE");
            params.setSubBarcodeCenterFlag("Y");
            params.setSubBarcodeSourceBatchCode("220");
            params.setSubConfigDetailID("12");
            params.setSubItemCode("123");
            params.setSubQty(1);
            params.setMfgSiteId("123");
            params.setMainBarcodeBigCategory("123");
            params.setMainBarcodeCenterFlag("N");
            params.setMainBarcodeBigCategory("SN_CODE");
            mesMaterialConfigBindService.getBarcodeInfo(params, 1);

            Map map =new HashMap();
            PowerMockito.when(materialConfigBindRepository.isMainBarcodeScaned(any())).thenReturn(1);
            boolean result = materialConfigBindService.isMainBarcodeScaned(map);
            PowerMockito.when(materialConfigBindRepository.isMainBarcodeScaned(any())).thenReturn(0);
            result = materialConfigBindService.isMainBarcodeScaned(map);
        } catch (Exception e) {
            Assert.assertEquals(null, e.getMessage());
        }
    }


    @Test
    public void maincheckin3() throws Exception {
        try {
            PowerMockito.mockStatic(CommonUtils.class);
            when(CommonUtils.getLmbMessage(anyString())).thenReturn("hi");
            MaterialConfigBindInDTO params = new MaterialConfigBindInDTO();
            params.setEntityName("123");
            params.setMainBarcode("12");

            params.setMainBarcodeCenterFlag("Y");
            params.setMainConfigDetailID("123");
            params.setMainItemCode("123");
            //params.setMfgSiteId("123");
            params.setNoLinkedEntityName("N");
            params.setOperationName("调试");
            params.setOrgID("635");
            params.setSubBarcode("123");
            params.setSubBarcodeBigCategory("SN_CODE");
            params.setSubBarcodeCenterFlag("Y");
            params.setSubBarcodeSourceBatchCode("220");
            params.setSubConfigDetailID("12");
            params.setSubItemCode("123");
            params.setSubQty(1);
            params.setMfgSiteId("123");
            params.setMainBarcodeBigCategory("123");
            params.setMainBarcodeCenterFlag("N");
            params.setMainBarcodeBigCategory("SN_CODE");


            List<String> ll = new ArrayList();
            Map<Integer, List<java.lang.String>> m = new HashMap<>();


            PowerMockito.when(bomConfigBindInfoService.getDtDataWeb(any())).thenReturn(m);
            List<BomConfigCheckEnDTO> resultTv = new ArrayList<>();
            BomConfigCheckEnDTO b = new BomConfigCheckEnDTO();
            PowerMockito.when(bomConfigBindInfoService.setResultTv(any())).thenReturn(resultTv);
            mesMaterialConfigBindService.getReturnBarcode(params, 1);


            PowerMockito.when(bomConfigBindInfoService.getDtDataWeb(any())).thenReturn(m);
            List<BomConfigCheckEnDTO> resultTv2 = new ArrayList<>();
            BomConfigCheckEnDTO b2 = new BomConfigCheckEnDTO();
            b2.setItemCode("123");
            b2.setItemId(12L);
            resultTv2.add(b2);
            PowerMockito.when(bomConfigBindInfoService.setResultTv(any())).thenReturn(resultTv2);
            PowerMockito.when(bomConfigCheckEnService.callItemId(any())).thenReturn(resultTv2);
            mesMaterialConfigBindService.getReturnBarcode(params, 1);
        } catch (Exception e) {
            Assert.assertEquals(null, e.getMessage());
        }
    }


    @Test
    public void maincheckin4() throws Exception {
        try {
            MaterialConfigBindInDTO remember = new MaterialConfigBindInDTO();
            remember.setMainBarcode("123");
            int mark = 1;
            mesMaterialConfigBindService.setParamMapBarcode(remember, mark);

            mark = 0;
            remember.setSubBarcode("123");
            mesMaterialConfigBindService.setParamMapBarcode(remember, mark);

        } catch (Exception e) {
            Assert.assertNotNull(e.getMessage(),e.getMessage());
        }
    }


    @Test
    public void maincheckin5() throws Exception {
        try {
            MaterialConfigBindInDTO params = new MaterialConfigBindInDTO();
            params.setEntityName("123");
            params.setMainBarcode("12");

            params.setMainBarcodeCenterFlag("Y");
            params.setMainConfigDetailID("123");
            params.setMainItemCode("123");
            //params.setMfgSiteId("123");
            params.setNoLinkedEntityName("N");
            params.setOperationName("调试");
            params.setOrgID("635");
            params.setSubBarcode("123");
            params.setSubBarcodeBigCategory("SN_CODE");
            params.setSubBarcodeCenterFlag("Y");
            params.setSubBarcodeSourceBatchCode("220");
            params.setSubConfigDetailID("12");
            params.setSubItemCode("123");
            params.setSubQty(1);
            params.setMfgSiteId("123");
            params.setMainBarcodeBigCategory("123");
            params.setMainBarcodeCenterFlag("N");
            params.setMainBarcodeBigCategory("SN_CODE");

            mesMaterialConfigBindService.valiateEnviroment(params, "mark", "12");


            mesMaterialConfigBindService.valiateEnviroment(params, "mark", "12");

        } catch (Exception e) {
            Assert.assertEquals(null, e.getMessage());
        }
    }


    @Test
    public void maincheckin7() throws Exception {
        try {
            ScanBoqBomBarcodeRequest req = new ScanBoqBomBarcodeRequest();
            MaterialConfigBindInDTO params = new MaterialConfigBindInDTO();
            params.setEntityName("123");
            params.setMainBarcode("12");

            params.setMainBarcodeCenterFlag("Y");
            params.setMainConfigDetailID("123");
            params.setMainItemCode("123");
            //params.setMfgSiteId("123");
            params.setNoLinkedEntityName("N");
            params.setOperationName("调试");
            params.setOrgID("635");
            params.setSubBarcode("123");
            params.setSubBarcodeBigCategory("SN_CODE");
            params.setSubBarcodeCenterFlag("Y");
            params.setSubBarcodeSourceBatchCode("220");
            params.setSubConfigDetailID("12");
            params.setSubItemCode("123");
            params.setSubQty(1);
            params.setMfgSiteId("123");
            params.setMainBarcodeBigCategory("123");
            params.setMainBarcodeCenterFlag("N");
            params.setMainBarcodeBigCategory("SN_CODE");

            Map map = new HashMap();
            PowerMockito.when(materialConfigBindService.getConfigDetailInfoListBOQBOM(any())).thenReturn(null);
            mesMaterialConfigBindService.setMainBarcodeInfo(params, req, map);

            List<ConfigLevel> configLevelList = new ArrayList<>();
            ConfigLevel c = new ConfigLevel();
            c.setLevelNodt("123.12");
            configLevelList.add(c);
            PowerMockito.when(materialConfigBindService.getConfigDetailInfoListBOQBOM(any())).thenReturn(configLevelList);
            mesMaterialConfigBindService.setMainBarcodeInfo(params, req, map);


            params.setNoLinkedEntityName("N");
            c.setLevelNodt("123.12.12");
            configLevelList.add(c);
            PowerMockito.when(materialConfigBindService.getConfigDetailInfoListBOQBOM(any())).thenReturn(configLevelList);
            mesMaterialConfigBindService.setMainBarcodeInfo(params, req, map);

            c.setLevelNodt("1");
            PowerMockito.when(materialConfigBindService.getConfigDetailInfoListBOQBOM(anyMap())).thenReturn(configLevelList);
            mesMaterialConfigBindService.setMainBarcodeInfo(params, req, map);

            BasBarcodeInfo b = new BasBarcodeInfo();
            map.put("listBarcodeInfo", b);
            c.setEntityId("12333");
            PowerMockito.when(materialConfigBindService.getConfigDetailInfoListBOQBOM(any())).thenReturn(configLevelList);
            mesMaterialConfigBindService.setMainBarcodeInfo(params, req, map);

        } catch (Exception e) {
            Assert.assertNull(e.getMessage());
        }
    }

    @Test
    public void maincheckin712() throws Exception {
        try {
            ScanBoqBomBarcodeRequest req = new ScanBoqBomBarcodeRequest();
            MaterialConfigBindInDTO params = new MaterialConfigBindInDTO();
            params.setEntityName("123");
            params.setMainBarcode("12");

            params.setMainBarcodeCenterFlag("Y");
            params.setMainConfigDetailID("123");
            params.setMainItemCode("123");
            //params.setMfgSiteId("123");
            params.setNoLinkedEntityName("N");
            params.setOperationName("调试");
            params.setOrgID("635");
            params.setSubBarcode("123");
            params.setSubBarcodeBigCategory("SN_CODE");
            params.setSubBarcodeCenterFlag("Y");
            params.setSubBarcodeSourceBatchCode("220");
            params.setSubConfigDetailID("12");
            params.setSubItemCode("123");
            params.setSubQty(1);
            params.setMfgSiteId("123");
            params.setMainBarcodeBigCategory("123");
            params.setMainBarcodeCenterFlag("N");
            params.setMainBarcodeBigCategory("SN_CODE");

            Map map = new HashMap();
            PowerMockito.when(materialConfigBindService.getConfigDetailInfoListBOQBOM(any())).thenReturn(null);
            params.setNoLinkedEntityName("Y");
            mesMaterialConfigBindService.setMainBarcodeInfo(params, req, map);

            List<ConfigLevel> configLevelList = new ArrayList<>();
            ConfigLevel c = new ConfigLevel();
            c.setLevelNodt("123.12");
            configLevelList.add(c);
            PowerMockito.when(materialConfigBindService.getConfigDetailInfoListBOQBOM(any())).thenReturn(configLevelList);
            mesMaterialConfigBindService.setMainBarcodeInfo(params, req, map);


            params.setNoLinkedEntityName("N");
            c.setLevelNodt("123.12.12");
            configLevelList.add(c);
            PowerMockito.when(materialConfigBindService.getConfigDetailInfoListBOQBOM(any())).thenReturn(configLevelList);
            mesMaterialConfigBindService.setMainBarcodeInfo(params, req, map);

            BasBarcodeInfo b = new BasBarcodeInfo();
            map.put("listBarcodeInfo", b);
            c.setEntityId("12333");
            PowerMockito.when(materialConfigBindService.getConfigDetailInfoListBOQBOM(any())).thenReturn(configLevelList);
            mesMaterialConfigBindService.setMainBarcodeInfo(params, req, map);

        } catch (Exception e) {
            Assert.assertEquals(null, e.getMessage());
        }
    }

    @Test
    public void maincheckin71() throws Exception {
        try {
            ScanBoqBomBarcodeRequest req = new ScanBoqBomBarcodeRequest();
            MaterialConfigBindInDTO params = new MaterialConfigBindInDTO();
            params.setEntityName("123");
            params.setMainBarcode("12");
            params.setMainBarcodeCenterFlag("Y");
            params.setMainConfigDetailID("123");
            params.setMainItemCode("123");
            //params.setMfgSiteId("123");
            params.setNoLinkedEntityName("N");
            params.setOperationName("调试");
            params.setOrgID("635");
            params.setSubBarcode("123");
            params.setSubBarcodeBigCategory("SN_CODE");
            params.setSubBarcodeCenterFlag("Y");
            params.setSubBarcodeSourceBatchCode("220");
            params.setSubConfigDetailID("12");
            params.setSubItemCode("123");
            params.setSubQty(1);
            params.setMfgSiteId("123");
            params.setMainBarcodeBigCategory("123");
            params.setMainBarcodeCenterFlag("N");
            params.setMainBarcodeBigCategory("SN_CODE");
            Map map = new HashMap();
            PowerMockito.when(materialConfigBindService.getConfigDetailInfoListBOQBOM(anyMap())).thenReturn(null);
            mesMaterialConfigBindService.setSubBarcodeInfo(params, req, map);
            List<ConfigLevel> configLevelList = new ArrayList<>();
            ConfigLevel c = new ConfigLevel();
            c.setLevelNodt("123.12");
            configLevelList.add(c);
            PowerMockito.when(materialConfigBindService.getConfigDetailInfoListBOQBOM(anyMap())).thenReturn(configLevelList);
            mesMaterialConfigBindService.setSubBarcodeInfo(params, req, map);
            params.setNoLinkedEntityName("N");
            c.setLevelNodt("123.12.12");
            //configLevelList.add(ct);
            PowerMockito.when(materialConfigBindService.getConfigDetailInfoListBOQBOM(anyMap())).thenReturn(configLevelList);
            mesMaterialConfigBindService.setSubBarcodeInfo(params, req, map);
            c.setLevelNodt("1");
            PowerMockito.when(materialConfigBindService.getConfigDetailInfoListBOQBOM(anyMap())).thenReturn(configLevelList);
            mesMaterialConfigBindService.setSubBarcodeInfo(params, req, map);
            BasBarcodeInfo b = new BasBarcodeInfo();
            map.put("listBarcodeInfo", b);
            c.setEntityId("12333");
            PowerMockito.when(materialConfigBindService.getConfigDetailInfoListBOQBOM(anyMap())).thenReturn(configLevelList);
            mesMaterialConfigBindService.setSubBarcodeInfo(params, req, map);
        } catch (Exception e) {
            Assert.assertNull(e.getMessage());
        }
    }


    @Test
    public void maincheckin8() throws Exception {
        try {

            CpmConfigItemAssemble ent = new CpmConfigItemAssemble();
            ent.setRecordId("12");
            PowerMockito.when(materialConfigBindService.getBOQBarCode(any())).thenReturn(null);
            mesMaterialConfigBindService.saveMainBarcode(ent);
            mesMaterialConfigBindService.saveMainBarcode(ent);


            List<SaveMainBarcodeDTO> saveMainBarcodeList = new ArrayList<>();
            SaveMainBarcodeDTO s = new SaveMainBarcodeDTO();
            s.setNodeCount(2);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Date fDate = sdf.parse("2015-12-31");
            s.setLastUpdate(fDate);
            saveMainBarcodeList.add(s);
            PowerMockito.when(materialConfigBindService.getBOQBarCode(any())).thenReturn(saveMainBarcodeList);
            mesMaterialConfigBindService.saveMainBarcode(ent);
            mesMaterialConfigBindService.saveMainBarcode(ent);
        } catch (Exception e) {
            Assert.assertEquals(null, e.getMessage());
        }
    }


    @Test
    public void maincheckin9() throws Exception {
        try {
            Map<java.lang.String, Object> inMap = new HashMap<>();
            inMap.put("itemBarcode", "123");
            inMap.put("subBarcodeType", "BATCw3rH_CODE");
            List<MesConfigMaterialDTO> outList = new ArrayList<>();
            MesConfigMaterialDTO entryMes = new MesConfigMaterialDTO();
            entryMes.setIsSn(new Integer(1));
            outList.add(entryMes);
            inMap.put(Constant.RESULT_CURSOR, outList);
            PowerMockito.when(mesMaterialConfigBindRepository.getStepItemBar(inMap));
            mesMaterialConfigBindService.getStepItemBar(inMap);
            inMap.put("subBarcodeType", "批次码");
            inMap.put("tempSubBarcode", "123");
            mesMaterialConfigBindService.getStepItemBar(inMap);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), e.getMessage());
        }
    }


    @Test
    public void maincheckin91() throws Exception {
        try {

            MaterialConfigBindInDTO params = new MaterialConfigBindInDTO();
            params.setEntityName("123");
            params.setMainBarcode("12");

            params.setMainBarcodeCenterFlag("Y");
            params.setMainConfigDetailID("123");
            params.setMainItemCode("123");
            //params.setMfgSiteId("123");
            params.setNoLinkedEntityName("N");
            params.setOperationName("调试");
            params.setOrgID("635");
            params.setSubBarcode("123");
            params.setSubBarcodeBigCategory("SN_CODE");
            params.setSubBarcodeCenterFlag("Y");
            params.setSubBarcodeSourceBatchCode("220");
            params.setSubConfigDetailID("12");
            params.setSubItemCode("123");
            params.setSubQty(1);
            params.setMfgSiteId("123");
            params.setMainBarcodeBigCategory("123");
            params.setMainBarcodeCenterFlag("N");
            params.setMainBarcodeBigCategory("SN_CODE");
            mesMaterialConfigBindService.setmBoqBomBindInfoList(params);

            params.setNoLinkedEntityName("Y");
            mesMaterialConfigBindService.setmBoqBomBindInfoList(params);

        } catch (Exception e) {
            Assert.assertEquals(null, e.getMessage());
        }
    }


    @Test
    public void maincheckin92() throws Exception {
        try {

            MaterialConfigBindInDTO params = new MaterialConfigBindInDTO();
            params.setEntityName("123");
            params.setMainBarcode("12");

            params.setMainBarcodeCenterFlag("Y");
            params.setMainConfigDetailID("123");
            params.setMainItemCode("123");
            //params.setMfgSiteId("123");
            params.setNoLinkedEntityName("N");
            params.setOperationName("调试");
            params.setOrgID("635");
            params.setSubBarcode("123");
            params.setSubBarcodeBigCategory("SN_CODE");
            params.setSubBarcodeCenterFlag("Y");
            params.setSubBarcodeSourceBatchCode("220");
            params.setSubConfigDetailID("12");
            params.setSubItemCode("123");
            params.setSubQty(1);
            params.setMfgSiteId("123");
            params.setMainBarcodeBigCategory("123");
            params.setMainBarcodeCenterFlag("N");
            params.setMainBarcodeBigCategory("SN_CODE");

            params.setNoLinkedEntityName("Y");
            mesMaterialConfigBindService.setmBoqBomBindInfoList(params);

        } catch (Exception e) {
            Assert.assertEquals(null, e.getMessage());
        }
    }


    @Test
    public void maincheckin923() throws Exception {
        try {

            MaterialConfigBindInDTO params = new MaterialConfigBindInDTO();
            params.setEntityName("123");
            params.setMainBarcode("12");

            params.setMainBarcodeCenterFlag("Y");
            params.setMainConfigDetailID("123");
            params.setMainItemCode("123");
            //params.setMfgSiteId("123");
            params.setNoLinkedEntityName("N");
            params.setOperationName("调试");
            params.setOrgID("635");
            params.setSubBarcode("123");
            params.setSubBarcodeBigCategory("SN_CODE");
            params.setSubBarcodeCenterFlag("Y");
            params.setSubBarcodeSourceBatchCode("220");
            params.setSubConfigDetailID("12");
            params.setSubItemCode("123");
            params.setSubQty(1);
            params.setMfgSiteId("123");
            params.setMainBarcodeBigCategory("123");
            params.setMainBarcodeCenterFlag("N");
            params.setMainBarcodeBigCategory("SN_CODE");

            params.setNoLinkedEntityName("Y");
            CpmConfigItemAssemble mainCpmConfigItemAssemble = new CpmConfigItemAssemble();
            CpmConfigItemAssemble ent = new CpmConfigItemAssemble();
            mainCpmConfigItemAssemble.setRecordId("0");
            mesMaterialConfigBindService.saveSubBarcode(ent, params, mainCpmConfigItemAssemble);
            mesMaterialConfigBindService.saveSubBarcode(ent, params, mainCpmConfigItemAssemble);
            mainCpmConfigItemAssemble.setRecordId("10");
            params.setSubBarcodeBigCategory("REEL_MP_CODE");
            params.setSubBarcode("123");
            mesMaterialConfigBindService.saveSubBarcode(ent, params, mainCpmConfigItemAssemble);
            mesMaterialConfigBindService.saveSubBarcode(ent, params, mainCpmConfigItemAssemble);

        } catch (Exception e) {
            Assert.assertEquals(null, e.getMessage());
        }
    }


    //总入口，2个入参校验，打log，头过滤，constant修改
    @Test
    public void maincheckin93() throws Exception {
        try {

            MaterialConfigBindInDTO params = new MaterialConfigBindInDTO();
            params.setEntityName("123");
            params.setMainBarcode("12");

            params.setMainBarcodeCenterFlag("Y");
            params.setMainConfigDetailID("123");
            params.setMainItemCode("123");
            //params.setMfgSiteId("123");
            params.setNoLinkedEntityName("N");
            params.setOperationName("调试");
            params.setOrgID("635");
            params.setSubBarcode("123");
            params.setSubBarcodeBigCategory("SN_CODE");
            params.setSubBarcodeCenterFlag("Y");
            params.setSubBarcodeSourceBatchCode("220");
            params.setSubConfigDetailID("12");
            params.setSubItemCode("123");
            params.setSubQty(1);
            params.setMfgSiteId("123");
            params.setMainBarcodeBigCategory("123");
            params.setMainBarcodeCenterFlag("N");
            params.setMainBarcodeBigCategory("SN_CODE");

            params.setNoLinkedEntityName("Y");
            CpmConfigItemAssemble mainCpmConfigItemAssemble = new CpmConfigItemAssemble();
            CpmConfigItemAssemble ent = new CpmConfigItemAssemble();
            mainCpmConfigItemAssemble.setRecordId("0");


            mesMaterialConfigBindService.mesMaterialConfigBind(null, "123");

            params.setNoLinkedEntityName("Y");

            PowerMockito.when(mesMaterialConfigBindRepository.qryBarcodeType(any())).thenReturn("序列码");
            mesMaterialConfigBindService.mesMaterialConfigBind(params, "123");

        } catch (Exception e) {
            Assert.assertEquals(null, e.getMessage());
        }
    }


    @Test
    public void maincheckin95() throws Exception {
        try {
            Map map = new HashMap();
            StringBuilder subCodeInfo = new StringBuilder();
            mesMaterialConfigBindService.paramCheckSub(null, map, subCodeInfo, "empNo");

            MaterialConfigBindInDTO p = new MaterialConfigBindInDTO();
            p.setEntityName("123");
            p.setMainBarcode("12");
            mesMaterialConfigBindService.paramCheckSub(p, map, subCodeInfo, "12");


            map.put("mainCpmConfigItemAssemble", "123");
            mesMaterialConfigBindService.paramCheckSub(p, map, subCodeInfo, "123");


            p.setSubBarcodeBigCategory("123");
            p.setSubConfigDetailID("q23");
            mesMaterialConfigBindService.paramCheckSub(p, map, subCodeInfo, "123");

            p.setSubQty(2);
            p.setSubBarcodeCenterFlag("Y");
            mesMaterialConfigBindService.paramCheckSub(p, map, subCodeInfo, "123");


            mesMaterialConfigBindService.paramCheckSub(p, map, subCodeInfo, "123");
            p.setSubItemCode("12");
            p.setSubBarcodeBigCategory("23");

            List<BasBarcodeInfo> dtBas = new ArrayList<>();
            BasBarcodeInfo b = new BasBarcodeInfo();
            dtBas.add(b);
            PowerMockito.when(mesMaterialConfigBindRepository.getCenterItemID(any())).thenReturn(dtBas);
            mesMaterialConfigBindService.paramCheckSub(p, map, subCodeInfo, "123");

            p.setMainBarcode("1");
            p.setSubBarcode("1");
            mesMaterialConfigBindService.paramCheckSub(p, map, subCodeInfo, "123");
            p.setSubBarcode("12");
            mesMaterialConfigBindService.paramCheckSub(p, map, subCodeInfo, "123");


            p.setSubBarcodeBigCategory("REEL_MP_CODE");
            mesMaterialConfigBindService.paramCheckSub(p, map, subCodeInfo, "123");

            p.setSubBarcodeSourceBatchCode("12");
            mesMaterialConfigBindService.paramCheckSub(p, map, subCodeInfo, "123");

            p.setSubBarcodeCenterFlag("N");
            p.setSubBarcode("12");
            PowerMockito.when(mesMaterialConfigBindRepository.qryBarcodeType("12")).thenReturn(null);
            mesMaterialConfigBindService.paramCheckSub(p, map, subCodeInfo, "123");

            PowerMockito.when(mesMaterialConfigBindRepository.qryBarcodeType("12")).thenReturn("12");

            mesMaterialConfigBindService.paramCheckSub(p, map, subCodeInfo, "123");


        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), e.getMessage());
        }
    }


    @Test
    public void maincheckin98() throws Exception {
        try {
            List<SysLookupValues> sysList = new ArrayList<>();
            SysLookupValues sysDto = new SysLookupValues();
            sysDto.setLookupMeaning("1223");
            sysDto.setLookupCode(new BigDecimal("2032006"));
            sysList.add(sysDto);
            PowerMockito.when(CenterfactoryRemoteService.getLookupValue(Constant.LOOKUP_TYPE_2032))
                    .thenReturn(sysList);


            //PowerMockito.when(centerfactoryRemoteService.getLookupValue("12")).thenReturn(null);
            mesMaterialConfigBindService.validateToken("123", "123");


            mesMaterialConfigBindService.validateToken("1", "12");

        } catch (Exception e) {
            Assert.assertEquals(null, e.getMessage());
        }
    }


    @Test
    public void maincheckin61() throws Exception {
        try {

            MaterialConfigBindInDTO params = new MaterialConfigBindInDTO();
            params.setEntityName("123");
            params.setMainBarcode("12");

            params.setMainBarcodeCenterFlag("Y");
            params.setMainConfigDetailID("123");
            params.setMainItemCode("123");
            //params.setMfgSiteId("123");
            params.setNoLinkedEntityName("N");
            params.setOperationName("调试");
            params.setOrgID("635");
            params.setSubBarcode("123");
            params.setSubBarcodeBigCategory("SN_CODE");
            params.setSubBarcodeCenterFlag("Y");
            params.setSubBarcodeSourceBatchCode("220");
            params.setSubConfigDetailID("12");
            params.setSubItemCode("123");
            params.setSubQty(1);
            params.setMfgSiteId("123");
            params.setMainBarcodeBigCategory("123");
            params.setMainBarcodeCenterFlag("N");
            params.setMainBarcodeBigCategory("SN_CODE");

            params.setNoLinkedEntityName("Y");
            CpmConfigItemAssemble mainCpmConfigItemAssemble = new CpmConfigItemAssemble();
            CpmConfigItemAssemble ent = new CpmConfigItemAssemble();
            mainCpmConfigItemAssemble.setRecordId("0");
            StringBuilder mainCodeInfo = new StringBuilder();
            Map map = new HashMap();
            params.setMainBarcodeCenterFlag("N");
            mesMaterialConfigBindService.paramCheckMainInput(params, map, mainCodeInfo, "123");


            params.setMainBarcodeCenterFlag("Y");
            params.setMainItemCode(null);
            mesMaterialConfigBindService.paramCheckMainInput(params, map, mainCodeInfo, "123");

            params.setMainItemCode("123");
            params.setMainBarcodeBigCategory("SN_CODE");
            mesMaterialConfigBindService.paramCheckMainInput(params, map, mainCodeInfo, "123");


            params.setMainItemCode("123");
            params.setMainBarcodeBigCategory("SN_CODE12");
            mesMaterialConfigBindService.paramCheckMainInput(params, map, mainCodeInfo, "123");


        } catch (Exception e) {
            Assert.assertEquals(null, e.getMessage());
        }
    }


    @Test
    public void maincheckin62() throws Exception {
        try {

            MaterialConfigBindInDTO params = new MaterialConfigBindInDTO();
            params.setEntityName("123");
            params.setMainBarcode("12");

            params.setMainBarcodeCenterFlag("Y");
            params.setMainConfigDetailID("123");
            params.setMainItemCode("123");
            //params.setMfgSiteId("123");
            params.setNoLinkedEntityName("N");
            params.setOperationName("调试");
            params.setOrgID("635");
            params.setSubBarcode("123");
            params.setSubBarcodeBigCategory("SN_CODE");
            params.setSubBarcodeCenterFlag("Y");
            params.setSubBarcodeSourceBatchCode("220");
            params.setSubConfigDetailID("12");
            params.setSubItemCode("123");
            params.setSubQty(1);
            params.setMfgSiteId("123");
            params.setMainBarcodeBigCategory("123");
            params.setMainBarcodeCenterFlag("N");
            params.setMainBarcodeBigCategory("SN_CODE");

            params.setNoLinkedEntityName("Y");
            Map map = new HashMap();
            map.put("tempSubBarcode", "12");
            CpmConfigItemAssemble ent = new CpmConfigItemAssemble();
            CpmConfigItemAssemble mainCpmConfigItemAssemble = new CpmConfigItemAssemble();
            //PowerMockito.when(centerfactoryRemoteService.getLookupValue("12")).thenReturn(null);
            mesMaterialConfigBindService.mesMaterialConfigBindSub(params, "1213", ent, mainCpmConfigItemAssemble, map);


        } catch (Exception e) {
            Assert.assertEquals(null, e.getMessage());
        }
    }


    @Test
    public void maincheckin623() throws Exception {
        try {

            MaterialConfigBindInDTO params = new MaterialConfigBindInDTO();
            params.setEntityName("123");
            params.setMainBarcode("12");

            params.setMainBarcodeCenterFlag("Y");
            params.setMainConfigDetailID("123");
            params.setMainItemCode("123");
            //params.setMfgSiteId("123");
            params.setNoLinkedEntityName("N");
            params.setOperationName("调试");
            params.setOrgID("635");
            params.setSubBarcode("123");
            params.setSubBarcodeBigCategory("SN_CODE");
            params.setSubBarcodeCenterFlag("Y");
            params.setSubBarcodeSourceBatchCode("220");
            params.setSubConfigDetailID("12");
            params.setSubItemCode("123");
            params.setSubQty(1);
            params.setMfgSiteId("123");
            params.setMainBarcodeBigCategory("123");
            params.setMainBarcodeCenterFlag("N");
            params.setMainBarcodeBigCategory("SN_CODE");

            params.setNoLinkedEntityName("Y");
            Map map = new HashMap();
            map.put("tempSubBarcode", "12");
            CpmConfigItemAssemble ent = new CpmConfigItemAssemble();
            CpmConfigItemAssemble mainCpmConfigItemAssemble = new CpmConfigItemAssemble();
            //PowerMockito.when(centerfactoryRemoteService.getLookupValue("12")).thenReturn(null);
            StringBuilder mainCodeInfo = new StringBuilder();
            params.setMainBarcode("12");
            params.setMainBarcodeCenterFlag("N");
            params.setOrgID("635");
            params.setNoLinkedEntityName("N");
            params.setMainConfigDetailID(null);
            mesMaterialConfigBindService.paramCheckMain(params, map, mainCodeInfo, "12");


        } catch (Exception e) {
            Assert.assertEquals(null, e.getMessage());
        }
    }


    @Test
    public void maincheckin63() throws Exception {
        try {


            BomConfigCheckEnDTO enDTO = new BomConfigCheckEnDTO();
            enDTO.setItemBarcode("12");
            enDTO.setItemCode("12");
            enDTO.setItemId(12L);
            enDTO.setItemName("12");
            mesMaterialConfigBindService.getBasBarcodeInfo(enDTO);


        } catch (Exception e) {
            Assert.assertEquals(null, e.getMessage());
        }
    }

    @Test
    public void maincheckin65() throws Exception {
        try {


            MaterialConfigBindInDTO params = new MaterialConfigBindInDTO();
            Map map = new HashMap();
            StringBuilder subCodeInfo = new StringBuilder();

            map.put(Constant.MAIN_CPM_CONFIG_ITEM_ASSEMBLE, "12");
            params.setSubBarcodeCenterFlag("Y");

            params.setNoLinkedEntityName("N");
            params.setSubConfigDetailID(null);
            mesMaterialConfigBindService.paramCheckSubInput(params, map, subCodeInfo, "12");


        } catch (Exception e) {
            Assert.assertEquals(null, e.getMessage());
        }
    }


    @Test
    public void maincheckin66() throws Exception {
        try {


            MaterialConfigBindInDTO params = new MaterialConfigBindInDTO();
            Map map = new HashMap();
            StringBuilder subCodeInfo = new StringBuilder();

            map.put(Constant.MAIN_CPM_CONFIG_ITEM_ASSEMBLE, "12");
            params.setSubBarcodeCenterFlag("Y");

            params.setNoLinkedEntityName("N");
            params.setSubConfigDetailID(null);
            ScanBoqBomBarcodeRequest req = new ScanBoqBomBarcodeRequest();


            map.put("tempSubBarcode", "12");
            params.setNoLinkedEntityName("21");
            params.setNoLinkedEntityName("Y");
            map.put(Constant.SUB_BARCODE_TYPE, "12");

            map.put(Constant.SUB_BARCODE_TYPE, "序列码");


            mesMaterialConfigBindService.setSubBarcodeInfo(params, req, map);


        } catch (Exception e) {
            Assert.assertEquals(null, e.getMessage());
        }
    }


    @Test
    public void maincheckin667() throws Exception {
        try {


            MaterialConfigBindInDTO params = new MaterialConfigBindInDTO();
            Map map = new HashMap();
            StringBuilder subCodeInfo = new StringBuilder();

            map.put(Constant.MAIN_CPM_CONFIG_ITEM_ASSEMBLE, "12");
            params.setSubBarcodeCenterFlag("Y");

            params.setNoLinkedEntityName("N");
            params.setSubConfigDetailID(null);
            ScanBoqBomBarcodeRequest req = new ScanBoqBomBarcodeRequest();


            map.put("tempSubBarcode", "12");
            params.setNoLinkedEntityName("21");
            params.setNoLinkedEntityName("Y");
            map.put(Constant.SUB_BARCODE_TYPE, "12");

            map.put(Constant.SUB_BARCODE_TYPE, "序列码");

            params.setMainConfigDetailID("12");
            mesMaterialConfigBindService.setMainBarcodeId(params);

            params.setSubConfigDetailID("12");
            mesMaterialConfigBindService.setSubBarcodeId(params);

        } catch (Exception e) {
            Assert.assertEquals(null, e.getMessage());
        }
    }

    @Test
    public void getWholeFlag() {
        try {
            MaterialConfigBindInDTO params = new MaterialConfigBindInDTO();
            ParamsBarcodeDetail paramsBarcodeDetail = new ParamsBarcodeDetail();
            ScanBoqBomBarcodeRequest scanBoqBomBarcodeRequest = new ScanBoqBomBarcodeRequest();
            params.setSubBarcode("21CADT");
            paramsBarcodeDetail.setLo(new Integer(0));
            paramsBarcodeDetail.setFlagMachine(new Integer(1));

            mesMaterialConfigBindService.getWholeFlag(params, paramsBarcodeDetail, scanBoqBomBarcodeRequest);
            params.setMainBarcode("21CADT");
            paramsBarcodeDetail.setLo(new Integer(1));
            paramsBarcodeDetail.setFlagMachine(new Integer(0));
            mesMaterialConfigBindService.getWholeFlag(params, paramsBarcodeDetail, scanBoqBomBarcodeRequest);
            paramsBarcodeDetail.setLo(new Integer(0));
            params.setSubBarcode("1233434343434");
            List<ConfigLevel> dtoConfig = new ArrayList<>();
            ConfigLevel dtConfig = new ConfigLevel();
            dtConfig.setLevelNodt("12333.456.789");
            dtoConfig.add(dtConfig);
            scanBoqBomBarcodeRequest.setConfigLevelData(dtoConfig);
            scanBoqBomBarcodeRequest.setConfigDetailInfoList(dtoConfig);
            mesMaterialConfigBindService.getWholeFlag(params, paramsBarcodeDetail, scanBoqBomBarcodeRequest);
            paramsBarcodeDetail.setLo(new Integer(1));
            params.setMainBarcode("12333333");
            mesMaterialConfigBindService.getWholeFlag(params, paramsBarcodeDetail, scanBoqBomBarcodeRequest);
            paramsBarcodeDetail.setLo(new Integer(0));
            params.setMainBarcode("12333333");
            paramsBarcodeDetail.setFlagMachine(new Integer(1));
            mesMaterialConfigBindService.getWholeFlag(params, paramsBarcodeDetail, scanBoqBomBarcodeRequest);
            mesMaterialConfigBindService.getMachineFlag(scanBoqBomBarcodeRequest);
            dtConfig.setMachineFlag("Y");
            mesMaterialConfigBindService.getMachineFlag(scanBoqBomBarcodeRequest);
            mesMaterialConfigBindService.sysnThreadUpdate(params, paramsBarcodeDetail);
        } catch (Exception ex) {
            Assert.assertEquals(null, ex.getMessage());
        }
    }

    @Test
    public void updateWholeSite() {
        try{
            MaterialConfigBindInDTO params = new MaterialConfigBindInDTO();
            params.setEntityName("ER2022019708");
            params.setOrgID("635");
            params.setMfgSiteId("456");
            ParamsBarcodeDetail paramsBarcodeDetail = new ParamsBarcodeDetail();
            paramsBarcodeDetail.setLastUpDateBy("10292636");
            paramsBarcodeDetail.setOrganizationID("635");
            PowerMockito.when(mesMaterialConfigBindServiceReplaceXdt.getEmpNoUserID(any())).thenReturn("112323233");
            List<ConfigLevel> configLevelList = new ArrayList<>();
            PowerMockito.when(materialConfigBindService.getConfigDetailInfoListBOQBOM(any())).thenReturn(configLevelList);
            ConfigLevel dtx = new ConfigLevel();
            dtx.setConfigType("必配件");
            dtx.setLevelNodt("1233.456.789.678");
            dtx.setStimulateSumQuality("789");
            dtx.setScanedQuality("789");
            configLevelList.add(dtx);
            mesUpdateWholeSiteServiceImpl.updateWholeSite(params, paramsBarcodeDetail);
            PowerMockito.when(materialConfigBindService.sumComputeWhole(any())).thenReturn(789);
            mesUpdateWholeSiteServiceImpl.updateWholeSite(params, paramsBarcodeDetail);
        } catch (Exception ex){
            Assert.assertEquals(null, ex.getMessage());
        }
    }
}
