/*Started by AICoder, pid:fd333m5afdm4cd4140300a67c004017a6103016b*/
package com.zte.autoTest.unitTest;

import com.zte.application.datawb.WsmAssembleLinesService;
import com.zte.application.datawb.impl.BaiduServiceImpl;
import com.zte.application.datawb.impl.ZmsIndicatorUploadServiceImpl;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.datawb.BaiduRepository;
import com.zte.domain.model.datawb.ZmsIndicatorUploadRepository;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.common.model.RetCode;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.powermock.api.mockito.PowerMockito.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({})
public class BaiduServiceImplTest {

    @Mock
    private WsmAssembleLinesService wsmAssembleLinesService;


    @InjectMocks
    private BaiduServiceImpl baiduServiceImpl;

    @Mock
    private ZmsIndicatorUploadRepository zmsIndicatorUploadRepository;
    @Mock
    private ZmsIndicatorUploadServiceImpl zmsIndicatorUploadService;
    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;
    @Mock
    private BaiduRepository baiduRepository;

    @Test
    public void test_DataEmpty() {
        try {
            List<SysLookupValues> sysLookupValuesList = new ArrayList<>();
            BDProjectDataUploadRequestDTO req = new BDProjectDataUploadRequestDTO();
            req.setTasknos(new ArrayList<>());
            SysLookupValues sysLookupValue1 = new SysLookupValues();
            sysLookupValue1.setLookupCode(new BigDecimal("203010100001"));
            sysLookupValue1.setDescription("北京百度网讯科技有限公司,百度在线网络技术（北京）有限公司,阿波罗智能技术（北京）有限公司,百度云计算技术（保定定兴）有限公司,北京百度智图科技有限公司上海分公司,北京百度智图科技有限公司佛山分公司,百度智能云（成都）科技有限公司,百度云智（上海）信息科技有限公司,百度云计算技术（北京）有限公司,百度时代网络技术（北京）有限公司,百度（中国）有限公司");
            SysLookupValues sysLookupValue2 = new SysLookupValues();
            sysLookupValue2.setLookupCode(new BigDecimal("203010100002"));
            sysLookupValue2.setDescription("-M");
            SysLookupValues sysLookupValue3 = new SysLookupValues();
            sysLookupValue3.setLookupCode(new BigDecimal("203010100003"));
            sysLookupValuesList.add(sysLookupValue1);
            sysLookupValuesList.add(sysLookupValue2);
            sysLookupValuesList.add(sysLookupValue3);
            when(wsmAssembleLinesService.getSysLookupValues(Mockito.anyString())).thenReturn(sysLookupValuesList);

            when(baiduRepository.getProjectDataUpload(req)).thenReturn(null);
            ServiceData ret = baiduServiceImpl.handleProjectDataUpload("11", req);
            Assert.assertEquals(ret.getCode(), RetCode.SUCCESS_CODE);
        } catch (Exception ex) {
            Assert.assertEquals(ex.getMessage(), null);
        }
    }

    @Test
    public void test() throws Exception {
        try {
            List<SysLookupValues> sysLookupValuesList = new ArrayList<>();
            BDProjectDataUploadRequestDTO req = new BDProjectDataUploadRequestDTO();
            req.setTasknos(new ArrayList<>());
            List<BDProjectDataUploadLogDTO> data = new ArrayList<>();
            BDProjectDataUploadLogDTO log = new BDProjectDataUploadLogDTO();
            log.setMoNum("1111-M222");
            log.setMoCreateTime("2024-10-10 10:10:10");
            log.setMoFinishTime("2024-10-10 10:10:10");
            log.setEstimateFinishTime("2024-10-10 10:10:10");
            log.setMoProductionTime("2024-10-10 10:10:10");
            log.setMoStatus("状态1");
            log.setMoInitAmount(1);
            log.setMoFinishAmount(0);
            log.setPoLineNum("111");
            BDProjectDataUploadLogDTO log1 = new BDProjectDataUploadLogDTO();
            log1.setMoNum("1111-M333");
            log1.setMoCreateTime("2024-10-10 10:10:10");
            log1.setMoFinishTime("2024-10-10 10:10:10");
            log1.setEstimateFinishTime("2024-10-10 10:10:10");
            log1.setMoProductionTime("2024-10-10 10:10:10");
            log1.setMoStatus("状态2");
            log1.setMoInitAmount(1);
            log1.setMoFinishAmount(0);
            log1.setPoLineNum("222");
            data.add(log1);

            SysLookupValues sysLookupValue1 = new SysLookupValues();
            sysLookupValue1.setLookupCode(new BigDecimal("203010100001"));
            sysLookupValue1.setDescription("北京百度网讯科技有限公司,百度在线网络技术（北京）有限公司,阿波罗智能技术（北京）有限公司,百度云计算技术（保定定兴）有限公司,北京百度智图科技有限公司上海分公司,北京百度智图科技有限公司佛山分公司,百度智能云（成都）科技有限公司,百度云智（上海）信息科技有限公司,百度云计算技术（北京）有限公司,百度时代网络技术（北京）有限公司,百度（中国）有限公司");
            SysLookupValues sysLookupValue2 = new SysLookupValues();
            sysLookupValue2.setLookupCode(new BigDecimal("203010100002"));
            sysLookupValue2.setDescription("-M");
            SysLookupValues sysLookupValue3 = new SysLookupValues();
            sysLookupValue3.setLookupCode(new BigDecimal("203010100003"));
            sysLookupValue3.setDescription("aa");
            sysLookupValuesList.add(sysLookupValue1);
            sysLookupValuesList.add(sysLookupValue2);
            sysLookupValuesList.add(sysLookupValue3);
            when(wsmAssembleLinesService.getSysLookupValues(Mockito.anyString())).thenReturn(sysLookupValuesList);
            when(baiduRepository.getProjectDataUpload(req)).thenReturn(data);
            doNothing().when(centerfactoryRemoteService).pushDataToB2B(null);
            ServiceData ret = baiduServiceImpl.handleProjectDataUpload("11", req);
            Assert.assertEquals(ret.getCode(), RetCode.SUCCESS_CODE);
        } catch (Exception ex) {
            Assert.assertEquals(ex.getMessage(), null);
        }
    }

    @Test
    public void test_SysLookupValuesEmpty() {
        try {
            BDProjectDataUploadRequestDTO req = new BDProjectDataUploadRequestDTO();
            req.setTasknos(new ArrayList<>());
            when(wsmAssembleLinesService.getSysLookupValues(Mockito.anyString())).thenReturn(null);
            ServiceData ret = baiduServiceImpl.handleProjectDataUpload("11", req);
            Assert.assertEquals(ret.getCode(), RetCode.BUSINESSERROR_CODE);


        } catch (Exception ex) {
            Assert.assertEquals(ex.getMessage(), null);
        }
    }
}
/*Ended by AICoder, pid:fd333m5afdm4cd4140300a67c004017a6103016b*/