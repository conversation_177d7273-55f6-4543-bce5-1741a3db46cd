package com.zte.interfaces;

import com.zte.application.BarcodeLockHeadService;
import com.zte.common.model.MessageId;
import com.zte.domain.model.BarcodeLockHead;
import com.zte.interfaces.dto.BarcodeLockDetailEntityDTO;
import com.zte.interfaces.dto.BarcodeLockHeadEntityDTO;
import com.zte.interfaces.dto.QaKxonlineBomlockedEntityDTO;
import com.zte.itp.msa.core.constant.SysGlobalConst;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.common.annotation.Export;
import com.zte.springbootframe.common.annotation.OpenApi;
import com.zte.springbootframe.common.consts.SysConst;
import com.zte.springbootframe.common.model.FactoryConfig;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.springbootframe.util.RequestHeadValidationUtil;
import com.zte.springbootframe.util.RequestHeaderUtil;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import io.swagger.annotations.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * 条码锁定头表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-03-31 15:43:02
 */
@RestController
@RequestMapping("/barcodelockhead")
public class BarcodeLockHeadController {
    @Autowired
    private BarcodeLockHeadService barcodeLockHeadService;

    @Autowired
    private FactoryConfig factoryConfig;

    @ApiOperation("根据批次获取锁定信息")
    @GetMapping("queryLockInfoByProdPlanId")
    public ServiceData<?> queryLockInfoByProdPlanId(HttpServletRequest request,
                                                         @RequestParam("prodplanId") String prodplanId) {
        RequestHeadValidationUtil.validaFactoryIdAndEmpno(request);
        return ServiceDataBuilderUtil.success(barcodeLockHeadService.queryLockInfoByProdPlanId(prodplanId));
    }
    /**
     * 分页列表
     */
    @ApiOperation("分页列表")
    @OpenApi(name = "条码信息查询-中试专用",describe = "条码信息查询-中试专用",consumer = "中试")
    @ApiResponses({ @ApiResponse(code = 400, message = "请求参数没填好"),
            @ApiResponse(code = 404, message = "请求路径没有或页面跳转路径不对") })
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "id", dataType = "java.lang.String",
                    required = false, value = "主键ID", defaultValue = "String"),
            @ApiImplicitParam(paramType = "query", name = "billNo", dataType = "java.lang.String",
                    required = false, value = "单据号", defaultValue = "String"),
            @ApiImplicitParam(paramType = "query", name = "status", dataType = "java.lang.String",
                    required = false, value = "状态", defaultValue = "String"),
            @ApiImplicitParam(paramType = "query", name = "type", dataType = "java.lang.String",
                    required = false, value = "锁定类型", defaultValue = "String"),
            @ApiImplicitParam(paramType = "query", name = "reason", dataType = "java.lang.String",
                    required = false, value = "锁定原因", defaultValue = "String"),
            @ApiImplicitParam(paramType = "query", name = "remark", dataType = "java.lang.String",
                    required = false, value = "备注", defaultValue = "String"),
            @ApiImplicitParam(paramType = "query", name = "ccList", dataType = "java.lang.String",
                    required = false, value = "抄送人(逗号分隔)", defaultValue = "String"),
            @ApiImplicitParam(paramType = "query", name = "createBy", dataType = "java.lang.String",
                    required = false, value = "创建人", defaultValue = "String"),
            @ApiImplicitParam(paramType = "query", name = "createDate", dataType = "java.lang.String",
                    required = false, value = "创建日期", defaultValue = "Date"),
            @ApiImplicitParam(paramType = "query", name = "lastUpdatedBy", dataType = "java.lang.String",
                    required = false, value = "更新人", defaultValue = "String"),
            @ApiImplicitParam(paramType = "query", name = "lastUpdatedDate", dataType = "java.lang.String",
                    required = false, value = "更新日期", defaultValue = "Date"),
            @ApiImplicitParam(paramType = "query", name = "enabledFlag", dataType = "java.lang.String",
                    required = false, value = "是否有效", defaultValue = "String"),
            @ApiImplicitParam(paramType = "query", name = "orgId", dataType = "java.lang.String",
                    required = false, value = "组织ID", defaultValue = "Integer"),
            @ApiImplicitParam(paramType = "query", name = "factoryId", dataType = "java.lang.String",
                    required = false, value = "工厂ID", defaultValue = "Integer"),
            @ApiImplicitParam(paramType = "query", name = "entityId", dataType = "java.lang.String",
                    required = false, value = "实体ID", defaultValue = "Integer"),
            @ApiImplicitParam(paramType = "query", name = "attribute1", dataType = "java.lang.String",
                    required = false, value = "预留字段1", defaultValue = "String"),
            @ApiImplicitParam(paramType = "query", name = "attribute2", dataType = "java.lang.String",
                    required = false, value = "预留字段2", defaultValue = "String"),
            @ApiImplicitParam(paramType = "query", name = "lineId", dataType = "java.lang.String",
                    required = false, value = "线体主键", defaultValue = ""),
            @ApiImplicitParam(paramType = "query", name = "sort", dataType = "String", required = false,
                    value = "排序字段(值areaId或者areaCode)", defaultValue = ""),
            @ApiImplicitParam(paramType = "query", name = "order", dataType = "String", required = false,
                    value = "排序方式(默认升序,设为desc时降序)", defaultValue = ""),
            @ApiImplicitParam(paramType = "query", name = "page", dataType = "String", required = false,
                    value = "请求的页码", defaultValue = ""),
            @ApiImplicitParam(paramType = "query", name = "rows", dataType = "String", required = false,
                    value = "每页条数", defaultValue = "") })
    @RequestMapping(value = "/pagelist", method = RequestMethod.POST,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ServiceData pageList(HttpServletRequest request,@RequestBody BarcodeLockHeadEntityDTO params) throws Exception{
        ServiceData ret = new ServiceData();
        String factoryId = request.getHeader(SysConst.HTTP_HEADER_X_FACTORY_ID);
        String empNo = request.getHeader(SysGlobalConst.HTTP_HEADER_X_EMP_NO);
        if (StringUtils.isEmpty(factoryId)) {
            ret.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.FACTORY_ID_IS_NULL));
            return ret;
        }
        params.setFactoryId(new Integer(factoryId));

        Page<BarcodeLockHeadEntityDTO> pageRow = barcodeLockHeadService.pageList(params);
        ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        ret.setBo(pageRow);
        return ret;
    }


    /**
     * 信息
     */
    

    /**
     * 保存
     */
    @ApiOperation("保存")
    @ApiResponses({ @ApiResponse(code = 400, message = "请求参数没填好"),
            @ApiResponse(code = 404, message = "请求路径没有或页面跳转路径不对") })
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "id", dataType = "java.lang.String",
                    required = false, value = "主键ID", defaultValue = "String"),
            @ApiImplicitParam(paramType = "query", name = "billNo", dataType = "java.lang.String",
                    required = false, value = "单据号", defaultValue = "String"),
            @ApiImplicitParam(paramType = "query", name = "status", dataType = "java.lang.String",
                    required = false, value = "状态", defaultValue = "String"),
            @ApiImplicitParam(paramType = "query", name = "type", dataType = "java.lang.String",
                    required = false, value = "锁定类型", defaultValue = "String"),
            @ApiImplicitParam(paramType = "query", name = "reason", dataType = "java.lang.String",
                    required = false, value = "锁定原因", defaultValue = "String"),
            @ApiImplicitParam(paramType = "query", name = "remark", dataType = "java.lang.String",
                    required = false, value = "备注", defaultValue = "String"),
            @ApiImplicitParam(paramType = "query", name = "ccList", dataType = "java.lang.String",
                    required = false, value = "抄送人(逗号分隔)", defaultValue = "String"),
            @ApiImplicitParam(paramType = "query", name = "createBy", dataType = "java.lang.String",
                    required = false, value = "创建人", defaultValue = "String"),
            @ApiImplicitParam(paramType = "query", name = "createDate", dataType = "java.lang.String",
                    required = false, value = "创建日期", defaultValue = "Date"),
            @ApiImplicitParam(paramType = "query", name = "lastUpdatedBy", dataType = "java.lang.String",
                    required = false, value = "更新人", defaultValue = "String"),
            @ApiImplicitParam(paramType = "query", name = "lastUpdatedDate", dataType = "java.lang.String",
                    required = false, value = "更新日期", defaultValue = "Date"),
            @ApiImplicitParam(paramType = "query", name = "enabledFlag", dataType = "java.lang.String",
                    required = false, value = "是否有效", defaultValue = "String"),
            @ApiImplicitParam(paramType = "query", name = "orgId", dataType = "java.lang.String",
                    required = false, value = "组织ID", defaultValue = "Integer"),
            @ApiImplicitParam(paramType = "query", name = "factoryId", dataType = "java.lang.String",
                    required = false, value = "工厂ID", defaultValue = "Integer"),
            @ApiImplicitParam(paramType = "query", name = "entityId", dataType = "java.lang.String",
                    required = false, value = "实体ID", defaultValue = "Integer"),
            @ApiImplicitParam(paramType = "query", name = "attribute1", dataType = "java.lang.String",
                    required = false, value = "预留字段1", defaultValue = "String"),
            @ApiImplicitParam(paramType = "query", name = "attribute2", dataType = "java.lang.String",
                    required = false, value = "预留字段2", defaultValue = "String"),
            @ApiImplicitParam(paramType = "query", name = "lineId", dataType = "java.lang.String",
                    required = false, value = "结尾凑数用", defaultValue = "")})
    @RequestMapping(value = "/save", method = RequestMethod.POST,consumes = MediaType.APPLICATION_JSON_VALUE,produces = MediaType.APPLICATION_JSON_VALUE)
    public ServiceData save(HttpServletRequest request, @RequestBody BarcodeLockHeadEntityDTO dto) throws Exception{
        ServiceData ret = new ServiceData();
        String factoryId = request.getHeader(SysConst.HTTP_HEADER_X_FACTORY_ID);
        String empNo = request.getHeader(SysGlobalConst.HTTP_HEADER_X_EMP_NO);
        if (StringUtils.isEmpty(factoryId)) {
            ret.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.FACTORY_ID_IS_NULL));
            return ret;
        }
        String entityId = factoryConfig.getCommonEntityId();
        if(StringUtils.isNotEmpty(entityId)) {
            dto.setEntityId(Integer.parseInt(entityId));
        }
        dto.setFactoryId(new Integer(factoryId));
        dto.setLastUpdatedBy(empNo);
        dto.setCreateBy(empNo);
        BarcodeLockHeadEntityDTO result = barcodeLockHeadService.save(dto);
        ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        ret.setBo(result);
        return ret;
    }
    /**
     * 删除
     */
    @RequestMapping(value = "/delete", method = RequestMethod.POST,consumes = MediaType.APPLICATION_JSON_VALUE,produces = MediaType.APPLICATION_JSON_VALUE)
    public ServiceData delete(HttpServletRequest request, @RequestBody BarcodeLockHeadEntityDTO dto) throws Exception{
        ServiceData ret = new ServiceData();
        String factoryId = request.getHeader(SysConst.HTTP_HEADER_X_FACTORY_ID);
        String empNo = request.getHeader(SysGlobalConst.HTTP_HEADER_X_EMP_NO);
        if (StringUtils.isEmpty(factoryId)) {
            ret.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.FACTORY_ID_IS_NULL));
            return ret;
        }
        dto.setFactoryId(new Integer(factoryId));
        dto.setLastUpdatedBy(empNo);
        int result = barcodeLockHeadService.delete(dto);
        ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        ret.setBo(result);
        return ret;
    }

    @GetMapping("/getHeadByBillNo")
    public ServiceData getHeadByBillNo(@RequestParam String billNo) throws Exception{
        ServiceData ret = new ServiceData();
        ret.setBo(barcodeLockHeadService.getHeadByBillNo(billNo));
        return ret;
    }

    /**
     * 锁定信息导出接口
     *
     * @param response 响应
     * @param headDTO  请求
     * @return
     */
    @Export
    @RequestMapping(value = "/exportExcel", method = RequestMethod.GET,
            produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public void exportExcel(HttpServletRequest request, HttpServletResponse response, BarcodeLockHeadEntityDTO headDTO) throws Exception {
        RequestHeaderUtil.setHeaderFactoryIdAndEmpNo(request, headDTO.getFactoryId()+StringUtils.EMPTY,headDTO.getEmpNo()+StringUtils.EMPTY);
        RequestHeadValidationUtil.validaFactoryId(request);
        barcodeLockHeadService.exportExcel(response, headDTO);
    }


    @RequestMapping(value = "/getLockBillNoAndReasonByProdPlanIdAndProcessCodes", method = RequestMethod.POST,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ServiceData getLockBillNoAndReasonByProdPlanIdAndProcessCodes(HttpServletRequest request,
                                                                         @RequestBody BarcodeLockDetailEntityDTO params) throws Exception{
        RequestHeadValidationUtil.validaFactoryIdAndEmpno(request);
        List<BarcodeLockHead> resultList = barcodeLockHeadService.getLockBillNoAndReasonByProdPlanIdAndProcessCodes(params);
        return ServiceDataBuilderUtil.success(resultList);
    }

    /**
     * 保存SPM锁定数据
     */
    @ApiOperation("保存SPM锁定数据")
    @ApiResponses({ @ApiResponse(code = 400, message = "请求参数没填好"),
            @ApiResponse(code = 404, message = "请求路径没有或页面跳转路径不对") })
    @RequestMapping(value = "/saveSPMLockData", method = RequestMethod.POST,consumes = MediaType.APPLICATION_JSON_VALUE,produces = MediaType.APPLICATION_JSON_VALUE)
    public ServiceData saveSPMLockData(HttpServletRequest request, @RequestBody List<QaKxonlineBomlockedEntityDTO> list) throws Exception{
        ServiceData ret = new ServiceData();
        String factoryId = request.getHeader(SysConst.HTTP_HEADER_X_FACTORY_ID);
        String empNo = request.getHeader(SysGlobalConst.HTTP_HEADER_X_EMP_NO);
        if (StringUtils.isEmpty(factoryId)) {
            ret.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.FACTORY_ID_IS_NULL));
            return ret;
        }
        barcodeLockHeadService.saveSPMLockData(list,factoryId);
        ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        return ret;
    }

    /**
     * 删除SPM锁定数据
     */
    @ApiOperation("删除SPM锁定数据")
    @ApiResponses({ @ApiResponse(code = 400, message = "请求参数没填好"),
            @ApiResponse(code = 404, message = "请求路径没有或页面跳转路径不对") })
    @RequestMapping(value = "/deleteSpmBarcodeLockData", method = RequestMethod.GET,consumes = MediaType.APPLICATION_JSON_VALUE,produces = MediaType.APPLICATION_JSON_VALUE)
    public ServiceData deleteSpmBarcodeLockData(HttpServletRequest request) throws Exception{
        ServiceData ret = new ServiceData();
        String factoryId = request.getHeader(SysConst.HTTP_HEADER_X_FACTORY_ID);
        if (StringUtils.isEmpty(factoryId)) {
            ret.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.FACTORY_ID_IS_NULL));
            return ret;
        }
        barcodeLockHeadService.deleteSpmBarcodeLockData();
        ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        return ret;
    }
}
