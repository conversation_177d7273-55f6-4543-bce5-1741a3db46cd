<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.PsWipInfoRepository">
  <resultMap id="BaseResultMap" type="com.zte.domain.model.PsWipInfo">
    <id column="WIP_ID" jdbcType="VARCHAR" property="wipId" />
    <result column="SN" jdbcType="VARCHAR" property="sn" />
    <result column="IS_PRINT" jdbcType="DECIMAL" property="isPrint" />
    <result column="WORK_ORDER_NO" jdbcType="VARCHAR" property="workOrderNo" />
    <result column="ITEM_NO" jdbcType="VARCHAR" property="itemNo" />
    <result column="ITEM_NAME" jdbcType="VARCHAR" property="itemName" />
    <result column="CURR_PROCESS_CODE" jdbcType="VARCHAR" property="currProcessCode" />
    <result column="STATUS" jdbcType="VARCHAR" property="status" />
    <result column="WORKER" jdbcType="VARCHAR" property="worker" />
    <result column="IN_TIME" jdbcType="TIMESTAMP" property="inTime" />
    <result column="OUT_TIME" jdbcType="TIMESTAMP" property="outTime" />
    <result column="PARENT_SN" jdbcType="VARCHAR" property="parentSn" />
    <result column="ERROR_CODE" jdbcType="VARCHAR" property="errorCode" />
    <result column="LINE_CODE" jdbcType="VARCHAR" property="lineCode" />
    <result column="TIME_SPAN" jdbcType="TIMESTAMP" property="timeSpan" />
    <result column="WORKSHOP_CODE" jdbcType="VARCHAR" property="workshopCode" />
    <result column="CRAFT_SECTION" jdbcType="VARCHAR" property="craftSection" />
    <result column="WORK_STATION" jdbcType="VARCHAR" property="workStation" />
    <result column="OPE_TIMES" jdbcType="DECIMAL" property="opeTimes" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
    <result column="LAST_UPDATED_BY" jdbcType="VARCHAR" property="lastUpdatedBy" />
    <result column="LAST_UPDATED_DATE" jdbcType="TIMESTAMP" property="lastUpdatedDate" />
    <result column="ENABLED_FLAG" jdbcType="VARCHAR" property="enabledFlag" />
    <result column="ORG_ID" jdbcType="DECIMAL" property="orgId" />
    <result column="FACTORY_ID" jdbcType="DECIMAL" property="factoryId" />
    <result column="ENTITY_ID" jdbcType="DECIMAL" property="entityId" />
    <result column="ATTRIBUTE1" jdbcType="VARCHAR" property="attribute1" />
    <result column="ATTRIBUTE2" jdbcType="VARCHAR" property="attribute2" />
    <result column="ATTRIBUTE3" jdbcType="VARCHAR" property="attribute3" />
    <result column="ATTRIBUTE4" jdbcType="VARCHAR" property="attribute4" />
    <result column="ATTRIBUTE5" jdbcType="TIMESTAMP" property="attribute5" />
    <result column="LAST_PROCESS" jdbcType="VARCHAR" property="lastProcess" />
    <result column="SOURCE_SYS" jdbcType="VARCHAR" property="sourceSys" />
    <result column="NEXT_PROCESS" jdbcType="VARCHAR" property="nextProcess" />
    <result column="SOURCE_IMU" jdbcType="DECIMAL" property="sourceImu" />
    <result column="SOURCE_BIMU" jdbcType="DECIMAL" property="sourceBimu" />
    <result column="COL_NAME" jdbcType="VARCHAR" property="colName" />
    <result column="SOURCE_SYS_NAME" jdbcType="VARCHAR" property="sourceSysName" />
    <result column="FIX_ID" jdbcType="VARCHAR" property="fixId" />
    <result column="STATION_NAME" jdbcType="VARCHAR" property="stationName" />
    <result column="DIP_FINISH_FLAG" jdbcType="VARCHAR" property="dipFinishFlag" />
    <result column="ASSEMBLE_FLAG" jdbcType="VARCHAR" property="assembleFlag" />
    <result column="SEC_ASSEMBLE_FLAG" jdbcType="VARCHAR" property="secAssembleFlag" />
  </resultMap>
  <resultMap id="PsWipInfoDTOMap" type="com.zte.interfaces.dto.PsWipInfoDTO">
    <id column="WIP_ID" jdbcType="VARCHAR" property="wipId" />
    <result column="SN" jdbcType="VARCHAR" property="sn" />
    <result column="WORK_ORDER_NO" jdbcType="VARCHAR" property="workOrderNo" />
    <result column="GROUP_QTY" jdbcType="VARCHAR" property="groupQty" />
    <result column="ITEM_NO" jdbcType="VARCHAR" property="itemNo" />
    <result column="ITEM_NAME" jdbcType="VARCHAR" property="itemName" />
    <result column="CURR_PROCESS_CODE" jdbcType="VARCHAR" property="currProcessCode" />
    <result column="STATUS" jdbcType="VARCHAR" property="status" />
    <result column="WORKER" jdbcType="VARCHAR" property="worker" />
    <result column="IN_TIME" jdbcType="TIMESTAMP" property="inTime" />
    <result column="OUT_TIME" jdbcType="TIMESTAMP" property="outTime" />
    <result column="PARENT_SN" jdbcType="VARCHAR" property="parentSn" />
    <result column="ERROR_CODE" jdbcType="VARCHAR" property="errorCode" />
    <result column="LINE_CODE" jdbcType="VARCHAR" property="lineCode" />
    <result column="TIME_SPAN" jdbcType="TIMESTAMP" property="timeSpan" />
    <result column="WORKSHOP_CODE" jdbcType="VARCHAR" property="workshopCode" />
    <result column="CRAFT_SECTION" jdbcType="VARCHAR" property="craftSection" />
    <result column="WORK_STATION" jdbcType="VARCHAR" property="workStation" />
    <result column="OPE_TIMES" jdbcType="DECIMAL" property="opeTimes" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
    <result column="LAST_UPDATED_BY" jdbcType="VARCHAR" property="lastUpdatedBy" />
    <result column="LAST_UPDATED_DATE" jdbcType="TIMESTAMP" property="lastUpdatedDate" />
    <result column="ENABLED_FLAG" jdbcType="VARCHAR" property="enabledFlag" />
    <result column="ORG_ID" jdbcType="DECIMAL" property="orgId" />
    <result column="FACTORY_ID" jdbcType="DECIMAL" property="factoryId" />
    <result column="ENTITY_ID" jdbcType="DECIMAL" property="entityId" />
    <result column="ATTRIBUTE1" jdbcType="VARCHAR" property="attribute1" />
    <result column="ATTRIBUTE2" jdbcType="VARCHAR" property="attribute2" />
    <result column="ATTRIBUTE3" jdbcType="VARCHAR" property="attribute3" />
    <result column="ATTRIBUTE4" jdbcType="VARCHAR" property="attribute4" />
    <result column="ATTRIBUTE5" jdbcType="TIMESTAMP" property="attribute5" />
    <result column="LAST_PROCESS" jdbcType="VARCHAR" property="lastProcess" />
    <result column="SOURCE_SYS" jdbcType="VARCHAR" property="sourceSys" />
    <result column="NEXT_PROCESS" jdbcType="VARCHAR" property="nextProcess" />
    <result column="inCountTotal" jdbcType="VARCHAR" property="inCountTotal" />
    <result column="SOURCE_IMU" jdbcType="DECIMAL" property="sourceImu" />
    <result column="SOURCE_BIMU" jdbcType="DECIMAL" property="sourceBimu" />
    <result column="COL_NAME" jdbcType="VARCHAR" property="colName" />
    <result column="SOURCE_SYS_NAME" jdbcType="VARCHAR" property="sourceSysName" />
    <result column="FIX_ID" jdbcType="VARCHAR" property="fixId" />
    <result column="STATION_NAME" jdbcType="VARCHAR" property="stationName" />
    <result column="ASSEMBLE_FLAG" jdbcType="VARCHAR" property="assembleFlag" />
  </resultMap>

  <resultMap id="BaseResultSimpleMap" type="com.zte.domain.model.PsWipInfoSimpleEntiy">
    <id column="WIP_ID" jdbcType="VARCHAR" property="wipId" />
    <result column="SN" jdbcType="VARCHAR" property="sn" />
    <result column="WORK_ORDER_NO" jdbcType="VARCHAR" property="workOrderNo" />
    <result column="CURR_PROCESS_CODE" jdbcType="VARCHAR" property="currProcessCode" />
    <result column="WORKER" jdbcType="VARCHAR" property="worker" />
    <result column="ITEM_NO" jdbcType="VARCHAR" property="itemNo" />
  </resultMap>
  <resultMap id="BaseTestMap" type="com.zte.domain.model.PsWipInfo">
    <id column="WIP_ID" jdbcType="VARCHAR" property="wipId" />
    <result column="SN" jdbcType="VARCHAR" property="sn" />
    <result column="WORK_ORDER_NO" jdbcType="VARCHAR" property="workOrderNo" />
    <result column="ITEM_NO" jdbcType="VARCHAR" property="itemNo" />
    <result column="ITEM_NAME" jdbcType="VARCHAR" property="itemName" />
    <result column="CURR_PROCESS_CODE" jdbcType="VARCHAR" property="currProcessCode" />
    <result column="STATUS" jdbcType="VARCHAR" property="status" />
    <result column="WORKER" jdbcType="VARCHAR" property="worker" />
    <result column="IN_TIME" jdbcType="TIMESTAMP" property="inTime" />
    <result column="OUT_TIME" jdbcType="TIMESTAMP" property="outTime" />
    <result column="PARENT_SN" jdbcType="VARCHAR" property="parentSn" />
    <result column="ERROR_CODE" jdbcType="VARCHAR" property="errorCode" />
    <result column="LINE_CODE" jdbcType="VARCHAR" property="lineCode" />
    <result column="TIME_SPAN" jdbcType="TIMESTAMP" property="timeSpan" />
    <result column="WORKSHOP_CODE" jdbcType="VARCHAR" property="workshopCode" />
    <result column="CRAFT_SECTION" jdbcType="VARCHAR" property="craftSection" />
    <result column="WORK_STATION" jdbcType="VARCHAR" property="workStation" />
    <result column="OPE_TIMES" jdbcType="DECIMAL" property="opeTimes" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
    <result column="LAST_UPDATED_BY" jdbcType="VARCHAR" property="lastUpdatedBy" />
    <result column="LAST_UPDATED_DATE" jdbcType="TIMESTAMP" property="lastUpdatedDate" />
    <result column="ENABLED_FLAG" jdbcType="VARCHAR" property="enabledFlag" />
    <result column="ORG_ID" jdbcType="DECIMAL" property="orgId" />
    <result column="FACTORY_ID" jdbcType="DECIMAL" property="factoryId" />
    <result column="ENTITY_ID" jdbcType="DECIMAL" property="entityId" />
    <result column="ATTRIBUTE1" jdbcType="VARCHAR" property="attribute1" />
    <result column="ATTRIBUTE2" jdbcType="VARCHAR" property="attribute2" />
    <result column="ATTRIBUTE3" jdbcType="VARCHAR" property="attribute3" />
    <result column="ATTRIBUTE4" jdbcType="VARCHAR" property="attribute4" />
    <result column="ATTRIBUTE5" jdbcType="TIMESTAMP" property="attribute5" />
    <result column="PROCESS_CODE" jdbcType="VARCHAR" property="processCode" />
    <result column="SYMPTOM_NAME" jdbcType="VARCHAR" property="testDescription" />
    <result column="SYMPTOM_CODE" jdbcType="VARCHAR" property="lastTest" />
    <result column="TEST_VALUE" jdbcType="VARCHAR" property="testValue" />
    <result column="FIX_ID" jdbcType="VARCHAR" property="fixId" />
    <result column="STATION_NAME" jdbcType="VARCHAR" property="stationName" />
    <result column="ASSEMBLE_FLAG" jdbcType="VARCHAR" property="assembleFlag" />
  </resultMap>
  <resultMap id="WipInfoFirstMap" type="com.zte.interfaces.dto.WipInfoFirstDTO">
    <result column="CRAFT_SECTION" jdbcType="VARCHAR" property="craftSection" />
    <result column="ITEM_NO" jdbcType="VARCHAR" property="itemNo" />
    <result column="PROD_PLAN_ID" jdbcType="VARCHAR" property="prodPlanId" />
    <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
    <result column="CRAFT_TYPE" jdbcType="VARCHAR" property="type" />
  </resultMap>
  <resultMap id="WipCraftQytMap" type="com.zte.interfaces.dto.WipCraftQtyDTO">
    <result column="CRAFT_SECTION" jdbcType="VARCHAR" property="craftSection" />
    <result column="PROD_PLAN_ID" jdbcType="VARCHAR" property="prodPlanId" />
    <result column="QTY" jdbcType="DECIMAL" property="qty" />
  </resultMap>

  <resultMap id="ResultMap1" type="com.zte.interfaces.dto.BoardInstructionCycleDataCreateDTO">
    <result column="PRODPLAN_ID" jdbcType="VARCHAR" property="prodplanId" />
    <result column="STOCK_85_DATE" jdbcType="TIMESTAMP" property="stock85Date" />
  </resultMap>

  <resultMap id="ConciseDailyQueryMap" type="com.zte.interfaces.dto.PsWipInfoDTO">
    <result column="ATTRIBUTE1" jdbcType="VARCHAR" property="attribute1" />
    <result column="CURR_PROCESS_CODE" jdbcType="VARCHAR" property="currProcessCode" />
    <result column="onhand_qty" jdbcType="NUMERIC" property="onhandQty" />
  </resultMap>

  <resultMap id="ResultMap2" type="com.zte.interfaces.dto.BoardInstructionCycleDataCreateDTO">
    <result column="PRODPLAN_ID" jdbcType="VARCHAR" property="prodplanId" />
    <result column="STOCK_90_DATE" jdbcType="TIMESTAMP" property="partStock90Date" />
  </resultMap>

  <resultMap id="ResultMap3" type="com.zte.interfaces.dto.TaskInstructionStatusDTO">
    <result column="attribute1" jdbcType="VARCHAR" property="prodplanId" />
    <result column="stock_num" jdbcType="DECIMAL" property="stockNum" />
  </resultMap>

  <resultMap id="TestStatMap" type="com.zte.domain.model.BoardTestStatInfo">
    <result column="PRODPLAN_ID" jdbcType="VARCHAR" property="prodplanId" />
    <result column="test_qty" jdbcType="INTEGER" property="testQty" />
  </resultMap>


  <resultMap id="SnQueryResultMap" type="com.zte.interfaces.dto.PsWipInfoSnQueryDTO">
    <id column="WIP_ID" jdbcType="VARCHAR" property="wipId" />
    <result column="SN" jdbcType="VARCHAR" property="sn" />
    <result column="IS_PRINT" jdbcType="DECIMAL" property="isPrint" />
    <result column="WORK_ORDER_NO" jdbcType="VARCHAR" property="workOrderNo" />
    <result column="ITEM_NO" jdbcType="VARCHAR" property="itemNo" />
    <result column="ITEM_NAME" jdbcType="VARCHAR" property="itemName" />
    <result column="CURR_PROCESS_CODE" jdbcType="VARCHAR" property="currProcessCode" />
    <result column="STATUS" jdbcType="VARCHAR" property="status" />
    <result column="WORKER" jdbcType="VARCHAR" property="worker" />
    <result column="IN_TIME" jdbcType="TIMESTAMP" property="inTime" />
    <result column="OUT_TIME" jdbcType="TIMESTAMP" property="outTime" />
    <result column="PARENT_SN" jdbcType="VARCHAR" property="parentSn" />
    <result column="ERROR_CODE" jdbcType="VARCHAR" property="errorCode" />
    <result column="LINE_CODE" jdbcType="VARCHAR" property="lineCode" />
    <result column="TIME_SPAN" jdbcType="TIMESTAMP" property="timeSpan" />
    <result column="WORKSHOP_CODE" jdbcType="VARCHAR" property="workshopCode" />
    <result column="CRAFT_SECTION" jdbcType="VARCHAR" property="craftSection" />
    <result column="WORK_STATION" jdbcType="VARCHAR" property="workStation" />
    <result column="OPE_TIMES" jdbcType="DECIMAL" property="opeTimes" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
    <result column="LAST_UPDATED_BY" jdbcType="VARCHAR" property="lastUpdatedBy" />
    <result column="LAST_UPDATED_DATE" jdbcType="TIMESTAMP" property="lastUpdatedDate" />
    <result column="ENABLED_FLAG" jdbcType="VARCHAR" property="enabledFlag" />
    <result column="ORG_ID" jdbcType="DECIMAL" property="orgId" />
    <result column="FACTORY_ID" jdbcType="DECIMAL" property="factoryId" />
    <result column="ENTITY_ID" jdbcType="DECIMAL" property="entityId" />
    <result column="ATTRIBUTE1" jdbcType="VARCHAR" property="attribute1" />
    <result column="ATTRIBUTE2" jdbcType="VARCHAR" property="attribute2" />
    <result column="ATTRIBUTE3" jdbcType="VARCHAR" property="attribute3" />
    <result column="ATTRIBUTE4" jdbcType="VARCHAR" property="attribute4" />
    <result column="ATTRIBUTE5" jdbcType="TIMESTAMP" property="attribute5" />
    <result column="LAST_PROCESS" jdbcType="VARCHAR" property="lastProcess" />
    <result column="SOURCE_SYS" jdbcType="VARCHAR" property="sourceSys" />
    <result column="NEXT_PROCESS" jdbcType="VARCHAR" property="nextProcess" />
    <result column="SOURCE_IMU" jdbcType="DECIMAL" property="sourceImu" />
    <result column="SOURCE_BIMU" jdbcType="DECIMAL" property="sourceBimu" />
    <result column="COL_NAME" jdbcType="VARCHAR" property="colName" />
    <result column="SOURCE_SYS_NAME" jdbcType="VARCHAR" property="sourceSysName" />
    <result column="FIX_ID" jdbcType="VARCHAR" property="fixId" />
    <result column="STATION_NAME" jdbcType="VARCHAR" property="stationName" />
    <result column="DIP_FINISH_FLAG" jdbcType="VARCHAR" property="dipFinishFlag" />
    <result column="ASSEMBLE_FLAG" jdbcType="VARCHAR" property="assembleFlag" />
    <result column="SEC_ASSEMBLE_FLAG" jdbcType="VARCHAR" property="secAssembleFlag" />
  </resultMap>

  <sql id="Base_Column_List">
    wip_id, sn, work_order_no, item_no, item_name, curr_process_code,status, worker,
    in_time, out_time, parent_sn, error_code, line_code, time_span, workshop_code, ope_times,
    craft_section, work_station, last_process, remark, create_by, create_date, last_updated_by,
    last_updated_date, enabled_flag, org_id, factory_id, entity_id, attribute1, attribute2,
    attribute3, attribute4, attribute5, next_process, source_sys, source_imu, source_bimu, col_name,
    source_sys_name, is_print, fix_id, station_name, print_date, dip_finish_flag, expected_deliver_date,
    deliver_date_remark, deliver_date_feedback_time, original_task, assemble_flag, sec_assemble_flag
  </sql>

  <select id="selectPsWipInfoById" parameterType="com.zte.domain.model.PsWipInfo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from WIP_INFO
    where WIP_ID = #{wipId,jdbcType=VARCHAR}
    and ENABLED_FLAG = 'Y'
  </select>

  <delete id="deletePsWipInfoById" parameterType="com.zte.domain.model.PsWipInfo">
    update WIP_INFO
    set ENABLED_FLAG = 'N'
    where WIP_ID = #{wipId,jdbcType=VARCHAR}
    AND ENABLED_FLAG = 'Y'
  </delete>

  <delete id="deleteWipInfoByWorkOrderNo" parameterType="com.zte.domain.model.PsWipInfo">
    DELETE FROM WIP_INFO
    where 1=1
    AND ENABLED_FLAG = 'Y'
    AND WORK_ORDER_NO = #{workOrderNo,jdbcType=VARCHAR}
  </delete>

  <delete id="deleteWipInfoByParentnSn">
    DELETE FROM WIP_INFO
    where 1=1
    AND ENABLED_FLAG = 'Y'
    AND parent_sn = #{parentSn,jdbcType=VARCHAR}
  </delete>

  <insert id="insertPsWipInfo" parameterType="com.zte.domain.model.PsWipInfo">
    insert into WIP_INFO (WIP_ID, SN, WORK_ORDER_NO,
    ITEM_NO, ITEM_NAME,
    CURR_PROCESS_CODE, STATUS, WORKER,
    IN_TIME, OUT_TIME, PARENT_SN,
    ERROR_CODE, LINE_CODE, TIME_SPAN,
    WORKSHOP_CODE, OPE_TIMES,CRAFT_SECTION,WORK_STATION, REMARK,
    CREATE_BY, CREATE_DATE, LAST_UPDATED_BY,
    LAST_UPDATED_DATE, ENABLED_FLAG, ORG_ID,
    FACTORY_ID, ENTITY_ID, ATTRIBUTE1,
    ATTRIBUTE2, ATTRIBUTE3, ATTRIBUTE4,
    ATTRIBUTE5,LAST_PROCESS,SOURCE_SYS,NEXT_PROCESS
    ,SOURCE_IMU,SOURCE_BIMU,COL_NAME,SOURCE_SYS_NAME,FIX_ID,STATION_NAME)
    values (#{wipId,jdbcType=VARCHAR}, #{sn,jdbcType=VARCHAR}, #{workOrderNo,jdbcType=VARCHAR},
    #{itemNo,jdbcType=VARCHAR}, #{itemName,jdbcType=VARCHAR},
    #{currProcessCode,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, #{worker,jdbcType=VARCHAR},
    #{inTime,jdbcType=TIMESTAMP}, #{outTime,jdbcType=TIMESTAMP}, #{parentSn,jdbcType=VARCHAR},
    #{errorCode,jdbcType=VARCHAR}, #{lineCode,jdbcType=VARCHAR}, #{timeSpan,jdbcType=TIMESTAMP},
    #{workshopCode,jdbcType=VARCHAR}, #{opeTimes,jdbcType=DECIMAL},
    #{craftSection,jdbcType=VARCHAR}, #{workStation,jdbcType=VARCHAR},#{remark,jdbcType=VARCHAR},
    #{createBy,jdbcType=VARCHAR}, sysdate, #{lastUpdatedBy,jdbcType=VARCHAR},
    sysdate, 'Y', #{orgId,jdbcType=DECIMAL},
    #{factoryId,jdbcType=DECIMAL}, #{entityId,jdbcType=DECIMAL}, #{attribute1,jdbcType=VARCHAR},
    #{attribute2,jdbcType=VARCHAR}, #{attribute3,jdbcType=VARCHAR}, #{attribute4,jdbcType=VARCHAR},
    #{attribute5,jdbcType=TIMESTAMP},#{lastProcess,jdbcType=VARCHAR},
    #{sourceSys,jdbcType=VARCHAR},#{nextProcess,jdbcType=VARCHAR},
    #{sourceImu,jdbcType=DECIMAL},#{sourceBimu,jdbcType=DECIMAL},#{colName,jdbcType=VARCHAR},#{sourceSysName,jdbcType=VARCHAR},
    #{fixId,jdbcType=VARCHAR},#{stationName,jdbcType=VARCHAR})
  </insert>


  <insert id="insertPsWipInfoSelective" parameterType="com.zte.domain.model.PsWipInfo">
    insert into WIP_INFO
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="wipId != null">
        WIP_ID,
      </if>

      <if test="sn != null">
        SN,
      </if>

      <if test="workOrderNo != null">
        WORK_ORDER_NO,
      </if>

      <if test="itemNo != null">
        ITEM_NO,
      </if>

      <if test="itemName != null">
        ITEM_NAME,
      </if>

      <if test="currProcessCode != null">
        CURR_PROCESS_CODE,
      </if>

      <if test="status != null">
        STATUS,
      </if>

      <if test="worker != null">
        WORKER,
      </if>

      <if test="inTime != null">
        IN_TIME,
      </if>

      <if test="outTime != null">
        OUT_TIME,
      </if>

      <if test="parentSn != null">
        PARENT_SN,
      </if>

      <if test="errorCode != null">
        ERROR_CODE,
      </if>

      <if test="lineCode != null">
        LINE_CODE,
      </if>

      <if test="timeSpan != null">
        TIME_SPAN,
      </if>

      <if test="workshopCode != null">
        WORKSHOP_CODE,
      </if>

      <if test="opeTimes != null">
        OPE_TIMES,
      </if>

      <if test="craftSection != null">
        CRAFT_SECTION,
      </if>

      <if test="workStation != null">
        WORK_STATION,
      </if>

      <if test="remark != null">
        REMARK,
      </if>

      <if test="createBy != null">
        CREATE_BY,
      </if>

      <if test="lastUpdatedBy != null">
        LAST_UPDATED_BY,
      </if>

      <if test="orgId != null">
        ORG_ID,
      </if>

      <if test="factoryId != null">
        FACTORY_ID,
      </if>

      <if test="entityId != null">
        ENTITY_ID,
      </if>

      <if test="attribute1 != null">
        ATTRIBUTE1,
      </if>

      <if test="attribute2 != null">
        ATTRIBUTE2,
      </if>

      <if test="attribute3 != null">
        ATTRIBUTE3,
      </if>

      <if test="attribute4 != null">
        ATTRIBUTE4,
      </if>

      <if test="attribute5 != null">
        ATTRIBUTE5,
      </if>

      <if test="lastProcess != null">
        LAST_PROCESS,
      </if>

      <if test="sourceSys != null">
        SOURCE_SYS,
      </if>

      <if test="nextProcess != null">
        NEXT_PROCESS,
      </if>

      <if test="sourceImu != null">
        SOURCE_IMU,
      </if>
      <if test="sourceBimu != null">
        SOURCE_BIMU,
      </if>
      <if test="colName != null">
        COL_NAME,
      </if>
      <if test="sourceSysName != null">
        SOURCE_SYS_NAME,
      </if>
      <if test="fixId != null">
        FIX_ID,
      </if>
      <if test="stationName != null">
        STATION_NAME,
      </if>
      ENABLED_FLAG,
      CREATE_DATE,
      LAST_UPDATED_DATE

    </trim>

    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="wipId != null">
        #{wipId,jdbcType=VARCHAR},
      </if>

      <if test="sn != null">
        #{sn,jdbcType=VARCHAR},
      </if>

      <if test="workOrderNo != null">
        #{workOrderNo,jdbcType=VARCHAR},
      </if>

      <if test="itemNo != null">
        #{itemNo,jdbcType=VARCHAR},
      </if>

      <if test="itemName != null">
        #{itemName,jdbcType=VARCHAR},
      </if>


      <if test="currProcessCode != null">
        #{currProcessCode,jdbcType=VARCHAR},
      </if>

      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>

      <if test="worker != null">
        #{worker,jdbcType=VARCHAR},
      </if>

      <if test="inTime != null">
        sysdate,
      </if>

      <if test="outTime != null">
        sysdate,
      </if>

      <if test="parentSn != null">
        #{parentSn,jdbcType=VARCHAR},
      </if>

      <if test="errorCode != null">
        #{errorCode,jdbcType=VARCHAR},
      </if>

      <if test="lineCode != null">
        #{lineCode,jdbcType=VARCHAR},
      </if>

      <if test="timeSpan != null">
        #{timeSpan,jdbcType=TIMESTAMP},
      </if>

      <if test="workshopCode != null">
        #{workshopCode,jdbcType=VARCHAR},
      </if>

      <if test="opeTimes != null">
        #{opeTimes,jdbcType=DECIMAL},
      </if>

      <if test="craftSection != null">
        #{craftSection,jdbcType=VARCHAR},
      </if>

      <if test="workStation != null">
        #{workStation,jdbcType=VARCHAR},
      </if>

      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>

      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>

      <if test="lastUpdatedBy != null">
        #{lastUpdatedBy,jdbcType=VARCHAR},
      </if>

      <if test="orgId != null">
        #{orgId,jdbcType=DECIMAL},
      </if>

      <if test="factoryId != null">
        #{factoryId,jdbcType=DECIMAL},
      </if>

      <if test="entityId != null">
        #{entityId,jdbcType=DECIMAL},
      </if>

      <if test="attribute1 != null">
        #{attribute1,jdbcType=VARCHAR},
      </if>

      <if test="attribute2 != null">
        #{attribute2,jdbcType=VARCHAR},
      </if>

      <if test="attribute3 != null">
        #{attribute3,jdbcType=VARCHAR},
      </if>

      <if test="attribute4 != null">
        #{attribute4,jdbcType=VARCHAR},
      </if>

      <if test="attribute5 != null">
        #{attribute5,jdbcType=TIMESTAMP},
      </if>

      <if test="lastProcess != null">
        #{lastProcess,jdbcType=VARCHAR},
      </if>

      <if test="sourceSys != null">
        #{sourceSys,jdbcType=VARCHAR},
      </if>

      <if test="nextProcess != null">
        #{nextProcess,jdbcType=VARCHAR},
      </if>

      <if test="sourceImu != null">
        #{sourceImu,jdbcType=DECIMAL},
      </if>
      <if test="sourceBimu != null">
        #{sourceBimu,jdbcType=DECIMAL},
      </if>
      <if test="colName != null">
        #{colName,jdbcType=VARCHAR},
      </if>
      <if test="sourceSysName != null">
        #{sourceSysName,jdbcType=VARCHAR},
      </if>
      <if test="fixId != null">
        #{fixId,jdbcType=VARCHAR},
      </if>
      <if test="stationName != null">
        #{stationName,jdbcType=VARCHAR},
      </if>
      'Y',
      sysdate,
      sysdate

    </trim>

  </insert>

  <insert id="insertPsWipInfoSelectiveForTest" parameterType="com.zte.domain.model.PsWipInfo">
    insert into WIP_INFO
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="wipId != null">
        WIP_ID,
      </if>

      <if test="sn != null">
        SN,
      </if>

      <if test="workOrderNo != null">
        WORK_ORDER_NO,
      </if>

      <if test="itemNo != null">
        ITEM_NO,
      </if>

      <if test="itemName != null">
        ITEM_NAME,
      </if>

      <if test="currProcessCode != null">
        CURR_PROCESS_CODE,
      </if>

      <if test="status != null">
        STATUS,
      </if>

      <if test="worker != null">
        WORKER,
      </if>

      <if test="inTime != null">
        IN_TIME,
      </if>

      <if test="outTime != null">
        OUT_TIME,
      </if>

      <if test="parentSn != null">
        PARENT_SN,
      </if>

      <if test="errorCode != null">
        ERROR_CODE,
      </if>

      <if test="lineCode != null">
        LINE_CODE,
      </if>

      <if test="timeSpan != null">
        TIME_SPAN,
      </if>

      <if test="workshopCode != null">
        WORKSHOP_CODE,
      </if>

      <if test="opeTimes != null">
        OPE_TIMES,
      </if>

      <if test="craftSection != null">
        CRAFT_SECTION,
      </if>

      <if test="workStation != null">
        WORK_STATION,
      </if>

      <if test="remark != null">
        REMARK,
      </if>

      <if test="createBy != null">
        CREATE_BY,
      </if>

      <if test="createDate != null">
        CREATE_DATE,
      </if>

      <if test="lastUpdatedBy != null">
        LAST_UPDATED_BY,
      </if>

      <if test="orgId != null">
        ORG_ID,
      </if>

      <if test="factoryId != null">
        FACTORY_ID,
      </if>

      <if test="entityId != null">
        ENTITY_ID,
      </if>

      <if test="attribute1 != null">
        ATTRIBUTE1,
      </if>

      <if test="attribute2 != null">
        ATTRIBUTE2,
      </if>

      <if test="attribute3 != null">
        ATTRIBUTE3,
      </if>

      <if test="attribute4 != null">
        ATTRIBUTE4,
      </if>

      <if test="attribute5 != null">
        ATTRIBUTE5,
      </if>

      <if test="lastProcess != null">
        LAST_PROCESS,
      </if>

      <if test="sourceSys != null">
        SOURCE_SYS,
      </if>

      <if test="nextProcess != null">
        NEXT_PROCESS,
      </if>

      <if test="sourceImu != null">
        SOURCE_IMU,
      </if>
      <if test="sourceBimu != null">
        SOURCE_BIMU,
      </if>
      <if test="colName != null">
        COL_NAME,
      </if>
      <if test="sourceSysName != null">
        SOURCE_SYS_NAME,
      </if>
      <if test="fixId != null">
        FIX_ID,
      </if>
      <if test="stationName != null">
        STATION_NAME,
      </if>
      ENABLED_FLAG,
      LAST_UPDATED_DATE

    </trim>

    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="wipId != null">
        #{wipId,jdbcType=VARCHAR},
      </if>

      <if test="sn != null">
        #{sn,jdbcType=VARCHAR},
      </if>

      <if test="workOrderNo != null">
        #{workOrderNo,jdbcType=VARCHAR},
      </if>

      <if test="itemNo != null">
        #{itemNo,jdbcType=VARCHAR},
      </if>

      <if test="itemName != null">
        #{itemName,jdbcType=VARCHAR},
      </if>

      <if test="currProcessCode != null">
        #{currProcessCode,jdbcType=VARCHAR},
      </if>

      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>

      <if test="worker != null">
        #{worker,jdbcType=VARCHAR},
      </if>

      <if test="inTime != null">
        sysdate,
      </if>

      <if test="outTime != null">
        sysdate,
      </if>

      <if test="parentSn != null">
        #{parentSn,jdbcType=VARCHAR},
      </if>

      <if test="errorCode != null">
        #{errorCode,jdbcType=VARCHAR},
      </if>

      <if test="lineCode != null">
        #{lineCode,jdbcType=VARCHAR},
      </if>

      <if test="timeSpan != null">
        #{timeSpan,jdbcType=TIMESTAMP},
      </if>

      <if test="workshopCode != null">
        #{workshopCode,jdbcType=VARCHAR},
      </if>

      <if test="opeTimes != null">
        #{opeTimes,jdbcType=DECIMAL},
      </if>

      <if test="craftSection != null">
        #{craftSection,jdbcType=VARCHAR},
      </if>

      <if test="workStation != null">
        #{workStation,jdbcType=VARCHAR},
      </if>

      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>

      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null">
        #{createDate,jdbcType=TIMESTAMP},
      </if>

      <if test="lastUpdatedBy != null">
        #{lastUpdatedBy,jdbcType=VARCHAR},
      </if>

      <if test="orgId != null">
        #{orgId,jdbcType=DECIMAL},
      </if>

      <if test="factoryId != null">
        #{factoryId,jdbcType=DECIMAL},
      </if>

      <if test="entityId != null">
        #{entityId,jdbcType=DECIMAL},
      </if>

      <if test="attribute1 != null">
        #{attribute1,jdbcType=VARCHAR},
      </if>

      <if test="attribute2 != null">
        #{attribute2,jdbcType=VARCHAR},
      </if>

      <if test="attribute3 != null">
        #{attribute3,jdbcType=VARCHAR},
      </if>

      <if test="attribute4 != null">
        #{attribute4,jdbcType=VARCHAR},
      </if>

      <if test="attribute5 != null">
        #{attribute5,jdbcType=TIMESTAMP},
      </if>

      <if test="lastProcess != null">
        #{lastProcess,jdbcType=VARCHAR},
      </if>

      <if test="sourceSys != null">
        #{sourceSys,jdbcType=VARCHAR},
      </if>

      <if test="nextProcess != null">
        #{nextProcess,jdbcType=VARCHAR},
      </if>
      <if test="sourceImu != null">
        #{sourceImu,jdbcType=DECIMAL},
      </if>
      <if test="sourceBimu != null">
        #{sourceBimu,jdbcType=DECIMAL},
      </if>
      <if test="colName != null">
        #{colName,jdbcType=VARCHAR},
      </if>
      <if test="sourceSysName != null">
        #{sourceSysName,jdbcType=VARCHAR},
      </if>
      <if test="fixId != null">
        #{fixId,jdbcType=VARCHAR},
      </if>
      <if test="stationName != null">
        #{stationName,jdbcType=VARCHAR},
      </if>
      'Y',
      sysdate
    </trim>

  </insert>


  <update id="updatePsWipInfoByIdSelective" parameterType="com.zte.domain.model.PsWipInfo">
    update WIP_INFO
    <set>
      <if test="sn != null">
        SN = #{sn,jdbcType=VARCHAR},
      </if>

      <if test="workOrderNo != null">
        WORK_ORDER_NO = #{workOrderNo,jdbcType=VARCHAR},
      </if>

      <if test="itemNo != null">
        ITEM_NO = #{itemNo,jdbcType=VARCHAR},
      </if>

      <if test="itemName != null">
        ITEM_NAME = #{itemName,jdbcType=VARCHAR},
      </if>

      <if test="currProcessCode != null">
        CURR_PROCESS_CODE = #{currProcessCode,jdbcType=VARCHAR},
      </if>

      <if test="status != null">
        STATUS = #{status,jdbcType=VARCHAR},
      </if>

      <if test="worker != null">
        WORKER = #{worker,jdbcType=VARCHAR},
      </if>

      <if test="inTime != null">
        IN_TIME = #{inTime,jdbcType=TIMESTAMP},
      </if>

      <if test="outTime != null">
        OUT_TIME = #{outTime,jdbcType=TIMESTAMP},
      </if>

      <if test="parentSn != null">
        PARENT_SN = #{parentSn,jdbcType=VARCHAR},
      </if>

      <if test="errorCode != null">
        ERROR_CODE = #{errorCode,jdbcType=VARCHAR},
      </if>

      <if test="lineCode != null">
        LINE_CODE = #{lineCode,jdbcType=VARCHAR},
      </if>

      <if test="timeSpan != null">
        TIME_SPAN = #{timeSpan,jdbcType=TIMESTAMP},
      </if>

      <if test="workshopCode != null">
        WORKSHOP_CODE = #{workshopCode,jdbcType=VARCHAR},
      </if>

      <if test="opeTimes != null">
        OPE_TIMES = #{opeTimes,jdbcType=DECIMAL},
      </if>

      <if test="craftSection != null">
        CRAFT_SECTION = #{craftSection,jdbcType=VARCHAR},
      </if>

      <if test="workStation != null">
        WORK_STATION = #{workStation,jdbcType=VARCHAR},
      </if>

      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>

      <if test="lastUpdatedBy != null">
        LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR},
      </if>

      <if test="attribute1 != null">
        ATTRIBUTE1 = #{attribute1,jdbcType=VARCHAR},
      </if>

      <if test="attribute2 != null">
        ATTRIBUTE2 = #{attribute2,jdbcType=VARCHAR},
      </if>

      <if test="attribute3 != null">
        ATTRIBUTE3 = #{attribute3,jdbcType=VARCHAR},
      </if>

      <if test="attribute4 != null">
        ATTRIBUTE4 = #{attribute4,jdbcType=VARCHAR},
      </if>

      <if test="attribute5 != null">
        ATTRIBUTE5 = #{attribute5,jdbcType=TIMESTAMP},
      </if>

      <if test="lastProcess != null">
        LAST_PROCESS = #{lastProcess,jdbcType=VARCHAR},
      </if>

      <if test="sourceSys != null">
        SOURCE_SYS = #{sourceSys,jdbcType=VARCHAR},
      </if>

      <if test="nextProcess != null">
        NEXT_PROCESS = #{nextProcess,jdbcType=VARCHAR},
      </if>

      <if test="sourceImu != null">
        SOURCE_IMU = #{sourceImu,jdbcType=DECIMAL},
      </if>
      <if test="sourceBimu != null">
        SOURCE_BIMU = #{sourceBimu,jdbcType=DECIMAL},
      </if>
      <if test="colName != null">
        COL_NAME = #{colName,jdbcType=VARCHAR},
      </if>
      <if test="sourceSysName != null">
        SOURCE_SYS_NAME = #{sourceSysName,jdbcType=VARCHAR},
      </if>
      <if test="fixId != null">
        FIX_ID = #{fixId,jdbcType=VARCHAR},
      </if>
      <if test="stationName != null">
        STATION_NAME = #{stationName,jdbcType=VARCHAR},
      </if>
      <if test="assembleFlag != null">
        ASSEMBLE_FLAG = #{assembleFlag,jdbcType=VARCHAR},
      </if>
      LAST_UPDATED_DATE =sysdate
    </set>
    where 1=1
    <if test="sn != null and sn != ''">AND SN = #{sn,jdbcType=VARCHAR}</if>
    <if test="wipId != null and wipId != ''">AND WIP_ID = #{wipId,jdbcType=VARCHAR}</if>
  </update>


  <update id="updatePsWipInfoByScan" parameterType="com.zte.domain.model.PsWipInfo">
    update WIP_INFO
    <set>
      <if test="sn != null">
        SN = #{sn,jdbcType=VARCHAR},
      </if>

      <if test="workOrderNo != null">
        WORK_ORDER_NO = #{workOrderNo,jdbcType=VARCHAR},
      </if>

      <if test="itemNo != null">
        ITEM_NO = #{itemNo,jdbcType=VARCHAR},
      </if>

      <if test="itemName != null">
        ITEM_NAME = #{itemName,jdbcType=VARCHAR},
      </if>

      <if test="currProcessCode != null">
        CURR_PROCESS_CODE = #{currProcessCode,jdbcType=VARCHAR},
      </if>

      <if test="status != null">
        STATUS = #{status,jdbcType=VARCHAR},
      </if>

      <if test="worker != null">
        WORKER = #{worker,jdbcType=VARCHAR},
      </if>

      <if test="inTime != null">
        IN_TIME = sysdate,
      </if>

      <if test="outTime != null">
        OUT_TIME = sysdate,
      </if>

      <if test="parentSn != null">
        PARENT_SN = #{parentSn,jdbcType=VARCHAR},
      </if>

      <if test="errorCode != null">
        ERROR_CODE = #{errorCode,jdbcType=VARCHAR},
      </if>

      <if test="lineCode != null">
        LINE_CODE = #{lineCode,jdbcType=VARCHAR},
      </if>

      <if test="timeSpan != null">
        TIME_SPAN = #{timeSpan,jdbcType=TIMESTAMP},
      </if>

      <if test="workshopCode != null">
        WORKSHOP_CODE = #{workshopCode,jdbcType=VARCHAR},
      </if>

      <if test="opeTimes != null">
        OPE_TIMES = #{opeTimes,jdbcType=DECIMAL},
      </if>

      <if test="craftSection != null">
        CRAFT_SECTION = #{craftSection,jdbcType=VARCHAR},
      </if>

      <if test="workStation != null">
        WORK_STATION = #{workStation,jdbcType=VARCHAR},
      </if>

      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>

      <if test="lastUpdatedBy != null">
        LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR},
      </if>

      <if test="attribute1 != null">
        ATTRIBUTE1 = #{attribute1,jdbcType=VARCHAR},
      </if>

      <if test="attribute2 != null">
        ATTRIBUTE2 = #{attribute2,jdbcType=VARCHAR},
      </if>

      <if test="attribute3 != null">
        ATTRIBUTE3 = #{attribute3,jdbcType=VARCHAR},
      </if>

      <if test="attribute4 != null">
        ATTRIBUTE4 = #{attribute4,jdbcType=VARCHAR},
      </if>

      <if test="attribute5 != null">
        ATTRIBUTE5 = #{attribute5,jdbcType=TIMESTAMP},
      </if>

      <if test="lastProcess != null">
        LAST_PROCESS = #{lastProcess,jdbcType=VARCHAR},
      </if>

      <if test="sourceSys != null">
        SOURCE_SYS = #{sourceSys,jdbcType=VARCHAR},
      </if>

      <if test="nextProcess != null">
        NEXT_PROCESS = #{nextProcess,jdbcType=VARCHAR},
      </if>

      <if test="sourceImu != null">
        SOURCE_IMU = #{sourceImu,jdbcType=DECIMAL},
      </if>
      <if test="sourceBimu != null">
        SOURCE_BIMU = #{sourceBimu,jdbcType=DECIMAL},
      </if>
      <if test="colName != null">
        COL_NAME = #{colName,jdbcType=VARCHAR},
      </if>
      <if test="sourceSysName != null">
        SOURCE_SYS_NAME = #{sourceSysName,jdbcType=VARCHAR},
      </if>
      <if test="fixId != null">
        FIX_ID = #{fixId,jdbcType=VARCHAR},
      </if>
      <if test="stationName != null">
        STATION_NAME = #{stationName,jdbcType=VARCHAR},
      </if>
      LAST_UPDATED_DATE =sysdate

    </set>

    where SN = #{sn,jdbcType=VARCHAR}
    AND ENABLED_FLAG = 'Y'
  </update>


  <update id="updatePsWipInfoByScanForTest" parameterType="com.zte.domain.model.PsWipInfo">
    update WIP_INFO
    <set>
      <if test="sn != null">
        SN = #{sn,jdbcType=VARCHAR},
      </if>

      <if test="workOrderNo != null">
        WORK_ORDER_NO = #{workOrderNo,jdbcType=VARCHAR},
      </if>

      <if test="itemNo != null">
        ITEM_NO = #{itemNo,jdbcType=VARCHAR},
      </if>

      <if test="itemName != null">
        ITEM_NAME = #{itemName,jdbcType=VARCHAR},
      </if>

      <if test="currProcessCode != null">
        CURR_PROCESS_CODE = #{currProcessCode,jdbcType=VARCHAR},
      </if>

      <if test="status != null">
        STATUS = #{status,jdbcType=VARCHAR},
      </if>

      <if test="worker != null">
        WORKER = #{worker,jdbcType=VARCHAR},
      </if>

      <if test="inTime != null">
        IN_TIME = sysdate,
      </if>

      <if test="outTime != null">
        OUT_TIME = sysdate,
      </if>

      <if test="parentSn != null">
        PARENT_SN = #{parentSn,jdbcType=VARCHAR},
      </if>

      <if test="errorCode != null">
        ERROR_CODE = #{errorCode,jdbcType=VARCHAR},
      </if>

      <if test="lineCode != null">
        LINE_CODE = #{lineCode,jdbcType=VARCHAR},
      </if>

      <if test="timeSpan != null">
        TIME_SPAN = #{timeSpan,jdbcType=TIMESTAMP},
      </if>

      <if test="workshopCode != null">
        WORKSHOP_CODE = #{workshopCode,jdbcType=VARCHAR},
      </if>

      <if test="opeTimes != null">
        OPE_TIMES = #{opeTimes,jdbcType=DECIMAL},
      </if>

      <if test="craftSection != null">
        CRAFT_SECTION = #{craftSection,jdbcType=VARCHAR},
      </if>

      <if test="workStation != null">
        WORK_STATION = #{workStation,jdbcType=VARCHAR},
      </if>

      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>

      <if test="lastUpdatedBy != null">
        LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR},
      </if>

      <if test="attribute1 != null">
        ATTRIBUTE1 = #{attribute1,jdbcType=VARCHAR},
      </if>

      <if test="attribute2 != null">
        ATTRIBUTE2 = #{attribute2,jdbcType=VARCHAR},
      </if>

      <if test="attribute3 != null">
        ATTRIBUTE3 = #{attribute3,jdbcType=VARCHAR},
      </if>

      <if test="attribute4 != null">
        ATTRIBUTE4 = #{attribute4,jdbcType=VARCHAR},
      </if>

      <if test="attribute5 != null">
        ATTRIBUTE5 = #{attribute5,jdbcType=TIMESTAMP},
      </if>

      <if test="lastProcess != null">
        LAST_PROCESS = #{lastProcess,jdbcType=VARCHAR},
      </if>

      <if test="sourceSys != null">
        SOURCE_SYS = #{sourceSys,jdbcType=VARCHAR},
      </if>

      <if test="nextProcess != null">
        NEXT_PROCESS = #{nextProcess,jdbcType=VARCHAR},
      </if>

      <if test="createDate != null">
        CREATE_DATE = #{createDate,jdbcType=TIMESTAMP},
      </if>

      <if test="sourceImu != null">
        SOURCE_IMU = #{sourceImu,jdbcType=DECIMAL},
      </if>
      <if test="sourceBimu != null">
        SOURCE_BIMU = #{sourceBimu,jdbcType=DECIMAL},
      </if>
      <if test="colName != null">
        COL_NAME = #{colName,jdbcType=VARCHAR},
      </if>
      <if test="sourceSysName != null">
        SOURCE_SYS_NAME = #{sourceSysName,jdbcType=VARCHAR},
      </if>
      <if test="fixId != null">
        FIX_ID = #{fixId,jdbcType=VARCHAR},
      </if>
      <if test="stationName != null">
        STATION_NAME = #{stationName,jdbcType=VARCHAR},
      </if>
      LAST_UPDATED_DATE =sysdate

    </set>

    where SN = #{sn,jdbcType=VARCHAR}
    AND ENABLED_FLAG = 'Y'
  </update>

  <update id="updatePsWipInfoById" parameterType="com.zte.domain.model.PsWipInfo">
    update WIP_INFO
    set WORK_ORDER_NO = #{workOrderNo,jdbcType=VARCHAR},
    ITEM_NO = #{itemNo,jdbcType=VARCHAR},
    ITEM_NAME = #{itemName,jdbcType=VARCHAR},
    CURR_PROCESS_CODE = #{currProcessCode,jdbcType=VARCHAR},
    STATUS = #{status,jdbcType=VARCHAR},
    WORKER = #{worker,jdbcType=VARCHAR},
    IN_TIME = #{inTime,jdbcType=TIMESTAMP},
    OUT_TIME = #{outTime,jdbcType=TIMESTAMP},
    PARENT_SN = #{parentSn,jdbcType=VARCHAR},
    ERROR_CODE = #{errorCode,jdbcType=VARCHAR},
    LINE_CODE = #{lineCode,jdbcType=VARCHAR},
    TIME_SPAN = #{timeSpan,jdbcType=TIMESTAMP},
    WORKSHOP_CODE = #{workshopCode,jdbcType=VARCHAR},
    OPE_TIMES = #{opeTimes,jdbcType=DECIMAL},
    CRAFT_SECTION = #{craftSection,jdbcType=VARCHAR},
    WORK_STATION = #{workStation,jdbcType=VARCHAR},
    REMARK = #{remark,jdbcType=VARCHAR},
    LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR},
    ATTRIBUTE1 = #{attribute1,jdbcType=VARCHAR},
    ATTRIBUTE2 = #{attribute2,jdbcType=VARCHAR},
    ATTRIBUTE3 = #{attribute3,jdbcType=VARCHAR},
    ATTRIBUTE4 = #{attribute4,jdbcType=VARCHAR},
    ATTRIBUTE5 = #{attribute5,jdbcType=TIMESTAMP},
    LAST_PROCESS = #{lastProcess,jdbcType=VARCHAR},
    SOURCE_SYS = #{sourceSys,jdbcType=VARCHAR},
    NEXT_PROCESS = #{nextProcess,jdbcType=VARCHAR},
    SOURCE_IMU = #{sourceImu,jdbcType=DECIMAL},
    SOURCE_BIMU = #{sourceBimu,jdbcType=DECIMAL},
    COL_NAME = #{colName,jdbcType=VARCHAR},
    SOURCE_SYS_NAME = #{sourceSysName,jdbcType=VARCHAR},
    FIX_ID = #{fixId,jdbcType=VARCHAR},
    STATION_NAME = #{stationName,jdbcType=VARCHAR},
    LAST_UPDATED_DATE =sysdate
    where WIP_ID = #{wipId,jdbcType=VARCHAR}
  </update>


  <update id="updatePsWipInfoForWarehouseEntry" parameterType="java.util.Map">
    update WIP_INFO
    set
    CURR_PROCESS_CODE=#{processCode,jdbcType=VARCHAR},
    LAST_PROCESS='Y',
    LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR},
    LAST_UPDATED_DATE =sysdate
    where  sn in(${sns})
  </update>

  <update id="updatePsWipInfoByBatchSn" parameterType="java.util.Map">
    update WIP_INFO
    set
    <if test="lastUpdatedBy != null and lastUpdatedBy != ''">LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR},
    </if>
    <if test="workStation != null and workStation != ''">WORK_STATION = #{workStation,jdbcType=VARCHAR},</if>
    <if test="currProcessCode != null and currProcessCode != ''">CURR_PROCESS_CODE =
      #{currProcessCode,jdbcType=VARCHAR},
    </if>
    <if test="craftSection != null and craftSection != ''">CRAFT_SECTION = #{craftSection,jdbcType=VARCHAR},</if>
    <if test="nextProcess != null and nextProcess != ''">NEXT_PROCESS = #{nextProcess,jdbcType=VARCHAR},</if>
    <if test="processCode != null">CURR_PROCESS_CODE = #{processCode,jdbcType=VARCHAR},</if>
    LAST_UPDATED_DATE =sysdate
    where ENABLED_FLAG = 'Y'
    <if test="sns != null and sns != ''">and SN in(${sns})</if>
    <if test="parentSns != null and parentSns != ''">and PARENT_SN in(${parentSns})</if>
    <if test="(parentSns == null or parentSns == '') and (sns == null or sns == '')">and  1=2 </if>
  </update>

  <select id="getGroupByWorkOrderNo" parameterType="java.util.Map" resultMap="PsWipInfoDTOMap">
    SELECT COUNT(*) as GROUP_QTY,work_order_no
    FROM WIP_INFO where sn in(${sns}) and ENABLED_FLAG = 'Y'  GROUP BY work_order_no
  </select>

  <select id="getLastWipInfoByWorkOrder" parameterType="java.util.List" resultMap="BaseResultMap" fetchSize="1000">
  select * from (select  row_number() over(partition by t.WORK_ORDER_NO order by t.last_updated_date desc) rn,t.* from WIP_INFO t
  where  t.ENABLED_FLAG = 'Y' and t.WORK_ORDER_NO in
    <foreach collection="workOrderNoList" index="index" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
    ) where rn = 1
  </select>

  <!-- 获取符合条件的记录列表 -->
  <select id="getList" parameterType="java.util.Map" resultMap="BaseResultMap" fetchSize="1000">
    select * from WIP_INFO t
    where 1=1
    and t.ENABLED_FLAG = 'Y'
    <if test="parentSn != null and parentSn != ''">and t.PARENT_SN = #{parentSn}</if>
    <if test="parentSnNotNull != null and parentSnNotNull != ''">and t.parentSn is not null and t.parentSn != ''
    </if>
    <if test="wipId != null and wipId != ''">and t.WIP_ID = #{wipId}</if>
    <if test="sn != null and sn != ''">and t.SN = #{sn}</if>
    <if test="isPrint != null">and t.IS_PRINT = #{isPrint}::numeric</if>
    <if test="inSns != null and inSns != ''">and t.SN in (${inSns})</if>
    <if test="parentSns != null and parentSns != ''">and PARENT_SN in(${parentSns})</if>
    <if test="prodplanId != null and prodplanId != ''">
      and t.attribute1 = #{prodplanId}
      and not exists(select 1 from warehousehm_entry_detail line where t.sn=line.sn)
      and t.sn not in
      (select d.sn from WAREHOUSE_ENTRY_INFO h, WAREHOUSE_ENTRY_DETAIL d
      where h.bill_no = d.bill_no
      and h.enabled_flag = 'Y' and d.enabled_flag = 'Y'
      and d.sn is not null
      <!-- 入库单明细状态为 3:已拒绝 时当做未入库, 单据类型为 6:转交单;7:转交扫货单;9:单板返修入库单 时不算已提交入库 -->
      and d.status not in ('3') and h.bill_type not in ('6','7','9'))
    </if>
    <if test="prodplanIdAll != null and prodplanIdAll != ''">and t.attribute1 = #{prodplanIdAll}</if>
    <if test="inWorkOrderNo != null and inWorkOrderNo != ''">and t.WORK_ORDER_NO IN (${inWorkOrderNo})</if>
    <if test="workOrderNo != null and workOrderNo != ''">and t.WORK_ORDER_NO = #{workOrderNo}</if>
    <if test="itemNo != null and itemNo != ''">and t.ITEM_NO = #{itemNo}</if>
    <if test="itemName != null and itemName != ''">and t.ITEM_NAME = #{itemName}</if>
    <if test="selectedProcessCode != null and selectedProcessCode != ''">and t.CURR_PROCESS_CODE !=
      #{selectedProcessCode}
    </if>
    <if test="currProcessCode != null and currProcessCode != ''">and t.CURR_PROCESS_CODE = #{currProcessCode}</if>
    <if test="status != null and status != ''">and t.STATUS = #{status}</if>
    <if test="worker != null and worker != ''">and t.WORKER = #{worker}</if>
    <if test="lineCode != null and lineCode != ''">and t.LINE_CODE = #{lineCode}</if>
    <if test="workshopCode != null and workshopCode != ''">and t.WORKSHOP_CODE = #{workshopCode}</if>
    <if test="inTimeBegin != null and inTimeEnd != null">
      <![CDATA[and t.IN_TIME > #{inTimeBegin} and t.IN_TIME < #{inTimeEnd}]]></if>
    <if test="outTimeBegin != null and outTimeEnd != null">
      <![CDATA[and t.OUT_TIME > #{outTimeBegin} and t.OUT_TIME < #{outTimeEnd}]]></if>
    <if test="craftSection != null and craftSection != ''">and t.CRAFT_SECTION = #{craftSection}</if>
    <if test="likeCraftSection != null and likeCraftSection != ''">and t.CRAFT_SECTION like '${likeCraftSection}%'
    </if>
    <if test="workStation != null and workStation != ''">and t.WORK_STATION = #{workStation}</if>
    <if test="attribute1 != null and attribute1 != ''">and t.attribute1 = #{attribute1}</if>
    <if test="attribute2 != null and attribute2 != ''">and t.attribute2 = #{attribute2}</if>
    <if test="orgId != null">and t.ORG_ID = cast(#{orgId} as numeric)</if>
    <if test="factoryId != null">and t.FACTORY_ID = cast(#{factoryId} as numeric)</if>
    <if test="entityId != null">and t.ENTITY_ID = cast(#{entityId} as numeric)</if>
    <if test="snStart != null and snStart != '' and snEnd != null and snEnd != ''">and t.SN between #{snStart} and
      #{snEnd}
    </if>
    <if test="notInWarehouse != null">and t.SN not in(select sn from warehousehm_entry_detail t where
      t.factory_id=cast(#{factoryId} as numeric) and t.enabled_flag='Y')
    </if>
    <if test="(parentSn == null or parentSn == '')
      and (parentSnNotNull == null or parentSnNotNull == '')
      and (wipId == null or wipId == '')
      and (sn == null or sn == '')
      and (inSns == null or inSns == '')
      and (parentSns == null or parentSns == '')
      and (prodplanId == null or prodplanId == '')
      and (prodplanIdAll == null or prodplanIdAll == '')
      and (inWorkOrderNo == null or inWorkOrderNo == '')
      and (workOrderNo == null or workOrderNo == '')
      and (itemNo == null or itemNo == '')
      and (itemName == null or itemName == '')
      and (currProcessCode == null or currProcessCode == '')
      and (worker == null or worker == '')
      and (lineCode == null or lineCode == '')
      and (workshopCode == null or workshopCode == '')
      and (inTimeBegin == null or inTimeEnd == null)
      and (outTimeBegin == null or outTimeEnd == null)
      and (craftSection == null or craftSection == '')
      and (workStation == null or workStation == '')
      and (attribute1 == null or attribute1 == '')
      and (attribute2 == null or attribute2 == '')
      and (snStart == null or snStart == '' or snEnd == null or snEnd == '')
      and (notInWarehouse == null)">
      and 1=2
    </if>
    <if test="orderField != null">
      <choose>
        <when test="orderField=='sn'">order by t.SN
          <if test="order != null and order == 'desc'">desc</if>
        </when>

        <when test="orderField=='workOrderNo'">order by t.WORK_ORDER_NO
          <if
                  test="order != null and order == 'desc'">desc
          </if>
        </when>
        <when test="orderField=='itemNo'">order by t.ITEM_NO
          <if test="order != null and order == 'desc'">desc
          </if>
        </when>
        <when test="orderField=='itemName'">order by t.ITEM_NAME
          <if test="order != null and order == 'desc'">
            desc
          </if>
        </when>
        <when test="orderField=='currProcessCode'">order by t.CURR_PROCESS_CODE
          <if
                  test="order != null and order == 'desc'">desc
          </if>
        </when>
        <when test="orderField=='status'">order by t.STATUS
          <if test="order != null and order == 'desc'">desc
          </if>
        </when>
        <when test="orderField=='worker'">order by t.WORKER
          <if test="order != null and order == 'desc'">desc
          </if>
        </when>
        <when test="orderField=='lineCode'">order by t.LINE_CODE
          <if test="order != null and order == 'desc'">
            desc
          </if>
        </when>
        <when test="orderField=='workshopCode'">order by t.WORKSHOP_CODE
          <if
                  test="order != null and order == 'desc'">desc
          </if>
        </when>
        <when test="orderField=='inTime'">order by t.IN_TIME
          <if test="order != null and order == 'desc'">desc
          </if>
        </when>
        <when test="orderField=='outTime'">order by t.OUT_TIME
          <if test="order != null and order == 'desc'">
            desc
          </if>
        </when>
        <when test="orderField=='craftSection'">order by t.CRAFT_SECTION
          <if
                  test="order != null and order == 'desc'">desc
          </if>
        </when>
        <when test="orderField=='workStation'">order by t.WORK_STATION
          <if
                  test="order != null and order == 'desc'">desc
          </if>
        </when>
        <when test="orderField=='lastProcess'">order by t.LAST_PROCESS
          <if
                  test="order != null and order == 'desc'">desc
          </if>
        </when>
        <when test="orderField=='createBy'">order by t.CREATE_BY
          <if test="order != null and order == 'desc'">
            desc
          </if>
        </when>
        <when test="orderField=='createDate'">order by t.CREATE_DATE
          <if
                  test="order != null and order == 'desc'">desc
          </if>
        </when>
        <when test="orderField=='lastUpdatedBy'">order by t.LAST_UPDATED_BY
          <if
                  test="order != null and order == 'desc'">desc
          </if>
        </when>
        <when test="orderField=='lastUpdatedDate'">order by t.LAST_UPDATED_DATE
          <if
                  test="order != null and order == 'desc'">desc
          </if>
        </when>
      </choose>
    </if>
  </select>

  <!-- 提交入库定时任务使用 -->
  <select id="getListByPage" parameterType="java.util.Map" resultMap="BaseResultMap" fetchSize="1000">
    select * from WIP_INFO t
    where 1=1
    and t.ENABLED_FLAG = 'Y'
    <if test="parentSn != null and parentSn != ''">and t.PARENT_SN = #{parentSn}</if>
    <if test="wipId != null and wipId != ''">and t.WIP_ID = #{wipId}</if>
    <if test="sn != null and sn != ''">and t.SN = #{sn}</if>
    <if test="isPrint != null">and t.IS_PRINT = #{isPrint}::numeric</if>
    <if test="inSns != null and inSns != ''">and t.SN in (${inSns})</if>
    <if test="prodplanId != null and prodplanId != ''">
      and t.attribute1 = #{prodplanId}
      and not exists(select 1 from warehousehm_entry_detail line where t.sn=line.sn)
      and t.sn not in
      (select d.sn from WAREHOUSE_ENTRY_INFO h, WAREHOUSE_ENTRY_DETAIL d
      where h.bill_no = d.bill_no
      and h.enabled_flag = 'Y' and d.enabled_flag = 'Y'
      and d.sn is not null
      <!-- 入库单明细状态为 3:已拒绝 时当做未入库, 单据类型为 6:转交单;7:转交扫货单;9:单板返修入库单 时不算已提交入库 -->
      and d.status not in ('3') and h.bill_type not in ('6','7','9'))
    </if>
    <if test="prodplanIdAll != null and prodplanIdAll != ''">and t.attribute1 = #{prodplanIdAll}</if>
    <if test="inWorkOrderNo != null and inWorkOrderNo != ''">and t.WORK_ORDER_NO IN (${inWorkOrderNo})</if>
    <if test="workOrderNo != null and workOrderNo != ''">and t.WORK_ORDER_NO = #{workOrderNo}</if>
    <if test="itemNo != null and itemNo != ''">and t.ITEM_NO = #{itemNo}</if>
    <if test="itemName != null and itemName != ''">and t.ITEM_NAME = #{itemName}</if>
    <if test="selectedProcessCode != null and selectedProcessCode != ''">and t.CURR_PROCESS_CODE !=
      #{selectedProcessCode}
    </if>
    <if test="currProcessCode != null and currProcessCode != ''">and t.CURR_PROCESS_CODE = #{currProcessCode}</if>
    <if test="status != null and status != ''">and t.STATUS = #{status}</if>
    <if test="worker != null and worker != ''">and t.WORKER = #{worker}</if>
    <if test="lineCode != null and lineCode != ''">and t.LINE_CODE = #{lineCode}</if>
    <if test="workshopCode != null and workshopCode != ''">and t.WORKSHOP_CODE = #{workshopCode}</if>
    <if test="inTimeBegin != null and inTimeEnd != null">
      <![CDATA[and t.IN_TIME > #{inTimeBegin} and t.IN_TIME < #{inTimeEnd}]]></if>
    <if test="outTimeBegin != null and outTimeEnd != null">
      <![CDATA[and t.OUT_TIME > #{outTimeBegin} and t.OUT_TIME < #{outTimeEnd}]]></if>
    <if test="craftSection != null and craftSection != ''">and t.CRAFT_SECTION = #{craftSection}</if>
    <if test="likeCraftSection != null and likeCraftSection != ''">and t.CRAFT_SECTION like '${likeCraftSection}%'
    </if>
    <if test="workStation != null and workStation != ''">and t.WORK_STATION = #{workStation}</if>
    <if test="attribute1 != null and attribute1 != ''">and t.attribute1 = #{attribute1}</if>
    <if test="orgId != null">and t.ORG_ID = cast(#{orgId} as numeric)</if>
    <if test="factoryId != null">and t.FACTORY_ID = cast(#{factoryId} as numeric)</if>
    <if test="entityId != null">and t.ENTITY_ID = cast(#{entityId} as numeric)</if>
    <if test="snStart != null and snStart != '' and snEnd != null and snEnd != ''">and t.SN between #{snStart} and
      #{snEnd}
    </if>
    <if test="notInWarehouse != null">and t.SN not in(select sn from warehousehm_entry_detail t where
      t.factory_id=cast(#{factoryId} as numeric) and t.enabled_flag='Y')
    </if>
    <if test="orderField != null">
      <choose>
        <when test="orderField=='sn'">order by t.SN
          <if test="order != null and order == 'desc'">desc</if>
        </when>
        <when test="orderField=='workOrderNo'">order by t.WORK_ORDER_NO
          <if
                  test="order != null and order == 'desc'">desc
          </if>
        </when>
        <when test="orderField=='itemNo'">order by t.ITEM_NO
          <if test="order != null and order == 'desc'">desc
          </if>
        </when>
        <when test="orderField=='itemName'">order by t.ITEM_NAME
          <if test="order != null and order == 'desc'">
            desc
          </if>
        </when>
        <when test="orderField=='currProcessCode'">order by t.CURR_PROCESS_CODE
          <if
                  test="order != null and order == 'desc'">desc
          </if>
        </when>
        <when test="orderField=='status'">order by t.STATUS
          <if test="order != null and order == 'desc'">desc
          </if>
        </when>
        <when test="orderField=='worker'">order by t.WORKER
          <if test="order != null and order == 'desc'">desc
          </if>
        </when>
        <when test="orderField=='lineCode'">order by t.LINE_CODE
          <if test="order != null and order == 'desc'">
            desc
          </if>
        </when>
        <when test="orderField=='workshopCode'">order by t.WORKSHOP_CODE
          <if
                  test="order != null and order == 'desc'">desc
          </if>
        </when>
        <when test="orderField=='inTime'">order by t.IN_TIME
          <if test="order != null and order == 'desc'">desc
          </if>
        </when>
        <when test="orderField=='outTime'">order by t.OUT_TIME
          <if test="order != null and order == 'desc'">
            desc
          </if>
        </when>
        <when test="orderField=='craftSection'">order by t.CRAFT_SECTION
          <if
                  test="order != null and order == 'desc'">desc
          </if>
        </when>
        <when test="orderField=='workStation'">order by t.WORK_STATION
          <if
                  test="order != null and order == 'desc'">desc
          </if>
        </when>
        <when test="orderField=='lastProcess'">order by t.LAST_PROCESS
          <if
                  test="order != null and order == 'desc'">desc
          </if>
        </when>
        <when test="orderField=='createBy'">order by t.CREATE_BY
          <if test="order != null and order == 'desc'">
            desc
          </if>
        </when>
        <when test="orderField=='createDate'">order by t.CREATE_DATE
          <if
                  test="order != null and order == 'desc'">desc
          </if>
        </when>
        <when test="orderField=='lastUpdatedBy'">order by t.LAST_UPDATED_BY
          <if
                  test="order != null and order == 'desc'">desc
          </if>
        </when>
        <when test="orderField=='lastUpdatedDate'">order by t.LAST_UPDATED_DATE
          <if
                  test="order != null and order == 'desc'">desc
          </if>
        </when>
      </choose>
    </if>
    limit #{endRow}::numeric-#{startRow}::numeric+1 offset case when ( #{startRow}::numeric-1 &lt; 0) then 0 else  #{startRow}::numeric-1 end
  </select>

  <select id="getCountForBusiness" parameterType="java.util.Map" resultType="java.lang.Long">
    select count(*)
    from WIP_INFO t
    where 1=1
    and t.ENABLED_FLAG = 'Y'
    <if test="parentSn != null and parentSn != ''">and t.PARENT_SN = #{parentSn}</if>
    <if test="wipId != null and wipId != ''">and t.WIP_ID = #{wipId}</if>
    <if test="sn != null and sn != ''">and t.SN = #{sn}</if>
    <if test="isPrint != null">and t.IS_PRINT = #{isPrint}::numeric</if>
    <if test="inSns != null and inSns != ''">and t.SN in (${inSns})</if>
    <if test="prodplanId != null and prodplanId != ''">
      and t.attribute1 = #{prodplanId}
      and not exists(select 1 from warehousehm_entry_detail line where t.sn=line.sn)
      and t.sn not in
      (select d.sn from WAREHOUSE_ENTRY_INFO h, WAREHOUSE_ENTRY_DETAIL d
      where h.bill_no = d.bill_no
      and h.enabled_flag = 'Y' and d.enabled_flag = 'Y'
      and d.sn is not null
      <!-- 入库单明细状态为 3:已拒绝 时当做未入库, 单据类型为 6:转交单;7:转交扫货单;9:单板返修入库单 时不算已提交入库 -->
      and d.status not in ('3') and h.bill_type not in ('6','7','9'))
    </if>
    <if test="prodplanIdAll != null and prodplanIdAll != ''">and t.attribute1 = #{prodplanIdAll}</if>
    <if test="inWorkOrderNo != null and inWorkOrderNo != ''">and t.WORK_ORDER_NO IN (${inWorkOrderNo})</if>
    <if test="workOrderNo != null and workOrderNo != ''">and t.WORK_ORDER_NO = #{workOrderNo}</if>
    <if test="itemNo != null and itemNo != ''">and t.ITEM_NO = #{itemNo}</if>
    <if test="itemName != null and itemName != ''">and t.ITEM_NAME = #{itemName}</if>
    <if test="selectedProcessCode != null and selectedProcessCode != ''">and t.CURR_PROCESS_CODE !=
      #{selectedProcessCode}
    </if>
    <if test="currProcessCode != null and currProcessCode != ''">and t.CURR_PROCESS_CODE = #{currProcessCode}</if>
    <if test="status != null and status != ''">and t.STATUS = #{status}</if>
    <if test="worker != null and worker != ''">and t.WORKER = #{worker}</if>
    <if test="lineCode != null and lineCode != ''">and t.LINE_CODE = #{lineCode}</if>
    <if test="workshopCode != null and workshopCode != ''">and t.WORKSHOP_CODE = #{workshopCode}</if>
    <if test="inTimeBegin != null and inTimeEnd != null">
      <![CDATA[and t.IN_TIME > #{inTimeBegin} and t.IN_TIME < #{inTimeEnd}]]></if>
    <if test="outTimeBegin != null and outTimeEnd != null">
      <![CDATA[and t.OUT_TIME > #{outTimeBegin} and t.OUT_TIME < #{outTimeEnd}]]></if>
    <if test="craftSection != null and craftSection != ''">and t.CRAFT_SECTION = #{craftSection}</if>
    <if test="likeCraftSection != null and likeCraftSection != ''">and t.CRAFT_SECTION like '${likeCraftSection}%'
    </if>
    <if test="workStation != null and workStation != ''">and t.WORK_STATION = #{workStation}</if>
    <if test="attribute1 != null and attribute1 != ''">and t.attribute1 = #{attribute1}</if>
    <if test="orgId != null">and t.ORG_ID = cast(#{orgId} as numeric)</if>
    <if test="factoryId != null">and t.FACTORY_ID = cast(#{factoryId} as numeric)</if>
    <if test="entityId != null">and t.ENTITY_ID = cast(#{entityId} as numeric)</if>
    <if test="snStart != null and snStart != '' and snEnd != null and snEnd != ''">and t.SN between #{snStart} and
      #{snEnd}
    </if>
    <if test="notInWarehouse != null">and t.SN not in(select sn from warehousehm_entry_detail t where
      t.factory_id=cast(#{factoryId} as numeric) and t.enabled_flag='Y')
    </if>
  </select>

  <!-- 获取符合条件的记录列表 -->
  <select id="getList4report" parameterType="com.zte.interfaces.dto.PsWipInfoDTO" resultMap="PsWipInfoDTOMap">
    select count(1) inCountTotal,t.attribute1 from WIP_INFO t
    where 1=1
    and t.CRAFT_SECTION in ('入库' ,'出库')
    <if test="inProdplanIds != null and inProdplanIds.size > 0">
      and t.attribute1 in
      <foreach collection="inProdplanIds" index="index" item="prodplanId" open="(" separator="," close=")">
        #{prodplanId}
      </foreach>
    </if>
    and t.ENABLED_FLAG = 'Y' group by t.attribute1
  </select>

  <!-- 翻页函数:获取符合条件的记录数 -->
  <select id="getCount" parameterType="java.util.Map" resultType="java.lang.Long">
    select count(*)
    from WIP_INFO t
    where 1=1
    and t.ENABLED_FLAG = 'Y'
    <if test="parentSn != null and parentSn != ''">and t.PARENT_SN = #{parentSn}</if>
    <if test="parentSnNotNull != null and parentSnNotNull != ''">and t.PARENT_SN is not null</if>
    <if test="wipId != null and wipId != ''">and t.WIP_ID = #{wipId}</if>
    <if test="sn != null and sn != ''">and t.SN = #{sn}</if>
    <if test="snList != null and snList.size > 0">and t.SN in
      <foreach item="item" collection="snList" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="parentSnList != null and parentSnList.size > 0">and t.PARENT_SN in
      <foreach item="item" collection="parentSnList" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="isPrint != null and isPrint == 1">and t.IS_PRINT = #{isPrint}::numeric</if>
    <if test="isPrint != null and isPrint == 0">and (t.IS_PRINT = #{isPrint}::numeric or t.IS_PRINT is Null)</if>
    <if test="workOrderNo != null and workOrderNo != ''">and t.WORK_ORDER_NO = #{workOrderNo}</if>
    <if test="sourceTask !=null and sourceTask !=''">and t.WORK_ORDER_NO like '%${sourceTask}%'</if>
    <if test="inWorkOrderNo != null and inWorkOrderNo != ''">and t.WORK_ORDER_NO IN (${inWorkOrderNo})</if>
    <if test="itemNo != null and itemNo != ''">and t.ITEM_NO = #{itemNo}</if>
    <if test="itemName != null and itemName != ''">and t.ITEM_NAME = #{itemName}</if>
    <if test="currProcessCode != null and currProcessCode != ''">and t.CURR_PROCESS_CODE = #{currProcessCode}</if>
    <if test="status != null and status != ''">and t.STATUS = #{status}</if>
    <if test="worker != null and worker != ''">and t.WORKER = #{worker}</if>
    <if test="lineCode != null and lineCode != ''">and t.LINE_CODE = #{lineCode}</if>
    <if test="workshopCode != null and workshopCode != ''">and t.WORKSHOP_CODE = #{workshopCode}</if>
    <if test="inTimeBegin != null and inTimeEnd != null">
      <![CDATA[and t.IN_TIME > #{inTimeBegin} and t.IN_TIME < #{inTimeEnd}]]></if>
    <if test="outTimeBegin != null and outTimeEnd != null">
      <![CDATA[and t.OUT_TIME > #{outTimeBegin} and t.OUT_TIME < #{outTimeEnd}]]></if>
    <if test="craftSection != null and craftSection != ''">and t.CRAFT_SECTION = #{craftSection}</if>
    <if test="workStation != null and workStation != ''">and t.WORK_STATION = #{workStation}</if>
    <if test="orgId != null">and t.ORG_ID = cast(#{orgId} as numeric)</if>
    <if test="factoryId != null">and t.FACTORY_ID = cast(#{factoryId} as numeric)</if>
    <if test="entityId != null">and t.ENTITY_ID = cast(#{entityId} as numeric)</if>
    <if test="snStart != null and snStart != '' and snEnd != null and snEnd != ''">and t.SN between #{snStart} and
      #{snEnd}
    </if>
    <if test="attribute1 != null and attribute1 != ''">and t.ATTRIBUTE1 = #{attribute1}</if>
  </select>

  <!-- 获取符合条件的记录列表 -->
  <select id="getWipByExtend" parameterType="java.util.Map" resultMap="BaseResultMap">
    select t.*
    from WIP_INFO t left join WIP_EXTEND_IDENTIFICATION e on e.SN = t.SN and e.ENABLED_FLAG = 'Y'
    <if test="dipFinishFlag != null and dipFinishFlag == 'Y'.toString()">
      join wip_scan_history wsc on wsc.sn=t.sn and  wsc.work_order_no=t.work_order_no  and wsc.dip_finish_flag =#{dipFinishFlag} and wsc.ENABLED_FLAG = 'Y'
    </if>
    where 1=1
    and t.ENABLED_FLAG = 'Y' and e.FORM_SN = #{sn}
    <if test="workOrderNo != null and workOrderNo != ''">and t.WORK_ORDER_NO = #{workOrderNo}</if>
    <if test="status != null and status != ''">and t.STATUS = #{status}</if>
    <if test="orgId != null">and t.ORG_ID = cast(#{orgId} as numeric)</if>
    <if test="factoryId != null">and t.FACTORY_ID = cast(#{factoryId} as numeric)</if>
    <if test="entityId != null">and t.ENTITY_ID = cast(#{entityId} as numeric)</if>
    <if test="currProcessCode != null and currProcessCode != ''">and t.CURR_PROCESS_CODE = #{currProcessCode}</if>
    <if test="workStation != null and workStation != ''"> <![CDATA[ and t.WORK_STATION <> '0' ]]></if>
    <if test="lastProcess != null and lastProcess != ''">and t.LAST_PROCESS = #{lastProcess}</if>
    union
    select t.*
    from WIP_INFO t
    <if test="dipFinishFlag != null and dipFinishFlag == 'Y'.toString()">
      join wip_scan_history wsc on wsc.sn=t.sn and  wsc.work_order_no=t.work_order_no  and wsc.dip_finish_flag =#{dipFinishFlag} and wsc.ENABLED_FLAG = 'Y'
    </if>
    where 1=1
    and t.ENABLED_FLAG = 'Y' and t.SN = #{sn}
    <if test="workOrderNo != null and workOrderNo != ''">and t.WORK_ORDER_NO = #{workOrderNo}</if>
    <if test="status != null and status != ''">and t.STATUS = #{status}</if>
    <if test="orgId != null">and t.ORG_ID = cast(#{orgId} as numeric)</if>
    <if test="factoryId != null">and t.FACTORY_ID = cast(#{factoryId} as numeric)</if>
    <if test="entityId != null">and t.ENTITY_ID = cast(#{entityId} as numeric)</if>
    <if test="currProcessCode != null and currProcessCode != ''">and t.CURR_PROCESS_CODE = #{currProcessCode}</if>
    <if test="workStation != null and workStation != ''"> <![CDATA[ and t.WORK_STATION <> '0' ]]></if>
    <if test="lastProcess != null and lastProcess != ''">and t.LAST_PROCESS = #{lastProcess}</if>
  </select>

  <!-- 获取符合条件的记录列表 -->
  <select id="getWipinfoByCondition" parameterType="java.util.Map" resultMap="BaseResultMap">
    select t.*
    from WIP_INFO t
    <if test="dipFinishFlag != null and dipFinishFlag == 'Y'.toString()">
      join wip_scan_history wsc on wsc.sn=t.sn and  wsc.work_order_no=t.work_order_no  and wsc.dip_finish_flag =#{dipFinishFlag} and wsc.ENABLED_FLAG = 'Y'
    </if>
    where 1=1
    and t.ENABLED_FLAG = 'Y'
    <if test="sn != null and sn != ''">and t.SN = #{sn}</if>
    <if test="workOrderNo != null and workOrderNo != ''">and t.WORK_ORDER_NO = #{workOrderNo}</if>
    <if test="status != null and status != ''">and t.STATUS = #{status}</if>
    <if test="orgId != null">and t.ORG_ID = cast(#{orgId} as numeric)</if>
    <if test="factoryId != null">and t.FACTORY_ID = cast(#{factoryId} as numeric)</if>
    <if test="entityId != null">and t.ENTITY_ID = cast(#{entityId} as numeric)</if>
    <if test="currProcessCode != null and currProcessCode != ''">and t.CURR_PROCESS_CODE = #{currProcessCode}</if>
    <if test="workStation != null and workStation != ''"> <![CDATA[ and t.WORK_STATION <> '0' ]]></if>
    <if test="lastProcess != null and lastProcess != ''">and t.LAST_PROCESS = #{lastProcess}</if>
    <if test="orgId == null and (sn == null or sn == '') and
        (workOrderNo == null or workOrderNo == '') and
        (status == null or status == '') and
        (currProcessCode == null or currProcessCode == '') and
        (workStation == null or workStation == '') and
        (lastProcess == null or lastProcess == '')">
      and 1=2
    </if>
  </select>
  <insert id="createTempSnTable" >
    CREATE TEMP TABLE temporary_storage_barcode_table (
    sn VARCHAR(50) PRIMARY KEY
    )ON COMMIT DROP
  </insert>

  <insert id="insertTempSn" >
    INSERT INTO temporary_storage_barcode_table (
    sn
    )
    VALUES
    <foreach collection="snList" item="item" index="index" separator=",">
      (
      #{item,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>

  <!-- 获取符合条件的记录列表 -->
  <select id="getWipinfoByConditionPage" parameterType="com.zte.springbootframe.common.model.Page" resultMap="BaseResultSimpleMap" flushCache="true">
    select u.SN,u.WORK_ORDER_NO,u.CURR_PROCESS_CODE,u.WORKER,u.ITEM_NO from (
    <foreach collection="params.currProcessCodeList" index="index" separator="UNION ALL">
      select t.SN,t.WORK_ORDER_NO,t.CURR_PROCESS_CODE,t.WORKER,t.ITEM_NO from WIP_INFO t
      <if test="params.dipFinishFlag != null and params.dipFinishFlag == 'Y'.toString()">
        join wip_scan_history wsc on wsc.sn=t.sn and  wsc.work_order_no=t.work_order_no  and wsc.dip_finish_flag =#{params.dipFinishFlag} and wsc.ENABLED_FLAG = 'Y'
      </if>
      where t. ENABLED_FLAG = 'Y'
      <if test="params.notExistSnList != null and params.notExistSnList.size() &lt;= 500 and params.notExistSnList.size() >0 ">
        and not exists (SELECT *
        FROM (
        <foreach collection = "params.notExistSnList" index="index2" separator="UNION ALL">
          SELECT  #{params.notExistSnList[${index2}]} AS sn
        </foreach>
        ) tmp
        WHERE tmp.sn = t.sn)
      </if>
      <if test="params.notExistSnList != null and params.notExistSnList.size() > 500 ">
        and not exists (SELECT *
        FROM temporary_storage_barcode_table tmp
        WHERE tmp.sn = t.sn)
      </if>
      <if test="params.notInQcFlag != null and params.notInQcFlag == 'Y'.toString()">
        and not exists (SELECT qc2.sn
        from qc_sampling_head qc1
        inner join qc_sampling_detail qc2 on qc1.head_id = qc2.head_id
        where qc1.status in ('1', '2', '3')
        and qc1.enabled_flag = 'Y'
        and qc2.enabled_flag = 'Y'
        <if test="params.workOrderNo != null and params.workOrderNo != ''">
          and qc1.work_order_no = #{params.workOrderNo}
        </if>
        and qc2.sn = t.sn)
      </if>
      <if test="params.sn != null and params.sn != ''">and t.SN = #{params.sn}</if>
      <if test="params.workOrderNo != null and params.workOrderNo != ''">and t.WORK_ORDER_NO = #{params.workOrderNo}</if>
      <if test="params.status != null and params.status != ''">and t.STATUS = #{params.status}</if>
      and t.CURR_PROCESS_CODE = #{params.currProcessCodeList[${index}]}
      <if test="params.workStation != null and params.workStation != ''"> <![CDATA[ and t.WORK_STATION <> '0' ]]></if>
      <if test="params.lastProcess != null and params.lastProcess != ''">and t.LAST_PROCESS = #{params.lastProcess}</if>
    </foreach>
    ) u order by sn
  </select>

  <!-- 获取符合条件的记录列表 -->
  <select id="getWipInfoBatch" parameterType="java.util.Map" resultMap="BaseResultMap">
    select t.*
    from WIP_INFO t left join WIP_EXTEND_IDENTIFICATION e on e.SN = t.SN and e.ENABLED_FLAG = 'Y'
    where 1=1
    and t.ENABLED_FLAG = 'Y'
    <if test="condition != null and condition != ''">and e.FORM_SN in ( ${condition} )</if>
    <if test="sn != null and sn != ''">and e.FORM_SN = #{sn}</if>
    <if test="workOrderNo != null and workOrderNo != ''">and t.WORK_ORDER_NO = #{workOrderNo}</if>
    <if test="status != null and status != ''">and t.STATUS = #{status}</if>
    <if test="orgId != null">and t.ORG_ID = cast(#{orgId} as numeric)</if>
    <if test="orgId == null and (condition == null or condition == '') and
        (sn == null or sn == '') and
        (workOrderNo == null or workOrderNo == '') and
        (status == null or status == '')">
      and 1=2
    </if>
    union
    select t.*
    from WIP_INFO t
    where 1=1
    and t.ENABLED_FLAG = 'Y'
    <if test="condition != null and condition != ''">and t.SN in ( ${condition} )</if>
    <if test="sn != null and sn != ''">and t.SN = #{sn}</if>
    <if test="workOrderNo != null and workOrderNo != ''">and t.WORK_ORDER_NO = #{workOrderNo}</if>
    <if test="curProcessCode != null and curProcessCode != ''">and t.curr_process_code = #{curProcessCode}</if>
    <if test="workStation != null and workStation != ''">and t.work_station = #{workStation}</if>
    <if test="status != null and status != ''">and t.STATUS = #{status}</if>
    <if test="orgId != null">and t.ORG_ID = cast(#{orgId} as numeric)</if>
    <if test="factoryId != null">and t.FACTORY_ID = cast(#{factoryId} as numeric)</if>
    <if test="entityId != null">and t.ENTITY_ID = cast(#{entityId} as numeric)</if>
    <if test="(condition == null or condition == '') and
        (sn == null or sn == '') and
        (workOrderNo == null or workOrderNo == '') and
        (curProcessCode == null or curProcessCode == '') and
        (workStation == null or workStation == '') and
        (status == null or status == '') and
        (orgId == null) and
        (factoryId == null) and
        (entityId == null)">
      and 1=2
    </if>
  </select>

  <insert id="insertPsWipInfoBatch" parameterType="java.util.List">
    insert into WIP_INFO
    (WIP_ID, SN, WORK_ORDER_NO,
    ITEM_NO, ITEM_NAME,
    CURR_PROCESS_CODE, STATUS, WORKER,
    IN_TIME, OUT_TIME, PARENT_SN,
    ERROR_CODE, LINE_CODE, TIME_SPAN,
    WORKSHOP_CODE, OPE_TIMES,CRAFT_SECTION,WORK_STATION, REMARK,
    CREATE_BY, CREATE_DATE, LAST_UPDATED_BY,
    LAST_UPDATED_DATE, ENABLED_FLAG, ORG_ID,
    FACTORY_ID, ENTITY_ID, ATTRIBUTE1,
    ATTRIBUTE2, ATTRIBUTE3, ATTRIBUTE4,
    ATTRIBUTE5,LAST_PROCESS,SOURCE_SYS,NEXT_PROCESS
    ,SOURCE_IMU,SOURCE_BIMU,COL_NAME,SOURCE_SYS_NAME,FIX_ID,STATION_NAME,ORIGINAL_TASK
    )
    select WIP_ID, SN, WORK_ORDER_NO,
    ITEM_NO, ITEM_NAME,
    CURR_PROCESS_CODE, STATUS, WORKER,
    IN_TIME, OUT_TIME, PARENT_SN,
    ERROR_CODE, LINE_CODE, TIME_SPAN,
    WORKSHOP_CODE, OPE_TIMES,CRAFT_SECTION,WORK_STATION, REMARK,
    CREATE_BY, CREATE_DATE, LAST_UPDATED_BY,
    LAST_UPDATED_DATE, ENABLED_FLAG, ORG_ID,
    FACTORY_ID, ENTITY_ID, ATTRIBUTE1,
    ATTRIBUTE2, ATTRIBUTE3, ATTRIBUTE4,
    ATTRIBUTE5::TIMESTAMP,LAST_PROCESS,SOURCE_SYS,NEXT_PROCESS
    ,SOURCE_IMU,SOURCE_BIMU,COL_NAME,SOURCE_SYS_NAME,FIX_ID,STATION_NAME,ORIGINAL_TASK
    from (
    <foreach collection="list" item="item" index="index" separator="UNION ALL">
      select
      #{item.wipId,jdbcType=VARCHAR} WIP_ID,
      #{item.sn,jdbcType=VARCHAR} SN,
      #{item.workOrderNo,jdbcType=VARCHAR} WORK_ORDER_NO,
      #{item.itemNo,jdbcType=VARCHAR} ITEM_NO,
      #{item.itemName,jdbcType=VARCHAR} ITEM_NAME,
      #{item.currProcessCode,jdbcType=VARCHAR} CURR_PROCESS_CODE,
      #{item.status,jdbcType=VARCHAR} STATUS,
      #{item.worker,jdbcType=VARCHAR} WORKER,
      #{item.inTime,jdbcType=TIMESTAMP}::TIMESTAMP IN_TIME,
      #{item.outTime,jdbcType=TIMESTAMP}::TIMESTAMP OUT_TIME,
      #{item.parentSn,jdbcType=VARCHAR} PARENT_SN,
      #{item.errorCode,jdbcType=VARCHAR} ERROR_CODE,
      #{item.lineCode,jdbcType=VARCHAR} LINE_CODE,
      #{item.timeSpan,jdbcType=TIMESTAMP}::TIMESTAMP TIME_SPAN,
      #{item.workshopCode,jdbcType=VARCHAR} WORKSHOP_CODE,
      #{item.opeTimes,jdbcType=DECIMAL} OPE_TIMES,
      #{item.craftSection,jdbcType=VARCHAR} CRAFT_SECTION,
      #{item.workStation,jdbcType=VARCHAR} WORK_STATION,
      #{item.remark,jdbcType=VARCHAR} REMARK,
      #{item.createBy,jdbcType=VARCHAR} CREATE_BY,
      sysdate CREATE_DATE,
      #{item.lastUpdatedBy,jdbcType=VARCHAR} LAST_UPDATED_BY,
      sysdate LAST_UPDATED_DATE,
      'Y' ENABLED_FLAG,
      #{item.orgId,jdbcType=DECIMAL} ORG_ID,
      #{item.factoryId,jdbcType=DECIMAL} FACTORY_ID,
      #{item.entityId,jdbcType=DECIMAL}ENTITY_ID,
      #{item.attribute1,jdbcType=VARCHAR} ATTRIBUTE1,
      #{item.attribute2,jdbcType=VARCHAR} ATTRIBUTE2,
      #{item.attribute3,jdbcType=VARCHAR} ATTRIBUTE3,
      #{item.attribute4,jdbcType=VARCHAR} ATTRIBUTE4,
      #{item.attribute5,jdbcType=TIMESTAMP}::TIMESTAMP ATTRIBUTE5,
      #{item.lastProcess,jdbcType=VARCHAR} LAST_PROCESS,
      #{item.sourceSys,jdbcType=VARCHAR} SOURCE_SYS,
      #{item.nextProcess,jdbcType=VARCHAR} NEXT_PROCESS,
      #{item.sourceImu,jdbcType=DECIMAL} SOURCE_IMU,
      #{item.sourceBimu,jdbcType=DECIMAL} SOURCE_BIMU,
      #{item.colName,jdbcType=VARCHAR} COL_NAME,
      #{item.sourceSysName,jdbcType=VARCHAR} SOURCE_SYS_NAME,
      #{item.fixId,jdbcType=VARCHAR} FIX_ID,
      #{item.stationName,jdbcType=VARCHAR} STATION_NAME,
      #{item.originalTask,jdbcType=VARCHAR} ORIGINAL_TASK
    </foreach>
    ) a
  </insert>

  <!-- 获取符合条件的记录列表 -->
  <select id="getWorkOrderNo" parameterType="java.util.Map" resultMap="PsWipInfoDTOMap">
    select count(1) total,
    t.factory_id,
    t.item_no,
    t.item_name,
    t.attribute1
    from wip_info t
    where t.LAST_UPDATED_DATE > sysdate - (cast(#{recentDay} as numeric ) * 24 || ' hour')::interval and t.curr_process_code = #{processCode}
    and not exists(select 1 from warehousehm_entry_detail line where t.sn=line.sn and line.enabled_flag='Y')
    and t.sn not in
    (select d.sn from WAREHOUSE_ENTRY_INFO h, WAREHOUSE_ENTRY_DETAIL d
    where h.bill_no = d.bill_no
    and h.enabled_flag = 'Y' and d.enabled_flag = 'Y'
    and d.sn is not null
    <!-- 入库单明细状态为 3:已拒绝 时当做未入库, 单据类型为 6:转交单;7:转交扫货单;9:单板返修入库单 时不算已提交入库 -->
    and d.status not in ('3') and h.bill_type not in ('6','7','9'))
    and t.enabled_flag='Y'
    and length(t.item_no)=15
    group by t.attribute1, t.factory_id, t.item_no, t.item_name
  </select>

  <!-- 批量更新在制信息 -->
  <update id="updatePsWipInfoByScanBatch" parameterType="java.util.List">
    <foreach collection="list" separator=";" item="item" >
      update WIP_INFO T
      <set>
        <if test="item.sn != null">
          T.SN = #{item.sn,jdbcType=VARCHAR},
        </if>
        <if test="item.workOrderNo != null">
          T.WORK_ORDER_NO = #{item.workOrderNo,jdbcType=VARCHAR},
        </if>
        <if test="item.itemNo != null">
          T.ITEM_NO = #{item.itemNo,jdbcType=VARCHAR},
        </if>
        <if test="item.itemName != null">
          T.ITEM_NAME = #{item.itemName,jdbcType=VARCHAR},
        </if>
        <if test="item.currProcessCode != null">
          T.CURR_PROCESS_CODE = #{item.currProcessCode,jdbcType=VARCHAR},
        </if>
        <if test="item.status != null">
          T.STATUS = #{item.status,jdbcType=VARCHAR},
        </if>
        <if test="item.worker != null">
          T.WORKER = #{item.worker,jdbcType=VARCHAR},
        </if>
        <if test="item.inTime != null">
          T.IN_TIME = #{item.inTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.outTime != null">
          T.OUT_TIME = #{item.outTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.parentSn != null">
          T.PARENT_SN = #{item.parentSn,jdbcType=VARCHAR},
        </if>
        <if test="item.errorCode != null">
          T.ERROR_CODE = #{item.errorCode,jdbcType=VARCHAR},
        </if>
        <if test="item.lineCode != null">
          T.LINE_CODE = #{item.lineCode,jdbcType=VARCHAR},
        </if>
        <if test="item.timeSpan != null">
          T.TIME_SPAN = #{item.timeSpan,jdbcType=TIMESTAMP},
        </if>
        <if test="item.workshopCode != null">
          T.WORKSHOP_CODE = #{item.workshopCode,jdbcType=VARCHAR},
        </if>
        <if test="item.opeTimes != null">
          T.OPE_TIMES = #{item.opeTimes,jdbcType=DECIMAL},
        </if>
        <if test="item.craftSection != null">
          T.CRAFT_SECTION = #{item.craftSection,jdbcType=VARCHAR},
        </if>
        <if test="item.workStation != null">
          T.WORK_STATION = #{item.workStation,jdbcType=VARCHAR},
        </if>
        <if test="item.lastProcess != null">
          T.LAST_PROCESS = #{item.lastProcess,jdbcType=VARCHAR},
        </if>
        <if test="item.sourceSys != null">
          T.SOURCE_SYS = #{item.sourceSys,jdbcType=VARCHAR},
        </if>
        <if test="item.nextProcess != null">
          T.NEXT_PROCESS = #{item.nextProcess,jdbcType=VARCHAR},
        </if>
        <if test="item.remark != null">
          T.REMARK = #{item.remark,jdbcType=VARCHAR},
        </if>
        <if test="item.createBy != null">
          T.CREATE_BY = #{item.createBy,jdbcType=VARCHAR},
        </if>
        <if test="item.createDate != null">
          T.CREATE_DATE = #{item.createDate,jdbcType=TIMESTAMP},
        </if>
        <if test="item.lastUpdatedBy != null">
          T.LAST_UPDATED_BY = #{item.lastUpdatedBy,jdbcType=VARCHAR},
        </if>
        <if test="item.enabledFlag != null">
          T.ENABLED_FLAG = #{item.enabledFlag,jdbcType=VARCHAR},
        </if>
        <if test="item.orgId != null">
          T.ORG_ID = #{item.orgId,jdbcType=DECIMAL},
        </if>
        <if test="item.factoryId != null">
          T.FACTORY_ID = #{item.factoryId,jdbcType=DECIMAL},
        </if>
        <if test="item.entityId != null">
          T.ENTITY_ID = #{item.entityId,jdbcType=DECIMAL},
        </if>
        <if test="item.sourceImu != null">
          T.SOURCE_IMU = #{item.sourceImu,jdbcType=DECIMAL},
        </if>
        <if test="item.sourceBimu != null">
          T.SOURCE_BIMU = #{item.sourceBimu,jdbcType=DECIMAL},
        </if>
        <if test="item.colName != null">
          T.COL_NAME = #{item.colName,jdbcType=VARCHAR},
        </if>
        <if test="item.sourceSysName != null">
          T.SOURCE_SYS_NAME = #{item.sourceSysName,jdbcType=VARCHAR},
        </if>
        <if test="item.fixId != null">
          T.FIX_ID = #{item.fixId,jdbcType=VARCHAR},
        </if>
        <if test="item.stationName != null">
          T.STATION_NAME = #{item.stationName,jdbcType=VARCHAR},
        </if>
        T.LAST_UPDATED_DATE = SYSDATE
      </set>
      where
      T.ENABLED_FLAG = 'Y'
      <if test="item.parentSn != null and item.parentSn != ''">
        and T.PARENT_SN = #{item.parentSn,jdbcType=VARCHAR}
      </if>
      <if test="item.sn != null and item.sn != ''">
        and T.SN = #{item.sn,jdbcType=VARCHAR}
      </if>
      <if test="(item.sn == null or item.sn == '') and (item.parentSn == null or item.parentSn == '')  ">and 1 =
        2
      </if>

    </foreach>
  </update>
  <!-- 批量更新在制父sn -->
  <update id="updatePsWipInfoBySnBatch" parameterType="java.util.List">
    <foreach collection="list" separator=";" item="item" >
      update WIP_INFO T
      <set>
        <if test="item.itemNo != null">
          T.ITEM_NO = #{item.itemNo,jdbcType=VARCHAR},
        </if>
        <if test="item.itemName != null">
          T.ITEM_NAME = #{item.itemName,jdbcType=VARCHAR},
        </if>
        <if test="item.currProcessCode != null">
          T.CURR_PROCESS_CODE = #{item.currProcessCode,jdbcType=VARCHAR},
        </if>
        <if test="item.status != null">
          T.STATUS = #{item.status,jdbcType=VARCHAR},
        </if>
        <if test="item.worker != null">
          T.WORKER = #{item.worker,jdbcType=VARCHAR},
        </if>
        <if test="item.inTime != null">
          T.IN_TIME = #{item.inTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.outTime != null">
          T.OUT_TIME = #{item.outTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.parentSn != null">
          T.PARENT_SN = #{item.parentSn,jdbcType=VARCHAR},
        </if>
        <if test="item.errorCode != null">
          T.ERROR_CODE = #{item.errorCode,jdbcType=VARCHAR},
        </if>
        <if test="item.lineCode != null">
          T.LINE_CODE = #{item.lineCode,jdbcType=VARCHAR},
        </if>
        <if test="item.timeSpan != null">
          T.TIME_SPAN = #{item.timeSpan,jdbcType=TIMESTAMP},
        </if>
        <if test="item.workshopCode != null">
          T.WORKSHOP_CODE = #{item.workshopCode,jdbcType=VARCHAR},
        </if>
        <if test="item.opeTimes != null">
          T.OPE_TIMES = #{item.opeTimes,jdbcType=DECIMAL},
        </if>
        <if test="item.craftSection != null">
          T.CRAFT_SECTION = #{item.craftSection,jdbcType=VARCHAR},
        </if>
        <if test="item.workStation != null">
          T.WORK_STATION = #{item.workStation,jdbcType=VARCHAR},
        </if>
        <if test="item.lastProcess != null">
          T.LAST_PROCESS = #{item.lastProcess,jdbcType=VARCHAR},
        </if>
        <if test="item.sourceSys != null">
          T.SOURCE_SYS = #{item.sourceSys,jdbcType=VARCHAR},
        </if>
        <if test="item.nextProcess != null">
          T.NEXT_PROCESS = #{item.nextProcess,jdbcType=VARCHAR},
        </if>
        <if test="item.remark != null">
          T.REMARK = #{item.remark,jdbcType=VARCHAR},
        </if>
        <if test="item.createBy != null">
          T.CREATE_BY = #{item.createBy,jdbcType=VARCHAR},
        </if>
        <if test="item.createDate != null">
          T.CREATE_DATE = #{item.createDate,jdbcType=TIMESTAMP},
        </if>
        <if test="item.lastUpdatedBy != null">
          T.LAST_UPDATED_BY = #{item.lastUpdatedBy,jdbcType=VARCHAR},
        </if>
        <if test="item.enabledFlag != null">
          T.ENABLED_FLAG = #{item.enabledFlag,jdbcType=VARCHAR},
        </if>
        <if test="item.sourceImu != null">
          T.SOURCE_IMU = #{item.sourceImu,jdbcType=DECIMAL},
        </if>
        <if test="item.sourceBimu != null">
          T.SOURCE_BIMU = #{item.sourceBimu,jdbcType=DECIMAL},
        </if>
        <if test="item.colName != null">
          T.COL_NAME = #{item.colName,jdbcType=VARCHAR},
        </if>
        <if test="item.sourceSysName != null">
          T.SOURCE_SYS_NAME = #{item.sourceSysName,jdbcType=VARCHAR},
        </if>
        <if test="item.fixId != null">
          T.FIX_ID = #{item.fixId,jdbcType=VARCHAR},
        </if>
        <if test="item.stationName != null">
          T.STATION_NAME = #{item.stationName,jdbcType=VARCHAR},
        </if>
        <if test="item.lastUpdatedDate != null">
          T.LAST_UPDATED_DATE = #{item.lastUpdatedDate,jdbcType=TIMESTAMP}::TIMESTAMP
        </if>
        <if test="item.lastUpdatedDate == null">
          T.LAST_UPDATED_DATE = SYSDATE
        </if>
      </set>
      where
      T.ENABLED_FLAG = 'Y'
      and T.SN = #{item.sn,jdbcType=VARCHAR}
      <if test="item.notInProcessList != null and item.notInProcessList.size > 0">
        and T.CURR_PROCESS_CODE not in
        <foreach item="processCode" collection="item.notInProcessList" open="(" separator="," close=")">
          #{processCode}
        </foreach>
      </if>
    </foreach>
  </update>

  <!--  工序回退-根据扫描历史更新条码表 -->
  <update id="updateWipInfoByHistory" parameterType="java.util.List">
    <foreach collection="list" separator=";" item="item" >
      update WIP_INFO T
      <set>
        T.CRAFT_SECTION = #{item.craftSection,jdbcType=VARCHAR},
        T.CURR_PROCESS_CODE = #{item.currProcessCode,jdbcType=VARCHAR},
        T.ITEM_NO = #{item.itemNo,jdbcType=VARCHAR},
        T.LAST_PROCESS = #{item.lastProcess,jdbcType=VARCHAR},
        T.LINE_CODE = #{item.lineCode,jdbcType=VARCHAR},

        T.NEXT_PROCESS = #{item.nextProcess,jdbcType=VARCHAR},
        T.SN = #{item.sn,jdbcType=VARCHAR},
        T.SOURCE_IMU = #{item.sourceImu,jdbcType=DECIMAL},
        T.SOURCE_SYS_NAME = #{item.sourceSysName,jdbcType=VARCHAR},
        T.STATION_NAME = #{item.stationName,jdbcType=VARCHAR},

        T.WORK_ORDER_NO = #{item.workOrderNo,jdbcType=VARCHAR},
        T.WORK_STATION = #{item.workStation,jdbcType=VARCHAR},
        T.LAST_UPDATED_BY = #{item.lastUpdatedBy,jdbcType=VARCHAR},
        T.LAST_UPDATED_DATE = SYSDATE
      </set>
      where
      T.ENABLED_FLAG = 'Y'  and T.SN = #{item.sn,jdbcType=VARCHAR}
    </foreach>
  </update>

  <update id="updateAssembleFlagBySns" parameterType="java.util.Set">
    update WIP_INFO
    set assemble_flag = 'N',
    last_updated_date = sysdate
    where sn in
    <foreach item="item" collection="sns" open="(" separator="," close=")">
      #{item}
    </foreach>
  </update>

  <update id="updateSecAssembleFlagBySns" parameterType="java.util.Set">
    update WIP_INFO
    set sec_assemble_flag = 'N',
    last_updated_date = sysdate
    where sn in
    <foreach item="item" collection="sns" open="(" separator="," close=")">
      #{item}
    </foreach>
  </update>


  <select id="getSnBySourceTask" parameterType="java.lang.String" resultType="java.lang.String">
    select sn from wip_info t where t.attribute1 = #{sourceTask}
  </select>

    <select id="getCurrProcessCodeBySourceTask" resultMap="WipCraftQytMap">
        select t.attribute1 prod_plan_id, count(1) qty from wip_info t where t.attribute1 in
        <foreach item="item" index="index" collection="prodplanIds"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="contorlProcess != null and contorlProcess.size > 0">
            and t.curr_process_code in
            <foreach item="item" index="index" collection="contorlProcess"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        and t.ENABLED_FLAG = 'Y'
        group by t.attribute1
    </select>


  <select id="getCountByWorkOrderNoList" parameterType="java.util.Map" resultType="java.lang.Long">
    select count(1)
    from WIP_INFO t
    <if test="dipFinishFlag != null and dipFinishFlag == 'Y'.toString()">
      join wip_scan_history wsc on wsc.sn=t.sn and  wsc.work_order_no=t.work_order_no  and wsc.dip_finish_flag =#{dipFinishFlag} and wsc.ENABLED_FLAG = 'Y'
    </if>
    where t.ENABLED_FLAG = 'Y'
    <!-- 排除已入库条码-->
    <if test="excludeStockSn != null and excludeStockSn == 'Y'.toString()">
      and t.sn not in
      (select d.sn from WAREHOUSE_ENTRY_INFO h, WAREHOUSE_ENTRY_DETAIL d
      where h.bill_no = d.bill_no
      and h.enabled_flag = 'Y' and d.enabled_flag = 'Y'
      and d.sn is not null
      <!-- 入库单明细状态为 3:已拒绝 时当做未入库, 单据类型为 6:转交单;7:转交扫货单;9:单板返修入库单 时不算已提交入库 -->
      <if test="isReturn == '0' or RR == 'N'.toString()">
        and d.status not in ('3') and h.bill_type not in ('6','7','9')
      </if>
      <!-- 如果为返修单,入库单头状态为1:已确认:2已拒绝 时可再次提交入库单-->
      <if test="isReturn == '1' or RR == 'Y'.toString()">
        and h.status not in ('1','2')
      </if>
      <if test="workOrderNo != null and workOrderNo != ''">
        and d.WORK_ORDER_NO in ( ${workOrderNo} )
      </if>
      <if test="lastWorkOrderProcess != null and lastWorkOrderProcess.size > 0">
        and d.prodplan_id in
        <foreach item="value" index="key" collection="lastWorkOrderProcess" open="(" separator="," close=")">
          #{key}
        </foreach>
      </if>
      <!-- Started by AICoder, pid:7cda5cd794u605114597095990ae8102a0160eb8 -->
      <if test="(workOrderNo == null or workOrderNo == '')
                and (lastWorkOrderProcess == null or lastWorkOrderProcess.size() == 0)">
        and 1=2
      </if>
      <!-- Ended by AICoder, pid:7cda5cd794u605114597095990ae8102a0160eb8 -->
      )
    </if>
    <if test="scanSnList != null and scanSnList.size() &lt;= 500 and scanSnList.size() >0 ">
      and not exists (SELECT *
      FROM (
      <foreach collection = "scanSnList" item = "notExistSn" index = "index" separator = "UNION ALL" >
        SELECT #{notExistSn} AS sn
      </foreach>
      ) tmp
      WHERE tmp.sn = t.sn)
    </if>
    <if test="scanSnList != null and scanSnList.size() > 500 ">
      and not exists (SELECT 1
      FROM temporary_storage_barcode_table tmp
      WHERE tmp.sn = t.sn)
    </if>

    and t.LAST_PROCESS = 'Y'
    and t.WORK_STATION <![CDATA[ <> ]]> '0'
    <if test="currProcessCode != null and currProcessCode != ''">
      and t.CURR_PROCESS_CODE = #{currProcessCode}
    </if>
    <if test="processCodeList != null and processCodeList.size > 0">
      and t.CURR_PROCESS_CODE in
      <foreach item="item" index="index" collection="processCodeList" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="workOrderNo != null and workOrderNo != ''">
      and t.WORK_ORDER_NO in ( ${workOrderNo} )
    </if>
    <if test="itemNo != null and itemNo != ''">
      and t.ITEM_NO = #{itemNo}
    </if>
    <if test="taskNo != null and taskNo != ''">
      and t.ATTRIBUTE2 = #{taskNo}
    </if>
    <if test="snList != null and snList != ''">
      and t.SN IN (${snList})
    </if>
    <if test="notInTask != null and notInTask.size > 0">and t.ATTRIBUTE2 NOT IN
      <foreach item="taskNo" index="index" collection="notInTask" open="(" separator="," close=")">
        #{taskNo}
      </foreach>
    </if>
    <if test="lastWorkOrderProcess != null and lastWorkOrderProcess.size > 0">and exists
      (
      select 1 from (
      <foreach item="value" index="key" collection="lastWorkOrderProcess" separator=" union  all ">
        SELECT #{key} prodplan_id,#{value} CURR_PROCESS_CODE
      </foreach>
      )a where a.prodplan_id = t.attribute1 and a.CURR_PROCESS_CODE = t.CURR_PROCESS_CODE
      )
    </if>
    <!-- 高温工序标识 -->
    <if test="highTempFlag != null and highTempFlag == true">
      and not exists (SELECT 1
      FROM high_temp_sn_info h
      WHERE h.sn = t.sn
      and h.enabled_flag = 'Y'
      and t.curr_process_code != #{highProcessCode})
    </if>
    <if test="changeConfigurationList != null and changeConfigurationList.size > 0">
      and t.sn IN
      <foreach item="item" index="index" collection="changeConfigurationList" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="changeConfigurationFlag != null and changeConfigurationFlag == true and
              (changeConfigurationList == null or changeConfigurationList.size == 0)">
      and 1=2
    </if>
    <!-- Started by AICoder, pid:g4f15ld87drefcd14c320a80c0885f05d658cd76 -->
    <if test="(workOrderNo == null or workOrderNo == '') and (itemNo == null or itemNo == '')
              and (taskNo == null or taskNo == '') and (snList == null or snList.size() == 0)
              and (lastWorkOrderProcess == null or lastWorkOrderProcess.size() == 0)">
      and 1=2
    </if>
    <!-- Ended by AICoder, pid:g4f15ld87drefcd14c320a80c0885f05d658cd76 -->
  </select>

  <select id="getSnListByWorkOrder" parameterType="java.util.Map" resultType="java.lang.String">
    select t.SN
    from WIP_INFO t
    <if test="dipFinishFlag != null and dipFinishFlag == 'Y'.toString()">
      join wip_scan_history wsc on wsc.sn=t.sn and  wsc.work_order_no=t.work_order_no  and wsc.dip_finish_flag =#{dipFinishFlag} and wsc.ENABLED_FLAG = 'Y'
    </if>
    where t.ENABLED_FLAG = 'Y'
    <!-- 排除已入库条码-->
    <if test="excludeStockSn != null and excludeStockSn == 'Y'.toString()">
      and t.sn not in
      (select d.sn from WAREHOUSE_ENTRY_INFO h, WAREHOUSE_ENTRY_DETAIL d
      where h.bill_no = d.bill_no
      and h.enabled_flag = 'Y' and d.enabled_flag = 'Y'
      and d.sn is not null
      <!-- 入库单明细状态为 3:已拒绝 时当做未入库, 单据类型为 6:转交单;7:转交扫货单;9:单板返修入库单 时不算已提交入库 -->
      <if test="isReturn == '0' or RR == 'N'.toString()">
        and d.status not in ('3') and h.bill_type not in ('6','7','9')
      </if>
      <!-- 如果为返修单,入库单头状态为1:已确认:2已拒绝 时可再次提交入库单-->
      <if test="isReturn == '1' or RR == 'Y'.toString()">
        and h.status not in ('1','2')
      </if>
      <if test="workOrderNo != null and workOrderNo != ''">
        and d.WORK_ORDER_NO in ( ${workOrderNo} )
      </if>
      <if test="lastWorkOrderProcess != null and lastWorkOrderProcess.size > 0">
        and d.prodplan_id in
        <foreach item="value" index="key" collection="lastWorkOrderProcess" open="(" separator="," close=")">
          #{key}
        </foreach>
      </if>
      <if test="(workOrderNo == null or workOrderNo == '')
                and (lastWorkOrderProcess == null or lastWorkOrderProcess.size() == 0)">
        and 1=2
      </if>
      )
    </if>
    <if test="scanSnList != null and scanSnList.size>0 ">
      and not exists (SELECT *
      FROM (
      <foreach collection = "scanSnList" item = "notExistSn" index = "index" separator = "UNION ALL" >
        SELECT #{notExistSn} AS sn
      </foreach>
      ) tmp
      WHERE tmp.sn = t.sn)
    </if>
    and t.LAST_PROCESS = 'Y'
    and t.WORK_STATION <![CDATA[ <> ]]> '0'
    <if test="currProcessCode != null and currProcessCode != ''">
      and t.CURR_PROCESS_CODE = #{currProcessCode}
    </if>
    <if test="processCodeList != null and processCodeList.size > 0">
      and t.CURR_PROCESS_CODE in
      <foreach item="item" index="index" collection="processCodeList" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="workOrderNo != null and workOrderNo != ''">
      and t.WORK_ORDER_NO in ( ${workOrderNo} )
    </if>
    <if test="itemNo != null and itemNo != ''">
      and t.ITEM_NO = #{itemNo}
    </if>
    <if test="taskNo != null and taskNo != ''">
      and t.ATTRIBUTE2 = #{taskNo}
    </if>
    <if test="snList != null and snList != ''">
      and t.SN IN (${snList})
    </if>
    <if test="notInTask != null and notInTask.size > 0">and t.ATTRIBUTE2 NOT IN
      <foreach item="taskNo" index="index" collection="notInTask" open="(" separator="," close=")">
        #{taskNo}
      </foreach>
    </if>
    <if test="lastWorkOrderProcess != null and lastWorkOrderProcess.size > 0">and exists
      (
      select 1 from (
      <foreach item="value" index="key" collection="lastWorkOrderProcess" separator=" union  all ">
        SELECT #{key} prodplan_id,#{value} CURR_PROCESS_CODE
      </foreach>
      )a where a.prodplan_id = t.attribute1 and a.CURR_PROCESS_CODE = t.CURR_PROCESS_CODE
      )
    </if>
    <!-- 高温工序标识 -->
    <if test="highTempFlag != null and highTempFlag == true">
      and not exists (SELECT 1
      FROM high_temp_sn_info h
      WHERE h.sn = t.sn
      and h.enabled_flag = 'Y'
      and t.curr_process_code != #{highProcessCode})
    </if>
    <if test="changeConfigurationList != null and changeConfigurationList.size > 0">
      and t.sn IN
      <foreach item="item" index="index" collection="changeConfigurationList" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="changeConfigurationFlag != null and changeConfigurationFlag == true and
              (changeConfigurationList == null or changeConfigurationList.size == 0)">
      and 1=2
    </if>
    <!-- Started by AICoder, pid:g4f15ld87drefcd14c320a80c0885f05d658cd76 -->
    <if test="(workOrderNo == null or workOrderNo == '') and (itemNo == null or itemNo == '')
              and (taskNo == null or taskNo == '') and (snList == null or snList.size() == 0)
              and (lastWorkOrderProcess == null or lastWorkOrderProcess.size() == 0)">
      and 1=2
    </if>
    <!-- Ended by AICoder, pid:g4f15ld87drefcd14c320a80c0885f05d658cd76 -->
  </select>

  <!-- 滞留未入库清单 -->
  <select id="getPageByWorkOrderNoList" parameterType="java.util.Map" resultMap="BaseResultMap">
    select T.*
    from WIP_INFO t
    <if test="dipFinishFlag != null and dipFinishFlag == 'Y'.toString()">
      join wip_scan_history wsc on wsc.sn=t.sn and  wsc.work_order_no=t.work_order_no  and wsc.dip_finish_flag =#{dipFinishFlag} and wsc.ENABLED_FLAG = 'Y'
    </if>
    where t.ENABLED_FLAG = 'Y'
    <!-- 排除已入库条码-->
    <if test="excludeStockSn != null and excludeStockSn == 'Y'.toString()">
      and t.sn not in
      (select d.sn from WAREHOUSE_ENTRY_INFO h, WAREHOUSE_ENTRY_DETAIL d
      where h.bill_no = d.bill_no
      and h.enabled_flag = 'Y' and d.enabled_flag = 'Y'
      and d.sn is not null
      <!-- 入库单明细状态为 3:已拒绝 时当做未入库, 单据类型为 6:转交单;7:转交扫货单;9:单板返修入库单 时不算已提交入库 -->
      <if test="isReturn == '0' or RR == 'N'.toString()">
        and d.status not in ('3') and h.bill_type not in ('6','7','9')
      </if>
      <!-- 如果为返修单,入库单头状态为1:已确认:2已拒绝 时可再次提交入库单-->
      <if test="isReturn == '1' or RR == 'Y'.toString()">
        and h.status not in ('1','2')
      </if>
      <if test="workOrderNo != null and workOrderNo != ''">
        and d.WORK_ORDER_NO in ( ${workOrderNo} )
      </if>
      <if test="lastWorkOrderProcess != null and lastWorkOrderProcess.size > 0">
        and d.prodplan_id in
        <foreach item="value" index="key" collection="lastWorkOrderProcess" open="(" separator="," close=")">
          #{key}
        </foreach>
      </if>
      <!-- Started by AICoder, pid:7cda5cd794u605114597095990ae8102a0160eb8 -->
      <if test="(workOrderNo == null or workOrderNo == '')
                and (lastWorkOrderProcess == null or lastWorkOrderProcess.size() == 0)">
        and 1=2
      </if>
      <!-- Ended by AICoder, pid:7cda5cd794u605114597095990ae8102a0160eb8 -->
      )
    </if>
    and t.LAST_PROCESS = 'Y'
    and t.WORK_STATION <![CDATA[ <> ]]> '0'
    <if test="currProcessCode != null and currProcessCode != ''">
      and t.CURR_PROCESS_CODE = #{currProcessCode}
    </if>
    <if test="processCodeList != null and processCodeList.size > 0">
      and t.CURR_PROCESS_CODE in
      <foreach item="item" index="index" collection="processCodeList" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="workOrderNo != null and workOrderNo != ''">
      and t.WORK_ORDER_NO in ( ${workOrderNo} )
    </if>
    <if test="itemNo != null and itemNo != ''">
      and t.ITEM_NO = #{itemNo}
    </if>
    <if test="taskNo != null and taskNo != ''">
      and t.ATTRIBUTE2 = #{taskNo}
    </if>
    <if test="snList != null and snList != ''">
      and t.SN IN (${snList})
    </if>
    <if test="scanSnList != null and scanSnList.size() &lt;= 500 and scanSnList.size() >0 ">
      and not exists (SELECT *
      FROM (
      <foreach collection = "scanSnList" item = "notExistSn" index = "index" separator = "UNION ALL" >
        SELECT #{notExistSn} AS sn
      </foreach>
      ) tmp
      WHERE tmp.sn = t.sn)
    </if>
    <!-- scanSnList数据量大于500时,关联临时表进行查询(调用前会创建临时表并插入数据),提升性能 -->
    <if test="scanSnList != null and scanSnList.size() > 500 ">
      and not exists (SELECT 1
      FROM temporary_storage_barcode_table tmp
      WHERE tmp.sn = t.sn)
    </if>
    <if test="notInTask != null and notInTask.size > 0">and t.ATTRIBUTE2 NOT IN
      <foreach item="taskNo" index="index" collection="notInTask" open="(" separator="," close=")">
        #{taskNo}
      </foreach>
    </if>
    <if test="lastWorkOrderProcess != null and lastWorkOrderProcess.size > 0">and exists
      (
      select 1 from (
      <foreach item="value" index="key" collection="lastWorkOrderProcess" separator=" union  all ">
        SELECT #{key} prodplan_id,#{value} CURR_PROCESS_CODE
      </foreach>
      )a where a.prodplan_id = t.attribute1 and a.CURR_PROCESS_CODE = t.CURR_PROCESS_CODE
      )
    </if>
    <!-- 高温工序标识 -->
    <if test="highTempFlag != null and highTempFlag == true">
      and not exists (SELECT 1
      FROM high_temp_sn_info h
      WHERE h.sn = t.sn
      and h.enabled_flag = 'Y'
      and t.curr_process_code != #{highProcessCode})
    </if>
    <if test="changeConfigurationList != null and changeConfigurationList.size > 0">
      and t.sn IN
      <foreach item="item" index="index" collection="changeConfigurationList" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="changeConfigurationFlag != null and changeConfigurationFlag == true and
              (changeConfigurationList == null or changeConfigurationList.size == 0)">
      and 1=2
    </if>
    <!-- Started by AICoder, pid:g4f15ld87drefcd14c320a80c0885f05d658cd76 -->
    <if test="(workOrderNo == null or workOrderNo == '') and (itemNo == null or itemNo == '')
              and (taskNo == null or taskNo == '') and (snList == null or snList.size() == 0)
              and (lastWorkOrderProcess == null or lastWorkOrderProcess.size() == 0)">
      and 1=2
    </if>
    <!-- Ended by AICoder, pid:g4f15ld87drefcd14c320a80c0885f05d658cd76 -->
    order by t.SN desc
    limit #{endRow}::numeric-#{startRow}::numeric+1 offset case when ( #{startRow}::numeric-1 &lt; 0) then 0 else  #{startRow}::numeric-1 end
  </select>

  <!-- 根据条码分页获取信息 -->
  <select id="getPagePsWipInfoBySnList" parameterType="java.util.Map" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/> from WIP_INFO t
    where 1=1
    and t.ENABLED_FLAG = 'Y'
    and  exists (SELECT *
    FROM (
    <foreach collection = "list" item = "item" index = "index" separator = "UNION" >
      SELECT #{item} AS sn
    </foreach>
    ) tmp
    WHERE tmp.sn = t.sn)
    limit #{endRow}::numeric-#{startRow}::numeric+1 offset case when ( #{startRow}::numeric-1 &lt; 0) then 0 else  #{startRow}::numeric-1 end
  </select>

  <!-- 待入库的任务 -->
  <select id="getWaitCommitTask" parameterType="java.util.Map" resultType="java.lang.String">
    select DISTINCT(T.ATTRIBUTE2)
    from WIP_INFO t
    left join (SELECT wed.sn, wei.status,wed.enabled_flag
    FROM WAREHOUSE_ENTRY_DETAIL wed
    left join WAREHOUSE_ENTRY_INFO wei on (wed.bill_no = wei.bill_no and wei.enabled_flag='Y')
    ) tt on (t.sn = tt.sn and tt.enabled_flag='Y')
    where t.ENABLED_FLAG = 'Y'
    and (tt.sn is null or tt.status = '2')
    <if test="currProcessCode != null and currProcessCode != ''">
      and t.CURR_PROCESS_CODE = #{currProcessCode}
    </if>
    <if test="itemNo != null and itemNo != ''">
      and t.ITEM_NO = #{itemNo}
    </if>
  </select>

  <!--根据一批条码查询在制表 1019-->
  <select id="getListByBatchSn" parameterType="java.util.List" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from WIP_INFO
    where ENABLED_FLAG = 'Y'
    and sn in
    <foreach item="item" index="index" collection="list"
             open="(" separator="," close=")">
      #{item}
    </foreach>
    order by sn ASC
  </select>
  <select id="getListByBatchParentSn" parameterType="java.util.List" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from WIP_INFO
    where ENABLED_FLAG = 'Y'
    and parent_sn in
    <foreach item="item" index="index" collection="list"
             open="(" separator="," close=")">
      #{item}
    </foreach>
    order by sn ASC
  </select>
  <!--根据一批条码查询在制表 1019-->
  <select id="getListByBatchSnByExist" parameterType="java.util.List" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from WIP_INFO t
    where ENABLED_FLAG = 'Y'
    and  exists (SELECT *
    FROM (
    <foreach collection = "list" item = "item" index = "index" separator = "UNION" >
      SELECT #{item} AS sn
    </foreach>
    ) tmp
    WHERE tmp.sn = t.sn)
  </select>

  <select id="getWipInfoBySn" parameterType="java.lang.String" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    from wip_info t
    where t.SN = #{sn} and t.ENABLED_FLAG='Y'
    limit 1
  </select>

  <select id="getDetailByParentSn" parameterType="java.lang.String" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    from wip_info t
    where t.PARENT_SN = #{sn} and t.ENABLED_FLAG='Y'
    order by CREATE_DATE desc
  </select>

  <select id="getWorkOrderNoEmptyWipInfoBySns" parameterType="java.util.List" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    from wip_info t
    where ENABLED_FLAG = 'Y' and (t.WORK_ORDER_NO is null or t.WORK_ORDER_NO = '' )  and  t.SN IN
    <foreach collection="snList" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>


  <select id="getWipInfoJoinTestBySn" parameterType="java.lang.String" resultMap="BaseTestMap">
    SELECT T.WIP_ID, T.SN, T.WORK_ORDER_NO, T.ITEM_NO, T.ITEM_NAME, T.CURR_PROCESS_CODE, T.STATUS,T.WORKER, T.IN_TIME,
    T.OUT_TIME, T.PARENT_SN, T.ERROR_CODE, T.LINE_CODE, T.TIME_SPAN,
    T.WORKSHOP_CODE,T.OPE_TIMES,T.CRAFT_SECTION, T.REMARK, T.CREATE_BY,
    T.CREATE_DATE, T.LAST_UPDATED_BY, T.LAST_UPDATED_DATE, T.ENABLED_FLAG,
    T.ORG_ID, T.FACTORY_ID, T.ENTITY_ID, T.ATTRIBUTE1, T.ATTRIBUTE2,
    T.ATTRIBUTE3, T.ATTRIBUTE4, T.ATTRIBUTE5,T.LAST_PROCESS,
    T.SOURCE_SYS,T.NEXT_PROCESS,T.SOURCE_IMU,T.SOURCE_BIMU,T.COL_NAME,
    T.SOURCE_SYS_NAME,T.WORK_STATION,F.PROCESS_CODE,F.SYMPTOM_CODE,
    F.SYMPTOM_NAME,F.TEST_VALUE
    FROM WIP_INFO T LEFT JOIN WIP_TEST_RECODE F ON T.SN=F.SN WHERE T.SN=#{sn} AND T.ENABLED_FLAG='Y'
    ORDER BY T.LAST_UPDATED_DATE DESC
  </select>

  <select id="getWorkOrderNoByCond" parameterType="java.util.Map" resultType="java.lang.String">
    select distinct(work_order_no) from wip_info t where 1=1
    <if test="attribute1 != null and attribute1 != ''">and t.attribute1 = #{attribute1}</if>
    <if test="currProcessCode != null and currProcessCode != ''">and t.CURR_PROCESS_CODE = #{currProcessCode}</if>
    <if test="workStation != null and workStation != ''">and t.WORK_STATION = #{workStation}</if>
    <if test="inSns != null and inSns != ''">and t.SN in (${inSns})</if>
    <if test="(attribute1 == null or attribute1 == '') and ( currProcessCode == null or currProcessCode == '') and
              (workStation == null or workStation == '') and  (inSns == null or inSns == '')"> and 1=2 </if>
  </select>

  <select id="getWipInfoByTaskNo" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from WIP_INFO
    where ENABLED_FLAG = 'Y'
    and ATTRIBUTE2 = #{taskNo, jdbcType=VARCHAR}
  </select>

  <select id="selectPsWipInfoBySn" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from WIP_INFO
    where SN = #{sn,jdbcType=VARCHAR}
    and ENABLED_FLAG = 'Y'
  </select>

  <select id="getGroupByAttribute1" parameterType="java.lang.String" resultMap="PsWipInfoDTOMap">
    SELECT COUNT(*) as GROUP_QTY,craft_section
    FROM WIP_INFO t where  t.attribute1 = #{attribute1} and ENABLED_FLAG = 'Y'  GROUP BY craft_section
  </select>

  <!-- 获取符合条件的记录列表 -->
  <select id="getWipInfoDTOList" parameterType="com.zte.interfaces.dto.PsWipInfoDTO" resultMap="PsWipInfoDTOMap">
    select * from WIP_INFO t
    where 1=1
    and t.ENABLED_FLAG = 'Y'
    <if test="parentSn != null and parentSn != ''">and t.PARENT_SN = #{parentSn}</if>
    <if test="wipId != null and wipId != ''">and t.WIP_ID = #{wipId}</if>
    <if test="sn != null and sn != ''">and t.SN = #{sn}</if>
    <if test="isPrint != null">and t.IS_PRINT = #{isPrint}::numeric</if>
    <if test="inSns != null and inSns != ''">and t.SN in (${inSns})</if>
    <if test="inWorkOrderNo != null and inWorkOrderNo != ''">and t.WORK_ORDER_NO IN (${inWorkOrderNo})</if>
    <if test="workOrderNo != null and workOrderNo != ''">and t.WORK_ORDER_NO = #{workOrderNo}</if>
    <if test="itemNo != null and itemNo != ''">and t.ITEM_NO = #{itemNo}</if>
    <if test="itemName != null and itemName != ''">and t.ITEM_NAME = #{itemName}</if>
    <if test="currProcessCode != null and currProcessCode != ''">and t.CURR_PROCESS_CODE = #{currProcessCode}</if>
    <if test="status != null and status != ''">and t.STATUS = #{status}</if>
    <if test="worker != null and worker != ''">and t.WORKER = #{worker}</if>
    <if test="lineCode != null and lineCode != ''">and t.LINE_CODE = #{lineCode}</if>
    <if test="workshopCode != null and workshopCode != ''">and t.WORKSHOP_CODE = #{workshopCode}</if>
    <if test="inTimeBegin != null and inTimeEnd != null">
      <![CDATA[and t.IN_TIME > #{inTimeBegin} and t.IN_TIME < #{inTimeEnd}]]></if>
    <if test="outTimeBegin != null and outTimeEnd != null">
      <![CDATA[and t.OUT_TIME > #{outTimeBegin}::timestamp and t.OUT_TIME < #{outTimeEnd}::timestamp]]></if>
    <if test="craftSection != null and craftSection != ''">and t.CRAFT_SECTION = #{craftSection}</if>
    <if test="workStation != null and workStation != ''">and t.WORK_STATION = #{workStation}</if>
    <if test="attribute1 != null and attribute1 != ''">and t.attribute1 = #{attribute1}</if>
    <if test="orgId != null">and t.ORG_ID = cast(#{orgId} as numeric)</if>
    <if test="snStart != null and snStart != '' and snEnd != null and snEnd != ''">and t.SN between #{snStart} and
      #{snEnd}
    </if>
    <if test="orgId == null and (parentSn == null or parentSn == '') and
        (wipId == null or wipId == '') and
        (sn == null or sn == '') and
        (isPrint == null or isPrint == '') and
        (inSns == null or inSns == '') and
        (inWorkOrderNo == null or inWorkOrderNo == '') and
        (workOrderNo == null or workOrderNo == '') and
        (itemNo == null or itemNo == '') and
        (itemName == null or itemName == '') and
        (currProcessCode == null or currProcessCode == '') and
        (status == null or status == '') and
        (worker == null or worker == '') and
        (lineCode == null or lineCode == '') and
        (workshopCode == null or workshopCode == '') and
        (inTimeBegin == null or inTimeBegin == '') and
        (outTimeBegin == null or outTimeBegin == '') and
        (craftSection == null or craftSection == '') and
        (workStation == null or workStation == '') and
        (attribute1 == null or attribute1 == '') and
        (snStart == null or snStart == '' or snEnd == null or snEnd == '') ">
      and 1=2
    </if>


    order by t.sn ASC
  </select>

  <select id="getListByBatchSnList" parameterType="java.util.List" resultMap="PsWipInfoDTOMap">
    select *
    from WIP_INFO
    where ENABLED_FLAG = 'Y'
    and sn in
    <foreach item="item" index="index" collection="list"
             open="(" separator="," close=")">
      #{item}
    </foreach>
    order by sn ASC
  </select>

  <insert id="batchInsertTaskBarcode" parameterType="com.zte.interfaces.dto.TaskBarcodeQueryDTO">
    <include refid="wipRec"/>
    insert into WIP_INFO (WIP_ID, SN, ITEM_NO, ITEM_NAME, CURR_PROCESS_CODE, CRAFT_SECTION,
    CREATE_BY, CREATE_DATE, LAST_UPDATED_BY, LAST_UPDATED_DATE, ENABLED_FLAG,
    ORG_ID, FACTORY_ID, ATTRIBUTE2, WORK_STATION, LAST_PROCESS, ATTRIBUTE3)
    select
    gen_random_uuid() WIP_ID, SN, #{itemNo,jdbcType=VARCHAR} ITEM_CODE,
    #{itemName,jdbcType=VARCHAR} ITEM_NAME, #{currProcessCode,jdbcType=VARCHAR} CURR_PROCESS_CODE,
    #{craftSection,jdbcType=VARCHAR} CRAFT_SECTION, #{createBy,jdbcType=VARCHAR} CREATED_BY, sysdate CREATE_DATE,
    #{lastUpdatedBy,jdbcType=VARCHAR} LAST_UPDATED_BY, sysdate LAST_UPDATE_DATE, 'Y' ENABLED_FLAG,
    #{orgId,jdbcType=DECIMAL} ORG_ID,  #{factoryId,jdbcType=DECIMAL} FACTORY_ID, #{attribute2,jdbcType=VARCHAR} ATTRIBUTE2,
    '0' WORK_STATION, 'Y' LAST_PROCESS, #{isLead,jdbcType=VARCHAR} ATTRIBUTE3
    from rec
  </insert>

  <insert id="batchInsertScanHistory" parameterType="com.zte.interfaces.dto.TaskBarcodeQueryDTO">
    <include refid="wipRec"/>
    insert into WIP_SCAN_HISTORY (SMT_SCAN_ID, SN, ITEM_NO, ITEM_NAME, CURR_PROCESS_CODE, CRAFT_SECTION,
    CREATE_BY, CREATE_DATE, LAST_UPDATED_BY, LAST_UPDATED_DATE, ENABLED_FLAG,
    ORG_ID, FACTORY_ID, ATTRIBUTE2, WORK_STATION, LAST_PROCESS, ATTRIBUTE3)
    select
    gen_random_uuid() SMT_SCAN_ID, SN, #{itemNo,jdbcType=VARCHAR} ITEM_CODE,
    #{itemName,jdbcType=VARCHAR} ITEM_NAME, #{currProcessCode,jdbcType=VARCHAR} CURR_PROCESS_CODE,
    #{craftSection,jdbcType=VARCHAR} CRAFT_SECTION, #{createBy,jdbcType=VARCHAR} CREATED_BY, sysdate CREATE_DATE,
    #{lastUpdatedBy,jdbcType=VARCHAR} LAST_UPDATED_BY, sysdate LAST_UPDATE_DATE, 'Y' ENABLED_FLAG,
    #{orgId,jdbcType=DECIMAL} ORG_ID,  #{factoryId,jdbcType=DECIMAL} FACTORY_ID, #{attribute2,jdbcType=VARCHAR} ATTRIBUTE2,
    '0' WORK_STATION, 'Y' LAST_PROCESS, #{isLead,jdbcType=VARCHAR} ATTRIBUTE3
    from rec
  </insert>

  <sql id="wipRec">
    with recursive rec as (
    select #{curValue,jdbcType=DECIMAL} as sn
    union all
    select (a.sn + 1) as sn from rec a
    where sn <![CDATA[<]]> #{curValue,jdbcType=DECIMAL} + #{registerQty,jdbcType=DECIMAL} - 1
    )
  </sql>

  <sql id="baseFromSql">
    from WIP_INFO t
    where t.ENABLED_FLAG = 'Y'
    <if test="taskNo != null and taskNo != ''">and t.attribute2 = #{taskNo}</if>
    <if test="workOrderNo != null and workOrderNo != ''">and t.work_order_no = #{workOrderNo}</if>
    <if test="factoryId != null and factoryId != ''">and t.factory_id = cast(#{factoryId} as numeric)</if>
    <if test="(taskNo == null or taskNo == '') and (workOrderNo == null or workOrderNo == '')  ">and 1 = 2</if>
  </sql>

  <!-- 翻页函数:获取一页的记录集 -->
  <select id="getSnList" parameterType="java.util.Map" resultType="java.util.Map">
    select sn,attribute3 lead
    <include refid="baseFromSql"/>
    <![CDATA[ and ROWNUM<=#{maxCnt}::numeric ]]>
  </select>

  <select id="getItemNo" resultType="java.lang.String">
    select t.ITEM_NO from WIP_INFO t
    where t.ENABLED_FLAG = 'Y' and rownum = 1
    <if test="taskNo != null and taskNo != ''">and t.attribute2 = #{taskNo}</if>
    <if test="workOrderNo != null and workOrderNo != ''">and t.work_order_no = #{workOrderNo}</if>
    <if test="factoryId != null and factoryId != ''">and t.factory_id = cast(#{factoryId} as numeric)</if>
    <if test="(taskNo == null or taskNo == '') and (workOrderNo == null or workOrderNo == '')  ">and 1 = 2</if>
  </select>

  <select id="getMaxSnOfProdplanId" resultType="java.lang.String">
    select sn from wip_info t where t.attribute1 = #{prodPlanId} and t.enabled_flag = 'Y' order by sn desc, wip_id desc limit 1
  </select>

  <select id="checkExistOfProdplanId" resultType="java.lang.Integer">
    select count(1) from wip_info t where t.attribute1 = #{prodPlanId} and t.enabled_flag = 'Y' limit 1
  </select>

  <select id="countCraftNum" resultType="java.lang.Long" parameterType="com.zte.interfaces.dto.SnCraftQtyInputDTO">
    select count(*) from
    (select distinct t.sn from WIP_INFO t
    where t.enabled_flag = 'Y'
    and t.workshop_code = #{workshopCode}
    and t.factory_id = cast(#{factoryId} as numeric)
    <choose>
      <when test="startDate != null and startDate != ''">
        <![CDATA[  and t.last_updated_date>=trunc( to_timestamp(#{startDate},'yyyy-MM-dd hh24:mi:ss')) ]]>
      </when>
      <otherwise>
        <![CDATA[ and t.last_updated_date>=trunc( to_timestamp(to_char(sysdate ,'yyyy-MM-dd'),'yyyy-MM-dd hh24:mi:ss')) ]]>
      </otherwise>
    </choose>

    <choose>
      <when test="endDate != null and endDate != ''">
        <![CDATA[  and t.last_updated_date < trunc( to_timestamp(#{endDate},'yyyy-MM-dd hh24:mi:ss')+1) ]]>
      </when>
      <otherwise>
        <![CDATA[ and t.last_updated_date < trunc( to_timestamp(to_char(sysdate, 'yyyy-MM-dd hh24:mi:ss'),'yyyy-MM-dd hh24:mi:ss')+1) ]]>
      </otherwise>
    </choose>
    <if test="inLineList != null and inLineList.size > 0">
      and t.LINE_CODE in
      <foreach collection="inLineList" index="index" item="lineCode" open="(" separator="," close=")">
        #{lineCode}
      </foreach>
    </if>
    and t.craft_section in
    <!-- 处理in的集合超过1000条时Oracle不支持的情况 -->
    <trim suffixOverrides="or t.craft_section in">    <!-- 表示删除最后一个条件 -->
      <foreach collection="craftArray" item="item" index="index" open="(" close=")">
        <if test="index != 0">
          <choose>
            <when test="index % 1000 == 999">) or t.craft_section in (</when>
            <otherwise>,</otherwise>
          </choose>
        </if>
        #{item}
      </foreach>
    </trim>
    ) a
  </select>

  <!-- 统计其它工艺段数量 -->
  <select id="countOtherCraftNum" resultType="java.lang.Long"
          parameterType="com.zte.interfaces.dto.SnCraftQtyInputDTO">
    select count(*) from
    (select distinct t.sn from WIP_INFO t
    where t.enabled_flag = 'Y'
    and t.workshop_code = #{workshopCode}
    and t.factory_id = cast(#{factoryId} as numeric)
    <choose>
      <when test="startDate != null and startDate != ''">
        <![CDATA[  and t.last_updated_date>=trunc( to_timestamp(#{startDate},'yyyy-MM-dd hh24:mi:ss')) ]]>
      </when>
      <otherwise>
        <![CDATA[ and t.last_updated_date>=trunc( to_timestamp(to_char(sysdate ,'yyyy-MM-dd'),'yyyy-MM-dd hh24:mi:ss')) ]]>
      </otherwise>
    </choose>

    <choose>
      <when test="endDate != null and endDate != ''">
        <![CDATA[  and t.last_updated_date < trunc( to_timestamp(#{endDate},'yyyy-MM-dd hh24:mi:ss')+1) ]]>
      </when>
      <otherwise>
        <![CDATA[ and t.last_updated_date < trunc( to_timestamp(to_char(sysdate, 'yyyy-MM-dd hh24:mi:ss'),'yyyy-MM-dd hh24:mi:ss')+1) ]]>
      </otherwise>
    </choose>
    <if test="inLineList != null and inLineList.size > 0">
      and t.LINE_CODE in
      <foreach collection="inLineList" index="index" item="lineCode" open="(" separator="," close=")">
        #{lineCode}
      </foreach>
    </if>
    and t.craft_section not in
    <!-- 处理in的集合超过1000条时Oracle不支持的情况 -->
    <trim suffixOverrides="or t.craft_section not in">    <!-- 表示删除最后一个条件 -->
      <foreach collection="allCraftArray" item="item" index="index" open="(" close=")">
        <if test="index != 0">
          <choose>
            <when test="index % 1000 == 999">) or t.craft_section not in (</when>
            <otherwise>,</otherwise>
          </choose>
        </if>
        #{item}
      </foreach>
    </trim>
    ) a
  </select>

  <select id="getWipInfoByTaskNoAndFactoryId" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from WIP_INFO
    where ENABLED_FLAG = 'Y'
    <if test="taskNo != null and taskNo != ''">and ATTRIBUTE2 = #{taskNo, jdbcType=VARCHAR}</if>
    <if test="factoryId != null and factoryId != ''">and FACTORY_ID = #{factoryId, jdbcType=VARCHAR}</if>

    <if test="(taskNo == null or taskNo == '')  and
              (factoryId == null or factoryId == '')"> and 1=2 </if>
  </select>

  <select id="getWipInfoByBarCodeSection" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from WIP_INFO
    where ENABLED_FLAG = 'Y'
    and SN >= #{sectionStart} and SN <![CDATA[ <= ]]> #{sectionEnd}
  </select>

  <select id="getWipInfoByBarCode" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from WIP_INFO
    where ENABLED_FLAG = 'Y'
    and SN = #{sn, jdbcType=VARCHAR}
  </select>

  <update id="updatePsWipInfoByBatchSnList" parameterType="com.zte.interfaces.dto.PsWipInfoDTO">
    update WIP_INFO
    set
    <if test="lastUpdatedBy != null and lastUpdatedBy != ''">LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR},
    </if>
    <if test="lastProcess != null and lastProcess != ''">LAST_PROCESS = #{lastProcess,jdbcType=VARCHAR},</if>
    <if test="workStation != null and workStation != ''">WORK_STATION = #{workStation,jdbcType=VARCHAR},</if>
    <if test="currProcessCode != null and currProcessCode != ''">CURR_PROCESS_CODE =
      #{currProcessCode,jdbcType=VARCHAR},
    </if>
    <if test="craftSection != null and craftSection != ''">CRAFT_SECTION = #{craftSection,jdbcType=VARCHAR},</if>
    <if test="attribute1 != null">ATTRIBUTE1 = #{attribute1,jdbcType=VARCHAR},</if>
    <if test="attribute2 != null">ATTRIBUTE2 = #{attribute2,jdbcType=VARCHAR},</if>
    <if test="attribute3 != null">ATTRIBUTE3 = #{attribute3,jdbcType=VARCHAR},</if>
    <if test="isClearWlw != null and isClearWlw != ''">WORK_ORDER_NO='',WORKSHOP_CODE='',LINE_CODE='',</if>
    LAST_UPDATED_DATE =sysdate
    where sn in(${inSns}) and ENABLED_FLAG = 'Y'
  </update>

  <update id="updatePsWipInfoByBatchSnListForReWork" parameterType="java.util.List">
    <foreach collection="list" separator=";" item="item" >
      update WIP_INFO
      set
      <if test="item.lastUpdatedBy != null and item.lastUpdatedBy != ''">LAST_UPDATED_BY =
        #{item.lastUpdatedBy,jdbcType=VARCHAR},
      </if>
      <if test="item.lastProcess != null and item.lastProcess != ''">LAST_PROCESS =
        #{item.lastProcess,jdbcType=VARCHAR},
      </if>
      <if test="item.workStation != null and item.workStation != ''">WORK_STATION =
        #{item.workStation,jdbcType=VARCHAR},
      </if>
      <if test="item.currProcessCode != null and item.currProcessCode != ''">CURR_PROCESS_CODE =
        #{item.currProcessCode,jdbcType=VARCHAR},
      </if>
      <if test="item.craftSection != null and item.craftSection != ''">CRAFT_SECTION =
        #{item.craftSection,jdbcType=VARCHAR},
      </if>
      <if test="item.attribute1 != null">ATTRIBUTE1 = #{item.attribute1,jdbcType=VARCHAR},</if>
      <if test="item.attribute2 != null">ATTRIBUTE2 = #{item.attribute2,jdbcType=VARCHAR},</if>
      <if test="item.attribute3 != null">ATTRIBUTE3 = #{item.attribute3,jdbcType=VARCHAR},</if>
      <if test="item.originalTask != null">ORIGINAL_TASK = #{item.originalTask,jdbcType=VARCHAR},</if>
      <if test="item.isClearWlw != null and item.isClearWlw != ''">
        WORK_ORDER_NO='',WORKSHOP_CODE='',LINE_CODE='',
      </if>
      LAST_UPDATED_DATE =sysdate
      where sn=#{item.sn,jdbcType=VARCHAR}
    </foreach>
  </update>

  <select id="getBarcodeList" parameterType="com.zte.domain.model.PsWipInfo" resultMap="BaseResultMap">
    SELECT COUNT(1) as barcodeNumber, attribute1
    FROM WIP_INFO t where t.ENABLED_FLAG = 'Y'
    <if test="inAttribute1 != null and inAttribute1 !=''">and t.ATTRIBUTE1 IN (${inAttribute1})</if>
    <if test="factoryId != null and factoryId !=''">and t.FACTORY_ID=cast(#{factoryId} as numeric)</if>
    <if test="craftSection != null and craftSection !=''">and t.CRAFT_SECTION=#{craftSection}</if>
    GROUP BY t.attribute1
  </select>

  <update id="updatePsWipPrintInfoBySns" parameterType="java.util.Map">
    update WIP_INFO
    set
    IS_PRINT = '1',
    LAST_UPDATED_DATE = sysdate,
    PRINT_DATE = sysdate
    where  sn in(${inSns})
  </update>

  <!-- 标模白名单扫描-SN是否存在于wip_info表中 -->
  <select id="selectSNfromWipInfo" parameterType="java.util.Map" resultMap="BaseResultMap">
    select t.*
    from WIP_INFO t
    where t.SN = #{sn}
  </select>

  <select id="getWipInfoByParentSn" parameterType="java.lang.String" resultMap="BaseResultMap">
    select t.PARENT_SN,t.SN,t.ITEM_NO,t.CURR_PROCESS_CODE,t.WORK_STATION,t.LAST_UPDATED_BY,t.CREATE_DATE
    from wip_info t
    where  ENABLED_FLAG = 'Y'
    and t.PARENT_SN=#{parentSn,jdbcType=VARCHAR}
  </select>

  <select id="getWipInfoByParentSns" parameterType="java.util.List" resultMap="BaseResultMap">
    select t.PARENT_SN,t.SN,t.ITEM_NO,t.CURR_PROCESS_CODE,t.WORK_STATION,t.LAST_UPDATED_BY,t.CREATE_DATE
    from wip_info t
    where  ENABLED_FLAG = 'Y'
    and t.PARENT_SN in
    <foreach collection="list" index="index" item="item"
             separator="," open="(" close=")">
      #{item,jdbcType=VARCHAR}
    </foreach>
  </select>

  <update id="updateByParentSnBatch" parameterType="java.util.List">
    <foreach collection="list" separator=";" item="item" >
      update WIP_INFO T
      <set>
        <if test="item.sn != null">
          T.SN = #{item.sn,jdbcType=VARCHAR},
        </if>
        <if test="item.workOrderNo != null">
          T.WORK_ORDER_NO = #{item.workOrderNo,jdbcType=VARCHAR},
        </if>
        <if test="item.itemNo != null">
          T.ITEM_NO = #{item.itemNo,jdbcType=VARCHAR},
        </if>
        <if test="item.itemName != null">
          T.ITEM_NAME = #{item.itemName,jdbcType=VARCHAR},
        </if>
        <if test="item.currProcessCode != null">
          T.CURR_PROCESS_CODE = #{item.currProcessCode,jdbcType=VARCHAR},
        </if>
        <if test="item.status != null">
          T.STATUS = #{item.status,jdbcType=VARCHAR},
        </if>
        <if test="item.worker != null">
          T.WORKER = #{item.worker,jdbcType=VARCHAR},
        </if>
        <if test="item.inTime != null">
          T.IN_TIME = #{item.inTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.outTime != null">
          T.OUT_TIME = #{item.outTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.parentSn != null">
          T.PARENT_SN = #{item.parentSn,jdbcType=VARCHAR},
        </if>
        <if test="item.errorCode != null">
          T.ERROR_CODE = #{item.errorCode,jdbcType=VARCHAR},
        </if>
        <if test="item.lineCode != null">
          T.LINE_CODE = #{item.lineCode,jdbcType=VARCHAR},
        </if>
        <if test="item.timeSpan != null">
          T.TIME_SPAN = #{item.timeSpan,jdbcType=TIMESTAMP},
        </if>
        <if test="item.workshopCode != null">
          T.WORKSHOP_CODE = #{item.workshopCode,jdbcType=VARCHAR},
        </if>
        <if test="item.opeTimes != null">
          T.OPE_TIMES = #{item.opeTimes,jdbcType=DECIMAL},
        </if>
        <if test="item.craftSection != null">
          T.CRAFT_SECTION = #{item.craftSection,jdbcType=VARCHAR},
        </if>
        <if test="item.workStation != null">
          T.WORK_STATION = #{item.workStation,jdbcType=VARCHAR},
        </if>
        <if test="item.lastProcess != null">
          T.LAST_PROCESS = #{item.lastProcess,jdbcType=VARCHAR},
        </if>
        <if test="item.sourceSys != null">
          T.SOURCE_SYS = #{item.sourceSys,jdbcType=VARCHAR},
        </if>
        <if test="item.nextProcess != null">
          T.NEXT_PROCESS = #{item.nextProcess,jdbcType=VARCHAR},
        </if>
        <if test="item.remark != null">
          T.REMARK = #{item.remark,jdbcType=VARCHAR},
        </if>
        <if test="item.createBy != null">
          T.CREATE_BY = #{item.createBy,jdbcType=VARCHAR},
        </if>
        <if test="item.createDate != null">
          T.CREATE_DATE = #{item.createDate,jdbcType=TIMESTAMP},
        </if>
        <if test="item.lastUpdatedBy != null">
          T.LAST_UPDATED_BY = #{item.lastUpdatedBy,jdbcType=VARCHAR},
        </if>
        <if test="item.enabledFlag != null">
          T.ENABLED_FLAG = #{item.enabledFlag,jdbcType=VARCHAR},
        </if>
        <if test="item.orgId != null">
          T.ORG_ID = #{item.orgId,jdbcType=DECIMAL},
        </if>
        <if test="item.factoryId != null">
          T.FACTORY_ID = #{item.factoryId,jdbcType=DECIMAL},
        </if>
        <if test="item.entityId != null">
          T.ENTITY_ID = #{item.entityId,jdbcType=DECIMAL},
        </if>
        <if test="item.sourceImu != null">
          T.SOURCE_IMU = #{item.sourceImu,jdbcType=DECIMAL},
        </if>
        <if test="item.sourceBimu != null">
          T.SOURCE_BIMU = #{item.sourceBimu,jdbcType=DECIMAL},
        </if>
        <if test="item.colName != null">
          T.COL_NAME = #{item.colName,jdbcType=VARCHAR},
        </if>
        <if test="item.sourceSysName != null">
          T.SOURCE_SYS_NAME = #{item.sourceSysName,jdbcType=VARCHAR},
        </if>
        <if test="item.fixId != null">
          T.FIX_ID = #{item.fixId,jdbcType=VARCHAR},
        </if>
        <if test="item.stationName != null">
          T.STATION_NAME = #{item.stationName,jdbcType=VARCHAR},
        </if>
        T.LAST_UPDATED_DATE = SYSDATE
      </set>
      where T.PARENT_SN = #{item.parentSn,jdbcType=VARCHAR}
      and T.ENABLED_FLAG = 'Y'
    </foreach>
  </update>

  <!-- 批量删除wipinfo-->
  <delete id="deleteWipInfo" parameterType="java.util.List">
    delete from WIP_INFO t
    where t.PARENT_SN in
    <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
  </delete>

  <!-- 批量删除wipinfo根据sn-->
  <delete id="deleteWipInfoBysn" parameterType="java.util.List">
    delete from WIP_INFO t
    where t.SN in
    <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
  </delete>

  <!-- 批量删除wipinfo-->
  <delete id="deleteWipInfoByWipId" parameterType="java.util.List">
    delete from WIP_INFO t
    where t.wip_id in
    <foreach collection="wipIdList" index="index" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
  </delete>

  <select id="selectCraftSectionProportion" resultType="com.zte.interfaces.dto.CraftSectionCountResultDTO">
    select CRAFT_SECTION craft,COUNT(CRAFT_SECTION) qty FROM
    WIP_INFO
    where ENABLED_FLAG = 'Y'
    AND LAST_UPDATED_DATE <![CDATA[ >= ]]>  to_timestamp(#{startDate}, 'yyyy-MM-dd hh24:mi:ss')
    AND LAST_UPDATED_DATE <![CDATA[ <= ]]>  to_timestamp(#{endDate}, 'yyyy-MM-dd hh24:mi:ss')
    AND CRAFT_SECTION in (${craftSection})
    AND FACTORY_ID = cast(#{factoryId} as numeric)
    <if test="inLineList != null and inLineList.size > 0">
      AND LINE_CODE IN
      <foreach collection="inLineList" index="index" item="lineCode" open="(" separator="," close=")">
        #{lineCode}
      </foreach>
    </if>
    GROUP BY CRAFT_SECTION
  </select>


  <select id="getListBy" parameterType="java.util.List" resultMap="BaseResultMap">
    select
    WIP_ID, SN, WORK_ORDER_NO, ITEM_NO, ITEM_NAME, CURR_PROCESS_CODE,
    STATUS, WORKER, IN_TIME, OUT_TIME, PARENT_SN, ERROR_CODE, LINE_CODE, TIME_SPAN, WORKSHOP_CODE,
    OPE_TIMES,CRAFT_SECTION,WORK_STATION, REMARK, CREATE_BY, CREATE_DATE, LAST_UPDATED_BY, LAST_UPDATED_DATE,
    ENABLED_FLAG,
    ORG_ID, FACTORY_ID, ENTITY_ID, ATTRIBUTE1, ATTRIBUTE2, ATTRIBUTE3, ATTRIBUTE4,
    ATTRIBUTE5,LAST_PROCESS,SOURCE_SYS,NEXT_PROCESS
    ,SOURCE_IMU,SOURCE_BIMU,COL_NAME,SOURCE_SYS_NAME,FIX_ID,STATION_NAME
    from WIP_INFO
    where PARENT_SN in
    <foreach collection="list" index="index" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
    AND ENABLED_FLAG = 'Y'
  </select>

  <select id="getWipInfoTotal" parameterType="com.zte.domain.model.PsWipInfo" resultMap="BaseResultMap">
    SELECT craft_section,count(*) as qty from WIP_INFO
    where ENABLED_FLAG = 'Y'
    <if test="itemNo != null and itemNo !=''">and item_no = #{itemNo,jdbcType=VARCHAR}</if>
    <if test="attribute2 != null and attribute2 !=''">and attribute2 = #{attribute2,jdbcType=VARCHAR}</if>
    <if test="attribute1 != null and attribute1 !=''">and attribute1 = #{attribute1,jdbcType=VARCHAR}</if>
    <if test="inCraftSection != null">
      and craft_section in
      <foreach collection="inCraftSection" index="index" item="craftSection" open="(" separator="," close=")">
        #{craftSection}
      </foreach>
    </if>

    <if test="(itemNo == null or itemNo == '') and ( attribute2 == null or attribute2 == '') and
              (attribute1 == null or attribute1 == '') and (inCraftSection == null or inCraftSection.size() == 0)"> and 1=2 </if>
    group by craft_section
  </select>

  <select id="getWipInfoFinished" parameterType="com.zte.domain.model.PsWipInfo" resultMap="BaseResultMap">
    SELECT craft_section,curr_process_code, count(*) as qty from WIP_INFO
    where ENABLED_FLAG = 'Y'
    and WORK_STATION is not null
    <if test="workStation != null and workStation !=''">and WORK_STATION != #{workStation,jdbcType=VARCHAR}</if>
    <if test="itemNo != null and itemNo !=''">and item_no = #{itemNo,jdbcType=VARCHAR}</if>
    <if test="attribute2 != null and attribute2 !=''">and attribute2 = #{attribute2,jdbcType=VARCHAR}</if>
    <if test="attribute1 != null and attribute1 !=''">and attribute1 = #{attribute1,jdbcType=VARCHAR}</if>
    <if test="inCraftSection != null">
      and craft_section in
      <foreach collection="inCraftSection" index="index" item="craftSection" open="(" separator="," close=")">
        #{craftSection}
      </foreach>
    </if>
    <if test="inProcessGroup != null">
      and curr_process_code in
      <foreach collection="inProcessGroup" index="index" item="processGroup" open="(" separator="," close=")">
        #{processGroup}
      </foreach>
    </if>
    <if test="lastProcess != null and lastProcess !=''">and last_process = #{lastProcess,jdbcType=VARCHAR}</if>
    group by craft_section,curr_process_code
  </select>

  <select id="getWipInfoQty" parameterType="com.zte.domain.model.PsWipInfo" resultMap="BaseResultMap">
    SELECT count(*) as qty FROM WIP_INFO WHERE ENABLED_FLAG = 'Y'
    <if test="attribute3 !=null and attribute3 != ''">AND attribute3 = #{attribute3,jdbcType=VARCHAR}</if>
    <if test="attribute2 !=null and attribute2 != ''">AND attribute2 = #{attribute2,jdbcType=VARCHAR}</if>
    <if test="craftSection !=null and craftSection != ''">AND craft_section = #{craftSection,jdbcType=VARCHAR}</if>
    <if test="lastUpdatedDate !=null">AND trunc(last_updated_date) = trunc(#{lastUpdatedDate,jdbcType=TIMESTAMP}::TIMESTAMP)
    </if>
    <if test="originalTask !=null and originalTask != ''">AND ORIGINAL_TASK = #{originalTask,jdbcType=VARCHAR}</if>
  </select>

  <update id="updateDipFinishFlagBySn" parameterType="java.util.Map">
    update WIP_INFO
    set
    DIP_FINISH_FLAG = #{dipFinishFlag},
    LAST_UPDATED_DATE = sysdate,
    LAST_UPDATED_BY= #{lastUpdatedBy}
    where  sn =#{sn} and ENABLED_FLAG='Y'
  </update>

  <update id="updatePsWipInfoFromPsScanHistory" parameterType="com.zte.domain.model.PsWipInfo">
    update WIP_INFO
    set WORK_ORDER_NO = #{workOrderNo,jdbcType=VARCHAR},
    ITEM_NO = #{itemNo,jdbcType=VARCHAR},
    ITEM_NAME = #{itemName,jdbcType=VARCHAR},
    CURR_PROCESS_CODE = #{currProcessCode,jdbcType=VARCHAR},
    STATUS = #{status,jdbcType=VARCHAR},
    WORKER = #{worker,jdbcType=VARCHAR},
    IN_TIME = #{inTime,jdbcType=TIMESTAMP},
    OUT_TIME = #{outTime,jdbcType=TIMESTAMP},
    PARENT_SN = #{parentSn,jdbcType=VARCHAR},
    ERROR_CODE = #{errorCode,jdbcType=VARCHAR},
    LINE_CODE = #{lineCode,jdbcType=VARCHAR},
    TIME_SPAN = #{timeSpan,jdbcType=TIMESTAMP},
    WORKSHOP_CODE = #{workshopCode,jdbcType=VARCHAR},
    OPE_TIMES = #{opeTimes,jdbcType=DECIMAL},
    CRAFT_SECTION = #{craftSection,jdbcType=VARCHAR},
    WORK_STATION = #{workStation,jdbcType=VARCHAR},
    REMARK = #{remark,jdbcType=VARCHAR},
    LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR},
    ATTRIBUTE1 = #{attribute1,jdbcType=VARCHAR},
    ATTRIBUTE2 = #{attribute2,jdbcType=VARCHAR},
    ATTRIBUTE3 = #{attribute3,jdbcType=VARCHAR},
    ATTRIBUTE4 = #{attribute4,jdbcType=VARCHAR},
    ATTRIBUTE5 = #{attribute5,jdbcType=TIMESTAMP},
    LAST_PROCESS = #{lastProcess,jdbcType=VARCHAR},
    SOURCE_SYS = #{sourceSys,jdbcType=VARCHAR},
    NEXT_PROCESS = #{nextProcess,jdbcType=VARCHAR},
    SOURCE_IMU = #{sourceImu,jdbcType=DECIMAL},
    SOURCE_BIMU = #{sourceBimu,jdbcType=DECIMAL},
    COL_NAME = #{colName,jdbcType=VARCHAR},
    SOURCE_SYS_NAME = #{sourceSysName,jdbcType=VARCHAR},
    FIX_ID = #{fixId,jdbcType=VARCHAR},
    STATION_NAME = #{stationName,jdbcType=VARCHAR},
    LAST_UPDATED_DATE =sysdate
    where  SN = #{sn,jdbcType=VARCHAR}
  </update>

  <update id="updatePsWipInfoFromPsScanHistoryBatch" parameterType="java.util.List">
    <foreach collection="list" separator=";" item="item" >
      update WIP_INFO
      set WORK_ORDER_NO = #{item.workOrderNo,jdbcType=VARCHAR},
      ITEM_NO = #{item.itemNo,jdbcType=VARCHAR},
      ITEM_NAME = #{item.itemName,jdbcType=VARCHAR},
      CURR_PROCESS_CODE = #{item.currProcessCode,jdbcType=VARCHAR},
      STATUS = #{item.status,jdbcType=VARCHAR},
      WORKER = #{item.worker,jdbcType=VARCHAR},
      IN_TIME = #{item.inTime,jdbcType=TIMESTAMP},
      OUT_TIME = #{item.outTime,jdbcType=TIMESTAMP},
      PARENT_SN = #{item.parentSn,jdbcType=VARCHAR},
      ERROR_CODE = #{item.errorCode,jdbcType=VARCHAR},
      LINE_CODE = #{item.lineCode,jdbcType=VARCHAR},
      TIME_SPAN = #{item.timeSpan,jdbcType=TIMESTAMP},
      WORKSHOP_CODE = #{item.workshopCode,jdbcType=VARCHAR},
      CRAFT_SECTION = #{item.craftSection,jdbcType=VARCHAR},
      WORK_STATION = #{item.workStation,jdbcType=VARCHAR},
      REMARK = #{item.remark,jdbcType=VARCHAR},
      LAST_UPDATED_BY = #{item.lastUpdatedBy,jdbcType=VARCHAR},
      ATTRIBUTE1 = #{item.attribute1,jdbcType=VARCHAR},
      ATTRIBUTE3 = #{item.attribute3,jdbcType=VARCHAR},
      ATTRIBUTE4 = #{item.attribute4,jdbcType=VARCHAR},
      ATTRIBUTE5 = #{item.attribute5,jdbcType=TIMESTAMP},
      LAST_PROCESS = #{item.lastProcess,jdbcType=VARCHAR},
      SOURCE_SYS = #{item.sourceSys,jdbcType=VARCHAR},
      NEXT_PROCESS = #{item.nextProcess,jdbcType=VARCHAR},
      SOURCE_IMU = #{item.sourceImu,jdbcType=DECIMAL},
      SOURCE_BIMU = #{item.sourceBimu,jdbcType=DECIMAL},
      COL_NAME = #{item.colName,jdbcType=VARCHAR},
      SOURCE_SYS_NAME = #{item.sourceSysName,jdbcType=VARCHAR},
      FIX_ID = #{item.fixId,jdbcType=VARCHAR},
      STATION_NAME = #{item.stationName,jdbcType=VARCHAR},
      LAST_UPDATED_DATE =sysdate
      where
      ENABLED_FLAG = 'Y'
      and SN = #{item.sn,jdbcType=VARCHAR}
    </foreach>
  </update>

  <update id="updatePsWipInfoFromPsScanHistoryForZs" parameterType="com.zte.domain.model.PsWipInfo">
    update WIP_INFO
    set WORK_ORDER_NO = #{workOrderNo,jdbcType=VARCHAR},
    ITEM_NO = #{itemNo,jdbcType=VARCHAR},
    ITEM_NAME = #{itemName,jdbcType=VARCHAR},
    CURR_PROCESS_CODE = #{currProcessCode,jdbcType=VARCHAR},
    STATUS = #{status,jdbcType=VARCHAR},
    WORKER = #{worker,jdbcType=VARCHAR},
    IN_TIME = #{inTime,jdbcType=TIMESTAMP},
    OUT_TIME = #{outTime,jdbcType=TIMESTAMP},
    PARENT_SN = #{parentSn,jdbcType=VARCHAR},
    ERROR_CODE = #{errorCode,jdbcType=VARCHAR},
    LINE_CODE = #{lineCode,jdbcType=VARCHAR},
    TIME_SPAN = #{timeSpan,jdbcType=TIMESTAMP},
    WORKSHOP_CODE = #{workshopCode,jdbcType=VARCHAR},
    CRAFT_SECTION = #{craftSection,jdbcType=VARCHAR},
    WORK_STATION = #{workStation,jdbcType=VARCHAR},
    REMARK = #{remark,jdbcType=VARCHAR},
    LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR},
    ATTRIBUTE1 = #{attribute1,jdbcType=VARCHAR},
    ATTRIBUTE3 = #{attribute3,jdbcType=VARCHAR},
    ATTRIBUTE4 = #{attribute4,jdbcType=VARCHAR},
    ATTRIBUTE5 = #{attribute5,jdbcType=TIMESTAMP},
    LAST_PROCESS = #{lastProcess,jdbcType=VARCHAR},
    SOURCE_SYS = #{sourceSys,jdbcType=VARCHAR},
    NEXT_PROCESS = #{nextProcess,jdbcType=VARCHAR},
    SOURCE_IMU = #{sourceImu,jdbcType=DECIMAL},
    SOURCE_BIMU = #{sourceBimu,jdbcType=DECIMAL},
    COL_NAME = #{colName,jdbcType=VARCHAR},
    SOURCE_SYS_NAME = #{sourceSysName,jdbcType=VARCHAR},
    FIX_ID = #{fixId,jdbcType=VARCHAR},
    STATION_NAME = #{stationName,jdbcType=VARCHAR},
    LAST_UPDATED_DATE =sysdate
    where
    ENABLED_FLAG = 'Y'
    and SN = #{sn,jdbcType=VARCHAR}
  </update>
  <select id="getWipInfoByBillNo" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    from (
    SELECT t2.*
    FROM (SELECT a.*
    FROM warehouse_entry_detail a
    where a.bill_no = #{billNo}
    and a.status = #{status}
    and a.enabled_flag = 'Y') t1
    left join wip_info t2 on t1.sn = t2.sn
    and t2.enabled_flag = 'Y' )
  </select>

  <select id="getCompleteNo" parameterType="com.zte.domain.model.PsWipInfo" resultMap="BaseResultMap">
    SELECT * FROM WIP_INFO WHERE ENABLED_FLAG = 'Y'and ASSEMBLE_FLAG = 'Y'
    <if test="attribute1 !=null and attribute1 != ''">AND attribute1 = #{attribute1,jdbcType=VARCHAR}</if>
  </select>

  <select id="getPartStock90Date" parameterType="java.util.List" resultMap="ResultMap2">
    <foreach collection="dtoList" item="item" separator=" UNION ALL ">
      select * from (
      select distinct attribute1 as PRODPLAN_ID,create_date as STOCK_90_DATE,
      rank() over(partition by attribute1 order by create_date,smt_scan_id) mm
      from wip_scan_history t2
      where t2.attribute1 = #{item.prodplanId,jdbcType=VARCHAR}
      and t2.ENABLED_FLAG = 'Y'
      and t2.work_station = '0'
      and t2.curr_process_code in
      <foreach collection="lookUpTypes" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
      )
      where mm = #{item.partStock90Index,jdbcType=DECIMAL}
    </foreach>
  </select>

  <select id="getNeverCompleteNo" parameterType="com.zte.domain.model.PsWipInfo" resultMap="BaseResultMap">
    SELECT * FROM WIP_INFO WHERE ENABLED_FLAG = 'Y'and ASSEMBLE_FLAG = 'N'
    <if test="attribute1 !=null and attribute1 != ''">AND attribute1 = #{attribute1,jdbcType=VARCHAR}</if>
  </select>
  <select id="getWipFirstDate" parameterType="com.zte.interfaces.dto.WipFirstDateGetDTO" resultMap="WipInfoFirstMap">
    SELECT T.CRAFT_SECTION,
    T.ATTRIBUTE1 PROD_PLAN_ID,
    MIN(T.CREATE_DATE) CREATE_DATE,
    MAX(CT.CRAFT_TYPE) CRAFT_TYPE
    FROM WIP_SCAN_HISTORY T
    LEFT JOIN (
    <foreach collection="craftType" item="item" separator=" UNION ">
      SELECT #{item.type,jdbcType=VARCHAR} CRAFT_TYPE, #{item.craft,jdbcType=VARCHAR} CRAFT_SECTION
    </foreach>
    ) CT ON T.CRAFT_SECTION =
    CT.CRAFT_SECTION
    WHERE T.ENABLED_FLAG = 'Y'
    AND T.ATTRIBUTE1 IN
    <foreach collection="planIds" item="planId" open="(" separator="," close=")">
      #{planId, jdbcType=VARCHAR}
    </foreach>
    AND T.CREATE_DATE IS NOT NULL
    AND T.WORK_STATION != '0'
    AND T.CRAFT_SECTION IN
    <foreach collection="craftList" item="craft" open="(" separator="," close=")">
      #{craft, jdbcType=VARCHAR}
    </foreach>
    GROUP BY T.CRAFT_SECTION, T.ATTRIBUTE1
  </select>
  <select id="getWipCraftQty" resultMap="WipCraftQytMap">
    SELECT T.CRAFT_SECTION,
    T.ATTRIBUTE1 PROD_PLAN_ID,
    COUNT(1) QTY
    FROM WIP_INFO T
    WHERE T.ENABLED_FLAG = 'Y'
    AND T.ATTRIBUTE1 IN
    <foreach collection="planIds" item="planId" open="(" separator="," close=")">
      #{planId, jdbcType=VARCHAR}
    </foreach>
    AND T.CRAFT_SECTION IN
    <foreach collection="craftList" item="craft" open="(" separator="," close=")">
      #{craft, jdbcType=VARCHAR}
    </foreach>
    GROUP BY T.CRAFT_SECTION, T.ATTRIBUTE1
  </select>

  <select id="getWipSmtOutQty" resultMap="WipCraftQytMap">
    SELECT PC.CRAFT_SECTION,
    T.ATTRIBUTE1 PROD_PLAN_ID,
    COUNT(1) QTY
    FROM WIP_INFO T
    JOIN (
    <foreach collection="crafts" item="item" separator="UNION">
      SELECT #{item.prodPlanId,jdbcType=VARCHAR} PROD_PLAN_ID,
      #{item.craftSection,jdbcType=VARCHAR} CRAFT_SECTION,
      #{item.nextCraftSection,jdbcType=VARCHAR} NEXT_CRAFT_SECTION

    </foreach>
    ) PC ON PC.PROD_PLAN_ID = T.ATTRIBUTE1
    AND PC.NEXT_CRAFT_SECTION = T.CRAFT_SECTION
    WHERE T.ENABLED_FLAG = 'Y'
    AND (T.CREATE_DATE BETWEEN TRUNC(#{date,jdbcType=TIMESTAMP}::TIMESTAMP) AND #{date,jdbcType=TIMESTAMP})
    GROUP BY PC.CRAFT_SECTION, T.ATTRIBUTE1
  </select>

  <select id="getWipSubCraftQty" resultMap="WipCraftQytMap">
    SELECT LM.CRAFT_SECTION,
    T.ATTRIBUTE1 PROD_PLAN_ID,
    COUNT(1) QTY
    FROM WIP_INFO T
    JOIN (
    <foreach collection="lookups" item="item" separator="UNION">
      SELECT #{item.lookupMeaning,jdbcType=VARCHAR} CURR_PROCESS_CODE,
      #{item.attribute1,jdbcType=VARCHAR} CRAFT_SECTION

    </foreach>
    ) LM ON LM.CURR_PROCESS_CODE = T.CURR_PROCESS_CODE
    WHERE T.ENABLED_FLAG = 'Y'
    AND T.ATTRIBUTE1 IN
    <foreach collection="planIds" item="planId" open="(" separator="," close=")">
      #{planId, jdbcType=VARCHAR}
    </foreach>
    AND T.CURR_PROCESS_CODE IN
    <foreach collection="subCrafts" item="craft" open="(" separator="," close=")">
      #{craft, jdbcType=VARCHAR}
    </foreach>
    GROUP BY LM.CRAFT_SECTION, T.ATTRIBUTE1
  </select>

  <update id="updatePsWipInfoAssembleFlag" parameterType="com.zte.domain.model.PsWipInfo">
    update WIP_INFO
    set
    ASSEMBLE_FLAG = #{assembleFlag,jdbcType=VARCHAR},
    SEC_ASSEMBLE_FLAG = #{secAssembleFlag,jdbcType=VARCHAR},
    LAST_UPDATED_BY =#{lastUpdatedBy,jdbcType=VARCHAR} ,
    LAST_UPDATED_DATE =sysdate
    where sn = #{sn,jdbcType=VARCHAR}
  </update>

  <select id="getProdplanIdListStandardAutomaticSubmitWarehouse" parameterType="com.zte.interfaces.dto.StandardAutomaticSubmitWarehouseDTO" resultType="java.lang.String">
    select distinct t.ATTRIBUTE1 from  wip_info t left join warehouse_entry_detail d on t.sn=d.sn and t.work_order_no=d.work_order_no and d.ENABLED_FLAG='Y'
    where t.ENABLED_FLAG='Y' and d.warehouse_entry_datail_id is null
    and t.CURR_PROCESS_CODE = #{currProcessCode,jdbcType=VARCHAR} and t.CRAFT_SECTION = #{craftSection,jdbcType=VARCHAR}
    <if test="workStation != null and workStation !=''">and t.WORK_STATION = #{workStation,jdbcType=VARCHAR}</if>
    <if test="originalTaskEmpty != null and originalTaskEmpty !=''">and (t.ORIGINAL_TASK is null or t.ORIGINAL_TASK ='')</if>
    and t.parent_sn like #{parentSn} and  length(t.item_no)=  #{itemNoLength}
    <if test="prodplanId != null and prodplanId !=''">and t.ATTRIBUTE1 = #{prodplanId,jdbcType=VARCHAR}</if>
    <if test="startDate != null and startDate != ''">
      <![CDATA[  and t.last_updated_date>= to_timestamp(#{startDate},'yyyy-MM-dd hh24:mi:ss') ]]>
    </if>
    <if test="endDate != null and endDate != ''">
      <![CDATA[  and t.last_updated_date<= to_timestamp(#{endDate},'yyyy-MM-dd hh24:mi:ss') ]]>
    </if>
  </select>

  <select id="getListForStandardAutomaticSubmitWarehouse" parameterType="com.zte.interfaces.dto.StandardAutomaticSubmitWarehouseDTO" resultMap="BaseResultMap">
    select t.sn,t.WORK_ORDER_NO,t.ITEM_NO,t.ITEM_NAME,t.CURR_PROCESS_CODE,t.FACTORY_ID,t.ORG_ID,t.ENTITY_ID,t.ATTRIBUTE1
    from  wip_info t left join warehouse_entry_detail d on t.sn=d.sn and t.work_order_no=d.work_order_no and d.ENABLED_FLAG='Y'
    where t.ENABLED_FLAG='Y' and d.warehouse_entry_datail_id is null  and t.ATTRIBUTE1 = #{prodplanId,jdbcType=VARCHAR}
    and t.CURR_PROCESS_CODE = #{currProcessCode,jdbcType=VARCHAR} and t.CRAFT_SECTION = #{craftSection,jdbcType=VARCHAR}
    <if test="workStation != null and workStation !=''">and t.WORK_STATION = #{workStation,jdbcType=VARCHAR}</if>
    <if test="originalTaskEmpty != null and originalTaskEmpty !=''">and (t.ORIGINAL_TASK is null or t.original_task ='')</if>
    and t.parent_sn like #{parentSn} and  length(t.item_no)=  #{itemNoLength}
  </select>


  <select id="currProcessListByBatchSn" parameterType="java.util.List" resultType="java.lang.String">
    SELECT DISTINCT(CURR_PROCESS_CODE)
    FROM WIP_INFO
    WHERE ENABLED_FLAG = 'Y'
    AND SN IN
    <foreach item="item" index="index" collection="list"
             open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>

  <delete id="deleteByParentSnList">
    delete from WIP_INFO
    where PARENT_SN in
    <foreach item="item" index="index" collection="snList"
             open="(" separator="," close=")">
      #{item}
    </foreach>
    AND ENABLED_FLAG = 'Y'
  </delete>

  <select id="countByAttribute1AndCurrProcessCode" resultMap="PsWipInfoDTOMap">
    SELECT COUNT(ATTRIBUTE1) inCountTotal,ATTRIBUTE1
    FROM WIP_INFO
    WHERE ENABLED_FLAG = 'Y'
    AND ATTRIBUTE1 IN
    <foreach item="item" index="index" collection="prodPlanIdList"
             open="(" separator="," close=")">
      #{item}
    </foreach>
    AND CURR_PROCESS_CODE IN
    <foreach item="item" index="index" collection="currProcessCodeList"
             open="(" separator="," close=")">
      #{item}
    </foreach>
    GROUP BY ATTRIBUTE1
  </select>

  <!-- 获取符合条件的记录列表 -->
  <select id="getInStoreSnMap" resultMap="TestStatMap">
    select prodplan_id, count(1) test_qty from (
      select t.sn, t.attribute1 prodplan_id
      from wip_info t where
      t.enabled_flag = 'Y'
      and t.craft_section ='入库'
      and t.attribute1 in
      <foreach collection="inProdplanIds" item="prodplanId" open="(" separator="," close=")">
        #{prodplanId}
      </foreach>
      union select r.sn, r.rcv_prodplan_id prodplan_id
      from pm_repair_rcv_detail r
      where
      r.enabled_flag = 'Y'
      and r.work_order_no in
      <foreach collection="inStoreRepairList" item="workOrderNo" open="(" separator="," close=")">
        #{workOrderNo}
      </foreach>
      and r.status not in ('10560006','10560005')
    ) group by prodplan_id
  </select>

  <update id="updateWorkStationBySnList" parameterType="java.util.Map">
    update WIP_INFO t
    set
    t.work_station = #{workStation},
    t.LAST_UPDATED_DATE = sysdate,
    t.last_updated_by = #{lastUpdatedBy}
    where
    sn in
    <foreach collection="snList" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
  </update>

  <update id="updateCraftToWarehousing">
    update WIP_INFO
    set
    curr_process_code = 'N',
    craft_section = '入库',
    LAST_UPDATED_DATE = sysdate,
    last_updated_by = 'system'
    where sn in
    <foreach collection="sns" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
  </update>

  <select id="batchSelectTaskNoWhetherHasSn" resultType="java.lang.String">
    select t.attribute1
    from wip_info t
    where t.enabled_flag = 'Y'
    <if test="prodList != null and prodList.size() > 0">
    and t.attribute1 in
    <foreach collection="prodList" open="(" separator="," item="item" close=")">
      #{item}
    </foreach>
    </if>
    <if test="prodList == null or prodList.size() == 0">
      and 1=2
    </if>
    group by t.attribute1
  </select>

  <select id="countSnQtyGroupByProdAndProcess" resultMap = "ConciseDailyQueryMap">
    select t.attribute1, t.curr_process_code, count(1) as onhand_qty
    from wip_info t
    where t.enabled_flag = 'Y'
    <if test="prodPlanIdList != null and prodPlanIdList.size() > 0">
      and t.attribute1 in
      <foreach collection="prodPlanIdList" item="item" open="(" separator="," close=")">
        #{item,jdbcType=VARCHAR}
      </foreach>
    </if>
    <if test="prodPlanIdList == null or prodPlanIdList.size() == 0">
      and 1 = 2
    </if>
    group by t.attribute1 , t.curr_process_code
  </select>

  <select id="checkSnInProdplanId" resultMap="BaseResultMap">
    select t.attribute1, t.sn
    from WIP_INFO t
    where t.ENABLED_FLAG = 'Y'
    and t.sn = #{sn,jdbcType=VARCHAR}
    and t.attribute1 = #{prodplanId,jdbcType=VARCHAR}
  </select>

  <select id="getStockNum" resultMap="ResultMap3">
    select count(1) as stock_num,t.attribute1
    from wip_info t
    where t.enabled_flag = 'Y'
    and t.craft_section in ('入库','出库')
    and t.attribute1 in
    <foreach collection="prodPlanIdList" item="item" open="(" separator="," close=")">
      #{item,jdbcType=VARCHAR}
    </foreach>
    group by t.attribute1
  </select>

  <select id="batchMaintenanceDataStatistics" resultType="com.zte.domain.model.WipDailyStatisticReport">
    select
    rcv_prodplan_id prodplanId,
    count(*) qty
    from (
        select * from (
              select
              row_number() over(partition by rcv.sn
              order by
              rcv.last_updated_date desc) rn , rcv.craft_section , rcv.rcv_prodplan_id
              from
              wip_info t
              join pm_repair_rcv_detail rcv on
              rcv.sn = t.sn
              where t.enabled_flag = 'Y' and rcv.enabled_flag = 'Y' and t.CURR_PROCESS_CODE = 'P0003'
              and t.attribute1 in
              <foreach collection="prodPlanIdList" item="item" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
              </foreach>
        )
        where
        rn = 1
    )
    where 1=1
    <if test="isWhole != null and isWhole == 'Y'.toString()">
      <choose>
        <when test="craftSectionList == null or craftSectionList.size() == 0">
          and 1 = 2
        </when>
        <otherwise>
          and craft_section in
          <foreach collection="craftSectionList" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
          </foreach>
        </otherwise>
      </choose>
    </if>

    <if test="isWhole != null and isWhole == 'N'.toString()">
      <if test="craftSectionList != null and craftSectionList.size() > 0">
        and craft_section not in
        <foreach collection="craftSectionList" item="item" open="(" separator="," close=")">
          #{item,jdbcType=VARCHAR}
        </foreach>
      </if>
    </if>

    group by
    rcv_prodplan_id
  </select>

  <select id="getWipInfoPage" parameterType="com.zte.springbootframe.common.model.Page" resultMap="BaseResultMap">
    select
    t.sn
    from WIP_INFO t
    where t.enabled_flag = 'Y'
    and t.work_station  = #{params.workStation,jdbcType=VARCHAR}
    and <![CDATA[ t.last_updated_date >= to_timestamp(#{params.startTime},'yyyy/mm/dd hh24:mi:ss')  ]]>
    and <![CDATA[ t.last_updated_date <= to_timestamp(#{params.endTime},'yyyy/mm/dd hh24:mi:ss')  ]]>
    order by t.last_updated_date,t.sn asc
  </select>
  <select id="getUnStoredVirtuallySns" resultType="java.lang.String">
    select t.sn
    from WIP_INFO t
    where t.ENABLED_FLAG = 'Y'
    <if test="maxSn != null and maxSn != ''">
      and SN > #{maxSn,jdbcType=VARCHAR}
    </if>
    and t.attribute1 = #{prodplanId,jdbcType=VARCHAR}
    order by sn
    limit #{qty,jdbcType=DECIMAL}
  </select>

  <select id="getWipInfoByItemAndInterval" resultType="com.zte.domain.model.PsWipInfo">
    select t.sn, t.craft_section, t.work_order_no,t.last_updated_date
    from WIP_INFO t
    where t.enabled_flag = 'Y'
    and
    <foreach collection="itemNos" item="itemNo" open="(" separator="or" close=")">
      item_no like #{itemNo} || '%'
    </foreach>
    and last_updated_date between #{startTime} and #{endTime}
    <if test="sn != null and sn !=''">
      and not exists (select 1 from WIP_INFO t2 where t2.enabled_flag = 'Y' and t2.last_updated_date = #{startTime}
      and t2.sn &lt;= #{sn} and t.sn = t2.sn)
    </if>
    order by t.last_updated_date, t.sn
    limit #{limit}
  </select>

  <select id="getWipInfoBySnList" parameterType="java.util.List" resultType="com.zte.domain.model.PsWipInfo">
    select sn, craft_section
    from WIP_INFO
    where ENABLED_FLAG = 'Y'
    and sn in
    <foreach item="sn" collection="snList" open="(" separator="," close=")">
      #{sn}
    </foreach>
  </select>

  <select id="getPage" parameterType="com.zte.springbootframe.common.model.Page" resultMap="SnQueryResultMap">
    select
      <include refid="Base_Column_List"/>
    from WIP_INFO t
    where 1=1
    and t.ENABLED_FLAG = 'Y'
    <if test="params.parentSn != null and params.parentSn != ''">and t.PARENT_SN = #{params.parentSn}</if>
    <if test="params.parentSnNotNull != null and params.parentSnNotNull != ''">and t.PARENT_SN is not null</if>
    <if test="params.wipId != null and params.wipId != ''">and t.WIP_ID = #{params.wipId}</if>
    <if test="params.sn != null and params.sn != ''">and t.SN = #{params.sn}</if>
    <if test="params.snList != null and params.snList.size > 0">and t.SN in
      <foreach item="item" collection="params.snList" open="(" separator="," close=")" index="index">
        #{params.snList[${index}],jdbcType=VARCHAR}
      </foreach>
    </if>
    <if test="params.isPrint != null and params.isPrint == 1">and t.IS_PRINT = #{params.isPrint}::numeric</if>
    <if test="params.isPrint != null and params.isPrint == 0">and (t.IS_PRINT = #{params.isPrint}::numeric or t.IS_PRINT is Null)</if>
    <if test="params.workOrderNo != null and params.workOrderNo != ''">and t.WORK_ORDER_NO = #{params.workOrderNo}</if>
    <if test="params.sourceTask !=null and params.sourceTask !=''">and t.WORK_ORDER_NO like '%${params.sourceTask}%'</if>
    <if test="params.inWorkOrderNo != null and params.inWorkOrderNo != ''">and t.WORK_ORDER_NO IN (${params.inWorkOrderNo})</if>
    <if test="params.itemNo != null and params.itemNo != ''">and t.ITEM_NO = #{params.itemNo}</if>
    <if test="params.itemName != null and params.itemName != ''">and t.ITEM_NAME = #{params.itemName}</if>
    <if test="params.currProcessCode != null and params.currProcessCode != ''">and t.CURR_PROCESS_CODE = #{params.currProcessCode}</if>
    <if test="params.status != null and params.status != ''">and t.STATUS = #{params.status}</if>
    <if test="params.worker != null and params.worker != ''">and t.WORKER = #{params.worker}</if>
    <if test="params.lineCode != null and params.lineCode != ''">and t.LINE_CODE = #{params.lineCode}</if>
    <if test="params.workshopCode != null and params.workshopCode != ''">and t.WORKSHOP_CODE = #{params.workshopCode}</if>
    <if test="params.inTimeBegin != null and params.inTimeEnd != null">
      <![CDATA[and t.IN_TIME > #{params.inTimeBegin} and t.IN_TIME < #{params.inTimeEnd}]]></if>
    <if test="params.outTimeBegin != null and params.outTimeEnd != null">
      <![CDATA[and t.OUT_TIME > #{params.outTimeBegin} and t.OUT_TIME < #{params.outTimeEnd}]]></if>
    <if test="params.craftSection != null and params.craftSection != ''">and t.CRAFT_SECTION = #{params.craftSection}</if>
    <if test="params.workStation != null and params.workStation != ''">and t.WORK_STATION = #{params.workStation}</if>
    <if test="params.orgId != null">and t.ORG_ID = cast(#{params.orgId} as numeric)</if>
    <if test="params.factoryId != null">and t.FACTORY_ID = cast(#{params.factoryId} as numeric)</if>
    <if test="params.entityId != null">and t.ENTITY_ID = cast(#{params.entityId} as numeric)</if>
    <if test="params.snStart != null and params.snStart != '' and params.snEnd != null and params.snEnd != ''">and t.SN between #{params.snStart} and
      #{params.snEnd}
    </if>
    <if test="params.attribute1 != null and params.attribute1 != ''">and t.ATTRIBUTE1 = #{params.attribute1}</if>
    <if test="(params.parentSn == null or params.parentSn == '') and (params.wipId == null or params.wipId == '')
			and (params.sn == null or params.sn == '') and (params.snList == null or params.snList.size == 0)
			and (params.itemNo == null or params.itemNo == '') and (params.workOrderNo == null or params.workOrderNo == '')
            and (params.inWorkOrderNo == null or params.inWorkOrderNo == '') and (params.attribute1 == null or params.attribute1 == '')
            and (params.snStart == null or params.snStart == '' or params.snEnd == null or params.snEnd == '')
			and (params.attribute1 == null or params.attribute1 == '')">
      and 1=2
    </if>
    <if test="params.orderField != null">
      <choose>
        <when test="params.orderField=='sn'">order by t.SN
          <if test="params.order != null and params.order == 'desc'">desc</if>
        </when>
        <when test="params.orderField=='workOrderNo'">order by t.WORK_ORDER_NO
          <if
                  test="params.order != null and params.order == 'desc'">desc
          </if>
        </when>
        <when test="params.orderField=='itemNo'">order by t.ITEM_NO
          <if test="params.order != null and params.order == 'desc'">desc
          </if>
        </when>
        <when test="params.orderField=='itemName'">order by t.ITEM_NAME
          <if test="order != null and order == 'desc'">
            desc
          </if>
        </when>
        <when test="params.orderField=='currProcessCode'">order by t.CURR_PROCESS_CODE
          <if
                  test="order != null and order == 'desc'">desc
          </if>
        </when>
        <when test="params.orderField=='status'">order by t.STATUS
          <if test="params.order != null and params.order == 'desc'">desc
          </if>
        </when>
        <when test="params.orderField=='worker'">order by t.WORKER
          <if test="params.order != null and params.order == 'desc'">desc
          </if>
        </when>
        <when test="params.orderField=='lineCode'">order by t.LINE_CODE
          <if test="params.order != null and params.order == 'desc'">
            desc
          </if>
        </when>
        <when test="params.orderField=='workshopCode'">order by t.WORKSHOP_CODE
          <if
                  test="params.order != null and params.order == 'desc'">desc
          </if>
        </when>
        <when test="params.orderField=='inTime'">order by t.IN_TIME
          <if test="params.order != null and params.order == 'desc'">desc
          </if>
        </when>
        <when test="params.orderField=='outTime'">order by t.OUT_TIME
          <if test="params.order != null and params.order == 'desc'">
            desc
          </if>
        </when>
        <when test="params.orderField=='craftSection'">order by t.CRAFT_SECTION
          <if
                  test="params.order != null and params.order == 'desc'">desc
          </if>
        </when>
        <when test="params.orderField=='workStation'">order by t.WORK_STATION
          <if
                  test="params.order != null and params.order == 'desc'">desc
          </if>
        </when>
        <when test="params.orderField=='lastProcess'">order by t.LAST_PROCESS
          <if
                  test="params.order != null and params.order == 'desc'">desc
          </if>
        </when>
        <when test="params.orderField=='createBy'">order by t.CREATE_BY
          <if test="params.order != null and params.order == 'desc'">
            desc
          </if>
        </when>
        <when test="params.orderField=='createDate'">order by t.CREATE_DATE
          <if
                  test="params.order != null and params.order == 'desc'">desc
          </if>
        </when>
        <when test="params.orderField=='lastUpdatedBy'">order by t.LAST_UPDATED_BY
          <if
                  test="params.order != null and params.order == 'desc'">desc
          </if>
        </when>
        <when test="params.orderField=='lastUpdatedDate'">order by t.LAST_UPDATED_DATE
          <if
                  test="params.order != null and params.order == 'desc'">desc
          </if>
        </when>
        <when test="params.orderField=='snQuery'">order by (case when t.CRAFT_SECTION = '入库' then '0' else t.craft_section end) desc,
          t.CURR_PROCESS_CODE,t.WORK_STATION,t.SN asc
        </when>
      </choose>
    </if>
  </select>

  <select id="getWipInfoByProdPlanIdAndLastUpdateDatePage" parameterType="com.zte.springbootframe.common.model.Page" resultMap="BaseResultMap">
    select
    t.sn,t.item_no,t.item_name,t.attribute1,t.attribute2,t.craft_section
    from WIP_INFO t
    where t.enabled_flag = 'Y'
    and t.attribute1  in
    <foreach collection="params.prodPlanIdList" item="prodPlanId" open="(" separator="," close=")">
      #{prodPlanId,jdbcType=VARCHAR}
    </foreach>
    <if test="params.lastExecDate != null">
      and <![CDATA[ t.last_updated_date >= #{params.lastExecDate,jdbcType=TIMESTAMP}  ]]>
    </if>
    and <![CDATA[ t.last_updated_date <= #{params.maxDate,jdbcType=TIMESTAMP}  ]]>
    order by t.create_date,t.sn asc
  </select>
  <select id="getMaxSnDate" resultType="java.util.Date">
    select max(t.last_updated_date)
    from WIP_INFO t
    where t.ENABLED_FLAG = 'Y' and t.last_updated_date is not null
    and t.attribute1 in
    <foreach item="item" collection="prodPlanIdList" open="(" separator="," close=")">
      #{item,jdbcType=VARCHAR}
    </foreach>
    <if test="lastExecDate != null">
      and <![CDATA[ t.last_updated_date >= #{lastExecDate,jdbcType=TIMESTAMP}  ]]>
    </if>
    and <![CDATA[ t.last_updated_date <= #{nowDate,jdbcType=TIMESTAMP}  ]]>
  </select>

  <select id="listSnAndTaskNoBySns" resultType="com.zte.domain.model.PsWipInfo">
    select t.sn, t.attribute2
    from WIP_INFO t
    where t.ENABLED_FLAG = 'Y'
    and t.sn in
    <foreach item="sn" collection="snList" open="(" separator="," close=")">
      #{sn}
    </foreach>
  </select>

  <update id="updateAssemblyIdentification" parameterType="com.zte.interfaces.dto.AssemblyRelationshipQueryDTO" >
    update wip_info tt
    set tt.assemble_flag = we.assemble_flag,tt.sec_assemble_flag = we.sec_assemble_flag,tt.last_updated_date = sysdate,tt.last_updated_by = #{empNo}
    from (
    select CASE WHEN t.main_product_code is null or t.main_product_code = '' THEN 'N' ELSE w.assemble_flag END AS assemble_flag,
           CASE WHEN t.main_product_code is null or t.main_product_code = '' THEN  w.sec_assemble_flag ELSE 'N' END AS sec_assemble_flag,
           t.form_sn
          from  WIP_EXTEND_IDENTIFICATION t join wip_info w on w.sn = t.form_sn
          where t.enabled_flag = 'Y' and w.enabled_flag = 'Y'  and t.FORM_TYPE = '3'
    <if test="taskNo != null and taskNo != ''">and t.TASK_NO = #{taskNo}</if>
    <if test="prodPlanId != null and prodPlanId != ''">and t.PRODPLAN_ID = #{prodPlanId}</if>
    <if test="createBy != null and createBy != ''">and t.create_By = #{createBy}</if>
    <if test="formSnList != null and formSnList.size() > 0">
      AND t.FORM_SN IN
      <foreach collection="formSnList" index="index" item="item" open="(" separator="," close=")">
        #{formSnList[${index}]}
      </foreach>
    </if>
    <if test="snList != null and snList.size() > 0">
      AND t.SN IN
      <foreach collection="snList" index="index" item="item" open="(" separator="," close=")">
        #{snList[${index}]}
      </foreach>
    </if>
    <if test="idList != null and idList.size() > 0">
      AND t.identi_id IN
      <foreach collection="idList" index="index" item="item" open="(" separator="," close=")">
        #{idList[${index}]}
      </foreach>
    </if>
    <if test="(taskNo == null or taskNo == '')
            and (prodPlanId == null or prodPlanId == '')
            and (formSnList == null or formSnList.size() == 0)
            and (snList == null or snList.size() == 0)
            and (idList == null or idList.size() == 0)"> and 1=2</if>
    limit 100000
    ) we
    where  tt.sn = we.form_sn and tt.sn is not null and tt.enabled_flag = 'Y'
  </update>

  <select id="selectRemainPage" resultType="com.zte.domain.model.CmDailyStatisticReport">
    <!-- 可能存在多个子工序一起算结存 -->
    select TASK_NO, STAT_ITEM,SYSDATE STATISTIC_DATE,count(1) STAT_QTY from (
    <foreach collection="codeGroups" item="item" separator=" UNION ALL ">
      SELECT T.ATTRIBUTE2 TASK_NO, ST.FIELD_NAME STAT_ITEM
      FROM
      WIP_INFO T
      LEFT JOIN
      <foreach item="lookup" index="idx" collection="codeGroups" separator="union all" open="(" close=")">
        SELECT #{lookup.lookupMeaning,jdbcType=VARCHAR} CURR_PROCESS_CODE,
        #{lookup.attribute3,jdbcType=VARCHAR} IS_SHOW,
        #{lookup.attribute4,jdbcType=VARCHAR} FIELD_NAME
      </foreach>
      ST ON T.CURR_PROCESS_CODE = ST.CURR_PROCESS_CODE
      WHERE T.ENABLED_FLAG = 'Y'
      <!-- 当工站为空时，则只考虑子工序的取值 -->
      <if test="item.attribute1 != null and item.attribute1 != ''">
        AND T.WORK_STATION = #{item.attribute1}
      </if>
      AND T.CURR_PROCESS_CODE = #{item.lookupMeaning}
      AND T.ATTRIBUTE2 IN
      <foreach item="taskNo" collection="taskNos" open="(" close=")" separator=",">
        #{taskNo}
      </foreach>
      <!-- 定时任务统计不排除隐藏字段，查询和导出需隐藏 -->
      <if test="isJob == false">
        AND ST.IS_SHOW = 'Y'
      </if>
    </foreach>
    ) GROUP BY TASK_NO,STAT_ITEM
  </select>

  <select id="getParentSnBySnList" parameterType="java.util.List" resultType="com.zte.domain.model.PsWipInfo">
    select parent_sn, sn, attribute2, item_no, item_name, work_order_no, status, attribute1, line_code
    from WIP_INFO
    where ENABLED_FLAG = 'Y'
    and parent_sn in
    <foreach item="sn" collection="snList" open="(" separator="," close=")">
      #{sn}
    </foreach>
  </select>

  <update id="updateWipInfoByBatch">
    with temp as(
    <foreach item="item" collection="list" separator="union all" open="(" close=")">
      SELECT #{item.parentSn} parent_sn,
      #{item.processCode} curr_process_code,
      #{item.workStation} work_station,
      #{item.craftSection} craft_section
    </foreach>
    )
    update WIP_INFO t
    set
    t.work_station = st.work_station,
    t.curr_process_code = st.curr_process_code,
    t.craft_section = st.craft_section,
    t.last_updated_date = sysdate,
    t.last_updated_by = #{empNo},
    t.last_process = 'Y'
    from temp st
    where t.enabled_flag = 'Y'
    and t.sn = st.parent_sn
  </update>

  <select id="getWipInfoByParentSnList" parameterType="java.util.List" resultType="com.zte.domain.model.PsWipInfo">
    select sn,attribute2, item_no
    from WIP_INFO
    where ENABLED_FLAG = 'Y'
    and sn in
    <foreach item="parentSn" collection="parentSnList" open="(" separator="," close=")">
      #{parentSn}
    </foreach>
  </select>

  <select id="getWipInfoByParentSnBatch" parameterType="java.util.Map" resultType="com.zte.interfaces.dto.SnUnBindDTO">
    select sn,parent_sn,attribute2 as taskNo,attribute1 as prodplanId,work_order_no,curr_process_code,work_station
    from WIP_INFO
    where ENABLED_FLAG = 'Y'
    and PARENT_SN in
    <foreach item="parentSn" collection="parentSnList" open="(" separator="," close=")">
      #{parentSn}
    </foreach>
    limit #{endRow} offset #{startRow}
  </select>

  <select id="getWipInfoBySnBatch" parameterType="java.util.Map" resultType="com.zte.interfaces.dto.SnUnBindDTO">
    select sn,parent_sn,attribute2 as taskNo,attribute1 as prodplanId,work_order_no,curr_process_code,work_station
    from WIP_INFO
    where ENABLED_FLAG = 'Y'
    and sn in
    <foreach item="sn" collection="snList" open="(" separator="," close=")">
      #{sn}
    </foreach>
    limit #{endRow} offset #{startRow}
  </select>

  <select id="getParentSnBySn" parameterType="java.util.List" resultType="java.lang.String">
    select sn
    from WIP_INFO
    where ENABLED_FLAG = 'Y'
    and parent_sn in
    <foreach item="parentSn" collection="parentSnList" open="(" separator="," close=")">
      #{parentSn}
    </foreach>
  </select>

  <select id="checkSnExist" parameterType="java.util.List" resultType="java.lang.String">
    select sn
    from WIP_INFO
    where ENABLED_FLAG = 'Y'
    and sn in
    <foreach item="sn" collection="snList" open="(" separator="," close=")">
      #{sn}
    </foreach>
  </select>

  <select id="checkSnSameState" parameterType="java.util.List"  resultType="com.zte.domain.model.PsWipInfo">
    select t.work_order_no, t.curr_process_code, t.work_station
    from WIP_INFO t
    where t.ENABLED_FLAG = 'Y'
    and t.sn in
    <foreach item="sn" collection="snList" open="(" separator="," close=")">
      #{sn}
    </foreach>
    group by t.work_order_no, t.curr_process_code, t.work_station
  </select>
  <select id="selectMaxSnOfProd" resultType="java.lang.String">
    select max(t.sn) from wip_info t where t.attribute1  = #{prodPlanId}
  </select>
  <!-- -->
  <select id="getRemovableBarcodeInfo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from WIP_INFO t
    where ENABLED_FLAG = 'Y' AND (attribute1 is null or attribute1 ='')
    and attribute2 = #{taskNo,jdbcType=VARCHAR}
    and  NOT EXISTS
    (select 1 from WIP_EXTEND_IDENTIFICATION m   where m.form_type = '2' and t.sn=m.form_sn  AND m.ENABLED_FLAG='Y'  and m.task_no = #{taskNo,jdbcType=VARCHAR})
    order by sn desc
    limit #{qty,jdbcType=DECIMAL}
  </select>

  <!-- 获取任务打印数量（未扫描）-->
  <select id="getTaskPrintingQuantity" resultType="java.lang.Integer">
    select
    count(sn)
    from WIP_INFO t
    where ENABLED_FLAG = 'Y' AND (attribute1 is null or attribute1 ='')
    and attribute2 = #{taskNo,jdbcType=VARCHAR}
    and  NOT EXISTS
    (select 1 from WIP_EXTEND_IDENTIFICATION m   where m.form_type = '2' and t.sn=m.form_sn  AND m.ENABLED_FLAG='Y'  and m.task_no = #{taskNo,jdbcType=VARCHAR})
  </select>

  <!-- 获取任务打印数量（未扫描）-->
  <select id="getCouldDelSns" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from WIP_INFO t
    where ENABLED_FLAG = 'Y' AND (attribute1 is null or attribute1 ='')
    and sn in
    <foreach item="sn" collection="delSnList" open="(" separator="," close=")">
      #{sn}
    </foreach>
    and  NOT EXISTS
    (select 1 from WIP_EXTEND_IDENTIFICATION m   where m.form_type = '2' and t.sn=m.form_sn  AND m.ENABLED_FLAG='Y'  and m.task_no = #{taskNo,jdbcType=VARCHAR})
  </select>

  <select id="getParentSnBySns" parameterType="java.util.List" resultType="com.zte.interfaces.dto.TaskNoQueryDTO">
    select * from
      (select parent_sn as sn, attribute2 as taskNo, work_order_no,
      row_number() over (partition by parent_sn order by last_updated_date desc) as rank
      from WIP_INFO
      where ENABLED_FLAG = 'Y'
      and parent_sn in
      <foreach item="parentSn" collection="parentSnList" open="(" separator="," close=")">
        #{parentSn}
      </foreach>) t
    where t.rank = 1
  </select>

  <select id="selectWipInfoByBatch" resultType="com.zte.domain.model.PsWipInfo">
    SELECT
    w.attribute1,
    w.sn
    FROM
    wip_info w
    WHERE
    w.attribute1 IN
    <foreach collection="prodPlanIds" item="prodPlanId" open="(" separator="," close=")">
      #{prodPlanId}
    </foreach>
    AND w.enabled_flag = 'Y'
  </select>
  <select id="getNeedPushSnByProdplanId" resultType="com.zte.domain.model.PsWipInfo">
      select t.sn, t.attribute1, t.last_updated_date from wip_info t
      where t.enabled_flag = 'Y'
      and t.last_updated_date >= #{startTime}
      and t.attribute1 in
      <foreach collection="prodplanIdList" item="prodPlanId" open="(" separator="," close=")">
        #{prodPlanId}
      </foreach>
      and t.curr_process_code not in
      <foreach collection="notInProcessCode" item="processCode" open="(" separator="," close=")">
        #{processCode}
      </foreach>
      <if test="sn != null and sn != ''">
        and not exists ( select 1 from wip_info a where t.sn = a.sn and a.sn &lt;= #{sn} and a.last_updated_date = #{startTime})
      </if>
      order by t.last_updated_date, t.sn
      limit #{limit}
    </select>

  <select id="selectInboundSnListBatch" resultType="java.lang.String" parameterType="java.util.List">
    select sn
      from WIP_INFO
      where ENABLED_FLAG = 'Y'
      and sn IN
      <foreach collection="list" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
    and curr_process_code = 'N'
    and craft_section = '入库'
  </select>


  <select id="getSnInfoList" resultType="com.zte.domain.model.PsWipInfo">
    select sn, attribute2, last_updated_date,curr_process_code,work_order_no  from wip_info t
    where enabled_flag = 'Y'
    <if test="startTime != null">
      and last_updated_date >= #{startTime}
    </if>
    <if test="sn != null and sn != ''">
      and not exists ( select 1 from wip_info a where t.sn = a.sn and a.sn &lt;= #{sn} and a.last_updated_date = #{startTime})
    </if>
    <if test="taskQuerySn != null and taskQuerySn != ''">
      and sn = #{taskQuerySn}
    </if>
    order by t.last_updated_date, t.sn
    limit #{limit}
  </select>


  <select id="getWipInfoByALi" parameterType="com.zte.springbootframe.common.model.Page" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from WIP_INFO t
    where t.enabled_flag = 'Y'
    <if test="params!= null and params.taskNo != null and params.taskNo !=''">
      and t.attribute2  = #{params.taskNo}
    </if>
    and <![CDATA[ t.last_updated_date >= #{params.startTime,jdbcType=TIMESTAMP} ]]>
    and <![CDATA[ t.last_updated_date <= #{params.endTime,jdbcType=TIMESTAMP}  ]]>
    order by t.last_updated_date,t.sn asc
  </select>
</mapper>
