CREATE TABLE technical_change_detail_his (
   detail_his_id varchar(64) NOT NULL,
   chg_req_no varchar(20) NOT NULL,
   change_time int4 NOT NULL,
   change_type varchar(1) NOT NULL,
   created_by varchar(20) NOT NULL,
   technical_status varchar(2) NOT NULL DEFAULT '0',
   last_updated_by varchar(64) NOT NULL,
   created_date timestamp NOT NULL DEFAULT sysdate,
   last_updated_date timestamp NOT NULL DEFAULT sysdate,
   enabled_flag varchar(1) NOT NULL DEFAULT 'Y',
   prodplan_id varchar(38) NOT NULL,
   craft_section varchar(500) NULL default '',
   product_name varchar(4000) NOT NULL,
   product_code varchar(40) NOT NULL,
   CONSTRAINT technical_change_detail_his_pk PRIMARY KEY (detail_his_id)
);
CREATE INDEX technical_change_detail_his_crn_idx ON technical_change_detail_his (chg_req_no);
CREATE INDEX technical_change_detail_his_pi_idx ON technical_change_detail (prodplan_id);
COMMENT ON TABLE technical_change_detail_his IS '技改明细历史表';

COMMENT ON COLUMN technical_change_detail_his.prodplan_id IS '批次';
COMMENT ON COLUMN technical_change_detail_his.craft_section IS '主工序';
COMMENT ON COLUMN technical_change_detail_his.technical_status IS '0待提交，1拟制中，2锁定中，3已解锁，4已作废';
COMMENT ON COLUMN technical_change_detail_his.product_name IS '料单名称';
COMMENT ON COLUMN technical_change_detail_his.product_code IS '料单代码';
COMMENT ON COLUMN technical_change_detail_his.chg_req_no IS '单据号';
COMMENT ON COLUMN technical_change_detail_his.change_time IS '技改次数';
COMMENT ON COLUMN technical_change_detail_his.change_type IS '技改类型：0-技改，1-提前技改';

ALTER TABLE doc_file_properties ADD enabled_flag varchar(1) NOT NULL DEFAULT 'Y';