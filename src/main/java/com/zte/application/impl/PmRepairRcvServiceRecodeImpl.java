/**
 * 项目名称 : ${project_name}
 * 创建日期 : ${date}
 * 修改历史 :
 * 1. [${date}] 创建文件 by ${user}
 **/
package com.zte.application.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Maps;
import com.zte.application.PmRepairRcvDetailService;
import com.zte.application.PmRepairRcvRecodeService;
import com.zte.application.PsScanHistoryService;
import com.zte.application.PsWipInfoService;
import com.zte.common.CommonUtils;
import com.zte.common.DateUtil;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.common.utils.NumConstant;
import com.zte.common.utils.ObtainRemoteServiceDataUtil;
import com.zte.consts.CommonConst;
import com.zte.domain.model.PmRepairRcv;
import com.zte.domain.model.PmRepairRcvDetail;
import com.zte.domain.model.PmRepairRcvDetailRepository;
import com.zte.domain.model.PmRepairRcvRepository;
import com.zte.domain.model.PsScanHistory;
import com.zte.domain.model.PsWipInfo;
import com.zte.domain.model.PsWipInfoRepository;
import com.zte.domain.model.PsWorkOrderBasic;
import com.zte.domain.model.SemiManufactureDealInfo;
import com.zte.domain.vo.PmRepairRcvVo;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.ProductionDeliveryRemoteService;
import com.zte.interfaces.assembler.PmRepairRcvDetailAssembler;
import com.zte.interfaces.dto.BSProcessDTO;
import com.zte.interfaces.dto.PmRepairRcvDTO;
import com.zte.interfaces.dto.PmRepairRcvDetailDTO;
import com.zte.interfaces.dto.PsWipInfoDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.annotation.RecordLogAnnotation;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.redis.RedisLock;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;


/**
 * 送修/返还维护
 *
 * <AUTHOR>
 **/
@Service
public class PmRepairRcvServiceRecodeImpl implements PmRepairRcvRecodeService {
    @Autowired
    private PmRepairRcvServiceImpl pmRepairRcvService;
    @Autowired
    private PmRepairRcvRepository pmRepairRcvRepository;
    @Autowired
    PmRepairRcvDetailRepository pmRepairRcvDetailRepository;

    @Autowired
    private PsWipInfoService psWipInfoService;
    @Autowired
    private PmRepairRcvDetailService pmRepairRcvDetailService;

    @Autowired
    private PsScanHistoryService psScanHistoryService;

    @Autowired
    private PmRepairRcvRecodeService pmRepairRcvRecodeService;

    @Autowired
    private PsWipInfoRepository psWipInfoRepository;

    @Autowired
    private PsWipInfoServiceImpl psWipInfoServiceImpl;
    @Autowired
    private PsScanHistoryServiceImpl scanHistoryServiceImpl;

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int sendRepair(PmRepairRcv record,List<PmRepairRcvDetailDTO> pmRepairRcvDetailDTOs) throws Exception {
        int num = 0;
        // 校验 recode  数据，  》》 总体数据校验
        if(StringUtils.isBlank(record.getFromStation())) {
            throw new Exception(CommonUtils.getLmbMessage(MessageId.REPAIR_SOURCE_IS_EMPTY));
        }
        // 移除重复记录  >> 同一批次可能提交了相同的条码,
        if(isDuplicate(pmRepairRcvDetailDTOs)){
            throw new Exception(MpConstant.SN_DUPLICATE);
        }
        // 获取DIP， assyoutput 子工序 code ，获取主工序code
        // 获取dip下限扫描的  getProcessCode
        Map<String, String> processMap = getProcessMap();
        // 待更新投入产出的指令
        List<PsWorkOrderBasic> listWorkOrder = new ArrayList<PsWorkOrderBasic>();
        //插入送修头表
        num = saveRcvHead(record);
        //整机写交易数据
        List<SemiManufactureDealInfo> machineList = new ArrayList<>();
        List<RedisLock> redisLocks = new ArrayList<RedisLock>();
        try{
            // 处理每一条的送修数据u
            for (PmRepairRcvDetailDTO pmRepairRcvDetailDTO : pmRepairRcvDetailDTOs) {
                // 校验每一条数据
                validParam(pmRepairRcvDetailDTO,record);
                RedisLock redisLock = new RedisLock(Constant.REPAIR+"_"+pmRepairRcvDetailDTO.getSn());
                boolean lock = redisLock.lock();
                redisLocks.add(redisLock);
                if (!lock) {
                    logger.error(pmRepairRcvDetailDTO.getSn() + CommonUtils.getLmbMessage(MessageId.SN_IS_SUBMITED_BY_OTHER));
                    throw new Exception(pmRepairRcvDetailDTO.getSn() + CommonUtils.getLmbMessage(MessageId.SN_IS_SUBMITED_BY_OTHER));
                }
                PsWipInfo wipInfo = psWipInfoService.getWipInfoBySn(pmRepairRcvDetailDTO.getSn());
                if(!ObjectUtils.isEmpty(wipInfo) && !wipInfo.getWipId().isEmpty()){
                    // 处理需要保存的详表实体数据
                    initPmRepairRcvDetailDtoData(pmRepairRcvDetailDTO,record,wipInfo);
                    pmRepairRcvDetailDTO.setStatus(Constant.REPAIR_STATUS_WAIT);
                    // 如果是整
                    if ((Constant.FROM_STATION_MACHINE).equals(record.getFromStation())) {
                        //整机插入 set数据
                        machineList.add(getSemiManufactureDealInfo(record,wipInfo,pmRepairRcvDetailDTO));
                    }
                }else{
                    throw new Exception(CommonUtils.getLmbMessage(MessageId.WIP_INFO_DATA_NOT_FOUND,pmRepairRcvDetailDTO.getSn()));
                }
            }
            // 批量增加送修详表数据
            num = pmRepairRcvDetailService.insertPmRepairRcvDetailBatch(pmRepairRcvDetailDTOs, CommonConst.BATCH_SIZE);
            updateWipInfo(record,processMap,pmRepairRcvDetailDTOs);
            //整机处理 DQAS, 推送数据,    !!!重要， 处理DQAS必须放在 插入交易表(点对点调用配送服务)之前, 否则事务有风险
            //  2019/11/5  维修送修保存中式开关添加
            String productType = psWipInfoServiceImpl.getExternalType(pmRepairRcvDetailDTOs.get(NumConstant.NUM_ZERO).getSn());
            handlerDQAS(record,pmRepairRcvDetailDTOs,productType);
            //半成品插入交易表并更新QTY数量(点对点之后调用), 整机插入交易信息
            handlerDealInfo(record,pmRepairRcvDetailDTOs,machineList,productType);
        }finally{
            for(RedisLock redisLock:redisLocks){
                redisLock.unlock();
            }
        }
        return num ;
    }

    public void addPsWipScanHistory(PsWipInfo oldWip, List<PsScanHistory> psScanHistorys, PsWipInfo newWip) {
        PsScanHistory psScanHistory = new PsScanHistory();
        BeanUtils.copyProperties(oldWip, psScanHistory);
        psScanHistory.setSmtScanId(UUID.randomUUID().toString());
        psScanHistory.setParentSn(oldWip.getParentSn());
        psScanHistory.setLineCode(oldWip.getLineCode());
        psScanHistory.setWorkshopCode(oldWip.getWorkshopCode());
        psScanHistory.setFactoryId(oldWip.getFactoryId());
        psScanHistory.setEntityId(oldWip.getEntityId());
        psScanHistory.setItemNo(oldWip.getItemNo());
        psScanHistory.setItemName(oldWip.getItemName());
        psScanHistory.setLastUpdatedBy(newWip.getLastUpdatedBy());
        psScanHistory.setCreateBy(newWip.getLastUpdatedBy());
        psScanHistory.setStatus(Constant.FLAG_Y);
        psScanHistory.setWorker(oldWip.getLastUpdatedBy());
        psScanHistory.setEnabledFlag(Constant.FLAG_Y);
        psScanHistory.setPreWorkOrderNo(oldWip.getWorkOrderNo());
        psScanHistory.setPreWorkStation(oldWip.getWorkStation());
        psScanHistory.setPreProcessCode(oldWip.getCurrProcessCode());
        psScanHistory.setSourceImu(oldWip.getSourceImu());
        psScanHistory.setWorkOrderNo(oldWip.getWorkOrderNo());
        psScanHistory.setSn(newWip.getSn());
        psScanHistory.setCurrProcessCode(newWip.getCurrProcessCode());
        psScanHistory.setCraftSection(newWip.getCraftSection());
        psScanHistory.setWorkStation(newWip.getWorkStation());
        psScanHistorys.add(psScanHistory);
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    @RecordLogAnnotation("保存或删除送修单数据")
    public int sendRepairNew(PmRepairRcv record,List<PmRepairRcvDetailDTO> pmRepairRcvDetailDTOs) throws Exception {
        // 是否为终端维修
        boolean terminalFlag = MpConstant.PAGE_TYPE_TERMINAL.equals(String.valueOf(record.getPageType()));
        checkWorkOrderNoEmpty(pmRepairRcvDetailDTOs);
        //校验数据是否为空
        validInputParam(pmRepairRcvDetailDTOs, terminalFlag);
        Map<String, List<PmRepairRcvDetailDTO>> groupMap = pmRepairRcvDetailDTOs.stream().collect(Collectors.groupingBy(PmRepairRcvDetailDTO::getOperationType));
        List<PmRepairRcvDetailDTO> detailDeleteDTOList = groupMap.get(Constant.DELETE);
        List<PmRepairRcvDetailDTO> insertOrUpdateList = groupMap.get(Constant.STR_EMPTY);
        int num =Constant.INT_0;
        // 更新或插入数据
        num = insertOrUpdate(record, pmRepairRcvDetailDTOs, insertOrUpdateList, num, terminalFlag);
        // 删除详情表
        deleteDetailList(record, detailDeleteDTOList, terminalFlag);
        return num ;
    }

    /**
     * 校验送修条码wip_info表指令是否为空
     * @param pmRepairRcvDetailDTOs
     * @throws MesBusinessException
     */
    public void checkWorkOrderNoEmpty(List<PmRepairRcvDetailDTO> pmRepairRcvDetailDTOs) throws MesBusinessException {
        //校验送修条码wip_info表指令是否为空
        List<String> snList= pmRepairRcvDetailDTOs.stream().map(e->e.getSn()).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(snList)){
            List<String> workOrderNoEmptyList=new ArrayList<>();
            CommonUtils.splitList(snList,NumConstant.NUM_300).forEach(p->{
                List<PsWipInfo> psWipInfoList=psWipInfoRepository.getWorkOrderNoEmptyWipInfoBySns(p);
                if(CollectionUtils.isNotEmpty(psWipInfoList)){
                    workOrderNoEmptyList.addAll(psWipInfoList.stream().map(e->e.getSn()).collect(Collectors.toList()));
                }
            });
            if(CollectionUtils.isNotEmpty(workOrderNoEmptyList)){
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,MessageId.SN_WORKORDERNO_INFO_IS_EMPTY_IN_WIPINFO,new Object[]{StringUtils.join(workOrderNoEmptyList,Constant.COMMA)});
            }
        }
    }

    /**
     * 校验送修条码提交时在制表子工序是否发生改变
     */
    private void checkSnProcessChange(List<PmRepairRcvDetailDTO> pmRepairRcvDetailDTOs) {
        List<String> snList = pmRepairRcvDetailDTOs.stream().map(PmRepairRcvDetailDTO::getSn).distinct().collect(Collectors.toList());
        List<PsWipInfoDTO> wipInfoList = psWipInfoService.getWipInfoList(snList);
        Map<String, String> snProcessMap = wipInfoList.stream().collect(Collectors.toMap(PsWipInfoDTO::getSn, PsWipInfoDTO::getCurrProcessCode));
        StringBuilder msg = new StringBuilder();
        for (PmRepairRcvDetailDTO pmRepairRcvDetailDTO : pmRepairRcvDetailDTOs) {
            if(null != snProcessMap.get(pmRepairRcvDetailDTO.getSn())
                    && (!Objects.equals(pmRepairRcvDetailDTO.getProcessCode(), snProcessMap.get(pmRepairRcvDetailDTO.getSn()))
                    && !StringUtils.equals(snProcessMap.get(pmRepairRcvDetailDTO.getSn()), Constant.REPAIR_ING))) {
                msg.append(pmRepairRcvDetailDTO.getSn()).append(Constant.EMPTY_STRING);
            }
        }
        if(StringUtils.isNotBlank(msg)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.REPAIR_SN_PROCESS_CHANGE, new String[]{msg.toString()});
        }
    }
    public int insertOrUpdate(PmRepairRcv record, List<PmRepairRcvDetailDTO> pmRepairRcvDetailDTOs,
                              List<PmRepairRcvDetailDTO> insertOrUpdateList, int num, boolean terminalFlag) throws Exception {
        this.checkPmRepairStatus(record.getDeliveryNo(),pmRepairRcvDetailDTOs);
        if (CollectionUtils.isNotEmpty(insertOrUpdateList)) {
            //是否在拟制中
            // 校验 recode  数据，  》》 总体数据校验
            if (StringUtils.isBlank(record.getFromStation())) {
                throw new Exception(CommonUtils.getLmbMessage(MessageId.REPAIR_SOURCE_IS_EMPTY));
            }
            // 移除重复记录  >> 同一批次可能提交了相同的条码,
            if (isDuplicate(insertOrUpdateList)) {
                throw new Exception(MpConstant.SN_DUPLICATE);
            }
            // 获取DIP， assyoutput 子工序 code ，获取主工序code
            // 获取dip下限扫描的  getProcessCode
            Map<String, String> processMap = getProcessMap();
            // 获取新增和更新list
            List<PmRepairRcvDetailDTO> insertList = insertOrUpdateList.stream()
                    .filter(pro -> null == pro.getReceptionId())
                    .collect(Collectors.toList());
            List<PmRepairRcvDetailDTO> updateList = insertOrUpdateList.stream()
                    .filter(pro -> null != pro.getReceptionId())
                    .collect(Collectors.toList());
            //送修信息存在就更新，不存在就新增
            num = insertMethod(record, num, processMap, insertList, terminalFlag);
            updateMethod(record, pmRepairRcvDetailDTOs, updateList, terminalFlag);
        }
        return num;
    }

    private void validInputParam(List<PmRepairRcvDetailDTO> pmRepairRcvDetailDTOs, boolean terminal) throws Exception {
        if (CollectionUtils.isEmpty(pmRepairRcvDetailDTOs)){
            throw new Exception(CommonUtils.getLmbMessage(MessageId.NO_DATA_TO_SAVE));
        }
        // 终端产品 校验有无条码录入
        validSnType(pmRepairRcvDetailDTOs.get(Constant.INT_0));
        // 终端维修查不到子工序，不判断
        if (!terminal) {
            checkSnProcessChange(pmRepairRcvDetailDTOs);
            //判断条码子工序是否能送修
            judgeExistCanNotRepairSn(pmRepairRcvDetailDTOs);
        }
        //如果已经在数据库存在，校验是否可以修改、删除
        for (PmRepairRcvDetailDTO detail : pmRepairRcvDetailDTOs){
            if (StringUtils.isNotBlank(detail.getReceptionId())){
                if (!Constant.REPAIR_STATUS_FICTION.equals(detail.getStatus())){
                    throw new Exception(detail.getSn() + CommonUtils.getLmbMessage(MessageId.DATA_REPAIR_STATUS_IS_NOT_FICTION));
                }
            }
        }
    }

    /**
     * 终端产品 校验有无条码录入
     */
    private void validSnType(PmRepairRcvDetailDTO dto) throws MesBusinessException {
        if (!MpConstant.PAGE_TYPE_TERMINAL.equals(dto.getPageType())) {
            return;
        }
        String snType = dto.getSnType();
        String prodplanId = dto.getSn().substring(0, 7);
        if (MpConstant.SN_TYPE_NO_SN.equals(snType)) {
            // 无条码 校验此批次是否有输入类型为“有条码录入”送修记录，如果有则报错
            if (pmRepairRcvDetailRepository.getProdplanSnRecord(prodplanId, MpConstant.SN_TYPE_HAS_SN) > 0) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PRODPLAN_EXIST_SN_RECORD);
            }
        } else {
            // 有条码 校验此批次是否有输入类型为“无条码录入”送修记录，如果有则报错
            if (pmRepairRcvDetailRepository.getProdplanSnRecord(prodplanId, MpConstant.SN_TYPE_NO_SN) > 0) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PRODPLAN_EXIST_NO_SN_RECORD);
            }
        }
    }

    private void updateMethod(PmRepairRcv record, List<PmRepairRcvDetailDTO> pmRepairRcvDetailDTOs,
                              List<PmRepairRcvDetailDTO> updateList, boolean terminalFlag) throws Exception {
        if (CollectionUtils.isNotEmpty(updateList)) {
            //更新头信息
            record.setReceptionId(pmRepairRcvDetailDTOs.get(Constant.INT_0).getReceptionId());
            record.setBillType(MpConstant.REPAIR_BILL_TYPE_TEN);
            record.setEnabledFlag(Constant.FLAG_Y);
            pmRepairRcvRepository.updatePmRepairRcvByCodeSelective(record);
            // 处理每一条的送修数据
            dealDetailInfo(record, updateList, terminalFlag);
            // 更新详情信息
            pmRepairRcvDetailService.updateDetailByReceptionDetailId(updateList, CommonConst.BATCH_SIZE);
        }
    }

    private int insertMethod(PmRepairRcv record, int num, Map<String, String> processMap,
                             List<PmRepairRcvDetailDTO> insertList, boolean terminalFlag) throws Exception {
        if (CollectionUtils.isNotEmpty(insertList)) {
            PmRepairRcvDTO rcvDTO = new PmRepairRcvDTO();
            rcvDTO.setDeliveryNo(record.getDeliveryNo());
            List<PmRepairRcv> rcvList=pmRepairRcvRepository.getList(rcvDTO);
            if (CollectionUtils.isEmpty(rcvList)){
                //插入送修头表
                num = saveRcvHead(record);
            }else {
                record.setReceptionId(rcvList.get(CommonConst.INT_0).getReceptionId());
            }
            // 处理每一条的送修数据
            dealDetailInfo(record, insertList, terminalFlag);
            // 批量增加送修详表数据
            num = pmRepairRcvDetailService.insertPmRepairRcvDetailBatch(insertList, CommonConst.BATCH_SIZE);
            if (!terminalFlag) {
                // 更新wipInfo表
                updateWipInfo(record, processMap, insertList);
            }
        }
        return num;
    }

    @Transactional(rollbackFor = Exception.class)
    public void dealDetailInfo(PmRepairRcv record, List<PmRepairRcvDetailDTO> insertOrUpdateList, boolean terminalFlag) throws Exception {
        for (PmRepairRcvDetailDTO pmRepairRcvDetailDTO : insertOrUpdateList) {
            // 校验每一条数据
            // 报废校验
            Long o = scapValid(pmRepairRcvDetailDTO);
            if (o > Constant.INT_0) {
                throw new Exception(pmRepairRcvDetailDTO.getSn() + CommonUtils.getLmbMessage(MessageId.SN_IS_SCRAPPED));
            }
            if (null==pmRepairRcvDetailDTO.getReceptionId()) {
                //返还校验
                List<PmRepairRcv> pmRepairRcvList = returnValid(pmRepairRcvDetailDTO);
                if (CollectionUtils.isNotEmpty(pmRepairRcvList)) {
                    throw new Exception(pmRepairRcvDetailDTO.getSn() + CommonUtils.getLmbMessage(MessageId.SN_HAS_NOT_BEEN_REPAIR_RETURNED));
                }
            }
            if (!terminalFlag) {
                // 在制表及测试表校验
                List<PsWipInfo> wipInfo = psWipInfoService.getWipInfoJoinTestBySn(pmRepairRcvDetailDTO.getSn());
                if (CollectionUtils.isEmpty(wipInfo)) {
                    throw new Exception(CommonUtils.getLmbMessage(MessageId.WIP_INFO_DATA_NOT_FOUND, pmRepairRcvDetailDTO.getSn()));
                }
                // 根据来源 校验 单板工序状态
                pmRepairRcvService.checkValue(record.getFromStation(), pmRepairRcvDetailDTO.getSn(), wipInfo);
            }
            // 如果扫描的是单板，再校验对应的整机，如果扫描的是整机，校验对应的单板
            validBoardOrDevice(record, pmRepairRcvDetailDTO);
            // 处理需要保存的详表实体数据
            initRepaireDetail(record, pmRepairRcvDetailDTO);
        }
    }

    private Long scapValid(PmRepairRcvDetailDTO pmRepairRcvDetailDTO)throws Exception {
        PmRepairRcvDTO prrs = new PmRepairRcvDTO();
        //条码报废校验
        prrs.setSn(pmRepairRcvDetailDTO.getSn());
        prrs.setBillType(MpConstant.REPAIR_BILL_TYPE_TEN);
        prrs.setStatus(Constant.REPAIR_STATUS_SCRAP);
        Long o = pmRepairRcvRepository.getRelOneCount(prrs);
        return o;
    }

    public List<PmRepairRcv> returnValid(PmRepairRcvDetailDTO pmRepairRcvDetailDTO) {
        // 维修返还校验
        PmRepairRcvDTO rcvDto = new PmRepairRcvDTO();
        rcvDto.setSn(pmRepairRcvDetailDTO.getSn());
        rcvDto.setInAcceptStatus(Constant.INT_0 + Constant.COMMA + Constant.INT_2);
        List<PmRepairRcv> pmRepairRcvList = pmRepairRcvRepository.getPmRepairRcvDetail(rcvDto);
        return pmRepairRcvList;
    }

    private void validBoardOrDevice(PmRepairRcv record, PmRepairRcvDetailDTO pmRepairRcvDetailDTO) throws Exception {
        if (Constant.DEVICE_PRODUCTION_MAINTENANCE.equals(record.getFromStation())) {
            //单板对应整机校验
            boardValid(pmRepairRcvDetailDTO);
            //整机对应单板校验
            deviceValid(pmRepairRcvDetailDTO);
        }
    }

    private void deviceValid(PmRepairRcvDetailDTO pmRepairRcvDetailDTO) throws Exception {
        // 如果扫描是整机条码，对应单板条码校验
        if (Constant.INT_15!=(pmRepairRcvDetailDTO.getItemCode().length())) {
            if (StringUtils.isNotBlank(pmRepairRcvDetailDTO.getDeviceBarCode())){

                PmRepairRcvDetailDTO tempPm = new PmRepairRcvDetailDTO();
                BeanUtils.copyProperties(pmRepairRcvDetailDTO, tempPm);
                //报废校验
                pmRepairRcvDetailDTO.setSn(tempPm.getDeviceBarCode());
                Long o = scapValid(pmRepairRcvDetailDTO);
                if (o > Constant.INT_0) {
                    throw new Exception(pmRepairRcvDetailDTO.getSn() + CommonUtils.getLmbMessage(MessageId.SN_OF_THE_DEVICE_IS_SCRAPPED));
                }
                //返还校验
                List<PmRepairRcv> pmRepairRcvList = returnValid (pmRepairRcvDetailDTO);
                if(CollectionUtils.isNotEmpty(pmRepairRcvList)) {
                    throw new Exception(pmRepairRcvDetailDTO.getSn() + CommonUtils.getLmbMessage(MessageId.STATUS_NO_RESTORE_DEVICE));
                }
//                // 在制表及测试表校验
//                List<PsWipInfo> wipInfo = psWipInfoService.getWipInfoJoinTestBySn(pmRepairRcvDetailDTO.getDeviceBarCode());
//                if (CollectionUtils.isEmpty(wipInfo)) {
//                    throw new Exception(pmRepairRcvDetailDTO.getDeviceBarCode() + CommonUtils.getLmbMessage(MessageId.NOT_FIND_RECORD_OF_SN));
//                }
                pmRepairRcvDetailDTO.setSn(tempPm.getSn());
            }
            pmRepairRcvDetailDTO.setDeviceBarCode(Constant.STR_EMPTY);
        }
    }

    private void boardValid(PmRepairRcvDetailDTO pmRepairRcvDetailDTO) throws Exception {
        if (Constant.INT_15==(pmRepairRcvDetailDTO.getItemCode().length())){
            PmRepairRcvDetailDTO tempPm = new PmRepairRcvDetailDTO();
            BeanUtils.copyProperties(pmRepairRcvDetailDTO, tempPm);
            //报废校验
            pmRepairRcvDetailDTO.setSn(tempPm.getDeviceBarCode());
            Long o = scapValid(pmRepairRcvDetailDTO);
            if (o > Constant.INT_0) {
                throw new Exception(pmRepairRcvDetailDTO.getSn() + CommonUtils.getLmbMessage(MessageId.DEVICE_TO_SN_IS_SCRAP));
            }
            //返还校验
            List<PmRepairRcv> pmRepairRcvList = returnValid (pmRepairRcvDetailDTO);
            if(CollectionUtils.isNotEmpty(pmRepairRcvList)) {
                throw new Exception(pmRepairRcvDetailDTO.getSn() + CommonUtils.getLmbMessage(MessageId.STATUS_NO_RESTORE_DEVICE));
            }
            // 在制表及测试表校验
            List<PsWipInfo> wipInfo = psWipInfoService.getWipInfoJoinTestBySn(pmRepairRcvDetailDTO.getDeviceBarCode());
            if (CollectionUtils.isEmpty(wipInfo)) {
                throw new Exception(pmRepairRcvDetailDTO.getDeviceBarCode() + CommonUtils.getLmbMessage(MessageId.NO_WIPINFO_OF_SN_CORRESPONDING_DEVICE));
            }
            pmRepairRcvDetailDTO.setSn(tempPm.getSn());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @RecordLogAnnotation("刪除送修数据，并回退工序")
    public void deleteDetailList(PmRepairRcv record, List<PmRepairRcvDetailDTO> detailDeleteDTOList, boolean terminalFlag) {
        if(CollectionUtils.isNotEmpty(detailDeleteDTOList)){
            List<PmRepairRcvDetail> detailDeleteList = PmRepairRcvDetailAssembler.toPmRepairRcvDetailList(detailDeleteDTOList);
            pmRepairRcvDetailService.deletePmRepairRcvDetailByReceptionDetailId(detailDeleteList, CommonConst.BATCH_SIZE);
            for(PmRepairRcvDetail detail :detailDeleteList) {
                PmRepairRcvDetail detailInfo = new PmRepairRcvDetail();
                detailInfo.setReceptionId(detail.getReceptionId());
                List<PmRepairRcvDetail> detailList = pmRepairRcvDetailRepository.getDetailList(detailInfo);
                if (CollectionUtils.isEmpty(detailList)) {
                    PmRepairRcv info = new PmRepairRcv();
                    info.setReceptionId(detail.getReceptionId());
                    pmRepairRcvRepository.deletePmRepairRcvByReceptionId(info);
                }
            }
            if (!terminalFlag) {
                returnCraftSection(record, detailDeleteList);
            }
        }
    }

    public void returnCraftSection(PmRepairRcv record, List<PmRepairRcvDetail> detailDeleteList) {
        String fromStation = record.getFromStation();
        if ((Constant.REPAIR_FROM_STATION).equals(fromStation)||(Constant.DEVICE_PRODUCTION_MAINTENANCE).equals(fromStation)
        || Constant.DEVICE_VENEER_MAINTENANCE.equals(fromStation)) {
            //將wipinfo表主工序、子工序、工站還原
            List<PsWipInfo> psWipInfosList = new ArrayList<PsWipInfo>();
            List<PsScanHistory> psScanHistorys = new ArrayList<>();
            for (PmRepairRcvDetail detail : detailDeleteList) {
                PsWipInfo psWipInfo = new PsWipInfo();
                if (Constant.DEVICE_PRODUCTION_MAINTENANCE.equals(record.getFromStation())&&Constant.INT_15==detail.getItemCode().length()){
                    psWipInfo.setSn(detail.getDeviceBarCode());
                }else {
                    psWipInfo.setSn(detail.getSn());
                }
                psWipInfo.setStatus(Constant.STR_Y);
                psWipInfo.setCurrProcessCode(detail.getProcessCode());
                psWipInfo.setWorkStation(detail.getWorkStation());
                psWipInfo.setCraftSection(detail.getCraftSection());
                psWipInfo.setLastUpdatedBy(detail.getLastUpdatedBy());
                psWipInfosList.add(psWipInfo);
                PsWipInfo currWip = psWipInfoService.getWipInfoBySn(psWipInfo.getSn());
                addPsWipScanHistory(currWip, psScanHistorys, psWipInfo);
            }
            //批量修改数据
            pmRepairRcvService.updateBatch(psWipInfosList);

            // 批量写入扫描历史
            scanHistoryServiceImpl.insertPsScanHistoryBatch(psScanHistorys,NumConstant.NUM_100);
        }
    }

    private void initRepaireDetail(PmRepairRcv record, PmRepairRcvDetailDTO pmRepairRcvDetailDTO) {
        pmRepairRcvDetailDTO.setSendFlag(MpConstant.REPAIR_SEND_FLAG_ZERO);
        pmRepairRcvDetailDTO.setRcvFlag(MpConstant.REPAIR_RCV_FLAG_ZERO);
        pmRepairRcvDetailDTO.setIsAccept(new BigDecimal(MpConstant.QTY_TWO));
        if (StringUtils.isEmpty(pmRepairRcvDetailDTO.getReceptionDetailId())) {
            //根据送修单号、条码查询最近一次的维修次数
            int recentCount = pmRepairRcvDetailRepository.getRecentRepairCount(pmRepairRcvDetailDTO.getSn());
            pmRepairRcvDetailDTO.setRepairCount(recentCount + NumConstant.NUM_ONE);

            pmRepairRcvDetailDTO.setReceptionDetailId(UUID.randomUUID().toString());
            pmRepairRcvDetailDTO.setReceptionId(record.getReceptionId());
        }
        pmRepairRcvDetailDTO.setFactoryId(record.getFactoryId());
        pmRepairRcvDetailDTO.setCreateBy(record.getCreateBy());
        pmRepairRcvDetailDTO.setLastUpdatedBy(record.getLastUpdatedBy());
        pmRepairRcvDetailDTO.setEntityId(record.getEntityId());
        pmRepairRcvDetailDTO.setStatus(Constant.REPAIR_STATUS_FICTION);
    }

    /**
     * 处理中式接口
     * @param record
     * @param pmRepairRcvDetailDTOs
     * @param productType
     * @throws Exception
     */
    private void handlerDQAS(PmRepairRcv record,List<PmRepairRcvDetailDTO> pmRepairRcvDetailDTOs,String productType) throws  Exception{
        SysLookupTypesDTO sysLookupTypesDTO = BasicsettingRemoteService.getSysLookUpValue(Constant.SYS_LOOK_DQAS,Constant.LOOKUP_DQAS_SEND_SAVE_SWITCH_);
        if(sysLookupTypesDTO != null && Constant.FLAG_Y.equals(sysLookupTypesDTO.getLookupMeaning())){
            handlerDQASWhenMachine(record,pmRepairRcvDetailDTOs,productType);
        }
    }

    /**
     * 处理交易信息
     * @param record
     * @param pmRepairRcvDetailDTOs
     * @param machineList
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    public void handlerDealInfo(PmRepairRcv record,List<PmRepairRcvDetailDTO> pmRepairRcvDetailDTOs,List<SemiManufactureDealInfo> machineList,String productType) throws Exception{
        //半成品插入交易表并更新QTY数量(点对点之后调用)
        if ((Constant.FROM_STATION_SEMIS).equals(record.getFromStation())) {
            insertSemis(record,pmRepairRcvDetailDTOs);
        }else if ((Constant.FROM_STATION_MACHINE).equals(record.getFromStation())) {
            //  根据 parent_sn 删除wip_info
            for(PmRepairRcvDetailDTO pmRepairRcvDetailDTO:pmRepairRcvDetailDTOs){
                psWipInfoRepository.deleteWipInfoByParentnSn(pmRepairRcvDetailDTO.getSn());
            }
            for(SemiManufactureDealInfo semiManufactureDealInfo:machineList){
                semiManufactureDealInfo.setRemark(productType);
            }
            //整机插入交易表
            insertMachine(machineList);
        }
    }
    /**
     * 整机处理 DQAS, 推送数据
     */
    private boolean handlerDQASWhenMachine(PmRepairRcv record,List<PmRepairRcvDetailDTO> pmRepairRcvDetailDTOs,String productType) throws  Exception{
        return sendRepairDataToDQAS(record,pmRepairRcvDetailDTOs,productType);
    }
    private boolean sendRepairDataToDQAS(PmRepairRcv record,List<PmRepairRcvDetailDTO> pmRepairRcvDetailDTOs,String productType) throws Exception{
        return sendRepairDQAS(packageRcvPostData(record,pmRepairRcvDetailDTOs,productType));
    }

    /**
     * 封装接口参数
     * @param record
     * @param pmRepairRcvDetailDTOs
     * @return
     */
    public Map<String, Object> packageRcvPostData(PmRepairRcv record,List<PmRepairRcvDetailDTO> pmRepairRcvDetailDTOs,String productType) throws  Exception{
        Map<String, Object> postMap = Maps.newHashMap();
        List<String> barcodes = new ArrayList<String>();
        for(PmRepairRcvDetailDTO pmRepairRcvDTO:pmRepairRcvDetailDTOs){
            barcodes.add(pmRepairRcvDTO.getSn());
        }
        postMap.put("productType",productType);
        postMap.put("barcodes",barcodes);
        postMap.put("scanningTime", DateUtil.convertDateToString(new Date(),DateUtil.DATE_FORMATE_FULL));
//        postMap.put("machineMaterialcode",record.getItemCode());
        postMap.put("processName",Constant.CRAFTSECTION_REPAIR);
        postMap.put("station",Constant.CRAFTSECTION_REPAIR);
        postMap.put("barcodeFrom",Constant.FROM_STATION_MACHINE.equals(record.getFromStation())?Constant.FROM_STATION_MACHINE:Constant.STEP);

        postMap.put("status",Constant.INT_0);
        postMap.put("userId",record.getCreateBy());
        return postMap;
    }
    /**
     * 调用DQAS推送数据, 送修返还可以共用改方法
     * @param mapPost
     * @return
     */
    @Override
    public boolean sendRepairDQAS(Map<String, Object> mapPost) {
        // 获取联机平台url地址
        SysLookupTypesDTO sysLookupTypesDTO = BasicsettingRemoteService.getSysLookUpValue(Constant.SYS_LOOK_DQAS, Constant.LOOKUP_TYPE_DQAS_REPAIR_POST);
        if (sysLookupTypesDTO == null) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.REPAIR_ZS_VAILDATE_FAIL);
        }
        // 调用联机平台的plc放行服务
        String paramsPost = JacksonJsonConverUtil.beanToJson(mapPost);
        String result = HttpClientUtil.httpPostWithJSON(sysLookupTypesDTO.getLookupMeaning(), paramsPost, null);
        JsonNode json = null;
        try {
            json = JacksonJsonConverUtil.getMapperInstance().readTree(result);
        } catch (Exception e) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.CUSTOMIZE_MSG, new Object[]{e.getMessage()});
        }
        if (json == null) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.REPAIR_ZS_VAILDATE_FAIL);
        }
        JsonNode msgNode = json.get(Constant.STR_MSG);
        if (msgNode == null) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.REPAIR_ZS_VAILDATE_FAIL);
        }
        String code = msgNode.get(Constant.STR_CODE) == null ? "" : msgNode.get(Constant.STR_CODE).asText();
        if (Constant.STR_NUMBER_ZERO.equals(code)) {
            return true;
        } else {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.CUSTOMIZE_MSG, new Object[]{msgNode.get(Constant.STR_MSG).asText()});
        }
    }

    /**
     *  处理整机实体
     * @param record
     * @param wipInfo
     * @param pmRepairRcvDetailDTO
     * @return
     */
    private SemiManufactureDealInfo getSemiManufactureDealInfo(PmRepairRcv record, PsWipInfo wipInfo,PmRepairRcvDetailDTO pmRepairRcvDetailDTO){
        SemiManufactureDealInfo semiManufactureDealInfo = new SemiManufactureDealInfo();
        semiManufactureDealInfo.setLpn(record.getDeliveryNo());
        semiManufactureDealInfo.setProdplanId(wipInfo.getAttribute1());
        semiManufactureDealInfo.setSn(pmRepairRcvDetailDTO.getSn());
        semiManufactureDealInfo.setDealType(Constant.REPAIR_THREE);
        semiManufactureDealInfo.setAttribute1(Constant.REPAIR);
        semiManufactureDealInfo.setAttribute2(wipInfo.getItemNo());
        semiManufactureDealInfo.setAttribute3(Constant.REPAIR_MACHINE_ORDER);
        semiManufactureDealInfo.setFactoryId(pmRepairRcvDetailDTO.getFactoryId());
        return semiManufactureDealInfo;
    }

    /**
     * <AUTHOR>
     * 半成品插入交易表并更新QTY数量(点对点)
     * @param record
     * @param pmRepairRcvDetailDTOs
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    public void insertSemis(PmRepairRcv record,List<PmRepairRcvDetailDTO> pmRepairRcvDetailDTOs) throws Exception{
        StringBuilder sns = new StringBuilder();
        // 1、新增送修记录，判断送修来源，如果为半成品库，校验箱内容是否存在（container_content_info），
        // 如果存在，删除箱内容对应单板条码，更新箱库存数量（stock_onhand_info），
        // 如果箱库存不存在，校验箱内容是否存在，如果存在，删除箱内容；--装箱查询，单板条码条码查询，半成品库装箱查询
        // 2、1满足的情况下，写交易信息表，类型为out；--semi_manufacture_deal_info
        try {
            for(PmRepairRcvDetailDTO pmRepairRcvDetailDTO:pmRepairRcvDetailDTOs){
                sns.append(pmRepairRcvDetailDTO.getSn()).append(",");
            }
            if (sns.length() > 0) {
                sns.deleteCharAt(sns.length() - 1);
                ObtainRemoteServiceDataUtil.getLpn(String.valueOf(sns),record);
            }
        } catch (Exception e) {
            logger.info(Constant.LOG_ERROR + sns + e.getMessage());
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.FAILED_TO_UPDATE_QTY);
        }
    }

    /**
     * @param machineList
     * <AUTHOR>
     * 整机插入交易表(点对点)
     */
    @Override
    public void insertMachine(List<SemiManufactureDealInfo> machineList, boolean writeModeStock) {
        //点对点  整机插入交易表
        if (CollectionUtils.isEmpty(machineList)) {
            return;
        }
        if (writeModeStock) {
            ProductionDeliveryRemoteService.semiManufactureDealInfoInsertBatchAndWriteMode(machineList);
        } else {
            ProductionDeliveryRemoteService.semiManufactureDealInfoInsertBatch(machineList);
        }
    }

    /**
     * <AUTHOR>
     * 整机插入交易表(点对点)
     * @param machineList
     * @throws Exception
     */
    @Override
    public void insertMachine(List<SemiManufactureDealInfo> machineList) {
        insertMachine(machineList,false);
    }

    @Transactional(rollbackFor = Exception.class)
    public void  updateWipInfo(PmRepairRcv record ,Map<String, String> processMap,List<PmRepairRcvDetailDTO> pmRepairRcvDetailDTOs ){
        //批量更新  来源为单板  送修条码在制表实体数据    更新 当前工序、工站、主工序、修改人
        if ((Constant.REPAIR_FROM_STATION).equals(record.getFromStation())||(Constant.DEVICE_PRODUCTION_MAINTENANCE).equals(record.getFromStation())||(Constant.DEVICE_VENEER_MAINTENANCE).equals(record.getFromStation())) {
            List<PsWipInfo> psWipInfos = new ArrayList<PsWipInfo>();
            List<PsScanHistory> psScanHistorys = new ArrayList<>();
            for (PmRepairRcvDetailDTO pmRepairRcvDetailDTO : pmRepairRcvDetailDTOs) {
                PsWipInfo psWipInfo = new PsWipInfo();
                if (Constant.DEVICE_PRODUCTION_MAINTENANCE.equals(record.getFromStation())&&Constant.INT_15==pmRepairRcvDetailDTO.getItemCode().length()){
                    psWipInfo.setSn(pmRepairRcvDetailDTO.getDeviceBarCode());
                }else {
                    psWipInfo.setSn(pmRepairRcvDetailDTO.getSn());
                }
                psWipInfo.setWorkStation(Constant.REPAIR_TWO);
                psWipInfo.setStatus(Constant.FLAG_N);
                psWipInfo.setLastUpdatedBy(pmRepairRcvDetailDTO.getLastUpdatedBy());
                psWipInfos.add(psWipInfo);
                PsWipInfo currWip = psWipInfoService.getWipInfoBySn(psWipInfo.getSn());
                addPsWipScanHistory(currWip, psScanHistorys, psWipInfo);
            }
            //批量修改数据
            pmRepairRcvService.updateBatch(psWipInfos);
        }

    }

    /**
     * 插入送修头表
     * @param record
     * @return
     */
    private Integer saveRcvHead(PmRepairRcv record){
        String receptionId = UUID.randomUUID().toString();
        record.setReceptionId(receptionId);
        int num = Constant.INT_0;
        //插入一条送修头表
        PmRepairRcv pmRepairRcv = new PmRepairRcv();
        BeanUtils.copyProperties(record,pmRepairRcv);
        pmRepairRcv.setReceptionId(receptionId);
        pmRepairRcv.setBillType(MpConstant.REPAIR_BILL_TYPE_TEN);
        pmRepairRcv.setEnabledFlag(Constant.FLAG_Y);
        return pmRepairRcvRepository.insertPmRepairRcvSelective(pmRepairRcv);
    }
    /**
     * 处理待更新投入产出的指令
     */
    private  void handlerWorkOrderList(Map<String, String> processMap,PsWipInfo wipInfo,
                                       List<PsWorkOrderBasic> listWorkOrder,PmRepairRcvDetailDTO pmRepairRcvDetailDTO) throws  Exception{
        Map<String,Object> scanHistory = new HashMap<>();
        String workOrderNo = Constant.STR_EMPTY;

        PsWorkOrderBasic workOrderIn = new PsWorkOrderBasic();
        PsWorkOrderBasic workOrderOut = new PsWorkOrderBasic();
        if(processMap.get(Constant.DIP_STR).equals(wipInfo.getCurrProcessCode()) && processMap.containsKey(Constant.DIP_OFFLINE_SCAN)
                && processMap.get(Constant.DIP_OFFLINE_SCAN).equals(wipInfo.getWorkStation())){
            workOrderOut.setWorkOrderNo(wipInfo.getWorkOrderNo());
            workOrderOut.setAttribute1(MpConstant.WORKORDER_ATTR1_OUTPUT);
            listWorkOrder.add(workOrderOut);
        }
        if(processMap.get(Constant.ASSY_OUT_PUT_STR).equals(wipInfo.getCurrProcessCode())){
            workOrderIn.setWorkOrderNo(wipInfo.getWorkOrderNo());
            workOrderIn.setAttribute1(MpConstant.WORKORDER_ATTR1_INPUT);
            listWorkOrder.add(workOrderIn);

            scanHistory.put("sn", pmRepairRcvDetailDTO.getSn());
            scanHistory.put("currProcessCode", processMap.get(Constant.DIP_STR));
            scanHistory.put("orderField", MpConstant.CREATE_DATE);
            // 取最早一条
            List<PsScanHistory> historyList = psScanHistoryService.getList(scanHistory);
            if(CollectionUtils.isNotEmpty(historyList)){
                workOrderNo = historyList.get(Constant.INT_0).getWorkOrderNo();
                workOrderOut.setWorkOrderNo(workOrderNo);
                workOrderOut.setAttribute1(MpConstant.WORKORDER_ATTR1_OUTPUT);
                listWorkOrder.add(workOrderOut);
            }
        }
    }


    /**
     * 处理送修详表数据实体
     * @param pmRepairRcvDetailDTO
     * @param record
     * @param wipInfo
     */
    private void initPmRepairRcvDetailDtoData(PmRepairRcvDetailDTO pmRepairRcvDetailDTO,PmRepairRcv record, PsWipInfo wipInfo){
        pmRepairRcvDetailDTO.setItemCode(wipInfo.getItemNo());
        pmRepairRcvDetailDTO.setItemName(wipInfo.getItemName());
        pmRepairRcvDetailDTO.setWorkStation(wipInfo.getWorkStation());
        pmRepairRcvDetailDTO.setProcessCode(wipInfo.getCurrProcessCode());
        pmRepairRcvDetailDTO.setCraftSection(wipInfo.getCraftSection());
        pmRepairRcvDetailDTO.setRcvProdplanId(wipInfo.getAttribute1());
        pmRepairRcvDetailDTO.setSendFlag(MpConstant.REPAIR_SEND_FLAG_ZERO);
        pmRepairRcvDetailDTO.setRcvFlag(MpConstant.REPAIR_RCV_FLAG_ZERO);
        pmRepairRcvDetailDTO.setReceptionId(record.getReceptionId());
        pmRepairRcvDetailDTO.setIsAccept(MpConstant.REPAIR_IS_ACCEPT);
        pmRepairRcvDetailDTO.setReceptionDetailId(UUID.randomUUID().toString());
        pmRepairRcvDetailDTO.setFactoryId(record.getFactoryId());
        pmRepairRcvDetailDTO.setCreateBy(record.getCreateBy());
        pmRepairRcvDetailDTO.setLastUpdatedBy(record.getLastUpdatedBy());
        pmRepairRcvDetailDTO.setEntityId(record.getEntityId());
    }
    /**
     * 校验参数:  1. 报废校验、2. 返还校验、3.在制表校验、4. 单板工序状态校验
     * @param pmRepairRcvDetailDTO
     * @param record
     * @throws Exception
     */
    private void validParam(PmRepairRcvDetailDTO pmRepairRcvDetailDTO,PmRepairRcv record) throws Exception{
        // 报废校验
        if(isScrap(pmRepairRcvDetailDTO.getSn())){
            logger.error(pmRepairRcvDetailDTO.getSn() + CommonUtils.getLmbMessage(MessageId.SN_IS_SCRAPPED));
            throw new Exception(pmRepairRcvDetailDTO.getSn() + CommonUtils.getLmbMessage(MessageId.SN_IS_SCRAPPED));
        }
        // 是否返还校验
        if(isNotRepairReturn(pmRepairRcvDetailDTO.getSn())){
            throw new Exception(pmRepairRcvDetailDTO.getSn() + CommonUtils.getLmbMessage(MessageId.SN_HAS_NOT_BEEN_REPAIR_RETURNED));
        }
        // 在制表及测试表校验
        List<PsWipInfo> wipInfo = psWipInfoService.getWipInfoJoinTestBySn(pmRepairRcvDetailDTO.getSn());
        if(CollectionUtils.isEmpty(wipInfo)) {
            throw new Exception(CommonUtils.getLmbMessage(MessageId.WIP_INFO_DATA_NOT_FOUND,pmRepairRcvDetailDTO.getSn()));
        }
        // 根据来源 校验 单板工序状态
        pmRepairRcvService.checkValue(record.getFromStation(),pmRepairRcvDetailDTO.getSn(), wipInfo);
    }

    /**
     * 未做返还处理
     * @param sn
     * @return
     */
    private boolean isNotRepairReturn(String sn){
        PmRepairRcvDTO pmRepairRcvDTO = new PmRepairRcvDTO();
        pmRepairRcvDTO.setSn(sn);
        PmRepairRcv pmRepairRcv = pmRepairRcvRepository.getOnePmRepairRcvDetail(pmRepairRcvDTO);
        return pmRepairRcv!=null && pmRepairRcv.getReceptionId()!=null && pmRepairRcv.getIsAccept().intValue()==0;
    }
    /**
     * 条码是否报废
     * @param sn
     * @return
     */
    private boolean isScrap(String sn){
        PmRepairRcvDTO prrs = new PmRepairRcvDTO();
        prrs.setSn(sn);
        prrs.setBillType(MpConstant.REPAIR_BILL_TYPE_TEN);
        prrs.setStatus(Constant.REPAIR_STATUS_SCRAP);
        return pmRepairRcvRepository.getRelOneCount(prrs) > 0 ? true:false;
    }

    /**
     * 获取 dip,assny 等的 code
     * @return
     */
    public Map<String,String> getProcessMap(){
        List<BSProcessDTO> bsProcessList = new ArrayList<BSProcessDTO>();
        Map<String, String> processMap = new HashMap<>();
        try {
            bsProcessList =  ObtainRemoteServiceDataUtil.getProcessInfo( MpConstant.PROCESS_X_TYPE_P);
            pmRepairRcvService.verification(bsProcessList,processMap);
        } catch (Exception e) {
            logger.error(Constant.LOG_PROCESS_ERROR, e);
        }
        List<BSProcessDTO> bsProcessForWorkStation = new ArrayList<BSProcessDTO>();
        try {
            bsProcessForWorkStation = ObtainRemoteServiceDataUtil.getProcessInfo( MpConstant.PROCESS_X_TYPE_S);
            pmRepairRcvService.verificationDip(bsProcessForWorkStation,processMap);
        } catch (Exception e) {
            logger.error(Constant.LOG_PROCESS_ERROR, e);
        }
        return processMap;
    }

    /**
     * 移除重复的条码对象
     * @param list
     * @return
     */
    public   static   boolean isDuplicate(List<PmRepairRcvDetailDTO> list)  {
        for  ( int  i  =   0 ; i  <  list.size()  -   1 ; i ++ )  {
            for  ( int  j  =  list.size()  -   1 ; j  >  i; j -- )  {
                if  (list.get(j).getSn().equals(list.get(i).getSn()))  {
                    return true;
                }
            }
        }
        return false;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public  int submitRepairOrder(PmRepairRcv record, List<PmRepairRcvDetailDTO> pmRepairRcvDetailDTOs) throws Exception{
        if (CollectionUtils.isEmpty(pmRepairRcvDetailDTOs)){
            throw new Exception(CommonUtils.getLmbMessage(MessageId.NO_MESSAGE_TO_SUBMIT));
        }
        //校验条码在制表指令是否为空
        checkWorkOrderNoEmpty(pmRepairRcvDetailDTOs);
        this.checkPmRepairStatus(record.getDeliveryNo(),pmRepairRcvDetailDTOs);
        int num =Constant.INT_0;
        //整机写交易数据
        List<SemiManufactureDealInfo> machineList = new ArrayList<>();
        List<RedisLock> redisLocks = new ArrayList<RedisLock>();
        boolean terminalFlag = MpConstant.PAGE_TYPE_TERMINAL.equals(String.valueOf(record.getPageType()));
        try{
            if (!terminalFlag) {
                //判断条码子工序是否能送修
                judgeExistCanNotRepairSn(pmRepairRcvDetailDTOs);
            }
            //获取免接收部门班组
            List<SysLookupTypesDTO>  sysLookupTypesDTOList=BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_IS_FREE_RECEIVING_TEAM);
            // 处理每一条的送修数据
            for (PmRepairRcvDetailDTO pmRepairRcvDetailDTO : pmRepairRcvDetailDTOs) {
                submitRepairOrderEx(record, machineList, redisLocks, sysLookupTypesDTOList, pmRepairRcvDetailDTO);
            }
            // 批量修改修详表状态
            List<PmRepairRcvDetail> pmRepairRcvDetailList = PmRepairRcvDetailAssembler.toPmRepairRcvDetailList(pmRepairRcvDetailDTOs);
            num = pmRepairRcvDetailService.updateStatusByReceptionId(pmRepairRcvDetailList,Constant.BATCH_SIZE);

            //整机处理 DQAS, 推送数据,    !!!重要， 处理DQAS必须放在 插入交易表(点对点调用配送服务)之前, 否则事务有风险
            //  2019/11/5  维修送修保存中式开关添加
            if (!Constant.DEVICE_PRODUCTION_MAINTENANCE.equals(record.getFromStation())) {
                String productType = psWipInfoServiceImpl.getExternalType(pmRepairRcvDetailDTOs.get(Constant.INT_0).getSn());
                handlerDQAS(record, pmRepairRcvDetailDTOs, productType);
                //半成品插入交易表并更新QTY数量(点对点之后调用), 整机插入交易信息
                handlerDealInfo(record, pmRepairRcvDetailDTOs, machineList, productType);
            }
        }finally{
            for(RedisLock redisLock:redisLocks){
                redisLock.unlock();
            }
        }
        return num ;
    }

    public void submitRepairOrderEx(PmRepairRcv record, List<SemiManufactureDealInfo> machineList,
                                    List<RedisLock> redisLocks, List<SysLookupTypesDTO> sysLookupTypesDTOList,
                                    PmRepairRcvDetailDTO pmRepairRcvDetailDTO) throws Exception {
        boolean terminalFlag = MpConstant.PAGE_TYPE_TERMINAL.equals(String.valueOf(record.getPageType()));
        RedisLock redisLock = new RedisLock(Constant.REPAIR+Constant.GANG+ pmRepairRcvDetailDTO.getSn());
        boolean lock = redisLock.lock();
        redisLocks.add(redisLock);
        if (!lock) {
            logger.error(pmRepairRcvDetailDTO.getSn() + MpConstant.STATUS_REPAIRING);
            throw new Exception(pmRepairRcvDetailDTO.getSn() + CommonUtils.getLmbMessage(MessageId.SN_IS_SUBMITED_BY_OTHER));
        }
        if (!Constant.REPAIR_STATUS_FICTION.equals(pmRepairRcvDetailDTO.getStatus())){
            throw new Exception(pmRepairRcvDetailDTO.getSn() + CommonUtils.getLmbMessage(MessageId.DATA_REPAIR_STATUS_IS_NOT_FICTION));
        }
        if (!terminalFlag) {
            PsWipInfo wipInfo = psWipInfoService.getWipInfoBySn(pmRepairRcvDetailDTO.getSn());
            if (!ObjectUtils.isEmpty(wipInfo) && StringUtils.isNotBlank(wipInfo.getWipId())) {
                // 如果是整机
                if ((Constant.FROM_STATION_MACHINE).equals(record.getFromStation())) {
                    //整机插入 set数据
                    machineList.add(getSemiManufactureDealInfo(record, wipInfo, pmRepairRcvDetailDTO));
                }
            } else {
                throw new Exception(CommonUtils.getLmbMessage(MessageId.WIP_INFO_DATA_NOT_FOUND, pmRepairRcvDetailDTO.getSn()));
            }
        }
        //判断是否可以免接收
        if(isFreeReceivingTeam(pmRepairRcvDetailDTO, sysLookupTypesDTOList)){
            pmRepairRcvDetailDTO.setStatus(Constant.REPAIR_STATUS_WAIT);
        }else {
            pmRepairRcvDetailDTO.setStatus(Constant.REPAIR_STATUS_TO_BE_RECEIVED);
        }
        pmRepairRcvDetailDTO.setIsAccept(MpConstant.REPAIR_IS_ACCEPT);
    }

    /**
     * 校验送修单状态
     * @param deliveryNo
     */
    public void checkPmRepairStatus(String deliveryNo,List<PmRepairRcvDetailDTO> pmRepairRcvDetailDTOs) throws Exception {
        List<PmRepairRcvVo> list = pmRepairRcvRepository.getListRepairPcvAndDeatilByDto(new PmRepairRcvDTO(){{
            setDeliveryNo(deliveryNo);
        }});
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Set<String> snSet=pmRepairRcvDetailDTOs.stream().map(PmRepairRcvDetailDTO::getSn).collect(Collectors.toSet());
        // 保存或者提交的条码存在非拟制单据，不能提交
        if (list.stream().anyMatch(e -> (!Constant.TYPE_10560005.equals(e.getStatus()))&&snSet.contains(e.getSn()))) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.REPAIR_STATUS_ERROR);
        }
    }

    //判断是否存在不能送修条码
    private void judgeExistCanNotRepairSn(List<PmRepairRcvDetailDTO> pmRepairRcvDetailDTOs) throws Exception {
        List<SysLookupTypesDTO>  repairProcessCodeList= BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_CAN_REPAIR_PROCESS_CODE);
        if(CollectionUtils.isNotEmpty(repairProcessCodeList)){
            StringBuilder canNotBeRepairSn=new StringBuilder();
            for (PmRepairRcvDetailDTO pmRepairRcvDetailDTO : pmRepairRcvDetailDTOs) {
              if(!judgeProcessCodeCanRepaire(pmRepairRcvDetailDTO,repairProcessCodeList)){
                  canNotBeRepairSn.append(pmRepairRcvDetailDTO.getSn()+Constant.COMMA);
              }
            }
            if(canNotBeRepairSn.length()>0){
                String errorMsg=canNotBeRepairSn.substring(0,canNotBeRepairSn.length()-1).toString();
                throw new Exception(CommonUtils.getLmbMessage(MessageId.SN_PROCESS_CANNOT_BE_REPAIR,errorMsg));
            }
        }
    }

    //判断送修部门，班组是否在数据字典配置
    private boolean judgeProcessCodeCanRepaire(PmRepairRcvDetailDTO pmRepairRcvDetailDTO,List<SysLookupTypesDTO>  list) throws Exception {
        if(CollectionUtils.isNotEmpty(list)) {
            for (SysLookupTypesDTO sysLookupTypesDTO : list) {
                if (StringUtils.equals(sysLookupTypesDTO.getLookupMeaning(), pmRepairRcvDetailDTO.getProcessCode())) {
                    return true;
                }
            }
        }
        return false;
    }

    //判断送修部门，班组是否在数据字典配置
    private boolean isFreeReceivingTeam(PmRepairRcvDetailDTO pmRepairRcvDetailDTO,List<SysLookupTypesDTO>  list) throws Exception {
        if(CollectionUtils.isNotEmpty(list)) {
            for (SysLookupTypesDTO sysLookupTypesDTO : list) {
                if (StringUtils.equals(sysLookupTypesDTO.getLookupMeaning(), pmRepairRcvDetailDTO.getApplicationDepartment()) &&
                        StringUtils.equals(sysLookupTypesDTO.getAttribute1(), pmRepairRcvDetailDTO.getApplicationSection())) {
                    return true;
                }
            }
        }
        return false;
    }

}


