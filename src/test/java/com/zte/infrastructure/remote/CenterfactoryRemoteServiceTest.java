package com.zte.infrastructure.remote;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zte.common.CommonUtils;
import com.zte.common.ConstantInterface;
import com.zte.interfaces.dto.VMesBoxNoNewDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;

@PrepareForTest({ServiceDataBuilderUtil.class, HttpClientUtil.class, JacksonJsonConverUtil.class,
        MicroServiceRestUtil.class,MESHttpHelper.class,HttpRemoteUtil.class, CommonUtils.class})
public class CenterfactoryRemoteServiceTest extends PowerBaseTestCase {
    @InjectMocks
    private CenterfactoryRemoteService centerfactoryRemoteService;
    @Mock
    private ConstantInterface constantInterface;
    @Mock
    ObjectMapper mapperInstance;
    @Mock
    JsonNode json;
    @Test
    public void getHisInfoBySerialKeyList() throws Exception {
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class, MESHttpHelper.class, HttpClientUtil.class,HttpRemoteUtil.class);
        PowerMockito.when(constantInterface.getUrl(any())).thenReturn("http://10.5.209.128/zte-mes-manufactureshare-centerfactory/vMesBoxNoHisCtrl/getHisInfoList");
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), Mockito.anyMap(), anyString(), anyString())).thenReturn("");
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn("");
        List<String> inforList = new ArrayList<>();
        try{
            centerfactoryRemoteService.getHisInfoBySerialKeyList(inforList);
        }catch (Exception e){
            Assert.assertTrue(inforList.size()>=0);
        }
    }

    @Test
    public void getSyncInfoFromTemp() {
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class, MESHttpHelper.class, HttpClientUtil.class,HttpRemoteUtil.class);
        PowerMockito.when(constantInterface.getUrl(any())).thenReturn("http://10.5.209.128/zte-mes-manufactureshare-centerfactory/vMesBoxNoHisCtrl/getCopyInfoList");
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), Mockito.anyMap(), anyString(), anyString())).thenReturn("");
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn("");
        VMesBoxNoNewDTO vMesBoxNoNewDTO= new VMesBoxNoNewDTO();
        vMesBoxNoNewDTO.setStartRow(new BigDecimal("1"));
        vMesBoxNoNewDTO.setEndRow(new BigDecimal("1"));
        try{
            centerfactoryRemoteService.getSyncInfoFromTemp(vMesBoxNoNewDTO);
        }catch (Exception e){
            Assert.assertTrue(vMesBoxNoNewDTO.getStartRow().intValue()>=0);
        }
    }

    @Test
    public void saveErrorInfoForZHToCenter() throws Exception {
        List<VMesBoxNoNewDTO> inforList = new ArrayList<>();
        centerfactoryRemoteService.saveErrorInfoToCenter(inforList);
        Assert.assertTrue(inforList.size()>=0);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class, CommonUtils.class,JacksonJsonConverUtil.class,MESHttpHelper.class, HttpClientUtil.class,HttpRemoteUtil.class);
        PowerMockito.when(constantInterface.getUrl(any())).thenReturn("http://10.5.209.128/zte-mes-manufactureshare-centerfactory/vMesBoxNoCopyCtrl/insertCopyInfoBatch");
        Map<String,Object> result = new HashMap<>();

        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), anyString(), Mockito.anyMap())).thenReturn(JSON.toJSONString(new ServiceData(){{
            setCode(new RetCode(RetCode.BUSINESSERROR_CODE, RetCode.BUSINESSERROR_MSGID));
            setBo(result);
        }}));
        PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
        PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json);
        PowerMockito.when(json.get(Mockito.anyString())).thenReturn(json);
        PowerMockito.when(json.asText()).thenReturn("0001");
        VMesBoxNoNewDTO vMesBoxNoNewDTO = new VMesBoxNoNewDTO();
        inforList.add(vMesBoxNoNewDTO);
        centerfactoryRemoteService.saveErrorInfoToCenter(inforList);
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(any(), any(), any())).thenReturn("{\n" +
                "    \"infCode\": null,\n" +
                "    \"infDesc\": null,\n" +
                "    \"list\": [],\n" +
                "}");
        PowerMockito.when(CommonUtils.getLmbMessage(Mockito.anyString(),Mockito.anyString())).thenReturn("result");
        try{
            centerfactoryRemoteService.saveErrorInfoToCenter(inforList);
        }catch (Exception e) {
            Assert.assertTrue(inforList.size()>=0);
        }
        PowerMockito.when(json.asText()).thenReturn("0000");
        centerfactoryRemoteService.saveErrorInfoToCenter(inforList);
    }

    @Test
    public void saveSuccessInfoZHToCenter() throws Exception {
        List<VMesBoxNoNewDTO> inforList = new ArrayList<>();
        centerfactoryRemoteService.saveSuccessInfoToCenter(inforList);
        Assert.assertTrue(inforList.size()>=0);
        VMesBoxNoNewDTO vMesBoxNoNewDTO = new VMesBoxNoNewDTO();
        inforList.add(vMesBoxNoNewDTO);
        centerfactoryRemoteService.saveSuccessInfoToCenter(inforList);
        vMesBoxNoNewDTO.setSerialKey("1");
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class, CommonUtils.class,JacksonJsonConverUtil.class,MESHttpHelper.class, HttpClientUtil.class,HttpRemoteUtil.class);
        PowerMockito.when(constantInterface.getUrl(any())).thenReturn("http://10.5.209.128/zte-mes-manufactureshare-centerfactory/vMesBoxNoCopyCtrl/insertCopyInfoBatch");
        Map<String,Object> result = new HashMap<>();

        PowerMockito.when(HttpClientUtil.httpPostWithJSON(any(), any(), any())).thenReturn("{\n" +
                "    \"infCode\": null,\n" +
                "    \"infDesc\": null,\n" +
                "    \"list\": [],\n" +
                "}");
        PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
        PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json);
        PowerMockito.when(json.get(Mockito.anyString())).thenReturn(json);
        PowerMockito.when(json.asText()).thenReturn("0001");
        PowerMockito.when(CommonUtils.getLmbMessage(Mockito.anyString(),Mockito.anyString())).thenReturn("result");
        try{
            centerfactoryRemoteService.saveSuccessInfoToCenter(inforList);
        }catch (Exception e) {
            Assert.assertTrue(inforList.size()>=0);
        }
        PowerMockito.when(json.asText()).thenReturn("0000");
        centerfactoryRemoteService.saveSuccessInfoToCenter(inforList);
    }

    @Test
    public void deleteRePushSuccessInfor() throws Exception {
        List<VMesBoxNoNewDTO> inforList = new ArrayList<>();
        centerfactoryRemoteService.deleteRePushSuccessInfo(inforList);
        Assert.assertTrue(inforList.size()>=0);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class, CommonUtils.class,JacksonJsonConverUtil.class,MESHttpHelper.class, HttpClientUtil.class,HttpRemoteUtil.class);
        PowerMockito.when(constantInterface.getUrl(any())).thenReturn("http://10.5.209.128/zte-mes-manufactureshare-centerfactory/vMesBoxNoCopyCtrl/insertCopyInfoBatch");
        Map<String,Object> result = new HashMap<>();

        PowerMockito.when(HttpClientUtil.httpDeleteWithJson(Mockito.any(), anyString(), Mockito.anyMap())).thenReturn(JSON.toJSONString(new ServiceData(){{
            setCode(new RetCode(RetCode.BUSINESSERROR_CODE, RetCode.BUSINESSERROR_MSGID));
            setBo(result);
        }}));
        PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
        PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json);
        PowerMockito.when(json.get(Mockito.anyString())).thenReturn(json);
        PowerMockito.when(json.asText()).thenReturn("0001");
        VMesBoxNoNewDTO vMesBoxNoNewDTO = new VMesBoxNoNewDTO();
        inforList.add(vMesBoxNoNewDTO);
        centerfactoryRemoteService.deleteRePushSuccessInfo(inforList);
        PowerMockito.when(HttpClientUtil.httpDeleteWithJson(any(), any(), any())).thenReturn("{\n" +
                "    \"infCode\": null,\n" +
                "    \"infDesc\": null,\n" +
                "    \"list\": [],\n" +
                "}");
        PowerMockito.when(CommonUtils.getLmbMessage(Mockito.anyString(),Mockito.anyString())).thenReturn("result");
        try{
            centerfactoryRemoteService.deleteRePushSuccessInfo(inforList);
        }catch (Exception e) {
            Assert.assertTrue(inforList.size()>=0);
        }
        PowerMockito.when(json.asText()).thenReturn("0000");
        centerfactoryRemoteService.deleteRePushSuccessInfo(inforList);
    }
}