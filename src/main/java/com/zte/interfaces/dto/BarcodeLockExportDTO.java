package com.zte.interfaces.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 条码锁定头表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-03-31 15:43:02
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class BarcodeLockExportDTO extends PageDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键ID
	 */
	private String id;
	/**
	 * 单据号
	 */
	private String billNo;
	/**
	 * 状态
	 */
	private String status;
	/**
	 * 锁定类型
	 */
	private String type;
	/**
	 * 锁定原因
	 */
	private String reason;
	/**
	 * 抄送人(逗号分隔)
	 */
	private String ccList;
	/**
	 * 创建人
	 */
	private String createBy;
	/**
	 * 创建日期
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date createDate;
	/**
	 * 更新人
	 */
	private String lastUpdatedBy;
	/**
	 * 更新日期
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date lastUpdatedDate;

	/**
	 * 批次/条码
	 */
	private String batchSn;

	/**
	 * 详情表状态
	 */
	private String detailStatus;
	/**
	 * 锁定工序
	 */
	private String currProcessCode;
	private String currProcessCodeName;

	/**
	 * 锁定工序
	 */
	private String lockProcessCode;
	private String lockProcessName;

	/**
	 * 解锁原因
	 */
	private String unlockReason;
	/**
	 * 备注
	 */
	private String remark;
	/**
	 * 抄送人
	 */
	private String detailCcList;
	/**
	 * 解锁人
	 */
	private String unlockBy;
	/**
	 * 解锁时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date unlockDate;
	/**
	 * 主批次
	 */
	private String mainCardBatch;

	/**
	 * 是否锁定排产
	 */
	private String schedulingLockFlag;

}
