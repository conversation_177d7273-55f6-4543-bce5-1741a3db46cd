<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.BaBomHeadRepository">
    <resultMap id="BaseResultMap" type="com.zte.domain.model.BaBomHead">
        <id column="BOM_NO" jdbcType="VARCHAR" property="bomNo"/>
        <result column="ITEM_NO" jdbcType="VARCHAR" property="codeDesc"/>
        <result column="ITEM_NAME" jdbcType="VARCHAR" property="codeType"/>
        <result column="IS_LEAD" jdbcType="VARCHAR" property="isLead"/>
        <result column="CODE_DESC" jdbcType="VARCHAR" property="codeDesc"/>
        <result column="LAST_PRICE" jdbcType="VARCHAR" property="lastPrice"/>
        <result column="BOM_NAME" jdbcType="VARCHAR" property="bomName"/>
        <result column="STANDARD_COST" jdbcType="VARCHAR" property="standardCost"/>
        <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy"/>
        <result column="ARCHIVER" jdbcType="VARCHAR" property="archiver"/>
        <result column="REMARK" jdbcType="VARCHAR" property="remark"/>
        <result column="STYLE" jdbcType="VARCHAR" property="style"/>
        <result column="FIRSTROW" jdbcType="VARCHAR" property="firstrow"/>
        <result column="SECONDROW" jdbcType="VARCHAR" property="secondrow"/>
        <result column="ARCHIVE_DATE" jdbcType="TIMESTAMP" property="archiveDate"/>
    </resultMap>

    <resultMap id="ResultMap" type="com.zte.domain.model.BaBomHead">
        <id column="PARAM" jdbcType="VARCHAR" property="param"/>
        <result column="ENCAPSULATION" jdbcType="VARCHAR" property="encapsulation"/>
        <result column="CODE_DESC" jdbcType="VARCHAR" property="codeDesc"/>
        <result column="ITEM_ID" jdbcType="VARCHAR" property="itemId"/>
        <result column="BOM_ID" jdbcType="VARCHAR" property="bomId"/>
        <result column="TOTAL" jdbcType="BIGINT" property="total"/>
    </resultMap>

    <select id="getBomInfoByBomNo" parameterType="com.zte.domain.model.BaBomHead" resultMap="BaseResultMap">
        select t.is_lead,p.code_desc,t.bom_no from KXSTEPIII.ba_bom_head t,kxstepiii.pu_codeinfo p
        where t.bom_no=#{bomNo} and p.code_type = #{codeType,jdbcType=VARCHAR} AND p.code = t.is_lead
    </select>

    <select id="getBomInfoByBomNoList" parameterType="java.util.List" resultMap="BaseResultMap">
        select t.LAST_PRICE,t.BOM_NAME,t.bom_no, t.standard_cost from KXSTEPIII.ba_bom_head t
        where t.bom_no in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="queryBomInfoByBomNoList" parameterType="java.util.List" resultMap="BaseResultMap">
        SELECT T.BOM_NO,TC.CODE_DESC
        ,T.IS_LEAD,T.CREATED_BY,T.ARCHIVER,T.REMARK,T.STYLE,T.FIRSTROW,T.SECONDROW,T.ARCHIVE_DATE FROM
        KXSTEPIII.BA_BOM_HEAD T
        LEFT JOIN KXSTEPIII.TECH_CODEINFO TC ON TC.CODE = T.STATUS AND TC.CODE_TYPE = 'BomStatus'
        WHERE T.BOM_NO IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="getItemParamEncapsulationByItemId" parameterType="com.zte.domain.model.BaBomHead" resultMap="ResultMap">
        select vbb.item_id,vbb.param,vbb.encapsulation,tc.code_desc from kxstepiii.v_ba_bomitem vbb
        left join kxstepiii.tech_codeinfo tc on tc.code=vbb.property_no and code_type= #{codeType}
        where vbb.item_id in
        <foreach item="itemId" index="index" collection="inItemIds" open="(" separator="," close=")">
            #{itemId}
        </foreach>
    </select>

    <select id="getIsAvlByItemId" parameterType="com.zte.domain.model.BaBomHead"
            resultType="com.zte.domain.model.BaBomHead">
        select bbna.item_id,count(item_id) total from kxstepiii.ba_bom_navl_all bbna
        where bbna.bom_id = #{bomHeaderId}
        <if test="inItemIds != null and inItemIds.size() > 0">
            and bbna.item_id in
            <foreach item="itemId" index="index" collection="inItemIds" open="(" separator="," close=")">
                #{itemId}
            </foreach>
        </if>
        group by bbna.item_id
    </select>
    <select id="getBomInfoByBomNoListAndCodeType" parameterType="com.zte.domain.model.BaBomHead"
            resultMap="BaseResultMap">
        select t.is_lead,p.code_desc,t.bom_no from KXSTEPIII.ba_bom_head t,kxstepiii.pu_codeinfo p
        where t.bom_no in
        <foreach item="bomNo" index="index" collection="bomNoList" open="(" separator="," close=")">
            #{bomNo}
        </foreach>
        and p.code_type = #{codeType,jdbcType=VARCHAR} AND p.code = t.is_lead
    </select>
</mapper>