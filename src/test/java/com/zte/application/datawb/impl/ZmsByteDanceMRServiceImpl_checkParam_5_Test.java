/*Started by AICoder, pid:l713293a70sd98714bf50974c0c7e36611b97fe8*/
package com.zte.application.datawb.impl;

import com.alibaba.fastjson.JSON;
import com.zte.application.datawb.WsmAssembleLinesService;
import com.zte.common.CommonUtils;
import com.zte.common.utils.DateUtil;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.interfaces.dto.ZmsByteDanceMRDTO;
import com.zte.springbootframe.util.json.JacksonJsonConverUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.Date;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

@RunWith(PowerMockRunner.class)
@PrepareForTest({CenterfactoryRemoteService.class,
        WsmAssembleLinesService.class, JacksonJsonConverUtil.class,
        JSON.class, CommonUtils.class, DateUtil.class})
@PowerMockIgnore({"javax.net.ssl.*", "javax.security.*"})
public class ZmsByteDanceMRServiceImpl_checkParam_5_Test {
    private ZmsByteDanceMRServiceImpl service;
    private ZmsByteDanceMRDTO dto;

    @Before
    public void setUp() throws Exception {
        service = new ZmsByteDanceMRServiceImpl();
        dto = new ZmsByteDanceMRDTO();
    }

    @Test(timeout = 8000)
    public void testCheckParam_emptyDateTime() {
        dto.setDataTime("");
        assertTrue(service.checkParam(dto));
    }

    @Test(timeout = 8000)
    public void testCheckParam_nullDateTime() {
        dto.setDataTime(null);
        assertTrue(service.checkParam(dto));
    }

    @Test(timeout = 8000)
    public void testCheckParam_validDateTime() {
        dto.setDataTime(DateUtil.convertNowToString());
        assertTrue(service.checkParam(dto));
    }

    @Test(timeout = 8000)
    public void testCheckParam_invalidDateTimeFormat() {
        dto.setDataTime("2021/13/01");
        assertFalse(service.checkParam(dto));
    }
}
/*Ended by AICoder, pid:l713293a70sd98714bf50974c0c7e36611b97fe8*/