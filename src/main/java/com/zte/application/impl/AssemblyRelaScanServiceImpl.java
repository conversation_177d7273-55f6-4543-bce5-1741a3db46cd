package com.zte.application.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.zte.application.*;
import com.zte.application.warehouse.MixWarehouseSubmitService;
import com.zte.common.CommonUtils;
import com.zte.common.ConstantInterface;
import com.zte.common.enums.InterfaceEnum;
import com.zte.common.model.MessageId;
import com.zte.common.utils.BusinessConstant;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.common.utils.NumConstant;
import com.zte.common.utils.ObtainRemoteServiceDataUtil;
import com.zte.common.utils.RedisKeyConstant;
import com.zte.consts.CommonConst;
import com.zte.domain.model.*;
import com.zte.domain.model.CFLine;
import com.zte.infrastructure.remote.*;
import com.zte.interfaces.dto.*;
import com.zte.interfaces.dto.PsWorkOrderDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.redis.util.RedisCacheUtils;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.itp.msa.util.string.StringHelper;
import com.zte.microservice.name.MicroServiceNameEum;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.redis.RedisLock;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.JsonConvertUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.ums.nfv.eco.hsif.msb.common.RouteException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;


@Service
@Transactional(rollbackFor = Exception.class)
public class AssemblyRelaScanServiceImpl implements AssemblyRelaScanService {
	private final Logger logger = LoggerFactory.getLogger(this.getClass());

	@Autowired
	private PsWipInfoService psWipInfoService;

	@Autowired
	private ProdBindingSettingRepository prodBindingSettingRepository;

	@Autowired
	private AssemblyRelaScanService assemblyRelaScanService;

	@Autowired
	private WipExtendIdentificationRepository wipExtendIdentificationRepository;

	@Autowired
	private BarcodeLockDetailRepository barcodeLockDetailRepository;

	@Autowired
	private StandardModeCommonScanServiceImpl standardModeCommonScanService;

	@Autowired
	private PsWipInfoRepository psWipInfoRepository;

	@Autowired
	private ConstantInterface constantInterface;

	@Autowired
	private BarcodeCenterRemoteService barcodeCenterRemoteService;

	@Autowired
	private AssemblyRelaScanRecordInfoService assemblyRelaScanRecordInfoService;

	@Autowired
	private DatawbRemoteService datawbRemoteService;


	@Autowired
	private ProdBindingSettingService prodBindingSettingService;

	@Autowired
	private AssemblyRelaScanServiceImpl service;

	@Autowired
	private WipExtendIdentificationService wipExtendIdentificationService;

	@Autowired
	private MixWarehouseSubmitService mixWarehouseSubmitService;

	@Autowired
	private CenterfactoryRemoteService centerfactoryRemoteService;
	@Autowired
	private CollectionCodeScanService collectionCodeScanService;

	public void setProdBindingSettingRepository (ProdBindingSettingRepository prodBindingSettingRepository) {
		this.prodBindingSettingRepository = prodBindingSettingRepository;
	}

	public void setPsWipInfoRepository(PsWipInfoRepository psWipInfoRepository) {
		this.psWipInfoRepository = psWipInfoRepository;
	}

	public void setWipExtendIdentificationRepository(WipExtendIdentificationRepository wipExtendIdentificationRepository) {
		this.wipExtendIdentificationRepository = wipExtendIdentificationRepository;
	}

	@Override
	public RetCode assemblyRelaScan(AssemblyRelaScanDTO dto) throws Exception {
		// 校验参数
		RetCode checkParamsResult = checkParams(dto);
		if (!RetCode.SUCCESS_CODE.equals(checkParamsResult.getCode())) {
			return checkParamsResult;
		}
		if (dto.isMainSnScan()) {
			Pair<RetCode, FlowControlInfoDTO> result = dealMainSn(dto);
			return result.getFirst();
		} else if (dto.isSubSnScan()) {
			FlowControlInfoDTO fcInfo = new FlowControlInfoDTO();
			return subSnScan(dto, fcInfo);
		}
		return new RetCode();

	}


	@Override
	@Transactional(rollbackFor = Exception.class)
	public RetCode assemblyRelaScanForZsSn(AssemblyRelaScanForZsDto assemblyRelaScanForZsDto) throws Exception {
		RetCode code=this.assemblyRelaScan(assemblyRelaScanForZsDto.getAssemblyRelaScanDTO());
		//成功才做中试绑定
		if(StringUtils.equals(code.getCode(),RetCode.SUCCESS_CODE)){
			//获取中试装配关系
			logger.info("调中试绑定接口 入参{} "+ assemblyRelaScanForZsDto.getJsonNode().toString());
			ServiceData<Map<String,Object>> resultRet=BasicsettingRemoteService.zsUniteInterface(assemblyRelaScanForZsDto.getJsonNode());
			logger.info("调中试绑定接口 出参{} "+JSON.toJSONString(resultRet));
			if(!RetCode.SUCCESS_CODE.equals(resultRet.getCode().getCode())){
				throw  new MesBusinessException(RetCode.BUSINESSERROR_CODE,MessageId.FAILED_TO_BIND_FOR_ZS);
			}
		}
		return code;

	}

	@Override
	public RetCode relaScanBatch(AssemblyRelaScanDTO dto) throws Exception {
		// 主条码不能为空
		if (StringUtils.isEmpty(dto.getMainSn())) {
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.MAIN_SN_IS_NOT_NULL);
		}
		String key = RedisKeyConstant.MAIN_SN_BIND_LOCK + dto.getMainSn();
		RedisLock redisLock = new RedisLock(key);
		if (!redisLock.lock()) {
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.MAIN_SN_WIP_EXT_BIND_LOCK);
		}
		try {
			RetCode ret = new RetCode();
			// 通过接口进行组装扫描
			dto.setExternalScan(true);
			dto.setBatchFlag(true);
			dto.setToPassWorkStaion(Constant.FLAG_Y.equals(dto.getIsPassWorkStaion()));
			// 校验参数
			checkSaveParams(dto);
			// 获取子工序/工站代码
			List<BSProcess> processList = getProcessInfo(dto);
			if (CollectionUtils.isEmpty(processList)) {
				throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.CURRPROCESSCODE_OR_WORKSTATION_OF_WIPINFO_IS_NULL, new Object[]{dto.getMainSn()});
			}
			ret = setProcessAndWorkStation(processList, dto);
			if (!RetCode.SUCCESS_CODE.equals(ret.getCode())) {
				return ret;
			}
			// 校验主条码
			Pair<RetCode, FlowControlInfoDTO> flow = checkMainSn(dto);
			ret = flow.getFirst();
			if (!RetCode.SUCCESS_CODE.equals(ret.getCode())) {
				return ret;
			}
			// 通过接口扫描主条码已绑定完毕也输入异常情况，需返回
			if (!Constant.MAIN_SN_SCAN_SUCCESS.equals(ret.getMsg())) {
				return new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.MAINSN_BINDED_COMPLETE);
			}
			//物料需求清单
			Map<String, ItemListEntityDTO> itemListMap = new HashMap<>();
			// 初始化任务需求清单
			initItemMap(dto, itemListMap);
			// 获取需替代物料清单
			List<String> needReplaceItem = dto.getSubSnDTOList().stream().filter(e -> StringUtils.isNotEmpty(e.getItemNo())
					&& e.getItemNo().length() >= Constant.INT_12).map(AssemblyRelaScanSubSnDTO::getItemNo).collect(Collectors.toList());
			// 获取替代物料
			Map<String, List<MtlRelatedItemsEntityDTO>> replaceItem = this.getReplaceItem(needReplaceItem);
			// 获取绑定清单
			List<ProdBindingSettingDTO> bindList = this.getBindList(dto);
			// 校验主条码在传入的子工序和工站下是否有绑定记录
			checkExistWipExt(dto);
			// 从条码中心获取子条码物料信息并过滤非imes处理条码
			Set<String> wipSnSet = new HashSet<>();
			Set<String> prodSet = new HashSet<>();
			// 从条码中心获取子条码信息
			this.getItemCodeByBarcodeCenter(dto, wipSnSet, prodSet);
			// 校验物料是否在物料/绑定清单
			this.checkItemInList(bindList, dto, itemListMap, replaceItem, wipSnSet);
			this.checkSnLock(dto, prodSet);
			FlowControlInfoDTO checkFlowResult = flow.getSecond();
			checkFlowResult.setLastUpdatedBy(dto.getCreateBy());
			checkFlowResult.setCreateBy(dto.getCreateBy());
			// 处理子条码
			ret = subSnScanNew(replaceItem, bindList, dto, checkFlowResult, wipSnSet);
			return ret;
		} finally {
			redisLock.unlock();
		}
	}

	/**
	 *<AUTHOR>
	 * 设置过站线体
	 *@Date 2023/6/7 14:08
	 *@Param [com.zte.interfaces.dto.AssemblyRelaScanDTO]
	 *@return
	 **/
	private String setPassLineCode (AssemblyRelaScanDTO dto, String taskNo, StringBuilder workOrderNo) throws Exception {
		List<PsWorkOrderDTO> workOrderList = PlanscheduleRemoteService.getWorkOrderBasicByProdPlanId(null, taskNo);
		if (CollectionUtils.isEmpty(workOrderList)) {
			// 没有找到当前指令
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.GET_CURRENT_WORKORDER_ERROR);
		}
		List<PsWorkOrderDTO> psWorkOrderDTOs = workOrderList.stream()
				.filter(p -> StringUtils.isNotEmpty(p.getProcessGroup()) &&
						Arrays.asList(StringUtils.split(p.getProcessGroup(), MpConstant.SPLIT_CHAR)).contains(dto.getProcessCode()))
				.collect(Collectors.toList());
		if (CollectionUtils.isEmpty(psWorkOrderDTOs)) {
			// 未查询到指令的工艺路径
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.GET_CARFT_NULL);
		}
		if (psWorkOrderDTOs.size() > Constant.INT_1) {
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.TASK_NO_HAVE_MORE_WORK_ORDER_NO, new String[]{taskNo, dto.getProcessName()});
		}
		dto.setLineCode(psWorkOrderDTOs.get(Constant.INT_0).getLineCode());
		workOrderNo.append(psWorkOrderDTOs.get(Constant.INT_0).getWorkOrderNo());
		return psWorkOrderDTOs.get(Constant.INT_0).getSourceTask();
	}
	/**
	 *<AUTHOR>
	 * 处理子条码
	 *@Date 2023/5/30 14:09
	 *@Param [java.util.Map<java.lang.String,java.util.List<com.zte.interfaces.dto.MtlRelatedItemsEntityDTO>>, java.util.List<com.zte.interfaces.dto.ProdBindingSettingDTO>, com.zte.interfaces.dto.AssemblyRelaScanDTO, com.zte.interfaces.dto.FlowControlInfoDTO, java.util.Map<java.lang.String,java.lang.Long>]
	 *@return
	 **/
	private RetCode subSnScanNew (Map<String, List<MtlRelatedItemsEntityDTO>> replaceItem, List<ProdBindingSettingDTO> bindList,
							   AssemblyRelaScanDTO dto, FlowControlInfoDTO checkFlowResult, Set<String> wipSnSet) throws Exception {
		RetCode ret;
		// 校验子条码是否已有绑定记录
		ret = validateSn(dto);
		if (!RetCode.SUCCESS_CODE.equals(ret.getCode())) {
			return ret;
		}
		StringBuilder itemCountSb = new StringBuilder();
		Map<String, String> replaceMap = new HashMap<>();
		List<BaItem> itemInfoList = new ArrayList<BaItem>();
		Map<String, Long> qtyMap = new HashMap<>();
		// 根据条码绑定
		for (AssemblyRelaScanSubSnDTO subSnDTO : dto.getSubSnDTOList()) {
			BaItem baItem = new BaItem();
			baItem.setSn(subSnDTO.getSubSn());
			baItem.setItemNo(subSnDTO.getItemNo());
			baItem.setQty(new BigDecimal(subSnDTO.getQty()));
			itemInfoList.add(baItem);
			boolean wipSnFlag = wipSnSet.contains(subSnDTO.getSubSn());
			subSnDTO.setWipSnFlag(wipSnFlag);
			Long bindQty = this.eachMainMaterialBySn(replaceMap, replaceItem, bindList, subSnDTO, itemCountSb);
			Long itemQty = qtyMap.get(subSnDTO.getItemNo()) == null ? bindQty : qtyMap.get(subSnDTO.getItemNo()) + bindQty;
			baItem.setItemNo(subSnDTO.getItemNo());
			baItem.setItemName(subSnDTO.getItemName());
			baItem.setNotByReplaceItem(subSnDTO.isNotByReplaceItem());
			qtyMap.put(subSnDTO.getItemNo(), itemQty);
		}
		checkSnItemBindOK(itemCountSb, bindList, qtyMap, true);
		dto.setProdBindingSettingDTOS(bindList);
		dto.setItemInfoParamList(itemInfoList);
		return service.getRetCode(replaceMap ,dto , checkFlowResult, ret);
	}

	/**
	 *<AUTHOR>
	 * 通过条码维度绑定到对应物料
	 *@Date 2023/6/2 19:01
	 *@Param [java.util.Map<java.lang.String,java.lang.String>, java.util.Map<java.lang.String,java.util.List<com.zte.interfaces.dto.MtlRelatedItemsEntityDTO>>, java.util.List<com.zte.interfaces.dto.ProdBindingSettingDTO>, com.zte.interfaces.dto.AssemblyRelaScanSubSnDTO, java.lang.StringBuilder, boolean]
	 *@return
	 **/
	private Long eachMainMaterialBySn (Map<String, String> replaceMap, Map<String, List<MtlRelatedItemsEntityDTO>> replaceItemMap, List<ProdBindingSettingDTO> bindList,
									   AssemblyRelaScanSubSnDTO subSnDTO, StringBuilder itemCountSb) throws Exception {
		boolean wipSnFlag = subSnDTO.isWipSnFlag();
		String itemNo = subSnDTO.getItemNo();
		String reelItemNo = wipSnFlag ? itemNo.substring(0, 12) : itemNo;
		Long bindQty = Long.valueOf(subSnDTO.getQty());
		List<MtlRelatedItemsEntityDTO> mtlRelatedItemsEntityDTOList = replaceItemMap.get(itemNo);
		for (ProdBindingSettingDTO bindDto : bindList) {
			int count = NumConstant.NUM_ZERO;
			String replaceItem = getReplaceItemInBindList(itemNo, mtlRelatedItemsEntityDTOList, bindDto.getItemCode(), count);
			if (reelItemNo.equals(bindDto.getItemCode())) {
				// 绑定后数量
				setBinderCount(itemNo, bindQty, itemCountSb, bindDto);
				// 找到子物料代码对应到绑定关系后结束本次循环，继续找下一个子物料代码
				subSnDTO.setNotByReplaceItem(true);
				break;
			}else if (StringUtils.isNotEmpty(replaceItem)) {
				//记录替代关系
				replaceMap.put(itemNo, replaceItem);
				setBinderCount(replaceItem, bindQty, itemCountSb, bindDto);
				// 找到子物料代码对应到绑定关系后结束本次循环，继续找下一个子物料代码
				break;
			}
		}
		return bindQty;
	}

	/**
	 *<AUTHOR>
	 * 校验条码是否被锁定
	 *@Date 2023/5/30 19:44
	 *@Param [com.zte.interfaces.dto.AssemblyRelaScanDTO]
	 *@return
	 **/
	private void checkSnLock(AssemblyRelaScanDTO dto, Set<String> prodSet) throws Exception {
		List<String> snList = new ArrayList<>(dto.getSubSnList());
		if (isCheck(Constant.SYS_LOOK_CHECK_TECHINAL)) {
			// 校验条码及所有子条码是否技改
			String technicalChangeBarcode = standardModeCommonScanService.getTechnicalChangeBarcode(snList, Constant.IN_STORE);
			if (StringUtils.isNotEmpty(technicalChangeBarcode)) {
				String resultMsg = CommonUtils.getLmbMessage(MessageId.SN_TECHNICAL_CHANGE, technicalChangeBarcode);
				throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, resultMsg);
			}
			List<String> mianSnList = new ArrayList<>();
			String craftSection = dto.getWipInfo().getCraftSection();
			technicalChangeBarcode = standardModeCommonScanService.getTechnicalChangeBarcode(mianSnList, craftSection);
			if (StringUtils.isNotEmpty(technicalChangeBarcode)) {
				String resultMsg = CommonUtils.getLmbMessage(MessageId.SN_TECHNICAL_CHANGE, technicalChangeBarcode);
				throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, resultMsg);
			}
		}
		snList.add(dto.getMainSn());
		for (String sn : dto.getSubSnList()) {
			List<WipExtendIdentification> bindingList = wipExtendIdentificationRepository.getAllChildProdBinding(sn);
			if (!CollectionUtils.isEmpty(bindingList)) {
				snList.addAll(bindingList.stream().map(i -> i.getSn()).collect(Collectors.toList()));
			}
		}
		snList.addAll(prodSet);
		snList.add(dto.getWipInfo().getAttribute1());
		List<BarcodeLockDetail> lockDetailList = barcodeLockDetailRepository.queryLockDetail(snList, null);
		if (CollectionUtils.isEmpty(lockDetailList)) {
			return;
		}
		List<String> snLockList = lockDetailList.stream().filter(e -> StringUtils.equals(Constant.LOCK_TYPE_SN, e.getType())).map(BarcodeLockDetail::getBatchSn).collect(Collectors.toList());
		if (!CollectionUtils.isEmpty(snLockList)) {
			String errMsg = String.join(Constant.COMMA, snLockList);
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SN_IS_LOCK_CAN_NOT_BIND, new Object[]{ errMsg });
		}
		List<String> prodLockList = lockDetailList.stream().filter(e -> StringUtils.equals(Constant.LOCK_TYPE_BATCH, e.getType())).map(BarcodeLockDetail::getBatchSn).collect(Collectors.toList());
		if (!CollectionUtils.isEmpty(prodLockList)) {
			String errMsg = String.join(Constant.COMMA, prodLockList);
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.BATCH_IS_LOCK_CAN_NOT_BIND, new Object[]{ errMsg });
		}
	}

	/**
	 *<AUTHOR>
	 * 校验子条码
	 *@Date 2023/5/22 20:03
	 *@Param [java.util.List<java.lang.String>]
	 *@return
	 **/
	private void checkSubSn (List<String> snList, AssemblyRelaScanDTO dto, List<BarcodeExpandDTO> newBarcodeList, Set<String> wipSnSet, Set<String> prodSet) throws Exception {
		List<PsWipInfo> list = psWipInfoRepository.getListByBatchSn(snList);
		List<String> relatedSnList=new ArrayList<>();
		List<String> leadSnList=new ArrayList<>();
		Map<String, SysLookupValuesDTO> leadMap =  getLeadList();
		SysLookupValuesDTO  lookupValuesDTOForMainSn = leadMap.get(dto.getMainSnWipInfo().getAttribute3());
		// 校验条码是否为ssp物料
		checkItemIsSSP(newBarcodeList, relatedSnList, leadSnList, leadMap, lookupValuesDTOForMainSn);
		if (CollectionUtils.isEmpty(list)) {
			return;
		}
		for (PsWipInfo wipInfo : list) {
			if (!Constant.IN_STORE.equals(wipInfo.getCraftSection())) {
				throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SN_STATUS_NOT_WAREHOUSE_IN, new Object[] { wipInfo.getSn() });
			}
			wipSnSet.add(wipInfo.getSn());
			prodSet.add(wipInfo.getAttribute1());
		}
	}

	/**
	 *<AUTHOR>
	 * 验条码是否为ssp物料
	 *@Date 2023/6/1 9:44
	 *@Param [java.util.List<com.zte.interfaces.dto.BarcodeExpandDTO>, java.util.List<java.lang.String>, java.util.List<java.lang.String>, java.util.Map<java.lang.String,com.zte.interfaces.dto.SysLookupValuesDTO>, com.zte.interfaces.dto.SysLookupValuesDTO]
	 *@return
	 **/
	private void checkItemIsSSP(List<BarcodeExpandDTO> newBarcodeList, List<String> relatedSnList, List<String> leadSnList, Map<String, SysLookupValuesDTO> leadMap, SysLookupValuesDTO lookupValuesDTOForMainSn) {
		for (BarcodeExpandDTO barcodeExpandTempDTO : newBarcodeList) {
			veifyReleateSnAndHbattr(relatedSnList, leadSnList, leadMap, lookupValuesDTOForMainSn, barcodeExpandTempDTO);
		}
		veifyReleateSnAndHbattr(relatedSnList, leadSnList);
	}

	/**
	 *<AUTHOR>
	 * 校验物料是否在物料/绑定清单中
	 *@Date 2023/5/22 19:39
	 *@Param [java.util.List<com.zte.interfaces.dto.ProdBindingSettingDTO>, com.zte.interfaces.dto.AssemblyRelaScanDTO, java.util.Map<java.lang.String,com.zte.interfaces.dto.ItemListEntityDTO>, java.util.Map<java.lang.String,java.util.List<com.zte.interfaces.dto.MtlRelatedItemsEntityDTO>>]
	 *@return
	 **/
	private void checkItemInList (List<ProdBindingSettingDTO> bindList, AssemblyRelaScanDTO dto, Map<String, ItemListEntityDTO> itemListMap,
								  Map<String, List<MtlRelatedItemsEntityDTO>> replaceItem, Set<String> wipSnSet) throws Exception {

		Map<String, ProdBindingSettingDTO> collect = bindList.stream().collect(Collectors.toMap(ProdBindingSettingDTO::getItemCode, y -> y, (k, y) -> k));
		for (AssemblyRelaScanSubSnDTO subSnDTO : dto.getSubSnDTOList()) {
			if (StringUtils.isEmpty(subSnDTO.getItemNo()) || subSnDTO.getItemNo().length() < Constant.INT_12) {
				throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SN_ITEM_LENGTH_NO_12, new Object[] { subSnDTO.getSubSn() });
			}
			// 子条码在wip_info中存在取12位物料代码校验
			String itemNo = subSnDTO.getItemNo().substring(Constant.INT_0, Constant.INT_12);
			List<MtlRelatedItemsEntityDTO> itemsEntityDTOS = replaceItem.get(subSnDTO.getItemNo().substring(Constant.INT_0, Constant.INT_12));
			ItemListEntityDTO entityDTO = itemListMap.get(itemNo);

			// 校验替代物料是否在物料清单中
			boolean checkErpResult = this.checkReplaceItemInList(entityDTO, itemsEntityDTOS, itemListMap, subSnDTO);
			if (!checkErpResult) {
				// erp无信息时，校验子条码物料是否在绑定清单中
				extracted(collect, subSnDTO, itemsEntityDTOS);
			}
		}
	}

	/**
	 *<AUTHOR>
	 * 校验子条码替代物料是否在任务清单中
	 *@Date 2023/6/2 16:59
	 *@Param [java.util.List<com.zte.interfaces.dto.MtlRelatedItemsEntityDTO>, java.util.Map<java.lang.String,com.zte.interfaces.dto.ItemListEntityDTO>, java.lang.String]
	 *@return
	 **/
	private boolean checkReplaceItemInList(ItemListEntityDTO entityDTO, List<MtlRelatedItemsEntityDTO> itemsEntityDTOS, Map<String, ItemListEntityDTO> itemListMap,
										AssemblyRelaScanSubSnDTO subSnDTO) throws Exception {
		// 子条码物料不在物料清单中且不是替代物料
		if (entityDTO == null && CollectionUtils.isEmpty(itemsEntityDTOS)) {
			// 先不报错，登记。
			return false;
		}
		// 子条码物料代码在物料清单中返回
		if (entityDTO != null) {
			return true;
		}
		int count = Constant.INT_0;
		String finalReplaceItemNo = "";
		for (MtlRelatedItemsEntityDTO itemsEntityDTO : itemsEntityDTOS) {
			if (StringUtils.isEmpty(itemsEntityDTO.getInventoryItemCode()) || itemsEntityDTO.getInventoryItemCode().length() < Constant.INT_12) {
				throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SN_ITEM_LENGTH_NO_12, new Object[] { subSnDTO.getSubSn() });
			}
			// 取前12位物料代码校验
			String replaceItemNo = itemsEntityDTO.getInventoryItemCode().substring(Constant.INT_0, Constant.INT_12);
			if (itemListMap.get(replaceItemNo) != null) {
				finalReplaceItemNo = replaceItemNo;
				count++;
			}
		}
		// 替代物料不在物料清单中报错
		if (StringUtils.isEmpty(finalReplaceItemNo)) {
			// 先不报错，登记
			return false;
		}
		// 替代物料在物料清单中存在多个报错
		if (count > Constant.INT_1) {
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,MessageId.ITEM_CODE_EXIST_IN_LIST_MORE_THAN_ONE,new Object[]{subSnDTO.getItemNo()});
		}
		return true;
	}

	/**
	 *<AUTHOR>
	 * 校验子条码物料是否在绑定清单中
	 *@Date 2023/5/22 17:25
	 *@Param [com.zte.interfaces.dto.AssemblyRelaScanDTO, java.util.Map<java.lang.String,com.zte.interfaces.dto.ProdBindingSettingDTO>, com.zte.interfaces.dto.AssemblyRelaScanSubSnDTO, java.util.List<com.zte.interfaces.dto.MtlRelatedItemsEntityDTO>]
	 *@return
	 **/
	private void extracted(Map<String, ProdBindingSettingDTO> collect,
						   AssemblyRelaScanSubSnDTO subSnDTO, List<MtlRelatedItemsEntityDTO> itemsEntityDTOS) {
		String itemNo = subSnDTO.getItemNo().substring(Constant.INT_0, Constant.INT_12);
		ProdBindingSettingDTO settingDTO = collect.get(itemNo);
		if (settingDTO != null) {
			return;
		}
		String replaceItem = Constant.STRING_EMPTY;
		for (Map.Entry<String, ProdBindingSettingDTO> settingDTOEntry : collect.entrySet()) {
			int count = Constant.INT_0;
			ProdBindingSettingDTO dto = settingDTOEntry.getValue();
			replaceItem = this.getReplaceItemInBindList(subSnDTO.getItemNo(), itemsEntityDTOS, dto.getItemCode(), count);
		}
		if (StringUtils.isEmpty(replaceItem)) {
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.ITEM_CODE_NOT_IN_BIND_LIST, new Object[] { subSnDTO.getSubSn() });
		}
	}


	/**
	 *<AUTHOR>
	 * 获取替代物料
	 *@Date 2023/5/22 19:27
	 *@Param [java.util.List<java.lang.String>]
	 *@return
	 **/
	private Map<String, List<MtlRelatedItemsEntityDTO>> getReplaceItem (List<String> needReplaceItem) throws Exception {
		if (CollectionUtils.isEmpty(needReplaceItem)) {
			return new HashMap<>();
		}
        List<MtlRelatedItemsEntityDTO> mtlRelatedItemsEntityDTOList = this.getReplaceItemByErp(needReplaceItem, true);
        if (CollectionUtils.isEmpty(mtlRelatedItemsEntityDTOList)) {
			return new HashMap<>();
		}
		//按物料代码分组
		return mtlRelatedItemsEntityDTOList.stream().distinct().collect(Collectors.groupingBy(MtlRelatedItemsEntityDTO::getRelatedItemCode));
	}

	/**
	 *<AUTHOR>
	 * 从erp获取替代物料
	 *@Date 2023/5/29 10:31
	 *@Param [java.util.List<java.lang.String>]
	 *@return
	 **/
	public List<MtlRelatedItemsEntityDTO> getReplaceItemByErp (List<String> needReplaceItem, boolean isReplaceItem) throws Exception {
		SysLookupTypesDTO sysLookupTypesDTO = BasicsettingRemoteService.getSysLookUpValue(MpConstant.LOOKUP_TYPE_ERP_WS, MpConstant.LOOKUP_CODE_ERP_REPLACE_ITEM);
		if (sysLookupTypesDTO == null || StringUtils.isEmpty(sysLookupTypesDTO.getLookupMeaning())) {
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SYS_LOOK_NOT_CONFIG, new Object[]{ MpConstant.LOOKUP_CODE_ERP_REPLACE_ITEM });
		}
		String erpUrl = sysLookupTypesDTO.getLookupMeaning();
		List<List<String>> listOfList = CommonUtils.splitList(needReplaceItem, CommonConst.BATCH_SIZE);
		int searchCount;
		int count = Constant.INT_0;
		int page = Constant.INT_0;
		List<MtlRelatedItemsEntityDTO> relatedItemsEntityDTOList = new ArrayList<>();
		Map<String,Object> params = new HashMap<>();
		params.put(Constant.PAGESIZE, Constant.INT_500);
		for (List<String> list : listOfList) {
			do {
				page++;
				count +=Constant.INT_500;
				String itemCode = String.join(Constant.COMMA, list);
				params.put(Constant.INVENTORY_ITEM_CODE, itemCode);
				// 是否作为替代物料查询
				if (isReplaceItem) {
					params.put(Constant.RELATED_ITEM_CODE, itemCode);
					params.put(Constant.INVENTORY_ITEM_CODE, null);
				}
				params.put(Constant.PAGE_NO, page);
				// 从erp获取替代物料
				JSONObject json = this.checkResult(erpUrl, params);
				if (Objects.isNull(json.get(Constant.BO)) || Objects.isNull(json.get(Constant.OTHER))) {
					searchCount = Constant.INT_0;
					continue;
				}
				String bo = json.get(Constant.BO).toString();
				String other = json.get(Constant.OTHER).toString();
				List<MtlRelatedItemsEntityDTO> entityDTOS = JsonConvertUtil.jsonToBean(bo, List.class, MtlRelatedItemsEntityDTO.class);
				entityDTOS.stream().forEach(e -> {
					e.setReplaceItemCode(e.getRelatedItemCode());
					e.setItemCode(e.getInventoryItemCode());
				});
				relatedItemsEntityDTOList.addAll(entityDTOS);
				Map<String,Integer> otherMap=JacksonJsonConverUtil.jsonToBean(other, new TypeReference<Map<String, Integer>>(){});
				if(otherMap == null){
					searchCount = Constant.INT_0;
					continue;
				}
				searchCount = otherMap.get(Constant.TOTAL_COUNT);
			} while (searchCount > count);
			count = Constant.INT_0;
			page = Constant.INT_0;
		}
		return relatedItemsEntityDTOList;
	}

	/**
	 *<AUTHOR>
	 * 从erp获取替代物料并校验
	 *@Date 2023/5/31 9:41
	 *@Param [java.lang.String, java.util.Map<java.lang.String,java.lang.Object>]
	 *@return
	 **/
	private JSONObject checkResult(String erpUrl, Map<String, Object> params) throws RouteException {
		Map<String, String> headParams = MESHttpHelper.getHttpRequestHeader();
		String getResult = HttpRemoteUtil.remoteExe(JSON.toJSONString(params), headParams, erpUrl, MicroServiceNameEum.SENDTYPEGET);
		JSONObject json=JSON.parseObject(getResult);
		if (null == json) {
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.FAILED_TO_OBTAIN_MATERIAL_SUBSTITUTION_RELATIONSHIP);
		}
		String retCode = json.getJSONObject(Constant.CODE).get(Constant.CODE).toString();
		if (!RetCode.SUCCESS_CODE.equalsIgnoreCase(retCode)) {
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.GET_ITEM_INFO_FAIL);
		}
		return json;
	}

	/**
	 *<AUTHOR>
	 * 从条码中心获取子条码物料信息
	 *@Date 2023/5/19 15:56
	 *@Param [com.zte.interfaces.dto.AssemblyRelaScanDTO]
	 *@return
	 **/
	private void getItemCodeByBarcodeCenter (AssemblyRelaScanDTO dto, Set<String> wipSnSet, Set<String> prodSet) throws Exception {
		List<String> subSnList = dto.getSubSnDTOList().stream().map(AssemblyRelaScanSubSnDTO::getSubSn).collect(Collectors.toList());
		dto.setSubSnList(subSnList);
		// 从条码中心获取子条码信息
		List<BarcodeExpandDTO> barcodeExpandDTOList = getBarcodeExpandDTOMap(dto);
		if (CollectionUtils.isEmpty(barcodeExpandDTOList)) {
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SUB_SN_NOT_EXIST);
		}
		Map<String, BarcodeExpandDTO> itemMap = barcodeExpandDTOList.stream().filter(e -> StringUtils.isNotEmpty(e.getBarcode()) && StringUtils.isNotEmpty(e.getItemCode()))
				.collect(Collectors.toMap(BarcodeExpandDTO::getBarcode,
						k2 -> k2, (k1, k2) -> k2));
		List<AssemblyRelaScanSubSnDTO> saveList = new ArrayList<>();
		List<BarcodeExpandDTO> newBarcodeList = new ArrayList<>();
		BarcodeExpandDTO barcodeExpandDTO = barcodeExpandDTOList.get(NumConstant.NUM_ZERO);
		for (AssemblyRelaScanSubSnDTO snDTO : dto.getSubSnDTOList()) {
			// 不在条码中心的条码不处理
			if (itemMap.get(snDTO.getSubSn()) == null) {
				continue;
			}
			// 传入的物料代码与条码中心不一致报错
			if (!StringUtils.equals(snDTO.getItemNo(), itemMap.get(snDTO.getSubSn()).getItemCode())) {
				throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SN_ITEMNO_NOT_SAME, new Object[] { snDTO.getSubSn(), snDTO.getItemNo() });
			}
			// 未传数量为1
			snDTO.setQty(snDTO.getQty() == null ? Constant.INT_1 : snDTO.getQty());
			newBarcodeList.add(itemMap.get(snDTO.getSubSn()));
			snDTO.setItemName(itemMap.get(snDTO.getSubSn()).getItemName());
			saveList.add(snDTO);
		}
		// 需处理list为空报错
		if (CollectionUtils.isEmpty(saveList)) {
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.NOT_NEED_BIND_SUB_SN);
		}
		// 刷新子条码list
		subSnList = saveList.stream().map(AssemblyRelaScanSubSnDTO::getSubSn).collect(Collectors.toList());
		dto.setSubSnDTOList(saveList);
		dto.setSubSnList(subSnList);
		// 校验子条码
		this.checkSubSn(dto.getSubSnList(), dto, newBarcodeList, wipSnSet, prodSet);
	}

	/**
	 *<AUTHOR>
	 * 校验主条码在传入的子工序和工站下是否有绑定记录
	 *@Date 2023/5/19 13:38
	 *@Param [com.zte.interfaces.dto.AssemblyRelaScanDTO]
	 *@return
	 **/
	private void checkExistWipExt(AssemblyRelaScanDTO dto) throws Exception {
		WipExtendIdentificationDTO wipExtDTO = new WipExtendIdentificationDTO();
		wipExtDTO.setFormSn(dto.getMainSn());
		wipExtDTO.setProcessCode(dto.getProcessCode());
		wipExtDTO.setWorkStation(dto.getWorkStationCode());
		Integer extExist = wipExtendIdentificationRepository.checkWipExtExist(wipExtDTO);
		if (extExist != null && extExist > Constant.INT_0) {
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.THIS_MAIN_SN_IS_BIND, new Object[] { dto.getMainSn(), dto.getProcessName(), dto.getWorkStationName() });
		}
	}

	/**
	 *<AUTHOR>
	 * 获取主卡物料绑定清单
	 *@Date 2023/5/19 10:58
	 *@Param [com.zte.interfaces.dto.AssemblyRelaScanDTO]
	 *@return List<ProdBindingSettingDTO>
	 **/
	private List<ProdBindingSettingDTO> getBindList(AssemblyRelaScanDTO dto) throws Exception {
		ProdBindingSettingDTO settingDTO = new ProdBindingSettingDTO();
		settingDTO.setProductCode(dto.getItemCode());
		settingDTO.setProcessCode(dto.getProcessCode());
		settingDTO.setWorkStation(dto.getWorkStationCode());
		settingDTO.setMainSn(dto.getMainSn());
		settingDTO.setFactoryId(dto.getFactoryId());
		// 通过子工序及工站一起查
		List<ProdBindingSettingDTO> settingDTOList = prodBindingSettingRepository.getBindingInfoByItemNew(settingDTO);
		if (CollectionUtils.isEmpty(settingDTOList)) {
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.ITEM_NO_NO_BIND_LIST, new Object[] { dto.getItemCode(), dto.getProcessName(), dto.getWorkStationName() });
		}
		return settingDTOList;
	}

	/**
	 *<AUTHOR>
	 * 校验主条码
	 *@Date 2023/5/19 13:52
	 *@Param [com.zte.interfaces.dto.AssemblyRelaScanDTO]
	 *@return
	 **/
	private Pair<RetCode, FlowControlInfoDTO> checkMainSn (AssemblyRelaScanDTO dto) throws Exception {
		// 获取主条码信息
		PsWipInfo wipInfo = psWipInfoRepository.selectPsWipInfoBySn(dto.getMainSn());
		if (wipInfo == null) {
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.NO_MAIN_SN, new Object[] { dto.getMainSn() });
		}
		if (Constant.IN_STORE.equals(wipInfo.getCraftSection())) {
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.MAIN_SN_IS_IN_STORE);
		}
		dto.setItemCode(wipInfo.getItemNo());
		StringBuilder workOrderNo = new StringBuilder();
		// 设置过站线体
		wipInfo.setAttribute1(this.setPassLineCode(dto, wipInfo.getAttribute2(), workOrderNo));
		if (StringUtils.isEmpty(workOrderNo)) {
			// 没有找到当前指令
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.GET_CURRENT_WORKORDER_ERROR);
		}
		dto.setWorkOrderNo(workOrderNo.toString());
		FlowControlInfoDTO checkFlowRsult = new FlowControlInfoDTO();
		if (dto.isToPassWorkStaion()) {
			// 调用流程管控校验
			checkFlowRsult = checkFlow(dto);
			if (Constant.FAIL.equals(checkFlowRsult.getResultType())) {
				RetCode retCode = new RetCode(RetCode.BUSINESSERROR_CODE, RetCode.BUSINESSERROR_MSGID);
				retCode.setMsg(checkFlowRsult.getErrorMessage());
				return Pair.of(retCode, checkFlowRsult);
			}
		}
		dto.setWipInfo(wipInfo);
		dto.setMainSnWipInfo(wipInfo);
		RetCode ret = new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID);
		ret.setMsg(Constant.MAIN_SN_SCAN_SUCCESS);
		return Pair.of(ret, checkFlowRsult);
	}

	/**
	 *<AUTHOR>
	 * 校验需保存数据
	 *@Date 2023/5/17 16:55
	 *@Param [com.zte.interfaces.dto.AssemblyRelaScanDTO]
	 *@return
	 **/
	private void checkSaveParams (AssemblyRelaScanDTO dto) throws Exception {
		// 子工序不能为空
		if (StringUtils.isEmpty(dto.getProcessName())) {
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PROCESS_IS_NOT_NULL);
		}
		// 工站不能为空
		if (StringUtils.isEmpty(dto.getWorkStationName())) {
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.WORK_STATION_IS_NOT_NULL);
		}
		// 子条码不能为空
		if (CollectionUtils.isEmpty(dto.getSubSnDTOList())) {
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SUB_SN_IS_NOT_NULL);
		}
		// 子条码一次最多一百条
		if (dto.getSubSnDTOList().size() > Constant.BATCH_SIZE) {
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SN_MORE_THAN_100);
		}
	}

	@Override
	public RetCode relaCount(AssemblyRelaScanDTO dto) throws Exception {
		RetCode ret = this.checkFormSn(dto);
		if(RetCode.SERVERERROR_CODE.equals(ret.getCode())){
			return ret;
		}

		RetCode countRet = new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID);
		Map<String,Object> map = CommonUtils.object2Map(dto);
		long result = wipExtendIdentificationRepository.getCount(map);
		countRet.setMsg(result + "");
		return countRet;
	}

	@Override
	public RetCode checkFormSn(AssemblyRelaScanDTO dto){
		RetCode ret = new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID);
		Map<String, Object> wipMap = new HashMap<>();
		wipMap.put("sn", dto.getFormSn());
		List<PsWipInfo> wipList = psWipInfoRepository.getList(wipMap);
		// 若没有在制信息，则抛异常信息
		if(CollectionUtils.isEmpty(wipList)){
			String[] params = new String[] { dto.getFormSn() };
			return new com.zte.springbootframe.common.model.RetCode(RetCode.SERVERERROR_CODE, MessageId.NO_MAIN_SN, params);
		}
		// 获取绑定信息
		wipMap.put("formType",Constant.BINDED_AND_PASSWORKSTATION_COMPLETE );
		wipMap.put("sn", "");
		wipMap.put("formSn", dto.getFormSn());
		List<WipExtendIdentification> list = wipExtendIdentificationRepository.getList(wipMap);
		// 若没有绑定信息，则抛异常信息
		if(CollectionUtils.isEmpty(list)){
			String[] params = new String[] { dto.getFormSn() };
			return new com.zte.springbootframe.common.model.RetCode(RetCode.SERVERERROR_CODE, MessageId.NO_BIND_INFO_MAIN, params);
		}

		return ret;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public RetCode relaUnbind(AssemblyRelaScanDTO dto) throws Exception {
		JsonNode json = null;
		RetCode ret = new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID);
		int delResult = Constant.INT_0;
		Map<String,Object> map = CommonUtils.object2Map(dto);
		// 通过主条码获取在制信息
		Map<String, Object> wipMap = new HashMap<>();
		wipMap.put("sn", dto.getFormSn());
		List<PsWipInfo> wipList = psWipInfoRepository.getList(wipMap);
		// 若没有在制信息，则抛异常信息
		if(CollectionUtils.isEmpty(wipList)){
			String[] params = new String[] { dto.getFormSn() };
			return new com.zte.springbootframe.common.model.RetCode(RetCode.SERVERERROR_CODE, MessageId.NO_MAIN_SN, params);
		}
		String itemNo = "";
		String prodplanId = "";
		BigDecimal entityId = new BigDecimal(Constant.INT_0);
		for(PsWipInfo info: wipList){
			itemNo = info.getItemNo();
			entityId = info.getEntityId();
			prodplanId = info.getAttribute1();
		}
        // 获取绑定信息
		map.put("formType",Constant.BINDED_AND_PASSWORKSTATION_COMPLETE );
		List<WipExtendIdentification> list = wipExtendIdentificationRepository.getList(map);
		// 若没有绑定信息，则抛异常信息
		if(CollectionUtils.isEmpty(list)){
			// 若按子条码解绑
			if(!StringUtils.isEmpty(dto.getSn())){
				String[] params = new String[] { dto.getSn() };
				return new com.zte.springbootframe.common.model.RetCode(RetCode.SERVERERROR_CODE, MessageId.NO_BIND_INFO_SUB, params);
			}else{
				String[] params = new String[] { dto.getFormSn() };
				return new com.zte.springbootframe.common.model.RetCode(RetCode.SERVERERROR_CODE, MessageId.NO_BIND_INFO_MAIN, params);
			}
		}
		try{
			String attribute3 = Constant.UNBIND_BY_MAIN_SN;
			// 若子条码为空则按照主条码解绑，否则按照子条码解绑
			if(StringUtils.isEmpty(dto.getSn())){
				WipExtendIdentification o = new WipExtendIdentification();
				o.setFormSn(dto.getFormSn());
				o.setFormType(Constant.BINDED_AND_PASSWORKSTATION_COMPLETE);
				insertOptRecord(dto, wipExtendIdentificationRepository.selectWipExtendByFromSn(o));
				delResult = wipExtendIdentificationRepository.deleteWipExtend(o);
			}else{
				attribute3 = Constant.UNBIND_BY_SUB_SN;
				insertOptRecord(dto, wipExtendIdentificationRepository.selectWipExtendBySubSn(map));
				delResult = wipExtendIdentificationRepository.deleteWipExtendBySubSn(map);

			}
			// 若删除绑定关系失败
			if(!(delResult > 0)){
				throw new Exception(Constant.UNBIND_ERROR);
			}
            // 组装交易信息
			List<SemiManufactureDealInfo> dtoList = new ArrayList<>();
			for(WipExtendIdentification ccInfo: list){
				// 插入交易表所需信息
				SemiManufactureDealInfo semi = generateSemiManufactureDealInfo(dto, itemNo, prodplanId, attribute3, ccInfo);
				semi.setEntityId(entityId);
				dtoList.add(semi);
			}
			ProductionDeliveryRemoteService.semiManufactureDealInfoInsertBatch(dtoList);
		}catch(Exception e){
			ret = new RetCode(RetCode.SERVERERROR_CODE, RetCode.SERVERERROR_MSGID);
			throw(e);
		}
		return ret;
	}

	/**
	 * 组装交易信息
	 * @param dto
	 * @param itemNo
	 * @param prodplanId
	 * @param attribute3
	 * @param ccInfo
	 * @return
	 */
	private SemiManufactureDealInfo generateSemiManufactureDealInfo(AssemblyRelaScanDTO dto, String itemNo, String prodplanId, String attribute3, WipExtendIdentification ccInfo) {
		SemiManufactureDealInfo semi = new SemiManufactureDealInfo();
		semi.setRecordId(UUID.randomUUID().toString());
		semi.setLpn(ccInfo.getFormSn());
		semi.setSn(ccInfo.getSn());
		semi.setProdplanId(prodplanId);
		semi.setDealType(Constant.STRING_6);
		semi.setAttribute3(attribute3);
		semi.setAttribute1(attribute3);
		semi.setAttribute2(itemNo);
		semi.setFactoryId(dto.getFactoryId());
		semi.setCreateBy(dto.getCreateBy());
		semi.setLastUpdatedBy(dto.getCreateBy());
		return semi;
	}

	/**
	 * 写解绑操作记录
	 * @param dto
	 * @param wipExtendIdentifications
	 */
	public void insertOptRecord(AssemblyRelaScanDTO dto, List<WipExtendIdentification> wipExtendIdentifications) {
		List<WipExtendIdentification> wipExtendIdentificationList = wipExtendIdentifications;
		if (!CollectionUtils.isEmpty(wipExtendIdentificationList)) {
			wipExtendIdentificationList.forEach(p -> {
				p.setEmpNo(dto.getCreateBy());
			});
			wipExtendIdentificationService.batchInsertOptRecord(Constant.STR_2, wipExtendIdentificationList);
		}
	}

	/**
	 * 获取code
	 * 原语句
	 * String code = null != json && null != json.get(Constant.STR_CODE)?json.get(Constant.STR_CODE).get(Constant.STR_CODE) + Constant.STR_EMPTY:Constant.STR_EMPTY;
	 * @param json json节点
	 * @return code
	 */
	private String getCode(JsonNode json) {
		return null != json && null != json.get(Constant.STR_CODE)?json.get(Constant.STR_CODE).get(Constant.STR_CODE) + Constant.STR_EMPTY:Constant.STR_EMPTY;
	}

	/**
	 * 设置线体代码
	 * @param dto
	 * @return
	 */
	public RetCode setLineCode (AssemblyRelaScanDTO dto) throws Exception{
		Map<String,Object> queryCond = new HashMap<String,Object>();
		queryCond.put("lineName", dto.getLineName());
		List<CFLine> lineList = BasicsettingRemoteService.getLine(queryCond);
		if (CollectionUtils.isEmpty(lineList)) {
			return new RetCode(RetCode.BUSINESSERROR_CODE,MessageId.LINE_NAME_NOT_FOUND);
		}else {
			dto.setLineCode(lineList.get(NumConstant.NUM_ZERO).getLineCode());
			return new RetCode(RetCode.SUCCESS_CODE,RetCode.SUCCESS_MSGID);
		}
	}

	/**
	 * 设置子工序和工站
	 *
	 * @param processList
	 * @param dto
	 */
	public RetCode setProcessAndWorkStation(List<BSProcess> processList, AssemblyRelaScanDTO dto) {
		if (!CollectionUtils.isEmpty(processList)) {
			List<BSProcess> resultList = processList.stream()
					.filter(process -> dto.getProcessName().equals(process.getProcessName()) &&  MpConstant.PROCESS_X_TYPE_P.equals(process.getxType()))
					.collect(Collectors.toList());
			List<BSProcess> workStationList = processList.stream()
					.filter(process -> dto.getWorkStationName().equals(process.getProcessName()) && MpConstant.PROCESS_X_TYPE_S.equals(process.getxType()))
					.collect(Collectors.toList());
			if (!CollectionUtils.isEmpty(resultList)) {
				dto.setProcessCode(resultList.get(NumConstant.NUM_ZERO).getProcessCode());
				dto.setCraftSection(resultList.get(NumConstant.NUM_ZERO).getCraftSection());
			}
			if (!CollectionUtils.isEmpty(workStationList)) {
				dto.setWorkStationCode(workStationList.get(NumConstant.NUM_ZERO).getProcessCode());
			}
		}
		if (StringHelper.isEmpty(dto.getProcessCode())) {
			return new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.PROCESS_DETAILS_NOT_FOUND);
		}
		if (StringHelper.isEmpty(dto.getWorkStationCode())) {
			return new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.WORKSTATION_ERROR);
		}
		return new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID);
	}

	/**
	 * 获取子工序和工站信息
	 *
	 * @param dto
	 * @return
	 * @throws Exception
	 */
	public List<BSProcess> getProcessInfo(AssemblyRelaScanDTO dto) throws Exception {

		List<BSProcess> returnList = new ArrayList<>();
		StringBuilder inProcessCode = new StringBuilder();
		inProcessCode.append(Constant.SINGLE_QUOTE).append(dto.getProcessName()).append(Constant.SINGLE_QUOTE)
				.append(Constant.COMMA);
		inProcessCode.append(Constant.SINGLE_QUOTE).append(dto.getWorkStationName()).append(Constant.SINGLE_QUOTE);
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("inProcessName", inProcessCode.toString());
		JsonNode json = CrafttechRemoteService.getProcessInfo(map);
		if(json != null) {
			String retCode = json.get(MpConstant.JSON_CODE).get(MpConstant.JSON_CODE).asText();
			String bo = json.get(MpConstant.JSON_BO).toString();
			if (!RetCode.SUCCESS_CODE.equalsIgnoreCase(retCode)) {
				throw new Exception(bo);
			}
			returnList = JsonConvertUtil.jsonToBean(bo, List.class, BSProcess.class);
		}
		return returnList;
	}

	/**
	 * 子条码扫描
	 *
	 * @param dto
	 * @return
	 * @throws Exception
	 */
	public RetCode subSnScan(AssemblyRelaScanDTO dto, FlowControlInfoDTO checkFlowRsult) throws Exception {
		RetCode ret = new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID);
		// 校验子条码是否已有绑定记录
		ret = validateSn(dto);
		if (!RetCode.SUCCESS_CODE.equals(ret.getCode())) {
			return ret;
		}
		PsWipInfo wipInfo = psWipInfoRepository.getWipInfoBySn(dto.getMainSn());
		dto.setWipInfo(wipInfo);

		// 界面扫描子条码是才需要再次调用流程管控或者已全部绑定完成（包含中试条码）需要过站时
		if ((dto.isSubSnScan() && Constant.STR_NUMBER_ONE.equals(dto.getIsPassWorkStaion()))
				|| dto.isToPassWorkStaion()) {
			ret = getworkOrderBySnScan(dto);
			if(Constant.BUS_ERROR.equals(ret.getCode())) {
				return ret;
			}
			// 调用流程管控校验
			checkFlowRsult = checkFlow(dto);
			if (Constant.FAIL.equals(checkFlowRsult.getResultType())) {
				RetCode retCode = new RetCode(RetCode.BUSINESSERROR_CODE, RetCode.BUSINESSERROR_MSGID);
				retCode.setMsg(checkFlowRsult.getErrorMessage());
				return retCode;
			}
		}
		List<BaItem> itemInfoList = new ArrayList<BaItem>();
		Map<String, String> replaceMap=new HashMap<>();
		List<ProdBindingSettingDTO> bindList = new ArrayList<ProdBindingSettingDTO>();

	    ret = dealItemList(replaceMap,dto,itemInfoList, bindList);
		dto.setProdBindingSettingDTOS(bindList);
		dto.setItemInfoParamList(itemInfoList);
		return getRetCode(replaceMap,dto, checkFlowRsult, ret);
	}

	/**
	 * 重复代码
	 * @param dto
	 * @param checkFlowRsult
	 * @param ret
	 * @return
	 * @throws Exception
	 */

	@Transactional(rollbackFor = Exception.class)
	public RetCode getRetCode(Map<String, String> replaceMap,AssemblyRelaScanDTO dto, FlowControlInfoDTO checkFlowRsult, RetCode ret) throws Exception {
		if (!RetCode.SUCCESS_CODE.equals(ret.getCode())) {
			return ret;
		}
		List<BaItem> itemInfoList = dto.getItemInfoParamList();
		List<ProdBindingSettingDTO> bindList = dto.getProdBindingSettingDTOS();
		// 主条码绑定完成后过站、回写
		insertWipExtend(dto, itemInfoList, dto.getMainWorkOrder(),replaceMap);
		ret.setMsg(Constant.SUB_SN_SCAN_SUCCESS);
		setRetCode(ret, dto, itemInfoList);
		// 批量接口不过站返回
		if (dto.isBatchFlag() && !dto.isToPassWorkStaion()) {
			return ret;
		}
		//自动过站（不包含中试条码）界面上勾选了过站且全部绑定完成时或者前台判断已经绑定完成时做过站处理
		if ((!dto.isNewAssemblyRelaScan() || (dto.isNewAssemblyRelaScan() && isCanSkipFlag(dto))) && this.isFinish(dto, bindList)) {
			RetCode retCode = dealAllBinded(dto, checkFlowRsult);
			if (retCode != null) {
				setRetCode(retCode, dto, itemInfoList);
			}
			return retCode;
		}
		return ret;
	}

	private RetCode getworkOrderBySnScan(AssemblyRelaScanDTO dto) throws Exception {
		RetCode ret = new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID);
		if (StringHelper.isNotEmpty(dto.getWorkOrderNo())) {
			// 获取指令信息
			List<PsWorkOrderDTO> workOrderList = getWorkOrderInfo(dto);
			if (CollectionUtils.isEmpty(workOrderList)) {
				ret = new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.WORDER_ORDER_NOT_FOUND);
				return ret;
			}
			PsWorkOrderDTO workOrder = workOrderList.get(NumConstant.NUM_ZERO);
			dto.setMainWorkOrder(workOrder);
		}
		return ret;
	}

	public RetCode dealItemList(Map<String, String> replaceMap,AssemblyRelaScanDTO dto,List<BaItem> itemInfoList,List<ProdBindingSettingDTO> bindList) throws Exception{
		RetCode ret = new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID);
		if (dto.isNotAutoPassWorkStaion()) {
			//包含中试条码则直接获取前台传过来的物料信息
			itemInfoList.addAll(dto.getItemInfoList());
			return ret;
		}
		//自动过站（不包含中试条码）保留原有逻辑
		// 根据子条码获取子物料信息（前端传入）

		List<BaItem> item = getItemCode(dto);
		if(item != null && item.size() > 0){
			itemInfoList.addAll(item);
		}

		// 未找到物料信息
		if (CollectionUtils.isEmpty(itemInfoList) || itemInfoList.size() != dto.getSubSnList().size()) {
			return new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.ITEM_INFO_FIND_ERROR);
		}
		if (StringHelper.isNotEmpty(itemInfoList.get(NumConstant.NUM_ZERO).getErrMsg())) {
			String[] params = new String[] { itemInfoList.get(NumConstant.NUM_ZERO).getErrMsg() };
			return new com.zte.springbootframe.common.model.RetCode(RetCode.BUSINESSERROR_CODE, MessageId.ITEM_INFO_NOT_FOUND, params);
		}

		// 主物料代码
		ProdBindingSettingDTO queryCode = new ProdBindingSettingDTO();
		queryCode.setProductCode(dto.getItemCode());
		queryCode.setMainSn(dto.getMainSn());
		queryCode.setFactoryId(dto.getFactoryId());
		queryCode.setProcessCode(dto.getProcessCode());
		// 根据主物料代码获取绑定关系
		bindList.addAll(prodBindingSettingRepository.getBindingInfoByItemNew(queryCode));
		if (CollectionUtils.isEmpty(bindList)) {
			return new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.BIND_RELATION_NOT_FOUND);
		}
		ret = validateItemInfo(replaceMap,itemInfoList, bindList);
		if (!RetCode.SUCCESS_CODE.equals(ret.getCode())) {
			return ret;
		}
		return ret;
	}


	/**
	 * 获取子物料代码的物料需求清单
	 */
	public List<ItemListEntityDTO> getErpItemList(String taskNo, String itemNo) throws Exception{
		Map<String, String> paramsMap = new HashMap<>();
		paramsMap.put(BusinessConstant.TASK_NO, taskNo);
		paramsMap.put(BusinessConstant.ITEM_NO, itemNo);
		logger.info("远程调用同步的入参 :{}" , paramsMap);
		Map<String,String> headParams = MESHttpHelper.getHttpRequestHeader();
		String url = constantInterface.getUrl(InterfaceEnum.dataWbSysErpList);
		logger.info("远程调用同步的URL :{}" , url);
		String msg = HttpRemoteService.remoteExe(InterfaceEnum.dataWbSysErpList,paramsMap,headParams,url);
		if (StringUtils.isBlank(msg)) {
			throw new Exception(CommonUtils.getLmbMessage(MessageId.SELECT_ERROR));
		}
		JsonNode json = JacksonJsonConverUtil.getMapperInstance().readTree(msg);
		logger.info("远程调用同步的结果 :{}" , json);
		String retCode = json.get(MpConstant.JSON_CODE).get(MpConstant.JSON_CODE).asText();
		String bo = json.get(MpConstant.JSON_BO).toString();
		if(!RetCode.SUCCESS_CODE.equalsIgnoreCase(retCode)){
			throw new Exception(bo);
		}
		return JsonConvertUtil.jsonToBean(bo, List.class, ItemListEntityDTO.class);
	}

	public void setRetCode(RetCode retCode, AssemblyRelaScanDTO dto, List<BaItem> itemInfoList) {
		if (!dto.isExternalScan()) {
			retCode.setMsg(retCode.getMsg() + Constant.COMMA + itemInfoList.get(NumConstant.NUM_ZERO).getItemNo()+ Constant.COMMA + dto.getSubSnQty()+Constant.COMMA + itemInfoList.get(NumConstant.NUM_ZERO).getItemName());
		}
	}

	/**
	 * 校验子条码是否已经有绑定记录
	 *
	 * @param dto
	 * @return
	 */
	public RetCode validateSn(AssemblyRelaScanDTO dto) {
		List<List<String>> listOfList = CommonUtils.splitList(dto.getSubSnList(), Constant.BATCH_SIZE);
		List<WipExtendIdentification> resultList = new ArrayList<WipExtendIdentification>();
		StringBuilder snSb = new StringBuilder();
		StringBuilder parentSnSb = new StringBuilder();
		for (List<String> snList : listOfList) {
			Map<String, Object> map = new HashMap<String, Object>();
			map.put("snList", snList);
			List<WipExtendIdentification> tempList = wipExtendIdentificationRepository.getList(map);
			resultList.addAll(tempList);
		}
		if (!CollectionUtils.isEmpty(resultList)) {
			for (WipExtendIdentification wipExend : resultList) {
				snSb.append(wipExend.getSn()).append(" ");
				parentSnSb.append(wipExend.getFormSn()).append(" ");
			}
			String[] params = new String[] { snSb.toString(),parentSnSb.toString() };
			return new com.zte.springbootframe.common.model.RetCode(RetCode.BUSINESSERROR_CODE, MessageId.SN_HAS_BINDED, params);
		}
		return new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID);
	}


	/**
	 * 遍历主物料的绑定关系判断子物料是否存在绑定关系及绑定后数量是否大于需求数量
	 * @param bindList 绑定list
	 * @param itemNoPair 集合
	 * @param mtlRelatedMap
	 * @param pair
	 */
	public boolean eachMainMaterial(Map<String, String> replaceMap,Map<String, List<MtlRelatedItemsEntityDTO>> mtlRelatedMap, List<ProdBindingSettingDTO> bindList,
									Pair<String,Long> itemNoPair , Pair<StringBuilder,Boolean> pair)throws Exception{
		String itemNo = itemNoPair.getFirst();
		Long bindQty = itemNoPair.getSecond();
		StringBuilder itemCountSb = pair.getFirst();
		boolean wipSnFlag = pair.getSecond();
		boolean findKey = false;
		List<MtlRelatedItemsEntityDTO> mtlRelatedItemsEntityDTOList=mtlRelatedMap.get(itemNo);
		for (ProdBindingSettingDTO bindDto : bindList) {
			int count = NumConstant.NUM_ZERO;
			String replaceItem= getReplaceItemInBindList(itemNo, mtlRelatedItemsEntityDTOList, bindDto.getItemCode(), count);
			if (itemNo.equals(bindDto.getItemCode())) {
				findKey = true;
				// 绑定后数量
				setBinderCount(itemNo, bindQty, itemCountSb, bindDto);
				// 找到子物料代码对应到绑定关系后结束本次循环，继续找下一个子物料代码
				break;
			}else if (StringUtils.isNotEmpty(replaceItem)) {
				//记录替代关系
				replaceMap.put(replaceItem, itemNo);
				findKey = true;
				setBinderCount(replaceItem, bindQty, itemCountSb, bindDto);
				// 找到子物料代码对应到绑定关系后结束本次循环，继续找下一个子物料代码
				break;
			}
		}
		return findKey;
	}

	private void setBinderCount(String itemNo, Long bindQty, StringBuilder itemCountSb, ProdBindingSettingDTO bindDto) {
		// 绑定后数量
		BigDecimal bindedAfter = bindDto.getBindedCount().add(new BigDecimal(bindQty));
		// 绑定后数量大于需求数量
		if (bindedAfter.compareTo(bindDto.getUsageCount()) > 0) {
			itemCountSb.append(itemNo).append(" ");
		} else {
			// 设置新的已绑定数量=绑定数量+本次绑定数量，用于后面判断主条码是否完成绑定
			bindDto.setBindedCount(bindedAfter);
		}
	}

	/**
	 * 获取物料对应替代物料是否在绑定清单
	 * @param itemNo
	 * @param mtlRelatedItemsEntityDTOList
	 * @param itemCode
	 * @param count
	 * @throws MesBusinessException
	 */
	private String getReplaceItemInBindList(String itemNo, List<MtlRelatedItemsEntityDTO> mtlRelatedItemsEntityDTOList, String itemCode, int count) throws MesBusinessException {
		if(CollectionUtils.isEmpty(mtlRelatedItemsEntityDTOList)){
			return null;
		}
		String replaceItemNo = null;
		for(MtlRelatedItemsEntityDTO mtlRelatedItemsEntityDTO: mtlRelatedItemsEntityDTOList){
			String replaceItem = mtlRelatedItemsEntityDTO.getInventoryItemCode().substring(Constant.INT_0, Constant.INT_12);
			if(StringUtils.equals(itemCode,replaceItem)){
				count++;
				replaceItemNo = mtlRelatedItemsEntityDTO.getInventoryItemCode();
			}
		}
		//替代物料在绑定清单有多个报错
		if(count >NumConstant.NUM_ONE){
		  throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,MessageId.ITEM_CODE_EXIST_IN_LIST_MORE_THAN_ONE,new Object[]{itemNo});
		}
		return replaceItemNo;
	}

	/**
	 * 校验子物料是否存在绑定关系及绑定数量是否大于需求数量
	 *
	 * @param itemInfoList
	 * @param bindList
	 * @return
	 */
	public RetCode validateItemInfo(Map<String, String> replaceMap,List<BaItem> itemInfoList, List<ProdBindingSettingDTO> bindList) throws Exception{
		// 统计每个物料待绑定的数量
		Map<String, Long> countMap = itemInfoList.stream()
				.collect(Collectors.groupingBy(BaItem::getItemNo, Collectors.counting()));
		StringBuilder itemCountSb = new StringBuilder();
		//替代物料
		List<String> itemCodeList=itemInfoList.stream().map(e->e.getItemNo()).collect(Collectors.toList());
		List<MtlRelatedItemsEntityDTO> mtlRelatedItemsEntityDTOList=new ArrayList<>();
		if(!CollectionUtils.isEmpty(itemCodeList)){
			mtlRelatedItemsEntityDTOList.addAll(datawbRemoteService.getItemInfoListBatch(itemCodeList));
		}
		//按物料代码分组
		Map<String, List<MtlRelatedItemsEntityDTO>> mtlRelatedMap = mtlRelatedItemsEntityDTOList.stream().distinct().collect(Collectors.groupingBy(MtlRelatedItemsEntityDTO::getItemCode));

		for (Map.Entry<String, Long> entry : countMap.entrySet()) {
			String itemNo = entry.getKey();
			// boolean findKey = false;
			// 遍历主物料的绑定关系判断子物料及替代物料是否存在绑定关系及绑定后数量是否大于需求数量
			boolean findKey = this.eachMainMaterial(replaceMap,mtlRelatedMap,bindList, Pair.of(itemNo, entry.getValue()), Pair.of(itemCountSb, false));
//			// 未找到该子物料的绑定关系  20211124 没有此接口
//			if (!findKey) {
//				// 主条码对应的任务号是整机任务，校验子物料代码是否在物料需求清单中；主条码对应的任务号是单板任务,执行现有逻辑
//				if (dto.getItemCode().length() == MpConstant.NUM_12) {
//					List<ItemListEntityDTO> itemListEntityDTOList = this.getErpItemList(dto.getWipInfo().getAttribute2(), itemNo);
//					if (CollectionUtils.isEmpty(itemListEntityDTOList)) {
//						String[] params = new String[] {itemNo};
//						return new com.zte.springbootframe.common.model.RetCode(RetCode.BUSINESSERROR_CODE, MessageId.CHILD_ITEMNO_INCONSISTENT_ERP_LIST, params);
//					}
//				} else {
//					itemRelaSb.append(itemNo).append(" ");
//				}
//
//			}
		}
		checkSnItemBindOK(itemCountSb, bindList, countMap,false);
		return new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID);
	}

	/**
	 *<AUTHOR>
	 * 校验子卡是否可以绑定
	 *@Date 2023/5/30 13:54
	 *@Param [java.lang.StringBuilder, java.lang.StringBuilder]
	 *@return
	 **/
	private void checkSnItemBindOK(StringBuilder itemCountSb, List<ProdBindingSettingDTO> bindList, Map<String, Long> qtyMap, boolean checkFlag) {
		if (StringUtils.isEmpty(itemCountSb)) {
			if (!checkFlag) {
				return;
			}
			for (ProdBindingSettingDTO settingDTO : bindList) {
				int needQty = settingDTO.getUsageCount().subtract(settingDTO.getBindedCount()).intValue();
				if (needQty > 0) {
					throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.BIND_COUNT_NEED_MORE_THAN, new Object[]{ settingDTO.getItemCode(), needQty });
				}
			}
			return;
		}
		String errItem = itemCountSb.toString();
		String[] params = new String[] { errItem };
		throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.BIND_COUNT_MORE_THAN_USAGE, params);
	}

	/**
	 * 绑定关系判断
	 * @return 布尔值
	 */
	private boolean bindIsNotEmpty(String notFoundRela, String errItem){
		return StringHelper.isNotEmpty(notFoundRela) && StringHelper.isNotEmpty(errItem);
	}



	/**
	 * 写绑定记录
	 *
	 * @param dto
	 */

	public void insertWipExtend(AssemblyRelaScanDTO dto, List<BaItem> itemInfoList,PsWorkOrderDTO workOrder,Map<String, String> replaceMap) {
		List<WipExtendIdentification> wipExtedList = new ArrayList<WipExtendIdentification>();
		List<String> snList=dto.getSubSnList();
		String source=dto.getSource();
		for (int i=0;i<snList.size();i++) {
			String sn = snList.get(i);
			WipExtendIdentification extendInfo = new WipExtendIdentification();
			BaItem itemInfo = getItemNo(itemInfoList, sn,replaceMap);
			extendInfo.setIdentiId(UUID.randomUUID().toString());
			extendInfo.setSn(sn);
			extendInfo.setFormSn(dto.getMainSn());
			extendInfo.setProcessCode(dto.getProcessCode());
			extendInfo.setWorkStation(dto.getWorkStationCode());
			if(dto.isNotAutoPassWorkStaion()){
				setSomeProperties(extendInfo, itemInfo);
			}else{
				extendInfo.setFormQty(new BigDecimal(Constant.INT_1));
				//设置替代物料
				setReplaceItemAndQty(dto, extendInfo, itemInfo);
			}
			// 设置的部分信息
			this.setExtendInfo(extendInfo, itemInfo);
			setTaskNoAndProdplanId(dto, workOrder, extendInfo);
			extendInfo.setLastUpdatedBy(dto.getCreateBy());
			extendInfo.setCreateBy(dto.getCreateBy());
			extendInfo.setFactoryId(dto.getFactoryId());
			// 环保属性
			extendInfo.setHbCode(dto.getHbCode());
			//是否中试
			if (StringUtils.isNotEmpty(source)&&MpConstant.SOURCE_DQAS.equals(source)){
				extendInfo.setIsZsIdentification(Constant.FLAG_Y);
				extendInfo.setZsScanOrder(i+"");
		     }else{
				extendInfo.setIsZsIdentification(Constant.FLAG_N);
		     }
			wipExtedList.add(extendInfo);
		}
		List<List<WipExtendIdentification>> listOfList = CommonUtils.splitList(wipExtedList, Constant.BATCH_SIZE);
		for (List<WipExtendIdentification> extendList : listOfList) {
			extendList.forEach(p->{p.setEmpNo(dto.getCreateBy());});
			wipExtendIdentificationService.batchInsertOptRecord(NumConstant.STR_ONE,extendList);
			wipExtendIdentificationRepository.insertWipExtendBatch(extendList);
		}

	}

	private void setSomeProperties(WipExtendIdentification extendInfo, BaItem itemInfo) {
		extendInfo.setFormQty(null == itemInfo ? new BigDecimal(Constant.INT_1) : itemInfo.getQty());
		extendInfo.setReplaceItemNo(null == itemInfo ? "" : itemInfo.getReplaceItemNo());
		extendInfo.setRemark(null == itemInfo ? "" : itemInfo.getRemark());
	}

	/**
	 * 设置任务号 批次
	 * @param dto
	 * @param workOrder
	 * @param extendInfo
	 */
	public void setTaskNoAndProdplanId(AssemblyRelaScanDTO dto, PsWorkOrderDTO workOrder, WipExtendIdentification extendInfo) {
		String taskNo = null != workOrder ? workOrder.getTaskNo() : MpConstant.STR_EMPTY;
		if(StringUtils.isEmpty(taskNo)){
			taskNo = dto.getWipInfo() == null?MpConstant.STR_EMPTY: dto.getWipInfo().getAttribute2();
		}
		String prodplanId = null != workOrder ? workOrder.getProdplanId() : MpConstant.STR_EMPTY;
		if(StringUtils.isEmpty(prodplanId)){
			prodplanId = dto.getWipInfo() == null?MpConstant.STR_EMPTY: dto.getWipInfo().getAttribute1();
		}
		extendInfo.setProdPlanId(prodplanId);
		extendInfo.setTaskNo(taskNo);
	}

	private void setReplaceItemAndQty(AssemblyRelaScanDTO dto, WipExtendIdentification extendInfo, BaItem itemInfo) {
		if(itemInfo !=null) {
			extendInfo.setReplaceItemNo(itemInfo.getReplaceItemNo());
			//新组装关系接口调用取传入的数量
			if(dto.isNewAssemblyRelaScan()){
				extendInfo.setFormQty(itemInfo.getQty());
			}
		}
	}

	/**
	 * 设置扩展信息，提取出的这部分，降低圈复杂度
	 * @param extendInfo 扩展信息
	 * @param itemInfo 集合信息
	 */
	private void setExtendInfo(WipExtendIdentification extendInfo,BaItem itemInfo) {
		extendInfo.setItemNo(null == itemInfo ? "" : itemInfo.getItemNo());
		extendInfo.setAttribute1(null == itemInfo ? "" : itemInfo.getItemName());
		extendInfo.setAttribute2(null == itemInfo ? "" : itemInfo.getItemVersion());
		extendInfo.setAttribute3(null == itemInfo ? "" : String.valueOf(itemInfo.getItemId()));
		extendInfo.setAttribute4(null == itemInfo ? "" : itemInfo.getScanType());
		extendInfo.setFormType(MpConstant.STRING_TWO);
	}

	/**
	 *
	 * @param itemInfoList
	 * @param sn
	 * @return
	 */
	public BaItem getItemNo(List<BaItem> itemInfoList, String sn,Map<String, String> replaceMap) {
		for (BaItem itemInfo : itemInfoList) {
			if (sn.equals(itemInfo.getSn())) {
				setReplaceItemCode(replaceMap, itemInfo);
				return itemInfo;
			}
		}
		return null;

	}

	/**
	 * 设置替代物料
	 * @param replaceMap
	 * @param itemInfo
	 */
	public void setReplaceItemCode(Map<String, String> replaceMap, BaItem itemInfo) {
		if(replaceMap == null || replaceMap.size()<NumConstant.NUM_ONE || itemInfo.isNotByReplaceItem()){
			return;
		}
		//如果是通过替代物料找到绑定清单物料
		String replaceItemCode= replaceMap.get(itemInfo.getItemNo());
		if(StringUtils.isNotBlank(replaceItemCode)){
			itemInfo.setReplaceItemNo(replaceItemCode);
		}
	}

	/**
	 * 校验主条码
	 *
	 * @param dto
	 * @return
	 * @throws Exception
	 */
	public Pair<RetCode, FlowControlInfoDTO> dealMainSn(AssemblyRelaScanDTO dto) throws Exception {
		FlowControlInfoDTO checkFlowRsult = new FlowControlInfoDTO();
		String itemCode  = dto.getItemCode();
		if (StringHelper.isNotEmpty(dto.getWorkOrderNo())) {
			// 获取指令信息
			List<PsWorkOrderDTO> workOrderList = getWorkOrderInfo(dto);
			if (CollectionUtils.isEmpty(workOrderList)) {
				return Pair.of(new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.WORDER_ORDER_NOT_FOUND), checkFlowRsult);
			}
			PsWorkOrderDTO workOrder = workOrderList.get(NumConstant.NUM_ZERO);
			dto.setMainWorkOrder(workOrder);
		}

		if (Constant.STR_NUMBER_ONE.equals(dto.getIsPassWorkStaion())) {
			// 调用流程管控校验
			checkFlowRsult = checkFlow(dto);
			if (Constant.FAIL.equals(checkFlowRsult.getResultType())) {
				RetCode retCode = new RetCode(RetCode.BUSINESSERROR_CODE, RetCode.BUSINESSERROR_MSGID);
				retCode.setMsg(checkFlowRsult.getErrorMessage());
				return Pair.of(retCode, checkFlowRsult);
			}
		}

		PsWipInfo wipInfo = psWipInfoRepository.getWipInfoBySn(dto.getMainSn());
		if (null == wipInfo) {
			return Pair.of(new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.SN_NOT_FOUND), checkFlowRsult);
		}
		if (!dto.getItemCode().equals(wipInfo.getItemNo())) {
			return Pair.of(new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.ITEM_DIFFERENCE), checkFlowRsult);
		}

		List<ProdBindingSettingDTO> bindList = new ArrayList<>();
		//绑定关系不包含中试条码需校验imes是否配置绑定关系
		RetCode  checkBindedListRetCode = checkBindedList(dto, itemCode,bindList);
		if (!StringUtils.equals(checkBindedListRetCode.getCode(),RetCode.SUCCESS_CODE)) {
			return Pair.of(new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.BIND_RELATION_NOT_FOUND), checkFlowRsult);
		}
		// 主条码绑定清单绑定完成后处理
		//新组装关系扫描 达到跳转数量绑定数量
		boolean canSkipFlag = dto.isNewAssemblyRelaScan() && isCanSkipFlag(dto);
		if ((!dto.isNewAssemblyRelaScan() || canSkipFlag) && this.isFinish(dto, bindList)) {
			RetCode retCode = dealAllBinded(dto, checkFlowRsult);
			return Pair.of(retCode, checkFlowRsult);
		}

		RetCode ret = new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID);
		ret.setMsg(Constant.MAIN_SN_SCAN_SUCCESS);
		return Pair.of(ret, checkFlowRsult);
	}

	/**
	 * 判断绑定数量是否大于等于跳转数量
	 *
	 * @param dto
	 * @return
	 */
	private boolean isCanSkipFlag(AssemblyRelaScanDTO dto) {
		if (!dto.isNewAssemblyRelaScan()) {
			return false;
		}
		//获取料单代码，子工序的已绑定总数量
		BigDecimal totalQty = getBindedCount(dto, "");
		return totalQty.compareTo(dto.getSkipTotalQty()) >= NumConstant.NUM_ZERO;
	}

	//校验勾选过站,绑定清单
	private RetCode checkBindedList(AssemblyRelaScanDTO dto, String itemCode,List<ProdBindingSettingDTO> bindList) {
		if (!dto.isNotAutoPassWorkStaion()) {
			ProdBindingSettingDTO queryCode = new ProdBindingSettingDTO();
			queryCode.setProcessCode(dto.getProcessCode());
			queryCode.setProductCode(itemCode);
			queryCode.setMainSn(dto.getMainSn());
			queryCode.setFactoryId(dto.getFactoryId());
			//旧组装关系扫描 ，没有绑定清单就报错 前端也有校
			bindList.addAll(prodBindingSettingRepository.getBindingInfoByItemNew(queryCode));
			if(!dto.isNewAssemblyRelaScan() && CollectionUtils.isEmpty(bindList)){
				return new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.BIND_RELATION_NOT_FOUND);
			}
		}
		return new RetCode(RetCode.SUCCESS_CODE, MessageId.SUCCESS);
	}


	/**
	 * 是否完成绑定
	 * 没有中试条码并且全部绑定完成，或者是前端已经判断可以过站
	 * @param dto dto
	 * @param bindList 绑定集合
	 * @return 布尔值
	 */
	private boolean isFinish(AssemblyRelaScanDTO dto, List<ProdBindingSettingDTO> bindList) {
		return (!dto.isNotAutoPassWorkStaion() && isAllBinded(bindList,dto)) ||
				dto.isToPassWorkStaion();
	}

	/**
	 * 主条码绑定完成处理
	 *
	 * @param dto
	 * @return
	 * @throws Exception
	 */

	public RetCode dealAllBinded(AssemblyRelaScanDTO dto, FlowControlInfoDTO checkFlowRsult) throws Exception {
		RetCode ret = new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID);
		ret.setMsg(Constant.MAINSN_BINDED_COMPLETE);
		// 主条码已绑定完成且需要过站则调用在制保存过站
		if (Constant.STR_NUMBER_ONE.equals(dto.getIsPassWorkStaion()) || dto.isToPassWorkStaion() || Constant.FLAG_Y.equals(dto.getIsPassWorkStaion())) {
			String resultMsg = "";
			if (null != dto.getMainWorkOrder()) {
				PsEntityPlanBasicDTO workOrderDto = new PsEntityPlanBasicDTO();
				BeanUtils.copyProperties(dto.getMainWorkOrder(), workOrderDto);
				checkFlowRsult.setEntityPlanBasic(workOrderDto);
			}
			// 指令来源为WMES调用整机在制保存，指令来源为STEP调用单板在制保存
			if (dto.getItemCode().length() == MpConstant.NUM_12) {
				resultMsg = standardModeCommonScanService.saveWipInfo(checkFlowRsult,dto.getProcessCode());
			} else if (dto.getItemCode().length() == MpConstant.NUM_15) {
				resultMsg = psWipInfoService.pmScanWipSave(checkFlowRsult);
			}
			if (!StringHelper.isEmpty(resultMsg)) {
				RetCode retCode = new RetCode(RetCode.BUSINESSERROR_CODE, RetCode.BUSINESSERROR_MSGID);
				retCode.setMsg(resultMsg);
				return retCode;
			}
			ret = new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID);
			ret.setMsg(Constant.BINDED_AND_PASSWORKSTATION_COMPLETE);
			return ret;
		}
		return ret;
	}

	/**
	 * 判断是否会写开关
	 * @param isWriteBack 是会写开关
	 * @param dto 传输
	 * @return 布尔值
	 */
	private boolean isNotBackSwitch(boolean isWriteBack, AssemblyRelaScanDTO dto) {
		return ((Constant.STR_NUMBER_ONE.equals(dto.getIsPassWorkStaion())) || dto.isToPassWorkStaion() && isWriteBack && dto.getItemCode().length() == MpConstant.NUM_12);
	}

	/**
	 * 调用在制保存进行过站
	 *
	 * @param dto
	 * @return
	 * @throws Exception
	 */
	public String passWorkStation(List<PsEntityPlanBasic> workOrderList, AssemblyRelaScanDTO dto) throws Exception {
		FlowControlInfoDTO flow = new FlowControlInfoDTO();
		PsEntityPlanBasicDTO workOrderDto = new PsEntityPlanBasicDTO();
		BeanUtils.copyProperties(workOrderList.get(NumConstant.NUM_ZERO), workOrderDto);
		flow.setEntityPlanBasic(workOrderDto);
		flow.setCurrProcessCode(dto.getProcessCode());
		flow.setWorkOrderNo(dto.getWorkOrderNo());
		flow.setSn(dto.getMainSn());
		flow.setWorkStation(dto.getWorkStationCode());
		flow.setLineCode(dto.getLineCode());
		flow.setCreateBy(dto.getCreateBy());
		flow.setLastUpdatedBy(dto.getCreateBy());
		flow.setFactoryId(dto.getFactoryId());
		return psWipInfoService.pmScanWipSave(flow);
	}

	/**
	 * 校验绑定清单是否已绑定完成
	 *
	 * @param bindList
	 */
	public Boolean isAllBinded(List<ProdBindingSettingDTO> bindList,AssemblyRelaScanDTO dto) {
		//新组装关系扫描 绑定清单为空时认为绑定清单是已绑定完成
		if(dto.isNewAssemblyRelaScan() && CollectionUtils.isEmpty(bindList)){
			return true;
		}
		boolean isAssBinded = true;
		if (CollectionUtils.isEmpty(bindList)) {
			return false;
		}
		for (ProdBindingSettingDTO bindInfo : bindList) {
			if (bindInfo.getUsageCount().compareTo(bindInfo.getBindedCount()) != 0) {
				isAssBinded = false;
				break;
			}
		}
		return isAssBinded;
	}

	/**
	 * 点对点调用获取指令信息
	 *
	 * @param dto
	 * @return
	 * @throws Exception
	 */
	public List<PsWorkOrderDTO> getWorkOrderInfo(AssemblyRelaScanDTO dto) throws Exception {

		List<PsWorkOrderDTO> workOrderList = new ArrayList<>();
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("workOrderNo", dto.getWorkOrderNo());
		JsonNode json = PlanscheduleRemoteService.getBasicWorkOrderInfo(map);
		if(json != null) {
			String retCode = json.get(MpConstant.JSON_CODE).get(MpConstant.JSON_CODE).asText();
			String bo = json.get(MpConstant.JSON_BO).toString();
			if (!RetCode.SUCCESS_CODE.equalsIgnoreCase(retCode)) {
				throw new Exception(bo);
			}
			workOrderList = JsonConvertUtil.jsonToBean(bo, List.class, PsWorkOrderDTO.class);
		}
		return workOrderList;
	}

	/**
	 * 调用流程管控
	 *
	 * @param dto
	 * @return
	 * @throws Exception
	 */
	public FlowControlInfoDTO checkFlow(AssemblyRelaScanDTO dto) throws Exception {
		FlowControlInfoDTO result = new FlowControlInfoDTO();
		FlowControlConditionDTO flow = new FlowControlConditionDTO();
		flow.setCurrProcessCode(dto.getProcessCode());
		flow.setWorkOrderNo(dto.getWorkOrderNo());
		flow.setWorkStation(dto.getWorkStationCode());
		flow.setLineCode(dto.getLineCode());
		flow.setSn(dto.getMainSn());
		flow.setFactoryId(dto.getFactoryId());
		if (dto.getItemCode().length() == MpConstant.NUM_12) {
			flow.setCraftSection(dto.getCraftSection());
			// 整机流程管控
			result = standardModeCommonScanService.checkWorkorderAndFlow(flow);
			return result;
		} else if (dto.getItemCode().length() == MpConstant.NUM_15) {
			// 单板流程管控
			result = psWipInfoService.checkFlowControled(flow);
			return result;
		}
		result.setResultType(Constant.FAIL);
		result.setErrorMessage(CommonUtils.getLmbMessage(MessageId.WORKORDER_SOURSYS_ERROR));
		return result;
	}

	/**
	 * 校验参数
	 *
	 * @param assemblyRelaScanDTO
	 * @return
	 */
	public RetCode checkParams(AssemblyRelaScanDTO assemblyRelaScanDTO) {
		// 指令不能为空
		if (StringHelper.isEmpty(assemblyRelaScanDTO.getItemCode())) {
			return new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.ITEM_CODE_EMPTY);
		}
		if (assemblyRelaScanDTO.getItemCode().length() == MpConstant.NUM_15 && StringHelper.isEmpty(assemblyRelaScanDTO.getWorkOrderNo())) {
			return new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.WORKORDER_NO_IS_NOT_NULL);
		}
		// 子工序不能为空
		if (assemblyRelaScanDTO.isSubSnScan() && StringHelper.isEmpty(assemblyRelaScanDTO.getProcessCode())) {
			return new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.PROCESS_IS_NOT_NULL);
		}
		// 主条码不能为空
		if (StringHelper.isEmpty(assemblyRelaScanDTO.getMainSn())) {
			return new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.PROCESS_IS_NOT_NULL);
		}
		// 扫描子条码或通过接口扫描时校验子条码不能为空
		if ((assemblyRelaScanDTO.isSubSnScan() || assemblyRelaScanDTO.isExternalScan()) && CollectionUtils.isEmpty(assemblyRelaScanDTO.getSubSnList())) {
			return new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.SUB_SN_IS_NOT_NULL);
		}
		// 过站时工站不能为空
		if (Constant.STR_NUMBER_ONE.equals(assemblyRelaScanDTO.getIsPassWorkStaion()) && assemblyRelaScanDTO.isSubSnScan()
				&& StringHelper.isEmpty(assemblyRelaScanDTO.getWorkStationCode()) && !assemblyRelaScanDTO.isExternalScan()) {
			return new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.WORK_STATION_IS_NOT_NULL);
		}
        if (assemblyRelaScanDTO.isExternalScan()) {
        	return checkForExternalScan(assemblyRelaScanDTO);
        }

		return new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID);
	}


	/**
	 * 接口扫描时参数校验
	 * @param dto
	 * @return
	 */
	public RetCode checkForExternalScan(AssemblyRelaScanDTO dto) {
		// 通过接口组装扫描时工站名不能为空
		if (Constant.STR_NUMBER_ONE.equals(dto.getIsPassWorkStaion())
				&& StringHelper.isEmpty(dto.getWorkStationName())) {
			return new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.WORK_STATION_IS_NOT_NULL);
		}
		// 通过接口组装扫描时子工序名不能为空
		if (StringHelper.isEmpty(dto.getProcessName())) {
			return new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.WORK_STATION_IS_NOT_NULL);
		}

		if (StringHelper.isEmpty(dto.getLineName())) {
			return new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.LINE_NAME_NOT_NULL);
		}
		return new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID);
	}

	/**
	 * 点对点调用获取物料信息
	 * 条码中心无记录的，将物料代码转材料代码查询
	 * @param dto
	 * @return
	 * @throws Exception
	 */
	public List<BaItem> getItemCode(AssemblyRelaScanDTO dto) throws Exception {
		List<SnValidateDto> dtoList = new ArrayList<SnValidateDto>();
		for (String sn : dto.getSubSnList()) {
			SnValidateDto validateDto = new SnValidateDto();
			validateDto.setSubSn(sn);
			if (dto.getItemCode().length() == MpConstant.NUM_15) {
				validateDto.setSubProdPlanId(sn.substring(0, 7));
				validateDto.setWorkOrderSource(Constant.SYS_TYPE_STEP);
			}
			if (dto.getItemCode().length() == MpConstant.NUM_12) {
				validateDto.setWorkOrderSource(Constant.SYS_TYPE_WMES);
			}
			dtoList.add(validateDto);
		}
		JsonNode json = DatawbRemoteService.getItemCode(dtoList);
		if(null == json){
			return null;
		}
		String retCode = json.get(MpConstant.JSON_CODE).get(MpConstant.JSON_CODE).asText();
		String bo = json.get(MpConstant.JSON_BO).toString();
		if (!RetCode.SUCCESS_CODE.equalsIgnoreCase(retCode)) {
			String msg = json.get(MpConstant.JSON_CODE).get(MpConstant.JSON_MSG).toString();
			String[] param = {msg};
			throw new MesBusinessException(retCode,MessageId.CUSTOMIZE_MSG,param);
		}
		return JsonConvertUtil.jsonToBean(bo, List.class, BaItem.class);
	}

	/**
	 * 查询1036环保属性数据字典
	 * @return
	 * @throws Exception
	 */
	private Map<String, SysLookupValuesDTO> getLeadList() throws Exception {
		//查询1036环保属性数据字典
		Map<String,Object> map = new HashMap<>(2);
		map.put("lookupType", Constant.ENV_LOOK_UP_TYPES);
		List<SysLookupValuesDTO> listSys  = BasicsettingRemoteService.getSysLookupValuesList(new SysLookupValuesDTO(){{setLookupType(new BigDecimal(Constant.ENV_LOOK_UP_TYPES));}});
		if(CollectionUtils.isEmpty(listSys)){
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,MessageId.SYS_LOOK_NOT_CONFIG,new Object[]{Constant.ENV_LOOK_UP_TYPES});
		}
		return listSys.stream().collect(Collectors.toMap(SysLookupValuesDTO::getDescriptionChin, a -> a, (k1, k2) -> k1));
	}

	/**
	 * 根据条码获取物料代码
	 * @param dto
	 * @return
	 * @throws Exception
	 */
	@Override
	public ServiceData getItemInfo(AssemblyRelaScanDTO dto) throws Exception{
		ServiceData ret = new ServiceData();
		AssemblyRelaScanDTO assemblyRelaScanDTO = new AssemblyRelaScanDTO();
		//新组装关系扫描优先从条码中心查询
		List<BaItem> baItemList=new ArrayList<>();
		List<String> relatedSnList=new ArrayList<>();
		List<String> leadSnList=new ArrayList<>();
		Map<String, SysLookupValuesDTO> leadMap =  getLeadList();
		dto.setMainSnWipInfo(psWipInfoRepository.getWipInfoBySn(dto.getMainSn()));
		//主条码环保属性
		SysLookupValuesDTO  lookupValuesDTOForMainSn = leadMap.get(dto.getMainSnWipInfo().getAttribute3());
		if(lookupValuesDTOForMainSn == null){
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,MessageId.ISLEAD_DATA_DICTIONARY_DOES_NOT_EXIST,new Object[]{dto.getMainSnWipInfo().getAttribute3()});
		}
		if(dto.isNewAssemblyRelaScan()){
			AssemblyRelaScanDTO tempDto=new AssemblyRelaScanDTO();
			tempDto.setSubSnList(dto.getSubSnList());
			List<BarcodeExpandDTO> barcodeExpandDTOList=this.getBarcodeExpandDTOMap(tempDto);
			if(!CollectionUtils.isEmpty(barcodeExpandDTOList)) {
				for (BarcodeExpandDTO barcodeExpandDTO : barcodeExpandDTOList) {
					//校验关联条码以及环保属性
					SysLookupValuesDTO sysLookupValuesDTO = veifyReleateSnAndHbattr(relatedSnList, leadSnList, leadMap, lookupValuesDTOForMainSn, barcodeExpandDTO);
					// 设置子条码环保属性
					assemblyRelaScanDTO.setHbCode(sysLookupValuesDTO.getAttribute1());
					BaItem baItem=new BaItem();
					baItem.setSn(barcodeExpandDTO.getBarcode());
					baItem.setItemName(barcodeExpandDTO.getItemName());
					baItem.setItemNo(barcodeExpandDTO.getItemCode());
					baItem.setBarCodeType(barcodeExpandDTO.getParentCategoryName());
					baItem.setQty(barcodeExpandDTO.getQuantity());
					baItemList.add(baItem);
				}
				veifyReleateSnAndHbattr(relatedSnList, leadSnList);
				dto.setHbAttrVerifyPassed(true);
			}
		}
		if(CollectionUtils.isEmpty(baItemList)) {
			baItemList = this.getItemCode(dto);
		}
		ret.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, RetCode.BUSINESSERROR_MSGID));
		ret = checkAttrAndTechnical(dto, assemblyRelaScanDTO);
		if (RetCode.BUSINESSERROR_CODE.equals(ret.getCode().getCode())) {
			return ret;
		}
		assemblyRelaScanDTO.setItemInfoList(baItemList);
		ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
		ret.setBo(assemblyRelaScanDTO);
		return ret;
	}

	/**
	 * 校验是否有关联条码以及环保属性校验
	 *
	 * @param relatedSnList 用于校验SSP物料，不允许扫描
	 * @param leadSnList   子条码的环保属性校验结果，存在则说明不通过
	 * @throws MesBusinessException
	 */
	private void veifyReleateSnAndHbattr(List<String> relatedSnList, List<String> leadSnList) throws MesBusinessException {
		//序列码存在关联条码的需要报错
		String msg = StringUtils.EMPTY;
		if (!CollectionUtils.isEmpty(relatedSnList)) {
			msg += CommonUtils.getLmbMessage(MessageId.SN_IS_ASSOCIATED_WITH_ZTE_SERIAL_CODE, StringUtils.join(relatedSnList, Constant.COMMA));
		}
		if (!CollectionUtils.isEmpty(leadSnList)) {
			msg += CommonUtils.getLmbMessage(MessageId.SUB_SN_HB_ATTR_VEIFY_NOT_PASSED, StringUtils.join(leadSnList, Constant.COMMA));
		}
		if (!StringUtils.isEmpty(msg)) {
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.BLINK, new Object[]{msg});
		}
	}

	/**
	 * 校验是否有关联条码以及环保属性校验
	 *
	 * @param relatedSnList
	 * @param leadSnList
	 * @param leadMap
	 * @param lookupValuesDTOForMainSn
	 * @param barcodeExpandDTO
	 * @return
	 * @throws MesBusinessException
	 */
	private SysLookupValuesDTO veifyReleateSnAndHbattr(List<String> relatedSnList, List<String> leadSnList, Map<String, SysLookupValuesDTO> leadMap, SysLookupValuesDTO lookupValuesDTOForMainSn, BarcodeExpandDTO barcodeExpandDTO) throws MesBusinessException {
		if (StringUtils.equals(Constant.TYPE_SEQUENCE_CODE, barcodeExpandDTO.getParentCategoryName()) &&
				(barcodeExpandDTO.getQuantity() == null || barcodeExpandDTO.getQuantity().intValue()== NumConstant.NUM_ZERO)
				&& StringUtils.isNotEmpty(barcodeExpandDTO.getRelatedSnBarcode())) {
			relatedSnList.add(barcodeExpandDTO.getBarcode());
		}
		//条码中心对应环保属性
		SysLookupValuesDTO sysLookupValuesDTO = leadMap.get(barcodeExpandDTO.getIsLead());
		if (sysLookupValuesDTO == null) {
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.ISLEAD_DATA_DICTIONARY_DOES_NOT_EXIST, new Object[]{barcodeExpandDTO.getIsLead()});
		}
		if(lookupValuesDTOForMainSn == null){
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,MessageId.ISLEAD_DATA_DICTIONARY_DOES_NOT_EXIST,new Object[]{Constant.MAIN_SN});
		}
		//环保属性校验
		if (new BigDecimal(sysLookupValuesDTO.getAttribute1()).compareTo(new BigDecimal(lookupValuesDTOForMainSn.getAttribute1())) < NumConstant.NUM_ZERO) {
			leadSnList.add(barcodeExpandDTO.getBarcode());
		}
		return sysLookupValuesDTO;
	}

	private boolean isCheck(String lookupType) throws Exception {
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("lookupType", lookupType);
		List<SysLookupTypesDTO> sysLookUpList = BasicsettingRemoteService.getSysLookUpValue(map);
		if (!CollectionUtils.isEmpty(sysLookUpList)
				&& Constant.FLAG_OFF.equals(sysLookUpList.get(NumConstant.NUM_ZERO).getLookupMeaning())) {
			return false;
		}
		return true;
	}

	private ServiceData checkAttrAndTechnical(AssemblyRelaScanDTO dto,AssemblyRelaScanDTO assemblyRelaScanDTO) throws Exception {
		ServiceData ret = new ServiceData();
		ret.setCode(new RetCode(RetCode.SUCCESS_CODE,RetCode.SUCCESS_MSGID));
		if (dto.getItemCode().length() != MpConstant.NUM_12) {
			return ret;
		}
		if (isCheckHbAttr(dto)) {
			 PsWipInfo wipInfo = dto.getMainSnWipInfo() == null?psWipInfoRepository.getWipInfoBySn(dto.getMainSn()):dto.getMainSnWipInfo();
			 // 校验环保属性是否一致
			 TechnicalChangeBarcodeDTO technicalChangeBarcodeDTO = this.getBarAttr(wipInfo,dto);
			 if (null == technicalChangeBarcodeDTO || BusinessConstant.N.equals(technicalChangeBarcodeDTO.getIsChecked())) {
				String[] params = new String[]{wipInfo.getAttribute2()};
				ret.setCode(new com.zte.springbootframe.common.model.RetCode(RetCode.BUSINESSERROR_CODE, MessageId.SN_BARATTR_INCONSISTENT_TASKNO, params));
				return ret;
			 }
			// 返回子条码环保属性
			assemblyRelaScanDTO.setHbCode(technicalChangeBarcodeDTO.getSnBarAttr());
		}
		if (isCheck(Constant.SYS_LOOK_CHECK_TECHINAL)) {
			// 校验子条码是否有技改
			String checkTechnicalReformMsg = checkTechnicalReform(dto);
			if (StringUtils.isNotEmpty(checkTechnicalReformMsg)) {
				RetCode retCode = new RetCode(RetCode.BUSINESSERROR_CODE,RetCode.BUSINESSERROR_MSGID);
				retCode.setMsg(checkTechnicalReformMsg);
				ret.setCode(retCode);
			}
		}
		return ret;
	}

	/**
	 * 是否校验环保属性：开关+是否已校验
	 * @param dto
	 * @return
	 * @throws Exception
	 */
	public boolean isCheckHbAttr(AssemblyRelaScanDTO dto) throws Exception {
		return isCheck(Constant.SYS_LOOK_CHECK_ATTR) && !dto.isHbAttrVerifyPassed();
	}

	/**
     * 校验条码是否技改
     */
    public String checkTechnicalReform(AssemblyRelaScanDTO dto) throws Exception {
        String resultMsg = "";
        List<BSProcess> processList = getProcessInfoByCode(dto);
        if (CollectionUtils.isEmpty(processList)) {
        	return CommonUtils.getLmbMessage(MessageId.PROCESS_DETAILS_NOT_FOUND);
        }

        String craftSection = processList.get(NumConstant.NUM_ZERO).getCraftSection();
        // 获取条码在绑定关系配置表中，所有的子条码
        List<String> snList = new ArrayList<>();
        snList.addAll(dto.getSubSnList());
        for (String sn : dto.getSubSnList()) {
        	 List<WipExtendIdentification> bindingList = wipExtendIdentificationRepository.getAllChildProdBinding(sn);
             if (!CollectionUtils.isEmpty(bindingList)) {
                 snList.addAll(bindingList.stream().map(i -> i.getSn()).collect(Collectors.toList()));
             }
        }
        // 校验条码及所有子条码是否技改
        String technicalChangeBarcode = standardModeCommonScanService.getTechnicalChangeBarcode(snList,craftSection);
        if (StringUtils.isNotEmpty(technicalChangeBarcode)) {
            resultMsg = CommonUtils.getLmbMessage(MessageId.SN_TECHNICAL_CHANGE, technicalChangeBarcode);
        }
        return resultMsg;
    }



    private List<BSProcess> getProcessInfoByCode(AssemblyRelaScanDTO dto) throws Exception {
    	List<BSProcess> returnList = new ArrayList<>();
    	Map<String, Object> map = new HashMap<String, Object>();
 		map.put("processCode", dto.getProcessCode());
 		JsonNode json = CrafttechRemoteService.getProcessInfo(map);
 		if(json != null) {
 			String retCode = json.get(MpConstant.JSON_CODE).get(MpConstant.JSON_CODE).asText();
 			String bo = json.get(MpConstant.JSON_BO).toString();
 			if (!RetCode.SUCCESS_CODE.equalsIgnoreCase(retCode)) {
 				throw new Exception(bo);
 			}
 			returnList = JsonConvertUtil.jsonToBean(bo, List.class, BSProcess.class);
 		}
 		return returnList;
    }

	/**
	 * 获取环保属性
	 */
	public TechnicalChangeBarcodeDTO getBarAttr(PsWipInfo wipInfo,AssemblyRelaScanDTO dto) throws Exception {
		Map<String, String> paramsMap = new HashMap<>();
		paramsMap.put(BusinessConstant.ENTITY_BAR_ATTR, wipInfo.getAttribute3());// 主条码环保属性
		paramsMap.put(BusinessConstant.BARCODE, wipInfo.getSn());
		List<TechnicalChangeBarcodeDTO> technicalDtoList = new ArrayList<TechnicalChangeBarcodeDTO>();
		for (String subSn : dto.getSubSnList()) {
			TechnicalChangeBarcodeDTO technicalDto = new TechnicalChangeBarcodeDTO();
			technicalDto.setBarcode(subSn);
			technicalDto.setEntityBarAttr(wipInfo.getAttribute3());
			technicalDtoList.add(technicalDto);
		}

//		// 通过工厂ID获取组织ID
//		Map<String, Object> map = new HashMap<>();
//		map.put(BusinessConstant.FACTORY_ID, wipInfo.getFactoryId().toString());
//		CFFactory cfFactory = BasicsettingRemoteService.getFactory(map).get(NumConstant.NUM_ZERO);
//		paramsMap.put(BusinessConstant.ORGANIZATION_ID, cfFactory.getOrganizationId().toString());
		logger.info("远程调用同步的入参 :{}" , technicalDtoList);
		Map<String,String> headParams = MESHttpHelper.getHttpRequestHeader();
		String url = constantInterface.getUrl(InterfaceEnum.dataWbSysBarAttr);
		logger.info("远程调用同步的URL :{}" , url);
		String msg = HttpClientUtil.httpPostWithJSON(url, JacksonJsonConverUtil.beanToJson(technicalDtoList), headParams);
		//String msg = HttpRemoteService.remoteExe(InterfaceEnum.dataWbSysBarAttr,paramsMap,headParams,url);
		if (StringUtils.isBlank(msg)) {
			throw new Exception(CommonUtils.getLmbMessage(MessageId.SELECT_ERROR));
		}
		JsonNode json = JacksonJsonConverUtil.getMapperInstance().readTree(msg);
		logger.info("远程调用同步的结果 :{}" , json);
		String retCode = json.get(MpConstant.JSON_CODE).get(MpConstant.JSON_CODE).asText();
		String bo = json.get(MpConstant.JSON_BO).toString();
		if(!RetCode.SUCCESS_CODE.equalsIgnoreCase(retCode)){
			throw new Exception(bo);
		}
		return JsonConvertUtil.jsonToBean(bo, TechnicalChangeBarcodeDTO.class);
	}

	/**
	 * 回写
	 *
	 * @param dto
	 * @return
	 * @throws Exception
	 */
	public RetCode wirteBack(AssemblyRelaScanDTO dto) throws Exception {
		BsItemInfo itemInfo = getImesItemInfo(dto);
		if (null == itemInfo) {
			return new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.CAN_NOT_FOUND_MAINSN_ITEM_INFO);
		}
		AssemblyWriteBackDto writeDto = new AssemblyWriteBackDto();
		WsmAssembleHeaders mainSn = new WsmAssembleHeaders();
		mainSn.setItemBarcode(dto.getMainSn());
		mainSn.setItemCode(itemInfo.getItemNo());
		mainSn.setItemName(itemInfo.getItemName());
		mainSn.setItemVersion(itemInfo.getVersion());
		mainSn.setItemId(itemInfo.getSourceItemId());
		mainSn.setCreatedBy(new BigDecimal(dto.getCreateBy()));
		mainSn.setSiteId(new BigDecimal(Constant.INT_0));
		mainSn.setScanType(Constant.SEQUENCE_CODE);
		mainSn.setItemQty(new BigDecimal(Constant.INT_1));
		writeDto.setMainSn(mainSn);
		writeDto.setSubSnList(setSubSnForWriteBack(dto));
		if (null == dto.getMainWorkOrder()) {
			List<PsTask> taskList = ObtainRemoteServiceDataUtil.getPsTaskInfo("", dto.getWipInfo().getAttribute2());
			mainSn.setProdBigcategory(taskList.get(NumConstant.NUM_ZERO).getExternalType());
		} else {
			mainSn.setProdBigcategory(dto.getMainWorkOrder().getExternalType());
		}
		JsonNode json = DatawbRemoteService.assembleWriteBack(writeDto);
		if(null == json) {
			return new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.CAN_NOT_FOUND_MAINSN_ITEM_INFO);
		}
		String retCode = json.get(MpConstant.JSON_CODE).get(MpConstant.JSON_CODE).asText();
		if (!RetCode.SUCCESS_CODE.equalsIgnoreCase(retCode)) {
			throw new Exception(json.toString());
		}
		return new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID);

	}

	/**
	 * 回写开关
	 *
	 * @return
	 * @throws Exception
	 */
	public boolean isWriteBack() throws Exception {
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("lookupType", Constant.ASSEMBLY_WRITE_BACK);
		List<SysLookupTypesDTO> sysLookUpList = BasicsettingRemoteService.getSysLookUpValue(map);
		if (!CollectionUtils.isEmpty(sysLookUpList)
				&& Constant.FLAG_ON.equals(sysLookUpList.get(NumConstant.NUM_ZERO).getLookupMeaning())) {
			return true;
		}
		return false;
	}

	/**
	 * 设置子条码回写信息
	 *
	 * @param dto
	 * @return
	 */
	public List<WsmAssembleLinesWriteBack> setSubSnForWriteBack(AssemblyRelaScanDTO dto) {
		List<WsmAssembleLinesWriteBack> subSnList = new ArrayList<WsmAssembleLinesWriteBack>();
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("processCode", dto.getProcessCode());
		map.put("formSn", dto.getMainSn());
		List<WipExtendIdentification> extendList = wipExtendIdentificationRepository.getList(map);
		if (CollectionUtils.isEmpty(extendList)) {
			return subSnList;
		}
		for (WipExtendIdentification wipExtend : extendList) {
			WsmAssembleLinesWriteBack subSn = new WsmAssembleLinesWriteBack();
			subSn.setItemBarcode(wipExtend.getSn());
			subSn.setItemCode(wipExtend.getItemNo());
			subSn.setItemName(wipExtend.getAttribute1());
			subSn.setItemVersion(wipExtend.getAttribute2());
			subSn.setItemId(new BigDecimal(wipExtend.getAttribute3()));
			subSn.setScanType(wipExtend.getAttribute4());
			subSn.setCreatedBy(new BigDecimal(dto.getCreateBy()));
			subSn.setItemQty(new BigDecimal(Constant.INT_1));
			subSnList.add(subSn);
		}
		return subSnList;
	}

	/**
	 * 点对点调用中心工厂服务查询物料信息
	 *
	 * @param dto
	 * @return
	 * @throws Exception
	 */
	public BsItemInfo getImesItemInfo(AssemblyRelaScanDTO dto) throws Exception {
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("itemNo", dto.getItemCode());
		List<BsItemInfo> itemList = BasicsettingRemoteService.getItemInfo(dto.getItemCode());
		return itemList.get(NumConstant.NUM_ZERO);
	}

	/**
	 * 获取主条码回写记录
	 *
	 * @param dto
	 * @return
	 * @throws Exception
	 */
	public WsmAssembleHeaders getByItemBarCode(AssemblyRelaScanDTO dto) throws Exception {
		WsmAssembleHeaders queryCond = new WsmAssembleHeaders();
		queryCond.setItemBarcode(dto.getMainSn());
		JsonNode json = DatawbRemoteService.getByItemBarCode(queryCond);
		if(null == json) {
			return queryCond;
		}
		String retCode = json.get(MpConstant.JSON_CODE).get(MpConstant.JSON_CODE).asText();
		String bo = json.get(MpConstant.JSON_BO).toString();
		if (!RetCode.SUCCESS_CODE.equalsIgnoreCase(retCode)) {
			throw new Exception(bo);
		}
		return JsonConvertUtil.jsonToBean(bo, WsmAssembleHeaders.class);
	}

	@Override
	public RetCode onlyPassWorkStation (AssemblyRelaScanDTO dto) throws Exception {
		String resultMsg = "";
		RetCode	ret = getworkOrderBySnScan(dto);
		if(Constant.BUS_ERROR.equals(ret.getCode())) {
			return ret;
		}
		// 调用流程管控校验
		FlowControlInfoDTO checkFlowRsult = checkFlow(dto);
		if (Constant.FAIL.equals(checkFlowRsult.getResultType())) {
			RetCode retCode = new RetCode(RetCode.BUSINESSERROR_CODE, RetCode.BUSINESSERROR_MSGID);
			retCode.setMsg(checkFlowRsult.getErrorMessage());
			return retCode;
		}

		if (null != dto.getMainWorkOrder()) {
			PsEntityPlanBasicDTO workOrderDto = new PsEntityPlanBasicDTO();
			BeanUtils.copyProperties(dto.getMainWorkOrder(), workOrderDto);
			checkFlowRsult.setEntityPlanBasic(workOrderDto);
		}

		// 指令来源为WMES调用整机在制保存，指令来源为STEP调用单板在制保存
		checkFlowRsult.setLastUpdatedBy(dto.getCreateBy());
		if (dto.getItemCode().length() == MpConstant.NUM_12) {
			resultMsg = standardModeCommonScanService.saveWipInfo(checkFlowRsult,dto.getProcessCode());
		} else if (dto.getItemCode().length() == MpConstant.NUM_15) {
			resultMsg = psWipInfoService.pmScanWipSave(checkFlowRsult);
		}
		if (!StringHelper.isEmpty(resultMsg)) {
			RetCode retCode = new RetCode(RetCode.BUSINESSERROR_CODE, RetCode.BUSINESSERROR_MSGID);
			retCode.setMsg(resultMsg);
			return retCode;
		}
		ret = new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID);
		ret.setMsg(Constant.BINDED_AND_PASSWORKSTATION_COMPLETE);
		return ret;

	}

	/**
	 * 不影响旧接口使用
	 * 增加支持替代物料，对接条码中心
	 * 2021-11-15
	 * @param dto
	 * @return
	 * @throws Exception
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public RetCode assemblyRelaScanNew(AssemblyRelaScanDTO dto) throws Exception {
		//设置是新绑定扫描
		dto.setNewAssemblyRelaScan(true);
		// 校验参数
		RetCode checkParamsResult = checkParams(dto);
		if (!RetCode.SUCCESS_CODE.equals(checkParamsResult.getCode())) {
			return checkParamsResult;
		}
		if (dto.isMainSnScan()) {
			Pair<RetCode, FlowControlInfoDTO> result = dealMainSn(dto);
			return result.getFirst();
		} else if (dto.isSubSnScan()) {
			FlowControlInfoDTO fcInfo = new FlowControlInfoDTO();
			return subSnScanNew(dto, fcInfo);
		}
		return new RetCode();

	}

	/**
	 * 子条码扫描-新
	 *
	 * @param dto
	 * @return
	 * @throws Exception
	 *
	 */
	public RetCode subSnScanNew(AssemblyRelaScanDTO dto, FlowControlInfoDTO checkFlowRsult) throws Exception {
		RetCode ret = new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID);
		PsWipInfo wipInfo = psWipInfoRepository.getWipInfoBySn(dto.getMainSn());
		dto.setWipInfo(wipInfo);
		ret = getworkOrderBySnScan(dto);
		if(Constant.BUS_ERROR.equals(ret.getCode())) {
			return ret;
		}
		// 界面扫描子条码是才需要再次调用流程管控或者已全部绑定完成（包含中试条码）需要过站时
		if ((dto.isSubSnScan() && Constant.STR_NUMBER_ONE.equals(dto.getIsPassWorkStaion()))
				|| dto.isToPassWorkStaion()) {
			// 调用流程管控校验
			checkFlowRsult = checkFlow(dto);
			if (Constant.FAIL.equals(checkFlowRsult.getResultType())) {
				RetCode retCode = new RetCode(RetCode.BUSINESSERROR_CODE, RetCode.BUSINESSERROR_MSGID);
				retCode.setMsg(checkFlowRsult.getErrorMessage());
				return retCode;
			}
		}

		List<BaItem> itemInfoList = new ArrayList<BaItem>();
		List<ProdBindingSettingDTO> bindList = new ArrayList<ProdBindingSettingDTO>();
		//不存在条码按原逻辑查物流代码
		ret = dealItemListNew(dto,itemInfoList, bindList);
		dto.setProdBindingSettingDTOS(bindList);
		dto.setItemInfoParamList(itemInfoList);
		return getRetCode(null,dto, checkFlowRsult, ret);
	}

	/**
	 * 校验子条码
	 * @param dto
	 * @param itemInfoList
	 * @param bindList
	 * @return
	 * @throws Exception
	 */
	public RetCode dealItemListNew(AssemblyRelaScanDTO dto,List<BaItem> itemInfoList,List<ProdBindingSettingDTO> bindList) throws Exception{
		RetCode ret = new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID);
		if (dto.isNotAutoPassWorkStaion()) {
			//包含中试条码则直接获取前台传过来的物料信息
			itemInfoList.addAll(dto.getItemInfoList());
			return ret;
		}
		//自动过站（不包含中试条码）保留原有逻辑
		// 根据子条码获取子物料信息（前端传入）
		List<String> subSnList=dto.getSubSnList();
		//调条码中心查询条码信息
		List<BarcodeExpandDTO> barcodeExpandDTOList=getBarcodeExpandDTOMap(dto);
		List<String> relatedSnList=new ArrayList<>();
		List<String> leadSnList=new ArrayList<>();
		Map<String, SysLookupValuesDTO> leadMap =  getLeadList();
		dto.setMainSnWipInfo(psWipInfoRepository.getWipInfoBySn(dto.getMainSn()));
		//主条码环保属性
		SysLookupValuesDTO  lookupValuesDTOForMainSn = leadMap.get(dto.getMainSnWipInfo().getAttribute3());
		if(CollectionUtils.isEmpty(barcodeExpandDTOList)){
			List<BaItem> item = getItemCode(dto);
			if(!CollectionUtils.isEmpty(item)){
				itemInfoList.addAll(item);
			}
			//不是条码中心查询到的条码数据需要写日志
			insertAssemblyRelaScanRecordInfo(dto, item);
		}else{
			BarcodeExpandDTO barcodeExpandDTO=barcodeExpandDTOList.get(NumConstant.NUM_ZERO);
			BaItem baItem=new BaItem();
			baItem.setFromBarCodeCenter(true);
			baItem.setSn(barcodeExpandDTO.getBarcode());
			baItem.setItemNo(barcodeExpandDTO.getItemCode());
			baItem.setItemName(barcodeExpandDTO.getItemName());
			//条码大类
			baItem.setBarCodeType(barcodeExpandDTO.getParentCategoryName());
			itemInfoList.add(baItem);
			//校验关联条码以及环保属性
			for (BarcodeExpandDTO barcodeExpandTempDTO : barcodeExpandDTOList) {
				veifyReleateSnAndHbattr(relatedSnList, leadSnList, leadMap, lookupValuesDTOForMainSn, barcodeExpandTempDTO);
			}
			veifyReleateSnAndHbattr(relatedSnList, leadSnList);
		}
        // 未找到物料信息
		if (CollectionUtils.isEmpty(itemInfoList)) {
			return new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.FAILED_TO_GET_SN_INFO);
		}
		if (StringUtils.isNotEmpty(itemInfoList.get(NumConstant.NUM_ZERO).getErrMsg())) {
			String[] params = new String[] { itemInfoList.get(NumConstant.NUM_ZERO).getErrMsg() };
			return new com.zte.springbootframe.common.model.RetCode(RetCode.BUSINESSERROR_CODE, MessageId.ITEM_INFO_NOT_FOUND, params);
		}

		// 主物料代码
		ProdBindingSettingDTO queryCode = new ProdBindingSettingDTO();
		queryCode.setProductCode(dto.getItemCode());
		queryCode.setMainSn(dto.getMainSn());
		queryCode.setFactoryId(dto.getFactoryId());
		queryCode.setProcessCode(dto.getProcessCode());
		// 根据主物料代码获取绑定关系
		bindList.addAll(prodBindingSettingRepository.getBindingInfoByItemNew(queryCode));
		//校验子条码用量
		ret = validateItemInfoNew(itemInfoList, bindList, dto);
		return ret;
	}

	/**
	 * 校验子条码物料是否存在绑定关系，或者在物料清单中，或者是替代物料，以及绑定数量是否大于需求数量
	 *
	 * @param itemInfoAllList
	 * @param bindList
	 * @return
	 */
	public RetCode validateItemInfoNew(List<BaItem> itemInfoAllList, List<ProdBindingSettingDTO> bindList, AssemblyRelaScanDTO dto) throws Exception{
		RetCode ret = new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID);
		BaItem baItem=itemInfoAllList.get(NumConstant.NUM_ZERO);
		String barCodeType = baItem.getBarCodeType();
		// 校验子条码是否已经绑定在主条码上，分序列码和批次码情况。
		ret = getRetCode(dto, baItem, barCodeType);
		if (!RetCode.SUCCESS_CODE.equals(ret.getCode())) {
			return ret;
		}
		PsWorkOrderDTO psWorkOrderDTO=dto.getMainWorkOrder();
		//erp任务数量
		BigDecimal taskQty = getErpTaskQty(psWorkOrderDTO);
		dto.setTaskQty(taskQty);
		//绑定清单
		Map<String, ProdBindingSettingDTO> prodBindingSettingDTOMap = CollectionUtils.isEmpty(bindList)?new HashMap<>():bindList.stream().collect(Collectors.toMap(ProdBindingSettingDTO::getItemCode, a -> a, (k1, k2) -> k1));
		//序列码 数量为空默认1
		if(StringUtils.equals(Constant.TYPE_SEQUENCE_CODE,barCodeType) && dto.getSubSnQty() == null){
			dto.setSubSnQty(BigDecimal.ONE);
		}
		//将前端填写数量写入BaItem
		baItem.setQty(dto.getSubSnQty());
		String itemNo = baItem.getItemNo();
		BigDecimal qty = baItem.getQty();
		ProdBindingSettingDTO prodBindingSettingDTO=prodBindingSettingDTOMap.get(itemNo);
		//批次码时，如果没有数量，默认为用量
		//a、子条码所属物料是否在绑定清单内，在则校验通过
		//用量
		BigDecimal usageCount = null;
		if(null !=prodBindingSettingDTO){
			//用量
			usageCount=prodBindingSettingDTO.getUsageCount();
			//已绑定数量
			dto.setBindedCount(getBindedCount(dto, itemNo));
		}

		//b、条码所属物料代码是否是替代物料，先根据物料代码+任务组织ID获取物料id，再根据物料id查询提单关系表是否有数据（mtl_related_items
		// inventory_item_id被替换物料id、related_item_id 替换物料id、 relationship_type_id=2、   reciprocal_flag Y相互替代，N单向替代），
		// 如果有数据再判断被替代物料是否在绑定清单，在则校验通过
		//获取替代物料
		if(usageCount == null) {
			usageCount = getUsageCount(baItem, prodBindingSettingDTOMap, itemNo, dto);
		}
		//物料需求清单
		Map<String, ItemListEntityDTO> itemListEntityDTOHashMap = new HashMap<>();
		//c、条码所属物料代码是否在任务所需物料清单中（任务所需物料清单获取方式参考：物料清单查询），在则校验通过
		if(usageCount == null) {
			initItemMap(dto, itemListEntityDTOHashMap);
			usageCount = getUsageCount(itemNo, dto,itemListEntityDTOHashMap);
		}
        //最终没获取到用量 报错
		if(usageCount == null) {
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,MessageId.FAILED_TO_OBTAIN_THE_STANDARD_QUANTITY_OF_MATERIAL_CODE,new Object[]{itemNo});
		}
		if(StringUtils.equals(Constant.TYPE_BATCH_CODE,barCodeType) && qty == null){
			qty = usageCount;
			baItem.setQty(qty);
		}
		dto.setSubSnQty(qty);
		//现有绑定数量
		setBindedCount(dto, baItem, itemNo);
		checkUsageCount(baItem,barCodeType, qty, dto.getBindedCount(), usageCount);
		//校验通过,如果物料代码或者替代物料在绑定清单，将已绑定数量设置为 已绑定数+待绑定数,方便后面计算该子工序是否都绑定完成
		for(ProdBindingSettingDTO p:bindList){
			if(StringUtils.equals(p.getItemCode(),itemNo) || StringUtils.equals(p.getItemCode(),baItem.getReplaceItemNo())){
                 p.setBindedCount(p.getBindedCount().add(qty));
			}
		}
		return ret;
	}

	/**
	 * 设置已绑定数量
	 * @param dto
	 * @param baItem
	 * @param itemNo
	 */
	private void setBindedCount(AssemblyRelaScanDTO dto, BaItem baItem, String itemNo) {
		if(StringUtils.isNotEmpty(baItem.getReplaceItemNo())){
			//已绑定数量
			dto.setBindedCount(getBindedCount(dto, baItem.getReplaceItemNo()));
		}else{
			//已绑定数量
			dto.setBindedCount(getBindedCount(dto, itemNo));
		}
	}

	/**
	 * 获取绑定数量
	 * @param dto
	 * @param itemNo
	 * @return
	 */
	private BigDecimal getBindedCount(AssemblyRelaScanDTO dto, String itemNo) {
		WipExtendIdentificationDTO wipExtendIdentificationDTO=new WipExtendIdentificationDTO();
		wipExtendIdentificationDTO.setFormSn(dto.getMainSn());
		wipExtendIdentificationDTO.setProcessCode(dto.getProcessCode());
		wipExtendIdentificationDTO.setItemNo(itemNo);
		Integer formQty = wipExtendIdentificationRepository.getUsageCount(wipExtendIdentificationDTO);
		return formQty == null ? BigDecimal.ZERO : new BigDecimal(formQty);
	}

	/**
	 * 获取erp任务数量
	 * @param psWorkOrderDTO
	 * @return
	 * @throws Exception
	 */
	private BigDecimal getErpTaskQty(PsWorkOrderDTO psWorkOrderDTO) throws Exception {
		if(psWorkOrderDTO !=null && StringUtils.isNotEmpty(psWorkOrderDTO.getTaskNo())) {
			List<WipEntitiesDTO> wipEntitiesDTOList = DatawbRemoteService.selectMpsNetQty(psWorkOrderDTO.getTaskNo());
			if(CollectionUtils.isEmpty(wipEntitiesDTOList)){
				throw  new MesBusinessException(RetCode.BUSINESSERROR_CODE,MessageId.FAILED_TO_GET_ERP_TASK_QTY);
			}
			return wipEntitiesDTOList.get(NumConstant.NUM_ZERO).getStartQuantity();
		}
		return null;
	}

	/**
	 * 校验用量
	 * @param baItem
	 * @param qty
	 * @param bindedCount
	 * @param usageCount
	 * @throws MesBusinessException
	 */
	private void checkUsageCount(BaItem baItem,String barCodeType, BigDecimal qty, BigDecimal bindedCount, BigDecimal usageCount) throws MesBusinessException {
		if(usageCount == null) {
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,MessageId.FAILED_TO_GET_MATERIAL_STANDARD_USAGE);
		}
		//序列码时用量为小数，报错
		if (StringUtils.equals(Constant.TYPE_SEQUENCE_CODE, barCodeType) && new BigDecimal(usageCount.intValue()).compareTo(usageCount)!=NumConstant.NUM_ZERO){
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,MessageId.USE_QTY_CAN_NOT_BE_DECIMAL);
		}
		//序列码数量大于1不允许批量扫描
		if(StringUtils.equals(Constant.TYPE_SEQUENCE_CODE, barCodeType) && qty.compareTo(BigDecimal.ONE)>NumConstant.NUM_ZERO){
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,MessageId.SERIAL_CODE_DOES_NOT_ALLOW_BATCH_SCANNING);
		}
		String itemCode = StringUtils.isEmpty(baItem.getReplaceItemNo())?baItem.getItemNo():baItem.getItemNo()+MpConstant.REPLACE_ITEM+baItem.getReplaceItemNo();
		//如果用量<=已绑定数量
		if((usageCount.compareTo(bindedCount))<=NumConstant.NUM_ZERO){
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,MessageId.ITEM_CODE_HAS_BEEN_BOUND,new Object[]{itemCode});
		}
		//如果用量<已绑定数量+待绑定数量
		if((usageCount.subtract(bindedCount).compareTo(qty))<NumConstant.NUM_ZERO){
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,MessageId.INSUFFICIENT_QUANTITY_TO_BE_BOUND,new Object[]{itemCode});
		}
	}
	//校验子条码绑定情况
	public RetCode getRetCode(AssemblyRelaScanDTO dto,BaItem baItem, String barCodeType) {
		RetCode ret = new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID);
		//序列码校验子条码是否已有绑定记录
		if(StringUtils.equals(Constant.TYPE_SEQUENCE_CODE, barCodeType)){
			ret = validateSn(dto);
		}
		String sn = baItem.getSn();
		//允许同一批次码绑定在不同主条码上，但不允许重复绑在同一主条码
		if(StringUtils.equals(Constant.TYPE_BATCH_CODE, barCodeType)){
			WipExtendIdentificationDTO tempWipDto=new WipExtendIdentificationDTO();
			tempWipDto.setFormSn(dto.getMainSn());
			tempWipDto.setSn(sn);
			List<WipExtendIdentification> wipExtendIdentificationList=wipExtendIdentificationRepository.getWipExtendIdentificationBySn(tempWipDto);
			if(!CollectionUtils.isEmpty(wipExtendIdentificationList)){
				ret = new RetCode(RetCode.BUSINESSERROR_CODE,MessageId.SAME_BATCH_CODE_CANNOT_BE_BOUND_TO_REPEATEDLY_SN);
			}
		}
		return ret;
	}

	/**
	 * 获取用量
	 * @param itemNo
	 * @param dto
	 * @param itemListEntityDTOHashMap
	 * @return
	 */
	public BigDecimal getUsageCount(String itemNo, AssemblyRelaScanDTO dto, Map<String, ItemListEntityDTO> itemListEntityDTOHashMap)throws Exception {
		BigDecimal usageCount = null;
		//任务数量
		PsWorkOrderDTO psWorkOrderDTO=dto.getMainWorkOrder();
		checkTaskInfo(psWorkOrderDTO);
		BigDecimal taskQty = this.getTaskQty(dto);
		ItemListEntityDTO itemListEntityDTO = itemListEntityDTOHashMap.get(itemNo);
		if (itemListEntityDTO != null) {
			//需求数量
			String quantity = itemListEntityDTO.getRequiredQuantity();
			//3位小数
			usageCount = new BigDecimal(quantity).divide(taskQty, NumConstant.NUM_THREE, BigDecimal.ROUND_HALF_DOWN);
			//计算任务需求清单用量记录日志
			List<AssemblyRelaScanRecordInfoEntityDTO> assemblyRelaScanRecordInfoEntityDTOList=new ArrayList<>();
			AssemblyRelaScanRecordInfoEntityDTO assemblyRelaScanRecordInfoEntityDTO = new AssemblyRelaScanRecordInfoEntityDTO();
			assemblyRelaScanRecordInfoEntityDTO.setRemark(Constant.REMARK_QTY);
			assemblyRelaScanRecordInfoEntityDTO.setTaskNo(psWorkOrderDTO.getTaskNo());
			assemblyRelaScanRecordInfoEntityDTO.setItemCode(itemNo);
			assemblyRelaScanRecordInfoEntityDTO.setRequirementQty(new BigDecimal(quantity));
			assemblyRelaScanRecordInfoEntityDTO.setAttribute1(dto.getMainSn());
			assemblyRelaScanRecordInfoEntityDTO.setTaskQty(taskQty);
			assemblyRelaScanRecordInfoEntityDTO.setCreateBy(dto.getCreateBy());
			assemblyRelaScanRecordInfoEntityDTO.setLastUpdatedBy(dto.getCreateBy());
			assemblyRelaScanRecordInfoEntityDTO.setFactoryId(dto.getFactoryId());
			assemblyRelaScanRecordInfoEntityDTOList.add(assemblyRelaScanRecordInfoEntityDTO);
			assemblyRelaScanRecordInfoService.batchInsert(assemblyRelaScanRecordInfoEntityDTOList);
		}
		return usageCount;
	}

	public BigDecimal getTaskQty(AssemblyRelaScanDTO dto) {
		BigDecimal taskQty= dto.getTaskQty();
		if(taskQty == null){
			taskQty = BigDecimal.ZERO;
		}
		return taskQty;
	}

	/**
	 * 获取用量
	 * @param baItem
	 * @param prodBindingSettingDTOMap
	 * @param itemNo
	 * @param dto
	 * @return
	 * @throws Exception
	 */
	private BigDecimal getUsageCount(BaItem baItem, Map<String, ProdBindingSettingDTO> prodBindingSettingDTOMap, String itemNo, AssemblyRelaScanDTO dto) throws Exception {
		//任务数量
		PsWorkOrderDTO psWorkOrderDTO=dto.getMainWorkOrder();
		checkTaskInfo(psWorkOrderDTO);
		BigDecimal taskQty=dto.getTaskQty();
		BigDecimal usageCount;
		//获取替代物料
		List<MtlRelatedItemsEntityDTO> mtlRelatedItemsEntityDTOList = datawbRemoteService.getItemInfoList(baItem.getItemNo());
		Set<String> replaceItemCodeSet = getReplaceItemCodeSet(itemNo, mtlRelatedItemsEntityDTOList);
		int prodCount = NumConstant.NUM_ZERO;
		BigDecimal prodUsageCount = null;

		for (String repacleItemCode : replaceItemCodeSet) {
			//先取绑定数量
			ProdBindingSettingDTO tempProdDto = prodBindingSettingDTOMap.get(repacleItemCode);
			if (tempProdDto != null) {
				prodUsageCount = tempProdDto.getUsageCount();
				prodCount++;
				baItem.setReplaceItemNo(repacleItemCode);
			}
		}
		BigDecimal quantity = null;
		//绑定清单存在1个以上报错，或者绑定清单不存在，需求清单存在多个，报错
		if(prodCount>NumConstant.NUM_ONE){
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.ITEM_CODE_EXIST_IN_LIST_MORE_THAN_ONE, new Object[]{itemNo});
		}
		return prodUsageCount;
	}

	private void checkTaskInfo(PsWorkOrderDTO psWorkOrderDTO) throws MesBusinessException {
		if(psWorkOrderDTO == null){
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.FAILED_TO_GET_TASK_INFO);
		}
	}

	private Set<String> getReplaceItemCodeSet(String itemNo, List<MtlRelatedItemsEntityDTO> mtlRelatedItemsEntityDTOList) throws MesBusinessException {
		Set<String> replaceItemCodeSet;
		if (!CollectionUtils.isEmpty(mtlRelatedItemsEntityDTOList)) {
			replaceItemCodeSet = mtlRelatedItemsEntityDTOList.stream().map(e -> e.getReplaceItemCode()).collect(Collectors.toSet());
		}else{
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.FAILED_TO_GET_MATERIAL_STANDARD_USAGE, new Object[]{itemNo});
		}
		return replaceItemCodeSet;
	}


	/**
	 * 初始化物料需求清单
	 * @param dto
	 * @param itemListEntityDTOHashMap
	 * @throws Exception
	 */
	private void initItemMap(AssemblyRelaScanDTO dto, Map<String, ItemListEntityDTO> itemListEntityDTOHashMap) throws Exception {
		PsWipInfo wipInfo = dto.getWipInfo();
		if(wipInfo == null) {
			return;
		}
		String taskNo;
		// 返工任务需取原任务号
		if (StringUtils.isNotEmpty(wipInfo.getOriginalTask())) {
			taskNo = wipInfo.getOriginalTask();
		} else {
			taskNo = wipInfo.getAttribute2();
		}
		if (StringUtils.isEmpty(taskNo)) {
			return;
		}
		// 从erp获取任务清单
		List<ItemListEntityDTO> itemListEntityDTOList = datawbRemoteService.getErpItemListByTaskNo(taskNo);
		for(ItemListEntityDTO itemListEntityDTO:itemListEntityDTOList){
			itemListEntityDTOHashMap.put(itemListEntityDTO.getItemNo(),itemListEntityDTO);
		}
	}

	/**
	 * 查条码中心获取条码信息
	 * @param dto
	 * @throws Exception
	 */
	private List<BarcodeExpandDTO> getBarcodeExpandDTOMap(AssemblyRelaScanDTO dto) throws Exception {
		List<BarcodeExpandDTO> list = new ArrayList<>();
		List<List<String>> subSnList = CommonUtils.splitList(dto.getSubSnList(), Constant.INT_1);
		for (List<String> barcodList : subSnList) {
			BarcodeExpandQueryDTO barcodeExpandQueryDTO=new BarcodeExpandQueryDTO();
			barcodeExpandQueryDTO.setBarcodeList(barcodList);
			list.addAll(barcodeCenterRemoteService.expandQuery(barcodeExpandQueryDTO));
		}
		return list;
	}

	/**
	 * 写日志
	 * @param dto
	 * @param baItemList
	 * @throws Exception
	 */
	public void insertAssemblyRelaScanRecordInfo(AssemblyRelaScanDTO dto, List<BaItem> baItemList) throws Exception {
		List<AssemblyRelaScanRecordInfoEntityDTO> assemblyRelaScanRecordInfoEntityDTOList=new ArrayList<>();
		if(CollectionUtils.isEmpty(baItemList)){
            return;
		}
		for (BaItem baItem:baItemList) {
			AssemblyRelaScanRecordInfoEntityDTO assemblyRelaScanRecordInfoEntityDTO = new AssemblyRelaScanRecordInfoEntityDTO();
			assemblyRelaScanRecordInfoEntityDTO.setRemark(Constant.REMARK_SN);
			assemblyRelaScanRecordInfoEntityDTO.setSn(baItem.getSn());
			assemblyRelaScanRecordInfoEntityDTO.setAttribute1(dto.getMainSn());
			assemblyRelaScanRecordInfoEntityDTO.setItemCode(baItem.getItemNo());
			assemblyRelaScanRecordInfoEntityDTO.setCreateBy(dto.getCreateBy());
			assemblyRelaScanRecordInfoEntityDTO.setLastUpdatedBy(dto.getCreateBy());
			assemblyRelaScanRecordInfoEntityDTO.setFactoryId(dto.getFactoryId());
			assemblyRelaScanRecordInfoEntityDTOList.add(assemblyRelaScanRecordInfoEntityDTO);
		}
		assemblyRelaScanRecordInfoService.batchInsert(assemblyRelaScanRecordInfoEntityDTOList);
	}

	/**
	 * 组装关系融扫过站接口
	 */
	@Override
	public String assemblyPassStation(AssemblyRelaScanDTO dto) throws Exception {
		checkParamer(dto);
		// 获取指令
		PsWorkOrderDTO psWorkOrderDTO = getPsWorkOrderDTO(dto);
		// 获取子工序下所有工站
		List<CtRouteDetailDTO> routesInprocess = checkWorkStation(dto, psWorkOrderDTO.getLineCode());
		// 校验绑定是否完成
		checkBindingComplete(dto, routesInprocess, psWorkOrderDTO.getItemNo());
		// 过站
		PmScanConditionDTO entity = getPmScanConditionDTO(dto, psWorkOrderDTO, routesInprocess);
		standardModeCommonScanService.smCommonScanTran(entity);
		return MpConstant.RESULT_TYPE_OK;
	}


	private void checkParamer(AssemblyRelaScanDTO dto) {
		// 对传入的条码、子工序、工站进行校验
		if (StringUtils.isEmpty(dto.getMainSn())) {
			// 条码不能为空 例：219433234995
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SN_IS_NULL);
		}
		if (StringUtils.isEmpty(dto.getProcessName())) {
			// 请设置子工序 例：贴片A面
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PROCESS_IS_NOT_NULL);
		}
		if (StringUtils.isEmpty(dto.getWorkStationName())) {
			// 请设置工站 例：SMT产出
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.WORK_STATION_IS_NOT_NULL);
		}
	}

	/**
	 * 校验并获取指令
	 */
	private PsWorkOrderDTO getPsWorkOrderDTO(AssemblyRelaScanDTO dto) throws Exception {
		// 查询wip_info获取批次号
		PsWipInfo wipInfoBySn = psWipInfoRepository.getWipInfoBySn(dto.getMainSn());
		if (wipInfoBySn == null) {
			// 条码在在制信息表中不存在，请确认
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.FORM_SN_NOT_IN_WIP_INFO);
		}
		if (StringUtils.isEmpty(wipInfoBySn.getAttribute1())) {
			// 批次号为空
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PLAN_NO_IS_NULL);
		}
		// 根据批次号查找指令
		List<PsWorkOrderDTO> workOrderList = PlanscheduleRemoteService.getWorkOrderBasicByProdPlanId(wipInfoBySn.getAttribute1(), null);
		if (CollectionUtils.isEmpty(workOrderList)) {
			// 没有找到当前指令
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.GET_CURRENT_WORKORDER_ERROR);
		}
		// 获取传入的子工序代码
		dto.setProcessCode(getProcessCodeByName(dto.getProcessName()));
		// 找出当前子工序对应的指令
		List<PsWorkOrderDTO> psWorkOrderDTOs = workOrderList.stream()
				.filter(p -> StringUtils.isNotEmpty(p.getProcessGroup()) &&
						Arrays.asList(StringUtils.split(p.getProcessGroup(), MpConstant.SPLIT_CHAR)).contains(dto.getProcessCode()))
				.collect(Collectors.toList());
		if (CollectionUtils.isEmpty(psWorkOrderDTOs)) {
			// 未查询到指令的工艺路径
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.GET_CARFT_NULL);
		}
		return psWorkOrderDTOs.get(NumConstant.NUM_ZERO);
	}

	/**
	 * 校验工站、子工序并返回当前子工序下的所有工站
	 */
	private List<CtRouteDetailDTO> checkWorkStation(AssemblyRelaScanDTO dto, String lineCode) throws Exception {
		// 根据线体获取线体建模
		// 工站是否是当前线体、子工序的最后工站校验。
		List<CtRouteDetailDTO> routeDetailsByLine = CrafttechRemoteService.getCtRouteByRouteId(null, lineCode);
		if (CollectionUtils.isEmpty(routeDetailsByLine)) {
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.NOT_FIND_ROUTE);
		}
		// 获取当前线体、子工序下的所有工站
		List<CtRouteDetailDTO> routesInprocess = routeDetailsByLine.stream()
				.filter(p -> dto.getProcessCode().equals(p.getProcessCode()))
				.sorted(Comparator.comparing(CtRouteDetailDTO::getProcessSeq))
				.collect(Collectors.toList());
		if (CollectionUtils.isEmpty(routesInprocess)) {
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.CRAFT_NOT_CONTAIN_CURRENT);
		}
		// 校验传入的工站是否是子工序最后的工站
		CtRouteDetailDTO ctRouteDetailLast = routesInprocess.get(routesInprocess.size() - 1);
		if (!dto.getWorkStationName().equals(ctRouteDetailLast.getNextProcessName())) {
			// 子工序{0}的最后工站应为{1}
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PROCESS_LAST_WORKSTATION_ERROR,
					new Object[]{dto.getProcessName(), ctRouteDetailLast.getNextProcessName()});
		}
		return routesInprocess;
	}

	/**
	 * 校验绑定是否完成
	 */
	private void checkBindingComplete(AssemblyRelaScanDTO dto, List<CtRouteDetailDTO> routesInprocess, String itemNo) throws Exception {
		// 获取绑定关系模板
		ProdBindingSettingDTO queryDto = new ProdBindingSettingDTO();
		queryDto.setProcessCode(dto.getProcessCode());
		queryDto.setProductCode(itemNo);
		queryDto.setBindType(Constant.INT_1);
		List<ProdBindingSettingDTO> bindingSettings = prodBindingSettingRepository.getProdBindingSettingDTOList(queryDto);
		if (CollectionUtils.isEmpty(bindingSettings)) {
			return;
		}
		// 获取装配关系清单
		List<WipExtendIdentification> wipExtendIdentifications =
				wipExtendIdentificationRepository.getByFromSnProcessCode(dto.getMainSn(), dto.getProcessCode());
		if (bindingSettings.stream().anyMatch(p -> StringUtils.isEmpty(p.getWorkStation())) ||
				wipExtendIdentifications.stream().anyMatch(p -> StringUtils.isEmpty(p.getWorkStation()))) {
			// 如果工站为空，判断整个工序是否有少绑定的
			checkBinging(wipExtendIdentifications, MpConstant.STR_EMPTY, bindingSettings);
		} else {
			// 如果工站不为空，根据工站依次判断
			for (CtRouteDetailDTO ctRouteDetailDTO : routesInprocess) {
				checkBinging(wipExtendIdentifications, ctRouteDetailDTO.getNextProcess(), bindingSettings);
			}
		}
	}

	/**
	 * 校验绑定
	 *
	 * @param wipExtendIdentifications 装配关系清单
	 * @param workStation              工站
	 * @param bindingSettings          绑定设置
	 */
	private void checkBinging(List<WipExtendIdentification> wipExtendIdentifications,
							  String workStation,
							  List<ProdBindingSettingDTO> bindingSettings) {
		// 如果传入工站，过滤相关的工站
		if (StringUtils.isNotEmpty(workStation)) {
			bindingSettings = bindingSettings.stream()
					.filter(p -> workStation.equals(p.getWorkStation())).collect(Collectors.toList());
			wipExtendIdentifications = wipExtendIdentifications.stream()
					.filter(p -> workStation.equals(p.getWorkStation())).collect(Collectors.toList());
		}
		// 根据物料代码分组
		Map<String, List<WipExtendIdentification>> itemNoMap =
				wipExtendIdentifications.stream().collect(Collectors.groupingBy(WipExtendIdentification::getItemNo));
		for (ProdBindingSettingDTO setting : bindingSettings) {
			if (!checkBoundCount(setting, itemNoMap)) {
				// 工站{0}下的物料代码{1}没有绑定完成
				throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.ITEM_NOT_BOUND,
						new Object[]{workStation, setting.getItemCode()});
			}
		}
	}

	private boolean checkBoundCount(ProdBindingSettingDTO setting,
									Map<String, List<WipExtendIdentification>> itemNoMap) {
		if (setting.getUsageCount() == null || setting.getUsageCount().intValue() <= 0) {
			return true;
		}
		int usageCount = setting.getUsageCount().intValue();
		List<WipExtendIdentification> wipExtendByItemCode = itemNoMap.get(setting.getItemCode());
		int boundCount = CollectionUtils.isEmpty(wipExtendByItemCode) ? 0 : wipExtendByItemCode.size();
		return boundCount == usageCount;
	}

	/**
	 * 组装过站信息
	 */
	private PmScanConditionDTO getPmScanConditionDTO(AssemblyRelaScanDTO dto,
													 PsWorkOrderDTO psWorkOrderDTO,
													 List<CtRouteDetailDTO> routesInprocess) {
		// 组装工站到过站信息
		List<PmScanConditionDTO> processInfoList = getProcessInfoList(routesInprocess, dto.getCreateBy());
		PmScanConditionDTO entity = new PmScanConditionDTO();
		entity.setSn(dto.getMainSn());
		entity.setLineCode(psWorkOrderDTO.getLineCode());
		entity.setWorkOrderNo(psWorkOrderDTO.getWorkOrderNo());
		entity.setProcessInfoList(processInfoList);
		PmScanConditionDTO processInfoLast = processInfoList.get(processInfoList.size() - 1);
		entity.setWorkStation(processInfoLast.getWorkStation());
		entity.setCurrProcessCode(processInfoLast.getCurrProcessCode());
		entity.setCraftSection(processInfoLast.getCraftSection());
		entity.setUserId(dto.getCreateBy());
		entity.setCreateBy(dto.getCreateBy());
		entity.setLastUpdatedBy(dto.getCreateBy());
		entity.setFactoryId(dto.getFactoryId());
		return entity;
	}


	/**
	 * 根据子工序名找到子工序代码
	 */
	private String getProcessCodeByName(String processName) throws Exception {
		BSProcess processQuery = new BSProcess();
		processQuery.setxType(MpConstant.PROCESS_X_TYPE_P);
		processQuery.setProcessName(processName);
		List<BSProcess> processes = CrafttechRemoteService.getProcess(processQuery);
		if (CollectionUtils.isEmpty(processes) || StringUtils.isEmpty(processes.get(NumConstant.NUM_ZERO).getProcessCode())) {
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PROCESS_CODE_IS_NOT_EXISTED);
		}
		return processes.get(NumConstant.NUM_ZERO).getProcessCode();
	}

	/**
	 * 组装工站到过站信息
	 */
	private List<PmScanConditionDTO> getProcessInfoList(List<CtRouteDetailDTO> routesInprocess, String createBy) {
		List<PmScanConditionDTO> processInfoList = new ArrayList<>();
		for (CtRouteDetailDTO route : routesInprocess) {
			PmScanConditionDTO pmScanConditionDTO = new PmScanConditionDTO();
			pmScanConditionDTO.setWorkStation(route.getNextProcess());
			pmScanConditionDTO.setCurrProcessCode(route.getProcessCode());
			pmScanConditionDTO.setCraftSection(route.getCraftSection());
			pmScanConditionDTO.setaProcessCode(route.getProcessCode());
			pmScanConditionDTO.setUserId(createBy);
			pmScanConditionDTO.setCreateBy(createBy);
			pmScanConditionDTO.setLastUpdatedBy(createBy);
			processInfoList.add(pmScanConditionDTO);
		}
		return processInfoList;
	}
	/**
	* @Description: 输入主条码后入口
	* @Param: [comMacAssScanDTO]
	* @return: com.zte.interfaces.dto.ComMachineAssemblyScanDTO
	* @Author: Saber[10307315]
	* @Date: 2023/6/5 下午3:55
	*/
	@Override
	public ComMachineAssemblyScanDTO handleAfterInputMainBarcode(ComMachineAssemblyScanDTO comMacAssScanDTO) throws Exception {
		// 基础校验
		checkParamsOfComMac(comMacAssScanDTO);
		// 校验wip_info在制信息和主工序
		PsWipInfo psWipInfo = checkWipInfoAndProcess(comMacAssScanDTO);
		// 校验是否按照装配规则扫描，主条码是否已经完成绑定,并返回查询的绑定清单信息
		// 2.查看数据字典是否需要查询在绑定清单中
		SysLookupTypesDTO sysDTO = BasicsettingRemoteService.getSysLookUpValue
				(Constant.LOOKUP_TYPE_1003018, Constant.LOOK_UP_CODE_1003018001);
		boolean needCheckBindSetting = false;
		if (sysDTO != null && Constant.FLAG_Y.equals(sysDTO.getLookupMeaning())) {
			// 必须校验绑定清单
			needCheckBindSetting = true;
		}
		this.fixBomCheck(psWipInfo);
		List<ProdBindingSettingDTO> bindingList = checkBindRuleAndIfFinishBind(psWipInfo.getItemNo(), comMacAssScanDTO, needCheckBindSetting);
		// 校验跳转总数量
		if (comMacAssScanDTO.getSkipTotalQty() != null && comMacAssScanDTO.getSkipTotalQty() > 0) {
			checkSkipTotalQty(bindingList, comMacAssScanDTO.getSkipTotalQty());
		}
		// 获取指令信息，并拼接前端所需字段用于返回。
		ComMachineAssemblyScanDTO resultEntity = getInProcessInfo(comMacAssScanDTO.getLineCode(), psWipInfo, comMacAssScanDTO.getProcessCode());
		resultEntity.setBindingInfo(bindingList);
		// 返回前更新用户缓存设置
		saveSettingCache(comMacAssScanDTO);
		return resultEntity;
	}

	/**
	 *<AUTHOR>
	 * 主条码fixBom校验
	 *@Date 2025/5/9 14:32
	 *@Param psWipInfo
	 *@return
	 **/
	private void fixBomCheck (PsWipInfo psWipInfo) {
		// 根据任务号获取箱单必须上传物料
		List<FixBomDetailDTO> fixBomDetails = centerfactoryRemoteService.getFixBomByTaskNo(psWipInfo.getAttribute2());
		if (CollectionUtils.isEmpty(fixBomDetails)) {
			return;
		}
		Map<String, Double> qtyMap = fixBomDetails.stream().filter(e -> Constant.FLAG_Y.equals(e.getRequireMaterialUpload()) && !Constant.ITEM_TYPE_FINISH.equals(e.getItemType()))
				.collect(Collectors.groupingBy(FixBomDetailDTO::getZteCode, Collectors.summingDouble(FixBomDetailDTO::getItemQty)));
		Map<String, Object> bindParamMap = new HashMap<>();
		bindParamMap.put(Constant.PRODUCTCODE, psWipInfo.getItemNo());
		// 根据料单代码获取绑定清单
		List<ProdBindingSetting> prodBindingInfoList = prodBindingSettingService.getUsageCountByProductCode(bindParamMap);
		if (CollectionUtils.isEmpty(prodBindingInfoList)) {
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.NOT_FOUND_ITEM_BIND_INFO);
		}
		Map<String, ProdBindingSetting> bindMap = prodBindingInfoList.stream().filter(e -> StringUtils.isNotEmpty(e.getItemCode()))
				.collect(Collectors.toMap(ProdBindingSetting::getItemCode, e -> e));
		List<String> noItemList = new ArrayList<>();
		for (Map.Entry<String, Double> entry : qtyMap.entrySet()) {
			ProdBindingSetting bindingSetting = bindMap.get(entry.getKey());
			// 不在绑定清单中或数量不一致则校验不通过
			if (bindingSetting == null || bindingSetting.getUsageCount().compareTo(BigDecimal.valueOf(entry.getValue())) != 0) {
				noItemList.add(entry.getKey());
			}
		}
		if (!CollectionUtils.isEmpty(noItemList)) {
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.MATERIAL_BIND_INCOMPLETE, new String[] {noItemList.toString()});
		}
	}

	/**
	* @Description: 缓存前端设置的信息
	* @Param: [comMacAssScanDTO]
	* @return: void
	* @Author: Saber[10307315]
	* @Date: 2023/6/8 下午4:10
	*/
	private void saveSettingCache(ComMachineAssemblyScanDTO comMacAssScanDTO) {
		// 都记录缓存
		ComMachineAssemblyScanDTO comMachineAssemblyScanDTO = new ComMachineAssemblyScanDTO();
		comMachineAssemblyScanDTO.setLineCode(comMacAssScanDTO.getLineCode());
		comMachineAssemblyScanDTO.setProcessCode(comMacAssScanDTO.getProcessCode());
		comMachineAssemblyScanDTO.setWorkStation(comMacAssScanDTO.getWorkStation());
		String jsonString = JSONObject.toJSONString(comMachineAssemblyScanDTO);
		//设置缓存
		String redisKey = Constant.ASSEMBLY_SCAN_SELECT_SETTING_CACHE + Constant.AND + comMacAssScanDTO.getEmpNo()
				+ Constant.AND + comMacAssScanDTO.getFactoryId();
		RedisCacheUtils.set(redisKey, jsonString, Constant.CACHE_EXPIRED_TIME_30_DAY);
	}

	private void checkSkipTotalQty(List<ProdBindingSettingDTO> bindingList, Integer skipTotalQty) {
		if (CollectionUtils.isEmpty(bindingList) || skipTotalQty == null) {
			return;
		}
		// 计算绑定清单总数
		BigDecimal sum = new BigDecimal(Constant.STR_0);
		for (int i = 0; i < bindingList.size(); i++) {
			ProdBindingSettingDTO entity = bindingList.get(i);
			if (entity == null) {
				continue;
			}
			BigDecimal usageCount = entity.getUsageCount() == null ? new BigDecimal(Constant.STR_0) : entity.getUsageCount();
			sum = sum.add(usageCount);
		}
		// 如果总数大于跳转数量，则报错，小数舍去
		if (sum.intValue() > skipTotalQty) {
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SKIP_TOTAL_QTY_ILLEGAL);
		}
	}

	/** 
	* @Description: 获取指令信息，并拼接前端所需字段用于返回。
	* @Param: [lineCode, psWipInfo]
	* @return: com.zte.interfaces.dto.ComMachineAssemblyScanDTO
	* @Author: Saber[10307315]
	* @Date: 2023/6/8 下午4:07
	*/
	private ComMachineAssemblyScanDTO getInProcessInfo(String lineCode, PsWipInfo psWipInfo, String processCode) {
		String workOrderNo = psWipInfo.getWorkOrderNo();
		// 指令为空，通过页面所选线体和根据主条码带出的任务取计划开始时间最早的状态为（“已提交”或“已开工”或“挂起”或“零星板挂起”）的 指令
		if (StringUtils.isEmpty(workOrderNo)) {
			List<String> workOrderNoList = PlanscheduleRemoteService.selectWorkNoOfComMachine(lineCode, psWipInfo.getAttribute2(), processCode);
			if (CollectionUtils.isEmpty(workOrderNoList)) {
				String[] params = new String[]{psWipInfo.getSn()};
				throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.MAIN_BARCODE_NOT_HAVE_WORK_ORDER_NO, params);
			}
			if (workOrderNoList.size() > 1) {
				String[] params = new String[]{psWipInfo.getAttribute2()};
				throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.MAIN_BARCODE_HAVE_MORE_WORK_ORDER_NO, params);
			}
			workOrderNo = workOrderNoList.get(0);
		}
		// 组装
		ComMachineAssemblyScanDTO result = new ComMachineAssemblyScanDTO();
		result.setWorkOrderNo(workOrderNo);
		result.setItemCode(psWipInfo.getItemNo());
		result.setItemName(psWipInfo.getItemName());
		result.setStandardModelTask(psWipInfo.getAttribute2());
		return result;
	}

	/**
	* @Description: 校验是否按照装配规则扫描，needCheckBindSetting为TRUE则主条码是否已经完成绑定,反之，不报错
	 * 返回查询的绑定清单信息
	* @Param: [mainSnItemNo, comMacAssScanDTO, needCheckBindSettingFlag]
	* @return: java.util.List<com.zte.interfaces.dto.ProdBindingSettingDTO>
	* @Author: Saber[10307315]
	* @Date: 2023/6/8 下午3:19
	*/
	private List<ProdBindingSettingDTO> checkBindRuleAndIfFinishBind(String mainSnItemNo, ComMachineAssemblyScanDTO comMacAssScanDTO,
																	 boolean needCheckBindSettingFlag) {
		ProdBindingSettingDTO prodBindSetDTO = new ProdBindingSettingDTO();
		prodBindSetDTO.setProductCode(mainSnItemNo);
		prodBindSetDTO.setMainSn(comMacAssScanDTO.getMainBarcode());
		prodBindSetDTO.setProcessCode(comMacAssScanDTO.getProcessCode());
		List<ProdBindingSettingDTO> prodBindingInfoList = prodBindingSettingService.getBindingInfoByItemNew(prodBindSetDTO);
		// 无配置关系报错
		if (CollectionUtils.isEmpty(prodBindingInfoList)) {
			String[] params = new String[]{comMacAssScanDTO.getMainBarcode(), mainSnItemNo};
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.MAIN_BARCODE_NOT_HAVE_BINDING_SETTING, params);
		}
		// 判断是否按照装配规则扫描, 如果点了过站，则不校验，工站作为过站参数。
		ProdBindingSettingDTO entity = prodBindingInfoList.get(Constant.INT_0);
		// 要么工站全为空，按照子工序维度扫描，要么工站全不为空，按照工站维度扫描
		boolean compliantRuleFlag = (StringUtils.isEmpty(entity.getWorkStation()) && StringUtils.isEmpty(comMacAssScanDTO.getWorkStation()))
				|| (!StringUtils.isEmpty(entity.getWorkStation()) && !StringUtils.isEmpty(comMacAssScanDTO.getWorkStation()));
		if (!comMacAssScanDTO.isIfPassStationSwitch() && !compliantRuleFlag) {
			String[] params = new String[]{comMacAssScanDTO.getMainBarcode()};
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.ASSEMBLY_NOT_COMPLY_WITH_RULES, params);
		}
		// 如果是按工站维度绑定，则要过滤符合工站的绑定关系。
		if (!StringUtils.isEmpty(entity.getWorkStation()) && !StringUtils.isEmpty(comMacAssScanDTO.getWorkStation())) {
			// 记录是按工站维度维护的绑定关系
			comMacAssScanDTO.setWorkStationBindFlag(true);
			prodBindingInfoList = filterWorkStation(mainSnItemNo, comMacAssScanDTO, prodBindingInfoList);
		}
		// 校验主条码是否绑定完成
		boolean finishBindFlag = checkIfFinishBind(prodBindingInfoList);
		if (finishBindFlag && needCheckBindSettingFlag) {
			String[] params = new String[]{comMacAssScanDTO.getMainBarcode()};
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.MAIN_BARCODE_HAVE_FINISHED_BIND, params);
		}
		return prodBindingInfoList;
	}

	/**
	* @Description: 校验是否绑定完成
	* @Param: [bindingInfoList]
	* @return: boolean
	* @Author: Saber[10307315]
	* @Date: 2023/6/8 下午4:10
	*/
	private boolean checkIfFinishBind(List<ProdBindingSettingDTO> bindingInfoList) {
		boolean finishBindFlag = true;
		for (ProdBindingSettingDTO bindInfo : bindingInfoList) {
			if (bindInfo.getUsageCount().compareTo(bindInfo.getBindedCount()) > 0) {
				finishBindFlag = false;
				break;
			}
		}
		return finishBindFlag;
	}

	/** 
	* @Description: 如果维护到工站维度，则过滤工站
	* @Param: [mainSnItemNo, comMacAssScanDTO, bindingInfoList]
	* @return: java.util.List<com.zte.interfaces.dto.ProdBindingSettingDTO>
	* @Author: Saber[10307315]
	* @Date: 2023/6/9 下午1:57
	*/
	private List<ProdBindingSettingDTO> filterWorkStation(String mainSnItemNo, ComMachineAssemblyScanDTO comMacAssScanDTO, List<ProdBindingSettingDTO> bindingInfoList) {
		List<ProdBindingSettingDTO> bindingInfoListFilter = new ArrayList<>();
		for (ProdBindingSettingDTO bindInfo : bindingInfoList) {
			if (comMacAssScanDTO.getWorkStation().equals(bindInfo.getWorkStation())) {
				bindingInfoListFilter.add(bindInfo);
			}
		}
		// 无配置关系报错
		if (CollectionUtils.isEmpty(bindingInfoListFilter)) {
			String[] params = new String[]{comMacAssScanDTO.getMainBarcode(), mainSnItemNo};
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.MAIN_BARCODE_NOT_HAVE_BINDING_SETTING, params);
		}
		return bindingInfoListFilter;
	}

	/**
	* @Description: 校验wip_info在制信息和主工序
	 * 主条码必须在wip——info中，且主工序不能为入库
	* @Param: [comMacAssScanDTO]
	* @return: com.zte.domain.model.PsWipInfo
	* @Author: Saber[10307315]
	* @Date: 2023/6/8 下午3:02
	*/
	private PsWipInfo checkWipInfoAndProcess(ComMachineAssemblyScanDTO comMacAssScanDTO) {
		PsWipInfo wipInfoBySn = psWipInfoService.getWipInfoBySn(comMacAssScanDTO.getMainBarcode());
		if (wipInfoBySn == null) {
			String[] params = new String[]{comMacAssScanDTO.getMainBarcode()};
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.MAIN_BARCODE_NOT_HAVE_WIP_INFO, params);
		}
		// 主工序不能为入库
		if (Constant.WAREHOUSE_ENTRY.equals(wipInfoBySn.getCraftSection())) {
			String[] params = new String[]{comMacAssScanDTO.getMainBarcode()};
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.MAIN_BARCODE_CRAFT_SECTION_ILLEGAL, params);
		}
		return wipInfoBySn;
	}

	/**
	* @Description:
	* @Param: [comMacAssemblyScanDTO]
	* @return: void
	* @Author: Saber[10307315]
	* @Date: 2023/6/5 下午4:00
	*/
	private void checkParamsOfComMac(ComMachineAssemblyScanDTO comMacAssemblyScanDTO) {
		if (comMacAssemblyScanDTO == null) {
			throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, MessageId.PARAM_IS_NULL);
		}
		if (StringUtils.isEmpty(comMacAssemblyScanDTO.getLineCode())) {
			throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, MessageId.LINE_CODE_OF_COM_ASS_SCAN_NULL);
		}
		if (StringUtils.isEmpty(comMacAssemblyScanDTO.getProcessCode())) {
			throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, MessageId.PROCESS_CODE_OF_COM_ASS_SCAN_NULL);
		}
		if (StringUtils.isEmpty(comMacAssemblyScanDTO.getMainBarcode())) {
			throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, MessageId.MAIN_BARCODE_OF_COM_ASS_SCAN_NULL);
		}
	}

	@Override
	public ComMachineAssemblyScanDTO handlerAfterInputSubBarcode(ComMachineAssemblyScanDTO comMacAssScanDTO) throws Exception {
		// 加锁
		String redisKey = RedisKeyConstant.MAIN_SN_BIND_LOCK + comMacAssScanDTO.getMainBarcode();
		RedisLock redisLock = new RedisLock(redisKey);
		if (!redisLock.lock()) {
			throw new MesBusinessException(com.zte.springbootframe.common.model.RetCode.BUSINESSERROR_CODE, MessageId.MAIN_BARCODE_IS_BINDING_NOW);
		}
		try {
			List<BarcodeExpandDTO> barcodeExpandDTOList = this.getBarCodeBySubSn(comMacAssScanDTO.getSubBarcode());
			// 存在扫长码情况，条码中心存在以条码中心为准
			if(!CollectionUtils.isEmpty(barcodeExpandDTOList)){
				comMacAssScanDTO.setSubBarcode(barcodeExpandDTOList.get(0).getBarcode());
			}
			CollectionCodeScanDTO collectionCodeScanDTO = new CollectionCodeScanDTO();
			collectionCodeScanDTO.setMasterSn(comMacAssScanDTO.getSubBarcode());
			collectionCodeScanDTO.setEnabledFlag(Constant.FLAG_Y);
			// 根据扫描的子条码作为主条码查询集合码信息
			List<CollectionCodeScanDTO> codeList = collectionCodeScanService.getList(collectionCodeScanDTO);
			comMacAssScanDTO.setSubBarcodeList(new ArrayList<>());
			comMacAssScanDTO.getSubBarcodeList().add(comMacAssScanDTO.getSubBarcode());
			// 获取到集合码信息将所有子条码一起校验绑定到主条码
			if (!CollectionUtils.isEmpty(codeList)) {
				comMacAssScanDTO.getSubBarcodeList().addAll(codeList.stream().map(CollectionCodeScanDTO::getSubSn)
						.filter(StringUtils::isNotEmpty).collect(Collectors.toList()));
				comMacAssScanDTO.setCollectionCodeMap(codeList.stream().collect(Collectors.toMap(CollectionCodeScanDTO::getSubSn, CollectionCodeScanDTO::getQuantity)));
			}
			// return this.checkAndSubmit(comMacAssScanDTO);
			return this.subBarcodeScanCheck(comMacAssScanDTO, codeList);
		} finally {
			redisLock.unlock();
		}
	}

	/**
	 *<AUTHOR>
	 * 批量获取条码中心数据
	 *@Date 2025/5/19 13:50
	 *@Param [java.util.List<java.lang.String>]
	 *@return
	 **/
	private List<BarcodeExpandDTO> getBarcodeExpandDTOList(List<String> subBarcodeList) throws Exception{
		List<BarcodeExpandDTO> barcodeExpandDTOList = new ArrayList<>();
		if (CollectionUtils.isEmpty(subBarcodeList)) {
			return barcodeExpandDTOList;
		}
		// 条码中心接口支持批量查询，但数据缺少环保属性，后续条码中心修复的话可以改为批量
		for (String barcode : subBarcodeList) {
			BarcodeExpandQueryDTO barcodeExpandQueryDTO = new BarcodeExpandQueryDTO();
			barcodeExpandQueryDTO.setBarcodeList(new ArrayList() {{add(barcode);}});
			// 获取子条码条码中心数据
			List<BarcodeExpandDTO> barcodeExpandDTOS = barcodeCenterRemoteService.expandQuery(barcodeExpandQueryDTO);
			if (!CollectionUtils.isEmpty(barcodeExpandDTOS)) {
				barcodeExpandDTOList.addAll(barcodeExpandDTOS);
			}
		}
		return barcodeExpandDTOList;
	}

	/**
	 *<AUTHOR>
	 * 校验提交
	 *@Date 2025/5/9 9:28
	 *@Param [com.zte.interfaces.dto.ComMachineAssemblyScanDTO]
	 *@return
	 **/
	private ComMachineAssemblyScanDTO subBarcodeScanCheck (ComMachineAssemblyScanDTO comMacAssScanDTO, List<CollectionCodeScanDTO> codeList) throws Exception{
		if(comMacAssScanDTO.isAutoSubmitWarehouse() && null == comMacAssScanDTO.getMixWarehouseSubmit()){
			throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE,MessageId.SUBMIT_WAREHOUSE_IS_NULL);
		}
		List<BaItem> baItemInfoList = new ArrayList<>();
		// 获取主条码信息
		PsWipInfo mainSnWipInfo = psWipInfoService.getWipInfoBySn(comMacAssScanDTO.getMainBarcode());
		if (mainSnWipInfo == null) {
			String[] params = new String[]{comMacAssScanDTO.getMainBarcode()};
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.MAIN_BARCODE_NOT_HAVE_WIP_INFO, params);
		}
		// 获取子条码条码中心数据
		List<BarcodeExpandDTO> barcodeExpandDTOList = this.getBarcodeExpandDTOList(comMacAssScanDTO.getSubBarcodeList());
		Map<String, BarcodeExpandDTO> barcodeMap = new HashMap<>();
		// 校验环保属性，和SSP物料。只有在条码中心存在该条码才校验
		if (!CollectionUtils.isEmpty(barcodeExpandDTOList)) {
			barcodeMap = barcodeExpandDTOList.stream().filter(e -> StringUtils.isNotEmpty(e.getBarcode()))
					.collect(Collectors.toMap(BarcodeExpandDTO::getBarcode, e -> e));
		}
		this.subSnCheckFixBom(comMacAssScanDTO, mainSnWipInfo, barcodeExpandDTOList);
		// 校验条码的来源是否是条码中心，如果不是，转化为材料代码，如果无法转化，则报错，无此条码信息。
		this.checkSubSnSourceNew(comMacAssScanDTO, baItemInfoList, barcodeMap);
		// 验证条码是否与已绑定条码类型一致、ssp、环保属性
		this.checkSubSnCategoryNew(comMacAssScanDTO, barcodeExpandDTOList, mainSnWipInfo);
		// 如果是批次码，要求返回前端输入数量，如果已经输入了数量则跳过，如果已经勾选了取需求量也跳过
		if (!this.batchCodeUsageCheck(comMacAssScanDTO, baItemInfoList)) {
			ComMachineAssemblyScanDTO returnEntity = new ComMachineAssemblyScanDTO();
			returnEntity.setBatchCodeQty(new BigDecimal(Constant.INT_NEGATIVE_1));
			return returnEntity;
		}
		// 校验子条码如果在wipinfo存在，则必须主工序为入库
		checkProcessOfSubSnNew(comMacAssScanDTO, comMacAssScanDTO.getSubBarcodeList());
		// 如果wipinfo存在，则为单板条码，后续料单代码匹配使用12位
		// 2.查看数据字典是否需要查询在绑定清单中，如需要则校验子条码物料代码（包含替代物料）是否在绑定清单中。
		SysLookupTypesDTO dto = BasicsettingRemoteService.getSysLookUpValue
				(Constant.LOOKUP_TYPE_1003018, Constant.LOOK_UP_CODE_1003018001);
		boolean needCheckBindSetting = dto != null && Constant.FLAG_Y.equals(dto.getLookupMeaning());
		comMacAssScanDTO.setNeedCheckBindSetting(needCheckBindSetting);
		// 必须校验绑定清单
		// 主子条码的绑定关系(任务清单/绑定清单)和用量,并返回已经绑定后，绑定清单的已绑信息，用于是否绑定完成
		/*List<ProdBindingSettingDTO> bindingInfoList = checkBindRelationAndQty(comMacAssScanDTO, barCodeType, baItem, needCheckBindSetting, mainSnWipInfo);*/
		// 序列码
		List<BaItem> seqCodeList = baItemInfoList.stream().filter(e -> Constant.TYPE_SEQUENCE_CODE.equals(e.getBarCodeType())).collect(Collectors.toList());
		// 批次码
		List<BaItem> batchCodeList = baItemInfoList.stream().filter(e -> !Constant.TYPE_SEQUENCE_CODE.equals(e.getBarCodeType())).collect(Collectors.toList());
		List<ProdBindingSettingDTO> bindingInfoList = checkBindRelationAndQtyNew(comMacAssScanDTO, seqCodeList, batchCodeList, baItemInfoList, mainSnWipInfo);
		// 得到需要进行技改和锁定单校验的条码表
		List<String> needCheckTechSns = getNeedCheckSnsNew(comMacAssScanDTO);
		// 校验技改管控（包括MES锁定）,返回方法过程查询的子工序，用于流程管控
		String craftSection = checkTechChangeControl(comMacAssScanDTO, needCheckTechSns);
		// 校验锁定单信息
		checkLockOrderControl(needCheckTechSns);
		// 处理返回前端数据，并判断跳转总数量，是否满足跳转到主条码
		// ComMachineAssemblyScanDTO result = handlerReturnEntityAndSkip(comMacAssScanDTO, bindingInfoList, baItem, needCheckBindSetting);
		// 校验序列码绑定数量
		ComMachineAssemblyScanDTO seqResultDTO = handlerSeqReturnEntityAndSkip(comMacAssScanDTO, bindingInfoList, seqCodeList, needCheckBindSetting);
		// 校验批次码绑定数量
		ComMachineAssemblyScanDTO batchResultDTO = handlerBatchReturnEntityAndSkip(comMacAssScanDTO, bindingInfoList, codeList, batchCodeList, needCheckBindSetting);
		// 处理返回前端数据
		ComMachineAssemblyScanDTO scanDTO = resolveResultDTO(seqResultDTO, batchResultDTO);
		scanDTO.setNeedUsage(comMacAssScanDTO.isNeedUsage());

		// 处理过站和绑定等写表操作，使用同一事务。
		assemblyRelaScanService.handlerBindAndPassStation(comMacAssScanDTO, baItemInfoList, bindingInfoList, craftSection, mainSnWipInfo);
		// 如果成功处理了过站，则设置标志位
		scanDTO.setPassStationSucFlag(comMacAssScanDTO.isPassStationSucFlag());
		// 如果绑定清单全部绑定完成，则设置标志位
		scanDTO.setFinishBindFlag(comMacAssScanDTO.isFinishBindFlag());
		scanDTO.setSubmitWarehouseEnableFlag(comMacAssScanDTO.isSubmitWarehouseEnableFlag());
		// 如果完成入库，则设置标志位
		scanDTO.setSubmitWarehouseSucFlag(comMacAssScanDTO.isSubmitWarehouseSucFlag());
		scanDTO.setErrMsg(comMacAssScanDTO.getErrMsg());
		scanDTO.setNeedCheckBindSetting(needCheckBindSetting);
		scanDTO.setAutoSubmitWarehouse(comMacAssScanDTO.isAutoSubmitWarehouse());
		return scanDTO;

	}

	/**
	 *<AUTHOR>
	 * 子条码fixBom校验
	 *@Date 2025/5/17 16:53
	 *@Param [com.zte.interfaces.dto.ComMachineAssemblyScanDTO, com.zte.domain.model.PsWipInfo, java.util.List<com.zte.interfaces.dto.BarcodeExpandDTO>]
	 *@return
	 **/
	private void subSnCheckFixBom (ComMachineAssemblyScanDTO comMacAssScanDTO, PsWipInfo wipInfoByMainSn, List<BarcodeExpandDTO> barcodeExpandDTOList) {
		List<FixBomDetailDTO> fixBomByTaskNo = centerfactoryRemoteService.getFixBomByTaskNo(wipInfoByMainSn.getAttribute2());
		comMacAssScanDTO.setFixBomSet(new HashSet<>());
		if (CollectionUtils.isEmpty(fixBomByTaskNo)) {
			return;
		}
		Set<String> errSet = fixBomByTaskNo.stream()
				.filter(e -> !Constant.ITEM_TYPE_FINISH.equals(e.getItemType()) && StringUtils.isEmpty(e.getItemNumber()))
				.map(FixBomDetailDTO::getZteCode).collect(Collectors.toSet());
		// 异常fixBom信息
		if (!CollectionUtils.isEmpty(errSet)) {
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.FIX_BOM_ERR_CHECK);
		}
		// 箱单必须物料
		Set<String> materialSet = fixBomByTaskNo.stream()
				.filter(e -> Constant.FLAG_Y.equals(e.getRequireMaterialUpload()) && !Constant.ITEM_TYPE_FINISH.equals(e.getItemType()))
				.map(FixBomDetailDTO::getZteCode).collect(Collectors.toSet());
		// 按sn上传物料--需为箱单上传物料
		Set<String> bySnSet = fixBomByTaskNo.stream().filter(e -> materialSet.contains(e.getZteCode()) && Constant.FLAG_Y.equals(e.getUploadBySn()))
				.map(FixBomDetailDTO::getZteCode).collect(Collectors.toSet());
		// fixBom需要物料
		Set<String> fixBomSet = fixBomByTaskNo.stream().filter(e -> Constant.FLAG_Y.equals(e.getFixBomRequired()))
				.map(FixBomDetailDTO::getZteCode).collect(Collectors.toSet());
		comMacAssScanDTO.setFixBomSet(fixBomSet);
		for (BarcodeExpandDTO expandDTO : barcodeExpandDTOList) {
			// 箱单必须上传物料原始箱码不能为空
			if (materialSet.contains(expandDTO.getItemCode()) && StringUtils.isEmpty(expandDTO.getRelatedContainerBarcode())) {
				throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.BY_CONTAINER_ITEM_BOX_NO_EMPTY, new Object[]{expandDTO.getBarcode()});
			}
			// 按sn上传物料必须是序列码
			if (bySnSet.contains(expandDTO.getItemCode()) && !StringUtils.equals(Constant.TYPE_SEQUENCE_CODE, expandDTO.getParentCategoryName())) {
				throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.BY_SN_NOT_SEQUENCE_CODE, new Object[]{expandDTO.getBarcode()});
			}
		}
	}

	/**
	 *<AUTHOR>
	 * 组合返回数据
	 *@Date 2025/5/15 20:22
	 *@Param [com.zte.interfaces.dto.ComMachineAssemblyScanDTO, com.zte.interfaces.dto.ComMachineAssemblyScanDTO]
	 *@return
	 **/
	private ComMachineAssemblyScanDTO resolveResultDTO (ComMachineAssemblyScanDTO seqResultDTO, ComMachineAssemblyScanDTO batchResultDTO) {
		// 批次码和序列码返回结果都为空报错
		if (batchResultDTO == null && seqResultDTO == null) {
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, RetCode.BUSINESSERROR_MSGID);
		}
		// 批次码返回结果为空直接返回序列码数据
		if (batchResultDTO == null) {
			return seqResultDTO;
		}
		// 序列码返回结果为空直接返回批次码数据
		if (seqResultDTO == null) {
			return batchResultDTO;
		}
		// 合并子条码返回数据
		batchResultDTO.getDetailDTOList().addAll(seqResultDTO.getDetailDTOList());
		return batchResultDTO;
	}

	/**
	 *<AUTHOR>
	 * 序列码跳转校验
	 *@Date 2025/5/17 17:14
	 *@Param [com.zte.interfaces.dto.ComMachineAssemblyScanDTO, java.util.List<com.zte.interfaces.dto.ProdBindingSettingDTO>, java.util.List<com.zte.domain.model.BaItem>, boolean]
	 *@return
	 **/
	private ComMachineAssemblyScanDTO handlerSeqReturnEntityAndSkip(ComMachineAssemblyScanDTO comMacAssScanDTO,
																	List<ProdBindingSettingDTO> bindingInfoList,
																	List<BaItem> seqCodeList, boolean needCheckBindSetting) {
		ComMachineAssemblyScanDTO result = new ComMachineAssemblyScanDTO();
		// 无序列码数据返回
		if (CollectionUtils.isEmpty(seqCodeList)) {
			return null;
		}
		List<ComMachineAssemblyScanDetailDTO> detailDTOList = new ArrayList<>();
		seqCodeList.forEach(e -> {
			ComMachineAssemblyScanDetailDTO detailDTO = new ComMachineAssemblyScanDetailDTO();
			detailDTO.setMainBarcode(comMacAssScanDTO.getMainBarcode());
			detailDTO.setMainItemCode(comMacAssScanDTO.getItemCode());
			detailDTO.setSubBarcode(e.getSn());
			detailDTO.setSubItemCode(e.getItemNo());
			detailDTO.setSubItemName(e.getItemName());
			detailDTO.setBindQty(new BigDecimal(Constant.STR_1));
			e.setBindQty(detailDTO.getBindQty());
			detailDTOList.add(detailDTO);
		});
		result.setDetailDTOList(detailDTOList);
		result.setSateSkipFlag(false);
		result.setBindingInfo(bindingInfoList);
		if (CollectionUtils.isEmpty(bindingInfoList)) {
			return result;
		}
		// 如果未输入跳转数量，则直接返回
		if (comMacAssScanDTO.getSkipTotalQty() == null || comMacAssScanDTO.getSkipTotalQty() <= 0) {
			return result;
		}
		//赋值是否跳转
		this.setSateSkipFlag(comMacAssScanDTO, bindingInfoList, new BigDecimal(seqCodeList.size()), needCheckBindSetting, result);
		return result;
	}

	/**
	 *<AUTHOR>
	 * 跳转校验
	 *@Date 2025/5/17 17:15
	 *@Param [com.zte.interfaces.dto.ComMachineAssemblyScanDTO, java.util.List<com.zte.interfaces.dto.ProdBindingSettingDTO>, java.math.BigDecimal, boolean, com.zte.interfaces.dto.ComMachineAssemblyScanDTO]
	 *@return
	 **/
	private void setSateSkipFlag(ComMachineAssemblyScanDTO comMacAssScanDTO, List<ProdBindingSettingDTO> bindingInfoList, BigDecimal bindingQty, boolean needCheckBindSetting, ComMachineAssemblyScanDTO result) {
		// 计算主条码已经绑定完的数量
		BigDecimal boundQtySum;
		// 如果是必须在绑定清单中，则直接用绑定清单信息来统计已经绑定的数量，否则，应该直接查询绑定关系记录表来统计，因为包含在ERP清单但不在绑定清单的子条码绑定。
		if (needCheckBindSetting) {
			boundQtySum = getBoundQtyByProdSetting(bindingInfoList);
		} else {
			boundQtySum = getBoundQtyByWipExtend(comMacAssScanDTO, bindingQty);
		}
		result.setBindingQty(boundQtySum);
		// 当前主条码绑定的子条码数量等于界面设置的跳转数量且必绑清单绑定完成（若字典开启）时,可跳转
		if (boundQtySum.intValue() >= comMacAssScanDTO.getSkipTotalQty()) {
			result.setSateSkipFlag(true);
			comMacAssScanDTO.setSateSkipFlag(true);
		}
	}

	/**
	 *<AUTHOR>
	 * 获取批次码返回信息并计算跳转数量
	 *@Date 2025/5/18 11:45
	 *@Param [com.zte.interfaces.dto.ComMachineAssemblyScanDTO, java.util.List<com.zte.interfaces.dto.ProdBindingSettingDTO>, java.util.List<com.zte.interfaces.dto.CollectionCodeScanDTO>, java.util.List<com.zte.domain.model.BaItem>, boolean]
	 *@return
	 **/
	private ComMachineAssemblyScanDTO handlerBatchReturnEntityAndSkip(ComMachineAssemblyScanDTO comMacAssScanDTO,
																 List<ProdBindingSettingDTO> bindingInfoList, List<CollectionCodeScanDTO> codeList,
																 List<BaItem> snItemInfo, boolean needCheckBindSetting) {
		ComMachineAssemblyScanDTO result = new ComMachineAssemblyScanDTO();
		if (CollectionUtils.isEmpty(snItemInfo)) {
			return null;
		}
		// 集合码取集合码数量，不取前端数量
		Map<String, Integer> subCodeQtyMap = codeList.stream().collect(Collectors.toMap(CollectionCodeScanDTO::getSubSn, CollectionCodeScanDTO::getQuantity));
		BigDecimal batchCodeQty = comMacAssScanDTO.getBatchCodeQty();
		List<ComMachineAssemblyScanDetailDTO> detailDTOList = new ArrayList<>();
		BigDecimal sumQty = new BigDecimal(Constant.INT_0);
		snItemInfo.forEach(e -> {
			// 条码对应数量
			Integer subCodeQty = subCodeQtyMap.get(e.getSn());
			ComMachineAssemblyScanDetailDTO detailDTO = new ComMachineAssemblyScanDetailDTO();
			detailDTO.setMainBarcode(comMacAssScanDTO.getMainBarcode());
			detailDTO.setMainItemCode(comMacAssScanDTO.getItemCode());
			detailDTO.setSubBarcode(e.getSn());
			detailDTO.setSubItemCode(e.getItemNo());
			detailDTO.setSubItemName(e.getItemName());
			// 非集合码取前端数量
			detailDTO.setBindQty(subCodeQty == null ? batchCodeQty : new BigDecimal(subCodeQty));
			e.setBindQty(detailDTO.getBindQty());
			sumQty.add(detailDTO.getBindQty());
			detailDTOList.add(detailDTO);
		});
		result.setDetailDTOList(detailDTOList);
		result.setSateSkipFlag(false);
		result.setBindingInfo(bindingInfoList);
		if (CollectionUtils.isEmpty(bindingInfoList)) {
			return result;
		}
		// 如果未输入跳转数量，则直接返回
		if (comMacAssScanDTO.getSkipTotalQty() == null || comMacAssScanDTO.getSkipTotalQty() <= 0) {
			return result;
		}
		setSateSkipFlag(comMacAssScanDTO, bindingInfoList, sumQty, needCheckBindSetting, result);
		return result;
	}


	/**
	 *<AUTHOR>
	 * 获取需校验子条码
	 *@Date 2025/5/18 11:47
	 *@Param [com.zte.interfaces.dto.ComMachineAssemblyScanDTO]
	 *@return
	 **/
	private List<String> getNeedCheckSnsNew(ComMachineAssemblyScanDTO comMacAssScanDTO) {
		String mainSn = comMacAssScanDTO.getMainBarcode();
		List<String> needCheckTechSns = new ArrayList<>();
		needCheckTechSns.add(mainSn);
		needCheckTechSns.addAll(comMacAssScanDTO.getSubBarcodeList());
		// 查询子条码下的所有关联条码
		List<WipExtendIdentification> bindingList = wipExtendIdentificationRepository.getAllChildSn(comMacAssScanDTO.getSubBarcodeList());
		if (!CollectionUtils.isEmpty(bindingList)) {
			needCheckTechSns.addAll(bindingList.stream().map(WipExtendIdentification::getSn).collect(Collectors.toList()));
		}
		return needCheckTechSns;
	}


	/**
	 *<AUTHOR>
	 * 物料校验及数量计算
	 *@Date 2025/5/18 11:47
	 *@Param [com.zte.interfaces.dto.ComMachineAssemblyScanDTO, java.util.List<com.zte.domain.model.BaItem>, java.util.List<com.zte.domain.model.BaItem>, java.util.List<com.zte.domain.model.BaItem>, com.zte.domain.model.PsWipInfo]
	 *@return
	 **/
	private List<ProdBindingSettingDTO> checkBindRelationAndQtyNew (ComMachineAssemblyScanDTO comMacAssScanDTO, List<BaItem> seqCodeList, List<BaItem> batchCodeList, List<BaItem> baItemInfoList,
																	PsWipInfo wipInfoByMainSn) throws Exception {
		// 校验子条码是否已经绑定过。
		this.checkHaveBindRelationNew(comMacAssScanDTO, seqCodeList, batchCodeList);
		// 校验主子条码的物料代码(包含替代物料)是否有绑定关系。
		// 1.校验子条码物料代码（包含替代物料）是否在任务清单中，并记录到comMacAssScanDTO中的haveErpRelFlag中
		// 校验是否按照装配规则扫描，主条码是否已经完成绑定,并返回查询的绑定清单信息
		List<ProdBindingSettingDTO> bindingList = checkBindRuleAndIfFinishBind(comMacAssScanDTO.getItemCode(), comMacAssScanDTO, comMacAssScanDTO.isNeedCheckBindSetting());
		// BA确认过，ERP任务使用了替代物料匹配，绑定清单中匹配时，不受影响，仍然可以用本身物料或其他替代物料匹配
		checkErpTaskHaveTheItemNew(comMacAssScanDTO, baItemInfoList, wipInfoByMainSn, bindingList);
		return bindingList;
	}


	/**
	 *<AUTHOR>
	 * erp任务清单校验及绑定清单校验
	 *@Date 2025/5/18 11:48
	 *@Param [com.zte.interfaces.dto.ComMachineAssemblyScanDTO, java.util.List<com.zte.domain.model.BaItem>, com.zte.domain.model.PsWipInfo, java.util.List<com.zte.interfaces.dto.ProdBindingSettingDTO>]
	 *@return
	 **/
	private void checkErpTaskHaveTheItemNew (ComMachineAssemblyScanDTO comMacAssScanDTO, List<BaItem> baItemInfoList,
											 PsWipInfo wipInfoByMainSn, List<ProdBindingSettingDTO> bindingList) throws Exception {
		// 使用主条码的任务号查询ERP任务清单, 返工任务使用originTaskNo
		String taskNo = comMacAssScanDTO.getStandardModelTask();
		if (wipInfoByMainSn != null && !StringUtils.isEmpty(wipInfoByMainSn.getOriginalTask())) {
			taskNo = wipInfoByMainSn.getOriginalTask();
		}
		if (StringUtils.isEmpty(taskNo)) {
			String[] params = new String[]{comMacAssScanDTO.getMainBarcode()};
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.MAIN_BARCODE_WIP_INFO_NOT_HAVE_TASK_NO, params);
		}
		// 转化为MAP形式，如果是单板模式，则使用12位匹配
		Map<String, ProdBindingSettingDTO> stepBindInfoMap = this.getItemNoToBindInfoMap(bindingList, true);
		// 非单板绑定清单map
		Map<String, ProdBindingSettingDTO> noStepBindInfoMap = this.getItemNoToBindInfoMap(bindingList, false);
		List<String> repalceParamList = baItemInfoList.stream().map(BaItem::getItemNo).collect(Collectors.toList());
		// 获取条码替代物料 (总)
		Map<String, List<MtlRelatedItemsEntityDTO>> replaceItem = this.getReplaceItem(repalceParamList);
		// 查询出后，校验子条码物料代码是否在ERP任务清单中
		List<ItemListEntityDTO> itemListEntityDTOList = datawbRemoteService.getErpItemListByTaskNo(taskNo);
		// 无erp任务清单直接校验绑定清单
		if (CollectionUtils.isEmpty(itemListEntityDTOList)) {
			// 单板条码绑定清单校验
			this.checkBindListHaveTheItemStep(comMacAssScanDTO, stepBindInfoMap,
					baItemInfoList, new HashSet<>(), replaceItem);
			// 非单板条码绑定清单校验
			this.checkBindListHaveTheItemNoStep(comMacAssScanDTO, noStepBindInfoMap,
					baItemInfoList, new HashSet<>(), replaceItem);
			return;
		}
		// 非单板物料map
		Map<String, ItemListEntityDTO> noStepItemMap = itemListEntityDTOList.stream().collect(Collectors.toMap(ItemListEntityDTO::getItemNo, v -> v));
		// 单板物料map
		Map<String, ItemListEntityDTO> stepItemMap = itemListEntityDTOList.stream().collect(Collectors.toMap(e -> e.getItemNo().substring(0, Constant.INT_12), v -> v));
		// 校验获取在erp任务清单存在或替代物料在erp任务清单存在的单板物料
		Set<String> stepErpSet = this.stepItemInErpInfoCheck(baItemInfoList, replaceItem, comMacAssScanDTO, stepItemMap);
		// 校验获取在erp任务清单存在或替代物料在erp任务清单存在的非单板物料
		Set<String> noStepErpSet = this.noStepItemInErpInfoCheck(baItemInfoList, replaceItem, comMacAssScanDTO, noStepItemMap);
		// 单板条码绑定清单校验
		this.checkBindListHaveTheItemStep(comMacAssScanDTO, stepBindInfoMap,
				baItemInfoList, stepErpSet, replaceItem);
		// 非单板条码绑定清单校验
		this.checkBindListHaveTheItemNoStep(comMacAssScanDTO, noStepBindInfoMap,
				baItemInfoList, noStepErpSet, replaceItem);
	}

	/**
	 *<AUTHOR>
	 * 单板条码任务清单校验
	 *@Date 2025/5/18 11:49
	 *@Param [com.zte.interfaces.dto.ComMachineAssemblyScanDTO, java.util.Map<java.lang.String,com.zte.interfaces.dto.ProdBindingSettingDTO>, java.util.List<com.zte.domain.model.BaItem>, java.util.Set<java.lang.String>, java.util.Map<java.lang.String,java.util.List<com.zte.interfaces.dto.MtlRelatedItemsEntityDTO>>]
	 *@return
	 **/
	private void checkBindListHaveTheItemStep(ComMachineAssemblyScanDTO comMacAssScanDTO, Map<String, ProdBindingSettingDTO> stepBindInfoMap,
															   List<BaItem> baItemInfoList, Set<String> stepErpSet,
																	 Map<String, List<MtlRelatedItemsEntityDTO>> replaceItem) throws Exception {
		List<String> stepSnList = comMacAssScanDTO.getStepSnList();
		if (CollectionUtils.isEmpty(stepSnList)) {
			return;
		}
		for (BaItem baItem : baItemInfoList) {
			if (!stepSnList.contains(baItem.getSn())) {
				continue;
			}
			// 查询待绑定关系表
			String itemNo = baItem.getItemNo().substring(0, Constant.INT_12);
			if (stepBindInfoMap.containsKey(itemNo)) {
				// 3.校验已绑数量,需绑数量，本次绑定数量
				checkQtyOfBind(comMacAssScanDTO, stepBindInfoMap.get(itemNo), baItem.getBarCodeType(), baItem);
				continue;
			}
			// 如果替代物料也没在任务清单中，或在清单中存在多个，报错
			String key = stepSnGetKey(comMacAssScanDTO, stepBindInfoMap, stepErpSet, replaceItem, baItem);
			// 3.校验已绑数量,需绑数量，本次绑定数量
			checkQtyOfBind(comMacAssScanDTO, stepBindInfoMap.get(key), baItem.getBarCodeType(), baItem);
		}
	}

	/**
	 *<AUTHOR>
	 * 非单板条码任务清单校验
	 *@Date 2025/5/18 11:49
	 *@Param
	 *@return
	 **/
	private void checkBindListHaveTheItemNoStep(ComMachineAssemblyScanDTO comMacAssScanDTO, Map<String, ProdBindingSettingDTO> noStepBindInfoMap,
																	 List<BaItem> baItemInfoList, Set<String> noStepErpSet, Map<String, List<MtlRelatedItemsEntityDTO>> replaceItem) throws Exception {
		List<String> noStepSnList = comMacAssScanDTO.getNoStepSnList();
		if (CollectionUtils.isEmpty(noStepSnList)) {
			return;
		}
		for (BaItem baItem : baItemInfoList) {
			if (!noStepSnList.contains(baItem.getSn())) {
				continue;
			}
			// 查询待绑定关系表
			String itemNo = baItem.getItemNo();
			if (noStepBindInfoMap.containsKey(itemNo)) {
				// 3.校验已绑数量,需绑数量，本次绑定数量
				checkQtyOfBind(comMacAssScanDTO, noStepBindInfoMap.get(itemNo), baItem.getBarCodeType(), baItem);
				continue;
			}
			// 如果替代物料也没在任务清单中，或在清单中存在多个，报错
			String key = noStepSnGetKey(comMacAssScanDTO, noStepBindInfoMap, noStepErpSet, replaceItem, baItem);
			// 3.校验已绑数量,需绑数量，本次绑定数量
			checkQtyOfBind(comMacAssScanDTO, noStepBindInfoMap.get(key), baItem.getBarCodeType(), baItem);
		}
	}

	/**
	 *<AUTHOR>
	 * 单板条码替代物料获取
	 *@Date 2025/5/18 11:51
	 *@Param [com.zte.interfaces.dto.ComMachineAssemblyScanDTO, java.util.Map<java.lang.String,com.zte.interfaces.dto.ProdBindingSettingDTO>, java.util.Set<java.lang.String>, java.util.Map<java.lang.String,java.util.List<com.zte.interfaces.dto.MtlRelatedItemsEntityDTO>>, com.zte.domain.model.BaItem]
	 *@return
	 **/

	private String stepSnGetKey(ComMachineAssemblyScanDTO comMacAssScanDTO, Map<String, ProdBindingSettingDTO> itemNoToBindInfoMap,
						  Set<String> stepErpSet, Map<String, List<MtlRelatedItemsEntityDTO>> replaceItemMap, BaItem baItem) {
		int sum = 0;
		Set<String> relatedSet = new HashSet<>();
		List<MtlRelatedItemsEntityDTO> itemsEntityDTOS = replaceItemMap.get(baItem.getItemNo());
		if (!CollectionUtils.isEmpty(itemsEntityDTOS)) {
			relatedSet = itemsEntityDTOS.stream().map(MtlRelatedItemsEntityDTO::getItemCode).collect(Collectors.toSet());
		}
		String replaceItem = Constant.STR_EMPTY;
		String key = Constant.STR_EMPTY;
		for (String s : relatedSet) {
			String itemNoEntity = s;
			if (itemNoEntity != null && itemNoEntity.length() >= Constant.INT_12) {
				itemNoEntity = itemNoEntity.substring(0, Constant.INT_12);
			}
			if (itemNoToBindInfoMap.containsKey(itemNoEntity)) {
				replaceItem = s;
				key = itemNoEntity;
				sum++;
			}
		}
		checkItemInBind(comMacAssScanDTO, comMacAssScanDTO.isNeedCheckBindSetting(), sum, stepErpSet.contains(baItem.getSn()));
		// 赋值替代物料
		baItem.setReplaceItemNo(replaceItem);
		Set<String> fixBomSet = comMacAssScanDTO.getFixBomSet();
		if (fixBomSet.contains(replaceItem)) {
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.FIX_BOM_CAN_NOT_BE_REPLACE, new Object[]{replaceItem});
		}
		return key;
	}

	/**
	 *<AUTHOR>
	 * 非单板条码替代物料获取
	 *@Date 2025/5/18 11:51
	 *@Param [com.zte.interfaces.dto.ComMachineAssemblyScanDTO, java.util.Map<java.lang.String,com.zte.interfaces.dto.ProdBindingSettingDTO>, java.util.Set<java.lang.String>, java.util.Map<java.lang.String,java.util.List<com.zte.interfaces.dto.MtlRelatedItemsEntityDTO>>, com.zte.domain.model.BaItem]
	 *@return
	 **/
	private String noStepSnGetKey(ComMachineAssemblyScanDTO comMacAssScanDTO, Map<String, ProdBindingSettingDTO> itemNoToBindInfoMap,
								Set<String> noStepErpSet, Map<String, List<MtlRelatedItemsEntityDTO>> replaceItemMap, BaItem baItem) {
		int sum = 0;
		Set<String> relatedSet = new HashSet<>();
		List<MtlRelatedItemsEntityDTO> itemsEntityDTOS = replaceItemMap.get(baItem.getItemNo());
		if (!CollectionUtils.isEmpty(itemsEntityDTOS)) {
			relatedSet = itemsEntityDTOS.stream().map(MtlRelatedItemsEntityDTO::getItemCode).collect(Collectors.toSet());
		}
		String replaceItem = Constant.STR_EMPTY;
		String key = Constant.STR_EMPTY;
		for (String s : relatedSet) {
            if (itemNoToBindInfoMap.containsKey(s)) {
				replaceItem = s;
				key = s;
				sum++;
			}
		}
		checkItemInBind(comMacAssScanDTO, comMacAssScanDTO.isNeedCheckBindSetting(), sum, noStepErpSet.contains(baItem.getSn()));
		// 赋值替代物料
		baItem.setReplaceItemNo(replaceItem);
		Set<String> fixBomSet = comMacAssScanDTO.getFixBomSet();
		if (fixBomSet.contains(replaceItem)) {
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.FIX_BOM_CAN_NOT_BE_REPLACE, new Object[]{replaceItem});
		}
		return key;
	}

	private Set<String> stepItemInErpInfoCheck (List<BaItem> baItemInfoList, Map<String, List<MtlRelatedItemsEntityDTO>> replaceItem,
										 ComMachineAssemblyScanDTO comMacAssScanDTO, Map<String, ItemListEntityDTO> stepItemMap) {
		List<String> stepSnList = comMacAssScanDTO.getStepSnList();
		Set<String> fixBomSet = comMacAssScanDTO.getFixBomSet();
		Set<String> snInErpSet = new HashSet<>();
		if (CollectionUtils.isEmpty(stepSnList)) {
			return snInErpSet;
		}
		for (BaItem itemDTO : baItemInfoList) {
			String sn = itemDTO.getSn();
			if (!stepSnList.contains(sn)) {
				continue;
			}
			Set<String> itemNos = new HashSet<>();
			itemNos.add(itemDTO.getItemNo());
			List<String> replaceItemList = new ArrayList<>();
			this.getStepSumOfErpCore(itemNos, stepItemMap, replaceItemList);
			// 物料代码在任务清单中存在跳过
			if (!CollectionUtils.isEmpty(replaceItemList)) {
				snInErpSet.add(sn);
				continue;
			}
			List<MtlRelatedItemsEntityDTO> relatedItemsList = replaceItem.get(itemDTO.getItemNo());
			String[] params = new String[]{sn};
			// 未获取到物料替代信息
			if (CollectionUtils.isEmpty(relatedItemsList)) {
				continue;
			}
			Set<String> replaceItemSet = relatedItemsList.stream().map(MtlRelatedItemsEntityDTO::getInventoryItemCode).collect(Collectors.toSet());
			// 根据替代物料匹配
			this.getStepSumOfErpCore(replaceItemSet, stepItemMap, replaceItemList);
			if (CollectionUtils.isEmpty(replaceItemList)) {
				continue;
			}
			// 替代物料在任务清单中存在多个
			if (replaceItemList.size() > 1) {
				throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.ITEM_OR_REPLACE_ITEM_IN_ERP_TASK_MORE, params);
			}
			if (fixBomSet.contains(replaceItemList.get(0))) {
				throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.FIX_BOM_CAN_NOT_BE_REPLACE, new Object[]{replaceItemList.get(0)});
			}
			snInErpSet.add(sn);
		}
		return snInErpSet;
	}

	private Set<String> noStepItemInErpInfoCheck (List<BaItem> baItemInfoList, Map<String, List<MtlRelatedItemsEntityDTO>> replaceItem,
										   ComMachineAssemblyScanDTO comMacAssScanDTO, Map<String, ItemListEntityDTO> noStepItemMap) {
		List<String> noStepSnList = comMacAssScanDTO.getNoStepSnList();
		Set<String> snInErpSet = new HashSet<>();
		Set<String> fixBomSet = comMacAssScanDTO.getFixBomSet();
		if (CollectionUtils.isEmpty(noStepSnList)) {
			return snInErpSet;
		}
		for (BaItem item : baItemInfoList) {
			String sn = item.getSn();
			if (!noStepSnList.contains(sn)) {
				continue;
			}
			Set<String> itemNos = new HashSet<>();
			itemNos.add(item.getItemNo());
			List<String> replaceItemList = new ArrayList<>();
			this.getNoStepSumOfErpCore(itemNos, noStepItemMap, replaceItemList);
			// 物料代码在任务清单中存在跳过
			if (!CollectionUtils.isEmpty(replaceItemList)) {
				snInErpSet.add(sn);
				continue;
			}
			List<MtlRelatedItemsEntityDTO> relatedItemsList = replaceItem.get(item.getItemNo());
			String[] params = new String[]{sn};
			// 未获取到物料替代信息
			if (CollectionUtils.isEmpty(relatedItemsList)) {
				continue;
			}
			Set<String> replaceItemSet = relatedItemsList.stream().map(MtlRelatedItemsEntityDTO::getInventoryItemCode).collect(Collectors.toSet());
			// 根据替代物料匹配
			this.getNoStepSumOfErpCore(replaceItemSet, noStepItemMap, replaceItemList);
			if (CollectionUtils.isEmpty(replaceItemList)) {
				continue;
			}
			// 替代物料在任务清单中存在多个
			if (replaceItemList.size() > Constant.INT_1) {
				throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.ITEM_OR_REPLACE_ITEM_IN_ERP_TASK_MORE, params);
			}
			if (fixBomSet.contains(replaceItemList.get(0))) {
				throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.FIX_BOM_CAN_NOT_BE_REPLACE, new Object[]{replaceItemList.get(0)});
			}
			snInErpSet.add(sn);
		}
		return snInErpSet;
	}

	private int getStepSumOfErpCore(Set<String> itemNos, Map<String, ItemListEntityDTO> itemNoToErpEntityMap, List<String> replaceItemList) {
		int sum = 0;
		for(String s : itemNos) {
			String itemNo = s;
			if (itemNo.length() >= Constant.INT_12) {
				itemNo = s.substring(0, Constant.INT_12);
			}
			if (itemNoToErpEntityMap.containsKey(itemNo)) {
				// 替代物料需进行非fixBom需要校验
				replaceItemList.add(itemNo);
				sum++;
			}
		}
		return sum;
	}

	private int getNoStepSumOfErpCore(Set<String> itemNos, Map<String, ItemListEntityDTO> itemNoToErpEntityMap, List<String> replaceItemList) {
		int sum = 0;
		for(String s : itemNos) {
            if (itemNoToErpEntityMap.containsKey(s)) {
				// 替代物料需进行非fixBom需要校验
				replaceItemList.add(s);
				sum++;
			}
		}
		return sum;
	}

	private void checkHaveBindRelationNew(ComMachineAssemblyScanDTO comMacAssScanDTO, List<BaItem> seqCodeList, List<BaItem> batchCodeList) {
		// 序列码只能绑定一次在主条码上
		if (!CollectionUtils.isEmpty(seqCodeList)) {
			List<String> barcodeList = seqCodeList.stream().map(BaItem::getSn).collect(Collectors.toList());
			// 根据SN查询WIP_EXTEND_IDENTIFICATION表
			List<WipExtendIdentification> entityList = wipExtendIdentificationRepository.getWipExtendBySnList(barcodeList);
			if (!CollectionUtils.isEmpty(entityList)) {
				String[] params = new String[]{entityList.stream().map(WipExtendIdentification::getSn).collect(Collectors.toList()).toString()};
				throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SUB_BARCODE_HAVE_BIND_RELATION, params);
			}
		}
		//允许同一批次码绑定在不同主条码上，但不允许重复绑在同一主条码
		if (!CollectionUtils.isEmpty(batchCodeList)) {
			List<String> barcodeList = seqCodeList.stream().map(BaItem::getSn).collect(Collectors.toList());
			List<String> formSnList = new ArrayList<>();
			formSnList.add(comMacAssScanDTO.getMainBarcode());
			List<WipExtendIdentification> wipExtendIdentificationList = wipExtendIdentificationRepository.getWipExtendBySnAndFormSn(barcodeList, formSnList);
			if (!CollectionUtils.isEmpty(wipExtendIdentificationList)) {
				String[] params = new String[]{wipExtendIdentificationList.stream().map(WipExtendIdentification::getSn).collect(Collectors.toList()).toString(), comMacAssScanDTO.getMainBarcode()};
				throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SUB_BARCODE_HAVE_BOUND_IN_MAIN_BARCODE, params);
			}
		}
	}

	/**
	 *<AUTHOR>
	 * 如果wipinfo存在，则为单板条码，后续料单代码匹配使用12位
	 *@Date 2025/5/12 16:58
	 *@Param [java.util.List<java.lang.String>]
	 *@return
	 **/
	private void checkProcessOfSubSnNew(ComMachineAssemblyScanDTO comMacAssScanDTO, List<String> subBarcodeList) {
		List<PsWipInfo> wipInfoBySn = psWipInfoService.getWipInfoBySnList(subBarcodeList);
		Map<String, PsWipInfo> wipMap = wipInfoBySn.stream().collect(Collectors.toMap(PsWipInfo::getSn, e -> e));
		List<String> stepSnList = new ArrayList<>();
		List<String> noStepSnList = new ArrayList<>();
		for (String barcode : subBarcodeList) {
			PsWipInfo wipInfo = wipMap.get(barcode);
			if (wipInfo == null) {
				noStepSnList.add(barcode);
				continue;
			}
			// 219开头的条码不做校验, 直接返回查的wip_info
			if (barcode.startsWith(Constant.STR_219)) {
				stepSnList.add(barcode);
				continue;
			}
			// 子条码的主工序必须为入库，不为入库，报错
			if (!Constant.WAREHOUSE_ENTRY.equals(wipInfo.getCraftSection())) {
				String[] params = new String[]{barcode};
				throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SUB_BARCODE_CRAFT_SECTION_ILLEGAL, params);
			}
			stepSnList.add(barcode);
		}
		comMacAssScanDTO.setStepSnList(stepSnList);
		comMacAssScanDTO.setNoStepSnList(noStepSnList);
	}

	/**
	 *<AUTHOR>
	 * 是否批次码
	 *@Date 2025/5/18 11:56
	 *@Param [com.zte.interfaces.dto.ComMachineAssemblyScanDTO, java.util.List<com.zte.domain.model.BaItem>]
	 *@return
	 **/
	private boolean batchCodeUsageCheck (ComMachineAssemblyScanDTO comMacAssScanDTO, List<BaItem> baItemInfoList) {
		if (baItemInfoList.size() > Constant.INT_1) {
			return true;
		}
		if (comMacAssScanDTO.isNeedUsage()) {
			return true;
		}
		if (comMacAssScanDTO.getBatchCodeQty() != null && comMacAssScanDTO.getBatchCodeQty().compareTo(BigDecimal.ZERO) > 0) {
			return true;
		}
        return Constant.TYPE_SEQUENCE_CODE.equals(baItemInfoList.get(NumConstant.NUM_ZERO).getBarCodeType());
    }

	/**
	 * 验证条码是否与已绑定条码类型一致
	 *
	 * @param comMacAssScanDTO
	 * @param barcodeExpandDTOList
	 */
	private void checkSubSnCategoryNew (ComMachineAssemblyScanDTO comMacAssScanDTO, List<BarcodeExpandDTO> barcodeExpandDTOList, PsWipInfo mainSnWipInfo) throws Exception {
		if (barcodeExpandDTOList.isEmpty()) {
			return;
		}
		if (mainSnWipInfo == null) {
			String[] params = new String[]{comMacAssScanDTO.getMainBarcode()};
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.MAIN_BARCODE_NOT_HAVE_WIP_INFO, params);
		}
		Map<String, SysLookupValuesDTO> leadMap = getLeadList();
		SysLookupValuesDTO lookupValuesOfMainSn = leadMap.get(mainSnWipInfo.getAttribute3());
		if (lookupValuesOfMainSn == null) {
			String[] params = new String[]{comMacAssScanDTO.getMainBarcode(), mainSnWipInfo.getAttribute3()};
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.ISLEAD_OF_MAIN_SN_NOT_FIND_DICTIONARY, params);
		}
		Map<String, Object> param = new HashMap<>();
		// BarcodeExpandDTO barcodeExpandDTO = barcodeExpandDTOList.get(NumConstant.NUM_ZERO);
		// param.put("itemNo", barcodeExpandDTO.getItemCode());
		param.put("formSn", comMacAssScanDTO.getMainBarcode());
		param.put("formType", NumConstant.STR_TWO);
		List<String> deviceSnList = new ArrayList<>();
		List<String> deviceItemList = new ArrayList<>();
		Map<String, String> snProductCodeMap = new HashMap<>();
		List<WipExtendIdentification> existList = wipExtendIdentificationRepository.getList(param);
		for (BarcodeExpandDTO expandDTO : barcodeExpandDTOList) {
			this.checkItemOfSSP(comMacAssScanDTO.getSubBarcode(), expandDTO);
			this.checkLeadFlagNew(comMacAssScanDTO, expandDTO, leadMap, mainSnWipInfo, lookupValuesOfMainSn);
			List<String> existCategoryList = existList.stream().filter(e -> StringUtils.isNotEmpty(e.getCategoryCode()) && StringUtils.equals(expandDTO.getItemCode(), e.getItemNo()))
					.map(WipExtendIdentification::getCategoryCode).distinct().collect(Collectors.toList());
			if (Constant.ZJ_CATEGORY_CODE.equals(expandDTO.getCategoryCode())) {
				deviceSnList.add(expandDTO.getBarcode());
				deviceItemList.add(expandDTO.getItemCode());
				snProductCodeMap.put(expandDTO.getBarcode(), expandDTO.getItemCode());
			}
			if (CollectionUtils.isEmpty(existCategoryList)) {
				continue;
			}
			if (!existCategoryList.contains(expandDTO.getParentCategoryCode())) {
				throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.BARCODE_CATEGORY_NOT_IN_BIND_LIST, new Object[]{expandDTO.getBarcode(), expandDTO.getParentCategoryName()});
			}
		}
		this.subDeviceSnBindCheck(snProductCodeMap, deviceSnList, deviceItemList);

	}

	/**
	 *<AUTHOR>
	 * 整机子条码绑定校验
	 *@Date 2025/5/17 9:45
	 *@Param snProductCodeMap, deviceSnList, deviceItemList
	 *@return
	 **/
	private void subDeviceSnBindCheck (Map<String, String> snProductCodeMap, List<String> deviceSnList, List<String> deviceItemList) {
		// 无整机条码不处理
		if (CollectionUtils.isEmpty(deviceSnList)) {
			return;
		}
		ProdBindingSettingDTO settingDTO = new ProdBindingSettingDTO();
		settingDTO.setProductCodeList(deviceItemList);
		// 获取物料绑定清单
		List<ProdBindingSetting> settingList = prodBindingSettingRepository.queryList(settingDTO);
		if (CollectionUtils.isEmpty(settingList)) {
			return;
		}
		// 批量获取条码绑定信息
		List<WipExtendIdentification> wipExtendList = wipExtendIdentificationRepository.queryDeviceBindingQtyBatch(deviceSnList);
		if (CollectionUtils.isEmpty(wipExtendList)) {
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.NO_BIND_INFO_SN, new Object[]{deviceSnList.toString()});
		}
		for (Map.Entry<String, String> stringEntry : snProductCodeMap.entrySet()) {
			List<ProdBindingSetting> bindingSettings = settingList.stream().filter(e -> StringUtils.equals(stringEntry.getValue(), e.getProductCode())).collect(Collectors.toList());
			List<WipExtendIdentification> identifications = wipExtendList.stream().filter(e -> StringUtils.equals(stringEntry.getKey(), e.getFormSn())).collect(Collectors.toList());
			// 条码无绑定记录报错
			if (CollectionUtils.isEmpty(identifications)) {
				throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.NO_BIND_INFO_SN, new Object[]{stringEntry.getKey()});
			}
			// 物料已绑数量
			Map<String, BigDecimal> itemFormQty = identifications.stream().collect(Collectors.toMap(WipExtendIdentification::getItemNo, WipExtendIdentification::getFormQty));
			for (ProdBindingSetting bindingSetting : bindingSettings) {
				BigDecimal bindQty = itemFormQty.get(bindingSetting.getItemCode());
				// 物料未绑定或绑定数量不一致报错
				if (bindQty == null || !Objects.equals(bindQty, bindingSetting.getUsageCount())) {
					throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.MATERIAL_BIND_NO_END, new Object[]{stringEntry.getKey(), bindingSetting.getItemCode()});
				}
			}
		}
	}

	private void checkLeadFlagNew (ComMachineAssemblyScanDTO comMacAssScanDTO, BarcodeExpandDTO barcodeExpandDTO,
								   Map<String, SysLookupValuesDTO> leadMap, PsWipInfo mainSnWipInfo, SysLookupValuesDTO lookupValuesOfMainSn) throws Exception {
		//主条码环保属性
		//条码中心对应环保属性
		SysLookupValuesDTO sysLookupValuesOfSubSn = leadMap.get(barcodeExpandDTO.getIsLead());
		if (sysLookupValuesOfSubSn == null) {
			String[] params = new String[]{comMacAssScanDTO.getSubBarcode(), barcodeExpandDTO.getIsLead()};
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.ISLEAD_OF_SUB_SN_NOT_FIND_DICTIONARY, params);
		}
		//环保属性校验
		if (new BigDecimal(sysLookupValuesOfSubSn.getAttribute1())
				.compareTo(new BigDecimal(lookupValuesOfMainSn.getAttribute1())) < NumConstant.NUM_ZERO) {
			String[] params = new String[]{barcodeExpandDTO.getIsLead(), mainSnWipInfo.getAttribute3()};
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.LEAD_FLAG_OF_SUB_SN_ILLEGAL, params);
		}
	}

	/**
	 * @Description: 校验条码是否来源于条码中心。
	 * 如果条码不在条码中心，则转为材料代码，如果在，则设置相关信息。
	 * 都不在则报错。
	 * @Param: [comMacAssScanDTO, baItemInfoList, barcodeExpandList]
	 * @return: void
	 * @Author: Saber[10307315]
	 * @Date: 2023/6/9 上午10:14
	 */
	private void checkSubSnSourceNew (ComMachineAssemblyScanDTO comMacAssScanDTO, List<BaItem> baItemInfoList, Map<String, BarcodeExpandDTO> barcodeMap) throws Exception {
		List<String> notExistsList = new ArrayList<>();
		for (String barcode : comMacAssScanDTO.getSubBarcodeList()) {
			BarcodeExpandDTO expandDTO = barcodeMap.get(barcode);
			if (expandDTO == null) {
				notExistsList.add(barcode);
				continue;
			}
			BaItem baItemInfo = new BaItem();
			baItemInfo.setFromBarCodeCenter(true);
			baItemInfo.setSn(expandDTO.getBarcode());
			baItemInfo.setItemNo(expandDTO.getItemCode());
			baItemInfo.setItemName(expandDTO.getItemName());
			//条码大类
			baItemInfo.setBarCodeType(expandDTO.getParentCategoryName());
			baItemInfo.setParentCategoryCode(expandDTO.getParentCategoryCode());
			baItemInfoList.add(baItemInfo);
		}
		// 条码中心无的，使用物料代码转材料代码，并记录日志
		if (!CollectionUtils.isEmpty(notExistsList)) {
			AssemblyRelaScanDTO dto = new AssemblyRelaScanDTO();
			dto.setSubSnList(notExistsList);
			dto.setItemCode(comMacAssScanDTO.getItemCode());
			List<BaItem> item = getItemCode(dto);
			if (!CollectionUtils.isEmpty(item)) {
				baItemInfoList.addAll(item);
			}
			// 不是条码中心查询到的条码数据需要写日志
			dto.setMainSn(comMacAssScanDTO.getMainBarcode());
			dto.setCreateBy(comMacAssScanDTO.getEmpNo());
			insertAssemblyRelaScanRecordInfo(dto, item);
		}
		// 未找到物料信息
		if (CollectionUtils.isEmpty(baItemInfoList)) {
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.FAILED_TO_GET_SN_INFO);
		}
		// 过滤出未获取到的条码
		List<String> snList = baItemInfoList.stream().map(BaItem::getSn).collect(Collectors.toList());
		notExistsList = notExistsList.stream().filter(e -> !snList.contains(e)).collect(Collectors.toList());
		// 未找到物料信息
		if (!CollectionUtils.isEmpty(notExistsList)) {
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.FAILED_TO_GET_SN_INFO);
		}
		List<BaItem> errList = baItemInfoList.stream().filter(e -> StringUtils.isNotEmpty(e.getErrMsg())).collect(Collectors.toList());
		if (!CollectionUtils.isEmpty(errList)) {
			String[] params = new String[]{errList.get(NumConstant.NUM_ZERO).getErrMsg()};
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.ITEM_INFO_NOT_FOUND, params);
		}
	}
/*


	*/
/**
	 *<AUTHOR>
	 * 校验提交
	 *@Date 2025/5/9 9:28
	 *@Param [com.zte.interfaces.dto.ComMachineAssemblyScanDTO]
	 *@return
	 **//*

	private ComMachineAssemblyScanDTO checkAndSubmit (ComMachineAssemblyScanDTO comMacAssScanDTO) throws Exception{
		if(comMacAssScanDTO.isAutoSubmitWarehouse()&&null == comMacAssScanDTO.getMixWarehouseSubmit()){
			throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE,MessageId.SUBMIT_WAREHOUSE_IS_NULL);
		}
		List<BaItem> itemInfoList = new ArrayList<>();
		// 查条码中心，区分是否批次码
		List<BarcodeExpandDTO> barcodeExpandDTOList = getBarCodeBySubSn(comMacAssScanDTO.getSubBarcode());
		// 校验条码的来源是否是条码中心，如果不是，转化为材料代码，如果无法转化，则报错，无次条码信息。
		checkSubSnSource(comMacAssScanDTO, itemInfoList, barcodeExpandDTOList);
		// 验证条码是否与已绑定条码类型一致
		checkSubSnCategory(comMacAssScanDTO.getMainBarcode(), barcodeExpandDTOList);
		// baItem用于记录子条码物料的基本信息。会在各种校验中保存信息。
		BaItem baItem = itemInfoList.get(NumConstant.NUM_ZERO);
		String barCodeType = baItem.getBarCodeType();
		// 如果是批次码，要求返回前端输入数量，如果已经输入了数量则跳过，如果已经勾选了取需求量也跳过
		if (!comMacAssScanDTO.isNeedUsage() && ((comMacAssScanDTO.getBatchCodeQty() == null || comMacAssScanDTO.getBatchCodeQty().compareTo(BigDecimal.ZERO) <= 0) && !Constant.TYPE_SEQUENCE_CODE.equals(barCodeType))) {
			ComMachineAssemblyScanDTO returnEntity = new ComMachineAssemblyScanDTO();
			returnEntity.setBatchCodeQty(new BigDecimal(Constant.INT_NEGATIVE_1));
			return returnEntity;
		}
		// 校验环保属性，和SSP物料。只有在条码中心存在该条码才校验
		if (!CollectionUtils.isEmpty(barcodeExpandDTOList)) {
			checkItemOfSSP(comMacAssScanDTO.getSubBarcode(), barcodeExpandDTOList.get(0));
			Map<String, SysLookupValuesDTO> leadMap = getLeadList();
			//主条码环保属性
			PsWipInfo psWipInfoOfMainSnEntity = psWipInfoService.getWipInfoBySn(comMacAssScanDTO.getMainBarcode());
			checkLeadFlag(leadMap, psWipInfoOfMainSnEntity, comMacAssScanDTO, barcodeExpandDTOList);
		}

		// 校验子条码如果在wipinfo存在，则必须主工序为入库
		PsWipInfo psWipInfoOfSub = checkProcessOfSubSn(comMacAssScanDTO.getSubBarcode());
		// 如果wipinfo存在，则为单板条码，后续料单代码匹配使用12位
		if (psWipInfoOfSub != null) {
			comMacAssScanDTO.setSubBarcodeStepFlag(true);
		}
		// 2.查看数据字典是否需要查询在绑定清单中，如需要则校验子条码物料代码（包含替代物料）是否在绑定清单中。
		SysLookupTypesDTO dto = BasicsettingRemoteService.getSysLookUpValue
				(Constant.LOOKUP_TYPE_1003018, Constant.LOOK_UP_CODE_1003018001);
		boolean needCheckBindSetting = dto != null && Constant.FLAG_Y.equals(dto.getLookupMeaning());
		// 必须校验绑定清单
		// 主子条码的绑定关系(任务清单/绑定清单)和用量,并返回已经绑定后，绑定清单的已绑信息，用于是否绑定完成
		PsWipInfo wipInfoByMainSn = psWipInfoService.getWipInfoBySn(comMacAssScanDTO.getMainBarcode());
		List<ProdBindingSettingDTO> bindingInfoList = checkBindRelationAndQty(comMacAssScanDTO, barCodeType, baItem, needCheckBindSetting, wipInfoByMainSn);
		// 得到需要进行技改和锁定单校验的条码表
		List<String> needCheckTechSns = getNeedCheckSns(comMacAssScanDTO);
		// 校验技改管控（包括MES锁定）,返回方法过程查询的子工序，用于流程管控
		String craftSection = checkTechChangeControl(comMacAssScanDTO, needCheckTechSns);
		// 校验锁定单信息
		checkLockOrderControl(needCheckTechSns);
		// 处理返回前端数据，并判断跳转总数量，是否满足跳转到主条码
		ComMachineAssemblyScanDTO result = handlerReturnEntityAndSkip(comMacAssScanDTO, bindingInfoList, baItem, needCheckBindSetting);
		result.setNeedUsage(comMacAssScanDTO.isNeedUsage());
		// 处理过站和绑定等写表操作，使用同一事务。
		// assemblyRelaScanService.handlerBindAndPassStation(comMacAssScanDTO, baItem, bindingInfoList, craftSection, null);
		// 如果成功处理了过站，则设置标志位
		result.setPassStationSucFlag(comMacAssScanDTO.isPassStationSucFlag());
		// 如果绑定清单全部绑定完成，则设置标志位
		result.setFinishBindFlag(comMacAssScanDTO.isFinishBindFlag());
		result.setSubmitWarehouseEnableFlag(comMacAssScanDTO.isSubmitWarehouseEnableFlag());
		// 如果完成入库，则设置标志位
		result.setSubmitWarehouseSucFlag(comMacAssScanDTO.isSubmitWarehouseSucFlag());
		result.setErrMsg(comMacAssScanDTO.getErrMsg());
		result.setNeedCheckBindSetting(needCheckBindSetting);
		result.setAutoSubmitWarehouse(comMacAssScanDTO.isAutoSubmitWarehouse());
		return result;
	}
*/

	/**
	 * 验证条码是否与已绑定条码类型一致
	 *
	 * @param mainBarcode
	 * @param barcodeExpandDTOList
	 */
	private void checkSubSnCategory(String mainBarcode, List<BarcodeExpandDTO> barcodeExpandDTOList) {
		if (barcodeExpandDTOList.isEmpty()) {
			return;
		}
		Map<String, Object> param = new HashMap<>();
		BarcodeExpandDTO barcodeExpandDTO = barcodeExpandDTOList.get(NumConstant.NUM_ZERO);
		param.put("formSn", mainBarcode);
		param.put("itemNo", barcodeExpandDTO.getItemCode());
		param.put("formType", NumConstant.STR_TWO);
		List<WipExtendIdentification> existList = wipExtendIdentificationRepository.getList(param);
		List<String> existCategoryList = existList.stream().map(WipExtendIdentification::getCategoryCode).filter(Objects::nonNull).distinct().collect(Collectors.toList());
		if (!existCategoryList.isEmpty()) {
			List<String> categoryList = existCategoryList.stream().filter(item -> item.equals(barcodeExpandDTO.getParentCategoryCode())).collect(Collectors.toList());
			if (categoryList.isEmpty()) {
				throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.BARCODE_CATEGORY_NOT_IN_BIND_LIST, new Object[]{barcodeExpandDTO.getBarcode(), barcodeExpandDTO.getParentCategoryName()});
			}
		}
	}

	private ComMachineAssemblyScanDTO handlerReturnEntityAndSkip(ComMachineAssemblyScanDTO comMacAssScanDTO,
																 List<ProdBindingSettingDTO> bindingInfoList,
																 BaItem snItemInfo, boolean needCheckBindSetting) {
		ComMachineAssemblyScanDTO result = new ComMachineAssemblyScanDTO();
		BigDecimal bindingQty = new BigDecimal(Constant.INT_1);
		// 计算本次绑定用量，序列码默认为1，非序列码的从前端输入取
		if (!Constant.TYPE_SEQUENCE_CODE.equals(snItemInfo.getBarCodeType())) {
			bindingQty = comMacAssScanDTO.getBatchCodeQty();
		}
		result.setSnItemInfo(snItemInfo);
		result.setBindingQty(bindingQty);
		result.setSateSkipFlag(false);
		result.setBindingInfo(bindingInfoList);
		if (CollectionUtils.isEmpty(bindingInfoList)) {
			return result;
		}
		// 如果未输入跳转数量，则直接返回
		if (comMacAssScanDTO.getSkipTotalQty() == null || comMacAssScanDTO.getSkipTotalQty() <= 0) {
			return result;
		}
		// 计算主条码已经绑定完的数量
		BigDecimal boundQtySum = new BigDecimal(Constant.STR_0);
		// 如果是必须在绑定清单中，则直接用绑定清单信息来统计已经绑定的数量，否则，应该直接查询绑定关系记录表来统计，因为包含在ERP清单但不在绑定清单的子条码绑定。
		if (needCheckBindSetting) {
			boundQtySum = getBoundQtyByProdSetting(bindingInfoList);
		} else {
			boundQtySum = getBoundQtyByWipExtend(comMacAssScanDTO, bindingQty);
		}
		// 当前主条码绑定的子条码数量等于界面设置的跳转数量且必绑清单绑定完成（若字典开启）时,可跳转
		if (boundQtySum.intValue() >= comMacAssScanDTO.getSkipTotalQty()) {
			result.setSateSkipFlag(true);
			comMacAssScanDTO.setSateSkipFlag(true);
		}
		return result;
	}

	private BigDecimal getBoundQtyByWipExtend(ComMachineAssemblyScanDTO comMacAssScanDTO, BigDecimal bindingQty) {
		BigDecimal boundQtySum = new BigDecimal(Constant.STR_0);
		WipExtendIdentificationDTO params = new WipExtendIdentificationDTO();
		params.setFormSn(comMacAssScanDTO.getMainBarcode());
		params.setProcessCode(comMacAssScanDTO.getProcessCode());
		if (comMacAssScanDTO.isWorkStationBindFlag()) {
			params.setWorkStation(comMacAssScanDTO.getWorkStation());
		}
		Integer boundQty = wipExtendIdentificationRepository.getUsageCount(params);
		boundQtySum = boundQty == null ? boundQtySum : new BigDecimal(boundQty.toString());
		// 加上本次绑定未提交的数量
		boundQtySum = boundQtySum.add(bindingQty);
		return boundQtySum;
	}

	private BigDecimal getBoundQtyByProdSetting(List<ProdBindingSettingDTO> bindingInfoList) {
		BigDecimal boundQtySum = new BigDecimal(Constant.STR_0);
		for (int i = 0; i < bindingInfoList.size(); i++) {
			ProdBindingSettingDTO entity = bindingInfoList.get(i);
			if (entity == null) {
				continue;
			}
			BigDecimal boundQty = entity.getBindedCount() == null ? new BigDecimal(Constant.STR_0) : entity.getBindedCount();
			boundQtySum = boundQtySum.add(boundQty);
		}
		return boundQtySum;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void handlerBindAndPassStation(ComMachineAssemblyScanDTO comMacAssScanDTO, List<BaItem> baItemList,
										  List<ProdBindingSettingDTO> bindingInfoList, String craftSection, PsWipInfo mainSnWipInfo) throws Exception {
		// 写绑定表
		handlerSaveBindRelation(comMacAssScanDTO, baItemList,  mainSnWipInfo);
		// 查看是否绑定完成
		boolean bindFinish = checkIfFinishBind(bindingInfoList);
		comMacAssScanDTO.setFinishBindFlag(bindFinish);
		// 如果满足自动过站，则流程管控校验，通过后，操作过站.注意设置了跳转总数量，则未到跳转总数，不过站
		boolean canSkipFlag = (comMacAssScanDTO.getSkipTotalQty() == null || comMacAssScanDTO.getSkipTotalQty() <= 0)
				|| (comMacAssScanDTO.isSateSkipFlag());
		if (comMacAssScanDTO.isIfPassStationSwitch() && bindFinish && canSkipFlag) {
			handlerPassStation(comMacAssScanDTO, craftSection);
			// 判定是否过站成功，且自动入库打开
			if (comMacAssScanDTO.isAutoSubmitWarehouse()) {
				List<PsWipInfo> wipInfoList = psWipInfoService.getListByBatchSn(comMacAssScanDTO.getMixWarehouseSubmit().getWipSns());
                if (!Constant.FLAG_Y.equals(wipInfoList.get(NumConstant.NUM_ZERO).getLastProcess())) {
					comMacAssScanDTO.setSubmitWarehouseEnableFlag(false);
					return;
                }
				comMacAssScanDTO.setSubmitWarehouseEnableFlag(true);
			}
		}
	}

	private void handlerSaveBindRelation(ComMachineAssemblyScanDTO comMacAssScanDTO, List<BaItem> baItemList, PsWipInfo mainSnWipInfo) throws Exception{
		List<WipExtendIdentification> extendInfoList = new ArrayList<>();
		for (BaItem baItem : baItemList) {
			WipExtendIdentification extendInfo = new WipExtendIdentification();
			extendInfo.setCategoryCode(baItem.getParentCategoryCode());
			extendInfo.setIdentiId(java.util.UUID.randomUUID().toString());
			extendInfo.setSn(baItem.getSn());
			if (!StringUtils.equals(comMacAssScanDTO.getSubBarcode(), baItem.getSn())) {
				extendInfo.setMasterSn(comMacAssScanDTO.getSubBarcode());
			}
			extendInfo.setFormSn(comMacAssScanDTO.getMainBarcode());
			extendInfo.setMainProductCode(comMacAssScanDTO.getItemCode());
			extendInfo.setProcessCode(comMacAssScanDTO.getProcessCode());
			// 如果绑定到工站维度，则设置工站
			if (comMacAssScanDTO.isWorkStationBindFlag()) {
				extendInfo.setWorkStation(comMacAssScanDTO.getWorkStation());
			}
			extendInfo.setFormQty(baItem.getBindQty());
			/*if (comMacAssScanDTO.getBatchCodeQty() == null || comMacAssScanDTO.getBatchCodeQty().compareTo(BigDecimal.ZERO) <= 0) {
				extendInfo.setFormQty(new BigDecimal(Constant.INT_1));
			} else {
				extendInfo.setFormQty(new BigDecimal(comMacAssScanDTO.getBatchCodeQty().toString()));
			}*/
			//设置替代物料
			extendInfo.setReplaceItemNo(baItem.getReplaceItemNo());
			// 设置的部分信息
			baItem.setScanType(baItem.getBarCodeType());
			this.setExtendInfo(extendInfo, baItem);
			// 设置批次信息
			// PsWipInfo wipInfoOfMainSn = psWipInfoService.getWipInfoBySn(comMacAssScanDTO.getMainBarcode());
			if (mainSnWipInfo != null) {
				extendInfo.setProdPlanId(mainSnWipInfo.getAttribute1());
			}
			extendInfo.setTaskNo(comMacAssScanDTO.getStandardModelTask());
			extendInfo.setLastUpdatedBy(comMacAssScanDTO.getEmpNo());
			extendInfo.setCreateBy(comMacAssScanDTO.getEmpNo());
			extendInfo.setFactoryId(new BigDecimal(comMacAssScanDTO.getFactoryId()));
			extendInfo.setIsZsIdentification(Constant.FLAG_N);
			extendInfoList.add(extendInfo);
		}
		wipExtendIdentificationService.batchInsertOptRecord(NumConstant.STR_ONE,extendInfoList);
		wipExtendIdentificationRepository.insertWipExtendBatch(extendInfoList);
		// wipExtendIdentificationService.insertWipExtendIdentification(extendInfo);
	}

	private void handlerPassStation(ComMachineAssemblyScanDTO comMacAssScanDTO, String craftSection) throws Exception {
		// 调用流程管控校验
		AssemblyRelaScanDTO params = new AssemblyRelaScanDTO();
		params.setProcessCode(comMacAssScanDTO.getProcessCode());
		params.setWorkOrderNo(comMacAssScanDTO.getWorkOrderNo());
		params.setWorkStationCode(comMacAssScanDTO.getWorkStation());
		params.setMainSn(comMacAssScanDTO.getMainBarcode());
		params.setLineCode(comMacAssScanDTO.getLineCode());
		params.setFactoryId(new BigDecimal(comMacAssScanDTO.getFactoryId()));
		params.setItemCode(comMacAssScanDTO.getItemCode());
		if (StringUtils.isEmpty(craftSection)) {
			List<BSProcess> listProcess = getProcessInfoByCode(params);
			if (CollectionUtils.isEmpty(listProcess)) {
				throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PROCESS_DETAILS_NOT_FOUND);
			}
			craftSection = listProcess.get(0).getCraftSection();
		}
		params.setCraftSection(craftSection);
		FlowControlInfoDTO checkFlowResult = checkFlow(params);
		checkFlowResult.setCreateBy(comMacAssScanDTO.getEmpNo());
		checkFlowResult.setLastUpdatedBy(comMacAssScanDTO.getEmpNo());
		if (Constant.FAIL.equals(checkFlowResult.getResultType())) {
			String[] param = new String[]{checkFlowResult.getErrorMessage()};
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.MAIN_BARCODE_FLOW_CONTROL_NOT_PASS, param);
		}
		// 如果通过，则处理过站
		// 获取指令信息
		List<PsWorkOrderDTO> workOrderInfoList = getWorkOrderInfo(params);
		if (CollectionUtils.isEmpty(workOrderInfoList)) {
			String[] param = new String[]{comMacAssScanDTO.getMainBarcode()};
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.MAIN_BARCODE_NOT_HAVE_WORK_ORDER_NO, param);
		}
		PsWorkOrderDTO workOrder = workOrderInfoList.get(0);
		params.setMainWorkOrder(workOrder);
		params.setToPassWorkStaion(true);
		RetCode ret = dealAllBinded(params, checkFlowResult);
		// 判断是否过站成功
		if (!Constant.BINDED_AND_PASSWORKSTATION_COMPLETE.equals(ret.getMsg())) {
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, ret.getMsg());
		}
		// 成功过站，记录标志位
		comMacAssScanDTO.setPassStationSucFlag(true);
	}

	private void checkLockOrderControl(List<String> needCheckTechSns) {
		// 也需校验相关条码的批次是否锁定，因此查询批次
		List<PsWipInfo> psWipInfos = psWipInfoService.queryWipSnBatch(needCheckTechSns);
		for (PsWipInfo entity : psWipInfos) {
			if (StringUtils.isEmpty(entity.getAttribute1())) {
				continue;
			}
			needCheckTechSns.add(entity.getAttribute1());
		}
		List<BarcodeLockDetail> lockDetailList = barcodeLockDetailRepository.queryLockDetail(needCheckTechSns, null);
		Set<String> snSet = new HashSet<>();
		Set<String> prodSet = new HashSet<>();
		for (BarcodeLockDetail entity : lockDetailList) {
			if (Constant.LOCK_TYPE_SN.equals(entity.getType())) {
				snSet.add(entity.getBatchSn());
			}
			if (Constant.LOCK_TYPE_BATCH.equals(entity.getType())) {
				prodSet.add(entity.getBatchSn());
			}
		}
		if (!CollectionUtils.isEmpty(snSet) || !CollectionUtils.isEmpty(prodSet)) {
			String[] param = new String[] {snSet.toString(), prodSet.toString()};
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SN_LOCK_NOT_ALLOW_BIND, param);
		}
	}

	private List<String> getNeedCheckSns(ComMachineAssemblyScanDTO comMacAssScanDTO) {
		String subSn = comMacAssScanDTO.getSubBarcode();
		String mainSn = comMacAssScanDTO.getMainBarcode();
		List<String> needCheckTechSns = new ArrayList<>();
		needCheckTechSns.add(mainSn);
		needCheckTechSns.add(subSn);
		// 查询子条码下的所有关联条码
		List<WipExtendIdentification> allChildProdBinding = wipExtendIdentificationRepository.getAllChildProdBinding(subSn);
		for (WipExtendIdentification entity : allChildProdBinding) {
			needCheckTechSns.add(entity.getSn());
		}
		return needCheckTechSns;
	}

	private String checkTechChangeControl(ComMachineAssemblyScanDTO comMacAssScanDTO, List<String> needCheckTechSns) throws Exception {
		if (!isCheck(Constant.SYS_LOOK_CHECK_TECHINAL)) {
			return Constant.STRING_EMPTY;
		}
		// 根据子工序得到对应的主工序
		AssemblyRelaScanDTO params = new AssemblyRelaScanDTO();
		params.setProcessCode(comMacAssScanDTO.getProcessCode());
		List<BSProcess> processList = getProcessInfoByCode(params);
		if (CollectionUtils.isEmpty(processList)) {
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PROCESS_DETAILS_NOT_FOUND);
		}
		String craftSection = processList.get(0).getCraftSection();
		// 察看是否技改
		String techCheckResult = standardModeCommonScanService.getTechnicalChangeBarcode(needCheckTechSns,craftSection);
		if (StringUtils.isNotEmpty(techCheckResult)) {
			String[] param = new String[] {techCheckResult};
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SN_TECHNICAL_CHANGE, param);
		}
		return craftSection;
	}

	/**
	* @Description: 校验条码是否来源于条码中心。
	 * 如果条码不在条码中心，则转为材料代码，如果在，则设置相关信息。
	 * 都不在则报错。
	* @Param: [comMacAssScanDTO, baItemInfoList, barcodeExpandList]
	* @return: void
	* @Author: Saber[10307315]
	* @Date: 2023/6/9 上午10:14
	*/
	private void checkSubSnSource(ComMachineAssemblyScanDTO comMacAssScanDTO, List<BaItem> baItemInfoList, List<BarcodeExpandDTO> barcodeExpandList) throws Exception {
		// 条码中心无的，使用物料代码转材料代码，并记录日志
		if (CollectionUtils.isEmpty(barcodeExpandList)) {
			AssemblyRelaScanDTO dto = new AssemblyRelaScanDTO();
			List<String> snList = new ArrayList<>();
			snList.add(comMacAssScanDTO.getSubBarcode());
			dto.setSubSnList(snList);
			dto.setItemCode(comMacAssScanDTO.getItemCode());
			List<BaItem> item = getItemCode(dto);
			if (!CollectionUtils.isEmpty(item)) {
				baItemInfoList.addAll(item);
			}
			// 不是条码中心查询到的条码数据需要写日志
			dto.setMainSn(comMacAssScanDTO.getMainBarcode());
			dto.setCreateBy(comMacAssScanDTO.getEmpNo());
			insertAssemblyRelaScanRecordInfo(dto, item);
		} else {
			BarcodeExpandDTO barcodeExpandInfo = barcodeExpandList.get(NumConstant.NUM_ZERO);
			BaItem baItemInfo = new BaItem();
			baItemInfo.setFromBarCodeCenter(true);
			baItemInfo.setSn(barcodeExpandInfo.getBarcode());
			baItemInfo.setItemNo(barcodeExpandInfo.getItemCode());
			baItemInfo.setItemName(barcodeExpandInfo.getItemName());
			//条码大类
			baItemInfo.setBarCodeType(barcodeExpandInfo.getParentCategoryName());
			baItemInfoList.add(baItemInfo);
		}
		// 未找到物料信息
		if (CollectionUtils.isEmpty(baItemInfoList)) {
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.FAILED_TO_GET_SN_INFO);
		}
		if (StringUtils.isNotBlank(baItemInfoList.get(NumConstant.NUM_ZERO).getErrMsg())) {
			String[] params = new String[]{baItemInfoList.get(NumConstant.NUM_ZERO).getErrMsg()};
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.ITEM_INFO_NOT_FOUND, params);
		}
	}

	/*private void checkLeadFlag(Map<String, SysLookupValuesDTO> leadMap, PsWipInfo psWipInfoOfMainSnEntity,
							   ComMachineAssemblyScanDTO comMacAssScanDTO, List<BarcodeExpandDTO> barcodeExpandDTOList) throws Exception {
		SysLookupValuesDTO lookupValuesOfMainSn = leadMap.get(psWipInfoOfMainSnEntity.getAttribute3());
		for (BarcodeExpandDTO expandDTO : barcodeExpandDTOList) {
			if (Constant.TYPE_SEQUENCE_CODE.equals(expandDTO.getParentCategoryName()) &&
					(expandDTO.getQuantity() == null || expandDTO.getQuantity().intValue() == NumConstant.NUM_ZERO)
					&& StringUtils.isNotEmpty(expandDTO.getRelatedSnBarcode())) {
				String[] params = new String[]{expandDTO.getBarcode()};
				throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.ITEM_OF_SSP_HAVE_RELATED_SN, params);
			}
			//条码中心对应环保属性
			SysLookupValuesDTO sysLookupValuesOfSubSn = leadMap.get(expandDTO.getIsLead());
			if (sysLookupValuesOfSubSn == null) {
				String[] params = new String[]{expandDTO.getBarcode(), expandDTO.getIsLead()};
				throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.ISLEAD_OF_SUB_SN_NOT_FIND_DICTIONARY, params);
			}
			if (lookupValuesOfMainSn == null) {
				String[] params = new String[]{comMacAssScanDTO.getMainBarcode(), psWipInfoOfMainSnEntity.getAttribute3()};
				throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.ISLEAD_OF_MAIN_SN_NOT_FIND_DICTIONARY, params);
			}
			//环保属性校验
			if (new BigDecimal(sysLookupValuesOfSubSn.getAttribute1())
					.compareTo(new BigDecimal(lookupValuesOfMainSn.getAttribute1())) < NumConstant.NUM_ZERO) {
				String[] params = new String[]{expandDTO.getIsLead(), psWipInfoOfMainSnEntity.getAttribute3()};
				throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.LEAD_FLAG_OF_SUB_SN_ILLEGAL, params);
			}
		}
	}*/

	private void checkItemOfSSP(String subBarcode, BarcodeExpandDTO barcodeExpandDTO) {
		// 扫描子条码时若其条码大类为序列码（parentCategoryName），
		// 数量为0，关联条码为空（related_sn_barcode），
		// 则判断为已生成我司序列码的供应商序列码
		if (Constant.TYPE_SEQUENCE_CODE.equals(barcodeExpandDTO.getParentCategoryName()) &&
				(barcodeExpandDTO.getQuantity() == null || barcodeExpandDTO.getQuantity().intValue() == NumConstant.NUM_ZERO)
				&& StringUtils.isNotEmpty(barcodeExpandDTO.getRelatedSnBarcode())) {
			String[] params = new String[]{subBarcode};
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.ITEM_OF_SSP_HAVE_RELATED_SN, params);
		}
	}

	private List<ProdBindingSettingDTO> checkBindRelationAndQty(ComMachineAssemblyScanDTO comMacAssScanDTO,
																String snType, BaItem snItemInfo, boolean needCheckBindSetting, PsWipInfo wipInfoByMainSn) throws Exception {
		// 校验子条码是否已经绑定过。
		checkHaveBindRelation(comMacAssScanDTO, snType);
		// 校验主子条码的物料代码(包含替代物料)是否有绑定关系。
		Set<String> replaceItemNoSet = new HashSet<>();
		// 1.校验子条码物料代码（包含替代物料）是否在任务清单中，并记录到comMacAssScanDTO中的haveErpRelFlag中
		// BA确认过，ERP任务使用了替代物料匹配，绑定清单中匹配时，不受影响，仍然可以用本身物料或其他替代物料匹配
		checkErpTaskHaveTheItem(comMacAssScanDTO, snItemInfo, replaceItemNoSet, wipInfoByMainSn);
		// 校验是否按照装配规则扫描，主条码是否已经完成绑定,并返回查询的绑定清单信息
		List<ProdBindingSettingDTO> bindingList = checkBindRuleAndIfFinishBind(comMacAssScanDTO.getItemCode(), comMacAssScanDTO, needCheckBindSetting);
		// 转化为MAP形式，如果是单板模式，则使用12位匹配
		Map<String, ProdBindingSettingDTO> itemNoToBindInfoMap = getItemNoToBindInfoMap(bindingList, comMacAssScanDTO.isSubBarcodeStepFlag());
		ProdBindingSettingDTO bindInfoOfSubItem = checkBindListHaveTheItem(comMacAssScanDTO, itemNoToBindInfoMap,
				snItemInfo, replaceItemNoSet, needCheckBindSetting);
		// 3.校验已绑数量,需绑数量，本次绑定数量
		checkQtyOfBind(comMacAssScanDTO, bindInfoOfSubItem, snType, snItemInfo);
		return bindingList;
	}

	private Map<String, ProdBindingSettingDTO> getItemNoToBindInfoMap(List<ProdBindingSettingDTO> bindingList,
																	  boolean subBarcodeStepFlag) {
		if (!subBarcodeStepFlag) {
			return bindingList.stream().collect(Collectors.toMap(k -> k.getItemCode(),
					v -> v, (oldValue, newValue) -> newValue));
		}
		// 如果是单板条码则使用12为料单匹配
		Map<String, ProdBindingSettingDTO> resultMap = new HashMap<>();
		for (ProdBindingSettingDTO entity :bindingList) {
			String itemNo = entity.getItemCode();
			if (StringUtils.isEmpty(itemNo) || itemNo.length() < Constant.INT_12) {
				continue;
			}
			resultMap.put(itemNo.substring(0,Constant.INT_12), entity);
		}
		return resultMap;
	}

	private void checkQtyOfBind(ComMachineAssemblyScanDTO comMacAssScanDTO, ProdBindingSettingDTO bindInfoOfSubItem,
								String snType, BaItem snItemInfo) {
		// 如果没在绑定关系里，则不校验数量，直接返回。
		if (bindInfoOfSubItem == null) {
			return;
		}
		BigDecimal bindingQty = new BigDecimal(Constant.INT_1);
		// 计算已经绑定的数量。
		BigDecimal haveBoundQty = bindInfoOfSubItem.getBindedCount();
		BigDecimal needBindingQty = bindInfoOfSubItem.getUsageCount();
		Map<String, Integer> collectionCodeMap = comMacAssScanDTO.getCollectionCodeMap();
		Integer codeBindQty = null;
		if (collectionCodeMap != null) {
			codeBindQty = collectionCodeMap.get(snItemInfo.getSn());
		}
		// 计算本次绑定用量，序列码默认为1
		if (!Constant.TYPE_SEQUENCE_CODE.equals(snType)) {
			//前端勾选自动取数，从绑定清单获取
			if (comMacAssScanDTO.isNeedUsage()) {
				bindingQty = needBindingQty.subtract(haveBoundQty);
			} else {
				//否则从前端获取
				bindingQty = codeBindQty == null ? comMacAssScanDTO.getBatchCodeQty() : new BigDecimal(codeBindQty);
			}
		}
		checkUsageCount(snItemInfo, snType, bindingQty, haveBoundQty, needBindingQty);
		comMacAssScanDTO.setBatchCodeQty(bindingQty);
		// 将对应的物料，已绑数量更新为本次绑定后的，方便后续，判断是否绑定完成
		bindInfoOfSubItem.setBindedCount(bindInfoOfSubItem.getBindedCount().add(bindingQty));
	}

	private ProdBindingSettingDTO checkBindListHaveTheItem(ComMachineAssemblyScanDTO comMacAssScanDTO,
														   Map<String, ProdBindingSettingDTO> itemNoToBindInfoMap,
														   BaItem snItemInfo, Set<String> replaceItemNoSet,
														   boolean needCheckBindSetting) throws Exception {
		boolean subSnStepFlag = comMacAssScanDTO.isSubBarcodeStepFlag();
		// 查询待绑定关系表
		String itemNo = snItemInfo.getItemNo();
		if (subSnStepFlag && itemNo != null && itemNo.length() >= Constant.INT_12) {
			itemNo = itemNo.substring(0, Constant.INT_12);
		}
		if (itemNoToBindInfoMap.keySet().contains(itemNo)) {
			return itemNoToBindInfoMap.get(itemNo);
		}
		// 如果不在，则看替代物料,这里替代物料集合如果上一步校验ERP任务时，已经查询了就不再查询
		if (CollectionUtils.isEmpty(replaceItemNoSet)) {
			replaceItemNoSet = handleGetReplaceItemCode(snItemInfo.getItemNo());
		}
		// 如果替代物料也没在任务清单中，或在清单中存在多个，报错
		int sum = 0;
		String replaceItem = Constant.STR_EMPTY;
		String key = Constant.STR_EMPTY;
		for (String s : replaceItemNoSet) {
			String itemNoEntity = s;
			if (subSnStepFlag && itemNoEntity != null && itemNoEntity.length() >= Constant.INT_12) {
				itemNoEntity = itemNoEntity.substring(0, Constant.INT_12);
			}
			if (itemNoToBindInfoMap.keySet().contains(itemNoEntity)) {
				replaceItem = s;
				key = itemNoEntity;
				sum++;
			}
		}
		checkItemInBind(comMacAssScanDTO, needCheckBindSetting, sum, comMacAssScanDTO.isHaveErpRelFlag());
		// 赋值替代物料
		snItemInfo.setReplaceItemNo(replaceItem);
		return itemNoToBindInfoMap.get(key);
	}

	/**
	 *<AUTHOR>
	 * 校验物料是否在绑定清单
	 *@Date 2023/11/8 9:56
	 *@Param [com.zte.interfaces.dto.ComMachineAssemblyScanDTO, boolean, int]
	 *@return
	 **/
	private void checkItemInBind(ComMachineAssemblyScanDTO comMacAssScanDTO, boolean needCheckBindSetting, int sum, boolean haveErpRelFlag) {
		// 如果数据字典打开且必须在绑定清单
		if (sum == 0 && needCheckBindSetting) {
			String[] params = new String[]{comMacAssScanDTO.getSubBarcode()};
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.ITEM_OR_REPLACE_ITEM_NOT_IN_BIND_LIST, params);
		}
		// 存在多个配置关系，也报错。
		if (sum > 1) {
			String[] params = new String[]{comMacAssScanDTO.getSubBarcode()};
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.ITEM_OR_REPLACE_ITEM_IN_BIND_LIST_MORE, params);
		}
		// 如过erp任务清单没有，绑定清单也没有，则报错。
		if (sum == 0 && !haveErpRelFlag) {
			String[] params = new String[]{comMacAssScanDTO.getSubBarcode()};
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.ITEM_OR_REPLACE_ITEM_NOT_IN_ERP_AND_BIND_LIST, params);
		}
	}

	private void checkErpTaskHaveTheItem(ComMachineAssemblyScanDTO comMacAssScanDTO, BaItem snItemInfo,
													  Set<String> replaceItemNoSet, PsWipInfo wipInfoByMainSn) throws Exception {
		// 使用主条码的任务号查询ERP任务清单, 返工任务使用originTaskNo
		String taskNo = comMacAssScanDTO.getStandardModelTask();
		if (wipInfoByMainSn != null && !StringUtils.isEmpty(wipInfoByMainSn.getOriginalTask())) {
			taskNo = wipInfoByMainSn.getOriginalTask();
		}
		if (StringUtils.isEmpty(taskNo)) {
			String[] params = new String[]{comMacAssScanDTO.getMainBarcode()};
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.MAIN_BARCODE_WIP_INFO_NOT_HAVE_TASK_NO, params);
		}

		// 查询出后，校验子条码物料代码是否在ERP任务清单中
		List<ItemListEntityDTO> itemListEntityDTOList = datawbRemoteService.getErpItemListByTaskNo(taskNo);
		if (CollectionUtils.isEmpty(itemListEntityDTOList)) {
			comMacAssScanDTO.setHaveErpRelFlag(false);
		}
		Set<String> itemNos = new HashSet<String>() {{add(snItemInfo.getItemNo());}};
		if (getSumOfErpInfo(itemListEntityDTOList, itemNos, comMacAssScanDTO.isSubBarcodeStepFlag()) > 0) {
			comMacAssScanDTO.setHaveErpRelFlag(true);
			return;
		}
		// 子条码物料代码如果没在ERP任务中，则查看替代物料。
		// 查询替代物料
		replaceItemNoSet = handleGetReplaceItemCode(snItemInfo.getItemNo());
		// 如果替代物料也没在任务清单中，或在清单中存在多个，报错
		int sum = getSumOfErpInfo(itemListEntityDTOList, replaceItemNoSet, comMacAssScanDTO.isSubBarcodeStepFlag());
		if (sum > 1) {
			String[] params = new String[]{comMacAssScanDTO.getSubBarcode()};
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.ITEM_OR_REPLACE_ITEM_IN_ERP_TASK_MORE, params);
		}
		if (sum == 0) {
			comMacAssScanDTO.setHaveErpRelFlag(false);
		} else {
			comMacAssScanDTO.setHaveErpRelFlag(true);
		}
	}

	private int getSumOfErpInfo(List<ItemListEntityDTO> itemListEntityDTOList, Set<String> itemNos,
																   boolean isSubBarcodeStepFlag) {
		Map<String, ItemListEntityDTO> itemNoToErpEntityMap = new HashMap<>();
		if (!isSubBarcodeStepFlag) {
			for (ItemListEntityDTO itemListEntityDTO : itemListEntityDTOList) {
				itemNoToErpEntityMap.put(itemListEntityDTO.getItemNo(), itemListEntityDTO);
			}
		} else {
			for (ItemListEntityDTO itemListEntityDTO : itemListEntityDTOList) {
				String itemNo = itemListEntityDTO.getItemNo();
				if (StringUtils.isEmpty(itemNo) || itemNo.length() < Constant.INT_12) {
					continue;
				}
				itemNoToErpEntityMap.put(itemNo.substring(0, Constant.INT_12), itemListEntityDTO);
			}
		}
		int sum = getSumOfErpCore(itemNos, isSubBarcodeStepFlag, itemNoToErpEntityMap);
		return sum;
	}

	private int getSumOfErpCore(Set<String> itemNos, boolean isSubBarcodeStepFlag, Map<String, ItemListEntityDTO> itemNoToErpEntityMap) {
		int sum = 0;
		for(String s : itemNos) {
			String itemNo = s;
			if (isSubBarcodeStepFlag && itemNo.length() >= Constant.INT_12) {
				itemNo = s.substring(0, Constant.INT_12);
			}
			if (itemNoToErpEntityMap.keySet().contains(itemNo)) {
				sum++;
			}
		}
		return sum;
	}

	private Set<String> handleGetReplaceItemCode(String itemNo) throws Exception {
		Set<String> resultSet = new HashSet<>();
		List<String> itemNos = new ArrayList<>();
		itemNos.add(itemNo.substring(0, Constant.INT_12));
		List<MtlRelatedItemsEntityDTO> resultDTOs = getReplaceItemByErp(itemNos, true);
		for (MtlRelatedItemsEntityDTO entity : resultDTOs) {
			resultSet.add(entity.getInventoryItemCode());
		}
		return resultSet;
	}

	private void checkHaveBindRelation(ComMachineAssemblyScanDTO comMacAssScanDTO, String snType) {
		// 序列码只能绑定一次在主条码上
		if (Constant.TYPE_SEQUENCE_CODE.equals(snType)) {
			// 根据SN查询WIP_EXTEND_IDENTIFICATION表
			WipExtendIdentification entity = wipExtendIdentificationRepository.selectEntityBySn(comMacAssScanDTO.getSubBarcode());
			if (entity != null) {
				String[] params = new String[]{comMacAssScanDTO.getSubBarcode()};
				throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SUB_BARCODE_HAVE_BIND_RELATION, params);
			}
		}
		//允许同一批次码绑定在不同主条码上，但不允许重复绑在同一主条码
		if (Constant.TYPE_BATCH_CODE.equals(snType)) {
			WipExtendIdentificationDTO tempWipDto = new WipExtendIdentificationDTO();
			tempWipDto.setFormSn(comMacAssScanDTO.getMainBarcode());
			tempWipDto.setSn(comMacAssScanDTO.getSubBarcode());
			List<WipExtendIdentification> wipExtendIdentificationList = wipExtendIdentificationRepository.getWipExtendIdentificationBySn(tempWipDto);
			if (!CollectionUtils.isEmpty(wipExtendIdentificationList)) {
				String[] params = new String[]{comMacAssScanDTO.getSubBarcode(), comMacAssScanDTO.getMainBarcode()};
				throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SUB_BARCODE_HAVE_BOUND_IN_MAIN_BARCODE, params);
			}
		}
	}

	/** 
	* @Description: 根据条码获取条码中心条码信息
	* @Param: [sn]
	* @return: java.util.List<com.zte.interfaces.dto.BarcodeExpandDTO>
	* @Author: Saber[10307315]
	* @Date: 2023/6/9 上午9:47
	*/
	public List<BarcodeExpandDTO> getBarCodeBySubSn(String sn) throws Exception {
		BarcodeExpandQueryDTO barcodeExpandQueryDTO = new BarcodeExpandQueryDTO();
		List<String> snList = new ArrayList<>();
		snList.add(sn);
		barcodeExpandQueryDTO.setBarcodeList(snList);
		return barcodeCenterRemoteService.expandQuery(barcodeExpandQueryDTO);
	}

	private PsWipInfo checkProcessOfSubSn(String subBarcode) {
		PsWipInfo wipInfoBySn = psWipInfoService.getWipInfoBySn(subBarcode);
		if (wipInfoBySn == null) {
			return null;
		}
		// 219开头的条码不做校验, 直接返回查的wip_info
		if (subBarcode.startsWith(Constant.STR_219)) {
			return wipInfoBySn;
		}
		// 子条码的主工序必须为入库，不为入库，报错
		if (!Constant.WAREHOUSE_ENTRY.equals(wipInfoBySn.getCraftSection())) {
			String[] params = new String[]{subBarcode};
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SUB_BARCODE_CRAFT_SECTION_ILLEGAL, params);
		}
		return wipInfoBySn;
	}

	@Override
	public ComMachineAssemblyScanDTO assemblyScanningSelectSettingCache(String empNo, String factoryId) {
		String redisKey = Constant.ASSEMBLY_SCAN_SELECT_SETTING_CACHE + Constant.AND + empNo
				+ Constant.AND + factoryId;
		// 读取redis缓存
		String value = RedisCacheUtils.get(redisKey, String.class);
		// 区分空串和null
		if (value == null) {
			return null;
		}
		ComMachineAssemblyScanDTO comMachineAssemblyScanDTO = JSONObject.parseObject(value, ComMachineAssemblyScanDTO.class);
		return comMachineAssemblyScanDTO;
	}
}
