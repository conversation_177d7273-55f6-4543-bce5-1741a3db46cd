package com.zte.application.datawb.impl;

import com.zte.application.datawb.StbProducteInfoDataService;
import com.zte.common.utils.Constant;
import com.zte.domain.model.datawb.StbProdDeliveryInfoQuery;
import com.zte.domain.model.datawb.StbProdDeliveryInfoQueryExtend;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

@RunWith(PowerMockRunner.class)
@PrepareForTest({Constant.class})
public class StbProdDeliveryInfoQueryServiceImplTest {

    @InjectMocks
    private StbProdDeliveryInfoQueryServiceImpl service;

    @Mock
    private StbProducteInfoDataService stbProducteInfoDataService;


    @Test
    public void copyPropertiesTest() throws Exception {
        // 准备sourceList数据
        List<StbProdDeliveryInfoQueryExtend> sourceList = new ArrayList<>();
        StbProdDeliveryInfoQueryExtend source = new StbProdDeliveryInfoQueryExtend();
        source.setProductSn("123456");
        source.setRemark1("Source Remark");
        sourceList.add(source);

        // 准备targetList数据
        List<StbProdDeliveryInfoQuery> targetList = new ArrayList<>();
        StbProdDeliveryInfoQuery target = new StbProdDeliveryInfoQuery();
        target.setBarcode("123456");
        target.setRemark1("Target Remark");
        targetList.add(target);

        // 调用测试方法
        Method method = StbProdDeliveryInfoQueryServiceImpl.class.getDeclaredMethod("copyProperties", List.class, List.class);
        method.setAccessible(true);
        method.invoke(service, sourceList, targetList);

        // 断言验证
        Assert.assertEquals("'Source Remark", targetList.get(0).getRemark1()); // 验证是否添加单引号
    }
}
