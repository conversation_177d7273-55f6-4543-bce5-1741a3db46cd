package com.zte.interfaces;

import javax.servlet.http.HttpServletRequest;

import com.zte.common.utils.MpConstant;
import com.zte.consts.CommonConst;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.zte.application.CodingService;
import com.zte.domain.model.BoardSnResult;
import com.zte.interfaces.dto.PrintSnDto;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.common.consts.SysConst;
import com.zte.itp.msa.core.model.RetCode;


import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@RestController
@RequestMapping("/PrintSnCtrl")
@Api(description="喷码api")
public class PrintSnController {
	
	@Autowired
	private CodingService codingService;
  
	/**
	 * 人工确认喷码失败后处理
	 * @param request
	 * @param dto
	 * @return
	 * @throws Exception
	 */
    @ApiOperation("喷码机喷码失败")
    @RequestMapping(value = "/dealFailed", method = RequestMethod.POST,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ServiceData dealFailed(HttpServletRequest request,  @RequestBody PrintSnDto dto ) throws Exception {
    	ServiceData ret = new ServiceData();
    	String result = codingService.dealFialed(dto.getMacAddress());
    	if (CommonConst.FLAG_OK.equals(result)) {
    		ret.setCode(new RetCode(RetCode.SUCCESS_CODE,RetCode.SUCCESS_MSGID));
    	}       
        return ret;
    }

    
    @ApiOperation("获取客户端IP")
    @RequestMapping(value = "/getIP", method = RequestMethod.POST,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ServiceData<String> getIP(HttpServletRequest request) throws Exception {
    	System.out.println(request);
    	String ip = "";
    	ServiceData<String> ret = new ServiceData<String>(); 
    	 if (request.getHeader(CommonConst.X_FORWARDED_FOR) == null) {
    		 ip = request.getRemoteAddr();  
    	 } else {
    		 ip = request.getHeader(CommonConst.X_FORWARDED_FOR);
    	 } 	 
    	 ret.setCode(new RetCode(RetCode.SUCCESS_CODE,RetCode.SUCCESS_MSGID));
    	 ret.setBo(ip);
        return ret;
    }
    

    @ApiOperation("获取当前指令已扫描数量")
    @RequestMapping(value = "/getPrintSnNum", method = RequestMethod.POST,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ServiceData<String> getPrintSnNum(HttpServletRequest request, @RequestBody PrintSnDto dto) throws Exception {
    	String printSnNum = MpConstant.STRING_ZERO;
    	ServiceData<String> ret = new ServiceData<String>();    	
    	printSnNum = codingService.getPrintSnNum(dto.getMacAddress());  	 
    	ret.setCode(new RetCode(RetCode.SUCCESS_CODE,RetCode.SUCCESS_MSGID));
    	ret.setBo(printSnNum);
        return ret;
    }
    
    @ApiOperation("处理喷码请求")
    @RequestMapping(value = "/dealRequest", method = RequestMethod.POST,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ServiceData dealRequest(HttpServletRequest request,@RequestBody PrintSnDto dto) throws Exception {
    	ServiceData<BoardSnResult> ret = new ServiceData<BoardSnResult>();
    	String factoryId = request.getHeader(SysConst.HTTP_HEADER_X_FACTORY_ID);
    	dto.setFactoryId(factoryId);
    	BoardSnResult snResult = codingService.dealRequest(dto);
    	ret.setCode(new RetCode(RetCode.SUCCESS_CODE,RetCode.SUCCESS_MSGID));
    	ret.setBo(snResult);

    	return ret;
       	
    }
}
