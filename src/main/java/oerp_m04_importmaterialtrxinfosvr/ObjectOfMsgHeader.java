
package oerp_m04_importmaterialtrxinfosvr;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>ObjectOfMsgHeader complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="ObjectOfMsgHeader">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="SOURCE_SYSTEM_ID" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="SOURCE_SYSTEM_NAME" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="USER_ID" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="USER_NAME" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="SUBMIT_DATE" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ObjectOfMsgHeader", propOrder = {
    "sourcesystemid",
    "sourcesystemname",
    "userid",
    "username",
    "submitdate"
})
public class ObjectOfMsgHeader {

    @XmlElementRef(name = "SOURCE_SYSTEM_ID", namespace = "OERP_M04_ImportMaterialTrxInfoSvr", type = JAXBElement.class, required = false)
    protected JAXBElement<String> sourcesystemid;
    @XmlElementRef(name = "SOURCE_SYSTEM_NAME", namespace = "OERP_M04_ImportMaterialTrxInfoSvr", type = JAXBElement.class, required = false)
    protected JAXBElement<String> sourcesystemname;
    @XmlElementRef(name = "USER_ID", namespace = "OERP_M04_ImportMaterialTrxInfoSvr", type = JAXBElement.class, required = false)
    protected JAXBElement<String> userid;
    @XmlElementRef(name = "USER_NAME", namespace = "OERP_M04_ImportMaterialTrxInfoSvr", type = JAXBElement.class, required = false)
    protected JAXBElement<String> username;
    @XmlElementRef(name = "SUBMIT_DATE", namespace = "OERP_M04_ImportMaterialTrxInfoSvr", type = JAXBElement.class, required = false)
    protected JAXBElement<String> submitdate;

    /**
     * 获取sourcesystemid属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getSOURCESYSTEMID() {
        return sourcesystemid;
    }

    /**
     * 设置sourcesystemid属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setSOURCESYSTEMID(JAXBElement<String> value) {
        this.sourcesystemid = value;
    }

    /**
     * 获取sourcesystemname属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getSOURCESYSTEMNAME() {
        return sourcesystemname;
    }

    /**
     * 设置sourcesystemname属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setSOURCESYSTEMNAME(JAXBElement<String> value) {
        this.sourcesystemname = value;
    }

    /**
     * 获取userid属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getUSERID() {
        return userid;
    }

    /**
     * 设置userid属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setUSERID(JAXBElement<String> value) {
        this.userid = value;
    }

    /**
     * 获取username属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getUSERNAME() {
        return username;
    }

    /**
     * 设置username属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setUSERNAME(JAXBElement<String> value) {
        this.username = value;
    }

    /**
     * 获取submitdate属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getSUBMITDATE() {
        return submitdate;
    }

    /**
     * 设置submitdate属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setSUBMITDATE(JAXBElement<String> value) {
        this.submitdate = value;
    }

}
