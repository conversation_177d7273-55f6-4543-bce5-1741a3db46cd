<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.datawb.BoardCyclingLogMapper">
  <resultMap id="BaseResultMap" type="com.zte.domain.model.datawb.BoardCyclingLog">
    <result column="TIME_POINT" jdbcType="TIMESTAMP" property="timePoint" />
    <result column="FLAG" jdbcType="VARCHAR" property="flag" />
  </resultMap>
    <insert id="insertTime" parameterType="com.zte.domain.model.datawb.BoardCyclingLog">
        insert into BOARD_CYCLING_LOG (TIME_POINT, FLAG)
        values (#{timePoint,jdbcType=TIMESTAMP}, #{flag,jdbcType=VARCHAR})
    </insert>

    <select id="selectTime" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT * FROM (
        SELECT
        TIME_POINT
        FROM BOARD_CYCLING_LOG WHERE FLAG = #{flag,jdbcType=VARCHAR}
        ORDER BY FLAG DESC ) t
        WHERE rownum = 1
    </select>

    <delete id="deleteTime" parameterType="com.zte.domain.model.datawb.BoardCyclingLog" >
        delete from BOARD_CYCLING_LOG
        where FLAG = #{flag,jdbcType=VARCHAR}
    </delete>

    <update id="updateTime" parameterType="java.lang.String">
        update BOARD_CYCLING_LOG set TIME_POINT = TO_DATE(#{endTime,jdbcType=VARCHAR},'YY/MM/DD HH24:MI:SS')
        where FLAG = #{flag,jdbcType=VARCHAR}
    </update>

    <select id="selectAll" resultMap="BaseResultMap">
        select TIME_POINT,FLAG from BOARD_CYCLING_LOG
        where rownum <![CDATA[ <= 500 ]]>
    </select>
</mapper>