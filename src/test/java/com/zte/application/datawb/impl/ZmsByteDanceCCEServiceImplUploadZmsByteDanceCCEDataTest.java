/*Started by AICoder, pid:0db69e662dt6648141070855916f860deef94f5e*/
package com.zte.application.datawb.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zte.application.datawb.WsmAssembleLinesService;
import com.zte.application.datawb.ZmsByteDanceMRService;
import com.zte.application.datawb.ZmsDeviceInventoryUploadService;
import com.zte.application.datawb.ZmsOverallUnitService;
import com.zte.common.CommonUtils;
import com.zte.common.utils.Constant;
import com.zte.common.utils.DateUtil;
import com.zte.domain.model.MesGetDictInforRepository;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.datawb.SalesOrderUploadHistoryRepository;
import com.zte.domain.model.datawb.ZmsByteDanceCCERepository;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.interfaces.dto.*;
import com.zte.springbootframe.util.json.JacksonJsonConverUtil;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;

@RunWith(PowerMockRunner.class)
@PrepareForTest({WsmAssembleLinesService.class, JacksonJsonConverUtil.class, ZmsOverallUnitService.class,
        JSON.class, CommonUtils.class, DateUtil.class})
@PowerMockIgnore({"javax.net.ssl.*", "javax.security.*"})
public class ZmsByteDanceCCEServiceImplUploadZmsByteDanceCCEDataTest {
    @InjectMocks
    private ZmsByteDanceCCEServiceImpl zmsByteDanceCCEService;

    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Mock
    private ZmsDeviceInventoryUploadService zmsDeviceInventoryUploadService;

    @Mock
    private ZmsByteDanceMRService zmsByteDanceMRService;

    @Mock
    private ZmsByteDanceCCERepository zmsByteDanceCCERepository;

    @Mock
    private WsmAssembleLinesService wsmAssembleLinesService;

    @Mock
    private ZmsOverallUnitService zmsOverallUnitService;

    private List<SysLookupValues> sysLookupValuesList;


    @Mock
    private SalesOrderUploadHistoryRepository salesOrderUploadHistoryRepository;

    @Mock
    private MesGetDictInforRepository mesGetDictInforRepository;

    @Mock
    private ZmsIndicatorUploadServiceImpl zmsIndicatorUploadService;


    @Before
    public void setUp() throws Exception {
        sysLookupValuesList = new ArrayList<>();
        SysLookupValues sysLookupValues1 = new SysLookupValues();
        sysLookupValues1.setLookupCode(BigDecimal.valueOf(Long.parseLong(Constant.LOOKUP_TYPE_824004200020)));
        sysLookupValues1.setDescription("111");
        sysLookupValuesList.add(sysLookupValues1);
        SysLookupValues sysLookupValues2 = new SysLookupValues();
        sysLookupValues2.setLookupCode(BigDecimal.valueOf(Long.parseLong(Constant.LOOKUP_TYPE_824004200021)));
        sysLookupValues2.setDescription("222");
        sysLookupValuesList.add(sysLookupValues2);
        SysLookupValues sysLookupValues3 = new SysLookupValues();
        sysLookupValues3.setLookupCode(BigDecimal.valueOf(Long.parseLong(Constant.LOOKUP_TYPE_824004200023)));
        sysLookupValues3.setDescription("333");
        sysLookupValuesList.add(sysLookupValues3);
        SysLookupValues sysLookupValues4 = new SysLookupValues();
        sysLookupValues4.setLookupCode(BigDecimal.valueOf(Long.parseLong(Constant.LOOKUP_TYPE_824004200024)));
        sysLookupValues4.setDescription("222");
        sysLookupValuesList.add(sysLookupValues4);
        PowerMockito.when(wsmAssembleLinesService.getSysLookupValues(anyString())).thenReturn(sysLookupValuesList);

        String dataTransferBatchNo = "ZTE120240505000008";
        PowerMockito.when(zmsDeviceInventoryUploadService.getDataTransferBatchNo()).thenReturn(dataTransferBatchNo);

        List<CpmConfigItemAssembleDTO> configDetailDTOList = new ArrayList<>();
        CpmConfigItemAssembleDTO cpmConfigItemAssembleDTO = new CpmConfigItemAssembleDTO();
        cpmConfigItemAssembleDTO.setItemCode("code");
        cpmConfigItemAssembleDTO.setItemBarcode("barcode");
        cpmConfigItemAssembleDTO.setBarcodeQty("20");
        configDetailDTOList.add(cpmConfigItemAssembleDTO);
        PowerMockito.when(wsmAssembleLinesService.getAssemblyMaterialsByEntityId(anyInt()))
                .thenReturn(configDetailDTOList);
        List<CustomerItemsDTO> customerItemsDTOList = new ArrayList<>();
        CustomerItemsDTO customerItemsDTO = new CustomerItemsDTO();
        customerItemsDTO.setZteCode("code");
        customerItemsDTO.setProjectType("3");
        customerItemsDTOList.add(customerItemsDTO);
        PowerMockito.when(centerfactoryRemoteService.getCustomerItemsInfo(anyString(), anyList()))
                .thenReturn(customerItemsDTOList);
        List<WsmAssembleLinesEntityDTO> wsmAssembleLinesList = new ArrayList<>();
        WsmAssembleLinesEntityDTO wsmAssembleLinesEntityDTO1 = new WsmAssembleLinesEntityDTO();
        wsmAssembleLinesEntityDTO1.setItemCode("code");
        wsmAssembleLinesEntityDTO1.setItemQty(10);
        wsmAssembleLinesList.add(wsmAssembleLinesEntityDTO1);
        PowerMockito.when(wsmAssembleLinesService.getAssemblyMaterials(anyString())).thenReturn(wsmAssembleLinesList);
        PowerMockito.when(wsmAssembleLinesService.getAssemblyMaterialList(anyList())).thenReturn(null);

        ReflectionTestUtils.setField(zmsByteDanceCCEService, "b2bSize", 100);
    }

    @Test(timeout = 8000)
    public void testUploadZmsByteDanceCCEData_EmptyEntityNameList() throws Exception {
        ZmsByteDanceCCEDTO dto = new ZmsByteDanceCCEDTO();
        dto.setEntityNameList(new ArrayList<>());
        zmsByteDanceCCEService.uploadZmsByteDanceCCEData(dto, "123");
        assertNotNull(dto);
    }

    @Test(timeout = 8000)
    public void testUploadZmsByteDanceCCEData_NullEntityNameList() throws Exception {
        ZmsByteDanceCCEDTO dto = new ZmsByteDanceCCEDTO();
        dto.setEntityNameList(null);
        zmsByteDanceCCEService.uploadZmsByteDanceCCEData(dto, "123");
        assertNotNull(dto);
    }

    @Test//(timeout = 8000)
    public void testUploadZmsByteDanceCCEData_EmptyZmsByteDanceCCEHeadDTOList() throws Exception {
        ZmsByteDanceCCEDTO dto = new ZmsByteDanceCCEDTO();
        List<String> entityNameList = new ArrayList<>();
        entityNameList.add("ABC");
        dto.setEntityNameList(entityNameList);
        List<ZmsByteDanceCCEHeadDTO> zmsByteDanceCCEHeadDTOList = new ArrayList<>();
        ZmsByteDanceCCEHeadDTO zmsByteDanceCCEHeadDTO1 = new ZmsByteDanceCCEHeadDTO();
        zmsByteDanceCCEHeadDTO1.setProdOrderNo("111");
        zmsByteDanceCCEHeadDTO1.setBdPurchaseNo("XO222");
        zmsByteDanceCCEHeadDTO1.setEntityId("111");
        zmsByteDanceCCEHeadDTO1.setUserAddress("333");
        zmsByteDanceCCEHeadDTOList.add(zmsByteDanceCCEHeadDTO1);
        ZmsByteDanceCCEHeadDTO zmsByteDanceCCEHeadDTO2 = new ZmsByteDanceCCEHeadDTO();
        zmsByteDanceCCEHeadDTO2.setProdOrderNo("222");
        zmsByteDanceCCEHeadDTO2.setLobCode("22_33");
        zmsByteDanceCCEHeadDTO2.setBdPurchaseNo("PO222");
        zmsByteDanceCCEHeadDTO2.setEntityId("111");
        zmsByteDanceCCEHeadDTOList.add(zmsByteDanceCCEHeadDTO2);
        ZmsByteDanceCCEHeadDTO zmsByteDanceCCEHeadDTO3 = new ZmsByteDanceCCEHeadDTO();
        zmsByteDanceCCEHeadDTO3.setProdOrderNo("333");
        zmsByteDanceCCEHeadDTO3.setLobCode("22_33_44");
        zmsByteDanceCCEHeadDTO3.setEntityId("111");
        zmsByteDanceCCEHeadDTOList.add(zmsByteDanceCCEHeadDTO3);
        PowerMockito.when(zmsByteDanceCCERepository.getEntityHead(any())).thenReturn(zmsByteDanceCCEHeadDTOList);

        List<ZmsCbomInfoDTO> zmsCbomInfoDTOList = new ArrayList<>();
        ZmsCbomInfoDTO zmsCbomInfoDTO1 = new ZmsCbomInfoDTO();
        zmsCbomInfoDTO1.setEntityName("333");
        zmsCbomInfoDTOList.add(zmsCbomInfoDTO1);
        PowerMockito.when(this.zmsDeviceInventoryUploadService.getMaterialInfo(anyList(), any())).thenReturn(zmsCbomInfoDTOList);

        zmsByteDanceCCEService.uploadZmsByteDanceCCEData(dto, "123");

        ZmsCbomInfoDTO zmsCbomInfoDTO2 = new ZmsCbomInfoDTO();
        zmsCbomInfoDTO2.setEntityName("111");
        zmsCbomInfoDTOList.add(zmsCbomInfoDTO2);
        PowerMockito.when(this.zmsDeviceInventoryUploadService.getMaterialInfo(anyList(), any())).thenReturn(zmsCbomInfoDTOList);

        zmsByteDanceCCEService.uploadZmsByteDanceCCEData(dto, "123");
        assertNotNull(dto);
    }

    @Test(timeout = 8000)
    public void testUploadZmsByteDanceCCEData_WithData() throws Exception {
        ZmsByteDanceCCEDTO dto = new ZmsByteDanceCCEDTO();
        List<String> entityNameList = new ArrayList<>();
        entityNameList.add("ABC");
        dto.setEntityNameList(entityNameList);
        ZmsByteDanceCCEHeadDTO zmsByteDanceCCEHeadDTO = new ZmsByteDanceCCEHeadDTO();
        zmsByteDanceCCEHeadDTO.setProdOrderNo("ABC");
        zmsByteDanceCCEHeadDTO.setBuCode("BU1");
        zmsByteDanceCCEHeadDTO.setLobCode("LOB1_LOB2");
        zmsByteDanceCCEHeadDTO.setBdPurchaseNo("PO1");
        zmsByteDanceCCEHeadDTO.setUnit("UNIT1");
        zmsByteDanceCCEHeadDTO.setOdmPlantCode("PLANT1");
        zmsByteDanceCCEHeadDTO.setEntityId("111");
        List<ZmsByteDanceCCEHeadDTO> zmsByteDanceCCEHeadDTOList = new ArrayList<>();
        zmsByteDanceCCEHeadDTOList.add(zmsByteDanceCCEHeadDTO);
        PowerMockito.when(zmsByteDanceCCERepository.getEntityHead(any())).thenReturn(zmsByteDanceCCEHeadDTOList);
        ZmsCbomInfoDTO zmsCbomInfoDTO = new ZmsCbomInfoDTO();
        zmsCbomInfoDTO.setEntityName("ABC");
        List<ZmsCbomInfoDTO> zmsCbomInfoDTOList = new ArrayList<>();
        zmsCbomInfoDTOList.add(zmsCbomInfoDTO);
        PowerMockito.when(zmsDeviceInventoryUploadService
                        .getMaterialInfo(entityNameList, null))
                .thenReturn(zmsCbomInfoDTOList);
        zmsByteDanceCCEService.uploadZmsByteDanceCCEData(dto, "123");
        assertNotNull(dto);
    }

    @Test
    public void machineOutstandingSaleUpload() throws Exception{
        zmsByteDanceCCEService.machineOutstandingSaleUpload(new ZmsByteDanceCCEDTO(), "10313234");

        List<EntityWeightDTO> dictCompanys = new ArrayList<>();
        EntityWeightDTO dto = new EntityWeightDTO();
        dto.setDescription("123");
        dictCompanys.add(dto);
        PowerMockito.when(mesGetDictInforRepository.getDict(Mockito.anyString())).thenReturn(dictCompanys);
        List<ByteDanceSalesOrderHeadDTO> headDTOList = new ArrayList<>();
        ByteDanceSalesOrderHeadDTO headDTO = new ByteDanceSalesOrderHeadDTO();
        headDTO.setSalesOrderNo("123321");
        headDTOList.add(headDTO);
        ByteDanceSalesOrderHeadDTO headDTO1 = new ByteDanceSalesOrderHeadDTO();
        headDTO1.setSalesOrderNo("123323");
        headDTO1.setSalesOrderTypeDesc("补发(产品)");
        headDTO1.setCustomerPo("123");
        headDTOList.add(headDTO1);
        ByteDanceSalesOrderHeadDTO headDTO2 = new ByteDanceSalesOrderHeadDTO();
        headDTO2.setSalesOrderNo("123322Q1");
        headDTO2.setSalesOrderTypeDesc("补发(产品)");
        headDTO2.setCustomerPo("123");
        headDTOList.add(headDTO2);
        ByteDanceSalesOrderHeadDTO headDTO3 = new ByteDanceSalesOrderHeadDTO();
        headDTO3.setSalesOrderNo("123323");
        headDTOList.add(headDTO3);
        ByteDanceSalesOrderHeadDTO headDTO4 = new ByteDanceSalesOrderHeadDTO();
        headDTO4.setSalesOrderNo("123324Q1");
        headDTO4.setSalesOrderTypeDesc("补发(产品)");
        headDTO4.setCustomerPo("123");
        headDTOList.add(headDTO4);
        PowerMockito.when(zmsByteDanceCCERepository.getContractHeadList(Mockito.anyList(), Mockito.any())).thenReturn(headDTOList);
        zmsByteDanceCCEService.machineOutstandingSaleUpload(new ZmsByteDanceCCEDTO(), "10313234");

        List<ByteDanceSalesOrderItemDTO> contractQtyList = new ArrayList<>();
        ByteDanceSalesOrderItemDTO itemDTO = new ByteDanceSalesOrderItemDTO();
        itemDTO.setSalesOrderNo("1233211");
        itemDTO.setSalesQuantity(1);
        itemDTO.setDeliveriedQuantity(1);
        itemDTO.setEstimateDispatchDate("20241012");
        contractQtyList.add(itemDTO);
        ByteDanceSalesOrderItemDTO itemDTO1 = new ByteDanceSalesOrderItemDTO();
        itemDTO1.setSalesOrderNo("123322Q11");
        itemDTO1.setSalesQuantity(2);
        itemDTO1.setDeliveriedQuantity(null);
        itemDTO1.setEstimateDispatchDate("20241012");
        contractQtyList.add(itemDTO1);
        ByteDanceSalesOrderItemDTO itemDTO12 = new ByteDanceSalesOrderItemDTO();
        itemDTO12.setSalesOrderNo("123324Q11");
        itemDTO12.setSalesQuantity(2);
        itemDTO12.setDeliveriedQuantity(null);
        itemDTO12.setEstimateDispatchDate("20241012");
        contractQtyList.add(itemDTO12);
        ByteDanceSalesOrderItemDTO itemDTO5 = new ByteDanceSalesOrderItemDTO();
        itemDTO5.setSalesOrderNo("1233231");
        itemDTO5.setSalesQuantity(2);
        itemDTO5.setDeliveriedQuantity(null);
        itemDTO5.setEstimateDispatchDate("20241012");
        contractQtyList.add(itemDTO5);
        PowerMockito.when(zmsByteDanceCCERepository.getContractItemList(Mockito.any())).thenReturn(contractQtyList);
        List<ByteDanceSalesOrderItemDTO> entityNameList = new ArrayList<>();
        ByteDanceSalesOrderItemDTO itemDTO2 = new ByteDanceSalesOrderItemDTO();
        itemDTO2.setEntityName("");
        itemDTO2.setSalesOrderNo("123");
        ByteDanceSalesOrderItemDTO itemDTO3 = new ByteDanceSalesOrderItemDTO();
        itemDTO3.setEntityName("123");
        itemDTO3.setSalesOrderNo("");
        ByteDanceSalesOrderItemDTO itemDTO4 = new ByteDanceSalesOrderItemDTO();
        itemDTO4.setEntityName("123333");
        itemDTO4.setSalesOrderNo("123322Q11");
        entityNameList.add(itemDTO2);
        entityNameList.add(itemDTO3);
        entityNameList.add(itemDTO4);
        PowerMockito.when(zmsByteDanceCCERepository.getEntityNameList(Mockito.any())).thenReturn(entityNameList);
        List<ZmsCbomInfoDTO> zmsCbomInfoDTOList = new ArrayList<>();
        ZmsCbomInfoDTO cbomInfoDTO = new ZmsCbomInfoDTO();
        cbomInfoDTO.setCbomNameEn("123");
        zmsCbomInfoDTOList.add(cbomInfoDTO);
        PowerMockito.when(zmsDeviceInventoryUploadService.getMaterialInfo(Mockito.anyList(), Mockito.any())).thenReturn(zmsCbomInfoDTOList);
        zmsByteDanceCCEService.machineOutstandingSaleUpload(new ZmsByteDanceCCEDTO(), "10313234");
        itemDTO.setSalesOrderNo("123321");
        itemDTO1.setSalesOrderNo("123322Q1");
        itemDTO12.setSalesOrderNo("123324Q1");
        itemDTO5.setSalesOrderNo("123323");
        itemDTO4.setSalesOrderNo("123322Q1");
        zmsByteDanceCCEService.machineOutstandingSaleUpload(new ZmsByteDanceCCEDTO(), "10313234");
        List<ByteDanceSalesOrderHeadDTO> danceSalesOrderHeadDTOS = new ArrayList<>();
        ByteDanceSalesOrderHeadDTO headDTO5 = new ByteDanceSalesOrderHeadDTO();
        headDTO5.setSalesOrderNo("123322");
        headDTO5.setSalesOrderType("qqq");
        danceSalesOrderHeadDTOS.add(headDTO5);
        PowerMockito.when(zmsByteDanceCCERepository.getSalesOrderType(Mockito.anyList())).thenReturn(danceSalesOrderHeadDTOS);
        zmsByteDanceCCEService.machineOutstandingSaleUpload(new ZmsByteDanceCCEDTO(), "10313234");
        Assert.assertNull(null);
    }

    @Test
    public void updateSalesOrderUpload() throws Exception {
        zmsByteDanceCCEService.updateSalesOrderUpload(new ZmsMesInfoUploadLogDTO());
        ZmsMesInfoUploadLogDTO uploadLogDTO = new ZmsMesInfoUploadLogDTO();
        uploadLogDTO.setUploadStatus("0");
        zmsByteDanceCCEService.updateSalesOrderUpload(uploadLogDTO);
        uploadLogDTO.setUploadStatus("1");
        Map<String,Object> map = new HashMap<>();
        map.put("data", null);
        uploadLogDTO.setJsonData(JSONObject.toJSONString(map));
        zmsByteDanceCCEService.updateSalesOrderUpload(uploadLogDTO);
        List<ByteDanceSalesOrderHeadDTO> list = new ArrayList<>();
        map.put("data", JSONObject.toJSONString(list));
        uploadLogDTO.setJsonData(JSONObject.toJSONString(map));
        zmsByteDanceCCEService.updateSalesOrderUpload(uploadLogDTO);
        ByteDanceSalesOrderHeadDTO headDTO = new ByteDanceSalesOrderHeadDTO();
        headDTO.setSalesOrderNo("123");
        list.add(headDTO);
        map.put("data", JSONObject.toJSONString(list));
        uploadLogDTO.setJsonData(JSONObject.toJSONString(map));
        zmsByteDanceCCEService.updateSalesOrderUpload(uploadLogDTO);
        Assert.assertNull(null);
    }
}
/*Ended by AICoder, pid:0db69e662dt6648141070855916f860deef94f5e*/