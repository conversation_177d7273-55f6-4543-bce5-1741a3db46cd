package com.zte.application.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zte.aiop.dtems.AlarmHelper;
import com.zte.common.CommonUtils;
import com.zte.common.DataPusher;
import com.zte.common.PushDataHandler;
import com.zte.common.authority.HttpConstant;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.NetworkB2B;
import com.zte.common.utils.NetworkLicenseUploadB2B;
import com.zte.common.utils.NetworkLicenseUploadResultB2B;
import com.zte.domain.model.*;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.km.udm.api.DiskFileDownloadApi;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.common.redis.RedisLock;
import com.zte.springbootframe.util.CloudDiskHelper;
import com.zte.springbootframe.util.EmailUtils;
import com.zte.springbootframe.util.FileUtils;
import com.zte.springbootframe.util.IdGenerator;
import com.zte.springbootframe.util.MESHttpHelper;
import org.apache.commons.codec.binary.Base64;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.mock.web.MockMultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.*;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.doReturn;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023/9/19 10:34
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({BasicsettingRemoteService.class,FileUtils.class,File.class, DiskFileDownloadApi.class,com.zte.km.udm.common.httpclient.HttpClientUtil.class,
        BeanUtils.class,JSONObject.class,MESHttpHelper.class, UUID.class, HttpClientUtil.class,FileInputStream.class, AlarmHelper.class,
        MESHttpHelper.class, CommonUtils.class, RedisHelper.class,org.apache.commons.io.FileUtils.class})
public class NetworkLicenseServiceImplTest {

    @InjectMocks
    private NetworkLicenseServiceImpl networkLicenseService;
    @InjectMocks
    private NetworkLicenseUploadServiceImpl networkLicenseUploadService;
    @Mock
    private ResourceInfoRepository resourceInfoRepository;
    @Mock
    private ResourceUseInfoRepository resourceUseInfoRepository;
    @Mock
    private ResourceInfoDetailTempRepository resourceInfoDetailTempRepository;
    @Mock
    private RedisTemplate<String, Object> redisTemplate;
    @Mock
    private ResourceInfoDetailRepository resourceInfoDetailRepository;
    @Mock
    private NetworkB2B networkB2B;
    @Mock
    private IdGenerator idGenerator;
    @Mock
    private NetworkLicenseUploadB2B networkLicenseUploadB2B;
    @Mock
    private NetworkLicenseUploadResultB2B networkLicenseUploadResultB2B;
    @Mock
    private EmailUtils emailUtils;
    @Mock
    private PushDataHandler handler;
    @Mock
    private CloudDiskHelper cloudDiskHelper;
    @Mock
    private SysLookupValuesServiceImpl sysLookupValuesService;

    @Mock
    private RedisLock redisLock;

    @Test
    public void importResult() {
        Assert.assertThrows(MesBusinessException.class, () -> networkLicenseService.importResult(new MockMultipartFile("1.xlsx", new byte[0])));
    }

    @Test
    public void setDate() {
        List<NetworkLicenseExcelBO> result = new ArrayList<>();
        NetworkLicenseExcelBO networkLicenseExcelBO = new NetworkLicenseExcelBO();
        NetworkLicenseExcelBO networkLicenseExcelBO1 = new NetworkLicenseExcelBO();
        networkLicenseExcelBO1.setExpiryDate(new Date());
        result.add(networkLicenseExcelBO);
        result.add(networkLicenseExcelBO1);
        networkLicenseService.setDate(result);
        Assert.assertNotNull(result);
    }

    @Test
    public void save() {
        PowerMockito.mockStatic(HttpClientUtil.class, MESHttpHelper.class);
        Map<String, String> header = new HashMap<>();
        header.put("x-factory-id", "51");
        header.put("X-Emp-No", "10296137");
        header.put(HttpConstant.X_AUTH_VALUE, "token");
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(header);

        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(anyString(), anyString())).thenReturn(
                JSON.toJSONString("msg"));
        List<NetworkLicenseExcelBO> list = new ArrayList<>();
        networkLicenseService.save(list);
        NetworkLicenseExcelBO networkLicenseExcelBO1 = new NetworkLicenseExcelBO();
        networkLicenseExcelBO1.setResourceNo("1212");
        list.add(networkLicenseExcelBO1);

        Assert.assertThrows(MesBusinessException.class, () -> networkLicenseService.save(list));
        NetworkLicenseExcelBO networkLicenseExcelBO = new NetworkLicenseExcelBO();
        networkLicenseExcelBO.setSeq(1);
        networkLicenseExcelBO.setDeviceType("1");
        networkLicenseExcelBO.setResourceNo("11-2212-558748");
        networkLicenseExcelBO.setExpiryDate(new Date());
        networkLicenseExcelBO.setCertName("进网试用");
        networkLicenseExcelBO.setCertAdministrator("1");
        networkLicenseExcelBO.setProductLine("1");
        networkLicenseExcelBO.setProductName("1");
        networkLicenseExcelBO.setResourceType("入网许可");
        networkLicenseService.save(Collections.singletonList(networkLicenseExcelBO));
        networkLicenseExcelBO.setResourceNo("11-************");
        networkLicenseService.save(Collections.singletonList(networkLicenseExcelBO));
        for (int i = 0; i < 1001; i++) {
            list.add(new NetworkLicenseExcelBO());
        }
        Assert.assertThrows(MesBusinessException.class, () -> networkLicenseService.save(list));
    }

    @Test
    public void save1() {
        PowerMockito.mockStatic(HttpClientUtil.class, MESHttpHelper.class);
        Map<String, String> header = new HashMap<>();
        header.put("x-factory-id", "51");
        header.put("X-Emp-No", "10296137");
        header.put(HttpConstant.X_AUTH_VALUE, "token");
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(header);

        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(anyString(), anyString())).thenReturn(
                JSON.toJSONString("msg"));

        List<NetworkLicenseExcelBO> list = new ArrayList<>();
        NetworkLicenseExcelBO networkLicenseExcelBO = new NetworkLicenseExcelBO();
        networkLicenseExcelBO.setSeq(1);
        networkLicenseExcelBO.setDeviceType("1");
        networkLicenseExcelBO.setResourceNo("11-2212-558748");
        networkLicenseExcelBO.setExpiryDate(new Date());
        networkLicenseExcelBO.setCertName("进网试用");
        networkLicenseExcelBO.setCertAdministrator("1");
        networkLicenseExcelBO.setProductLine("1");
        networkLicenseExcelBO.setProductName("1");
        networkLicenseExcelBO.setResourceType("入网许可");
        list.add(networkLicenseExcelBO);
        networkLicenseService.save(list);

        NetworkLicenseExcelBO networkLicenseExcelBO1 = new NetworkLicenseExcelBO();

        list.add(networkLicenseExcelBO1);
        Assert.assertThrows(MesBusinessException.class, () -> networkLicenseService.save(list));
        networkLicenseExcelBO1.setResourceNo("11-2212-558748");
        Assert.assertThrows(MesBusinessException.class, () -> networkLicenseService.save(list));
        networkLicenseExcelBO1.setResourceNo("11-2212-5587481");
        Assert.assertThrows(MesBusinessException.class, () -> networkLicenseService.save(list));
        networkLicenseExcelBO1.setResourceNo("12");
        Assert.assertThrows(MesBusinessException.class, () -> networkLicenseService.save(list));

        List<ResourceInfoEntityDTO> resourceInfoList = new ArrayList<>();
        ResourceInfoEntityDTO resourceInfoEntityDTO = new ResourceInfoEntityDTO();
        resourceInfoEntityDTO.setResourceNo("11-2212-558748");
        resourceInfoList.add(resourceInfoEntityDTO);
        PowerMockito.when(resourceInfoRepository.getListByResource(any())).thenReturn(resourceInfoList);
        Assert.assertThrows(MesBusinessException.class, () -> networkLicenseService.save(list));


    }

    @Test
    public void signImport() {
        PowerMockito.mockStatic(HttpClientUtil.class, MESHttpHelper.class);
        Map<String, String> header = new HashMap<>();
        header.put("X-Factory-Id", "52");
        header.put("X-Emp-No", "10296137");
        header.put(HttpConstant.X_AUTH_VALUE, "token");
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(header);
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(anyString(), anyString())).thenReturn(
                JSON.toJSONString("msg"));
        PowerMockito.mockStatic(MESHttpHelper.class);
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(true);
        List<ResourceInfoEntityDTO> resourceInfoList = new ArrayList<>();
        resourceInfoList.add(new ResourceInfoEntityDTO());
        PowerMockito.when(resourceInfoRepository.getListByResource(any())).thenReturn(resourceInfoList);
        PowerMockito.when(resourceInfoDetailRepository.insertByTemp()).thenReturn(1L);
        Assert.assertThrows(MesBusinessException.class, () -> networkLicenseService.signImport(new MockMultipartFile("1.xlsx", new byte[0])));
        String not31 = "1212121";
        MockMultipartFile firstFile = new MockMultipartFile("data", "file111112121212121313131.txt", "text/plain", not31.getBytes(StandardCharsets.UTF_8));
        networkLicenseService.signImport(firstFile);

        String max500 = get500();
        MockMultipartFile firstFile500 = new MockMultipartFile("data", "file111112121212121313131.txt", "text/plain", max500.getBytes(StandardCharsets.UTF_8));
        networkLicenseService.signImport(firstFile500);

        String not2 = "2288880358640000000P2C54N4P1301";
        MockMultipartFile firstFile2 = new MockMultipartFile("data", "file111112121212121313131.txt", "text/plain", not2.getBytes(StandardCharsets.UTF_8));

        networkLicenseService.signImport(firstFile2);

        String s1 = "22888803586400,0000P2C54N4P1301";
        MockMultipartFile s11 = new MockMultipartFile("data", "file111112121212121313131.txt", "text/plain", s1.getBytes(StandardCharsets.UTF_8));
        List<ResourceInfoDetailDTO> resourceInfoDetailDTOS = new ArrayList<>();
        resourceInfoDetailDTOS.add(new ResourceInfoDetailDTO());
        PowerMockito.when(resourceInfoDetailRepository.getListByResourceNum(any())).thenReturn(resourceInfoDetailDTOS);
        networkLicenseService.signImport(s11);

        List<ResourceInfoDetailDTO> resourceInfoDetailDTO1S = new ArrayList<>();
        List<ResourceInfoDetailDTO> resourceInfoDetailDTO2S = new ArrayList<>();
        resourceInfoDetailDTO2S.add(new ResourceInfoDetailDTO());
        PowerMockito.when(resourceInfoRepository.updateAmount(any())).thenReturn(1);
        PowerMockito.when(resourceInfoDetailRepository.getListByResourceNum(any())).thenReturn(resourceInfoDetailDTO1S);
        PowerMockito.when(resourceInfoDetailTempRepository.getListByResourceNum(any())).thenReturn(resourceInfoDetailDTO2S);
        networkLicenseService.signImport(s11);
    }

    @Test
    public void signImport1() throws Exception {
        PowerMockito.mockStatic(HttpClientUtil.class, MESHttpHelper.class);
        PowerMockito.when(resourceInfoDetailRepository.insertByTemp()).thenReturn(1L);
        PowerMockito.when(resourceInfoRepository.updateAmount(any())).thenReturn(1);
        PowerMockito.when(resourceInfoRepository.updateAvailableQuantityByNal(any())).thenReturn(1);
        Map<String, String> header = new HashMap<>();
        header.put("X-Factory-Id", "52");
        header.put("X-Emp-No", "10296137");
        header.put(HttpConstant.X_AUTH_VALUE, "token");
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(header);
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(anyString(), anyString())).thenReturn(
                JSON.toJSONString("msg"));
        PowerMockito.mockStatic(MESHttpHelper.class);

        MockMultipartFile firstFile = new MockMultipartFile("data", "file111112121212121313131.txt", "text/plain", "1121".getBytes(StandardCharsets.UTF_8));
        Assert.assertThrows(MesBusinessException.class, () -> networkLicenseService.signImport(firstFile));

        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(true);
        List<ResourceInfoEntityDTO> resourceInfoList = new ArrayList<>();
        resourceInfoList.add(new ResourceInfoEntityDTO());
        PowerMockito.when(resourceInfoRepository.getListByResource(any())).thenReturn(resourceInfoList);
        networkLicenseService.signImport(firstFile);

        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(false);
        Assert.assertThrows(MesBusinessException.class, () -> networkLicenseService.signImport(firstFile));

        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(true);
        try{
        	networkLicenseService.signImport(firstFile);
        }catch (Exception e){
			Assert.assertEquals(e.getMessage(), MessageId.NETWORK_LICENSE_SIGN_LOCK_ERROR);
		}
    }

    @Test
    public void resourceFailure() {
        networkLicenseService.resourceFailure();
        List<ResourceInfoEntityDTO> resourceInfoList = new ArrayList<>();
        resourceInfoList.add(new ResourceInfoEntityDTO());
        resourceInfoList.add(new ResourceInfoEntityDTO());
        resourceInfoList.add(new ResourceInfoEntityDTO());
        resourceInfoList.add(new ResourceInfoEntityDTO());
        resourceInfoList.get(0).setResourceNo("111");
        resourceInfoList.get(0).setResourceId("333");
        resourceInfoList.get(0).setExpiryDate(new Date(System.currentTimeMillis() + 24 * 60 * 60 * 1000 * 3));
        resourceInfoList.get(1).setResourceNo("222");
        resourceInfoList.get(1).setResourceId("444");
        resourceInfoList.get(1).setExpiryDate(new Date(System.currentTimeMillis() + 24 * 60 * 60 * 1000 * 3));
        resourceInfoList.get(2).setResourceNo("111");
        resourceInfoList.get(2).setResourceId("333");
        resourceInfoList.get(2).setExpiryDate(new Date(System.currentTimeMillis() + 24 * 60 * 60 * 1000 * 3));
        resourceInfoList.get(3).setResourceNo("222");
        resourceInfoList.get(3).setResourceId("444");
        resourceInfoList.get(3).setExpiryDate(new Date(System.currentTimeMillis()));

        PowerMockito.when(resourceInfoRepository.pageQueryExpiryData(any())).thenReturn(resourceInfoList);

        networkLicenseService.resourceFailure();
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

    @Test
    public void binding() {
        PowerMockito.mockStatic(HttpClientUtil.class, MESHttpHelper.class);
        Map<String, String> header = new HashMap<>();
        header.put("X-Factory-Id", "52");
        header.put("X-Emp-No", "10296137");
        header.put(HttpConstant.X_AUTH_VALUE, "token");
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(header);
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(anyString(), anyString())).thenReturn(
                JSON.toJSONString("msg"));
        PowerMockito.mockStatic(MESHttpHelper.class);
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(false);

        NetworkLicenseBindingDTO networkLicenseBindingDTO = new NetworkLicenseBindingDTO();
        Assert.assertThrows(MesBusinessException.class, () -> networkLicenseService.binding(networkLicenseBindingDTO));
        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(true);
        Assert.assertThrows(MesBusinessException.class, () -> networkLicenseService.binding(networkLicenseBindingDTO));


        PowerMockito.when(resourceInfoDetailRepository.queryResourceNoByNum(any())).thenReturn(new ResourceInfoDetailDTO());
        Assert.assertThrows(MesBusinessException.class, () -> networkLicenseService.binding(networkLicenseBindingDTO));
        ResourceInfoDetailDTO resourceInfoDetailDTO = new ResourceInfoDetailDTO();
        resourceInfoDetailDTO.setStatus("1");
        PowerMockito.when(resourceInfoDetailRepository.queryResourceNoByNum(any())).thenReturn(resourceInfoDetailDTO);
        Assert.assertThrows(MesBusinessException.class, () -> networkLicenseService.binding(networkLicenseBindingDTO));
        resourceInfoDetailDTO.setResourceNo("11-2222-333333");

        List<ResourceUseInfoDTO> resourceUses = new ArrayList<>();
        resourceUses.add(new ResourceUseInfoDTO());
        PowerMockito.when(resourceUseInfoRepository.queryPage(any())).thenReturn(resourceUses);
        Assert.assertThrows(MesBusinessException.class, () -> networkLicenseService.binding(networkLicenseBindingDTO));

        PowerMockito.when(resourceUseInfoRepository.queryPage(any())).thenReturn(new ArrayList<>());
        PowerMockito.when(resourceInfoDetailRepository.batchBinding(any())).thenReturn(1);
        PowerMockito.when(resourceUseInfoRepository.insertEntity(any())).thenReturn(1L);

        List<ResourceInfoEntityDTO> resourceInfoEntityDTOList = new ArrayList<>();
        resourceInfoEntityDTOList.add(new ResourceInfoEntityDTO());
        PowerMockito.when(resourceInfoRepository.getList(any())).thenReturn(resourceInfoEntityDTOList);

        networkLicenseService.binding(networkLicenseBindingDTO);
    }
    @Test
    public void uploadProductInfo() throws Exception {
		PowerMockito.mockStatic(JSONObject.class,AlarmHelper.class);
        try{
        	networkLicenseUploadService.uploadProductInfo();
		}catch(Exception e){
			Assert.assertEquals(e.getMessage(), e.getMessage());
		}
    }

	@Test
	public void updateResource()throws Exception{
		List<ResourceUseInfoDTO> resourceUses = new ArrayList<>();
		resourceUses.add(new ResourceUseInfoDTO());
		resourceUses.get(0).setResourceNo("11-2222-333333");
		resourceUses.get(0).setIsRework("1");
		JSONObject result = JSONObject.parseObject("{\"RspCode\":\"0000\",\"JobID\":\"2020061815000001\"}");
		Whitebox.invokeMethod(networkLicenseUploadService,"updateResource",resourceUses,result);
	    Assert.assertNotNull(resourceUses);
    }

	@Test
	public void updateResourceOne()throws Exception{
		List<ResourceUseInfoDTO> resourceUses = new ArrayList<>();
		resourceUses.add(new ResourceUseInfoDTO());
		resourceUses.get(0).setResourceNo("11-2222-333333");
		resourceUses.get(0).setIsRework("1");
		JSONObject result = JSONObject.parseObject("{\"RspCode\":\"0001\",\"ErrorInfo\":\"文件已经上传过\"}");
		Whitebox.invokeMethod(networkLicenseUploadService,"updateResource",resourceUses,result);
        Assert.assertNotNull(resourceUses);
    }

	@Test
	public void updateResourceTwo()throws Exception{
		List<ResourceUseInfoDTO> resourceUses = new ArrayList<>();
		resourceUses.add(new ResourceUseInfoDTO());
		resourceUses.get(0).setResourceNo("11-2222-333333");
		resourceUses.get(0).setIsRework("1");
		JSONObject result = JSONObject.parseObject("");
		Whitebox.invokeMethod(networkLicenseUploadService,"updateResource",resourceUses,result);
        Assert.assertNotNull(resourceUses);
    }

    private String get500() {
        return "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18\n" +
                "228888036874350,0001APC92Y7P5PY\n" +
                "228888035864000,0000P2C54N4P301\n" +
                "228888036635270,00011937XPP752P\n" +
                "228888035511200,0001225119T83C0\n" +
                "228888035636280,00013142PY7AX4X\n" +
                "228888036352010,0001A3X345NXT18";
    }

    @Test
    public void fillProductAddressTest() throws Exception {
        PowerMockito.when(resourceInfoRepository.selectNoAddressInfo()).thenReturn(new ArrayList<>());
        networkLicenseService.fillProductAddress();
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

	@Test
	public void fillProductAddressTestTwo() throws Exception {
		List<ResourceInfoEntityDTO> list = new ArrayList<>();
		ResourceInfoEntityDTO dto = new ResourceInfoEntityDTO();
		dto.setResourceNo("TEST");
		list.add(dto);
		PowerMockito.when(resourceInfoRepository.selectNoAddressInfo()).thenReturn(list);
		networkLicenseService.fillProductAddress();
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
	}

	@Test
	public void updateProdAddress() throws Exception {
		ResourceInfoEntityDTO dto = new ResourceInfoEntityDTO();
		dto.setResourceNo("TEST");
		String result = "{\"RspCode\":\"0000\",\"MadeIn\":\"广东深深圳市\"}";
		Whitebox.invokeMethod(networkLicenseService,"updateProdAddress",result,dto);
        Assert.assertNotNull(dto);
    }

	@Test
	public void updateProdAddressTwo() throws Exception {
		ResourceInfoEntityDTO dto = new ResourceInfoEntityDTO();
		dto.setResourceNo("TEST");
		String result = "{\"RspCode\":\"0000\"}";
		Whitebox.invokeMethod(networkLicenseService,"updateProdAddress",result,dto);
        Assert.assertNotNull(dto);
    }

    @Test
    public void registrationObtain() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class,HttpClientUtil.class,JSONObject.class);
        List<ResourceUseInfoDTO> records = new ArrayList<>();
        ResourceUseInfoDTO resourceUseInfoDTO = new ResourceUseInfoDTO();
        resourceUseInfoDTO.setJobId("test123");
        resourceUseInfoDTO.setId(new Long("123456789"));
        resourceUseInfoDTO.setStartTime(new Date());
        records.add(resourceUseInfoDTO);
        DataPusher retryUtil = new DataPusher(networkLicenseUploadResultB2B);
        PowerMockito.whenNew(DataPusher.class).withAnyArguments().thenReturn(retryUtil);
        PowerMockito.when(resourceUseInfoRepository.queryNotResult(Mockito.any())).thenReturn(records).thenReturn(new ArrayList<>());
        List<SysLookupValues> sysList = new ArrayList<>();
        PowerMockito.when(sysLookupValuesService.findByLookupType(Mockito.anyString())).thenReturn(sysList);
        PowerMockito.when(emailUtils.sendMail(Mockito.any(),Mockito.anyString(),Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(true);
        networkLicenseService.registrationObtain(new ResourceUseInfoDTO());
        NetworkLicenseUploadResultDTO result = new NetworkLicenseUploadResultDTO();
        result.setRspCode("0001");
        result.setResult("0");
        result.setRemark("0000");
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(),Mockito.anyString(),Mockito.anyMap())).thenReturn(JSONObject.toJSONString(result));
        networkLicenseService.registrationObtain(new ResourceUseInfoDTO());
        Assert.assertNotNull(resourceUseInfoDTO);
    }

    @Test
    public void sendEmailWithFailedMsg() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class,HttpClientUtil.class,JSONObject.class);
        networkLicenseService.sendEmailWithFailedMsg(new HashMap<>());
        networkLicenseService.sendEmailWithFailedMsg(null);
        Map<String, String> inMap =new HashMap<>();
        inMap.put("lookupMeaning","00286523");
        List<SysLookupValues> sysList = new ArrayList<>();
        PowerMockito.when(sysLookupValuesService.findByLookupType(Mockito.anyString())).thenReturn(sysList);
        PowerMockito.when(emailUtils.sendMail(Mockito.any(),Mockito.anyString(),Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(true);
        networkLicenseService.sendEmailWithFailedMsg(inMap);
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupType(new BigDecimal("286522"));
        sysLookupValues.setLookupCode(new BigDecimal("286522"));
        sysLookupValues.setLookupMeaning("test123");
        sysList.add(sysLookupValues);
        networkLicenseService.sendEmailWithFailedMsg(inMap);
        Assert.assertNotNull(inMap);
    }

    @Test
    public void handlerLine() {
        List<ResourceUseInfoDTO> jobBarcodeList = new ArrayList<>();
        PowerMockito.when(resourceUseInfoRepository.queryByJobId(Mockito.anyString())).thenReturn(jobBarcodeList);
        networkLicenseService.handlerLine("00286523","test123",new ArrayList<>());
        List<String> resultLines = new ArrayList<>();
        resultLines.add("test123");
        resultLines.add("sn:test123,test123");
        resultLines.add("test123,test123:failed");
        resultLines.add("sn:test123,test123:登记失败");
        ResourceUseInfoDTO resourceUseInfoDTO = new ResourceUseInfoDTO();
        resourceUseInfoDTO.setStartTime(new Date());
        resourceUseInfoDTO.setJobId("test123");
        resourceUseInfoDTO.setBarcode("test123");
        resourceUseInfoDTO.setResourceNum("test123");
        jobBarcodeList.add(resourceUseInfoDTO);
        networkLicenseService.handlerLine("00286523","test123",resultLines);
        Assert.assertNotNull(resultLines);
        ResourceUseInfoDTO resourceUseInfoDTO1 = new ResourceUseInfoDTO();
        resourceUseInfoDTO1.setStartTime(new Date());
        resourceUseInfoDTO1.setJobId("test123");
        resourceUseInfoDTO1.setBarcode("12325425");
        resourceUseInfoDTO1.setResourceNum("test123");
        jobBarcodeList.add(resourceUseInfoDTO1);
        PowerMockito.when(resourceUseInfoRepository.batchUpdateWithRegistMsg(Mockito.anyList())).thenReturn(1);
        PowerMockito.when( resourceInfoDetailRepository.batchUpdateForRegisterStatus(Mockito.anyList())).thenReturn(1);
        networkLicenseService.handlerLine("00286523","test123",resultLines);
        Assert.assertNotNull(resultLines);
    }

    @Test
    public void handleB2BReturnFile() throws Exception {
        PowerMockito.mockStatic(FileUtils.class,DiskFileDownloadApi.class,
                com.zte.km.udm.common.httpclient.HttpClientUtil.class,org.apache.commons.io.FileUtils.class);
        NetworkLicenseUploadResultDTO dto =new NetworkLicenseUploadResultDTO();
        dto.setFailAmount(0);
        PowerMockito.when(resourceUseInfoRepository.updateRegistStatusByJobId(Mockito.any())).thenReturn(1);
        PowerMockito.when(resourceInfoDetailRepository.updateDetailStatusForJobIdUse(Mockito.any())).thenReturn(1);
        networkLicenseService.handleB2BReturnFile("00286523",new HashMap<>(),"test123",dto);
        dto.setFailAmount(1);
        networkLicenseService.handleB2BReturnFile("00286523",new HashMap<>(),"test123",dto);

        dto.setFileName("text.txt");
        MockMultipartFile firstFile500 = new MockMultipartFile("data", "text.txt", "text/plain", get500().getBytes(StandardCharsets.UTF_8));
        File file = new File(String.valueOf(firstFile500));
        PowerMockito.when(FileUtils.createTempFileName(Mockito.anyString())).thenReturn("text.txt");
        PowerMockito.when(cloudDiskHelper.getFile(Mockito.any(),Mockito.anyString(),Mockito.anyString())).thenReturn(file);
        PowerMockito.when(com.zte.km.udm.common.httpclient.HttpClientUtil.initDiskHeaders(Mockito.anyString())).thenReturn(new HashMap<>());
//        PowerMockito.whenNew(FileInputStream.class).withAnyArguments().thenReturn((FileInputStream) inputStream);
        byte[] message = Base64.decodeBase64(dto.getFileName());
        PowerMockito.when(com.zte.km.udm.common.httpclient.HttpClientUtil.getStream(Mockito.anyString(),Mockito.anyMap())).thenReturn(message);

        networkLicenseService.handleB2BReturnFile("00286523",new HashMap<>(),"test123",dto);
        Assert.assertNotNull(dto);
    }
}
