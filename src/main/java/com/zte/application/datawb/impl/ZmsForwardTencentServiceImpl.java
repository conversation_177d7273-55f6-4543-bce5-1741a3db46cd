package com.zte.application.datawb.impl;

/* Started by AICoder, pid:371c0c8245ae3191422c0997d5eeb57739f29e87 */

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.zte.application.datawb.WsmAssembleLinesService;
import com.zte.application.datawb.ZmsForwardTencentService;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.DateUtils;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.datawb.ZmsDeviceInventoryUploadRepository;
import com.zte.domain.model.datawb.ZmsForwardTencentRepository;
import com.zte.domain.model.datawb.ZmsStationLogUploadRepository;
import com.zte.infrastructure.remote.BarcodeCenterRemoteService;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.annotation.DataSource;
import com.zte.springbootframe.common.datasource.DatabaseType;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.RetCode;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.beans.PropertyDescriptor;
import java.util.*;
import java.util.stream.Collectors;

import static com.zte.common.utils.Constant.*;
import static com.zte.springbootframe.common.Constants.STR_ONE;

/**
 * <AUTHOR>
 * @since 2024年1月26日15:24
 */
@Service
@DataSource(DatabaseType.WMES)
public class ZmsForwardTencentServiceImpl implements ZmsForwardTencentService {

    @Value("${meituan.b2b.size:100}")
    private Integer b2bSize;

    @Autowired
    private ZmsForwardTencentRepository zmsForwardTencentRepository;

    @Autowired
    private ZmsDeviceInventoryUploadRepository zmsDeviceInventoryUploadRepository;

    @Autowired
    private ZmsStationLogUploadServiceImpl zmsStationLogUploadService;

    @Autowired
    private ZmsOverallUnitServiceImpl zmsOverallUnitService;

    @Autowired
    private ZmsIndicatorUploadServiceImpl zmsIndicatorUploadService;

    @Autowired
    private ZmsStationLogUploadRepository zmsStationLogUploadRepository;

    @Autowired
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Autowired
    private WsmAssembleLinesService wsmAssembleLinesService;

    @Autowired
    CfgCodeRuleItemServiceImpl cfgCodeRuleItemService;

    @Autowired
    BarcodeCenterRemoteService barcodeCenterRemoteService;


    @Override
    public void uploadForwardData(ZmsForwardTencentInDTO dto, String empNo) throws Exception {

        // 手工修改后重推B2B
        if (CollectionUtils.isNotEmpty(dto.getRecordIdList())) {
            uploadCompleteMachineDataByManual(dto.getRecordIdList(), empNo);
            return;
        }

        if (CollectionUtils.isNotEmpty(dto.getEntityNameList())) {
            dto.setDescLike(zmsStationLogUploadRepository.getZSUrl(LOOKUP_TYPE_FORWARD_TENCENT, LOOKUP_TYPE_FORWARD_TENCENT_BOQ));
            dto.setUserAddress(zmsStationLogUploadRepository.getZSUrl(LOOKUP_TYPE_FORWARD_TENCENT, LOOKUP_TYPE_FORWARD_TENCENT_USRADD));
            dto.setIntercept(zmsStationLogUploadRepository.getZSUrl(LOOKUP_TYPE_FORWARD_TENCENT, LOOKUP_TYPE_FORWARD_TENCENT_INTERCEPT));
        } else {
            getLookupMeanList(dto);
        }
        List<ZmsForwardTencentEntityDTO> zmsForwardTencentEntityDTOS = zmsForwardTencentRepository.getTencentEntity(dto);
        if (CollectionUtils.isEmpty(zmsForwardTencentEntityDTOS)) {
            return;
        }
        zmsForwardTencentEntityDTOS.forEach(e -> e.setUserAddress(dto.getUserAddress()));
        uploadCompleteMachineData(zmsForwardTencentEntityDTOS, empNo, dto);
    }

    /**
     * 获取数据字典配置
     */
    @Override
    public void getLookupMeanList(ZmsForwardTencentInDTO dto) {
        List<SysLookupValues> sysLookupValuesList = wsmAssembleLinesService.getSysLookupValues(Constant.LOOKUP_TYPE_FORWARD_TENCENT);
        if (CollectionUtils.isEmpty(sysLookupValuesList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SYS_LOOK_NOT_CONFIG,
                    new Object[]{Constant.LOOKUP_TYPE_FORWARD_TENCENT});
        }
        for (SysLookupValues sysLookupValues : sysLookupValuesList) {
            switch (sysLookupValues.getLookupCode().toString()) {
                case Constant.LOOKUP_TYPE_FORWARD_TENCENT_ORGID:
                    dto.setOrganizationIdList(Arrays.asList(sysLookupValues.getDescription().split(Constant.COMMA)));
                    break;
                case Constant.LOOKUP_TYPE_FORWARD_TENCENT_USRADD:
                    dto.setUserAddress(sysLookupValues.getDescription());
                    break;
                case Constant.LOOKUP_TYPE_FORWARD_TENCENT_ENAME:
                    dto.setEntityNameLike(sysLookupValues.getDescription());
                    break;
                case Constant.LOOKUP_TYPE_FORWARD_TENCENT_STAT:
                    dto.setStatusNameList(Arrays.asList(sysLookupValues.getDescription().split(Constant.COMMA)));
                    break;
                case Constant.LOOKUP_TYPE_FORWARD_TENCENT_BOQ:
                    dto.setDescLike(sysLookupValues.getDescription());
                    break;
                case Constant.LOOKUP_TYPE_FORWARD_TENCENT_CUSTOMER_ID:
                    dto.setCustomerId(sysLookupValues.getDescription());
                    break;
                case Constant.LOOKUP_TYPE_FORWARD_TENCENT_CPQD_URL:
                    dto.setCpqdUrl(sysLookupValues.getDescription());
                    break;
                case Constant.LOOKUP_TYPE_FORWARD_TENCENT_INTERCEPT:
                    dto.setIntercept(sysLookupValues.getDescription());
                    break;
                default:
                    break;
            }
        }
    }

    public void uploadCompleteMachineData(List<ZmsForwardTencentEntityDTO> zmsForwardTencentEntityDTOS, String empNo,
                                          ZmsForwardTencentInDTO dto) throws Exception {

        // 查询套餐名称
        List<String> listEntityName = zmsForwardTencentEntityDTOS.stream().map(ZmsForwardTencentEntityDTO::getEntityName).collect(Collectors.toList());
        List<ZmsCbomInfoDTO> configDescList = zmsDeviceInventoryUploadRepository.getConfigDesc(listEntityName, dto.getDescLike(), dto.getIntercept());
        // 查询部件批次号获取规则
        List<ZmsForwardTencentBatchRuleDTO> batchRuleDTOList = zmsForwardTencentRepository.getBatchRuleDTOList();

        // 按任务循环处理
        for (ZmsForwardTencentEntityDTO zmsForwardTencentEntityDTO : zmsForwardTencentEntityDTOS) {
            zmsForwardTencentEntityDTO.setConfigDescList(configDescList);
            zmsForwardTencentEntityDTO.setBatchRuleDTOList(batchRuleDTOList);
            zmsForwardTencentEntityDTO.setEmpNo(empNo);
            // 获取需要推送B2B的数据
            ZmsForwardTencentSaveDTO zmsForwardTencentSaveDTO = setServerMotherboardSn(zmsForwardTencentEntityDTO);
            if (zmsForwardTencentSaveDTO == null || CollectionUtils.isEmpty(zmsForwardTencentSaveDTO.getZmsForwardTencentDTOList())) {
                continue;
            }
            if (filterSnList(zmsForwardTencentSaveDTO.getZmsForwardTencentDTOList(), zmsForwardTencentEntityDTO)) {
                // 按照任务维度推送B2B
                batchInsert(zmsForwardTencentSaveDTO, empNo);
            }
            insertBoardSn(zmsForwardTencentSaveDTO.getBoardSnDTOList());

            // 互联网传输主表写表
            insertInternetMain(zmsForwardTencentEntityDTO);
        }
    }

    /**
     * 根据单个任务去判断是否SN已比对完成
     *
     * @param zmsForwardTencentDTOList
     * @param zmsForwardTencentEntityDTO
     * @throws Exception
     */
    public boolean filterSnList(List<ZmsForwardTencentDTO> zmsForwardTencentDTOList,
                                ZmsForwardTencentEntityDTO zmsForwardTencentEntityDTO) throws Exception {
        CustomerQualityDTO customerQualityDTO = new CustomerQualityDTO();
        customerQualityDTO.setCustomerName(TENCENT_ZH);
        List<String> zmsServerSNList = zmsForwardTencentDTOList.stream()
                .map(ZmsForwardTencentDTO::getSvrSN).distinct().collect(Collectors.toList());
        customerQualityDTO.setServerSnList(zmsServerSNList);
        customerQualityDTO.setMessageType(ZTE_IMES_TENCENT_FORWARD_QUERY);
        List<QualityCodeOutputDTO> forWardDTOList = centerfactoryRemoteService.getRealTimeInteractiveB2B(customerQualityDTO);
        if (CollectionUtils.isEmpty(forWardDTOList)) {
            zmsForwardTencentEntityDTO.setDataUpStatus(STR_NUMBER_ZERO);
            return true;
        }
        QualityCodeOutputDTO qualityCodeOutputDTO = forWardDTOList.get(INT_0);
        if (qualityCodeOutputDTO.getQualityCode().equals(STR_NUMBER_ZERO)) {
            zmsForwardTencentEntityDTO.setDataUpStatus(Constant.STR_NUMBER_TWO);
            zmsForwardTencentEntityDTO.setFailReason(STRING_EMPTY);
            return false;
        } else {
            zmsForwardTencentEntityDTO.setDataUpStatus(STR_TWO_NEGATIVE);
            zmsForwardTencentEntityDTO.setFailReason(qualityCodeOutputDTO.getErrCause());
            if (StringUtils.isEmpty(qualityCodeOutputDTO.getErrCause())) {
                zmsForwardTencentEntityDTO.setFailReason(Constant.ZMS_TENCENT_QL_ERROR);
            }
        }
        return true;
    }

    /**
     * 互联网传输主表写表
     */
    public void insertInternetMain(ZmsForwardTencentEntityDTO zmsForwardTencentEntityDTO) {
        ZmsInternetMainDTO zmsInternetMainDTO = new ZmsInternetMainDTO();
        BeanUtils.copyProperties(zmsForwardTencentEntityDTO, zmsInternetMainDTO);
        zmsInternetMainDTO.setCreatedBy(zmsForwardTencentEntityDTO.getEmpNo());
        zmsInternetMainDTO.setLastUpdatedBy(zmsForwardTencentEntityDTO.getEmpNo());
        zmsInternetMainDTO.setCustomerName(TENCENT_ZH);
        zmsInternetMainDTO.setDataUpStatus(Constant.ZERO_STRING);
        String dataType = cfgCodeRuleItemService.getSmallBoxSize(LOOKUP_TYPE_824009200001);
        zmsInternetMainDTO.setDataType(dataType);
        zmsForwardTencentRepository.insertInternetMain(zmsInternetMainDTO);
    }

    /**
     * 批量新增
     */
    /**
     * 批量新增
     */
    public void batchInsert(ZmsForwardTencentSaveDTO zmsForwardTencentSaveDTO, String empNo) throws Exception {

        if (StringUtils.isEmpty(empNo)) {
            empNo = Constant.SYSTEM;
        }
        //推送B2B
        if (CollectionUtils.isEmpty(zmsForwardTencentSaveDTO.getZmsForwardTencentDTOList())) {
            return;
        }
        pushDataToB2B(zmsForwardTencentSaveDTO.getZmsForwardTencentDTOList(), empNo);
    }

    private void insertBoardSn(List<ZmsForwardTencentBoardSnDTO> boardSnDTOList) {
        // 主板SN数据写表
        if (CollectionUtils.isEmpty(boardSnDTOList)) {
            return;
        }
        for (List<ZmsForwardTencentBoardSnDTO> tempInsertList : CommonUtils.splitList(boardSnDTOList, Constant.INT_500)) {
            zmsForwardTencentRepository.insertForwardTencentBoardSn(tempInsertList);
        }
    }

    /**
     * 组装数据推送B2B
     */
    public void pushDataToB2B(List<ZmsForwardTencentDTO> zmsForwardTencentDTOList, String empNo) throws Exception {
        List<CustomerDataLogDTO> dataList = new ArrayList<>();
        List<ZmsMesInfoUploadLogDTO> zmsMesInfoUploadDTOList = new ArrayList<>();
        for (List<ZmsForwardTencentDTO> list : CommonUtils.splitList(zmsForwardTencentDTOList, INT_500)) {
            String id = UUID.randomUUID().toString().replace(CENTER_LINE, STRING_EMPTY);

            List<ZmsForwardTencentPartInfoDTO> zmsForwardTencentPartInfoDTOList = new ArrayList<>();
            for (ZmsForwardTencentDTO zmsForwardTencentDTO : list) {
                zmsForwardTencentDTO.setMessageId(id);
                zmsForwardTencentDTO.setMessageType(ZTE_IMES_TENCENT_FORWARD_UPLOAD);
                ZmsForwardTencentPartInfoDTO zmsForwardTencentPartInfoDTO = new ZmsForwardTencentPartInfoDTO();
                BeanUtils.copyProperties(zmsForwardTencentDTO, zmsForwardTencentPartInfoDTO, getNullPropertyNames(zmsForwardTencentDTO));
                zmsForwardTencentPartInfoDTOList.add(zmsForwardTencentPartInfoDTO);
            }
            ZmsForwardTencentDataDTO zmsForwardTencentDataDTO = new ZmsForwardTencentDataDTO();
            zmsForwardTencentDataDTO.setPartInfo(zmsForwardTencentPartInfoDTOList);
            ZmsForwardTencentUploadDTO zmsForwardTencentUploadDTO = new ZmsForwardTencentUploadDTO();
            zmsForwardTencentUploadDTO.setAction(SUPPLIER_WRITE_SERVER_PART_INFO);
            zmsForwardTencentUploadDTO.setStartCompany(STR_ZTE);
            zmsForwardTencentUploadDTO.setMethod(RUN);
            zmsForwardTencentUploadDTO.setData(zmsForwardTencentDataDTO);

            CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
            customerDataLogDTO.setId(id);
            customerDataLogDTO.setOrigin(MES);
            customerDataLogDTO.setCustomerName(TENCENT);
            customerDataLogDTO.setProjectName(TENCENT_FORWARD_ZH);
            customerDataLogDTO.setProjectPhase(DateUtils.format(new Date(), DateUtils.DATE_FORMAT_FULL));
            customerDataLogDTO.setCooperationMode(STRING_EMPTY);
            customerDataLogDTO.setMessageType(ZTE_IMES_TENCENT_FORWARD_UPLOAD);
            customerDataLogDTO.setContractNo(STRING_EMPTY);
            customerDataLogDTO.setTaskNo(list.get(INT_0).getEntityName());
            customerDataLogDTO.setItemNo(STRING_EMPTY);
            customerDataLogDTO.setSn(list.get(INT_0).getSvrSN());
            customerDataLogDTO.setJsonData(JSON.toJSONString(zmsForwardTencentUploadDTO, SerializerFeature.WriteMapNullValue));
            customerDataLogDTO.setFactoryId(INT_51);
            customerDataLogDTO.setCreateBy(empNo);
            customerDataLogDTO.setLastUpdatedBy(empNo);
            dataList.add(customerDataLogDTO);

            // 组装日志对象
            ZmsMesInfoUploadLogDTO zmsMesInfoUploadDTO = new ZmsMesInfoUploadLogDTO();
            BeanUtils.copyProperties(customerDataLogDTO, zmsMesInfoUploadDTO);
            zmsMesInfoUploadDTOList.add(zmsMesInfoUploadDTO);
        }

        // 写入上传日志
        zmsIndicatorUploadService.insertMesInfoUploadLog(zmsMesInfoUploadDTOList);

        // 调用iMES接口上传B2B
        zmsIndicatorUploadService.pushDataToB2B(dataList);

        // 删除历史正向数据
        List<String> entityNameList = zmsForwardTencentDTOList.stream().map(ZmsForwardTencentDTO::getEntityName).distinct().collect(Collectors.toList());
        for (List<String> tempDeleteList : CommonUtils.splitList(entityNameList, Constant.INT_500)) {
            zmsForwardTencentRepository.deleteForwardTencent(tempDeleteList);
        }

        // 正向数据写表
        for (List<ZmsForwardTencentDTO> tempInsertList : CommonUtils.splitList(zmsForwardTencentDTOList, Constant.INT_500)) {
            zmsForwardTencentRepository.insertForwardTencent(tempInsertList);
        }
    }

    public static String[] getNullPropertyNames(ZmsForwardTencentDTO zmsForwardTencentDTO) {
        final BeanWrapper src = new BeanWrapperImpl(zmsForwardTencentDTO);
        PropertyDescriptor[] pds = src.getPropertyDescriptors();
        Set<String> emptyNames = new HashSet<>();
        for (PropertyDescriptor pd : pds) {
            try {
                Object srcValue = src.getPropertyValue(pd.getName());
                if (srcValue == null) {
                    emptyNames.add(pd.getName());
                }
            } catch (Exception e) {
                continue;
            }
        }
        String[] result = new String[emptyNames.size()];
        return emptyNames.toArray(result);
    }

    /**
     * 设置任务对应服务器sn 主板sn
     */
    public ZmsForwardTencentSaveDTO setServerMotherboardSn(ZmsForwardTencentEntityDTO dto) throws Exception {
        // 获取任务对应的装配物料
        List<CpmConfigItemAssembleDTO> configDetailDTOList = wsmAssembleLinesService.getAssemblyMaterialsByEntityId(Integer.valueOf(dto.getEntityId()));
        if (CollectionUtils.isEmpty(configDetailDTOList)) {
            return null;
        }
        // 根据去重后的itemCode，调iMES获取物料代码客户基础信息
        List<String> itemCodeList = configDetailDTOList.stream().map(CpmConfigItemAssembleDTO::getItemCode)
                .filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        List<CustomerItemsDTO> customerItemsDTOList = centerfactoryRemoteService.getCustomerItemsInfo(dto.getUserAddress(), itemCodeList);
        // 没物料基础信息，则返回
        if (CollectionUtils.isEmpty(customerItemsDTOList)) {
            return null;
        }
        // 过滤出服务器sn
        List<CustomerItemsDTO> completeMachineList = customerItemsDTOList.stream()
                .filter(e -> StringUtils.equals(e.getProjectType(), NumConstant.STR_3)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(completeMachineList)) {
            return null;
        }
        // 多个服务器sn也报错
        if (completeMachineList.size() > NumConstant.NUM_ONE) {
            return null;
        }
        // 服务器SN对应客户物料信息
        CustomerItemsDTO serviceSnCustomerItemsDTO = completeMachineList.get(NumConstant.NUM_ZERO);
        CpmConfigItemAssembleDTO cpmConfigItemAssembleDTO = configDetailDTOList.stream()
                .filter(e -> StringUtils.equals(e.getItemCode(), serviceSnCustomerItemsDTO.getZteCode())).findFirst().orElse(null);
        if (cpmConfigItemAssembleDTO == null) {
            return null;
        }
        //服务器SN
        String serviceSn = cpmConfigItemAssembleDTO.getItemBarcode();
        dto.setServerSn(serviceSn);
        //获取型号
        String customerModel = serviceSnCustomerItemsDTO.getCustomerModel();
        dto.setCustomerModel(customerModel);
        // 主板sn
        List<CustomerItemsDTO> motherboardList = customerItemsDTOList.stream()
                .filter(e -> StringUtils.equals(e.getProjectType(), NumConstant.STR_5)).collect(Collectors.toList());
        // 获取任务对应的装配物料中的主板条码
        List<String> boardSnList = getMainBoardSn(configDetailDTOList, motherboardList);
        // 获取服务器sn对应的装配物料
        List<WsmAssembleLinesEntityDTO> wsmAssembleLinesList = wsmAssembleLinesService.getAssemblyMaterials(serviceSn);
        if (CollectionUtils.isEmpty(wsmAssembleLinesList)) {
            return null;
        }
        // 部件信息查询需要查询三层，物料代码为1开头的需要再向下查询
        zmsOverallUnitService.getAllWsmAssembleLines(wsmAssembleLinesList);
        // 机框模组
        List<String> shelfModItemCodeList = customerItemsDTOList.stream().filter(e ->
                StringUtils.equals(e.getProjectType(), NumConstant.STR_8)).map(CustomerItemsDTO::getZteCode).collect(Collectors.toList());
        List<String> shelfModItemBarcodeList = configDetailDTOList.stream().filter(i ->
                shelfModItemCodeList.contains(i.getItemCode())).map(CpmConfigItemAssembleDTO::getItemBarcode).collect(Collectors.toList());
        // 机框模组查询三层装配关系
        getThreeWsmAssembleLines(shelfModItemBarcodeList, wsmAssembleLinesList);
        // 根据去重后的itemCode，调iMES获取物料代码客户基础信息
        List<String> zbItemCodeList = wsmAssembleLinesList.stream()
                .map(WsmAssembleLinesEntityDTO::getItemCode).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        List<CustomerItemsDTO> serviceSnCustomerItemsDTOList = centerfactoryRemoteService.getCustomerItemsInfo(dto.getUserAddress(), zbItemCodeList);
        // 获取服务器SN对应的装配物料中的主板条码
        List<String> serviceSnBoardSnList = getServiceSnBoardSn(serviceSnCustomerItemsDTOList, wsmAssembleLinesList);

        if (CollectionUtils.isNotEmpty(serviceSnCustomerItemsDTOList)) {
            customerItemsDTOList.addAll(serviceSnCustomerItemsDTOList);
        }
        ZmsForwardTencentSaveDTO zmsForwardTencentSaveDTO = new ZmsForwardTencentSaveDTO();
        // 去除整机物料、机框模组和不在iMES基础数据中的物料
        List<ZmsForwardTencentDTO> zmsForwardTencentDTOList = getZmsForwardTencentDTOList(configDetailDTOList, wsmAssembleLinesList, customerItemsDTOList);
        // 组装正向数据对象
        zmsForwardTencentSaveDTO.setZmsForwardTencentDTOList(getZmsForwardTencentDTO(dto, customerItemsDTOList, zmsForwardTencentDTOList));
        // 组装主板SN对象
        zmsForwardTencentSaveDTO.setBoardSnDTOList(getZmsForwardTencentBoardSnDTO(dto, boardSnList, serviceSnBoardSnList));
        return zmsForwardTencentSaveDTO;
    }

    /**
     * 机框模组查询三层装配关系
     */
    public void getThreeWsmAssembleLines(List<String> itemBarcodeList, List<WsmAssembleLinesEntityDTO> wsmAssembleLinesList) {

        // 第一层
        if (CollectionUtils.isEmpty(itemBarcodeList)) {
            return;
        }
        List<WsmAssembleLinesEntityDTO> wsmAssembleLinesList1 = wsmAssembleLinesService.getAssemblyMaterialList(itemBarcodeList);
        if (CollectionUtils.isNotEmpty(wsmAssembleLinesList1)) {
            wsmAssembleLinesList.addAll(wsmAssembleLinesList1);
        }

        // 第二层
        List<String> itemBarcodeList2 = wsmAssembleLinesList1.stream().filter(i -> i.getItemCode().startsWith(STR_ONE)).map(WsmAssembleLinesEntityDTO::getItemBarcode).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(itemBarcodeList2)) {
            List<String> newitemBarcodeList = wsmAssembleLinesList1.stream().map(m->m.getItemBarcode()).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(newitemBarcodeList)){
                return;
            }
            newitemBarcodeList.removeAll(itemBarcodeList);
            getThreeWsmAssembleLines(newitemBarcodeList,wsmAssembleLinesList);
            return;
        }
        List<WsmAssembleLinesEntityDTO> wsmAssembleLinesList2 = wsmAssembleLinesService.getAssemblyMaterialList(itemBarcodeList2);
        if (CollectionUtils.isNotEmpty(wsmAssembleLinesList2)) {
            wsmAssembleLinesList.addAll(wsmAssembleLinesList2);
        }

        // 第三层
        List<String> itemBarcodeList3 = wsmAssembleLinesList2.stream().filter(i -> i.getItemCode().startsWith(STR_ONE)).map(WsmAssembleLinesEntityDTO::getItemBarcode).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(itemBarcodeList3)) {
            return;
        }
        List<WsmAssembleLinesEntityDTO> wsmAssembleLinesList3 = wsmAssembleLinesService.getAssemblyMaterialList(itemBarcodeList3);
        if (CollectionUtils.isNotEmpty(wsmAssembleLinesList3)) {
            wsmAssembleLinesList.addAll(wsmAssembleLinesList3);
        }
    }

    /**
     * 去除整机物料和不在iMES基础数据中的物料
     */
    public List<ZmsForwardTencentDTO> getZmsForwardTencentDTOList(List<CpmConfigItemAssembleDTO> configDetailDTOList, List<WsmAssembleLinesEntityDTO> wsmAssembleLinesList,
                                                                  List<CustomerItemsDTO> customerItemsDTOList) {
        List<ZmsForwardTencentDTO> zmsForwardTencentDTOList = new ArrayList<>();
        List<String> imesItemCodeList = customerItemsDTOList.stream().filter(i ->
                        !StringUtils.equals(i.getProjectType(), NumConstant.STR_3) && !StringUtils.equals(i.getProjectType(), NumConstant.STR_8))
                .map(CustomerItemsDTO::getZteCode).distinct().collect(Collectors.toList());
        for (CpmConfigItemAssembleDTO cpmConfigItemAssembleDTO : configDetailDTOList) {
            if (imesItemCodeList.contains(cpmConfigItemAssembleDTO.getItemCode())) {
                ZmsForwardTencentDTO zmsForwardTencentDTO = new ZmsForwardTencentDTO();
                zmsForwardTencentDTO.setItemCode(cpmConfigItemAssembleDTO.getItemCode());
                zmsForwardTencentDTO.setOriPartSN(cpmConfigItemAssembleDTO.getItemBarcode());
                zmsForwardTencentDTO.setScanPartSN(cpmConfigItemAssembleDTO.getItemBarcode());
                zmsForwardTencentDTO.setQty(cpmConfigItemAssembleDTO.getBarcodeQty());
                zmsForwardTencentDTOList.add(zmsForwardTencentDTO);
            }
        }
        for (WsmAssembleLinesEntityDTO wsmAssembleLinesEntityDTO : wsmAssembleLinesList) {
            if (imesItemCodeList.contains(wsmAssembleLinesEntityDTO.getItemCode())) {
                ZmsForwardTencentDTO zmsForwardTencentDTO = new ZmsForwardTencentDTO();
                zmsForwardTencentDTO.setItemCode(wsmAssembleLinesEntityDTO.getItemCode());
                zmsForwardTencentDTO.setOriPartSN(wsmAssembleLinesEntityDTO.getItemBarcode());
                zmsForwardTencentDTO.setScanPartSN(wsmAssembleLinesEntityDTO.getItemBarcode());
                zmsForwardTencentDTO.setQty(wsmAssembleLinesEntityDTO.getItemQty().toString());
                zmsForwardTencentDTOList.add(zmsForwardTencentDTO);
            }
        }
        return zmsForwardTencentDTOList.stream().sorted(Comparator.comparing(ZmsForwardTencentDTO::getItemCode)).collect(Collectors.toList());
    }

    public List<ZmsForwardTencentDTO> getZmsForwardTencentDTO(ZmsForwardTencentEntityDTO dto, List<CustomerItemsDTO> customerItemsDTOList,
                                                              List<ZmsForwardTencentDTO> zmsForwardTencentDTOList) throws Exception {

        if (CollectionUtils.isEmpty(zmsForwardTencentDTOList)) {
            return zmsForwardTencentDTOList;
        }
        // 调用中试接口,获取整机任务信息
        //ZmsSspTaskInfoDTO zmsSspTaskInfoDTO = getSspTaskInfo(dto.getServerSn());
        ZmsSspTaskInfoDTO zmsSspTaskInfoDTO = getSspTaskInfo(dto.getEntityName());
        List<String> barcodeList = zmsForwardTencentDTOList.stream().map(ZmsForwardTencentDTO::getScanPartSN).distinct().collect(Collectors.toList());
        //查询条码扩展信息数据
        Map<String, String> barcodeExpandMap = getBarcodeExpandMap(barcodeList);
        String itemCode = null;
        int num = NumConstant.NUM_ZERO;
        List<ZmsForwardTencentDTO> zmsForwardTencentDtopuList = new ArrayList<>();
        List<ZmsForwardTencentDTO> newZmsForwardTencentDtoList = new ArrayList<>();
        for (ZmsForwardTencentDTO zmsForwardTencentDTO : zmsForwardTencentDTOList) {
            zmsForwardTencentDTO.setEntityId(dto.getEntityId());
            zmsForwardTencentDTO.setEntityName(dto.getEntityName());
            zmsForwardTencentDTO.setContractNumber(dto.getContractNumber());
            zmsForwardTencentDTO.setCreatedBy(dto.getEmpNo());
            zmsForwardTencentDTO.setLastUpdatedBy(dto.getEmpNo());
            zmsForwardTencentDTO.setId(UUID.randomUUID().toString().replace(CENTER_LINE, STRING_EMPTY));
            zmsForwardTencentDTO.setSvrSN(dto.getServerSn());
            //设置客户规格
            zmsForwardTencentDTO.setServerModelName(dto.getCustomerModel());

            ZmsCbomInfoDTO zmsCbomInfoDTO = dto.getConfigDescList().stream().filter(f -> f.getEntityName().equals(dto.getEntityName())).findFirst().orElse(null);
            if (zmsCbomInfoDTO != null) {
                // 标准设备类型:套餐名称
                zmsForwardTencentDTO.setDeviceClassName(zmsCbomInfoDTO.getCbomNameCn());
                // 机型版本号
                zmsForwardTencentDTO.setStrVersion(zmsCbomInfoDTO.getCbomVerison());
            }
            // iMES基础信息数据
            matchImesCustomerInfo(zmsForwardTencentDTO, customerItemsDTOList);
            if (StringUtils.equals(zmsForwardTencentDTO.getItemCode(), itemCode)) {
                num += NumConstant.NUM_ONE;
            } else {
                num = NumConstant.NUM_ZERO;
            }
            //以防会被中试接口重新赋值，先取出来
            String scanPartSN = zmsForwardTencentDTO.getScanPartSN();
            // 中试数据
            zmsForwardTencentDtopuList = matchSspTaskInfo(zmsForwardTencentDTO, zmsSspTaskInfoDTO, dto.getBatchRuleDTOList(), num, customerItemsDTOList);
            if(CollectionUtils.isNotEmpty(zmsForwardTencentDtopuList)){
                newZmsForwardTencentDtoList.addAll(zmsForwardTencentDtopuList);
            }
            itemCode = zmsForwardTencentDTO.getItemCode();

            //针对部件类型 = cable or 风扇 子卡，cpu设置不同的生产批次
            this.setProductionBatch(dto, zmsForwardTencentDTO, barcodeExpandMap, scanPartSN);

            //如果部件类型=内存时，客户物料代码需要按照ZTE代码+中试接口返回的part_number作为条件匹配ZTE规格型号和ZTE代码查询客户代码和PN码
            this.ncOtherCustomerInfo(customerItemsDTOList, zmsForwardTencentDTO);

            // 如果部件类型=背板或子卡时，客户代码需要按照ZTE代码【itemCode】+套餐名【deviceClassName】,作为条件匹配
            // customerItemsDTO中的ZTE代码【zteCode】和套餐名【customerModer】查询客户代码
            this.bbOrZkOtherCustomerInfo(customerItemsDTOList, zmsForwardTencentDTO);
        }

        zmsForwardTencentDTOList.stream()
                .filter(tencentDTO -> ZH_GPU.equals(tencentDTO.getPartType()))
                .findFirst()
                .ifPresent(gpuDTO -> {
                    List<String> snList = new ArrayList<>();
                    snList.add(gpuDTO.getScanPartSN());
                    List<ZmsGPUTempDTO> zmsGpuTempDtos = this.zmsForwardTencentRepository.getGpnTemp(snList);
                    if(CollectionUtils.isNotEmpty(zmsGpuTempDtos)) {
                        ZmsGPUTempDTO zmsGpuTempDto = zmsGpuTempDtos.get(INT_0);
                        gpuDTO.setPartType(ZH_ZK);
                        gpuDTO.setCollectSlot(zmsGpuTempDto.getCollectSlot());
                        gpuDTO.setFw(STRING_EMPTY);
                        gpuDTO.setMaterialsCode(zmsGpuTempDto.getMaterialsCode());
                        gpuDTO.setOriPartSN(zmsGpuTempDto.getGpuSn());
                        gpuDTO.setProductionBatch(zmsGpuTempDto.getGpuSn().substring(INT_4, INT_7));
                        gpuDTO.setScanPartSN(zmsGpuTempDto.getGpuSn());
                        gpuDTO.setSlot(zmsGpuTempDto.getCollectSlot());
                        gpuDTO.setOriPartPN(zmsGpuTempDto.getOriPartPn());
                        gpuDTO.setScanPartPN(zmsGpuTempDto.getScanPartPn());
                    }
                });
        newZmsForwardTencentDtoList.removeAll(newZmsForwardTencentDtoList.stream().filter(m->zmsForwardTencentDTOList.stream().anyMatch(n->n.getOriPartSN().equals(m.getOriPartSN()))).collect(Collectors.toList()));
        zmsForwardTencentDTOList.removeAll(zmsForwardTencentDTOList.stream().filter(m->ZH_GPU.equals(m.getPartType())&&StringUtils.isEmpty(m.getCollectSlot())).collect(Collectors.toList()));
        newZmsForwardTencentDtoList.addAll(zmsForwardTencentDTOList);
        return  newZmsForwardTencentDtoList;
    }

    /**
     * 如果部件类型=内存时，客户物料代码需要按照ZTE代码+中试接口返回的part_number作为条件匹配ZTE规格型号和ZTE代码查询客户代码和PN码
     *
     * @param customerItemsDTOList
     * @param zmsForwardTencentDTO
     */
    private void ncOtherCustomerInfo(List<CustomerItemsDTO> customerItemsDTOList, ZmsForwardTencentDTO zmsForwardTencentDTO) {
        //类型要为内存
        if (!StringUtils.equals(ZH_NC, zmsForwardTencentDTO.getPartType())) {
            return;
        }
        CustomerItemsDTO customerItemsDTO = customerItemsDTOList.stream().filter(i ->
                StringUtils.equals(i.getZteCode(), zmsForwardTencentDTO.getItemCode()) &&
                        StringUtils.equals(i.getZteBrandStyle(), zmsForwardTencentDTO.getPartNumber())
        ).findFirst().orElse(null);
        if (Objects.nonNull(customerItemsDTO)) {
            // 基础信息表—客户代码
            zmsForwardTencentDTO.setMaterialsCode(customerItemsDTO.getCustomerCode());
            // 基础信息表—PN code
            zmsForwardTencentDTO.setOriPartPN(customerItemsDTO.getPnCode());
            // 基础信息表—PN code
            zmsForwardTencentDTO.setScanPartPN(customerItemsDTO.getPnCode());
        }
    }


    /**
     * 如果部件类型=背板或子卡时，客户代码需要按照ZTE代码【itemCode】+套餐名【deviceClassName】
     * 作为条件匹配 customerItemsDTO中的ZTE代码【zteCode】和套餐名【customerModer】查询客户代码
     *
     * @param customerItemsDTOList
     * @param zmsForwardTencentDTO
     */
    public void bbOrZkOtherCustomerInfo(List<CustomerItemsDTO> customerItemsDTOList, ZmsForwardTencentDTO zmsForwardTencentDTO) {
        //类型要为背板，或者为子卡
        if (!StringUtils.equals(ZH_ZK, zmsForwardTencentDTO.getPartType()) && !StringUtils.equals(ZH_BB, zmsForwardTencentDTO.getPartType())) {
            return;
        }
        CustomerItemsDTO customerItemsDTO = customerItemsDTOList.stream().filter(i ->
                StringUtils.equals(i.getZteCode(), zmsForwardTencentDTO.getItemCode()) &&
                        StringUtils.equals(i.getCustomerModel(), zmsForwardTencentDTO.getDeviceClassName())
        ).findFirst().orElse(null);
        if (Objects.nonNull(customerItemsDTO)) {
            // 基础信息表—客户代码
            zmsForwardTencentDTO.setMaterialsCode(customerItemsDTO.getCustomerCode());
        }
    }


    /**
     * 查询条码扩展信息数据
     *
     * @param barcodeList
     * @return
     * @throws Exception
     */
    private Map<String, String> getBarcodeExpandMap(List<String> barcodeList) throws Exception {
        BarcodeExpandQueryDTO barcodeExpandQueryDTO = new BarcodeExpandQueryDTO();
        barcodeExpandQueryDTO.setBarcodeList(barcodeList);
        //条码扩展信息查询
        List<BarcodeExpandDTO> barcodeExpandList = barcodeCenterRemoteService.expandQuery(barcodeExpandQueryDTO);
        return barcodeExpandList
                .stream()
                .filter(x -> StringUtils.isNotBlank(x.getProdBatchNo())).collect(Collectors.toMap(BarcodeExpandDTO::getBarcode, BarcodeExpandDTO::getProdBatchNo, (a, b) -> a));
    }

    /**
     * 针对部件类型 = cable or 风扇 子卡，cpu设置不同的生产批次
     *
     * @param dto
     * @param zmsForwardTencentDTO
     * @param barcodeExpandMap
     * @param scanPartSN
     */
    private void setProductionBatch(ZmsForwardTencentEntityDTO dto, ZmsForwardTencentDTO zmsForwardTencentDTO, Map<String, String> barcodeExpandMap, String scanPartSN) {
        if (StringUtils.isEmpty(zmsForwardTencentDTO.getPartType())) {
            return;
        }
        if (StringUtils.equals(zmsForwardTencentDTO.getPartType(), ZH_CABLE) || StringUtils.equals(zmsForwardTencentDTO.getPartType(), ZH_FS)) {
            //如果部件类型 = cable or 风扇时,取M后面的第四到第七位：RSVRG228AD-M20240200326
            String entityName = zmsForwardTencentDTO.getEntityName();
            zmsForwardTencentDTO.setProductionBatch(getCableProductionBatch(entityName));
            return;
        }

        if (StringUtils.equals(zmsForwardTencentDTO.getPartType(), ZH_ZK)) {
            //如果部件类型是子卡时，按照互联网看板基础表维护的截取规则截取
            zmsForwardTencentDTO.setProductionBatch(getProductionBatch(dto.getBatchRuleDTOList(), zmsForwardTencentDTO.getPartType(),
                    zmsForwardTencentDTO.getPartVendor(), zmsForwardTencentDTO.getOriPartSN()));
            return;
        }
        if (StringUtils.equals(zmsForwardTencentDTO.getPartType(), ZH_CPU)) {
            //如果部件类型 = CPU 时，需要调用条码中心接口获取批次信息
            zmsForwardTencentDTO.setProductionBatch(barcodeExpandMap.get(scanPartSN));
        }

    }

    private String getCableProductionBatch(String productionBatch) {
        // 找到 "-M" 的位置
        int mIndex = productionBatch.indexOf(M_VALUE);
        if (mIndex != Constant.INT_B1) {
            // 计算需要提取的子字符串的起始和结束位置
            int startIndex = mIndex + Constant.INT_4;
            int endIndex = mIndex + INT_8;
            // 提取子字符串
            return productionBatch.substring(startIndex, endIndex);
        }
        return null;
    }

    public void matchImesCustomerInfo(ZmsForwardTencentDTO zmsForwardTencentDTO, List<CustomerItemsDTO> customerItemsDTOList) {

        CustomerItemsDTO customerItemsDTO = customerItemsDTOList.stream().filter(i ->
                StringUtils.equals(i.getZteCode(), zmsForwardTencentDTO.getItemCode())).findFirst().orElse(null);
        if (customerItemsDTO == null) {
            return;
        }
        // 基础信息表—客户型号
        zmsForwardTencentDTO.setManufacturerModel(customerItemsDTO.getCustomerModel());
        // 基础信息表—客户代码
        zmsForwardTencentDTO.setMaterialsCode(customerItemsDTO.getCustomerCode());
        // 基础信息表—PN code
        zmsForwardTencentDTO.setOriPartPN(customerItemsDTO.getPnCode());
        // 基础信息表-部件类型为主板时,取PCB版本
        this.setPCBAVersion(zmsForwardTencentDTO, customerItemsDTO);
        // 基础信息表—客户部件类型
        zmsForwardTencentDTO.setPartType(customerItemsDTO.getCustomerComponentType());
        // 基础信息表—客户供应商
        zmsForwardTencentDTO.setPartVendor(customerItemsDTO.getCustomerSupplier());
        // 基础信息表—PN code
        zmsForwardTencentDTO.setScanPartPN(customerItemsDTO.getPnCode());
    }


    /**
     * 根据部件条码获取imes接口中PCBAVersion
     *
     * @param zmsForwardTencentDTO
     * @param customerItemsDTO
     */
    private void setPCBAVersion(ZmsForwardTencentDTO zmsForwardTencentDTO, CustomerItemsDTO customerItemsDTO) {
        //部件类型为主板
        if (StringUtils.equals(customerItemsDTO.getCustomerComponentType(), ZH_ZHU_BAN) && StringUtils.isNotBlank(zmsForwardTencentDTO.getScanPartSN())) {
            SpecifiedPsTaskReq specifiedPsTaskReq = new SpecifiedPsTaskReq();
            specifiedPsTaskReq.setSn(zmsForwardTencentDTO.getScanPartSN());
            List<SpecifiedPsTaskDTO> specifiedPsTaskList = centerfactoryRemoteService.getSpecifiedPsTaskList(specifiedPsTaskReq);
            if (CollectionUtils.isNotEmpty(specifiedPsTaskList)) {
                zmsForwardTencentDTO.setPCBAVersion(specifiedPsTaskList.get(INT_0).getPcbVersion());
            }
        }
    }

    public List<ZmsForwardTencentDTO> matchSspTaskInfo(ZmsForwardTencentDTO zmsForwardTencentDTO,
                                                       ZmsSspTaskInfoDTO zmsSspTaskInfoDTO,
                                                       List<ZmsForwardTencentBatchRuleDTO> batchRuleDTOList,
                                                       int num,
                                                       List<CustomerItemsDTO> customerItemsDTOList) {
        List<ZmsForwardTencentDTO> tencentDtos = new ArrayList<>();
        // 1. 基础数据准备
        List<ZmsSspTaskDtailDTO> zmsSspTaskDtailDTOList = zmsSspTaskInfoDTO.getSspList();
        if (CollectionUtils.isEmpty(zmsSspTaskDtailDTOList)) {
            return tencentDtos;
        }

        ZmsSspTaskDtailDTO zmsSspTaskDtailDTO = zmsSspTaskDtailDTOList.get(INT_0);
        zmsForwardTencentDTO.setSvrAssetId(zmsSspTaskDtailDTO.getAssetNum());

        // 2. 获取并过滤目标项（安全处理type为null的情况）
        List<ZmsSspTaskItemDTO> filteredItems = getFilteredItems(zmsForwardTencentDTO, zmsSspTaskDtailDTO);
        if (CollectionUtils.isEmpty(filteredItems)) {
            return tencentDtos;
        }

        //类型不为GPU时，正常处理
        if (!StringUtils.equals(ZH_GPU, zmsForwardTencentDTO.getPartType())) {
            if (num + NumConstant.NUM_ONE > filteredItems.size()) {
                return tencentDtos;
            }
            ZmsSspTaskItemDTO dto = filteredItems.get(num);
            zmsForwardTencentDTO.setFw(dto.getFwVer());
            assembleItemData(zmsForwardTencentDTO, dto, batchRuleDTOList);
        }else{
            tencentDtos = selectItem(filteredItems, customerItemsDTOList,zmsForwardTencentDTO,batchRuleDTOList);
        }
        return tencentDtos;
    }

    // 私有方法1：获取过滤后的项列表（处理type为null的情况）
    private List<ZmsSspTaskItemDTO> getFilteredItems(ZmsForwardTencentDTO dto, ZmsSspTaskDtailDTO detailDTO) {
        return getZmsSspTaskItemDTOList(detailDTO).stream()
                .filter(i -> dto.getItemCode().equals(i.getItemCode()))
                .sorted(Comparator.comparing(ZmsSspTaskItemDTO::getSn))
                .collect(Collectors.toList());
    }

    // 私有方法2：选择目标项（合并GPU和非GPU处理逻辑）
    public List<ZmsForwardTencentDTO> selectItem(List<ZmsSspTaskItemDTO> items,
                                                  List<CustomerItemsDTO> customerItems, ZmsForwardTencentDTO dto,
                                                  List<ZmsForwardTencentBatchRuleDTO> batchRules) {
        // 使用partitioningBy一次性分类
        Map<Boolean, List<ZmsSspTaskItemDTO>> partitioned = items.stream()
                .collect(Collectors.partitioningBy(
                        i -> StringUtils.equals(ZH_GPU, i.getType())
                ));

        List<ZmsSspTaskItemDTO> gpuList = partitioned.get(true);
        List<ZmsForwardTencentDTO> resultList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(gpuList)) {
            for (ZmsSspTaskItemDTO gpuItem : gpuList) {
                resultList.add(get(gpuItem,dto,customerItems,batchRules));
            }
            return resultList;
        }

        // 如果没有GPU项，返回空列表
        return Collections.emptyList();
    }

    public ZmsForwardTencentDTO get(ZmsSspTaskItemDTO gpuItem,ZmsForwardTencentDTO dto,List<CustomerItemsDTO> customerItems,List<ZmsForwardTencentBatchRuleDTO> batchRules){
        ZmsForwardTencentDTO newDto = new ZmsForwardTencentDTO();
        BeanUtils.copyProperties(dto,newDto);
        if (gpuItem.getUbbType() == INT_1) {
            CustomerItemsDTO customerItem = customerItems.stream()
                    .filter(i -> newDto.getItemCode().equals(i.getZteCode())&&STR_NINE.equals(i.getProjectType()))
                    .findFirst()
                    .orElse(null);

            if (customerItem != null) {
                newDto.setMaterialsCode(customerItem.getCustomerCode());
                newDto.setOriPartPN(customerItem.getPnCode());
                newDto.setScanPartPN(customerItem.getPnCode());
                newDto.setPartType(ZH_ZK);
            }
        }
        else{
            CustomerItemsDTO customerItem = customerItems.stream()
                    .filter(i -> newDto.getItemCode().equals(i.getZteCode())&&!STR_NINE.equals(i.getProjectType()))
                    .findFirst()
                    .orElse(null);

            if (customerItem != null) {
                newDto.setMaterialsCode(customerItem.getCustomerCode());
            }
        }
        newDto.setFw(gpuItem.getFwVer());
        assembleItemData(newDto, gpuItem, batchRules);
        return newDto;
    }

    // 私有方法3：组装数据（原字段设置逻辑）
    private void assembleItemData(ZmsForwardTencentDTO target,
                                  ZmsSspTaskItemDTO source,
                                  List<ZmsForwardTencentBatchRuleDTO> batchRules) {
        target.setPartNumber(source.getPartNumber());
        target.setCPLDVersion(source.getCpldVer());
        target.setDeviceId(source.getDeviceId());
        //target.setFw(source.getFwVer());
        target.setMacAddress(source.getPort1Mac() != null ? source.getPort1Mac() : source.getPort2Mac());

        if (StringUtils.isNotEmpty(source.getSn())) {
            target.setOriPartSN(source.getSn());
            target.setScanPartSN(source.getSn());
        }

        target.setSlot(source.getSlot());
        target.setCollectSlot(source.getSlot());
        target.setSubsystemId(source.getSubSystemId());
        target.setSnmpTrap(source.getSlot());

        // 设置生产批次号
        target.setProductionBatch(getProductionBatch(
                batchRules,
                target.getPartType(),
                target.getPartVendor(),
                target.getOriPartSN()));
    }

    public List<ZmsSspTaskItemDTO> getZmsSspTaskItemDTOList(ZmsSspTaskDtailDTO zmsSspTaskDtailDTO) {
        List<ZmsSspTaskItemDTO> zmsSspTaskItemDTOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(zmsSspTaskDtailDTO.getCpuList())) {
            zmsSspTaskItemDTOList.addAll(zmsSspTaskDtailDTO.getCpuList());
        }
        if (CollectionUtils.isNotEmpty(zmsSspTaskDtailDTO.getMemoryList())) {
            zmsSspTaskItemDTOList.addAll(zmsSspTaskDtailDTO.getMemoryList());
        }
        if (CollectionUtils.isNotEmpty(zmsSspTaskDtailDTO.getNicList())) {
            zmsSspTaskItemDTOList.addAll(zmsSspTaskDtailDTO.getNicList());
        }
        if (CollectionUtils.isNotEmpty(zmsSspTaskDtailDTO.getDiskList())) {
            zmsSspTaskItemDTOList.addAll(zmsSspTaskDtailDTO.getDiskList());
        }
        //GPU 需要设置类型
        if (CollectionUtils.isNotEmpty(zmsSspTaskDtailDTO.getGpuList())) {
            zmsSspTaskDtailDTO.getGpuList().forEach(gpu -> {
                gpu.setType(ZH_GPU);
                zmsSspTaskItemDTOList.add(gpu);
            });
        }
        if (CollectionUtils.isNotEmpty(zmsSspTaskDtailDTO.getRaidList())) {
            zmsSspTaskItemDTOList.addAll(zmsSspTaskDtailDTO.getRaidList());
        }
        if (CollectionUtils.isNotEmpty(zmsSspTaskDtailDTO.getOpticalList())) {
            zmsSspTaskItemDTOList.addAll(zmsSspTaskDtailDTO.getOpticalList());
        }
        if (CollectionUtils.isNotEmpty(zmsSspTaskDtailDTO.getPsuList())) {
            zmsSspTaskItemDTOList.addAll(zmsSspTaskDtailDTO.getPsuList());
        }
        if (CollectionUtils.isNotEmpty(zmsSspTaskDtailDTO.getBpList())) {
            zmsSspTaskItemDTOList.addAll(zmsSspTaskDtailDTO.getBpList());
        }
        if (zmsSspTaskDtailDTO.getMainBoard() != null) {
            zmsSspTaskItemDTOList.add(zmsSspTaskDtailDTO.getMainBoard());
        }
        return zmsSspTaskItemDTOList;
    }

    @Override
    public String getProductionBatch(List<ZmsForwardTencentBatchRuleDTO> batchRuleDTOList, String partType,
                                     String partBrand, String serverSn) {
        ZmsForwardTencentBatchRuleDTO batchRuleDTO = batchRuleDTOList.stream().filter(b ->
                b.getPartType().equals(partType) && b.getPartBrand().equals(partBrand)
                        && b.getEnabledFlag().equals(FLAG_Y) && b.getSnLen() == serverSn.length()).findFirst().orElse(null);

        if (batchRuleDTO != null) {
            return serverSn.substring(batchRuleDTO.getSnStart() - INT_1, batchRuleDTO.getSnEnd());
        }
        return null;
    }

    /**
     * 调用中试接口
     * 根据sn调用
     */
    public ZmsSspTaskInfoDTO getSspTaskInfo(String sn) throws Exception {
        // 调用中试接口获取token
        String strToken = zmsStationLogUploadService.getToken();
        // 获取中试整机任务信息接口的地址
        String zsUrl = zmsStationLogUploadRepository.getZSUrl(LOOKUP_TYPES_8240042, LOOKUP_TYPE_824004200027);
        // 调用中试接口查询整机任务信息
        Map<String, Object> paramMap = new HashMap<>(2);
        //paramMap.put(SN, sn);
        paramMap.put(TASK_ID, sn);
        String bo = zmsStationLogUploadService.getZsStationLog(paramMap, zsUrl, strToken);
        return JacksonJsonConverUtil.jsonToBean(bo, ZmsSspTaskInfoDTO.class);
    }

    public List<ZmsForwardTencentBoardSnDTO> getZmsForwardTencentBoardSnDTO(ZmsForwardTencentEntityDTO dto, List<String> boardSnList, List<String> serviceSnBoardSnList) {
        List<ZmsForwardTencentBoardSnDTO> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(serviceSnBoardSnList)) {
            boardSnList.addAll(serviceSnBoardSnList);
        }
        // 去重
        boardSnList = boardSnList.stream().distinct().collect(Collectors.toList());
        for (String boardSn : boardSnList) {
            ZmsForwardTencentBoardSnDTO zmsForwardTencentBoardSnDTO = new ZmsForwardTencentBoardSnDTO();
            zmsForwardTencentBoardSnDTO.setBoardSn(boardSn);
            zmsForwardTencentBoardSnDTO.setEntityId(dto.getEntityId());
            zmsForwardTencentBoardSnDTO.setEntityName(dto.getEntityName());
            zmsForwardTencentBoardSnDTO.setCreatedBy(dto.getEmpNo());
            zmsForwardTencentBoardSnDTO.setLastUpdatedBy(dto.getEmpNo());
            list.add(zmsForwardTencentBoardSnDTO);
        }
        return list;
    }

    /**
     * 判断任务装配物料中是否存在主板条码
     */
    public List<String> getMainBoardSn(List<CpmConfigItemAssembleDTO> configDetailDTOList, List<CustomerItemsDTO> motherboardList) {
        List<String> list = new ArrayList<>();
        if (CollectionUtils.isEmpty(motherboardList)) {
            return list;
        }
        // 根据主板SN，过滤任务对应的装配物料
        List<CpmConfigItemAssembleDTO> cpmConfigItemAssembleDTOS = configDetailDTOList.stream()
                .filter(al -> CollectionUtils.isNotEmpty(motherboardList.stream().filter(
                                sc -> StringUtils.equals(al.getItemCode(), sc.getZteCode()))
                        .collect(Collectors.toList())))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(cpmConfigItemAssembleDTOS)) {
            return list;
        }
        list = cpmConfigItemAssembleDTOS.stream().map(CpmConfigItemAssembleDTO::getItemBarcode).distinct().collect(Collectors.toList());
        return list;
    }

    /**
     * 获取服务器sn对应主板条码
     */
    public List<String> getServiceSnBoardSn(List<CustomerItemsDTO> customerItemsDTOList, List<WsmAssembleLinesEntityDTO> wsmAssembleLinesList) {
        List<String> list = new ArrayList<>();
        if (CollectionUtils.isEmpty(customerItemsDTOList)) {
            return list;
        }

        // 主板sn
        List<CustomerItemsDTO> zbSnCustomerItemsDTOList = customerItemsDTOList.stream()
                .filter(e -> StringUtils.equals(e.getProjectType(), NumConstant.STR_5)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(zbSnCustomerItemsDTOList)) {
            return list;
        }

        // 根据主板SN，过滤任务对应的装配物料
        List<WsmAssembleLinesEntityDTO> wsmAssembleLinesEntityDTOS = wsmAssembleLinesList.stream()
                .filter(al -> CollectionUtils.isNotEmpty(zbSnCustomerItemsDTOList.stream().filter(
                                sc -> StringUtils.equals(al.getItemCode(), sc.getZteCode()))
                        .collect(Collectors.toList())))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(wsmAssembleLinesEntityDTOS)) {
            return list;
        }
        list = wsmAssembleLinesEntityDTOS.stream().map(WsmAssembleLinesEntityDTO::getItemBarcode).collect(Collectors.toList());
        return list;
    }

    /**
     * 手工修改后重推B2B
     */
    @Override
    public void uploadCompleteMachineDataByManual(List<String> recordIdList, String empNo) throws Exception {

        List<ZmsForwardTencentDTO> zmsForwardTencentDTOList = zmsForwardTencentRepository.getForwardTencentByRecordId(recordIdList);
        if (CollectionUtils.isEmpty(zmsForwardTencentDTOList)) {
            return;
        }
        List<CustomerDataLogDTO> dataList = new ArrayList<>();
        List<ZmsMesInfoUploadLogDTO> zmsMesInfoUploadDTOList = new ArrayList<>();
        //根据服务器SN分组
        Map<String, List<ZmsForwardTencentDTO>> svrSnMap = zmsForwardTencentDTOList
                .stream()
                .collect(Collectors.groupingBy(x -> x.getSvrSN() + x.getMessageId()));

        svrSnMap.forEach((a, b) -> {
            //数据整和在json中
            ZmsForwardTencentUploadDTO zmsForwardTencentUploadDTO = new ZmsForwardTencentUploadDTO();
            zmsForwardTencentUploadDTO.setAction(SUPPLIER_WRITE_SERVER_PART_INFO);
            zmsForwardTencentUploadDTO.setStartCompany(STR_ZTE);
            zmsForwardTencentUploadDTO.setMethod(RUN);
            List<ZmsForwardTencentPartInfoDTO> zmsForwardTencentPartInfoDTOList = new ArrayList<>();
            b.forEach(x -> {
                ZmsForwardTencentPartInfoDTO zmsForwardTencentPartInfoDTO = new ZmsForwardTencentPartInfoDTO();
                BeanUtils.copyProperties(x, zmsForwardTencentPartInfoDTO, getNullPropertyNames(x));
                zmsForwardTencentPartInfoDTOList.add(zmsForwardTencentPartInfoDTO);
            });
            ZmsForwardTencentDataDTO zmsForwardTencentDataDTO = new ZmsForwardTencentDataDTO();
            zmsForwardTencentDataDTO.setPartInfo(zmsForwardTencentPartInfoDTOList);
            zmsForwardTencentUploadDTO.setData(zmsForwardTencentDataDTO);
            ZmsForwardTencentDTO zmsForwardTencentDTO = b.get(NumConstant.NUM_ZERO);
            String id = UUID.randomUUID().toString().replace(CENTER_LINE, STRING_EMPTY);
            zmsForwardTencentDTO.setMessageId(id);
            zmsForwardTencentDTO.setLastUpdatedBy(empNo);
            CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
            customerDataLogDTO.setId(id);
            customerDataLogDTO.setOrigin(MES);
            customerDataLogDTO.setCustomerName(TENCENT);
            customerDataLogDTO.setProjectName(TENCENT_FORWARD_ZH);
            customerDataLogDTO.setProjectPhase(DateUtils.format(new Date(), DateUtils.DATE_FORMAT_FULL));
            customerDataLogDTO.setCooperationMode(STRING_EMPTY);
            customerDataLogDTO.setMessageType(ZTE_IMES_TENCENT_FORWARD_UPLOAD);
            customerDataLogDTO.setContractNo(STRING_EMPTY);
            customerDataLogDTO.setTaskNo(zmsForwardTencentDTO.getEntityName());
            customerDataLogDTO.setItemNo(STRING_EMPTY);
            customerDataLogDTO.setSn(zmsForwardTencentDTO.getSvrSN());
            customerDataLogDTO.setJsonData(JSON.toJSONString(zmsForwardTencentUploadDTO, SerializerFeature.WriteMapNullValue));
            customerDataLogDTO.setFactoryId(INT_51);
            customerDataLogDTO.setCreateBy(empNo);
            customerDataLogDTO.setLastUpdatedBy(empNo);
            dataList.add(customerDataLogDTO);

            // 组装日志对象
            ZmsMesInfoUploadLogDTO zmsMesInfoUploadDTO = new ZmsMesInfoUploadLogDTO();
            BeanUtils.copyProperties(customerDataLogDTO, zmsMesInfoUploadDTO);
            zmsMesInfoUploadDTOList.add(zmsMesInfoUploadDTO);
        });

        // 写入上传日志
        zmsIndicatorUploadService.insertMesInfoUploadLog(zmsMesInfoUploadDTOList);

        // 调用iMES接口上传B2B
        zmsIndicatorUploadService.pushDataToB2B(dataList);

        // 更新正向数据
        for (List<ZmsForwardTencentDTO> tempInsertList : CommonUtils.splitList(zmsForwardTencentDTOList, Constant.INT_500)) {
            zmsForwardTencentRepository.updateForwardTencentByRecordId(tempInsertList);
        }
    }
    /* Ended by AICoder, pid:371c0c8245ae3191422c0997d5eeb57739f29e87 */

    @Override
    public void updateMesInfoUploadFailedLog(CustomerDataLogDTO dto) {
        // 类型为腾讯正向数据时，需要更新MES消息主表的上传标识为上传失败
        if (Constant.ZTE_IMES_TENCENT_FORWARD_UPLOAD.equals(dto.getMessageType())) {
            zmsIndicatorUploadService.updateInternetMain(dto.getSn(), LOOKUP_TYPE_824009200001,
                    ZERO_NUMBER_STRING_DT, dto.getErrMsg());
        }
        // 类型为阿里测试数据时，需要更新阿里测试数据表的上传标识为上传失败
        if (Constant.ZTE_IMES_ALIBABA_AIS_META_MCT.equals(dto.getMessageType())) {
            zmsIndicatorUploadService.updateInternetMain(dto.getSn(), LOOKUP_TYPE_824009200005,
                    ZERO_NUMBER_STRING_DT, dto.getErrMsg());
        }
    }
}
