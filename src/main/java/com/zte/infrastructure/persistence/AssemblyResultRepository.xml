<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zte.domain.model.AssemblyResultRepository">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.zte.interfaces.dto.AssemblyResultEntityDTO" id="assemblyResultMap">
        <result property="resultId" column="RESULT_ID" jdbcType="VARCHAR"/>
        <result property="itemCode" column="ITEM_CODE" jdbcType="VARCHAR"/>
        <result property="itemVersion" column="ITEM_VERSION" jdbcType="VARCHAR"/>
        <result property="processStatus" column="PROCESS_STATUS" jdbcType="VARCHAR"/>
        <result property="sendCount" column="SEND_COUNT" jdbcType="DECIMAL"/>
        <result property="combineFlag" column="COMBINE_FLAG" jdbcType="VARCHAR"/>
        <result property="startTime" column="START_TIME" jdbcType="TIMESTAMP"/>
        <result property="endTime" column="END_TIME" jdbcType="TIMESTAMP"/>
        <result property="remark" column="REMARK" jdbcType="VARCHAR"/>
        <result property="createBy" column="CREATE_BY" jdbcType="VARCHAR"/>
        <result property="createDate" column="CREATE_DATE" jdbcType="TIMESTAMP" />
        <result property="lastUpdatedBy" column="LAST_UPDATED_BY" jdbcType="VARCHAR"/>
        <result property="lastUpdatedDate" column="LAST_UPDATED_DATE" jdbcType="TIMESTAMP" />
        <result property="enabledFlag" column="ENABLED_FLAG" jdbcType="VARCHAR" />
        <result property="orgId" column="ORG_ID" jdbcType="DECIMAL"/>
        <result property="factoryId" column="FACTORY_ID" jdbcType="DECIMAL"/>
        <result property="entityId" column="ENTITY_ID" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
      RESULT_ID,
      ITEM_CODE,
      ITEM_VERSION,
      PROCESS_STATUS,
      SEND_COUNT,
      COMBINE_FLAG,
      START_TIME,
      END_TIME,
      REMARK,
      CREATE_BY,
      CREATE_DATE,
      LAST_UPDATED_BY,
      LAST_UPDATED_DATE,
      ENABLED_FLAG,
      ORG_ID,
      FACTORY_ID,
      ENTITY_ID
    </sql>

    <select id="pageList" parameterType="com.zte.springbootframe.common.model.Page" resultMap="assemblyResultMap">
      select
      <include refid="Base_Column_List" />
      from ASSEMBLY_RESULT where 1=1
      <if test="params != null and params.resultId != null and params.resultId != ''">and RESULT_ID = #{params.resultId}</if>
      <if test="params != null and params.itemCode != null and params.itemCode != ''">and ITEM_CODE = #{params.itemCode}</if>
      <if test="params != null and params.itemVersion != null and params.itemVersion != ''">and ITEM_VERSION = #{params.itemVersion}</if>
      <if test="params != null and params.processStatus != null and params.processStatus != ''">and PROCESS_STATUS = #{params.processStatus}</if>
      <if test="params != null and params.sendCount != null and params.sendCount != ''">and SEND_COUNT = #{params.sendCount}</if>
      <if test="params != null and params.combineFlag != null and params.combineFlag != ''">and COMBINE_FLAG = #{params.combineFlag}</if>
      <if test="params != null and params.startTime != null and params.startTime != ''">and START_TIME = #{params.startTime}</if>
      <if test="params != null and params.endTime != null and params.endTime != ''">and END_TIME = #{params.endTime}</if>
      <if test="params != null and params.remark != null and params.remark != ''">and REMARK = #{params.remark}</if>
      <if test="params != null and params.createBy != null and params.createBy != ''">and CREATE_BY = #{params.createBy}</if>
      <if test="params != null and params.createDate != null ">and CREATE_DATE = #{params.createDate}</if>
      <if test="params != null and params.lastUpdatedBy != null and params.lastUpdatedBy != ''">and LAST_UPDATED_BY = #{params.lastUpdatedBy}</if>
      <if test="params != null and params.lastUpdatedDate != null and params.lastUpdatedDate != ''">and LAST_UPDATED_DATE = #{params.lastUpdatedDate}</if>
      <if test="params != null and params.enabledFlag != null and params.enabledFlag != ''">and ENABLED_FLAG = #{params.enabledFlag}</if>
      <if test="params != null and params.orgId != null and params.orgId != ''">and ORG_ID = #{params.orgId}</if>
      <if test="params != null and params.factoryId != null and params.factoryId != ''">and FACTORY_ID = #{params.factoryId}</if>
      <if test="params != null and params.entityId != null and params.entityId != ''">and ENTITY_ID = #{params.entityId}</if>
    </select>

    <select id="getListByStatus" parameterType="com.zte.interfaces.dto.AssemblyResultEntityDTO" resultMap="assemblyResultMap">
        select
        <include refid="Base_Column_List" />
        from ASSEMBLY_RESULT T where ENABLED_FLAG ='Y' and PROCESS_STATUS in (${inProcessStatus})
        <if test="sendCount != null ">
            and SEND_COUNT >= #{sendCount}
        </if>
        <if test="existItemList != null and existItemList.size() > 0">
            AND T.ITEM_CODE IN
            <foreach collection="existItemList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getListByItemAndVersion" parameterType="com.zte.interfaces.dto.AssemblyResultEntityDTO" resultMap="assemblyResultMap">
        select
        <include refid="Base_Column_List" />
        from ASSEMBLY_RESULT T where ENABLED_FLAG ='Y' and ITEM_CODE = #{itemCode} and ITEM_VERSION = #{itemVersion}
        <if test="factoryId != null ">
            and FACTORY_ID = #{factoryId,jdbcType=DECIMAL}
        </if>
    </select>


    <insert id="batchInsert" parameterType="java.util.List">
        insert into ASSEMBLY_RESULT
        (
        RESULT_ID,
        ITEM_CODE,
        ITEM_VERSION,
        PROCESS_STATUS,
        SEND_COUNT,
        COMBINE_FLAG,
        START_TIME,
        END_TIME,
        REMARK,
        CREATE_BY,
        CREATE_DATE,
        LAST_UPDATED_BY,
        LAST_UPDATED_DATE,
        ENABLED_FLAG,
        ORG_ID,
        FACTORY_ID,
        ENTITY_ID)
        select
        RESULT_ID,
        ITEM_CODE,
        ITEM_VERSION,
        PROCESS_STATUS,
        SEND_COUNT,
        COMBINE_FLAG,
        START_TIME,
        END_TIME,
        REMARK,
        CREATE_BY,
        CREATE_DATE,
        LAST_UPDATED_BY,
        LAST_UPDATED_DATE,
        ENABLED_FLAG,
        ORG_ID,
        FACTORY_ID,
        ENTITY_ID
        from (
        <foreach collection="list" item="item" index="index" separator="UNION ALL">
            SELECT
            #{item.resultId,jdbcType=VARCHAR} RESULT_ID,
            #{item.itemCode,jdbcType=VARCHAR} ITEM_CODE,
            #{item.itemVersion,jdbcType=VARCHAR} ITEM_VERSION,
            #{item.processStatus,jdbcType=VARCHAR} PROCESS_STATUS,
            #{item.sendCount ,jdbcType=DECIMAL} SEND_COUNT,
            #{item.combineFlag,jdbcType=VARCHAR} COMBINE_FLAG,
            #{item.startTime,jdbcType=TIMESTAMP}::timestamp START_TIME,
            #{item.endTime,jdbcType=TIMESTAMP}::timestamp END_TIME,
            #{item.remark ,jdbcType=VARCHAR} REMARK ,
            #{item.createBy ,jdbcType=VARCHAR} CREATE_BY,
            sysdate CREATE_DATE,
            #{item.lastUpdatedBy ,jdbcType=VARCHAR} LAST_UPDATED_BY,
            sysdate LAST_UPDATED_DATE,
            'Y' ENABLED_FLAG,
            #{item.orgId ,jdbcType=DECIMAL} ORG_ID,
            #{item.factoryId ,jdbcType=DECIMAL} FACTORY_ID,
            #{item.entityId ,jdbcType=DECIMAL} ENTITY_ID

        </foreach>
        ) a where not exists( select 1 from ASSEMBLY_RESULT t where  t.ENABLED_FLAG = 'Y' and t.ITEM_CODE = a.ITEM_CODE  and t.ITEM_VERSION = a.ITEM_VERSION )
    </insert>

    <update id="batchUpdate" parameterType="java.util.List">
        <foreach collection="list" separator=";" item="item" >
            update ASSEMBLY_RESULT t set
              t.PROCESS_STATUS=#{item.processStatus,jdbcType=VARCHAR},
            <if test="item.sendCount != null ">
                t.SEND_COUNT=#{item.sendCount,jdbcType=DECIMAL},
            </if>
            <if test="item.combineFlag != null and item.combineFlag !=''">
                t.COMBINE_FLAG=#{item.combineFlag,jdbcType=VARCHAR},
            </if>
            <if test="item.lastUpdatedBy != null and item.lastUpdatedBy !=''">
                t.LAST_UPDATED_BY=#{item.lastUpdatedBy,jdbcType=VARCHAR},
            </if>
              t.LAST_UPDATED_DATE=sysdate
              where ENABLED_FLAG = 'Y'
              and RESULT_ID = #{item.resultId,jdbcType=VARCHAR}
        </foreach>
    </update>
</mapper>