<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.PmRepairRcvDetailRepository">
  <resultMap id="BaseResultMap" type="com.zte.domain.model.PmRepairRcvDetail">
    <result column="RECEPTION_DETAIL_ID" jdbcType="VARCHAR" property="receptionDetailId" />
    <result column="RECEPTION_ID" jdbcType="VARCHAR" property="receptionId" />
    <result column="REPAIR_ID" jdbcType="VARCHAR" property="repairId" />
    <result column="ITEM_CODE" jdbcType="VARCHAR" property="itemCode" />
    <result column="ITEM_NAME" jdbcType="VARCHAR" property="itemName" />
    <result column="SN" jdbcType="VARCHAR" property="sn" />
    <result column="IS_ACCEPT" jdbcType="DECIMAL" property="isAccept" />
    <result column="ERROR_CODE" jdbcType="VARCHAR" property="errorCode" />
    <result column="ERROR_DESCRIPTION" jdbcType="VARCHAR" property="errorDescription" />
    <result column="ENABLED_FLAG" jdbcType="VARCHAR" property="enabledFlag" />
    <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="LAST_UPDATED_BY" jdbcType="VARCHAR" property="lastUpdatedBy" />
    <result column="LAST_UPDATED_DATE" jdbcType="TIMESTAMP" property="lastUpdatedDate" />
    <result column="ORG_ID" jdbcType="DECIMAL" property="orgId" />
    <result column="FACTORY_ID" jdbcType="DECIMAL" property="factoryId" />
    <result column="ENTITY_ID" jdbcType="DECIMAL" property="entityId" />
    <result column="WORK_STATION" jdbcType="VARCHAR" property="workStation" />
    <result column="PROCESS_CODE" jdbcType="VARCHAR" property="processCode" />
    <result column="CRAFT_SECTION" jdbcType="VARCHAR" property="craftSection" />
    <result column="STATUS" jdbcType="VARCHAR" property="status" />
    <result column="REPAIR_RCV_DATE" jdbcType="TIMESTAMP" property="repairRcvDate" />
    <result column="RCV_PRODPLAN_ID" jdbcType="VARCHAR" property="rcvProdplanId" />
    <result column="SEND_FLAG" jdbcType="VARCHAR" property="sendFlag" />
    <result column="RCV_FLAG" jdbcType="VARCHAR" property="rcvFlag" />
    <result column="SCRAP_REASON" jdbcType="VARCHAR" property="scrapReason" />
    <result column="SCAN_TIME" jdbcType="VARCHAR" property="scanTime" />
    <result column="MATERIAL_VERSION" jdbcType="VARCHAR" property="materialVersion" />
    <result column="PCB_VERSION" jdbcType="VARCHAR" property="pcbVersion" />
    <result column="DEVICE_BAR_CODE" jdbcType="VARCHAR" property="deviceBarCode" />
    <result column="TASK_NO" jdbcType="VARCHAR" property="taskNo" />
    <result column="WORK_ORDER_NO" jdbcType="VARCHAR" property="workOrderNo" />
    <result column="FRONT_SOFTWARE_VERSION" jdbcType="VARCHAR" property="frontSoftwareVersion" />
    <result column="BACK_SOFTWARE_VERSION" jdbcType="VARCHAR" property="backSoftwareVersion" />
    <result column="RACK_BAR_CODE" jdbcType="VARCHAR" property="rackBarCode" />
    <result column="ECC_DELIVERY_FORM" jdbcType="VARCHAR" property="eccDeliveryForm" />
    <result column="TEST_RECORD_NO" jdbcType="VARCHAR" property="testRecordNo" />
    <result column="DETAILS_REMARK" jdbcType="VARCHAR" property="detailsRemark" />
    <result column="ERROR_MESSAGE" jdbcType="VARCHAR" property="errorMessage" />
    <result column="RETURNED_BY" jdbcType="VARCHAR" property="returnedBy" />
    <result column="RETURNED_DATE" jdbcType="TIMESTAMP" property="returnedDate" />
    <result column="RETURNED_TO" jdbcType="VARCHAR" property="returnedTo" />
    <result column="CRAFT_SECTION_ZS" jdbcType="VARCHAR" property="craftSectionZs" />
    <result column="PROCESS_CODE_ZS" jdbcType="VARCHAR" property="processCodeZs" />
    <result column="WORK_STATION_ZS" jdbcType="VARCHAR" property="workStationZs" />
    <result column="RECEIVING_TIME" jdbcType="VARCHAR" property="receivingTime" />
    <result column="PRODUCT_CLASS" jdbcType="VARCHAR" property="productClass" />
    <result column="PRODUCT_SMLCLASS" jdbcType="VARCHAR" property="productSmlclass" />
    <result column="STYLE" jdbcType="VARCHAR" property="style" />
    <result column="REPLACING_BARCODE" jdbcType="VARCHAR" property="replacingBarcode" />
    <result column="RECEPTION_BY" jdbcType="VARCHAR" property="receptionBy" />
    <result column="LINE_CODE" jdbcType="VARCHAR" property="lineCode" />
    <result column="LOCATION_NO" jdbcType="VARCHAR" property="locationNo" />
  </resultMap>
  <resultMap id="BaseResultMapDto" type="com.zte.interfaces.dto.PmRepairRcvDetailDTO">
    <result column="RECEPTION_DETAIL_ID" jdbcType="VARCHAR" property="receptionDetailId" />
    <result column="RECEPTION_ID" jdbcType="VARCHAR" property="receptionId" />
    <result column="REPAIR_ID" jdbcType="VARCHAR" property="repairId" />
    <result column="ITEM_CODE" jdbcType="VARCHAR" property="itemCode" />
    <result column="ITEM_NAME" jdbcType="VARCHAR" property="itemName" />
    <result column="SN" jdbcType="VARCHAR" property="sn" />
    <result column="IS_ACCEPT" jdbcType="DECIMAL" property="isAccept" />
    <result column="ERROR_CODE" jdbcType="VARCHAR" property="errorCode" />
    <result column="ERROR_DESCRIPTION" jdbcType="VARCHAR" property="errorDescription" />
    <result column="ENABLED_FLAG" jdbcType="VARCHAR" property="enabledFlag" />
    <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="LAST_UPDATED_BY" jdbcType="VARCHAR" property="lastUpdatedBy" />
    <result column="LAST_UPDATED_DATE" jdbcType="TIMESTAMP" property="lastUpdatedDate" />
    <result column="ORG_ID" jdbcType="DECIMAL" property="orgId" />
    <result column="FACTORY_ID" jdbcType="DECIMAL" property="factoryId" />
    <result column="ENTITY_ID" jdbcType="DECIMAL" property="entityId" />
    <result column="WORK_STATION" jdbcType="VARCHAR" property="workStation" />
    <result column="PROCESS_CODE" jdbcType="VARCHAR" property="processCode" />
    <result column="CRAFT_SECTION" jdbcType="VARCHAR" property="craftSection" />
    <result column="STATUS" jdbcType="VARCHAR" property="status" />
    <result column="REPAIR_RCV_DATE" jdbcType="TIMESTAMP" property="repairRcvDate" />
    <result column="RCV_PRODPLAN_ID" jdbcType="VARCHAR" property="rcvProdplanId" />
    <result column="SEND_FLAG" jdbcType="VARCHAR" property="sendFlag" />
    <result column="RCV_FLAG" jdbcType="VARCHAR" property="rcvFlag" />
    <result column="SCRAP_REASON" jdbcType="VARCHAR" property="scrapReason" />
    <result column="SCAN_TIME" jdbcType="VARCHAR" property="scanTime" />
    <result column="MATERIAL_VERSION" jdbcType="VARCHAR" property="materialVersion" />
    <result column="PCB_VERSION" jdbcType="VARCHAR" property="pcbVersion" />
    <result column="DEVICE_BAR_CODE" jdbcType="VARCHAR" property="deviceBarCode" />
    <result column="TASK_NO" jdbcType="VARCHAR" property="taskNo" />
    <result column="WORK_ORDER_NO" jdbcType="VARCHAR" property="workOrderNo" />
    <result column="FRONT_SOFTWARE_VERSION" jdbcType="VARCHAR" property="frontSoftwareVersion" />
    <result column="BACK_SOFTWARE_VERSION" jdbcType="VARCHAR" property="backSoftwareVersion" />
    <result column="RACK_BAR_CODE" jdbcType="VARCHAR" property="rackBarCode" />
    <result column="ECC_DELIVERY_FORM" jdbcType="VARCHAR" property="eccDeliveryForm" />
    <result column="TEST_RECORD_NO" jdbcType="VARCHAR" property="testRecordNo" />
    <result column="DETAILS_REMARK" jdbcType="VARCHAR" property="detailsRemark" />
    <result column="ERROR_MESSAGE" jdbcType="VARCHAR" property="errorMessage" />
    <result column="RETURNED_BY" jdbcType="VARCHAR" property="returnedBy" />
    <result column="RETURNED_DATE" jdbcType="TIMESTAMP" property="returnedDate" />
    <result column="RETURNED_TO" jdbcType="VARCHAR" property="returnedTo" />
    <result column="CRAFT_SECTION_ZS" jdbcType="VARCHAR" property="craftSectionZs" />
    <result column="PROCESS_CODE_ZS" jdbcType="VARCHAR" property="processCodeZs" />
    <result column="WORK_STATION_ZS" jdbcType="VARCHAR" property="workStationZs" />
    <result column="RECEIVING_TIME" jdbcType="VARCHAR" property="receivingTime" />
    <result column="PRODUCT_CLASS" jdbcType="VARCHAR" property="productClass" />
    <result column="PRODUCT_SMLCLASS" jdbcType="VARCHAR" property="productSmlclass" />
    <result column="STYLE" jdbcType="VARCHAR" property="style" />
    <result column="REPLACING_BARCODE" jdbcType="VARCHAR" property="replacingBarcode" />
    <result column="RECEPTION_BY" jdbcType="VARCHAR" property="receptionBy" />
    <result column="LINE_CODE" jdbcType="VARCHAR" property="lineCode" />
    <result column="LOCATION_NO" jdbcType="VARCHAR" property="locationNo" />
    <result column="REPAIR_COUNT" jdbcType="VARCHAR" property="repairCount" />
    <result column="FROM_STATION" jdbcType="VARCHAR" property="fromStation" />
    <result column="APPLICATION_DEPARTMENT" jdbcType="VARCHAR" property="applicationDepartment" />
    <result column="APPLICATION_SECTION" jdbcType="VARCHAR" property="applicationSection" />
    <result column="DELIVERY_NO" jdbcType="VARCHAR" property="deliveryNo" />
    <result column="BUILDING" jdbcType="VARCHAR" property="building" />
    <result column="SN_TYPE" jdbcType="VARCHAR" property="snType" />
    <result column="REPAIR_PRODUCT_MSTYPE" jdbcType="VARCHAR" property="repairProuctMstype" />

  </resultMap>
  <resultMap id="RelOneResultMap" type="com.zte.domain.model.PmRepairRcvDetail">
    <result column="RECEPTION_DETAIL_ID" jdbcType="VARCHAR" property="receptionDetailId" />
    <result column="RECEPTION_ID" jdbcType="VARCHAR" property="receptionId" />
    <result column="REPAIR_ID" jdbcType="VARCHAR" property="repairId" />
    <result column="ITEM_NAME" jdbcType="VARCHAR" property="itemName" />
    <result column="ITEM_CODE" jdbcType="VARCHAR" property="itemCode" />
    <result column="SN" jdbcType="VARCHAR" property="sn" />
    <result column="IS_ACCEPT" jdbcType="DECIMAL" property="isAccept" />
    <result column="ERROR_CODE" jdbcType="VARCHAR" property="errorCode" />
    <result column="ERROR_DESCRIPTION" jdbcType="VARCHAR" property="errorDescription" />
    <result column="ENABLED_FLAG" jdbcType="VARCHAR" property="enabledFlag" />
    <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="LAST_UPDATED_BY" jdbcType="VARCHAR" property="lastUpdatedBy" />
    <result column="LAST_UPDATED_DATE" jdbcType="TIMESTAMP" property="lastUpdatedDate" />
    <result column="ORG_ID" jdbcType="DECIMAL" property="orgId" />
    <result column="FACTORY_ID" jdbcType="DECIMAL" property="factoryId" />
    <result column="ENTITY_ID" jdbcType="DECIMAL" property="entityId" />
    <result column="WORK_STATION" jdbcType="VARCHAR" property="workStation" />
    <result column="PROCESS_CODE" jdbcType="VARCHAR" property="processCode" />
    <result column="CRAFT_SECTION" jdbcType="VARCHAR" property="craftSection" />
    <result column="FROM_STATION" jdbcType="VARCHAR" property="fromStation" />
    <result column="BUILDING" jdbcType="VARCHAR" property="building" />
    <result column="STATUS" jdbcType="VARCHAR" property="status" />
    <result column="REPAIR_RCV_DATE" jdbcType="TIMESTAMP" property="repairRcvDate" />
    <result column="RCV_PRODPLAN_ID" jdbcType="VARCHAR" property="rcvProdplanId" />

    <result column="DELIVERY_NO" jdbcType="VARCHAR" property="deliveryNo" />
    <result column="BILL_TYPE" jdbcType="DECIMAL" property="billType" />
    <result column="DELIVERY_BY" jdbcType="VARCHAR" property="deliveryBy" />
    <result column="RECEPTION_BY" jdbcType="VARCHAR" property="receptionBy" />
    <result column="QTY" jdbcType="DECIMAL" property="qty" />

    <result column="SEND_FLAG" jdbcType="VARCHAR" property="sendFlag" />
    <result column="RCV_FLAG" jdbcType="VARCHAR" property="rcvFlag" />
  </resultMap>
  <resultMap id="OnlySnResultMap" type="java.lang.String">
    <result column="SN" jdbcType="VARCHAR" property="sn" />
  </resultMap>

  <resultMap id="TestStatMap" type="com.zte.domain.model.BoardTestStatInfo">
    <result column="PRODPLAN_ID" jdbcType="VARCHAR" property="prodplanId" />
    <result column="test_craft_section" jdbcType="VARCHAR" property="testCraftSection" />
    <result column="test_defect_qty" jdbcType="INTEGER" property="testDefectQty" />
  </resultMap>

  <sql id="Base_Column_List">
    RECEPTION_DETAIL_ID, RECEPTION_ID, REPAIR_ID,ITEM_CODE, SN, IS_ACCEPT,
    ENABLED_FLAG, CREATE_DATE, CREATE_BY, LAST_UPDATED_BY, LAST_UPDATED_DATE, ORG_ID,
    FACTORY_ID, ENTITY_ID, ERROR_CODE, ERROR_DESCRIPTION,CRAFT_SECTION,
    STATUS,PROCESS_CODE,WORK_STATION,REPAIR_RCV_DATE,RCV_PRODPLAN_ID,SEND_FLAG,RCV_FLAG,SCRAP_REASON,SCAN_TIME,
    MATERIAL_VERSION,PCB_VERSION,DEVICE_BAR_CODE,TASK_NO,WORK_ORDER_NO,FRONT_SOFTWARE_VERSION,
    BACK_SOFTWARE_VERSION,RACK_BAR_CODE,ECC_DELIVERY_FORM,TEST_RECORD_NO ,DETAILS_REMARK,ERROR_MESSAGE,
    RETURNED_BY,RETURNED_DATE,RETURNED_TO,CRAFT_SECTION_ZS,PROCESS_CODE_ZS,WORK_STATION_ZS,
    RECEIVING_TIME,PRODUCT_CLASS,PRODUCT_SMLCLASS,STYLE,REPLACING_BARCODE,RECEPTION_BY,LINE_CODE,LOCATION_NO</sql>
  <!--mybatis引用： 表名 -->
  <sql id="Table_Name">
    PM_REPAIR_RCV_DETAIL
  </sql>
  <!--mybatis引用： 基础列结构  begin -->
  <!--mybatis引用： 基础列筛选结构 -->
  <sql id="Base_Column_Filter_List">
    <if test="createBy != null">CREATE_BY,</if>
    <if test="lastUpdatedBy != null">LAST_UPDATED_BY,</if>
    <if test="orgId != null">ORG_ID,</if>
    <if test="factoryId != null">FACTORY_ID,</if>
    <if test="entityId != null">ENTITY_ID,</if>
    ENABLED_FLAG,CREATE_DATE, LAST_UPDATED_DATE
  </sql>
  <!--mybatis引用： 基础字段筛选结构 -->
  <sql id="Base_Filed_Filter_List">
    <if test="createBy != null">#{createBy,jdbcType=VARCHAR},</if>
    <if test="lastUpdatedBy != null">#{lastUpdatedBy,jdbcType=VARCHAR},</if>
    <if test="orgId != null">#{orgId,jdbcType=DECIMAL},</if>
    <if test="factoryId != null">#{factoryId,jdbcType=DECIMAL},</if>
    <if test="entityId != null">#{entityId,jdbcType=DECIMAL},</if>
    'Y',SYSDATE,SYSDATE
  </sql>
  <sql id="Base_Condtions">
    <include refid="Table_Name" />.ENABLED_FLAG = 'Y'
    <if test="receptionDetailId != null and receptionDetailId != ''"> AND <include refid="Table_Name" />.RECEPTION_DETAIL_ID = #{receptionDetailId}</if>
  </sql>

  <!--mybatis引用： 列筛选结构 -->
  <sql id="Column_Filter_List">
    <if test="receptionDetailId != null">RECEPTION_DETAIL_ID,</if>
    <if test="receptionId != null">RECEPTION_ID,</if>
    <if test="repairId != null">REPAIR_ID,</if>
    <if test="sn != null">SN,</if>
    <if test="itemCode != null">ITEM_CODE,</if>
    <if test="itemName != null">ITEM_NAME,</if>
    <if test="isAccept != null">IS_ACCEPT,</if>
    <if test="errorCode != null">ERROR_CODE,</if>
    <if test="errorDescription != null">ERROR_DESCRIPTION,</if>
    <if test="workStation != null">WORK_STATION,</if>
    <if test="processCode != null">PROCESS_CODE,</if>
    <if test="craftSection != null">CRAFT_SECTION,</if>
    <if test="status != null">STATUS,</if>
    <if test="repairRcvDate != null">REPAIR_RCV_DATE,</if>
    <if test="rcvProdplanId != null">RCV_PRODPLAN_ID,</if>
    <if test="sendFlag != null">SEND_FLAG,</if>
    <if test="rcvFlag != null">RCV_FLAG,</if>

    <include refid="Base_Column_Filter_List" />
  </sql>
  <!--mybatis引用： 字段筛选结构 -->
  <sql id="Filed_Filter_List">
    <if test="receptionDetailId != null">#{receptionDetailId,jdbcType=VARCHAR},</if>
    <if test="receptionId != null">#{receptionId,jdbcType=DECIMAL},</if>
    <if test="repairId != null">#{repairId,jdbcType=DECIMAL},</if>
    <if test="sn != null">#{sn,jdbcType=VARCHAR},</if>
    <if test="itemCode != null">#{itemCode,jdbcType=VARCHAR},</if>
    <if test="itemName != null">#{itemName,jdbcType=DECIMAL},</if>
    <if test="isAccept != null">#{isAccept,jdbcType=VARCHAR},</if>
    <if test="errorCode != null">#{errorCode,jdbcType=VARCHAR},</if>
    <if test="errorDescription != null">#{errorDescription,jdbcType=VARCHAR},</if>
    <if test="workStation != null">#{workStation,jdbcType=VARCHAR},</if>
    <if test="processCode != null">#{processCode,jdbcType=VARCHAR},</if>
    <if test="craftSection != null">#{craftSection,jdbcType=VARCHAR},</if>
    <if test="status != null">#{status,jdbcType=VARCHAR},</if>
    <if test="repairRcvDate != null">#{repairRcvDate},</if>
    <if test="rcvProdplanId != null">#{rcvProdplanId},</if>
    <if test="sendFlag != null">#{sendFlag},</if>
    <if test="rcvFlag != null">#{rcvFlag},</if>

    <include refid="Base_Filed_Filter_List" />
  </sql>
  <!--mybatis引用： 列字段筛选结构 -->
  <sql id="Column_Filed_List">
    <if test="item.receptionId != null">RECEPTION_ID = #{item.receptionId,jdbcType=VARCHAR},</if>
    <if test="item.sn != null">SN = #{item.sn,jdbcType=VARCHAR},</if>
    <if test="item.itemCode != null">ITEM_CODE = #{item.itemCode,jdbcType=VARCHAR},</if>
    <if test="item.itemName != null">ITEM_NAME = #{item.itemName,jdbcType=VARCHAR},</if>
    <if test="item.isAccept != null">IS_ACCEPT = #{item.isAccept,jdbcType=VARCHAR},</if>
    <if test="item.errorCode != null">ERROR_CODE = #{item.errorCode,jdbcType=VARCHAR},</if>
    <if test="item.errorDescription != null">ERROR_DESCRIPTION = #{item.errorDescription,jdbcType=VARCHAR},</if>
    <if test="item.workStation != null">WORK_STATION = #{item.workStation,jdbcType=VARCHAR},</if>
    <if test="item.processCode != null">PROCESS_CODE = #{item.processCode,jdbcType=VARCHAR},</if>
    <if test="item.craftSection != null">CRAFT_SECTION = #{item.craftSection,jdbcType=VARCHAR},</if>
    <if test="item.status != null">STATUS = #{item.status,jdbcType=VARCHAR},</if>
    <if test="item.repairRcvDate != null">REPAIR_RCV_DATE = #{item.repairRcvDate,jdbcType=TIMESTAMP},</if>
    <if test="item.rcvProdplanId != null">RCV_PRODPLAN_ID = #{item.rcvProdplanId},</if>
    <if test="item.sendFlag != null">SEND_FLAG = #{item.sendFlag},</if>
    <if test="item.rcvFlag != null">RCV_FLAG = #{item.rcvFlag},</if>
    <if test="item.lastUpdatedBy != null">LAST_UPDATED_BY = #{item.lastUpdatedBy,jdbcType=VARCHAR}, </if>
    LAST_UPDATED_DATE =SYSDATE,
    SCAN_TIME =SYSDATE,
    <if test="item.materialVersion != null">MATERIAL_VERSION = #{item.materialVersion,jdbcType=VARCHAR}, </if>
    <if test="item.pcbVersion != null">PCB_VERSION = #{item.pcbVersion,jdbcType=VARCHAR}, </if>
    <if test="item.deviceBarCode != null">DEVICE_BAR_CODE = #{item.deviceBarCode,jdbcType=VARCHAR}, </if>
    <if test="item.taskNo != null">TASK_NO = #{item.taskNo,jdbcType=VARCHAR}, </if>
    <if test="item.workOrderNo != null">WORK_ORDER_NO = #{item.workOrderNo,jdbcType=VARCHAR}, </if>
    <if test="item.frontSoftwareVersion != null">FRONT_SOFTWARE_VERSION = #{item.frontSoftwareVersion,jdbcType=VARCHAR}, </if>
    <if test="item.backSoftwareVersion != null">BACK_SOFTWARE_VERSION = #{item.backSoftwareVersion,jdbcType=VARCHAR}, </if>
    <if test="item.rackBarCode != null">RACK_BAR_CODE = #{item.rackBarCode,jdbcType=VARCHAR}, </if>
    <if test="item.eccDeliveryForm != null">ECC_DELIVERY_FORM = #{item.eccDeliveryForm,jdbcType=VARCHAR}, </if>
    <if test="item.testRecordNo != null">TEST_RECORD_NO = #{item.testRecordNo,jdbcType=VARCHAR}, </if>
    <if test="item.detailsRemark != null">DETAILS_REMARK = #{item.detailsRemark,jdbcType=VARCHAR}, </if>
    <if test="item.errorMessage != null">ERROR_MESSAGE = #{item.errorMessage,jdbcType=VARCHAR}, </if>
    <if test="item.receivingTime != null">RECEIVING_TIME = #{item.receivingTime,jdbcType=TIMESTAMP}, </if>
    <if test="item.productClass != null">PRODUCT_CLASS = #{item.productClass,jdbcType=VARCHAR}, </if>
    <if test="item.productSmlclass != null">PRODUCT_SMLCLASS = #{item.productSmlclass,jdbcType=VARCHAR}, </if>
    <if test="item.style != null">STYLE = #{item.style,jdbcType=VARCHAR}, </if>
    <if test="item.replacingBarcode != null">REPLACING_BARCODE = #{item.replacingBarcode,jdbcType=VARCHAR}, </if>
    <if test="item.receptionBy != null">RECEPTION_BY = #{item.receptionBy,jdbcType=VARCHAR}, </if>
    <if test="item.lineCode != null">LINE_CODE = #{item.lineCode,jdbcType=VARCHAR}, </if>
    <if test="item.locationNo != null">LOCATION_NO = #{item.locationNo,jdbcType=VARCHAR}, </if>
  </sql>
  <sql id="Condtions">
    <include refid="Base_Condtions" />
    <if test="receptionId != null and receptionId != ''"> AND <include refid="Table_Name" />.RECEPTION_ID = #{receptionId}</if>
    <if test="sn != null and sn != ''"> AND <include refid="Table_Name" />.SN = #{sn}</if>
    <if test="snList != null and snList.size() > 0">
       AND <include refid="Table_Name" />.SN in
      <foreach collection="snList" index="index" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="itemCode != null and itemCode != ''"> AND <include refid="Table_Name" />.ITEM_CODE = #{itemCode}</if>
    <if test="itemName != null and itemName != ''"> AND <include refid="Table_Name" />.ITEM_NAME = #{itemName}</if>
    <if test="isAccept != null "> AND <include refid="Table_Name" />.IS_ACCEPT = #{isAccept,jdbcType=DECIMAL}</if>
    <if test="errorCode != null and errorCode != ''"> AND <include refid="Table_Name" />.ERROR_CODE = #{errorCode}</if>
    <if test="errorDescription != null and errorDescription != ''"> AND <include refid="Table_Name" />.ERROR_DESCRIPTION = #{errorDescription}</if>
    <if test="workStation != null and workStation != ''"> AND <include refid="Table_Name" />.WORK_STATION = #{workStation,jdbcType=VARCHAR}</if>
    <if test="processCode != null and processCode != ''"> AND <include refid="Table_Name" />.PROCESS_CODE = #{processCode,jdbcType=VARCHAR}</if>
    <if test="craftSection != null and craftSection != ''"> AND <include refid="Table_Name" />.CRAFT_SECTION = #{craftSection,jdbcType=VARCHAR}</if>
    <if test="status != null and status != ''"> AND <include refid="Table_Name" />.STATUS = #{status,jdbcType=VARCHAR}</if>
    <if test="rcvProdplanId != null and rcvProdplanId != ''"> AND <include refid="Table_Name" />.RCV_PRODPLAN_ID = #{rcvProdplanId,jdbcType=VARCHAR}</if>
    <if test="sendFlag != null and sendFlag != ''"> AND <include refid="Table_Name" />.SEND_FLAG in (${sendFlag})</if>
    <if test="rcvFlag != null and rcvFlag != ''"> AND <include refid="Table_Name" />.RCV_FLAG in (${rcvFlag})</if>
    <if test="billType != null and billType != ''"> AND PM_REPAIR_RCV.BILL_TYPE = #{billType}::numeric</if>
    <if test="inSns != null and inSns != ''"> and <include refid="Table_Name" />.sn in (${inSns})</if>
    <if test="orgId != null"> AND <include refid="Table_Name" />.ORG_ID = cast(#{orgId} as numeric)</if>
    <if test="factoryId != null"> AND <include refid="Table_Name" />.FACTORY_ID = cast(#{factoryId} as numeric)</if>
    <if test="entityId != null"> AND <include refid="Table_Name" />.ENTITY_ID = cast(#{entityId} as numeric)</if>

    <if test="lastUpdatedDate !=null "> AND <include refid="Table_Name" />.LAST_UPDATED_DATE &gt;= to_timestamp(#{lastUpdatedDate},'yyyy-mm-dd hh24:mi:ss') </if>
    <if test="lastUpdatedBy !=null and lastUpdatedBy != ''"> AND <include refid="Table_Name" />.LAST_UPDATED_BY=#{lastUpdatedBy} </if>
  </sql>
  <sql id="ORDER_FILELD">
    <if test="sort != null">
      <choose>
        <when test="sort=='receptionDetailId'"> order by <include refid="Table_Name" />.RECEPTION_DETAIL_ID <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='receptionId'"> order by <include refid="Table_Name" />.RECEPTION_ID <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='sn'"> order by <include refid="Table_Name" />.SN <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='itemCode'"> order by <include refid="Table_Name" />.ITEM_CODE <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='itemName'"> order by <include refid="Table_Name" />.ITEM_NAME <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='isAccept'"> order by <include refid="Table_Name" />.IS_ACCEPT <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='lastUpdatedDate'"> order by <include refid="Table_Name" />.last_updated_date <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='lastUpdatedBy'"> order by <include refid="Table_Name" />.last_updated_by <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='createDate'"> order by <include refid="Table_Name" />.create_date <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='createBy'"> order by <include refid="Table_Name" />.create_by <if test="order != null and order == 'desc'"> desc </if> </when>
      </choose>
    </if>
  </sql>
  <sql id="RCV_ORDER_FILELD">
    <if test="sort != null">
      <choose>
        <when test="sort=='receptionDetailId'"> order by <include refid="Table_Name" />.RECEPTION_DETAIL_ID <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='receptionId'"> order by <include refid="Table_Name" />.RECEPTION_ID <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='sn'"> order by <include refid="Table_Name" />.SN <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='itemCode'"> order by <include refid="Table_Name" />.ITEM_CODE <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='itemName'"> order by <include refid="Table_Name" />.ITEM_NAME <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='isAccept'"> order by <include refid="Table_Name" />.IS_ACCEPT <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='lastUpdatedDate'"> order by PM_REPAIR_RCV.last_updated_date <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='createDate'"> order by PM_REPAIR_RCV.create_date <if test="order != null and order == 'desc'"> desc </if> </when>
      </choose>
    </if>
  </sql>
  <!--mybatis引用： 基础列结构   end -->

  <!--mybatis引用： 引用列结构  begin
  <sql id="Rel_Column_List">
    <include refid="Table_Name" />.RECEPTION_DETAIL_ID,
    <include refid="Table_Name" />.RECEPTION_ID,
    <include refid="Table_Name" />.REPAIR_ID,
    <include refid="Table_Name" />.SN,
    <include refid="Table_Name" />.ITEM_CODE,
    <include refid="Table_Name" />.ITEM_NAME,
    <include refid="Table_Name" />.IS_ACCEPT,
    <include refid="Table_Name" />.ENABLED_FLAG,
    <include refid="Table_Name" />.ERROR_CODE,
    <include refid="Table_Name" />.ERROR_DESCRIPTION,
    <include refid="Table_Name" />.WORK_STATION,
    <include refid="Table_Name" />.PROCESS_CODE,
    <include refid="Table_Name" />.CRAFT_SECTION,
    <include refid="Table_Name" />.STATUS,
    <include refid="Table_Name" />.CREATE_BY,
    <include refid="Table_Name" />.CREATE_DATE,
    <include refid="Table_Name" />.LAST_UPDATED_BY,
    <include refid="Table_Name" />.LAST_UPDATED_DATE,
    <include refid="Table_Name" />.ENABLED_FLAG,
    <include refid="Table_Name" />.ORG_ID,
    <include refid="Table_Name" />.FACTORY_ID,
    <include refid="Table_Name" />.ENTITY_ID
  </sql>-->
  <!--mybatis引用： 引用列结构  begin>>-->
  <sql id="Rel_Column_Lists">
    <include refid="Table_Name" />.RECEPTION_DETAIL_ID,
    <include refid="Table_Name" />.RECEPTION_ID,
    <include refid="Table_Name" />.REPAIR_ID,
    <include refid="Table_Name" />.SN,
    <include refid="Table_Name" />.ITEM_CODE,
    <include refid="Table_Name" />.ITEM_NAME,
    <include refid="Table_Name" />.IS_ACCEPT,
    <include refid="Table_Name" />.ERROR_CODE,
    <include refid="Table_Name" />.ERROR_DESCRIPTION,
    <include refid="Table_Name" />.WORK_STATION,
    <include refid="Table_Name" />.PROCESS_CODE,
    <include refid="Table_Name" />.CRAFT_SECTION,
    <include refid="Table_Name" />.CREATE_BY,
    <include refid="Table_Name" />.CREATE_DATE,
    <include refid="Table_Name" />.LAST_UPDATED_BY,
    <include refid="Table_Name" />.LAST_UPDATED_DATE,
    <include refid="Table_Name" />.ENABLED_FLAG,
    <include refid="Table_Name" />.ORG_ID,
    <include refid="Table_Name" />.FACTORY_ID,
    <include refid="Table_Name" />.ENTITY_ID,
    <include refid="Table_Name" />.STATUS,
    <include refid="Table_Name" />.REPAIR_RCV_DATE,
    <include refid="Table_Name" />.RCV_PRODPLAN_ID,
    <include refid="Table_Name" />.SEND_FLAG,
    <include refid="Table_Name" />.RCV_FLAG,
    PM_REPAIR_RCV.DELIVERY_NO,
    PM_REPAIR_RCV.BILL_TYPE,
    PM_REPAIR_RCV.DELIVERY_BY,
    PM_REPAIR_RCV.RECEPTION_BY,
    PM_REPAIR_RCV.QTY,
    PM_REPAIR_RCV.BUILDING,
    PM_REPAIR_RCV.WAREHOUSE_CODE,
    PM_REPAIR_RCV.FROM_STATION
  </sql>
  <!--mybatis引用： 关联表名 -->
  <sql id="Rel_Table_Name">

    <include refid="Table_Name" />,
    PM_REPAIR_RCV

  </sql>
  <sql id="Rel_Condtions">
    PM_REPAIR_RCV_DETAIL.RECEPTION_ID = PM_REPAIR_RCV.RECEPTION_ID
    AND PM_REPAIR_RCV.ENABLED_FLAG = 'Y' AND <include refid="Condtions" />
  </sql>
  <!--mybatis引用：引用列结构  <<end -->



  <!--
      mybatis 插入命令：有选择性的增加不良原因维护数据
  -->
  <insert id="insertPmRepairRcvDetailSelective" parameterType="com.zte.domain.model.PmRepairRcvDetail">
    insert into <include refid="Table_Name" />
    <trim prefix="(" suffix=")" suffixOverrides=","><include refid="Column_Filter_List" /></trim>
    <trim prefix="values (" suffix=")" suffixOverrides=","><include refid="Filed_Filter_List" /></trim>
  </insert>
  <!--pmRepairRcvDetailService
      mybatis 删除命令：删除不良原因维护根据主键
                   说明：将有效标识设置为N
                    条件：RECEPTION_DETAIL_ID
  -->
  <delete id="deletePmRepairRcvDetailByCode" parameterType="com.zte.domain.model.PmRepairRcvDetail">
    update <include refid="Table_Name" />
    set ENABLED_FLAG = 'N'
    where <include refid="Base_Condtions" />
  </delete>

  <!--
       mybatis 修改命令：有选择性的更新不良原因维护数据
   -->
  <update id="updatePmRepairRcvDetailByCodeSelective" parameterType="com.zte.domain.model.PmRepairRcvDetail">
    update <include refid="Table_Name" />
    <set>
      <include refid="Column_Filed_List" />
    </set>
    where <include refid="Base_Condtions" />
  </update>
  <!--
      mybatis 查询命令：查询不良原因维护根据主键
                    条件：RECEPTION_DETAIL_ID
  -->
  <select id="selectPmRepairRcvDetailById" parameterType="com.zte.domain.model.PmRepairRcvDetail" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from   <include refid="Table_Name" />
    where
    <include refid="Table_Name" />.ENABLED_FLAG = 'Y' AND <include refid="Table_Name" />.RECEPTION_DETAIL_ID = #{receptionDetailId}
  </select>
  <!--
      mybatis 查询命令：获取符合条件的不良原因维护数据
  -->
  <select id="getList" parameterType="com.zte.interfaces.dto.PmRepairRcvDetailDTO" resultMap="BaseResultMap">
    SELECT <include refid="Base_Column_List" />
    FROM   <include refid="Table_Name" />
    WHERE
    <include refid="Table_Name" />.ENABLED_FLAG = 'Y'
    <if test="receptionDetailId != null and receptionDetailId != ''"> AND <include refid="Table_Name" />.RECEPTION_DETAIL_ID = #{receptionDetailId}</if>
    <if test="receptionId != null and receptionId != ''"> AND <include refid="Table_Name" />.RECEPTION_ID = #{receptionId}</if>
    <if test="sn != null and sn != ''"> AND <include refid="Table_Name" />.SN = #{sn}</if>
    <if test="itemCode != null and itemCode != ''"> AND <include refid="Table_Name" />.ITEM_CODE = #{itemCode}</if>
    <if test="itemName != null and itemName != ''"> AND <include refid="Table_Name" />.ITEM_NAME = #{itemName}</if>
    <if test="isAccept != null "> AND <include refid="Table_Name" />.IS_ACCEPT = #{isAccept,jdbcType=DECIMAL}</if>
    <if test="errorCode != null and errorCode != ''"> AND <include refid="Table_Name" />.ERROR_CODE = #{errorCode}</if>
    <if test="errorDescription != null and errorDescription != ''"> AND <include refid="Table_Name" />.ERROR_DESCRIPTION = #{errorDescription}</if>
    <if test="workStation != null and workStation != ''"> AND <include refid="Table_Name" />.WORK_STATION = #{workStation,jdbcType=VARCHAR}</if>
    <if test="processCode != null and processCode != ''"> AND <include refid="Table_Name" />.PROCESS_CODE = #{processCode,jdbcType=VARCHAR}</if>
    <if test="craftSection != null and craftSection != ''"> AND <include refid="Table_Name" />.CRAFT_SECTION = #{craftSection,jdbcType=VARCHAR}</if>
    <if test="status != null and status != ''"> AND <include refid="Table_Name" />.STATUS = #{status,jdbcType=VARCHAR}</if>
    <if test="rcvProdplanId != null and rcvProdplanId != ''"> AND <include refid="Table_Name" />.RCV_PRODPLAN_ID = #{rcvProdplanId,jdbcType=VARCHAR}</if>
    <if test="sendFlag != null and sendFlag != ''"> AND <include refid="Table_Name" />.SEND_FLAG in (${sendFlag})</if>
    <if test="rcvFlag != null and rcvFlag != ''"> AND <include refid="Table_Name" />.RCV_FLAG in (${rcvFlag})</if>
    <if test="billType != null and billType != ''"> AND PM_REPAIR_RCV.BILL_TYPE = #{billType}::numeric</if>
    <if test="inSns != null and inSns != ''"> and <include refid="Table_Name" />.sn in (${inSns})</if>
    <if test="lastUpdatedDate !=null "> AND <include refid="Table_Name" />.LAST_UPDATED_DATE &gt;= to_timestamp(#{lastUpdatedDate},'yyyy-mm-dd hh24:mi:ss') </if>
    <if test="lastUpdatedBy !=null and lastUpdatedBy != ''"> AND <include refid="Table_Name" />.LAST_UPDATED_BY=#{lastUpdatedBy} </if>

    <if test="(receptionDetailId == null or receptionDetailId == '') and (receptionId == null or receptionId == '')
        and (sn == null or sn == '') and (itemCode == null or itemCode == '')
        and (itemName == null or itemName == '') and (isAccept == null or isAccept == '')
        and (errorCode == null or errorCode == '') and (errorDescription == null or errorDescription == '')
        and (workStation == null or workStation == '') and (processCode == null or processCode == '')
        and (status == null or status == '') and (craftSection == null or craftSection == '')
        and (rcvProdplanId == null or rcvProdplanId == '') and (sendFlag == null or sendFlag == '')
        and (rcvFlag == null or rcvFlag == '') and (billType == null or billType == '')
        and (inSns == null or inSns == '') and (lastUpdatedBy == null or lastUpdatedBy == '')
            and lastUpdatedDate == null  "> and 1=2</if>

    <include refid="ORDER_FILELD" />
  </select>
  <!--
      mybatis 查询命令：获取符合条件的不良原因维护数据
  -->
  <select id="getRelOneList" parameterType="com.zte.interfaces.dto.PmRepairRcvDetailDTO" resultMap="RelOneResultMap">
    SELECT <include refid="Rel_Column_Lists" />
    FROM   <include refid="Rel_Table_Name" />
    WHERE <include refid="Rel_Condtions" />
    <if test="(receptionDetailId == null or receptionDetailId == '') and (receptionId == null or receptionId == '')
        and (sn == null or sn == '') and (itemCode == null or itemCode == '') and (snList == null or snList.size() == 0)
        and (itemName == null or itemName == '') and (isAccept == null or isAccept == '')
        and (errorCode == null or errorCode == '') and (errorDescription == null or errorDescription == '')
        and (workStation == null or workStation == '') and (processCode == null or processCode == '')
        and (status == null or status == '') and (craftSection == null or craftSection == '')
        and (rcvProdplanId == null or rcvProdplanId == '') and (sendFlag == null or sendFlag == '')
        and (rcvFlag == null or rcvFlag == '') and (billType == null or billType == '')
        and (inSns == null or inSns == '') and (lastUpdatedBy == null or lastUpdatedBy == '')
            and lastUpdatedDate == null  "> and 1=2</if>
    <include refid="RCV_ORDER_FILELD" />
  </select>
  <!-- 翻页函数:获取一页的记录集 -->
  <select id="getRelOnePage" parameterType="com.zte.interfaces.dto.PmRepairRcvDetailDTO" resultMap="RelOneResultMap">

    SELECT * FROM   (
    SELECT U.*, ROWNUM RN
    FROM (
    SELECT <include refid="Rel_Column_Lists" />
    FROM <include refid="Rel_Table_Name" />
    WHERE
    <include refid="Rel_Condtions" />
    <include refid="RCV_ORDER_FILELD" />
    ) U
    )
    <if test="startRow != null and endRow != null"> where RN BETWEEN #{startRow}::numeric AND #{endRow}::numeric</if>
  </select>

  <insert id="insertPmRepairRcvDetailBatch" parameterType="java.util.List" >
    insert into PM_REPAIR_RCV_DETAIL
    (
    RECEPTION_DETAIL_ID, RECEPTION_ID, REPAIR_ID,ITEM_CODE,ITEM_NAME, SN, IS_ACCEPT,
    ENABLED_FLAG, CREATE_DATE, CREATE_BY, LAST_UPDATED_BY, LAST_UPDATED_DATE, ORG_ID,
    FACTORY_ID, ENTITY_ID, ERROR_CODE, ERROR_DESCRIPTION,
    WORK_STATION,PROCESS_CODE,CRAFT_SECTION,STATUS,REPAIR_RCV_DATE,
    RCV_PRODPLAN_ID,SEND_FLAG,RCV_FLAG,SCRAP_REASON,SCAN_TIME,
    MATERIAL_VERSION,PCB_VERSION,DEVICE_BAR_CODE,TASK_NO,WORK_ORDER_NO,FRONT_SOFTWARE_VERSION,
    BACK_SOFTWARE_VERSION,RACK_BAR_CODE,ECC_DELIVERY_FORM,TEST_RECORD_NO ,DETAILS_REMARK,ERROR_MESSAGE,
    RETURNED_BY,RETURNED_DATE,RETURNED_TO,CRAFT_SECTION_ZS,PROCESS_CODE_ZS,WORK_STATION_ZS,
    RECEIVING_TIME,PRODUCT_CLASS,PRODUCT_SMLCLASS,STYLE,REPLACING_BARCODE,RECEPTION_BY,REPAIR_COUNT,LINE_CODE,LOCATION_NO
    )
    select RECEPTION_DETAIL_ID, RECEPTION_ID, REPAIR_ID,ITEM_CODE,ITEM_NAME, SN, IS_ACCEPT,
    ENABLED_FLAG, CREATE_DATE, CREATE_BY, LAST_UPDATED_BY, LAST_UPDATED_DATE, ORG_ID,
    FACTORY_ID, ENTITY_ID, ERROR_CODE, ERROR_DESCRIPTION,
    WORK_STATION,PROCESS_CODE,CRAFT_SECTION,STATUS,REPAIR_RCV_DATE,
    RCV_PRODPLAN_ID,SEND_FLAG,RCV_FLAG,SCRAP_REASON,SCAN_TIME,
    MATERIAL_VERSION,PCB_VERSION,DEVICE_BAR_CODE,TASK_NO,WORK_ORDER_NO,FRONT_SOFTWARE_VERSION,
    BACK_SOFTWARE_VERSION,RACK_BAR_CODE,ECC_DELIVERY_FORM,TEST_RECORD_NO ,DETAILS_REMARK,ERROR_MESSAGE,
    RETURNED_BY,RETURNED_DATE,RETURNED_TO,CRAFT_SECTION_ZS,PROCESS_CODE_ZS,WORK_STATION_ZS,
    RECEIVING_TIME,PRODUCT_CLASS,PRODUCT_SMLCLASS,STYLE,REPLACING_BARCODE,RECEPTION_BY,REPAIR_COUNT,LINE_CODE,LOCATION_NO
    from (
    <foreach collection ="list" item="item" index= "index" separator ="UNION ALL">
      SELECT
      #{item.receptionDetailId,jdbcType=VARCHAR} RECEPTION_DETAIL_ID,
      #{item.receptionId,jdbcType=VARCHAR} RECEPTION_ID,
      #{item.repairId,jdbcType=VARCHAR} REPAIR_ID,
      #{item.itemCode,jdbcType=VARCHAR} ITEM_CODE,
      #{item.itemName,jdbcType=VARCHAR} ITEM_NAME,
      #{item.sn,jdbcType=VARCHAR} SN,
      #{item.isAccept,jdbcType=VARCHAR} IS_ACCEPT,
      'Y' ENABLED_FLAG,
      SYSDATE CREATE_DATE,
      #{item.createBy,jdbcType=VARCHAR} CREATE_BY,
      #{item.lastUpdatedBy,jdbcType=VARCHAR} LAST_UPDATED_BY,
      SYSDATE LAST_UPDATED_DATE,
      #{item.orgId,jdbcType=DECIMAL} ORG_ID,
      #{item.factoryId,jdbcType=DECIMAL} FACTORY_ID,
      #{item.entityId,jdbcType=DECIMAL} ENTITY_ID,
      #{item.errorCode,jdbcType=VARCHAR} ERROR_CODE,
      #{item.errorDescription,jdbcType=VARCHAR} ERROR_DESCRIPTION,
      #{item.workStation,jdbcType=VARCHAR} WORK_STATION,
      #{item.processCode,jdbcType=VARCHAR} PROCESS_CODE,
      #{item.craftSection,jdbcType=VARCHAR} CRAFT_SECTION,
      #{item.status,jdbcType=VARCHAR} STATUS,
      #{item.repairRcvDate,jdbcType=TIMESTAMP}::timestamp REPAIR_RCV_DATE,
      #{item.rcvProdplanId,jdbcType=VARCHAR} RCV_PRODPLAN_ID,
      #{item.sendFlag,jdbcType=VARCHAR} SEND_FLAG,
      #{item.rcvFlag,jdbcType=VARCHAR} RCV_FLAG,
      #{item.scrapReason,jdbcType=VARCHAR} SCRAP_REASON,
      SYSDATE SCAN_TIME,
      #{item.materialVersion,jdbcType=VARCHAR} MATERIAL_VERSION,
      #{item.pcbVersion,jdbcType=VARCHAR} PCB_VERSION,
      #{item.deviceBarCode,jdbcType=VARCHAR} DEVICE_BAR_CODE,
      #{item.taskNo,jdbcType=VARCHAR} TASK_NO,
      #{item.workOrderNo,jdbcType=VARCHAR} WORK_ORDER_NO,
      #{item.frontSoftwareVersion,jdbcType=VARCHAR} FRONT_SOFTWARE_VERSION,
      #{item.backSoftwareVersion,jdbcType=VARCHAR} BACK_SOFTWARE_VERSION,
      #{item.rackBarCode,jdbcType=VARCHAR} RACK_BAR_CODE,
      #{item.eccDeliveryForm,jdbcType=VARCHAR} ECC_DELIVERY_FORM,
      #{item.testRecordNo,jdbcType=VARCHAR} TEST_RECORD_NO,
      #{item.detailsRemark,jdbcType=VARCHAR} DETAILS_REMARK,
      #{item.errorMessage,jdbcType=VARCHAR} ERROR_MESSAGE,
      #{item.returnedBy,jdbcType=VARCHAR} RETURNED_BY,
      #{item.returnedDate,jdbcType=TIMESTAMP}::timestamp RETURNED_DATE,
      #{item.returnedTo,jdbcType=VARCHAR} RETURNED_TO,
      #{item.craftSectionZs,jdbcType=VARCHAR} CRAFT_SECTION_ZS,
      #{item.processCodeZs,jdbcType=VARCHAR}  PROCESS_CODE_ZS,
      #{item.workStationZs,jdbcType=VARCHAR}  WORK_STATION_ZS,
      #{item.receivingTime,jdbcType=VARCHAR}::timestamp RECEIVING_TIME,
      #{item.productClass,jdbcType=VARCHAR} PRODUCT_CLASS,
      #{item.productSmlclass,jdbcType=VARCHAR} PRODUCT_SMLCLASS,
      #{item.style,jdbcType=VARCHAR} STYLE,
      #{item.replacingBarcode,jdbcType=VARCHAR} REPLACING_BARCODE,
      #{item.receptionBy,jdbcType=VARCHAR} RECEPTION_BY,
      #{item.repairCount,jdbcType=DECIMAL} REPAIR_COUNT,
      #{item.lineCode,jdbcType=VARCHAR} LINE_CODE,
      #{item.locationNo,jdbcType=VARCHAR} LOCATION_NO

    </foreach>
    ) a
  </insert>

  <update id="updateFlagNine" parameterType="java.util.List">
    UPDATE <include refid="Table_Name" />
    SET   SEND_FLAG = 9 WHERE
    <foreach collection="list" item="item" index="index" open="" close="" separator="or">
      RECEPTION_DETAIL_ID = #{item.receptionDetailId}
    </foreach>
  </update>

  <update id="updateFlag" parameterType="java.util.List">
    <foreach collection="list" item="item"   separator=";">
      UPDATE <include refid="Table_Name" />
      SET
      <if test="item.sendFlag != null and item.sendFlag != ''">
        SEND_FLAG = #{item.sendFlag,jdbcType=VARCHAR},
      </if>
      <if test="item.rcvFlag != null and item.rcvFlag != ''">
        RCV_FLAG = #{item.rcvFlag,jdbcType=VARCHAR},
      </if>
      LAST_UPDATED_DATE = sysdate
      WHERE RECEPTION_DETAIL_ID = #{item.receptionDetailId}
    </foreach>
  </update>

  <update id="updateRcvFlagNine" parameterType="java.util.List">
    UPDATE <include refid="Table_Name" />
    SET   RCV_FLAG = 9 WHERE
    <foreach collection="list" item="item" index="index" open="" close="" separator="or">
      RECEPTION_DETAIL_ID = #{item.receptionDetailId}
    </foreach>
  </update>

  <update id="updateRcvFlag" parameterType="java.util.List">
    <foreach collection="list" item="item" index="index" separator=";">
      UPDATE <include refid="Table_Name" />
      SET   RCV_FLAG = #{item.rcvFlag,jdbcType=VARCHAR} WHERE
      RECEPTION_DETAIL_ID = #{item.receptionDetailId}
    </foreach>
  </update>

  <!--运维处理： 后台维修返还 条码-->
  <update id="omBatchRepairReturn"  parameterType="java.util.List">
    <foreach collection="list" separator=";" item="item" >
      update pm_repair_rcv_detail t set t.is_accept = 1, t.status = '10560003' ,t.last_updated_date = sysdate,t.REPAIR_RCV_DATE = sysdate
      where t.sn = #{item} and t.status in ('10560001','10560002')
    </foreach>
  </update>

  <!--运维处理： 后台维修返还 条码-->
  <select id="omGetRepairReturnBySn" resultMap="RelOneResultMap">
    select * from ( select  <include refid="Base_Column_List" />  from pm_repair_rcv_detail a where a.sn = #{sn} and a.status in (#{repairStatusRestore},#{repairStatusScrap})
    and exists (select 1 from pm_repair_rcv t where t.RECEPTION_ID = a.reception_id and t.from_station != '单板生产')
    order by a.last_updated_date desc ) where rownum =1
  </select>

  <!-- 根据条码查询送修记录 -->
  <select id="getLatestRcvDetailBySn" resultMap="RelOneResultMap">
    select sn, item_code, item_name, error_description, reception_detail_id, line_code , work_order_no, rcv_prodplan_id, create_date, create_by
      from pm_repair_rcv_detail t
      where t.sn = #{sn}
      <if test="isAccept != null">
        and is_accept = #{isAccept}
      </if>
      order by last_updated_date desc limit 1
  </select>

  <delete id="deletePmRepairRcvDetailByReceptionDetailId" parameterType="java.util.List">
    DELETE FROM  pm_repair_rcv_detail
    where 1=1
    AND ENABLED_FLAG = 'Y'
    AND SN in
    <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
      #{item.sn}
    </foreach>
    AND RECEPTION_ID in
    <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
      #{item.receptionId}
    </foreach>
  </delete>

  <update id="updateStatusByReceptionId"  parameterType="java.util.List">
    <foreach collection="list" separator=";" item="item" >
      update pm_repair_rcv_detail t set
      t.status=#{item.status,jdbcType=VARCHAR},
      t.IS_ACCEPT=#{item.isAccept,jdbcType=VARCHAR},
      t.last_updated_date = sysdate,
      t.LAST_UPDATED_BY = #{item.lastUpdatedBy,jdbcType=VARCHAR}
      where t.RECEPTION_ID = #{item.receptionId}
      and t.sn=#{item.sn}
      and t.ENABLED_FLAG = 'Y'
    </foreach>
  </update>

  <!-- 筛选维修未返还sn -->
  <select id="getRepairSnNotReturned" parameterType="java.util.List" resultMap="OnlySnResultMap" >
    select distinct t.sn from pm_repair_rcv_detail t
    where
    <if test="list == null">
      1=2
    </if>
    <if test="list != null">
      t.sn in
      <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    and t.enabled_flag = 'Y'
    and (t.is_accept != 1 or t.status != '10560003')
  </select>

  <select id="getDetailList" parameterType="com.zte.domain.model.PmRepairRcvDetail" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" /> FROM  pm_repair_rcv_detail
    where 1=1
    AND ENABLED_FLAG = 'Y'
    <if test="receptionId != null and receptionId != ''"> AND RECEPTION_ID = #{receptionId}</if>
    <if test="receptionDetailId != null and receptionDetailId != ''">
      AND RECEPTION_DETAIL_ID IN (${receptionDetailId})
    </if>
    <if test="(receptionId == null or receptionId == '') and (receptionDetailId == null or receptionDetailId == '') ">and  1=2</if>

  </select>

  <update id="updatePmRepairRcvDetailBatch" parameterType="java.util.List">
    <foreach collection="list" separator=";" item="item" >
      update pm_repair_rcv_detail
      <set>
        <include refid="Column_Filed_List" />
      </set>
      where ENABLED_FLAG = 'Y'
      <if test="item.receptionId != null and item.receptionId != ''"> AND RECEPTION_ID = #{item.receptionId}</if>
      <if test="item.sn != null and item.sn != ''"> AND SN = #{item.sn}</if>
    </foreach>
  </update>


  <update id="updatePmRepairRcvDetailBySn" parameterType="com.zte.domain.model.PmRepairRcvDetail">
    update pm_repair_rcv_detail
    set status=#{status,jdbcType=VARCHAR},device_bar_code=#{deviceBarCode,jdbcType=VARCHAR},
    last_updated_by = #{lastUpdatedBy,jdbcType=VARCHAR},last_updated_date= SYSDATE
    where sn=#{sn} AND ENABLED_FLAG = 'Y' AND status = '10560001'
  </update>

  <!--   维修录入-查询待维修和维修置换的送修信息-->
  <select id="getToRepairRecInfo" parameterType="com.zte.interfaces.dto.PmRepairRcvDetailDTO" resultMap="BaseResultMapDto">
    select
    t.RECEPTION_DETAIL_ID ,
    t.STATUS ,
    t.SN,
    t.ITEM_CODE,
    t.MATERIAL_VERSION,

    t.PCB_VERSION ,
    t.ITEM_NAME,
    t.ERROR_CODE,
    t.ERROR_DESCRIPTION,
    t.CRAFT_SECTION,

    t.PROCESS_CODE,
    t.DEVICE_BAR_CODE ,
    t.TASK_NO,
    t.WORK_ORDER_NO ,
    t.RECEPTION_BY ,

    t.RECEIVING_TIME,
    t.PRODUCT_CLASS,
    t.PRODUCT_SMLCLASS,
    t.LINE_CODE,
    t.LOCATION_NO,
    t.REPAIR_COUNT,
    t.STYLE,
    t.DETAILS_REMARK,

    t.REPLACING_BARCODE,
    t.RCV_PRODPLAN_ID,

    h.RECEPTION_ID,
    h.DELIVERY_NO,
    h.FROM_STATION,
    h.BUILDING,
    h.APPLICATION_DEPARTMENT ,

    h.APPLICATION_SECTION,
    h.SN_TYPE,
    h.CREATE_BY,
    h.CREATE_DATE
    from pm_repair_rcv_detail t
    left  join pm_repair_rcv h ON t.RECEPTION_ID = h.RECEPTION_ID
    WHERE t.ENABLED_FLAG = 'Y'
    <if test="sn != null and sn != ''">AND t.SN = #{sn}</if>
    and t.STATUS in ('10560001','10560002','10560007')
    <if test="sn == null or sn == ''">AND 1=2</if>
  </select>

  <select id="getToRepairRecInfoList"  parameterType="java.util.List" resultMap="BaseResultMapDto">
    select
    t.RECEPTION_DETAIL_ID,
    t.STATUS,
    t.SN,
    t.ITEM_CODE,
    t.MATERIAL_VERSION,
    t.PCB_VERSION,
    t.ITEM_NAME,
    t.ERROR_CODE,
    t.ERROR_DESCRIPTION,
    t.CRAFT_SECTION,
    t.PROCESS_CODE,
    t.DEVICE_BAR_CODE,
    t.TASK_NO,
    t.WORK_ORDER_NO,
    t.RECEPTION_BY,
    t.RECEIVING_TIME,
    t.PRODUCT_CLASS,
    t.PRODUCT_SMLCLASS,
    t.LINE_CODE,
    t.LOCATION_NO,
    t.REPAIR_COUNT,
    t.STYLE,
    t.DETAILS_REMARK,
    t.REPLACING_BARCODE,
    t.RCV_PRODPLAN_ID,
    h.RECEPTION_ID,
    h.DELIVERY_NO,
    h.FROM_STATION,
    h.BUILDING,
    h.APPLICATION_DEPARTMENT,
    h.APPLICATION_SECTION,
    h.SN_TYPE,
    h.CREATE_BY,
    h.CREATE_DATE
    from pm_repair_rcv_detail t
    left  join pm_repair_rcv h ON t.RECEPTION_ID = h.RECEPTION_ID
    WHERE t.ENABLED_FLAG = 'Y'
    AND t.SN in
    <foreach item="sn" collection="list" open="(" separator="," close=")">
      #{sn}
    </foreach>
    and t.STATUS in ('10560001','10560002','10560007')
  </select>

  <!--  查询送修信息-->
  <select id="getRepairDetailInfoBySnOrDeviceBarCode" parameterType="java.util.Map" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from pm_repair_rcv_detail t
    WHERE t.ENABLED_FLAG = 'Y'
    AND t.FACTORY_ID = cast(#{factoryId} as numeric)
    AND (t.SN = #{sn}
    OR t.DEVICE_BAR_CODE= #{sn})
  </select>

  <update id="updatePmRepairRcvDetailZsInfoById" parameterType="com.zte.domain.model.PmRepairRcvDetail">
    update PM_REPAIR_RCV_DETAIL
    set
    LAST_UPDATED_DATE =SYSDATE,
    SCAN_TIME =SYSDATE,
    CRAFT_SECTION_ZS = #{craftSectionZs,jdbcType=VARCHAR},
    PROCESS_CODE_ZS = #{processCodeZs,jdbcType=VARCHAR},
    WORK_STATION_ZS = #{workStationZs,jdbcType=VARCHAR}
    where ENABLED_FLAG = 'Y'
    AND RECEPTION_DETAIL_ID = #{receptionDetailId,jdbcType=VARCHAR}
  </update>

  <select id="getRepairCount" parameterType="com.zte.interfaces.dto.PmRepairRcvDetailDTO" resultType="java.lang.Long">
    select count(*)
    from PM_REPAIR_RCV_DETAIL t
    WHERE t.ENABLED_FLAG = 'Y'
    AND t.SN=#{sn,jdbcType=VARCHAR}
  </select>

  <select id="getRecentRepairCount" resultType="java.lang.Integer">
    SELECT NVL(max(DETAIL.REPAIR_COUNT),0) FROM PM_REPAIR_RCV INFO, PM_REPAIR_RCV_DETAIL DETAIL
    WHERE INFO.RECEPTION_ID = DETAIL.RECEPTION_ID AND INFO.ENABLED_FLAG = 'Y' AND DETAIL.ENABLED_FLAG = 'Y'
    <if test=" sn== null">
      AND 1 = 2
    </if>
    <if test=" sn!= null and sn!= ''">
      AND DETAIL.SN = #{sn}
    </if>
  </select>

  <update id="updatePmRepairRcvDetailByDetailId" parameterType="java.util.List">
    <foreach collection="list" separator=";" item="item" >
      update pm_repair_rcv_detail
      <set>
        <if test="item.receptionId != null">RECEPTION_ID = #{item.receptionId,jdbcType=VARCHAR},</if>
        <if test="item.sn != null">SN = #{item.sn,jdbcType=VARCHAR},</if>
        <if test="item.itemCode != null">ITEM_CODE = #{item.itemCode,jdbcType=VARCHAR},</if>
        <if test="item.itemName != null">ITEM_NAME = #{item.itemName,jdbcType=VARCHAR},</if>
        <if test="item.isAccept != null">IS_ACCEPT = #{item.isAccept,jdbcType=VARCHAR},</if>
        <if test="item.errorCode != null">ERROR_CODE = #{item.errorCode,jdbcType=VARCHAR},</if>
        <if test="item.errorDescription != null">ERROR_DESCRIPTION = #{item.errorDescription,jdbcType=VARCHAR},</if>
        <if test="item.workStation != null">WORK_STATION = #{item.workStation,jdbcType=VARCHAR},</if>
        <if test="item.processCode != null">PROCESS_CODE = #{item.processCode,jdbcType=VARCHAR},</if>
        <if test="item.craftSection != null">CRAFT_SECTION = #{item.craftSection,jdbcType=VARCHAR},</if>
        <if test="item.status != null">STATUS = #{item.status,jdbcType=VARCHAR},</if>
        <if test="item.repairRcvDate != null">REPAIR_RCV_DATE = #{item.repairRcvDate,jdbcType=TIMESTAMP},</if>
        <if test="item.rcvProdplanId != null">RCV_PRODPLAN_ID = #{item.rcvProdplanId},</if>
        <if test="item.sendFlag != null">SEND_FLAG = #{item.sendFlag},</if>
        <if test="item.rcvFlag != null">RCV_FLAG = #{item.rcvFlag},</if>
        <if test="item.lastUpdatedBy != null">LAST_UPDATED_BY = #{item.lastUpdatedBy,jdbcType=VARCHAR}, </if>
        <if test="item.materialVersion != null">MATERIAL_VERSION = #{item.materialVersion,jdbcType=VARCHAR}, </if>
        <if test="item.pcbVersion != null">PCB_VERSION = #{item.pcbVersion,jdbcType=VARCHAR}, </if>
        <if test="item.deviceBarCode != null">DEVICE_BAR_CODE = #{item.deviceBarCode,jdbcType=VARCHAR}, </if>
        <if test="item.taskNo != null">TASK_NO = #{item.taskNo,jdbcType=VARCHAR}, </if>
        <if test="item.workOrderNo != null">WORK_ORDER_NO = #{item.workOrderNo,jdbcType=VARCHAR}, </if>
        <if test="item.frontSoftwareVersion != null">FRONT_SOFTWARE_VERSION = #{item.frontSoftwareVersion,jdbcType=VARCHAR}, </if>
        <if test="item.backSoftwareVersion != null">BACK_SOFTWARE_VERSION = #{item.backSoftwareVersion,jdbcType=VARCHAR}, </if>
        <if test="item.rackBarCode != null">RACK_BAR_CODE = #{item.rackBarCode,jdbcType=VARCHAR}, </if>
        <if test="item.eccDeliveryForm != null">ECC_DELIVERY_FORM = #{item.eccDeliveryForm,jdbcType=VARCHAR}, </if>
        <if test="item.testRecordNo != null">TEST_RECORD_NO = #{item.testRecordNo,jdbcType=VARCHAR}, </if>
        <if test="item.detailsRemark != null">DETAILS_REMARK = #{item.detailsRemark,jdbcType=VARCHAR}, </if>
        <if test="item.errorMessage != null">ERROR_MESSAGE = #{item.errorMessage,jdbcType=VARCHAR}, </if>
        <if test="item.receivingTime != null">RECEIVING_TIME = #{item.receivingTime,jdbcType=TIMESTAMP}, </if>
        <if test="item.productClass != null">PRODUCT_CLASS = #{item.productClass,jdbcType=VARCHAR}, </if>
        <if test="item.productSmlclass != null">PRODUCT_SMLCLASS = #{item.productSmlclass,jdbcType=VARCHAR}, </if>
        <if test="item.style != null">STYLE = #{item.style,jdbcType=VARCHAR}, </if>
        <if test="item.replacingBarcode != null">REPLACING_BARCODE = #{item.replacingBarcode,jdbcType=VARCHAR}, </if>
        <if test="item.receptionBy != null">RECEPTION_BY = #{item.receptionBy,jdbcType=VARCHAR}, </if>
        <if test="item.repairCount != null">REPAIR_COUNT = #{item.repairCount,jdbcType=DECIMAL}, </if>
        <if test="item.lineCode != null">LINE_CODE = #{item.lineCode,jdbcType=VARCHAR}, </if>
        <if test="item.locationNo != null">LOCATION_NO = #{item.locationNo,jdbcType=VARCHAR}, </if>
      </set>
      where ENABLED_FLAG = 'Y'
      <if test="item.receptionDetailId == null and item.receptionDetailId == ''"> AND 1 = 2</if>
      <if test="item.receptionDetailId != null and item.receptionDetailId != ''"> AND RECEPTION_DETAIL_ID = #{item.receptionDetailId}</if>
    </foreach>
  </update>

  <!-- 送修查询分页 -->
  <select id="getSns" parameterType="com.zte.interfaces.dto.PmRepairRcvDetailDTO" resultType="java.lang.String">
    SELECT SN FROM   (
    SELECT U.*, ROWNUM RN
    FROM (
    SELECT DETAIL.SN FROM PM_REPAIR_RCV_DETAIL DETAIL
    WHERE DETAIL.ENABLED_FLAG = 'Y'
    GROUP BY DETAIL.SN ORDER BY DETAIL.SN
    ) U
    )
    <if test="startRow != null and endRow != null"> where RN BETWEEN #{startRow}::numeric AND #{endRow}::numeric</if>
  </select>

  <!-- 查询条码总数 -->
  <select id="getSnCount" resultType="java.lang.Integer">
    SELECT COUNT(*) FROM (
    SELECT DETAIL.SN FROM PM_REPAIR_RCV INFO, PM_REPAIR_RCV_DETAIL DETAIL
    WHERE INFO.RECEPTION_ID = DETAIL.RECEPTION_ID
    AND INFO.ENABLED_FLAG = 'Y'
    GROUP BY DETAIL.SN)
  </select>

  <!-- 获取批次是否送修记录 -->
  <select id="getProdplanSnRecord" resultType="java.lang.Integer">
    SELECT count(b.sn)
    FROM PM_REPAIR_RCV a
    INNER JOIN PM_REPAIR_RCV_DETAIL b ON a.RECEPTION_ID = b.RECEPTION_ID
    WHERE b.RCV_PRODPLAN_ID = #{prodplanId, jdbcType=VARCHAR}
    AND a.sn_type = #{snType, jdbcType=VARCHAR}
    AND b.STATUS in ('10560001', '10560002', '10560003', '10560004', '10560007')
    AND b.ENABLED_FLAG = 'Y'
    AND a.ENABLED_FLAG = 'Y'
  </select>

  <!-- 获取批次待维修的最小条码 -->
  <select id="getRepairMinSn" resultType="java.lang.String">
    SELECT min(b.sn)
    FROM PM_REPAIR_RCV a
    INNER JOIN PM_REPAIR_RCV_DETAIL b ON a.RECEPTION_ID = b.RECEPTION_ID
    WHERE b.RCV_PRODPLAN_ID = #{prodplanId, jdbcType=VARCHAR}
    AND b.STATUS = '10560001'
    AND a.SN_TYPE = '2'
    AND a.ENABLED_FLAG = 'Y'
    AND b.ENABLED_FLAG = 'Y'
  </select>

  <!-- 获取无条码送修批次待维修的条码 -->
  <select id="getNoSnRepairSns" resultType="java.lang.String">
    SELECT b.sn
    FROM PM_REPAIR_RCV a
    INNER JOIN PM_REPAIR_RCV_DETAIL b ON a.RECEPTION_ID = b.RECEPTION_ID
    WHERE b.RCV_PRODPLAN_ID = #{prodplanId, jdbcType=VARCHAR}
    <if test="startSn != null and startSn != ''">AND b.sn > #{startSn,jdbcType=VARCHAR}</if>
    AND b.STATUS = '10560001'
    AND a.SN_TYPE = '2'
    AND a.ENABLED_FLAG = 'Y'
    order by b.sn
    limit #{qty}::numeric
  </select>

  <!-- Started by AICoder, pid:7d96e0cf51q7a8a14d9d0a5a00dbc4205d710883 -->
  <!-- 获取最新送修记录 -->
  <select id="getTheLatestRepairRecord" resultType="com.zte.domain.model.PmRepairRcvDetail">
    SELECT sn, craft_section, work_station, process_code, reception_detail_id, last_updated_date
    FROM (
    SELECT
    sn,
    craft_section,
    work_station,
    process_code,
    reception_detail_id,
    last_updated_date,
    ROW_NUMBER() OVER (PARTITION BY sn ORDER BY last_updated_date DESC, reception_detail_id) AS rank
    FROM pm_repair_rcv_detail
    WHERE enabled_flag = 'Y'
    AND sn IN
    <foreach collection="snList" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
    ) subquery
    WHERE rank = 1
  </select>

  <!-- Ended by AICoder, pid:7d96e0cf51q7a8a14d9d0a5a00dbc4205d710883 -->

  <select id="getRepairSnForCusDataPush" resultType="com.zte.domain.model.PmRepairRcvDetail">
    select * from (select sn, craft_section, reception_detail_id, last_updated_date,
    row_number() over (partition by sn, craft_section order by last_updated_date desc) as rank
    from pm_repair_rcv_detail
    where enabled_flag = 'Y'
    and
    <foreach collection="itemNos" item="itemNo" open="(" separator="or" close=")">
      item_code like #{itemNo} || '%'
    </foreach>
    and craft_section in
    <foreach collection="sections" item="section" open="(" separator="," close=")">
      #{section}
    </foreach>
    and last_updated_date between #{startTime}::timestamp - interval '5 min' and #{endTime}
    ) where rank = 1
    order by last_updated_date, sn
    limit 100 offset #{pageNum} * 100
  </select>

  <!-- 获取条码维修完成并且维修次小类为报废的数据-->
  <select id="getLatestDataBySnList" parameterType="java.util.List" resultMap="BaseResultMapDto">
    SELECT  b.sn,b.STATUS,prd.REPAIR_PRODUCT_MSTYPE
    FROM PM_REPAIR_RCV a
    INNER JOIN PM_REPAIR_RCV_DETAIL b ON a.RECEPTION_ID = b.RECEPTION_ID
    LEFT JOIN PM_REPAIR_INFO pri
    ON pri.RECEPTION_ID = b.RECEPTION_DETAIL_ID
    LEFT JOIN PM_REPAIR_DETAIL prd on prd.REPAIR_ID = pri.REPAIR_ID
    WHERE  a.ENABLED_FLAG = 'Y' and b.ENABLED_FLAG = 'Y' and b.status = '10560002' and prd.REPAIR_PRODUCT_MSTYPE ='报废'  AND b.SN in
    <foreach collection="snList" index="index" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>
  <!-- 将条码的状态修改为维修报废 -->
  <update id="updateRepairScrap" parameterType="com.zte.interfaces.dto.PmRepairRcvDetailDTO">
    update
    pm_repair_rcv_detail t
    set
    t.status = '10560004',
    t.last_updated_by = #{lastUpdatedBy, jdbcType=VARCHAR},
    t.returned_by = #{returnedBy, jdbcType=VARCHAR},
    t.returned_date = sysdate,
    t.last_updated_date = sysdate,
    t.repair_rcv_date = sysdate
    where
    t.sn in
    <foreach collection="snList" item="item" index="index" open="(" separator="," close=")">
      #{item}
    </foreach>
  </update>

  <select id="getRepairSnList" resultMap="TestStatMap" parameterType="java.util.List">
    select
    rcv_prodplan_id prodplan_id, craft_section test_craft_section, count(1) test_defect_qty
    from (
    select
    distinct t.sn, t.craft_section, t.rcv_prodplan_id
    from
    pm_repair_rcv_detail t
    where
    t.enabled_flag = 'Y'
    and t.work_order_no in
    <foreach collection="workOrderNoList" item="item" index="index" open="(" separator="," close=")">
      #{item}
    </foreach>
    and t.status not in ('10560006','10560005')
    )
    group by prodplan_id, test_craft_section
  </select>

  <select id="getTestRepairDataForPush" resultType="com.zte.domain.model.WipScanHistory">
    select 'repair'||r.sn||r.craft_section craftSection ,r.sn
    from pm_repair_rcv_detail r
    left join wip_info t on r.sn = t.sn
    WHERE r.ENABLED_FLAG = 'Y'
    AND t.CRAFT_SECTION = '维修'
    AND r.sn in
    <foreach collection="snList" index="index" item="item" separator="," open="(" close=")">
      #{item,jdbcType=VARCHAR}
    </foreach>
    AND r.CRAFT_SECTION in
    <foreach collection="testList" index="index" item="item" separator="," open="(" close=")">
      #{item,jdbcType=VARCHAR}
    </foreach>
    group by r.sn, r.CRAFT_SECTION
  </select>

  <select id="selectPmRepairRcvDetailBatch" resultType="com.zte.domain.model.PmRepairRcvDetail">
    SELECT SN,IS_ACCEPT,STATUS FROM PM_REPAIR_RCV_DETAIL
    WHERE ENABLED_FLAG = 'Y'
    AND SN IN
    <foreach collection="snList" open="(" separator="," item="item" close=")">
      #{item}
    </foreach>
  </select>

  <select id="getRepairInfoListBySnAndCraft" resultType="com.zte.interfaces.dto.PmRepairRcvDetailDTO">
    select
    nvl(prd.repair_product_type, pri.repair_product_type) as repair_type,
    nvl(prd.repair_product_stype, pri.repair_product_stype) || '-' || nvl(prd.repair_product_mstype, pri.repair_product_mstype) as repair_small_type,
    t.sn, t.craft_section, t.reception_detail_id, t.last_updated_date,
    t.create_date, t.returned_date, prd.location_no,prd.item_code
    from (
          select
          sn, craft_section, reception_detail_id, last_updated_date,create_date, returned_date,
          row_number() over (partition by sn, craft_section order by last_updated_date desc) as rank
          from pm_repair_rcv_detail
          where enabled_flag = 'Y'
          <if test="snList != null and snList.size() > 0">
            and sn  in
            <foreach collection="snList" item="item" open="(" separator="," close=")">
              #{item,jdbcType=VARCHAR}
            </foreach>
          </if>
          <if test="craftSectionList != null and craftSectionList.size() > 0">
            and craft_section in
            <foreach collection="craftSectionList" item="item" open="(" separator="," close=")">
              #{item,jdbcType=VARCHAR}
            </foreach>
          </if>
          <if test="snList == null or snList.size() == 0">
            and 1=2
          </if>
          ) t
    left join pm_repair_info pri on t.reception_detail_id = pri.reception_id  and pri.enabled_flag = 'Y'
    left join pm_repair_detail prd on pri.repair_id = prd.repair_id  and prd.enabled_flag = 'Y'
    where rank = 1
  </select>

  <select id="getProdplanSnListRecordCount" resultType="java.lang.Integer">
    SELECT count(b.sn)
    FROM PM_REPAIR_RCV a
    INNER JOIN PM_REPAIR_RCV_DETAIL b ON a.RECEPTION_ID = b.RECEPTION_ID
    WHERE b.RCV_PRODPLAN_ID in
    <foreach collection="prodplanIdList" item="item" open="(" separator="," close=")">
      #{item,jdbcType=VARCHAR}
    </foreach>
    <if test="snType != null and snType != ''">
      AND a.sn_type = #{snType, jdbcType=VARCHAR}
    </if>
    AND b.STATUS in ('10560001', '10560002', '10560003', '10560004', '10560007')
    AND b.ENABLED_FLAG = 'Y'
    AND a.ENABLED_FLAG = 'Y'
  </select>
</mapper>
