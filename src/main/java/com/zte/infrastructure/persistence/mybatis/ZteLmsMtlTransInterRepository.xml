<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.erpdt.ZteLmsMtlTransInterRepository">
  <resultMap id="BaseResultMap" type="com.zte.domain.model.erpdt.ZteLmsMtlTransInter">
    <result column="LAST_UPDATE_DATE" jdbcType="TIMESTAMP" property="lastUpdateDate" />
    <result column="LAST_UPDATED_BY" jdbcType="DECIMAL" property="lastUpdatedBy" />
    <result column="CREATION_DATE" jdbcType="TIMESTAMP" property="creationDate" />
    <result column="CREATED_BY" jdbcType="DECIMAL" property="createdBy" />
    <result column="LAST_UPDATE_LOGIN" jdbcType="DECIMAL" property="lastUpdateLogin" />
    <result column="TRANSFER_TYPE_ID" jdbcType="DECIMAL" property="transferTypeId" />
    <result column="TRANSACTION_QUANTITY" jdbcType="DECIMAL" property="transactionQuantity" />
    <result column="TRANSACTION_DATE" jdbcType="TIMESTAMP" property="transactionDate" />
    <result column="ORGANIZATION_ID" jdbcType="DECIMAL" property="organizationId" />
    <result column="SUBINVENTORY_CODE" jdbcType="VARCHAR" property="subinventoryCode" />
    <result column="LOCATOR_ID" jdbcType="DECIMAL" property="locatorId" />
    <result column="TRANSFER_ORGANIZATION" jdbcType="DECIMAL" property="transferOrganization" />
    <result column="TRANSFER_SUBINVENTORY" jdbcType="VARCHAR" property="transferSubinventory" />
    <result column="TRANSFER_LOCATOR" jdbcType="DECIMAL" property="transferLocator" />
    <result column="INVENTORY_ITEM" jdbcType="VARCHAR" property="inventoryItem" />
    <result column="COMPLETED_FLAG" jdbcType="VARCHAR" property="completedFlag" />
    <result column="ERR_MSG" jdbcType="VARCHAR" property="errMsg" />
    <result column="TRANSACTION_REFERENCE" jdbcType="VARCHAR" property="transactionReference" />
    <result column="OUT_DOC_NUM" jdbcType="VARCHAR" property="outDocNum" />
    <result column="IN_DOC_NUM" jdbcType="VARCHAR" property="inDocNum" />
    <result column="DOC_DETAIL_ID" jdbcType="DECIMAL" property="docDetailId" />
    <result column="SOURCE_HEADER_ID" jdbcType="DECIMAL" property="sourceHeaderId" />
    <result column="SOURCE_LINE_ID" jdbcType="DECIMAL" property="sourceLineId" />
    <result column="TRANSACTION_INTERFACE_ID" jdbcType="DECIMAL" property="transactionInterfaceId" />
    <result column="RENEW_PRICE_FLAG" jdbcType="VARCHAR" property="renewPriceFlag" />
    <result column="VENDOR_NUMBER" jdbcType="VARCHAR" property="vendorNumber" />
    <result column="UNIT_PRICE" jdbcType="DECIMAL" property="unitPrice" />
    <result column="ITEM_REVISION" jdbcType="VARCHAR" property="itemRevision" />
    <result column="SOURCE_REFERENCE" jdbcType="VARCHAR" property="sourceReference" />
    <result column="ATTRIBUTE7" jdbcType="VARCHAR" property="attribute7" />
    <result column="ATTRIBUTE8" jdbcType="VARCHAR" property="attribute8" />
    <result column="ATTRIBUTE9" jdbcType="VARCHAR" property="attribute9" />
    <result column="IMPORT_SOURCE" jdbcType="VARCHAR" property="importSource" />
    <result column="SOURCE_TABLE_NAME" jdbcType="VARCHAR" property="sourceTableName" />
    <result column="SOURCE_TABLE_DETAILID" jdbcType="VARCHAR" property="sourceTableDetailid" />
    <result column="VENDOR_LOT_NUM" jdbcType="VARCHAR" property="vendorLotNum" />
  </resultMap>
  <insert id="insert" parameterType="com.zte.domain.model.erpdt.ZteLmsMtlTransInter">
    insert into ZTE_LMS_MTL_TRANSACTIONS_INTER (LAST_UPDATE_DATE, LAST_UPDATED_BY, CREATION_DATE, 
      CREATED_BY, LAST_UPDATE_LOGIN, TRANSFER_TYPE_ID, 
      TRANSACTION_QUANTITY, TRANSACTION_DATE, ORGANIZATION_ID, 
      SUBINVENTORY_CODE, LOCATOR_ID, TRANSFER_ORGANIZATION, 
      TRANSFER_SUBINVENTORY, TRANSFER_LOCATOR, 
      INVENTORY_ITEM, COMPLETED_FLAG, ERR_MSG, 
      TRANSACTION_REFERENCE, OUT_DOC_NUM, IN_DOC_NUM, 
      DOC_DETAIL_ID, SOURCE_HEADER_ID, SOURCE_LINE_ID, 
      TRANSACTION_INTERFACE_ID, RENEW_PRICE_FLAG, 
      VENDOR_NUMBER, UNIT_PRICE, ITEM_REVISION, 
      SOURCE_REFERENCE, ATTRIBUTE7, ATTRIBUTE8, 
      ATTRIBUTE9, IMPORT_SOURCE, SOURCE_TABLE_NAME, 
      SOURCE_TABLE_DETAILID, VENDOR_LOT_NUM)
    values (#{lastUpdateDate,jdbcType=TIMESTAMP}, #{lastUpdatedBy,jdbcType=DECIMAL}, #{creationDate,jdbcType=TIMESTAMP},
      #{createdBy,jdbcType=DECIMAL}, #{lastUpdateLogin,jdbcType=DECIMAL}, #{transferTypeId,jdbcType=DECIMAL}, 
      #{transactionQuantity,jdbcType=DECIMAL}, #{transactionDate,jdbcType=TIMESTAMP}, #{organizationId,jdbcType=DECIMAL},
      #{subinventoryCode,jdbcType=VARCHAR}, #{locatorId,jdbcType=DECIMAL}, #{transferOrganization,jdbcType=DECIMAL}, 
      #{transferSubinventory,jdbcType=VARCHAR}, #{transferLocator,jdbcType=DECIMAL}, 
      #{inventoryItem,jdbcType=VARCHAR}, #{completedFlag,jdbcType=VARCHAR}, #{errMsg,jdbcType=VARCHAR}, 
      #{transactionReference,jdbcType=VARCHAR}, #{outDocNum,jdbcType=VARCHAR}, #{inDocNum,jdbcType=VARCHAR}, 
      #{docDetailId,jdbcType=DECIMAL}, #{sourceHeaderId,jdbcType=DECIMAL}, #{sourceLineId,jdbcType=DECIMAL}, 
      #{transactionInterfaceId,jdbcType=DECIMAL}, #{renewPriceFlag,jdbcType=VARCHAR}, 
      #{vendorNumber,jdbcType=VARCHAR}, #{unitPrice,jdbcType=DECIMAL}, #{itemRevision,jdbcType=VARCHAR}, 
      #{sourceReference,jdbcType=VARCHAR}, #{attribute7,jdbcType=VARCHAR}, #{attribute8,jdbcType=VARCHAR}, 
      #{attribute9,jdbcType=VARCHAR}, #{importSource,jdbcType=VARCHAR}, #{sourceTableName,jdbcType=VARCHAR}, 
      #{sourceTableDetailid,jdbcType=VARCHAR}, #{vendorLotNum,jdbcType=VARCHAR})
  </insert>

  <insert id="insertZteLmsMtlTransInterBatch" parameterType="java.util.List">
    merge into ZTE.ZTE_LMS_MTL_TRANSACTIONS_INTER a
    using (
    <foreach collection="list" item="item" index="index" separator ="UNION ALL">
      select  #{item.lastUpdateDate,jdbcType=TIMESTAMP} LAST_UPDATE_DATE,
      #{item.lastUpdatedBy,jdbcType=DECIMAL} LAST_UPDATED_BY,
      #{item.creationDate,jdbcType=TIMESTAMP} CREATION_DATE,
      #{item.createdBy,jdbcType=DECIMAL} CREATED_BY,
      #{item.lastUpdateLogin,jdbcType=DECIMAL} LAST_UPDATE_LOGIN,
      #{item.transferTypeId,jdbcType=DECIMAL} TRANSFER_TYPE_ID,
      #{item.transactionQuantity,jdbcType=DECIMAL} TRANSACTION_QUANTITY,
      #{item.transactionDate,jdbcType=TIMESTAMP} TRANSACTION_DATE,
      #{item.organizationId,jdbcType=DECIMAL} ORGANIZATION_ID,
      #{item.subinventoryCode,jdbcType=VARCHAR} SUBINVENTORY_CODE,
      #{item.locatorId,jdbcType=DECIMAL} LOCATOR_ID,
      #{item.transferOrganization,jdbcType=DECIMAL} TRANSFER_ORGANIZATION,
      #{item.transferSubinventory,jdbcType=VARCHAR} TRANSFER_SUBINVENTORY,
      #{item.transferLocator,jdbcType=DECIMAL} TRANSFER_LOCATOR,
      #{item.inventoryItem,jdbcType=VARCHAR} INVENTORY_ITEM,
      #{item.completedFlag,jdbcType=VARCHAR} COMPLETED_FLAG,
      #{item.errMsg,jdbcType=VARCHAR} ERR_MSG,
      #{item.transactionReference,jdbcType=VARCHAR} TRANSACTION_REFERENCE,
      #{item.outDocNum,jdbcType=VARCHAR} OUT_DOC_NUM,
      #{item.inDocNum,jdbcType=VARCHAR} IN_DOC_NUM,
      #{item.docDetailId,jdbcType=DECIMAL} DOC_DETAIL_ID,
      #{item.sourceHeaderId,jdbcType=DECIMAL} SOURCE_HEADER_ID,
      #{item.sourceLineId,jdbcType=DECIMAL} SOURCE_LINE_ID,
      #{item.transactionInterfaceId,jdbcType=DECIMAL} TRANSACTION_INTERFACE_ID,
      #{item.itemRevision,jdbcType=VARCHAR} ITEM_REVISION,
      #{item.renewPriceFlag,jdbcType=VARCHAR} RENEW_PRICE_FLAG,
      #{item.vendorNumber,jdbcType=VARCHAR} VENDOR_NUMBER,
      #{item.unitPrice,jdbcType=DECIMAL} UNIT_PRICE,
      #{item.sourceReference,jdbcType=VARCHAR} SOURCE_REFERENCE,
      #{item.attribute7,jdbcType=VARCHAR} ATTRIBUTE7,
      #{item.attribute8,jdbcType=VARCHAR} ATTRIBUTE8,
      #{item.attribute9,jdbcType=VARCHAR} ATTRIBUTE9,
      #{item.importSource,jdbcType=VARCHAR} IMPORT_SOURCE,
      #{item.sourceTableName,jdbcType=VARCHAR} SOURCE_TABLE_NAME,
      #{item.sourceTableDetailid,jdbcType=VARCHAR} SOURCE_TABLE_DETAILID,
      #{item.vendorLotNum,jdbcType=VARCHAR} VENDOR_LOT_NUM   --切记： 结尾不需要 ，
      from dual
    </foreach>
    ) b
    on (a.TRANSACTION_REFERENCE = b.TRANSACTION_REFERENCE and a.SOURCE_HEADER_ID = b.SOURCE_HEADER_ID)
    --when matched then
    --update set     <!-- 注意，这里不要写（也就是主键）-->
    --a.LAST_UPDATE_DATE = b.LAST_UPDATE_DATE,     --切记： 结尾不需要 ，
    --a.LAST_UPDATED_BY = b.LAST_UPDATED_BY
    when not matched then
    insert
    (LAST_UPDATE_DATE, LAST_UPDATED_BY,
    CREATION_DATE, CREATED_BY, LAST_UPDATE_LOGIN,
    TRANSFER_TYPE_ID, TRANSACTION_QUANTITY, TRANSACTION_DATE,
    ORGANIZATION_ID, SUBINVENTORY_CODE, LOCATOR_ID,
    TRANSFER_ORGANIZATION, TRANSFER_SUBINVENTORY,
    TRANSFER_LOCATOR, INVENTORY_ITEM, COMPLETED_FLAG,
    ERR_MSG, TRANSACTION_REFERENCE, OUT_DOC_NUM,
    IN_DOC_NUM, DOC_DETAIL_ID, SOURCE_HEADER_ID,
    SOURCE_LINE_ID, TRANSACTION_INTERFACE_ID, ITEM_REVISION,
    RENEW_PRICE_FLAG, VENDOR_NUMBER, UNIT_PRICE,
    SOURCE_REFERENCE, ATTRIBUTE7, ATTRIBUTE8,
    ATTRIBUTE9, IMPORT_SOURCE, SOURCE_TABLE_NAME,
    SOURCE_TABLE_DETAILID, VENDOR_LOT_NUM)
    values
    (b.LAST_UPDATE_DATE, b.LAST_UPDATED_BY,
    b.CREATION_DATE, b.CREATED_BY, b.LAST_UPDATE_LOGIN,
    b.TRANSFER_TYPE_ID, b.TRANSACTION_QUANTITY, b.TRANSACTION_DATE,
    b.ORGANIZATION_ID, b.SUBINVENTORY_CODE, b.LOCATOR_ID,
    b.TRANSFER_ORGANIZATION, b.TRANSFER_SUBINVENTORY,
    b.TRANSFER_LOCATOR, b.INVENTORY_ITEM, b.COMPLETED_FLAG,
    b.ERR_MSG, b.TRANSACTION_REFERENCE, b.OUT_DOC_NUM,
    b.IN_DOC_NUM, b.DOC_DETAIL_ID, b.SOURCE_HEADER_ID,
    b.SOURCE_LINE_ID, b.TRANSACTION_INTERFACE_ID, b.ITEM_REVISION,
    b.RENEW_PRICE_FLAG, b.VENDOR_NUMBER, b.UNIT_PRICE,
    b.SOURCE_REFERENCE, b.ATTRIBUTE7, b.ATTRIBUTE8,
    b.ATTRIBUTE9, b.IMPORT_SOURCE, b.SOURCE_TABLE_NAME,
    b.SOURCE_TABLE_DETAILID, b.VENDOR_LOT_NUM)
  </insert>

  <insert id="insertSelective" parameterType="com.zte.domain.model.erpdt.ZteLmsMtlTransInter">
    insert into ZTE_LMS_MTL_TRANSACTIONS_INTER
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="lastUpdateDate != null">
        LAST_UPDATE_DATE,
      </if>
      <if test="lastUpdatedBy != null">
        LAST_UPDATED_BY,
      </if>
      <if test="creationDate != null">
        CREATION_DATE,
      </if>
      <if test="createdBy != null">
        CREATED_BY,
      </if>
      <if test="lastUpdateLogin != null">
        LAST_UPDATE_LOGIN,
      </if>
      <if test="transferTypeId != null">
        TRANSFER_TYPE_ID,
      </if>
      <if test="transactionQuantity != null">
        TRANSACTION_QUANTITY,
      </if>
      <if test="transactionDate != null">
        TRANSACTION_DATE,
      </if>
      <if test="organizationId != null">
        ORGANIZATION_ID,
      </if>
      <if test="subinventoryCode != null">
        SUBINVENTORY_CODE,
      </if>
      <if test="locatorId != null">
        LOCATOR_ID,
      </if>
      <if test="transferOrganization != null">
        TRANSFER_ORGANIZATION,
      </if>
      <if test="transferSubinventory != null">
        TRANSFER_SUBINVENTORY,
      </if>
      <if test="transferLocator != null">
        TRANSFER_LOCATOR,
      </if>
      <if test="inventoryItem != null">
        INVENTORY_ITEM,
      </if>
      <if test="completedFlag != null">
        COMPLETED_FLAG,
      </if>
      <if test="errMsg != null">
        ERR_MSG,
      </if>
      <if test="transactionReference != null">
        TRANSACTION_REFERENCE,
      </if>
      <if test="outDocNum != null">
        OUT_DOC_NUM,
      </if>
      <if test="inDocNum != null">
        IN_DOC_NUM,
      </if>
      <if test="docDetailId != null">
        DOC_DETAIL_ID,
      </if>
      <if test="sourceHeaderId != null">
        SOURCE_HEADER_ID,
      </if>
      <if test="sourceLineId != null">
        SOURCE_LINE_ID,
      </if>
      <if test="transactionInterfaceId != null">
        TRANSACTION_INTERFACE_ID,
      </if>
      <if test="renewPriceFlag != null">
        RENEW_PRICE_FLAG,
      </if>
      <if test="vendorNumber != null">
        VENDOR_NUMBER,
      </if>
      <if test="unitPrice != null">
        UNIT_PRICE,
      </if>
      <if test="itemRevision != null">
        ITEM_REVISION,
      </if>
      <if test="sourceReference != null">
        SOURCE_REFERENCE,
      </if>
      <if test="attribute7 != null">
        ATTRIBUTE7,
      </if>
      <if test="attribute8 != null">
        ATTRIBUTE8,
      </if>
      <if test="attribute9 != null">
        ATTRIBUTE9,
      </if>
      <if test="importSource != null">
        IMPORT_SOURCE,
      </if>
      <if test="sourceTableName != null">
        SOURCE_TABLE_NAME,
      </if>
      <if test="sourceTableDetailid != null">
        SOURCE_TABLE_DETAILID,
      </if>
      <if test="vendorLotNum != null">
        VENDOR_LOT_NUM,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="lastUpdateDate != null">
        #{lastUpdateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdatedBy != null">
        #{lastUpdatedBy,jdbcType=DECIMAL},
      </if>
      <if test="creationDate != null">
        #{creationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=DECIMAL},
      </if>
      <if test="lastUpdateLogin != null">
        #{lastUpdateLogin,jdbcType=DECIMAL},
      </if>
      <if test="transferTypeId != null">
        #{transferTypeId,jdbcType=DECIMAL},
      </if>
      <if test="transactionQuantity != null">
        #{transactionQuantity,jdbcType=DECIMAL},
      </if>
      <if test="transactionDate != null">
        #{transactionDate,jdbcType=TIMESTAMP},
      </if>
      <if test="organizationId != null">
        #{organizationId,jdbcType=DECIMAL},
      </if>
      <if test="subinventoryCode != null">
        #{subinventoryCode,jdbcType=VARCHAR},
      </if>
      <if test="locatorId != null">
        #{locatorId,jdbcType=DECIMAL},
      </if>
      <if test="transferOrganization != null">
        #{transferOrganization,jdbcType=DECIMAL},
      </if>
      <if test="transferSubinventory != null">
        #{transferSubinventory,jdbcType=VARCHAR},
      </if>
      <if test="transferLocator != null">
        #{transferLocator,jdbcType=DECIMAL},
      </if>
      <if test="inventoryItem != null">
        #{inventoryItem,jdbcType=VARCHAR},
      </if>
      <if test="completedFlag != null">
        #{completedFlag,jdbcType=VARCHAR},
      </if>
      <if test="errMsg != null">
        #{errMsg,jdbcType=VARCHAR},
      </if>
      <if test="transactionReference != null">
        #{transactionReference,jdbcType=VARCHAR},
      </if>
      <if test="outDocNum != null">
        #{outDocNum,jdbcType=VARCHAR},
      </if>
      <if test="inDocNum != null">
        #{inDocNum,jdbcType=VARCHAR},
      </if>
      <if test="docDetailId != null">
        #{docDetailId,jdbcType=DECIMAL},
      </if>
      <if test="sourceHeaderId != null">
        #{sourceHeaderId,jdbcType=DECIMAL},
      </if>
      <if test="sourceLineId != null">
        #{sourceLineId,jdbcType=DECIMAL},
      </if>
      <if test="transactionInterfaceId != null">
        #{transactionInterfaceId,jdbcType=DECIMAL},
      </if>
      <if test="renewPriceFlag != null">
        #{renewPriceFlag,jdbcType=VARCHAR},
      </if>
      <if test="vendorNumber != null">
        #{vendorNumber,jdbcType=VARCHAR},
      </if>
      <if test="unitPrice != null">
        #{unitPrice,jdbcType=DECIMAL},
      </if>
      <if test="itemRevision != null">
        #{itemRevision,jdbcType=VARCHAR},
      </if>
      <if test="sourceReference != null">
        #{sourceReference,jdbcType=VARCHAR},
      </if>
      <if test="attribute7 != null">
        #{attribute7,jdbcType=VARCHAR},
      </if>
      <if test="attribute8 != null">
        #{attribute8,jdbcType=VARCHAR},
      </if>
      <if test="attribute9 != null">
        #{attribute9,jdbcType=VARCHAR},
      </if>
      <if test="importSource != null">
        #{importSource,jdbcType=VARCHAR},
      </if>
      <if test="sourceTableName != null">
        #{sourceTableName,jdbcType=VARCHAR},
      </if>
      <if test="sourceTableDetailid != null">
        #{sourceTableDetailid,jdbcType=VARCHAR},
      </if>
      <if test="vendorLotNum != null">
        #{vendorLotNum,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
</mapper>
