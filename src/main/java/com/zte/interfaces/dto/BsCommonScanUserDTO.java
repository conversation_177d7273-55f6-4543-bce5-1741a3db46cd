/**
 * 项目名称 : ${project_name}
 * 创建日期 : ${date}
 * 修改历史 :
 *   1. [${date}] 创建文件 by ${user}
 **/
package com.zte.interfaces.dto;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * // TODO 添加类/接口功能描述
 * 
 * <AUTHOR>
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
public class BsCommonScanUserDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * // TODO remarks
     **/
    private String scanUuid;

    /**
     * // TODO remarks
     **/
    private String lineCode;

    /**
     * // TODO remarks
     **/
    private String processCode;

    /**
     * // TODO remarks
     **/
    private String workStation;

    /**
     * // TODO remarks
     **/
    private String userId;

    /**
     * // TODO remarks
     **/
    private String userName;

    /**
     * // TODO remarks
     **/
    private String macAddress;

    /**
     * // TODO remarks
     **/
    private String createBy;

    /**
     * // TODO remarks
     **/
    private Date createDate;

    /**
     * // TODO remarks
     **/
    private String lastUpdatedBy;

    /**
     * // TODO remarks
     **/
    private Date lastUpdatedDate;

    /**
     * // TODO remarks
     **/
    private String enabledFlag;

    /**
     * // TODO remarks
     **/
    private BigDecimal orgId;

    /**
     * // TODO remarks
     **/
    private BigDecimal factoryId;

    /**
     * // TODO remarks
     **/
    private BigDecimal entityId;

    /**
     * // TODO remarks
     **/
    private String attribute1;

    /**
     * // TODO remarks
     **/
    private String attribute2;

    public void setScanUuid(String scanUuid) {
        this.scanUuid = scanUuid;
    }

    public String getScanUuid() {
        return scanUuid;
    }

    public void setLineCode(String lineCode) {
        this.lineCode = lineCode;
    }

    public String getLineCode() {
        return lineCode;
    }

    public void setProcessCode(String processCode) {
        this.processCode = processCode;
    }

    public String getProcessCode() {
        return processCode;
    }

    public void setWorkStation(String workStation) {
        this.workStation = workStation;
    }

    public String getWorkStation() {
        return workStation;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getUserName() {
        return userName;
    }

    public void setMacAddress(String macAddress) {
        this.macAddress = macAddress;
    }

    public String getMacAddress() {
        return macAddress;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setLastUpdatedBy(String lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy;
    }

    public String getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedDate(Date lastUpdatedDate) {
        this.lastUpdatedDate = lastUpdatedDate;
    }

    public Date getLastUpdatedDate() {
        return lastUpdatedDate;
    }

    public void setEnabledFlag(String enabledFlag) {
        this.enabledFlag = enabledFlag;
    }

    public String getEnabledFlag() {
        return enabledFlag;
    }

    public void setOrgId(BigDecimal orgId) {
        this.orgId = orgId;
    }

    public BigDecimal getOrgId() {
        return orgId;
    }

    public void setFactoryId(BigDecimal factoryId) {
        this.factoryId = factoryId;
    }

    public BigDecimal getFactoryId() {
        return factoryId;
    }

    public void setEntityId(BigDecimal entityId) {
        this.entityId = entityId;
    }

    public BigDecimal getEntityId() {
        return entityId;
    }

    public void setAttribute1(String attribute1) {
        this.attribute1 = attribute1;
    }

    public String getAttribute1() {
        return attribute1;
    }

    public void setAttribute2(String attribute2) {
        this.attribute2 = attribute2;
    }

    public String getAttribute2() {
        return attribute2;
    }

    @ApiModelProperty(value = "排序字段(值errorCode等)", hidden = true)
    private java.lang.String sort;

    @ApiModelProperty(value = "排序方式(默认升序,设为desc时降序)", hidden = true)
    private java.lang.String order;

    @ApiModelProperty(value = "请求的页码", hidden = true)
    private java.lang.Long page;

    @ApiModelProperty(value = "每页条数", hidden = true)
    private java.lang.Long rows;

    public java.lang.String getSort() {

        return sort;
    }

    public void setSort(java.lang.String sort) {

        this.sort = sort;
    }

    public java.lang.String getOrder() {

        return order;
    }

    public void setOrder(java.lang.String order) {

        this.order = order;
    }

    public java.lang.Long getPage() {

        return page;
    }

    public void setPage(java.lang.Long page) {

        this.page = page;
    }

    public java.lang.Long getRows() {

        return rows;
    }

    public void setRows(java.lang.Long rows) {

        this.rows = rows;
    }

}
