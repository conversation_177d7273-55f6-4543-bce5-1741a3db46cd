/* Started by AICoder, pid:zc34ah13ed0d0e214c8209ff13ae1040d681af70 */
package com.zte.autoTest.unitTest;
import com.zte.application.datawb.CurbNameTagsPrintService;
import com.zte.application.datawb.impl.AutoBatchMarkImpl;
import com.zte.application.datawb.impl.CurbNameTagsPrintServiceImpl;
import com.zte.application.datawb.impl.OneSizeToEndPalletServiceImpl;
import com.zte.application.datawb.impl.OneSizeToEndServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.idatashare.client.clientfactory.DataServiceClientV1;
import com.zte.domain.model.datawb.*;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.springbootframe.util.httpclient.HttpClientUtil;
import com.zte.springbootframe.util.json.JacksonJsonConverUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.beans.factory.annotation.Autowired;

import java.text.Normalizer;
import java.util.*;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.powermock.api.mockito.PowerMockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({DataServiceClientV1.class, CenterfactoryRemoteService.class, MESHttpHelper.class, HttpClientUtil.class,
        JacksonJsonConverUtil.class, ServiceDataBuilderUtil.class, CommonUtils.class, Normalizer.class, GbomCsgInfosDTO.class})
@PowerMockIgnore({"javax.net.ssl.*", "javax.security.*"})
public class OneSizeToEndPalletServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    private OneSizeToEndPalletServiceImpl oneSizeToEndPalletServiceImpl;
    @Mock
    private OneSizeToEndSFCRepository oneSizeToEndSFCRepository;
    @Mock
    private OneSizeToEndRepository oneSizeToEndRepository;
    @Mock
    private DeliverEntityCycleRepository deliverEntityCycleRepository;
    @Mock
    private MesEntryKafkaSfcRepository mesEntryKafkaSfcRepository;
    @Mock
    private CfgCodeRuleItemRepository cfgCodeRuleItemRepository;

    @Mock
    private AutoBatchMarkImpl autoBatchMarkImpl;
    @Mock
    private CurbNameTagsPrintServiceImpl curbNameTagsPrintService;
    @Before
    public void init() {
        PowerMockito.mockStatic(MESHttpHelper.class, HttpClientUtil.class, GbomCsgInfosDTO.class,
                JacksonJsonConverUtil.class, ServiceDataBuilderUtil.class, Normalizer.class);
    }
    @Test
    public void Test() throws Exception {
        ServiceData data = new ServiceData();
        OneSizeToEndPalletInDTO dto = new OneSizeToEndPalletInDTO();
        try {
            oneSizeToEndPalletServiceImpl.checkParamIn(dto);
            assertEquals(true, true);
        } catch (Exception e) {
            assertEquals(MessageId.INPUT_ORGANIZATIONID_IS_NULL_PLEASE_CHECK_ORGANIZATIONID, e.getMessage());
        }
        try {
            dto.setOrgID("635");
            oneSizeToEndPalletServiceImpl.checkParamIn(dto);
            assertEquals(true, true);
        } catch (Exception e) {
            assertEquals(MessageId.BILLNO_AND_PALLETNO_INPUT_ONLY_ONE, e.getMessage());
        }
        try {
            dto.setPalletNo("1111");
            dto.setBillNo("2222");
            oneSizeToEndPalletServiceImpl.checkParamIn(dto);
            assertEquals(true, true);
        } catch (Exception e) {
            assertEquals(MessageId.BILLNO_AND_PALLETNO_INPUT_ONLY_ONE, e.getMessage());
        }
        try {
            dto = new OneSizeToEndPalletInDTO();
            dto.setOrgID("635");
            dto.setBillNo("222");
            when(oneSizeToEndRepository.getPalletAllBillByBillNo(anyString())).thenReturn(null);
            oneSizeToEndPalletServiceImpl.checkParamIn(dto);
            assertEquals(true, true);
        } catch (Exception e) {
            assertEquals(MessageId.BILLNO_NO_PALLET_NO_GET_BILL_DETAIL_INFO, e.getMessage());
        }
        try {
            dto = new OneSizeToEndPalletInDTO();
            dto.setOrgID("635");
            dto.setBillNo("222");
            List<OneSizeToEndPalletInDTO> ddd = new ArrayList<>();
            OneSizeToEndPalletInDTO d = new OneSizeToEndPalletInDTO();
            d.setBillNo("111");
            d.setPalletNo("222");
            ddd.add(d);
            d = new OneSizeToEndPalletInDTO();
            d.setBillNo("112");
            d.setPalletNo("222");
            ddd.add(d);
            d = new OneSizeToEndPalletInDTO();
            d.setBillNo("113");
            d.setPalletNo("222");
            ddd.add(d);
            List<OneSizeToEndPalletScanFlagDTO> scanFlags = new ArrayList<>();
            OneSizeToEndPalletScanFlagDTO v = new OneSizeToEndPalletScanFlagDTO();
            v.setBillNo("111");
            v.setScanFlag("2");
            scanFlags.add(v);
            v = new OneSizeToEndPalletScanFlagDTO();
            v.setBillNo("112");
            v.setScanFlag("5");
            scanFlags.add(v);
            when(oneSizeToEndRepository.getPalletAllBillByBillNo(anyString())).thenReturn(ddd);
            when(oneSizeToEndSFCRepository.getScanFlagList(anyList())).thenReturn(scanFlags);
            oneSizeToEndPalletServiceImpl.checkParamIn(dto);
            assertEquals(ddd.size(), 3);
        } catch (Exception e) {
            assertEquals(MessageId.PALLET_EXISTS_NO_SCAN_END_BILL, e.getMessage());
        }
        try {
            dto = new OneSizeToEndPalletInDTO();
            dto.setOrgID("635");
            dto.setPalletNo("222");
            List<String> billNos = new ArrayList<>();
            when(oneSizeToEndRepository.getPalletAllBillByPalletNo(anyString())).thenReturn(billNos);
            when(oneSizeToEndSFCRepository.getScanFlagList(anyList())).thenReturn(null);
            oneSizeToEndPalletServiceImpl.checkParamIn(dto);
            assertEquals(billNos.size(), 0);
        } catch (Exception e) {
            assertEquals(MessageId.BILLNO_NO_PALLET_NO_GET_BILL_DETAIL_INFO, e.getMessage());
        }
        try {
            dto = new OneSizeToEndPalletInDTO();
            dto.setOrgID("635");
            dto.setPalletNo("222");
            when(oneSizeToEndRepository.getPalletManageInfo(anyString())).thenReturn(null);
            oneSizeToEndPalletServiceImpl.handlePalletLabelPrint(dto);
            assertEquals(true, true);
        } catch (Exception e) {
            assertEquals(MessageId.NO_DATA_FOUND, e.getMessage());
        }
        List<BoqBomDTO> boqBomList = new ArrayList<>();
        BoqBomDTO item =new BoqBomDTO();
        item.setMaterialName("aaaaaaa");
        boqBomList.add(item);
        try {
            dto = new OneSizeToEndPalletInDTO();
            dto.setOrgID("635");
            dto.setPalletNo("222");
            OneSizeToEndPalletInfoDTO d = new OneSizeToEndPalletInfoDTO();
            when(oneSizeToEndRepository.getPalletManageInfo(anyString())).thenReturn(d);
            when(oneSizeToEndRepository.getPalletPlusInfo(d.getEntityName())).thenReturn(null);
            oneSizeToEndPalletServiceImpl.handlePalletLabelPrint(dto);
            assertEquals(true, true);
        } catch (Exception ex) {
        }
        try {
            dto = new OneSizeToEndPalletInDTO();
            dto.setOrgID("635");
            dto.setPalletNo("222");
            OneSizeToEndPalletInfoDTO d = new OneSizeToEndPalletInfoDTO();
            d.setEntityName("33");
            d.setCountryId("2");
            OneSizeToEndPalletOutDTO palletOut  = new OneSizeToEndPalletOutDTO();
            palletOut.setGrossWeight("222");
            when(oneSizeToEndRepository.getPalletManageInfo(anyString())).thenReturn(d);
            when(oneSizeToEndRepository.getPalletPlusInfo(d.getEntityName())).thenReturn(palletOut);
            when(oneSizeToEndRepository.getPalletGrossWeight(Mockito.any())).thenReturn("0");
            when(oneSizeToEndRepository.getTempLanuagePlus(anyString())).thenReturn("english");
            when(mesEntryKafkaSfcRepository.boqBomSelectSfc(Mockito.any())).thenReturn(boqBomList);
            oneSizeToEndPalletServiceImpl.handlePalletLabelPrint(dto);
            assertEquals(true, true);
        } catch (Exception e) {
        }  try {
            dto = new OneSizeToEndPalletInDTO();
            dto.setOrgID("635");
            dto.setPalletNo("222");
            OneSizeToEndPalletInfoDTO d = new OneSizeToEndPalletInfoDTO();
            d.setEntityName("33");
            d.setCountryId("1");
            OneSizeToEndPalletOutDTO vvvv = new OneSizeToEndPalletOutDTO();
            vvvv.setCustomerName("22");
            when(oneSizeToEndRepository.getPalletManageInfo(anyString())).thenReturn(d);
            when(oneSizeToEndRepository.getPalletPlusInfo(d.getEntityName())).thenReturn(vvvv);
            when(oneSizeToEndRepository.getPalletGrossWeight(Mockito.any())).thenReturn("0");
            when(oneSizeToEndRepository.getTempLanuagePlus(anyString())).thenReturn("");
            when(mesEntryKafkaSfcRepository.boqBomSelectSfc(Mockito.any())).thenReturn(boqBomList);
            oneSizeToEndPalletServiceImpl.handlePalletLabelPrint(dto);
            assertEquals(true, true);
        } catch (Exception e) {
        }  try {
            dto = new OneSizeToEndPalletInDTO();
            dto.setOrgID("635");
            dto.setPalletNo("222");
            dto.setBillNo("111");
            OneSizeToEndPalletInfoDTO d = new OneSizeToEndPalletInfoDTO();
            d.setEntityName("33");
            d.setCountryId("1");
            OneSizeToEndPalletOutDTO vt = new OneSizeToEndPalletOutDTO();
            vt.setCustomerName("22");
            when(oneSizeToEndRepository.getPalletManageInfo(anyString())).thenReturn(d);
            when(oneSizeToEndRepository.getPalletPlusInfo(d.getEntityName())).thenReturn(vt);
            when(oneSizeToEndRepository.getPalletGrossWeight(Mockito.any())).thenReturn("0");
            when(oneSizeToEndRepository.getTempLanuagePlus(anyString())).thenReturn("中文");
            when(deliverEntityCycleRepository.getMfgSiteType(anyString())).thenReturn("mfgSiteType");

            when(mesEntryKafkaSfcRepository.boqBomSelectSfc(Mockito.any())).thenReturn(boqBomList);
            oneSizeToEndPalletServiceImpl.handlePalletLabelPrint(dto);
            assertEquals(true, true);
        } catch (Exception e) {
        }

        List<OneSizeToEndPalletInDTO> ddd = new ArrayList<>();
        OneSizeToEndPalletInDTO d = new OneSizeToEndPalletInDTO();
        d.setBillNo("111");
        d.setPalletNo("222");
        ddd.add(d);
        d = new OneSizeToEndPalletInDTO();
        d.setBillNo("112");
        d.setPalletNo("222");
        ddd.add(d);
        d = new OneSizeToEndPalletInDTO();
        d.setBillNo("113");
        d.setPalletNo("222");
        ddd.add(d);
        List<OneSizeToEndPalletScanFlagDTO> scanFlags = new ArrayList<>();
        OneSizeToEndPalletScanFlagDTO v = new OneSizeToEndPalletScanFlagDTO();
        v.setBillNo("111");
        v.setScanFlag("2");
        scanFlags.add(v);
        v = new OneSizeToEndPalletScanFlagDTO();
        v.setBillNo("112");
        v.setScanFlag("2");
        scanFlags.add(v);
        v = new OneSizeToEndPalletScanFlagDTO();
        v.setBillNo("113");
        v.setScanFlag("2");
        scanFlags.add(v);
        when(oneSizeToEndRepository.getPalletAllBillByBillNo(anyString())).thenReturn(ddd);
        when(oneSizeToEndSFCRepository.getScanFlagList(anyList())).thenReturn(scanFlags);
        dto = new OneSizeToEndPalletInDTO();
        dto.setOrgID("635");
        dto.setBillNo("112");
        OneSizeToEndPalletInfoDTO od = new OneSizeToEndPalletInfoDTO();
        od.setCountryId("1");
        od.setEntityName("33");
        od.setContractNumber("S3KM2021070601PTNFP93777");
        od.setDeliverSetNum("JF002S20230223006");
        OneSizeToEndPalletOutDTO vt = new OneSizeToEndPalletOutDTO();
        vt.setCustomerName("22");
        when(oneSizeToEndRepository.getPalletManageInfo(anyString())).thenReturn(od);
        when(oneSizeToEndRepository.getPalletPlusInfo(od.getEntityName())).thenReturn(vt);
        when(oneSizeToEndRepository.getTempLanuagePlus(anyString())).thenReturn("1");
        String mfgSiteType = "aaa";
        when(deliverEntityCycleRepository.getMfgSiteType(anyString())).thenReturn(mfgSiteType);

        boqBomList = new ArrayList<>();
        item =new BoqBomDTO();
        item.setMaterialName("aaaaaaa");
        item.setBoqLevel("111");
        boqBomList.add(item);
        when(mesEntryKafkaSfcRepository.boqBomSelectSfc(Mockito.any())).thenReturn(boqBomList);
        oneSizeToEndPalletServiceImpl.handlePalletLabelPrint(dto);
        assertEquals(true, true);

        boqBomList = new ArrayList<>();
        item =new BoqBomDTO();
        item.setMaterialName("aaaaaaa");
        item.setBoqLevel("111.11");
        boqBomList.add(item);
        when(mesEntryKafkaSfcRepository.boqBomSelectSfc(Mockito.any())).thenReturn(boqBomList);

        String url ="http://icc.zte.com.cn/zte-sac-icc-logisticscoordination/external/delivery/note/info/mes";
        PowerMockito.when( cfgCodeRuleItemRepository.getDicDescription(any())).thenReturn(url);

        // 使用PowerMockito来模拟HttpClientUtil.httpGet方法的返回值
        String result1 = "{\n" +
                "    \"code\": {\n" +
                "        \"code\": \"0000\",\n" +
                "        \"msgId\": \"RetCode.Success\",\n" +
                "        \"msg\": \"Success\"\n" +
                "    },\n" +
                "    \"bo\": [\n" +
                "        {\n" +
                "            \"pkInfoId\": \"1733347142826033155\",\n" +
                "            \"contractId\": \"6711231\",\n" +
                "            \"contractNo\": \"81SBOM20230703077\",\n" +
                "            \"deliverySetId\": \"3270996\",\n" +
                "            \"deliverySet\": \"JF002S20230223006\",\n" +
                "            \"productInformation\": \"SPN\",\n" +
                "            \"supplyNotificationNo\": \"APO304610230200320\",\n" +
                "            \"supplyIssuanceDate\": \"2023-02-14 00:00:00\",\n" +
                "            \"requiringTel\": \"古议成：18887609736\",\n" +
                "            \"requiringUnit\": \"文山移动\",\n" +
                "            \"salesManagerInfor\": \"伏艳刚13648847412\",\n" +
                "            \"flagBatch\": \"是\",\n" +
                "            \"projectNo\": \"C23304626CA1001\",\n" +
                "            \"projectName\": \"中国移动云南公司2023年文山分公司富宁县田蓬口岸5G专网项目_文山分公司\",\n" +
                "            \"lockStatus\": \"Y\",\n" +
                "            \"enableFlag\": 1,\n" +
                "            \"createDate\": \"2023-03-08 14:01:20\",\n" +
                "            \"createBy\": \"nullundefined\",\n" +
                "            \"lastUpdateDate\": \"2023-03-08 14:17:18\",\n" +
                "            \"lastUpdateBy\": \"1009436910094369\",\n" +
                "            \"operatorType\": \"AB004\",\n" +
                "            \"representOfficeCode\": \"ORG2227654\",\n" +
                "            \"poNumber\": \"11111\",\n" +
                "            \"customerErpPoNumber\": null\n" +
                "        }\n" +
                "    ],\n" +
                "    \"other\": null,\n" +
                "    \"responseRule\": \"msa\"\n" +
                "}";
        PowerMockito.when(HttpClientUtil.httpGet(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(result1);

        dto = new OneSizeToEndPalletInDTO();
        dto.setOrgID("635");
        dto.setBillNo("112");
        oneSizeToEndPalletServiceImpl.handlePalletLabelPrint(dto);
        assertEquals(true, true);
    }

    @Test
    public void testBoqBomListEmpty() {
        // 分支1：boqBomList为空
        String result = oneSizeToEndPalletServiceImpl.getCbomName(Collections.emptyList(), "contract1");
        assertEquals(Constant.STRING_EMPTY, result);
    }

    @Test
    public void testNoLastBoqLevel() {
        // 分支2：boqBomList无符合条件的BoqLevel
        BoqBomDTO dto = Mockito.mock(BoqBomDTO.class);
        Mockito.when(dto.getBoqLevel()).thenReturn("1.2"); // 只有一个点
        List<BoqBomDTO> list = Arrays.asList(dto);
        String result = oneSizeToEndPalletServiceImpl.getCbomName(list, "contract1");
        assertEquals(Constant.STRING_EMPTY, result);
    }

    @Test
    public void testListCPQDIsEmpty() {
        // 分支3：lastBoqLevel有数据，但listCPQD为空
        BoqBomDTO dto = Mockito.mock(BoqBomDTO.class);
        Mockito.when(dto.getBoqLevel()).thenReturn("1.2.3.4"); // 3个点
        Mockito.when(dto.getConfigDetailId()).thenReturn("id1");
        List<BoqBomDTO> list = Arrays.asList(dto);

        Mockito.when(autoBatchMarkImpl.getCPQD(anyString(), anyList())).thenReturn(Collections.emptyList());

        String result = oneSizeToEndPalletServiceImpl.getCbomName(list, "contract1");
        assertEquals(Constant.STRING_EMPTY, result);
    }

    @Test
    public void testCbomCountNotOne() {
        // 分支4：listCPQD有数据，但cbomCount.size()!=1
        BoqBomDTO dto = Mockito.mock(BoqBomDTO.class);
        Mockito.when(dto.getBoqLevel()).thenReturn("1.2.3.4");
        Mockito.when(dto.getConfigDetailId()).thenReturn("id1");
        List<BoqBomDTO> list = Arrays.asList(dto);

        CPQDDTO cpqd1 = Mockito.mock(CPQDDTO.class);
        CPQDDTO cpqd2 = Mockito.mock(CPQDDTO.class);
        Mockito.when(cpqd1.getCbomCode()).thenReturn("code1");
        Mockito.when(cpqd2.getCbomCode()).thenReturn("code2");
        Mockito.when(autoBatchMarkImpl.getCPQD(anyString(), anyList())).thenReturn(Arrays.asList(cpqd1, cpqd2));

        String result = oneSizeToEndPalletServiceImpl.getCbomName(list, "contract1");
        assertEquals(Constant.STRING_EMPTY, result);
    }

    @Test
    public void testCbomCountIsOne() {
        // 分支5：listCPQD有数据，cbomCount.size()==1，返回cbomNameCn
        BoqBomDTO dto = Mockito.mock(BoqBomDTO.class);
        Mockito.when(dto.getBoqLevel()).thenReturn("1.2.3.4");
        Mockito.when(dto.getConfigDetailId()).thenReturn("id1");
        List<BoqBomDTO> list = Arrays.asList(dto);

        CPQDDTO cpqd = Mockito.mock(CPQDDTO.class);
        Mockito.when(cpqd.getCbomCode()).thenReturn("code1");
        Mockito.when(cpqd.getCbomNameCn()).thenReturn("CBOM中文名");
        Mockito.when(autoBatchMarkImpl.getCPQD(anyString(), anyList())).thenReturn(Arrays.asList(cpqd));

        String result = oneSizeToEndPalletServiceImpl.getCbomName(list, "contract1");
        assertEquals("CBOM中文名", result);
    }
}
/* Ended by AICoder, pid:zc34ah13ed0d0e214c8209ff13ae1040d681af70 */