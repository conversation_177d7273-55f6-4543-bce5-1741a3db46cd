<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.SmtMachineMaterialMoutingRepository">
    <resultMap id="BaseResultMap" type="com.zte.domain.model.SmtMachineMaterialMouting">
        <id column="MACHINE_MATERIAL_MOUTING_ID" jdbcType="VARCHAR" property="machineMaterialMoutingId"/>
        <result column="WORK_ORDER" jdbcType="VARCHAR" property="workOrder"/>
        <result column="ITEM_CODE" jdbcType="VARCHAR" property="itemCode"/>
        <result column="LINE_CODE" jdbcType="VARCHAR" property="lineCode"/>
        <result column="ITEM_NAME" jdbcType="VARCHAR" property="itemName"/>
        <result column="MACHINE_NO" jdbcType="VARCHAR" property="machineNo"/>
        <result column="LOCATION_NO" jdbcType="VARCHAR" property="locationNo"/>
        <result column="TRACK_NO" jdbcType="VARCHAR" property="trackNo"/>
        <result column="FEEDER_NO" jdbcType="VARCHAR" property="feederNo"/>
        <result column="OBJECT_ID" jdbcType="VARCHAR" property="objectId"/>
        <result column="QTY" jdbcType="DECIMAL" property="qty"/>
        <result column="FORWARD" jdbcType="VARCHAR" property="forward"/>
        <result column="NEXT_REEL_ROWID" jdbcType="VARCHAR" property="nextReelRowid"/>
        <result column="REMARK" jdbcType="VARCHAR" property="remark"/>
        <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="CREATE_USER" jdbcType="VARCHAR" property="createUser"/>
        <result column="LAST_UPDATED_DATE" jdbcType="TIMESTAMP" property="lastUpdatedDate"/>
        <result column="LAST_UPDATED_BY" jdbcType="VARCHAR" property="lastUpdatedBy"/>
        <result column="ENABLED_FLAG" jdbcType="VARCHAR" property="enabledFlag"/>
        <result column="ORG_ID" jdbcType="DECIMAL" property="orgId"/>
        <result column="ENTITY_ID" jdbcType="DECIMAL" property="entityId"/>
        <result column="FACTORY_ID" jdbcType="DECIMAL" property="factoryId"/>
        <result column="RAWQTY" jdbcType="DECIMAL" property="ramQty"/>
        <result column="MODULE_NO" jdbcType="VARCHAR" property="moduleNo"/>
        <result column="IS_LEAD" jdbcType="VARCHAR" property="isLead"/>
        <result column="LPN" jdbcType="VARCHAR" property="lpn"/>
        <result column="COMBINATION" jdbcType="VARCHAR" property="combination"/>
        <result column="AVL" jdbcType="VARCHAR" property="avl"/>
        <result column="POLAR_INFO" jdbcType="VARCHAR" property="polarInfo"/>
        <result column="SOURCE_BATCH_CODE" jdbcType="VARCHAR" property="sourceBatchCode"/>
        <result column="CFG_HEADER_ID" jdbcType="VARCHAR" property="cfgHeaderId"/>
        <result column="WET_LEVEL" jdbcType="VARCHAR" property="wetLevel"/>
        <result column="ITEM_TYPE" jdbcType="VARCHAR" property="itemType"/>
        <result column="RAW_QTY" jdbcType="DECIMAL" property="rawQty"/>
        <result column="PRODUCT_TASK" jdbcType="DECIMAL" property="productTask"/>
        <result column="switched_quickly_flag" jdbcType="VARCHAR" property="switchedQuicklyFlag"/>
    </resultMap>

    <resultMap id="ResultMap1" type="com.zte.interfaces.dto.SmtMachineMaterialMoutingDTO">
        <result column="WORK_ORDER" jdbcType="VARCHAR" property="workOrder"/>
        <result column="ENABLED_FLAG" jdbcType="VARCHAR" property="enabledFlag"/>
        <result column="BOM_NO" jdbcType="VARCHAR" property="bomNo"/>
        <result column="CRAFT_SECTION" jdbcType="VARCHAR" property="craftSection"/>
    </resultMap>

    <sql id="Base_Column_List">
        MACHINE_MATERIAL_MOUTING_ID, WORK_ORDER, ITEM_CODE, LINE_CODE, ITEM_NAME, MACHINE_NO,
        LOCATION_NO, TRACK_NO, FEEDER_NO, OBJECT_ID, QTY, FORWARD, NEXT_REEL_ROWID, REMARK,
        CREATE_DATE, CREATE_USER, LAST_UPDATED_DATE, LAST_UPDATED_BY, ENABLED_FLAG, ORG_ID,
        ENTITY_ID,FACTORY_ID,RAWQTY,MODULE_NO,IS_LEAD,LPN,COMBINATION,AVL,POLAR_INFO,SOURCE_BATCH_CODE,CFG_HEADER_ID,
        WET_LEVEL,ITEM_TYPE
    </sql>


    <select id="selectSmtMachineMaterialMoutingById" parameterType="com.zte.domain.model.SmtMachineMaterialMouting"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from SMT_MACHINE_MATERIAL_MOUTING
        where ENABLED_FLAG = 'Y'
        AND MACHINE_MATERIAL_MOUTING_ID = #{machineMaterialMoutingId,jdbcType=VARCHAR}
    </select>

    <select id="selectByWorkOrderNO" parameterType="com.zte.domain.model.SmtMachineMaterialMouting" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from SMT_MACHINE_MATERIAL_MOUTING
        WHERE WORK_ORDER = #{workOrder,jdbcType=VARCHAR}
    </select>

    <select id="selectSmtMachineMaterialMoutingSelective" parameterType="com.zte.domain.model.SmtMachineMaterialMouting"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from SMT_MACHINE_MATERIAL_MOUTING
        WHERE ENABLED_FLAG = 'Y'
        <if test="workOrder != null and workOrder != ''">
            AND WORK_ORDER = #{workOrder,jdbcType=VARCHAR}
        </if>

        <if test="itemCode != null and itemCode != ''">
            AND ITEM_CODE = #{itemCode,jdbcType=VARCHAR}
        </if>

        <if test="lineCode != null and lineCode != ''">
            AND LINE_CODE = #{lineCode,jdbcType=VARCHAR}
        </if>

        <if test="itemName != null and itemName != ''">
            AND ITEM_NAME = #{itemName,jdbcType=VARCHAR}
        </if>

        <if test="machineNo != null and machineNo != ''">
            AND MACHINE_NO = #{machineNo,jdbcType=VARCHAR}
        </if>

        <if test="locationNo != null and locationNo != ''">
            AND LOCATION_NO = #{locationNo,jdbcType=VARCHAR}
        </if>

        <if test="trackNo != null and trackNo != ''">
            AND TRACK_NO = #{trackNo,jdbcType=VARCHAR}
        </if>

        <if test="feederNo != null and feederNo != ''">
            AND FEEDER_NO = #{feederNo,jdbcType=VARCHAR}
        </if>

        <if test="objectId != null and objectId != ''">
            AND OBJECT_ID = #{objectId,jdbcType=VARCHAR}
        </if>


        <if test="forward != null and forward != ''">
            AND FORWARD = #{forward,jdbcType=VARCHAR}
        </if>

        <if test="nextReelRowid != null and nextReelRowid != ''">
            AND NEXT_REEL_ROWID = #{nextReelRowid,jdbcType=VARCHAR}
        </if>

        <if test="remark != null and remark != ''">
            AND REMARK = #{remark,jdbcType=VARCHAR}
        </if>

        <if test="createDate != null">
            AND CREATE_DATE = #{createDate,jdbcType=TIMESTAMP}
        </if>

        <if test="createUser != null">
            AND CREATE_USER = #{createUser,jdbcType=VARCHAR}
        </if>

        <if test="lastUpdatedDate != null">
            AND LAST_UPDATED_DATE = #{lastUpdatedDate,jdbcType=TIMESTAMP}
        </if>

        <if test="lastUpdatedBy != null">
            AND LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR}
        </if>

        <if test="ramQty != null">
            AND RAWQTY = #{ramQty,jdbcType=DECIMAL}
        </if>

        <if test="moduleNo != null and moduleNo != ''">
            AND MODULE_NO = #{moduleNo,jdbcType=VARCHAR}
        </if>

        <if test="isLead != null and isLead != ''">
            AND IS_LEAD = #{isLead,jdbcType=VARCHAR}
        </if>

        <if test="lpn != null and lpn != ''">
            AND LPN = #{lpn,jdbcType=VARCHAR}
        </if>
        <if test="combination != null and combination != ''">
            AND COMBINATION = #{combination,jdbcType=VARCHAR}
        </if>

        <if test="avl != null and avl != ''">
            AND AVL = #{avl,jdbcType=VARCHAR}
        </if>

        <if test="polarInfo != null and polarInfo != ''">
            AND POLAR_INFO = #{polarInfo,jdbcType=VARCHAR}
        </if>

        <if test="sourceBatchCode != null and sourceBatchCode != ''">
            AND SOURCE_BATCH_CODE = #{sourceBatchCode,jdbcType=VARCHAR}
        </if>

        <if test="cfgHeaderId != null and cfgHeaderId != ''">
            AND CFG_HEADER_ID = #{cfgHeaderId,jdbcType=VARCHAR}
        </if>
        <if test="inObjectId != null and inObjectId != ''">
            AND OBJECT_ID in (${inObjectId})
        </if>
        <if test="orgId != null">
            AND ORG_ID = #{orgId,jdbcType=DECIMAL}
        </if>

        <if test="orgId == null and (workOrder == null or workOrder == '') and ( itemCode == null or itemCode == '') and (lineCode == null or lineCode == '') and ( itemName == null or itemName == '') and
             (machineNo == null or machineNo == '') and ( locationNo == null or locationNo == '') and (trackNo == null or trackNo == '') and ( feederNo == null or feederNo == '') and
             (objectId == null or objectId == '') and ( forward == null or forward == '') and (nextReelRowid == null or nextReelRowid == '') and ( createDate == null ) and
             (createUser == null or createUser == '') and ( lastUpdatedDate == null ) and (lastUpdatedBy == null or lastUpdatedBy == '') and ( moduleNo == null or moduleNo == '') and
             (isLead == null or isLead == '') and ( lpn == null or lpn == '') and (combination == null or combination == '') and ( avl == null or avl == '') and
             (polarInfo == null or polarInfo == '') and ( sourceBatchCode == null or sourceBatchCode == '') and (cfgHeaderId == null or cfgHeaderId == '') and ( inObjectId == null or inObjectId == '') "> and 1=2 </if>
    </select>

    <select id="queryListWithWorkorderOnline" resultType="com.zte.interfaces.dto.SmtMachineMaterialMoutingDTO">
        select distinct(smmm.work_order) workOrder,smmm.enabled_flag enabledFlag,wo.item_no bomNo,wo.craft_section craftSection
        from smt_machine_material_mouting smmm left join workorder_online wo on smmm.work_order = wo.work_order
        <where>
            <if test="workOrder != null and workOrder != ''">
                and smmm.work_order = #{workOrder,jdbcType=VARCHAR}
            </if>
            <if test="lineCode != null and lineCode != ''">
                and smmm.line_code = #{lineCode,jdbcType=VARCHAR}
            </if>
            <if test="moduleNo != null and moduleNo != ''">
                and smmm.module_no = #{moduleNo,jdbcType=VARCHAR}
            </if>
            <if test="machineNo != null and machineNo != ''">
                AND smmm.machine_no = #{machineNo,jdbcType=VARCHAR}
            </if>
            <if test="(moduleNo == null or moduleNo == '') and ( workOrder == null or workOrder == '') and (lineCode == null or lineCode == '') and ( machineNo == null or machineNo == '')  "> and 1=2 </if>
        </where>

    </select>

    <select id="queryListWithSmtBomHeader" resultType="com.zte.interfaces.dto.SmtMachineMaterialMoutingDTO" resultMap="ResultMap1">
        select distinct a.work_order,a.enabled_flag,b.product_code bom_no,b.craft_section
        from smt_machine_material_mouting a
        left join b_smt_bom_header b
        on a.line_code = b.line_code and b.attr1 = SUBSTR(a.work_order,0,7) and a.work_order like '%' || b.craft_section || '%'
        where a.work_order is not null
        and length(a.work_order) > 7
        <if test="lineCode != null and lineCode != ''">
            and a.line_code = #{lineCode,jdbcType=VARCHAR}
        </if>
        <if test="machineNo != null and machineNo != ''">
            AND a.machine_no = #{machineNo,jdbcType=VARCHAR}
        </if>
    </select>



    <delete id="deleteSmtMachineMaterialMoutingById" parameterType="com.zte.domain.model.SmtMachineMaterialMouting">
        delete from SMT_MACHINE_MATERIAL_MOUTING
        where 1=1
        <if test="enabledFlag == null or enabledFlag == ''">
            AND ENABLED_FLAG = 'Y'
        </if>
        <if test="machineMaterialMoutingId != null and machineMaterialMoutingId != ''">
            and MACHINE_MATERIAL_MOUTING_ID = #{machineMaterialMoutingId,jdbcType=VARCHAR}
        </if>
        <if test="workOrder != null and workOrder != ''">
            and WORK_ORDER = #{workOrder,jdbcType=VARCHAR}
        </if>
        <if test="lineCode != null and lineCode != ''">
            and LINE_CODE = #{lineCode,jdbcType=VARCHAR}
        </if>
        <if test="prodplanId!=null and prodplanId!=''">and WORK_ORDER LIKE '${prodplanId}%'</if>
        <if test="moduleNo != null and moduleNo != ''">
            AND MODULE_NO = #{moduleNo,jdbcType=VARCHAR}
        </if>
        <if test="machineNo != null and machineNo != ''">
            AND MACHINE_NO = #{machineNo,jdbcType=VARCHAR}
        </if>
        <if test="locationNo != null and locationNo != ''">
            AND LOCATION_NO = #{locationNo,jdbcType=VARCHAR}
        </if>
        <if test="lpn != null and lpn != ''">
            AND LPN = #{lpn,jdbcType=VARCHAR}
        </if>
        <if test="(machineMaterialMoutingId == null or machineMaterialMoutingId == '') and (workOrder == null or workOrder == '')
   and (lineCode == null or lineCode == '') and (prodplanId ==null or prodplanId=='') and (moduleNo == null or moduleNo == '')
and (machineNo == null or machineNo == '') and (locationNo == null or locationNo == '') and (lpn == null or lpn == '') ">
            and 1=2
        </if>

    </delete>

    <delete id="deleteMountingByLpn" parameterType="com.zte.domain.model.SmtMachineMaterialMouting">
        delete from SMT_MACHINE_MATERIAL_MOUTING
        where ENABLED_FLAG = 'Y'
        <if test="lpn != null and lpn != ''">
            AND LPN = #{lpn,jdbcType=VARCHAR}
        </if>
        <if test="lpn == null or lpn == ''">
            AND 1=2
        </if>
    </delete>

    <insert id="insertSmtMachineMaterialMouting" parameterType="com.zte.domain.model.SmtMachineMaterialMouting">
        insert into SMT_MACHINE_MATERIAL_MOUTING (MACHINE_MATERIAL_MOUTING_ID, WORK_ORDER,
        ITEM_CODE, LINE_CODE, ITEM_NAME,
        MACHINE_NO, LOCATION_NO, TRACK_NO,
        FEEDER_NO, OBJECT_ID, QTY,
        FORWARD, NEXT_REEL_ROWID, REMARK,
        CREATE_DATE, CREATE_USER, LAST_UPDATED_DATE,
        LAST_UPDATED_BY, ENABLED_FLAG, ORG_ID,
        ENTITY_ID,FACTORY_ID,RAWQTY,MODULE_NO,IS_LEAD,LPN,COMBINATION,AVL,POLAR_INFO,SOURCE_BATCH_CODE,CFG_HEADER_ID,
        WET_LEVEL,ITEM_TYPE)
        values (#{machineMaterialMoutingId,jdbcType=VARCHAR}, #{workOrder,jdbcType=VARCHAR},
        #{itemCode,jdbcType=VARCHAR}, #{lineCode,jdbcType=VARCHAR}, #{itemName,jdbcType=VARCHAR},
        #{machineNo,jdbcType=VARCHAR}, #{locationNo,jdbcType=VARCHAR}, #{trackNo,jdbcType=VARCHAR},
        #{feederNo,jdbcType=VARCHAR}, #{objectId,jdbcType=VARCHAR}, #{qty,jdbcType=DECIMAL},
        #{forward,jdbcType=VARCHAR}, #{nextReelRowid,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR},
        SYSDATE, #{createUser,jdbcType=VARCHAR}, SYSDATE,
        #{lastUpdatedBy,jdbcType=VARCHAR}, #{enabledFlag,jdbcType=VARCHAR}, #{orgId,jdbcType=DECIMAL},
        #{entityId,jdbcType=DECIMAL},#{factoryId,jdbcType=DECIMAL},#{ramQty,jdbcType=DECIMAL},#{moduleNo,jdbcType=VARCHAR}
        ,#{isLead,jdbcType=VARCHAR},#{lpn,jdbcType=VARCHAR},#{combination,jdbcType=VARCHAR},#{avl,jdbcType=VARCHAR},#{polarInfo,jdbcType=VARCHAR}
        ,#{sourceBatchCode,jdbcType=VARCHAR},#{cfgHeaderId,jdbcType=VARCHAR},#{wetLevel,jdbcType=VARCHAR},#{itemType,jdbcType=VARCHAR})
    </insert>

    <insert id="insertSmtMachineMaterialMoutingSelective"
            parameterType="com.zte.domain.model.SmtMachineMaterialMouting">
        insert into SMT_MACHINE_MATERIAL_MOUTING
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="machineMaterialMoutingId != null">
                MACHINE_MATERIAL_MOUTING_ID,
            </if>

            <if test="workOrder != null">
                WORK_ORDER,
            </if>

            <if test="itemCode != null">
                ITEM_CODE,
            </if>

            <if test="lineCode != null">
                LINE_CODE,
            </if>

            <if test="itemName != null">
                ITEM_NAME,
            </if>

            <if test="machineNo != null">
                MACHINE_NO,
            </if>

            <if test="locationNo != null">
                LOCATION_NO,
            </if>

            <if test="trackNo != null">
                TRACK_NO,
            </if>

            <if test="feederNo != null">
                FEEDER_NO,
            </if>

            <if test="objectId != null">
                OBJECT_ID,
            </if>

            <if test="qty != null">
                QTY,
            </if>

            <if test="forward != null">
                FORWARD,
            </if>

            <if test="nextReelRowid != null">
                NEXT_REEL_ROWID,
            </if>

            <if test="remark != null">
                REMARK,
            </if>


            <if test="createUser != null">
                CREATE_USER,
            </if>

            <if test="lastUpdatedBy != null">
                LAST_UPDATED_BY,
            </if>

            <if test="orgId != null">
                ORG_ID,
            </if>

            <if test="entityId != null">
                ENTITY_ID,
            </if>

            <if test="factoryId != null">
                FACTORY_ID,
            </if>

            <if test="ramQty != null">
                RAWQTY,
            </if>

            <if test="moduleNo != null">
                MODULE_NO,
            </if>

            <if test="isLead != null">
                IS_LEAD,
            </if>

            <if test="lpn != null">
                LPN,
            </if>

            <if test="combination != null">
                COMBINATION,
            </if>

            <if test="avl != null">
                AVL,
            </if>

            <if test="polarInfo != null">
                POLAR_INFO,
            </if>

            <if test="sourceBatchCode != null">
                SOURCE_BATCH_CODE,
            </if>
            <if test="cfgHeaderId != null">
                CFG_HEADER_ID,
            </if>
            <if test="wetLevel != null">
                WET_LEVEL,
            </if>
            <if test="itemType != null">
                ITEM_TYPE,
            </if>

            CREATE_DATE,
            LAST_UPDATED_DATE,
            ENABLED_FLAG
        </trim>

        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="machineMaterialMoutingId != null">
                #{machineMaterialMoutingId,jdbcType=VARCHAR},
            </if>

            <if test="workOrder != null">
                #{workOrder,jdbcType=VARCHAR},
            </if>

            <if test="itemCode != null">
                #{itemCode,jdbcType=VARCHAR},
            </if>

            <if test="lineCode != null">
                #{lineCode,jdbcType=VARCHAR},
            </if>

            <if test="itemName != null">
                #{itemName,jdbcType=VARCHAR},
            </if>

            <if test="machineNo != null">
                #{machineNo,jdbcType=VARCHAR},
            </if>

            <if test="locationNo != null">
                #{locationNo,jdbcType=VARCHAR},
            </if>

            <if test="trackNo != null">
                #{trackNo,jdbcType=VARCHAR},
            </if>

            <if test="feederNo != null">
                #{feederNo,jdbcType=VARCHAR},
            </if>

            <if test="objectId != null">
                #{objectId,jdbcType=VARCHAR},
            </if>

            <if test="qty != null">
                #{qty,jdbcType=DECIMAL},
            </if>

            <if test="forward != null">
                #{forward,jdbcType=VARCHAR},
            </if>

            <if test="nextReelRowid != null">
                #{nextReelRowid,jdbcType=VARCHAR},
            </if>

            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>

            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>

            <if test="lastUpdatedBy != null">
                #{lastUpdatedBy,jdbcType=VARCHAR},
            </if>

            <if test="orgId != null">
                #{orgId,jdbcType=DECIMAL},
            </if>

            <if test="entityId != null">
                #{entityId,jdbcType=DECIMAL},
            </if>

            <if test="factoryId != null">
                #{factoryId,jdbcType=DECIMAL},
            </if>

            <if test="ramQty != null">
                #{ramQty,jdbcType=DECIMAL},
            </if>

            <if test="moduleNo != null">
                #{moduleNo,jdbcType=VARCHAR},
            </if>

            <if test="isLead != null">
                #{isLead,jdbcType=VARCHAR},
            </if>

            <if test="lpn != null">
                #{lpn,jdbcType=VARCHAR},
            </if>

            <if test="combination != null">
                #{combination,jdbcType=VARCHAR},
            </if>

            <if test="avl != null">
                #{avl,jdbcType=VARCHAR},
            </if>

            <if test="polarInfo != null">
                #{polarInfo,jdbcType=VARCHAR},
            </if>

            <if test="sourceBatchCode != null">
                #{sourceBatchCode,jdbcType=VARCHAR},
            </if>
            <if test="cfgHeaderId != null">
                #{cfgHeaderId,jdbcType=VARCHAR},
            </if>
            <if test="wetLevel != null">
                #{wetLevel,jdbcType=VARCHAR},
            </if>
            <if test="itemType != null">
                #{itemType,jdbcType=VARCHAR},
            </if>
            SYSDATE,
            SYSDATE,
            'Y'
        </trim>

    </insert>

    <update id="updateSmtMachineMaterialMoutingByIdSelective"
            parameterType="com.zte.domain.model.SmtMachineMaterialMouting">
        update SMT_MACHINE_MATERIAL_MOUTING
        <set>
            <if test="workOrder != null">
                WORK_ORDER = #{workOrder,jdbcType=VARCHAR},
            </if>

            <if test="itemCode != null">
                ITEM_CODE = #{itemCode,jdbcType=VARCHAR},
            </if>

            <if test="lineCode != null">
                LINE_CODE = #{lineCode,jdbcType=VARCHAR},
            </if>

            <if test="itemName != null">
                ITEM_NAME = #{itemName,jdbcType=VARCHAR},
            </if>

            <if test="machineNo != null">
                MACHINE_NO = #{machineNo,jdbcType=VARCHAR},
            </if>

            <if test="locationNo != null">
                LOCATION_NO = #{locationNo,jdbcType=VARCHAR},
            </if>

            <if test="trackNo != null">
                TRACK_NO = #{trackNo,jdbcType=VARCHAR},
            </if>

            <if test="feederNo != null">
                FEEDER_NO = #{feederNo,jdbcType=VARCHAR},
            </if>

            <if test="objectId != null">
                OBJECT_ID = #{objectId,jdbcType=VARCHAR},
            </if>

            <if test="qty != null">
                QTY = #{qty,jdbcType=DECIMAL},
            </if>

            <if test="forward != null">
                FORWARD = #{forward,jdbcType=VARCHAR},
            </if>

            <if test="nextReelRowid != null">
                NEXT_REEL_ROWID = #{nextReelRowid,jdbcType=VARCHAR},
            </if>

            <if test="remark != null">
                REMARK = #{remark,jdbcType=VARCHAR},
            </if>

            <if test="createDate != null">
                CREATE_DATE= SYSDATE,
            </if>

            <if test="createUser != null">
                CREATE_USER = #{createUser,jdbcType=VARCHAR},
            </if>

            <if test="lastUpdatedBy != null">
                LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR},
            </if>

            <if test="enabledFlag != null">
                ENABLED_FLAG = #{enabledFlag,jdbcType=VARCHAR},
            </if>

            <if test="orgId != null">
                ORG_ID = #{orgId,jdbcType=DECIMAL},
            </if>

            <if test="entityId != null">
                ENTITY_ID = #{entityId,jdbcType=DECIMAL},
            </if>

            <if test="factoryId != null">
                FACTORY_ID = #{factoryId,jdbcType=DECIMAL},
            </if>
            <if test="ramQty != null">
                RAWQTY = #{ramQty,jdbcType=DECIMAL},
            </if>

            <if test="moduleNo != null">
                MODULE_NO = #{moduleNo,jdbcType=VARCHAR},
            </if>

            <if test="isLead != null">
                IS_LEAD = #{isLead,jdbcType=VARCHAR},
            </if>

            <if test="lpn != null">
                LPN = #{lpn,jdbcType=VARCHAR},
            </if>

            <if test="combination != null">
                COMBINATION = #{combination,jdbcType=VARCHAR},
            </if>

            <if test="avl != null">
                AVL = #{avl,jdbcType=VARCHAR},
            </if>

            <if test="polarInfo != null">
                POLAR_INFO = #{polarInfo,jdbcType=VARCHAR},
            </if>

            <if test="sourceBatchCode != null">
                SOURCE_BATCH_CODE = #{sourceBatchCode,jdbcType=VARCHAR},
            </if>
            <if test="cfgHeaderId != null">
                CFG_HEADER_ID = #{cfgHeaderId,jdbcType=VARCHAR},
            </if>
            <if test="wetLevel != null">
                WET_LEVEL = #{wetLevel,jdbcType=VARCHAR},
            </if>
            <if test="itemType != null">
                ITEM_TYPE = #{itemType,jdbcType=VARCHAR},
            </if>
            LAST_UPDATED_DATE= SYSDATE
        </set>

        where MACHINE_MATERIAL_MOUTING_ID = #{machineMaterialMoutingId,jdbcType=VARCHAR}
    </update>

    <update id="updateMoutingByPreReelSelectiveWithTime"
            parameterType="com.zte.interfaces.dto.SmtMachineMaterialMoutingDTO">
        update SMT_MACHINE_MATERIAL_MOUTING
        <set>
            <trim suffixOverrides=",">
                <if test="workOrder != null">
                    WORK_ORDER = #{workOrder,jdbcType=VARCHAR},
                </if>

                <if test="itemCode != null">
                    ITEM_CODE = #{itemCode,jdbcType=VARCHAR},
                </if>

                <if test="lineCode != null">
                    LINE_CODE = #{lineCode,jdbcType=VARCHAR},
                </if>

                <if test="itemName != null">
                    ITEM_NAME = #{itemName,jdbcType=VARCHAR},
                </if>

                <if test="machineNo != null">
                    MACHINE_NO = #{machineNo,jdbcType=VARCHAR},
                </if>

                <if test="locationNo != null">
                    LOCATION_NO = #{locationNo,jdbcType=VARCHAR},
                </if>

                <if test="trackNo != null">
                    TRACK_NO = #{trackNo,jdbcType=VARCHAR},
                </if>

                <if test="feederNo != null">
                    FEEDER_NO = #{feederNo,jdbcType=VARCHAR},
                </if>

                <if test="objectId != null">
                    OBJECT_ID = #{objectId,jdbcType=VARCHAR},
                </if>

                <if test="qty != null">
                    QTY = #{qty,jdbcType=DECIMAL},
                </if>

                <if test="forward != null">
                    FORWARD = #{forward,jdbcType=VARCHAR},
                </if>
                NEXT_REEL_ROWID = null,
                <if test="remark != null">
                    REMARK = #{remark,jdbcType=VARCHAR},
                </if>

                <if test="createDate != null">
                    CREATE_DATE= #{createDate,jdbcType=TIMESTAMP},
                </if>

                <if test="createUser != null">
                    CREATE_USER = #{createUser,jdbcType=VARCHAR},
                </if>

                <if test="lastUpdatedDate != null">
                    LAST_UPDATED_DATE= #{lastUpdatedDate,jdbcType=TIMESTAMP},
                </if>

                <if test="lastUpdatedBy != null">
                    LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR},
                </if>

                <if test="enabledFlag != null">
                    ENABLED_FLAG = #{enabledFlag,jdbcType=VARCHAR},
                </if>

                <if test="orgId != null">
                    ORG_ID = #{orgId,jdbcType=DECIMAL},
                </if>

                <if test="entityId != null">
                    ENTITY_ID = #{entityId,jdbcType=DECIMAL},
                </if>

                <if test="factoryId != null">
                    FACTORY_ID = #{factoryId,jdbcType=DECIMAL},
                </if>
                <if test="rawQty != null">
                    RAWQTY = #{rawQty,jdbcType=DECIMAL},
                </if>

                <if test="moduleNo != null">
                    MODULE_NO = #{moduleNo,jdbcType=VARCHAR},
                </if>

                <if test="isLead != null">
                    IS_LEAD = #{isLead,jdbcType=VARCHAR},
                </if>

                <if test="lpn != null">
                    LPN = #{lpn,jdbcType=VARCHAR},
                </if>

                <if test="combination != null">
                    COMBINATION = #{combination,jdbcType=VARCHAR},
                </if>

                <if test="avl != null">
                    AVL = #{avl,jdbcType=VARCHAR},
                </if>

                <if test="polarInfo != null">
                    POLAR_INFO = #{polarInfo,jdbcType=VARCHAR},
                </if>

                <if test="sourceBatchCode != null">
                    SOURCE_BATCH_CODE = #{sourceBatchCode,jdbcType=VARCHAR},
                </if>

                <if test="cfgHeaderId != null">
                    CFG_HEADER_ID = #{cfgHeaderId,jdbcType=VARCHAR},
                </if>
                <if test="wetLevel != null">
                    WET_LEVEL = #{wetLevel,jdbcType=VARCHAR},
                </if>
                <if test="itemType != null">
                    ITEM_TYPE = #{itemType,jdbcType=VARCHAR},
                </if>
            </trim>
        </set>
        where OBJECT_ID = #{preReelId,jdbcType=VARCHAR}
    </update>


    <update id="updateMoutingByPreReel" parameterType="com.zte.interfaces.dto.SmtMachineMaterialMoutingDTO">
        update SMT_MACHINE_MATERIAL_MOUTING
        <set>
            <trim suffixOverrides=",">
                <if test="itemCode != null">
                    ITEM_CODE = #{itemCode,jdbcType=VARCHAR},
                </if>

                <if test="itemName != null">
                    ITEM_NAME = #{itemName,jdbcType=VARCHAR},
                </if>

                <if test="machineNo != null">
                    MACHINE_NO = #{machineNo,jdbcType=VARCHAR},
                </if>

                <if test="locationNo != null">
                    LOCATION_NO = #{locationNo,jdbcType=VARCHAR},
                </if>

                <if test="trackNo != null">
                    TRACK_NO = #{trackNo,jdbcType=VARCHAR},
                </if>

                <if test="feederNo != null">
                    FEEDER_NO = #{feederNo,jdbcType=VARCHAR},
                </if>

                <if test="objectId != null">
                    OBJECT_ID = #{objectId,jdbcType=VARCHAR},
                </if>

                <if test="qty != null">
                    QTY = #{qty,jdbcType=DECIMAL},
                </if>

                <if test="forward != null">
                    FORWARD = #{forward,jdbcType=VARCHAR},
                </if>
                NEXT_REEL_ROWID = null,
                <if test="remark != null">
                    REMARK = #{remark,jdbcType=VARCHAR},
                </if>

                <if test="createDate != null">
                    CREATE_DATE= #{createDate,jdbcType=TIMESTAMP},
                </if>

                <if test="createUser != null">
                    CREATE_USER = #{createUser,jdbcType=VARCHAR},
                </if>

                <if test="lastUpdatedDate != null">
                    LAST_UPDATED_DATE= #{lastUpdatedDate,jdbcType=TIMESTAMP},
                </if>

                <if test="lastUpdatedBy != null">
                    LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR},
                </if>

                <if test="enabledFlag != null">
                    ENABLED_FLAG = #{enabledFlag,jdbcType=VARCHAR},
                </if>

                <if test="orgId != null">
                    ORG_ID = #{orgId,jdbcType=DECIMAL},
                </if>

                <if test="entityId != null">
                    ENTITY_ID = #{entityId,jdbcType=DECIMAL},
                </if>

                <if test="factoryId != null">
                    FACTORY_ID = #{factoryId,jdbcType=DECIMAL},
                </if>
                <if test="rawQty != null">
                    RAWQTY = #{rawQty,jdbcType=DECIMAL},
                </if>

                <if test="moduleNo != null">
                    MODULE_NO = #{moduleNo,jdbcType=VARCHAR},
                </if>

                <if test="isLead != null">
                    IS_LEAD = #{isLead,jdbcType=VARCHAR},
                </if>

                <if test="lpn != null">
                    LPN = #{lpn,jdbcType=VARCHAR},
                </if>

                <if test="combination != null">
                    COMBINATION = #{combination,jdbcType=VARCHAR},
                </if>

                <if test="avl != null">
                    AVL = #{avl,jdbcType=VARCHAR},
                </if>

                <if test="polarInfo != null">
                    POLAR_INFO = #{polarInfo,jdbcType=VARCHAR},
                </if>

                <if test="sourceBatchCode != null">
                    SOURCE_BATCH_CODE = #{sourceBatchCode,jdbcType=VARCHAR},
                </if>

                <if test="cfgHeaderId != null">
                    CFG_HEADER_ID = #{cfgHeaderId,jdbcType=VARCHAR},
                </if>
                <if test="wetLevel != null">
                    WET_LEVEL = #{wetLevel,jdbcType=VARCHAR},
                </if>
                <if test="itemType != null">
                    ITEM_TYPE = #{itemType,jdbcType=VARCHAR},
                </if>
            </trim>
        </set>
        where OBJECT_ID = #{preReelId,jdbcType=VARCHAR}
    </update>

    <update id="updateSmtMachineMaterialMoutingById" parameterType="com.zte.domain.model.SmtMachineMaterialMouting">
        update SMT_MACHINE_MATERIAL_MOUTING
        set WORK_ORDER = #{workOrder,jdbcType=VARCHAR},
        ITEM_CODE = #{itemCode,jdbcType=VARCHAR},
        LINE_CODE = #{lineCode,jdbcType=VARCHAR},
        ITEM_NAME = #{itemName,jdbcType=VARCHAR},
        MACHINE_NO = #{machineNo,jdbcType=VARCHAR},
        LOCATION_NO = #{locationNo,jdbcType=VARCHAR},
        TRACK_NO = #{trackNo,jdbcType=VARCHAR},
        FEEDER_NO = #{feederNo,jdbcType=VARCHAR},
        OBJECT_ID = #{objectId,jdbcType=VARCHAR},
        QTY = #{qty,jdbcType=DECIMAL},
        FORWARD = #{forward,jdbcType=VARCHAR},
        NEXT_REEL_ROWID = #{nextReelRowid,jdbcType=VARCHAR},
        REMARK = #{remark,jdbcType=VARCHAR},
        CREATE_DATE = SYSDATE,
        CREATE_USER = #{createUser,jdbcType=VARCHAR},
        LAST_UPDATED_DATE = SYSDATE,
        LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR},
        ENABLED_FLAG = #{enabledFlag,jdbcType=VARCHAR},
        ORG_ID = #{orgId,jdbcType=DECIMAL},
        ENTITY_ID = #{entityId,jdbcType=DECIMAL},
        FACTORY_ID = #{factoryId,jdbcType=DECIMAL},
        RAWQTY = #{ramQty,jdbcType=DECIMAL},
        MODULE_NO = #{moduleNo,jdbcType=VARCHAR},
        IS_LEAD = #{isLead,jdbcType=VARCHAR},
        LPN = #{lpn,jdbcType=VARCHAR},
        COMBINATION = #{combination,jdbcType=VARCHAR},
        AVL = #{avl,jdbcType=VARCHAR},
        POLAR_INFO = #{polarInfo,jdbcType=VARCHAR},
        SOURCE_BATCH_CODE = #{sourceBatchCode,jdbcType=VARCHAR},
        CFG_HEADER_ID = #{cfgHeaderId,jdbcType=VARCHAR},
        WET_LEVEL = #{wetLevel,jdbcType=VARCHAR},
        ITEM_TYPE = #{itemType,jdbcType=VARCHAR}
        where ENABLED_FLAG = 'Y'
        AND MACHINE_MATERIAL_MOUTING_ID = #{machineMaterialMoutingId,jdbcType=VARCHAR}
    </update>

    <!--批量新增SMT机台在用物料信息-->
    <insert id="batchInsertSmtMachineMaterialMouting" parameterType="java.util.List">
        insert into SMT_MACHINE_MATERIAL_MOUTING (MACHINE_MATERIAL_MOUTING_ID, WORK_ORDER, ITEM_CODE,
        LINE_CODE,ITEM_NAME, MACHINE_NO, LOCATION_NO,
        TRACK_NO, FEEDER_NO, OBJECT_ID,
        QTY, FORWARD,NEXT_REEL_ROWID, REMARK,
        CREATE_DATE, CREATE_USER, LAST_UPDATED_DATE,
        LAST_UPDATED_BY, ENABLED_FLAG, ORG_ID,
        ENTITY_ID, FACTORY_ID, RAWQTY, MODULE_NO
        ,IS_LEAD, LPN,COMBINATION, AVL, POLAR_INFO, SOURCE_BATCH_CODE, CFG_HEADER_ID,
        WET_LEVEL,ITEM_TYPE
        )
        <foreach collection="list" item="item" index="index" separator="UNION ALL">
            SELECT #{item.machineMaterialMoutingId,jdbcType=VARCHAR} MACHINE_MATERIAL_MOUTING_ID,
            #{item.workOrder,jdbcType=VARCHAR} WORK_ORDER,
            #{item.itemCode,jdbcType=VARCHAR} ITEM_CODE,
            #{item.lineCode,jdbcType=VARCHAR} LINE_CODE,
            #{item.itemName,jdbcType=VARCHAR} ITEM_NAME,
            #{item.machineNo,jdbcType=VARCHAR} MACHINE_NO,
            #{item.locationNo,jdbcType=VARCHAR} LOCATION_NO,
            #{item.trackNo,jdbcType=VARCHAR} TRACK_NO,
            #{item.feederNo,jdbcType=VARCHAR} FEEDER_NO,
            #{item.objectId,jdbcType=VARCHAR} OBJECT_ID,
            #{item.qty,jdbcType=DECIMAL} QTY,
            #{item.forward,jdbcType=VARCHAR} "FORWARD",
            #{item.nextReelRowid,jdbcType=VARCHAR} NEXT_REEL_ROWID,
            #{item.remark,jdbcType=VARCHAR} REMARK,
            sysdate CREATE_DATE,
            #{item.createUser,jdbcType=VARCHAR} CREATE_USER,
            sysdate LAST_UPDATED_DATE,
            #{item.lastUpdatedBy,jdbcType=VARCHAR} LAST_UPDATED_BY,
            'Y' ENABLED_FLAG,
            #{item.orgId,jdbcType=DECIMAL} ORG_ID,
            #{item.entityId,jdbcType=DECIMAL} ENTITY_ID,
            #{item.factoryId,jdbcType=DECIMAL} FACTORY_ID,
            #{item.rawQty,jdbcType=DECIMAL} RAWQTY,
            #{item.moduleNo,jdbcType=VARCHAR} MODULE_NO,
            #{item.isLead,jdbcType=VARCHAR} IS_LEAD,
            #{item.lpn,jdbcType=VARCHAR} LPN,
            #{item.combination,jdbcType=VARCHAR} COMBINATION,
            #{item.avl,jdbcType=VARCHAR} AVL,
            #{item.polarInfo,jdbcType=VARCHAR} POLAR_INFO,
            #{item.sourceBatchCode,jdbcType=VARCHAR} SOURCE_BATCH_CODE,
            #{item.cfgHeaderId,jdbcType=VARCHAR} CFG_HEADER_ID,
            #{item.wetLevel,jdbcType=VARCHAR} WET_LEVEL,
            #{item.itemType,jdbcType=VARCHAR} ITEM_TYPE
        </foreach>
    </insert>

    <update id="batchUpdateSmtMachineMaterialMouting" parameterType="java.util.List">
        <foreach collection="list" separator=";" item="item" >
            update SMT_MACHINE_MATERIAL_MOUTING T set
            T.QTY =#{item.qty,jdbcType=DECIMAL},
            T.LAST_UPDATED_DATE = sysdate
            where T.MACHINE_MATERIAL_MOUTING_ID = #{item.machineMaterialMoutingId,jdbcType=VARCHAR}
            and T.ENABLED_FLAG = 'Y'
        </foreach>
    </update>

    <update id="batchUpdateById" parameterType="java.util.List">
        <foreach collection="list" separator=";" item="item" >
            update SMT_MACHINE_MATERIAL_MOUTING T set
            T.OBJECT_ID =#{item.objectId,jdbcType=DECIMAL},
            T.SOURCE_BATCH_CODE =#{item.sourceBatchCode,jdbcType=DECIMAL},
            T.NEXT_REEL_ROWID =#{item.nextReelRowid,jdbcType=DECIMAL},
            T.LAST_UPDATED_DATE = sysdate
            where T.MACHINE_MATERIAL_MOUTING_ID = #{item.machineMaterialMoutingId,jdbcType=VARCHAR}
            and T.ENABLED_FLAG = 'Y'
        </foreach>
    </update>

    <update id="batchUpdateNextReelRowidByReelId" parameterType="java.util.List">
        <foreach collection="list" separator=";" item="item" >
            update SMT_MACHINE_MATERIAL_MOUTING T set
            T.OBJECT_ID =#{item.objectId,jdbcType=VARCHAR},
            T.NEXT_REEL_ROWID =#{item.nextReelRowid,jdbcType=VARCHAR},
            T.LAST_UPDATED_DATE = sysdate
            where T.OBJECT_ID = #{item.nextReelRowid,jdbcType=VARCHAR}
            and T.ENABLED_FLAG = 'Y'
        </foreach>
    </update>
    <!--wl,逻辑删除-->
    <update id="deleteSmtMachineMaterialMoutingByOther" parameterType="com.zte.domain.model.SmtMachineMaterialMouting">
        update SMT_MACHINE_MATERIAL_MOUTING
        set ENABLED_FLAG = 'N'
        where ENABLED_FLAG = 'Y'
        <if test="machineMaterialMoutingId != null and machineMaterialMoutingId != ''">
            and MACHINE_MATERIAL_MOUTING_ID = #{machineMaterialMoutingId,jdbcType=VARCHAR}
        </if>
        <if test="workOrder != null and workOrder != ''">
            and WORK_ORDER = #{workOrder,jdbcType=VARCHAR}
        </if>
        <if test="itemCode != null and itemCode != ''">
            and ITEM_CODE = #{itemCode,jdbcType=VARCHAR}
        </if>
        <if test="locationNo != null and locationNo != ''">
            and LOCATION_NO = #{locationNo,jdbcType=VARCHAR}
        </if>
    </update>

    <!--wl根据多条件修改-->
    <update id="updateSmtMachineMaterialMoutingByOthers" parameterType="com.zte.domain.model.SmtMachineMaterialMouting">
        update SMT_MACHINE_MATERIAL_MOUTING
        <set>
            <if test="workOrder != null">
                WORK_ORDER = #{workOrder,jdbcType=VARCHAR},
            </if>
            <if test="itemCode != null">
                ITEM_CODE = #{itemCode,jdbcType=VARCHAR},
            </if>

            <if test="lineCode != null">
                LINE_CODE = #{lineCode,jdbcType=VARCHAR},
            </if>

            <if test="itemName != null">
                ITEM_NAME = #{itemName,jdbcType=VARCHAR},
            </if>

            <if test="machineNo != null">
                MACHINE_NO = #{machineNo,jdbcType=VARCHAR},
            </if>

            <if test="locationNo != null">
                LOCATION_NO = #{locationNo,jdbcType=VARCHAR},
            </if>

            <if test="lpn != null">
                LPN = #{lpn,jdbcType=VARCHAR},
            </if>

            <if test="combination != null">
                COMBINATION = #{combination,jdbcType=VARCHAR},
            </if>

            <if test="trackNo != null">
                TRACK_NO = #{trackNo,jdbcType=VARCHAR},
            </if>

            <if test="feederNo != null">
                FEEDER_NO = #{feederNo,jdbcType=VARCHAR},
            </if>

            <if test="objectId != null">
                OBJECT_ID = #{objectId,jdbcType=VARCHAR},
            </if>

            <if test="qty != null">
                QTY = #{qty,jdbcType=DECIMAL},
            </if>

            <if test="forward != null">
                FORWARD = #{forward,jdbcType=VARCHAR},
            </if>

            <if test="nextReelRowid != null">
                NEXT_REEL_ROWID = #{nextReelRowid,jdbcType=VARCHAR},
            </if>

            <if test="remark != null">
                REMARK = #{remark,jdbcType=VARCHAR},
            </if>

            <if test="createDate != null">
                CREATE_DATE= SYSDATE,
            </if>

            <if test="createUser != null">
                CREATE_USER = #{createUser,jdbcType=VARCHAR},
            </if>

            <if test="lastUpdatedBy != null">
                LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR},
            </if>

            <if test="enabledFlag != null">
                ENABLED_FLAG = #{enabledFlag,jdbcType=VARCHAR},
            </if>

            <if test="orgId != null">
                ORG_ID = #{orgId,jdbcType=DECIMAL},
            </if>

            <if test="entityId != null">
                ENTITY_ID = #{entityId,jdbcType=DECIMAL},
            </if>

            <if test="factoryId != null">
                FACTORY_ID = #{factoryId,jdbcType=DECIMAL},
            </if>
            <if test="ramQty != null">
                RAWQTY = #{ramQty,jdbcType=DECIMAL},
            </if>

            <if test="moduleNo != null">
                MODULE_NO = #{moduleNo,jdbcType=VARCHAR},
            </if>

            <if test="isLead != null">
                IS_LEAD = #{isLead,jdbcType=VARCHAR},
            </if>

            <if test="avl != null">
                AVL = #{avl,jdbcType=VARCHAR},
            </if>

            <if test="polarInfo != null">
                POLAR_INFO = #{polarInfo,jdbcType=VARCHAR},
            </if>
            <if test="sourceBatchCode != null">
                SOURCE_BATCH_CODE = #{sourceBatchCode,jdbcType=VARCHAR},
            </if>
            <if test="cfgHeaderId != null">
                CFG_HEADER_ID = #{cfgHeaderId,jdbcType=VARCHAR},
            </if>
            <if test="wetLevel != null">
                WET_LEVEL = #{wetLevel,jdbcType=VARCHAR},
            </if>
            <if test="itemType != null">
                ITEM_TYPE = #{itemType,jdbcType=VARCHAR},
            </if>
            LAST_UPDATED_DATE= SYSDATE
        </set>
        where 1=1 and ENABLED_FLAG = 'Y'
        <if test="machineMaterialMoutingId != null and machineMaterialMoutingId != ''">
            and MACHINE_MATERIAL_MOUTING_ID = #{machineMaterialMoutingId,jdbcType=VARCHAR}
        </if>
        <if test="workOrder != null and workOrder != ''">
            and WORK_ORDER = #{workOrder,jdbcType=VARCHAR}
        </if>
        <if test="itemCode != null and itemCode != ''">
            and ITEM_CODE = #{itemCode,jdbcType=VARCHAR}
        </if>
        <if test="locationNo != null and locationNo != ''">
            and LOCATION_NO = #{locationNo,jdbcType=VARCHAR}
        </if>
        <if test="lpn != null and lpn != ''">
            and LPN = #{lpn,jdbcType=VARCHAR}
        </if>
        <if test="(machineMaterialMoutingId == null or machineMaterialMoutingId == '')
        and (workOrder == null or workOrder == '') and (itemCode == null or itemCode == '')
        and (locationNo == null or locationNo == '') and (lpn == null or lpn == '')">
            AND 1 = 2
        </if>
    </update>

    <update id="updateMoutingByIdForFeederChange" parameterType="com.zte.domain.model.SmtMachineMaterialMouting">
        update SMT_MACHINE_MATERIAL_MOUTING
        <set>
            <if test="feederNo != null">
                FEEDER_NO = #{feederNo,jdbcType=VARCHAR},
            </if>
            <if test="lastUpdatedBy != null">
                LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR},
            </if>
            <if test="entityId != null">
                ENTITY_ID = #{entityId,jdbcType=DECIMAL},
            </if>
            <if test="factoryId != null">
                FACTORY_ID = #{factoryId,jdbcType=DECIMAL},
            </if>
            LAST_UPDATED_DATE= SYSDATE
        </set>
        where MACHINE_MATERIAL_MOUTING_ID = #{machineMaterialMoutingId,jdbcType=VARCHAR}
    </update>

    <update id="updateMountingInfoByObjectId" parameterType="com.zte.domain.model.SmtMachineMaterialMouting">
        update SMT_MACHINE_MATERIAL_MOUTING
        <set>
            <if test="workOrder != null">
                WORK_ORDER = #{workOrder,jdbcType=VARCHAR},
            </if>
            <if test="itemCode != null">
                ITEM_CODE = #{itemCode,jdbcType=VARCHAR},
            </if>

            <if test="lineCode != null">
                LINE_CODE = #{lineCode,jdbcType=VARCHAR},
            </if>

            <if test="itemName != null">
                ITEM_NAME = #{itemName,jdbcType=VARCHAR},
            </if>

            <if test="machineNo != null">
                MACHINE_NO = #{machineNo,jdbcType=VARCHAR},
            </if>

            <if test="locationNo != null">
                LOCATION_NO = #{locationNo,jdbcType=VARCHAR},
            </if>

            <if test="trackNo != null">
                TRACK_NO = #{trackNo,jdbcType=VARCHAR},
            </if>

            <if test="feederNo != null">
                FEEDER_NO = #{feederNo,jdbcType=VARCHAR},
            </if>

            <if test="objectId != null">
                OBJECT_ID = #{objectId,jdbcType=VARCHAR},
            </if>

            <if test="qty != null">
                QTY = #{qty,jdbcType=DECIMAL},
            </if>

            <if test="forward != null">
                FORWARD = #{forward,jdbcType=VARCHAR},
            </if>

            <if test="nextReelRowid != null">
                NEXT_REEL_ROWID = #{nextReelRowid,jdbcType=VARCHAR},
            </if>

            <if test="remark != null">
                REMARK = #{remark,jdbcType=VARCHAR},
            </if>

            <if test="createDate != null">
                CREATE_DATE= SYSDATE,
            </if>

            <if test="createUser != null">
                CREATE_USER = #{createUser,jdbcType=VARCHAR},
            </if>

            <if test="lastUpdatedBy != null">
                LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR},
            </if>

            <if test="enabledFlag != null">
                ENABLED_FLAG = #{enabledFlag,jdbcType=VARCHAR},
            </if>

            <if test="orgId != null">
                ORG_ID = #{orgId,jdbcType=DECIMAL},
            </if>

            <if test="entityId != null">
                ENTITY_ID = #{entityId,jdbcType=DECIMAL},
            </if>

            <if test="factoryId != null">
                FACTORY_ID = #{factoryId,jdbcType=DECIMAL},
            </if>
            <if test="ramQty != null">
                RAWQTY = #{ramQty,jdbcType=DECIMAL},
            </if>

            <if test="moduleNo != null">
                MODULE_NO = #{moduleNo,jdbcType=VARCHAR},
            </if>

            <if test="isLead != null">
                IS_LEAD = #{isLead,jdbcType=VARCHAR},
            </if>

            <if test="avl != null">
                AVL = #{avl,jdbcType=VARCHAR},
            </if>

            <if test="lpn != null">
                LPN = #{lpn,jdbcType=VARCHAR},
            </if>

            <if test="combination != null">
                COMBINATION = #{combination,jdbcType=VARCHAR},
            </if>

            <if test="polarInfo != null">
                POLAR_INFO = #{polarInfo,jdbcType=VARCHAR},
            </if>
            <if test="sourceBatchCode != null">
                SOURCE_BATCH_CODE = #{sourceBatchCode,jdbcType=VARCHAR},
            </if>
            <if test="cfgHeaderId != null">
                CFG_HEADER_ID = #{cfgHeaderId,jdbcType=VARCHAR},
            </if>
            <if test="wetLevel != null">
                WET_LEVEL = #{wetLevel,jdbcType=VARCHAR},
            </if>
            <if test="itemType != null">
                ITEM_TYPE = #{itemType,jdbcType=VARCHAR},
            </if>
            LAST_UPDATED_DATE= SYSDATE
        </set>
        where MACHINE_MATERIAL_MOUTING_ID = #{machineMaterialMoutingId,jdbcType=VARCHAR}
    </update>

    <update id="updateByReelId" parameterType="com.zte.domain.model.SmtMachineMaterialMouting">
        update SMT_MACHINE_MATERIAL_MOUTING
        <set>
            <if test="workOrder != null">
                WORK_ORDER = #{workOrder,jdbcType=VARCHAR},
            </if>
            <if test="itemCode != null">
                ITEM_CODE = #{itemCode,jdbcType=VARCHAR},
            </if>

            <if test="lineCode != null">
                LINE_CODE = #{lineCode,jdbcType=VARCHAR},
            </if>

            <if test="itemName != null">
                ITEM_NAME = #{itemName,jdbcType=VARCHAR},
            </if>

            <if test="machineNo != null">
                MACHINE_NO = #{machineNo,jdbcType=VARCHAR},
            </if>

            <if test="locationNo != null">
                LOCATION_NO = #{locationNo,jdbcType=VARCHAR},
            </if>

            <if test="lpn != null">
                LPN = #{lpn,jdbcType=VARCHAR},
            </if>

            <if test="combination != null">
                COMBINATION = #{combination,jdbcType=VARCHAR},
            </if>

            <if test="trackNo != null">
                TRACK_NO = #{trackNo,jdbcType=VARCHAR},
            </if>

            <if test="feederNo != null">
                FEEDER_NO = #{feederNo,jdbcType=VARCHAR},
            </if>

            <if test="qty != null">
                QTY = #{qty,jdbcType=DECIMAL},
            </if>

            <if test="forward != null">
                FORWARD = #{forward,jdbcType=VARCHAR},
            </if>

            <if test="remark != null">
                REMARK = #{remark,jdbcType=VARCHAR},
            </if>

            <if test="createDate != null">
                CREATE_DATE= SYSDATE,
            </if>

            <if test="createUser != null">
                CREATE_USER = #{createUser,jdbcType=VARCHAR},
            </if>

            <if test="lastUpdatedBy != null">
                LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR},
            </if>

            <if test="enabledFlag != null">
                ENABLED_FLAG = #{enabledFlag,jdbcType=VARCHAR},
            </if>

            <if test="orgId != null">
                ORG_ID = #{orgId,jdbcType=DECIMAL},
            </if>

            <if test="entityId != null">
                ENTITY_ID = #{entityId,jdbcType=DECIMAL},
            </if>

            <if test="factoryId != null">
                FACTORY_ID = #{factoryId,jdbcType=DECIMAL},
            </if>
            <if test="ramQty != null">
                RAWQTY = #{ramQty,jdbcType=DECIMAL},
            </if>

            <if test="moduleNo != null">
                MODULE_NO = #{moduleNo,jdbcType=VARCHAR},
            </if>

            <if test="isLead != null">
                IS_LEAD = #{isLead,jdbcType=VARCHAR},
            </if>

            <if test="avl != null">
                AVL = #{avl,jdbcType=VARCHAR},
            </if>

            <if test="polarInfo != null">
                POLAR_INFO = #{polarInfo,jdbcType=VARCHAR},
            </if>
            <if test="sourceBatchCode != null">
                SOURCE_BATCH_CODE = #{sourceBatchCode,jdbcType=VARCHAR},
            </if>
            <if test="cfgHeaderId != null">
                CFG_HEADER_ID = #{cfgHeaderId,jdbcType=VARCHAR},
            </if>
            <if test="wetLevel != null">
                WET_LEVEL = #{wetLevel,jdbcType=VARCHAR},
            </if>
            <if test="itemType != null">
                ITEM_TYPE = #{itemType,jdbcType=VARCHAR},
            </if>
            LAST_UPDATED_DATE= SYSDATE,
        </set>
        where OBJECT_ID = #{objectId,jdbcType=VARCHAR}
        or NEXT_REEL_ROWID = #{objectId,jdbcType=VARCHAR}
    </update>

    <update id="updateByFeederNo" parameterType="com.zte.domain.model.SmtMachineMaterialMouting">
        update SMT_MACHINE_MATERIAL_MOUTING
        <set>
            <if test="workOrder != null">
                WORK_ORDER = #{workOrder,jdbcType=VARCHAR},
            </if>
            <if test="itemCode != null">
                ITEM_CODE = #{itemCode,jdbcType=VARCHAR},
            </if>

            <if test="lineCode != null">
                LINE_CODE = #{lineCode,jdbcType=VARCHAR},
            </if>

            <if test="itemName != null">
                ITEM_NAME = #{itemName,jdbcType=VARCHAR},
            </if>

            <if test="machineNo != null">
                MACHINE_NO = #{machineNo,jdbcType=VARCHAR},
            </if>

            <if test="locationNo != null">
                LOCATION_NO = #{locationNo,jdbcType=VARCHAR},
            </if>

            <if test="lpn != null">
                LPN = #{lpn,jdbcType=VARCHAR},
            </if>

            <if test="combination != null">
                COMBINATION = #{combination,jdbcType=VARCHAR},
            </if>

            <if test="trackNo != null">
                TRACK_NO = #{trackNo,jdbcType=VARCHAR},
            </if>

            <if test="objectId != null">
                OBJECT_ID = #{objectId,jdbcType=VARCHAR},
            </if>

            <if test="qty != null">
                QTY = #{qty,jdbcType=DECIMAL},
            </if>

            <if test="forward != null">
                FORWARD = #{forward,jdbcType=VARCHAR},
            </if>

            <if test="remark != null">
                REMARK = #{remark,jdbcType=VARCHAR},
            </if>

            <if test="createDate != null">
                CREATE_DATE= SYSDATE,
            </if>

            <if test="createUser != null">
                CREATE_USER = #{createUser,jdbcType=VARCHAR},
            </if>

            <if test="lastUpdatedBy != null">
                LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR},
            </if>

            <if test="enabledFlag != null">
                ENABLED_FLAG = #{enabledFlag,jdbcType=VARCHAR},
            </if>

            <if test="orgId != null">
                ORG_ID = #{orgId,jdbcType=DECIMAL},
            </if>

            <if test="entityId != null">
                ENTITY_ID = #{entityId,jdbcType=DECIMAL},
            </if>

            <if test="factoryId != null">
                FACTORY_ID = #{factoryId,jdbcType=DECIMAL},
            </if>
            <if test="ramQty != null">
                RAWQTY = #{ramQty,jdbcType=DECIMAL},
            </if>

            <if test="moduleNo != null">
                MODULE_NO = #{moduleNo,jdbcType=VARCHAR},
            </if>

            <if test="isLead != null">
                IS_LEAD = #{isLead,jdbcType=VARCHAR},
            </if>

            <if test="avl != null">
                AVL = #{avl,jdbcType=VARCHAR},
            </if>

            <if test="polarInfo != null">
                POLAR_INFO = #{polarInfo,jdbcType=VARCHAR},
            </if>
            <if test="sourceBatchCode != null">
                SOURCE_BATCH_CODE = #{sourceBatchCode,jdbcType=VARCHAR},
            </if>
            <if test="cfgHeaderId != null">
                CFG_HEADER_ID = #{cfgHeaderId,jdbcType=VARCHAR},
            </if>
            <if test="wetLevel != null">
                WET_LEVEL = #{wetLevel,jdbcType=VARCHAR},
            </if>
            <if test="itemType != null">
                ITEM_TYPE = #{itemType,jdbcType=VARCHAR},
            </if>
            LAST_UPDATED_DATE= SYSDATE,
        </set>
        where FEEDER_NO = #{feederNo,jdbcType=VARCHAR}
    </update>

    <!-- 根据id批量修改 -->
    <update id="updateSmtMachineMaterialMoutingByIdBatch" parameterType="java.util.List">
        <foreach collection="list" separator=";" item="item" >
            update SMT_MACHINE_MATERIAL_MOUTING
            <set>
                WORK_ORDER = #{item.workOrder,jdbcType=VARCHAR},
                MACHINE_NO = #{item.machineNo,jdbcType=VARCHAR},
                LOCATION_NO = #{item.locationNo,jdbcType=VARCHAR},
                REMARK = #{item.remark,jdbcType=VARCHAR},
                LAST_UPDATED_DATE= SYSDATE
            </set>
            where ENABLED_FLAG = 'Y'
            AND MACHINE_MATERIAL_MOUTING_ID = #{item.machineMaterialMoutingId,jdbcType=VARCHAR}
        </foreach>
    </update>

    <update id="pdaBatchUpdateSmtMachineMaterialMoutingBatch" parameterType="java.util.List">
        <foreach collection="list" separator=";" item="item" >
            update SMT_MACHINE_MATERIAL_MOUTING T set
            T.QTY =NVL(T.QTY,0) + #{item.qty,jdbcType=DECIMAL},
            T.LAST_UPDATED_DATE = sysdate,
            T.LAST_UPDATED_BY=#{item.lastUpdatedBy,jdbcType=VARCHAR}
            where T.ENABLED_FLAG = 'Y'
            and T.WORK_ORDER=#{item.workOrder,jdbcType=VARCHAR}
            and T.LOCATION_NO=#{item.locationNo,jdbcType=VARCHAR}
            <if test="item.machineNo != null and item.machineNo != ''">
                and T.MACHINE_NO=#{item.machineNo,jdbcType=VARCHAR}
            </if>
            <if test="item.trackNo != null and item.trackNo != ''">
                and T.TRACK_NO=#{item.trackNo,jdbcType=VARCHAR}
            </if>
            <if test="item.itemCode != null and item.itemCode != ''">
                and T.ITEM_CODE=#{item.itemCode,jdbcType=VARCHAR}
            </if>
            <if test="item.lineCode != null and item.lineCode != ''">
                and T.LINE_CODE=#{item.lineCode,jdbcType=VARCHAR}
            </if>
        </foreach>
    </update>
    <select id="pdaSelectCountSmtMachineMaterialMouting"
            parameterType="com.zte.interfaces.dto.SmtMachineMaterialMoutingDTO" resultType="java.lang.Long">
        select count(*)
        from SMT_MACHINE_MATERIAL_MOUTING T
        where T.ENABLED_FLAG = 'Y'
        and T.WORK_ORDER=#{workOrder,jdbcType=VARCHAR}
        and T.LOCATION_NO=#{locationNo,jdbcType=VARCHAR}
        <if test="machineNo != null and machineNo != ''">
            and T.MACHINE_NO=#{machineNo,jdbcType=VARCHAR}
        </if>
        <if test="trackNo != null and trackNo != ''">
            and T.TRACK_NO=#{trackNo,jdbcType=VARCHAR}
        </if>
        <if test="itemCode != null and itemCode != ''">
            and T.ITEM_CODE=#{itemCode,jdbcType=VARCHAR}
        </if>
        <if test="lineCode != null and lineCode != ''">
            and T.LINE_CODE=#{lineCode,jdbcType=VARCHAR}
        </if>
    </select>

    <!-- 获取符合条件的记录列表 -->
    <select id="getList" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        T.MACHINE_MATERIAL_MOUTING_ID,
        T.WORK_ORDER,
        T.ITEM_CODE,
        T.ITEM_NAME,
        T.LINE_CODE,
        T.MACHINE_NO,
        T.LOCATION_NO,
        T.TRACK_NO,
        T.FEEDER_NO,
        T.OBJECT_ID,
        T.QTY,
        T.LPN,
        T.COMBINATION,
        T.REMARK,
        T.NEXT_REEL_ROWID,
        T.CREATE_DATE,
        T.FACTORY_ID,
        T.ENTITY_ID,
        T.ENABLED_FLAG,
        T.CREATE_USER,MODULE_NO,RAWQTY,IS_LEAD,AVL,POLAR_INFO,SOURCE_BATCH_CODE,CFG_HEADER_ID,
        WET_LEVEL,ITEM_TYPE
        FROM
        SMT_MACHINE_MATERIAL_MOUTING T
        where T.ENABLED_FLAG = 'Y'
        <if test="workOrder!=null and workOrder!=''">and T.WORK_ORDER = #{workOrder}</if>
        <if test="itemCode!=null and itemCode!=''">and T.ITEM_CODE = #{itemCode}</if>
        <if test="itemCodes!=null and itemCodes!=''">and T.ITEM_CODE in ${itemCodes}</if>
        <if test="lineCode!=null and lineCode!=''">and T.LINE_CODE = #{lineCode}</if>
        <if test="machineNo!=null and machineNo!=''">and T.MACHINE_NO = #{machineNo}</if>
        <if test="locationNo!=null and locationNo!=''">and T.LOCATION_NO = #{locationNo}</if>
        <if test="trackNo!=null and trackNo!=''">and T.TRACK_NO = #{trackNo}</if>
        <if test="feederNo!=null and feederNo!=''">and T.FEEDER_NO = #{feederNo}</if>
        <if test="objectId!=null and objectId!=''">and (T.OBJECT_ID = #{objectId} or T.NEXT_REEL_ROWID = #{objectId})</if>
        <if test="moduleNo != null and moduleNo != ''"> and T.MODULE_NO = #{moduleNo,jdbcType=VARCHAR} </if>
        <if test="inModuleNo != null and inModuleNo != ''">and t.MODULE_NO in (${inModuleNo})</if>
        <if test="inWorkOrder!=null and inModuleNo != ''">and T.WORK_ORDER in (${inWorkOrder})</if>
        <if test="nextReelRowid!=null and nextReelRowid!=''">and T.NEXT_REEL_ROWID = #{nextReelRowid}</if>
        <if test="inNextReelRowid!=null and inNextReelRowid != ''">and T.NEXT_REEL_ROWID in (${inNextReelRowid})</if>
        <if test="prodplanId!=null and prodplanId!=''">and T.WORK_ORDER LIKE '${prodplanId}%'</if>
        <if test="qty!=null and qty!=''">and T.QTY=#{qty}::numeric</if>
        <if test="(workOrder == null or workOrder == '') and ( itemCode == null or itemCode == '') and (itemCodes == null or itemCodes == '') and ( lineCode == null or lineCode == '') and
             (machineNo == null or machineNo == '') and ( locationNo == null or locationNo == '') and (trackNo == null or trackNo == '') and ( feederNo == null or feederNo == '') and
             (objectId == null or objectId == '') and ( moduleNo == null or moduleNo == '') and (inModuleNo == null or inModuleNo == '') and ( inWorkOrder == null or inWorkOrder == '') and
             (nextReelRowid == null or nextReelRowid == '') and ( inNextReelRowid == null or inNextReelRowid == '') and (prodplanId == null or prodplanId == '') "> and 1=2 </if>
        <if test="orderField!=null">
            <choose>
                <when test="orderField=='workOrder'">order by T.WORK_ORDER  <if
                        test="order != null and order == 'desc'">desc
                </if>
                </when>
                <when test="orderField=='itemCode'">order by T.ITEM_CODE  <if test="order != null and order == 'desc'">
                    desc
                </if>
                </when>
                <when test="orderField=='lineCode'">order by T.LINE_CODE  <if test="order != null and order == 'desc'">
                    desc
                </if>
                </when>
                <when test="orderField=='machineNo'">order by T.MACHINE_NO  <if
                        test="order != null and order == 'desc'">desc
                </if>
                </when>
                <when test="orderField=='locationNo'">order by T.LOCATION_NO  <if
                        test="order != null and order == 'desc'">desc
                </if>
                </when>
                <when test="orderField=='trackNo'">order by T.TRACK_NO   <if test="order != null and order == 'desc'">
                    desc
                </if>
                </when>
                <when test="orderField=='feederNo'">order by T.FEEDER_NO  <if test="order != null and order == 'desc'">
                    desc
                </if>
                </when>
                <when test="orderField=='objectId'">order by T.OBJECT_ID  <if test="order != null and order == 'desc'">
                    desc
                </if>
                </when>
                <when test="orderField=='qty'">order by T.QTY <if test="order != null and order == 'desc'">desc</if>
                </when>
                <when test="orderField=='createDate'">order by T.CREATE_DATE <if
                        test="order != null and order == 'desc'">desc
                </if>
                </when>
                <when test="orderField=='createUser'">order by T.CREATE_USER <if
                        test="order != null and order == 'desc'">desc
                </if>
                </when>
            </choose>
        </if>
    </select>

    <select id="getListByModuleNoAndWorkOrderAndLineCode"  resultMap="BaseResultMap">
        SELECT
        T.MACHINE_MATERIAL_MOUTING_ID,
        T.WORK_ORDER,
        T.ITEM_CODE,
        T.ITEM_NAME,
        T.LINE_CODE,
        T.MACHINE_NO,
        T.LOCATION_NO,
        T.TRACK_NO,
        T.FEEDER_NO,
        T.OBJECT_ID,
        T.QTY,
        T.LPN,
        T.COMBINATION,
        T.REMARK,
        T.NEXT_REEL_ROWID,
        T.CREATE_DATE,
        T.FACTORY_ID,
        T.ENTITY_ID,
        T.ENABLED_FLAG,
        T.CREATE_USER,MODULE_NO,RAWQTY,IS_LEAD,AVL,POLAR_INFO,SOURCE_BATCH_CODE,CFG_HEADER_ID,
        WET_LEVEL,ITEM_TYPE
        FROM
        SMT_MACHINE_MATERIAL_MOUTING T
        where 1=1
        <if test="lineCode!=null and lineCode!=''">and T.LINE_CODE = #{lineCode,jdbcType=VARCHAR}</if>
        <if test="workOrderList != null and workOrderList.size() > 0">
            and T.WORK_ORDER IN
            <foreach item="workOrder" index="index" collection="workOrderList" open="(" separator="," close=")">
                #{workOrder}
            </foreach>
        </if>
        <if test="moduleNoList != null and moduleNoList.size() > 0">
            and T.MODULE_NO IN
            <foreach item="moduleNo" index="index" collection="moduleNoList" open="(" separator="," close=")">
                #{moduleNo}
            </foreach>
        </if>
        <if test="(workOrderList ==null or workOrderList.size() == 0) and (moduleNoList ==null or moduleNoList.size() == 0) and (lineCode ==null or lineCode =='') ">and 1=2</if>
    </select>

    <!-- 翻页函数:获取符合条件的记录数 -->
    <select id="getCount" parameterType="java.util.Map" resultType="java.lang.Long">
        select count(*)
        FROM SMT_MACHINE_MATERIAL_MOUTING T
        WHERE 1=1
        <if test="workOrder!=null and workOrder!=''">and T.WORK_ORDER = #{workOrder}</if>
        <if test="lpn!=null and lpn!=''">and T.LPN = #{lpn}</if>
        <if test="itemCode!=null and itemCode!=''">and T.ITEM_CODE = #{itemCode}</if>
        <if test="objectId!=null and objectId!=''">and (T.OBJECT_ID = #{objectId} or T.NEXT_REEL_ROWID = #{objectId})</if>
        <if test="lineCode!=null and lineCode!=''">and T.LINE_CODE = #{lineCode}</if>
        <if test="machineNo!=null and machineNo!=''">and T.MACHINE_NO = #{machineNo}</if>
        <if test="locationNo!=null and locationNo!=''">and T.LOCATION_NO = #{locationNo}</if>
        <if test="trackNo!=null and trackNo!=''">and T.TRACK_NO = #{trackNo}</if>
        <if test="feederNo!=null and feederNo!=''">and T.FEEDER_NO = #{feederNo}</if>
        <if test="prodplanId!=null and prodplanId!=''">and T.WORK_ORDER LIKE '${prodplanId}%'</if>
        <if test="isEffective!=null and isEffective!='' and isEffective!='ALL'">and T.ENABLED_FLAG = #{isEffective}</if>
        <if test="isEffective==null or isEffective==''">and T.ENABLED_FLAG = 'Y'</if>
    </select>

    <!-- 翻页函数:获取一页的记录集 -->
    <select id="getPage" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        T.WORK_ORDER,
        T.ITEM_CODE,
        T.ITEM_NAME,
        T.LINE_CODE,
        T.MACHINE_NO,
        T.LOCATION_NO,
        T.TRACK_NO,
        T.FEEDER_NO,
        T.OBJECT_ID,
        T.FORWARD,
        T.QTY,
        T.LPN,
        T.COMBINATION,
        T.REMARK,
        T.NEXT_REEL_ROWID,
        T.CREATE_DATE,
        T.CREATE_USER,
        T.ENABLED_FLAG,MODULE_NO,RAWQTY,IS_LEAD,AVL,POLAR_INFO,SOURCE_BATCH_CODE,CFG_HEADER_ID,
        WET_LEVEL,ITEM_TYPE,SOURCE_BATCH_CODE
        FROM
        SMT_MACHINE_MATERIAL_MOUTING T
        WHERE 1 = 1
        <if test="workOrder!=null and workOrder!=''">and T.WORK_ORDER = #{workOrder}</if>
        <if test="lpn!=null and lpn!=''">and T.LPN = #{lpn}</if>
        <if test="itemCode!=null and itemCode!=''">and T.ITEM_CODE = #{itemCode}</if>
        <if test="lineCode!=null and lineCode!=''">and T.LINE_CODE = #{lineCode}</if>
        <if test="machineNo!=null and machineNo!=''">and T.MACHINE_NO = #{machineNo}</if>
        <if test="locationNo!=null and locationNo!=''">and T.LOCATION_NO = #{locationNo}</if>
        <if test="trackNo!=null and trackNo!=''">and T.TRACK_NO = #{trackNo}</if>
        <if test="feederNo!=null and feederNo!=''">and T.FEEDER_NO = #{feederNo}</if>
        <if test="objectId!=null and objectId!=''">and (T.OBJECT_ID = #{objectId} or T.NEXT_REEL_ROWID = #{objectId})</if>
        <if test="prodplanId!=null and prodplanId!=''">and T.WORK_ORDER LIKE '${prodplanId}%'</if>
        <if test="qty!=null and qty!=''">and T.QTY=#{qty}::numeric</if>
        <if test="isEffective!=null and isEffective!='' and isEffective!='ALL'">and T.ENABLED_FLAG = #{isEffective}</if>
        <if test="isEffective==null or isEffective==''">and T.ENABLED_FLAG = 'Y'</if>
        <if test="orderField!=null">
            <choose>
                <when test="orderField=='workOrder'">order by T.WORK_ORDER  <if
                        test="order != null and order == 'desc'">desc
                </if>
                </when>
                <when test="orderField=='itemCode'">order by T.ITEM_CODE  <if test="order != null and order == 'desc'">
                    desc
                </if>
                </when>
                <when test="orderField=='lineCode'">order by T.LINE_CODE  <if test="order != null and order == 'desc'">
                    desc
                </if>
                </when>
                <when test="orderField=='machineNo'">order by T.MACHINE_NO  <if
                        test="order != null and order == 'desc'">desc
                </if>
                </when>
                <when test="orderField=='locationNo'">order by T.LOCATION_NO  <if
                        test="order != null and order == 'desc'">desc
                </if>
                </when>
                <when test="orderField=='trackNo'">order by T.TRACK_NO   <if test="order != null and order == 'desc'">
                    desc
                </if>
                </when>
                <when test="orderField=='feederNo'">order by T.FEEDER_NO  <if test="order != null and order == 'desc'">
                    desc
                </if>
                </when>
                <when test="orderField=='objectId'">order by T.OBJECT_ID  <if test="order != null and order == 'desc'">
                    desc
                </if>
                </when>
                <when test="orderField=='qty'">order by T.QTY <if test="order != null and order == 'desc'">desc</if>
                </when>
                <when test="orderField=='createDate'">order by T.CREATE_DATE <if
                        test="order != null and order == 'desc'">desc
                </if>
                </when>
                <when test="orderField=='createUser'">order by T.CREATE_USER <if
                        test="order != null and order == 'desc'">desc
                </if>
                </when>
            </choose>
        </if>
        limit #{endRow}::numeric-#{startRow}::numeric+1 offset case when ( #{startRow}::numeric-1 &lt; 0) then 0 else  #{startRow}::numeric-1 end
    </select>

    <!--根据id批量删除 -->
    <delete id="deleteSmtMachineMaterialMoutingByIdBatch" parameterType="java.util.List">
        <foreach collection="list" separator=";" item="item" >
            delete from SMT_MACHINE_MATERIAL_MOUTING T
            where T.MACHINE_MATERIAL_MOUTING_ID = #{item.machineMaterialMoutingId,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <select id="selectMoutingWithPkCodeInfoSelective"
            parameterType="com.zte.interfaces.dto.SmtMachineMaterialMoutingDTO" resultMap="BaseResultMap">
        select
        p.RAW_QTY as RAWQTY, p.ITEM_QTY as QTY, t.MACHINE_MATERIAL_MOUTING_ID, t.WORK_ORDER, t.ITEM_CODE, t.LINE_CODE,
        t.ITEM_NAME, t.MACHINE_NO,
        t.LOCATION_NO, t.TRACK_NO, t.FEEDER_NO, t.OBJECT_ID, t.FORWARD, t.NEXT_REEL_ROWID, t.REMARK,
        t.CREATE_DATE, t.CREATE_USER, t.LAST_UPDATED_DATE, t.LAST_UPDATED_BY, t.ENABLED_FLAG, t.ORG_ID,
        t.ENTITY_ID, t.FACTORY_ID, t.MODULE_NO, t.IS_LEAD,t.LPN,t.COMBINATION, t.AVL, t.POLAR_INFO, t.SOURCE_BATCH_CODE,
        t.CFG_HEADER_ID,
        t.WET_LEVEL, t.ITEM_TYPE,p.PRODUCT_TASK
        from SMT_MACHINE_MATERIAL_MOUTING t
        left join PK_CODE_INFO p on t.OBJECT_ID = p.PK_CODE and p.ENABLED_FLAG = 'Y'
        WHERE 1=1
        <if test="enabledFlag == null or enabledFlag == ''">
            AND t.ENABLED_FLAG = 'Y'
        </if>
        <if test="workOrder != null and workOrder != ''">
            AND t.WORK_ORDER = #{workOrder,jdbcType=VARCHAR}
        </if>
        <if test="locationNo != null and locationNo != ''">
            AND t.LOCATION_NO = #{locationNo,jdbcType=VARCHAR}
        </if>
        <if test="objectId != null and objectId != ''">
            AND t.OBJECT_ID = #{objectId,jdbcType=VARCHAR}
        </if>
        <if test="orgId != null">
            AND t.ORG_ID = #{orgId,jdbcType=DECIMAL}::numeric
        </if>
        <if test="objectIdOrNext != null and objectIdOrNext != ''">
            AND (t.OBJECT_ID = #{objectIdOrNext,jdbcType=VARCHAR} or t.NEXT_REEL_ROWID =
            #{objectIdOrNext,jdbcType=VARCHAR} )
        </if>
        <if test="orgId == null and (objectIdOrNext == null or objectIdOrNext == '') and (locationNo == null or locationNo == '') and  (workOrder == null or workOrder == '') and (objectId == null or objectId == '') ">
            and 1=2
        </if>
    </select>

    <select id="selectMoutingWithPkCodeInfo" parameterType="java.util.Map" resultMap="BaseResultMap">
        select
        p.RAW_QTY as RAWQTY, p.ITEM_QTY as QTY, t.MACHINE_MATERIAL_MOUTING_ID, t.WORK_ORDER, t.ITEM_CODE, t.LINE_CODE,
        t.ITEM_NAME, t.MACHINE_NO,
        t.LOCATION_NO, t.TRACK_NO, t.FEEDER_NO, t.OBJECT_ID, t.FORWARD, t.NEXT_REEL_ROWID, t.REMARK,
        t.CREATE_DATE, t.CREATE_USER, t.LAST_UPDATED_DATE, t.LAST_UPDATED_BY, t.ENABLED_FLAG, t.ORG_ID,
        t.ENTITY_ID, t.FACTORY_ID, t.MODULE_NO, t.IS_LEAD,t.LPN,t.COMBINATION, t.AVL, t.POLAR_INFO, t.SOURCE_BATCH_CODE,
        t.CFG_HEADER_ID,
        t.WET_LEVEL, t.ITEM_TYPE, t.switched_quickly_flag
        from SMT_MACHINE_MATERIAL_MOUTING t
        left join PK_CODE_INFO p on t.OBJECT_ID = p.PK_CODE
        <where>
            <if test="enabledFlag == null">
                AND t.ENABLED_FLAG = 'Y'
            </if>
            <if test="enabledFlag != null and enabledFlag != '' and enabledFlag != 'ALL'">
                AND t.ENABLED_FLAG = #{enabledFlag,jdbcType=VARCHAR}
            </if>
            <if test="workOrder != null and workOrder != ''">
                AND t.WORK_ORDER = #{workOrder,jdbcType=VARCHAR}
            </if>
            <if test="itemCode != null and itemCode != ''">
                AND t.ITEM_CODE = #{itemCode,jdbcType=VARCHAR}
            </if>
            <if test="lineCode != null and lineCode != ''">
                AND t.LINE_CODE = #{lineCode,jdbcType=VARCHAR}
            </if>
            <if test="cfgHeaderId != null and cfgHeaderId != ''">
                AND t.CFG_HEADER_ID = #{cfgHeaderId,jdbcType=VARCHAR}
            </if>
            <if test="moduleNo != null and moduleNo != ''">
                AND MODULE_NO = #{moduleNo,jdbcType=VARCHAR}
            </if>
            <if test="machineNo != null and machineNo != ''">
                AND MACHINE_NO = #{machineNo,jdbcType=VARCHAR}
            </if>
            <if test="inMachineNo != null and inMachineNo != ''">
                AND MACHINE_NO in (${inMachineNo})
            </if>
            <if test="notInMachineNo != null and notInMachineNo != ''">
                AND MACHINE_NO not in (${notInMachineNo})
            </if>
            <if test="notMachine != null and notMachine != ''">
                AND MACHINE_NO != #{notMachine,jdbcType=VARCHAR}
            </if>
            <if test="locationNo != null and locationNo != ''">
                AND LOCATION_NO = #{locationNo,jdbcType=VARCHAR}
            </if>
            <if test="(workOrder == null or workOrder == '') and ( itemCode == null or itemCode == '') and (lineCode == null or lineCode == '') and ( cfgHeaderId == null or cfgHeaderId == '') and
             (moduleNo == null or moduleNo == '') and ( machineNo == null or machineNo == '') and (inMachineNo == null or inMachineNo == '') and ( notInMachineNo == null or notInMachineNo == '') and
             (notMachine == null or notMachine == '') and ( locationNo == null or locationNo == '') "> and 1=2 </if>
            <if test="orderField!=null">
                <choose>
                    <when test="orderField=='workOrder'">order by t.WORK_ORDER  <if
                            test="order != null and order == 'desc'">desc
                    </if>
                    </when>
                    <when test="orderField=='itemCode'">order by t.ITEM_CODE  <if test="order != null and order == 'desc'">
                        desc
                    </if>
                    </when>
                    <when test="orderField=='lineCode'">order by t.LINE_CODE  <if test="order != null and order == 'desc'">
                        desc
                    </if>
                    </when>
                    <when test="orderField=='machineNo'">order by t.MACHINE_NO  <if
                            test="order != null and order == 'desc'">desc
                    </if>
                    </when>
                    <when test="orderField=='locationNo'">order by t.LOCATION_NO  <if
                            test="order != null and order == 'desc'">desc
                    </if>
                    </when>
                    <when test="orderField=='trackNo'">order by t.TRACK_NO   <if test="order != null and order == 'desc'">
                        desc
                    </if>
                    </when>
                    <when test="orderField=='feederNo'">order by t.FEEDER_NO  <if test="order != null and order == 'desc'">
                        desc
                    </if>
                    </when>
                    <when test="orderField=='objectId'">order by t.OBJECT_ID  <if test="order != null and order == 'desc'">
                        desc
                    </if>
                    </when>
                    <when test="orderField=='qty'">order by t.QTY <if test="order != null and order == 'desc'">desc</if>
                    </when>
                    <when test="orderField=='createDate'">order by t.CREATE_DATE <if
                            test="order != null and order == 'desc'">desc
                    </if>
                    </when>
                    <when test="orderField=='createUser'">order by t.CREATE_USER <if
                            test="order != null and order == 'desc'">desc
                    </if>
                    </when>
                    <when test="orderField=='lastUpdatedDate'">order by t.LAST_UPDATED_DATE <if
                            test="order != null and order == 'desc'">desc
                    </if>
                    </when>
                </choose>
            </if>
        </where>
    </select>

    <delete id="deleteSmtMachineMaterialMountingByDoubleReelId"
            parameterType="com.zte.domain.model.SmtMachineMaterialPrepare">
        delete from SMT_MACHINE_MATERIAL_MOUTING t3
        <where>
            t3.WORK_ORDER = #{workOrder,jdbcType=VARCHAR}
            and t3.OBJECT_ID in (
            select t2.OBJECT_ID from SMT_MACHINE_MATERIAL_MOUTING t2 where t2.LINE_CODE = #{lineCode,jdbcType=VARCHAR}
            and t2.OBJECT_ID in (
            select t1.OBJECT_ID from SMT_MACHINE_MATERIAL_MOUTING t1 where t1.OBJECT_ID in (
            select t.OBJECT_ID from SMT_MACHINE_MATERIAL_MOUTING t where t.WORK_ORDER = #{workOrder,jdbcType=VARCHAR}
            ) group by t1.OBJECT_ID having count(*)>1
            )
            )
        </where>
    </delete>

    <update id="updateInFeedChange" parameterType="java.util.List">
        <foreach collection="list" separator=";" item="item" >
            update SMT_MACHINE_MATERIAL_MOUTING T set
            T.REMARK =#{item.remark,jdbcType=VARCHAR},
            T.QTY =#{item.qty,jdbcType=DECIMAL},
            T.LAST_UPDATED_DATE = sysdate,
            T.OBJECT_ID = #{item.objectId,jdbcType=VARCHAR},
            T.NEXT_REEL_ROWID = null
            where T.NEXT_REEL_ROWID = #{item.objectId,jdbcType=VARCHAR}
            and T.OBJECT_ID = #{item.nextReelRowid,jdbcType=VARCHAR}
            and T.ENABLED_FLAG = 'Y'
        </foreach>
    </update>

    <insert id="batchInsertBakInfo" parameterType="com.zte.domain.model.SmtMachineMaterialMouting">
        insert into smt_machine_material_mtbak (MACHINE_MATERIAL_MOUTING_ID, WORK_ORDER, ITEM_CODE, LINE_CODE,
        ITEM_NAME, MACHINE_NO,
        LOCATION_NO, TRACK_NO, FEEDER_NO, OBJECT_ID, QTY, FORWARD, NEXT_REEL_ROWID, REMARK,
        CREATE_DATE, CREATE_USER, LAST_UPDATED_DATE, LAST_UPDATED_BY, ENABLED_FLAG, ORG_ID,
        ENTITY_ID,FACTORY_ID,RAWQTY,MODULE_NO,IS_LEAD,LPN,COMBINATION,AVL,POLAR_INFO,SOURCE_BATCH_CODE,CFG_HEADER_ID,
        WET_LEVEL,ITEM_TYPE)
        select
        <include refid="Base_Column_List"/>
        from smt_machine_material_mouting t
        where 1=1
        and t.enabled_flag = 'Y'
        <if test="workOrder!=null and workOrder!=''">and T.WORK_ORDER = #{workOrder}</if>
        <if test="prodplanId!=null and prodplanId!=''">and T.WORK_ORDER LIKE '${prodplanId}%'</if>
    </insert>

    <insert id="batchInsertReturnInfo" parameterType="com.zte.domain.model.SmtMachineMaterialMouting">
        insert into smt_machine_material_return
        (work_order,
        item_code,
        line_code,
        craft_section,
        process_code,
        machine_no,
        module_no,
        location_no,
        track_no,
        feeder_no,
        object_id,
        qty,
        status,
        remark,
        create_date,
        create_user,
        last_update_date,
        factory_id,
        org_id,
        entity_id,
        machine_material_return_id,
        forward,
        cfg_header_id,
        source_batch_code,
        enabled_flag,
        is_lead,
        wet_level,
        item_type)
        select work_order,
        item_code,
        line_code,
        null craft_section,
        null process_code,
        machine_no,
        module_no,
        location_no,
        track_no,
        feeder_no,
        object_id,
        qty,
        null status,
        remark,
        create_date,
        create_user,
        last_updated_date,
        factory_id,
        org_id,
        entity_id,
        t.machine_material_mouting_id,
        forward,
        cfg_header_id,
        source_batch_code,
        enabled_flag,
        is_lead,
        wet_level,
        item_type
        from smt_machine_material_mouting t
        where 1=1
        and t.enabled_flag = 'Y'
        <if test="workOrder!=null and workOrder!=''">and T.WORK_ORDER = #{workOrder}</if>
        <if test="prodplanId!=null and prodplanId!=''">and T.WORK_ORDER LIKE '${prodplanId}%'</if>
    </insert>

    <insert id="copySameLocationMouting">
        insert into smt_machine_material_mouting(<include refid="Base_Column_List"></include>)
        select gen_random_uuid() machine_material_mouting_id, #{dto.tarWorkOrder}, t.item_code, t.line_code, t.item_name, t.machine_no,
        t.location_no, t.track_no, t.feeder_no, t.object_id, p.ITEM_QTY as qty, t.forward, t.next_reel_rowid, t.remark,
        sysdate, #{dto.empNo}, sysdate, #{dto.empNo}, 'Y', t.org_id,
        t.entity_id,t.factory_id,p.RAW_QTY as rawqty,t.module_no,t.is_lead,t.lpn,t.combination,t.avl,t.polar_info,t.source_batch_code,t.cfg_header_id,
        t.wet_level,t.item_type
        from smt_machine_material_mouting t left join PK_CODE_INFO p  on t.OBJECT_ID = p.PK_CODE
        where machine_material_mouting_id in
        <foreach collection="moutingIds" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </insert>
    <insert id="copyPrepareToMouting">
        INSERT INTO smt_machine_material_mouting
        (machine_material_mouting_id, work_order, item_code, line_code,
        machine_no, location_no,track_no,feeder_no,object_id, qty,
        "forward", remark, create_date, create_user, last_updated_date,
        last_updated_by, enabled_flag, org_id, entity_id,
        factory_id, module_no, rawqty, is_lead, avl, polar_info,
        source_batch_code, cfg_header_id, wet_level, item_type)
        select
        gen_random_uuid() machine_material_mouting_id, #{tarWorkOrder}, item_code, line_code,
        machine_no, location_no, track_no, feeder_no, object_id, qty,
        "forward", remark, sysdate, #{empNo}, sysdate,
        #{empNo}, 'Y', org_id, entity_id,
        factory_id, module_no, qty, is_lead,avl, polar_info,
        source_batch_code, cfg_header_id, wet_level, item_type
        from smt_machine_material_prepare where mtl_prepare_id in
        <foreach collection="prepareIds" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </insert>

    <insert id="copyToPrepareByFeeder">
        INSERT INTO smt_machine_material_prepare
        (mtl_prepare_id, work_order, item_code, line_code,
        machine_no, location_no,track_no,object_id, qty,
        "forward", remark, create_date, create_user, last_updated_date,
        last_updated_by, enabled_flag, org_id, entity_id,
        factory_id, module_no, is_lead, avl, polar_info,
        source_batch_code, cfg_header_id, wet_level, item_type)
        select
        gen_random_uuid() , work_order, item_code, line_code,
        machine_no, location_no, track_no, object_id, qty,
        "forward", remark, sysdate, create_user, sysdate,
        last_updated_by, 'Y', org_id, entity_id,
        factory_id, module_no, is_lead,avl, polar_info,
        source_batch_code, cfg_header_id, wet_level, item_type
        from smt_machine_material_mouting
        where feeder_no = #{feederNo}
    </insert>

    <insert id="copyToPrepareByIds">
        INSERT INTO smt_machine_material_prepare
        (mtl_prepare_id, work_order, item_code, line_code,
        machine_no, location_no,track_no,object_id, qty,
        "forward", remark, create_date, create_user, last_updated_date,
        last_updated_by, enabled_flag, org_id, entity_id,
        factory_id, module_no, is_lead, avl, polar_info,
        source_batch_code, cfg_header_id, wet_level, item_type)
        select
        gen_random_uuid() , work_order, item_code, line_code,
        machine_no, location_no, track_no, object_id, qty,
        "forward", remark, sysdate, create_user, sysdate,
        last_updated_by, 'Y', org_id, entity_id,
        factory_id, module_no, is_lead,avl, polar_info,
        source_batch_code, cfg_header_id, wet_level, item_type
        from smt_machine_material_mouting
        where machine_material_mouting_id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </insert>

    <select id="getAllByLineCode" resultMap="BaseResultMap">
        select * from SMT_MACHINE_MATERIAL_MOUTING t where t.LINE_CODE = #{lineCode} and t.WORK_ORDER = #{taskCode} and
        t.FACTORY_ID = cast(#{factoryId} as numeric) and ENABLED_FLAG='Y'
    </select>

    <update id="deleteAllByLineCode">
        update SMT_MACHINE_MATERIAL_MOUTING set ENABLED_FLAG='N',LAST_UPDATED_DATE=sysdate where LINE_CODE = #{lineCode}
        and WORK_ORDER =
        #{taskCode} and FACTORY_ID = cast(#{factoryId} as numeric)
    </update>

    <select id="selectSmtMachineMaterialMouting" parameterType="com.zte.domain.model.SmtMachineMaterialMouting"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from SMT_MACHINE_MATERIAL_MOUTING
        where ENABLED_FLAG = 'Y'
        AND (LPN = #{lpn,jdbcType=VARCHAR}
        OR NEXT_REEL_ROWID = #{nextReelRowid,jdbcType=VARCHAR})
    </select>

    <select id="getSmtMachineMaterialMoutingList" parameterType="com.zte.interfaces.dto.SmtMachineMaterialMoutingDTO"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from SMT_MACHINE_MATERIAL_MOUTING
        WHERE ENABLED_FLAG = 'Y'  and WORK_ORDER IN (${inWorkOrder})
    </select>

    <select id="getMoutingInfoCount" parameterType="com.zte.domain.model.SmtMachineMaterialMouting"
            resultType="java.lang.Long">
        select count(*)
        from SMT_MACHINE_MATERIAL_MOUTING t
        where 1=1
        and t.ENABLED_FLAG = 'Y'
        <if test="workOrder != null and workOrder != ''">and t.work_order = #{workOrder,jdbcType=VARCHAR}</if>
        <if test="itemCode != null and itemCode != ''">and t.ITEM_CODE = #{itemCode,jdbcType=VARCHAR}</if>
        <if test="workOrder == null or workOrder == ''">and 1=2</if>
    </select>

    <select id="getMoutingInfo" parameterType="com.zte.domain.model.SmtMachineMaterialMouting"
            resultMap="BaseResultMap">
        select t.work_order,t.object_id,t.MACHINE_NO,t.MODULE_NO,t.LOCATION_NO,t.ITEM_CODE,t.FEEDER_NO,
        t.ITEM_NAME,p.item_qty qty,
        p.raw_qty
        from SMT_MACHINE_MATERIAL_MOUTING t
        left join pk_code_info p on t.object_id = p.pk_code
        where 1=1
        and t.ENABLED_FLAG = 'Y'
        <if test="workOrder != null and workOrder != ''">and t.work_order = #{workOrder,jdbcType=VARCHAR}</if>
        <if test="itemCode != null and itemCode != ''">and t.ITEM_CODE = #{itemCode,jdbcType=VARCHAR}</if>
        <if test="workOrder == null or workOrder == ''">and 1=2</if>
        limit #{endRow}::numeric-#{startRow}::numeric+1 offset case when ( #{startRow}::numeric-1 &lt; 0) then 0 else  #{startRow}::numeric-1 end
    </select>

    <update id="deleteSmtMachineMaterialMoutingBatch" parameterType="java.util.List">
        <foreach collection="lpns" separator=";" item="item" >
            <if test="item!=null and item!=''">
                update SMT_MACHINE_MATERIAL_MOUTING set ENABLED_FLAG = 'N'
                where ENABLED_FLAG = 'Y' AND LPN = #{item,jdbcType=VARCHAR}
            </if>
        </foreach>
    </update>

    <delete id="deleteMoutingBatchByObjectId" parameterType="java.util.List">
        <foreach collection="objectIds" separator=";" item="item" >
            <if test="item!=null and item!=''">
                DELETE FROM SMT_MACHINE_MATERIAL_MOUTING
                where ENABLED_FLAG = 'Y' AND OBJECT_ID = #{item}
            </if>
        </foreach>
    </delete>

    <update id="updateNextReelRowId" parameterType="java.util.List">
        <foreach collection="list" separator=";" item="item" >
            update SMT_MACHINE_MATERIAL_MOUTING set NEXT_REEL_ROWID = null
            where ENABLED_FLAG = 'Y' AND NEXT_REEL_ROWID = #{item.nextReelRowid,jdbcType=VARCHAR}
        </foreach>
    </update>

    <select id="getMoutingList" parameterType="java.util.Map" resultMap="BaseResultMap">
        select
        p.ITEM_QTY as QTY,
        t.MACHINE_MATERIAL_MOUTING_ID,
        t.WORK_ORDER,
        t.ITEM_CODE,
        t.LINE_CODE,
        t.ITEM_NAME,
        t.MACHINE_NO,
        t.LOCATION_NO,
        t.TRACK_NO,
        t.FEEDER_NO,
        t.OBJECT_ID,
        t.FORWARD,
        t.NEXT_REEL_ROWID,
        t.REMARK,
        t.CREATE_DATE,
        t.CREATE_USER,
        t.LAST_UPDATED_DATE,
        t.LAST_UPDATED_BY,
        t.ENABLED_FLAG,
        t.FACTORY_ID,
        t.MODULE_NO,
        t.IS_LEAD,
        t.LPN,
        t.COMBINATION,
        t.AVL,
        t.POLAR_INFO,
        t.SOURCE_BATCH_CODE,
        t.CFG_HEADER_ID,
        t.WET_LEVEL,
        t.ITEM_TYPE
        from SMT_MACHINE_MATERIAL_MOUTING t
        left join PK_CODE_INFO p on t.OBJECT_ID = p.PK_CODE
        WHERE t.ENABLED_FLAG = 'Y'
        <if test="inWorkOrder != null and inWorkOrder != ''">and WORK_ORDER IN (${inWorkOrder})</if>
        <if test="inWorkOrder == null or inWorkOrder == ''">and 1=2</if>
    </select>

    <resultMap id="BaseReelIdMap" type="com.zte.domain.vo.EquipmentConsumeReelIdVO">
        <result column="LINE_CODE" jdbcType="VARCHAR" property="lineCode"/>
        <result column="OBJECT_ID" jdbcType="VARCHAR" property="reelId"/>
        <result column="NEXT_REEL_ROWID" jdbcType="VARCHAR" property="nextReelRowid"/>
        <result column="QTY" jdbcType="DECIMAL" property="qty"/>
    </resultMap>

    <!-- 获取在料表中的线体 -->
    <select id="getReelIdUseInfo" resultMap="BaseReelIdMap">
        SELECT S.LINE_CODE,S.OBJECT_ID,S.NEXT_REEL_ROWID,qty FROM SMT_MACHINE_MATERIAL_MOUTING S
        <choose>
            <when test="list != null and list.size !=0">
                WHERE S.OBJECT_ID IN
                <foreach collection="list" item="item" open="(" separator="," close=")">
                    #{item.reelId,jdbcType=VARCHAR}
                </foreach>
            </when>
            <otherwise>
                WHERE 1=2
            </otherwise>
        </choose>
    </select>

    <select id="getNextReelIdUseInfo" resultMap="BaseReelIdMap">
        SELECT S.LINE_CODE,S.OBJECT_ID,S.NEXT_REEL_ROWID FROM SMT_MACHINE_MATERIAL_MOUTING S
        <choose>
            <when test="list != null and list.size > 0">
                WHERE S.NEXT_REEL_ROWID IN
                <foreach collection="list" item="item" open="(" separator="," close=")">
                    #{item.reelId,jdbcType=VARCHAR}
                </foreach>
            </when>
            <otherwise>
                WHERE 1=2
            </otherwise>
        </choose>
    </select>


    <!-- 物理删除旧料在机台在用表的信息 -->
    <delete id="deleteOldNextReelRowId">
        DELETE
        FROM SMT_MACHINE_MATERIAL_MOUTING
        WHERE ENABLED_FLAG = 'Y'
        AND OBJECT_ID = #{reelId,jdbcType=VARCHAR}
    </delete>

    <delete id="deleteByIds">
        DELETE
        FROM SMT_MACHINE_MATERIAL_MOUTING
        WHERE machine_material_mouting_id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteByReelId">
        DELETE
        FROM SMT_MACHINE_MATERIAL_MOUTING
        WHERE object_id = #{reelId} or next_reel_rowid = #{reelId}
    </delete>

    <delete id="deleteByFeederNo">
        DELETE
        FROM SMT_MACHINE_MATERIAL_MOUTING
        WHERE feeder_no = #{feederNo}
    </delete>

    <!--  根据指令号集合批量修改flag-->
    <update id = "updateSmtMoutingEnabledFlag">
        UPDATE SMT_MACHINE_MATERIAL_MOUTING sm
        SET sm.ENABLED_FLAG = 'N' WHERE sm.WORK_ORDER IN ${workOrderNoSet}
    </update>

    <update id="disableSameLocationMouting">
        update smt_machine_material_mouting
        set enabled_flag = 'N',
        last_updated_date = sysdate,
        last_updated_by = #{empNo},
        switched_quickly_flag = 'Y'
        where machine_material_mouting_id in
        <foreach collection="moutingIds" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </update>

    <select id="selectByLineModel" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from SMT_MACHINE_MATERIAL_MOUTING
        where ENABLED_FLAG = 'Y'
        and line_code = #{lineCode}
        and module_no = #{moduleNo}
    </select>

    <select id="selectByLineAndWorkOrderNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from SMT_MACHINE_MATERIAL_MOUTING
        where ENABLED_FLAG = 'Y'
        and line_code = #{lineCode}
        and work_order = #{workOrder}
    </select>

    <select id="getBySmtBomDetail" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from smt_machine_material_mouting m ,
        (
        <foreach collection="bomDetails" item="detail" separator=" union all ">
            select #{detail.itemCode} itemCode, #{detail.locationNo} locationNo
        </foreach>
        ) b
        where m.item_code = b.itemCode and m.location_no = b.locationNo
        and m.line_code = #{dto.lineCode}
        and m.module_no = #{dto.moduleNo}
        and m.work_order = #{dto.curWorkOrder}
    </select>

    <!--  根据id使对应数据生效-->
    <update id = "setMaterialMoutingEnabled" parameterType="com.zte.domain.model.SmtMachineMaterialMouting">
        UPDATE SMT_MACHINE_MATERIAL_MOUTING sm
        SET sm.ENABLED_FLAG = 'Y',
        sm.LAST_UPDATED_DATE = sysdate,
        sm.LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR}
        WHERE sm.MACHINE_MATERIAL_MOUTING_ID = #{machineMaterialMoutingId,jdbcType=VARCHAR}
    </update>

    <update id = "setMaterialMoutingDisenabledbyIdSet" parameterType="java.util.Set">
        UPDATE SMT_MACHINE_MATERIAL_MOUTING sm
        SET sm.ENABLED_FLAG = 'N',
        sm.LAST_UPDATED_DATE = sysdate,
        sm.LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR}
        WHERE sm.ENABLED_FLAG = 'Y'
        <if test="moutingIdSet != null and moutingIdSet.size > 0">
            and sm.MACHINE_MATERIAL_MOUTING_ID in
            <foreach collection="moutingIdSet" item="item" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="moutingIdSet == null or moutingIdSet.size == 0">
            and 1=2
        </if>
    </update>

    <!--  根据id使对应数据生效,并修改feederNo-->
    <update id = "updateFeederAndSetEnabled" parameterType="com.zte.domain.model.SmtMachineMaterialMouting">
        UPDATE SMT_MACHINE_MATERIAL_MOUTING sm
        SET sm.ENABLED_FLAG = 'Y',
        sm.FEEDER_NO = #{feederNo,jdbcType=VARCHAR},
        sm.LAST_UPDATED_DATE = sysdate,
        sm.LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR}
        WHERE sm.MACHINE_MATERIAL_MOUTING_ID = #{machineMaterialMoutingId,jdbcType=VARCHAR}
    </update>

    <!-- 得到feeder插入中满足参数的enabled为Y和N的全部数据-->
    <select id = "getAllByParamsOfFeederInsertion" parameterType="com.zte.domain.model.SmtMachineMaterialMouting" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from SMT_MACHINE_MATERIAL_MOUTING t
        where 1=1
        <if test="lineCode!=null and lineCode!=''">and T.LINE_CODE = #{lineCode}</if>
        <if test="locationNo!=null and locationNo!=''">and T.LOCATION_NO = #{locationNo}</if>
        <if test="moduleNo != null and moduleNo != ''"> and T.MODULE_NO = #{moduleNo,jdbcType=VARCHAR} </if>
        <if test="inObjectId != null and inObjectId != ''"> and T.object_id in (${inObjectId}) </if>
        <if test="(lineCode==null or lineCode=='') and (locationNo==null or locationNo=='')
                    and (moduleNo==null or moduleNo=='') and (inObjectId==null or inObjectId=='')">
            and 1=2
        </if>
    </select>

    <select id="countByFeederNoOrReelId" parameterType="com.zte.interfaces.dto.SmtMachineMaterialPrepareDTO" resultType="int">
        select count(1)
        from SMT_MACHINE_MATERIAL_MOUTING
        where enabled_flag = 'Y'
        <if test="feederNo!=null and feederNo!=''"> and feeder_no = #{feederNo} </if>
        <if test="objectId!=null and objectId!=''"> and object_id = #{objectId} </if>
        <if test="(feederNo==null or feederNo=='') and (objectId==null or objectId=='')"> and 1=2 </if>
    </select>

    <update id="unbindFeederMouting" parameterType="com.zte.interfaces.dto.SmtMachineMaterialPrepareDTO">
        update smt_machine_material_mouting
        set last_updated_date = sysdate,
        <if test="newFeederNo!=null and newFeederNo!=''"> feeder_no = #{newFeederNo} </if>
        <if test="newFeederNo==null or newFeederNo==''"> feeder_no = null </if>
        where enabled_flag = 'N'
        <if test="feederNo!=null and feederNo!=''"> and feeder_no = #{feederNo} </if>
        <if test="objectId!=null and objectId!=''"> and object_id = #{objectId} </if>
        <if test="(feederNo==null or feederNo=='') and (objectId==null or objectId=='')"> and 1=2 </if>
    </update>

    <update id="updateMoutingById" parameterType="com.zte.domain.model.SmtMachineMaterialMouting">
        update SMT_MACHINE_MATERIAL_MOUTING
        <set>
            <trim suffixOverrides=",">
                <if test="itemCode != null">
                    ITEM_CODE = #{itemCode,jdbcType=VARCHAR},
                </if>

                <if test="itemName != null">
                    ITEM_NAME = #{itemName,jdbcType=VARCHAR},
                </if>

                <if test="objectId != null">
                    OBJECT_ID = #{objectId,jdbcType=VARCHAR},
                </if>

                <if test="qty != null">
                    QTY = #{qty,jdbcType=DECIMAL},
                </if>

                <if test="forward != null">
                    FORWARD = #{forward,jdbcType=VARCHAR},
                </if>
                NEXT_REEL_ROWID = null,
                <if test="createDate != null">
                    CREATE_DATE= #{createDate,jdbcType=TIMESTAMP},
                </if>

                <if test="createUser != null">
                    CREATE_USER = #{createUser,jdbcType=VARCHAR},
                </if>

                <if test="lastUpdatedDate != null">
                    LAST_UPDATED_DATE= #{lastUpdatedDate,jdbcType=TIMESTAMP},
                </if>

                <if test="lastUpdatedBy != null">
                    LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR},
                </if>

                <if test="rawQty != null">
                    RAWQTY = #{rawQty,jdbcType=DECIMAL},
                </if>

                <if test="isLead != null">
                    IS_LEAD = #{isLead,jdbcType=VARCHAR},
                </if>

                <if test="avl != null">
                    AVL = #{avl,jdbcType=VARCHAR},
                </if>

                <if test="sourceBatchCode != null">
                    SOURCE_BATCH_CODE = #{sourceBatchCode,jdbcType=VARCHAR},
                </if>
            </trim>
        </set>
        where machine_material_mouting_id = #{machineMaterialMoutingId,jdbcType=VARCHAR}
    </update>

    <select id="countLocationMt" parameterType="com.zte.interfaces.dto.PDATransferScanDTO" resultType="java.lang.Integer">
        select count(1) from SMT_MACHINE_MATERIAL_MOUTING
        where ENABLED_FLAG = 'Y'
        and line_code = #{lineCode}
        and module_no = #{moduleNo}
        and location_no = #{drLocationNo}
    </select>

    <select id="getMoutingWithPkCode" resultType="com.zte.domain.model.SmtMachineMaterialMouting">
        select machine_material_mouting_id, p.RAW_QTY, p.ITEM_QTY qty, t.WORK_ORDER,
        t.ITEM_CODE, t.LINE_CODE, t.ITEM_NAME, t.MACHINE_NO,
        t.LOCATION_NO, t.TRACK_NO, t.FEEDER_NO, t.OBJECT_ID, t.FORWARD, t.NEXT_REEL_ROWID, t.REMARK,
        t.CREATE_DATE, t.CREATE_USER,
        t.ENABLED_FLAG, t.MODULE_NO, t.IS_LEAD,t.LPN, t.AVL, t.POLAR_INFO, t.SOURCE_BATCH_CODE
        from SMT_MACHINE_MATERIAL_MOUTING t, PK_CODE_INFO p
        where t.OBJECT_ID = p.PK_CODE
        and t.LINE_CODE = #{lineCode}
        and t.WORK_ORDER = #{workOrderNo}
        <if test="machineNoList != null and machineNoList.size() > 0">
            and t.MACHINE_NO in
            <foreach collection="machineNoList" item="item" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        limit 10000
    </select>
</mapper>
