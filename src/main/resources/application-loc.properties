#======================
#@title:??????????????
#@desc:
#	1?????????????? ????.properteis ????????????
#	2????????????http://devconfig.test.zte.com.cn/it-config-server
#	3??????????????????????????
#	4?maven?????????????
#@???
#	zte-itp-isoa-frame??????????????????????????????????
#	http://devconfig.test.zte.com.cn/zte-itp-isoa/zte-itp-isoa/blob/zte-itp-isoa/v1.0.1/zte-itp-isoa-frame.yml
#======================
spring.druid.allow =
#*****************************************************
#MSB??????
#*****************************************************

#??msb??????????????MSB? #msrtest.zte.com.cn:10081
servicecenter = msb
eureka.client.enabled = false
register.address = ************:10081
#????,???????,?:api.prm.zte.com.cn.user,api.ichannel.zte.com.cn.order
#??????????????????????IP?
register.node.ipport = imes.dev.zte.com.cn:80
#?????
register.serviceName = ${spring.application.name}

#???????????100MB????????????-1???????
spring.servlet.multipart.max-file-size = 100MB
spring.servlet.multipart.max-request-size = 200MB


#*****************************************************
# ???????????
#*****************************************************
common.exception.log.enabled = false
common.exception.log.always =
common.auth.enabled = true
common.auth.secretKey = KENC(puN73khQxsQtzB0mZ1Edt7a7UajqU/M0xWlvgh39xgDZXPZmzl5h2OC+0wmjkkxKZq/A+Q==$$/rXr65FOpJSEb2MG)
common.auth.ignore.emp = 0668000743,0668001145,0668001246,00286523,10313234,0668000830,00286569,10317937,10309565,0668000851,10350563,0668001147,10351947,0668000782,00339259,10337580,10349620,10351836
common.auth.ignore.path = /info,/inforInquery/queryProdBoardList
common.auth.ignore.ip = *************,************,************,*************,*************,*************


#*****************************************************
#????
#*****************************************************
common.tokenMap[tokenVerifyUrl] = http://mdmtest.zte.com.cn:8888/uactoken/auth/token/verify.serv

#?????? test
common.otherMap[email_url] = http://*********:80/api
#?????? ??
#common.otherMap[email_url]= http://msr.zte.com.cn/api
#????????
center.interface.address = http://imes.dev.zte.com.cn


#*****************************************************
#?????
#*****************************************************

#=======================================jdbc1=====================================
### druid setting ###
jdbc1.type = com.alibaba.druid.pool.DruidDataSource
jdbc1.url = **********************************************
jdbc1.username = KENC(7+Xjcnzau/1iCUv6r0Jveq2P+KBkQSvX$$9YaVCStxhdgjac9x)
jdbc1.password = KENC(UIg1bCYcSGfQyuP6xzHmYk7fcdiuTcB+d1OLfg==$$8uVsVL7d+m/Xf4/5)
jdbc1.driverClassName = oracle.jdbc.driver.OracleDriver
#=======================================jdbc2=====================================
### druid setting ###
jdbc2.type = com.alibaba.druid.pool.DruidDataSource
jdbc2.url = **********************************************
jdbc2.username = KENC(dgvNpzml78Q/9URJ8cSUIJwuFmJFLv1zJg==$$Xi7wrW2H95O4bkaI)
jdbc2.password = KENC(h3bPHuCPm87FRz9fEVbq8YEsLnCIssFJpwGelJf4$$y4xiVrxG93TwX+ED)
jdbc2.driverClassName = oracle.jdbc.driver.OracleDriver

#=======================================jdbc3=====================================
### druid setting  SFC??????###
jdbc3.type = com.alibaba.druid.pool.DruidDataSource
jdbc3.url = **********************************************
jdbc3.username = KENC(YDnBKZlsWHydMb8+Yh5A0dHQIw==$$zBqY8IGZsnfGVltV)
jdbc3.password = KENC(z/HyqsnOrehm/e1YDTnpoEmyR1MmBlA=$$VSsO0KkSuXdXSzYR)
jdbc3.driverClassName = oracle.jdbc.driver.OracleDriver


#=======================================MES====================================
### druid setting  MES??????###
jdbc4.type = com.alibaba.druid.pool.DruidDataSource
jdbc4.url = **********************************************
jdbc4.username = KENC(gT3Tybqw2vvR3U9HQzJzr5pMzYI=$$Z5STHt4H4zbyGkVl)
jdbc4.password = KENC(MAWBp23aM8BzA4h/HeciiTigJ4sBNhh+$$iiIZ4piAgH1g4bSz)
jdbc4.driverClassName = oracle.jdbc.driver.OracleDriver

#=======================================jdbc5=====================================
### druid setting ###
jdbc5.type = com.alibaba.druid.pool.DruidDataSource
jdbc5.url = **********************************************
jdbc5.username = KENC(F6aeMZZ6Fc7sY7KoAK9kyATHiyL+ruY=$$5ojkHPV3J23sAIvq)
jdbc5.password = KENC(Cb5HaEMpvxVwotqyJExnH3VuicEfH3U=$$6dRLYIpzj+0sorjV)
jdbc5.driverClassName = oracle.jdbc.driver.OracleDriver

#=======================================jdbc??=====================================
### druid setting ###
jdbcXinSong.type = com.alibaba.druid.pool.DruidDataSource
jdbcXinSong.url = *********************************************
jdbcXinSong.username = KENC(l9AafqS175JWxIIXVN9P8P1mJkwvrSE=$$jErYfHGy2W4X06J/)
jdbcXinSong.password = KENC(Cb5HaEMpvxVwotqyJExnH3VuicEfH3U=$$6dRLYIpzj+0sorjV)
jdbcXinSong.driverClassName = oracle.jdbc.driver.OracleDriver

#=======================================infor=====================================
### druid setting ?????????###
jdbc.infor.type = com.alibaba.druid.pool.DruidDataSource
jdbc.infor.url = ********************************************
jdbc.infor.username = KENC(at+FBnxyL+wyuF4s16Y8mR63LINnpQ==$$43wpQfNacVgMLFjX)
jdbc.infor.password = KENC(tB14IvQKzmwQhhekwLFqDsoZ3g4oB8UMvM0=$$iTtXCkNIgAkjr62Y)
jdbc.infor.driverClassName = oracle.jdbc.driver.OracleDriver

#=======================================jdbc WMESTEST=====================================
### druid setting ###
jdbcWmes.type = com.alibaba.druid.pool.DruidDataSource
jdbcWmes.url = **********************************************
jdbcWmes.username = KENC(G+3hAXx8m70pUOZ2Atq9agHzuzQ=$$z///VIq8kCvo48WM)
jdbcWmes.password = KENC(FXGpzC43gLcc2LKQty5wvkSQV73Wab+2$$Ogaqhw+XpbQNVRwe)
jdbcWmes.driverClassName = oracle.jdbc.driver.OracleDriver


#=======================================WMSPROD====================================
### druid setting  ###
jdbc.applms.type = com.alibaba.druid.pool.DruidDataSource

### MES ??????
jdbc.applms.url = **********************************************
jdbc.applms.username = KENC(VQ2nUu/rj8vTqRRhnwKlzX6lOBnQYHs=$$XGzsE6eHB14nqdQ0)
jdbc.applms.password = KENC(Cb5HaEMpvxVwotqyJExnH3VuicEfH3U=$$6dRLYIpzj+0sorjV)
#-----------
#jdbc.applms.url = *********************************************
#jdbc.applms.username = SfgnMObRSBrqb1MHnCoGqPeJ6gSbIixlOeRi4lMxywZRxD92Hg==
#jdbc.applms.password = 3ZuCFxrM8vnVl/qMA0TMQtKcviAn2wyBFZp2faeKZlwV1Q0=
jdbc.applms.driverClassName = oracle.jdbc.driver.OracleDriver


#=======================================SPM????????=====================================
### druid setting ###
jdbceai.type = com.alibaba.druid.pool.DruidDataSource
jdbceai.url = **********************************************
jdbceai.username = KENC(F6aeMZZ6Fc7sY7KoAK9kyATHiyL+ruY=$$5ojkHPV3J23sAIvq)
jdbceai.password = KENC(Cb5HaEMpvxVwotqyJExnH3VuicEfH3U=$$6dRLYIpzj+0sorjV)
jdbceai.driverClassName = oracle.jdbc.driver.OracleDriver


#=======================================MES_SFC???====================================
### ????? SFC ??????###
jdbc.sfc.type = com.alibaba.druid.pool.DruidDataSource
jdbc.sfc.url = **********************************************
jdbc.sfc.username = KENC(YDnBKZlsWHydMb8+Yh5A0dHQIw==$$zBqY8IGZsnfGVltV)
jdbc.sfc.password = KENC(z/HyqsnOrehm/e1YDTnpoEmyR1MmBlA=$$VSsO0KkSuXdXSzYR)
jdbc.sfc.driverClassName = oracle.jdbc.driver.OracleDriver

#=======================================ERP=====================================
### druid setting ###
jdbcerp.type = com.alibaba.druid.pool.DruidDataSource
jdbcerp.url = **********************************************
jdbcerp.username = KENC(gT3Tybqw2vvR3U9HQzJzr5pMzYI=$$Z5STHt4H4zbyGkVl)
jdbcerp.password = KENC(+08AgB2EmNKVkVYTTL/aKlPrA6LP8+2xqQXcfA4=$$QqsMr87EN29ICd+R)
jdbcerp.driverClassName = oracle.jdbc.driver.OracleDriver

#=======================================ERP=====================================
### druid setting ###
jdbcerp1.type = com.alibaba.druid.pool.DruidDataSource
jdbcerp1.url = **********************************************
jdbcerp1.username = KENC(gT3Tybqw2vvR3U9HQzJzr5pMzYI=$$Z5STHt4H4zbyGkVl)
jdbcerp1.password = KENC(+08AgB2EmNKVkVYTTL/aKlPrA6LP8+2xqQXcfA4=$$QqsMr87EN29ICd+R)
jdbcerp1.driverClassName = oracle.jdbc.driver.OracleDriver

#=======================================MES-BARCODE=====================================
### druid setting ###
jdbc.barcode.type = com.alibaba.druid.pool.DruidDataSource
jdbc.barcode.url = ********************************************
jdbc.barcode.username = KENC(21R6N8zOrERKaKAEgHQiPVLX1mYPCBY=$$MDHUAsQtGZN1t4nF)
jdbc.barcode.password = KENC(8V53WzE/6rQ8yuNFrRDQ+1TwRK128/c=$$uawbv7sJpGVQABrT)
jdbc.barcode.driverClassName = oracle.jdbc.OracleDriver

#*****************************************************
#KAFKA??
#*****************************************************
spring.kafka.bootstrapServers[0] = **********:9092
#message.server=http://**********:8080/zte-itp-msp-message/v1
#??????--???????
message.server = http://msptest.zte.com.cn:8888/zte-itp-msp-message/v1


#*****************************************************
# 2020-09-30 ??????????????redis??
#*****************************************************
#?????????

msa.ccs.enable = true
#??????????????????
msa.ccs.resourceCode = mesdev
#????????url?????????????3.2.10.RELEASE????????
#??????UAT?????????redis??????????????????UAT????redis????????UAT?????????
#?????UAT?????????????
cacheCloud.server = http://mspuat.zte.com.cn:8888/zte-itp-msp-cachecloud/
#????;??
msa.redis.serializerType = genericJackson2Json


# ?????????????????
board.predictout.recipients = <EMAIL>

#??????????????????
ma.mso.limit = 10
#???cron?????????????????
ma.mso.task.cron = 0/5 * * * * ?
#???????????????????
ma.mso.dbType = WMES

#EMS??
#????EMS
dtEms.agent.enable = true
#DT???????
dtEms.agent.serverUrl = *************:9091

# ??/??swgger  ????????false??swagger
springfox.documentation.swagger-ui.enabled = true


#*****************************************************
# ?????????
#*****************************************************
ucs.sdk.mode = ucstoken
ucs.sdk.issuer = https://test55.ucs.zte.com.cn/zte-bmt-ucs-portalbff/oidc/10001
ucs.sdk.clientId = 30014
ucs.sdk.secretKey = KENC(gr2UtOsvnSwLi444srf8ye1D+MrJwVK/NqBEMzWvaI7+0F6kwj13Yh16Ho45wkbdty/RqRsLugoV2v6VJxFdZbeJvg3SNdgfaX7luIhOeuM=$$0YBlDPZ/KqM4B57z)
ucs.sdk.baseUrl = https://test.imes.zte.com.cn/zte-mes-manufactureshare-centerfactory
ucs.sdk.scope = openid
# redis?memory???memory????? redis???????????????????? redis ?????????????????? memory????????
ucs.sdk.store = redis
ucs.sdk.checkState = true
ucs.sdk.ucstoken.authFrom = header
#??/??token??
ucs.sdk.ucstoken.forceCheck = true
#???????
ucs.auth.whiteList = system,10260525,10307315,10296137,00286523,10313234,10328274,10270446,10274973,00236517,00286569,00236785,10275508,00260524,10266925,10321587
ucs.auth.exclusionsPath = /info,/testResultMergeController/testRecordUpload,/swagger-ui/**,/swagger-resources/**,/v3/api-docs,/webjars/**,/swagger.json,/swagger-ui.html

#???
cloudDiskSDK.host = https://idrive.test.zte.com.cn/zte-km-cloududm-clouddisk

cloudDiskSDK.httpClientLogSwitch = true

cloudDiskSDK.xOrgId = MES_ACCESS_KEY

cloudDiskSDK.xOriginServiceName = MES

cloudDiskSDK.xSecretKey = KENC(HT7vBw53L+coihTv1T2j2CW+EvMTYkit$$vS6pK/R9o+nqNJLR)
imes.interface.log.db.Path = /boardFirstwarehouse/getCountByFirstwarehouseBj
sn.write.spm = false
ignore.log.list = ??
mybatis.configuration.maxLimit = -1

spm.offline.validatesubmitstatus = Y
spm.offline.listunitbasicdata = Y
spm.offline.binding = Y
spm.offline.prodPlanFlag = Y


#????
archive.file.empNo = admin
archive.file.cloudServiceName = MES
archive.file.cloudAccessKey = KENC(Em28tVzO26mZvMRTiw12Qp1bgi12JE0ZKWhh$$hBTBmcfvBhCLIIHq)
archive.file.cloudSecretKey = KENC(KeIku62ZZJoOWa9+wBWZLsBYX4Ic4XGAKb9nfiMQYUSBtoPh0yXPcUyxS41dkYsY$$BAJY5uQ9KXCRdhMW)
archive.file.url = http://test.idrive.zte.com.cn
archive.file.mapping = 3155
archive.file.communicationCode = F6A34B64C3C08999


# ????
# ????????????(??)?LocalTaskServerClient???????????????
gei.taskServerClient = RedisTaskServerClient
# ??????(??)?LocalFileStore ??? CloudUdmFileStore ???
gei.fileStoreType = CloudUdmFileStore
# ??????(??). IMESMainTaskCallback?????????????
gei.mainTaskCallback = IMESMainTaskCallback
# ???????????(??)
gei.directExportEnable = true
# ??????????5W (??)
get.directExportThreshold = 50000
archive.ftp.host = *************
archive.ftp.port = 22
archive.ftp.username = KENC(zbqdEsfteYPKKdaSisXJjbTm1dsWEA==$$TcVBKlyJ72rciBMD)
archive.ftp.password = KENC(yyYqOyvUSbF//OmdU8NI7ea1ei+4g6iCHg==$$iYY8p4qcxtptxr2g)



default.x.auth.value = 7V4nDoytYqGGPEPIhQP3tIsjRXp72Da0
management.endpoints.web.exposure.include = info,health,metrics

springfox.documentation.auto-startup = true
springfox.documentation.enabled = true

msa.druid.web.enable = false
common.entity.id = 2


msa.rootkey.salt = fghijklmabcden
msa.rootkey.factor1 = imes_dev
msa.encrypt.datakey = X/e5NXCAIwEWdzUAY2+yH3mL5dx4fB2OcDmvZgkCbQn3Ap6VJqzIibfdYctZEdQkYhaaJ5AcNlovPWZZi65wFA==
msa.ccs.encrypt.factor = KENC(JU4tp5+g/mosLZMNKfNAXD5Z8SWpWg6VV1yigWMVHfxq+pASaVCk1Q==$$f7WJv0Z39JqZIkWI)
message.inoneAppCode = imes
feedback.of.production.station.testing.information.url = https://mdsdev.zte.com.cn:29001/api/mds-web-outservice/v1/api/rest/byte/meituan_queryStationTestInfo
feedback.of.production.station.testing.generateFile.url = https://mdsdev.zte.com.cn:29001/api/mds-web-outservice/v1/api/rest/byte/meituan_queryStationLogInfoNew
redis.mode = 4

###########KMS??###########
msa.security.server.dt-kms.enable = true
msa.security.server.dt-kms.appName = ENC(yR34+KOFhLgtUi3hi532XtcSxrFaAAkffGNnT6mGsgLgg2SGFEp3gP8=)
msa.security.server.dt-kms.appSecret = ENC(eQs7gM7ifjapqR9tVPLWn5EvuDCkUV/x0g6Cp7MLxnZhK902Ub7ZrPUTBA==)
msa.security.server.dt-kms.userId = ENC(wBo+diTsiWzg225PNHEosOwyyaTRBVpr9mX18+bVCTS8ZV0kq4Xxb3HOY30=)
msa.security.server.dt-kms.endpoint = kms-msp.test.zte.com.cn
## = ZteSecurity?????????????isoa-security?dt-kms????isoa-security
msa.security.client.zteSecurity.serverType = dt-kms
### = ???????dt-kms???????true??zteSecurity?????kms?????????????????isoa-security???????
msa.security.client.zteSecurity.decryptCompatibleEnable = true
#### = ?????????????????????
msa.security.server.dt-kms.defaultMasterKey = imesKeys
msa.security.server.dt-kms.masterKeys.imesKeys.secretKeyType = zxiscp:imes300:imesweb:general
##### = ???????imesKeys????????????????????
msa.security.server.dt-kms.masterKeys.imesKeys.defaultDataKey = general
msa.security.server.dt-kms.masterKeys.imesKeys.dataKeys.general = NmU3YTE5ZjlmYzI2YTQwMWI4OGMyM2Y5ZWY2ZDU3M2UxYzRhYmNlNWRiZDk5MDZkYmM0MGMwYmJlMzY2YzhiOTkzZDUzM2VjZDA2Yzk5YzgwYjAyNGFhMDUwYjVmODJh$$NWJhNjYyZmQ0YzVkNzJlMzhlM2ZkYTQ3ODE3MmRjNzY=&&MGY2Zjc3ZmItNGQ1Yi00NzQ0LWFjZTctOTgzYzU0MjM5YjE5
imes.interface.log.path = /zmsIndicatorUpload/updateMesInfoUploadLog
server.port = 8080
spring.mail.username = <EMAIL>
inone.mes.app.code = KENC(5iOrmMS5b+TDrbrWNaieUze4fNLxjYvA1Pqx49l+53AKrK9jcgQ57++1bfHCXHTp$$GDpRxAS7LdrRXwv9)
inone.base.url = https://icosg.test.zte.com.cn
icms.inone.mes.xInterfaceKey = KENC(+DjI5gSB3onsUU7/NiWHBNdNPWM/$$wqutR+V1pojgOij6)
