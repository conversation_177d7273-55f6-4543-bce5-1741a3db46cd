<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.PmRepairDetailRepository">
  <resultMap id="BaseResultMap" type="com.zte.domain.model.PmRepairDetail">
    <id column="REPAIR_DETAIL_ID" jdbcType="VARCHAR" property="repairDetailId" />
    <result column="REPAIR_ID" jdbcType="VARCHAR" property="repairId" />
    <result column="ERROR_CODE" jdbcType="VARCHAR" property="errorCode" />
    <result column="ERROR_DESCRIPTION" jdbcType="VARCHAR" property="errorDescription" />
    <result column="REASON_CODE" jdbcType="VARCHAR" property="reasonCode" />
    <result column="REASON_DESCRIPTION" jdbcType="VARCHAR" property="reasonDescription" />
    <result column="LOCATION_NO" jdbcType="VARCHAR" property="locationNo" />  

    <result column="OBJECT_ID" jdbcType="VARCHAR" property="objectId" />
    <result column="ENTITY_TYPE" jdbcType="VARCHAR" property="entityType" />
    <result column="REPAIR_METHOD" jdbcType="VARCHAR" property="repairMethod" />
    <result column="ADVERSE_TYPE" jdbcType="VARCHAR" property="adverseType" />
    <result column="APPROVER" jdbcType="VARCHAR" property="approver" />
    <result column="EXCHANGE_SN" jdbcType="VARCHAR" property="exchangeSn" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="OLD_ENTITY_TYPE" jdbcType="VARCHAR" property="oldEntityType" />
   
    <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="LAST_UPDATED_DATE" jdbcType="TIMESTAMP" property="lastUpdatedDate" />
    <result column="LAST_UPDATED_BY" jdbcType="VARCHAR" property="lastUpdatedBy" />
    <result column="ENABLED_FLAG" jdbcType="VARCHAR" property="enabledFlag" />
    <result column="ORG_ID" jdbcType="DECIMAL" property="orgId" />
    <result column="FACTORY_ID" jdbcType="DECIMAL" property="factoryId" />
    <result column="ENTITY_ID" jdbcType="DECIMAL" property="entityId" />

  </resultMap>

    <resultMap id="BaseResultMapDto" type="com.zte.interfaces.dto.PmRepairDetailDTO">
        <id column="REPAIR_DETAIL_ID" jdbcType="VARCHAR" property="repairDetailId" />
        <result column="REPAIR_ID" jdbcType="VARCHAR" property="repairId" />
        <result column="ERROR_CODE" jdbcType="VARCHAR" property="errorCode" />
        <result column="ERROR_DESCRIPTION" jdbcType="VARCHAR" property="errorDescription" />
        <result column="REASON_CODE" jdbcType="VARCHAR" property="reasonCode" />
        <result column="REASON_DESCRIPTION" jdbcType="VARCHAR" property="reasonDescription" />
        <result column="LOCATION_NO" jdbcType="VARCHAR" property="locationNo" />

        <result column="OBJECT_ID" jdbcType="VARCHAR" property="objectId" />
        <result column="ENTITY_TYPE" jdbcType="VARCHAR" property="entityType" />
        <result column="REPAIR_METHOD" jdbcType="VARCHAR" property="repairMethod" />
        <result column="ADVERSE_TYPE" jdbcType="VARCHAR" property="adverseType" />
        <result column="APPROVER" jdbcType="VARCHAR" property="approver" />
        <result column="EXCHANGE_SN" jdbcType="VARCHAR" property="exchangeSn" />
        <result column="REMARK" jdbcType="VARCHAR" property="remark" />
        <result column="OLD_ENTITY_TYPE" jdbcType="VARCHAR" property="oldEntityType" />

        <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
        <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
        <result column="LAST_UPDATED_DATE" jdbcType="TIMESTAMP" property="lastUpdatedDate" />
        <result column="LAST_UPDATED_BY" jdbcType="VARCHAR" property="lastUpdatedBy" />
        <result column="ENABLED_FLAG" jdbcType="VARCHAR" property="enabledFlag" />
        <result column="ORG_ID" jdbcType="DECIMAL" property="orgId" />
        <result column="FACTORY_ID" jdbcType="DECIMAL" property="factoryId" />
        <result column="ENTITY_ID" jdbcType="DECIMAL" property="entityId" />

        <result column="REPAIR_PRODUCT_TYPE" jdbcType="VARCHAR" property="repairProductType" />
        <result column="REPAIR_PRODUCT_STYPE" jdbcType="VARCHAR" property="repairProductStype" />
        <result column="REPAIR_PRODUCT_MSTYPE" jdbcType="VARCHAR" property="repairProductMstype" />
        <result column="IS_SUB" jdbcType="VARCHAR" property="isSub" />
        <result column="SUB_ITEM_SN" jdbcType="VARCHAR" property="subItemSn" />

        <result column="SUB_ITEM_CODE" jdbcType="VARCHAR" property="subItemCode" />
        <result column="SUN_ITEM_NAME" jdbcType="VARCHAR" property="sunItemName" />
        <result column="IS_LOCATION_NO" jdbcType="VARCHAR" property="isLocationNo" />
        <result column="SN" jdbcType="VARCHAR" property="sn" />
        <result column="STYLE" jdbcType="VARCHAR" property="style" />

        <result column="BG_BRAND_NO" jdbcType="VARCHAR" property="bgBrandNo" />
        <result column="SUPPLIER_NAME" jdbcType="VARCHAR" property="supplierName" />
        <result column="IS_CONTROL" jdbcType="VARCHAR" property="isControl" />
        <result column="REPLACE_SN" jdbcType="VARCHAR" property="replaceSn" />
        <result column="REPLACE_ITEM_CODE" jdbcType="VARCHAR" property="replaceItemCode" />

        <result column="REPLACE_BRAND" jdbcType="VARCHAR" property="replaceBrand" />
        <result column="REPLACE_SUPPLIER" jdbcType="VARCHAR" property="replaceSupplier" />
        <result column="REPLACE_STYLE" jdbcType="VARCHAR" property="replaceStyle" />
        <result column="REPAIR_BY" jdbcType="VARCHAR" property="repairBy" />
        <result column="REPAIR_DEPATRMENT" jdbcType="VARCHAR" property="repairDepatrment" />

        <result column="REPAIR_DATE" jdbcType="TIMESTAMP" property="repairDate" />
        <result column="REPAIR_DONE_DATE" jdbcType="TIMESTAMP" property="repairDoneDate" />

        <result column="RESULT" jdbcType="VARCHAR" property="result" />
        <result column="RECEPTION_DETAIL_ID" jdbcType="VARCHAR" property="receptionDetailId" />
        <result column="ITEM_CODE" jdbcType="VARCHAR" property="itemCode" />
        <result column="ITEM_NAME" jdbcType="VARCHAR" property="itemName" />
        <result column="ITEM_SN" jdbcType="VARCHAR" property="itemSn" />
        <result column="REPAIR_PROCESS" jdbcType="VARCHAR" property="repairProcess" />
        <result column="repair_team" jdbcType="VARCHAR" property="repairTeam" />
        <result column="status" jdbcType="VARCHAR" property="status" />

        <!--送修单信息-->
        <result column="DELIVERY_NO" jdbcType="VARCHAR" property="deliveryNo" />


    </resultMap>
  <!--mybatis引用： 表名 -->
  <sql id="Table_Name">
    PM_REPAIR_DETAIL
  </sql>
  <sql id="Base_Column_List">
    REPAIR_DETAIL_ID, REPAIR_ID, ERROR_CODE, ERROR_DESCRIPTION, REASON_CODE, REASON_DESCRIPTION, 
    LOCATION_NO,  ERROR_DESCRIPTION,  OBJECT_ID, ENTITY_TYPE, REPAIR_METHOD, ADVERSE_TYPE, APPROVER,EXCHANGE_SN
    REMARK, OLD_ENTITY_TYPE, OLD_ENTITY_TYPE, CREATE_DATE, CREATE_BY, LAST_UPDATED_DATE, 
    LAST_UPDATED_BY, ENABLED_FLAG, ORG_ID, FACTORY_ID, ENTITY_ID
  </sql>

  <!--mybatis引用： 基础列筛选结构 -->
  <sql id="Base_Column_Filter_List">
    <if test="remark != null">REMARK,</if>
    <if test="createBy != null">CREATE_BY,</if>
    <if test="lastUpdatedBy != null">LAST_UPDATED_BY,</if>
    <if test="orgId != null">ORG_ID,</if>
    <if test="factoryId != null">FACTORY_ID,</if>
    <if test="entityId != null">ENTITY_ID,</if> 
    ENABLED_FLAG,CREATE_DATE, LAST_UPDATED_DATE
  </sql>
  <!--mybatis引用： 基础字段筛选结构 -->
  <sql id="Base_Filed_Filter_List">    
      <if test="remark != null">#{remark,jdbcType=VARCHAR},</if>
      <if test="createBy != null">#{createBy,jdbcType=VARCHAR},</if>
      <if test="lastUpdatedBy != null">#{lastUpdatedBy,jdbcType=VARCHAR},</if>
      <if test="orgId != null">#{orgId,jdbcType=DECIMAL},</if>
      <if test="factoryId != null">#{factoryId,jdbcType=DECIMAL},</if>      
      <if test="entityId != null">#{entityId,jdbcType=DECIMAL},</if>   
      'Y',SYSDATE,SYSDATE
  </sql>  
  <sql id="Base_Condtions">    
    <include refid="Table_Name" />.ENABLED_FLAG = 'Y' 
    <if test="repairDetailId != null and repairDetailId != ''"> AND <include refid="Table_Name" />.REPAIR_DETAIL_ID = #{repairDetailId}</if>
  </sql>
  
  <!--mybatis引用： 列筛选结构 -->
  <sql id="Column_Filter_List">
    <if test="repairDetailId != null">REPAIR_DETAIL_ID,</if>
    <if test="repairId != null">REPAIR_ID,</if>
    <if test="errorCode != null">ERROR_CODE,</if>
    <if test="errorDescription != null">ERROR_DESCRIPTION,</if>      
    <if test="reasonCode != null">REASON_CODE,</if>
    <if test="reasonDescription != null">REASON_DESCRIPTION,</if>
    <if test="locationNo != null">LOCATION_NO,</if>
    <if test="prodplanId != null">PRODPLAN_ID,</if>
    <if test="itemCode != null">ITEM_CODE,</if>   
    <if test="itemName != null">ITEM_NAME,</if>
    <if test="objectId != null">OBJECT_ID,</if>
    <if test="entityType != null">ENTITY_TYPE,</if>
    <if test="repairMethod != null">REPAIR_METHOD,</if>
    <if test="adverseType != null">ADVERSE_TYPE,</if>
    <if test="approver != null">APPROVER,</if>
    <if test="exchangeSn != null">EXCHANGE_SN,</if>
    <if test="oldEntityType != null">OLD_ENTITY_TYPE,</if>
    <include refid="Base_Column_Filter_List" />
  </sql>
  <!--mybatis引用： 字段筛选结构 -->
  <sql id="Filed_Filter_List">
    <if test="repairDetailId != null">#{repairDetailId,jdbcType=VARCHAR},</if>
    <if test="repairId != null">#{repairId,jdbcType=DECIMAL},</if>
    <if test="errorCode != null">#{errorCode,jdbcType=VARCHAR},</if>
    <if test="errorDescription != null">#{errorDescription,jdbcType=VARCHAR},</if>      
    <if test="reasonCode != null">#{reasonCode,jdbcType=DECIMAL},</if>
    <if test="reasonDescription != null">#{reasonDescription,jdbcType=VARCHAR},</if>
    <if test="locationNo != null">#{locationNo,jdbcType=VARCHAR},</if>
    <if test="prodplanId != null">#{prodplanId,jdbcType=VARCHAR},</if>
    <if test="itemCode != null">#{itemCode,jdbcType=VARCHAR},</if>   
    <if test="itemName != null">#{itemName,jdbcType=VARCHAR},</if>
    <if test="objectId != null">#{objectId,jdbcType=VARCHAR},</if>
    <if test="entityType != null">#{entityType,jdbcType=VARCHAR},</if>
    <if test="repairMethod != null">#{repairMethod,jdbcType=VARCHAR},</if>
    <if test="adverseType != null">#{adverseType,jdbcType=VARCHAR},</if>
    <if test="approver != null">#{approver,jdbcType=VARCHAR},</if>
    <if test="oldEntityType != null">#{oldEntityType,jdbcType=VARCHAR},</if>
    <include refid="Base_Filed_Filter_List" />
  </sql>
  <!--mybatis引用： 列字段筛选结构 -->
  <sql id="Column_Filed_List">  
    <if test="repairId != null">REPAIR_ID = #{repairId,jdbcType=DECIMAL},</if>
    <if test="errorCode != null">ERROR_CODE = #{errorCode,jdbcType=VARCHAR},</if>
    <if test="errorDescription != null">ERROR_DESCRIPTION = #{errorDescription,jdbcType=VARCHAR},</if>      
    <if test="reasonCode != null">REASON_CODE = #{reasonCode,jdbcType=DECIMAL},</if>      
    <if test="reasonDescription != null">REASON_DESCRIPTION = #{reasonDescription,jdbcType=VARCHAR},</if>    
    
    <if test="locationNo != null">PRODPLAN_ID = #{locationNo,jdbcType=DECIMAL},</if>
    <if test="prodplanId != null">ITEM_CODE = #{prodplanId,jdbcType=VARCHAR},</if>
    <if test="itemCode != null">WORK_STATION = #{itemCode,jdbcType=VARCHAR},</if>   
    <if test="itemName != null">OBJECT_ID = #{itemName,jdbcType=VARCHAR},</if>
    <if test="objectId != null">ENTITY_TYPE = #{objectId,jdbcType=VARCHAR},</if>
    <if test="entityType != null">REMARK = #{entityType,jdbcType=VARCHAR},</if>
    <if test="repairMethod != null">REPAIR_METHOD = #{repairMethod,jdbcType=VARCHAR},</if>
    <if test="adverseType != null">ADVERSE_TYPE = #{adverseType,jdbcType=VARCHAR},</if>
    <if test="approver != null">APPROVER = #{approver,jdbcType=VARCHAR},</if>
    <if test="exchangeSn != null">EXCHANGE_SN = #{exchangeSn,jdbcType=VARCHAR},</if>
    <if test="oldEntityType != null">OLD_ENTITY_TYPE = #{oldEntityType,jdbcType=VARCHAR},</if>
         
    <if test="remark != null">REMARK = #{remark,jdbcType=VARCHAR},</if>
    <if test="lastUpdatedBy != null">LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR}, </if>
    LAST_UPDATED_DATE =SYSDATE
  </sql>
  <sql id="Condtions">
    <include refid="Base_Condtions" />
    <if test="repairId != null and repairId != ''"> AND <include refid="Table_Name" />.REPAIR_ID = #{repairId}</if>
    <if test="errorCode != null and errorCode != ''"> AND <include refid="Table_Name" />.ERROR_CODE like #{errorCode}</if>
    <if test="errorDescription != null and errorDescription != ''"> AND <include refid="Table_Name" />.ERROR_DESCRIPTION like #{errorDescription}</if>
    <if test="reasonCode != null and reasonCode != ''"> AND <include refid="Table_Name" />.REASON_CODE = #{reasonCode}</if>
    <if test="reasonDescription != null and reasonDescription != ''"> AND <include refid="Table_Name" />.REASON_DESCRIPTION like #{reasonDescription}</if>
    <if test="locationNo != null and locationNo != ''"> AND <include refid="Table_Name" />.LOCATION_NO like #{locationNo}</if>
    <if test="prodplanId != null and prodplanId != ''"> AND <include refid="Table_Name" />.PRODPLAN_ID = #{prodplanId}</if>
    <if test="itemCode != null and itemCode != ''"> AND <include refid="Table_Name" />.ITEM_CODE like #{itemCode}</if>
    <if test="itemName != null and itemName != ''"> AND <include refid="Table_Name" />.WORK_STATION like #{itemName}</if>
    <if test="objectId != null and objectId != ''"> AND <include refid="Table_Name" />.OBJECT_ID like #{objectId}</if>
    <if test="entityType != null and entityType != ''"> AND <include refid="Table_Name" />.ENTITY_TYPE like #{entityType}</if>
    <if test="repairMethod != null and repairMethod != ''"> AND <include refid="Table_Name" />.REPAIR_METHOD like #{repairMethod}</if>
    <if test="adverseType != null and adverseType != ''"> AND <include refid="Table_Name" />.ADVERSE_TYPE like #{adverseType}</if>
    <if test="approver != null and approver != ''"> AND <include refid="Table_Name" />.APPROVER = #{approver}</if>
    <if test="exchangeSn != null and exchangeSn != ''"> AND <include refid="Table_Name" />.EXCHANGE_SN = #{exchangeSn}</if>
    <if test="remark != null and remark != ''"> AND <include refid="Table_Name" />.REMARK like #{remark}</if>
    <if test="oldEntityType != null and oldEntityType != ''"> AND <include refid="Table_Name" />.OLD_ENTITY_TYPE like #{oldEntityType}</if>
    <if test="sn != null and sn != ''"> AND <include refid="Table_Name" />.SN = #{sn}</if>
    <if test="subItemSn != null and subItemSn != ''"> AND <include refid="Table_Name" />.SUB_ITEM_SN = #{subItemSn}</if>
    <if test="itemLocationNo != null and itemLocationNo != ''"> AND <include refid="Table_Name" />.LOCATION_NO = #{itemLocationNo}</if>
    <if test="queryItemCode != null and queryItemCode != ''"> AND <include refid="Table_Name" />.ITEM_CODE = #{queryItemCode}</if>
    <if test="orgId != null"> AND <include refid="Table_Name" />.ORG_ID = cast(#{orgId} as numeric)</if>
      <if test="(repairId == null or repairId == '') and (errorCode == null or errorCode == '')
        and (errorDescription == null or errorDescription == '') and (reasonCode == null or reasonCode == '')
        and (reasonDescription == null or reasonDescription == '') and (locationNo == null or locationNo == '')
        and (prodplanId == null or prodplanId == '') and (itemCode == null or itemCode == '')
        and (itemName == null or itemName == '') and (objectId == null or objectId == '')
        and (entityType == null or entityType == '') and (repairMethod == null or repairMethod == '')
        and (adverseType == null or adverseType == '') and (approver == null or approver == '')
        and (exchangeSn == null or exchangeSn == '') and (remark == null or remark == '')
        and (oldEntityType == null or oldEntityType == '') and (sn == null or sn == '')
        and (subItemSn == null or subItemSn == '') and (itemLocationNo == null or itemLocationNo == '')
        and (queryItemCode == null or queryItemCode == '') "> and 1=2</if>
  </sql>

  <sql id="ORDER_FILELD">   
    <if test="sort != null">
    <choose>
      <when test="sort=='repairDetailId'"> order by <include refid="Table_Name" />.REPAIR_DETAIL_ID <if test="order != null and order == 'desc'"> desc </if> </when>
      <when test="sort=='repairId'"> order by <include refid="Table_Name" />.REPAIR_ID <if test="order != null and order == 'desc'"> desc </if> </when>
      <when test="sort=='errorCode'"> order by <include refid="Table_Name" />.ERROR_CODE <if test="order != null and order == 'desc'"> desc </if> </when>
      <when test="sort=='reasonCode'"> order by <include refid="Table_Name" />.REASON_CODE <if test="order != null and order == 'desc'"> desc </if> </when>
      <when test="sort=='lastUpdatedDate'"> order by <include refid="Table_Name" />.last_updated_date <if test="order != null and order == 'desc'"> desc </if> </when>
      <when test="sort=='lastUpdatedBy'"> order by <include refid="Table_Name" />.last_updated_by <if test="order != null and order == 'desc'"> desc </if> </when>
      <when test="sort=='createDate'"> order by <include refid="Table_Name" />.create_date <if test="order != null and order == 'desc'"> desc </if> </when>
      <when test="sort=='createBy'"> order by <include refid="Table_Name" />.create_by <if test="order != null and order == 'desc'"> desc </if> </when>
    </choose>
    </if>
  </sql>
  
  <!--mybatis引用： 基础列结构   end -->
  
  <!--mybatis引用： 引用列结构  begin>>-->
  <sql id="Rel_Column_List">
  
  
  
    <include refid="Table_Name" />.REMARK, 
    <include refid="Table_Name" />.CREATE_BY, 
    <include refid="Table_Name" />.CREATE_DATE, 
    <include refid="Table_Name" />.LAST_UPDATED_BY, 
    <include refid="Table_Name" />.LAST_UPDATED_DATE, 
    <include refid="Table_Name" />.ENABLED_FLAG, 
    <include refid="Table_Name" />.ORG_ID, 
    <include refid="Table_Name" />.FACTORY_ID, 
    <include refid="Table_Name" />.ENTITY_ID
  </sql>  
  <!--mybatis引用： 关联表名 -->
  <sql id="Rel_Table_Name">
    
    <include refid="Table_Name" />
  </sql>
  
  <!--mybatis引用：引用列结构  <<end -->
  
  <!--
      mybatis 插入命令：有选择性的增加不良原因维护数据
  -->
  <insert id="insertPmRepairDetailSelective" parameterType="com.zte.domain.model.PmRepairDetail">
    insert into <include refid="Table_Name" />
    <trim prefix="(" suffix=")" suffixOverrides=","><include refid="Column_Filter_List" /></trim>
    <trim prefix="values (" suffix=")" suffixOverrides=","><include refid="Filed_Filter_List" /></trim>
  </insert>
  <!--
      mybatis 删除命令：删除不良原因维护根据主键
                   说明：将有效标识设置为N
                    条件：REPAIR_DETAIL_ID
  -->
  <delete id="deletePmRepairDetailByCode" parameterType="com.zte.domain.model.PmRepairDetail">
    update <include refid="Table_Name" />
       set ENABLED_FLAG = 'N'
     where <include refid="Base_Condtions" />
  </delete>
  
 <!--
      mybatis 修改命令：有选择性的更新不良原因维护数据
  -->
  <update id="updatePmRepairDetailByCodeSelective" parameterType="com.zte.domain.model.PmRepairDetail">
    update <include refid="Table_Name" />
    <set>
      <include refid="Column_Filed_List" />
    </set>
    where <include refid="Base_Condtions" />    
  </update>
  <!--
      mybatis 查询命令：查询不良原因维护根据主键
                    条件：REPAIR_DETAIL_ID
  -->
  <select id="selectPmRepairDetailById" parameterType="com.zte.domain.model.PmRepairDetail" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from   <include refid="Table_Name" />
    where  <include refid="Base_Condtions" />
    limit 1
  </select>

    <!--
        mybatis 查询命令：获取符合条件的不良原因维护数据
    -->
    <select id="getList" parameterType="com.zte.interfaces.dto.PmRepairDetailDTO" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM <include refid="Table_Name"/>
        WHERE <include refid="Condtions"/>
        <include refid="ORDER_FILELD"/>
    </select>

  <!--
      mybatis 翻页函数:获取符合条件的记录数 
  --> 
  <select id="getCount" parameterType="com.zte.interfaces.dto.PmRepairDetailDTO" resultType="java.lang.Long">
      select count(1)
      from <include refid="Rel_Table_Name" /> 
      WHERE <include refid="Condtions" />        
  </select>

    <!-- 翻页函数:获取一页的记录集 -->
    <select id="getPage" parameterType="com.zte.interfaces.dto.PmRepairDetailDTO" resultMap="BaseResultMap">
        SELECT * FROM (
            SELECT U.*, ROWNUM RN
            FROM (
                SELECT <include refid="Base_Column_List"/>
                FROM <include refid="Rel_Table_Name"/>
                WHERE
                        <include refid="Base_Condtions" />
                        <if test="repairId != null and repairId != ''"> AND <include refid="Table_Name" />.REPAIR_ID = #{repairId}</if>
                        <if test="errorCode != null and errorCode != ''"> AND <include refid="Table_Name" />.ERROR_CODE like #{errorCode}</if>
                        <if test="errorDescription != null and errorDescription != ''"> AND <include refid="Table_Name" />.ERROR_DESCRIPTION like #{errorDescription}</if>
                        <if test="reasonCode != null and reasonCode != ''"> AND <include refid="Table_Name" />.REASON_CODE = #{reasonCode}</if>
                        <if test="reasonDescription != null and reasonDescription != ''"> AND <include refid="Table_Name" />.REASON_DESCRIPTION like #{reasonDescription}</if>
                        <if test="locationNo != null and locationNo != ''"> AND <include refid="Table_Name" />.LOCATION_NO like #{locationNo}</if>
                        <if test="prodplanId != null and prodplanId != ''"> AND <include refid="Table_Name" />.PRODPLAN_ID = #{prodplanId}</if>
                        <if test="itemCode != null and itemCode != ''"> AND <include refid="Table_Name" />.ITEM_CODE like #{itemCode}</if>
                        <if test="itemName != null and itemName != ''"> AND <include refid="Table_Name" />.WORK_STATION like #{itemName}</if>
                        <if test="objectId != null and objectId != ''"> AND <include refid="Table_Name" />.OBJECT_ID like #{objectId}</if>
                        <if test="entityType != null and entityType != ''"> AND <include refid="Table_Name" />.ENTITY_TYPE like #{entityType}</if>
                        <if test="repairMethod != null and repairMethod != ''"> AND <include refid="Table_Name" />.REPAIR_METHOD like #{repairMethod}</if>
                        <if test="adverseType != null and adverseType != ''"> AND <include refid="Table_Name" />.ADVERSE_TYPE like #{adverseType}</if>
                        <if test="approver != null and approver != ''"> AND <include refid="Table_Name" />.APPROVER = #{approver}</if>
                        <if test="exchangeSn != null and exchangeSn != ''"> AND <include refid="Table_Name" />.EXCHANGE_SN = #{exchangeSn}</if>
                        <if test="remark != null and remark != ''"> AND <include refid="Table_Name" />.REMARK like #{remark}</if>
                        <if test="oldEntityType != null and oldEntityType != ''"> AND <include refid="Table_Name" />.OLD_ENTITY_TYPE like #{oldEntityType}</if>
                        <if test="sn != null and sn != ''"> AND <include refid="Table_Name" />.SN = #{sn}</if>
                        <if test="subItemSn != null and subItemSn != ''"> AND <include refid="Table_Name" />.SUB_ITEM_SN = #{subItemSn}</if>
                        <if test="itemLocationNo != null and itemLocationNo != ''"> AND <include refid="Table_Name" />.LOCATION_NO = #{itemLocationNo}</if>
                        <if test="queryItemCode != null and queryItemCode != ''"> AND <include refid="Table_Name" />.ITEM_CODE = #{queryItemCode}</if>
                        <if test="orgId != null"> AND <include refid="Table_Name" />.ORG_ID = cast(#{orgId} as numeric)</if>
                <include refid="ORDER_FILELD"/>
            ) U
        )
        where RN BETWEEN #{startRow}::numeric AND #{endRow}::numeric
    </select>

    <!-- 批量写入 -->
   <insert id="insertPmRepairDetailBatch" parameterType="java.util.List" >
    insert into PM_REPAIR_DETAIL
    (
    REPAIR_DETAIL_ID, REPAIR_ID, ERROR_CODE, ERROR_DESCRIPTION, REASON_CODE, REASON_DESCRIPTION, 
    LOCATION_NO,  PRODPLAN_ID, ITEM_CODE,ITEM_NAME, OBJECT_ID, ENTITY_TYPE, REPAIR_METHOD, ADVERSE_TYPE, APPROVER, EXCHANGE_SN,
    REMARK, OLD_ENTITY_TYPE, OLD_OBJECT_ID,CREATE_DATE, CREATE_BY, LAST_UPDATED_DATE, 
    LAST_UPDATED_BY, ENABLED_FLAG, ORG_ID, FACTORY_ID, ENTITY_ID,REPAIR_BY,SN
    )
     select REPAIR_DETAIL_ID, REPAIR_ID, ERROR_CODE, ERROR_DESCRIPTION, REASON_CODE, REASON_DESCRIPTION, 
    LOCATION_NO,  PRODPLAN_ID, ITEM_CODE,ITEM_NAME,  OBJECT_ID, ENTITY_TYPE, REPAIR_METHOD, ADVERSE_TYPE, APPROVER, EXCHANGE_SN,
    REMARK, OLD_ENTITY_TYPE,OLD_OBJECT_ID, CREATE_DATE, CREATE_BY, LAST_UPDATED_DATE, 
    LAST_UPDATED_BY, ENABLED_FLAG, ORG_ID, FACTORY_ID, ENTITY_ID,REPAIR_BY,SN
    from (
     <foreach collection ="list" item="item" index= "index" separator ="UNION ALL">
       SELECT
        #{item.repairDetailId,jdbcType=VARCHAR} REPAIR_DETAIL_ID,
        #{item.repairId,jdbcType=VARCHAR} REPAIR_ID,
        #{item.errorCode,jdbcType=VARCHAR} ERROR_CODE,
        #{item.errorDescription,jdbcType=VARCHAR} ERROR_DESCRIPTION,
        #{item.reasonCode,jdbcType=VARCHAR} REASON_CODE,
        #{item.reasonDescription,jdbcType=VARCHAR} REASON_DESCRIPTION,
        #{item.locationNo,jdbcType=VARCHAR} LOCATION_NO,
        #{item.prodplanId,jdbcType=VARCHAR} PRODPLAN_ID,
        #{item.itemCode,jdbcType=VARCHAR} ITEM_CODE, 
        #{item.itemName,jdbcType=VARCHAR} ITEM_NAME,       
        #{item.objectId,jdbcType=VARCHAR} OBJECT_ID,
        #{item.entityType,jdbcType=VARCHAR} ENTITY_TYPE,
        #{item.repairMethod,jdbcType=VARCHAR} REPAIR_METHOD,
        #{item.adverseType,jdbcType=VARCHAR} ADVERSE_TYPE,
        #{item.approver,jdbcType=VARCHAR} APPROVER,
        #{item.exchangeSn,jdbcType=VARCHAR} EXCHANGE_SN,
        #{item.remark,jdbcType=VARCHAR} REMARK,
        #{item.oldEntityType,jdbcType=VARCHAR} OLD_ENTITY_TYPE,
        #{item.oldObjectId,jdbcType=VARCHAR} OLD_OBJECT_ID,
        SYSDATE CREATE_DATE,
        #{item.createBy,jdbcType=VARCHAR} CREATE_BY,
        SYSDATE LAST_UPDATED_DATE,
        #{item.lastUpdatedBy,jdbcType=VARCHAR} LAST_UPDATED_BY,
        'Y' ENABLED_FLAG,
        #{item.orgId,jdbcType=DECIMAL} ORG_ID,
        #{item.factoryId,jdbcType=DECIMAL} FACTORY_ID,        
        #{item.entityId,jdbcType=DECIMAL} ENTITY_ID,
        #{item.repairBy,jdbcType=VARCHAR} REPAIR_BY,
        #{item.sn,jdbcType=VARCHAR} SN

    </foreach>
   ) a
  </insert>

    <!--    维修录入-查询维修历史数量-->
    <select id="getRepairInfoCount" parameterType="com.zte.interfaces.dto.PmRepairDetailDTO" resultType="java.lang.Long">
        select count(*)
        from pm_repair_detail t
        LEFT JOIN pm_repair_info h ON t.REPAIR_ID = h.REPAIR_ID
        WHERE t.ENABLED_FLAG = 'Y'
        <if test="sn != null and sn != ''">AND t.SN = #{sn}</if>
        <if test="sn == null or sn == ''">AND 1=2</if>
    </select>

    <!--    维修录入-查询维修历史-->
    <select id="getRepairInfo" parameterType="com.zte.interfaces.dto.PmRepairDetailDTO" resultMap="BaseResultMapDto">
        SELECT
        h.RECEPTION_DETAIL_ID,

        t.REPAIR_DETAIL_ID ,
        t.REPAIR_ID ,
        t.REPAIR_PRODUCT_TYPE,
        t.REPAIR_PRODUCT_STYPE,
        t.REPAIR_PRODUCT_MSTYPE,

        t.REMARK ,
        t.IS_SUB,
        t.SUB_ITEM_SN,
        t.SUB_ITEM_CODE,
        t.SUN_ITEM_NAME,

        t.IS_LOCATION_NO,
        t.LOCATION_NO ,
        t.SN,
        t.ITEM_CODE ,
        t.ITEM_NAME ,

        t.STYLE,
        t.BG_BRAND_NO,
        t.SUPPLIER_NAME,
        t.IS_CONTROL,
        t.REPLACE_SN,

        t.REPLACE_ITEM_CODE,
        t.REPLACE_BRAND,
        t.REPLACE_SUPPLIER,
        t.REPLACE_STYLE,
        t.REPAIR_BY,

        t.REPAIR_DEPATRMENT,
        t.REPAIR_DATE,
        t.CREATE_DATE,
        t.REPAIR_DONE_DATE,
        t.ITEM_SN,
        t.RESULT,
        t.REPAIR_PROCESS,
        t.ERROR_CODE,
        t.ERROR_DESCRIPTION,
        t.REPAIR_METHOD,
        t.ADVERSE_TYPE,
        t.APPROVER,
        t.EXCHANGE_SN,
        t.repair_team,
        d.status
        FROM pm_repair_detail t,pm_repair_info h,pm_repair_rcv_detail d
        WHERE t.ENABLED_FLAG = 'Y'
        and t.REPAIR_ID = h.REPAIR_ID
        and h.reception_detail_id = d.reception_detail_id
        <if test="sn != null and sn != ''"> AND t.SN = #{sn}</if>
        <if test="sn == null or sn == ''">AND 1=2</if>
        <if test="sort != null">
            <choose>
                <when test="sort=='createDate'"> order by t.CREATE_DATE <if test="order != null and order == 'desc'"> desc </if> </when>
            </choose>
        </if>
        limit #{endRow}::numeric-#{startRow}::numeric+1 offset case when ( #{startRow}::numeric-1 &lt; 0) then 0 else  #{startRow}::numeric-1 end
    </select>

    <!--    维修录入-根据送修明细ID查询维修信息数量-->
    <select id="getRepairInfoByRecIdCount" parameterType="com.zte.interfaces.dto.PmRepairDetailDTO" resultType="java.lang.Long">
        select count(*)
        from pm_repair_info h
        LEFT JOIN pm_repair_detail t ON t.REPAIR_ID = h.REPAIR_ID
        WHERE t.ENABLED_FLAG = 'Y'
        <if test="receptionDetailId != null and receptionDetailId != ''">AND h.RECEPTION_DETAIL_ID = #{receptionDetailId}</if>
        <if test="receptionDetailId == null or receptionDetailId == ''">AND 1=2</if>
    </select>

    <!--    维修录入-根据送修明细ID查询维修信息-->
    <select id="getRepairInfoByRecId" parameterType="com.zte.interfaces.dto.PmRepairDetailDTO" resultMap="BaseResultMapDto">
        SELECT
        h.RESULT,
        h.RECEPTION_DETAIL_ID,

        t.REPAIR_DETAIL_ID ,
        t.REPAIR_ID ,
        t.REPAIR_PRODUCT_TYPE,
        t.REPAIR_PRODUCT_STYPE,
        t.REPAIR_PRODUCT_MSTYPE,

        t.REMARK ,
        t.IS_SUB,
        t.SUB_ITEM_SN,
        t.SUB_ITEM_CODE,
        t.SUN_ITEM_NAME,

        t.IS_LOCATION_NO,
        t.LOCATION_NO ,
        t.SN,
        t.ITEM_CODE ,
        t.ITEM_NAME ,

        t.STYLE,
        t.BG_BRAND_NO,
        t.SUPPLIER_NAME,
        t.IS_CONTROL,
        t.REPLACE_SN,

        t.REPLACE_ITEM_CODE,
        t.REPLACE_BRAND,
        t.REPLACE_SUPPLIER,
        t.REPLACE_STYLE,
        t.REPAIR_BY,

        t.REPAIR_DEPATRMENT,
        t.REPAIR_DATE,
        t.CREATE_DATE,
        t.REPAIR_DONE_DATE,
        t.ITEM_SN
        FROM pm_repair_info h
        LEFT JOIN pm_repair_detail t ON t.REPAIR_ID = h.REPAIR_ID
        WHERE t.ENABLED_FLAG = 'Y'
        <if test="receptionDetailId != null and receptionDetailId != ''">AND h.RECEPTION_DETAIL_ID = #{receptionDetailId}</if>
        <if test="receptionDetailId == null or receptionDetailId == ''">AND 1=2</if>
        <if test="sort != null">
            <choose>
                <when test="sort=='createDate'"> order by t.CREATE_DATE <if test="order != null and order == 'desc'"> desc </if> </when>
            </choose>
        </if>
        limit #{endRow}::numeric-#{startRow}::numeric+1 offset case when ( #{startRow}::numeric-1 &lt; 0) then 0 else  #{startRow}::numeric-1 end
    </select>

    <!--    维修录入-根据送修明细ID查询维修信息-不分页-->
    <select id="getRepairInfoByRecIdNew" parameterType="com.zte.interfaces.dto.PmRepairDetailDTO" resultMap="BaseResultMapDto">
        SELECT
        h.RESULT,
        h.RECEPTION_DETAIL_ID,

        t.REPAIR_DETAIL_ID ,
        t.REPAIR_ID ,
        t.REPAIR_PRODUCT_TYPE,
        t.REPAIR_PRODUCT_STYPE,
        t.REPAIR_PRODUCT_MSTYPE,

        t.REMARK ,
        t.IS_SUB,
        t.SUB_ITEM_SN,
        t.SUB_ITEM_CODE,
        t.SUN_ITEM_NAME,

        t.IS_LOCATION_NO,
        t.LOCATION_NO ,
        t.SN,
        t.ITEM_CODE ,
        t.ITEM_NAME ,

        t.STYLE,
        t.BG_BRAND_NO,
        t.SUPPLIER_NAME,
        t.IS_CONTROL,
        t.REPLACE_SN,

        t.REPLACE_ITEM_CODE,
        t.REPLACE_BRAND,
        t.REPLACE_SUPPLIER,
        t.REPLACE_STYLE,
        t.REPAIR_BY,

        t.REPAIR_DEPATRMENT,
        t.REPAIR_DATE,
        t.CREATE_DATE,
        t.REPAIR_DONE_DATE,
        t.ITEM_SN
        FROM pm_repair_info h
        LEFT JOIN pm_repair_detail t ON t.REPAIR_ID = h.REPAIR_ID
        WHERE t.ENABLED_FLAG = 'Y'  AND h.RECEPTION_DETAIL_ID = #{receptionDetailId}
    </select>

    <!--   维修录入-根据送修明细ID查询维修头信息-->
    <select id="getRepairHead" parameterType="com.zte.interfaces.dto.PmRepairDetailDTO" resultMap="BaseResultMapDto">
        select
        t.REPAIR_ID,
        t.RECEPTION_DETAIL_ID
        from pm_repair_info t
        WHERE t.ENABLED_FLAG = 'Y' AND t.RECEPTION_DETAIL_ID = #{receptionDetailId}
    </select>

    <!--    维修录入-新增头信息-->
    <insert id="postRepairHead" parameterType="com.zte.interfaces.dto.PmRepairDetailDTO">
    insert into pm_repair_info
    (
    RESULT, RECEPTION_DETAIL_ID,
    REPAIR_ID,SN,
    STATUS,FACTORY_ID,
    CREATE_BY, CREATE_DATE,
    ORG_ID,LAST_UPDATED_BY,
    ITEM_CODE,ITEM_NAME,
    PRODPLAN_ID,WORK_ORDER_NO,
    CRAFT_SECTION,WORK_STATION,
    NEXT_PROCESS,REPAIR_PRODUCT_TYPE,
    REPAIR_PRODUCT_STYPE,REPAIR_PRODUCT_MSTYPE,
    IS_FIRST_PIECE,ENTITY_ID
    )
    values
    (
    #{result,jdbcType=VARCHAR}, #{receptionDetailId,jdbcType=VARCHAR},
    #{repairId,jdbcType=VARCHAR},#{sn,jdbcType=VARCHAR},
    #{reciptStatus,jdbcType=DECIMAL},#{factoryId,jdbcType=DECIMAL},
    #{createBy,jdbcType=VARCHAR}, sysdate,
    #{orgId,jdbcType=DECIMAL},#{repairBy,jdbcType=VARCHAR},
    #{rcvItemCode,jdbcType=VARCHAR},#{rcvItemName,jdbcType=VARCHAR},
    #{rcvProdplanId,jdbcType=VARCHAR},#{rcvWorkOrderNo,jdbcType=VARCHAR},
    #{rcvCraftSection,jdbcType=VARCHAR},#{rcvWorkStation,jdbcType=VARCHAR},
    #{rcvNextProcess,jdbcType=VARCHAR},#{repairProductType,jdbcType=VARCHAR},
    #{repairProductStype,jdbcType=VARCHAR},#{repairProductMstype,jdbcType=VARCHAR},
    #{rcvIsFirstPiece,jdbcType=DECIMAL},#{entityId,jdbcType=DECIMAL}
    )
    </insert>

    <!--    维修录入-新增维修明细-->
    <insert id="postRepairDetail" parameterType="com.zte.interfaces.dto.PmRepairDetailDTO">
    insert into pm_repair_detail
    (
    REPAIR_DETAIL_ID,REPAIR_ID,
    REPAIR_PRODUCT_TYPE,REPAIR_PRODUCT_STYPE,
    REPAIR_PRODUCT_MSTYPE,REMARK ,
    IS_SUB,SUB_ITEM_SN,
    SUB_ITEM_CODE,SUN_ITEM_NAME,

    IS_LOCATION_NO,LOCATION_NO ,
    SN,ITEM_CODE ,
    ITEM_NAME  ,STYLE,
    BG_BRAND_NO,SUPPLIER_NAME,
    IS_CONTROL, REPLACE_SN,

    REPLACE_ITEM_CODE,REPLACE_BRAND ,
    REPLACE_SUPPLIER,REPLACE_STYLE ,
    REPAIR_BY  ,REPAIR_DEPATRMENT,
    CREATE_BY,ITEM_SN,
    RESULT,REPAIR_DONE_DATE,

    ERROR_CODE,ERROR_DESCRIPTION,
    REASON_CODE,REASON_DESCRIPTION,
    FACTORY_ID,ENTITY_ID,
    PRODPLAN_ID,REPAIR_PROCESS,
    REPAIR_METHOD,ADVERSE_TYPE,
    APPROVER,EXCHANGE_SN,REPAIR_TEAM
    )
    values
    (
    #{repairDetailId ,jdbcType=VARCHAR}, #{repairId,jdbcType=VARCHAR},
    #{repairProductType,jdbcType=VARCHAR}, #{repairProductStype,jdbcType=VARCHAR},
    #{repairProductMstype,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR},
    #{isSub,jdbcType=VARCHAR}, #{subItemSn,jdbcType=VARCHAR},
    #{subItemCode,jdbcType=VARCHAR}, #{sunItemName,jdbcType=VARCHAR},

    #{isLocationNo,jdbcType=VARCHAR}, #{locationNo ,jdbcType=VARCHAR},
    #{sn ,jdbcType=VARCHAR}, #{itemCode ,jdbcType=VARCHAR},
    #{itemName ,jdbcType=VARCHAR}, #{style,jdbcType=VARCHAR},
    #{bgBrandNo,jdbcType=VARCHAR}, #{supplierName,jdbcType=VARCHAR},
    #{isControl,jdbcType=VARCHAR}, #{replaceSn,jdbcType=VARCHAR},

    #{replaceItemCode,jdbcType=VARCHAR}, #{replaceBrand,jdbcType=VARCHAR},
    #{replaceSupplier,jdbcType=VARCHAR}, #{replaceStyle,jdbcType=VARCHAR},
    #{repairBy,jdbcType=VARCHAR}, #{repairDepatrment,jdbcType=VARCHAR},
    #{createBy,jdbcType=VARCHAR},#{itemSn,jdbcType=DECIMAL},
    #{result,jdbcType=VARCHAR},sysdate,

    #{errorCode,jdbcType=VARCHAR},#{errorDescription,jdbcType=VARCHAR},
    #{reasonCode,jdbcType=VARCHAR},#{reasonDescription,jdbcType=VARCHAR},
    #{factoryId,jdbcType=DECIMAL},#{entityId,jdbcType=DECIMAL},
    #{rcvProdplanId,jdbcType=VARCHAR},#{repairProcess,jdbcType=VARCHAR},
    #{repairMethod,jdbcType=VARCHAR},#{adverseType,jdbcType=VARCHAR},
    #{approver,jdbcType=VARCHAR},#{exchangeSn,jdbcType=VARCHAR},#{repairTeam,jdbcType=VARCHAR}
    )
    </insert>

    <!--    维修录入-更新送修状态-->
    <update id="updateRcvInfo" parameterType="com.zte.interfaces.dto.PmRepairDetailDTO">
    update pm_repair_rcv_detail
    set STATUS = #{newState,jdbcType=VARCHAR},
      LAST_UPDATED_BY = #{createBy,jdbcType=VARCHAR},
      LAST_UPDATED_DATE = sysdate
    where RECEPTION_DETAIL_ID = #{receptionDetailId,jdbcType=VARCHAR}
    </update>

    <!--    维修录入-更新维修时间-->
    <update id="updateDate" parameterType="com.zte.interfaces.dto.PmRepairDetailDTO">
        update pm_repair_detail
        <set>
            REPAIR_DONE_DATE=sysdate,
            LAST_UPDATED_BY = #{createBy,jdbcType=VARCHAR},
            LAST_UPDATED_DATE = sysdate
        </set>
        where  REPAIR_ID=#{repairId ,jdbcType=VARCHAR}
    </update>

    <!--    维修录入-更新维修信息-->
    <update id="updateRepairInfo" parameterType="com.zte.interfaces.dto.PmRepairDetailDTO">
    update pm_repair_detail
    set REPAIR_DETAIL_ID=#{repairDetailId ,jdbcType=VARCHAR},
    REPAIR_PRODUCT_TYPE=#{repairProductType,jdbcType=VARCHAR},
    REPAIR_PRODUCT_STYPE=#{repairProductStype,jdbcType=VARCHAR},
    REPAIR_PRODUCT_MSTYPE=#{repairProductMstype,jdbcType=VARCHAR},

    REMARK=#{remark,jdbcType=VARCHAR},
    IS_SUB=#{isSub,jdbcType=VARCHAR},
    SUB_ITEM_SN=#{subItemSn,jdbcType=VARCHAR},
    SUB_ITEM_CODE=#{subItemCode,jdbcType=VARCHAR},
    SUN_ITEM_NAME=#{sunItemName,jdbcType=VARCHAR},

    IS_LOCATION_NO=#{isLocationNo,jdbcType=VARCHAR},
    LOCATION_NO=#{locationNo ,jdbcType=VARCHAR},
    SN=#{sn ,jdbcType=VARCHAR},
    ITEM_CODE=#{itemCode ,jdbcType=VARCHAR},
    ITEM_NAME=#{itemName ,jdbcType=VARCHAR},

    STYLE=#{style,jdbcType=VARCHAR},
    BG_BRAND_NO=#{bgBrandNo,jdbcType=VARCHAR},
    SUPPLIER_NAME=#{supplierName,jdbcType=VARCHAR},
    IS_CONTROL=#{isControl,jdbcType=VARCHAR},
    REPLACE_SN=#{replaceSn,jdbcType=VARCHAR},

    REPLACE_ITEM_CODE=#{replaceItemCode,jdbcType=VARCHAR},
    REPLACE_BRAND =#{replaceBrand,jdbcType=VARCHAR},
    REPLACE_SUPPLIER=#{replaceSupplier,jdbcType=VARCHAR},
    REPLACE_STYLE =#{replaceStyle,jdbcType=VARCHAR},
    REPAIR_BY=#{repairBy,jdbcType=VARCHAR},

    REPAIR_DEPATRMENT=#{repairDepatrment,jdbcType=VARCHAR},
    LAST_UPDATED_DATE = sysdate,
    LAST_UPDATED_BY = #{createBy,jdbcType=VARCHAR},
    ITEM_SN = #{itemSn,jdbcType=VARCHAR},
    RESULT = #{result,jdbcType=VARCHAR},
    REPAIR_TEAM = #{repairTeam,jdbcType=VARCHAR},
    REPAIR_PROCESS = #{repairProcess,jdbcType=VARCHAR}
    where REPAIR_DETAIL_ID = #{repairDetailId,jdbcType=VARCHAR}
    </update>

    <!--    维修录入-更新维修头信息-->
    <update id="updatreRepairHead" parameterType="com.zte.interfaces.dto.PmRepairDetailDTO">
        update pm_repair_info
        <set>
            RESULT=#{result,jdbcType=VARCHAR},
            RECEPTION_ID=#{receptionDetailId,jdbcType=VARCHAR},
            LAST_UPDATED_BY = #{repairBy,jdbcType=VARCHAR},
            LAST_UPDATED_DATE = sysdate
        </set>
        where  REPAIR_ID=#{repairId ,jdbcType=VARCHAR}
    </update>

    <delete id="deleteRepairInfo" parameterType="com.zte.interfaces.dto.PmRepairDetailDTO">
    delete from pm_repair_detail
    where REPAIR_DETAIL_ID = #{repairDetailId,jdbcType=VARCHAR}
    </delete>

    <!--   维修录入-查询品牌，型号，供应商-->
    <select id="getStyleInfo" parameterType="com.zte.interfaces.dto.PmRepairDetailDTO" resultMap="BaseResultMapDto">
        select
        t.REPAIR_DETAIL_ID ,
        t.REPAIR_ID ,
        t.REPAIR_PRODUCT_TYPE,
        t.REPAIR_PRODUCT_STYPE,
        t.REPAIR_PRODUCT_MSTYPE,

        t.REMARK ,
        t.IS_SUB,
        t.SUB_ITEM_SN,
        t.SUB_ITEM_CODE,
        t.SUN_ITEM_NAME,

        t.IS_LOCATION_NO,
        t.LOCATION_NO ,
        t.SN,
        t.ITEM_CODE ,
        t.ITEM_NAME ,

        t.STYLE,
        t.BG_BRAND_NO,
        t.SUPPLIER_NAME,
        t.IS_CONTROL,
        t.REPLACE_SN,

        t.REPLACE_ITEM_CODE,
        t.REPLACE_BRAND,
        t.REPLACE_SUPPLIER,
        t.REPLACE_STYLE,
        t.REPAIR_BY,

        t.REPAIR_DEPATRMENT,
        t.REPAIR_DATE,
        t.CREATE_DATE,
        t.REPAIR_DONE_DATE,
        t.ITEM_SN
        from pm_repair_detail t
        WHERE t.ENABLED_FLAG = 'Y' AND t.ITEM_SN = #{itemSn}
    </select>

    <!--    维修录入-更新维修头表的送修ID字段-->
    <update id="updateReciptionId" parameterType="com.zte.interfaces.dto.PmRepairDetailDTO">
        update pm_repair_info
        <set>
            RECEPTION_ID=#{receptionDetailId,jdbcType=VARCHAR}
        </set>
        where  REPAIR_ID=#{repairId ,jdbcType=VARCHAR}
    </update>


    <select id="getListBySnAndDeliveryNo" resultMap="BaseResultMapDto"
            parameterType="java.util.List">
        SELECT r.DELIVERY_NO,rd.SN,d.IS_SUB,d.IS_CONTROL,d.REPLACE_SN,d.REPAIR_PRODUCT_MSTYPE,d.LOCATION_NO
        from PM_REPAIR_DETAIL d
        left join PM_REPAIR_INFO i on d.REPAIR_ID=d.REPAIR_ID
        left join PM_REPAIR_RCV_DETAIL rd on i.RECEPTION_DETAIL_ID=rd.RECEPTION_DETAIL_ID
        left join PM_REPAIR_RCV r on rd.RECEPTION_ID = r.RECEPTION_ID
        WHERE d.ENABLED_FLAG = 'Y' and i.ENABLED_FLAG = 'Y' and rd.ENABLED_FLAG = 'Y' and r.ENABLED_FLAG = 'Y'
        and (rd.STATUS ='${@com.zte.common.utils.Constant@REPAIR_STATUS_COMPLETE}' or rd.STATUS ='${@com.zte.common.utils.Constant@REPAIR_STATUS_WAIT}')
        AND RD.SN in　　
        <foreach collection="list" index="index" item="item"
                 separator="," open="(" close=")">
            #{item.sn,jdbcType=VARCHAR}
        </foreach>
        AND RD.DELIVERY_NO in　　
        <foreach collection="list" index="index" item="item"
                 separator="," open="(" close=")">
            #{item.deliveryNo,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="getListByReceptionDetailId" resultMap="BaseResultMapDto"
            parameterType="java.util.List">
        SELECT i.SN,d.IS_SUB,d.IS_CONTROL,d.REPLACE_SN,d.REPAIR_PRODUCT_MSTYPE,d.LOCATION_NO,i.RECEPTION_DETAIL_ID,d.SUB_ITEM_SN
        from PM_REPAIR_DETAIL d
        left join PM_REPAIR_INFO i on d.REPAIR_ID=i.REPAIR_ID
        WHERE d.ENABLED_FLAG = 'Y' and d.ENABLED_FLAG = 'Y'
        AND i.RECEPTION_DETAIL_ID in
        <foreach collection="list" index="index" item="item"
                 separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <!--    维修录入-查询维修历史-->
    <select id="getRepairDetailBySnList" parameterType="java.util.List" resultMap="BaseResultMapDto">
        SELECT
        t.REPAIR_PRODUCT_STYPE,
        t.REPAIR_PRODUCT_MSTYPE,
        t.SN
        FROM pm_repair_detail t
        WHERE t.ENABLED_FLAG = 'Y'
        AND t.sn in
        <foreach collection="snList" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="getRelReadyToUpdateListCount" parameterType="java.util.List" resultType="java.lang.Integer">
        select count(b.repair_detail_id)
        FROM PM_REPAIR_DETAIL b
        where 1=1
        and b.reason_code is not null
        and b.reason_code !=''
        and b.enabled_flag = 'Y'
        and (
        b.repair_product_type is null or b.repair_product_type = ''
        or b.repair_product_stype is null or b.repair_product_stype = ''
        or b.repair_product_mstype is null or b.repair_product_mstype = ''
        )
        <if test="reasonCodeList != null and reasonCodeList.size() > 0">and b.reason_code in
            <foreach collection="reasonCodeList" index="index" item="item" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="reasonCodeList == null and reasonCodeList.size() == 0">and 1 = 2</if>
    </select>

    <select id="getRelReadyToUpdateList" parameterType="java.util.List"
            resultMap="BaseResultMap">
        select b.repair_detail_id,b.reason_code
        FROM PM_REPAIR_DETAIL b
        where 1=1
        and b.reason_code is not null
        and b.reason_code !=''
        and b.enabled_flag = 'Y'
        and (
        b.repair_product_type is null or b.repair_product_type = ''
        or b.repair_product_stype is null or b.repair_product_stype = ''
        or b.repair_product_mstype is null or b.repair_product_mstype = ''
        )
        <if test="reasonCodeList != null and reasonCodeList.size() > 0">and b.reason_code in
            <foreach collection="reasonCodeList" index="index" item="item" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="reasonCodeList == null and reasonCodeList.size() == 0">and 1 = 2</if>
        limit 1000
    </select>

    <update id="updatePmRepairDetailProductTypeBatch" parameterType="com.zte.interfaces.dto.BsErrorCodeInfoDTO">
        UPDATE PM_REPAIR_DETAIL t
        <set>
            t.repair_product_type= #{attribute1,jdbcType=VARCHAR},
            t.repair_product_stype= #{descChi,jdbcType=VARCHAR},
            t.repair_product_mstype= #{attribute2,jdbcType=VARCHAR}
        </set>
        WHERE t.ENABLED_FLAG='Y'
        <if test="repairDetailIdList != null and repairDetailIdList.size() > 0">and t.repair_detail_id in
            <foreach collection="repairDetailIdList" index="index" item="item" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="repairDetailIdList == null and repairDetailIdList.size() == 0">and 1 = 2</if>
    </update>
</mapper>
