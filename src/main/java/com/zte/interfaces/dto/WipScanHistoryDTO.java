package com.zte.interfaces.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class WipScanHistoryDTO extends PageDTO implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键ID
	 **/
	private String smtScanId;
	/**
	 * 产品序号
	 **/
	@ApiModelProperty(value = "小板条码")
	private String sn;
	/**
	 * 工单号
	 **/
	@ApiModelProperty(value = "指令")
	@NotBlank(message = "指令不能为空")
	private String workOrderNo;
	/**
	 * 物料代码
	 **/
	private String itemNo;
	/**
	 * 物料名称
	 **/
	private String itemName;
	/**
	 * 工艺路径
	 **/
	private String routeId;
	/**
	 * 当前工序代码
	 **/
	@NotBlank(message = "当前工序代码不能为空")
	private String currProcessCode;
	/**
	 * 结果标识
	 **/
	private String status;
	/**
	 * 扫描人
	 **/
	private String worker;
	/**
	 * 入站时间
	 **/
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date inTime;
	/**
	 * 出站时间
	 **/
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date outTime;
	/**
	 * 父SN
	 **/
	@ApiModelProperty(value = "大板条码")
	private String parentSn;
	/**
	 * 故障描述
	 **/
	private String errorCode;
	/**
	 * 线体代码
	 **/
	private String lineCode;
	/**
	 * 耗费时间
	 **/
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date timeSpan;
	/**
	 * 车间代码
	 **/
	private String workshopCode;
	/**
	 * 操作次数
	 **/
	private BigDecimal opeTimes;
	/**
	 * 工艺段
	 */
	private String craftSection;
	/**
	 * 工位
	 */
	@NotBlank(message = "工站不能为空")
	private String workStation;
	/**
	 * 备注
	 **/
	private String remark;
	/**
	 * 创建人
	 **/
	private String createBy;
	/**
	 * 创建日期
	 **/
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date createDate;
	/**
	 * 修改人
	 **/
	private String lastUpdatedBy;
	/**
	 * 修改日期
	 **/
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date lastUpdatedDate;
	/**
	 * 是否有效
	 **/
	private String enabledFlag;
	/**
	 * 组织ID
	 **/
	private BigDecimal orgId;
	/**
	 * 工厂ID
	 **/
	private BigDecimal factoryId;
	/**
	 * 实体ID
	 **/
	private BigDecimal entityId;
	/**
	 * // TODO remarks
	 **/
	private String attribute1;
	/**
	 * // TODO remarks
	 **/
	private String attribute2;
	/**
	 * // TODO remarks
	 **/
	private String attribute3;
	/**
	 * // TODO remarks
	 **/
	private String attribute4;
	/**
	 * // TODO remarks
	 **/
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date attribute5;
	/**
	 * 是否当前指令最后工序
	 */
	private String lastProcess;
	/**
	 * 来源系统
	 */
	private String sourceSys;
	/**
	 * 下工序
	 */
	private String nextProcess;
	private BigDecimal sourceImu;
	private BigDecimal sourceBimu;
	/**
	 * 日报列名
	 */
	private String colName;
	/**
	 * IMU名称
	 */
	private String sourceSysName;
	/**
	 * 是否过站回写
	 */
	private String isWriteBackScan;
	/**
	 * 是否工号回写
	 */
	private String isWriteBackUser;
	/**
	 * 是否追溯计算
	 */
	private String isTraceCalculate;
	/**
	 * 过板数
	 */
	private BigDecimal pcbQty;

	private java.lang.String sort; // 排序字段(值areaId或者areaCode)

	private java.lang.String order; // 排序方式(默认升序,设为desc时降序)

	private java.lang.Long pageNo = 0L; // 请求的页码

	private java.lang.Long pageSize = 0L; // 每页条数

	private Long startRow = 0L;

	private Long endRow = 0L;

	private String updatedTime;

	//日期范围
	private String lastUpdatedDateRange;

	@JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss")
	private String startTime;

	@JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss")
	private String endTime;

	@JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss")
	private Date start;

	@JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss")
	private Date end;

	//是否dip过板扫描完成
	private  String dipFinishFlag;

	//  简明日报存储，批次和工序分组后的过站数量统计。
	private Long crossStationQty;

	// 简明日报存储，批次和工序分组后的转交数量统计
	private Long transmitQty;

	// 简明日报存储，批次和主工序分组后的条码中最早的创建时间
	private Date onLineTime;

	private List<String> snList;

	private String preProcessCode;

	private String preWorkStation;

	private String preWorkOrderNo;

	public String getPreProcessCode() {
		return preProcessCode;
	}

	public void setPreProcessCode(String preProcessCode) {
		this.preProcessCode = preProcessCode;
	}

	public String getPreWorkStation() {
		return preWorkStation;
	}

	public void setPreWorkStation(String preWorkStation) {
		this.preWorkStation = preWorkStation;
	}

	public String getPreWorkOrderNo() {
		return preWorkOrderNo;
	}

	public void setPreWorkOrderNo(String preWorkOrderNo) {
		this.preWorkOrderNo = preWorkOrderNo;
	}

	public List<String> getSnList() {
		return snList;
	}

	public void setSnList(List<String> snList) {
		this.snList = snList;
	}

	public Date getOnLineTime() {
		return onLineTime;
	}

	public void setOnLineTime(Date onLineTime) {
		this.onLineTime = onLineTime;
	}

	public Long getCrossStationQty() {
		return crossStationQty;
	}

	public void setCrossStationQty(Long crossStationQty) {
		this.crossStationQty = crossStationQty;
	}

	public Long getTransmitQty() {
		return transmitQty;
	}

	public void setTransmitQty(Long transmitQty) {
		this.transmitQty = transmitQty;
	}

	public String getDipFinishFlag() {
		return dipFinishFlag;
	}

	public void setDipFinishFlag(String dipFinishFlag) {
		this.dipFinishFlag = dipFinishFlag;
	}

	public String getSmtScanId() {
		return smtScanId;
	}

	public void setSmtScanId(String smtScanId) {
		this.smtScanId = smtScanId;
	}

	public String getSn() {
		return sn;
	}

	public void setSn(String sn) {
		this.sn = sn;
	}

	public String getWorkOrderNo() {
		return workOrderNo;
	}

	public void setWorkOrderNo(String workOrderNo) {
		this.workOrderNo = workOrderNo;
	}

	public String getItemNo() {
		return itemNo;
	}

	public void setItemNo(String itemNo) {
		this.itemNo = itemNo;
	}

	public String getItemName() {
		return itemName;
	}

	public void setItemName(String itemName) {
		this.itemName = itemName;
	}

	public String getRouteId() {
		return routeId;
	}

	public void setRouteId(String routeId) {
		this.routeId = routeId;
	}

	public String getCurrProcessCode() {
		return currProcessCode;
	}

	public void setCurrProcessCode(String currProcessCode) {
		this.currProcessCode = currProcessCode;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getWorker() {
		return worker;
	}

	public void setWorker(String worker) {
		this.worker = worker;
	}

	public Date getInTime() {
		return inTime;
	}

	public void setInTime(Date inTime) {
		this.inTime = inTime;
	}

	public Date getOutTime() {
		return outTime;
	}

	public void setOutTime(Date outTime) {
		this.outTime = outTime;
	}

	public String getParentSn() {
		return parentSn;
	}

	public void setParentSn(String parentSn) {
		this.parentSn = parentSn;
	}

	public String getErrorCode() {
		return errorCode;
	}

	public void setErrorCode(String errorCode) {
		this.errorCode = errorCode;
	}

	public String getLineCode() {
		return lineCode;
	}

	public void setLineCode(String lineCode) {
		this.lineCode = lineCode;
	}

	public Date getTimeSpan() {
		return timeSpan;
	}

	public void setTimeSpan(Date timeSpan) {
		this.timeSpan = timeSpan;
	}

	public String getWorkshopCode() {
		return workshopCode;
	}

	public void setWorkshopCode(String workshopCode) {
		this.workshopCode = workshopCode;
	}

	public BigDecimal getOpeTimes() {
		return opeTimes;
	}

	public void setOpeTimes(BigDecimal opeTimes) {
		this.opeTimes = opeTimes;
	}

	public String getCraftSection() {
		return craftSection;
	}

	public void setCraftSection(String craftSection) {
		this.craftSection = craftSection;
	}

	public String getWorkStation() {
		return workStation;
	}

	public void setWorkStation(String workStation) {
		this.workStation = workStation;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getCreateBy() {
		return createBy;
	}

	public void setCreateBy(String createBy) {
		this.createBy = createBy;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public String getLastUpdatedBy() {
		return lastUpdatedBy;
	}

	public void setLastUpdatedBy(String lastUpdatedBy) {
		this.lastUpdatedBy = lastUpdatedBy;
	}

	public Date getLastUpdatedDate() {
		return lastUpdatedDate;
	}

	public void setLastUpdatedDate(Date lastUpdatedDate) {
		this.lastUpdatedDate = lastUpdatedDate;
	}

	public String getEnabledFlag() {
		return enabledFlag;
	}

	public void setEnabledFlag(String enabledFlag) {
		this.enabledFlag = enabledFlag;
	}

	public BigDecimal getOrgId() {
		return orgId;
	}

	public void setOrgId(BigDecimal orgId) {
		this.orgId = orgId;
	}

	public BigDecimal getFactoryId() {
		return factoryId;
	}

	public void setFactoryId(BigDecimal factoryId) {
		this.factoryId = factoryId;
	}

	public BigDecimal getEntityId() {
		return entityId;
	}

	public void setEntityId(BigDecimal entityId) {
		this.entityId = entityId;
	}

	public String getAttribute1() {
		return attribute1;
	}

	public void setAttribute1(String attribute1) {
		this.attribute1 = attribute1;
	}

	public String getAttribute2() {
		return attribute2;
	}

	public void setAttribute2(String attribute2) {
		this.attribute2 = attribute2;
	}

	public String getAttribute3() {
		return attribute3;
	}

	public void setAttribute3(String attribute3) {
		this.attribute3 = attribute3;
	}

	public String getAttribute4() {
		return attribute4;
	}

	public void setAttribute4(String attribute4) {
		this.attribute4 = attribute4;
	}

	public Date getAttribute5() {
		return attribute5;
	}

	public void setAttribute5(Date attribute5) {
		this.attribute5 = attribute5;
	}

	public String getLastProcess() {
		return lastProcess;
	}

	public void setLastProcess(String lastProcess) {
		this.lastProcess = lastProcess;
	}

	public String getSourceSys() {
		return sourceSys;
	}

	public void setSourceSys(String sourceSys) {
		this.sourceSys = sourceSys;
	}

	public String getNextProcess() {
		return nextProcess;
	}

	public void setNextProcess(String nextProcess) {
		this.nextProcess = nextProcess;
	}

	public BigDecimal getSourceImu() {
		return sourceImu;
	}

	public void setSourceImu(BigDecimal sourceImu) {
		this.sourceImu = sourceImu;
	}

	public BigDecimal getSourceBimu() {
		return sourceBimu;
	}

	public void setSourceBimu(BigDecimal sourceBimu) {
		this.sourceBimu = sourceBimu;
	}

	public String getColName() {
		return colName;
	}

	public void setColName(String colName) {
		this.colName = colName;
	}

	public String getSourceSysName() {
		return sourceSysName;
	}

	public void setSourceSysName(String sourceSysName) {
		this.sourceSysName = sourceSysName;
	}

	public String getIsWriteBackScan() {
		return isWriteBackScan;
	}

	public void setIsWriteBackScan(String isWriteBackScan) {
		this.isWriteBackScan = isWriteBackScan;
	}

	public String getIsWriteBackUser() {
		return isWriteBackUser;
	}

	public void setIsWriteBackUser(String isWriteBackUser) {
		this.isWriteBackUser = isWriteBackUser;
	}

	public String getIsTraceCalculate() {
		return isTraceCalculate;
	}

	public void setIsTraceCalculate(String isTraceCalculate) {
		this.isTraceCalculate = isTraceCalculate;
	}

	public BigDecimal getPcbQty() {
		return pcbQty;
	}

	public void setPcbQty(BigDecimal pcbQty) {
		this.pcbQty = pcbQty;
	}

	private String fixId;
	private String stationName;

	public String getFixId() {
		return fixId;
	}

	public void setFixId(String fixId) {
		this.fixId = fixId;
	}

	public String getStationName() {
		return stationName;
	}

	public void setStationName(String stationName) {
		this.stationName = stationName;
	}

	public java.lang.String getSort() {

		return sort;
	}

	public void setSort(java.lang.String sort) {

		this.sort = sort;
	}

	public java.lang.String getOrder() {

		return order;
	}

	public void setOrder(java.lang.String order) {

		this.order = order;
	}

	public java.lang.Long getPageNo() {

		return pageNo;
	}

	public void setPageNo(java.lang.Long pageNo) {

		this.pageNo = pageNo;
	}

	public java.lang.Long getPageSize() {

		return pageSize;
	}

	public void setPageSize(java.lang.Long pageSize) {

		this.pageSize = pageSize;
	}

	public Long getStartRow() {
		return startRow;
	}

	public void setStartRow(Long startRow) {
		this.startRow = startRow;
	}

	public Long getEndRow() {
		return endRow;
	}

	public void setEndRow(Long endRow) {
		this.endRow = endRow;
	}

	public String getUpdatedTime() {
		return updatedTime;
	}

	public void setUpdatedTime(String updatedTime) {
		this.updatedTime = updatedTime;
	}

	public String getLastUpdatedDateRange() {
		return lastUpdatedDateRange;
	}

	public void setLastUpdatedDateRange(String lastUpdatedDateRange) {
		this.lastUpdatedDateRange = lastUpdatedDateRange;
	}

	public String getStartTime() {
		return startTime;
	}

	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}

	public String getEndTime() {
		return endTime;
	}

	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}

	public Date getStart() {
		return start;
	}

	public void setStart(Date start) {
		this.start = start;
	}

	public Date getEnd() {
		return end;
	}

	public void setEnd(Date end) {
		this.end = end;
	}

}
