package com.zte.application.impl;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.zte.application.BSmtBomDetailService;
import com.zte.application.BSmtBomHeaderService;
import com.zte.application.IMESLogService;
import com.zte.application.ItemCheckService;
import com.zte.application.ItemCheckinfoDetailService;
import com.zte.application.ItemCheckinfoHeadService;
import com.zte.application.PkCodeInfoService;
import com.zte.application.StItemBarcodeService;
import com.zte.application.TaskMaterialIssueSeqService;
import com.zte.common.excel.ExcelCommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.ExcelName;
import com.zte.common.utils.MpConstant;
import com.zte.common.utils.NumConstant;
import com.zte.common.utils.RedisKeyConstant;
import com.zte.domain.model.BSmtBomDetail;
import com.zte.domain.model.BSmtBomHeader;
import com.zte.domain.model.ItemCheckInfoDetail;
import com.zte.domain.model.ItemCheckInfoHead;
import com.zte.domain.model.PkCodeInfo;
import com.zte.domain.model.PsTask;
import com.zte.domain.model.PsWorkOrderSmt;
import com.zte.domain.model.VReelidInfo;
import com.zte.domain.model.Ztebarcode;
import com.zte.gei.processor.handler.exporter.AbstractExportTaskHandler;
import com.zte.infrastructure.remote.BarcodeCenterRemoteService;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.assembler.PkCodeInfoAssembler;
import com.zte.interfaces.dto.BBomDetailDTO;
import com.zte.interfaces.dto.BarcodeCenterTemplatePrintDTO;
import com.zte.interfaces.dto.BatchReelIdDataDTO;
import com.zte.interfaces.dto.DistributeLocationNotPreDTO;
import com.zte.interfaces.dto.HrmPersonInfoDTO;
import com.zte.interfaces.dto.ItemCheckDTO;
import com.zte.interfaces.dto.ItemCheckDifferenceDTO;
import com.zte.interfaces.dto.ItemCheckInfoDetailDTO;
import com.zte.interfaces.dto.ItemCheckInfoDetailExportDTO;
import com.zte.interfaces.dto.ItemCheckPrintContentDTO;
import com.zte.interfaces.dto.ItemCheckPrintReelIdDTO;
import com.zte.interfaces.dto.ItemCheckRelatedInfoDTO;
import com.zte.interfaces.dto.PkCodeInfoDTO;
import com.zte.interfaces.dto.PrintRecordDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.interfaces.dto.TaskMaterialIssueSeqEntityDTO;
import com.zte.interfaces.dto.VReelidInfoDTO;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.itp.msa.util.string.StringHelper;
import com.zte.microservice.name.MicroServiceNameEum;
import com.zte.springbootframe.common.annotation.AsyncExport;
import com.zte.springbootframe.common.consts.SysConst;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.springbootframe.common.redis.RedisLock;
import com.zte.springbootframe.util.AsyncExportFileCommonService;
import com.zte.springbootframe.util.FileUtils;
import com.zte.springbootframe.util.MESHttpHelper;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.FileOutputStream;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.PriorityQueue;
import java.util.Set;
import java.util.TreeSet;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * @Author: 10307315
 * @Date: 2022/6/6 上午10:29
 */
@Service
public class ItemCheckServiceImpl extends AbstractExportTaskHandler<ItemCheckDifferenceDTO, ItemCheckInfoDetailExportDTO> implements ItemCheckService {
    @Autowired
    private BSmtBomHeaderService bSmtBomHeaderService;

    @Autowired
    private ItemCheckinfoDetailService itemCheckinfoDetailService;

    @Autowired
    private ItemCheckinfoHeadService itemCheckinfoHeadService;

    @Autowired
    private BSmtBomDetailService bSmtBomDetailService;

    @Autowired
    private TaskMaterialIssueSeqService taskMaterialIssueSeqService;

    @Autowired
    private PkCodeInfoService pkCodeInfoService;

    @Autowired
    private StItemBarcodeService stItemBarcodeService;

    @Autowired
    private IMESLogService imesLogService;

    @Autowired
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Autowired
    private BarcodeCenterRemoteService barcodeCenterRemoteService;

    @Autowired
    private DatawbRemoteService datawbRemoteService;
    @Autowired
    private AsyncExportFileCommonService asyncExportFileCommonService;

    /**
    * 物料清点输入reelid后调用的根方法
     * 1.校验必填参数，校验reelid是否已预先清点
     * 2.如果输入lfid,校验lfid正确性。
     * 3.对reelid上锁，
     * 4.
    *@Author: 10307315陈俊熙
    *@date 2022/7/5 下午4:40
    *@param
     * @param itemCheckDTO
    *@return com.zte.interfaces.dto.ItemCheckDTO
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ItemCheckDTO handleAfterInputReelId(ItemCheckDTO itemCheckDTO) throws Exception {
        ItemCheckDTO resultDto = new ItemCheckDTO();
        List<ItemCheckInfoDetail> resultList = new ArrayList<>();
        // 校验参数
        validateProdAndReelId(itemCheckDTO);
        // 查询reelId
        PkCodeInfoDTO param = new PkCodeInfoDTO();
        param.setPkCode(itemCheckDTO.getReelId());
        param.setProductTask(itemCheckDTO.getProdPlanId());
        List<PkCodeInfo> pkCodeInfoList = pkCodeInfoService.getList(param);
        if (CollectionUtils.isEmpty(pkCodeInfoList)) {
            String[] params = new String[]{itemCheckDTO.getReelId(), itemCheckDTO.getProdPlanId()};
            throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, MessageId.REEL_ID_NOT_REGISTER_ITEM_CHECK, params);
        }
        PkCodeInfo pkCodeInfo = pkCodeInfoList.get(Constant.INT_0);
        // 如果有输入lfid，就校验
        if (!StringUtils.isEmpty(itemCheckDTO.getLfId())) {
            if (!itemCheckDTO.getLfId().equals(pkCodeInfo.getLfid())){
                String[] params = new String[]{itemCheckDTO.getLfId(), itemCheckDTO.getReelId()};
                throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, MessageId.LFID_NOT_MATCH_REGISTER_INFO, params);
            }
        }
        // 使用reelid的物料代码+批次上锁，因为同物料代码的reelId同时清点会导致，非预分配的情况下，最小站位多次被分配
        String reelId = itemCheckDTO.getReelId();
        String redisKey = String.format(RedisKeyConstant.ITEM_CHECK_AFTER_INPUT_REEL_ID, itemCheckDTO.getProdPlanId(),reelId);
        RedisLock redisLock = new RedisLock(redisKey);
        if (!redisLock.lock()) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.FAILED_TO_GET_REDIS_LOCK);
        }
        try {
            // 根据reelid查询清点记录详表
            ItemCheckInfoDetail itemCheckInfoDetail = itemCheckinfoDetailService.getEntityByReelIdAndProdId
                    (itemCheckDTO.getProdPlanId(), itemCheckDTO.getReelId());
            if (itemCheckInfoDetail == null) {
                String[] params = new String[]{itemCheckDTO.getReelId()};
                throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, MessageId.REEL_ID_NOT_PRE_CHECK, params);
            }
            itemCheckInfoDetail.setContainerType(itemCheckDTO.getContainerType());
            itemCheckInfoDetail.setContainerNumber(itemCheckDTO.getContainerNumber());
            // 检验清点状态
            validateCheckStatus(itemCheckInfoDetail, itemCheckDTO.getReelId());
            // 是首盘,肯定是预分配，直接打印
            if (Constant.FLAG_Y.equals(itemCheckInfoDetail.getFirstFlag())) {
                resultList = doWhenFirstFlagTrue(itemCheckInfoDetail, itemCheckDTO.getIp());
                resultDto.setUpdateDetailInfo(resultList.get(0));
                return resultDto;
            }
            // 非首盘，需要区分是否预分配，是否按顺序清点
            resultList = doWhenFirstFlagFalse(itemCheckDTO, itemCheckInfoDetail, pkCodeInfo);
            if (!CollectionUtils.isEmpty(resultList)) {
                if (Constant.ITEM_CHECK_STATUS_CAN.equals(resultList.get(0).getCheckStatus())) {
                    resultDto.setPriorityCheckDetList(resultList);
                }
                else {
                    resultDto.setUpdateDetailInfo(resultList.get(0));
                }
            }
            return resultDto;
        } finally {
            redisLock.unlock();
        }
    }

    /**
    * 校验reelId清点状态，不可清点和已清点的都不能再次清点
    *@Author: 10307315陈俊熙
    *@date 2022/7/6 上午11:03
    *@param
     * @param itemCheckInfoDetail
     * @param reelId
    *@return void
    */
    private void validateCheckStatus(ItemCheckInfoDetail itemCheckInfoDetail, String reelId) throws Exception {
        if (Constant.ITEM_CHECK_STATUS_FORBID.equals(itemCheckInfoDetail.getCheckStatus())) {
            String[] params = new String[]{reelId};
            throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, MessageId.REEL_ID_FORBIDDEN_CHECK, params);
        }
        if (Constant.ITEM_CHECK_STATUS_FINISH.equals(itemCheckInfoDetail.getCheckStatus())) {
            String[] params = new String[]{reelId};
            throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, MessageId.REEL_ID_FINISH_CHECK, params);
        }
    }

    /**
    * 非首盘的逻辑，需要考虑区分是否预分配，是否按顺序清点
     * 主要做以下事情：
     * 1.判断是否弹过窗，及是否忽略发料顺序进行清点，如果不忽略，首先要做发料顺序校验，这一步骤如果有高优先级物料未清点，则会返回这些物料代码用于提示
     * 2. 不管是忽略发料顺序还是顺序清点，只要清点记录站位有值，就是预分配好的，直接分配即可
     * 3. 如果不是预分配，需要准备数据，查询批次物料代码对应的详细表,并合并AB面,并且lineName暂时存放A和B的线体编码，后面再转换
     *  4. 调用非首盘且不是预分配的逻辑。
    *@Author: 10307315陈俊熙
    *@date 2022/7/6 上午11:06
    *@param
     * @param itemCheckDTO
     * @param itemCheckInfoDetail
     * @param pkCodeInfo
    *@return java.util.List<com.zte.domain.model.ItemCheckInfoDetail>
    */
    private List<ItemCheckInfoDetail> doWhenFirstFlagFalse(ItemCheckDTO itemCheckDTO, ItemCheckInfoDetail itemCheckInfoDetail,
                                                           PkCodeInfo pkCodeInfo) throws Exception {
        List<ItemCheckInfoDetail> itemCheckDetList = new ArrayList<>();
        List<TaskMaterialIssueSeqEntityDTO> seqEntityList = new ArrayList<>();
        boolean hasSeq = false;
        // 有序时，会记录和当前物料对应的优先级相同的lot和sup的组合字符
        Set<String> theSameSeqLotAndSupSet = new HashSet<>();
        // 判断是否忽略发料顺序清点，如果弹过提示信息，则为true，未弹则为false;
        // 如果没有弹过提示，则进行校验清点是否按发料顺序
        if (!itemCheckDTO.getIgnorePriorityFlag()) {
            seqEntityList = taskMaterialIssueSeqService.getListByItemNo(itemCheckDTO.getProdPlanId(), itemCheckInfoDetail.getItemNo());
            // 当物料代码有维护顺序时(顺序表如果有数据，在物料代码如果没有维护顺序时，其所有相关数据行seq都为空，所以只需判断第一个)
            hasSeq = !CollectionUtils.isEmpty(seqEntityList) && seqEntityList.get(Constant.INT_0).getSeq() != null;
            if (hasSeq) {
                // 根据seq升序排序，
                Collections.sort(seqEntityList, new Comparator<TaskMaterialIssueSeqEntityDTO>() {
                    @Override
                    public int compare(TaskMaterialIssueSeqEntityDTO o1, TaskMaterialIssueSeqEntityDTO o2) {
                        return o1.getSeq().compareTo(o2.getSeq());
                    }
                });
                itemCheckDetList = itemCheckinfoDetailService.getListByItemNoAndProdId(itemCheckInfoDetail.getItemNo(),
                        itemCheckInfoDetail.getProdPlanId());
                // 检查是否顺序清点，若不是，则记录当前输入的reelId优先级更高且还未清点的reelId记录
                List<ItemCheckInfoDetail> priorityCheckDetList = validateCheckSeq(itemCheckDetList, seqEntityList,
                        pkCodeInfo, theSameSeqLotAndSupSet);
                // 如果有高优先级reelid未清点，那么直接返回这些reelid记录，用于前端提示。
                if (!CollectionUtils.isEmpty(priorityCheckDetList)) {
                    return priorityCheckDetList;
                }
            }
        }
        // 按顺序(包括物料代码无序)，或者是忽略发料顺序
        // 如果是预分配了站位的，直接处理。
        if (StringUtils.isNotEmpty(itemCheckInfoDetail.getLocationNo())) {
            return doWhenMultiLocation(itemCheckInfoDetail, itemCheckDTO.getIp());
        }
        // 不是预分配
        // 准备数据
        if (CollectionUtils.isEmpty(itemCheckDetList)) {
            itemCheckDetList = itemCheckinfoDetailService.getListByItemNoAndProdId(itemCheckInfoDetail.getItemNo(),
                    itemCheckInfoDetail.getProdPlanId());
        }
        // 查询批次物料代码对应的详细表,并合并AB面,并且lineName暂时存放A和B的线体编码，后面再转换
        List<BSmtBomDetail> bSmtBomDetailList = getBomDetailByItemNo(itemCheckInfoDetail, itemCheckDTO);
        // 准备参数
        DistributeLocationNotPreDTO distLocNotPreDTO = new DistributeLocationNotPreDTO();
        distLocNotPreDTO.setHasSeq(hasSeq);
        distLocNotPreDTO.setbSmtBomDetailList(bSmtBomDetailList);
        distLocNotPreDTO.setItemCheckDetList(itemCheckDetList);
        distLocNotPreDTO.setItemCheckInfoDetail(itemCheckInfoDetail);
        distLocNotPreDTO.setIgnorePriorityFlag(itemCheckDTO.getIgnorePriorityFlag());
        distLocNotPreDTO.setPrintMachineIp(itemCheckDTO.getIp());
        distLocNotPreDTO.setTheSameSeqLotAndSupSet(theSameSeqLotAndSupSet);
        return doWhenNotPreDist(distLocNotPreDTO, false);
    }

    /**
    * 查询批次物料代码对应的详细表,并合并AB面,且lineName暂时存放A和B的线体编码，后面再转换
    *@Author: 10307315陈俊熙
    *@date 2022/7/6 下午1:43
    *@param
     * @param itemCheckInfoDetail
     * @param itemCheckDTO
    *@return java.util.List<com.zte.domain.model.BSmtBomDetail>
    */
    private List<BSmtBomDetail> getBomDetailByItemNo(ItemCheckInfoDetail itemCheckInfoDetail, ItemCheckDTO itemCheckDTO) {
        // 取清点头表记录的ID，和输入reelid的物料代码去查询上料表详表
        ItemCheckInfoHead itemCheckHead = itemCheckinfoHeadService.getEntityByProdPlanId(itemCheckDTO.getProdPlanId());
        Set<String> cfgHeadIdSet = new HashSet<>(Constant.INT_2);
        cfgHeadIdSet.add(itemCheckHead.getCfgHeaderIdA());
        cfgHeadIdSet.add(itemCheckHead.getCfgHeaderIdB());
        List<BSmtBomDetail> bSmtBomDetailList = bSmtBomDetailService.selectBSmtBomDetailByIdSetAndItemNo
                (cfgHeadIdSet, itemCheckInfoDetail.getItemNo());
        // 查看是否同线体
        // 忽略enabled_flag为'Y'的条件，因为清点只按照预清点生成的清点头表中的上料表ID，不管它是否失效
        List<BSmtBomHeader> bSmtBomHeaderList = bSmtBomHeaderService.selectInfosByIdListIgnoreEnabeld(new ArrayList<>(cfgHeadIdSet));
        Map<String, String> mapCraftToLine = bSmtBomHeaderList.stream().collect(Collectors.toMap(
                BSmtBomHeader::getCraftSection, BSmtBomHeader::getLineCode, (oldValue, newValue) -> newValue));
        String lineCode = Constant.STR_UPPERCASE_A + Constant.COLON + mapCraftToLine.get(Constant.CRAFTSECTION_SMT_A)
                + Constant.COMMA + Constant.STR_UPPERCASE_B + Constant.COLON + mapCraftToLine.get(Constant.CRAFTSECTION_SMT_B);
        // 暂时存放A和B的线体编码，后面再转换
        itemCheckInfoDetail.setLineName(lineCode);
        // 合并A/B上料表
        bSmtBomDetailList = combineBom(bSmtBomDetailList, itemCheckHead.getCfgHeaderIdA(),
                itemCheckHead.getCfgHeaderIdB(), mapCraftToLine);
        return bSmtBomDetailList;
    }

    /**
    * 不是预分配走的逻辑
     * 1.取参数。
     * 2.得到该批次使用该物料代码的站位列表，并根据此列表，生成多站位标签内容，用于打印
     * 3.先判断是否已经弹窗，弹过窗(忽略清点顺序)则说明该reelid不是优先级最高物料，直接多站位处理
     * 4.如未弹窗，筛选出未分配reelId的站位，以及未清点的reelId。同时记录和此reelId同供应商代码和sysLotCode的一组reelId
     * 5.对步骤4结果分析，如果未分配reelId的站位列表为空，则只能是多站位情况，如果站位不为空，进行下一步分析
     * 6.如果未分配reelId的站位和未清点的reelId数量相同，那么当前reelId肯定是首盘，把reelId添加到最小站位做首盘
     * 7.如果未清点的reelId数量比待分配站位多(只可能多，因为如果比站位少，物料就不可清点，直接在前面就校验住了)，则调用方法判断是否是首盘还是多站位。
     * 8.对第7步骤中结果选择打印首盘还是多站位
    *@Author: 10307315陈俊熙
    *@date 2022/7/6 下午1:45
    *@param
     * @param distLocNotPreDTO 包括：包含该批次所使用该物料的站位列表信息的上料表详情list,是否忽略清点顺序标志，本次reelId对应的清点记录，
     *  本次清点的物料所属的物料代码下的其他清点记录，
    *@return java.util.List<com.zte.domain.model.ItemCheckInfoDetail>
    */
    private List<ItemCheckInfoDetail> doWhenNotPreDist(DistributeLocationNotPreDTO distLocNotPreDTO, boolean strictThreshold) throws Exception {
        // 得到参数
        List<BSmtBomDetail> bSmtBomDetailList = distLocNotPreDTO.getbSmtBomDetailList();
        boolean ignorePriorityFlag = distLocNotPreDTO.getIgnorePriorityFlag();
        ItemCheckInfoDetail itemCheckInfoDetail = distLocNotPreDTO.getItemCheckInfoDetail();
        String printMachineIp = distLocNotPreDTO.getPrintMachineIp();
        List<ItemCheckInfoDetail> itemCheckDetList = distLocNotPreDTO.getItemCheckDetList();
        Set<String> theSameSeqLotAndSupSet = distLocNotPreDTO.getTheSameSeqLotAndSupSet();
        boolean hasSeq = distLocNotPreDTO.getHasSeq();

        // 得到输入的reelId对应的物料代码在上料表中可适用的站位List(不能是集合，因为AB非同线体时，不能合并站位)
        List<String> locationNos = bSmtBomDetailList.stream().map(BSmtBomDetail::getLocationNo).collect(Collectors.toList());
        // 多站位字符生成
        Collections.sort(locationNos);
        String multiLocation = locationNos.toString();
        if (ignorePriorityFlag) {
            // 说明物料代码有序，且输入的reelid肯定不是优先级最高的，那当做多站位处理
            itemCheckInfoDetail.setLocationNo(multiLocation);
            itemCheckInfoDetail.setFirstFlag(Constant.FLAG_N);
            return doWhenMultiLocation(itemCheckInfoDetail, printMachineIp);
        }
        // 使用站位查询清点记录表，筛选出未分配reelId的站位，以及未清点的reelId。同时记录和此reelId同供应商代码和sysLotCode的一组reelId;
        TreeSet<String> undistributedLocations = new TreeSet<>();
        List<ItemCheckInfoDetail> canCheckReelIds = new ArrayList<>();
        List<ItemCheckInfoDetail> theSameSeqEntitys = new ArrayList<>();
        for (ItemCheckInfoDetail entity : itemCheckDetList) {
            if (Constant.ITEM_CHECK_STATUS_FINISH.equals(entity.getCheckStatus())) {
                locationNos.remove(entity.getLocationNo());
                continue;
            }
            canCheckReelIds.add(entity);
            String key = entity.getSysLotCode() + Constant.AND + entity.getSupplerCode();
            if (!CollectionUtils.isEmpty(theSameSeqLotAndSupSet) && theSameSeqLotAndSupSet.contains(key)) {
                theSameSeqEntitys.add(entity);
            }
        }
        // 存放尚未分配的站位
        undistributedLocations = new TreeSet<>(locationNos);
        // 如果站位都分配完毕。直接分配多站位
        if (undistributedLocations.size() == 0) {
            itemCheckInfoDetail.setLocationNo(multiLocation);
            itemCheckInfoDetail.setFirstFlag(Constant.FLAG_N);
            return doWhenMultiLocation(itemCheckInfoDetail, printMachineIp);
        }
        // 未分配reelId的站位和未清点的reelId数量相同，把reelId添加到最小站位做首盘
        if (undistributedLocations.size() == canCheckReelIds.size()) {
            itemCheckInfoDetail.setLocationNo(undistributedLocations.first());
            itemCheckInfoDetail.setFirstFlag(Constant.FLAG_Y);
            return doWhenFirstFlagTrue(itemCheckInfoDetail, printMachineIp);
        }
        // reelId比站位多(不可能小，因为物料可清点)，先筛选满足条件的reelId。
        List<ItemCheckInfoDetail> suitableReelIds = canCheckReelIds;
        // 先判断有序还是无序
        if (hasSeq) {
            // 如果有序，则肯定是优先级最高物料。 取最高优先级的一批物料作为待分配首盘。
            suitableReelIds = theSameSeqEntitys;
        }
        // 判断当前reelId是否是首盘还是多站位。
        boolean firstReelIdFlag = checkFirstReelId(suitableReelIds, undistributedLocations.size(),
                itemCheckInfoDetail.getPkCode(), itemCheckInfoDetail.getItemQty(), strictThreshold);
        // 首盘处理
        if (firstReelIdFlag) {
            itemCheckInfoDetail.setLocationNo(undistributedLocations.first());
            itemCheckInfoDetail.setFirstFlag(Constant.FLAG_Y);
            return doWhenFirstFlagTrue(itemCheckInfoDetail, printMachineIp);
        }
        // 多站位处理
        itemCheckInfoDetail.setLocationNo(multiLocation);
        itemCheckInfoDetail.setFirstFlag(Constant.FLAG_N);
        return doWhenMultiLocation(itemCheckInfoDetail, printMachineIp);
    }

    /**
    *  判定当前输入的reelId是首盘 还是多站位
    *@Author: 10307315陈俊熙
    *@date 2022/7/6 下午2:18
    *@param
     * @param suitableReelIds 可分配首盘站位的reelId
     * @param k  还未分配的站位
     * @param reelId   当前清点的reelId
     * @param strictThreshold   是否严格判断大于阈值的
    *@return boolean
    */
    private boolean checkFirstReelId(List<ItemCheckInfoDetail> suitableReelIds, int k, String reelId,
                                     BigDecimal qty, boolean strictThreshold) throws Exception {
        // 获取数据字典设置的比较阈值。
        SysLookupTypesDTO dto = BasicsettingRemoteService.getSysLookUpValue
                (Constant.LOOKUP_TYPE_ITEM_CHECK, Constant.LOOKUP_CODE_DISTRIBUTE_THRESHOLD);
        BigDecimal threshold = dto == null ? Constant.ITEM_CHECK_DEFAULT_THRESHOLD : new BigDecimal(dto.getLookupMeaning());
        if (strictThreshold) {
            List<ItemCheckInfoDetail> suitableReelIdFilters = suitableReelIds.stream()
                    .filter(p -> StringUtils.equals(reelId, p.getPkCode()) && p.getItemQty() != null
                            && p.getItemQty().compareTo(threshold) >= Constant.INT_0)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(suitableReelIdFilters)) {
                return false;
            }
        }
        // 倒序排列
        Collections.sort(suitableReelIds, new Comparator<ItemCheckInfoDetail>() {
            @Override
            public int compare(ItemCheckInfoDetail o1, ItemCheckInfoDetail o2) {
                BigDecimal qty1 = o1.getItemQty();
                BigDecimal qty2 = o2.getItemQty();
                qty1 = qty1 == null ? new BigDecimal(Constant.STR_0) : qty1;
                qty2 = qty2 == null ? new BigDecimal(Constant.STR_0) : qty2;
                if (qty1.compareTo(threshold) >= 0 && qty2.compareTo(threshold) >= 0) {
                    return qty1.compareTo(qty2);
                }
                return qty2.compareTo(qty1);
            }
        });
        return setReturnBooleanVaule(suitableReelIds, k, reelId, qty);
    }

    private boolean setReturnBooleanVaule(List<ItemCheckInfoDetail> suitableReelIds, int k, String reelId, BigDecimal qty) {
        // 是前k(k为尚未分配的站位数)大说明是首盘。
        for(int i = 0; i < k; i++) {
            String tempReelId = suitableReelIds.get(i).getPkCode();
            if (StringUtils.equals(reelId, tempReelId)) {
                return true;
            }
        }
        // 如果数量相同，说明实际他们同优先级，那么认为谁先扫描谁做首盘，
        BigDecimal tempQty = suitableReelIds.get(k - 1).getItemQty();
        if (tempQty != null && tempQty.equals(qty)) {
            return true;
        }
        return false;
    }

    /**
    *
    *@Author: 10307315陈俊熙
    *@date 2022/7/6 下午2:20
    *@param
     * @param itemCheckInfoDetail
     * @param printMachineIp
    *@return java.util.List<com.zte.domain.model.ItemCheckInfoDetail>
    */
    private List<ItemCheckInfoDetail> doWhenMultiLocation(ItemCheckInfoDetail itemCheckInfoDetail, String printMachineIp) throws Exception {
        List<ItemCheckInfoDetail> resultList = new ArrayList<>();
        // 更新清点状态
        itemCheckInfoDetail.setCheckStatus(Constant.ITEM_CHECK_STATUS_FINISH);
        // 获取工号,设置更新人
        Map<String, String> headerMap = MESHttpHelper.getHttpRequestHeader();
        itemCheckInfoDetail.setLastUpdatedBy(headerMap.get(Constant.X_EMP_NO_SMALL));
        // 设置线体名称
        setItemCheckDetailLineName(itemCheckInfoDetail);
        itemCheckinfoDetailService.updateCheckInfoByReelId(itemCheckInfoDetail);
        // 获取数据字典是否打印多站位标签。
        SysLookupTypesDTO dto = BasicsettingRemoteService.getSysLookUpValue
                (Constant.LOOKUP_TYPE_ITEM_CHECK, Constant.LOOKUP_CODE_PRINT_MULTI_LOCATION);
        if (dto == null || Constant.FLAG_Y.equals(dto.getLookupMeaning())) {
            // 打印多站位标签
            printLocationLabel(itemCheckInfoDetail, printMachineIp);
        }
        resultList.add(itemCheckInfoDetail);
        return resultList;
    }

    /**
    * 设置打印标签所需的线体字段，填充到清点详情中。
     * 若lineName原先有值，则存的是格式为A:线体编码,B:线体编码的待转换信息。
     * 如果没有值，则先查清点头表，使用头表中上料AB面id查询上料头表获得线体编码
     * 对线体编码转换为线体名称。
    *@Author: 10307315陈俊熙
    *@date 2022/7/6 下午2:22
    *@param
     * @param itemCheckInfoDetail
    *@return void
    */
    private void setItemCheckDetailLineName(ItemCheckInfoDetail itemCheckInfoDetail) {
        Set<String> lineCodeSet = new HashSet<>();
        String lineCodeA = null;
        String lineCodeB = null;
        // 设置线体名称
        if (StringUtils.isEmpty(itemCheckInfoDetail.getLineName())) {
            // 根据头表ID查询上料头表id，
            ItemCheckInfoHead headEntity = itemCheckinfoHeadService.getEntityById(itemCheckInfoDetail.getHeadId());
            // 组合头表id集合查询上料头表，获得线体。
            Set<String> cfgHeadIdSet = new HashSet<>();
            cfgHeadIdSet.add(headEntity.getCfgHeaderIdA());
            cfgHeadIdSet.add(headEntity.getCfgHeaderIdB());
            // 忽略enabled_flag为'Y'的条件，因为清点只按照预清点生成的清点头表中的上料表ID，不管它是否失效
            List<BSmtBomHeader> bSmtBomHeaderList = bSmtBomHeaderService.selectInfosByIdListIgnoreEnabeld(new ArrayList<>(cfgHeadIdSet));
            Map<String, String> mapCraftToLine = bSmtBomHeaderList.stream().collect(Collectors.toMap(
                    BSmtBomHeader::getCraftSection, BSmtBomHeader::getLineCode, (oldValue, newValue) -> newValue));
            lineCodeA = mapCraftToLine.get(Constant.CRAFTSECTION_SMT_A);
            lineCodeB = mapCraftToLine.get(Constant.CRAFTSECTION_SMT_B);
        } else {
            // 不为空则格式为A:线体编码,B:线体编码
            int beginA = itemCheckInfoDetail.getLineName().indexOf(Constant.COLON) + 1;
            int endA = itemCheckInfoDetail.getLineName().indexOf(Constant.COMMA);
            int beginB = itemCheckInfoDetail.getLineName().lastIndexOf(Constant.COLON) + 1;
            int endB = itemCheckInfoDetail.getLineName().length();
            if (beginA < endA) {
                lineCodeA = itemCheckInfoDetail.getLineName().substring(beginA, endA);
            }
            if (beginB < endB) {
                lineCodeB = itemCheckInfoDetail.getLineName().substring(beginB, endB);
            }
        }
        lineCodeSet.add(lineCodeA);
        lineCodeSet.add(lineCodeB);
        // 查询线体名称。
        Map<String, String> mapCodeToName = BasicsettingRemoteService.getLineNameByCodeList(new ArrayList<>(lineCodeSet));
        String location = itemCheckInfoDetail.getLocationNo();
        String lineName = Constant.STR_EMPTY;
        // 如果同线体
        if(StringUtils.equals(mapCodeToName.get(lineCodeA), mapCodeToName.get(lineCodeB))) {
            itemCheckInfoDetail.setLineName(mapCodeToName.get(lineCodeA));
            return;
        }
        if (!StringUtils.isEmpty(location)) {
            lineName = getLineName(location, mapCodeToName, lineCodeA, lineCodeB);
        }
        itemCheckInfoDetail.setLineName(lineName);
    }

    /**
    * AB面不同线体时，根据站位字段是否含A/B面站位，决定lineName内容。
    *@Author: 10307315陈俊熙
    *@date 2022/7/6 下午2:26
    *@param
     * @param location
     * @param mapCodeToName
     * @param lineCodeA
     * @param lineCodeB
    *@return java.lang.String
    */
    private String getLineName(String location, Map<String, String> mapCodeToName, String lineCodeA, String lineCodeB) {
        String lineName = Constant.STR_EMPTY;
        if (location.indexOf(Constant.STR_UPPERCASE_A) > -1 && location.indexOf(Constant.STR_UPPERCASE_B) < 0 ) {
            lineName = mapCodeToName.get(lineCodeA);
        }
        if (location.indexOf(Constant.STR_UPPERCASE_B) > -1 && location.indexOf(Constant.STR_UPPERCASE_A) < 0 ) {
            lineName = mapCodeToName.get(lineCodeB);
        }
        if (location.indexOf(Constant.STR_UPPERCASE_B) > -1 && location.indexOf(Constant.STR_UPPERCASE_A) > -1 ) {
            lineName = Constant.STR_UPPERCASE_A + Constant.COLON + mapCodeToName.get(lineCodeA)
                    + Constant.COMMA + Constant.STR_UPPERCASE_B + Constant.COLON + mapCodeToName.get(lineCodeB);
        }
        return lineName;
    }

    /**
    * // 检查是否顺序清点，若不是，则记录当前输入的reelId优先级更高且还未清点的reelId记录
    *@Author: 10307315陈俊熙
    *@date 2022/7/6 下午2:28
    *@param
     * @param itemCheckDetList
     * @param seqEntityList
     * @param pkCodeInfo
    *@return java.util.List<com.zte.domain.model.ItemCheckInfoDetail>
    */
    private List<ItemCheckInfoDetail> validateCheckSeq(List<ItemCheckInfoDetail> itemCheckDetList,
                                                       List<TaskMaterialIssueSeqEntityDTO> seqEntityList,
                                                       PkCodeInfo pkCodeInfo, Set<String> theSameSeqLotAndSupSet) {
        List<ItemCheckInfoDetail> priorityCheckDetList = new ArrayList<>();
        String supplerCode = pkCodeInfo.getSupplerCode();
        supplerCode = StringUtils.isEmpty(supplerCode) ? Constant.STR_EMPTY : supplerCode;
        String sysLotCode = pkCodeInfo.getSysLotCode();
        sysLotCode = StringUtils.isEmpty(sysLotCode) ? Constant.STR_EMPTY : sysLotCode;
        Set<String> prioritySupAndLotCodeSet = new HashSet<>();
        // 记录当前reelId对应物料的发料顺序，
        Integer curSeq = -1;
        // 找到当前reelId对应的优先级seq。
        curSeq = findSeq(seqEntityList, sysLotCode, supplerCode);
        if (curSeq == null) {
            return priorityCheckDetList;
        }
        for(int i = 0; i < seqEntityList.size(); i++) {
            TaskMaterialIssueSeqEntityDTO seqEntity = seqEntityList.get(i);
            String supTemp = seqEntity.getSupplerCode();
            String sysLotTemp = seqEntity.getSysLotCode();
            if (seqEntity.getSeq().equals(curSeq)) {
                theSameSeqLotAndSupSet.add(sysLotTemp + Constant.AND + supTemp);
            }
            else if (seqEntity.getSeq() > curSeq) {
                break;
            }
            else {
                // 如果优先级高于当前reelId相同，就添加
                prioritySupAndLotCodeSet.add(supTemp + Constant.AND + sysLotTemp);
            }
        }
        for (ItemCheckInfoDetail itemCheckInfoDetail : itemCheckDetList) {
            if (Constant.ITEM_CHECK_STATUS_FINISH.equals(itemCheckInfoDetail.getCheckStatus())) {
                continue;
            }
            String key = itemCheckInfoDetail.getSupplerCode() + Constant.AND + itemCheckInfoDetail.getSysLotCode();
            if (prioritySupAndLotCodeSet.contains(key)) {
                priorityCheckDetList.add(itemCheckInfoDetail);
            }
        }
        return priorityCheckDetList;
    }

    /**
    * 找到当前reelId对应物料在发料顺序中的seq值
    *@Author: 10307315陈俊熙
    *@date 2022/7/29 上午10:29
    *@param
     * @param sysLotCode
     * @param supplerCode
    *@return java.lang.Integer
    */
    private Integer findSeq(List<TaskMaterialIssueSeqEntityDTO> seqEntityList, String sysLotCode, String supplerCode) {
        for(int i = 0; i < seqEntityList.size(); i++) {
            TaskMaterialIssueSeqEntityDTO seqEntity = seqEntityList.get(i);
            String supTemp = seqEntity.getSupplerCode();
            String sysLotTemp = seqEntity.getSysLotCode();
            // 遍历到当前reelid顺序就停止。
            if (supplerCode.equals(supTemp) && sysLotCode.equals(sysLotTemp)) {
                return seqEntity.getSeq();
            }
        }
        return null;
    }

    /**
    * 如果首盘的话，要处理的逻辑
    *@Author: 10307315陈俊熙
    *@date 2022/7/6 下午2:28
    *@param
     * @param itemCheckInfoDetail
     * @param printMachineIp
    *@return java.util.List<com.zte.domain.model.ItemCheckInfoDetail>
    */
    private List<ItemCheckInfoDetail> doWhenFirstFlagTrue(ItemCheckInfoDetail itemCheckInfoDetail,
                                                          String printMachineIp) throws Exception {
        List<ItemCheckInfoDetail> resultList = new ArrayList<>();
        // 更新清点状态
        itemCheckInfoDetail.setCheckStatus(Constant.ITEM_CHECK_STATUS_FINISH);
        // 获取工号,设置更新人
        Map<String, String> headerMap = MESHttpHelper.getHttpRequestHeader();
        itemCheckInfoDetail.setLastUpdatedBy(headerMap.get(Constant.X_EMP_NO_SMALL));
        // 设置线体名称
        setItemCheckDetailLineName(itemCheckInfoDetail);
        itemCheckinfoDetailService.updateCheckInfoByReelId(itemCheckInfoDetail);
        // 获取数据字典
        SysLookupTypesDTO dto = BasicsettingRemoteService.getSysLookUpValue
                (Constant.LOOKUP_TYPE_ITEM_CHECK, Constant.LOOKUP_CODE_PRINT_FIRST_LOCATION);
        if (dto == null || Constant.FLAG_Y.equals(dto.getLookupMeaning())) {
            // 打印首盘标签
            printLocationLabel(itemCheckInfoDetail, printMachineIp);
        }
        resultList.add(itemCheckInfoDetail);
        return resultList;
    }

    /**
    * 校验批次和reelId必填
    *@Author: 10307315陈俊熙
    *@date 2022/7/6 下午2:29
    *@param
     * @param itemCheckDTO
    *@return void
    */
    private void validateProdAndReelId(ItemCheckDTO itemCheckDTO) throws Exception{
        // 校验参数
        if (StringUtils.isEmpty(itemCheckDTO.getProdPlanId())) {
            throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, MessageId.PLAN_NO_IS_NULL);
        }
        // 校验reelId
        if (StringUtils.isEmpty(itemCheckDTO.getReelId())) {
            throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, MessageId.REEL_ID_IS_NULL);
        }
    }

    /**
    * 物料清点输入批次后调用的根方法
    *@Author: 10307315陈俊熙
    *@date 2022/7/5 下午4:43
    *@param
     * @param itemCheckDTO
    *@return void
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleAfterInputProdId(ItemCheckDTO itemCheckDTO) throws Exception {
        // 校验参数
        if (StringUtils.isEmpty(itemCheckDTO.getProdPlanId())) {
            throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, MessageId.PLAN_NO_IS_NULL);
        }
        // 批次上锁
        String prodPlanId = itemCheckDTO.getProdPlanId();
        String redisKey = String.format(RedisKeyConstant.ITEM_CHECK_AFTER_INPUT_PROD_ID, prodPlanId);
        RedisLock redisLock = new RedisLock(redisKey);
        if (!redisLock.lock()) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.FAILED_TO_GET_REDIS_LOCK);
        }
        try {
            List<PsWorkOrderSmt> psWorkOrderSmtList = PlanscheduleRemoteService.getPsWorkSmtListByProdPlanId(itemCheckDTO
                    .getProdPlanId());
            if (CollectionUtils.isEmpty(psWorkOrderSmtList)) {
                throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, MessageId.NOT_HAVE_SMT_WORK_ORDER,
                        new String[]{prodPlanId});
            }
            // 转化为map的同时，验证头表id是否为空
            Map<String, String> mapCraftToCfgHeadId = convertToCfgHeaderIdMap(psWorkOrderSmtList, prodPlanId);
            // 得到头表id列表，然后去查询上料头表信息
            List<String> cfgHeadIdList = psWorkOrderSmtList.stream().filter(t -> StringUtils.isNotEmpty(t.getCfgHeaderId())).map(PsWorkOrderSmt::getCfgHeaderId).collect(Collectors.toList());
            List<BSmtBomHeader> bSmtBomHeaderList = bSmtBomHeaderService.selectBSmtBomHeaderByIdList(cfgHeadIdList);
            // 验证批次所属的SMT指令的A/B面上料表是否还生效，
            validateCfgOfSmtWorkOrder(mapCraftToCfgHeadId, bSmtBomHeaderList, prodPlanId);
            // 查询批次的清点记录头表，用于判断是第一次清点还是已经清点过
            ItemCheckInfoHead itemCheckHead = itemCheckinfoHeadService.getEntityByProdPlanId(prodPlanId);
            if (itemCheckHead == null) {
                doWithoutCheckInfo(itemCheckDTO, mapCraftToCfgHeadId, bSmtBomHeaderList);
            } else {
                doWithCheckInfo(itemCheckDTO, mapCraftToCfgHeadId, itemCheckHead, bSmtBomHeaderList);
            }
        } finally {
            redisLock.unlock();
        }
    }

    /**
    * 第一次清点，走的逻辑
     * 该方法，做以下事情：
     * 1.查询上料详情表，合并A/B面上料详情表，
     * 2.根据数据字典中配送地和批次查询pkCodeInfo表，使用该结果中包含的物料代码集合和1步骤中的详情表包含的物料代码做交集，作为需要清点的物料代码集合
     * 3.使用2步骤物料集合查询发料顺序表，作为分配的要参考的优先级表。
     * 4.将参数都塞到dto中，调用distributeReelId进行分配。分配结果会写入到传入的对象实例的成员变量中。
     * 5.根据分配结果，准备插入数据库的数据。区分插入和更新，是否预分配的，具体逻辑看方法
     * 6.操作数据库，调用DAO层,写数据
    *@Author: 10307315陈俊熙
    *@date 2022/7/5 下午4:56
    *@param
     * @param itemCheckDTO
     * @param mapCraftToCfgHeadId
     * @param bSmtBomHeaderList
    *@return void
    */
    private void doWithoutCheckInfo(ItemCheckDTO itemCheckDTO, Map<String, String> mapCraftToCfgHeadId,
                                     List<BSmtBomHeader> bSmtBomHeaderList) throws Exception {
        // 准备参数
        ItemCheckRelatedInfoDTO dto = new ItemCheckRelatedInfoDTO();
        dto.setMapCraftToCfgHeadId(mapCraftToCfgHeadId);
        dto.setBSmtBomHeaderList(bSmtBomHeaderList);
        // 查询详表，并根据A/B是否同线体进行合并详情表，设置到dto中
        handleBomNotChanged(dto);
        // 查询数据字典，获取配送地信息。
        List<SysLookupTypesDTO> sysLookupTypesList = BasicsettingRemoteService.getSysLookUpValue
                (Constant.LOOKUP_TYPE_PK_CODE_DELIVERY_ADDRESS);
        List<String> deliveryAddressList = sysLookupTypesList.stream().map(SysLookupTypesDTO::getLookupMeaning).collect(Collectors.toList());
        // 无清点记录的，要查询批次下所有注册的物料代码的PKcodeinfo和上料表详表中的物料。加配送地
        List<PkCodeInfo> pkCodeInfoList = pkCodeInfoService.getPkCodeInfoListByProdPlanId(itemCheckDTO.getProdPlanId(), deliveryAddressList);

        Set<String> needCheckItemNoSet = pkCodeInfoList.stream().map(PkCodeInfo::getItemCode).collect(Collectors.toSet());
        Set<String> needCheckItemNoSetDet = dto.getBSmtBomDetailList().stream().map(BSmtBomDetail::getItemCode).collect(Collectors.toSet());
        needCheckItemNoSet.addAll(needCheckItemNoSetDet);
        // 无清点记录的，需要查询批次全部的物料代码的发料顺序
        List<TaskMaterialIssueSeqEntityDTO> seqEntityList = taskMaterialIssueSeqService.getEntityListByItemSet(
                itemCheckDTO.getProdPlanId(), needCheckItemNoSet);
        // 准备参数
        dto.setNeedCheckItemNoSet(needCheckItemNoSet);
        dto.setPkCodeInfoList(pkCodeInfoList);
        dto.setSeqEntityList(seqEntityList);
        // 清点物料代码，匹配站位和reelId，
        distributeReelId(dto);
        // 准备数据
        dto.setProdPlanId(itemCheckDTO.getProdPlanId());
        prepareData(dto);
        // 处理数据库相关操作
        operateDatabase(dto);
    }

    /**
    * 非首次清点，做以下事情：
     * 1.查询批次所有的清点记录详细表，得到不可清点的物料代码集合，作为本次要清点的物料集合，
     * 2.判断上料表是否变更过，如果变更过，则将变更引起的要清点的物料添加到要清点的集合中。
     * 3.无论变没变更，都进行合并A/B上料表操作。
     * 4.根据数据字典中配送地和批次+物料代码集合查询pkCodeInfo表，
     * 5.使用2步骤物料集合查询发料顺序表，作为分配的要参考的优先级表。
     * 6.将参数都塞到dto中，调用distributeReelId进行分配。分配结果会写入到传入的对象实例的成员变量中。
     * 7.根据分配结果，准备插入数据库的数据。区分插入和更新，是否预分配的，具体逻辑看方法
     * 8.操作数据库，调用DAO层,写数据
    *@Author: 10307315陈俊熙
    *@date 2022/7/5 下午6:18
    *@param
     * @param itemCheckDTO
     * @param mapCraftToCfgHeadId
     * @param itemCheckHead
     * @param bSmtBomHeaderList
    *@return void
    */
    private void doWithCheckInfo(ItemCheckDTO itemCheckDTO, Map<String, String> mapCraftToCfgHeadId,
                                 ItemCheckInfoHead itemCheckHead, List<BSmtBomHeader> bSmtBomHeaderList) throws Exception {
        // 查询批次所有的清点记录详细表，返回清点记录
        List<ItemCheckInfoDetail> itemCheckDetList = itemCheckinfoDetailService.getListByProdPlanId(itemCheckDTO.getProdPlanId());
        // -1为不可清点
        Set<String> forbiddenCheckSet = itemCheckDetList.stream().filter(e -> e.getCheckStatus().equals
                (Constant.ITEM_CHECK_STATUS_FORBID)).map(ItemCheckInfoDetail::getItemNo).collect(Collectors.toSet());
        // 组装参数
        ItemCheckRelatedInfoDTO dto = new ItemCheckRelatedInfoDTO();
        dto.setMapCraftToCfgHeadId(mapCraftToCfgHeadId);
        dto.setItemCheckHead(itemCheckHead);
        dto.setHasCheckInfoFLag(true);
        dto.setBSmtBomHeaderList(bSmtBomHeaderList);
        // 判断上料表是否变更过
        boolean bomChangedFlag = checkBomHasChanged(mapCraftToCfgHeadId, itemCheckHead);
        if (bomChangedFlag) {
            // 得到上料表变更导致的需要重新清点的物料代码,并对dto中上料详表赋值为A/B合并后的详表
            Set<String> needCheckItemNoSet = handleBomChanged(dto);
            // 将原本不可清点物料代码+上料表变更涉及的物料代码作为代清点列表。
            forbiddenCheckSet.addAll(needCheckItemNoSet);
        } else {
            // 合并A/B上料表，并设置到dto中。
            handleBomNotChanged(dto);
        }
        // 有清点记录的，只查询需要清点的物料代码的发料顺序
        List<TaskMaterialIssueSeqEntityDTO> seqEntityList = taskMaterialIssueSeqService.getEntityListByItemSet(
                itemCheckDTO.getProdPlanId(), forbiddenCheckSet);
        // 有清点记录的，只查询批次下需要清点的物料代码的pkCodeinfo,加配送地方
        // 查询数据字典，获取配送地信息。
        List<SysLookupTypesDTO> sysLookupTypesList = BasicsettingRemoteService.getSysLookUpValue
                (Constant.LOOKUP_TYPE_PK_CODE_DELIVERY_ADDRESS);
        List<String> deliveryAddressList = sysLookupTypesList.stream().map(SysLookupTypesDTO::getLookupMeaning).collect(Collectors.toList());
        List<PkCodeInfo> pkCodeInfoList = pkCodeInfoService.getListByItemNoSetAndProd(forbiddenCheckSet, itemCheckDTO.getProdPlanId(), deliveryAddressList);
        // 准备参数
        dto.setNeedCheckItemNoSet(forbiddenCheckSet);
        dto.setPkCodeInfoList(pkCodeInfoList);
        dto.setSeqEntityList(seqEntityList);
        // 清点物料代码，匹配站位和reelId，
        distributeReelId(dto);
        // 准备数据
        dto.setProdPlanId(itemCheckDTO.getProdPlanId());
        dto.setItemCheckDetList(itemCheckDetList);
        prepareData(dto);
        // 处理数据库相关操作
        operateDatabase(dto);
    }

    /**
    * 对准备好的数据，调用DAO层方法插入/更新数据库
    *@Author: 10307315陈俊熙
    *@date 2022/7/5 下午6:17
    *@param
     * @param dto
    *@return void
    */
    private void operateDatabase(ItemCheckRelatedInfoDTO dto) {
        // 获取工号,设置更新人
        Map<String, String> headerMap = MESHttpHelper.getHttpRequestHeader();
        String empNo = headerMap.get(Constant.X_EMP_NO_SMALL);
        // 处理头表
        if (dto.getHasCheckInfoFLag()) {
            // 更新
            dto.getItemCheckHead().setLastUpdatedBy(empNo);
            itemCheckinfoHeadService.updateByEntity(dto.getItemCheckHead());
        } else {
            // 插入
            dto.getItemCheckHead().setLastUpdatedBy(empNo);
            dto.getItemCheckHead().setCreateBy(empNo);
            itemCheckinfoHeadService.insertByEntity(dto.getItemCheckHead());
        }
        // 处理详情表
        // 存储需要更新信息的清点数据
        List<ItemCheckInfoDetail> updateList = dto.getUpdateList();
        // 存储需要批量新增的清点数据，
        List<ItemCheckInfoDetail> insertList = dto.getInsertList();
        // foreach更新
        itemCheckinfoDetailService.batchUpdateByList(updateList);
        // 批量插入
        if (!CollectionUtils.isEmpty(insertList)){
            for (int i = 0; i < insertList.size(); i++) {
                insertList.get(i).setCheckType(Constant.CHECK_TYPE_INCOME);
            }
        }
        itemCheckinfoDetailService.batchInsertByList(insertList);
    }

    /**
    * 根据分配结果准备要更新/插入数据库的数据实体。
    *@Author: 10307315陈俊熙
    *@date 2022/7/5 下午6:11
    *@param
     * @param dto
    *@return void
    */
    private void prepareData(ItemCheckRelatedInfoDTO dto) throws Exception {
        List<PkCodeInfo> pkCodeInfoList = dto.getPkCodeInfoList();
        // 如果没有清点记录，需要判空。
        List<ItemCheckInfoDetail> itemCheckDetList = CollectionUtils.isEmpty(dto.getItemCheckDetList()) ?
                 new ArrayList<>() : dto.getItemCheckDetList();
        Map<String, String> distributeResultMap = dto.getDistributeResultMap();
        List<TaskMaterialIssueSeqEntityDTO> seqEntityList = dto.getSeqEntityList();
        Map<String, String> mapBran = new HashMap<>();
        // 确定清点头表信息，并返回头表id,用于组装详表数据，
        String headId = prepareHeadData(dto);
        // 存储需要更新信息的清点数据
        List<ItemCheckInfoDetail> updateList = new ArrayList<>();
        // 存储需要批量新增的清点数据，
        List<ItemCheckInfoDetail> insertList = new ArrayList<>();
        // list转map,key为reelId,value为实体。
        Map<String, ItemCheckInfoDetail> itemCheckDetMap = itemCheckDetList.stream().collect(Collectors.toMap
                (k -> k.getPkCode(), v -> v, (oldValue, newValue) -> newValue));
        // 遍历批次所有注册的reelid。
        for (PkCodeInfo pkCodeInfo : pkCodeInfoList) {
            if (!dto.getNeedCheckItemNoSet().contains(pkCodeInfo.getItemCode())) {
                continue;
            }
            // 有清点记录，则是更新数据
            if (itemCheckDetMap.containsKey(pkCodeInfo.getPkCode())) {
                //得到分配的结果
                String distributeResult = distributeResultMap.get(pkCodeInfo.getPkCode());
                // 组装数据，在原有数据上改
                ItemCheckInfoDetail itemCheckInfoDetail = itemCheckDetMap.get(pkCodeInfo.getPkCode());
                //更新reelId的数量字段
                itemCheckInfoDetail.setItemQty(pkCodeInfo.getItemQty());
                // 生成更新信息
                generateUpdateEntity(itemCheckInfoDetail, distributeResult);
                updateList.add(itemCheckInfoDetail);
                continue;
            }
            // 无记录，则是插入数据,需完成拼数据
            // 获取品牌
            if (mapBran.size() == 0) {
                mapBran = getMapBran(seqEntityList);
            }
            ItemCheckInfoDetail itemCheckInfoDetail = generateInsertEntity(pkCodeInfo, distributeResultMap.get(pkCodeInfo.getPkCode()), headId, mapBran);
            insertList.add(itemCheckInfoDetail);
        }
        // 将待插入/更新到数据库的数据保存到dto，方便下一步操作。
        dto.setUpdateList(updateList);
        dto.setInsertList(insertList);
    }

    /**
    * 得到key为供应商编码+uuid，value为品牌的map关系
    *@Author: 10307315陈俊熙
    *@date 2022/7/5 下午6:34
    *@param
     * @param seqEntityList
    *@return java.util.Map<java.lang.String,java.lang.String>
    */
    private Map<String, String> getMapBran(List<TaskMaterialIssueSeqEntityDTO> seqEntityList) {
        Map<String, String> mapBran = new HashMap<>();
        mapBran = seqEntityList.stream().collect(Collectors.toMap(k -> k.getSupplerCode() +
                        k.getSysLotCode(), v -> (StringUtils.isEmpty(v.getBgBrandNo()) ? Constant.STR_EMPTY : v.getBgBrandNo()),
                (oldValue, newValue) -> newValue));
        return mapBran;
    }

    /**
    * 准备清点头表数据
     * 如果有记录，则更新A/B头表ID，无论上料表是否更新，清点头表中记录的都是通过计划查询到的ID
     * 无记录，则生成一条新的清点头表记录，
    *@Author: 10307315陈俊熙
    *@date 2022/7/5 下午6:15
    *@param
     * @param dto
    *@return java.lang.String
    */
    private String prepareHeadData(ItemCheckRelatedInfoDTO dto) throws Exception {
        Map<String, String> mapCraftToCfgHeadId = dto.getMapCraftToCfgHeadId();
        if(mapCraftToCfgHeadId==null){
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,MessageId.MAPCRAFTTOCFGHEADID_IS_NULL);
        }
        ItemCheckInfoHead itemCheckHead = dto.getItemCheckHead();
        // 无清点记录
        if (itemCheckHead == null) {
            // 根据批次查询ps_Task 获取任务号
            List<PsTask> psTaskList = PlanscheduleRemoteService.getPsTaskByProdPlanId(dto.getProdPlanId());
            if (CollectionUtils.isEmpty(psTaskList)) {
                throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, MessageId.PS_TASK_NULL);
            }
            ItemCheckInfoHead temp = new ItemCheckInfoHead();
            temp.setCfgHeaderIdA(mapCraftToCfgHeadId.get(Constant.CRAFTSECTION_SMT_A));
            temp.setCfgHeaderIdB(mapCraftToCfgHeadId.get(Constant.CRAFTSECTION_SMT_B));
            temp.setId(UUID.randomUUID().toString());
            temp.setProdPlanId(dto.getProdPlanId());
            temp.setTaskNo(psTaskList.get(Constant.INT_0).getTaskNo());
            // 用于更新清点头表
            dto.setItemCheckHead(temp);
            return temp.getId();
        }
        // 有清点记录, 直接更新A,B上料头表ID，（无论上料表是否更新，清点头表中记录的都是通过计划查询到的ID）
        itemCheckHead.setCfgHeaderIdA(mapCraftToCfgHeadId.get(Constant.CRAFTSECTION_SMT_A));
        itemCheckHead.setCfgHeaderIdB(mapCraftToCfgHeadId.get(Constant.CRAFTSECTION_SMT_B));
        return itemCheckHead.getId();
    }

    /**
    * 生成需要插入清点详表的数据
    *@Author: 10307315陈俊熙
    *@date 2022/7/5 下午6:13
    *@param
     * @param pkCodeInfo
     * @param distributeResult
     * @param headId
     * @param mapBran
    *@return com.zte.domain.model.ItemCheckInfoDetail
    */
    private ItemCheckInfoDetail generateInsertEntity(PkCodeInfo pkCodeInfo, String distributeResult, String headId,
                                                     Map<String, String> mapBran) {
        ItemCheckInfoDetail itemCheckInfoDetail = new ItemCheckInfoDetail();
        // 生成基础信息
        generateBasicInfo(itemCheckInfoDetail, pkCodeInfo, headId);
        // 设置品牌
        String key = pkCodeInfo.getSupplerCode() + pkCodeInfo.getSysLotCode();
        itemCheckInfoDetail.setBrandName(mapBran.get(key));
        // 处理清点状态和站位信息。
        generateUpdateEntity(itemCheckInfoDetail, distributeResult);
        // 获取工号,设置更新人
        Map<String, String> headerMap = MESHttpHelper.getHttpRequestHeader();
        String empNo = headerMap.get(Constant.X_EMP_NO_SMALL);
        itemCheckInfoDetail.setCreateBy(empNo);
        itemCheckInfoDetail.setLastUpdatedBy(empNo);
        return itemCheckInfoDetail;
    }

    /**
    * 为插入实体设置基本信息
    *@Author: 10307315陈俊熙
    *@date 2022/7/5 下午6:14
    *@param
     * @param itemCheckInfoDetail
     * @param pkCodeInfo
     * @param headId
    *@return void
    */
    private void generateBasicInfo(ItemCheckInfoDetail itemCheckInfoDetail, PkCodeInfo pkCodeInfo, String headId) {
        itemCheckInfoDetail.setId(UUID.randomUUID().toString());
        itemCheckInfoDetail.setHeadId(headId);
        itemCheckInfoDetail.setPkCode(pkCodeInfo.getPkCode());
        itemCheckInfoDetail.setProdPlanId(pkCodeInfo.getProductTask());
        itemCheckInfoDetail.setItemNo(pkCodeInfo.getItemCode());
        itemCheckInfoDetail.setItemName(pkCodeInfo.getItemName());
        itemCheckInfoDetail.setSourceBatchCode(pkCodeInfo.getSourceBatchCode());
        itemCheckInfoDetail.setItemQty(pkCodeInfo.getItemQty());
        itemCheckInfoDetail.setLfId(pkCodeInfo.getLfid());
        itemCheckInfoDetail.setWetLevel(pkCodeInfo.getWetLevel());
        itemCheckInfoDetail.setSupplerCode(pkCodeInfo.getSupplerCode());
        itemCheckInfoDetail.setSysLotCode(pkCodeInfo.getSysLotCode());
        return;
    }

    private void generateUpdateEntity(ItemCheckInfoDetail itemCheckInfoDetail, String distributeResult) {
        if (itemCheckInfoDetail == null) {
            return;
        }
        // 获取工号,设置更新人
        Map<String, String> headerMap = MESHttpHelper.getHttpRequestHeader();
        String empNo = headerMap.get(Constant.X_EMP_NO_SMALL);
        itemCheckInfoDetail.setLastUpdatedBy(empNo);
        // 可清点
        if (Constant.REELID_CAN_CHECK_STATUS.equals(distributeResult)) {
            itemCheckInfoDetail.setCheckStatus(Constant.ITEM_CHECK_STATUS_CAN);
            itemCheckInfoDetail.setLocationNo(null);
            itemCheckInfoDetail.setFirstFlag(null);
            itemCheckInfoDetail.setLineName(null);
            return;
        }
        // 不可清点
        if (Constant.REELID_FORBIDDEN_CHECK_STATUS.equals(distributeResult)) {
            itemCheckInfoDetail.setCheckStatus(Constant.ITEM_CHECK_STATUS_FORBID);
            itemCheckInfoDetail.setLocationNo(null);
            itemCheckInfoDetail.setFirstFlag(null);
            itemCheckInfoDetail.setLineName(null);
            return;
        }
        // 有站位分配信息的
        itemCheckInfoDetail.setLocationNo(distributeResult);
        itemCheckInfoDetail.setCheckStatus(Constant.ITEM_CHECK_STATUS_CAN);
        itemCheckInfoDetail.setLineName(null);
        // 首盘
        if (!distributeResult.contains(Constant.STR_LEFT_SQUARE_BRACKETS)) {
            itemCheckInfoDetail.setFirstFlag(Constant.FLAG_Y);
        }
        // 非首盘
        else {
            itemCheckInfoDetail.setFirstFlag(Constant.FLAG_N);
        }
    }

    /**
    * 上料表没有变化时(包含第一次清点和非第一次但上料表没有变化)，根据是否是同线体，进行不同规则合并上料详表
    *@Author: 10307315陈俊熙
    *@date 2022/7/5 下午5:13
    *@param
     * @param dto
     * 使用了dto中的头表信息，以及A/B面对应头表id的MAP
    *@return void
    */
    private void handleBomNotChanged(ItemCheckRelatedInfoDTO dto) {
        Map<String, String> mapCraftToCfgHeadId = dto.getMapCraftToCfgHeadId();
        List<BSmtBomHeader> bSmtBomHeaderList = dto.getBSmtBomHeaderList();
        Set<String> cfgHeadIdSet = new HashSet<>();
        cfgHeadIdSet.addAll(mapCraftToCfgHeadId.values());
        // 查询详细表
        List<BSmtBomDetail> bSmtBomDetailList = bSmtBomDetailService.selectBSmtBomDetailByIdSetAndItemNo(cfgHeadIdSet, null);
        // 合并A/B面。
        Map<String, String> mapCraftToLine = bSmtBomHeaderList.stream().collect(Collectors.toMap(
                k -> k.getCraftSection(), v -> v.getLineCode(), (oldValue, newValue) -> newValue));
        // 调用合并方法，并将结果设置到参数中，作为后续分配逻辑中的总站位列表。
        dto.setBSmtBomDetailList(combineBom(bSmtBomDetailList, mapCraftToCfgHeadId.get(Constant.CRAFTSECTION_SMT_A),
                mapCraftToCfgHeadId.get(Constant.CRAFTSECTION_SMT_B), mapCraftToLine));
    }

    /**
    * 处理上料表变更逻辑
     * 1、使用新旧头表id一次性查询详细表
     * 2.筛选分类出新上料详表A/B面，旧上料详表A/B面
     * 3.合并新A/B面，旧A/B面。
     * 4.对应合并后的新旧上料表站位信息。
    *@Author: 10307315陈俊熙
    *@date 2022/7/5 下午6:26
    *@param
     * @param dto
    *@return java.util.Set<java.lang.String>
    */
    private Set<String> handleBomChanged(ItemCheckRelatedInfoDTO dto) {
        Map<String, String> mapCraftToCfgHeadId = dto.getMapCraftToCfgHeadId();
        ItemCheckInfoHead itemCheckHead = dto.getItemCheckHead();
        List<BSmtBomHeader> bSmtBomHeaderList = dto.getBSmtBomHeaderList();
        if(CollectionUtils.isEmpty(bSmtBomHeaderList)){
            return  new HashSet<>();
        }
        // 如果变更过，则头表id新旧都要传
        Set<String> cfgHeadIdSet = new HashSet<>();
        cfgHeadIdSet.add(itemCheckHead.getCfgHeaderIdA());
        cfgHeadIdSet.add(itemCheckHead.getCfgHeaderIdB());
        // 忽略enabled_flag为'Y'的条件，因为要查询之前的上料表ID，
        List<BSmtBomHeader> bSmtBomHeaderOldList = bSmtBomHeaderService.selectInfosByIdListIgnoreEnabeld(new ArrayList<>(cfgHeadIdSet));
        //  查询详细表，包含新旧记录
        List<String> cfgOfBomList = bSmtBomHeaderList.stream().map(BSmtBomHeader::getCfgHeaderId).collect(Collectors.toList());
        cfgHeadIdSet.addAll(cfgOfBomList);
        List<BSmtBomDetail> bSmtBomDetailList = bSmtBomDetailService.selectBSmtBomDetailByIdSetAndItemNo(cfgHeadIdSet, null);
        // 上料表更新过，需要对比新旧上料表
        List<BSmtBomDetail> bomDetOldListA = new ArrayList<>();
        List<BSmtBomDetail> bomDetListA = new ArrayList<>();
        List<BSmtBomDetail> bomDetOldListB = new ArrayList<>();
        List<BSmtBomDetail> bomDetListB = new ArrayList<>();
        // 筛选A面新的上料表详情信息
        String cfgHeadIdA = StringUtils.isEmpty(mapCraftToCfgHeadId.get(Constant.CRAFTSECTION_SMT_A)) ?
                Constant.STR_EMPTY : mapCraftToCfgHeadId.get(Constant.CRAFTSECTION_SMT_A);
        // 筛选A面旧的上料表详情信息
        String cfgHeadIdOldA = StringUtils.isEmpty(itemCheckHead.getCfgHeaderIdA()) ?
                Constant.STR_EMPTY : itemCheckHead.getCfgHeaderIdA();
        // 筛选B面新的上料表详情信息
        String cfgHeadIdB = StringUtils.isEmpty(mapCraftToCfgHeadId.get(Constant.CRAFTSECTION_SMT_B)) ?
                Constant.STR_EMPTY : mapCraftToCfgHeadId.get(Constant.CRAFTSECTION_SMT_B);
        // 筛选B面旧的上料表详情信息
        String cfgHeadIdOldB = StringUtils.isEmpty(itemCheckHead.getCfgHeaderIdB()) ?
                Constant.STR_EMPTY : itemCheckHead.getCfgHeaderIdB();
        for (int i = 0; i < bSmtBomDetailList.size(); i++) {
            BSmtBomDetail bSmtBomDetail = bSmtBomDetailList.get(i);
            if (cfgHeadIdA.equals(bSmtBomDetail.getCfgHeaderId())) {
                bomDetListA.add(bSmtBomDetail);
            }
            if (cfgHeadIdOldA.equals(bSmtBomDetail.getCfgHeaderId())) {
                bomDetOldListA.add(bSmtBomDetail);
            }
            if (cfgHeadIdB.equals(bSmtBomDetail.getCfgHeaderId())) {
                bomDetListB.add(bSmtBomDetail);
            }
            if (cfgHeadIdOldB.equals(bSmtBomDetail.getCfgHeaderId())) {
                bomDetOldListB.add(bSmtBomDetail);
            }
        }
        // 只变更同一面时，需要深拷贝
        bomDetOldListA = JSONObject.parseArray(JSONObject.toJSONString(bomDetOldListA), BSmtBomDetail.class);
        bomDetOldListB = JSONObject.parseArray(JSONObject.toJSONString(bomDetOldListB), BSmtBomDetail.class);
        // 得到新上料表线体对应信息 。合并A/B面新上料表,
        Map<String, String> mapCraftToLine = bSmtBomHeaderList.stream().collect(Collectors.toMap(
                k -> k.getCraftSection(), v -> v.getLineCode(), (oldValue, newValue) -> newValue));
        List<BSmtBomDetail> bomDetList = combineBom(bomDetListA, bomDetListB, mapCraftToLine);
        // 将合并后的新上料表赋值到dto中。
        dto.setBSmtBomDetailList(bomDetList);

        // 得到旧上料表线体对应信息。合并A/B面旧上料表
        Map<String, String> oldMapCraftToLine = bSmtBomHeaderOldList.stream().collect(Collectors.toMap(
                BSmtBomHeader::getCraftSection, BSmtBomHeader::getLineCode, (oldValue, newValue) -> newValue));
        List<BSmtBomDetail> bomDetOldList = combineBom(bomDetOldListA, bomDetOldListB, oldMapCraftToLine);
        // 对比新旧上料表,返回需要重新盘点的物料代码。
        Set<String> needCheckItemNoSet = compareBom(bomDetList, bomDetOldList);
        return needCheckItemNoSet;
    }

    /**
    * 分配reelId的主要逻辑，如果预分配了会分配站位，如果不是预分配，会确定哪些物料可清点和不可清点。
     *主要做以下事情：
     * 1.取出数据字典配置，包含是否预分配，分配的数量阈值
     * 2.取出参数，主要有待分配的物料集合，物料对应的发料顺序，PKcode中信息，合并处理过的上料详表（包含站位信息）
     * 3.对应数据进行处理，发料顺序按照物料代码分组，每组数据若物料支持按顺序，则经过了排序处理
     * 4。reelid和上料表信息，也使用物料代码分组
     * 5.使用待分配的物料集合setNeedCheckItemNoSet，遍历处理每一种物料。匹配reelid和站位（注意有发料顺序的要进过筛选）.
     * 7。结果得到一个key为reelId。value为分配结果(单站位，多站位，不可清点，可清点)，放入到dto,方便后续处理
    *@Author: 10307315陈俊熙
    *@date 2022/7/5 下午5:41
    *@param
     * @param dto
    *@return void
    */
    private void distributeReelId(ItemCheckRelatedInfoDTO dto) throws Exception{
        // 获取相关数据字典。
        List<SysLookupTypesDTO> sysLookupTypesList = BasicsettingRemoteService.getSysLookUpValue
                (Constant.LOOKUP_TYPE_ITEM_CHECK);
        Map<String, String> lookUpTypeMap = sysLookupTypesList.stream().collect(Collectors.toMap(
                k -> (k.getLookupCode() == null ? Constant.STR_EMPTY : k.getLookupCode().toString()),
                v -> v.getLookupMeaning(), (oldValue, newValue) -> newValue));
        // 查询数据字典，获取是否预分配，
        boolean preAllocationFlag = StringUtils.equals(Constant.FLAG_Y, lookUpTypeMap.get(Constant.LOOKUP_CODE_PRE_ALLOCATION));
        // 查询数据字典，获取优先分配的数量阈值
        BigDecimal threshold = StringUtils.isEmpty(lookUpTypeMap.get(Constant.LOOKUP_CODE_DISTRIBUTE_THRESHOLD)) ?
                Constant.ITEM_CHECK_DEFAULT_THRESHOLD :
                new BigDecimal(lookUpTypeMap.get(Constant.LOOKUP_CODE_DISTRIBUTE_THRESHOLD));
        // 需要清点的物料集合
        Set<String> setNeedCheckItemNoSet = dto.getNeedCheckItemNoSet();
        // 物料发料顺序
        List<TaskMaterialIssueSeqEntityDTO> seqEntityList = dto.getSeqEntityList();
        // 批次所有的reelId注册信息
        List<PkCodeInfo> pkCodeInfoList = dto.getPkCodeInfoList();
        // 批次上料表详细信息(AB面合并过),
        List<BSmtBomDetail> bSmtBomDetailList = dto.getBSmtBomDetailList();
        // reelId按物料代码分组，每一组存储对应的reelId注册信息list
        Map<String, List<PkCodeInfo>> mapItemToPkCodes = pkCodeInfoList.stream()
                .collect(Collectors.groupingBy(PkCodeInfo::getItemCode));
        // 上料表详细表按物料代码分组,每一组存储对应的站位list。
        Map<String, List<String>> mapItemToLocationNos = bSmtBomDetailList.stream().collect(Collectors.groupingBy
                (BSmtBomDetail::getItemCode, Collectors.mapping(BSmtBomDetail::getLocationNo, Collectors.toList())));
        // 物料发料顺序按物料代码分组，每一组存储对应的实体。
        Map<String, PriorityQueue<TaskMaterialIssueSeqEntityDTO>> mapItemToSeqList = getMapItemToSeqList(seqEntityList);
        // 用于记录清点的分配结果
        Map<String, String> distributeResultMap = new HashMap<>();
        // 针对每一个物料代码，清点对应的所有reelId
        for (String itemNo : setNeedCheckItemNoSet) {
            List<PkCodeInfo> pkCodes = mapItemToPkCodes.get(itemNo);
            // 如果一个满足的reelid都没，且上料表需要此物料，原版：说明该物料没做齐套,直接报错，现在不报错，允许清点
            if (CollectionUtils.isEmpty(pkCodes) && mapItemToLocationNos.containsKey(itemNo)) {
                continue;
            }
            // 需要得到符合条件的reelId。先判断是否有发料顺序。
            List<PkCodeInfo> suitableReelIds = pkCodes;
            List<PkCodeInfo> unsuitableReelIds = new ArrayList<>();
            if (mapItemToSeqList.containsKey(itemNo)) {
                // 如果有，取出最高优先级uuid，供应商编码, 过滤reelid
                PriorityQueue<TaskMaterialIssueSeqEntityDTO> priQueueSeq = mapItemToSeqList.get(itemNo);
                // unsuitableReelIds传入后，会把不满足的添加到该列表
                suitableReelIds = filterReelId(priQueueSeq, pkCodes, unsuitableReelIds);
            }
            if(Objects.isNull(suitableReelIds)){
                suitableReelIds = new LinkedList<>();
            }
            List<String> list = mapItemToLocationNos.get(itemNo);
            List<String> locationNos = CollectionUtils.isEmpty(list) ?
                    new ArrayList<>() : list;
            // 匹配reelid和站位,返回key为reelId。value为站位
            Map<String, String> distributeMap = handleDistribute(locationNos, suitableReelIds, unsuitableReelIds,
                    preAllocationFlag, threshold);
            distributeResultMap.putAll(distributeMap);
        }
        dto.setDistributeResultMap(distributeResultMap);
    }

    /**
    * 对有序的物料代码进行筛选，将优先级最高的返回，不是优先级最高的放入unsuitableReelIds
    *@Author: 10307315陈俊熙
    *@date 2022/7/5 下午6:01
    *@param
     * @param priQueueSeq
     * @param pkCodes
     * @param unsuitableReelIds
    *@return java.util.List<com.zte.domain.model.PkCodeInfo>
    */
    private List<PkCodeInfo> filterReelId(PriorityQueue<TaskMaterialIssueSeqEntityDTO> priQueueSeq,
                                          List<PkCodeInfo> pkCodes, List<PkCodeInfo> unsuitableReelIds) {
        TaskMaterialIssueSeqEntityDTO minDTO = priQueueSeq.peek();
        if (minDTO == null) {
            return pkCodes;
        }
        Integer minSeq = minDTO.getSeq() == null ? -1 : minDTO.getSeq();
        Set<String> sysLotAndSupplerOfMinSeq = new HashSet<>();
        List<TaskMaterialIssueSeqEntityDTO> seqList = new ArrayList<>(priQueueSeq);
        for (int i = 0; i < seqList.size(); i++) {
            // 如果是优先级最高的物料
            if (minSeq.equals(seqList.get(i).getSeq())) {
                TaskMaterialIssueSeqEntityDTO curDTO = seqList.get(i);
                String sysLotCode = StringUtils.isEmpty(curDTO.getSysLotCode()) ? Constant.STR_EMPTY : curDTO.getSysLotCode();
                String supplerCode = StringUtils.isEmpty(curDTO.getSupplerCode()) ? Constant.STR_EMPTY : curDTO.getSupplerCode();
                sysLotAndSupplerOfMinSeq.add(sysLotCode + Constant.AND + supplerCode);
            }
        }
        // 过滤,取得满足条件的ReelId
        List<PkCodeInfo> suitableList = new ArrayList<>();
        if(CollectionUtils.isEmpty(pkCodes)){
            return suitableList;
        }
        for (PkCodeInfo pkCodeInfo : pkCodes) {
            String key = pkCodeInfo.getSysLotCode() + Constant.AND + pkCodeInfo.getSupplerCode();
            if (sysLotAndSupplerOfMinSeq.contains(key)) {
                suitableList.add(pkCodeInfo);
                continue;
            }
            unsuitableReelIds.add(pkCodeInfo);
        }
        return suitableList;
    }

    /**
    * 对物料代码对应的在上料表中的站位和注册信息中可用于分配的reelid，进行分配。
     * 1.比较站位数量和可分配的reelid数量比较，如果站位多，不可清点，如果少，继续，
     * 2.如果非预分配，直接这批reelid都设置可清点。
     * 3.使用自定义比较器的优先级对列，根据分配阈值， 如果大于阈值取最小，小于阈值的取最大的规则。
     * 4.遍历站位，分配优先队列头元素。分配一个，队列头元素就出队列。
     * 5.站位都分配完后，对剩余reelid，分配多站位
    *@Author: 10307315陈俊熙
    *@date 2022/7/5 下午5:55
    *@param
     * @param locationNos
     * @param suitableReelIds
     * @param unsuitableReelIds
     * @param preAllocationFlag
     * @param threshold
    *@return java.util.Map<java.lang.String,java.lang.String>
    */
    private Map<String, String> handleDistribute(List<String> locationNos, List<PkCodeInfo> suitableReelIds,
                                                 List<PkCodeInfo> unsuitableReelIds,
                                                 boolean preAllocationFlag, BigDecimal threshold) {
        Map<String, String> distributeMap = new HashMap<>();
        // 如果不需要此物料，那么物料对应的reelId都不可清点。或者站位数大于可用物料
        if (locationNos.size() > suitableReelIds.size() || locationNos.size() == 0) {
            // 将此物料代码全部设置为不可清点
            return setForbiddenCheckStatus(suitableReelIds, unsuitableReelIds);
        }
        if (!preAllocationFlag) {
            return setCanCheckStatus(suitableReelIds, unsuitableReelIds);
        }
        PriorityQueue<PkCodeInfo> reelIdPriorityQueue = getPkCodeInfoPriorityQueue(threshold);
        for (int i = 0; i < suitableReelIds.size(); i++) {
            reelIdPriorityQueue.offer(suitableReelIds.get(i));
        }
        Collections.sort(locationNos);
        // 遍历站位
        for (int i = 0; i < locationNos.size(); i++) {
            // 优先从队列头取元素。取一个，就将该元素出队列
            PkCodeInfo pkCodeInfo = reelIdPriorityQueue.poll();
            if (pkCodeInfo == null) {
                break;
            }
            distributeMap.put(pkCodeInfo.getPkCode(), locationNos.get(i));
        }
        // 多站位字符生成
        String multiLocation = locationNos.toString();
        // 剩余的，reelId分配多站位。
        while (reelIdPriorityQueue.size() > 0) {
            distributeMap.put(reelIdPriorityQueue.poll().getPkCode(), multiLocation);
        }
        addDistributeMap(unsuitableReelIds, distributeMap, multiLocation);
        return distributeMap;
    }

    private void addDistributeMap(List<PkCodeInfo> unsuitableReelIds, Map<String, String> distributeMap, String multiLocation) {
        for (int i = 0; i < unsuitableReelIds.size(); i++) {
            distributeMap.put(unsuitableReelIds.get(i).getPkCode(), multiLocation);
        }
    }

    private PriorityQueue<PkCodeInfo> getPkCodeInfoPriorityQueue(BigDecimal threshold) {
        PriorityQueue<PkCodeInfo> reelIdPriorityQueue = new PriorityQueue<PkCodeInfo>(new Comparator<PkCodeInfo>() {
            // 自定义比较器，超过threshold，选最小，没有超过选最大。
            @Override
            public int compare(PkCodeInfo o1, PkCodeInfo o2) {
                BigDecimal qty1 = o1.getItemQty();
                BigDecimal qty2 = o2.getItemQty();
                qty1 = qty1 == null ? new BigDecimal(Constant.STR_0) : qty1;
                qty2 = qty2 == null ? new BigDecimal(Constant.STR_0) : qty2;
                if (qty1.compareTo(threshold) >= 0 && qty2.compareTo(threshold) >= 0) {
                    return qty1.compareTo(qty2);
                }
                return qty2.compareTo(qty1);
            }
        });
        return reelIdPriorityQueue;
    }

    /**
    *将一批物料代码对应的reelid分配为可清点
    *@Author: 10307315陈俊熙
    *@date 2022/7/5 下午6:08
    *@param
     * @param suitableReelIds
     * @param unsuitableReelIds
    *@return java.util.Map<java.lang.String,java.lang.String>
    */
    private Map<String, String> setCanCheckStatus(List<PkCodeInfo> suitableReelIds, List<PkCodeInfo> unsuitableReelIds) {
        Map<String, String> distributeMap = new HashMap<>();
        for (int i = 0; i < suitableReelIds.size(); i++) {
            distributeMap.put(suitableReelIds.get(i).getPkCode(), Constant.REELID_CAN_CHECK_STATUS);
        }
        for (int i = 0; i < unsuitableReelIds.size(); i++) {
            distributeMap.put(unsuitableReelIds.get(i).getPkCode(), Constant.REELID_CAN_CHECK_STATUS);
        }
        return distributeMap;
    }

    /**
    * 将一批物料代码对应的reelid分配为不可清点
    *@Author: 10307315陈俊熙
    *@date 2022/7/5 下午6:07
    *@param
     * @param suitableReelIds
     * @param unsuitableReelIds
    *@return java.util.Map<java.lang.String,java.lang.String>
    */
    private Map<String, String> setForbiddenCheckStatus(List<PkCodeInfo> suitableReelIds, List<PkCodeInfo> unsuitableReelIds) {
        Map<String, String> distributeMap = new HashMap<>();
        for (int i = 0; i < suitableReelIds.size(); i++) {
            distributeMap.put(suitableReelIds.get(i).getPkCode(), Constant.REELID_FORBIDDEN_CHECK_STATUS);
        }
        // 有序时才会用到，那些低优先级的reelId也需要清点，
        for (int i = 0; i < unsuitableReelIds.size(); i++) {
            distributeMap.put(unsuitableReelIds.get(i).getPkCode(), Constant.REELID_FORBIDDEN_CHECK_STATUS);
        }
        return distributeMap;
    }

    /**
    * 对批次所有物料对应的发料顺序进行处理，
     * 1.按照物料分组，
     * 2.一组数据如果发料顺序seq为空，当做无序处理
     * 3.有序的使用优先级队列进行升序排序，因为seq越小优先级越大。
    *@Author: 10307315陈俊熙
    *@date 2022/7/5 下午5:48
    *@param
     * @param seqEntityList
    *@return java.util.Map<java.lang.String,java.util.PriorityQueue<com.zte.interfaces.dto.TaskMaterialIssueSeqEntityDTO>>
    */
    private Map<String, PriorityQueue<TaskMaterialIssueSeqEntityDTO>> getMapItemToSeqList(List<TaskMaterialIssueSeqEntityDTO> seqEntityList) {
        Map<String, PriorityQueue<TaskMaterialIssueSeqEntityDTO>> resultMap = new HashMap<>();
        if (CollectionUtils.isEmpty(seqEntityList)) {
            return resultMap;
        }
        for (TaskMaterialIssueSeqEntityDTO dto : seqEntityList) {
            // 物料代码为空，或顺序为空，不处理。
            if (StringUtils.isEmpty(dto.getItemNo()) || dto.getSeq() == null) {
                continue;
            }
            PriorityQueue<TaskMaterialIssueSeqEntityDTO> temp = resultMap.get(dto.getItemNo());
            if (temp == null) {
                temp = new PriorityQueue<TaskMaterialIssueSeqEntityDTO>(new Comparator<TaskMaterialIssueSeqEntityDTO>() {
                    @Override
                    public int compare(TaskMaterialIssueSeqEntityDTO o1, TaskMaterialIssueSeqEntityDTO o2) {
                        return o1.getSeq().compareTo(o2.getSeq());
                    }
                });
                resultMap.put(dto.getItemNo(), temp);
            }
            temp.add(dto);
        }
        return resultMap;
    }


    /**
    * 该方法接收输入总的上料详表记录和A.B头表id,区分详表A/B面记录，再去调用合并方法
     * 注意：该方法是对原有combineBom（list，list，map）方法的重载，多做了一步对详表A/B面数据的分类
    *@Author: 10307315陈俊熙
    *@date 2022/7/5 下午5:19
    *@param
     * @param bomDetList
     * @param cfgHeadIdA
     * @param cfgHeadIdB
     * @param mapCraftToLine
    *@return java.util.List<com.zte.domain.model.BSmtBomDetail>
    */
    private List<BSmtBomDetail> combineBom(List<BSmtBomDetail> bomDetList, String cfgHeadIdA, String cfgHeadIdB,
                                           Map<String, String> mapCraftToLine) {
        List<BSmtBomDetail> bomDetListA = new ArrayList<>();
        List<BSmtBomDetail> bomDetListB = new ArrayList<>();
        // A面
        cfgHeadIdA = StringUtils.isEmpty(cfgHeadIdA) ? Constant.STR_EMPTY : cfgHeadIdA;
        // B面
        cfgHeadIdB = StringUtils.isEmpty(cfgHeadIdB) ? Constant.STR_EMPTY : cfgHeadIdB;

        for (int i = 0; i < bomDetList.size(); i++) {
            BSmtBomDetail bSmtBomDetail = bomDetList.get(i);
            if (bSmtBomDetail == null) {
                continue;
            }
            if (cfgHeadIdA.equals(bSmtBomDetail.getCfgHeaderId())) {
                bomDetListA.add(bSmtBomDetail);
            }
            if (cfgHeadIdB.equals(bSmtBomDetail.getCfgHeaderId())) {
                bomDetListB.add(bSmtBomDetail);
            }
        }
        return combineBom(bomDetListA, bomDetListB, mapCraftToLine);
    }

    /**
    *  合并两个A/B面，通过传入的map来决定A/B是否同线体，同线体相同站位需要去重，不同线体则不需要
    *@Author: 10307315陈俊熙
    *@date 2022/7/5 下午5:25
    *@param
     * @param bomDetListA
     * @param bomDetListB
     * @param mapCraftToLine
    *@return java.util.List<com.zte.domain.model.BSmtBomDetail>
    */
    private List<BSmtBomDetail> combineBom(List<BSmtBomDetail> bomDetListA, List<BSmtBomDetail> bomDetListB,
                                           Map<String, String> mapCraftToLine) {
        boolean theSameLineFlag = StringUtils.isNotEmpty(mapCraftToLine.get(Constant.CRAFTSECTION_SMT_A)) &&
                mapCraftToLine.get(Constant.CRAFTSECTION_SMT_A).equals(mapCraftToLine.get(Constant.CRAFTSECTION_SMT_B));
        List<BSmtBomDetail> bomDetList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(bomDetListA)) {
            bomDetList.addAll(bomDetListA);
        }
        if (!CollectionUtils.isEmpty(bomDetListB)) {
            bomDetList.addAll(bomDetListB);
        }
        Map<String, BSmtBomDetail> tempMap = new HashMap<>();
        Set<String> duplicateItemCodeAndLocation = new HashSet<>();

        // A/B同线体
        if (theSameLineFlag) {
            // 对同站位且同物料代码，因为只分配一个首盘，因此默认覆盖值，利用key的唯一性，实现去重。
            for (BSmtBomDetail entity : bomDetList) {
                String key = entity.getItemCode() + Constant.COLON + entity.getLocationNo();
                if (tempMap.containsKey(key)) {
                    duplicateItemCodeAndLocation.add(key);
                }
                tempMap.put(key, entity);
            }
            bomDetList = new ArrayList<>(tempMap.values());
        }
        String cfgHeadA = CollectionUtils.isEmpty(bomDetListA) ? Constant.STR_EMPTY : bomDetListA.get(0).getCfgHeaderId();
        String cfgHeadB = CollectionUtils.isEmpty(bomDetListB) ? Constant.STR_EMPTY : bomDetListB.get(0).getCfgHeaderId();
        // 将站位字段，变更为以A:或B:或AB:开头的形式
        modifyLocation(bomDetList, cfgHeadA, cfgHeadB, duplicateItemCodeAndLocation, theSameLineFlag);
        return bomDetList;
    }

    /**
    * 将站位，加上对应的面别，
     * 例： 1-11-1
     * 如果是A面站位，A:1-11-1;如果是B面站位，B:1-11-1;如果是AB公用，AB:1-11-1
    *@Author: 10307315陈俊熙
    *@date 2022/7/5 下午5:37
    *@param
     * @param bomDetList
     * @param cfgHeadA
     * @param cfgHeadB
     * @param duplicateLocation
     * @param theSameLineFlag
    *@return void
    */
    private void modifyLocation(List<BSmtBomDetail> bomDetList, String cfgHeadA, String cfgHeadB,
                                Set<String> duplicateItemCodeAndLocation, boolean theSameLineFlag) {
        for (BSmtBomDetail entity : bomDetList){
            String key = entity.getItemCode() + Constant.COLON + entity.getLocationNo();
            if (theSameLineFlag && duplicateItemCodeAndLocation.contains(key)) {
                entity.setLocationNo(Constant.STR_UPPERCASE_A + Constant.STR_UPPERCASE_B + Constant.COLON + entity.getLocationNo());
                continue;
            }
            if (StringUtils.equals(cfgHeadA, entity.getCfgHeaderId())) {
                entity.setLocationNo(Constant.STR_UPPERCASE_A + Constant.COLON + entity.getLocationNo());
            }
            if (StringUtils.equals(cfgHeadB, entity.getCfgHeaderId())) {
                entity.setLocationNo(Constant.STR_UPPERCASE_B + Constant.COLON + entity.getLocationNo());
            }
        }
    }

    /**
    * 对比新旧上料表，得到变更涉及的物料代码集合
    *@Author: 10307315陈俊熙
    *@date 2022/7/5 下午6:31
    *@param
     * @param bomDetailList
     * @param bomDetailOldList
    *@return java.util.Set<java.lang.String>
    */
    private Set<String> compareBom(List<BSmtBomDetail> bomDetailList, List<BSmtBomDetail> bomDetailOldList) {
        Set<String> needCheckItemNoSet = new HashSet<>();
        if (CollectionUtils.isEmpty(bomDetailList)) {
            return CollectionUtils.isEmpty(bomDetailOldList) ? needCheckItemNoSet :
                    bomDetailOldList.stream().map(BSmtBomDetail::getItemCode).collect(Collectors.toSet());
        }
        if (CollectionUtils.isEmpty(bomDetailOldList)) {
            return bomDetailList.stream().map(BSmtBomDetail::getItemCode).collect(Collectors.toSet());
        }
        // 转为key为站位，value为物料代码，多条使用逗号隔开(相加情况只有A/B不同线体，且A和B上料表有相同站位时)，
        Map<String, Set<String>> mapLocToItemSet = bomDetailList.stream().collect(Collectors.toMap(
                k -> k.getLocationNo(), v -> {
                    Set<String> itemNoTreeSet = new TreeSet<>();
                    itemNoTreeSet.add(v.getItemCode());
                    return itemNoTreeSet;
                },
                (Set<String> oldSet, Set<String> newSet) -> {
                    oldSet.addAll(newSet);
                    return oldSet;
                }));
        Map<String, Set<String>> oldMapLocToItemSet = bomDetailOldList.stream().collect(Collectors.toMap(
                k -> k.getLocationNo(), v -> {
                    Set<String> itemNoTreeSet = new TreeSet<>();
                    itemNoTreeSet.add(v.getItemCode());
                    return itemNoTreeSet;
                },
                (Set<String> oldSet, Set<String> newSet) -> {
                    oldSet.addAll(newSet);
                    return oldSet;
                }));
        // 遍历新上料表Map的站位,找出新增或更新站位导致需要盘点的物料代码。
        for (Map.Entry<String, Set<String>> entry : mapLocToItemSet.entrySet()) {
            String key = entry.getKey();
            Set<String> value = entry.getValue();
            // 如果key存在，需要进一步判断物料代码是否相同
            if (oldMapLocToItemSet.containsKey(key)) {
                Set<String> oldValue = oldMapLocToItemSet.get(key);
                // 如果key存在，且物料代码相同（使用TreeSet,是因为要有序比较），说明该站位无变更
                if (value.equals(oldValue)) {
                    continue;
                }
                // 如果key存在，但是物料代码不同，把不同的添加到结果集合中
                Set<String> tempSet = new HashSet<>(oldValue);
                oldValue.removeAll(value);
                value.removeAll(tempSet);
                needCheckItemNoSet.addAll(oldValue);
                needCheckItemNoSet.addAll(value);
            }
            // 如果key不存在，说明站位是新的，所以对应的物料代码就需要重新清点
            else {
                needCheckItemNoSet.addAll(value);
            }
        }
        // 遍历旧上料表Map的站位,找出被删除的站位，涉及的物料代码，
        for (Map.Entry<String, Set<String>> entry : oldMapLocToItemSet.entrySet()) {
            String key = entry.getKey();
            Set<String> value = entry.getValue();
            if (mapLocToItemSet.containsKey(key)) {
                // 上一步骤已经处理过
                continue;
            }
            needCheckItemNoSet.addAll(value);
        }
        if (!CollectionUtils.isEmpty(needCheckItemNoSet)) {
            imesLogService.log(needCheckItemNoSet, "物料清点-新旧上料表变更");
        }
        return needCheckItemNoSet;
    }

    /**
    * 判断上料表是否更新过
    *@Author: 10307315陈俊熙
    *@date 2022/7/5 下午6:25
    *@param
     * @param mapCraftToCfgHeadId
     * @param itemCheckHead
    *@return boolean
    */
    private boolean checkBomHasChanged(Map<String, String> mapCraftToCfgHeadId, ItemCheckInfoHead itemCheckHead) {
        String cfgA = mapCraftToCfgHeadId.get(Constant.CRAFTSECTION_SMT_A);
        String cfgB = mapCraftToCfgHeadId.get(Constant.CRAFTSECTION_SMT_B);
        // 要么全为空，要么不为空时equal，否则都是变更
        boolean bomAHasChanged = !(StringUtils.isEmpty(cfgA) && StringUtils.isEmpty(itemCheckHead.getCfgHeaderIdA())
                || StringUtils.isNotEmpty(cfgA) && cfgA.equals(itemCheckHead.getCfgHeaderIdA()));
        boolean bomBHasChanged = !(StringUtils.isEmpty(cfgB) && StringUtils.isEmpty(itemCheckHead.getCfgHeaderIdB())
                || StringUtils.isNotEmpty(cfgB) && cfgB.equals(itemCheckHead.getCfgHeaderIdB()));
        return bomAHasChanged || bomBHasChanged;
    }

    /**
    * 验证批次所属的SMT指令的A/B面上料表是否还生效，通过查看计划服务psWorkOrderSmt表记录的A/B面头表ID是否都在生产管理bSmtBomHead表中生效。
    *@Author: 10307315陈俊熙
    *@date 2022/7/5 下午4:52
    *@param
     * @param mapCraftToCfgHeadId
     * @param bSmtBomHeaderList
     * @param prodPlanId
    *@return void
    */
    private void validateCfgOfSmtWorkOrder(Map<String, String> mapCraftToCfgHeadId, List<BSmtBomHeader> bSmtBomHeaderList,
                                           String prodPlanId) throws Exception {
        // list转map
        Map<String, String> cfgHeadIdMap = bSmtBomHeaderList.stream().collect(Collectors.toMap(
                k -> k.getCraftSection(), v -> v.getCfgHeaderId(), (oldValue, newValue) -> newValue));
        for (Map.Entry<String, String> entry : mapCraftToCfgHeadId.entrySet()) {
            // 如果smt指令表中的SMT-A/B在上料头表无有效数据
            if (!cfgHeadIdMap.containsKey(entry.getKey())) {
                // 如果A面无数据
                if (Constant.CRAFTSECTION_SMT_A.equals(entry.getKey())) {
                    throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, MessageId.SMT_A_BOM_NOT_ACTIVE,
                            new String[]{prodPlanId});
                } else {
                    throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, MessageId.SMT_B_BOM_NOT_ACTIVE,
                            new String[]{prodPlanId});
                }
            }
        }
    }

    /**
     * 将列表转为key=SMT-A/SMT-B,value为cfgHeaderId的Map，同时验证头表id是否为空
     *
     * @param
     * @param psWorkOrderSmtList
     * @return java.util.Map<java.lang.String, java.lang.String>
     * @Author: 10307315陈俊熙
     * @date 2022/6/6 下午4:26
     */
    private Map<String, String> convertToCfgHeaderIdMap(List<PsWorkOrderSmt> psWorkOrderSmtList, String prodPlanId) throws Exception {
        Map<String, String> mapCraftToCfgHeadId = new HashMap<>();
        for (PsWorkOrderSmt entity : psWorkOrderSmtList) {
            if (StringUtils.isEmpty(entity.getWorkOrderNo())) {
                continue;
            }
            if (entity.getWorkOrderNo().contains(Constant.CRAFTSECTION_SMT_A)) {
                processCraftSectionSMTA(entity, mapCraftToCfgHeadId, prodPlanId);
            }
            if (entity.getWorkOrderNo().contains(Constant.CRAFTSECTION_SMT_B)) {
                processCraftSectionSMTB(entity, mapCraftToCfgHeadId, prodPlanId);
            }
        }
        return mapCraftToCfgHeadId;
    }

    private void processCraftSectionSMTA(PsWorkOrderSmt entity, Map<String, String> mapCraftToCfgHeadId, String prodPlanId) {
        // cfgId为空，说明未导入上料表
        if (StringUtils.isEmpty(entity.getCfgHeaderId())) {
            // 判断指令上料表状态
            if (Constant.HAS_IMPORT.equals(entity.getCfgStatus())){
                return;
            }
            throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, MessageId.SMT_A_BOM_NOT_ACTIVE,
                    new String[]{prodPlanId});
        }
        mapCraftToCfgHeadId.put(Constant.CRAFTSECTION_SMT_A, entity.getCfgHeaderId());
    }
    private void processCraftSectionSMTB(PsWorkOrderSmt entity, Map<String, String> mapCraftToCfgHeadId, String prodPlanId) {
        // cfgId为空，说明未导入上料表
        if (StringUtils.isEmpty(entity.getCfgHeaderId())) {
            // 判断指令上料表状态
            if (Constant.HAS_IMPORT.equals(entity.getCfgStatus())){
                return;
            }
            throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, MessageId.SMT_B_BOM_NOT_ACTIVE,
                    new String[]{prodPlanId});
        }
        mapCraftToCfgHeadId.put(Constant.CRAFTSECTION_SMT_B, entity.getCfgHeaderId());
    }

    /**
    * 分页查询批次物料清点详细记录
    *@Author: 10307315陈俊熙
    *@date 2022/7/6 上午9:14
    *@param
     * @param itemCheckDTO
    *@return com.zte.springbootframe.common.model.Page<com.zte.domain.model.ItemCheckInfoDetail>
    */
    @Override
    public Page<ItemCheckInfoDetail> itemCheckRecordQuery(ItemCheckDTO itemCheckDTO) {
        Integer page = itemCheckDTO.getPage() < 1 ? 1 : itemCheckDTO.getPage();
        Integer rows = itemCheckDTO.getRows() < 1 ? Constant.INT_5000 : itemCheckDTO.getRows();
        Integer offset = (page - 1) * rows;
        Integer limit = rows;
        int total = itemCheckinfoDetailService.getCountByProdPlanId(itemCheckDTO.getProdPlanId());
        List<ItemCheckInfoDetail> list = new ArrayList<>();
        if (total != 0) {
            list = itemCheckinfoDetailService.getPageByProdPlanId(itemCheckDTO.getProdPlanId(), offset, limit);
        }
        Page<ItemCheckInfoDetail> result = new Page<>();
        result.setCurrent(page);
        result.setTotal(total);
        result.setRows(list);
        return result;
    }

    /**
    * 物料清点标签打印根方法
    *@Author: 10307315陈俊熙
    *@date 2022/7/6 上午9:14
    *@param
     * @param itemCheckDTO
    *@return void
    */
    @Override
    public void itemCheckPrintReelIdLabel(ItemCheckDTO itemCheckDTO) throws Exception {
        // 根据reelId查询清点详细记录，用于准备打印数据
        ItemCheckInfoDetail detailInfo = itemCheckinfoDetailService.getEntityByReelIdAndProdId
                (itemCheckDTO.getProdPlanId(), itemCheckDTO.getPrintReelId());
        // 校验是否存在清点记录
        if (detailInfo == null) {
            throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, MessageId.REEL_ID_ITEM_CHECK_INFO_NOT_EXIST,
                    new String[]{itemCheckDTO.getPrintReelId()});
        }
        // 校验是否已清点
        if (!Constant.ITEM_CHECK_STATUS_FINISH.equals(detailInfo.getCheckStatus())) {
            throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, MessageId.REEL_ID_ITEM_CHECK_NOT_FINISH,
                    new String[]{itemCheckDTO.getPrintReelId()});
        }
        // 打印
        printLocationLabel(detailInfo, itemCheckDTO.getIp());
    }

    /**
    * 打印主要逻辑实现
    *@Author: 10307315陈俊熙
    *@date 2022/7/6 上午9:16
    *@param
     * @param detailInfo
     * @param ip
    *@return void
    */
    private void printLocationLabel(ItemCheckInfoDetail detailInfo, String ip) throws Exception {
        if (StringUtils.isEmpty(ip)) {
            return;
        }
        BarcodeCenterTemplatePrintDTO barcodeCenterTemplatePrintDTO = generateBasicPrintParams();
        barcodeCenterTemplatePrintDTO.setServerIp(ip);
        Map<String, String> dictionary = getDataDictionary();
        // 设置打印模板和打印机
        barcodeCenterTemplatePrintDTO.setTemplateName(dictionary.get(Constant.TEMPLATE_NAME));
        // 获取打印机，从6680数据字典获取
        // 获取相关数据字典。
        List<SysLookupTypesDTO> sysLookupTypesList = BasicsettingRemoteService.getSysLookUpValue
                (Constant.LOOKUP_TYPE_6680);
        Map<String, String> printMachineTypeToName = sysLookupTypesList.stream().collect(Collectors.toMap(
                k -> (k.getLookupMeaning() == null ? Constant.STR_EMPTY : k.getLookupMeaning().toString()),
                v -> v.getAttribute1(), (oldValue, newValue) -> newValue));
        barcodeCenterTemplatePrintDTO.setPrinter(printMachineTypeToName.get(dictionary.get(Constant.PRINTER)));
        // 打印内容设置
        Integer maxPrintLen = Integer.valueOf(dictionary.get(Constant.MAX_PRINT_LEN));
        // 使用详情表构造参数。
        ItemCheckPrintContentDTO printContentDTO = getPrintContent(detailInfo, maxPrintLen);
        // 设置内容。
        barcodeCenterTemplatePrintDTO.setSubString(JSON.toJSONString(printContentDTO));
        barcodeCenterRemoteService.serverTemplatePrint(barcodeCenterTemplatePrintDTO);
        // 写打印记录
        insertPrintRecord(detailInfo, barcodeCenterTemplatePrintDTO);
    }

    /**
    * 向数据库写打印记录
    *@Author: 10307315陈俊熙
    *@date 2022/7/4 上午11:22
    *@param
     * @param detailInfo
     * @param centerPrintDTO
    *@return void
    */
    private void insertPrintRecord(ItemCheckInfoDetail detailInfo, BarcodeCenterTemplatePrintDTO centerPrintDTO) throws Exception {
        // 获取工号,
        Map<String, String> headerMap = MESHttpHelper.getHttpRequestHeader();
        String empNo = headerMap.get(Constant.X_EMP_NO_SMALL);
        String factoryId = headerMap.get(SysConst.HTTP_HEADER_X_FACTORY_ID_LOW_CASE);
        //写打印记录
        List<PrintRecordDTO> printRecordList = new ArrayList<>();
        PrintRecordDTO printRecord = new PrintRecordDTO();
        printRecord.setPrintId(UUID.randomUUID().toString());
        printRecord.setPrintTemplate(centerPrintDTO.getTemplateName());
        printRecord.setPrintTemplateId(centerPrintDTO.getTemplateName());
        printRecord.setPrintData(centerPrintDTO.getSubString());
        printRecord.setCreateBy(empNo);
        printRecord.setUpdateBy(empNo);
        printRecord.setFactoryId(new BigDecimal(factoryId));
        printRecord.setPrintCategory(MpConstant.IMES_ITEM_CHECK_PRINT);
        printRecord.setPrintType(MpConstant.PRINT_TYPE_ONE);
        printRecord.setSn(detailInfo.getPkCode());
        printRecord.setPrintCount(Constant.INT_1);
        printRecordList.add(printRecord);
        if (!CollectionUtils.isEmpty(printRecordList)) {
            centerfactoryRemoteService.batchInsertPrintRecord(printRecordList);
        }
    }

    /**
    * 得到打印内容
    *@Author: 10307315陈俊熙
    *@date 2022/7/4 上午11:21
    *@param
     * @param detailInfo
     * @param maxPrintLen
    *@return com.zte.interfaces.dto.ItemCheckPrintContentDTO
    */
    private ItemCheckPrintContentDTO getPrintContent(ItemCheckInfoDetail detailInfo, Integer maxPrintLen) {
        ItemCheckPrintContentDTO printContentDTO = new ItemCheckPrintContentDTO();
        String firstTrayLogo = Constant.STR_EMPTY;
        String location = Constant.STR_EMPTY;
        String lineName = Constant.STR_EMPTY;
        String wetLever = StringUtils.isEmpty(detailInfo.getWetLevel()) ? Constant.STR_EMPTY : detailInfo.getWetLevel();
        if (Constant.FLAG_Y.equals(detailInfo.getFirstFlag())) {
            firstTrayLogo = new StringBuilder(Constant.STR_$).append(Constant.STR_$).toString();
        }
        if (!StringUtils.isEmpty(detailInfo.getLocationNo())) {
            location = detailInfo.getLocationNo().replaceAll(Constant.COMMA, Constant.FORWARD_SLASH).replaceAll(Constant.REGEX_BLANK, Constant.STR_EMPTY);
            if (location.indexOf(Constant.STR_LEFT_SQUARE_BRACKETS) > 0) {
                location = location.substring(1,location.length() - 1);
            }
        }
        if (!StringUtils.isEmpty(detailInfo.getLineName())) {
            lineName = detailInfo.getLineName().replaceAll(Constant.COMMA, Constant.FORWARD_SLASH);
        }
        // 需求限制50个字符，虽然个人感觉违反打印模板的初衷。
        int totalLen = firstTrayLogo.length() + location.length() + lineName.length() + wetLever.length();
        if (totalLen > maxPrintLen) {
            location = location.substring(0, location.length() - (totalLen - maxPrintLen));
            location = location.substring(0, location.lastIndexOf(Constant.FORWARD_SLASH));
        }
        printContentDTO.setFirstTrayLogo(firstTrayLogo);
        printContentDTO.setLocation(location);
        printContentDTO.setWetLevel(wetLever);
        printContentDTO.setLineName(lineName);
        return printContentDTO;
    }

    /**
    * 获取打印的相关配置
    *@Author: 10307315陈俊熙
    *@date 2022/7/6 上午9:17
    *@param
    *@return java.util.Map<java.lang.String,java.lang.String>
    */
    private Map<String, String> getDataDictionary() throws Exception {
        // 获取相关数据字典。
        List<SysLookupTypesDTO> sysLookupTypesList = BasicsettingRemoteService.getSysLookUpValue
                (Constant.LOOKUP_TYPE_ITEM_CHECK);
        Map<String, String> lookUpTypeMap = sysLookupTypesList.stream().collect(Collectors.toMap(
                k -> (k.getLookupCode() == null ? Constant.STR_EMPTY : k.getLookupCode().toString()),
                v -> v.getLookupMeaning(), (oldValue, newValue) -> newValue));
        // 查询数据字典，获取打印模板目录，
        String templateName = StringUtils.isEmpty(lookUpTypeMap.get(Constant.LOOKUP_CODE_PRINT_TEMPLATE)) ? Constant.STR_EMPTY
                : lookUpTypeMap.get(Constant.LOOKUP_CODE_PRINT_TEMPLATE);
        // 查询数据字典，获取打印模板目录，
        String printer = StringUtils.isEmpty(lookUpTypeMap.get(Constant.LOOKUP_CODE_PRINT_MACHINE)) ? Constant.STR_EMPTY
                : lookUpTypeMap.get(Constant.LOOKUP_CODE_PRINT_MACHINE);
        // 数据字典获取打印最大字符长度
        String maxPrintLen = StringUtils.isEmpty(lookUpTypeMap.get(Constant.LOOKUP_CODE_PRINT_MAX_LENGTH)) ? Constant.DEFAULT_ITEM_CHECK_PRINT_MAX_LEN
                : lookUpTypeMap.get(Constant.LOOKUP_CODE_PRINT_MAX_LENGTH);
        Map<String, String> resultMap = new HashMap<>(Constant.INT_4);
        resultMap.put(Constant.TEMPLATE_NAME, templateName);
        resultMap.put(Constant.PRINTER, printer);
        resultMap.put(Constant.MAX_PRINT_LEN, maxPrintLen);
        return resultMap;
    }

    /**
    * 生成打印的基本参数
    *@Author: 10307315陈俊熙
    *@date 2022/7/6 上午9:17
    *@param
    *@return com.zte.interfaces.dto.BarcodeCenterTemplatePrintDTO
    */
    private BarcodeCenterTemplatePrintDTO generateBasicPrintParams() {
        BarcodeCenterTemplatePrintDTO barcodeCenterTemplatePrintDTO = new BarcodeCenterTemplatePrintDTO();
        barcodeCenterTemplatePrintDTO.setIdenticalCopies(1);
        barcodeCenterTemplatePrintDTO.setSerializedCopies(1);
        barcodeCenterTemplatePrintDTO.setMarginBottom(NumConstant.NUM_ZERO);
        barcodeCenterTemplatePrintDTO.setMarginTop(NumConstant.NUM_ZERO);
        barcodeCenterTemplatePrintDTO.setMarginLeft(NumConstant.NUM_ZERO);
        barcodeCenterTemplatePrintDTO.setMarginRight(NumConstant.NUM_ZERO);
        return barcodeCenterTemplatePrintDTO;
    }

    /**
    * 物料清点查询根方法
    *@Author: 10307315陈俊熙
    *@date 2022/7/4 上午11:21
    *@param
     * @param itemCheckDifferenceDTO
    *@return com.zte.springbootframe.common.model.Page<com.zte.interfaces.dto.ItemCheckDifferenceDTO>
    */
    @Override
    public Page<ItemCheckDifferenceDTO> itemCheckDifferenceQuery(ItemCheckDifferenceDTO itemCheckDifferenceDTO) throws Exception {
        // 检验参数，可以通过批次或任务号或reelId查询且若只输入任务号，查询批次设置到参数中
        validateProdAndTaskNo(itemCheckDifferenceDTO);
        // 根据参数查询物料清点详情表，分组统计。
        // 得到总的记录
        Integer offset = null;
        Integer limit = null;
        List<ItemCheckDifferenceDTO> totalRecordList = itemCheckinfoDetailService.getTotalQtyGroupByItemNo
                (itemCheckDifferenceDTO, offset, limit);
        // 检验返回是否为空
        if (CollectionUtils.isEmpty(totalRecordList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.RESULT_LIST_EMPTY);
        }
        int total = totalRecordList.size();
        List<ItemCheckDifferenceDTO> pageRecordList = totalRecordList;
        if (itemCheckDifferenceDTO.getPage() != null) {
            Integer page = itemCheckDifferenceDTO.getPage() < 1 ? 1 : itemCheckDifferenceDTO.getPage();
            Integer rows = itemCheckDifferenceDTO.getRows() < 1 ? Constant.INT_5000 : itemCheckDifferenceDTO.getRows();
            offset = (page - 1) * rows;
            limit = rows;
            pageRecordList = itemCheckinfoDetailService.getTotalQtyGroupByItemNo
                    (itemCheckDifferenceDTO, offset, limit);
        }
        // 根据批次查询 单套用量/item_no
        List<PsTask> psTaskList = getTaskQty(itemCheckDifferenceDTO.getProdPlanId());
        // 物料代码
        List<BBomDetailDTO> bomDetailDTOList = getNumByProductCodeAndItemCode(psTaskList, pageRecordList);
        calculateDifferenceCount(pageRecordList,psTaskList,bomDetailDTOList);
        // 得到已清点的记录
        List<ItemCheckDifferenceDTO> checkFinishList = itemCheckinfoDetailService.getCheckQtyGroupByItemNo(itemCheckDifferenceDTO);
        // 计算差异
        calculateDifference(pageRecordList, checkFinishList);
        // 返回数据
        Page<ItemCheckDifferenceDTO> result = new Page<>();
        result.setTotal(total);
        result.setRows(pageRecordList);
        return result;
    }


    /**
     * 设置 计划数量/单套数量/需求数量/差异数量
     *
     * @param pageRecordList   查询的业务数据
     * @param psTaskList       psTask计划数量
     * @param bomDetailDTOList bomDetailDTO单套数量
     */
    private void calculateDifferenceCount(List<ItemCheckDifferenceDTO> pageRecordList, List<PsTask> psTaskList,
                                          List<BBomDetailDTO> bomDetailDTOList) {
        if (CollectionUtils.isEmpty(pageRecordList)) {
            return;
        }
        // 计划数量
        if (!CollectionUtils.isEmpty(psTaskList)) {
            BigDecimal taskQty = psTaskList.get(0).getTaskQty();
            pageRecordList.forEach(p -> p.setTaskQty(taskQty));
        } else {
            pageRecordList.forEach(p -> p.setTaskQty(new BigDecimal("0")));
        }
        // 单套数量
        if (!CollectionUtils.isEmpty(bomDetailDTOList)) {
            Map<String, BigDecimal> usageCountMap =
                    bomDetailDTOList.stream().collect(Collectors.toMap(BBomDetailDTO::getItemCode,
                            BBomDetailDTO::getUsageCount,(k1,k2)->k1));
            for (int i = 0; i < pageRecordList.size(); i++) {
                ItemCheckDifferenceDTO dto = pageRecordList.get(i);
                // 单套数量
                BigDecimal usgaeCount = usageCountMap.get(dto.getItemNo());
                if (usgaeCount == null) {
                    usgaeCount = new BigDecimal("0");
                }
                dto.setUsageCount(usgaeCount);
                // 需求数量
                BigDecimal needCount = usgaeCount.multiply(dto.getTaskQty() == null ? BigDecimal.ZERO : dto.getTaskQty());
                dto.setNeedCount(needCount);
            }
        }
    }

    /**
     * 通过ps_task表的item_no 以及物料代码查询 单套用量
     *
     * @param psTaskList     item_no
     * @param pageRecordList 物料代码
     * @return List<BBomDetailDTO>
     */
    private List<BBomDetailDTO> getNumByProductCodeAndItemCode(List<PsTask> psTaskList,
                                                               List<ItemCheckDifferenceDTO> pageRecordList) throws Exception {
        // 入参为空直接返回
        if (CollectionUtils.isEmpty(psTaskList) || CollectionUtils.isEmpty(pageRecordList)) {
            return new ArrayList<>();
        }
        // 物料代码 prodPlanId 为空直接返回
        String prodPlanId = psTaskList.get(0).getItemNo();
        List<String> itemNoList = new ArrayList<>();
        for (ItemCheckDifferenceDTO itemCheckDifferenceDTO : pageRecordList) {
            if (StringUtils.isNotEmpty(itemCheckDifferenceDTO.getItemNo())) {
                itemNoList.add(itemCheckDifferenceDTO.getItemNo());
            }
        }
        if (itemNoList.isEmpty() || StringUtils.isEmpty(prodPlanId)) {
            return new ArrayList<>();
        }
        itemNoList = itemNoList.stream().distinct().collect(Collectors.toList());
        List<BBomDetailDTO> queryBomParamList = new ArrayList<>();
        for (String itemCode : itemNoList) {
            BBomDetailDTO bBomDetailDTO = new BBomDetailDTO();
            bBomDetailDTO.setItemCode(itemCode);
            bBomDetailDTO.setProductCode(prodPlanId);
            queryBomParamList.add(bBomDetailDTO);
        }
        // 调用接口一次性返回所有的BOM用量
        return CenterfactoryRemoteService.getNumByProductCodeAndItemCode(queryBomParamList);
    }

    /**
     * 根据批次查询 单套用量/item_no
     *
     * @param prodPlanId 批次号
     * @return PsWorkOrderSnDTO
     * @throws Exception 异常
     */
    private List<PsTask> getTaskQty(String prodPlanId) throws Exception {
        if (StringUtils.isEmpty(prodPlanId)) {
            return new ArrayList<>();
        }
        List<PsTask> psTaskList = new ArrayList<>();
        Map<String, Object> map = new HashMap<String, Object>();
        Map<String, String> headerParamsMap = MESHttpHelper.getHttpRequestHeader();
        // 设置查询条件
        map.put("prodplanId", prodPlanId);
        String params = JacksonJsonConverUtil.beanToJson(map);
        // 点对点调用服务
        String serviceName = MicroServiceNameEum.PLANSCHEDULE;
        String version = MicroServiceNameEum.VERSION;
        String sendType = MicroServiceNameEum.SENDTYPEPOST;
        String getUrl = "/PS/getTaskListByProdPlanId";
        String getresult = MicroServiceRestUtil.invokeService(serviceName, version, sendType, getUrl, params,
                headerParamsMap);
        JsonNode json = JacksonJsonConverUtil.getMapperInstance().readTree(getresult);
        String bo = json.get(MpConstant.JSON_BO).toString();
        if (StringHelper.isNotEmpty(bo)) {
            psTaskList = JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<ArrayList<PsTask>>() {
            });
        }
        return psTaskList;
    }

    /**
    * 计算总数和清点数的数量差异和盘数差异，然后更新传入的总记录引用
    *@Author: 10307315陈俊熙
    *@date 2022/7/4 上午9:49
    *@param
     * @param pageRecordList
     * @param checkFinishList
    *@return void
    */
    private void calculateDifference(List<ItemCheckDifferenceDTO> pageRecordList, List<ItemCheckDifferenceDTO> checkFinishList) {
        Map<String, BigDecimal> itemNoToQty = new HashMap<>();
        Map<String, Integer> itemNoToTrayQty = new HashMap<>();
        // 处理清点数据，形成MAP
        for (int i = 0; i < checkFinishList.size(); i++) {
            String itemNo = checkFinishList.get(i).getItemNo();
            BigDecimal qty = checkFinishList.get(i).getItemTotalQty();
            Integer trayQty = checkFinishList.get(i).getItemTrayQty();
            itemNoToQty.put(itemNo, qty);
            itemNoToTrayQty.put(itemNo, trayQty);
        }
        // 计算差异，并处理数据
        for (int i = 0; i < pageRecordList.size(); i++) {
            ItemCheckDifferenceDTO record = pageRecordList.get(i);
            if (record == null || record.getItemTotalQty() == null || record.getItemTrayQty() == null ) {
                continue;
            }
            String itemNo = record.getItemNo();
            BigDecimal itemNoQty = itemNoToQty.get(itemNo);
            BigDecimal itemCheckTotalQty = itemNoQty == null ? new BigDecimal(Constant.STR_0)
                    : itemNoQty;
            Integer itemNoTrayQty = itemNoToTrayQty.get(itemNo);
            Integer itemCheckTrayQty = itemNoTrayQty == null ? 0 : itemNoTrayQty;
            // 相减，且如果结果是小数，四色五入保留一位小数。
            BigDecimal differentQty = record.getItemTotalQty().subtract(itemCheckTotalQty);
            if (differentQty.scale() > 0) {
                differentQty = differentQty.setScale(1, BigDecimal.ROUND_HALF_UP);
            }
            // 相减，且如果结果是小数，四色五入保留一位小数。
            Integer differentTrayQty = record.getItemTrayQty() - itemCheckTrayQty;
            BigDecimal needCount = record.getNeedCount() == null ? new BigDecimal(Constant.STR_0) : record.getNeedCount();
            // 欠料数量，清点总数量-需求数量
            BigDecimal differenceCount = itemCheckTotalQty.subtract(needCount);

            // 设置值
            record.setDifferenceCount(differenceCount);
            record.setItemCheckTotalQty(itemCheckTotalQty);
            record.setItemCheckTrayQty(itemCheckTrayQty);
            record.setDifferentQty(differentQty);
            record.setDifferentTrayQty(differentTrayQty);

        }
    }

    /**
    * 校验批次和计划跟踪单号和reelId必须输入其一，若只输入计划跟踪单号，则查询批次填入传入的对象
    *@Author: 10307315陈俊熙
    *@date 2022/7/1 下午3:07
    *@param
     * @param itemCheckDifferenceDTO
    *@return void
    */
    private void validateProdAndTaskNo(ItemCheckDifferenceDTO itemCheckDifferenceDTO) throws Exception {
        if(StringUtils.isEmpty(itemCheckDifferenceDTO.getPkCode())) {
            // 判断批次和任务号是否存在
            if (StringUtils.isEmpty(itemCheckDifferenceDTO.getProdPlanId())
                    && StringUtils.isEmpty(itemCheckDifferenceDTO.getTaskNo())) {
                throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, MessageId.PROD_PLAN_ID_AND_TASK_NO_ALL_EMPTY);
            }
            if (StringUtils.isEmpty(itemCheckDifferenceDTO.getProdPlanId())) {
                // 根据计划跟踪单号查询批次
                PsTask psTask = PlanscheduleRemoteService.getPsTaskByTaskNo(itemCheckDifferenceDTO.getTaskNo());
                if (ObjectUtils.isEmpty(psTask)) {
                    return;
                }
                itemCheckDifferenceDTO.setProdPlanId(psTask.getProdplanId());
            }
        }
    }

    /**
    * 物料清点查询双击物料差异后展示某个物料代码的清点记录。
    *@Author: 10307315陈俊熙
    *@date 2022/7/6 上午9:18
    *@param
     * @param itemCheckDifferenceDTO
    *@return com.zte.springbootframe.common.model.Page<com.zte.interfaces.dto.ItemCheckInfoDetailDTO>
    */
    @Override
    public Page<ItemCheckInfoDetailDTO> queryDetailByItemNoAndTaskInfo(ItemCheckDifferenceDTO itemCheckDifferenceDTO) throws Exception {
        // 检验参数，且若只输入任务号，查询批次设置到参数中
        validateProdAndTaskNo(itemCheckDifferenceDTO);
        // 查询清点详表
        Integer page = itemCheckDifferenceDTO.getPage() < 1 ? 1 : itemCheckDifferenceDTO.getPage();
        Integer rows = itemCheckDifferenceDTO.getRows() < 1 ? Constant.INT_5000 : itemCheckDifferenceDTO.getRows();
        Integer offset = (page - 1) * rows;
        Integer limit = rows;
        int total = itemCheckinfoDetailService.getCountByItemNoAndProdPlanId(itemCheckDifferenceDTO);
        List<ItemCheckInfoDetailDTO> list = new ArrayList<>();
        if (total != 0) {
            list = itemCheckinfoDetailService.getPageByItemNoAndProdPlanId(itemCheckDifferenceDTO, offset, limit);
        }
        Page<ItemCheckInfoDetailDTO> result = new Page<>();
        result.setCurrent(page);
        result.setTotal(total);
        result.setRows(list);
        return result;
    }

    @Override
    @AsyncExport(functionName = "物料清点导出Excel")
    public void exportExcel(HttpServletResponse response, ItemCheckDifferenceDTO dto) throws Exception {
        dto.setPage(Constant.INT_1);
        dto.setRows(Constant.INT_40000);
        Page<ItemCheckDifferenceDTO> itemList = itemCheckDifferenceQuery(dto);
        // 物料清点详情
        List<ItemCheckInfoDetail> itemDetailList = itemCheckinfoDetailService.getPageByInfo(dto, Constant.INT_0, Constant.INT_40000);
        itemDetailList.sort(Comparator.comparing(ItemCheckInfoDetail::getItemNo));
        // 设置文件名称
        SimpleDateFormat sdf = new SimpleDateFormat(MpConstant.DATE_FORMAT_YYYYMMDD);
        String fileName = sdf.format(new Date()) + UUID.randomUUID().toString().replace("-", "") + MpConstant.ITEM_CHECK_FILE;
        writeExcel(itemList.getRows(), itemDetailList, fileName, response);
    }

    /**
     * 导出电子表格
     */
    public void writeExcel(List<ItemCheckDifferenceDTO> itemList, List<ItemCheckInfoDetail> itemDetailList,
                           String fileName, HttpServletResponse resp) throws Exception {
        tranStatus(itemDetailList);
        String filePath = FileUtils.tempPath + fileName;
        FileUtils.checkFilePath(filePath);
        try (SXSSFWorkbook wb = new SXSSFWorkbook();
            FileOutputStream fileOutputStream = new FileOutputStream(filePath);
            BufferedOutputStream os = new BufferedOutputStream(fileOutputStream)) {
            wb.createSheet(MpConstant.ITEM_CHECK_SHEET_DIFF);
            ExcelCommonUtils.writeSheet(wb.getSheet(MpConstant.ITEM_CHECK_SHEET_DIFF), itemList, ExcelName.ITEM_CHECK_DIFF_EXPORT_TITLE, ExcelName.ITEM_CHECK_DIFF_EXPORT_PROPS);
            wb.createSheet(MpConstant.ITEM_CHECK_SHEET_DETAIL);
            ExcelCommonUtils.writeSheet(wb.getSheet(MpConstant.ITEM_CHECK_SHEET_DETAIL), itemDetailList, ExcelName.ITEM_CHECK_DETAIL_EXPORT_TITLE, ExcelName.ITEM_CHECK_DETAIL_EXPORT_PROPS);
                wb.write(os);
                os.flush();
                wb.dispose();
        }
        asyncExportFileCommonService.uploadFileThenClearLocalFileUpdateLog(fileName, filePath);
    }

    /**
     * 处理状态并工号转为姓名工号
     * @param
     * @param
     * @throws Exception 业务异常
     */
    private void tranStatus(List<ItemCheckInfoDetail> itemDetailList) throws Exception {
        Map<String, String> map = new HashMap<>();
        map.put(Constant.ITEM_CHECK_STATUS_FORBID, Constant.REELID_FORBIDDEN_CHECK_STATUS);
        map.put(Constant.ITEM_CHECK_STATUS_CAN, Constant.REELID_CAN_CHECK_STATUS);
        map.put(Constant.ITEM_CHECK_STATUS_FINISH, Constant.REELID_FINISH_CHECK_STATUS);
        List<String> empNoList = itemDetailList.stream().map(ItemCheckInfoDetail::getLastUpdatedBy).distinct().collect(Collectors.toList());
        Map<String, HrmPersonInfoDTO> hrmPersonInfoDTOMap = centerfactoryRemoteService.getHrmPersonInfo(empNoList);
        for(ItemCheckInfoDetail dto : itemDetailList){
            dto.setCheckStatus(map.get(dto.getCheckStatus()));
            String lastUpdatedBy = dto.getLastUpdatedBy();
            HrmPersonInfoDTO hrmPersonInfoDTO = hrmPersonInfoDTOMap.get(lastUpdatedBy);
            if (hrmPersonInfoDTO != null) {
                dto.setLastUpdatedBy(hrmPersonInfoDTO.getEmpName() + lastUpdatedBy);
            }
            // 只有当状态是已清点时设置最后更新人和最后更新时间
            if(dto.getCheckStatus() == Constant.REELID_FINISH_CHECK_STATUS) {
                dto.setCheckBy(dto.getLastUpdatedBy());
                dto.setCheckDate(dto.getLastUpdatedDate());
            }
        }
    }

    /***
     * reelId标签打印
     *
     * <AUTHOR>
     * @date 2023/9/20 16:54
     */
    @Override
    public void printReelIdLabel(ItemCheckDTO itemCheckDTO) throws Exception {
        // 根据reelId查询记录
        PkCodeInfoDTO param = new PkCodeInfoDTO();
        param.setPkCode(itemCheckDTO.getPrintReelId());
        List<PkCodeInfo> pkCodeInfoList = pkCodeInfoService.getList(param);
        if (CollectionUtils.isEmpty(pkCodeInfoList)) {
            String[] params = new String[]{itemCheckDTO.getPrintReelId(), itemCheckDTO.getProdPlanId()};
            throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, MessageId.REEL_ID_NOT_REGISTER_ITEM_CHECK, params);
        }
        PkCodeInfo pkCodeInfo = pkCodeInfoList.get(Constant.INT_0);
        // 打印
        printReelIdLabel(pkCodeInfo, itemCheckDTO.getIp());
    }

    /**
     * 打印reelid标签
     */
    private void printReelIdLabel(PkCodeInfo pkCodeInfo, String ip) throws Exception {
        BarcodeCenterTemplatePrintDTO barcodeCenterTemplatePrintDTO = generateBasicPrintParams();
        barcodeCenterTemplatePrintDTO.setServerIp(ip);
        Map<String, String> dictionary = getReelIDPrintDictionary();
        // 设置打印模板和打印机
        barcodeCenterTemplatePrintDTO.setTemplateName(dictionary.get(Constant.TEMPLATE_NAME));
        // 获取打印机，从6680数据字典获取
        List<SysLookupTypesDTO> sysLookupTypesList = BasicsettingRemoteService.getSysLookUpValue
                (Constant.LOOKUP_TYPE_6680);
        Map<String, String> printMachineTypeToName = sysLookupTypesList.stream().collect(Collectors.toMap(
                k -> (k.getLookupMeaning() == null ? Constant.STR_EMPTY : k.getLookupMeaning().toString()),
                v -> v.getAttribute1(), (oldValue, newValue) -> newValue));
        barcodeCenterTemplatePrintDTO.setPrinter(printMachineTypeToName.get(dictionary.get(Constant.PRINTER)));
        // 打印内容
        ItemCheckPrintReelIdDTO printReelIdDTO = new ItemCheckPrintReelIdDTO();
        printReelIdDTO.setReelId(pkCodeInfo.getPkCode());
        printReelIdDTO.setItemCode(pkCodeInfo.getItemCode());
        // 设置内容。
        barcodeCenterTemplatePrintDTO.setSubString(JSON.toJSONString(printReelIdDTO));
        barcodeCenterRemoteService.serverTemplatePrint(barcodeCenterTemplatePrintDTO);
        // 写打印记录
        insertReelIDPrintRecord(pkCodeInfo, barcodeCenterTemplatePrintDTO);
    }

    /**
     * 获取reelid打印字典
     */
    private Map<String, String> getReelIDPrintDictionary() throws Exception {
        // 获取物料清点相关数据字典
        List<SysLookupTypesDTO> sysLookupTypesList = BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_ITEM_CHECK);
        Map<String, String> lookUpTypeMap = sysLookupTypesList.stream().collect(Collectors.toMap(
                k -> (k.getLookupCode() == null ? Constant.STR_EMPTY : k.getLookupCode().toString()),
                v -> v.getLookupMeaning(), (oldValue, newValue) -> newValue));
        // 获取reeld打印模板名称
        String templateName = StringUtils.isEmpty(lookUpTypeMap.get(Constant.LOOKUP_CODE_PRINT_REEL_ID_TEMPLATE)) ? Constant.STR_EMPTY
                : lookUpTypeMap.get(Constant.LOOKUP_CODE_PRINT_REEL_ID_TEMPLATE);
        // 获取打印机类型
        String printer = StringUtils.isEmpty(lookUpTypeMap.get(Constant.LOOKUP_CODE_PRINT_MACHINE)) ? Constant.STR_EMPTY
                : lookUpTypeMap.get(Constant.LOOKUP_CODE_PRINT_MACHINE);
        Map<String, String> resultMap = new HashMap<>(Constant.INT_3);
        resultMap.put(Constant.TEMPLATE_NAME, templateName);
        resultMap.put(Constant.PRINTER, printer);
        return resultMap;
    }

    /**
     * 向数据库写reelid打印记录
     */
    private void insertReelIDPrintRecord(PkCodeInfo pkCodeInfo, BarcodeCenterTemplatePrintDTO centerPrintDTO) throws Exception {
        // 获取工号,
        Map<String, String> headerMap = MESHttpHelper.getHttpRequestHeader();
        String empNo = headerMap.get(Constant.X_EMP_NO_SMALL);
        String factoryId = headerMap.get(SysConst.HTTP_HEADER_X_FACTORY_ID_LOW_CASE);
        //写打印记录
        List<PrintRecordDTO> printRecordList = new ArrayList<>();
        PrintRecordDTO printRecord = new PrintRecordDTO();
        printRecord.setPrintId(UUID.randomUUID().toString());
        printRecord.setPrintTemplate(centerPrintDTO.getTemplateName());
        printRecord.setPrintTemplateId(centerPrintDTO.getTemplateName());
        printRecord.setPrintData(centerPrintDTO.getSubString());
        printRecord.setCreateBy(empNo);
        printRecord.setUpdateBy(empNo);
        printRecord.setFactoryId(new BigDecimal(factoryId));
        printRecord.setPrintCategory(MpConstant.IMES_ITEM_CHECK_PRINT);
        printRecord.setPrintType(MpConstant.PRINT_TYPE_ONE);
        printRecord.setSn(pkCodeInfo.getPkCode());
        printRecord.setPrintCount(Constant.INT_1);
        printRecordList.add(printRecord);
        if (!CollectionUtils.isEmpty(printRecordList)) {
            centerfactoryRemoteService.batchInsertPrintRecord(printRecordList);
        }
    }

    /**
     * lfid校验
     * @param itemCheckDTO
     * @return
     * @throws Exception
     */
    public int itemCheckLfIdExist(ItemCheckDTO itemCheckDTO) throws Exception {
        VReelidInfoDTO dto = new VReelidInfoDTO();
        dto.setSusr1(itemCheckDTO.getProdPlanId());
        dto.setDropid(itemCheckDTO.getLfId());
        List<VReelidInfo> listResult = datawbRemoteService.getReelidInfoList(dto);
        if (CollectionUtils.isEmpty(listResult)) {
            // {0}批次未找到lfid为{1}的发料记录
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.LFID_NOT_FOUND_IN_PROD,
                    new String[]{itemCheckDTO.getProdPlanId(), itemCheckDTO.getLfId()});
        }
        return Constant.INT_1;
    }

    /**
     * 校验Reel ID是否已注册
     * @param pkCode
     * @return
     * @throws Exception
     */
    public int checkReelIdRegistered(String pkCode) throws Exception {
        PkCodeInfo pkCodeInfo = centerfactoryRemoteService.getPkCodeInfo(pkCode);
        if (pkCodeInfo != null) {
            // {0}REEL ID已注册，请确认
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.REEL_ID_REGISTERED,
                    new String[]{pkCode});
        }
        return Constant.INT_1;
    }

    /**
     * lfid + 220 条码校验
     * @param dto
     * @return
     * @throws Exception
     */
    public BatchReelIdDataDTO batchReelIdDataProcess(VReelidInfoDTO dto) throws Exception {
        BatchReelIdDataDTO batchReelIdDataDTO = centerfactoryRemoteService.batchReelIdDataProcess(dto);
        if (batchReelIdDataDTO == null || batchReelIdDataDTO.getTotalNum().compareTo(new BigDecimal(Constant.STRING_0)) < 1) {
            // {0}条码在{1}lfid未找到发料记录，请确认
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.RECORD_NOT_FOUND_WITH_BARCODE,
                    new String[]{dto.getLottable02(),dto.getDropid()});
        }
        if (batchReelIdDataDTO.getRegisterTotalQtyNum().compareTo(batchReelIdDataDTO.getTotalNum()) >= 0 ) {
            // {0}lfid{1}条码已完成注册，不允许再次注册
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.BARCODE_HAS_REGISTERED,
                    new String[]{dto.getDropid(),dto.getLottable02()});
        }
        return batchReelIdDataDTO;
    }

    /**
     * reelID注册
     * @param dto
     * @return
     * @throws Exception
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public PkCodeInfoDTO reelIdRegister(PkCodeInfoDTO dto, String ip) throws Exception {
        // 生成reelId
        dto.setPkCode(genReelId(String.valueOf(dto.getFactoryId())));
        //1、调用中心工厂服务写入pk_code_info，包含写入本地
        // 获取ztebarcode信息，设置防潮等级
        Ztebarcode ztebarcode = datawbRemoteService.getZteBarcodeInfo(dto.getSourceBatchCode());
        if (null != ztebarcode) {
            dto.setWetLevel(ztebarcode.getWetlevel());
        }
        // 根据物料代码获取物料名称
        String itemName = dto.getItemName();
        if (StringUtils.isNotBlank(dto.getItemCode()) && StringUtils.isBlank(itemName)) {
            itemName = datawbRemoteService.getItemNameByItemNo(dto.getItemCode());
            dto.setItemName(itemName);
        }
        // 加锁：使用reelid的物料代码+批次上锁
        String redisKey = String.format(RedisKeyConstant.ITEM_CHECK_REEL_ID_REGISTERING, dto.getProductTask(),dto.getItemCode());
        RedisLock redisLock = new RedisLock(redisKey);
        if (!redisLock.lock()) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.FAILED_TO_GET_REDIS_LOCK);
        }
        try{
            PkCodeInfo pkCodeInfo = PkCodeInfoAssembler.toEntity(dto);
            // 1、写入本地工厂
            pkCodeInfoService.lateReelIdBindToLocal(pkCodeInfo);
            // 2、站位标签打印
            handleAfterReelidReg(pkCodeInfo, ip);
            // 3、写入中心工厂
            centerfactoryRemoteService.centerFactoryInsertPkCodeInfo(dto);
            return dto;
        }finally {
            redisLock.unlock();
        }
    }

    /**
     * 生成一个reelid
     */
    private String genReelId(String factoryId) throws Exception {
        Map<String, String> header = MESHttpHelper.getHttpRequestHeader();
        String token = header.get(Constant.X_AUTH_VALUE.toLowerCase());
        List<String> reelIds = centerfactoryRemoteService.createReelIds(Constant.STR_1, factoryId, token);
        if (org.apache.commons.collections.CollectionUtils.isEmpty(reelIds)) {
            throw new MesBusinessException(com.zte.itp.msa.core.model.RetCode.BUSINESSERROR_CODE, MessageId.FAILED_TO_GENERATE_REELID_IN_THE_FACTORY);
        }
        return reelIds.get(Constant.INT_0);
    }

    /**
     * reelid注册后写入物料清点明细表
     */
    private int handleAfterReelidReg(PkCodeInfo pkCodeInfo, String ip) throws Exception {
        ItemCheckDTO itemCheckDTO = new ItemCheckDTO();
        itemCheckDTO.setProdPlanId(pkCodeInfo.getProductTask());
        itemCheckDTO.setReelId(pkCodeInfo.getPkCode());
        // 校验参数
        validateProdAndReelId(itemCheckDTO);
        // 获取物料清点头信息
        ItemCheckInfoHead itemCheckInfoHead = itemCheckinfoHeadService.getEntityByProdPlanId(itemCheckDTO.getProdPlanId());
        if (itemCheckInfoHead == null) {
            // 没有找到批次{0}的清点信息
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.INVENTORY_INFORMATION_NOT_FOUND,
                    new String[]{itemCheckDTO.getProdPlanId()});
        }
        // 获取品牌信息
        stItemBarcodeService.pkCodeChange(Arrays.asList(pkCodeInfo));
        // 组装物料清点详表
        ItemCheckInfoDetail itemCheckInfoDetail = generateInsertEntity(pkCodeInfo, Constant.REELID_CAN_CHECK_STATUS, itemCheckInfoHead.getId(), new HashMap<>());
        itemCheckInfoDetail.setBrandName(pkCodeInfo.getBgBrandNo());
        // reelid注册时 为注册类型
        itemCheckInfoDetail.setCheckType(Constant.CHECK_TYPE_REGISTER);
        // 写入物料清点详表
        itemCheckinfoDetailService.batchInsertByList(Arrays.asList(itemCheckInfoDetail));
        // 查询批次物料代码对应的详细表,并合并AB面
        List<BSmtBomDetail> bSmtBomDetailList = getBomDetailByItemNo(itemCheckInfoDetail, itemCheckDTO);
        DistributeLocationNotPreDTO distributeLocationNotPre = getLocationNotPreDTO(bSmtBomDetailList, itemCheckInfoDetail, ip);
        doWhenNotPreDist(distributeLocationNotPre, true);
        // 标记清点状态未未清点
        itemCheckInfoDetail.setCheckStatus(Constant.ITEM_CHECK_STATUS_CAN);
        itemCheckinfoDetailService.updateCheckInfoByReelId(itemCheckInfoDetail);
        return Constant.INT_1;
    }

    private DistributeLocationNotPreDTO getLocationNotPreDTO(List<BSmtBomDetail> bSmtBomDetailList,
                                                             ItemCheckInfoDetail itemCheckInfoDetail,
                                                             String ip) {
        DistributeLocationNotPreDTO distributeLocationNotPre = new DistributeLocationNotPreDTO();
        distributeLocationNotPre.setHasSeq(false);
        distributeLocationNotPre.setbSmtBomDetailList(bSmtBomDetailList);
        List<ItemCheckInfoDetail> itemCheckDetList =
                itemCheckinfoDetailService.getListByItemNoAndProdId(itemCheckInfoDetail.getItemNo(),
                        itemCheckInfoDetail.getProdPlanId());
        distributeLocationNotPre.setItemCheckDetList(itemCheckDetList);
        distributeLocationNotPre.setItemCheckInfoDetail(itemCheckInfoDetail);
        distributeLocationNotPre.setIgnorePriorityFlag(false);
        distributeLocationNotPre.setPrintMachineIp(ip);
        distributeLocationNotPre.setTheSameSeqLotAndSupSet(new HashSet<>());
        return distributeLocationNotPre;
    }

    @Override
    public Integer countExportTotal(ItemCheckDifferenceDTO itemCheckDifferenceDTO) {
        // 检验参数，且若只输入任务号，查询批次设置到参数中
        try {
            validateProdAndTaskNo(itemCheckDifferenceDTO);
        } catch (Exception e) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, e.getMessage());
        }
        return itemCheckinfoDetailService.getCountByItemNoAndProdPlanId(itemCheckDifferenceDTO);
    }

    @Override
    public List<ItemCheckInfoDetailExportDTO> queryExportData(ItemCheckDifferenceDTO itemCheckDifferenceDTO, int pageNo,int pageSize) {
        Page<ItemCheckDifferenceDTO> page = new Page<>(pageNo, pageSize);
        page.setParams(itemCheckDifferenceDTO);
        List<ItemCheckInfoDetailDTO> detailDTOList = itemCheckinfoDetailService.getPageByItemNoAndProdPlanId(itemCheckDifferenceDTO, null, null);
        List<ItemCheckInfoDetailExportDTO> resultList = new ArrayList<>();
        for (ItemCheckInfoDetailDTO detailDTO : detailDTOList) {
            ItemCheckInfoDetailExportDTO exportDTO = new ItemCheckInfoDetailExportDTO();
            BeanUtils.copyProperties(detailDTO, exportDTO);
            exportDTO.setReelId(detailDTO.getPkCode());
            // 导出信息转化
            if (StringUtils.equals(exportDTO.getCheckStatus(),Constant.MINUS_ONE)) {
                exportDTO.setCheckStatus(Constant.REELID_FORBIDDEN_CHECK_STATUS);
            } else if (StringUtils.equals(exportDTO.getCheckStatus(),Constant.STR_0)) {
                exportDTO.setCheckStatus(Constant.REELID_CAN_CHECK_STATUS);
            } else if (StringUtils.equals(exportDTO.getCheckStatus(),Constant.STR_1)) {
                exportDTO.setCheckStatus(Constant.REELID_FINISH_CHECK_STATUS);
            }

            if (StringUtils.equals(exportDTO.getContainerType(),Constant.STR_0)) {
                exportDTO.setContainerType(Constant.CONTAINER_TYPE_0);
            } else if (StringUtils.equals(exportDTO.getContainerType(),Constant.STR_1)) {
                exportDTO.setContainerType(Constant.CONTAINER_TYPE_1);
            }
            resultList.add(exportDTO);
        }
        return resultList;
    }
}
