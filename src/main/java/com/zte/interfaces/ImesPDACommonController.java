package com.zte.interfaces;

import com.zte.application.ImesPDACommonService;
import com.zte.application.SmtMachineMTLHistoryLService;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.BusinessConstant;
import com.zte.common.utils.Constant;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.SmtMachineMTLHistoryL;
import com.zte.interfaces.dto.OneKeySwitchScanLocationDto;
import com.zte.interfaces.dto.PDAQCSpotCheckDTO;
import com.zte.interfaces.dto.PDARcvOldPkCodeDto;
import com.zte.interfaces.dto.PDAReceiveItemsScanDTO;
import com.zte.interfaces.dto.PDAReelIdQtyModifyDto;
import com.zte.interfaces.dto.PDAReelIdSplitDto;
import com.zte.interfaces.dto.PDATransferScanCommonDto;
import com.zte.interfaces.dto.PDATransferScanDTO;
import com.zte.interfaces.dto.SmtMachineMTLHistoryLDTO;
import com.zte.interfaces.dto.SmtMachineMaterialMoutingDTO;
import com.zte.itp.msa.core.constant.SysGlobalConst;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.string.StringHelper;
import com.zte.springbootframe.common.annotation.RecordLogAnnotation;
import com.zte.springbootframe.common.consts.SysConst;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.FactoryConfig;
import com.zte.springbootframe.util.RequestHeadValidationUtil;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.List;


/**
 * IMES-PDA公共接口入口
 *
 * <AUTHOR>
 **/
@RestController
@RequestMapping("/PDA")
@Api(description = "IMESPDA公共接口入口API")
public class ImesPDACommonController {
	/**
	 * 日志对象
	 */
	private static final Logger LOG = LoggerFactory.getLogger(ImesPDACommonController.class);

	@Autowired
	private ImesPDACommonService imesPdaCommonService;

	@Autowired
	private SmtMachineMTLHistoryLService smtMachineMTLHistoryLService;

	@Autowired
	private FactoryConfig factoryConfig;

	@ApiOperation("PDA接料扫描")
	@RecordLogAnnotation("PDA接料扫描")
	@RequestMapping(value = "/pdaReceiveItemsScan", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ServiceData<String> pdaReceiveItemsScan(HttpServletRequest request, @RequestBody List<PDAReceiveItemsScanDTO> pdaList) throws Exception {
		ServiceData<String> ret = new ServiceData<String>();
		RetCode retCode = new RetCode(RetCode.BUSINESSERROR_CODE,RetCode.BUSINESSERROR_MSGID);
		String empNo = request.getHeader(SysGlobalConst.HTTP_HEADER_X_EMP_NO);
		if(CollectionUtils.isEmpty(pdaList)){
			retCode.setMsg(BusinessConstant.EMPTY_INFO);
			ret.setCode(retCode);
			ret.setBo(BusinessConstant.EMPTY_INFO);
		}
		String factoryId = request.getHeader(SysConst.HTTP_HEADER_X_FACTORY_ID);
		for(PDAReceiveItemsScanDTO scanInfo : pdaList){
			scanInfo.setEmpNo(empNo);
			if (scanInfo.getEntityId() == null) {
				scanInfo.setEntityId(new BigDecimal(factoryConfig.getCommonEntityId()));
			}
			if (scanInfo.getFactoryId() == null) {
				if(StringHelper.isNotEmpty(factoryId)) {
					scanInfo.setFactoryId(new BigDecimal(factoryId));
				}
			}
		}
		if (pdaList.get(NumConstant.NUM_ZERO).getFactoryId() == null){
			retCode.setMsg(BusinessConstant.GET_FACTORY_FIAL);
			ret.setCode(retCode);
			ret.setBo(BusinessConstant.GET_FACTORY_FIAL);
			return ret;
		}

		try {
			retCode = imesPdaCommonService.pdaReceiveItemsScan(pdaList);
		}catch (Exception e){
			String result = BusinessConstant.ITEM_SCAN_FIAL + ":" + e.getMessage();
			retCode.setMsg(result);
			ret.setCode(retCode);
			insertErrorHistory(pdaList.get(NumConstant.NUM_ZERO), result);
			return ret;
		}
		if(!Constant.API_RESULT_CODE_SUCCESS.equals(retCode.getCode())){
			ret.setCode(retCode);
			ret.setBo(retCode.getMsg());
			insertErrorHistory(pdaList.get(NumConstant.NUM_ZERO), retCode.getMsg());
		}else{
			ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
		}
		return ret;
	}

	@ApiOperation("PDA接料扫描极性校验")
	@RequestMapping(value = "/pdaRsPolarCheck", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ServiceData<String> pdaRsPolarCheck(HttpServletRequest request, @RequestBody PDAReceiveItemsScanDTO dto) throws Exception {
		ServiceData<String> ret = new ServiceData<String>();
		RetCode retCode = new RetCode(RetCode.BUSINESSERROR_CODE,RetCode.BUSINESSERROR_MSGID);
		String factoryId = request.getHeader(SysConst.HTTP_HEADER_X_FACTORY_ID);
		if (dto.getEntityId() == null) {
			dto.setEntityId(new BigDecimal(factoryConfig.getCommonEntityId()));
		}
		if (dto.getFactoryId() == null) {
			if(StringHelper.isNotEmpty(factoryId)) {
				dto.setFactoryId(new BigDecimal(factoryId));
			}
		}
		if (dto.getFactoryId() == null){
			retCode.setMsg(BusinessConstant.GET_FACTORY_FIAL);
			ret.setCode(retCode);
			ret.setBo(BusinessConstant.GET_FACTORY_FIAL);
			return ret;
		}
		String result;
		try {
			result  = imesPdaCommonService.polarCheck(dto);
		}catch (Exception e){
			retCode.setMsg(BusinessConstant.ITEM_SCAN_FIAL);
			ret.setCode(retCode);
			return ret;
		}
		// 不为空意味着报错。
		if(StringUtils.isNotEmpty(result)){
			retCode.setMsg(result);
			ret.setCode(retCode);
		}else{
			ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
		}
		return ret;
	}

	@ApiOperation("接料扫描旧ReelID检验转机/接料复检是否完成")
	@RequestMapping(value = "/pdaReceiveCheck", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
	public ServiceData<SmtMachineMaterialMoutingDTO> pdaReceiveCheck(HttpServletRequest request, SmtMachineMaterialMoutingDTO dto) throws Exception {
		return imesPdaCommonService.pdaReceiveCheck(request.getHeader(SysConst.HTTP_HEADER_X_FACTORY_ID),dto);
	}

	@ApiOperation("PDA转机扫描")
	@RecordLogAnnotation("PDA转机扫描")
	@RequestMapping(value = "/pdaTransferScan", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ServiceData pdaTransferScan(HttpServletRequest request, @RequestBody PDATransferScanDTO dto) throws Exception {
		ServiceData ret = new ServiceData();
		RetCode retCode = new RetCode(RetCode.BUSINESSERROR_CODE,RetCode.BUSINESSERROR_MSGID);
		if (dto.getEntityId() == null) {
			dto.setEntityId(new BigDecimal(factoryConfig.getCommonEntityId()));
		}
		if (dto.getFactoryId() == null) {
			String factoryId = request.getHeader(SysConst.HTTP_HEADER_X_FACTORY_ID);
			if(StringHelper.isNotEmpty(factoryId)) {
				dto.setFactoryId(new BigDecimal(factoryId));
			}
		}
		if (dto.getFactoryId() == null){
			retCode.setMsg(BusinessConstant.GET_FACTORY_FIAL);
			ret.setCode(retCode);
			ret.setBo(BusinessConstant.GET_FACTORY_FIAL);
			return ret;
		}
		String result;
		try {
			result = imesPdaCommonService.pdaTransferScan(dto);
		}catch (Exception e){
			String errTip = BusinessConstant.TRANSFER_SCAN_FAIL + ":" + e.getMessage();
			retCode.setMsg(errTip);
			ret.setCode(retCode);
			ret.setBo(errTip);
			try {
				insertErrorHistory(dto, errTip);
			}catch (Exception e2){}
			return ret;
		}
		if(StringHelper.isNotEmpty(result)){
			retCode.setMsg(result);
			ret.setCode(retCode);
			ret.setBo(result);
			try {
				insertErrorHistory(dto, result);
			}catch (Exception e){}
		}else{
			ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
			ret.setBo(result);
		}
		return ret;
	}

	@ApiOperation("5G城堡单板子卡ReelID批次校验")
	@RequestMapping(value = "/pkCodeSourceTaskCheck", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ServiceData pkCodeSourceTaskCheck(HttpServletRequest request, @RequestBody PDATransferScanDTO dto) throws Exception {
		ServiceData ret = new ServiceData();
		RetCode retCode = new RetCode(RetCode.BUSINESSERROR_CODE,RetCode.BUSINESSERROR_MSGID);
		if (dto.getEntityId() == null) {
			dto.setEntityId(new BigDecimal(factoryConfig.getCommonEntityId()));
		}
		if (dto.getFactoryId() == null) {
			String factoryId = request.getHeader(SysConst.HTTP_HEADER_X_FACTORY_ID);
			if(StringHelper.isNotEmpty(factoryId)) {
				dto.setFactoryId(new BigDecimal(factoryId));
			}
		}
		if (dto.getFactoryId() == null){
			retCode.setMsg(BusinessConstant.GET_FACTORY_FIAL);
			ret.setCode(retCode);
			ret.setBo(BusinessConstant.GET_FACTORY_FIAL);
			return ret;
		}
		Boolean result = imesPdaCommonService.pkCodeSourceTaskCheck(dto.getPartsSourceTask(), dto.getSourceTask());
		ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
		ret.setBo(result);
		return ret;
	}


	@ApiOperation("接料时如果批次不匹配时。查询是否时是5G城堡单板子卡情况、或者跨批次挪料情况")
	@RequestMapping(value = "/pkCodeCastleOrAppropriation", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ServiceData pkCodeCastleOrAppropriation(HttpServletRequest request, @RequestBody PDATransferScanDTO dto) throws Exception {
		ServiceData ret = new ServiceData();
		RetCode retCode = new RetCode(RetCode.BUSINESSERROR_CODE, RetCode.BUSINESSERROR_MSGID);
		if (dto.getEntityId() == null) {
			dto.setEntityId(new BigDecimal(factoryConfig.getCommonEntityId()));
		}
		if (dto.getFactoryId() == null) {
			String factoryId = request.getHeader(SysConst.HTTP_HEADER_X_FACTORY_ID);
			if (StringHelper.isNotEmpty(factoryId)) {
				dto.setFactoryId(new BigDecimal(factoryId));
			}
		}
		if (dto.getFactoryId() == null) {
			retCode.setMsg(BusinessConstant.GET_FACTORY_FIAL);
			ret.setCode(retCode);
			ret.setBo(BusinessConstant.GET_FACTORY_FIAL);
			return ret;
		}
        String empNo = request.getHeader(SysConst.HTTP_HEADER_X_EMP_NO);
		Boolean result = imesPdaCommonService.pkCodeCastleOrAppropriation(dto.getPartsSourceTask(), dto.getSourceTask(), dto.getDrItemCode(), dto.getLineCode(), empNo);
		ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
		ret.setBo(result);
		return ret;
	}

	private void insertErrorHistory(PDATransferScanDTO entity, String message) throws MesBusinessException{
		if (entity.getFormFlag().longValue() == NumConstant.NUM_ZERO) {
			SmtMachineMTLHistoryL historyLObj = new SmtMachineMTLHistoryL();
			historyLObj.setEntityId(entity.getEntityId());
			historyLObj.setFactoryId(entity.getFactoryId());
			historyLObj.setCreateUser(entity.getUserId());
			historyLObj.setLineCode(entity.getLineCode());
			if(StringUtils.isBlank(historyLObj.getLineCode())){
				historyLObj.setLineCode(entity.getLineCode2());
			}
			historyLObj.setWorkOrder(entity.getWorkOrder());
			if(StringUtils.isBlank(historyLObj.getWorkOrder())){
				historyLObj.setWorkOrder(entity.getWorkOrder2());
			}
			historyLObj.setLastUpdatedBy(entity.getUserId());
			historyLObj.setCfgHeaderId(entity.getCfgHeaderId());
			historyLObj.setMountType(NumConstant.STR_ONE);
			historyLObj.setLocationNo(entity.getDrLocationNo());
			historyLObj.setModuleNo(entity.getModuleNo());
			historyLObj.setMachineNo(entity.getDrMachineNo());
			historyLObj.setPolarInfo("");
			historyLObj.setObjectId(entity.getPkCode());
			if (StringUtils.isBlank(message)) {
				message = Constant.ERROR;
			}
			historyLObj.setOperateMsg(message);
			historyLObj.setLastScanFlag(false);
			historyLObj.setItemCode(entity.getDrItemCode());
			historyLObj.setQty(new BigDecimal(NumConstant.NUM_ZERO));
			smtMachineMTLHistoryLService.insertSmtMachineMTLHistoryAll(historyLObj);
		}
	}
	private void insertErrorHistory(PDAReceiveItemsScanDTO entity, String message) throws MesBusinessException{
		SmtMachineMTLHistoryL historyLObj = new SmtMachineMTLHistoryL();
		historyLObj.setEntityId(entity.getEntityId());
		historyLObj.setFactoryId(entity.getFactoryId());
		historyLObj.setCreateUser(entity.getUserId());
		historyLObj.setLastUpdatedBy(entity.getUserId());
		historyLObj.setLineCode(entity.getLineCode());
		historyLObj.setWorkOrder(entity.getWorkOrder());
		historyLObj.setCfgHeaderId(entity.getCfgHeaderId());
		historyLObj.setMountType(NumConstant.STR_TWO);
		historyLObj.setLocationNo(entity.getOldLocationNo());
		historyLObj.setLastScanFlag(false);
		historyLObj.setMachineNo(entity.getOldMachineNo());
		historyLObj.setIsLead(entity.getIsLead());
		historyLObj.setPolarInfo(Constant.STR_EMPTY);
		historyLObj.setObjectId(entity.getNewPkCode());
		historyLObj.setAttr2(entity.getOldPkCode());
		historyLObj.setAttr3(entity.getOldQty().toString());
		historyLObj.setModuleNo(entity.getModuleNo());
		if (StringUtils.isBlank(message)) {
			message = Constant.ERROR;
		}
		historyLObj.setOperateMsg(message);
		historyLObj.setItemCode(entity.getOldItemCode());
		historyLObj.setQty(entity.getOldQty());
		smtMachineMTLHistoryLService.insertSmtMachineMTLHistoryAll(historyLObj);
	}
	
	
	@ApiOperation("PDA上料扫描")
	@RequestMapping(value = "/dipMaterialScan", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ServiceData dipMaterialScan(HttpServletRequest request, @RequestBody SmtMachineMTLHistoryLDTO dto) throws Exception {
		ServiceData ret = new ServiceData();
		RetCode retCode = new RetCode(RetCode.BUSINESSERROR_CODE,RetCode.BUSINESSERROR_MSGID);
		String empNo = request.getHeader(SysGlobalConst.HTTP_HEADER_X_EMP_NO);		

		String factoryId = request.getHeader(SysConst.HTTP_HEADER_X_FACTORY_ID);
		if(StringHelper.isEmpty(factoryId)) {
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.FACTORY_ID_IS_NULL);
		}
		dto.setFactoryId(new BigDecimal(factoryId));
		dto.setCreateUser(empNo);
		dto.setLastUpdatedBy(empNo);
		try {
	        dto.setModuleNo(CommonUtils.getFromBase64(dto.getModuleNo()));
	        dto.setMachineNo(CommonUtils.getFromBase64(dto.getMachineNo()));
	        dto.setLocationNo(CommonUtils.getFromBase64(dto.getLocationNo()));
			imesPdaCommonService.dipMaterialScan(dto);
		}catch (Exception e){
			throw e;
		}
		ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
		ret.setBo("");
		return ret;
	}

	@ApiOperation("手补料上料扫描")
	@RequestMapping(value = "/handFeedingScan",
			method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_VALUE,
			produces = MediaType.APPLICATION_JSON_VALUE)
	@ResponseBody
	public ServiceData handFeedingScan(HttpServletRequest request, @RequestBody SmtMachineMaterialMoutingDTO dto) throws Exception {
		RequestHeadValidationUtil.validaFactoryIdAndEmpno(request);
		String empNo = request.getHeader(SysGlobalConst.HTTP_HEADER_X_EMP_NO);
		dto.setCreateUser(empNo);
		dto.setLastUpdatedBy(empNo);
		String factoryId = request.getHeader(SysConst.HTTP_HEADER_X_FACTORY_ID);
		dto.setFactoryId(new BigDecimal(factoryId));
		dto.setEntityId(new BigDecimal(factoryConfig.getCommonEntityId()));
		SmtMachineMaterialMoutingDTO smtMachineMaterialMoutingDTO=imesPdaCommonService.handFeedingScan(dto);
		ServiceData ret=new ServiceData();
		ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
		ret.setBo(smtMachineMaterialMoutingDTO);
		return ret;
	}

	@ApiOperation("PDA 转机扫描/QC转机巡检/QC抽检/生产物料巡检-查询模组下拉数据")
	@PostMapping("/getTransferScanModuleSelectData")
	public ServiceData<List<PDATransferScanCommonDto>> getTransferScanModuleSelectData(HttpServletRequest request, @RequestBody PDATransferScanCommonDto dto) throws Exception {
		// 校验工号和工厂ID
		RequestHeadValidationUtil.validaFactoryIdAndEmpno(request);
		String factoryId=request.getHeader(SysConst.HTTP_HEADER_X_FACTORY_ID);
		dto.setFactoryId(new BigDecimal(factoryId));
		ServiceData<List<PDATransferScanCommonDto>> ret = new ServiceData<>();
		List<PDATransferScanCommonDto> list = imesPdaCommonService.getTransferScanModuleSelectData(dto);
		ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
		ret.setBo(list);
		return ret;
	}


	@ApiOperation("PDA转机扫描查询模组上料信息")
	@PostMapping(value = "/getModuleBSmtBomDetailInfo")
	public ServiceData getModuleBSmtBomDetailInfo(HttpServletRequest request, @RequestBody PDATransferScanCommonDto dto) throws Exception {
		RequestHeadValidationUtil.validaFactoryIdAndEmpno(request);
		String factoryId=request.getHeader(SysConst.HTTP_HEADER_X_FACTORY_ID);
		dto.setFactoryId(new BigDecimal(factoryId));
		return imesPdaCommonService.getModuleBSmtBomDetailInfo(dto);
	}

	@ApiOperation("PDA QC转机巡检 生产物料巡检查询模组上料信息")
	@PostMapping(value = "/getModuleBSmtBomDetailInfoForQCTransfer")
	public ServiceData getModuleBSmtBomDetailInfoForQCTransfer(HttpServletRequest request, @RequestBody PDATransferScanCommonDto dto) throws Exception {
		RequestHeadValidationUtil.validaFactoryIdAndEmpno(request);
		String factoryId=request.getHeader(SysConst.HTTP_HEADER_X_FACTORY_ID);
		dto.setFactoryId(new BigDecimal(factoryId));
		return imesPdaCommonService.getModuleBSmtBomDetailInfoForQCTransfer(dto);
	}

	@ApiOperation("PDA一键切换扫描站位")
	@RequestMapping(value = "/scanLocationSnOneKeySwitch", method = RequestMethod.GET,
			produces = MediaType.APPLICATION_JSON_VALUE)
	@ResponseBody
	public ServiceData scanLocationSnOneKeySwitch(HttpServletRequest request, @RequestParam String locationSn) {
		RequestHeadValidationUtil.validaFactoryIdAndEmpno(request);
		String factoryId=request.getHeader(SysConst.HTTP_HEADER_X_FACTORY_ID);
		if(StringUtils.isEmpty(factoryId)){
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.FACTORY_ID_IS_NULL);
		}
		ServiceData<OneKeySwitchScanLocationDto> ret = new ServiceData<>();
		OneKeySwitchScanLocationDto dto = imesPdaCommonService.scanLocationSnOneKeySwitch(locationSn,factoryId);
		ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
		ret.setBo(dto);
		return ret;
	}

	@ApiOperation("PDAReelId拆分扫描旧reelid")
	@RequestMapping(value = "/scanOldReelIdForReelIdSplit", method = RequestMethod.GET,
			produces = MediaType.APPLICATION_JSON_VALUE)
	@ResponseBody
	public ServiceData scanOldReelIdForReelIdSplit(HttpServletRequest request, @RequestParam String pkCode) throws Exception {
		RequestHeadValidationUtil.validaFactoryIdAndEmpno(request);
		String factoryId = request.getHeader(SysConst.HTTP_HEADER_X_FACTORY_ID);
		if (StringUtils.isEmpty(factoryId)) {
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.FACTORY_ID_IS_NULL);
		}
		ServiceData<Integer> ret = new ServiceData<>();
		int qty = imesPdaCommonService.scanOldReelIdForReelIdSplit(pkCode, factoryId);
		ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
		ret.setBo(qty);
		return ret;
	}

	@ApiOperation("PDAReelId拆分扫描新数量")
	@PostMapping(value = "/scanNewQtyForReelIdSplit")
	public ServiceData scanNewQtyForReelIdSplit(HttpServletRequest request, @RequestBody PDAReelIdSplitDto dto) {
		RequestHeadValidationUtil.validaFactoryIdAndEmpno(request);
		String factoryId = request.getHeader(SysConst.HTTP_HEADER_X_FACTORY_ID);
		if (StringUtils.isEmpty(factoryId)) {
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.FACTORY_ID_IS_NULL);
		}
		String empNo = request.getHeader(SysConst.HTTP_HEADER_X_EMP_NO);
		if (StringUtils.isEmpty(factoryId)) {
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.EMPNO_IS_NULL);
		}
		dto.setFactoryId(factoryId);
		dto.setLoginUser(empNo);
		ServiceData ret = new ServiceData<>();
		imesPdaCommonService.scanNewQtyForReelIdSplit(dto);
		ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
		ret.setBo(null);
		return ret;
	}


    @ApiOperation("接料扫描-扫描旧料盘 Android PDA")
    @RequestMapping(value = "/rcvScanOldPkCode", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
	public ServiceData<PDARcvOldPkCodeDto> rcvScanOldPkCode(HttpServletRequest request, @RequestParam String oldPkCode, @RequestParam String newPkCode, @RequestParam String productTask) throws Exception {
        RequestHeadValidationUtil.validaFactoryIdAndEmpno(request);
        String factoryId = request.getHeader(SysConst.HTTP_HEADER_X_FACTORY_ID);
		String empNo = request.getHeader(SysConst.HTTP_HEADER_X_EMP_NO);
		return ServiceDataBuilderUtil.success(imesPdaCommonService.rcvScanOldPkCode(oldPkCode, newPkCode, productTask, factoryId, empNo));
    }

    @ApiOperation("QC抽检扫描料盘 Android PDA")
    @PostMapping(value = "/checkPkCodeQCSpotCheck")
    public ServiceData checkPkCodeQCSpotCheck(HttpServletRequest request, @RequestBody PDAQCSpotCheckDTO dto) throws Exception {
        RequestHeadValidationUtil.validaFactoryIdAndEmpno(request);
        return ServiceDataBuilderUtil.success(imesPdaCommonService.checkPkCodeQCSpotCheck(dto));
    }

    @ApiOperation("接料扫描-扫描站位 Android PDA")
    @RequestMapping(value = "/scanLocationSnRcvScan", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ServiceData scanLocationSnRcvScan(HttpServletRequest request, @RequestBody @RequestParam String locationSn, @RequestParam String pkCode) throws Exception {
        RequestHeadValidationUtil.validaFactoryIdAndEmpno(request);
        return ServiceDataBuilderUtil.success(imesPdaCommonService.scanLocationSnRcvScan(locationSn, pkCode));
    }

	@ApiOperation("PDA ReelId数量修改")
	@PostMapping(value = "/updateReelIdQty")
	public ServiceData updateReelIdQty(HttpServletRequest request, @RequestBody PDAReelIdQtyModifyDto dto) throws Exception {
		RequestHeadValidationUtil.validaFactoryIdAndEmpno(request);
		String factoryId = request.getHeader(SysConst.HTTP_HEADER_X_FACTORY_ID);
		if (StringUtils.isEmpty(factoryId)) {
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.FACTORY_ID_IS_NULL);
		}
		String empNo = request.getHeader(SysConst.HTTP_HEADER_X_EMP_NO);
		if (StringUtils.isEmpty(factoryId)) {
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.EMPNO_IS_NULL);
		}
		dto.setFactoryId(new BigDecimal(factoryId));
		dto.setLoginUser(empNo);
		ServiceData ret = new ServiceData<>();
		imesPdaCommonService.updateReelIdQty(dto);
		ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
		ret.setBo(null);
		return ret;
	}

}
