/**
 * 项目名称 : ${project_name}
 * 创建日期 : ${date}
 * 修改历史 :
 * 1. [${date}] 创建文件 by ${user}
 **/
package com.zte.application.impl;


import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.util.DateUtils;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zte.application.*;
import com.zte.application.impl.warehouse.WarehouseSubmitServiceImpl;
import com.zte.application.scan.FlowControlCommonService;
import com.zte.common.CommonUtils;
import com.zte.common.ConstantInterface;
import com.zte.common.DateUtil;
import com.zte.common.SpringUtil;
import com.zte.common.enums.InterfaceEnum;
import com.zte.common.model.MessageId;
import com.zte.common.utils.*;
import com.zte.consts.CommonConst;
import com.zte.domain.model.*;
import com.zte.gei.processor.excel.EasyExcelUtils;
import com.zte.gei.processor.excel.ExcelConfig;
import com.zte.infrastructure.remote.*;
import com.zte.interfaces.assembler.WarehouseEntryInfoAssembler;
import com.zte.interfaces.dto.CFLine;
import com.zte.interfaces.dto.PsWorkOrderDTO;
import com.zte.interfaces.dto.*;
import com.zte.interfaces.dto.export.WarehouseEntryExportDTO;
import com.zte.interfaces.dto.scan.BarcodeBindingDTO;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.exception.BusiException;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.redis.util.RedisCacheUtils;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.itp.msa.util.string.StringHelper;
import com.zte.microservice.name.MicroServiceNameEum;
import com.zte.springbootframe.common.annotation.AsyncExport;
import com.zte.springbootframe.common.annotation.RecordLogAnnotation;
import com.zte.springbootframe.common.annotation.RedisDistributedLockAnnotation;
import com.zte.springbootframe.common.annotation.RedisLockParamAnnotation;
import com.zte.springbootframe.common.consts.SysConst;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.redis.RedisLock;
import com.zte.springbootframe.util.*;
import com.zte.ums.nfv.eco.hsif.msb.common.RouteException;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.util.Pair;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.zte.common.utils.BusinessConstant.*;
import static com.zte.common.utils.Constant.*;
import static com.zte.common.utils.NumConstant.*;


/**
 * // TODO 添加类/接口功能描述
 *
 * <AUTHOR>
 **/
@Service
public class WarehouseEntryInfoServiceImpl implements WarehouseEntryInfoService {
    private static final int PAGE_ROWS = NumConstant.NUM_5000;
    // 开始时间和结束时间最大间隔限制 30天
    private static final long MAX_TIME_RANGE = 2678400000L;

    @Autowired
    private WarehouseEntryInfoRepository warehouseEntryInfoRepository;

    @Autowired
    private WarehouseEntryDetailService warehouseEntryDetailService;

    @Autowired
    private PsWipInfoService psWipInfoService;

    @Autowired
    private DatawbRemoteService datawbRemoteService;

    @Autowired
    private PsScanHistoryRepository psScanHistoryRepository;

    @Autowired
    private PsWipInfoServiceImpl psWipInfoServiceImp;

    @Autowired
    private WarehouseRequirementInfoRepository warehouseRequirementInfoRepository;

    @Autowired
    private WarehouseRequirementDetailRepository warehouseRequirementDetailRepository;

    @Autowired
    private ApsRemoteService apsRemoteService;

    @Autowired
    private ErpRemoteService erpRemoteService;

    @Autowired
    private IMESLogService imesLogService;

    @Autowired
    private WarehouseEntryErpInfoRepository warehouseEntryErpInfoRepository;

    @Autowired
    private WarehouseEntryDetailRepository warehouseEntryDetailRepository;

    @Autowired
    private ScrapBillDetailService scrapBillDetailService;
    @Autowired
    private FlowControlCommonService flowControlCommonService;

    @Autowired
    private CenterfactoryRemoteService centerfactoryRemoteService;
    @Value("${warehouse.info.subCard.enable:true}")
    private Boolean subCardEnable;

    @Value("${warehouse.check.erp.status:true}")
    private boolean checkErpStatus;

    @Value("${imes.check.weight:true}")
    private boolean needCheckWeight;

    @Autowired
    private PsWipInfoRepository wipInfoRepository;
    @Autowired
    private PlanscheduleRemoteService planscheduleRemoteService;

    @Autowired
    DataSourceTransactionManager transactionManager;

    @Autowired
    TransactionDefinition transactionDefinition;

    @Autowired
    WipScanHistoryRepository wipScanHistoryRepository;
    @Autowired
    private ProdBindingSettingRepository prodBindingSettingRepository;
    @Autowired
    private AssemblyRelaScanServiceImpl assemblyRelaScanService;
    @Autowired
    private WipExtendIdentificationRepository wipExtendIdentificationRepository;

    @Autowired
    private AsyncExportFileCommonService asyncExportFileCommonService;
    @Value("${high.process.code:P2065}")
    private String highProcessCode;
    @Value("${home.ledger.flag:Y}")
    private String homeLedgerFlag;
    @Value("${entry.info.export.limit:50000}")
    private int exportLimit;
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    @Autowired
    private WarehouseSubmitServiceImpl submitService;

    @Autowired
    private PmMachineWeightService pmMachineWeightService;

    @Autowired
    private HrmUserInfoService hrmUserInfoService;

    @Autowired
    private TaskReconfigurationRecordService taskReconfigurationRecordService;

    private final Logger logger = LoggerFactory.getLogger(this.getClass());


    public void setWarehouseEntryInfoRepository(WarehouseEntryInfoRepository warehouseEntryInfoRepository) {

        this.warehouseEntryInfoRepository = warehouseEntryInfoRepository;
    }

    /**
     * 增加实体数据
     *
     * @param record
     **/
    @Override
    public int insertWarehouseEntryInfo(WarehouseEntryInfo record) {

        return warehouseEntryInfoRepository.insertWarehouseEntryInfo(record);
    }

    /**
     * 有选择性的增加实体数据
     *
     * @param record
     **/
    @Override
    public void insertWarehouseEntryInfoSelective(WarehouseEntryInfo record) {

        warehouseEntryInfoRepository.insertWarehouseEntryInfoSelective(record);
    }

    /**
     * 根据主键删除实体数据
     *
     * @param record
     **/
    @Override
    public void deleteWarehouseEntryInfoById(WarehouseEntryInfo record) {

        warehouseEntryInfoRepository.deleteWarehouseEntryInfoById(record);
    }

    /**
     * 有选择性的更新实体数据
     *
     * @param record
     **/
    @Override
    public void updateWarehouseEntryInfoByIdSelective(WarehouseEntryInfo record) {

        warehouseEntryInfoRepository.updateWarehouseEntryInfoByIdSelective(record);
    }

    /**
     * 根据主键更新实体数据
     *
     * @param record
     **/
    @Override
    public int updateWarehouseEntryInfoById(WarehouseEntryInfo record) {

        return warehouseEntryInfoRepository.updateWarehouseEntryInfoById(record);
    }


    /**
     * 根据主键查询实体信息
     *
     * @param record
     * @return WarehouseEntryInfo
     **/
    @Override
    public WarehouseEntryInfo selectWarehouseEntryInfoByBillNo(WarehouseEntryInfo record) {

        return warehouseEntryInfoRepository.selectWarehouseEntryInfoByBillNo(record);
    }

    @Override
    public List<WarehouseEntryInfo> getPage(Map<String, Object> record, Long page, Long rows) {

        List<WarehouseEntryInfo> list = null;
        record.put("startRow", (page - 1) * rows + 1);
        record.put("endRow", page * rows);
        list = warehouseEntryInfoRepository.getPage(record);
        dealResult(list);
        this.setMBomProductCode(list);
        return list;
    }

    /**
     * 数据存的时候，stock_name存的就是仓库代码,需要处理正确并补充仓库名称
     * @param list
     */
    @Override
    public void dealResult(List<WarehouseEntryInfo> list) {
        if (null == list || list.isEmpty()) {
            return;
        }
        list.forEach(item -> item.setStockCode(item.getStockName()));
        StringBuilder warehouseCodeBuild = new StringBuilder();
        list.forEach(item -> warehouseCodeBuild.append("'").append(item.getStockCode()).append("'").append(","));
        List<LinesideWarehouseInfo> warehouseNameList = ProductionDeliveryRemoteService.queryWarehouseListByCode(warehouseCodeBuild.substring(0, warehouseCodeBuild.toString().length() - 1));
        Map<String, String> warehouseNameMap = new HashMap<>();
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(warehouseNameList)) {
            warehouseNameList.forEach(info -> warehouseNameMap.put(info.getWarehouseCode(), info.getWarehouseName()));
        }
        list.forEach(item -> item.setStockName(warehouseNameMap.get(item.getStockCode())));
    }

    @Override
    public List<WarehouseEntryInfo> getWarehouseEntryInfoList(Map<String, Object> record) throws Exception {

        List<WarehouseEntryInfo> listWarehouseEntryInfo = null;
        listWarehouseEntryInfo = warehouseEntryInfoRepository.getWarehouseEntryInfoList(record);
        if (CollectionUtils.isEmpty(listWarehouseEntryInfo)) {
            return null;
        }

        String inProdplanId = "";
        for (int i = 0; i < listWarehouseEntryInfo.size(); i++) {

            WarehouseEntryInfo info = listWarehouseEntryInfo.get(i);
            inProdplanId = inProdplanId + "'" + info.getProdplanId() + "',";
        }
        if (inProdplanId.length() > 0) {
            inProdplanId = inProdplanId.substring(0, inProdplanId.length() - 1);

            // Map<String, String> headerParamsMap = new HashMap<String, String>();

            Map<String, Object> map = new HashMap<String, Object>();
            // 设置查询条件
            map.put("inProdplanId", inProdplanId);
            String params = JacksonJsonConverUtil.beanToJson(map);
            Map<String, String> headerParamsMap = MESHttpHelper.getHttpRequestHeader();
            // 点对点调用服务，获取该批次的任务信息
            String serviceName = MicroServiceNameEum.PLANSCHEDULE;
            String version = MicroServiceNameEum.VERSION;
            String sendType = MicroServiceNameEum.SENDTYPEGET;
            String getUrl = "/PS/psTask";

            String getresult = MicroServiceRestUtil.invokeService(serviceName, version, sendType, getUrl, params,
                    headerParamsMap);
            JsonNode json = JacksonJsonConverUtil.getMapperInstance().readTree(getresult);
            String bo = json.get(MpConstant.JSON_BO).toString();
            List<PsTask> list = JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<PsTask>>() {
            });


            for (int i = 0; i < listWarehouseEntryInfo.size(); i++) {
                WarehouseEntryInfo info = listWarehouseEntryInfo.get(i);

                List<PsTask> compareList = list.stream()
                        .filter(pro -> info.getProdplanId().equals(pro.getProdplanId()))
                        .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(compareList)) {
                    info.setTaskQty(compareList.get(NUM_ZERO).getTaskQty());
                }
            }
        }
        this.setMBomProductCode(listWarehouseEntryInfo);

        return listWarehouseEntryInfo;
    }

    private void setMBomProductCode(List<WarehouseEntryInfo> list) {
        if (null == list || list.isEmpty()) {
            return;
        }
        List<String> prodplanIds = list.stream().map(WarehouseEntryInfo::getProdplanId).collect(Collectors.toList());
        List<BProdBomHeaderDTO> mBomList = centerfactoryRemoteService.queryProductCodeByProdPlanIdList(prodplanIds);
        Map<String, String> mBomProductCodeByProdplanIdMap = mBomList.stream().collect(Collectors.toMap(BProdBomHeaderDTO::getProdplanId, BProdBomHeaderDTO::getProductCode, (v1, v2) -> v1));
        for(WarehouseEntryInfo dto : list) {
            String mBomProductCode = mBomProductCodeByProdplanIdMap.get(dto.getProdplanId());
            dto.setMBomProductCode(mBomProductCode == null ? dto.getItemNo() : mBomProductCode);
        }
    }

    @Override
    public Long getCount(Map<String, Object> record) {

        return warehouseEntryInfoRepository.getCount(record);
    }

    @Override
    public int sync(String startTime, String endTime) throws Exception {
        // 校验扫描是否切换imes，未切换直接返回
        if (checkIsChangeImes()) {
            return 0;
        }
        SysLookupTypesDTO sysLookupTypesDTO = BasicsettingRemoteService.getSysLookUpValue(Constant.SYS_LOOK_7000, Constant.SYS_LOOK_7000004);
        if (sysLookupTypesDTO == null) {
            String[] params = {Constant.SYS_LOOK_7000004};
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SYS_LOOK_PARAM_NOT_CONFIG, params);
        }
        boolean updateSysLookup = true;
        Date startDate;
        Date endDate;
        if (inputTimeNotEmpty(startTime, endTime)) {
            endDate = DateUtil.convertStringToDate(endTime, DateUtil.DATE_FORMATE_FULL);
            startDate = DateUtil.convertStringToDate(startTime, DateUtil.DATE_FORMATE_FULL);
            updateSysLookup = false;
        } else if (StringUtils.isNotBlank(sysLookupTypesDTO.getLookupMeaning())) {
            endDate = warehouseEntryInfoRepository.getMaxCreateDate();
            startDate = DateUtil.convertStringToDate(sysLookupTypesDTO.getLookupMeaning(), DateUtil.DATE_FORMATE_FULL);
        } else {
            endDate = warehouseEntryInfoRepository.getMaxCreateDate();
            startDate = warehouseEntryInfoRepository.getMinCreateDate();
        }
        if (dateIsEmpty(startDate, endDate)) {
            return NumConstant.NUM_ZERO;
        }
        if (MAX_TIME_RANGE < endDate.getTime() - startDate.getTime()) {
            endDate = new Date(MAX_TIME_RANGE + startDate.getTime());
        }
        if (NumConstant.NUM_ZERO >= endDate.getTime() - startDate.getTime()) {
            return NumConstant.NUM_ZERO;
        }
        return getCount(sysLookupTypesDTO, updateSysLookup, startDate, endDate);
    }

    /**
     * @return
     * <AUTHOR>
     * 校验扫描是否已切换imes
     * @Date 2023/1/11 10:49
     * @Param []
     **/
    private boolean checkIsChangeImes() throws Exception {
        SysLookupValues values = CenterfactoryRemoteService.getSysLookupValuesCenter(SYS_LOOK_7000010);
        if(StringUtils.isEmpty(values.getLookupMeaning())){
            return true;
        }
        Date configDate=DateUtil.convertStringToDate(values.getLookupMeaning(), DateUtil.DATE_FORMATE_FULL);
        return  Objects.isNull(configDate)
                || configDate.getTime() - System.currentTimeMillis() > 0;
    }

    private int getCount(SysLookupTypesDTO sysLookupTypesDTO, boolean updateSysLookup, Date startDate, Date endDate) throws Exception {
        List<WarehouseEntryStatics> statDataList = new ArrayList<WarehouseEntryStatics>();
        int startRow = NumConstant.NUM_ZERO;
        int endRow = NumConstant.NUM_5000;
        int count = NumConstant.NUM_ZERO;
        String startDateStr = DateUtil.convertDateToString(startDate, DateUtil.DATE_FORMATE_FULL);
        String endDateStr = DateUtil.convertDateToString(endDate, DateUtil.DATE_FORMATE_FULL);

        // 是否首次查询
        boolean firstQuery = true;
        // 返回的数据量 >= 分页数量继续查询后一页处理
        while (firstQuery || statDataList.size() >= PAGE_ROWS) {
            firstQuery = false;
            // 获取物料代码、单据数量、创建时间关联关系数据(分页查询)
            statDataList = warehouseEntryInfoRepository.warehouseEntryStatics(startDateStr, endDateStr, startRow, endRow);
            startRow += PAGE_ROWS;
            endRow += PAGE_ROWS;
            count += this.dealStatDate(statDataList);
        }
        // 更新数据字典值
        if (updateSysLookup) {
            BasicsettingRemoteService.updateSysLookupValuesMeaning(sysLookupTypesDTO.getLookupCode(), endDateStr);
        }
        return count;
    }

    /**
     * 处理统计得到的料单代码、单据数量、单据日期、
     * 已存在跳过，不存在插入
     *
     * @param statDataList
     */
    private int dealStatDate(List<WarehouseEntryStatics> statDataList) throws Exception {
        if (CollectionUtils.isEmpty(statDataList)) {
            return NumConstant.NUM_ZERO;
        }
        List<WarehouseEntryStatics> needInsertList = new ArrayList<>();
        // 分批处理
        List<List<WarehouseEntryStatics>> listOfList = CommonUtils.splitList(statDataList, NumConstant.NUM_500);
        for (List<WarehouseEntryStatics> list : listOfList) {
            // 判断是否已存在
            List<String> warehouseEntryIdList = list.stream().map(WarehouseEntryStatics::getWarehouseEntryId).collect(Collectors.toList());
            // 获取数据库已存在的数据
            List<String> existWarehouseEntryIdList = CenterfactoryRemoteService.queryWarehouseEntryIdIsExist(warehouseEntryIdList);
            if (CollectionUtils.isEmpty(existWarehouseEntryIdList)) {
                needInsertList.addAll(list);
                continue;
            }
            // 剔除已存在的数据
            List<WarehouseEntryStatics> notExistData = list.stream().filter(curr -> !existWarehouseEntryIdList.contains(curr.getWarehouseEntryId())).collect(Collectors.toList());
            needInsertList.addAll(notExistData);
        }
        CenterfactoryRemoteService.batchInsertIntoWarehouseEntryIdIsExist(needInsertList);
        return needInsertList.size();
    }

    private boolean dateIsEmpty(Date startDate, Date endDate) {
        return startDate == null || endDate == null;
    }

    private boolean inputTimeNotEmpty(String startTime, String endTime) {
        return StringUtils.isNotEmpty(startTime) && StringUtils.isNotEmpty(endTime);
    }

    /**
     * @param entity
     * @return
     * @throws RouteException
     * @throws JsonProcessingException
     * @throws IOException
     */
    @Override
    public WarehouseEntryInfoDTO getPrintInfo(WarehouseEntryInfo entity)
            throws RouteException, JsonProcessingException, IOException {

        WarehouseEntryInfoDTO warehouseEntryInfoDTO = new WarehouseEntryInfoDTO();
        WarehouseEntryInfo warehouseEntryInfo =
                warehouseEntryInfoRepository.selectWarehouseEntryInfoByBillNo(entity);
        if (warehouseEntryInfo == null) {
            return warehouseEntryInfoDTO;
        }

        // Map<String, String> headerParamsMap = new HashMap<String, String>();
        Map<String, Object> map = new HashMap<String, Object>();
        // 设置查询条件
        map.put("prodplanId", warehouseEntryInfo.getProdplanId());
        String params = JacksonJsonConverUtil.beanToJson(map);
        Map<String, String> headerParamsMap = MESHttpHelper.getHttpRequestHeader();
        // 点对点调用服务，获取该批次的任务信息
        String serviceName = MicroServiceNameEum.PLANSCHEDULE;
        String version = MicroServiceNameEum.VERSION;
        String sendType = MicroServiceNameEum.SENDTYPEGET;
        String getUrl = "/PS/psTask";

        String getresult = MicroServiceRestUtil.invokeService(serviceName, version, sendType, getUrl, params,
                headerParamsMap);
        JsonNode json = JacksonJsonConverUtil.getMapperInstance().readTree(getresult);
        String bo = json.get(MpConstant.JSON_BO).toString();
        List<PsTask> list = JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<PsTask>>() {
        });
        if (CollectionUtils.isEmpty(list)) {
            return warehouseEntryInfoDTO;
        }
        BeanUtils.copyProperties(list.get(NUM_ZERO), warehouseEntryInfoDTO);

        warehouseEntryInfoDTO.setCommitedQty(entity.getCommitedQty());
        warehouseEntryInfoDTO.setBillNo(entity.getBillNo());
        warehouseEntryInfoDTO.setRemark(entity.getRemark());
        warehouseEntryInfoDTO.setApplyNo(entity.getApplyNo());
        SimpleDateFormat sf = new SimpleDateFormat(MpConstant.DATE_FORMAT_YYYY_YEAR_MM_MOUTH_DD_DAY_HHMMSS);
        Date date = new Date();
        warehouseEntryInfoDTO.setCurrDate(sf.format(date));
        return warehouseEntryInfoDTO;

    }

    /**
     * 根据批次查询入库单相关信息
     *
     * @see com.zte.application.WarehouseEntryInfoService#getWarehouseEntryRelatedInfoByProdplanId(java.lang.String)
     */
    @Override
    public WarehouseEntryInfoDTO getWarehouseEntryRelatedInfoByProdplanId(String prodplanId)
            throws RouteException, JsonProcessingException, IOException {

        // Map<String, String> headerParamsMap = new HashMap<String, String>();
        Map<String, Object> map = new HashMap<String, Object>();
        // 设置查询条件
        map.put("prodplanId", prodplanId);
        String params = JacksonJsonConverUtil.beanToJson(map);
        Map<String, String> headerParamsMap = MESHttpHelper.getHttpRequestHeader();
        // 点对点调用服务，获取该批次的任务信息
        String serviceName = MicroServiceNameEum.PLANSCHEDULE;
        String version = MicroServiceNameEum.VERSION;
        String sendType = MicroServiceNameEum.SENDTYPEGET;
        String getUrl = "/PS/psTask";

        String getresult = MicroServiceRestUtil.invokeService(serviceName, version, sendType, getUrl, params,
                headerParamsMap);
        JsonNode json = JacksonJsonConverUtil.getMapperInstance().readTree(getresult);
        String bo = json.get(MpConstant.JSON_BO).toString();
        List<PsTask> list = JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<PsTask>>() {
        });
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        WarehouseEntryInfoDTO warehouseEntryInfoDTO = new WarehouseEntryInfoDTO();
        BeanUtils.copyProperties(list.get(NUM_ZERO), warehouseEntryInfoDTO);
        Long commitedQty = getSumCommitedQty(map);
        if (commitedQty != null) {
            warehouseEntryInfoDTO.setCommitedQty(new BigDecimal(commitedQty));
        } else {
            warehouseEntryInfoDTO.setCommitedQty(new BigDecimal(NUM_ZERO));
        }

        return warehouseEntryInfoDTO;

    }

    private Boolean hasPositiveInt(Long num) {
        return num != null && num > 0;
    }

    /**
     * 查询入库单相关信息
     *
     * @param map
     * @return
     * @throws RouteException
     * @throws JsonProcessingException
     * @throws IOException
     * @autchor 6407001148
     * @date 2018-09-29
     */
    public WarehouseEntryInfoQueryDTO getWmesWarehouseEntryInfo(Map<String, Object> map, List<PsTask> psTaskList) throws Exception {
        long page = NUM_ONE;
        long rows = NUM_200;
        //获取是否做DIP校验；  1071004 数据字典，目录只为Y ,则返回Y, 否则返回N
        String dipFinish = checkDipFinish();
        WarehouseEntryInfoQueryDTO entryInfoQueryDTO = new WarehouseEntryInfoQueryDTO();
        entryInfoQueryDTO.setQueryStatus(Constant.FLAG_Y);
        // 2020/4/1    warehouseEntryInfoQueryDTO 取值取第一条    (psTaskList.get(NumConstant.NUM_ZERO), warehouseEntryInfoQueryDTO);  ？？？
        BeanUtils.copyProperties(psTaskList.get(NUM_ZERO), entryInfoQueryDTO);
        String flag = psTaskList.get(NUM_ZERO).getLeadFlag();
        if (Constant.SYS_TYPE_WMES.equals(psTaskList.get(NUM_ZERO).getSourceSys())) {
            dipFinish = Constant.FLAG_N;
        }
        if (!StringUtils.isEmpty(flag)) {
            entryInfoQueryDTO.setIsLead(flag);
        }
        //来源系统 2020/4/1  来源系统取值   取第一条
        String sourceSys = entryInfoQueryDTO.getSourceSys();
        // 设置任务相关属性，返回该任务是否锁定条码
        String lockByTask = this.setTaskProperty(map, entryInfoQueryDTO, psTaskList);

        //查询指令信息
        Map<String, String> hashMap = new HashMap<>(4);
        // 2020/4/1  任务号取值   取第一条
        hashMap.put("taskNo", entryInfoQueryDTO.getTaskNo());
        List<PsWorkOrderBasic> psWorkOrderBasics = getWorkorderBasicInfo(hashMap);
        if (CollectionUtils.isEmpty(psWorkOrderBasics)) {
            entryInfoQueryDTO.setQueryStatus(Constant.FLAG_N);
            entryInfoQueryDTO.setErrorMessage(CommonUtils.getLmbMessage(MessageId.WORDER_ORDER_NOT_FOUND));
            return entryInfoQueryDTO;
        }
        //  2020/4/1  任务号取值   取第一条  取第一个指令的 工艺路径，  此处有问题， key: workOrderNo, value: currProcessCode
        // 需注意单个批次工艺路径升级的情况，  应该取最新的一个批次 ,
        Map<String, String> lastRouteDetail = Maps.newHashMap();
        Map<String, String> errorWorkOrder = Maps.newHashMap();
        lastRouteDetail = getLastRouteDetails(psWorkOrderBasics, errorWorkOrder);
        //该批次的Route没有入库工序，不需要入库
        if (MapUtils.isEmpty(lastRouteDetail)) {
            entryInfoQueryDTO.setQueryStatus(Constant.FLAG_N);
            entryInfoQueryDTO.setErrorMessage(getErrorMsg(errorWorkOrder));
            return entryInfoQueryDTO;
        }
        PsWipInfoQueryDTO psWipInfoQueryDTO = this.getWipInfoQueryDTO(page, rows, dipFinish, lastRouteDetail);
        if (Constant.SYS_TYPE_WMES.equals(sourceSys)) {
            // 标模条码查询：根据任务号或者物料代码查询   判断是否根据任务锁定
            setTaskNoOrItemNo(entryInfoQueryDTO, lockByTask, psWipInfoQueryDTO);
            // 获取改配任务已完成装配、拆解物料清单
            this.getReconfigurationList(psWipInfoQueryDTO, entryInfoQueryDTO);
        } else {
            // 单板条码查询：根据指令查询
            setWorkOrderNo(entryInfoQueryDTO, psWorkOrderBasics, psWipInfoQueryDTO);
        }
        psWipInfoQueryDTO.setOriginalTask(psTaskList.get(NUM_ZERO).getOriginalTask());
        Map<String, Object> wipInfoResult = psWipInfoService.getPsWipInfoList(psWipInfoQueryDTO);
        Long total = (long) wipInfoResult.get("total");
        if (total != null && total > 0) {
            List<PsWipInfo> psWipInfoList = (List<PsWipInfo>) wipInfoResult.get(MpConstant.JSON_ROWS);
            setLpnByContent(psWipInfoList);
            entryInfoQueryDTO.setPsWipInfoList(psWipInfoList);
        } else {
            entryInfoQueryDTO.setQueryStatus(Constant.FLAG_N);
            entryInfoQueryDTO.setErrorMessage(CommonUtils.getLmbMessage(MessageId.WAIT_IN_WAREHOUSE_NOT_EXIST));
            return entryInfoQueryDTO;
        }
        entryInfoQueryDTO.setLastWorkOrderProcess(psWipInfoQueryDTO.getLastWorkOrderProcess());
        entryInfoQueryDTO.setTotal(total);
        entryInfoQueryDTO.setPage(page);
        entryInfoQueryDTO.setRows(rows);
        return entryInfoQueryDTO;
    }

    private void getReconfigurationList (PsWipInfoQueryDTO psWipInfoQueryDTO, WarehouseEntryInfoQueryDTO entryInfoQueryDTO) throws Exception{
        String taskNo = entryInfoQueryDTO.getTaskNo();
        // 校验当前任务是否改配任务
        boolean reconfigurationFlag = centerfactoryRemoteService.getReconfigurationFlag(taskNo);
        psWipInfoQueryDTO.setChangeConfigurationFlag(reconfigurationFlag);
        entryInfoQueryDTO.setChangeConfigurationFlag(reconfigurationFlag);
        // 非改配任务返回
        if (!reconfigurationFlag) {
            return;
        }
        // 从erp获取任务清单
        List<ItemListEntityDTO> itemListEntityDTOList = datawbRemoteService.getErpItemListByTaskNo(taskNo);
        if (CollectionUtils.isEmpty(itemListEntityDTOList)) {
            return;
        }
        Map<String, ItemListEntityDTO> itemMap = itemListEntityDTOList.stream().filter(e -> StringUtils.isNotEmpty(e.getItemNo()))
                .collect(Collectors.toMap(ItemListEntityDTO::getItemNo, e -> e, (k, v) -> k));
        if (CollectionUtils.isEmpty(itemMap)) {
            return;
        }
        int page = INT_0;
        int size;
        List<String> existList = new ArrayList<>();
        do {
            page++;
            TaskReconfigurationRecordPageQueryDTO queryDTO = new TaskReconfigurationRecordPageQueryDTO();
            queryDTO.setPage(page);
            queryDTO.setRows(INT_500);
            queryDTO.setTaskNo(taskNo);
            // 获取装配、拆解清单
            Page<TaskReconfigurationRecord> recordPage = taskReconfigurationRecordService.selectSumQuantityPage(queryDTO);
            List<TaskReconfigurationRecord> taskReconfigurationRecordList = recordPage.getRows();
            if (CollectionUtils.isEmpty(taskReconfigurationRecordList)) {
                break;
            }
            size = recordPage.getRows().size();
            // 过滤计算物料
            existList.addAll(filterExistItemList(itemMap, taskReconfigurationRecordList));
        } while (size >= INT_500);
        psWipInfoQueryDTO.setChangeConfigurationList(existList);
        entryInfoQueryDTO.setChangeConfigurationList(existList);
    }

    /**
     *<AUTHOR>
     * 过滤获取已完成改配的物料代码
     *@Date 2025/5/6 14:18
     *@Param [java.util.Map<java.lang.String,com.zte.interfaces.dto.ItemListEntityDTO>, java.util.List<com.zte.domain.model.TaskReconfigurationRecord>]
     *@return
     **/
    private List<String> filterExistItemList (Map<String, ItemListEntityDTO> itemMap, List<TaskReconfigurationRecord> taskReconfigurationRecordList) {
        List<String> existList = new ArrayList<>();
        // 根据物料代码分组求和
        for (TaskReconfigurationRecord reconfigurationRecord : taskReconfigurationRecordList) {
            ItemListEntityDTO entityDTO = itemMap.get(reconfigurationRecord.getItemCode());
            // 不在erp物料清单中跳过
            if (entityDTO == null) {
                continue;
            }
            // 数量为空校验
            if (StringUtils.isEmpty(entityDTO.getRequiredQuantity())) {
                continue;
            }
            int requiredQuantity = Integer.parseInt(entityDTO.getRequiredQuantity());
            // 已完成装配或拆解添加物料代码
            if (INT_0 == reconfigurationRecord.getQuantity() + requiredQuantity || reconfigurationRecord.getQuantity() == requiredQuantity) {
                existList.add(reconfigurationRecord.getSn());
            }
        }
        return existList;
    }

    private PsWipInfoQueryDTO getWipInfoQueryDTO(long page, long rows, String checkDipFinish, Map<String, String> lastRouteDetail) {
        PsWipInfoQueryDTO psWipInfoQueryDTO = new PsWipInfoQueryDTO();
        //批次 与 工序一起才能确认  ， 因此不同的批次 可能工艺路径不一致，  入库前的 子工序 也可能不一致，  将导致数据混乱
        psWipInfoQueryDTO.setLastWorkOrderProcess(lastRouteDetail);
        psWipInfoQueryDTO.setDipFinishFlag(checkDipFinish);
        psWipInfoQueryDTO.setPage(page);
        psWipInfoQueryDTO.setRows(rows);
        return psWipInfoQueryDTO;
    }

    /**
     * 拼接错误信息
     *
     * @param errorWorkOrder
     * @return
     */
    private String getErrorMsg(Map<String, String> errorWorkOrder) {
        String errorMsg = "";
        for (Map.Entry<String, String> error : errorWorkOrder.entrySet()) {
            errorMsg += error.getKey() + SYMBOL_COLON + error.getValue() + Constant.SYMBOL_COMMA;
        }
        return errorMsg;
    }

    //获取入库的最后一道工序及其指令
    private Map<String, String> getLastRouteDetails(List<PsWorkOrderBasic> psWorkOrders, Map<String, String> errorWorkOrder) throws Exception {
        Map<String, String> lastRouteDetail = Maps.newHashMap();
        for (PsWorkOrderBasic psWorkOrder : psWorkOrders) {
            if (lastRouteDetail.containsKey(psWorkOrder.getSourceTask())) {
                continue;
            }
            // 查询子工序
            String routeId = psWorkOrder.getRouteId();
            if (StringUtils.isBlank(routeId)) {
                errorWorkOrder.put(psWorkOrder.getWorkOrderNo(), CommonUtils.getLmbMessage(MessageId.NOT_FOUND_WORKORDER_ROUTE));
                continue;
            }
            List<CtRouteDetail> ctRouteDetails = getCtRouteDetailInfo(psWorkOrder.getRouteId(), "", "", "");
            if (CollectionUtils.isEmpty(ctRouteDetails)) {
                errorWorkOrder.put(psWorkOrder.getWorkOrderNo(), CommonUtils.getLmbMessage(MessageId.NOT_FOUND_WORKORDER_ROUTE));
                continue;
            }
            //判断是否最后一个工艺路径   currProcess：入库前的最后一个子工序
            for (CtRouteDetail routeDetail : ctRouteDetails) {
                if (Constant.FLAG_Y.equals(routeDetail.getLastProcess()) && Constant.WAREHOUSE_ENTRY.equals(routeDetail.getCraftSection())) {
                    lastRouteDetail.put(psWorkOrder.getSourceTask(), routeDetail.getCurrProcess());
                    break;
                }
            }
        }
        return lastRouteDetail;
    }

    @Override
    public WarehouseEntryInfoQueryDTO getWarehouseEntryInfo(Map<String, Object> map) throws Exception {
        WarehouseEntryInfoQueryDTO warehouseEntryInfoQueryDTO = new WarehouseEntryInfoQueryDTO();
        warehouseEntryInfoQueryDTO.setQueryStatus(Constant.FLAG_Y);
        //获取任务列表
        List<PsTask> psTaskList = getPsTasks(map);
        if (CollectionUtils.isEmpty(psTaskList)) {
            //不返回null，返回空对象
            warehouseEntryInfoQueryDTO.setQueryStatus(Constant.FLAG_N);
            warehouseEntryInfoQueryDTO.setErrorMessage(CommonUtils.getLmbMessage(MessageId.TRACKING_OR_TASK_NO_NOT_EXIST));
            return warehouseEntryInfoQueryDTO;
        }
        //单板
        if (Constant.SYS_TYPE_STEP.equals(psTaskList.get(Constant.INT_0).getSourceSys())) {
            warehouseEntryInfoQueryDTO = getStepWarehouseEntryInfo(map, psTaskList);
        } else if (Constant.SYS_TYPE_WMES.equals(psTaskList.get(Constant.INT_0).getSourceSys())) {//整机
            warehouseEntryInfoQueryDTO = getWmesWarehouseEntryInfo(map, psTaskList);
        }
        warehouseEntryInfoQueryDTO.setProductType(psTaskList.get(Constant.INT_0).getProductType());
        warehouseEntryInfoQueryDTO.setOrgId(psTaskList.get(Constant.INT_0).getOrgId());
        // 工序名称
        setProcessName(warehouseEntryInfoQueryDTO);
        return warehouseEntryInfoQueryDTO;
    }

    public void setProcessName(WarehouseEntryInfoQueryDTO dto) {
        List<PsWipInfo> wipInfos = dto.getPsWipInfoList();
        if (CollectionUtils.isEmpty(wipInfos)) {
            return;
        }
        // 最多200条码
        List<BSProcess> processList = CrafttechRemoteService.getBsProcessList(new BSProcessDTO(){{
            setInProcessCode(wipInfos.stream().map(PsWipInfo::getCurrProcessCode).distinct()
                    .collect(Collectors.joining(Constant.SEPARATED_COMMA_COMMA, Constant.SINGLE_QUOTE, Constant.SINGLE_QUOTE)));
        }});
        if (CollectionUtils.isEmpty(processList)) {
            return;
        }
        Map<String, String> processMap = processList.stream()
                .filter(process -> StringUtils.isNotBlank(process.getProcessName()))
                .collect(Collectors.toMap(BSProcess::getProcessCode, BSProcess::getProcessName, (a, b) -> a));
        wipInfos.forEach(wip -> wip.setCurrProcessName(processMap.get(wip.getCurrProcessCode())));
    }

    /**
     * 查询入库单相关信息
     *
     * @param map
     * @return
     * @throws RouteException
     * @throws JsonProcessingException
     * @throws IOException
     * @autchor 6407001148
     * @date 2018-09-29
     */
    private WarehouseEntryInfoQueryDTO getStepWarehouseEntryInfo(Map<String, Object> map, List<PsTask> psTaskList) throws Exception {
        long page = NUM_ONE;
        long rows = NUM_200;
        String lastProcess = "";
        String processCodes = "";
        String checkDipFinish = checkDipFinish();

        WarehouseEntryInfoQueryDTO warehouseEntryInfoQueryDTO = new WarehouseEntryInfoQueryDTO();
        warehouseEntryInfoQueryDTO.setQueryStatus(Constant.FLAG_Y);
        BeanUtils.copyProperties(psTaskList.get(NUM_ZERO), warehouseEntryInfoQueryDTO);
        String leadFlag = psTaskList.get(NUM_ZERO).getLeadFlag();
        if (Constant.SYS_TYPE_WMES.equals(psTaskList.get(NUM_ZERO).getSourceSys())) {
            checkDipFinish = Constant.FLAG_N;
        }
        if (!StringUtils.isEmpty(leadFlag)) {
            warehouseEntryInfoQueryDTO.setIsLead(leadFlag);
        }

        // 来源系统
        String sourceSys = warehouseEntryInfoQueryDTO.getSourceSys();
        // 设置任务相关属性，返回该任务是否锁定条码
        String lockByTask = setTaskProperty(map, warehouseEntryInfoQueryDTO, psTaskList);
        //查询指令信息
        Map<String, String> mapOrder = new HashMap<>(4);
        mapOrder.put("taskNo", warehouseEntryInfoQueryDTO.getTaskNo());
        List<PsWorkOrderBasic> psWorkOrderBasicList = getWorkorderBasicInfo(mapOrder);
        if (CollectionUtils.isEmpty(psWorkOrderBasicList)) {
            warehouseEntryInfoQueryDTO.setQueryStatus(Constant.FLAG_N);
            warehouseEntryInfoQueryDTO.setErrorMessage(CommonUtils.getLmbMessage(MessageId.WORDER_ORDER_NOT_FOUND));
            return warehouseEntryInfoQueryDTO;
        }

        PsWorkOrderBasic psWorkOrder = psWorkOrderBasicList.get(NUM_ZERO);
        // 查询子工序
        String routeId = psWorkOrder.getRouteId();
        if (StringUtils.isBlank(routeId)) {
            warehouseEntryInfoQueryDTO.setQueryStatus(Constant.FLAG_N);
            warehouseEntryInfoQueryDTO.setErrorMessage(CommonUtils.getLmbMessage(MessageId.NOT_FOUND_WORKORDER_ROUTE));
            return warehouseEntryInfoQueryDTO;
        }
        List<CtRouteDetail> ctRouteDetails = getCtRouteDetailInfo(psWorkOrder.getRouteId(), "", "", "");
        if (CollectionUtils.isEmpty(ctRouteDetails)) {
            warehouseEntryInfoQueryDTO.setQueryStatus(Constant.FLAG_N);
            warehouseEntryInfoQueryDTO.setErrorMessage(CommonUtils.getLmbMessage(MessageId.NOT_FOUND_WORKORDER_ROUTE));
            return warehouseEntryInfoQueryDTO;
        }
        // 高温工序前的子工序
        String beforeHighProcessCode = "";
        // 工艺路径包含的子工序列表
        List<String> processCodeList = ctRouteDetails.stream().sorted(Comparator.comparing(CtRouteDetail::getProcessSeq))
                .map(CtRouteDetail::getNextProcess).collect(Collectors.toList());
        // 判断入库前的工序是否为高温
        beforeHighProcessCode = this.getBeforeHighProcessCode(ctRouteDetails, beforeHighProcessCode, processCodeList);
        for (CtRouteDetail routeDetail : ctRouteDetails) {
            if (isLastProcessAndEntry(routeDetail)) {
                // 入库前的子工序
                processCodes = routeDetail.getCurrProcess();
                lastProcess = Constant.FLAG_Y;
                break;
            }
        }
        // 拼接高温前的子工序
        if (StringUtils.isNotBlank(beforeHighProcessCode)) {
            processCodes += Constant.COMMA + beforeHighProcessCode;
        }
        if (!Constant.FLAG_Y.equals(lastProcess)) {
            warehouseEntryInfoQueryDTO.setQueryStatus(Constant.FLAG_N);
            warehouseEntryInfoQueryDTO.setErrorMessage(CommonUtils.getLmbMessage(MessageId.ROUTES_IN_BATCH_NOT_STORED_IN_WAREHOUSE));
            return warehouseEntryInfoQueryDTO;
        }
        PsWipInfoQueryDTO psWipInfoQueryDTO = this.getWipInfoQueryDTO(page, rows, processCodes, checkDipFinish, sourceSys);
        if (this.getWipInfoInfo(psTaskList,psWipInfoQueryDTO, warehouseEntryInfoQueryDTO, lockByTask, psWorkOrderBasicList)) {
            return warehouseEntryInfoQueryDTO;
        }
        //仓库
        setStock(warehouseEntryInfoQueryDTO);
        return warehouseEntryInfoQueryDTO;
    }

    private String getBeforeHighProcessCode(List<CtRouteDetail> ctRouteDetails, String beforeHighProcessCode, List<String> processCodeList) {
        // 判断入库前的工序是否为高温
        if (processCodeList.contains(highProcessCode) && StringUtils.equals(highProcessCode, processCodeList.get(processCodeList.size() - NUM_TWO))) {
            for (CtRouteDetail detail : ctRouteDetails) {
                // 若高温是有条件的可做不可做工序
                if (StringUtils.equals(highProcessCode,detail.getNextProcess()) && StringUtils.equals(FLAG_Y,detail.getDoOrNotFlag())
                        && StringUtils.equals(Constant.HIGH_TEMP_RULE,detail.getSkipRule())) {
                    // 取高温前一个子工序
                    beforeHighProcessCode = processCodeList.get(processCodeList.size() - NUM_THREE);
                }
            }
        }
        return beforeHighProcessCode;
    }

    private PsWipInfoQueryDTO getWipInfoQueryDTO(long page, long rows, String processCodes, String checkDipFinish, String sourceSys) {
        PsWipInfoQueryDTO psWipInfoQueryDTO = new PsWipInfoQueryDTO();
        psWipInfoQueryDTO.setCurrProcessCode(processCodes);
        psWipInfoQueryDTO.setDipFinishFlag(checkDipFinish);
        psWipInfoQueryDTO.setPage(page);
        psWipInfoQueryDTO.setRows(rows);
        psWipInfoQueryDTO.setSourceSys(sourceSys);
        return psWipInfoQueryDTO;
    }

    private void setStock(WarehouseEntryInfoQueryDTO warehouseEntryInfoQueryDTO) throws Exception {
        //仓库
        if (!StringUtils.isEmpty(warehouseEntryInfoQueryDTO.getProdplanNo())) {
            warehouseEntryInfoQueryDTO.setStock(datawbRemoteService.getApsOpProdplanStock(warehouseEntryInfoQueryDTO.getProdplanNo()));
        }
    }

    public boolean getWipInfoInfo(List<PsTask> psTaskList, PsWipInfoQueryDTO psWipInfoQueryDTO, WarehouseEntryInfoQueryDTO warehouseEntryInfoQueryDTO,
                                   String lockByTask, List<PsWorkOrderBasic> psWorkOrderBasicList) throws Exception {
        if (Constant.SYS_TYPE_WMES.equals(psWipInfoQueryDTO.getSourceSys())) {
            // 标模条码查询：根据任务号或者物料代码查询
            setTaskNoOrItemNo(warehouseEntryInfoQueryDTO, lockByTask, psWipInfoQueryDTO);
        } else {
            // 单板条码查询：根据指令查询
            setWorkOrderNo(warehouseEntryInfoQueryDTO, psWorkOrderBasicList, psWipInfoQueryDTO);
        }
        psWipInfoQueryDTO.setOriginalTask(psTaskList.get(NUM_ZERO).getOriginalTask());
        Map<String, Object> wipInfoResult = psWipInfoService.getPsWipInfoList(psWipInfoQueryDTO);
        Long total = (long) wipInfoResult.get("total");
        if (!hasPositiveInt(total)) {
            warehouseEntryInfoQueryDTO.setCurrProcess(psWipInfoQueryDTO.getCurrProcessCode());
            if (!StringUtils.isEmpty(warehouseEntryInfoQueryDTO.getProdplanNo())) {
                warehouseEntryInfoQueryDTO.setStock(datawbRemoteService.getApsOpProdplanStock(warehouseEntryInfoQueryDTO.getProdplanNo()));
            }
            warehouseEntryInfoQueryDTO.setQueryStatus(Constant.FLAG_N);
            warehouseEntryInfoQueryDTO.setErrorMessage(CommonUtils.getLmbMessage(MessageId.WAIT_IN_WAREHOUSE_NOT_EXIST));
            return true;
        }

        List<PsWipInfo> psWipInfoList = (List<PsWipInfo>) wipInfoResult.get(MpConstant.JSON_ROWS);
        setLpnByContent(psWipInfoList);
        // 校验入库时距离前工序是否超出滞留时长
        List<String> retentionSnList = psWipInfoService.filterRetentionList(psWipInfoList);
        if (!CollectionUtils.isEmpty(retentionSnList)) {
            psWipInfoList.removeIf(item -> retentionSnList.contains(item.getSn()));
        }
        if (CollectionUtils.isEmpty(psWipInfoList)) {
            warehouseEntryInfoQueryDTO.setQueryStatus(Constant.FLAG_N);
            warehouseEntryInfoQueryDTO.setErrorMessage(CommonUtils.getLmbMessage(MessageId.WAIT_IN_WAREHOUSE_NOT_EXIST));
            return true;
        }
        warehouseEntryInfoQueryDTO.setPsWipInfoList(psWipInfoList);
        warehouseEntryInfoQueryDTO.setCurrProcess(psWipInfoQueryDTO.getCurrProcessCode());
        warehouseEntryInfoQueryDTO.setTotal(total);
        warehouseEntryInfoQueryDTO.setPage(psWipInfoQueryDTO.getPage());
        warehouseEntryInfoQueryDTO.setRows(psWipInfoQueryDTO.getRows());
        return false;
    }

    public void setLpnByContent(List<PsWipInfo> psWipInfoList) throws Exception {
        if (!CollectionUtils.isEmpty(psWipInfoList)) {
            StringBuilder snBuild = new StringBuilder();
            for (PsWipInfo info : psWipInfoList) {
                snBuild.append("'").append(info.getSn()).append("'").append(",");
            }
            //获取箱内容
            Map paraMap = new HashMap();
            paraMap.put("inEntityIdentification", snBuild.toString().substring(0, snBuild.toString().length() - 1));
            Map<String, String> snMap = new HashMap<>();
            List<ContainerContentInfoDTO> contentInfoDTOList = new ArrayList<>();
            contentInfoDTOList = ProductionDeliveryRemoteService.getContentInfoList(paraMap);
            setLpn(psWipInfoList, snMap, contentInfoDTOList);
        }
    }

    private void setLpn(List<PsWipInfo> psWipInfoList, Map<String, String> snMap, List<ContainerContentInfoDTO> contentInfoDTOList) {
        if (!CollectionUtils.isEmpty(contentInfoDTOList)) {
            for (ContainerContentInfoDTO info : contentInfoDTOList) {
                snMap.put(info.getEntityIdentification(), info.getLpn());
            }
        }
        for (PsWipInfo info : psWipInfoList) {
            if (StringHelper.isNotEmpty(info.getSn())) {
                if (snMap.containsKey(info.getSn())) {
                    info.setLpn(snMap.get(info.getSn()));
                }
            }
        }
    }

    /**
     * 判断是否是最后工序且是入库
     *
     * @param routeDetail
     * @return
     */
    private boolean isLastProcessAndEntry(CtRouteDetail routeDetail) {
        return Constant.FLAG_Y.equals(routeDetail.getLastProcess()) && (Constant.WAREHOUSE_ENTRY.equals(routeDetail.getCraftSection()) || Constant.STRING_CASTLE.equals(routeDetail.getCraftSection()));
    }

    private String checkDipFinish() throws Exception {
        // 查询数据字典 DIP上料完成校验标识 1071
        String checkDipFinish = Constant.FLAG_N;
        List<SysLookupTypesDTO> lookupValueList = BasicsettingRemoteService.getSysLookUpValue(MpConstant.LOOKUP_TYPE_POLAR_SWITCH);
        for (SysLookupTypesDTO checkDto : lookupValueList) {
            if (MpConstant.LOOKUP_VALUE_DIP_FINISH.equals(checkDto.getLookupCode().toString())
                    && Constant.FLAG_Y.equals(checkDto.getLookupMeaning())) {
                checkDipFinish = Constant.FLAG_Y;
                break;
            }
        }
        return checkDipFinish;
    }

    private String setTaskProperty(Map<String, Object> map, WarehouseEntryInfoQueryDTO warehouseEntryInfoQueryDTO, List<PsTask> psTaskList) {
        // 任务数量
        BigDecimal taskQty = BigDecimal.ZERO;
        StringBuilder taskIdList = new StringBuilder();
        for (PsTask psTask : psTaskList) {
            taskQty = taskQty.add(psTask.getTaskQty());
            taskIdList.append(psTask.getTaskId()).append(Constant.COMMA);
        }
        if (taskIdList.length() > 0) {
            taskIdList.deleteCharAt(taskIdList.length() - 1);
        }
        warehouseEntryInfoQueryDTO.setTaskIdList(taskIdList.toString());
        warehouseEntryInfoQueryDTO.setTaskQty(taskQty);
        // 是否锁定任务条码
        String lockByTask = Constant.STR_EMPTY;
        for (PsTask psTask : psTaskList) {
            lockByTask = psTask.getAttribute8();
            if (StringUtils.isNotBlank(psTask.getTaskStatus())) {
                break;
            }
        }
        warehouseEntryInfoQueryDTO.setLockByTask(lockByTask);

        // 已提交数量
        Long commitedQty = getCommitedQtySum(map);
        warehouseEntryInfoQueryDTO.setSubmitQty(new BigDecimal(commitedQty != null ? commitedQty : 0));
        return lockByTask;
    }

    private void setTaskNoOrItemNo(WarehouseEntryInfoQueryDTO warehouseEntryInfoQueryDTO, String lockByTask, PsWipInfoQueryDTO psWipInfoQueryDTO) {
        if (Constant.FLAG_Y.equals(lockByTask)) {
            psWipInfoQueryDTO.setTaskNo(warehouseEntryInfoQueryDTO.getTaskNo());
        } else {
            psWipInfoQueryDTO.setItemNo(warehouseEntryInfoQueryDTO.getItemNo());
            //查询任务信息
            List<String> lockedTask = psWipInfoService.getLockedTask(psWipInfoQueryDTO);
            psWipInfoQueryDTO.setItemNo(warehouseEntryInfoQueryDTO.getItemNo());
            psWipInfoQueryDTO.setNotInTask(lockedTask);
            warehouseEntryInfoQueryDTO.setNotInTask(lockedTask);
        }
    }

    private void setWorkOrderNo(WarehouseEntryInfoQueryDTO warehouseEntryInfoQueryDTO, List<PsWorkOrderBasic> psWorkOrderBasicList, PsWipInfoQueryDTO psWipInfoQueryDTO) {
        StringBuilder workOrderNo = new StringBuilder();
        for (PsWorkOrderBasic psWorkOrderBasic : psWorkOrderBasicList) {
            workOrderNo.append(Constant.SINGLE_QUOTE)
                    .append(psWorkOrderBasic.getWorkOrderNo())
                    .append(Constant.SINGLE_QUOTE).append(Constant.COMMA);
        }
        if (workOrderNo.length() > 0) {
            workOrderNo.deleteCharAt(workOrderNo.length() - 1);
        }
        psWipInfoQueryDTO.setWorkOrderNoList(workOrderNo.toString());
        warehouseEntryInfoQueryDTO.setWorkOrderNo(workOrderNo.toString());
    }

    /**
     * @param dto
     * @param factoryId
     * @param entityId
     * @param empNo
     * @return
     * @throws IOException
     * @throws RouteException a.定义 A=自建任务总套数    备注：自建任务 ps_task物料代码的“总数量”
     *                        b.B=已提交给ERP的套数（单据状态是“已接收”，不考虑单据状态是“已拒收”）    备注：根据该任务号汇总表 warehouse_entry_info的字段“COMMITED_QTY”单据数量
     *                        c.C=当前提交给erp的数量          备注：当前提交给erp入库单头 warehouse_entry_info的字段“单据数量”
     *                        d.D=自建任务清单130000003021代码需求数; （ 需求10  /   任务10 = 1
     *                        e.E=自建任务清单130000003021已发数;    10 
     *                        f.B+C小于等于A
     *                        g.E-（D/A）*(B+C)(如果D/A是小数，保留5位小数)大于等于0时 通过校验；如果小于0 ，则不能通过校验，提示物料代码XXXX已发数不足，不能完工，且本次提交失败；
     *                        h.校验的物料代码除去”虚拟件”，虚拟件来自表cs_sys_BasicSetting.WIP_REQUIREMENT_OPERATIONS  的字段“WIP_SUPPLY_TYPE”等于6；
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @RecordLogAnnotation("入库单提交")
    public Map<String, Object> saveWarehouseEntryInfo(WarehouseEntryInfoQueryDTO dto, String factoryId, String entityId, String empNo) throws Exception {
        checkOnlineRedisLock(dto);
        this.checkWeight(dto.getTaskNo(), dto.getPsWipInfoList());
        Map<String, Object> result = new HashMap<>(2);
        //  2020/11/16  防止重复对批次加锁，
        String redisKey = RedisKeyConstant.SAVE_WAREHOUSE_ENTRY_INFO_LOCK + dto.getProdplanId();
        RedisLock redisLock = new RedisLock(redisKey, NumConstant.NUM_1800);
        this.checkK2Submit(dto);
        // 外协入库单不能再次提交
        this.checkOutSource(dto);
        // 单板入库单。子卡入库单 进行技改管控
        this.checkTechnicalSaveWarehouseInfo(dto);
        // 整机入库单校验绑定关系
        this.checkWipExtendForZJ(dto);
        this.checkAuxMaterialBindingForZJ(dto);
        // 单板生产入库单校验入库前子工序滞留时长
        this.checkRetentionForDBRK(dto);
        try {
            boolean lock = redisLock.lock();
            if (!lock) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.CURRENT_BATCH_IS_BEING_SUBMITTED);
            }
            // 子卡完工入库单管控
            this.checkSubCardSaveWarehouseInfo(dto);
            //5.22 优化 提交之前校验条码当前工序是否一致
            psWipInfoServiceImp.checkWipInfoForSaveWarehouseEntryInfo(dto);

            // 该任务的组织ID与子库存的组织id不一致；请检查，谢谢！
            result.put("status", Constant.FLAG_N);
            result.put("msg", CommonUtils.getLmbMessage(MessageId.IN_WAREHOUSE_FAILED));
            // 0.查询  校验 orgId  数据来源于  ps_task 表   常用值395
            BigDecimal orgId = dto.getOrgId();
            if (dto.getBillTypeCode().intValue() == Constant.BILL_TYPE_LOOKUP_CODE_ZJ
                    && !StringUtils.equals(dto.getProductType(), Constant.INVENTORY_REWORK)) {

                String taskIdList = dto.getTaskIdList();
                if (checkTaskIdAndOrgId(result, orgId, taskIdList)) {
                    return result;
                }
                String[] taskIds = taskIdList.split(Constant.COMMA);
                // 校验 子库存在组织id 与任务的组织id是否一至； 点对点调用基础服务
                Map<String, String> record = new HashMap<>(2);
                record.put("secondaryInventoryName", dto.getSubStock());
                record.put("organizationId", dto.getOrgId().toString());
                List<MtlSecondaryInventories> subStocks = getMtlSecondaryInventories(record);
                if (CollectionUtils.isEmpty(subStocks)) {
                    result.put("msg", CommonUtils.getLmbMessage(MessageId.ORGANIZATION_ID_NOT_MATCH));
                    result.put("status", Constant.FLAG_N);
                    return result;
                }
                //调用回写服务校验  物料需求，组织ID与实体ID； 数据源： erp  表  WIP.WIP_REQUIREMENT_OPERATIONS
                List<ItemListEntityDTO> itemListEntityDTOList =
                        getItemListEntityListByTaskNo(dto.getTaskNo(), orgId);
                if (CollectionUtils.isEmpty(itemListEntityDTOList)) {
                    result.put("msg", CommonUtils.getLmbMessage(MessageId.TASK_NOT_QUERY_REQUIREMENTS));
                    result.put("status", Constant.FLAG_N);
                    return result;
                }
                // 1.校验 A 批次总数
                String errorMsg = this.checkQty(dto, itemListEntityDTOList);
                if (StringUtils.isNotBlank(errorMsg)) {
                    result.put("msg", errorMsg);
                    result.put("status", Constant.FLAG_N);
                    return result;
                }
            }
            // 2.保存  WarehouseEntryInfo
            result = this.validReqBillAndSaveWarehouse(dto, factoryId, entityId, empNo, result);
            //设置交货仓
            try {
                setStock(result);
            } catch (Exception e) {
                e.printStackTrace();
            }
        } finally {
            redisLock.unlock();
        }
        return result;
    }

    // 重量校验
    private void checkWeight(String taskNo, List<PsWipInfo> wipInfoList) throws Exception {
        if (!needCheckWeight) {
            return;
        }
        if (StringUtils.isBlank(taskNo) || CollectionUtils.isEmpty(wipInfoList)) {
            return;
        }

        PsTask psTask = PlanscheduleRemoteService.getPsTaskByTaskNo(taskNo);
        if (psTask == null) {
            String[] param = { taskNo };
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.FAILED_TO_GET_TASK_INFO_BY_TASKNO, param);
        }

        if (StringUtils.isBlank(psTask.getCustomerName())) {
            return;
        }

        // 判断是否阿里任务
        List<SysLookupValuesDTO> lookupValuesList = BasicsettingRemoteService.getSysLookupValues(Constant.LOOKUP_TYPE_7500);
        if (CollectionUtils.isEmpty(lookupValuesList)) {
            return;
        }
        List<String> customNameList = lookupValuesList.stream().map(SysLookupValuesDTO::getLookupMeaning).distinct().collect(Collectors.toList());

        if (!customNameList.contains(psTask.getCustomerName())) {
            // 非阿里任务无需校验
            return;
        }

        List<String> snList = wipInfoList.stream().map(PsWipInfo::getSn).distinct().collect(Collectors.toList());
        pmMachineWeightService.checkSnList(snList);
    }

    /**
     *<AUTHOR>
     * 单板生产入库220校验
     *@Date 2024/7/11 10:35
     *@Param [com.zte.interfaces.dto.WarehouseEntryInfoQueryDTO]
     *@return
     **/
    private void checkK2Submit (WarehouseEntryInfoQueryDTO dto) throws Exception {
        // 单板生产入库单类型限制
        if (!BILL_TYPE_SINGLE_CARD.equals(dto.getBillType()) || !Constant.STOCK_TYPE_K2.equals(dto.getStockType())) {
            return;
        }
        // 开关关闭时不允许提交仓库类型为半成品K2库的单据
        if (!validReqBillOn(Constant.LOOKUP_TYPE_6667, Constant.LOOKUP_TYPE_6667003)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.CANNOT_SUBMIT_K2_BILL);
        }
        // 获取220生成记录
        List<String> extraAttr = PlanscheduleRemoteService.getExtraAttr(dto.getProdplanId(), Constant.SOURCE_BATCH_CODE);
        // 存在220生成记录不能提交单板入库单
        if (!CollectionUtils.isEmpty(extraAttr)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PRODPLAN_EXIST_220_SN, new String[]{dto.getProdplanId()});
        }
    }
    private void checkOnlineRedisLock(WarehouseEntryInfoQueryDTO dto) {
        String onlineChangeLockKey = RedisKeyConstant.ONLINE_ROUTE_CHANGE + dto.getProdplanId();
        Object onlineChangeValue = redisTemplate.opsForValue().get(onlineChangeLockKey);
        if (onlineChangeValue != null) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.CURRENT_BATCH_IS_BEING_CHANGED);
        }
    }

    public void checkRetentionForDBRK(WarehouseEntryInfoQueryDTO dto) throws Exception {
        // 单板生产入库单类型限制
        if (!BILL_TYPE_SINGLE_CARD.equals(dto.getBillType())) {
            return;
        }
        List<PsWipInfo> psWipInfoList = dto.getPsWipInfoList();
        List<String> retentionSnList = psWipInfoService.filterRetentionList(psWipInfoList);
        if (!CollectionUtils.isEmpty(retentionSnList)) {
            retentionSnList.stream().distinct().collect(Collectors.toList());
            String[] infoParam = {StringUtils.join(retentionSnList, COMMA)};
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SN_CAN_NOT_SUBMIT_IN_STOCK, infoParam);
        }
    }

    public void checkWipExtendForZJ(WarehouseEntryInfoQueryDTO dto) throws Exception {
        if (!BILL_TYPE_ZJ.equals(dto.getBillType())) {
            return;
        }
        if(StringUtils.isEmpty(dto.getTaskNo()) || StringUtils.isEmpty(dto.getItemNo())){
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.TASK_NO_CAN_NOT_NULL);
        }
        // 首先校验任务号是否存在于绑定清单校验白名单中
        if (isExistInWhiteList(dto.getTaskNo(), Constant.LOOKUP_TYPE_7580006, Constant.LOOKUP_TYPE_7580006001, Constant.LOOKUP_TYPE_7580007)) {
            return;
        }
        // 白名单为空或任务号不在白名单中,默认需要校验绑定关系
        Map<String, Object> record = new HashMap<>();
        record.put(ProdBindingSetting.PRODUCT_CODE, dto.getItemNo());
        record.put(ProdBindingSetting.BIND_TYPE, Constant.INT_1);
        List<ProdBindingSetting> prodBindingList = prodBindingSettingRepository.getList(record);
        // 查询料单下不存在需要绑定的关系则认为校验通过
        if(CollectionUtils.isEmpty(prodBindingList)){
            return;
        }
        List<PsWipInfo> wipList = dto.getPsWipInfoList();
        if(CollectionUtils.isEmpty(wipList)){
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SN_NULL_VALID);
        }
        // 根据条码查询已有的装配关系
        List<String> formSnList = wipList.stream().map(PsWipInfo::getSn).distinct().collect(Collectors.toList());
        Map<String, Object> params = new HashMap<>();
        params.put("formSnList",formSnList);
        List<WipExtendIdentification> wipExtendList = wipExtendIdentificationRepository.getList(params);
        if(CollectionUtils.isEmpty(wipExtendList)){
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.TASK_NO_ALL_SN_NOT_FINISH_BINDING);
        }
        Map<String, List<String>> errorDetail = filterErrorSn(prodBindingList, formSnList, wipExtendList);
        // 组装错误信息
        if (!CollectionUtils.isEmpty(errorDetail)) {
            StringBuilder errorSb = new StringBuilder();
            for (String errorSn : errorDetail.keySet()) {
                errorSb.append(errorSn).append(ERROR_MSG_FOR_UNBINDING_PRE).append(STR_EMPTY_ONE)
                        .append(StringUtils.join(errorDetail.get(errorSn), COMMA)).append(STR_EMPTY_ONE)
                        .append(ERROR_MSG_FOR_UNBINDING_MIDDLE).append(SEMICOLON);
            }
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.ERROR_MSG_FOR_UNBINDING, new Object[]{errorSb.toString()});
        }
    }

    public Map<String, List<String>> filterErrorSn(List<ProdBindingSetting> prodBindingList, List<String> formSnList, List<WipExtendIdentification> wipExtendList) throws Exception {
        List<String> processCodeList = prodBindingList.stream().map(ProdBindingSetting::getProcessCode).distinct().collect(Collectors.toList());
        List<BSProcess> process = CrafttechRemoteService.getProcessByProList(processCodeList);
        Map<String, String> processMap = process.stream()
                .filter(p -> StringUtils.isNotBlank(p.getProcessName()))
                .collect(Collectors.toMap(BSProcess::getProcessCode, BSProcess::getProcessName, (a, b) -> a));
        prodBindingList.forEach(pb -> {
            pb.setProcessName(getProcessName(processMap, pb.getProcessCode()));
        });
        List<ProdBindingSetting> usageCountEmpty = prodBindingList.stream().filter(p->null == p.getUsageCount()).collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(usageCountEmpty)){
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.USAGE_COUNT_IS_NULL);
        }
        Map<String, List<WipExtendIdentification>> wipExtendMap = wipExtendList.stream().collect(Collectors.groupingBy(WipExtendIdentification::getFormSn));
        Map<String,List<String>> errorDetail =new HashMap<>();
        for (String formSn : formSnList) {
            List<String> processNameList = prodBindingList.stream().map(ProdBindingSetting::getProcessName).distinct().collect(Collectors.toList());
            List<WipExtendIdentification> wipExtendIdentificationList = wipExtendMap.get(formSn);
            if(CollectionUtils.isEmpty(wipExtendIdentificationList)){
                errorDetail.put(formSn,processNameList);
                continue;
            }
            filterErrorBindingDetail(prodBindingList, errorDetail, formSn, wipExtendIdentificationList);
        }
        return errorDetail;
    }

    public void filterErrorBindingDetail(List<ProdBindingSetting> prodBindingList, Map<String, List<String>> errorDetail, String formSn, List<WipExtendIdentification> wipExtendIdentificationList) {
        List<String> errorProcess =new ArrayList<>();
        for (ProdBindingSetting prodBindingSetting : prodBindingList) {
            String itemCode = prodBindingSetting.getItemCode();
            // 绑定完成校验，实际绑定的物料代码数量和绑定清单里面对应物料代码和替代物料代码需绑定数量比较，得到每种分组的实际绑定数量
            BigDecimal bindCount = wipExtendIdentificationList.stream().filter(i -> !Objects.equals(i.getFormQty(), null) && (Objects.equals(itemCode, i.getItemNo())
                    || Objects.equals(itemCode, i.getReplaceItemNo()))).map(WipExtendIdentification::getFormQty).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (bindCount.compareTo(prodBindingSetting.getUsageCount()) < Constant.INT_0) {
                errorProcess.add(prodBindingSetting.getProcessName());
            }
        }
        errorProcess = errorProcess.stream().distinct().collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(errorProcess)) {
            errorDetail.put(formSn, errorProcess);
        }
    }

    private String getProcessName(Map<String, String> processMap, String processCode) {
        String pro = null == processMap ? Constant.EMPTY_STRING : processMap.get(processCode);
        return StringUtils.isEmpty(pro) ? processCode : pro;
    }

    public boolean isExistInWhiteList(String taskNo, String lookupType, String lookupCode, String whiteLookupCode) throws Exception {
        SysLookupTypesDTO sysLookupTypesDTO = BasicsettingRemoteService.getSysLookUpValue(lookupType,
                lookupCode);
        if (sysLookupTypesDTO == null || !Constant.FLAG_Y.equals(sysLookupTypesDTO.getLookupMeaning())) {
            return true;
        }
        List<SysLookupValuesDTO> sysWhiteList = BasicsettingRemoteService.getSysLookupValuesList(new SysLookupValuesDTO() {{
            setLookupType(new BigDecimal(whiteLookupCode));
        }});
        List<String> whiteTasks = sysWhiteList.stream()
                .filter(e -> StringUtils.isNotBlank(e.getLookupMeaning()))
                .flatMap(e -> Arrays.stream(e.getLookupMeaning().split(Constant.COMMA)))
                .distinct().collect(Collectors.toList());
        if (whiteTasks.contains(taskNo)) {
            return true;
        }
        return false;
    }


    public void checkAuxMaterialBindingForZJ(WarehouseEntryInfoQueryDTO dto) throws Exception {
        if (!BILL_TYPE_ZJ.equals(dto.getBillType()) && !BILL_TYPE_SINGLE_CARD.equals(dto.getBillType())) {
            return;
        }
        if (dto.getCommitedQty() == null || dto.getCommitedQty().compareTo(BIG_ZERO) <= INT_0) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SUBMIT_QTY_IS_NULL);
        }
        if (isExistInWhiteList(dto.getTaskNo(), Constant.LOOKUP_VALUE_7580026, Constant.LOOKUP_TYPE_7580026001, Constant.LOOKUP_VALUE_7580027)) {
            return;
        }
        Map<String, Object> map = new HashMap<>();
        map.put("taskNo", Constant.SINGLE_QUOTE + dto.getTaskNo() + Constant.SINGLE_QUOTE);
        List<PsTask> psTaskList = PlanscheduleRemoteService.getTaskListByTaskNos(map);
        if (CollectionUtils.isEmpty(psTaskList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.TRACKING_OR_TASK_NO_NOT_EXIST);
        }
        // 该字段不是null则是返工任务，不需要进行后续校验
        boolean reFlag = psTaskList.stream().anyMatch(i -> StringUtils.isNotEmpty(i.getOriginalTask()));
        if (reFlag) {
            return;
        }
        Map<String, Object> record = new HashMap<>();
        // 根据料单查询辅料绑定关系
        record.put(ProdBindingSetting.BIND_TYPE, INT_2);
        record.put(ProdBindingSetting.PRODUCT_CODE, dto.getItemNo());
        List<ProdBindingSetting> prodBindingList = prodBindingSettingRepository.getList(record);
        // 不存在需要绑定的关系则认为校验通过
        if (CollectionUtils.isEmpty(prodBindingList)) {
            return;
        }
        List<String> taskNoList = new ArrayList<>();
        taskNoList.add(dto.getTaskNo());
        List<WarehouseEntryInfoDTO> commitedQtyList = warehouseEntryInfoRepository.getCommitQtyByTaskNo(taskNoList, dto.getBillType());
        BigDecimal receivedCount = CollectionUtils.isEmpty(commitedQtyList) ? BIG_ZERO : commitedQtyList.get(0).getCommitedQty();
        Map<String, Object> params = new HashMap<>();
        params.put("taskNo", dto.getTaskNo());
        params.put("formType", STR_4);
        List<WipExtendIdentification> wipExtendList = wipExtendIdentificationRepository.getList(params);
        // 根据任务号获取erp任务清单，可能需要计算每个条码的用量（暂定）
        List<ItemListEntityDTO> erpList = DatawbRemoteService.getItemListByTaskList(taskNoList);
        if (CollectionUtils.isEmpty(erpList)) {
            return;
        }
        Map<String, PsTask> psTaskMap = psTaskList.stream().collect(Collectors.toMap(PsTask::getItemNo, i -> i, (k1, k2) -> k1));
        StringBuilder errorMessage = calculateAuxBindingCount(prodBindingList, dto.getCommitedQty(), wipExtendList, receivedCount,psTaskMap);
        if (StringUtils.isNotBlank(errorMessage)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.STOCK_AUX_MATERIAL_UNBINDING, new Object[]{dto.getTaskNo(), errorMessage.toString()});
        }
    }

    public StringBuilder calculateAuxBindingCount(List<ProdBindingSetting> prodBindingList, BigDecimal commitQty, List<WipExtendIdentification> wipExtendList, BigDecimal receivedCount, Map<String, PsTask> psTaskMap) {
        StringBuilder errorMessage = new StringBuilder();
        for (ProdBindingSetting prodBindingSetting : prodBindingList) {
            if(null == prodBindingSetting.getUsageCount()){
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.BIND_SETTING_USAGE_COUNT_IS_NULL,new String[]{prodBindingSetting.getProductCode(),prodBindingSetting.getItemCode()});
            }
            // 如果没有获取到或erp对应的需绑物料者需求量为0，则直接跳过该物料
            if (prodBindingSetting.getUsageCount().compareTo(BIG_ZERO) <= INT_0) {
                continue;
            }
            // 计算已绑定数量，考虑替代物料情况
            BigDecimal bindCount = wipExtendList.stream().filter(i -> !Objects.equals(i.getFormQty(), null) && (Objects.equals(prodBindingSetting.getItemCode(), i.getItemNo())
                    || Objects.equals(prodBindingSetting.getItemCode(), i.getReplaceItemNo()))).map(WipExtendIdentification::getFormQty).reduce(BigDecimal.ZERO, BigDecimal::add);
            // 如果已绑定数量已经大于等于应绑定的全部数量（说明该物料已经全部绑完），则直接跳过
            PsTask psTask = psTaskMap.get(prodBindingSetting.getProductCode());
            if (psTask == null) {
                continue;
            }
            if (bindCount.compareTo(prodBindingSetting.getUsageCount().multiply(psTask.getTaskQty())) >= INT_0) {
                continue;
            }
            BigDecimal availableCount = bindCount.subtract(prodBindingSetting.getUsageCount().multiply(receivedCount));
            BigDecimal needCount = commitQty.multiply(prodBindingSetting.getUsageCount());
            // 计算物料数量是否符合要求：辅料已绑定数量>=辅料绑定关系的需绑定数量*（已提交入库数量+本次提交数量）
            if (availableCount.compareTo(needCount) < INT_0) {
                errorMessage.append(String.format(Constant.NEED_BIND_ERROR_MESSAGE, prodBindingSetting.getItemCode(), availableCount.setScale(0, RoundingMode.DOWN).intValue(), needCount.setScale(0, RoundingMode.UP).intValue())).append(STR_EMPTY_ONE);
            }
        }
        return errorMessage;
    }

    /**
     * 校验是否外协批次
     *
     * @param dto 请求参数
     * @throws Exception 业务异常
     */
    private void checkOutSource(WarehouseEntryInfoQueryDTO dto) throws Exception {
        if (STR_10.equals(dto.getBillType())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SUBMIT_OUTSOURCE_FORBIDDEN_THREE);
        }
        String prodplanId = dto.getProdplanId();
        List<PsWorkOrderDTO> workOrderInfo = PlanscheduleRemoteService.getWorkOrderInfo(prodplanId);
        if (CollectionUtils.isEmpty(workOrderInfo)) {
            return;
        }
        String processCode = CrafttechRemoteService.getProcessCode(Constant.OUTSOURCE);
        if (StringUtils.isBlank(processCode)) {
            return;
        }
        PsWorkOrderDTO psWorkOrderDTO = workOrderInfo.get(0);
        // 工艺路径是否包含外协
        List<CtRouteDetailDTO> details = CrafttechRemoteService.getCtRouteDetailByRouteIds(Arrays.asList(psWorkOrderDTO.getRouteId()));
        Optional<CtRouteDetailDTO> first = details.stream()
                .filter(item -> StringUtils.equals(processCode, item.getNextProcess()))
                .findFirst();
        // 外协任务，只有外协节点不能在此页面提交
        if (first.isPresent() && details.size() <= INT_2) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SUBMIT_OUTSOURCE_FORBIDDEN);
        }
    }

    /**
     * 单板入库单。子卡入库单 进行子卡入库管控
     * 子卡提交入库库数量（入库单状态为“已接收”）/子卡单位用量>=   主任务已提交入库数量（已提交）+本次提交数量（注意并发场景）则允许入库，
     * 否则报错“XXX（计划跟踪单号）已领用到主任务数量为XXX，还差Y块，请先处理再提交入库”(如果有多个子卡任务都不满足提交则一起提示)
     *
     * @param dto 入库单实体
     * @throws Exception 业务异常
     */
    public void checkSubCardSaveWarehouseInfo(WarehouseEntryInfoQueryDTO dto) throws Exception {
        if (this.checkCondition(dto)) {
            return;
        }
        // 获取子卡信息
        List<String> taskNoList = new LinkedList<>();
        taskNoList.add(dto.getTaskNo());
        List<PsTask> treeList = planscheduleRemoteService.getSubTaskTreeByTaskNo(taskNoList, FLAG_N);
        if (CollectionUtils.isEmpty(treeList) || CollectionUtils.isEmpty(treeList.get(0).getSubChildList())) {
            return;
        }
        // 查中心公产BOM 用量信息
        List<BBomDetailResponseDTO> bBomDetails = centerfactoryRemoteService.centerFactoryBBomInfoList(dto.getItemNo());
        if (CollectionUtils.isEmpty(bBomDetails)) {
            return;
        }
        this.extractedSub(dto, treeList, bBomDetails);
    }

    /**
     * 计算子卡数量是否满足
     *
     * @param dto         入库实体
     * @param treeList    主子卡关系
     * @param bBomDetails BOM 明细
     * @return 子卡入库锁
     * @throws MesBusinessException 业务异常
     */
    private void extractedSub(WarehouseEntryInfoQueryDTO dto, List<PsTask> treeList,
                              List<BBomDetailResponseDTO> bBomDetails) throws MesBusinessException {
        List<PsTask> subChildList = treeList.get(0).getSubChildList();
        List<String> itemNoList = subChildList.stream().map(PsTask::getItemNo).collect(Collectors.toList());
        Map<String, Integer> itemUsageCountMap = bBomDetails.stream()
                .filter(item -> itemNoList.contains(item.getItemCode()))
                .filter(item -> item.getUsageCount() != null)
                .collect(Collectors.toMap(BBomDetailResponseDTO::getItemCode, v -> v.getUsageCount().intValue()));
        // 查询当前主卡任务已入库数量
        List<String> mainTaskList = new LinkedList<>();
        mainTaskList.add(dto.getTaskNo());
        List<String> mainStatusList = new LinkedList<>();
        // 已提交 0
        mainStatusList.add(STR_0);
        // 已接收 2
        mainStatusList.add(STR_2);
        List<String> billTypeList = new LinkedList<>();
        // 单板入库单
        billTypeList.add(STR_2);
        // 子卡入库单
        billTypeList.add(STR_4);
        // 外协入库单
        billTypeList.add(STR_10);
        // 查询主卡提交数量
        List<WarehouseEntryDetail> mainCollect = warehouseEntryDetailRepository.queryDetailsCount(mainTaskList, mainStatusList, billTypeList);
        if (Objects.isNull(mainCollect)) {
            mainCollect = new LinkedList<>();
        }
        Map<String, Integer> mainSubmitMap = mainCollect.stream()
                .collect(Collectors.toMap(WarehouseEntryDetail::getTaskNo, WarehouseEntryDetail::getCountQty));
        // 查询子卡任务已经接收的数量
        List<String> subTaskList = subChildList.stream().map(PsTask::getTaskNo).collect(Collectors.toList());
        List<String> subStatusList = new LinkedList<>();
        subStatusList.add(STR_2);
        List<WarehouseEntryDetail> childDetails = warehouseEntryDetailRepository.queryDetailsCount(subTaskList, subStatusList, billTypeList);
        if (Objects.isNull(childDetails)) {
            childDetails = new LinkedList<>();
        }
        Map<String, Integer> childSubmitMap = childDetails.stream()
                .collect(Collectors.toMap(WarehouseEntryDetail::getTaskNo, WarehouseEntryDetail::getCountQty));
        // 计算匹配数量
        int size = dto.getPsWipInfoList().isEmpty() ? dto.getCommitedQty().intValue() : dto.getPsWipInfoList().size();
        Integer mainCount = mainSubmitMap.get(dto.getTaskNo());
        if (Objects.isNull(mainCount)) {
            mainCount = INT_0;
        }
        // 主卡入库数量等于
        mainCount += size;
        this.buildErrorMsgPossible(subChildList, childSubmitMap, itemUsageCountMap, mainCount);
    }

    /**
     * 计算 子卡用量， 不满足报错提示
     *
     * @param subChildList      子卡集合
     * @param childSubmitMap    子卡提交数量
     * @param itemUsageCountMap bom清单用量
     * @param mainCount         已提交主卡数量
     */
    private void buildErrorMsgPossible(List<PsTask> subChildList, Map<String, Integer> childSubmitMap,
                                       Map<String, Integer> itemUsageCountMap, int mainCount) throws MesBusinessException {
        StringBuilder errorMsgBuffer = new StringBuilder();
        for (PsTask psTask : subChildList) {
            String itemNo = psTask.getItemNo();
            // 获取BOM标准用量
            Integer bomInteger = itemUsageCountMap.get(itemNo);
            if (Objects.isNull(bomInteger)) {
                continue;
            }
            // 已入库子卡数量
            Integer childCount = childSubmitMap.get(psTask.getTaskNo());
            if (Objects.isNull(childCount)) {
                childCount = INT_0;
            }
            // 用量超出实际报错 主卡用量 * 子卡BOM 用量 <= 子卡入库数量 才满足
            if (mainCount * bomInteger > childCount) {
                int subCount = mainCount * bomInteger - childCount;
                errorMsgBuffer.append(CommonUtils.getLmbMessage(MessageId.WAREHOUSE_SUBMIT_ERROR_MSG,
                        new String[]{psTask.getTaskNo(), String.valueOf(childCount), String.valueOf(subCount)}));
            }
        }
        String msg = errorMsgBuffer.toString();
        if (StringUtils.isNotBlank(msg)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.CUSTOMIZE_MSG, new Object[]{msg});
        }
    }

    /**
     * 校验条件
     *
     * @param dto 入库单实体 check
     * @return 是否需要校验
     */
    private boolean checkCondition(WarehouseEntryInfoQueryDTO dto) {
        if (Boolean.FALSE.equals(subCardEnable)) {
            return true;
        }
        if (!StringUtils.equals(BILL_TYPE_SINGLE_CARD, dto.getBillType())
                && !StringUtils.equals(BILL_TYPE_SON_CARD, dto.getBillType())
        && !StringUtils.equals(BILL_TYPE_OUT_CARD, dto.getBillType())) {
            return true;
        }
        if (StringUtils.isBlank(dto.getTaskNo()) || StringUtils.isBlank(dto.getItemNo())
                || (CollectionUtils.isEmpty(dto.getPsWipInfoList())
                && (dto.getCommitedQty() == null || dto.getCommitedQty().compareTo(BigDecimal.ZERO) == 0))) {
            return true;
        }
        return false;
    }

    /**
     * 单板入库单。子卡入库单 进行技改管控
     *
     * @param dto 入库单实体
     * @throws Exception 业务异常
     */
    private void checkTechnicalSaveWarehouseInfo(WarehouseEntryInfoQueryDTO dto) throws Exception {
        // 单板入库单。子卡入库单 进行技改管控
        if (StringUtils.equals(BILL_TYPE_SINGLE_CARD, dto.getBillType())
                || StringUtils.equals(BILL_TYPE_SON_CARD, dto.getBillType())) {
            List<PsWipInfo> psWipInfoList = dto.getPsWipInfoList();
            if (CollectionUtils.isEmpty(psWipInfoList)) {
                return;
            }
            List<String> snList = psWipInfoList.stream()
                    .filter(item -> StringUtils.isNotBlank(item.getSn()))
                    .map(PsWipInfo::getSn).distinct()
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(snList)) {
                return;
            }
            BarcodeBindingDTO barcodeBindingDTO = new BarcodeBindingDTO();
            barcodeBindingDTO.setSnList(snList);
            barcodeBindingDTO.setWorkOrderNo(psWipInfoList.get(0).getWorkOrderNo());
            barcodeBindingDTO.setJumpProcess(Boolean.TRUE);
            barcodeBindingDTO.setJumpCraftSection(Boolean.TRUE);
            flowControlCommonService.technicalControl(barcodeBindingDTO);
        }
    }

    /**
     * 校验整机车间库是否有需求单， 且满足数量要求， 锁定需求单
     *
     * @param dto
     * @param factoryId
     * @param entityId
     * @param empNo
     * @param result
     * @return
     * @throws Exception
     */
    public Map<String, Object> validReqBillAndSaveWarehouse(WarehouseEntryInfoQueryDTO dto, String factoryId,
                                                            String entityId, String empNo, Map<String, Object> result) throws Exception {
        boolean needReleasLock = false;
        RedisLock redisLock = null;
        try {
            //判断整机入库单需求单是否为空
            if (validReqBillOn(Constant.LOOK_UP_TYPE_STOCK_RCV_RIGHT, Constant.LOOK_UP_CODE_REQ_VALID_SWITCH) && Constant.STOCK_TYPE_MODE_WORKSTATION.equals(dto.getStockType()) && MpConstant.BILL_TYPE_SINGLE.equals(dto.getBillType())) {
                if (StringHelper.isEmpty(dto.getReqBillNo())) {
                    result.put("msg", CommonUtils.getLmbMessage(MessageId.WAREHOUSE_MODE_REQ_EMPTY));
                    result.put("status", Constant.FLAG_N);
                    return result;
                } else {
                    //校验需求单最大能入库数量 与提交数量
                    String errorMsg = validAndLockWarehouseReq(dto.getReqBillNo(), dto.getCommitedQty());
                    if (StringUtils.isNotEmpty(errorMsg)) {
                        result.put("msg", errorMsg);
                        result.put("status", Constant.FLAG_N);
                        return result;
                    }
                    //  2020/7/16  需求单加锁，
                    String redisKey = RedisKeyConstant.REQ_BILL_NO_LOCK + dto.getReqBillNo();
                    redisLock = new RedisLock(redisKey);
                    boolean lock = redisLock.lock();
                    if (!lock) {
                        result.put("msg", CommonUtils.getLmbMessage(MessageId.REQ_BILL_IS_LOCK));
                        result.put("status", Constant.FLAG_N);
                        return result;
                    } else {
                        needReleasLock = true;
                        WarehouseRequirementInfo record = new WarehouseRequirementInfo();
                        record.setBillNo(dto.getReqBillNo());
                        record.setStatus(CommonConst.WAREHOUSE_REQUIREMENT_INFO_DONING);
                        warehouseRequirementInfoRepository.updateRequirmentInfo(record);
                    }
                }
            }
            verifyWholeMachineOrSingleBoard(dto);
            return this.saveWarehouse(dto, factoryId, entityId, empNo, result);
        } catch (Exception e) {
            result.put("msg", e.getMessage());
            result.put("status", Constant.FLAG_N);
            throw e;
        } finally {
            unlock(dto, empNo, needReleasLock, redisLock);
        }

    }

    public void unlock(WarehouseEntryInfoQueryDTO dto, String empNo, boolean needReleasLock, RedisLock redisLock) {
        if (needReleasLock && null != redisLock) {
            redisLock.unlock();
            //  2020/7/17 写需求单详表
            warehouseRequirementDetailRepository.insert(packageWarehouseReq(dto, empNo));
        }
    }

    public void verifyWholeMachineOrSingleBoard(WarehouseEntryInfoQueryDTO dto) throws MesBusinessException {
        //如果单据类型如果是整机车间库或者单板生产入库单，校验仓库类型不能为空
        if (dto.getBillTypeCode() == null ||
                ((dto.getBillTypeCode().intValue() == Constant.BILL_TYPE_LOOKUP_CODE_DB || dto.getBillTypeCode().intValue() == Constant.BILL_TYPE_LOOKUP_CODE_ZJ
                        || dto.getBillTypeCode().intValue() == BILL_TYPE_LOOKUP_CODE_DBFX)
                        && StringUtils.isBlank(dto.getStockType()))) {
            String[] params = {dto.getBillTypeCode().toString()};
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.WAREHOUSEENTRYINFO_STOCK_TYPE_IS_NULL, params);
        }
    }

    //组装写需求详表数据
    private WarehouseRequirementDetail packageWarehouseReq(WarehouseEntryInfoQueryDTO dto, String empno) {
        WarehouseRequirementDetail reqDetail = new WarehouseRequirementDetail();
        WarehouseRequirementInfo warehouseRequirementInfo = warehouseRequirementInfoRepository.getWarehouseReqByBillNo(dto.getReqBillNo());
        if (null == warehouseRequirementInfo) {
            return reqDetail;
        }
        BeanUtils.copyProperties(dto, reqDetail);
        reqDetail.setDetailId(UUID.randomUUID().toString());
        reqDetail.setHeadId(warehouseRequirementInfo.getRequirementId());
        reqDetail.setProdPlanId(dto.getProdplanId());
        reqDetail.setStockInNo(dto.getBillNo());
        reqDetail.setSubInventory(dto.getSubStock());
        reqDetail.setLocationNo(dto.getLocatorName() == null ? "" : dto.getLocatorName());
        reqDetail.setWarehouseCode(dto.getStockName());
        reqDetail.setQty(dto.getCommitedQty());
        reqDetail.setCreateBy(empno);
        reqDetail.setCreateDate(new Date());
        reqDetail.setLastUpdatedBy(empno);
        reqDetail.setLastUpdatedDate(new Date());
        reqDetail.setEnabledFlag(Constant.FLAG_Y);
        reqDetail.setWarehousingStatus(MpConstant.STATUS_ZERO);
        return reqDetail;
    }

    //校验需求单开关,  true： 打开开关，  校验，  false: 不校验
    private boolean validReqBillOn(String lookupType, String lookupValue) throws Exception {
        SysLookupTypesDTO sysLookupTypesDTO = BasicsettingRemoteService.getSysLookUpValue(lookupType, lookupValue);
        return null != sysLookupTypesDTO && Constant.FLAG_Y.equals(sysLookupTypesDTO.getLookupMeaning());
    }

    /**
     * 设置交货仓
     *
     * @param result
     * @throws Exception
     */
    public void setStock(Map<String, Object> result) throws Exception {
        if (null != result && result.containsKey(Constant.WAREHOUSE_ENTRY_INFO)) {
            WarehouseEntryInfo warehouseEntryInfo = (WarehouseEntryInfo) result.get(Constant.WAREHOUSE_ENTRY_INFO);
            if (StringUtils.isNotEmpty(warehouseEntryInfo.getProdplanNo())) {
                result.put("stock", datawbRemoteService.getApsOpProdplanStock(warehouseEntryInfo.getProdplanNo()));
            }
        }
    }

    private boolean checkTaskIdAndOrgId(Map<String, Object> result, BigDecimal orgId, String taskIdList) {
        if (orgId == null) {
            result.put("msg", CommonUtils.getLmbMessage(MessageId.TASK_ORG_ID_IS_NULL));
            result.put("status", Constant.FLAG_N);
            return true;
        }
        if (StringUtils.isBlank(taskIdList)) {
            result.put("msg", CommonUtils.getLmbMessage(MessageId.TASK_ID_IS_NULL));
            result.put("status", Constant.FLAG_N);
            return true;
        }
        return false;
    }

    public String checkQty(WarehouseEntryInfoQueryDTO dto, List<ItemListEntityDTO> itemListEntityDTOList) throws Exception {
        List<String> itemCodeList = itemListEntityDTOList.stream().map(ItemListEntityDTO::getItemNo).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        // 去erp查询物料代码是否存在替代物料
        List<MtlRelatedItemsEntityDTO> list = assemblyRelaScanService.getReplaceItemByErp(itemCodeList, false);
        if (CollectionUtils.isEmpty(list)) {
            list = new ArrayList<>();
        }
        Map<String, String> replaceItemCodeMap = list.stream().collect(Collectors.toMap(MtlRelatedItemsEntityDTO::getItemCode, MtlRelatedItemsEntityDTO::getReplaceItemCode, (k1, k2) -> k1));
        Map<String, ItemListEntityDTO> itemListEntityDTOMap = itemListEntityDTOList.stream().collect(Collectors.toMap(ItemListEntityDTO::getItemNo, k -> k, (k1, k2) -> k1));
        String errorMsg = Constant.STR_EMPTY;
        BigDecimal taskQty = dto.getTaskQty() == null ? new BigDecimal(NUM_ZERO) : dto.getTaskQty();
        if (taskQty.compareTo(new BigDecimal(NUM_ZERO)) == NUM_ZERO) {
            errorMsg = CommonUtils.getLmbMessage(MessageId.TASK_QTY_IS_ZERO);
            return errorMsg;
        }

        // B
        BigDecimal commitedQty = dto.getCommitedQty() == null ? new BigDecimal(NUM_ZERO) : dto.getCommitedQty();
        // C
        BigDecimal submitQty = dto.getSubmitQty() == null ? new BigDecimal(NUM_ZERO) : dto.getSubmitQty();
        if (commitedQty.add(submitQty).compareTo(taskQty) > NUM_ZERO) {
            errorMsg = CommonUtils.getLmbMessage(MessageId.CANNOT_EXCEED_TOTAL_NUMBER);
            return errorMsg;
        }
        int virtualType = 6;
        for (ItemListEntityDTO model : itemListEntityDTOList) {
            // H
            if (Integer.parseInt(model.getWipSupplyType()) == virtualType) {
                continue;
            }
            //D 需求数量
            BigDecimal requiredQty = new BigDecimal(model.getRequiredQuantity());

            //E 已发数
            BigDecimal qtyIssued = new BigDecimal(model.getQuantityIssued());

            //都是负数，取绝对值
            if (requiredQty.compareTo(new BigDecimal(NUM_ZERO)) < NUM_ZERO) {
                requiredQty = requiredQty.abs();
                qtyIssued = qtyIssued.abs();
            }

            //f.B+C小于等于A   &&    E-（D/A）*(B+C)(如果D/A是小数，保留5位小数)大于等于0
            //            if ((qtyIssued - (requiredQty / taskQty) * (commitedQty + submitQty)) < 0) {
            //单个用量
            BigDecimal singleQty = requiredQty.divide(taskQty, NumConstant.NUM_FIVE, RoundingMode.DOWN);
            //需要数量
            BigDecimal needQty = singleQty.multiply(commitedQty.add(submitQty));
            // 物料已发数量不足时，校验是否有替代物料以及替代物料+物料已发数是否足够
            if (qtyIssued.subtract(needQty).compareTo(new BigDecimal(NUM_ZERO)) < NUM_ZERO && this.checkRepalceItemQty(model, needQty, qtyIssued, replaceItemCodeMap, itemListEntityDTOMap)) {
				errorMsg = CommonUtils.getLmbMessage(MessageId.ITEM_CODE_NOT_ENOUGH_SENT, new String[]{dto.getTaskNo(),model.getItemNo()});
                return errorMsg;
            }
        }
        return errorMsg;
    }

    /**
     * 物料已发数量不足时，校验是否有替代物料以及替代物料+物料已发数是否足够
     */
    private boolean checkRepalceItemQty(ItemListEntityDTO model, BigDecimal needQty, BigDecimal qtyIssued, Map<String, String> replaceItemCodeMap, Map<String, ItemListEntityDTO> itemListEntityDTOMap) {
        // 没有替代物料，就直接返回
        String replaceItemCode = replaceItemCodeMap.get(model.getItemNo());
        if (replaceItemCode == null) {
            return true;
        }
        // 有替代物料但是在物料清单中没有对应物料数据，返回数量不足
        ItemListEntityDTO itemListEntityDTO = itemListEntityDTOMap.get(replaceItemCode);
        if (null == itemListEntityDTO) {
            return true;
        }
        // 物料清单中有替代物料数据，计算总的已发数量进行比较
        BigDecimal replaceQtyIssued = new BigDecimal(itemListEntityDTO.getQuantityIssued());
        BigDecimal totalQty = qtyIssued.add(replaceQtyIssued);
        return totalQty.subtract(needQty).compareTo(new BigDecimal(NUM_ZERO)) < NUM_ZERO;
    }

    private Map<String, Object> saveWarehouse(WarehouseEntryInfoQueryDTO dto,
                                              String factoryId, String entityId, String empNo, Map<String, Object> result) throws Exception {
        String applyNo = "";
        // 保存 WarehouseEntryInfo
        WarehouseEntryInfo warehouseEntryInfo = WarehouseEntryInfoAssembler.convertEntityByWarehouseEntryInfoQueryDTO(dto);
        if (StringUtils.isNotBlank(factoryId)) {
            warehouseEntryInfo.setFactoryId(new BigDecimal(factoryId));
        }
        if (StringUtils.isNotBlank(entityId)) {
            warehouseEntryInfo.setEntityId(new BigDecimal(entityId));
        }
        warehouseEntryInfo.setOrgId(dto.getOrgId());
        warehouseEntryInfo.setCreateBy(empNo);
        warehouseEntryInfo.setLastUpdatedBy(empNo);
        warehouseEntryInfo.setWarehouseEntryId(UUID.randomUUID().toString());
        //已提交
        warehouseEntryInfo.setStatus(Constant.STR_NUMBER_ZERO);
        //isRepair 或者 isSmall
        this.isRepairOrSmall(dto, warehouseEntryInfo);
        boolean isSubCard = this.checkSubCardNew(dto);
        boolean isSubcardK2 = isSubcardK2(result, warehouseEntryInfo, isSubCard);
        //提交入库单时，如果是单板生产入库单则判断该批次是否有已经入库的条码（wip_info 主工序等于入库），如果没有则更新该批次的首件入库日期（ps_task  first_warehouse_date ）为当前时间
        updateFirstWarehouseDate(dto, warehouseEntryInfo);
        // 获取入库前子工序是否维护了滞留时长
        boolean isCheckRetention = isPreserveRetention(dto.getProdplanId());
        isCheckRetention = isCheckRetention && dto.getBillTypeCode().intValue() == Constant.BILL_TYPE_LOOKUP_CODE_DB;
        if ((dto.getBillTypeCode().intValue() == Constant.BILL_TYPE_LOOKUP_CODE_DB || dto.getBillTypeCode().intValue() == BILL_TYPE_LOOKUP_CODE_DBFX)
                && (dto.getStockType().equals(Constant.STOCK_TYPE_K2) || dto.getStockType().equals(Constant.STOCK_TYPE_MODE_WORKSTATION))
                && isWriteBackStep() && !isSubCard) {
            WarehouseEntryInfoDTO warehouseEntryDTO = WarehouseEntryInfoAssembler.toDTO(warehouseEntryInfo);
            // 单板生产入库单入K2库且维护了滞留时长，在接收时再写barsubmit
            if (Constant.STOCK_TYPE_K2.equals(dto.getStockType()) && !isCheckRetention) {
                applyNo = warehouseEntryInfoWritebackStep(warehouseEntryDTO);
            }
            //单据类型为单板返修入库时，将ProdplanId赋值给Attribute1（内部还更新BoardOnline返修状态字段为0正常入库,同时单据号回写）
            setProdplanIdByAttribute(dto, warehouseEntryDTO);
            WarehouseEntryInfoQueryDTO warehouseEntryInfoQueryDTO = this.getWarehouseEntryInfoQueryDTO(factoryId, entityId, empNo, result, applyNo);
            result = this.insertWarehouse(dto, warehouseEntryInfoQueryDTO, warehouseEntryInfo);
        } else if (isSubCard) {
            //参数过多，这里设置
            dto.setFacId(factoryId);
            dto.setEntyId(entityId);
            dto.setEmNo(empNo);
            dto.setAppNo(applyNo);
            result = doK2OrNotK2(isSubcardK2, dto, result, warehouseEntryInfo);
        } else {
            WarehouseEntryInfoQueryDTO entryInfoQueryDTO = this.getWarehouseEntryInfoQueryDTO(factoryId, entityId, empNo, result, applyNo);
            // 插入
            result = this.insertWarehouse(dto, warehouseEntryInfo, entryInfoQueryDTO);
        }
        return result;
    }

    public boolean isPreserveRetention(String prodplanId) throws Exception {
        // 生产入库单且入库前子工序维护了滞留时长
        boolean isCheckRetention = false;
        List<PsWorkOrderDTO> workOrderList = PlanscheduleRemoteService.getWorkOrderBasicByProdPlanId(prodplanId, null);
        if(!CollectionUtils.isEmpty(workOrderList)){
            PsWorkOrderDTO psWorkOrderDTO = workOrderList.get(NUM_ZERO);
            String routeId = psWorkOrderDTO.getRouteId();
            List<CtRouteDetail> lastList = CrafttechRemoteService.getCtRouteDetailInfo(routeId, "", "", "");
            if(!CollectionUtils.isEmpty(lastList)){
                CtRouteDetail ctRouteTemp = lastList.stream().filter(c->Constant.PROCESS_CODE_WH.equals(c.getNextProcess())).findFirst().orElse(null);
                if(null == ctRouteTemp){
                    return isCheckRetention;
                }
                String lastProcessBeforeInStock = ctRouteTemp.getCurrProcess();
                CtRouteDetail ctRouteDetail = lastList.stream().filter(c->StringUtils.equals(lastProcessBeforeInStock,c.getNextProcess())).findFirst().orElse(null);
                if(null == ctRouteDetail){
                    return isCheckRetention;
                }
                BigDecimal remainTime = ctRouteDetail.getRemainTime();
                isCheckRetention = !Objects.isNull(remainTime);
                return isCheckRetention;
            }
        }
        return isCheckRetention;
    }

    /**
     * 将ProdplanId赋值给Attribute1
     *
     * @param dto
     * @param warehouseEntryDTO
     * @throws Exception
     */
    private void setProdplanIdByAttribute(WarehouseEntryInfoQueryDTO dto, WarehouseEntryInfoDTO warehouseEntryDTO) throws Exception {
        if (dto.getBillTypeCode().intValue() == BILL_TYPE_LOOKUP_CODE_DBFX) {
            if(!CollectionUtils.isEmpty(dto.getPsWipInfoList())){
                dto.getPsWipInfoList().forEach(item->{
                    if (StringUtils.isBlank(item.getAttribute1())) {
                        item.setAttribute1(dto.getProdplanId());
                    }
                });
            }
            // 单板返修入库提交时,更新BoardOnline返修状态字段为0正常入库,同时单据号回写spm的bar_submit表，类型为“2”
            updateBoardOnlineAndBarSubmit(dto, warehouseEntryDTO);
        }
    }

    private WarehouseEntryInfoQueryDTO getWarehouseEntryInfoQueryDTO(String factoryId, String entityId, String empNo, Map<String, Object> result, String applyNo) {
        WarehouseEntryInfoQueryDTO warehouseEntryInfoQueryDTO = new WarehouseEntryInfoQueryDTO();
        warehouseEntryInfoQueryDTO.setFacId(factoryId);
        warehouseEntryInfoQueryDTO.setEntyId(entityId);
        warehouseEntryInfoQueryDTO.setEmNo(empNo);
        warehouseEntryInfoQueryDTO.setAppNo(applyNo);
        warehouseEntryInfoQueryDTO.setResult(result);
        return warehouseEntryInfoQueryDTO;
    }

    public void updateBoardOnlineAndBarSubmit(WarehouseEntryInfoQueryDTO dto, WarehouseEntryInfoDTO warehouseEntryDTO) throws Exception {
        //单据号回写spm的bar_submit表，类型为“2”，并修改lms的board_online表的状态为正常入库
        Map<String, Object> mapBarSubmit = new HashMap<>();
        mapBarSubmit.put("prodplanId", warehouseEntryDTO.getProdplanId());
        mapBarSubmit.put("billNo", warehouseEntryDTO.getBillNo());
        List<BarSubmitDTO> barSubmitDTOList = datawbRemoteService.getBarSubmitBatch(mapBarSubmit);
        if (!CollectionUtils.isEmpty(barSubmitDTOList)) {
            BarSubmitDTO writeBackDTO = barSubmitDTOList.get(NUM_ZERO);
            writeBackDTO.setType(new Short(STR_2));
            datawbRemoteService.updateBarSubmitWithType(writeBackDTO);
        }
        List<BoardOnline> boardOnlineList = new ArrayList<>();
        List<PsWipInfo> psWipInfos = dto.getPsWipInfoList();
        if (CollectionUtils.isEmpty(psWipInfos)) {
            throw new Exception(CommonUtils.getLmbMessage(MessageId.SN_NOT_FOUND));
        }
        for (PsWipInfo psWipInfo : psWipInfos) {
            String sn = psWipInfo.getSn();
            sn = sn.substring(sn.length() - 5);
            Integer boardSn = Integer.parseInt(sn);

            BoardOnline boardOnline = new BoardOnline();
            boardOnline.setProdplanId(new BigDecimal(psWipInfo.getAttribute1()));
            boardOnline.setBoardSn(new BigDecimal(boardSn));
            boardOnline.setIsRepair(new BigDecimal(STR_0));
            boardOnlineList.add(boardOnline);
        }
        datawbRemoteService.updateBoardOnlineBatch(boardOnlineList);
    }

    @Override
    public boolean isSubcardK2(Map<String, Object> result, WarehouseEntryInfo warehouseEntryInfo, boolean isSubCard) {
        //是否整机子卡
        boolean isBomSubCardFlag = false;
        //类型是子卡时，进一步判断是否整机子卡，并设置字段
        isBomSubCardFlag = isBomSubCard(result, warehouseEntryInfo, isSubCard);
        //自动入库开关是否关闭
        boolean isClose = this.checkBomSubCardClose();
        boolean isSubcardK2 = isSubCard && isBomSubCardFlag && isClose;
        return isSubcardK2;
    }


    public Map<String, Object> doK2OrNotK2(boolean isK2, WarehouseEntryInfoQueryDTO dto, Map<String, Object> result, WarehouseEntryInfo warehouseEntryInfo) throws Exception {
        if (isK2) {
            return goK2AndSaveWarehouse(dto, result, warehouseEntryInfo);
        }
        return goNotK2AndSaveWarehouse(dto, result, warehouseEntryInfo);
    }

    private Map<String, Object> goNotK2AndSaveWarehouse(WarehouseEntryInfoQueryDTO dto, Map<String, Object> result, WarehouseEntryInfo warehouseEntryInfo) throws Exception {
        /**入库单类型修改为子卡入库单的类型**/
        warehouseEntryInfo = this.fixSubcard(warehouseEntryInfo, dto.getFacId());
        // 插入
        WarehouseEntryInfoQueryDTO entryInfoQueryDTO = this.getWarehouseEntryInfoQueryDTO(dto.getFacId(), dto.getEntyId(), dto.getEmNo(), result,dto.getAppNo());
        result = this.insertWarehouse(dto, warehouseEntryInfo, entryInfoQueryDTO);
        this.setParentProdplanNo(warehouseEntryInfo, dto);
        List<Map<String, String>> boards = new ArrayList<>(1);
        if (CollectionUtils.isEmpty(dto.getPsWipInfoList())) {
            throw new Exception(CommonUtils.getLmbMessage(MessageId.SN_NOT_FOUND));
        }
        this.packBoardData(boards, dto, warehouseEntryInfo);
        // 更新IMU为42
        updateImuForSubCard(boards);
        return result;
    }

    public Map<String, Object> goK2AndSaveWarehouse(WarehouseEntryInfoQueryDTO dto, Map<String, Object> result, WarehouseEntryInfo warehouseEntryInfo) throws Exception {
        /**入库单类型修改为子卡入库单的类型**/
        warehouseEntryInfo.setK2Flag(true);
        warehouseEntryInfo = this.fixSubcard(warehouseEntryInfo, dto.getFacId());
        WarehouseEntryInfoDTO warehouseEntryDTO = WarehouseEntryInfoAssembler.toDTO(warehouseEntryInfo);
        dto.setK2Flag(true);
        String applyNo = warehouseEntryInfoWritebackStep(warehouseEntryDTO);
        WarehouseEntryInfoQueryDTO warehouseEntryInfoQueryDTO = this.getWarehouseEntryInfoQueryDTO(dto.getFacId(), dto.getEntyId(), dto.getEmNo(), result, applyNo);
        result = insertWarehouse(dto, warehouseEntryInfoQueryDTO, warehouseEntryInfo);
        return result;
    }


    /**
     * 是否整机子卡，并设置打印标识
     *
     * @param result
     * @param warehouseEntryInfo
     * @param isSubCard
     * @return
     */
    public boolean isBomSubCard(Map<String, Object> result, WarehouseEntryInfo warehouseEntryInfo, boolean isSubCard) {
        boolean isBomSubCard = false;
        if (isSubCard) {
            isBomSubCard = this.checkBomSubCardNew(warehouseEntryInfo);
            warehouseEntryInfo.setZjSubcardFlag(isBomSubCard ? Y : N);
            result.put(IS_ZJ_SUBCARD, warehouseEntryInfo.getZjSubcardFlag());
            result.put(PRINT_FLAG, checkPrintOpen());
        }
        return isBomSubCard;
    }

    /**
     * 获取整机子卡提交入库数据字典：是否关闭
     * 是N时返回true,其他返回false
     *
     * @return
     */
    public boolean checkBomSubCardClose() {
        try {
            Map<String, Object> map = new HashMap<>();
            map.put(BusinessConstant.LOOK_UP_TYPE, ZJ_LOOKUP_TYPE);
            List<SysLookupTypesDTO> sysLookUpList = BasicsettingRemoteService.getSysLookUpValue(map);
            if (!CollectionUtils.isEmpty(sysLookUpList)
                    && N.equals(sysLookUpList.get(NumConstant.NUM_ZERO).getLookupMeaning())) {
                return true;
            }
            return false;
        } catch (Exception e) {
        }
        return false;
    }

    public String checkPrintOpen() {
        try {
            Map<String, Object> map = new HashMap<>();
            map.put(BusinessConstant.LOOK_UP_TYPE, ZJ_OPEN_TYPE);
            List<SysLookupTypesDTO> sysLookUpList = BasicsettingRemoteService.getSysLookUpValue(map);
            if (CollectionUtils.isEmpty(sysLookUpList)) {
                return "";
            }
            return sysLookUpList.get(NumConstant.NUM_ZERO).getLookupMeaning();
        } catch (Exception e) {
        }
        return "";
    }


    public boolean checkBomSubCardNew(WarehouseEntryInfo warehouseEntryInfo) {
        return Y.equals(getZjFlag(warehouseEntryInfo));
    }

    /**
     * 子卡更新IMU为42
     */
    private void updateImuForSubCard(List<Map<String, String>> boards) throws Exception {
        JsonNode jsonNode = DatawbRemoteService.updateImuForSubCard(boards);
        if (null == jsonNode || null == jsonNode.get(MpConstant.JSON_CODE)) {
            throw new NullPointerException();
        }
        String retCode = jsonNode.get(MpConstant.JSON_CODE).get(MpConstant.JSON_CODE).asText();
        if (!RetCode.SUCCESS_CODE.equalsIgnoreCase(retCode)) {
            String bo = JSONObject.toJSONString(jsonNode.get(MpConstant.JSON_BO));
            throw new Exception(bo);
        }
    }

    /**
     * 子卡更新IMU为613
     */
    public void updateImuForSubCardTo613(List<Map<String, String>> boards) throws Exception {
        JsonNode jsonNode = DatawbRemoteService.updateImuForSubCardTo613(boards);
        if (null == jsonNode || null == jsonNode.get(MpConstant.JSON_CODE)) {
            throw new NullPointerException();
        }
        String retCode = jsonNode.get(MpConstant.JSON_CODE).get(MpConstant.JSON_CODE).asText();
        if (!RetCode.SUCCESS_CODE.equalsIgnoreCase(retCode)) {
            String bo = JSONObject.toJSONString(jsonNode.get(MpConstant.JSON_BO));
            throw new Exception(bo);
        }
    }

    //更新该批次的首件入库日期
    private void updateFirstWarehouseDate(WarehouseEntryInfoQueryDTO dto, WarehouseEntryInfo warehouseEntryInfo) throws Exception {
        SysLookupTypesDTO sysLookupTypesDTO = BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_6031_TYPE, Constant.LOOKUP_TYPE_6031001_TYPE);
        if (sysLookupTypesDTO == null || !StringUtils.equals(sysLookupTypesDTO.getLookupMeaning(), Constant.FLAG_Y)) {
            return;
        }
        // 20210113 由判断入库单类型= 单板入库单 改成 判断任务是否单板任务
        if (StringUtils.equals(dto.getSourceSys(), Constant.SYS_TYPE_STEP)) {
            Map<String, Object> record = new HashMap<>();
            record.put("attribute1", warehouseEntryInfo.getProdplanId());
            record.put("craftSection", Constant.WAREHOUSE_ENTRY);
            long count = psWipInfoServiceImp.getCount(record);
            if (count < NUM_ONE) {
                Map<String, Object> params = new HashMap<>();
                params.put("updateFirstWarehouseDate", Constant.FLAG_Y);
                params.put("taskNo", warehouseEntryInfo.getTaskNo());
                params.put("prodplanId", warehouseEntryInfo.getProdplanId());
                PlanscheduleRemoteService.updatePsTaskInfoSelective(params);
            }
        }
    }

    private Map<String, Object> insertWarehouse(WarehouseEntryInfoQueryDTO dto, WarehouseEntryInfoQueryDTO warehouseEntryInfoQueryDTO, WarehouseEntryInfo warehouseEntryInfo) throws Exception {
        Map<String, Object> result = warehouseEntryInfoQueryDTO.getResult();
        String applyNo = warehouseEntryInfoQueryDTO.getAppNo();
        try {
            warehouseEntryInfo.setApplyNo(applyNo);
            // 插入
            result = this.insertWarehouse(dto, warehouseEntryInfo, warehouseEntryInfoQueryDTO);
            List<Map<String, String>> boards = new ArrayList<>(1);
            if (CollectionUtils.isEmpty(dto.getPsWipInfoList())) {
                throw new Exception(CommonUtils.getLmbMessage(MessageId.SN_NOT_FOUND));
            }
            this.packBoardData(boards, dto, warehouseEntryInfo);

            updateImuBySn(boards);
        } catch (Exception e) {
            // 回退补偿
            this.warehouseEntryInfoWritebackStepRollBack(applyNo);
            // 抛出异常rollback
            throw e;
        }
        return result;
    }

    /**
     * 设置子卡入库单批次所对应父批次。
     *
     * @param warehouseEntryInfo
     * @param dto
     * @throws MesBusinessException
     */
    private void setParentProdplanNo(WarehouseEntryInfo warehouseEntryInfo, WarehouseEntryInfoQueryDTO dto) throws MesBusinessException {
        List<PsTask> taskList = this.getPsTaskByProdplanId(dto.getProdplanId());
        if (CollectionUtils.isEmpty(taskList)) {
            String[] params = {dto.getProdplanId()};
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PARENT_TASK_OF_PRODPLANNO_NOT_EXIST, params);
        }
        PsTask psTask = taskList.get(NUM_ZERO);
        if (psTask != null) {
            String parentProdplanNo = psTask.getZbjprodplanNo();
            if (StringUtils.isBlank(parentProdplanNo)) {
                String[] params = {dto.getProdplanId()};
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PARENT_TASK_OF_PRODPLANNO_NOT_EXIST, params);
            }
            warehouseEntryInfo.setParentProdplanNo(parentProdplanNo);
        }
    }

    /**
     * 组装需要更新的单板数据
     *
     * @param boards
     * @param dto
     */
    private void packBoardData(List<Map<String, String>> boards, WarehouseEntryInfoQueryDTO dto, WarehouseEntryInfo warehouseEntryInfo) {
        if (null == dto || null == dto.getPsWipInfoList() || warehouseEntryInfo == null) {
            return;
        }
        for (PsWipInfo pw : dto.getPsWipInfoList()) {
            Map<String, String> board = new HashMap<>(4);
            String sn = pw.getSn();
            sn = sn.substring(sn.length() - 5);
            Integer boardSn = Integer.parseInt(sn);
            board.put("boardSn", boardSn.toString());
            if (warehouseEntryInfo != null) {
                board.put("cardNo", warehouseEntryInfo.getLastUpdatedBy());
                board.put("prodplanId", warehouseEntryInfo.getProdplanId());
                board.put("prodplanNo", warehouseEntryInfo.getProdplanNo());
            }
            boards.add(board);
        }
    }

    /**
     * 组装需要更新的单板数据
     *
     * @param boards
     * @param boards
     */
    private void packBoardData(List<Map<String, String>> boards, WarehouseEntryInfo warehouseEntryInfo) {
        if (warehouseEntryInfo == null || null == warehouseEntryInfo.getSnList()) {
            return;
        }
        for (PsWipInfoDTO pw : warehouseEntryInfo.getSnList()) {
            Map<String, String> board = new HashMap<>(4);
            String sn = pw.getSn();
            sn = sn.substring(sn.length() - 5);
            Integer boardSn = Integer.parseInt(sn);
            board.put("boardSn", boardSn.toString());
            if (warehouseEntryInfo != null) {
                board.put("cardNo", warehouseEntryInfo.getLastUpdatedBy());
                board.put("prodplanId", warehouseEntryInfo.getProdplanId());
                board.put("prodplanNo", warehouseEntryInfo.getProdplanNo());
            }
            boards.add(board);
        }
    }

    private void isRepairOrSmall(WarehouseEntryInfoQueryDTO dto, WarehouseEntryInfo warehouseEntryInfo) {
        if (dto.isRepair()) {
            warehouseEntryInfo.setIsRepair(Constant.STR_NUMBER_ONE);
        } else {
            warehouseEntryInfo.setIsRepair(Constant.STR_NUMBER_ZERO);
        }
        if (dto.isSmall()) {
            warehouseEntryInfo.setIsSmall(Constant.STR_NUMBER_ONE);
        } else {
            warehouseEntryInfo.setIsSmall(Constant.STR_NUMBER_ZERO);
        }
    }

    /**
     * 回写erp-子卡扫描专用接口
     */
    public String writeErpTransactionNew(WarehouseEntryInfo warehouseEntryInfo) throws Exception {
        JsonNode jsonNode = DatawbRemoteService.writeErpTransactionNew(warehouseEntryInfo);
        if (null == jsonNode) {
            return Constant.STR_EMPTY;
        }
        String retCode = jsonNode.get(MpConstant.JSON_CODE).get(MpConstant.JSON_CODE).asText();
        String bo = jsonNode.get(MpConstant.JSON_BO).toString();
        if (!RetCode.SUCCESS_CODE.equalsIgnoreCase(retCode)) {
            throw new Exception(bo);
        }
        return bo;
    }

    /**
     * 单板写ERP 入库表
     *
     * @param warehouseEntryInfo
     * @throws Exception
     */
    public void onlyWriteErp(WarehouseEntryInfo warehouseEntryInfo) throws Exception {
        BigDecimal commitQty = warehouseEntryInfo.getCommitedQty();
        if (!CollectionUtils.isEmpty(warehouseEntryInfo.getSnList())) {
            // 校验条码状态
            this.checkSnStatus(warehouseEntryInfo);
            warehouseEntryInfo.setCheckSnStatus(true);
            warehouseEntryInfo.setCommitedQty(new BigDecimal(warehouseEntryInfo.getSnList().size()));
        }
        JsonNode jsonNode = DatawbRemoteService.onlyWriteErp(warehouseEntryInfo);
        if (null == jsonNode) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.ERP_WAREHOUSING_FAILED, new String[]{});
        }
        JSONObject retJson = JSON.parseObject(jsonNode.toString());
        this.checkErpResult(retJson);
        warehouseEntryInfo.setErpErrorInfo("");
        //接收按钮 或者 扫描接收 全部接收了
        if (CollectionUtils.isEmpty(warehouseEntryInfo.getSnList()) || isBillAllReceive(warehouseEntryInfo)) {
            warehouseEntryInfo.setStatus(Constant.ERP_DONE_STATUS);
        }
        warehouseEntryInfo.setCommitedQty(commitQty);
        // 更新erp调用信息
        updateWarehouseEntryInfoById(warehouseEntryInfo);
        updateWarehouseStatus(warehouseEntryInfo);
    }

    public void checkErpResult(JSONObject retJson) throws MesBusinessException {
        JSONObject retCode = retJson.getJSONObject(MpConstant.JSON_CODE);
        if (retCode == null) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.ERP_WAREHOUSING_FAILED, new String[]{});
        }
        if (!RetCode.SUCCESS_CODE.equalsIgnoreCase(retCode.getString(MpConstant.JSON_CODE))) {
            String[] msg = {Constant.COMMA + retCode.getString(MpConstant.JSON_MSG)};
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.ERP_WAREHOUSING_FAILED, msg);
        }
    }

    private WarehouseEntryInfo fixSubcard(WarehouseEntryInfo warehouseEntryInfo, String factoryId) throws Exception {
        /**入库单类型修改为子卡入库单的类型**/
        Map<String, Object> mapLookTypes = new HashMap<>();
        mapLookTypes.put("lookupType", Constant.BILL_TYPE_LOOKUP_TYPE);
        String billType = MpConstant.BILL_TYPE_FOUR;
        List<SysLookupTypesDTO> lookupTypes = BasicsettingRemoteService.getSysLookUpValue(mapLookTypes);
        for (SysLookupTypesDTO sysLookupTypesDTO : lookupTypes) {
            if (Constant.SUB_CARD_TYPE_CONTENT.equals(sysLookupTypesDTO.getDescriptionChinV())) {
                billType = sysLookupTypesDTO.getLookupMeaning();
            }
        }
        warehouseEntryInfo.setBillType(billType);

        //整机子卡入K2库，不需要设置子库存
        if (!warehouseEntryInfo.isK2Flag()) {
            // 子库存根据orgId匹配数据字典2222，获取工厂后查询对应工厂的子库存
            String erpCode = getErpCode(warehouseEntryInfo);
            warehouseEntryInfo.setSubStock(erpCode);
        }
        warehouseEntryInfo.setErpMoveStatus(Constant.ERP_DONE_STATUS);
        warehouseEntryInfo.setErpDoneStatus(Constant.ERP_DONE_STATUS);
        return warehouseEntryInfo;
    }

    public String getErpCode(WarehouseEntryInfo warehouseEntryInfo) throws Exception {
        String orgId = warehouseEntryInfo.getOrgId().toString();
        Map<String, Object> subStockLookTypes = new HashMap<>();
        subStockLookTypes.put("lookupType", BusinessConstant.ORG_ID_LOOKUP_TYPE);
        List<SysLookupTypesDTO> subLookupTypes = BasicsettingRemoteService.getSysLookUpValue(subStockLookTypes);
        String subFactoryId = STR_EMPTY;
        for (SysLookupTypesDTO sysLookupTypesDTO : subLookupTypes) {
            if (orgId.equals(sysLookupTypesDTO.getLookupMeaning())) {
                subFactoryId = sysLookupTypesDTO.getAttribute1();
            }
        }
        List<CFFactory> subFactoryIdList = BasicsettingRemoteService.getFactoryByFactoryCode(subFactoryId);
        if (CollectionUtils.isEmpty(subFactoryIdList) || subFactoryIdList.get(NumConstant.NUM_ZERO).getErpCode() == null) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.FACTORY_INFO_NULL);
        }
        return subFactoryIdList.get(NumConstant.NUM_ZERO).getErpCode();
    }

    //获取整机子卡标识
    public String getZjFlag(WarehouseEntryInfo warehouseEntryInfo) {
        Map<String, String> map = new HashMap<>();
        map.put(PRODUCT_CODE, warehouseEntryInfo.getItemNo());
        JsonNode json = null;
        try {
            json = CenterfactoryRemoteService.getBBomHeaderList(map);
        } catch (Exception e) {
            throw new BusiException(RetCode.BUSINESSERROR_CODE, e.getMessage());
        }
        if (null == json) {
            throw new BusiException(RetCode.BUSINESSERROR_CODE, MessageId.CALL_SERVICE_FAILED);
        }
        return getStringFromJson(json);
    }

    public String getStringFromJson(JsonNode json) {
        JsonNode bo = json.get(MpConstant.JSON_BO);
        if (bo != null && bo.get(MpConstant.JSON_ROWS) != null) {
            JsonNode rows = bo.get(MpConstant.JSON_ROWS);
            List<BBomHeaderDTO> list = JSON.parseArray(rows.toString(), BBomHeaderDTO.class);
            if (list != null && list.size() > 0) {
                return list.get(0).getZjSubcardFlag();
            }
            return N;
        }
        return N;
    }

    private Map<String, Object> insertWarehouse(WarehouseEntryInfoQueryDTO dto, WarehouseEntryInfo warehouseEntryInfo,
                                                WarehouseEntryInfoQueryDTO warehouseEntryInfoQueryDTO) throws Exception {
        String factoryId = warehouseEntryInfoQueryDTO.getFacId();
        String entityId = warehouseEntryInfoQueryDTO.getEntyId();
        String empNo = warehouseEntryInfoQueryDTO.getEmNo();
        Map<String, Object> result = warehouseEntryInfoQueryDTO.getResult();
        String applyNo = warehouseEntryInfoQueryDTO.getAppNo();
        warehouseEntryInfo.setStatus(Constant.STR_0);
        int inserResult = insertWarehouseEntryInfo(warehouseEntryInfo);
        if (inserResult > 0) {

            // 保存  WarehouseEntryDetail
            StringBuilder sns = new StringBuilder();
            List<PsWipInfo> psWipInfoList = dto.getPsWipInfoList();
            List<WarehouseEntryDetailDTO> warehouseEntryDetailList = new ArrayList<>();

            for (int i = 0; i < psWipInfoList.size(); i++) {
                PsWipInfo psWinInfo = psWipInfoList.get(i);

                sns.append(Constant.SINGLE_QUOTE).append(psWinInfo.getSn())
                        .append(Constant.SINGLE_QUOTE).append(Constant.COMMA);

                WarehouseEntryDetailDTO warehouseEntryDetail = new WarehouseEntryDetailDTO();
                warehouseEntryDetail.setWarehouseEntryDetailId(UUID.randomUUID().toString());
                warehouseEntryDetail.setBillNo(warehouseEntryInfo.getBillNo());
                warehouseEntryDetail.setApplyNo(applyNo);
                warehouseEntryDetail.setSn(psWinInfo.getSn());
                warehouseEntryDetail.setWorkOrderNo(psWinInfo.getWorkOrderNo());
                warehouseEntryDetail.setProdplanId(warehouseEntryInfo.getProdplanId());
                warehouseEntryDetail.setCreateBy(empNo);
                warehouseEntryDetail.setLastUpdatedBy(empNo);
                warehouseEntryDetail.setOrgId(dto.getOrgId());
                warehouseEntryDetail.setLpn(psWinInfo.getLpn());
                if (factoryId != null && !factoryId.isEmpty()) {
                    warehouseEntryDetail.setFactoryId(new BigDecimal(factoryId));
                }
                if (entityId != null && !entityId.isEmpty()) {
                    warehouseEntryDetail.setEntityId(new BigDecimal(entityId));
                }
                warehouseEntryDetailList.add(warehouseEntryDetail);
            }
            // 插入 WarehouseEntryDetail
            int insertDetailResult = insertWarehouseEntryDetail(warehouseEntryDetailList);
            this.updateOrDelete(dto, insertDetailResult, warehouseEntryInfo, result);
            //  2020/8/8 更新箱内容中条码的当前工序及工站
            updateContainer(dto, warehouseEntryDetailList);
        }

        return result;
    }

    //新增入库单详情 根据开关 防止重复提交
    public int insertWarehouseEntryDetail(List<WarehouseEntryDetailDTO> warehouseEntryDetailList) throws Exception {
        SysLookupTypesDTO sysLookupTypesDTO = BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_6667, Constant.LOOKUP_TYPE_6667001);
        String useSwitch = Constant.FLAG_N;
        if (sysLookupTypesDTO != null) {
            useSwitch = sysLookupTypesDTO.getLookupMeaning();
        }
        int insertDetailResult = NUM_ZERO;
        if (StringUtils.equals(Constant.FLAG_Y, useSwitch)) {
            insertDetailResult = insertWarehouseEntryDetailBatchNotExist(warehouseEntryDetailList);
            if (insertDetailResult != warehouseEntryDetailList.size()) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.ACTUAL_STOCK_QTY_INCONSISTENT_SUBMITTED_QUANTITY);
            }
        } else {
            insertDetailResult = insertWarehouseEntryDetailBatch(warehouseEntryDetailList);
        }
        return insertDetailResult;
    }

    //更新箱内容数据
    private void updateContainer(WarehouseEntryInfoQueryDTO dto, List<WarehouseEntryDetailDTO> warehouseEntryDetailList) throws Exception {
        SysLookupTypesDTO sysLookupTypesDTO = BasicsettingRemoteService.getSysLookUpValue(Constant.LOOK_UP_TYPE_STOCK_RCV_RIGHT, Constant.LOOK_UP_CODE_WAREHOUSE_UPDATE_CONTAINER);
        if (sysLookupTypesDTO != null && Constant.FLAG_N.equals(sysLookupTypesDTO.getLookupMeaning())) {
            return;
        }
        // 条码列表
        List<String> listSn = new ArrayList();
        for (WarehouseEntryDetailDTO detail : warehouseEntryDetailList) {
            listSn.add(detail.getSn());
        }
        // 根据明细包含的条码查询该单据下的在制信息
        List<PsWipInfo> listPsWipInfo = dto.getCurWipInfoList();
        // 批量更新箱内容表
        if (listPsWipInfo.size() <= 0) {
            return;
        }
        String routeId = CommonUtils.getRouteIdByWip(listPsWipInfo.get(NUM_ZERO));
        CtRouteDetail lastProcess = this.getLastProcess(routeId);
        if (lastProcess == null) {
            return;
        }
        // 更新箱内容数据
        ProductionDeliveryRemoteService.batchSaveForCcInfo(getSns(listPsWipInfo), lastProcess.getNextProcess(), MpConstant.STRING_ZERO);

    }

    //获取条码   返回值：  '77777770001','77777770002'
    public String getSns(List<PsWipInfo> listPsWipInfo) {
        String sns = "";
        for (PsWipInfo wipinfo : listPsWipInfo) {
            sns += Constant.SINGLE_QUOTE + wipinfo.getSn() + Constant.SINGLE_QUOTE + Constant.SYMBOL_COMMA;
        }
        return StringHelper.isEmpty(sns) ? "" : sns.substring(0, sns.length() - 1);
    }

    private void updateOrDelete(WarehouseEntryInfoQueryDTO dto, int insertDetailResult,
                                WarehouseEntryInfo warehouseEntryInfo, Map<String, Object> result) throws Exception {
        if (insertDetailResult > 0) {
            result.put("status", Constant.FLAG_Y);
            result.put("msg", CommonUtils.getLmbMessage(MessageId.IN_WAREHOUSE_SUCCEED));
            result.put("warehouseEntryInfo", warehouseEntryInfo);
            // 排除返修，因为返修入库，wipinfo无数据。
            if (dto.getBillTypeCode().intValue() != BILL_TYPE_LOOKUP_CODE_DBFX) {
                this.updatePsWipInfoToEntryProcessNew(warehouseEntryInfo, MpConstant.STRING_ZERO, dto);
            }
        } else {
            deleteWarehouseEntryInfoById(warehouseEntryInfo);
        }
    }

    /**
     * 入库提单 - 回写spm 开关
     *
     * @return
     * @throws IOException
     * @throws RouteException
     */
    private boolean isWriteBackStep() throws IOException, RouteException {
        List<SysLookupTypesDTO> lookupTypes = getWriteBackSPMKey();
        if (CollectionUtils.isEmpty(lookupTypes)) {
            return false;
        }

        for (SysLookupTypesDTO lookupType : lookupTypes) {
            if (lookupType.getLookupCode().intValue() == Constant.RK_WRITE_BACK_LOOKUP_CODE && lookupType.getLookupMeaning().equals(Constant.FLAG_Y)) {
                return true;
            }
        }
        return false;
    }


    //判断是否整机子卡
    private boolean checkSubCardNew(WarehouseEntryInfoQueryDTO dto) throws MesBusinessException {
        if (null != dto && Constant.BILL_TYPE_SON_CARD.equals(dto.getBillType())) {
            return true;
        }
        return false;
    }

    /**
     * 根据批次号获取psTask列表
     *
     * @param prodplanId
     * @return
     */
    private List<PsTask> getPsTaskByProdplanId(String prodplanId) {
        if (StringUtils.isBlank(prodplanId)) {
            return new ArrayList<PsTask>();
        }
        Map<String, Object> paraMap = new HashMap<>();
        paraMap.put("prodplanId", prodplanId);
        return PlanscheduleRemoteService.getPsTask(paraMap);
    }

    private List<SysLookupTypesDTO> getLookupTypes(Map<String, String> map) throws RouteException, IOException {
        String params = JacksonJsonConverUtil.beanToJson(map);
        Map<String, String> headerParamsMap = MESHttpHelper.getHttpRequestHeader();
        // 点对点调用服务
        String serviceName = MicroServiceNameEum.BASICSETTING;
        String version = MicroServiceNameEum.VERSION;
        String sendType = MicroServiceNameEum.SENDTYPEGET;
        String getUrl = "/BS/sysLookupTypesAll";

        String result = MicroServiceRestUtil.invokeService(serviceName, version, sendType, getUrl, params,
                headerParamsMap);
        JsonNode json = JacksonJsonConverUtil.getMapperInstance().readTree(result);

        String bo = json.get(MpConstant.JSON_BO).toString();
        List<SysLookupTypesDTO> types = JacksonJsonConverUtil.jsonToListBean(bo,
                new TypeReference<List<SysLookupTypesDTO>>() {
                });
        return types;
    }

    public List<MtlSecondaryInventories> getMtlSecondaryInventories(Map<String, String> map) throws RouteException, IOException {
        String params = JacksonJsonConverUtil.beanToJson(map);
        Map<String, String> headerParamsMap = MESHttpHelper.getHttpRequestHeader();
        // 点对点调用服务
        String serviceName = MicroServiceNameEum.BASICSETTING;
        String version = MicroServiceNameEum.VERSION;
        String sendType = MicroServiceNameEum.SENDTYPEGET;
        String getUrl = "/BS/mtlSecondaryInventories";

        String result = MicroServiceRestUtil.invokeService(serviceName, version, sendType, getUrl, params,
                headerParamsMap);
        JsonNode json = JacksonJsonConverUtil.getMapperInstance().readTree(result);

        String bo = json.get(MpConstant.JSON_BO).toString();
        List<MtlSecondaryInventories> entities = JacksonJsonConverUtil.jsonToListBean(bo,
                new TypeReference<List<MtlSecondaryInventories>>() {
                });
        return entities;
    }

    private List<SysLookupTypesDTO> getWriteBackSPMKey() throws RouteException, IOException {
        Map<String, String> map = new HashMap<>(1);
        map.put("lookupType", Constant.WRITE_BACK_FLAG);
        return getLookupTypes(map);
    }

    public List<ItemListEntityDTO> getItemListEntityListByTaskNo(String taskNo, BigDecimal orgId)
            throws RouteException, JsonProcessingException, IOException {

        Map<String, Object> map = new HashMap<>(2);
        map.put("taskNo", taskNo);
        map.put("page", NUM_ONE);
        map.put("rows", NumConstant.NUM_10000);
        if (orgId != null) {
            map.put("organizationId", orgId.toString());
        }
        String params = JacksonJsonConverUtil.beanToJson(map);

        Map<String, String> headerParamsMap = MESHttpHelper.getHttpRequestHeader();
        String url = ConstantInterface.getUrlStatic(InterfaceEnum.erpItemList);
        String result = HttpRemoteUtil.remoteExe(params, headerParamsMap, url, MicroServiceNameEum.SENDTYPEGET);
        JsonNode json = JacksonJsonConverUtil.getMapperInstance().readTree(result);
        String bo = json.get(MpConstant.JSON_BO).toString();
        Page<ItemListEntityDTO> pageList = (Page<ItemListEntityDTO>) JacksonJsonConverUtil.jsonToListBean(bo,
                new TypeReference<Page<ItemListEntityDTO>>() {
                });

        return pageList.getRows();
    }

    /**
     * 根据查询条件获取指令信息
     *
     * @param map
     * @return
     * @throws RouteException
     * @throws JsonProcessingException
     * @throws IOException
     */
    private List<PsWorkOrderBasic> getWorkorderBasicInfo(Map<String, String> map) throws RouteException, JsonProcessingException, IOException {

        String params = JacksonJsonConverUtil.beanToJson(map);
        Map<String, String> headerParamsMap = MESHttpHelper.getHttpRequestHeader();

        // 点对点调用服务
        String serviceName = MicroServiceNameEum.PLANSCHEDULE;
        String version = MicroServiceNameEum.VERSION;
        String sendType = MicroServiceNameEum.SENDTYPEGET;
        String getUrl = "/PS/psEntityPlanBasic";

        String getresult = MicroServiceRestUtil.invokeService(serviceName, version, sendType, getUrl, params,
                headerParamsMap);
        JsonNode json = JacksonJsonConverUtil.getMapperInstance().readTree(getresult);
        String bo = json.get(MpConstant.JSON_BO).toString();
        List<PsWorkOrderBasic> psWorkOrderBasicList = (List<PsWorkOrderBasic>) JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<ArrayList<PsWorkOrderBasic>>() {
        });

        return psWorkOrderBasicList;
    }

    /**
     * 获取route明细信息任务没有查询到对应的需求
     *
     * @param routeId     route头ID
     * @param currProcess 当前工序/工站
     * @param nextProcess 下工序/下工站
     * @param processCode 子工序
     * @return route明细信息
     * @throws RouteException
     * @throws JsonProcessingException
     * @throws IOException
     * <AUTHOR>
     */
    public List<CtRouteDetail> getCtRouteDetailInfo(String routeId, String currProcess, String nextProcess, String processCode)
            throws RouteException, JsonProcessingException, IOException {

        CtRouteDetailParamDTO obj = new CtRouteDetailParamDTO();
        obj.setRouteId(routeId);
        obj.setCurrProcess(currProcess);
        obj.setNextProcess(nextProcess);
        obj.setProcessCode(processCode);
        String params = JacksonJsonConverUtil.beanToJson(obj);
        Map<String, String> headerParamsMap = MESHttpHelper.getHttpRequestHeader();

        // 点对点调用服务
        String serviceName = MicroServiceNameEum.CRAFTTECH;
        String version = MicroServiceNameEum.VERSION;
        String sendType = MicroServiceNameEum.SENDTYPEGET;
        String getUrl = "/CT/CtRouteDetail";

        String getresult = MicroServiceRestUtil.invokeService(serviceName, version, sendType, getUrl, params,
                headerParamsMap);
        JsonNode json = JacksonJsonConverUtil.getMapperInstance().readTree(getresult);
        String bo = json.get(MpConstant.JSON_BO).toString();
        List<CtRouteDetail> listDetail = (List<CtRouteDetail>) JacksonJsonConverUtil.jsonToListBean(bo,
                new TypeReference<ArrayList<CtRouteDetail>>() {
                });

        return listDetail;
    }


    /**
     * 查询任务
     *
     * @param map
     * @return
     * @throws RouteException
     * @throws IOException
     */
    private List<PsTask> getPsTasks(Map<String, Object> map) throws RouteException, IOException {
        String params = JacksonJsonConverUtil.beanToJson(map);
        Map<String, String> headerParamsMap = MESHttpHelper.getHttpRequestHeader();
        // 点对点调用服务，获取该批次的任务信息
        String serviceName = MicroServiceNameEum.PLANSCHEDULE;
        String version = MicroServiceNameEum.VERSION;
        String sendType = MicroServiceNameEum.SENDTYPEGET;
        String getUrl = "/PS/psTask";

        String getresult = MicroServiceRestUtil.invokeService(serviceName, version, sendType, getUrl, params,
                headerParamsMap);
        JsonNode json = JacksonJsonConverUtil.getMapperInstance().readTree(getresult);
        String bo = json.get(MpConstant.JSON_BO).toString();
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<PsTask>>() {
        });
    }


    @Override
    public Long getSumCommitedQty(Map<String, Object> record) {

        return warehouseEntryInfoRepository.getSumCommitedQty(record);
    }

    @Override
    public Long getCommitedQtySum(Map<String, Object> record) {

        return warehouseEntryInfoRepository.getCommitedQtySum(record);
    }


    @Override
    public String createBillNo() {

        return warehouseEntryInfoRepository.createBillNo();
    }

    @Override
    public Long getLastProcessCount(Map<String, Object> record)
            throws RouteException, JsonProcessingException, IOException {

        // Map<String, String> headerParamsMap = new HashMap<String, String>();

        String params = JacksonJsonConverUtil.beanToJson(record);
        Map<String, String> headerParamsMap = MESHttpHelper.getHttpRequestHeader();
        // 点对点调用服务，获取批次下的指令号
        String serviceName = MicroServiceNameEum.PLANSCHEDULE;
        String version = MicroServiceNameEum.VERSION;
        String sendType = MicroServiceNameEum.SENDTYPEGET;
        String getUrl = "/PS/getWorkOrderInfo";

        String getresult = MicroServiceRestUtil.invokeService(serviceName, version, sendType, getUrl, params,
                headerParamsMap);
        JsonNode json = JacksonJsonConverUtil.getMapperInstance().readTree(getresult);
        String bo = json.get(MpConstant.JSON_BO).toString();
        List<PsWorkOrderDTO> list =
                JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<PsWorkOrderDTO>>() {

                });
        // 未查到指令号
        if (CollectionUtils.isEmpty(list)) {
            return (long) 0;
        }
        // 拼接指令号
        StringBuilder workorderNoList = new StringBuilder();
        String queryCondition = "";
        for (int i = 0; i < list.size(); i++) {
            String workOrderNo = list.get(i).getWorkOrderNo();
            if (!StringUtils.isEmpty(workOrderNo)) {
                workorderNoList.append(Constant.SINGLE_QUOTE).append(workOrderNo)
                        .append(Constant.SINGLE_QUOTE).append(Constant.COMMA);
            }

        }
        if (workorderNoList.length() > 0) {
            queryCondition = workorderNoList.substring(0, workorderNoList.length() - 1);
        }
        if (queryCondition.length() == 0) {
            return (long) 0;
        }
        // 根据拼接的指令号查询最后工序数量
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("queryCondition", queryCondition);
        Long lastProcessCount = warehouseEntryInfoRepository.getLastProecssCount(map);
        if (lastProcessCount == null) {
            lastProcessCount = (long) 0;
        }
        return lastProcessCount;

    }

    /**
     * 更新SPM单板IMU的值
     */
    @RecordLogAnnotation("更新SPM单板IMU的值")
    public void updateImuBySn(List<Map<String, String>> boards) throws Exception {
        //点对点调用 更新IMU值&修改日报表
        String params = JacksonJsonConverUtil.beanToJson(boards);
        Map<String, String> headerParamsMap = MESHttpHelper.getHttpRequestHeader();
        String url = ConstantInterface.getUrlStatic(InterfaceEnum.updateImuBySn);
        String result = HttpRemoteUtil.remoteExe(params, headerParamsMap, url, MicroServiceNameEum.SENDTYPEPOST);
        JsonNode json = JacksonJsonConverUtil.getMapperInstance().readTree(result);
        String code = json.get(MpConstant.JSON_CODE).get(MpConstant.JSON_CODE).textValue();
        if (!RetCode.SUCCESS_CODE.equalsIgnoreCase(code)) {
            throw new Exception(CommonUtils.getLmbMessage(MessageId.IMU_UPDATE_FAILED));
        }
    }

    /**
     * 补偿回滚warehouseEntryInfoWritebackStep
     *
     * @param applyNo
     * @throws JsonProcessingException
     * @throws JsonMappingException
     */
    public void warehouseEntryInfoWritebackStepRollBack(String applyNo) throws JsonMappingException, JsonProcessingException, Exception {
        if (StringUtils.isBlank(applyNo)) {
            return;
        }
        Map<String, String> headerParamsMap = MESHttpHelper.getHttpRequestHeader();
        Map map = new HashMap();
        map.put("applyNo", applyNo);
        String params = JacksonJsonConverUtil.beanToJson(map);
        String url = ConstantInterface.getUrlStatic(InterfaceEnum.barSubmitRollBack);
        String result = HttpRemoteUtil.remoteExe(params, headerParamsMap, url, MicroServiceNameEum.SENDTYPEGET);
        JacksonJsonConverUtil.getMapperInstance().readTree(result);
    }

    @Override
    public String warehouseEntryInfoWritebackStep(WarehouseEntryInfoDTO dto)
            throws Exception {

        // Map<String, String> headerParamsMap = new HashMap<String, String>();
        BarSubmitDTO barSubmit = new BarSubmitDTO();
        SimpleDateFormat sf = new SimpleDateFormat(MpConstant.DATE_FORMAT_YYYYMMDDHHMMSS_12);
        Date date = new Date();
        String strDate = sf.format(date);
        Date currDate = sf.parse(strDate);
        barSubmit.setBillNo(dto.getBillNo());
        barSubmit.setProdplanId(Long.valueOf(dto.getProdplanId()));
        if (dto.getCommitedQty() != null) {
            barSubmit.setQty(dto.getCommitedQty().longValue());
        }
        barSubmit.setSubmitDate(currDate);
        barSubmit.setPoster(dto.getLastUpdatedBy());
        barSubmit.setStatus(Long.valueOf(dto.getStatus()));
        barSubmit.setStockedQty((long) 0);
        //类型:0为提交入库，1退回生产线
        barSubmit.setType(MpConstant.TYPE_ZERO);
        barSubmit.setIsSign(MpConstant.IS_SIGN_ZERO);
        barSubmit.setRemark(dto.getRemark());
        barSubmit.setIsRepair(dto.getIsRepair());
        barSubmit.setIsSmall(dto.getIsSmall());
        String params = JacksonJsonConverUtil.beanToJson(barSubmit);
        Map<String, String> headerParamsMap = MESHttpHelper.getHttpRequestHeader();

        String url = ConstantInterface.getUrlStatic(InterfaceEnum.WbBarSubmitAdd);
        String result = HttpRemoteUtil.remoteExe(params, headerParamsMap, url, MicroServiceNameEum.SENDTYPEPOST);
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(result);
        bo = bo.replaceAll("\"", "");
        return bo;

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertWarehouseEntryDetailBatch(List<WarehouseEntryDetailDTO> list) {
        int num = 0;
        if (!CollectionUtils.isEmpty(list)) {
            List<List<WarehouseEntryDetailDTO>> splitLists = CommonUtils.splitList(list, Constant.SPLITSIZE_HUNDRED);
            for (List<WarehouseEntryDetailDTO> oneList : splitLists) {
                num += warehouseEntryInfoRepository.insertWarehouseEntryDetailBatch(oneList);
            }
        }
        return num;
    }

    @Transactional(rollbackFor = Exception.class)
    public int insertWarehouseEntryDetailBatchNotExist(List<WarehouseEntryDetailDTO> list) {
        int num = 0;
        if (!CollectionUtils.isEmpty(list)) {
            List<List<WarehouseEntryDetailDTO>> splitLists = CommonUtils.splitList(list, Constant.SPLITSIZE_HUNDRED);
            for (List<WarehouseEntryDetailDTO> oneList : splitLists) {
                num += warehouseEntryInfoRepository.insertWarehouseEntryDetailBatchNotExist(oneList);
            }
        }
        return num;
    }

    @Override
    public List<WarehouseEntryInfoDetailDTO>
    selectWarehouseEntryInfoDetailByBillNo(Map<String, Object> record) {

        return warehouseEntryInfoRepository.selectWarehouseEntryInfoDetailByBillNo(record);
    }

    @Override
    public List<WarehouseEntryInfoDetailDTO>
    getPageWarehouseEntryInfoDetailByBillNo(Map<String, Object> record, Long page, Long rows) {

        List<WarehouseEntryInfoDetailDTO> list = null;
        record.put("startRow", (page - 1) * rows + 1);
        record.put("endRow", page * rows);
        list = warehouseEntryInfoRepository.getPageWarehouseEntryInfoDetailByBillNo(record);
        return list;
    }

    @Override
    public Long getCountWarehouseEntryInfoDetailByBillNo(Map<String, Object> record) {

        return warehouseEntryInfoRepository.getCountWarehouseEntryInfoDetailByBillNo(record);
    }

    @Override
    public Long getMaxbillNo(Map<String, Object> record) {
        return warehouseEntryInfoRepository.getMaxbillNo(record);
    }


    @Override
    public List<WarehouseEntryInfo> getWarehouseEntryInfoSum(WarehouseEntryInfo record) {
        return warehouseEntryInfoRepository.getWarehouseEntryInfoSum(record);
    }

    //设置条码信息
    private void setSnList(List<WarehouseEntryInfo> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for (WarehouseEntryInfo warehouseEntryInfo : list) {
            WarehouseEntryInfoDTO dto = new WarehouseEntryInfoDTO();
            dto.setBillNo(warehouseEntryInfo.getBillNo());
            List<WarehouseEntryInfoDTO> warehouseEntryInfoDTOS = warehouseEntryInfoRepository.selectWarehouseEntryDetailInfo(dto);

            // 条码列表
            List<String> listSn = new ArrayList();
            if (CollectionUtils.isEmpty(warehouseEntryInfoDTOS)) {
                continue;
            }
            warehouseEntryInfoDTOS.forEach(warehouseEntryInfoDTO -> listSn.add(warehouseEntryInfoDTO.getSn()));

            // 根据明细包含的条码查询该单据下的在制信息
            List<PsWipInfo> listPsWipInfos = psWipInfoService.getListByBatchSn(listSn);
            warehouseEntryInfo.setPsWipInfoList(listPsWipInfos);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateWarehouseEntryInfoBatch(List<WarehouseEntryInfo> list) throws Exception {
        if (CollectionUtils.isEmpty(list)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, RetCode.BUSINESSERROR_MSGID);
        }
        setSnList(list);
        SysLookupTypesDTO sysLookupTypesDTO = BasicsettingRemoteService.getSysLookUpValue(Constant.LOOK_UP_TYPE_STOCK_RCV_RIGHT, Constant.LOOK_UP_CODE_STOCK_REJECT_DATABACK);
        if (sysLookupTypesDTO != null && Constant.FLAG_Y.equals(sysLookupTypesDTO.getLookupMeaning())) {
            batchUpdateImuAndDelBill(list);
        }
        //批量更新入库头表
        int num = batchUpdateWarehouse(list);
        // 1. 入库单明细更新,单板返修入库拒收只需要更新入库单详表
        for (WarehouseEntryInfo warehouseEntryInfo : list) {
            // 外协入库单不需要更新明细
            if (STR_10.equals(warehouseEntryInfo.getBillType())) {
                continue;
            }
            updateWipInfo(warehouseEntryInfo);
        }
        return num;
    }

    //单板入库批量更新spm条码imu，并删除spm入库单
    public void batchUpdateImuAndDelBill(List<WarehouseEntryInfo> list) throws Exception {
        List<WarehouseEntryInfo> totalList = new ArrayList<>();
        List<WarehouseEntryInfo> fxList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(list)) {
            totalList = list.stream().filter(s -> MpConstant.BILL_TYPE_SINGLE.equals(s.getBillType()) || MpConstant.BILL_TYPE_FIVE.equals(s.getBillType())).collect(Collectors.toList());
            fxList = list.stream().filter(s -> MpConstant.BILL_TYPE_DBFX.equals(s.getBillType())).collect(Collectors.toList());// 单板返修入库单
            List<WarehouseEntryInfo> outsourcedWarehousingList = list.stream().filter(s -> MpConstant.BILL_TYPE_TEN.equals(s.getBillType())).collect(Collectors.toList());// 外协入库单
            if (!CollectionUtils.isEmpty(outsourcedWarehousingList)) {
                DatawbRemoteService.deleteByBillNoAndProdplanIdBatch(outsourcedWarehousingList);
            }
            if (!CollectionUtils.isEmpty(totalList)) {
                this.setImuId(totalList);
                this.packBoardOnlineDataList(totalList);
                List<List<WarehouseEntryInfo>> splitLists = CommonUtils.splitList(totalList, Constant.SPLITSIZE_HUNDRED);
                for (List<WarehouseEntryInfo> oneList : splitLists) {
                    DatawbRemoteService.updateImuAndDelBillBatch(oneList);
                }
            }
            // 单板返修入库不更新imu,更新is_repair为0
            if (!CollectionUtils.isEmpty(fxList)) {
                this.packUpdateIsRepairData(fxList);
                List<List<WarehouseEntryInfo>> splitFxLists = CommonUtils.splitList(fxList, Constant.SPLITSIZE_HUNDRED);
                for (List<WarehouseEntryInfo> tempList : splitFxLists) {
                    DatawbRemoteService.updateIsRepairAndDelBillBatch(tempList);
                }
            }
        }
    }

    public void packUpdateIsRepairData(List<WarehouseEntryInfo> fxList) {
        for (WarehouseEntryInfo warehouseEntryInfo : fxList) {
            List<BoardOnline> boardOnlineList = new ArrayList<>();
            if (warehouseEntryInfo == null || null == warehouseEntryInfo.getPsWipInfoList()) {
                continue;
            }
            for (PsWipInfo psWipInfo : warehouseEntryInfo.getPsWipInfoList()) {
                BoardOnline boardOnline = new BoardOnline();
                Map<String, String> board = new HashMap<>(4);
                String snUpdate = psWipInfo.getSn();
                snUpdate = snUpdate.substring(snUpdate.length() - 5);
                Integer boardSn = Integer.parseInt(snUpdate);
                boardOnline.setBoardSn(new BigDecimal(boardSn));
                boardOnline.setCardNo(warehouseEntryInfo.getLastUpdatedBy());
                boardOnline.setProdplanId(new BigDecimal(warehouseEntryInfo.getProdplanId()));
                boardOnlineList.add(boardOnline);
            }
            warehouseEntryInfo.setBoardOnlinelist(boardOnlineList);
        }
    }

    //设置imuId
    private void setImuId(List<WarehouseEntryInfo> list) throws MesBusinessException {
        for (WarehouseEntryInfo warehouseEntryInfo : list) {
            // 获取条码扫描历史表
            Map<String, Object> map = new HashMap<String, Object>();
            // 设置查询条件
            map.put("sn", warehouseEntryInfo.getPsWipInfoList().get(0).getSn());
            map.put("orderField", MpConstant.ORDER_FIELD);
            map.put("order", Constant.DESC);
            List<PsScanHistory> psScanHistories = psScanHistoryRepository.getList(map);
            if (psScanHistories == null || psScanHistories.isEmpty()) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.FAILED_TO_GET_SCAN_HISTORY);
            }
            PsScanHistory psScanHistory = psScanHistories.get(0);
            warehouseEntryInfo.setSourceImuId(psScanHistory.getSourceImu());
        }
    }

    /**
     * 组装boardOnline数据
     *
     * @param list
     */
    private void packBoardOnlineDataList(List<WarehouseEntryInfo> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for (WarehouseEntryInfo warehouseEntryInfo : list) {
            List<BoardOnline> boardOnlineList = new ArrayList<>();
            if (warehouseEntryInfo == null || null == warehouseEntryInfo.getPsWipInfoList()) {
                continue;
            }
            for (PsWipInfo pw : warehouseEntryInfo.getPsWipInfoList()) {
                BoardOnline boardOnline = new BoardOnline();
                Map<String, String> board = new HashMap<>(4);
                String sn = pw.getSn();
                sn = sn.substring(sn.length() - 5);
                Integer boardSn = Integer.parseInt(sn);
                boardOnline.setBoardSn(new BigDecimal(boardSn));
                if (warehouseEntryInfo != null) {
                    boardOnline.setCardNo(warehouseEntryInfo.getLastUpdatedBy());
                    boardOnline.setProdplanId(new BigDecimal(warehouseEntryInfo.getProdplanId()));
                    boardOnline.setProdplanNo(warehouseEntryInfo.getProdplanNo());
                }
                boardOnlineList.add(boardOnline);
            }
            warehouseEntryInfo.setBoardOnlinelist(boardOnlineList);
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public int closeWarehouseEntryInfoBatch(List<WarehouseEntryInfo> list) throws Exception {
        // 批量更新入库单状态
        List<RedisLock> redisLockList = new ArrayList<>();
        Boolean validateResult = validateWarehouseEntryInfoData(list, redisLockList);
        if (validateResult) {
            String[] params = {list.stream().map(entryInfo -> entryInfo.getBillNo()).collect(Collectors.joining(","))};
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.WAREHOUSEENTRYINFO_IS_LOCKED, params);
        }
        try {
            for (WarehouseEntryInfo warehouseEntryInfo : list) {
                Boolean result = warehouseEntryInfoRepository.validateWarehouseEntryInfoStatus(warehouseEntryInfo.getBillNo()
                        , null, Arrays.asList(String.valueOf(Constant.INT_0)));
                if (!result) {
                    String[] params = {warehouseEntryInfo.getBillNo()};
                    throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.CURRENT_STATUS_NOT_ALLOW_RECEIVE, params);
                }
            }
            // 回退條碼mui
            this.batchUpdateStatusBill(list);
            //批量更新入库头表
            int num = batchUpdateWarehouse(list);
            // 1. 入库单明细更新
            list.forEach(warehouseEntryInfo -> closeWarehouseEntryInfo(warehouseEntryInfo));
            return num;
        } catch (Exception e) {
            throw e;
        } finally {
            redisLockList.stream().forEach(redisLock -> redisLock.unlock());
        }
    }

    /**
     * 入库单关闭 回写imu
     *
     * @param list
     */
    private void batchUpdateStatusBill(List<WarehouseEntryInfo> list) throws Exception {
        SysLookupTypesDTO sysLookupTypesDTO = BasicsettingRemoteService
                .getSysLookUpValue(Constant.LOOK_UP_TYPE_STOCK_RCV_RIGHT, Constant.LOOK_UP_CODE_STOCK_CLOSE);
        if (sysLookupTypesDTO == null || !Constant.FLAG_Y.equals(sysLookupTypesDTO.getLookupMeaning())) {
            return;
        }
        List<WarehouseEntryInfo> informationList = list.stream()
                .filter(s -> MpConstant.BILL_TYPE_SINGLE.equals(s.getBillType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(informationList)) {
            return;
        }
        // 獲取已提交的條碼
        List<WarehouseEntryInfo> informationListNew = new LinkedList<>();
        for (WarehouseEntryInfo info : informationList) {
            List<PsWipInfo> psWipInfos = psWipInfoService.getWipInfoByBillNo(info.getBillNo(), CommonConst.WAREHOUSE_DATAIL_SATATUS_SUBMIT);
            if (CollectionUtils.isEmpty(psWipInfos)) {
                continue;
            }
            info.setPsWipInfoList(psWipInfos);
            informationListNew.add(info);
        }
        if (CollectionUtils.isEmpty(informationListNew)) {
            return;
        }
        this.setImuId(informationListNew);
        this.packBoardOnlineDataList(informationListNew);
        this.datawbRemoteService.updateImuStatusBatch(informationListNew);
    }

    //批量更新入库头表
    private int batchUpdateWarehouse(List<WarehouseEntryInfo> list) {
        int num = 0;
        if (!CollectionUtils.isEmpty(list)) {
            List<List<WarehouseEntryInfo>> splitLists = CommonUtils.splitList(list, Constant.SPLITSIZE_HUNDRED);
            for (List<WarehouseEntryInfo> oneList : splitLists) {
                num += warehouseEntryInfoRepository.updateWarehouseEntryInfoBatch(oneList);
                updateReqDetailStatusAndQty(oneList);
            }
        }
        return num;
    }


    /**
     * 通过线体代码获取线体具体信息
     *
     * @param lineCode 线体代码
     * @return CFLineDTO List
     * @throws RouteException
     * @throws IOException
     * <AUTHOR>
     */
    private Map<String, CFLine> getLineInfo(String lineCode) throws RouteException, IOException {

        Map<String, String> map = new HashMap<String, String>();
        if (StringHelper.isNotEmpty(lineCode)) {
            map.put("lineCode", lineCode);
        }
        String params = JacksonJsonConverUtil.beanToJson(map);
        Map<String, String> headerParamsMap = MESHttpHelper.getHttpRequestHeader();

        // 点对点调用服务
        String serviceName = MicroServiceNameEum.BASICSETTING;
        String version = MicroServiceNameEum.VERSION;
        String sendType = MicroServiceNameEum.SENDTYPEGET;
        String getUrl = "/CF/CFLine";

        String getresult = MicroServiceRestUtil.invokeService(serviceName, version, sendType, getUrl, params,
                headerParamsMap);
        JsonNode json = JacksonJsonConverUtil.getMapperInstance().readTree(getresult);
        String bo = json.get(MpConstant.JSON_BO).get(MpConstant.JSON_ROWS).toString();
        List<CFLine> listLine = (List<CFLine>) JacksonJsonConverUtil.jsonToListBean(bo,
                new TypeReference<ArrayList<CFLine>>() {
                });
        if (CollectionUtils.isEmpty(listLine)) {
            return new HashMap<>();
        }
        // list转map
        Map<String, CFLine> lineMap = listLine.stream()
                .collect(Collectors.toMap(CFLine::getLineCode, a -> a, (k1, k2) -> k1));

        return lineMap;
    }

    @Override
    public Map checkSn4WarehouseEntryInfo(Map<String, Object> record) throws RouteException, IOException {
        Map result = new HashMap();
        List<PsWipInfo> wipInfolist = psWipInfoService.getList(record);
        if (CollectionUtils.isEmpty(wipInfolist)) {
            result.put("isInWarehouse", "");
            result.put("time", "");
            result.put("line", "");
            result.put("errmsg", CommonUtils.getLmbMessage(MessageId.SN_NOT_EXIST));
            result.put("checkStatus", Constant.E);
            return result;
        } else if (!BusinessConstant.OUT_WAREHOUSE.equals(wipInfolist.get(NUM_ZERO).getCraftSection())) {
            result.put("isInWarehouse", "");
            result.put("time", "");
            result.put("line", "");
            result.put("errmsg", CommonUtils.getLmbMessage(MessageId.NOT_WAREHOUSE_OUT, record.get(BusinessConstant.SN) + ""));
            result.put("checkStatus", Constant.E);
            return result;
        } else if (BusinessConstant.OUT_WAREHOUSE.equals(wipInfolist.get(NUM_ZERO).getCraftSection())) {
            Map<String, CFLine> lineMap = this.getLineInfo(wipInfolist.get(NUM_ZERO).getLineCode());
            CFLine line = lineMap.get(wipInfolist.get(NUM_ZERO).getLineCode());

            result.put("isInWarehouse", MpConstant.IS_IN_WAREHOUSE_ONE);
            result.put("time", wipInfolist.get(NUM_ZERO).getLastUpdatedDate());
            result.put("line", null == line ? "" : line.getLineName());
            result.put("errmsg", "");
            result.put("checkStatus", Constant.S);
            return result;
        }
        return null;
    }

    /**
     * 批量拒收入库单
     * 更新入库单状态为 已拒收
     *
     * @param list
     * @return
     * @throws Exception
     * @autchor 6092002408
     * @date 2018-10-19
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void rejectWarehouseEntryInfoBatch(List<WarehouseEntryInfo> list) throws Exception {
        // 批量更新入库单状态
        List<RedisLock> redisLockList = new ArrayList<>();
        Boolean validateResult = validateWarehouseEntryInfoData(list, redisLockList);
        if (validateResult) {
            String[] params = {list.stream().map(entryInfo -> entryInfo.getBillNo()).collect(Collectors.joining(","))};
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.WAREHOUSEENTRYINFO_IS_LOCKED, params);
        }
        try {
            for (WarehouseEntryInfo warehouseEntryInfo : list) {
                Boolean result = warehouseEntryInfoRepository.validateWarehouseEntryInfoStatus(warehouseEntryInfo.getBillNo()
                        , null, Arrays.asList(String.valueOf(Constant.INT_0)));
                if (!result) {
                    String[] params = {warehouseEntryInfo.getBillNo()};
                    throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.CURRENT_STATUS_NOT_ALLOW_RECEIVE, params);
                }
            }

            // 校验erp移动和完工状态
            this.checkErpMoveAndDoneStatus(list);

            // 点击“拒收”按钮按钮后， 校验是SPM 入库单状态
            this.checkSpmStatus(list);

            // 点击“拒收”按钮确认拒收后，校验Lms中没有装箱信息
            this.checkLmsPackingInfo(list);

            this.checkIsPackBill(list);

            updateWarehouseEntryInfoBatch(list);
            //批量更新任务完成数量，逻辑更改为拒收不需要更改任务完成数量

//            Map<String, BigDecimal> prodplanIdAndQtyMap = new HashMap<>();
//            for (WarehouseEntryInfo warehouseEntryInfo : list) {
//                BigDecimal qty = prodplanIdAndQtyMap.get(warehouseEntryInfo.getProdplanId());
//                if (qty == null) {
//                    prodplanIdAndQtyMap.put(warehouseEntryInfo.getProdplanId(), BigDecimal.ZERO.subtract(warehouseEntryInfo.getCommitedQty()));
//                } else {
//                    prodplanIdAndQtyMap.put(warehouseEntryInfo.getProdplanId(), qty.subtract(warehouseEntryInfo.getCommitedQty()));
//                }
//            }
//            PlanscheduleRemoteService.batchHandleCompleteQty(prodplanIdAndQtyMap);
        } finally {
            redisLockList.stream().forEach(redisLock -> redisLock.unlock());
        }
    }

    /**
     * 校验单据erp移动和完工是否存在未知异常
     * @param list
     */
    private void checkErpMoveAndDoneStatus(List<WarehouseEntryInfo> list) {
        if (!checkErpStatus) {
            return;
        }
        List<String> billNoList = list.stream().map(WarehouseEntryInfo::getBillNo).distinct().collect(Collectors.toList());
        List<String> erpMoveOrDoneErrorList = warehouseEntryInfoRepository.getErpMoveOrDoneErrorList(billNoList);
        if (!CollectionUtils.isEmpty(erpMoveOrDoneErrorList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.ERP_MOVE_OR_DONE_ERROR, new Object[]{erpMoveOrDoneErrorList.toString()});
        }
    }

    /**
     *<AUTHOR>
     * 校验是否infor单据
     *@Date 2024/7/17 15:45
     *@Param [java.util.List<com.zte.domain.model.WarehouseEntryInfo>]
     *@return
     **/
    private void checkIsPackBill(List<WarehouseEntryInfo> list) throws Exception{
        List<String> billNoList = list.stream().map(WarehouseEntryInfo::getBillNo).collect(Collectors.toList());
        List<String> existList = warehouseEntryInfoRepository.getExistInforBillList(billNoList);
        if (!CollectionUtils.isEmpty(existList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.INFOR_BILL_PACKING_EXCEPTION, new Object[]{existList.toString()});
        }
    }

    /**
     * 单据对应仓库类型为K2库且spm状态为已提交状态，需获取单据在lms中是否已存在装箱信息
     * 如果已存在装箱信息不允许进行拒收操作（ KXBARIII.BAR_SUBMIT serial_id  关联 LMS.  apply_id 能查到数据则说明已产生装箱信息）
     *
     * @param list Constant.STOCK_TYPE_K2
     */
    public void checkLmsPackingInfo(List<WarehouseEntryInfo> list) throws Exception {
        // 筛选仓库类型是K2库
        List<String> billNoList = list.stream()
                .filter(item -> STOCK_TYPE_K2.equals(item.getStockType()) || STR_10.equals(item.getBillType()))
                .filter(item -> StringUtils.isNotBlank(item.getBillNo()))
                .map(WarehouseEntryInfo::getBillNo)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(billNoList)) {
            return;
        }
        // 查询单据在spm中serial_id信息
        List<BarSubmitDTO> barSubmitDTOS = datawbRemoteService.queryBarSubmitInfo(billNoList);
        if (CollectionUtils.isEmpty(barSubmitDTOS)) {
            return;
        }
        // 筛选spm中serialId不为空的单据信息
        List<Long> serialIdList = barSubmitDTOS.stream()
                .filter(item -> StringHelper.isNotEmpty(item.getSerialId()))
                .map(BarSubmitDTO::getSerialId)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(serialIdList)) {
            return;
        }
        // 根据序列号查询lms中的装箱信息
        List<SemisBoxInfoDTO> semisBoxInfoDTOS = datawbRemoteService.querySemisBoxInfo(serialIdList);
        if (CollectionUtils.isEmpty(semisBoxInfoDTOS)) {
            return;
        }
        List<String> errorBillNo = new ArrayList<>();
        // 遍历Lms装箱信息，筛选已有装箱信息的单据号，不允许进行拒收操作
        for (SemisBoxInfoDTO semisBoxInfoDTO : semisBoxInfoDTOS) {
            if (StringHelper.isNotEmpty(semisBoxInfoDTO.getApplyId())) {
                BarSubmitDTO barSubmitDTO = barSubmitDTOS.stream()
                        .filter(item -> StringUtils.equals(semisBoxInfoDTO.getApplyId().toString(), item.getSerialId().toString()))
                        .findFirst().orElse(null);
                if (barSubmitDTO == null) {
                    continue;
                }
                errorBillNo.add(barSubmitDTO.getBillNo());
            }
        }
        if (CollectionUtils.isEmpty(errorBillNo)) {
            return;
        }
        // 对拒收的单据号去重抛出异常
        errorBillNo = errorBillNo.stream().distinct().collect(Collectors.toList());
        throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.LMS_BILL_PACKING_EXCEPTION,
                new Object[]{errorBillNo});
    }

    /**
     * 单据对应仓库类型为K2库时，需获取单据在spm的状态，或者为外协入库单
     * 如果状态为“已确认”时不允许进行拒收操作（bar_submit  status  0为提交，否则为确认）
     *
     * @param list Constant.STOCK_TYPE_K2
     */
    public void checkSpmStatus(List<WarehouseEntryInfo> list) throws Exception {
        List<String> billNoList = list.stream()
                .filter(item -> STOCK_TYPE_K2.equals(item.getStockType()) || STR_10.equals(item.getBillType()))
                .filter(item -> StringUtils.isNotBlank(item.getBillNo()))
                .map(WarehouseEntryInfo::getBillNo)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(billNoList)) {
            return;
        }
        // 回写 服务 barSubmit 服务
        List<BarSubmitDTO> barSubmitDTOS = datawbRemoteService.queryBarSubmitInfo(billNoList);
        if (CollectionUtils.isEmpty(barSubmitDTOS)) {
            return;
        }
        List<String> errorBillNo = barSubmitDTOS.stream()
                .filter(item -> StringHelper.isNotEmpty(item.getStatus()))
                .filter(item -> INT_0 != item.getStatus().intValue())
                .map(BarSubmitDTO::getBillNo)
                .distinct()
                .collect(Collectors.toList());
        List<String> collect = barSubmitDTOS.stream()
                .filter(item -> StringHelper.isEmpty(item))
                .map(BarSubmitDTO::getBillNo)
                .distinct()
                .collect(Collectors.toList());
        errorBillNo.addAll(collect);
        if (CollectionUtils.isEmpty(errorBillNo)) {
            return;
        }
        throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SPM_BILL_STATUS_EXCEPTION,
                new Object[]{errorBillNo});
    }

    public void updateWipInfo(WarehouseEntryInfo warehouseEntryInfo) throws Exception {
        // 单板返修入库和转正入库单拒收只需要更新入库单详表
        if (this.notFxAndZzType(warehouseEntryInfo.getBillType())) {
            // 根据明细包含的条码查询该单据下的在制信息
            List<PsWipInfo> listPsWipInfos = warehouseEntryInfo.getPsWipInfoList();
            List<PsScanHistory> insertWipScanHistorys = new ArrayList<>(listPsWipInfos.size());
            // 如果是单板报废走单独的逻辑更新条码子工序为P0007
            if (MpConstant.BILL_TYPE_FIVE.equals(warehouseEntryInfo.getBillType())) {
                dealScrapBillSn(warehouseEntryInfo, listPsWipInfos, insertWipScanHistorys);
                return;
            }
            // 获取入库前最后子工序--next_process = 'N'
            String processForQuery = this.getProcessForQuery(warehouseEntryInfo);
            // 获取条码扫描历史表
            Map<String, Object> map = new HashMap<String, Object>();
            // 设置查询条件--增加入库前子工序作为查询条件
            map.put("sn", listPsWipInfos.get(0).getSn());
            if (processForQuery.contains(Constant.COMMA)) {
                List<String> processCodeList = Arrays.asList(processForQuery.split(Constant.COMMA));
                map.put("processCodeList", processCodeList);
            } else {
                map.put("currProcessCode", processForQuery);
            }
            map.put("orderField", MpConstant.ORDER_FIELD);
            map.put("order", Constant.DESC);
            List<PsScanHistory> psScanHistories = psScanHistoryRepository.getList(map);
            if (psScanHistories == null || psScanHistories.isEmpty()) {
                return;
            }
            // 筛选工站不为空的数据，保证不取到中试上传的数据
            List<PsScanHistory> psScanHistoriesFilter = psScanHistories.stream().filter(s -> StringHelper.isNotEmpty(s.getSourceSysName())).collect(Collectors.toList());
            PsScanHistory psScanHistory = psScanHistoriesFilter.get(0);
            listPsWipInfos.forEach(psWipInfo -> {
                psWipInfo.setLastUpdatedDate(new Date());
                psWipInfo.setWorkOrderNo(psScanHistory.getWorkOrderNo());
                psWipInfo.setWorkStation(psScanHistory.getWorkStation());
                psWipInfo.setNextProcess(psScanHistory.getNextProcess());
                psWipInfo.setLastProcess(psScanHistory.getLastProcess());
                psWipInfo.setCraftSection(psScanHistory.getCraftSection());
                psWipInfo.setCurrProcessCode(psScanHistory.getCurrProcessCode());
                psWipInfo.setLastUpdatedBy(warehouseEntryInfo.getLastUpdatedBy());
                // 获取待保存的扫描历史信息
                PsScanHistory scanHistoryTemp = new PsScanHistory();
                BeanUtils.copyProperties(psWipInfo, scanHistoryTemp);
                scanHistoryTemp.setSmtScanId(java.util.UUID.randomUUID().toString()); // 设置主键
                insertWipScanHistorys.add(scanHistoryTemp);
            });
            // 批量更新条码状态
            psWipInfoService.updateWipInfoByHistory(listPsWipInfos);
            batchInsertPsScanHistory(insertWipScanHistorys);
            // 更新库存明细表中，条码状态 为3 已拒绝.
            updateWarehouseStatus(warehouseEntryInfo, CommonConst.WAREHOUSE_DATAIL_SATATUS_REJECT);
            try {
                //更新箱内容状态
                SysLookupTypesDTO sysLookupTypesDTO = BasicsettingRemoteService.getSysLookUpValue(Constant.LOOK_UP_TYPE_STOCK_RCV_RIGHT, Constant.LOOK_UP_CODE_WAREHOUSE_UPDATE_CONTAINER);
                if (sysLookupTypesDTO != null && Constant.FLAG_N.equals(sysLookupTypesDTO.getLookupMeaning())) {
                    return;
                }
                ProductionDeliveryRemoteService.batchSaveForCcInfo(getSns(listPsWipInfos), psScanHistory.getCurrProcessCode(), psScanHistory.getWorkStation());
            } catch (Exception e) {
                logger.error("更新箱内容状态异常", e);
            }
        } else{
            // 单板返修只需要更新明细表中,条码状态为3 已拒绝.
            updateWarehouseStatus(warehouseEntryInfo, CommonConst.WAREHOUSE_DATAIL_SATATUS_REJECT);
        }
    }

    /**
     * 判断不是返修和转正入库单
     * @param billType
     * @return
     */
    private boolean notFxAndZzType(String billType) {
        return !MpConstant.BILL_TYPE_DBFX.equals(billType) && !MpConstant.BILL_TYPE_CONFIRMATION.equals(billType);
    }

    @Override
    public void batchInsertPsScanHistory(List<PsScanHistory> insertWipScanHistorys) {
        List<List<PsScanHistory>> listOfList = CommonUtils.splitList(insertWipScanHistorys, CommonConst.BATCH_SIZE);
        if (!CollectionUtils.isEmpty(listOfList)) {
            for (List<PsScanHistory> paramsList : listOfList) {
                psScanHistoryRepository.insertPsScanHistoryBatch(paramsList);
            }
        }
    }

    public void dealScrapBillSn(WarehouseEntryInfo warehouseEntryInfo, List<PsWipInfo> listPsWipInfos, List<PsScanHistory> insertWipScanHistorys) {
        List<ScrapBillDetailEntityDTO> scrapBilldetList = new ArrayList<>();
        listPsWipInfos.parallelStream().forEach(psWipInfo -> {
            psWipInfo.setLastUpdatedDate(new Date());
            psWipInfo.setCurrProcessCode(Constant.P0007);
            psWipInfo.setLastUpdatedBy(warehouseEntryInfo.getLastUpdatedBy());
            // 单板报废需要更新scrap_bill_detail当前工序为P0007
            ScrapBillDetailEntityDTO scrapDeEntity = new ScrapBillDetailEntityDTO();
            scrapDeEntity.setLastUpdatedBy(warehouseEntryInfo.getLastUpdatedBy());
            scrapDeEntity.setCurrProcessCode(P0007);
            scrapDeEntity.setSn(psWipInfo.getSn());
            scrapBilldetList.add(scrapDeEntity);
            // 获取待保存的扫描历史信息
            PsScanHistory scanHistory = new PsScanHistory();
            BeanUtils.copyProperties(psWipInfo, scanHistory);
            scanHistory.setSmtScanId(java.util.UUID.randomUUID().toString()); // 设置主键
            insertWipScanHistorys.add(scanHistory);
        });
        // 批量更新条码状态
        psWipInfoService.updateWipInfoByHistory(listPsWipInfos);
        // 单板报废需要更新scrap_bill_detail当前工序为P0007
        scrapBillDetailService.updateProcessCodeBatchBySn(scrapBilldetList);
        // 更新库存明细表中，条码状态 为3 已拒绝.
        updateWarehouseStatus(warehouseEntryInfo, CommonConst.WAREHOUSE_DATAIL_SATATUS_REJECT);
        batchInsertPsScanHistory(insertWipScanHistorys);
    }

    /**
     * 根据指令信息获取工艺路径入库前的子工序
     */
    public String getProcessForQuery(WarehouseEntryInfo warehouseEntryInfo) throws Exception {
        //获取对应指令信息
        List<PsWorkOrderDTO> psWorkOrderDTOList = PlanscheduleRemoteService.getBasicWorkOrderInfoByProdplanId(warehouseEntryInfo.getProdplanId());

        if (CollectionUtils.isEmpty(psWorkOrderDTOList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.WORKORDER_INFO_IS_NULL);
        }
        // 根据工艺路径查询ct_route_detail里next_process='N'的数据
        if (StringUtils.isNotEmpty(psWorkOrderDTOList.get(NUM_ZERO).getRouteId())) {
            CtRouteDetailParamDTO paramObj = new CtRouteDetailParamDTO();
            paramObj.setRouteId(psWorkOrderDTOList.get(NUM_ZERO).getRouteId());
            paramObj.setNextProcess(MpConstant.STR_IN_WH);
            List<CtRouteDetail> dtlListOne = CrafttechRemoteService.getCtRouteDetailInfoNew(paramObj);
            if (!CollectionUtils.isEmpty(dtlListOne)) {
                CtRouteDetail detail = dtlListOne.get(0);
                if (null == detail) {
                    return "";
                }
                String processForQuery = detail.getCurrProcess();
                processForQuery = this.checkIsHighTempProcess(paramObj, processForQuery);
                if (StringUtils.isNotBlank(processForQuery)) {
                    return processForQuery;
                }
            }
        }
        return "";
    }

    private String checkIsHighTempProcess(CtRouteDetailParamDTO paramObj, String processForQuery) {
        // 入库前如果是高温，则需拼接高温前的工序
        if (StringUtils.equals(highProcessCode, processForQuery)) {
            paramObj.setNextProcess(processForQuery);
            List<CtRouteDetail> detailList = CrafttechRemoteService.getCtRouteDetailInfoNew(paramObj);
            if (!CollectionUtils.isEmpty(detailList)) {
                CtRouteDetail ctRouteDetail = detailList.get(0);
                if (null == ctRouteDetail) {
                    return null;
                }
                processForQuery += Constant.COMMA + ctRouteDetail.getCurrProcess();
            }
        }
        return processForQuery;
    }

    //关闭入库单
    public void closeWarehouseEntryInfo(WarehouseEntryInfo warehouseEntryInfo) {
        //  2020/6/10  查询需要回退的条码
        //  2020/6/10  回退条码状态
        snBackOfSameProdplan(psWipInfoService.getWipInfoByBillNo(warehouseEntryInfo.getBillNo(), CommonConst.WAREHOUSE_DATAIL_SATATUS_SUBMIT)
                , warehouseEntryInfo.getLastUpdatedBy());
        // 2020/6/10  更新库存明细表中，只更新已提交的条码；
        updateWarehouseStatus(warehouseEntryInfo, CommonConst.WAREHOUSE_DATAIL_SATATUS_REJECT, CommonConst.WAREHOUSE_DATAIL_SATATUS_SUBMIT);
    }

    //通批次条码回退到最新状态
    private void snBackOfSameProdplan(List<PsWipInfo> listPsWipInfos, String userId) {
        if (CollectionUtils.isEmpty(listPsWipInfos)) {
            return;
        }
        // 获取条码扫描历史表
        Map<String, Object> map = new HashMap<String, Object>();
        // 设置查询条件
        map.put("sn", listPsWipInfos.get(0).getSn());
        map.put("orderField", MpConstant.ORDER_FIELD);
        map.put("order", Constant.DESC);
        List<PsScanHistory> psScanHistories = psScanHistoryRepository.getList(map);
        if (psScanHistories == null || psScanHistories.isEmpty()) {
            return;
        }
        PsScanHistory psScanHistory = psScanHistories.get(0);
        listPsWipInfos.parallelStream().forEach(psWipInfo -> handleWipInfoBackField(psWipInfo, psScanHistory, userId));
        // 批量更新条码状态
        psWipInfoService.updateWipInfoByHistory(listPsWipInfos);

        try {
            //更新箱内容状态
            SysLookupTypesDTO sysLookupTypesDTO = BasicsettingRemoteService.getSysLookUpValue(Constant.LOOK_UP_TYPE_STOCK_RCV_RIGHT, Constant.LOOK_UP_CODE_WAREHOUSE_UPDATE_CONTAINER);
            if (sysLookupTypesDTO != null && Constant.FLAG_N.equals(sysLookupTypesDTO.getLookupMeaning())) {
                return;
            }
            ProductionDeliveryRemoteService.batchSaveForCcInfo(getSns(listPsWipInfos), psScanHistory.getCurrProcessCode(), psScanHistory.getWorkStation());
        } catch (Exception e) {

        }

    }

    // 处理需要回退的字段
    private void handleWipInfoBackField(PsWipInfo psWipInfo, PsScanHistory psScanHistory, String userId) {
        psWipInfo.setLastUpdatedDate(new Date());
        psWipInfo.setWorkOrderNo(psScanHistory.getWorkOrderNo());
        psWipInfo.setWorkStation(psScanHistory.getWorkStation());
        psWipInfo.setNextProcess(psScanHistory.getNextProcess());
        psWipInfo.setLastProcess(psScanHistory.getLastProcess());
        psWipInfo.setCraftSection(psScanHistory.getCraftSection());
        psWipInfo.setCurrProcessCode(psScanHistory.getCurrProcessCode());
        psWipInfo.setLastUpdatedBy(userId);
    }

    private void updateWarehouseStatus(WarehouseEntryInfo warehouseEntryInfo, String status, String conditionStatus) {
        Map<String, String> params = new HashMap<>();
        params.put("lastUpdatedBy", warehouseEntryInfo.getLastUpdatedBy());
        params.put("status", status);
        params.put("billNo", warehouseEntryInfo.getBillNo());
        if (StringHelper.isNotEmpty(conditionStatus)) {
            params.put("conditionStatus", conditionStatus);
        }
        // 1. 更新详情表
        warehouseEntryInfoRepository.updateWarehouseEntryDetailsByBillNo(params);
    }

    private void updateWarehouseStatus(WarehouseEntryInfo warehouseEntryInfo, String status) {
        updateWarehouseStatus(warehouseEntryInfo, status, null);
    }

    /**
     * 批量接收入库单
     * 1、更新在制表状态
     * 2、调用erp工序移动接口
     * 3、调用erp工序完工接口 更新单据状态
     *
     * @param infoList
     * @param updateWorkStation
     * @return
     * @throws Exception
     * @autchor 6092002408
     * @date 2018-10-19
     */
    @Override
    public void receiveWarehouseEntryInfoBatch(List<WarehouseEntryInfo> infoList, String updateWorkStation) throws Exception {
        // 待接收的单据
        SysLookupTypesDTO sysLookupTypesDTO = queryLookupType(CommonConst.DIP_TRANSFER_WAREHOUSE_SYS_TYPE, CommonConst.ON_SHELF_PROCESS_CODE);
        if (sysLookupTypesDTO == null) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SYS_LOOK_PARAM_NOT_CONFIG);
        }
        List<RedisLock> redisLockList = new ArrayList<>();
        //如果验证失败
        Boolean validateResult = validateWarehouseEntryInfoData(infoList, redisLockList);
        if (validateResult) {
            String[] params = {infoList.stream().map(WarehouseEntryInfo::getBillNo).collect(Collectors.joining(","))};
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.WAREHOUSEENTRYINFO_IS_LOCKED, params);
        }
        StringBuilder msgBuffer = new StringBuilder();
        try {
            for (WarehouseEntryInfo warehouseEntryInfo : infoList) {
                //验证入库单状态是否为已确认，已拒绝，已关闭，如果为这三个状态不可以接收
                Boolean result = warehouseEntryInfoRepository.validateWarehouseEntryInfoStatus(warehouseEntryInfo.getBillNo()
                        , Arrays.asList(String.valueOf(Constant.INT_2), String.valueOf(Constant.INT_1), String.valueOf(Constant.INT_3)), null);
                if (!result) {
                    String[] params = {warehouseEntryInfo.getBillNo()};
                    throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.CURRENT_STATUS_NOT_ALLOW_RECEIVE, params);
                }
                //判断入库或是移库 （任务类型为库存返工为移库）
                if (StringUtils.isNotBlank(warehouseEntryInfo.getBillNo())) {
                    WarehouseEntryInfoServiceImpl currentProxy = SpringUtil.getBean(this.getClass());
                    msgBuffer.append(currentProxy.submitWareEntryInfo(warehouseEntryInfo, sysLookupTypesDTO, updateWorkStation));
                }
            }
        } finally {
            //统一关闭redis
            redisLockList.forEach(RedisLock::unlock);
        }
        if (StringUtils.isNotBlank(msgBuffer.toString())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.CUSTOMIZE_MSG, new Object[]{msgBuffer.toString()});
        }
    }

    /**
     * @param list
     * @param redisLockList
     * @return 返回true, 则有单据正在处理处于被锁定状态
     */
    private Boolean validateWarehouseEntryInfoData(List<WarehouseEntryInfo> list, List<RedisLock> redisLockList) {
        //判断redis是否有单据锁定
        Boolean result = Boolean.FALSE;
        for (WarehouseEntryInfo warehouseEntryInfo : list) {
            //锁定入库单
            String lockKey = String.format(MpConstant.LOCK_WAREHOUSE_ENTRY_INFO, warehouseEntryInfo.getBillNo());
            RedisLock redisLock = new RedisLock(lockKey, INT_600);
            Boolean lockResult = redisLock.lock();
            if (!lockResult) {
                result = Boolean.TRUE;
                break;
            }
            //添加到redis集合中
            redisLockList.add(redisLock);
        }
        //如果选中的入库单有已经被锁定的，直接返回失败释放锁资源
        if (result) {
            redisLockList.stream().forEach(redisLock -> redisLock.unlock());
        }

        return result;
    }

    /**
     * 单据中的条码是否都被接收了，    单据数量 = COMPLETE_QTY+本次接收数量
     *
     * @param warehouseEntryInfo
     * @return
     */
    @Override
    public boolean isBillAllReceive(WarehouseEntryInfo warehouseEntryInfo) {
        WarehouseEntryInfoDTO entryInfoDTO = new WarehouseEntryInfoDTO();
        entryInfoDTO.setBillNo(warehouseEntryInfo.getBillNo());
        int total = warehouseEntryInfoRepository.selectCountDetailInfo(entryInfoDTO);
        entryInfoDTO.setStatus(CommonConst.WAREHOUSE_DATAIL_SATATUS_SUBMIT);
        entryInfoDTO.setSns(psWipInfoServiceImp.getSns(warehouseEntryInfo.getSnList()));
        int received = warehouseEntryInfoRepository.selectCountDetailInfo(entryInfoDTO);
        return received + warehouseEntryInfo.getSnList().size() >= total;
    }

    private void dealWipInfo(WarehouseEntryInfo warehouseEntryInfo, SysLookupTypesDTO sysLookupTypesDTO, String updateWorkStation) throws Exception {
        if (null == updateWorkStation && null != warehouseEntryInfo) {
            String workStation = sysLookupTypesDTO.getDescriptionEngV();
            if (Constant.STOCK_TYPE_MODE_WORKSTATION.equals(warehouseEntryInfo.getStockType())) {
                SysLookupTypesDTO workStationLookUp = BasicsettingRemoteService.getSysLookUpValue(Constant.DIP_TRANSFER_WAREHOUSE_SYS_TYPE, Constant.WORK_STATION_IN_WAREHOUSE);
                workStation = null == workStationLookUp ? workStation : workStationLookUp.getDescriptionEngV();
            }
            updatePsWipInfoToEntryProcess(warehouseEntryInfo, workStation);
        }
    }

    /**
     * 保存库存信息
     *
     */
    private void saveStockInfo(WarehouseEntryInfo warehouseEntryInfo) throws Exception {
        if (StringUtils.equals(FLAG_N,homeLedgerFlag)) {
            return;
        }
        List<ContainerContentInfoDTO> list = new ArrayList<>();
        ContainerContentInfoDTO infoDTO = new ContainerContentInfoDTO();
        infoDTO.setItemCode(warehouseEntryInfo.getItemNo());
        infoDTO.setHomeReceiveFlag(FLAG_Y);
        infoDTO.setItemQty(new BigDecimal(warehouseEntryInfo.getSnList().size()));
        infoDTO.setLpn(warehouseEntryDetailRepository.getLpnByBillNo(warehouseEntryInfo.getBillNo()));
        infoDTO.setProdPlanId(warehouseEntryInfo.getProdplanId());
        infoDTO.setFactoryId(warehouseEntryInfo.getFactoryId());
        infoDTO.setCreateBy(warehouseEntryInfo.getCreateBy());
        infoDTO.setLastUpdatedBy(warehouseEntryInfo.getLastUpdatedBy());
        list.add(infoDTO);
        ProductionDeliveryRemoteService.saveStockInfo(list);
    }


    /**
     * 提交入库单
     *
     * @param warehouseEntryInfo
     * @param sysLookupTypesDTO
     * @param updateWorkStation
     * @return
     * @throws Exception
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String submitWareEntryInfo(WarehouseEntryInfo warehouseEntryInfo, SysLookupTypesDTO sysLookupTypesDTO, String updateWorkStation) throws Exception {
        // 2. 判断是移库还是入库
        StringBuffer errMsg = new StringBuffer();
        if (MpConstant.TASK_TYPE_RETURN.equals(warehouseEntryInfo.getTaskType())) {
            // 2.1 移库 - 调用 WebService oerp_m04_importmaterialtrxinfosvr_client_ep ERP 移库
            erpRemoteService.invokeErpImport(warehouseEntryInfo, errMsg);
            if (StringUtils.isNotBlank(errMsg.toString())) {
                return errMsg.toString();
            }
            // 2.1.1 更新条码状态为待上架
            dealWipInfo(warehouseEntryInfo, sysLookupTypesDTO, updateWorkStation);
        } else if (MpConstant.BILL_TYPE_CONFIRMATION.equals(warehouseEntryInfo.getBillType())) {
            // 根据单号更新入库单头表状态为1 已确认
            warehouseEntryInfo.setStatus(STR_ONE);
            WarehouseEntryInfoDTO updateDto = new WarehouseEntryInfoDTO();
            updateDto.setBillNo(warehouseEntryInfo.getBillNo());
            updateDto.setStatus(STR_ONE);
            updateDto.setLastUpdatedBy(warehouseEntryInfo.getLastUpdatedBy());
            warehouseEntryInfoRepository.updateWarehouseEntryInfosByBillNo(updateDto);
            // 1.根据单号更新入库单明细状态为2 已接收
            warehouseEntryDetailService.updateStatusByBillNo(warehouseEntryInfo.getBillNo(), CommonConst.WAREHOUSE_DATAIL_STATUS_CONFIRM, warehouseEntryInfo.getLastUpdatedBy());

            // 2.移库 - 调用 WebService oerp_m04_importmaterialtrxinfosvr_client_ep ERP 移库
            boolean result = erpRemoteService.invokeErpImport(warehouseEntryInfo, errMsg);
            if (!result) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.ERP_MOVE_ERROR, new Object[]{errMsg.toString()});
            }

            // 3.判断任务下入buffer库条码是否已全部接收，若全部转正接收则需更新任务转正状态变为已转正
            this.checkAndUpdateComfirmStatus(warehouseEntryInfo.getTaskNo());
        } else {
            // 分单据类型执行
            receiptByBillType(warehouseEntryInfo, sysLookupTypesDTO, updateWorkStation, errMsg);
            if (StringUtils.isNotBlank(errMsg.toString())) {
                return errMsg.toString();
            }
            // 接收完成更新任务已完工数量及状态- 2025-03-13 前置更改数量 避免因为数量更改失败导致重复erp记账
            updateTaskCompletedQuantity(warehouseEntryInfo);
        }

        return errMsg.toString();
    }

    private void receiptByBillType(WarehouseEntryInfo warehouseEntryInfo, SysLookupTypesDTO sysLookupTypesDTO, String updateWorkStation, StringBuffer errMsg) throws Exception {
        //  2.2 入库
        switch (warehouseEntryInfo.getBillType()) {
            //子卡入库单
            case MpConstant.BILL_TYPE_SON:
                // 更新erp调用信息
                updateWarehouseStatus(warehouseEntryInfo);
                setWarehouseEntryInfoStatus(warehouseEntryInfo);
                updateWarehouseEntryInfoById(warehouseEntryInfo);
                // 设置父任务prodplanNo(erp子卡出库wipEntityName要写父任务批次号)
                WarehouseEntryInfoQueryDTO dto = new WarehouseEntryInfoQueryDTO();
                dto.setProdplanId(warehouseEntryInfo.getProdplanId());
                List<PsWipInfoDTO> psWipDtoList = warehouseEntryInfo.getSnList();
                dto.setPsWipInfoList(toPsWipInfoList(psWipDtoList));
                setCommitQty(warehouseEntryInfo, psWipDtoList);
                this.setParentProdplanNo(warehouseEntryInfo, dto);
                List<Map<String, String>> boardsImu = new ArrayList<>(1);
                this.packBoardData(boardsImu, dto, warehouseEntryInfo);
                //卡回写erp并更新IMU为613
                DatawbRemoteService.writeToErpAndUpdateImu(warehouseEntryInfo, boardsImu);
                break;
            //单板生产入库单
            case MpConstant.BILL_TYPE_SINGLE:
                // 家端单板库 调ERP接口
                if (this.invokeErpInterface(warehouseEntryInfo, errMsg)) {
                    break;
                }
                //单板入库参考 生产入库单提交（子卡入库单） 只做入库
                if (Constant.STOCK_TYPE_K2.equals(warehouseEntryInfo.getStockType())) {
                    // 查询单据下的条码是否超出滞留时长
                    checkSnIsRetention(warehouseEntryInfo);
                    // 更新erp调用信息
                    receiveInK2(warehouseEntryInfo);
                    break;
                }
                //单板入整机库，回写条码为入库
                if (Constant.STOCK_TYPE_MODE_WORKSTATION.equals(warehouseEntryInfo.getStockType())) {
                    receiveInMode(warehouseEntryInfo);
                }
                //  2.1.2 更新条码状态为待上架     1.在制信息批量更新为“入库”
                dealWipInfo(warehouseEntryInfo, sysLookupTypesDTO, updateWorkStation);
                //处理接收的条码    2.处理需求单详细表数据
                dealReqDetail(warehouseEntryInfo);
                onlyWriteErp(warehouseEntryInfo);   //3.单板写ERP 入库表
                break;
            //整机入库单
            case MpConstant.BILL_TYPE_WHOLE:
                //整机入库走原逻辑
                //更新在制信息为“待上架”
                dealWipInfo(warehouseEntryInfo, sysLookupTypesDTO, updateWorkStation);
                // 调用ERP工序移动接口
                if (erpRemoteService.invokeErpMove(warehouseEntryInfo, errMsg)) {
                    // 调用ERP工序完工接口
                    erpRemoteService.invokeErpDone(warehouseEntryInfo, errMsg);
                }
                break;
            // 单板报废入库单
            case MpConstant.BILL_TYPE_FIVE:
                if (this.extractMethod(warehouseEntryInfo, errMsg)) {
                    break;
                }
                //单板入整机库，回写条码为入库
                if (Constant.STOCK_TYPE_MODE_WORKSTATION.equals(warehouseEntryInfo.getStockType())) {
                    receiveInMode(warehouseEntryInfo);
                }
                onlyWriteErp(warehouseEntryInfo);
                break;
            default:
                break;
        }
    }

    /**
     * 判断任务下入buffer库条码是否已全部接收
     * 若全部转正接收则需更新任务转正状态变为已转正
     * @param taskNo
     */
    private void checkAndUpdateComfirmStatus(String taskNo) {
        // 判断任务下入buffer库条码是否已全部接收

        SysLookupValues sysLookupValues = BasicsettingRemoteService.getSysLookUpValueByCode(Constant.LOOKUP_TYPE_7502002);
        if (sysLookupValues == null || StringUtils.isBlank(sysLookupValues.getAttribute2())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SYS_LOOK_NOT_CONFIG, new Object[]{Constant.LOOKUP_TYPE_7502002});
        }

        // 配置项可能存在多个，使用,分隔
        List<String> bufferStockNameList = Arrays.asList(sysLookupValues.getAttribute2().split(COMMA));
        // 查询入buffer库后还未转正接收条码数量
        int count = warehouseEntryDetailService.getNotComfirmedSnCount(taskNo, bufferStockNameList);
        if (count > NUM_ZERO) {
            return;
        }
        // 所有条码都已转正接收则更新任务转正状态变为3:已转正
        centerfactoryRemoteService.updateConfirmationStatus(STR_THREE, taskNo);
    }

    private boolean extractMethod(WarehouseEntryInfo warehouseEntryInfo, StringBuffer errMsg) throws Exception {
        // 家端单板库 调ERP接口
        if (Constant.STOCK_TYPE_HOME_BOARD.equals(warehouseEntryInfo.getStockType())) {
            // 调用ERP工序移动接口
            if (erpRemoteService.invokeErpMove(warehouseEntryInfo, errMsg)) {
                // 调用ERP工序完工接口
                erpRemoteService.invokeErpDone(warehouseEntryInfo, errMsg);
            }
            return true;
        }
        if (Constant.STOCK_TYPE_K2.equals(warehouseEntryInfo.getStockType())) {
            // 更新erp调用信息
            receiveInK2(warehouseEntryInfo);
            return true;
        }
        return false;
    }

    private boolean invokeErpInterface(WarehouseEntryInfo warehouseEntryInfo, StringBuffer errMsg) throws Exception {
        if (Constant.STOCK_TYPE_HOME_BOARD.equals(warehouseEntryInfo.getStockType())) {
            // 保存库存信息
            this.saveStockInfo(warehouseEntryInfo);
            // 调用ERP工序移动接口
            if (erpRemoteService.invokeErpMove(warehouseEntryInfo, errMsg)) {
                // 调用ERP工序完工接口
                erpRemoteService.invokeErpDone(warehouseEntryInfo, errMsg);
            }
            return true;
        }
        return false;
    }

    public void checkSnIsRetention(WarehouseEntryInfo warehouseEntryInfo) throws Exception {
        String prodplanId = warehouseEntryInfo.getProdplanId();
        boolean isCheckRetention = isPreserveRetention(prodplanId);
        if (isCheckRetention) {
            List<PsWipInfoDTO> psWipList = warehouseEntryInfo.getSnList();
            List<PsWipInfo> psWipInfoList = toPsWipInfoList(psWipList);
            if (CollectionUtils.isEmpty(psWipList)) {
                WarehouseEntryInfoDTO entryInfoDTO = new WarehouseEntryInfoDTO();
                entryInfoDTO.setBillNo(warehouseEntryInfo.getBillNo());
                List<WarehouseEntryInfoDTO> detailList = warehouseEntryInfoRepository.selectWarehouseEntryDetailInfo(entryInfoDTO);
                if (!CollectionUtils.isEmpty(detailList)) {
                    List<String> snList = detailList.stream().map(i -> i.getSn()).distinct().collect(Collectors.toList());
                    psWipInfoList = psWipInfoService.queryWipSnBatch(snList);
                }
            }
            List<String> retentionSnList = psWipInfoService.filterRetentionList(psWipInfoList);
            if (!CollectionUtils.isEmpty(retentionSnList)) {
                retentionSnList.stream().distinct().collect(Collectors.toList());
                String[] infoParam = {StringUtils.join(retentionSnList, COMMA)};
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SN_CAN_NOT_RECEIVE_IN_STOCK, infoParam);
            }
            WarehouseEntryInfoDTO dto = new WarehouseEntryInfoDTO();
            BeanUtils.copyProperties(warehouseEntryInfo, dto);
            String applyNo = warehouseEntryInfoWritebackStep(dto);
            warehouseEntryInfo.setApplyNo(applyNo);
            warehouseEntryInfo.setUpdateApplyNoFlag(FLAG_Y);
        }
    }

    /**
     * 更新任务已完成数量
     *
     * @param warehouseEntryInfo
     * @throws Exception
     */
    private void updateTaskCompletedQuantity(WarehouseEntryInfo warehouseEntryInfo) throws Exception {
        // 扫描接收时根据扫描的条码数更新任务已完成数量
        BigDecimal qty = CollectionUtils.isEmpty(warehouseEntryInfo.getSnList()) ? warehouseEntryInfo.getCommitedQty() : new BigDecimal(warehouseEntryInfo.getSnList().size());
        this.handleCompleteQty(warehouseEntryInfo.getProdplanId(), qty);
    }

    private void receiveInMode(WarehouseEntryInfo warehouseEntryInfo) throws Exception {
        List<Map<String, String>> boards = new ArrayList<>(1);
        this.packBoardData(boards, warehouseEntryInfo);
        DatawbRemoteService.updateImuBatch(boards);
    }

    private void receiveInK2(WarehouseEntryInfo warehouseEntryInfo) throws Exception {
        warehouseEntryInfo.setStatus(Constant.ERP_DONE_STATUS);
        updateWarehouseEntryInfoById(warehouseEntryInfo);
        updateWarehouseStatus(warehouseEntryInfo);
    }

    public void setCommitQty(WarehouseEntryInfo warehouseEntryInfo, List<PsWipInfoDTO> psWipDtoList) {
        if (!CollectionUtils.isEmpty(psWipDtoList)) {
            warehouseEntryInfo.setCommitedQty(new BigDecimal(psWipDtoList.size()));
        }
    }

    //实体类转换
    private List<PsWipInfo> toPsWipInfoList(List<PsWipInfoDTO> entityList) {
        List<PsWipInfo> dtoList = new ArrayList<PsWipInfo>();
        if (CollectionUtils.isEmpty(entityList)) {
            return dtoList;
        }
        for (PsWipInfoDTO entity : entityList) {
            dtoList.add(toWipInfo(entity));
        }
        return dtoList;
    }

    private PsWipInfo toWipInfo(PsWipInfoDTO entity) {
        PsWipInfo dto = new PsWipInfo();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }


    //设置单据状态
    private void setWarehouseEntryInfoStatus(WarehouseEntryInfo warehouseEntryInfo) {
        WarehouseEntryInfoDTO entryInfoDTO = new WarehouseEntryInfoDTO();
        entryInfoDTO.setBillNo(warehouseEntryInfo.getBillNo());
        entryInfoDTO.setStatus(CommonConst.WAREHOUSE_DATAIL_STATUS_CONFIRM);
        int noReceived = warehouseEntryInfoRepository.selectCountDetailInfo(entryInfoDTO);
        if (noReceived == 0) {
            warehouseEntryInfo.setStatus(Constant.ERP_DONE_STATUS);
        } else {
            warehouseEntryInfo.setStatus(CommonConst.WAREHOUSE_DATAIL_SATATUS_SUBMIT);
        }
    }

    //处理需求单详细表数据
    private void dealReqDetail(WarehouseEntryInfo warehouseEntryInfo) throws Exception {
        //变更需求单详表单据状态
        updateReqDetailStatus(warehouseEntryInfo.getBillNo(), warehouseEntryInfo.getStatus(), warehouseEntryInfo.getLastUpdatedBy());
        //处理需求单，判断是否需要完工，
        checkAndDoneReqStatus(warehouseEntryInfo.getBillNo());
    }

    /**
     * 修改需求单详表状态
     *
     * @param stockInNo
     * @param warehousingStatus
     * @param lastUpdatedBy
     * @param qty
     */
    private void updateReqDetailStatusAndQty(String stockInNo, String warehousingStatus, String lastUpdatedBy, BigDecimal qty) {
        if (StringUtils.isNotEmpty(stockInNo) && StringUtils.isNotEmpty(warehousingStatus)) {
            warehouseRequirementDetailRepository.updateByBillNo(Constant.SINGLE_QUOTE + stockInNo + Constant.SINGLE_QUOTE
                    , warehousingStatus, lastUpdatedBy, qty);
        }
    }

    /**
     * 修改需求单详表状态，  拒绝、关闭 入库单的时候 调用的
     *
     * @param warehouseEntryInfos
     */
    private void updateReqDetailStatusAndQty(List<WarehouseEntryInfo> warehouseEntryInfos) {
        // 拼接sns
        if (CollectionUtils.isEmpty(warehouseEntryInfos)) {
            return;
        }
        //关闭,关闭时需处理需求单详表的数据
        if (MpConstant.WAREHOUSE_ENTRY_STATUS_CLOSE.equals(warehouseEntryInfos.get(0).getStatus())) {
            for (WarehouseEntryInfo dto : warehouseEntryInfos) {
                StringBuffer stockInNo = new StringBuffer();
                stockInNo.append(Constant.SINGLE_QUOTE + dto.getBillNo() + Constant.SINGLE_QUOTE);
                warehouseRequirementDetailRepository.updateByBillNo(stockInNo.toString(), dto.getStatus()
                        , dto.getLastUpdatedBy(), new BigDecimal(warehouseEntryInfoRepository.getBillReceiveCnt(dto.getBillNo())));
            }
        } else {//拒绝
            StringBuffer stockInNo = new StringBuffer();
            for (WarehouseEntryInfo dto : warehouseEntryInfos) {
                stockInNo.append(Constant.SINGLE_QUOTE + dto.getBillNo() + Constant.SINGLE_QUOTE + Constant.COMMA);
            }
            // 删除最后一个逗号
            stockInNo.replace(stockInNo.length() - 1, stockInNo.length(), "");
            if (StringUtils.isNotEmpty(stockInNo)) {
                warehouseRequirementDetailRepository.updateByBillNo(stockInNo.toString(), warehouseEntryInfos.get(0).getStatus()
                        , warehouseEntryInfos.get(0).getLastUpdatedBy(), new BigDecimal(0));
            }
        }


    }

    private void updateReqDetailStatus(String stockInNo, String warehousingStatus, String lastUpdatedBy) {
        updateReqDetailStatusAndQty(stockInNo, warehousingStatus, lastUpdatedBy, null);
    }

    /**
     * 详情表状态为已确认
     *
     * @param warehouseEntryInfo
     */
    private void updateWarehouseStatus(WarehouseEntryInfo warehouseEntryInfo) throws Exception {
        Map<String, String> params = new HashMap<>();
        params.put("lastUpdatedBy", warehouseEntryInfo.getLastUpdatedBy());
        params.put("status", CommonConst.WAREHOUSE_DATAIL_STATUS_CONFIRM);
        params.put("billNo", warehouseEntryInfo.getBillNo());
        if(Constant.FLAG_Y.equals(warehouseEntryInfo.getUpdateApplyNoFlag())){
            params.put("applyNo", warehouseEntryInfo.getApplyNo());
        }
        if (!org.springframework.util.CollectionUtils.isEmpty(warehouseEntryInfo.getSnList())) {
            List<List<PsWipInfoDTO>> listOfList = CommonUtils.splitList(warehouseEntryInfo.getSnList());
            // 校验条码状态
            this.checkSnStatus(warehouseEntryInfo);
            for (List<PsWipInfoDTO> list : listOfList) {
                params.put("sns", psWipInfoServiceImp.getSns(list));
                // 分批更新详情表
                warehouseEntryInfoRepository.updateWarehouseEntryDetailsByBillNo(params);
            }
        } else {
            // 1. 更新详情表
            warehouseEntryInfoRepository.updateWarehouseEntryDetailsByBillNo(params);
        }
    }

    /**
     * CAS 校验比较条码状态
     *
     * @param warehouseEntryInfo
     */
    public void checkSnStatus(WarehouseEntryInfo warehouseEntryInfo) {
        if (warehouseEntryInfo.isCheckSnStatus()) {
            return;
        }
        // 校验条码状态，是否存在部分已经提交
        WarehouseEntryInfoDTO query = new WarehouseEntryInfoDTO();
        query.setBillNo(warehouseEntryInfo.getBillNo());
        query.setStatus(CommonConst.WAREHOUSE_DATAIL_STATUS_CONFIRM);
        List<String> listSn = warehouseEntryInfoRepository.selectDetailCas(query);
        List<PsWipInfoDTO> snList = warehouseEntryInfo.getSnList();
        List<String> inputSnList = snList.stream().map(PsWipInfoDTO::getSn).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(listSn)) {
            return;
        }
        List<String> errorList =
                inputSnList.stream().filter(item -> listSn.contains(item)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(errorList)) {
            return;
        }
        // 单据明细状态异常报错
        throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.BILL_DETAILS_SN_STATUS_ERROR,
                new Object[]{warehouseEntryInfo.getBillNo(), errorList.toString()});
    }


    private SysLookupTypesDTO queryLookupType(String lookupType, String lookupCode) {
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        try {
            sysLookupTypesDTO = BasicsettingRemoteService.getSysLookUpValue(lookupType, lookupCode);
        } catch (Exception e) {
        }
        return sysLookupTypesDTO;
    }

    /**
     * 在制信息批量更新为“入库”
     *
     * @param warehouseEntryInfo 入库单实体
     * @return List<PsWipInfo> 在制信息列表
     * @autchor 6092002408
     * @date 2018-11-08
     */
    private void updatePsWipInfoToEntryProcess(WarehouseEntryInfo warehouseEntryInfo, String workStation) throws Exception {
        // 入库后工位置为"0"
        if (null == workStation) {
            workStation = MpConstant.STRING_ZERO;
        }
        Map<String, Object> map = new HashMap<String, Object>(4);
        map.put("billNo", warehouseEntryInfo.getBillNo());
        map.put("factoryId", warehouseEntryInfo.getFactoryId());
        map.put("entityId", warehouseEntryInfo.getEntityId());
        map.put("orgId", warehouseEntryInfo.getOrgId());
        //如果扫描接收提交
        if (!org.springframework.util.CollectionUtils.isEmpty(warehouseEntryInfo.getSnList())) {
            map.put("sns", psWipInfoServiceImp.getSns(warehouseEntryInfo.getSnList()));
        }
        // 查询单据明细
        List<WarehouseEntryInfoDetailDTO> listDetail = warehouseEntryInfoRepository.selectWarehouseEntryInfoDetailByBillNo(map);

        // 条码列表
        List<String> listSn = new ArrayList();
        if (CollectionUtils.isEmpty(listDetail)) {
            return;
        }

        for (WarehouseEntryInfoDetailDTO detail : listDetail) {
            listSn.add(detail.getSn());
        }
        // 根据明细包含的条码查询该单据下的在制信息
        List<PsWipInfo> listPsWipInfo = psWipInfoService.getListByBatchSn(listSn);

        if (!CollectionUtils.isEmpty(listPsWipInfo) && listPsWipInfo.size() > 0) {
            // 工艺路径最后工序
            this.updatePsWipInfos(warehouseEntryInfo, listPsWipInfo, workStation);
        }
    }

    /**
     * 在制信息批量更新为“入库”
     *
     * @param warehouseEntryInfo 入库单实体
     * @return List<PsWipInfo> 在制信息列表
     * @autchor 6092002408
     * @date 2018-11-08
     */
    private void updatePsWipInfoToEntryProcessNew(WarehouseEntryInfo warehouseEntryInfo, String workStation, WarehouseEntryInfoQueryDTO dto) throws Exception {
//        boolean save = true;
        // 入库后工位置为"0"
        if (null == workStation) {
            workStation = MpConstant.STRING_ZERO;
//            save = false;
        }
        Map<String, Object> map = new HashMap<String, Object>(4);
        map.put("billNo", warehouseEntryInfo.getBillNo());
        map.put("factoryId", warehouseEntryInfo.getFactoryId());
        map.put("entityId", warehouseEntryInfo.getEntityId());
        map.put("orgId", warehouseEntryInfo.getOrgId());
        //如果扫描接收提交
        if (!org.springframework.util.CollectionUtils.isEmpty(warehouseEntryInfo.getSnList())) {
            map.put("sns", psWipInfoServiceImp.getSns(warehouseEntryInfo.getSnList()));
        }
        // 查询单据明细
        List<WarehouseEntryInfoDetailDTO> listDetail = warehouseEntryInfoRepository.selectWarehouseEntryInfoDetailByBillNo(map);
        if (CollectionUtils.isEmpty(listDetail)) {
            return;
        }

        // 根据明细包含的条码查询该单据下的在制信息
        List<PsWipInfo> listPsWipInfo = dto.getCurWipInfoList();
        if (listPsWipInfo.size() != listDetail.size()) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, RetCode.BUSINESSERROR_MSGID);
        }

        if (!CollectionUtils.isEmpty(listPsWipInfo) && listPsWipInfo.size() > 0) {
            //2023.05.18-统一在接收的时候计算任务已完成数量
//            if (save) {
//                handleCompleteQty(warehouseEntryInfo, listPsWipInfo);
//            }
            // 工艺路径最后工序
            this.updatePsWipInfos(warehouseEntryInfo, listPsWipInfo, workStation);
        }
    }

    private void updatePsWipInfos(WarehouseEntryInfo warehouseEntryInfo, List<PsWipInfo> listPsWipInfo, String workStation) throws Exception {
        String routeId = CommonUtils.getRouteIdByWip(listPsWipInfo.get(NUM_ZERO));
        CtRouteDetail lastProcess = this.getLastProcess(routeId);
        if (lastProcess == null) {
            String[] param = {routeId};
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.LAST_OPERATION_NOT_FOUND, param);
        }
        //更新任务数量, 若提交数量+原有提交数量 = 任务数量，则将任务完工；
        if (null == warehouseEntryInfo) {
            throw new Exception();
        }
        List<PsWipInfo> updatePsWipInfos = new ArrayList<>(listPsWipInfo.size());
        List<PsScanHistory> insertWipScanHistorys = new ArrayList<>(listPsWipInfo.size());
        // 当前条码的工序不等于，条码的下一道工序的再进行更新
        for (PsWipInfo wipInfo : listPsWipInfo) {
            // 工位置为"0"
            if (!wipInfo.getCurrProcessCode().equals(lastProcess.getNextProcess())
                    && !wipInfo.getCraftSection().equals(lastProcess.getCraftSection())
                    && !wipInfo.getWorkStation().equals(workStation)) {
                // "P0243"
                if (lastProcess.getCraftSection().equals(Constant.STRING_CASTLE)) {
                    wipInfo.setCurrProcessCode(Constant.FLAG_N);
                    // "入库"
                    wipInfo.setCraftSection(Constant.WAREHOUSE_ENTRY);
                } else {
                    wipInfo.setCurrProcessCode(lastProcess.getNextProcess());
                    // "入库"
                    wipInfo.setCraftSection(lastProcess.getCraftSection());
                }

            }
            wipInfo.setWorkStation(workStation);
            updatePsWipInfos.add(wipInfo);

            // 获取待保存的扫描历史信息
            PsScanHistory scanHistory = new PsScanHistory();
            BeanUtils.copyProperties(wipInfo, scanHistory);
            scanHistory.setSmtScanId(java.util.UUID.randomUUID().toString()); // 设置主键
            scanHistory.setSourceSysName(Constant.WAREHOUSE_ENTRY);
            insertWipScanHistorys.add(scanHistory);
        }
        // 批量更新在制表
        if (updatePsWipInfos.size() <= 0) {
            return;
        }
        psWipInfoService.updatePsWipInfoByScanBatch(listPsWipInfo);
        batchInsertPsScanHistory(insertWipScanHistorys);
    }

    //处理投入产出
    private void handleCompleteQty(String prodplanId, BigDecimal qty) throws Exception {
        Map<String, BigDecimal> prodplanIdAndQtyMap = new HashMap<>();
        prodplanIdAndQtyMap.put(prodplanId, qty);
        PlanscheduleRemoteService.batchHandleCompleteQty(prodplanIdAndQtyMap);
    }

    /**
     * 获取工艺路径中最后一道工序
     *
     * @param routeId
     * @return CtRouteDetail
     * @throws Exception
     * @autchor 6092002408
     * @date 2018-10-19
     */
    @Override
    public CtRouteDetail getLastProcess(String routeId) throws Exception {
        CtRouteDetail routeDetail = null;
        List<CtRouteDetail> listRouteDetail = this.getListCtRouteDetail(routeId);
        if (!CollectionUtils.isEmpty(listRouteDetail)) {
            for (CtRouteDetail detail : listRouteDetail) {
                // 取最后工序
                if (Constant.FLAG_Y.equals(detail.getLastProcess())) {
                    routeDetail = detail;
                    break;
                }
            }
        }
        return routeDetail;
    }

    /**
     * 获取工艺明细信息
     *
     * @param routeId
     * @return List<CtRouteDetail>
     * @throws RouteException
     * @throws JsonProcessingException
     * @throws IOException
     * @autchor 6092002408
     * @date 2018-10-19
     */
    public List<CtRouteDetail> getListCtRouteDetail(String routeId)
            throws RouteException, JsonProcessingException, IOException {
        List<CtRouteDetail> listDetail = new ArrayList<>();
        Map<String, Object> map = new HashMap<String, Object>();
        // 设置查询条件
        map.put("routeId", routeId);
        String params = JacksonJsonConverUtil.beanToJson(map);
        Map<String, String> headerParamsMap = MESHttpHelper.getHttpRequestHeader();
        // 点对点调用服务
        String serviceName = MicroServiceNameEum.CRAFTTECH;
        String version = MicroServiceNameEum.VERSION;
        String sendType = MicroServiceNameEum.SENDTYPEGET;
        String getUrl = "/CT/CtRouteDetail";

        String getresult = MicroServiceRestUtil.invokeService(serviceName, version, sendType, getUrl, params,
                headerParamsMap);
        JsonNode json = JacksonJsonConverUtil.getMapperInstance().readTree(getresult);
        String bo = json.get(MpConstant.JSON_BO).toString();
        if (StringHelper.isNotEmpty(bo)) {
            listDetail = JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<ArrayList<CtRouteDetail>>() {
            });
        }

        return listDetail;
    }

    /**
     * 入库单号
     *
     * @param factoryId
     * @return
     */
    @Override
    public String getRKBillNo(String factoryId) {
        Map<String, String> record = new HashMap<>(1);
        record.put("factoryId", factoryId);
        return warehouseEntryInfoRepository.getRKBillNo(record);
    }

    /**
     * 返修入库单号
     *
     * @param factoryId
     * @return
     */
    @Override
    public String getFXRKBillNo(String factoryId) throws Exception {
        Map<String, String> record = new HashMap<>(1);
        record.put("factoryId", factoryId);
        return warehouseEntryInfoRepository.getFXRKBillNo(record);
    }

    /**
     * 查询入库明细信息
     *
     * @param list
     * @return
     */
    @SuppressWarnings("unused")
    @Override
    public RetCode getWeDetailInfoBySn(List<WarehouseEntryInfoDTO> list) {

        RetCode retCode = new RetCode(RetCode.BUSINESSERROR_CODE, RetCode.BUSINESSERROR_MSGID);
        if (CollectionUtils.isEmpty(list)) {
            retCode.setMsg(CommonUtils.getLmbMessage(MessageId.BOX_CONTENTS_ARE_EMPTY));
            return retCode;
        }
        List<List<WarehouseEntryInfoDTO>> splitList = CommonUtils.splitList(list);
        StringBuilder msgSb = new StringBuilder();
        for (List<WarehouseEntryInfoDTO> innerList : splitList) {
            StringBuilder snSb = new StringBuilder();
            for (WarehouseEntryInfoDTO info : innerList) {
                snSb.append(Constant.SINGLE_QUOTE).append(info.getSn()).append(Constant.SINGLE_QUOTE).append(Constant.COMMA);
            }
            snSb.replace(snSb.length() - 1, snSb.length(), "");
            Map<String, Object> queryMap = new HashMap<String, Object>();
            queryMap.put("sn", snSb.toString());
            queryMap.put("factoryId", list.get(NUM_ZERO).getFactoryId());
            List<WarehouseEntryInfoDetailDTO> listDetail = warehouseEntryInfoRepository.selectDetailInfoBySn(queryMap);
            if (listDetail.size() < innerList.size()) {
                // 查找不存在的SN信息
                String snNotExsist = fingSn(listDetail, innerList);
                msgSb.append(snNotExsist);
            }
        }
        if (0 != msgSb.length()) {
            retCode.setMsg(msgSb.toString() + BusinessConstant.NOT_OUT_WAREHOUSE);
            return retCode;
        }
        retCode = new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID);
        return retCode;

    }

    /**
     * 查找未入库的条码
     *
     * @param listDetail
     * @param innerList
     * @return
     */
    private String fingSn(List<WarehouseEntryInfoDetailDTO> listDetail, List<WarehouseEntryInfoDTO> innerList) {

        Map<String, Integer> map = new HashMap<String, Integer>(listDetail.size() + innerList.size());
        StringBuilder diffSn = new StringBuilder();
        for (WarehouseEntryInfoDTO query : innerList) {
            map.put(query.getSn(), 1);
        }
        for (WarehouseEntryInfoDetailDTO result : listDetail) {
            Integer cc = map.get(result.getSn());
            if (cc != null) {
                map.put(result.getSn(), ++cc);
                continue;
            }
            map.put(result.getSn(), 1);
        }
        for (Map.Entry<String, Integer> entry : map.entrySet()) {
            if (entry.getValue() == 1) {
                diffSn.append(entry.getKey()).append(Constant.COMMA);
            }
        }
        String returnStr = "";
        if (null != diffSn) {
            diffSn.replace(diffSn.length() - 1, diffSn.length(), "");
            returnStr = diffSn.toString();
        }
        return returnStr;
    }


    /**
     * 新增转交单并获取打印信息
     *
     * @param dto
     * @param factoryId
     * @return
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public String insertTransfer(WarehouseEntryInfoQueryDTO dto, String factoryId, String entityId, String empNo) throws Exception {
        WarehouseEntryInfo warehouseEntryInfo = WarehouseEntryInfoAssembler.convertEntityByWarehouseEntryInfoQueryDTO(dto);
        warehouseEntryInfo.setWarehouseEntryId(UUID.randomUUID().toString());
        //生成单据号 B+factoryId+yymmdd+####
        StringBuilder billNo = new StringBuilder(BusinessConstant.STRING_B);
        billNo.append(factoryId);
        Calendar date = Calendar.getInstance();
        String year = String.format(Locale.CHINA, MpConstant.YEAR_FORMAT, date.get(Calendar.YEAR)).substring(2, 4);
        String month = String.format(Locale.CHINA, MpConstant.MOUTH_OR_DATE_FORMAT, date.get(Calendar.MONTH) + 1);
        String day = String.format(Locale.CHINA, MpConstant.MOUTH_OR_DATE_FORMAT, date.get(Calendar.DAY_OF_MONTH));
        billNo.append(year).append(month).append(day);
        String redisId = getRedisId(RedisKeyConstant.INSERTTRANSFER_REDISKEY + billNo, NumConstant.NUM_FOUR);
        billNo.append(redisId);
        warehouseEntryInfo.setStatus(Constant.WARHOUSE_DETAIL_STATUS_ZERO);
        warehouseEntryInfo.setBillNo(billNo.toString());
        if (StringUtils.isNotBlank(empNo)) {
            warehouseEntryInfo.setCreateBy(empNo);
            warehouseEntryInfo.setLastUpdatedBy(empNo);
        }
        if (StringUtils.isNotBlank(factoryId)) {
            warehouseEntryInfo.setFactoryId(new BigDecimal(factoryId));
        }
        if (StringUtils.isNotBlank(entityId)) {
            warehouseEntryInfo.setEntityId(new BigDecimal(entityId));
        }
        // 转库单单据类型改为6
        warehouseEntryInfo.setBillType(MpConstant.BILL_TYPE_TRANSFER);
        insertWarehouseEntryInfo(warehouseEntryInfo);   //插入头信息

        List<PsWipInfo> psWipInfoList = dto.getPsWipInfoList();
        List<WarehouseEntryDetailDTO> warehouseEntryDetailList = new ArrayList<>();

        for (int i = 0; i < psWipInfoList.size(); i++) {
            PsWipInfo psWinInfo = psWipInfoList.get(i);

            WarehouseEntryDetailDTO warehouseEntryDetail = new WarehouseEntryDetailDTO();
            warehouseEntryDetail.setSn(psWinInfo.getSn());
            warehouseEntryDetail.setBillNo(warehouseEntryInfo.getBillNo());
            warehouseEntryDetail.setWorkOrderNo(psWinInfo.getWorkOrderNo());
            warehouseEntryDetail.setProdplanId(warehouseEntryInfo.getProdplanId());
            if (dto.isByBoxFlag()) {
                warehouseEntryDetail.setProdplanId(psWinInfo.getAttribute1());
            }
            warehouseEntryDetail.setLpn(psWinInfo.getLpn());
            warehouseEntryDetail.setWarehouseEntryDetailId(UUID.randomUUID().toString());
            if (StringUtils.isNotBlank(empNo)) {
                warehouseEntryDetail.setLastUpdatedBy(empNo);
                warehouseEntryDetail.setCreateBy(empNo);
            }
            if (StringUtils.isNotBlank(factoryId)) {
                warehouseEntryDetail.setFactoryId(new BigDecimal(factoryId));
            }
            if (StringUtils.isNotBlank(entityId)) {
                warehouseEntryDetail.setEntityId(new BigDecimal(entityId));
            }

            warehouseEntryDetailList.add(warehouseEntryDetail);
        }
        insertWarehouseEntryDetailBatch(warehouseEntryDetailList); //插入detail表

        return billNo.toString();
    }

    /**
     * 新增转交送货单并获取打印信息
     *
     * @param dto
     * @param factoryId
     * @return
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public String insertDeliver(WarehouseEntryInfoQueryDTO dto, String factoryId, String entityId, String empNo) throws Exception {
        WarehouseEntryInfo warehouseEntryInfo = WarehouseEntryInfoAssembler.convertEntityByWarehouseEntryInfoQueryDTO(dto);
        warehouseEntryInfo.setWarehouseEntryId(UUID.randomUUID().toString());
        //生成单据号 P+yymmdd+factoryId+###
        StringBuilder billNo = new StringBuilder(BusinessConstant.FLAG_P);
        Calendar date = Calendar.getInstance();
        String year = String.format(Locale.CHINA, MpConstant.YEAR_FORMAT, date.get(Calendar.YEAR)).substring(2, 4);
        String month = String.format(Locale.CHINA, MpConstant.MOUTH_OR_DATE_FORMAT, date.get(Calendar.MONTH) + 1);
        String day = String.format(Locale.CHINA, MpConstant.MOUTH_OR_DATE_FORMAT, date.get(Calendar.DAY_OF_MONTH));
        billNo.append(year).append(month).append(day);
        String redisId = getRedisId(RedisKeyConstant.INSERTDELIVER_REDISKEY + billNo, 3);
        billNo.append(factoryId);
        billNo.append(redisId);
        if (StringUtils.isNotBlank(factoryId)) {
            warehouseEntryInfo.setFactoryId(new BigDecimal(factoryId));
        }
        if (StringUtils.isNotBlank(entityId)) {
            warehouseEntryInfo.setEntityId(new BigDecimal(entityId));
        }
        if (StringUtils.isNotBlank(empNo)) {
            warehouseEntryInfo.setCreateBy(empNo);
            warehouseEntryInfo.setLastUpdatedBy(empNo);
        }
        warehouseEntryInfo.setBillNo(billNo.toString());
        // 送货单单据号改为7
        warehouseEntryInfo.setBillType(MpConstant.BILL_TYPE_DELIVER);
        warehouseEntryInfo.setStatus(Constant.WARHOUSE_DETAIL_STATUS_ZERO);
        insertWarehouseEntryInfo(warehouseEntryInfo);   //插入头信息

        List<PsWipInfo> psWipInfoList = dto.getPsWipInfoList();
        List<WarehouseEntryDetailDTO> warehouseEntryDetailList = new ArrayList<>();

        for (int i = 0; i < psWipInfoList.size(); i++) {
            PsWipInfo psWinInfo = psWipInfoList.get(i);

            WarehouseEntryDetailDTO warehouseEntryDetail = new WarehouseEntryDetailDTO();
            warehouseEntryDetail.setWarehouseEntryDetailId(UUID.randomUUID().toString());
            warehouseEntryDetail.setBillNo(warehouseEntryInfo.getBillNo());
            warehouseEntryDetail.setSn(psWinInfo.getSn());
            warehouseEntryDetail.setWorkOrderNo(psWinInfo.getWorkOrderNo());
            warehouseEntryDetail.setProdplanId(warehouseEntryInfo.getProdplanId());
            if (dto.isByBoxFlag()) {
                warehouseEntryDetail.setProdplanId(psWinInfo.getAttribute1());
            }
            warehouseEntryDetail.setLpn(psWinInfo.getLpn());
            if (StringUtils.isNotBlank(factoryId)) {
                warehouseEntryDetail.setFactoryId(new BigDecimal(factoryId));
            }
            if (StringUtils.isNotBlank(entityId)) {
                warehouseEntryDetail.setEntityId(new BigDecimal(entityId));
            }
            if (StringUtils.isNotBlank(empNo)) {
                warehouseEntryDetail.setCreateBy(empNo);
                warehouseEntryDetail.setLastUpdatedBy(empNo);
            }

            warehouseEntryDetailList.add(warehouseEntryDetail);
        }
        insertWarehouseEntryDetailBatch(warehouseEntryDetailList); //插入detail表
        return billNo.toString();
    }

    /**
     * 生成length位序列码(最大五位)
     *
     * @param length （1-5）
     * @param key
     * @return
     * @throws Exception
     */
    private String getRedisId(String key, int length) throws Exception {
        String lockKey = this.getClass().getSimpleName() + RedisKeyConstant.GENERATETRANSFER_LOCK_REDISKEY;
        RedisLock redisLock = new RedisLock(lockKey);
        String redisId = null;
        try {
            boolean lock = redisLock.lock();
            if (!lock) {
                throw new Exception(CommonUtils.getLmbMessage(MessageId.FAILED_TO_GET_REDIS_LOCK));
            }
            Integer maxCountValue = findMaxCount(key);
            RedisCacheUtils.set(key, maxCountValue + 1, DAY_EXPIRE_TIME);
            StringBuilder serialCode = new StringBuilder(MpConstant.FILE_ZERO);
            serialCode = serialCode.append(maxCountValue);
            redisId = serialCode.substring(serialCode.length() - length, serialCode.length());
        } finally {
            redisLock.unlock();
        }
        return redisId;
    }

    /**
     * 获取最大的key对应值
     *
     * @param key
     * @return
     */
    private Integer findMaxCount(String key) {
        Integer maxCountValue = RedisCacheUtils.get(key, Integer.class);
        if (maxCountValue == null) {
            return Constant.INT_0;
        }
        return maxCountValue;
    }

    /**
     * 查询生产入库单明细
     */
    @Override
    public List<WarehouseEntryInfoDTO> selectWarehouseEntryDetailInfo(WarehouseEntryInfoDTO dto) {
        return warehouseEntryInfoRepository.selectWarehouseEntryDetailInfo(dto);
    }

    /**
     * 校验条码是否在入库单中
     *
     * @param
     * @return
     */
    @Override
    public ServiceData verifyWarehouseSn(String billNo, String sns) throws Exception {
        ServiceData ret = new ServiceData();
        ret.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, RetCode.BUSINESSERROR_CODE));
        WarehouseEntryInfoDTO dto = new WarehouseEntryInfoDTO();
        dto.setBillNo(billNo);
        dto.setSns(sns);
        dto.setEqStatus(Constant.STRING_0);
        List<WarehouseEntryInfoDTO> snList = warehouseEntryInfoRepository.selectWarehouseEntryDetailInfo(dto);
        if (CollectionUtils.isEmpty(snList)) {
            RetCode retCode = new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.SN_NOT_IN_BILL);
            retCode.setMsg(CommonUtils.getLmbMessage(MessageId.SN_NOT_IN_BILL, sns));
            ret.setCode(retCode);
            return ret;
        } else {
            ret.setBo(snList);
            ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        }
        // 判断条码imu是否为42入库，否则报错
        Map<String, Object> map = CommonUtils.object2Map(dto);
        String sn = StringUtils.replace(sns, SINGLE_QUOTE, "");
        if (StringUtils.isNotEmpty(sn)) {
            map.put("prodplanId", new BigDecimal(sn.substring(0, 7)));
            map.put("boardSn", new BigDecimal(sn.substring(7, 12)));
            map.put("imuId", new BigDecimal(NumConstant.STR_FOURTY_TWO));
            map.put("page", NUM_ONE);
            map.put("rows", NUM_TEN);
            Page<BoardOnline> boardOnlineList = datawbRemoteService.getBoardOnlineBatch(map);
            if(boardOnlineList == null || CollectionUtils.isEmpty(boardOnlineList.getRows())){
                RetCode retCode = new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.SN_IS_NOT_IN_RIGHT_IMU);
                retCode.setMsg(CommonUtils.getLmbMessage(MessageId.SN_IS_NOT_IN_RIGHT_IMU,sn));
                ret.setCode(retCode);
            }
        }
        return ret;
    }

    //获取入库单数据
    private List<WarehouseEntryInfoDTO> selectWarehouseEntryDetailInfo(String billNo, String status) {
        WarehouseEntryInfoDTO dto = new WarehouseEntryInfoDTO();
        dto.setBillNo(billNo);
        dto.setStatus(status);
        return warehouseEntryInfoRepository.selectWarehouseEntryDetailInfo(dto);
    }

    // 单据中是否有接收的条码
    @Override
    public boolean isExistRcvSn(String billNo) {
        if (StringHelper.isEmpty(billNo) || CollectionUtils.isEmpty(selectWarehouseEntryDetailInfo(billNo, CommonConst.WAREHOUSE_DATAIL_SATATUS_SUBMIT))) {
            return false;
        }
        return true;
    }

    //单据中是否有接收的条码,  返回值为  无待接收的单据号
    @Override
    public String isExistRcvSn(List<WarehouseEntryInfo> list) {
        String billNos = "";
        for (WarehouseEntryInfo warehouseEntryInfo : list) {
            if (!isExistRcvSn(warehouseEntryInfo.getBillNo())) {
                billNos += warehouseEntryInfo.getBillNo() + Constant.SYMBOL_COMMA;
            }
        }
        return StringHelper.isEmpty(billNos) ? "" : billNos.substring(0, billNos.length() - 1);
    }

    //获取有扫描接收的单据
    @Override
    public String getExistRcvBill(List<WarehouseEntryInfo> list) {
        String billNos = "";
        for (WarehouseEntryInfo warehouseEntryInfo : list) {
            if (isExistRcvSn(warehouseEntryInfo.getBillNo())) {
                billNos += warehouseEntryInfo.getBillNo() + Constant.SYMBOL_COMMA;
            }
        }
        return StringHelper.isEmpty(billNos) ? "" : billNos.substring(0, billNos.length() - 1);
    }

    /**
     * 更新生产入库单明细
     */
    @Override
    public int updateWarehouseEntryDetails(WarehouseEntryInfoDTO dto) {
        return warehouseEntryInfoRepository.updateWarehouseEntryDetails(dto);
    }

    /**
     * 更新生产入库单head
     */
    @Override
    public int selectWarehouseEntryByLPN(WarehouseEntryInfoDTO dto) {
        return warehouseEntryInfoRepository.updateWarehouseEntryInfos(dto);
    }

    /**
     * 根据箱号查询入库单
     *
     * @param lpn the lpn
     * @return the string
     */
    @Override
    public List<String> selectWarehouseEntryByLPN(String lpn) {
        // 拼接in条件
        lpn = Arrays.stream(lpn.split(Constant.COMMA))
                .collect(Collectors.joining("','", Constant.SINGLE_QUOTE, Constant.SINGLE_QUOTE));

        List<String> lpns = warehouseEntryInfoRepository.selectWarehouseEntryByLPN(lpn);

        if (CollectionUtils.isEmpty(lpns)) {
            return null;
        }

        // 有入库记录的箱号，并去重
        List<String> lpnWithEntry = lpns.stream().distinct().collect(Collectors.toList());

        return lpnWithEntry;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<WarehouseEntryDetailStatisticsDTO> statisticWarehouseEntryDetailByLpn(List<String> lpnList) throws Exception {
        //根据配送对象进行统计实际数量
        List<WarehouseEntryDetailStatisticsDTO> statisticsDTOList = warehouseEntryInfoRepository.statisticWarehouseEntryInfoDetailByLpn(lpnList, STR_ONE);
        List<WarehouseEntryInfo> warehouseEntryInfoList = warehouseEntryInfoRepository.getWarehouseEntryInfoDTOByLpn(lpnList);
        warehouseEntryInfoList.stream().forEach(warehouseEntryInfo -> {
            //根据LPN找到入库单
            WarehouseEntryDetailStatisticsDTO entryDetailStatisticsDTO = statisticsDTOList.stream().filter(statisticsDTO ->
                    statisticsDTO.getBillNo().equals(warehouseEntryInfo.getBillNo())).findFirst().orElse(null);
            if (entryDetailStatisticsDTO != null) {
                warehouseEntryInfo.setCommitedQty(new BigDecimal(entryDetailStatisticsDTO.getCount()));
            }
        });
        //进行ERP会写
        receiveWarehouseEntryInfoBatch(warehouseEntryInfoList, Constant.UPDATE);
        //更新入库单详情status为已关闭
        for (WarehouseEntryInfo warehouseEntryInfo : warehouseEntryInfoList) {
            List<String> detailIdList = warehouseEntryInfoRepository.selectWarehouseEntryInfoDetailIdByBillNo(warehouseEntryInfo.getBillNo());
            WarehouseEntryInfoDTO entryInfoDTO = null;
            // 查询生产入库单head的明细是否都已关闭,全部关闭则更新入库单头
            if (!CollectionUtils.isEmpty(detailIdList)) {
                entryInfoDTO = new WarehouseEntryInfoDTO();
                entryInfoDTO.setWarehouseEntryDetailIds(getInStrs(detailIdList));

                entryInfoDTO.setStatus(STR_ONE); //已关闭(1)
                warehouseEntryInfoRepository.updateWarehouseEntryDetails(entryInfoDTO);
            }
            entryInfoDTO = new WarehouseEntryInfoDTO();
            entryInfoDTO.setWarehouseEntryId(warehouseEntryInfo.getWarehouseEntryId());
            entryInfoDTO.setStatus(NumConstant.STR_THREE);// 状态-已关闭(3)

            Boolean result = warehouseEntryInfoRepository.selectCountWarehouseEntryDetailInfo(entryInfoDTO);
            //result = 0 全部关闭
            if (!result) {
                warehouseEntryInfoRepository.updateWarehouseEntryInfos(entryInfoDTO);
            }
        }
        return null;
    }

    //拼接in查询字符串
    private String getInStrs(List<String> detailIdList) {
        StringBuilder sb = new StringBuilder();
        for (String str : detailIdList) {
            sb.append(Constant.SINGLE_QUOTE).append(str).append(Constant.SINGLE_QUOTE).append(Constant.COMMA);
        }
        String sns = sb.toString();
        sns = sns.substring(0, sns.length() - 1);
        return sns;
    }

    /**
     * 是否有接收，入库单的权限
     *
     * @param userId
     * @return
     */
    @Override
    public boolean hasRcvStockRight(String userId) throws Exception {
        SysLookupTypesDTO sysLookupTypesDTO = BasicsettingRemoteService.getSysLookUpValue(Constant.LOOK_UP_TYPE_STOCK_RCV_RIGHT, Constant.LOOK_UP_CODE_STOCK_RCV_RIGHT);
        //没配置字典或配置字典为空则直接返回空
        if (StringHelper.isEmpty(userId) || null == sysLookupTypesDTO || StringHelper.isEmpty(sysLookupTypesDTO.getLookupMeaning())) {
            return false;
        }
        if (sysLookupTypesDTO.getLookupMeaning().contains(userId)) {
            return true;
        }
        return false;
    }

    /**
     * 是否有拒绝入库单的权限
     *
     * @param userId
     * @return
     */
    @Override
    public boolean hasRejectStockRight(String userId) throws Exception {
        SysLookupTypesDTO sysLookupTypesDTO = BasicsettingRemoteService.getSysLookUpValue(Constant.LOOK_UP_TYPE_STOCK_RCV_RIGHT, Constant.LOOK_UP_CODE_STOCK_REJECT_RIGHT);
        //没配置字典或配置字典为空则直接返回空
        if (StringHelper.isEmpty(userId) || null == sysLookupTypesDTO || StringHelper.isEmpty(sysLookupTypesDTO.getLookupMeaning())) {
            return false;
        }
        if (sysLookupTypesDTO.getLookupMeaning().contains(userId)) {
            return true;
        }
        return false;
    }


    //入库单各种操作权限
    @Override
    public PermissionControlForStockDTO permissionControlForStock(String userId) throws Exception {
        PermissionControlForStockDTO dto = new PermissionControlForStockDTO();
        List<SysLookupTypesDTO> list = BasicsettingRemoteService.getSysLookUpValue(Constant.LOOK_UP_TYPE_STOCK_RCV_RIGHT);
        if (CollectionUtils.isEmpty(list)) {
            return dto;
        }
        // 根据扩展信息获取各操作权限对应的数据字典子项
        Map<String, List<SysLookupTypesDTO>> sysMap = list.stream()
                .filter(item -> StringUtils.isNotBlank(item.getAttribute1()))
                .filter(item -> StringUtils.isNotBlank(item.getLookupMeaning()))
                .collect(Collectors.groupingBy(SysLookupTypesDTO::getAttribute1));
        String rejectList = StringUtils.EMPTY;
        String rcvList = StringUtils.EMPTY;
        String scanRcvList = StringUtils.EMPTY;
        for (Map.Entry<String, List<SysLookupTypesDTO>> entry : sysMap.entrySet()) {
            String key = entry.getKey();
            List<SysLookupTypesDTO> value = entry.getValue();
            switch (key) {
                //拒收
                case Constant.REJECT_RIGHT:
                    rejectList = value.stream().map(SysLookupTypesDTO::getLookupMeaning)
                            .collect(Collectors.joining(Constant.COMMA));
                    break;
                //接收
                case Constant.RCV_RIGHT:
                    rcvList = value.stream().map(SysLookupTypesDTO::getLookupMeaning)
                            .collect(Collectors.joining(Constant.COMMA));
                    break;
                // 扫描接收
                case Constant.SCANRECEIVE_RIGHT:
                    scanRcvList = value.stream().map(SysLookupTypesDTO::getLookupMeaning)
                            .collect(Collectors.joining(Constant.COMMA));
                    break;
                default:
            }
        }
        //拒收
        if (StringUtils.isNotBlank(rejectList) && rejectList.contains(userId)) {
            dto.setReject(true);
        }
        //接收
        if (StringUtils.isNotBlank(rcvList) && rcvList.contains(userId)) {
            dto.setReceive(true);
        }
        //扫描接收
        if (StringUtils.isNotBlank(scanRcvList) && scanRcvList.contains(userId)) {
            dto.setScanReceive(true);
        }
        return dto;
    }


    /**
     * 批量接收入库单
     * 1、更新在制表状态
     * 2、调用erp工序移动接口
     * 3、调用erp工序完工接口 更新单据状态
     *
     * @return
     * @throws Exception
     * @autchor 6092002408
     * @date 2018-10-19
     */
    @Override
    public void scanReceiveWarehouseEntryInfo(WarehouseEntryInfo warehouseEntryInfo, String userId) throws Exception {
        // 待接收的单据
        SysLookupTypesDTO sysLookupTypesDTO = queryLookupType(CommonConst.DIP_TRANSFER_WAREHOUSE_SYS_TYPE, CommonConst.ON_SHELF_PROCESS_CODE);
        if (sysLookupTypesDTO == null) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SYS_LOOK_PARAM_NOT_CONFIG);
        }
        if (!warehouseEntryInfo.getBillNo().isEmpty()) {
            warehouseEntryInfo.setLastUpdatedBy(StringHelper.isNotEmpty(userId) ? userId : warehouseEntryInfo.getLastUpdatedBy());
//            checkWarehouseEntryInfo(warehouseEntryInfo, sysLookupTypesDTO);
        }
    }

    /**
     * 根据箱号获取条码数据
     *
     * @param billNo
     * @param lpn
     * @return
     * @throws Exception
     */
    @RecordLogAnnotation("根据箱号获取条码数据")
    @Override
    public ServiceData getBillSnByLpn(String billNo, String lpn) throws Exception {
        ServiceData serviceData = new ServiceData();
        //获取箱内容数据
        if (StringHelper.isEmpty(lpn)) {
            RetCode retCode = new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.SN_NOT_IN_BILL);
            retCode.setMsg(CommonUtils.getLmbMessage(MessageId.SN_NOT_IN_BILL, MessageId.BOX_IS_NULL));
            serviceData.setCode(retCode);
            return serviceData;
        }
        List<WarehouseEntryInfoDetailDTO> detailDTOS = warehouseEntryInfoRepository.getDetailByLPN(lpn, billNo);
        // 判断是否都在对应单据里
        if (CollectionUtils.isEmpty(detailDTOS)) {
            RetCode retCode = new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.BOX_CONTENT_IS_NULL);
            serviceData.setCode(retCode);
            return serviceData;
        }
        RetCode retCode = new RetCode(RetCode.SUCCESS_CODE, RetCode.BUSINESSERROR_MSGID);
        serviceData.setCode(retCode);
        serviceData.setBo(detailDTOS);
        return serviceData;
    }

    /**
     * 校验单据号中是否有被扫描接收了的；
     *
     * @param list
     * @return 被扫描接收了的单据
     */
    @Override
    public String checkBillIsPartReceive(List<WarehouseEntryInfo> list) {
        return warehouseEntryInfoRepository.checkBillIsPartReceive(list);
    }

    /*3、提交时，需锁定需求单，判断提交数量是否大于需求单可入库数量
    （根据需求单查询warehouse_requirement_info得到需求单原始数量，
    再查询warehouse_requirement_detail表计算已入库数量，
    可入库数量=需求单原始数量-已入库数量），大于则报错，否则继续下一步操作*/

    /**
     * 需求单校验， 校验通过则锁定需求单
     *
     * @param reqBillNo 需求单号， commitQty 提交数量
     * @return 校验成功返回""; 否则返回错误信息（已做双语化）
     */
    private String validAndLockWarehouseReq(String reqBillNo, BigDecimal commitQty) {
        String errorMsgs = "";
        //  2020/7/16   获取需求单的原始数量
        WarehouseRequirementInfo warehouseRequirementInfo = warehouseRequirementInfoRepository.getWarehouseReqByBillNo(reqBillNo);
        if (null == warehouseRequirementInfo || null == warehouseRequirementInfo.getQty()) {
            return CommonUtils.getLmbMessage(MessageId.REQ_BILL_NO_IS_NULL);
        }
        //  2020/7/16  获取已入库数量
        Integer warehousedQty = warehouseRequirementInfoRepository.getWarehouseDetailQtyByBillNo(reqBillNo);
        warehousedQty = null == warehousedQty ? 0 : warehousedQty;
        //  2020/7/16  计算可入库数量=原始数量-已入库数量
        int maxCommitQty = warehouseRequirementInfo.getQty() - warehousedQty;
        //  2020/7/16  判断是提交数量大于可入库数量；
        if (null == commitQty || commitQty.compareTo(new BigDecimal(maxCommitQty)) > 0) {
            return CommonUtils.getLmbMessage(MessageId.COMMIT_QTY_MORETHAN_MAX);
        }
        return errorMsgs;
    }


    //完工需求单
    private void checkAndDoneReqStatus(String warehouseBillNo) throws Exception {
        //通过入库单查出需求单， 然后获取需求单下所有已经接收的数量
        Integer reqBillNoRcvQty = warehouseRequirementInfoRepository.getWarehouseReceiveQtyByBillNo(warehouseBillNo);
        reqBillNoRcvQty = null == reqBillNoRcvQty ? 0 : reqBillNoRcvQty;
        //  2020/7/16   获取需求单的原始数量
        WarehouseRequirementInfo warehouseRequirementInfo = warehouseRequirementInfoRepository.getReqByStockInNo(warehouseBillNo);
        if (null == warehouseRequirementInfo || null == warehouseRequirementInfo.getQty()) {
            return;
        }
        if (reqBillNoRcvQty.compareTo(warehouseRequirementInfo.getQty()) == 0) {
            //  2020/7/18  修改需求头表状态
            WarehouseRequirementInfo record = new WarehouseRequirementInfo();
            record.setBillNo(warehouseRequirementInfo.getBillNo());
            record.setStatus(CommonConst.WAREHOUSE_REQUIREMENT_INFO_DONE);
            warehouseRequirementInfoRepository.updateRequirmentInfo(record);
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ServiceData dealTridimensionalWarehousEntry(StandardMouldStoreHouseDTO dto) throws Exception {
        ServiceData serviceData = new ServiceData();

        if (StringUtils.isEmpty(dto.getTransNum())) {
            serviceData.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.WAREHOUSE_BILLNO_IS_NULL));
            return generateServiceData(serviceData);
        }
        List<StandardMouldContainerDTO> smcdList = dto.getPltList();
        if (CollectionUtils.isEmpty(smcdList)) {
            serviceData.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.PARMS_ERR));
            return generateServiceData(serviceData);
        }

        // 入库单处理逻辑
        if (RK_STR.equals(dto.getTransNum().substring(NUM_ZERO, NUM_TWO))) {
            serviceData = this.dealTridimensionalRk(dto, smcdList);
        }
        // 转库单处理逻辑
        else if (ZKP_STR.equals(dto.getTransNum().substring(NUM_ZERO, NUM_THREE))) {
            serviceData = this.dealTridimensionalZpk(dto, smcdList);
        }

        return generateServiceData(serviceData);
    }

    // 入库单处理逻辑
    public ServiceData dealTridimensionalRk(StandardMouldStoreHouseDTO dto, List<StandardMouldContainerDTO> smcdList) throws Exception {
        ServiceData serviceData = new ServiceData();

        Map<String, Object> record = new HashMap<>();
        record.put("billNo", dto.getTransNum());
        List<WarehouseEntryInfoDetailDTO> list = warehouseEntryInfoRepository.selectWarehouseEntryInfoDetailByBillNo(record);
        // 该入库单不存在，请确认！
        if (CollectionUtils.isEmpty(list)) {
            serviceData.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.WAREHOUSE_BILLNO_NOT_EXIST));
            return generateServiceData(serviceData);
        }
        // 该入库单已接收，请确认
        if (StringUtils.equals(list.get(NUM_ZERO).getStatus(), STR_ONE)) {
            serviceData.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.WAREHOUSE_BILLNO_ISRECEIVED));
            return generateServiceData(serviceData);
        }

        Map<String, List<WarehouseEntryInfoDetailDTO>> warHouseMap = new HashMap<>();
        // 按容器分组
        setWarHouseMap(warHouseMap, list);
        for (StandardMouldContainerDTO standardMouldContainerDTO : smcdList) {
            String lpn = standardMouldContainerDTO.getContainerID();
            List<WarehouseEntryInfoDetailDTO> tempDetailList = warHouseMap.get(lpn);
            if (CollectionUtils.isEmpty(tempDetailList)) {
                serviceData.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.LPN_NO_WARHOUSE_RECORD));
                serviceData.getCode().setMsg(CommonUtils.getLmbMessage(MessageId.LPN_NO_WARHOUSE_RECORD, lpn));
                return generateServiceData(serviceData);
            }
            // 判断行表记录是否是“已提交”，如果不是则报错“XXX容器状态不为待入库，请确认！”
            Boolean validateResult = tempDetailList.stream().allMatch(tempDto -> Constant.WARHOUSE_DETAIL_STATUS_ZERO.equals(tempDto.getDetailStatus()));
            if (!validateResult) {
                serviceData.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.LPN_STATUS_IS_NOT_IN_STORAGE));
                serviceData.getCode().setMsg(CommonUtils.getLmbMessage(MessageId.LPN_STATUS_IS_NOT_IN_STORAGE, lpn));
                return generateServiceData(serviceData);
            }
        }

        // 获取所有sn
        List<String> snList = getSnList(smcdList);
        // 更新在制表wip_info中work_station为在库（数据字典1218）。
        List<List<String>> splitList = CommonUtils.splitList(snList, Constant.BATCH_SIZE_NINE_HUNDRED);
        updateWipInfoBySnList(splitList);

        // 调用erp接口写交易完工(/receiveWarehouseEntryInfoBatch)，注意写ERP时以实际入库数量为准
        WarehouseEntryInfo warehouseEntryInfo = new WarehouseEntryInfo();
        generateWarehouseEnteyInfo(dto, list, snList, warehouseEntryInfo);
        // 调erp做移库或者移动完工操作
        invokeErpMoveOrComplete(warehouseEntryInfo);

        // 更新入库单行状态为已关闭（status=1）,如果入库单行表都已关闭则更新头表状态为已关闭（status=3）
        updateWarhouseEntryInfo(dto, splitList);
        return serviceData;
    }

    // 转库单处理逻辑
    public ServiceData dealTridimensionalZpk(StandardMouldStoreHouseDTO dto, List<StandardMouldContainerDTO> smcdList) throws Exception {
        ServiceData serviceData = new ServiceData();

        WarehouseEntryInfoDetailDTO warehouseEntryInfoDetailDTO = new WarehouseEntryInfoDetailDTO();
        warehouseEntryInfoDetailDTO.setApplicationNo(dto.getTransNum());
        List<WarehouseEntryInfoDetailDTO> list = ProductionDeliveryRemoteService.queryApplyOrderDetails(warehouseEntryInfoDetailDTO);
        // 该转库单不存在，请确认！
        if (CollectionUtils.isEmpty(list)) {
            serviceData.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.TRANSFER_BILLNO_NOT_EXIST));
            return generateServiceData(serviceData);
        }
        // 该转库单的转库类型不为“整机车间库-标模库”、“整机车间库-标模库”，请确认！
        if (!StringUtils.equals(list.get(NUM_ZERO).getApplicationType(), APPLICATION_TYPE_ZJ_TO_BM) &&
                !StringUtils.equals(list.get(NUM_ZERO).getApplicationType(), APPLICATION_TYPE_BM_TO_BM)) {
            serviceData.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.TRANSFER_APPLICATION_TYPE_NOT_ZJ_BM));
            return generateServiceData(serviceData);
        }
        // 该转库单已关闭，请确认！
        if (StringUtils.equals(list.get(NUM_ZERO).getHeadStatus(), CLOSED_ZH)) {
            serviceData.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.TRANSFER_BILLNO_CLOSED));
            return generateServiceData(serviceData);
        }

        Map<String, List<WarehouseEntryInfoDetailDTO>> warHouseMap = new HashMap<>();
        // 按容器分组
        setWarHouseMap(warHouseMap, list);
        for (StandardMouldContainerDTO standardMouldContainerDTO : smcdList) {
            String lpn = standardMouldContainerDTO.getContainerID();
            List<WarehouseEntryInfoDetailDTO> tempDetailList = warHouseMap.get(lpn);
            if (CollectionUtils.isEmpty(tempDetailList)) {
                serviceData.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.LPN_NO_TRANSFER_RECORD));
                serviceData.getCode().setMsg(CommonUtils.getLmbMessage(MessageId.LPN_NO_TRANSFER_RECORD, lpn));
                return generateServiceData(serviceData);
            }
            //判断行表记录是否是“已审批”，如果不是则报错“XXX容器状态不为已审批，请确认！”
            Boolean validateResult = tempDetailList.stream().allMatch(tempDto -> APPROVED.equals(tempDto.getStatus()));
            if (!validateResult) {
                serviceData.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.LPN_STATUS_IS_NOT_APPROVED));
                serviceData.getCode().setMsg(CommonUtils.getLmbMessage(MessageId.LPN_STATUS_IS_NOT_APPROVED, lpn));
                return generateServiceData(serviceData);
            }
        }

        // 获取所有sn
        List<String> snList = getSnList(smcdList);
        List<List<String>> splitList = CommonUtils.splitList(snList, Constant.BATCH_SIZE_NINE_HUNDRED);

        // 更新转库申请单行表状态为“已关闭”，如果该单据下所有行都已关闭则将头表置为“已关闭”
        for (List<String> tempList : splitList) {
            warehouseEntryInfoDetailDTO.setStatus(CLOSED_ZH);
            warehouseEntryInfoDetailDTO.setSnList(tempList);
            warehouseEntryInfoDetailDTO.setCreateBy(dto.getCreateBy());
            ProductionDeliveryRemoteService.updateApplyOrderDetails(warehouseEntryInfoDetailDTO);
        }
        //优先保障erp账务一致
        // 调er转库接口（psWipInfoService/invokeErpImport）做转库操作
        this.transferInvokeErpImport(list, snList);

        return serviceData;
    }

    // 转库申请单，调er转库接口（psWipInfoService/invokeErpImport）做转库操作
    private void transferInvokeErpImport(List<WarehouseEntryInfoDetailDTO> list, List<String> snList) throws Exception {

        WarehouseEntryInfo warehouseEntryInfo = new WarehouseEntryInfo();
        WarehouseEntryInfoDetailDTO weidDto = list.get(NUM_ZERO);
        warehouseEntryInfo.setBillType(MpConstant.BILL_TYPE_WHOLE);
        warehouseEntryInfo.setBillNo(weidDto.getApplicationNo());
        warehouseEntryInfo.setFactoryId(weidDto.getFactoryId());
        warehouseEntryInfo.setEntityId(weidDto.getEntityId());
        warehouseEntryInfo.setItemNo(weidDto.getItemCode());
        warehouseEntryInfo.setItemName(weidDto.getItemName());
        warehouseEntryInfo.setSubStock(weidDto.getTargetErpSubInventory());
        MtlItemLocations mtlItemLocations1 = this.getErpLocationId(weidDto.getTargetErpLocation(), null);
        warehouseEntryInfo.setLocatorId(mtlItemLocations1.getInventoryLocationId());
        warehouseEntryInfo.setSourceSubStock(weidDto.getSourceErpSubInventory());
        MtlItemLocations mtlItemLocations2 = this.getErpLocationId(weidDto.getSourceErpLocation(), null);
        warehouseEntryInfo.setSourceLocatorId(mtlItemLocations2.getInventoryLocationId());
        warehouseEntryInfo.setOrgId(mtlItemLocations2.getOrganizationId());
        warehouseEntryInfo.setWarehouseEntryId(StringUtils.substring(weidDto.getApplicationNo(), NUM_THREE));
        this.setSnList(snList, warehouseEntryInfo);
        StringBuffer errMsg = new StringBuffer();
        if (!erpRemoteService.invokeErpImport(warehouseEntryInfo, errMsg)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.TRANSFER_WARHOUSE_FAILED, new Object[]{errMsg.toString()});
        }
    }

    // 根据erp货位获取mtl_item_locations中的INVENTORY_LOCATION_ID
    @Override
    public MtlItemLocations getErpLocationId(String segment2, BigDecimal organizationId) throws Exception {
        MtlItemLocations mtlItemLocations = new MtlItemLocations();
        if (StringUtils.isNotEmpty(segment2)) {
            MtlItemLocationsDTO mtlItemLocationsDTO = new MtlItemLocationsDTO();
            mtlItemLocationsDTO.setSegment2(segment2);
            mtlItemLocationsDTO.setOrganizationId(organizationId);
            List<MtlItemLocations> mtlItemLocationsList = BasicsettingRemoteService.mtlItemLocations(mtlItemLocationsDTO);
            if (!CollectionUtils.isEmpty(mtlItemLocationsList)) {
                mtlItemLocations = mtlItemLocationsList.get(NumConstant.NUM_ZERO);
            }
        }
        return mtlItemLocations;
    }

    //组装返回值
    private ServiceData generateServiceData(ServiceData serviceData) {
        StandardMouldStoreHouseResultDTO standardMouldStoreHouseResultDTO = new StandardMouldStoreHouseResultDTO();
        standardMouldStoreHouseResultDTO.setInfCode(MpConstant.INFCODE_BM002);
        standardMouldStoreHouseResultDTO.setInfDesc(MpConstant.INFDESC);
        standardMouldStoreHouseResultDTO.setSenderSys(MpConstant.SENDER_SYS_TIMMS);
        standardMouldStoreHouseResultDTO.setTargetSys(MpConstant.IMES);
        standardMouldStoreHouseResultDTO.setMsgID(MpConstant.SENDER_SYS_TIMMS + MpConstant.UNDLINE + System.currentTimeMillis());
        standardMouldStoreHouseResultDTO.setOperTime(DateUtil.convertDateToString(new Date(), MpConstant.DATE_FORMAT_YYYYMMDDHHMMSS));
        if (serviceData.getCode().getCode().equals(RetCode.SUCCESS_CODE)) {
            standardMouldStoreHouseResultDTO.setStateCode(RetCode.SUCCESS_CODE);
        } else {
            standardMouldStoreHouseResultDTO.setStateCode(serviceData.getCode().getCode());
            standardMouldStoreHouseResultDTO.setStateDesc(serviceData.getCode().getMsg());
        }
        serviceData.setBo(standardMouldStoreHouseResultDTO);
        return serviceData;
    }

    //调erp做移库或者移动完工操作
    private void invokeErpMoveOrComplete(WarehouseEntryInfo warehouseEntryInfo) throws Exception {
        StringBuffer errMsg = new StringBuffer();
        if (MpConstant.TASK_TYPE_RETURN.equals(warehouseEntryInfo.getTaskType())) {
            // 2.1 移库 - 调用 WebService oerp_m04_importmaterialtrxinfosvr_client_ep ERP 移库
            if (!erpRemoteService.invokeErpImport(warehouseEntryInfo, errMsg)) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.TRANSFER_WARHOUSE_FAILED, new Object[]{errMsg.toString()});
            }
            // 调用ERP工序移动接口
        } else {
            if (erpRemoteService.invokeErpMove(warehouseEntryInfo, errMsg)) {
                // 调用ERP工序完工接口
                if (!erpRemoteService.invokeErpDone(warehouseEntryInfo, errMsg)) {
                    throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.ERP_MACHINE_MOVE_COMPLETE_FAILED, new Object[]{errMsg.toString()});
                }
            } else {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.ERP_MACHINE_MOVE_COMPLETE_FAILED, new Object[]{errMsg.toString()});
            }
        }
    }

    //生成WarehouseEntryInfo实体信息
    private void generateWarehouseEnteyInfo(StandardMouldStoreHouseDTO dto, List<WarehouseEntryInfoDetailDTO> list, List<String> snList, WarehouseEntryInfo warehouseEntryInfo) {
        WarehouseEntryInfoDetailDTO weidDto = list.get(NUM_ZERO);
        //整机入库或者移库
        warehouseEntryInfo.setBillType(MpConstant.BILL_TYPE_WHOLE);
        warehouseEntryInfo.setBillNo(dto.getTransNum());
        warehouseEntryInfo.setFactoryId(weidDto.getFactoryId());
        warehouseEntryInfo.setOrgId(weidDto.getOrgId());
        warehouseEntryInfo.setEntityId(weidDto.getEntityId());
        warehouseEntryInfo.setTaskNo(weidDto.getTaskNo());
        warehouseEntryInfo.setTaskType(weidDto.getTaskType());
        warehouseEntryInfo.setItemNo(weidDto.getItemNo());
        warehouseEntryInfo.setItemName(weidDto.getItemName());
        warehouseEntryInfo.setWarehouseEntryId(weidDto.getWarehouseEntryId());
        setSnList(snList, warehouseEntryInfo);
    }

    //更新在制表wip_info中work_station为在库（数据字典1218）
    private void updateWipInfoBySnList(List<List<String>> splitList) {
        for (List<String> tempSnList : splitList) {
            PsWipInfoDTO psWipInfoDTO = new PsWipInfoDTO();
            psWipInfoDTO.setSnList(tempSnList);
            psWipInfoDTO.setWorkStation(Constant.LOOKUP_TYPE_IN_STORE);
            psWipInfoServiceImp.updateWipInfoBySnList(psWipInfoDTO);
        }
    }

    //设置条码list
    private void setSnList(List<String> snList, WarehouseEntryInfo warehouseEntryInfo) {
        List<PsWipInfoDTO> psWipInfoDTOList = new ArrayList<>();
        for (String sn : snList) {
            PsWipInfoDTO psWipInfoDTO = new PsWipInfoDTO();
            psWipInfoDTO.setSn(sn);
            psWipInfoDTOList.add(psWipInfoDTO);
        }
        warehouseEntryInfo.setSnList(psWipInfoDTOList);
    }

    //更新入库单信息
    private void updateWarhouseEntryInfo(StandardMouldStoreHouseDTO dto, List<List<String>> splitList) {
        //更新入库单行状态为已关闭（status=1）,如果入库单行表都已关闭则更新头表状态为已关闭（status=3）
        for (List<String> tempList : splitList) {
            WarehouseEntryInfoDTO warehouseEntryInfoDTO = new WarehouseEntryInfoDTO();
            warehouseEntryInfoDTO.setBillNo(dto.getTransNum());
            warehouseEntryInfoDTO.setSns(getInSnsStrs(tempList));
            warehouseEntryInfoDTO.setStatus(Constant.WARHOUSE_DETAIL_STATUS_ONE);
            warehouseEntryInfoRepository.updateWarehouseEntryDetailsBatchByBillNo(warehouseEntryInfoDTO);
        }
        Map<String, Object> params = new HashMap<>();
        params.put("billNo", dto.getTransNum());
        params.put("notStatus", Constant.WARHOUSE_DETAIL_STATUS_ONE);
        long count = warehouseEntryInfoRepository.getCountWarehouseEntryInfoDetailByBillNo(params);
        if (count < NUM_ONE) {
            WarehouseEntryInfoDTO warehouseEntryInfoDTO = new WarehouseEntryInfoDTO();
            warehouseEntryInfoDTO.setStatus(Constant.WARHOUSE_INFO_STATUS_THREE);
            warehouseEntryInfoDTO.setBillNo(dto.getTransNum());
            warehouseEntryInfoRepository.updateWarehouseEntryInfosByBillNo(warehouseEntryInfoDTO);

        }
    }

    //方法提取
    private List<String> getSnList(List<StandardMouldContainerDTO> list) {
        List<String> snList = new ArrayList<>();
        for (StandardMouldContainerDTO dto : list) {
            snList.addAll(dto.getSnList());
        }
        removeDuplicate(snList);
        return snList;
    }

    //去重
    private static void removeDuplicate(List list) {
        HashSet set = new HashSet(list);
        list.clear();
        list.addAll(set);
    }

    //方法提取
    private String getInSnsStrs(List<String> list) {
        StringBuilder item = new StringBuilder();
        if (CollectionUtils.isEmpty(list)) {
            return item.toString();
        }
        for (String sn : list) {
            item.append(Constant.SINGLE_QUOTE).append(sn).append(Constant.SINGLE_QUOTE).append(Constant.COMMA);
        }
        String inItemNo = item.toString();
        return inItemNo.substring(0, inItemNo.length() - 1);
    }

    //按容器分组
    private void setWarHouseMap(Map<String, List<WarehouseEntryInfoDetailDTO>> warHouseMap, List<WarehouseEntryInfoDetailDTO> list) {
        for (WarehouseEntryInfoDetailDTO warehouseEntryInfoDetailDTO : list) {
            String lpn = warehouseEntryInfoDetailDTO.getLpn();
            if (StringUtils.isEmpty(lpn)) {
                continue;
            }
            List<WarehouseEntryInfoDetailDTO> tempList = warHouseMap.get(lpn);
            if (tempList == null) {
                tempList = new ArrayList<WarehouseEntryInfoDetailDTO>();
            }
            tempList.add(warehouseEntryInfoDetailDTO);
            warHouseMap.put(lpn, tempList);
        }
    }

    @Override
    public List<WarehouseEntryDetailDTO> getEntrySnList(List<WarehouseEntryInfo> dtoList) {
        List<String> billNoList = dtoList.stream().map(entryInfo -> entryInfo.getBillNo()).collect(Collectors.toList());
        List<WarehouseEntryDetailDTO> psWipInfoDTOList = warehouseEntryInfoRepository.getEntrySnList(billNoList);
        return psWipInfoDTOList;
    }

    @Override
    public int reWriteBackAps(String startTime, String endTime) {
        Date startDate = DateUtil.convertStringToDate(startTime, DateUtil.DATE_FORMATE_FULL);
        Date endDate = DateUtil.convertStringToDate(endTime, DateUtil.DATE_FORMATE_FULL);
        int count = NUM_ZERO;

        // 查询存在回写APS异常单据
        List<WarehouseEntryInfo> writeBackApsErrorBillList = warehouseEntryInfoRepository.getWriteBackApsErrorBill(startDate, endDate);
        for (WarehouseEntryInfo warehouseEntryInfo : writeBackApsErrorBillList) {
            String billNo = warehouseEntryInfo.getBillNo();
            String prodplanNo = warehouseEntryInfo.getProdplanNo();
            if (StringUtils.isBlank(billNo) || StringUtils.isBlank(prodplanNo)) {
                continue;
            }
            // 统计单据下回写APS异常条码。根据最后更新人、最后更新时间、receiveBatch分组
            List<WarehouseEntryDetailDTO> statisticData = warehouseEntryDetailService.statisticErrorDataOfBill(billNo);
            // 回写APS
            for (WarehouseEntryDetailDTO warehouseEntryDetailDTO : statisticData) {
                try {
                    // AOP调用，启用writeBackAps方法事务,直接this调用不会启用事务
                    count += SpringUtil.getBean(this.getClass()).writeBackAps(prodplanNo, billNo, warehouseEntryDetailDTO);
                } catch (Exception e) {
                    logger.error("回写APS异常", e);
                }
            }
        }

        return count;

    }

    @Override
    public int writeBackAps(String startTime, String endTime) {
        Date startDate = DateUtil.convertStringToDate(startTime, DateUtil.DATE_FORMATE_FULL);
        Date endDate = DateUtil.convertStringToDate(endTime, DateUtil.DATE_FORMATE_FULL);
        int count = NUM_ZERO;
        /**
         * 查找需要回写APS单据
         * 时间范围内单据类型为“单板生产入库单”,仓库类型为“整机车间库”
         * 且入库单条码存在状态为已接收但RECEIVE_BATCH为空单据
         */
        // 且存在已接收条码但未回写单据
        List<WarehouseEntryInfo> needWriteBackApsBillList = warehouseEntryInfoRepository.getNeedWriteBackApsBill(startDate, endDate);
        for (WarehouseEntryInfo warehouseEntryInfo : needWriteBackApsBillList) {
            String billNo = warehouseEntryInfo.getBillNo();
            String prodplanNo = warehouseEntryInfo.getProdplanNo();
            if (StringUtils.isBlank(billNo) || StringUtils.isBlank(prodplanNo)) {
                continue;
            }

            // 查询单据下所有已接收但未回写APS条码统计。根据最后更新人和最后更新时间分组
            List<WarehouseEntryDetailDTO> statisticData = warehouseEntryDetailService.statisticReceiveDataOfBill(billNo);
            // 回写APS
            for (WarehouseEntryDetailDTO warehouseEntryDetailDTO : statisticData) {
                try {
                    // AOP调用，启用writeBackAps方法事务,直接this调用不会启用事务
                    count += SpringUtil.getBean(this.getClass()).writeBackAps(prodplanNo, billNo, warehouseEntryDetailDTO);
                } catch (Exception e) {
                    logger.error("回写APS异常", e);
                }
            }
        }

        return count;
    }

    @Transactional(rollbackFor = Exception.class)
    public int writeBackAps(String prodplanNo, String billNo, WarehouseEntryDetailDTO warehouseEntryDetailDTO) throws Exception {
        int count = NUM_ZERO;
        if (StringUtils.isBlank(prodplanNo) || warehouseEntryDetailDTO == null || warehouseEntryDetailDTO.getReceiveCount() <= NUM_ZERO) {
            return count;
        }
        // 接收ID。APS对相同接收ID只更新一次数量
        String receiveBatch = StringUtils.isBlank(warehouseEntryDetailDTO.getReceiveBatch()) ? GenerateUUIDUtil.generateUUID() : warehouseEntryDetailDTO.getReceiveBatch();

        // RECEIVE_BATCH更新为接收ID、APS_STATUS更新为Y  查询条件：计划跟踪单号在billNoSet内 状态为已接收、接收批次为空数据。
        WarehouseEntryDetailDTO param = new WarehouseEntryDetailDTO();
        param.setBillNo(billNo);
        // 状态2为已接收
        param.setStatus(NumConstant.STR_TWO);
        // 设置接收批次和aps回写状态
        param.setReceiveBatch(receiveBatch);
        param.setApsStatus(Constant.FLAG_Y);
        param.setLastUpdatedBy(warehouseEntryDetailDTO.getLastUpdatedBy());
        param.setLastUpdatedDate(warehouseEntryDetailDTO.getLastUpdatedDate());

        if (StringUtils.isNotBlank(warehouseEntryDetailDTO.getReceiveBatch())) {
            // 接收ID不为空则根据接收ID更新数据
            count = warehouseEntryDetailService.updateApsStatusByReceiveBatch(param);
        } else {
            // count取最终更新数量。保证数量准确
            count = warehouseEntryDetailService.updateApsWriteBackInfo(param);
        }

        if (count <= NUM_ZERO) {
            // 更新数量小于0直接返回
            return count;
        }

        ServiceData<Object> ret = null;
        try {
            ret = apsRemoteService.updateProdplanInsmstockQty(receiveBatch, prodplanNo, count, DateUtil.convertDateToString(warehouseEntryDetailDTO.getLastUpdatedDate(), DateUtil.DATE_FORMATE_FULL), warehouseEntryDetailDTO.getLastUpdatedBy());
        } catch (Exception e) {
            logger.error(MpConstant.UPDATE_APS_STATUS_ERROR, e);
            // 调用APS异常更新状态为E(不回滚)。
            param.setApsStatus(Constant.FLAG_E);
            param.setApsDetail(StringWithChineseUtils.substring(e.getMessage(), NumConstant.NUM_500));
            try {
                count = warehouseEntryDetailService.updateApsStatusByReceiveBatch(param);
            } catch (Exception e2) {
                // 防止更新异常回滚（此处数据若更新失败可能需要修正数据。将APS_STATUS修改为E重新推送）
                logger.error(MpConstant.UPDATE_APS_STATUS_ERROR, e2);
                imesLogService.log(param, MpConstant.UPDATE_APS_STATUS_ERROR);
            }
            return count;
        }

        if (ret != null && ret.getCode() != null && !RetCode.SUCCESS_CODE.equals(ret.getCode().getCode())) {
            Map<String, Object> other = new HashMap<>();
            other.put("param", param);
            ret.setOther(other);
            imesLogService.log(ret, MpConstant.CALL_APS_WRITE_BACK_FAIL);
            // APS返回结果为不成功则回滚
            throw new Exception(MpConstant.CALL_APS_WRITE_BACK_FAIL + ret.getCode().getMsg() + receiveBatch);
        }
        return count;
    }

    @Override
    public Long getCountWarehouseEntryInfoDetailByStatus(WarehouseEntryInfoDTO dto) {

        Map<String, Object> map = new HashMap<String, Object>();
        // 设置查询条件
        map.put(NOT_INFO_STATUS, dto.getNotInfoStatus());
        map.put(NOT_DETAIL_STATUS, dto.getNotDetailStatus());
        map.put(STOCK_NAME_LIST, dto.getStockNameList());
        return warehouseEntryInfoRepository.getCountWarehouseEntryInfoDetailByBillNo(map);
    }

    /**
     * 通过查询psTask等信息，拼接入库信息返回前端，用于入库提交操作
     *
     * @param
     * @param prodplanId
     * @return com.zte.interfaces.dto.WarehouseEntryInfoQueryDTO
     * @Author: 10307315陈俊熙
     * @date 2022/6/23 下午5:08
     */
    @Override
    public WarehouseEntryInfoQueryDTO getWarehouseEntryInfoScrap(String prodplanId) throws Exception {
        WarehouseEntryInfoQueryDTO warehouseEntryInfoQueryDTO = new WarehouseEntryInfoQueryDTO();
        //获取任务列表
        Map<String, Object> map = new HashMap<>(1);
        map.put("prodplanId", prodplanId);
        List<PsTask> psTaskList = getPsTasks(map);
        if (CollectionUtils.isEmpty(psTaskList)) {
            // 直接跑异常
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SCRAP_BILL_PROD_PLAN_ID_NOT_HAVE_TASK_INFO,
                    new Object[]{prodplanId});
        }
        //报废默认是单板模式
        BeanUtils.copyProperties(psTaskList.get(NUM_ZERO), warehouseEntryInfoQueryDTO);
        String leadFlag = psTaskList.get(NUM_ZERO).getLeadFlag();
        if (!StringUtils.isEmpty(leadFlag)) {
            warehouseEntryInfoQueryDTO.setIsLead(leadFlag);
        }
        // 设置任务相关属性，返回该任务是否锁定条码
        setTaskProperty(map, warehouseEntryInfoQueryDTO, psTaskList);

        //仓库
        setStock(warehouseEntryInfoQueryDTO);
        warehouseEntryInfoQueryDTO.setProductType(psTaskList.get(Constant.INT_0).getProductType());
        warehouseEntryInfoQueryDTO.setOrgId(psTaskList.get(Constant.INT_0).getOrgId());
        return warehouseEntryInfoQueryDTO;
    }


    @Override
    public Pair<List<String>, List<Map<String, Object>>> saveWarehouseEntryInfoForBatchScrap(WarehouseEntryInfoQueryDTO dto, String factoryId,
                                                                                             String entityId, String empNo) throws Exception {
        List<Map<String, Object>> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(dto.getProdplanIdList()) && StringUtils.isEmpty(dto.getProdplanId())) {
            return null;
        }
        if (!dto.getStockType().equals(Constant.STOCK_TYPE_K2) && null == dto.getLocatorId()) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.LOCATOR_ID_ERROR);
        }
        String currProcessName = CrafttechRemoteService.getWorkCodeStation(dto.getCurrProcess());
        List<String> prodPlanIds = CollectionUtils.isEmpty(dto.getProdplanIdList()) ? new ArrayList<>() : dto.getProdplanIdList();
        if (!StringUtils.isEmpty(dto.getProdplanId())) {
            prodPlanIds.add(dto.getProdplanId());
        }
        prodPlanIds = prodPlanIds.stream().filter(item -> StringUtils.isNotEmpty(item)).distinct().collect(Collectors.toList());
        List<String> params = new ArrayList<>();
        Pair<Map<String, String>, Map<String, BigDecimal>> pair = this.getExternalType(prodPlanIds);
        Map<String, String> externalTypeMap = pair.getFirst();
        Map<String, BigDecimal> orgIdMap = pair.getSecond();
        WarehouseEntryInfoServiceImpl implClass = SpringUtil.getBean(WarehouseEntryInfoServiceImpl.class);
        for (String prodPlanId : prodPlanIds) {
            try {
                WarehouseEntryInfoQueryDTO warehouseEntry = implClass.getWarehouseEntryInfoScrap(prodPlanId);
                warehouseEntry.setBillNo(getRKBillNo(factoryId));
                warehouseEntry.setBillType(dto.getBillType());
                warehouseEntry.setBillTypeCode(dto.getBillTypeCode());
                warehouseEntry.setStockType(dto.getStockType());
                warehouseEntry.setStockName(dto.getStockName());
                warehouseEntry.setLocatorId(dto.getLocatorId());
                warehouseEntry.setLocatorName(dto.getLocatorName());
                warehouseEntry.setTaskType(dto.getTaskType());
                warehouseEntry.setCurrProcess(dto.getCurrProcess());
                warehouseEntry.setSubStock(dto.getSubStock());
                List<PsWipInfo> psWipInfoList = dto.getPsWipInfoList().stream()
                        .filter(psWipInfo -> StringUtils.isNotEmpty(psWipInfo.getAttribute1()) && prodPlanId.equals(psWipInfo.getAttribute1()))
                        .collect(Collectors.toList());
                warehouseEntry.setProdplanId(prodPlanId);
                warehouseEntry.setPsWipInfoList(psWipInfoList);
                warehouseEntry.setCommitedQty(new BigDecimal(psWipInfoList.size()));
                warehouseEntry.setCurrProcess(currProcessName);
                warehouseEntry.setExternalType(externalTypeMap.get(prodPlanId));
                warehouseEntry.setOrgId(orgIdMap.get(prodPlanId));
                warehouseEntry.setCarryAccountName(dto.getCarryAccountName());
                Map<String, Object> resultTemp = implClass.saveWarehouseEntryInfoScrap(warehouseEntry, factoryId, entityId, empNo);
                result.add(resultTemp);
            } catch (Exception e) {
                // 记录报错日志
                imesLogService.log(e.getMessage(), MpConstant.SCRAP_BILL_SUBMIT_ERROR);
                params.add(prodPlanId);
            }
        }
        Pair<List<String>, List<Map<String, Object>>> pairResult = Pair.of(params, result);
        return pairResult;
    }

    private Pair<Map<String, String>, Map<String, BigDecimal>> getExternalType(List<String> prodPlanIds) throws Exception {
        Map<String, String> externalTypeMap;
        Map<String, BigDecimal> orgIdMap;
        // 获取批次信息
        List<PsTask> psTaskList = PlanscheduleRemoteService.getPsTaskByProdplanIdList(prodPlanIds);
        if (CollectionUtils.isEmpty(psTaskList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PRODPLANID_IS_NOT_EXITS, new String[]{prodPlanIds.toString()});
        }
        externalTypeMap = psTaskList.stream().collect(Collectors.toMap(PsTask::getProdplanId, PsTask::getExternalType, (k1, k2) -> k1));
        orgIdMap = psTaskList.stream().collect(Collectors.toMap(PsTask::getProdplanId, PsTask::getOrgId, (k1, k2) -> k1));
        return Pair.of(externalTypeMap, orgIdMap);
    }


    /**
     * 处理入库操作主方法
     *
     * @param
     * @param dto
     * @param factoryId
     * @param entityId
     * @param empNo
     * @return java.util.Map<java.lang.String, java.lang.Object>
     * @Author: 10307315陈俊熙
     * @date 2022/6/23 下午5:08
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> saveWarehouseEntryInfoScrap(WarehouseEntryInfoQueryDTO dto, String factoryId,
                                                           String entityId, String empNo) throws Exception {
        Map<String, Object> result = new HashMap<>(2);
        // 批次上锁
        String prodplanId = dto.getProdplanId();
        String redisKey = String.format(RedisKeyConstant.SCRAP_BILL_NO_SAVE, prodplanId);
        RedisLock redisLock = new RedisLock(redisKey);
        if (!redisLock.lock()) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.FAILED_TO_GET_REDIS_LOCK);
        }
        try {
            //校验仓库类型不能为空
            if (dto.getBillTypeCode() == null || dto.getBillTypeCode().intValue() == Constant.BILL_TYPE_LOOKUP_CODE_SCRAP
                    && StringUtils.isEmpty(dto.getStockType())) {
                String[] params = {dto.getBillTypeCode().toString()};
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.WAREHOUSEENTRYINFO_STOCK_TYPE_IS_NULL, params);
            }
            String applyNo = STR_EMPTY;
            // 保存 WarehouseEntryInfo
            WarehouseEntryInfo entity = WarehouseEntryInfoAssembler.convertEntityByWarehouseEntryInfoQueryDTO(dto);
            entity.setOrgId(dto.getOrgId());
            entity.setCreateBy(empNo);
            entity.setLastUpdatedBy(empNo);
            entity.setWarehouseEntryId(UUID.randomUUID().toString());
            if (StringUtils.isNotBlank(factoryId)) {
                entity.setFactoryId(new BigDecimal(factoryId));
            }
            if (StringUtils.isNotBlank(entityId)) {
                entity.setEntityId(new BigDecimal(entityId));
            }
            //已提交
            entity.setStatus(Constant.STR_NUMBER_ZERO);
            //isRepair 或者 isSmall
            this.isRepairOrSmall(dto, entity);
            //提交入库单时，如果是单板生产入库单则判断该批次是否有已经入库的条码（wip_info 主工序等于入库），
            // 如果没有则更新该批次的首件入库日期（ps_task  first_warehouse_date ）为当前时间
            updateFirstWarehouseDate(dto, entity);
            if (dto.getBillTypeCode().intValue() == Constant.BILL_TYPE_LOOKUP_CODE_SCRAP
                    && (dto.getStockType().equals(Constant.STOCK_TYPE_K2) || dto.getStockType().equals(Constant.STOCK_TYPE_MODE_WORKSTATION))
                    && getScrapBillWriteBackStepFlag()) {
                WarehouseEntryInfoDTO warehouseEntryDTO = WarehouseEntryInfoAssembler.toDTO(entity);
                if (Constant.STOCK_TYPE_K2.equals(dto.getStockType())) {
                    checkCarryImes(warehouseEntryDTO, entity);
                    applyNo = warehouseEntryInfoWritebackStep(warehouseEntryDTO);
                }
                try {
                    result = insertnAndWriteBackWarehouseScrap(dto, entity, applyNo);
                } catch (Exception e) {
                    // 回退补偿
                    this.warehouseEntryInfoWritebackStepRollBack(applyNo);
                    // 抛出异常rollback
                    throw e;
                }
            } else {
                // 插入
                result = insertWarehouseScrap(dto, entity, applyNo);
            }
            //更新任务完成数量--2023.05.18统一在接收的时候计算任务已完成数量
//            this.handleCompleteQty(dto.getProdplanId(), dto.getCommitedQty());
            //设置交货仓
            setStock(result);
            return result;
        } finally {
            redisLock.unlock();
        }
    }

    /**
     *<AUTHOR>
     * iMes记账校验
     *@Date 2024/7/15 17:26
     *@Param [com.zte.interfaces.dto.WarehouseEntryInfoDTO]
     *@return
     **/
    public void checkCarryImes(WarehouseEntryInfoDTO warehouseEntryDTO, WarehouseEntryInfo entity) throws Exception {
        // 开关未打开不校验
        if (!validReqBillOn(Constant.LOOKUP_TYPE_6667, Constant.LOOKUP_TYPE_6667002)) {
            return;
        }
        // 别名为空报错
        if (StringUtils.isEmpty(entity.getCarryAccountName())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.CARRY_ACCOUNT_NAME_NOT_NULL);
        }
        // 获取erp相关数据字典
        List<SysLookupTypesDTO> sysLookupTypesList = BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_5972);
        if(CollectionUtils.isEmpty(sysLookupTypesList)){
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SYS_LOOK_NOT_CONFIG,new String[]{Constant.LOOKUP_TYPE_5972});
        }
        warehouseEntryDTO.setStatus(STR_1);
        WarehouseEntryInfo info = new WarehouseEntryInfo();
        info.setWarehouseEntryAdditionalId(UUID.randomUUID().toString());
        info.setBillNo(entity.getBillNo());
        info.setCarryAccountName(entity.getCarryAccountName());
        info.setCreateBy(entity.getCreateBy());
        info.setCarryAccount(STR_1);
        info.setOutboundType(STR_1);
        entity.setSourceOrgId(entity.getOrgId());
        entity.setBoardPackErpSucceNode(STR_1);
        if (StringUtils.isNotEmpty(entity.getItemNo()) && entity.getItemNo().length() > INT_12) {
            entity.setAttribute1(entity.getItemNo().substring(INT_0, INT_12));
            entity.setAttribute2(entity.getItemNo().substring(INT_12));
        } else {
            entity.setAttribute1(entity.getItemNo());
        }
        // 设置子库存信息
        setSourceSubByLocator(warehouseEntryDTO, entity, sysLookupTypesList);
        warehouseEntryInfoRepository.insertWarehouseEntryAdditionalInfo(info);
    }

    /**
     *<AUTHOR>
     * 设置子库存
     *@Date 2024/7/15 17:29
     *@Param [com.zte.interfaces.dto.WarehouseEntryInfoDTO, java.util.List<com.zte.interfaces.dto.SysLookupTypesDTO>]
     *@return
     **/
    private void setSourceSubByLocator(WarehouseEntryInfoDTO warehouseEntryDTO, WarehouseEntryInfo entity, List<SysLookupTypesDTO> sysLookupTypesList) throws Exception{
        // 组织ID和子库存的对应关系数据字典
        List<SysLookupTypesDTO> lookupValueList = BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_5976);
        if (CollectionUtils.isEmpty(lookupValueList)) {
            return;
        }
        // 过滤获取当前orgId对应子库存编码
        SysLookupTypesDTO lookupTypesDTO = lookupValueList.stream()
                .filter(t -> Constant.LOOKUP_TYPE_5976.equals(String.valueOf(t.getLookupType()))
                        && String.valueOf(warehouseEntryDTO.getOrgId()).equals(t.getAttribute1())).findFirst().orElse(null);
        if (lookupTypesDTO == null) {
            return;
        }
        LocatorInfoDTO dto = new LocatorInfoDTO();
        // 拼接源货位名称
        dto.setInventoryLocationCode(lookupTypesDTO.getLookupMeaning() + Constant.POINT + warehouseEntryDTO.getExternalType());
        dto.setOrganizationId(warehouseEntryDTO.getOrgId());
        // 获取erp货位id
        List<LocatorInfoDTO> locatorInfoList = erpRemoteService.getLocatorInfo(dto, getlookupValue(sysLookupTypesList, LOOKUP_TYPE_5972, LOOKUP_CODE_5972005));
        if(CollectionUtils.isEmpty(locatorInfoList)){
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.LOCATOR_ID_NOT_EXIST,new String[]{dto.getInventoryLocationCode()});
        }
        // 设置子库存货位id
        entity.setSourceLocatorId(locatorInfoList.get(INT_0).getInventoryLocationId());
        entity.setSourceSubStock(lookupTypesDTO.getLookupMeaning());
        entity.setSourceLocatorName(dto.getInventoryLocationCode());
    }

    public String getlookupValue(List<SysLookupTypesDTO> list, String lookupType, String lookupCode) throws Exception{
        SysLookupTypesDTO lookupValuesDTO = list.stream().filter(t -> lookupType.equals(String.valueOf(t.getLookupType())) && lookupCode.equals(String.valueOf(t.getLookupCode()))).findFirst().orElse(null);
        if(null == lookupValuesDTO){
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SYS_LOOK_NOT_CONFIG,new String[]{lookupCode});
        }
        return lookupValuesDTO.getLookupMeaning();
    }

    /**
     * 获取数据字典，判断是否需要回写SPM
     *
     * @param
     * @return boolean
     * @Author: 10307315陈俊熙
     * @date 2022/6/23 下午5:08
     */
    private boolean getScrapBillWriteBackStepFlag() throws Exception {
        List<SysLookupTypesDTO> lookupTypes = getWriteBackSPMKey();
        if (CollectionUtils.isEmpty(lookupTypes)) {
            return false;
        }
        for (SysLookupTypesDTO lookupType : lookupTypes) {
            if (lookupType.getLookupCode().intValue() == Constant.SCRAP_BILL_WRITE_BACK_LOOKUP_CODE && lookupType.getLookupMeaning().equals(Constant.FLAG_Y)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 开关打开时，额外要回写SPM操作和更新IMU。
     *
     * @param
     * @param dto
     * @param warehouseEntryInfo
     * @param applyNo
     * @return java.util.Map<java.lang.String, java.lang.Object>
     * @Author: 10307315陈俊熙
     * @date 2022/6/23 下午5:07
     */
    private Map<String, Object> insertnAndWriteBackWarehouseScrap(WarehouseEntryInfoQueryDTO dto, WarehouseEntryInfo
            warehouseEntryInfo, String applyNo) throws Exception {
        Map<String, Object> result = new HashMap<>();
        warehouseEntryInfo.setApplyNo(applyNo);
        // 插入
        result = this.insertWarehouseScrap(dto, warehouseEntryInfo, applyNo);
        List<Map<String, String>> boards = new ArrayList<>(1);
        if (CollectionUtils.isEmpty(dto.getPsWipInfoList())) {
            throw new Exception(CommonUtils.getLmbMessage(MessageId.SN_NOT_FOUND));
        }
        this.packBoardData(boards, dto, warehouseEntryInfo);
        // 更新IMU
        updateImuBySn(boards);
        return result;
    }

    /**
     * 处理入库头表详表，并更新条码相关在制表和和 报废详表
     *
     * @param
     * @param dto
     * @param warehouseEntryInfo
     * @param applyNo
     * @return java.util.Map<java.lang.String, java.lang.Object>
     * @Author: 10307315陈俊熙
     * @date 2022/6/23 下午5:05
     */
    private Map<String, Object> insertWarehouseScrap(WarehouseEntryInfoQueryDTO dto, WarehouseEntryInfo warehouseEntryInfo,
                                                     String applyNo) throws Exception {
        Map<String, Object> result = new HashMap<>();
        if (dto.isK2Flag()) {
            warehouseEntryInfo.setStatus(Constant.STR_3);
        } else {
            warehouseEntryInfo.setStatus(Constant.STR_0);
        }
        int inserResult = insertWarehouseEntryInfo(warehouseEntryInfo);
        if (inserResult > 0) {
            // 保存  WarehouseEntryDetail
            List<PsWipInfo> psWipInfoList = dto.getPsWipInfoList();
            // 组装数据
            List<WarehouseEntryDetailDTO> warehouseEntryDetailList = buildDetailList(psWipInfoList, warehouseEntryInfo, applyNo);
            // 插入 WarehouseEntryDetail
            int insertDetailResult = insertWarehouseEntryDetailBatchNotExist(warehouseEntryDetailList);
            if (insertDetailResult != warehouseEntryDetailList.size()) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.ACTUAL_STOCK_QTY_INCONSISTENT_SUBMITTED_QUANTITY);
            }
            // 更新在制表 和 报废详表
            updateScrapDetAndWipInfo(dto, warehouseEntryInfo.getCreateBy());
        }
        result.put("status", Constant.FLAG_Y);
        result.put("msg", CommonUtils.getLmbMessage(MessageId.IN_WAREHOUSE_SUCCEED));
        result.put("warehouseEntryInfo", warehouseEntryInfo);
        return result;
    }

    /**
     * 组装入库详表实体列表
     *
     * @param
     * @param psWipInfoList
     * @param warehouseEntryInfo
     * @param applyNo
     * @return java.util.List<com.zte.interfaces.dto.WarehouseEntryDetailDTO>
     * @Author: 10307315陈俊熙
     * @date 2022/6/23 下午5:04
     */
    private List<WarehouseEntryDetailDTO> buildDetailList(List<PsWipInfo> psWipInfoList,
                                                          WarehouseEntryInfo warehouseEntryInfo, String applyNo) {
        List<WarehouseEntryDetailDTO> warehouseEntryDetailList = new ArrayList<>();
        for (int i = 0; i < psWipInfoList.size(); i++) {
            PsWipInfo psWinInfo = psWipInfoList.get(i);
            WarehouseEntryDetailDTO warehouseEntryDetail = new WarehouseEntryDetailDTO();
            warehouseEntryDetail.setWarehouseEntryDetailId(UUID.randomUUID().toString());
            warehouseEntryDetail.setBillNo(warehouseEntryInfo.getBillNo());
            warehouseEntryDetail.setApplyNo(applyNo);
            warehouseEntryDetail.setSn(psWinInfo.getSn());
            warehouseEntryDetail.setWorkOrderNo(psWinInfo.getWorkOrderNo());
            warehouseEntryDetail.setProdplanId(warehouseEntryInfo.getProdplanId());
            warehouseEntryDetail.setCreateBy(warehouseEntryInfo.getCreateBy());
            warehouseEntryDetail.setLastUpdatedBy(warehouseEntryInfo.getCreateBy());
            warehouseEntryDetail.setOrgId(warehouseEntryInfo.getOrgId());
            warehouseEntryDetail.setLpn(psWinInfo.getLpn());
            if (warehouseEntryInfo.getFactoryId() != null) {
                warehouseEntryDetail.setFactoryId(warehouseEntryInfo.getFactoryId());
            }
            if (warehouseEntryInfo.getEntityId() != null) {
                warehouseEntryDetail.setEntityId(warehouseEntryInfo.getEntityId());
            }
            warehouseEntryDetailList.add(warehouseEntryDetail);
        }
        return warehouseEntryDetailList;
    }

    /**
     * 更新在制表以及报废详表
     *
     * @param
     * @param dto
     * @param empNo
     * @return void
     * @Author: 10307315陈俊熙
     * @date 2022/6/23 下午5:03
     */
    private void updateScrapDetAndWipInfo(WarehouseEntryInfoQueryDTO dto, String empNo) {
        List<PsWipInfo> listInfo = dto.getPsWipInfoList();
        List<ScrapBillDetailEntityDTO> scrapBilldetList = new ArrayList<>();
        List<PsScanHistory> batchInsertWipScanHis = new ArrayList<>();
        String workStation = MpConstant.STRING_ZERO;
        for (PsWipInfo entity : listInfo) {
            // 报废详表实体
            ScrapBillDetailEntityDTO scrapDeEntity = new ScrapBillDetailEntityDTO();
            scrapDeEntity.setLastUpdatedBy(empNo);
            scrapDeEntity.setCurrProcessCode(P0008);
            scrapDeEntity.setSn(entity.getSn());
            scrapBilldetList.add(scrapDeEntity);

            entity.setLastUpdatedBy(empNo);
            entity.setWorkStation(workStation);
            entity.setCurrProcessCode(P0008);
            entity.setCurrProcessName(Constant.SCRAP_IN_STOCK);
            // "入库"
            entity.setCraftSection(Constant.WAREHOUSE_ENTRY);
            // 获取待保存的扫描历史信息
            PsScanHistory scanHistory = new PsScanHistory();
            BeanUtils.copyProperties(entity, scanHistory);
            scanHistory.setSmtScanId(java.util.UUID.randomUUID().toString()); // 设置主键
            scanHistory.setSourceSysName(Constant.WAREHOUSE_ENTRY);
            batchInsertWipScanHis.add(scanHistory);
        }
        // 更新在制表。
        psWipInfoService.updatePsWipInfoByScanBatch(listInfo);
        // 报废详情表更新
        scrapBillDetailService.updateProcessCodeBatchBySn(scrapBilldetList);
        batchInsertPsScanHistory(batchInsertWipScanHis);
    }


    /**
     * 批量更新单据状态
     *
     * @param listInfo WarehouseEntryInfo实体
     * @return 更新数量
     * <AUTHOR>
     **/
    @Override
    public int batchUpdateWarehouseInfoStatus(List<WarehouseEntryInfo> listInfo, String headStatus, String detailStatus, String factoryId) throws Exception {
        if (CollectionUtils.isEmpty(listInfo)) {
            return 0;
        }
        List<WarehouseEntryInfo> listDetail = new ArrayList<>();
        List<WarehouseEntryInfo> listHead = new ArrayList<>();
        Map<String, BigDecimal> prodplanIdAndQtyMap = new HashMap<>();
        for (WarehouseEntryInfo item : listInfo) {
            // 获取单据状态和单据类型，状态非已提交时不执行更新，单据类型是返修入库单时不更新ps_task指令状态
            WarehouseEntryInfo headInfo = warehouseEntryInfoRepository.selectBillTypeByBillNo(item.getBillNo());
            if(null == headInfo || !Constant.STRING_0.equals(headInfo.getStatus())) {
                continue;
            }
            WarehouseEntryInfo warehouseEntryInfo = new WarehouseEntryInfo();
            WarehouseEntryInfo warehouseEntryInfoDetail = new WarehouseEntryInfo();

            warehouseEntryInfo.setBillNo(item.getBillNo());
            warehouseEntryInfo.setProdplanId(item.getProdplanId());
            warehouseEntryInfo.setStatus(headStatus);

            warehouseEntryInfoDetail.setBillNo(item.getBillNo());
            warehouseEntryInfoDetail.setProdplanId(item.getProdplanId());
            warehouseEntryInfoDetail.setStatus(detailStatus);
            listHead.add(warehouseEntryInfo);
            listDetail.add(warehouseEntryInfoDetail);
            // 查询单据下有多少条码不是已接收状态，这部分数量作为新增的已完成的任务数量
            WarehouseEntryInfoDTO entryInfoDTO = new WarehouseEntryInfoDTO();
            entryInfoDTO.setBillNo(item.getBillNo());
            entryInfoDTO.setStatus(detailStatus);
            int received = warehouseEntryInfoRepository.selectCountDetailInfo(entryInfoDTO);
            imesLogService.log(received,item.getProdplanId().toString()+"批次"+ item.getBillNo().toString() + "入库单下未接收条码数量");
            imesLogService.log(prodplanIdAndQtyMap,"已统计需更新的已完工任务及数量");
            if(Constant.STR_9.equals(headInfo.getBillType())) {
                continue;
            }
            BigDecimal prodplanIdQty=prodplanIdAndQtyMap.get(item.getProdplanId());
            if(null != prodplanIdQty){
                received += prodplanIdQty.intValue();
            }
            prodplanIdAndQtyMap.put(item.getProdplanId(),new BigDecimal(received));
        }
        int count = NumConstant.NUM_ZERO;
        List<List<WarehouseEntryInfo>> listOfList = CommonUtils.splitList(listHead, Constant.BATCH_SIZE);
        for (List<WarehouseEntryInfo> list : listOfList) {
            count += warehouseEntryInfoRepository.batchUpdateWarehouseEntryInfoStatus(list);
        }
        List<List<WarehouseEntryInfo>> listOfDetailList = CommonUtils.splitList(listDetail, Constant.BATCH_SIZE);
        for (List<WarehouseEntryInfo> listOfDetail : listOfDetailList) {
            warehouseEntryInfoRepository.batchUpdateWarehouseEntryDetailStatus(listOfDetail);
        }
        // 更新任务已完成数量,点对点调用塞入请求头
        try{
            Map<String, String> header =MESHttpHelper.getHttpRequestHeader();
            header.put(SysConst.HTTP_HEADER_X_FACTORY_ID, factoryId);
            header.put(SysConst.HTTP_HEADER_X_FACTORY_ID_LOW_CASE, factoryId);
            MESHttpHelper.setHttpRequestHeader(header);
            PlanscheduleRemoteService.batchHandleCompleteQty(prodplanIdAndQtyMap);
        } finally {
            MESHttpHelper.removeHttpRequestHeader();
        }
        return count;
    }

    /**
     * 导出入库单明细数据
     *
     * @param response 响应
     * @param dtoForm  查询参数
     * @throws Exception 业务异常报错
     */
    @Override
    @RedisDistributedLockAnnotation(redisPrefix = "exportWarehouseInfo", redisLockTime = 1800, lockFailMsgZh =
            "正在导出请稍后再试", lockFailMsgEn = "Exporting. Please try again later.", redisLockParam = {
            @RedisLockParamAnnotation(paramName = "dtoForm", propertiesString = "empNo")
    })
    @AsyncExport(functionName = "入库单明细导出")
    public void exportWarehouseInfo(HttpServletResponse response, WarehouseEntryInfoDTO dtoForm) throws Exception {
        // 获取最大导出数量配置项
        SimpleDateFormat sdf = new SimpleDateFormat(MpConstant.DATE_FORMAT_YYYYMMDDHHMM_ONE);
        String fileName = sdf.format(new Date()) + Constant.WAREHOUSE_EXPORT_NAME;
        ImesExcelUtil.setResponseHeader(response, fileName);
        List<SysLookupValuesDTO> valueByTypeCodes = this.getLookupTypes();
        String maxCount = valueByTypeCodes.stream()
                .filter(item -> WAREHOUSE_EXPORT.equals(item.getAttribute1()))
                .map(SysLookupValuesDTO::getLookupMeaning)
                .findFirst().orElse(MpConstant.STRING_100000);
        int maxSize = Integer.parseInt(maxCount);
        WriteSheet build = EasyExcelFactory.writerSheet(0, Constant.WAREHOUSE_EXPORT_SHEET_NAME).build();
        List<WarehouseEntryExportDTO> rows;
        String filePath = FileUtils.tempPath + System.currentTimeMillis()+Constant.GANG+fileName;
        FileUtils.checkFilePath(filePath);
        ExcelWriter excelWriter = EasyExcelFactory.write(filePath, WarehouseEntryExportDTO.class)
                .excelType(ExcelTypeEnum.XLSX).build();
        // 查询单据头
        dtoForm.setLimit(exportLimit);
        List<String> billNoList = warehouseEntryInfoRepository.getBillNo(dtoForm);
        int totalCount = NUM_ZERO;
        // 每页5000条数据
        int pageRows = Constant.INT_5000;
        outer: for (String billNo : billNoList) {
            int page = NUM_ONE;
            // 一个单据号最大循环查询100次
            for (int i = 0; i < NumConstant.NUM_100; i++) {
                Page<WarehouseEntryExportDTO> pageHead = new Page<>();
                WarehouseEntryExportDTO exportDTO=new WarehouseEntryExportDTO();
                exportDTO.setBillNo(billNo);
                exportDTO.setLpn(dtoForm.getLpn());
                pageHead.setCurrent(page++);
                pageHead.setSearchCount(false);
                pageHead.setPageSize(pageRows);
                pageHead.setParams(exportDTO);
                // 分页查询导出数据
                rows = warehouseEntryInfoRepository.exportWarehouseInfo(pageHead);
                if (CollectionUtils.isEmpty(rows)) {
                    break;
                }
                this.coverRowsProperties(rows, valueByTypeCodes);
                this.setMBomProductCodeExport(rows);
                excelWriter.write(rows, build);

                totalCount += rows.size();
                if (totalCount > maxSize) {
                    // 总数大于最大导出数写入告警信息并跳出循环
                    WarehouseEntryExportDTO a1 = new WarehouseEntryExportDTO();
                    a1.setBillNo(String.format(EXPORT_MAX_MSG, maxCount));
                    rows.clear();
                    rows.add(a1);
                    excelWriter.write(rows, build);
                    break outer;
                }
                if (rows.size() < pageRows) {
                    // 返回行数小于分页大小跳出循环
                    break;
                }
            }
        }
        excelWriter.finish();
        asyncExportFileCommonService.uploadFileThenClearLocalFileUpdateLog(fileName, filePath);
    }

    private void setMBomProductCodeExport(List<WarehouseEntryExportDTO> list) {
        List<String> prodplanIds = list.stream().map(WarehouseEntryExportDTO::getProdplanId).collect(Collectors.toList());
        List<BProdBomHeaderDTO> mBomList = centerfactoryRemoteService.queryProductCodeByProdPlanIdList(prodplanIds);
        Map<String, String> mBomProductCodeByProdplanIdMap = mBomList.stream().collect(Collectors.toMap(BProdBomHeaderDTO::getProdplanId, BProdBomHeaderDTO::getProductCode, (v1, v2) -> v1));
        for(WarehouseEntryExportDTO dto : list) {
            String mBomProductCode = mBomProductCodeByProdplanIdMap.get(dto.getProdplanId());
            dto.setMBomProductCode(mBomProductCode == null ? dto.getItemNo() : mBomProductCode);
        }
    }

    /**
     * 导出入库单头信息数据
     */
    @Override
    @RedisDistributedLockAnnotation(redisPrefix = "exportWarehouseInfoHead", redisLockTime = 1800, lockFailMsgZh =
            "正在导出请稍后再试", lockFailMsgEn = "Exporting. Please try again later.", redisLockParam = {
            @RedisLockParamAnnotation(paramName = "dto", propertiesString = "empNo")
    })
    public void exportWarehouseInfoHead(HttpServletResponse response, WarehouseEntryInfoDTO dto) throws Exception {
        if (StringUtils.isEmpty(dto.getBillType())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.TYPE_IS_EMPTY);
        }
        if (StringUtils.isEmpty(dto.getBillNo()) && StringUtils.isEmpty(dto.getInforBillNo()) && StringUtils.isEmpty(dto.getTaskNo())
                && StringUtils.isEmpty(dto.getProdplanId()) && StringUtils.isEmpty(dto.getSn()) && StringUtils.isEmpty(dto.getLpn())) {
            if (!checkTime(dto.getCreateDateStart(), dto.getCreateDateEnd())) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.EXPORT_TIME_THAN_THREE_MONTH);
            }
        }
        SimpleDateFormat sdf = new SimpleDateFormat(MpConstant.DATE_FORMAT_YYYYMMDDHHMM_ONE);
        String fileNameExport = sdf.format(new Date()) + Constant.WAREHOUSE_EXPORT_NAME_HEAD;
        ImesExcelUtil.setResponseHeader(response, fileNameExport);
        List<SysLookupValuesDTO> sysLookupValuesDTOS = getLookupTypes();
        // 最多导出5万条
        int maxLines = NumConstant.NUM_50000;
        WriteSheet writeSheet = EasyExcelFactory.writerSheet(0, Constant.WAREHOUSE_EXPORT_SHEET_NAME).build();
        int currentCount = 0;
        List<WarehouseEntryExportDTO> exportRows;


        ExcelConfig excelConfig = new ExcelConfig();
        excelConfig.setHeadNames(dto.getTitleList());
        excelConfig.setHeadFields(dto.getPropsList());
        ExcelWriter writer = EasyExcelFactory.write(response.getOutputStream()).head(EasyExcelUtils.nameList(excelConfig.getHeadNames()))
                .excelType(ExcelTypeEnum.XLSX).build();
        do {
            Page<WarehouseEntryExportDTO> pageParam = new Page<>();
            pageParam.setCurrent(++currentCount);
            pageParam.setSearchCount(false);
            // 一次查5千条
            pageParam.setPageSize(Constant.INT_5000);
            pageParam.setParams(dto);
            exportRows = warehouseEntryInfoRepository.exportWarehouseInfoHead(pageParam);
            if (CollectionUtils.isEmpty(exportRows)) {
                break;
            }
            maxLines -= exportRows.size();
            if (maxLines <= 0) {
                WarehouseEntryExportDTO msg = new WarehouseEntryExportDTO();
                msg.setBillNo(String.format(EXPORT_MAX_MSG, maxLines));
                exportRows.add(msg);
            }
            // 转换单据类型
            coverRowsProperties(exportRows, sysLookupValuesDTOS);
            List<Object> dataList = new ArrayList<>();
            exportRows.forEach(p->{dataList.add(p);});
            writer.write(EasyExcelUtils.dataList(dataList, excelConfig.getHeadFields()), writeSheet);
        } while (!CollectionUtils.isEmpty(exportRows) && maxLines > 0 && exportRows.size() == Constant.INT_5000);
        writer.finish();
    }

    private boolean checkTime(String startDate, String endDate) throws ParseException, MesBusinessException {
        if (StringUtils.isEmpty(startDate) || StringUtils.isEmpty(endDate)) {
            return false;
        }
        Date addedDate = CommonUtils.addDate(DateUtils.parseDate(startDate, DateUtils.DATE_FORMAT_19), NumConstant.NUM_THREE);
        if (addedDate == null) {
            return false;
        }
        return addedDate.getTime() > DateUtils.parseDate(endDate, DateUtils.DATE_FORMAT_19).getTime();
    }

    /**
     * 获取数据字典配置项
     *
     * @return 配置项
     * @throws Exception 业务异常
     */
    private List<SysLookupValuesDTO> getLookupTypes() throws Exception {
        List<String> lookUpList = new LinkedList<>();
        // 获取最大导出数量
        lookUpList.add(lookupType.VALUE_1004041);
        // 获取单据类型
        lookUpList.add(lookupType.VALUE_1048);
        // 单据状态
        lookUpList.add(lookupType.VALUE_1114);
        // erp 状态
        lookUpList.add(lookupType.VALUE_1004097);
        // 入库单明细转态
        lookUpList.add(lookupType.VALUE_1004098);
        lookUpList.add(Constant.LOOKUP_TYPE_5974);
        lookUpList.add(Constant.LOOKUP_TYPE_5979);
        lookUpList.add(Constant.LOOKUP_TYPE_5980);
        return BasicsettingRemoteService.getBatchSysValueByCode(lookUpList);
    }

    /**
     * 转换描述字段
     *
     * @param rows             查询行
     * @param valueByTypeCodes 数据字典配置项
     */
    private void coverRowsProperties(List<WarehouseEntryExportDTO> rows, List<SysLookupValuesDTO> valueByTypeCodes) {
        // 1 单据类型map
        Map<String, String> billTypeMap = new HashMap<>();
        Map<String, String> billStatusMap = new HashMap<>();
        Map<String, String> erpStatusMap = new HashMap<>();
        Map<String, String> detailStatusMap = new HashMap<>();
        Map<String, String> carryAccountMap = new HashMap<>();
        Map<String, String> outboundTypeMap = new HashMap<>();
        Map<String, String> boardPackErpSucceNodeMap = new HashMap<>();
        for (SysLookupValuesDTO valueByTypeCode : valueByTypeCodes) {
            if (Objects.isNull(valueByTypeCode.getLookupType())) {
                continue;
            }
            if (lookupType.VALUE_1048.equals(valueByTypeCode.getLookupType().toString())) {
                billTypeMap.put(valueByTypeCode.getLookupMeaning(), valueByTypeCode.getDescriptionChin());
            }
            if (lookupType.VALUE_1114.equals(valueByTypeCode.getLookupType().toString())) {
                billStatusMap.put(valueByTypeCode.getLookupMeaning(), valueByTypeCode.getDescriptionChin());
            }
            if (lookupType.VALUE_1004098.equals(valueByTypeCode.getLookupType().toString())) {
                detailStatusMap.put(valueByTypeCode.getLookupMeaning(), valueByTypeCode.getDescriptionChin());
            }
            if (lookupType.VALUE_1004097.equals(valueByTypeCode.getLookupType().toString())) {
                erpStatusMap.put(valueByTypeCode.getLookupMeaning(), valueByTypeCode.getDescriptionChin());
            }
            extracted(valueByTypeCode, carryAccountMap, outboundTypeMap, boardPackErpSucceNodeMap);
        }
        for (WarehouseEntryExportDTO row : rows) {
            // 单据类型
            row.setBillTypeDesc(billTypeMap.get(row.getBillType()));
            row.setBillStatusDesc(billStatusMap.get(row.getBillStatus()));
            row.setStatusDesc(detailStatusMap.get(row.getStatus()));
            row.setErpStatusDesc(erpStatusMap.get(row.getErpStatus()));
            if(StringUtils.equals(row.getZjSubcardFlag(), FLAG_Y)){
                row.setZjSubcardFlagName(Constant.COMPLETE_MACHINE_SUB_CARD);
            }else if(StringUtils.equals(row.getZjSubcardFlag(), FLAG_N)){
                row.setZjSubcardFlagName(Constant.COMPONENT_SUB_CARD);
            }
            row.setCarryAccount(carryAccountMap.get(row.getCarryAccount()));
            row.setOutboundType(outboundTypeMap.get(row.getOutboundType()));
            row.setBoardPackErpSucceNode(boardPackErpSucceNodeMap.get(row.getBoardPackErpSucceNode()));
        }
    }

    /***
     * 转换状态
     * @param valueByTypeCode
     * @param carryAccountMap
     * @param outboundTypeMap
     * @param boardPackErpSucceNodeMap
     */
    public void extracted(SysLookupValuesDTO valueByTypeCode, Map<String, String> carryAccountMap, Map<String, String> outboundTypeMap, Map<String, String> boardPackErpSucceNodeMap) {
        if (Constant.LOOKUP_TYPE_5980.equals(valueByTypeCode.getLookupType().toString())) {
            carryAccountMap.put(valueByTypeCode.getLookupMeaning(), valueByTypeCode.getDescriptionChin());
        }
        if (Constant.LOOKUP_TYPE_5974.equals(valueByTypeCode.getLookupType().toString())) {
            outboundTypeMap.put(valueByTypeCode.getLookupMeaning(), valueByTypeCode.getDescriptionChin());
        }
        if (Constant.LOOKUP_TYPE_5979.equals(valueByTypeCode.getLookupType().toString())) {
            boardPackErpSucceNodeMap.put(valueByTypeCode.getLookupMeaning(), valueByTypeCode.getDescriptionChin());
        }
    }

    @Override
    public WarehouseEntryInfoDTO scanedBillNo(WarehouseEntryInfoDTO dto) throws Exception {
        if (StringUtils.isEmpty(dto.getBillNo())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.TRANS_NO_IS_EMPTY);
        }
        Map<String, Object> map = new HashMap<>();
        map.put("billNo", dto.getBillNo());
        map.put("billType", dto.getBillType());
        WarehouseEntryInfoDTO warehouseEntryInfoDTO = warehouseEntryInfoRepository.getTransNoMsg(map);
        if (null == warehouseEntryInfoDTO) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.TRANS_NO_NOT_EXIST, new String[]{dto.getBillNo()});
        }
        PsWorkOrderBasic psWorkOrderBasic = PlanscheduleRemoteService.findWorkOrder(warehouseEntryInfoDTO.getWorkOrderNo());
        if(!Objects.isNull(psWorkOrderBasic)){
            warehouseEntryInfoDTO.setRouteId(psWorkOrderBasic.getRouteId());
        }
        warehouseEntryInfoDTO.setStock(datawbRemoteService.getApsOpProdplanStock(warehouseEntryInfoDTO.getProdplanNo()));
        return warehouseEntryInfoDTO;
    }

    /**
     * @return
     * <AUTHOR>
     * 转交单装箱扫描扫描条码
     * @Date 2022/11/22 16:31
     * @Param [com.zte.interfaces.dto.WarehouseEntryInfoDTO]
     **/
    @Override
    public List<PsWipInfo> transPackingScanSn(WarehouseEntryInfoDTO dto) throws Exception {
        List<String> snList = new ArrayList<>();
        // 扫描类型为批量则需要先转换条码
        if (StringUtils.equals(STR_1, dto.getScanType())) {
            if (StringUtils.isEmpty(dto.getStartSn()) || StringUtils.isEmpty(dto.getEndSn())) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SN_NULL_VALID);
            }
            snList = scanSnByBatch(dto);
        } else {
            snList.add(dto.getSn());
        }
        // 校验条码是否在转交单中存在
        List<WarehouseEntryDetail> detailList = this.checkSnInBillNo(dto, snList);
        // 校验条码是否已装箱
        this.checkSnInLpn(detailList);
        // 校验条码是否已转交到当前工序
        List<PsWipInfo> wipInfoList = this.checkSnTransToWorkStation(snList);
        // 获取条码线体建模信息
        List<CtRouteDetailDTO> ctRouteDetailDTOList = CrafttechRemoteService.getCtRouteByRouteId(null, wipInfoList.get(0).getLineCode());
        if (CollectionUtils.isEmpty(ctRouteDetailDTOList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.LINE_MODEL_NOT_FOUND);
        }
        String currProcessCode = wipInfoList.get(0).getCurrProcessCode();
        String lineCode = wipInfoList.get(0).getLineCode();
        String workOrderNo = wipInfoList.get(0).getWorkOrderNo();
        // 取第一个条码进行流程管控校验
        FlowControlConditionDTO conditionDTO = new FlowControlConditionDTO();
        conditionDTO.setSn(wipInfoList.get(0).getSn());
        conditionDTO.setCurrProcessCode(currProcessCode);
        conditionDTO.setLineCode(lineCode);
        conditionDTO.setWorkStation(ctRouteDetailDTOList.get(0).getNextProcess());
        conditionDTO.setSourceImu(ctRouteDetailDTOList.get(0).getSourceImu());
        conditionDTO.setWorkOrderNo(workOrderNo);
        psWipInfoServiceImp.checkFlowControled(conditionDTO);
        BSProcessDTO bsProcessDTO = new BSProcessDTO();
        bsProcessDTO.setProcessCode(currProcessCode);
        // 获取工序名称
        List<BSProcess> bsProcessList = CrafttechRemoteService.getBsProcessList(bsProcessDTO);
        if (CollectionUtils.isEmpty(bsProcessList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.GET_PROCESS_DETAILS_FAILURE);
        }
        List<String> flowFailedList = new ArrayList<>();
        for (PsWipInfo psWipInfo : wipInfoList) {
            // 与进行流程管控的条码进行对比，不同则视为流程管控校验失败
            if (!StringUtils.equals(currProcessCode, psWipInfo.getCurrProcessCode()) || !StringUtils.equals(lineCode, psWipInfo.getLineCode()) ||
                    !StringUtils.equals(workOrderNo, psWipInfo.getWorkOrderNo())) {
                flowFailedList.add(psWipInfo.getSn());
            }
            psWipInfo.setNextProcess(bsProcessList.get(0).getProcessName());
            psWipInfo.setLpn(dto.getLpn());
            psWipInfo.setWorkStation(ctRouteDetailDTOList.get(0).getNextProcess());
            psWipInfo.setSourceSysName(ctRouteDetailDTOList.get(0).getSourceSysName());
            psWipInfo.setSourceImu(ctRouteDetailDTOList.get(0).getSourceImu());
        }
        if (!CollectionUtils.isEmpty(flowFailedList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SN_FLOW_CTRL_FAILED, new String[]{flowFailedList.toString()});
        }
        return wipInfoList;
    }

    /**
     * @return
     * <AUTHOR>
     * 校验条码是否已经装箱
     * @Date 2022/11/22 20:23
     * @Param [java.util.List<com.zte.domain.model.WarehouseEntryDetail>]
     **/
    private void checkSnInLpn(List<WarehouseEntryDetail> detailList) throws Exception {
        StringBuilder sb = new StringBuilder();
        for (WarehouseEntryDetail detail : detailList) {
            if (!StringUtils.isEmpty(detail.getLpn())) {
                sb.append(detail.getSn()).append(",");
            }
        }
        if (!StringUtils.isEmpty(sb.toString())) {
            String boxedSn = sb.substring(0, sb.toString().length() - 1);
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SN_BOXED, new String[]{boxedSn});
        }
    }

    /**
     * @return
     * <AUTHOR>
     * 转交单装箱扫描 校验条码是否转交到当前工序
     * @Date 2022/11/22 20:10
     * @Param [com.zte.interfaces.dto.WarehouseEntryInfoDTO, java.util.List<java.lang.String>]
     **/
    private List<PsWipInfo> checkSnTransToWorkStation(List<String> snList) throws Exception {
        List<PsWipInfo> wipInfoList = new ArrayList<>();
        List<List<String>> splitList = CommonUtils.splitList(snList, BATCH_SIZE_NINE_HUNDRED);
        for (List<String> list : splitList) {
            wipInfoList.addAll(wipInfoRepository.getListByBatchSn(list));
        }
        StringBuilder sb = new StringBuilder();
        for (String s : snList) {
            for (int i = 0; i < wipInfoList.size(); i++) {
                if (s.equals(wipInfoList.get(i).getSn()) && STR_0.equals(wipInfoList.get(i).getWorkStation())) {
                    break;
                }
                if (i == wipInfoList.size() - 1) {
                    sb.append(s).append(",");
                }
            }
        }
        if (!StringUtils.isEmpty(sb.toString())) {
            String notInSn = sb.substring(0, sb.toString().length() - 1);
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SN_NOT_TRANS_TO_HERE, new String[]{notInSn});
        }
        return wipInfoList;
    }

    /**
     * @return
     * <AUTHOR>
     * 转交单装箱扫描 校验条码是否在转交单中存在
     * @Date 202/11/22 16:29
     * @Param [com.zte.interfaces.dto.WarehouseEntryInfoDTO]
     **/
    private List<WarehouseEntryDetail> checkSnInBillNo(WarehouseEntryInfoDTO dto, List<String> snList) throws Exception {
        List<List<String>> splitList = CommonUtils.splitList(snList, BATCH_SIZE_NINE_HUNDRED);
        List<WarehouseEntryDetail> detailList = new ArrayList<>();
        for (List<String> list : splitList) {
            WarehouseEntryInfoDTO entryInfoDTO = new WarehouseEntryInfoDTO();
            entryInfoDTO.setBillNo(dto.getBillNo());
            entryInfoDTO.setStrSnList(list);
            detailList.addAll(warehouseEntryDetailRepository.getDetailBySnList(entryInfoDTO));
        }
        StringBuilder sb = new StringBuilder();
        if (CollectionUtils.isEmpty(detailList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SN_NOT_IN_BILL_NO, new String[]{snList.toString(), dto.getBillNo()});
        }
        // 匹配传入的条码及查询出的条码
        for (String s : snList) {
            for (int i = 0; i < detailList.size(); i++) {
                if (s.equals(detailList.get(i).getSn()) && dto.getBillNo().equals(detailList.get(i).getBillNo())) {
                    break;
                }
                if (i == detailList.size() - 1) {
                    sb.append(s).append(",");
                }
            }
        }
        if (!StringUtils.isEmpty(sb.toString())) {
            String notInSn = sb.substring(0, sb.toString().length() - 1);
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SN_NOT_IN_BILL_NO, new String[]{notInSn, dto.getBillNo()});
        }
        return detailList;
    }

    /**
     * @return
     * <AUTHOR>
     * 转交单装箱扫描 整批扫描
     * @Date 202/11/22 16:29
     * @Param [com.zte.interfaces.dto.WarehouseEntryInfoDTO]
     **/
    private List<String> scanSnByBatch(WarehouseEntryInfoDTO dto) throws Exception {
        long startSn = Long.parseLong(dto.getStartSn());
        long endSn = Long.parseLong(dto.getEndSn());
        List<String> snList = new ArrayList<>();
        long snSize = endSn - startSn;
        for (int i = 0; i <= snSize; i++) {
            snList.add(startSn + i + "");
        }
        return snList;
    }

    /**
     * @return
     * <AUTHOR>
     * 转交单装箱扫描提交
     * @Date 2022/11/24 10:43
     * @Param [com.zte.interfaces.dto.WarehouseEntryInfoDTO]
     **/
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int transPackingScanSubmit(WarehouseEntryInfoDTO dto, String empNo) throws Exception {
        if (dto == null || CollectionUtils.isEmpty(dto.getSnList())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.DETAILS_CANNOT_BE_EMPTY_FOR_SUBMIT);
        }
        String lpn = dto.getLpn();
        if (StringUtils.isEmpty(lpn)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.TRANSFER_BOX_IS_NULL);
        }
        RedisLock redisLock = new RedisLock(RedisKeyConstant.TRANS_PACKING_SCAN_SUBMIT_LOCK + lpn, NumConstant.NUM_300);
        if (!redisLock.lock()) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.BILL_SUBMIT_ING, new String[]{lpn, dto.getBillNo()});
        }
        try {
            List<PsWipInfoDTO> wipInfoDTOList = dto.getSnList();
            List<String> snList = new ArrayList<>();
            PsWipInfoDTO wipInfoDTO = new PsWipInfoDTO();
            for (PsWipInfoDTO psWipInfoDTO : wipInfoDTOList) {
                snList.add(psWipInfoDTO.getSn());
            }
            wipInfoDTO.setWorkStation(wipInfoDTOList.get(0).getWorkStation());
            wipInfoDTO.setLastUpdatedBy(empNo);
            wipInfoDTO.setSnList(snList);
            // 校验条码是否在转交单中存在
            List<WarehouseEntryDetail> detailList = this.checkSnInBillNo(dto, snList);
            // 校验条码是否已装箱
            this.checkSnInLpn(detailList);
            this.insertContentInfo(dto.getSnList());
            updateLpnBySnList(dto, snList);
            updateWipInfo(wipInfoDTOList, snList);
            return checkTransNoAllBoxed(dto);
        } finally {
            redisLock.unlock();
        }
    }

    @Override
    public int pullOuterSnDetail(String billNoParam, Integer batchCnt) {
        if (batchCnt == null || batchCnt > INT_100) {
            batchCnt = INT_100;
        }
        int cnt = INT_0;
        List<String> billNos = warehouseEntryInfoRepository.getUnconfirmedOuterBill(billNoParam, batchCnt);
        if (CollectionUtils.isEmpty(billNos)) {
            return cnt;
        }
        for (String billNo : billNos) {
            // 每个单据独立事务控制
            cnt += pullSingleSnDetail(billNo);
        }
        return cnt;
    }

    /**
     * 处理单个单据，独立事务
     *
     * @param billNo
     * @return
     */
    private int pullSingleSnDetail(String billNo) {
        TransactionStatus transactionStatus = null;
        try {
            // 根据单号获取barSubmit
            List<BarSubmitDTO> barSubmits = datawbRemoteService.queryBarSubmitInfo(Lists.newArrayList(billNo));
            if (CollectionUtils.isEmpty(barSubmits)) {
                return INT_0;
            }
            // 筛选已提交
            List<Long> serialIds = barSubmits.stream()
                    .filter(e -> e.getStatus() != null && INT_0 != e.getStatus().intValue())
                    .filter(e -> e.getSerialId() != null)
                    .map(BarSubmitDTO::getSerialId)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(serialIds)) {
                return INT_0;
            }
            // 根据serialIds查询条码信息
            List<SemisBoxDTO> semisBoxes = datawbRemoteService.querySemisBox(serialIds);
            if (CollectionUtils.isEmpty(semisBoxes)) {
                return INT_0;
            }
            // 条码信息
            List<WarehouseEntryDetailDTO> details = new ArrayList();
            for (SemisBoxDTO boxDTO : semisBoxes) {
                WarehouseEntryDetailDTO detailDTO = new WarehouseEntryDetailDTO();
                detailDTO.setWarehouseEntryDetailId(UUID.randomUUID().toString());
                detailDTO.setBillNo(billNo);
                detailDTO.setProdplanId(boxDTO.getProdPlanId().toString());
                detailDTO.setSn(boxDTO.getSn());
                detailDTO.setStatus(STR_TWO);
                detailDTO.setCreateBy(SYSTEM);
                detailDTO.setLastUpdatedBy(SYSTEM);
                details.add(detailDTO);
            }
            transactionStatus = transactionManager.getTransaction(transactionDefinition);
            // 分批保存入库详情
            List<List<WarehouseEntryDetailDTO>> lists = CommonUtils.splitList(details);
            for (List<WarehouseEntryDetailDTO> list : lists) {
                warehouseEntryInfoRepository.insertBatchWithStatus(list);
                // 处理条码信息
                dealWip(list);
            }
            // 修改入库头表状态为已确认
            WarehouseEntryInfoDTO infoDTO = new WarehouseEntryInfoDTO();
            infoDTO.setBillNo(billNo);
            infoDTO.setStatus(STR_ONE);
            infoDTO.setLastUpdatedBy(SYSTEM);
            warehouseEntryInfoRepository.updateWarehouseEntryInfosByBillNo(infoDTO);
            transactionManager.commit(transactionStatus);
            return INT_1;
        } catch (Exception e) {
            // 异常回滚
            if (transactionStatus != null) {
                transactionManager.rollback(transactionStatus);
            }
            return INT_0;
        }
    }

    public void dealWip(List<WarehouseEntryDetailDTO> list) {
        List<String> sns = list.stream().map(WarehouseEntryDetailDTO::getSn).collect(Collectors.toList());
        wipInfoRepository.updateCraftToWarehousing(sns);
        wipScanHistoryRepository.insertForPullOuterSn(sns);
    }

    @Override
    public void syncFormSpm(List<String> planIds) throws Exception {
        if (CollectionUtils.isEmpty(planIds)) {
            return;
        }
        List<BarSubmitDTO> barSubmits = new ArrayList();
        List<List<String>> planIdLists = CommonUtils.splitList(planIds);
        for (List<String> planIdList: planIdLists) {
            List<BarSubmitDTO> barSubmitList = datawbRemoteService.dataWbGetBarSubmitByPlanIds(planIdList);
            if (!CollectionUtils.isEmpty(barSubmitList)) {
                barSubmits.addAll(barSubmitList);
            }
        }
        if (CollectionUtils.isEmpty(barSubmits)) {
            return;
        }
        List<List<BarSubmitDTO>> barSubmitLists = CommonUtils.splitList(barSubmits);
        for (List<BarSubmitDTO> subBarSubmit: barSubmitLists) {
            List<String> existBills = warehouseEntryInfoRepository
                    .getByBillNo(subBarSubmit.stream().map(BarSubmitDTO::getBillNo).collect(Collectors.toList()));
            if (!CollectionUtils.isEmpty(existBills)) {
                subBarSubmit = subBarSubmit.stream()
                        .filter(e -> !existBills.contains(e.getBillNo()))
                        .collect(Collectors.toList());
            }
            if (CollectionUtils.isEmpty(subBarSubmit)) {
                continue;
            }
            warehouseEntryInfoRepository.insertByBarSubmits(subBarSubmit);
        }
    }


    /**
     * @return
     * <AUTHOR>
     * 将条码过站
     * @Date 2022/11/26 9:53
     * @Param [java.util.List<com.zte.interfaces.dto.PsWipInfoDTO>, java.util.List<java.lang.String>]
     **/
    private void updateWipInfo(List<PsWipInfoDTO> wipInfoDTOList, List<String> snList) throws Exception {
        List<FlowControlConditionDTO> flowList = new ArrayList<>();
        for (PsWipInfoDTO psWipInfo : wipInfoDTOList) {
            FlowControlConditionDTO conditionDTO = new FlowControlConditionDTO();
            conditionDTO.setSn(psWipInfo.getSn());
            conditionDTO.setCurrProcessCode(psWipInfo.getCurrProcessCode());
            conditionDTO.setLineCode(psWipInfo.getLineCode());
            conditionDTO.setWorkStation(psWipInfo.getWorkStation());
            conditionDTO.setWorkOrderNo(psWipInfo.getWorkOrderNo());
            conditionDTO.setSnList(snList);
            conditionDTO.setSourceSysName(psWipInfo.getSourceSysName());
            conditionDTO.setSourceImu(psWipInfo.getSourceImu());
            flowList.add(conditionDTO);
        }
        List<List<FlowControlConditionDTO>> listOfList = CommonUtils.splitList(flowList, CommonConst.BATCH_SIZE);
        for (List<FlowControlConditionDTO> paramsList : listOfList) {
            psWipInfoService.pmWipSaveBatch(paramsList);
        }
    }

    /**
     * @return
     * <AUTHOR>
     * 校验该转交单是否已全部装箱
     * @Date 2022/11/24 14:19
     * @Param []
     **/
    private int checkTransNoAllBoxed(WarehouseEntryInfoDTO dto) throws Exception {
        int notBoxedSnQty = warehouseEntryDetailRepository.getNoBoxedSnCountByBillNo(dto.getBillNo());
        if (Constant.INT_0 == notBoxedSnQty) {
            dto.setStatus(STR_1);
            warehouseEntryInfoRepository.updateWarehouseEntryInfosByBillNo(dto);
        }
        return notBoxedSnQty;
    }

    /**
     * @return
     * <AUTHOR>
     * 转交装箱扫描更改条码箱信息
     * @Date 2022/11/24 11:29
     * @Param [com.zte.interfaces.dto.WarehouseEntryInfoDTO]
     **/
    private void updateLpnBySnList(WarehouseEntryInfoDTO dto, List<String> snList) throws Exception {
        List<List<String>> splitList = CommonUtils.splitList(snList, BATCH_SIZE);
        for (List<String> list : splitList) {
            dto.setStrSnList(list);
            warehouseEntryDetailRepository.packingLpnBySnList(dto);
        }
    }

    /**
     * @return
     * <AUTHOR>
     * 转交单装箱扫描新增箱内容
     * @Date 2022/11/24 11:16
     * @Param [java.util.List<com.zte.interfaces.dto.PsWipInfoDTO>]
     **/
    private void insertContentInfo(List<PsWipInfoDTO> snList) throws Exception {
        List<ContainerContentInfoDTO> contentInfoDTOList = new ArrayList<>();
        for (PsWipInfoDTO dto : snList) {
            ContainerContentInfoDTO contentInfoDTO = new ContainerContentInfoDTO();
            contentInfoDTO.setLpn(dto.getLpn());
            contentInfoDTO.setEntityIdentification(dto.getSn());
            contentInfoDTO.setEntityType(ENTITY_TYPE_SN);
            contentInfoDTO.setTaskBelongsTo(dto.getWorkOrderNo());
            contentInfoDTO.setAttribute1(dto.getAttribute1());
            contentInfoDTO.setRemark(dto.getWorkOrderNo());
            contentInfoDTO.setItemCode(dto.getItemNo());
            contentInfoDTO.setItemName(dto.getItemName());
            contentInfoDTO.setCurProcessCode(dto.getCurrProcessCode());
            contentInfoDTO.setWorkStation(dto.getWorkStation());
            contentInfoDTO.setParentSn(dto.getParentSn());
            contentInfoDTO.setSourceSys(dto.getSourceSys());
            contentInfoDTOList.add(contentInfoDTO);
        }
        ProductionDeliveryRemoteService.batchInsertContainerContentInfo(contentInfoDTOList);
    }

    @Override
    public WarehouseEntryInfoQueryDTO getBoardOnlineInfo(WarehouseEntryInfoDTO dto) throws Exception {
        WarehouseEntryInfoQueryDTO warehouseEntryInfoQueryDTO = new WarehouseEntryInfoQueryDTO();
        warehouseEntryInfoQueryDTO.setQueryStatus(Constant.FLAG_Y);
        //获取任务列表
        String prodplanId = dto.getProdplanId();
        Map<String, Object> map = new HashMap<>();
        map.put("prodplanId", prodplanId);
        long page = new Long(dto.getPage().toString());
        long rows = new Long(dto.getRows().toString());
        List<PsTask> psTaskList = getPsTasks(map);
        if (CollectionUtils.isEmpty(psTaskList)) {
            warehouseEntryInfoQueryDTO.setQueryStatus(Constant.FLAG_N);
            warehouseEntryInfoQueryDTO.setErrorMessage(CommonUtils.getLmbMessage(MessageId.TRACKING_OR_TASK_NO_NOT_EXIST));
            return warehouseEntryInfoQueryDTO;
        }
        String currProcess = "N";
        BeanUtils.copyProperties(psTaskList.get(NUM_ZERO), warehouseEntryInfoQueryDTO);
        String leadFlag = psTaskList.get(NUM_ZERO).getLeadFlag();
        if (!StringUtils.isEmpty(leadFlag)) {
            warehouseEntryInfoQueryDTO.setIsLead(leadFlag);
        }
        // 设置任务相关属性，返回该任务是否锁定条码
        setTaskProperty(map, warehouseEntryInfoQueryDTO, psTaskList);
        //查询指令信息
//        Map<String, String> mapOrder = new HashMap<>();
//        mapOrder.put("taskNo", warehouseEntryInfoQueryDTO.getTaskNo());
//        List<PsWorkOrderBasic> psWorkOrderBasicList = getWorkorderBasicInfo(mapOrder);
//        if (CollectionUtils.isEmpty(psWorkOrderBasicList)) {
//            warehouseEntryInfoQueryDTO.setQueryStatus(Constant.FLAG_N);
//            warehouseEntryInfoQueryDTO.setErrorMessage(CommonUtils.getLmbMessage(MessageId.WORDER_ORDER_NOT_FOUND));
//            return warehouseEntryInfoQueryDTO;
//        }
//        PsWipInfoQueryDTO psWipInfoQueryDTO = new PsWipInfoQueryDTO();
//        psWipInfoQueryDTO.setCurrProcessCode(currProcess);
//        psWipInfoQueryDTO.setPage(page);
//        psWipInfoQueryDTO.setRows(rows);// 单板条码查询：根据指令查询
//        setWorkOrderNo(warehouseEntryInfoQueryDTO, psWorkOrderBasicList, psWipInfoQueryDTO);
//        psWipInfoQueryDTO.setOriginalTask(psTaskList.get(NUM_ZERO).getOriginalTask());
        // 获取lms.board_online返修条码信息
        Map<String, Object> mapQuery = new HashMap<>();
        mapQuery.put("prodplanId", new BigDecimal(prodplanId.toString()));
        mapQuery.put("isRepair", new BigDecimal(STR_ONE));
        mapQuery.put("notInSnList", dto.getNotInSnList());
        mapQuery.put("page", page);
        mapQuery.put("rows", rows);
        Page<BoardOnline> boardOnlinePage = datawbRemoteService.getBoardOnlineBatch(mapQuery);
        if (boardOnlinePage == null || CollectionUtils.isEmpty(boardOnlinePage.getRows())) {
            warehouseEntryInfoQueryDTO.setQueryStatus(Constant.FLAG_N);
            warehouseEntryInfoQueryDTO.setErrorMessage(CommonUtils.getLmbMessage(MessageId.WAIT_IN_WAREHOUSE_NOT_EXIST));
            return warehouseEntryInfoQueryDTO;
        }
        List<BoardOnline> boardOnlines = boardOnlinePage.getRows();
        List<String> fxSnList = getBoardOnlineSnList(boardOnlines);
        // 单板返修入库单在wip_info中可能无数据，故屏蔽此处查询wip_info的处理
        List<PsWipInfoDTO> psWipList = new ArrayList<>();
//        List<PsWipInfoDTO> psWipList = psWipInfoService.getWipInfoList(fxSnList);
//        if (CollectionUtils.isEmpty(psWipList)) {
//            warehouseEntryInfoQueryDTO.setCurrProcess(currProcess);
//            if (!StringUtils.isEmpty(warehouseEntryInfoQueryDTO.getProdplanNo())) {
//                warehouseEntryInfoQueryDTO.setStock(datawbRemoteService.getApsOpProdplanStock(warehouseEntryInfoQueryDTO.getProdplanNo()));
//            }
//            warehouseEntryInfoQueryDTO.setQueryStatus(Constant.FLAG_N);
//            warehouseEntryInfoQueryDTO.setErrorMessage(CommonUtils.getLmbMessage(MessageId.WAIT_IN_WAREHOUSE_NOT_EXIST));
//            return warehouseEntryInfoQueryDTO;
//        }
        List<PsWipInfo> wipInfoList = setPsWipInfoList(currProcess, fxSnList, psWipList);
        setLpnByContent(wipInfoList);
        warehouseEntryInfoQueryDTO.setPsWipInfoList(wipInfoList);
        warehouseEntryInfoQueryDTO.setCurrProcess(currProcess);
        warehouseEntryInfoQueryDTO.setTotal(new Long(String.valueOf(wipInfoList.size())));
        warehouseEntryInfoQueryDTO.setPage(page);
        warehouseEntryInfoQueryDTO.setRows(rows);

        //仓库
        setStock(warehouseEntryInfoQueryDTO);

        warehouseEntryInfoQueryDTO.setProductType(psTaskList.get(Constant.INT_0).getProductType());
        warehouseEntryInfoQueryDTO.setOrgId(psTaskList.get(Constant.INT_0).getOrgId());
        return warehouseEntryInfoQueryDTO;
    }

    private List<PsWipInfo> setPsWipInfoList(String currProcess, List<String> fxSnList, List<PsWipInfoDTO> psWipList) {
        List<PsWipInfo> wipInfoList = toWipInfoList(psWipList);
        for (String fxSn : fxSnList) {
            PsWipInfo temp = wipInfoList.stream().filter(e -> fxSn.equals(e.getSn())).findFirst().orElse(null);
            if (temp == null) {
                PsWipInfo psWipInfo = new PsWipInfo();
                psWipInfo.setSn(fxSn);
                psWipInfo.setCurrProcessCode(currProcess);
                wipInfoList.add(psWipInfo);
            }
        }
        return wipInfoList;
    }

    /**
     * // TODO 添加方法功能描述
     *
     * @param dtoList
     * @return List<WipTestRecode>
     **/
    public static List<PsWipInfo> toWipInfoList(List<PsWipInfoDTO> dtoList) {

        List<PsWipInfo> entityList = new ArrayList<PsWipInfo>();
        for (PsWipInfoDTO dto : dtoList) {
            entityList.add(toEntity(dto));
        }
        return entityList;
    }

    /**
     * // TODO 添加方法功能描述
     *
     * @param dto
     * @return BsPremanuTraceInfo
     **/
    public static PsWipInfo toEntity(PsWipInfoDTO dto) {
        PsWipInfo entity = new PsWipInfo();
        BeanUtils.copyProperties(dto, entity);
        return entity;
    }

    private List<String> getBoardOnlineSnList(List<BoardOnline> boardOnlineList) {
        List<String> snList = new ArrayList<>();
        boardOnlineList.forEach(boardOnline -> {
            StringBuffer sb = new StringBuffer(boardOnline.getProdplanId().toString());
            int length = boardOnline.getBoardSn().toString().length();
            if (length >= NumConstant.NUM_FIVE) {
                sb.append(boardOnline.getBoardSn().toString());
            } else {
                sb.append(StringUtils.leftPad(boardOnline.getBoardSn().toString(), 5, "0"));
            }
            snList.add(sb.toString());
        });
        //调用中心工厂接口获得批次对应的分配工厂ID
        return snList.stream().filter(item -> StringUtils.isNotEmpty(item)).distinct().collect(Collectors.toList());
    }

    @Override
    public List<PsWipInfoDTO> queryBoardOnlineInfo(BoardOnline dto) throws Exception {
        Map<String, Object> map = CommonUtils.object2Map(dto);
        if (StringUtils.isNotEmpty(dto.getSyncSn())) {
            map.put("prodplanId", new BigDecimal(dto.getSyncSn().substring(0, 7)));
            map.put("boardSn", new BigDecimal(dto.getSyncSn().substring(7, 12)));
            map.put("isRepair", new BigDecimal(STR_1));
            map.put("page", NUM_ONE);
            map.put("rows", NUM_TEN);
        }
        Page<BoardOnline> boardOnlineList = datawbRemoteService.getBoardOnlineBatch(map);
        List<String> fxSnList = getBoardOnlineSnList(boardOnlineList.getRows());
//        List<PsWipInfoDTO> psWipList = psWipInfoService.getWipInfoList(fxSnList);
        // 单板返修入库单查不到在制表信息，直接组装一个List返回
        List<PsWipInfoDTO> psWipList = new ArrayList<>();
        for (String fxSn : fxSnList) {
            PsWipInfoDTO psWipInfoDTO = new PsWipInfoDTO();
            psWipInfoDTO.setSn(fxSn);
            // 工序设置成入库N
            psWipInfoDTO.setCurrProcessCode(FLAG_N);
            psWipList.add(psWipInfoDTO);
        }
        return psWipList;
    }

	/**
	 * 批量更新重复入库单状态
	 *
	 * @param empNo，billNoList
	 * @return BsItemInfo
	 **/
	@Transactional(rollbackFor = Exception.class)
	public void batchUpdateStatus(String empNo, List<String> billNoList) throws Exception {
		if (CollectionUtils.isEmpty(billNoList)) {
			return;
		}
		List<String> distList = billNoList.stream().distinct().collect(Collectors.toList());
		List<List<String>> listOfList = CommonUtils.splitList(distList, Constant.BATCH_SIZE);
		//不存在的单据报错
		this.checkParam(distList,listOfList);
		// 插入前赋值
		for (List<String> tempList : listOfList) {
			WarehouseEntryDetailDTO dto = new WarehouseEntryDetailDTO();
			dto.setLastUpdatedBy(empNo);
			dto.setBillNoList(tempList);
			dto.setStatus(Constant.CONFIRMED_VALUE);
			dto.setAttribute2(Constant.DATA_REMARK);
			// 把入库单状态改为已确认,并备注SPM数据迁移
			warehouseEntryInfoRepository.updateStatusByBillNo(dto);
			// 把详细表状态改为已接收--status重新赋值
			dto.setStatus(Constant.RECEPTTION_VALUE);
			warehouseEntryInfoRepository.updateDetailStatusByBillNo(dto);
		}
	}

    @Override
    public void mixWarehouseSubmit(WarehouseEntryInfoQueryDTO dto, String factoryId, String empNo) throws Exception {
        //调用回写服务校验物料需求，组织ID与实体ID； 数据源： erp  表  WIP.WIP_REQUIREMENT_OPERATIONS
        List<ItemListEntityDTO> itemListEntityDTOList = getItemListEntityListByTaskNo(dto.getTaskNo(), dto.getOrgId());
        if (CollectionUtils.isEmpty(itemListEntityDTOList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.TASK_NOT_QUERY_REQUIREMENTS);
        }
        // 校验批次总数
        String errorMsg = this.checkQty(dto, itemListEntityDTOList);
        if (StringUtils.isNotBlank(errorMsg)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.CUSTOMIZE_MSG,
                    new String[] {errorMsg});
        }
        // 保存入库单
        this.saveWarehouse(dto, factoryId, STR_2, empNo, new HashMap());
    }

    private void checkParam(List<String> distList, List<List<String>> listOfList)throws Exception {
		List<String> existList = new ArrayList();
		for (List<String> tempList : listOfList) {
			List<String> existSubList = warehouseEntryInfoRepository.getByBillNo(tempList);
			if(!CollectionUtils.isEmpty(existSubList)){
				existList.addAll(existSubList);
			}
		}
		if(CollectionUtils.isEmpty(existList)){
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.BILL_NO_NOT_EXIST, new Object[]{StringUtils.join(distList.toArray(), Constant.COMMA)});
		}else if(existList.size() != distList.size()){
			distList.removeAll(existList);
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.BILL_NO_NOT_EXIST, new Object[]{StringUtils.join(distList.toArray(), Constant.COMMA)});
		}
	}

    @Override
    public int getCountByProdplanId(String prodplanId) {
        return warehouseEntryInfoRepository.getCountByProdplanId(prodplanId);
    }

    /**
     *<AUTHOR>
     * 根据billNo获取打印批次及数量
     *@Date 2023/6/19 20:16
     *@Param [java.lang.String]
     *@return
     **/
    @Override
    public List<WarehouseEntryInfoDTO> getPrintDetailProdByBillNo(String billNo) {
        return warehouseEntryInfoRepository.getPrintDetailProdByBillNo(billNo);
    }

    /**
     * 根据billNo查询对应的箱号
     */
    @Override
    public List<String> getLpnByBillNo(String billNo) {
        List<String> res = warehouseEntryInfoRepository.getLpnByBillNo(billNo);
        return res.stream().filter(p -> StringUtils.isNotEmpty(p)).sorted().collect(Collectors.toList());
    }

    @Override
    public void deleteWarehouseEntryByBillNo(String billNo) {
        warehouseEntryInfoRepository.deleteWarehouseEntryByBillNo(billNo);
    }

    @Override
    public void deleteWarehouseEntryAdditionalInfo(String billNo) {
        warehouseEntryInfoRepository.deleteWarehouseEntryAdditionalInfo(billNo);
    }

    /**
     * 根据主键更新成功节点
     *
     * @param record
     **/
    @Override
    public int updateWarehouseEntryInfoByIdSucceNode(WarehouseEntryInfo record) {
        return warehouseEntryInfoRepository.updateWarehouseEntryInfoByIdSucceNode(record);
    }

    /***
     * 获取推送失败记录
     * @param dto
     * @return
     */
    @Override
    public List<WarehouseEntryInfo> getBoardPackageWarehouseInfoJob(WarehouseEntryInfo dto){
        return warehouseEntryInfoRepository.getBoardPackageWarehouseInfoJob(dto);
    }

    /***
     * 获取入库单明细数据
     * @param billNo
     * @return
     */
    @Override
    public List<WarehouseEntryDetailDTO> getBoardPackageWarehouseDetail(String billNo){
        return warehouseEntryDetailRepository.getDetailByBillNoList(billNo);
    }

    /***
     * 打印单板包装扫描入库单数据
     * @param billNo
     * @return
     */
    @Override
    public List<WarehouseEntryInfoDTO> printWarehouseDetail(String billNo) throws Exception {
        List<WarehouseEntryInfoDTO> list=warehouseEntryInfoRepository.printWarehouseDetail(billNo);
        if(CollectionUtils.isEmpty(list)){
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PRINT_INSTOCK_DATA_ERROR);
        }
        // 获取批次信息
        List<String> ids=new ArrayList<>();
        ids.add(list.get(INT_0).getProdplanId());
        List<PsTask> psTaskList = PlanscheduleRemoteService.getPsTaskByProdplanIdList(ids);
        if (CollectionUtils.isEmpty(psTaskList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PRODPLANID_IS_NOT_EXITS, new String[]{list.get(INT_0).getProdplanId()});
        }
        //获取转材料代码
        String itemNo=STR_EMPTY;
        if(Constant.POWER_MODULES_TO_RAW_MATERIALS.equals(psTaskList.get(INT_0).getStock())||StringUtils.contains(psTaskList.get(INT_0).getStock(),Constant.POWER_MODULES_TO_RAW_MATERIALS)){
            itemNo = datawbRemoteService.getItemTransfer(list.get(INT_0).getItemNo());
        }
        //获取环保属性目的仓名称数据字典
        SysLookupValuesDTO sysLookupValuesDTO = new SysLookupValuesDTO();
        sysLookupValuesDTO.setLookupTypes(Constant.LOOKUP_TYPE_INSTOCK_PRINT);
        List<SysLookupValuesDTO> sysLookupValuesDTOS= BasicsettingRemoteService.getLookupTypesByValue(sysLookupValuesDTO);
        String isLeadName = getlookupDesc(sysLookupValuesDTOS,Constant.LOOK_UP_TYPE_LEAD,list.get(INT_0).getIsLead());
        String destinationWarehouseName = getlookupDesc(sysLookupValuesDTOS,Constant.LOOKUP_TYPE_5973,psTaskList.get(INT_0).getProdAddress());
        // 根据工号查询用户信息
        BsPubHrvOrgId bsPubHrvOrgId = hrmUserInfoService.getBsPubHrvOrgIdInfoByUserId(list.get(INT_0).getCreateBy());
        //员工姓名
        String userName=bsPubHrvOrgId == null ? list.get(INT_0).getCreateBy() : bsPubHrvOrgId.getUserName()+bsPubHrvOrgId.getUserId();
        //赋值
        for (WarehouseEntryInfoDTO item : list) {
            item.setSku(itemNo);
            item.setIsLead(isLeadName);
            item.setDestinationWarehouse(destinationWarehouseName);
            item.setCreateBy(userName);
        }
        return list;
    }

    @Override
    public List<WarehouseEntryInfoDTO> getWarehouseEntryInfoDTOListByTaskNos(Set<String> taskNos) {
        return warehouseEntryInfoRepository.getWarehouseEntryInfoDTOListByTaskNos(taskNos);
    }

    @Override
    public List<WarehouseEntryInfoDTO> taskStockQuery(WarehouseBatchQueryDTO warehouseBatchQueryDTO) {
        return warehouseEntryInfoRepository.taskStockQuery(warehouseBatchQueryDTO);
    }

    public String getlookupDesc(List<SysLookupValuesDTO> list, String lookupType, String attribute) throws Exception{
        SysLookupValuesDTO lookupValuesDTO = list.stream().filter(t -> lookupType.equals(String.valueOf(t.getLookupType())) && attribute.equals(String.valueOf(t.getAttribute1()))).findFirst().orElse(null);
        if(null == lookupValuesDTO){
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SYS_LOOK_NOT_CONFIG,new String[]{lookupType+Constant.FIELD_LOOKUP_ATTRIBUTE1});
        }
        return lookupValuesDTO.getDescriptionChin();
    }
}
