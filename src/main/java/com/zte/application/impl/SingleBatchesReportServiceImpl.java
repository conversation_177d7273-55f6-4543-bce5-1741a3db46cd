package com.zte.application.impl;
import com.zte.application.PkCodeInfoService;
import com.zte.application.SingleBatchesReportService;
import com.zte.application.SmtMachineMTLHistoryLService;
import com.zte.application.SmtSnMtlTracingTService;
import com.zte.common.CommonUtils;
import com.zte.common.DateUtil;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.domain.vo.SingleBatchesReportVo;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * SMT扫描率日报 业务处理类
 * <AUTHOR>
 */
@Service
public class SingleBatchesReportServiceImpl implements SingleBatchesReportService {

    /**
     * 日志
     */
    private static final Logger LOG = LoggerFactory.getLogger(SingleBatchesReportServiceImpl.class);

    @Autowired
    private SmtMachineMTLHistoryLService smtMachineMtlHistorylService;
    @Autowired
    private SmtSnMtlTracingTService smtSnmtlTracingtService;
    @Autowired
    private PkCodeInfoService pkCodeInfoService;



    /**
     * 获取SMT扫描率数据
     * @return List<SingleBatchesReportVo>
     * @throws Exception
     */
    @Override
    public List<SingleBatchesReportVo> getPcbScale(String factoryId) throws Exception{
        Date startTimeToActualStart = DateUtil.getBeforeOrAfterStartTime(-3);
        Date queryStartTime = DateUtil.getBeforeStartTime();
        Date queryEndTime = DateUtil.getBeforeEndTime();
        QueryWorkOrderBasicDTO queryWorkOrderBasicDto = new QueryWorkOrderBasicDTO();
        queryWorkOrderBasicDto.setStartTimeToActualStart(startTimeToActualStart);
        queryWorkOrderBasicDto.setEndTimeToActualStart(queryEndTime);
        queryWorkOrderBasicDto.setStartTimeToActualEndTime(queryStartTime);
        queryWorkOrderBasicDto.setEndTimeToActualEndTime(queryEndTime);
        LOG.info("前三天开始时间：{}，昨天开始时间：{}。昨天结束时间：{}",
                DateUtil.getBeforeOrAfterStartTime(-3),DateUtil.getBeforeStartTime(),DateUtil.getBeforeEndTime());
        List<SingleBatchesReportVo> singleBatchesReportVoList = new ArrayList<>();
        List<PsWorkOrderBasicDTO> list = PlanscheduleRemoteService.selectWorkOrderBasicByActualStartDateAndEndTime(queryWorkOrderBasicDto);
        if(null == list || list.isEmpty()){
            return singleBatchesReportVoList;
        }
        List<String> sourceTaskList = list.stream().map(PsWorkOrderBasicDTO::getSourceTask).distinct().collect(Collectors.toList());
        // 查询参数类
        SmtSnMtlTracingTDTO smtSnMtlTracingtDto =  new SmtSnMtlTracingTDTO();
        smtSnMtlTracingtDto.setFactoryId(Long.parseLong(factoryId));
        smtSnMtlTracingtDto.setProductBatchCodeList(sourceTaskList);
        List<SmtSnMtlTracingCountDTO> countDtoList =
              smtSnmtlTracingtService.countReelIdAndWorkOderByProductBatchCodeAndSnScanDate(smtSnMtlTracingtDto);
        //指令表
        Map<String,PsWorkOrderBasicDTO> basicMap = list.stream().collect(Collectors.toMap(PsWorkOrderBasicDTO::getSourceTask, a -> a, (k1, k2) -> k1));
        //追溯数据转换
        Map<String, SmtSnMtlTracingCountDTO> smtSnMtlTracMap = new HashMap<>();
        if(null != countDtoList && !countDtoList.isEmpty()) {
            smtSnMtlTracMap = countDtoList.stream().collect(Collectors.toMap(SmtSnMtlTracingCountDTO::getProductBatchCode, a -> a, (k1, k2) -> k1));
        }
        //批次注册的Reel ID数量
        Map<String, PkCodeInfoDTO> pkCodeInfoMap = new HashMap<>();
        List<PkCodeInfoDTO> pkCodeInfoList = this.getPkCodeInfoMap(sourceTaskList);
        if(null != pkCodeInfoList && !pkCodeInfoList.isEmpty()){
            pkCodeInfoMap = pkCodeInfoList.stream().collect(Collectors.toMap(PkCodeInfoDTO::getProductTask, a -> a, (k1, k2) -> k1));
        }
        // 上料历史行数据
        Map<String, QuerySmtMachineMTLHistoryLDTO> historylMap = new HashMap<>();
        List<QuerySmtMachineMTLHistoryLDTO> historylDtoList = this.getBatchReelIdList(sourceTaskList,factoryId);
        if(null != historylDtoList && !historylDtoList.isEmpty()){
            historylMap = historylDtoList.stream().collect(Collectors.toMap(QuerySmtMachineMTLHistoryLDTO::getProductBatchCode, a -> a, (k1, k2) -> k1));
        }
        for(String sourceTask :sourceTaskList){
            SingleBatchesReportVo singleBatchesReportVo = new SingleBatchesReportVo();
            // 单板批次数
            BigDecimal totalWorkOderQty = BigDecimal.ZERO;
            // 该批次条码数量
            BigDecimal snCount = BigDecimal.ZERO;
            //reelId数量
            BigDecimal reelIdCount = BigDecimal.ZERO;
            // 该批次注册的ReelId数量
            BigDecimal reelIdTaskCount = BigDecimal.ZERO;
            // 扫描的ReelId数量
            BigDecimal reelIdScanCount = BigDecimal.ZERO;
            totalWorkOderQty = this.getTotalWorkOderQty(basicMap, sourceTask, totalWorkOderQty);
            if(null != smtSnMtlTracMap && !smtSnMtlTracMap.isEmpty() && smtSnMtlTracMap.containsKey(sourceTask)){
                reelIdCount = smtSnMtlTracMap.get(sourceTask).getReelIdCount();
                snCount = smtSnMtlTracMap.get(sourceTask).getSnCount();
            }
            reelIdTaskCount = this.getReelIdTaskCount(pkCodeInfoMap, sourceTask, reelIdTaskCount);
            reelIdScanCount = this.getReelIdScanCount(historylMap, sourceTask, reelIdScanCount);
            String workOrderNoScanPercent = this.countWorkOrderNoPercent(snCount, totalWorkOderQty);
            String reelIdPercent = this.countReelIdPercent(reelIdCount, reelIdTaskCount);
            String reelIdScanPrecent = this.getReelIdScanPrecent(reelIdScanCount, reelIdTaskCount);
            LOG.info("{}批次的条码扫描率：{}，Reel ID追溯率：{}，Reel ID扫描执行率：{}",
                    sourceTask,workOrderNoScanPercent,reelIdPercent,reelIdScanPrecent);
            singleBatchesReportVo.setSourceTask(sourceTask);
            singleBatchesReportVo.setWorkOrderNoScanPercent(workOrderNoScanPercent);
            singleBatchesReportVo.setReelIdPercent(reelIdPercent);
            singleBatchesReportVo.setReelIdScanPrecent(reelIdScanPrecent);
            singleBatchesReportVoList.add(singleBatchesReportVo);
        }
        return singleBatchesReportVoList;
    }

    /**
     * 扫描的ReelId数量
     * @param historylMap
     * @param sourceTask
     * @param reelIdScanCount
     * @return 数值
     */
    private BigDecimal getReelIdScanCount(Map<String, QuerySmtMachineMTLHistoryLDTO> historylMap, String sourceTask, BigDecimal reelIdScanCount) {
        if(null != historylMap && !historylMap.isEmpty() && historylMap.containsKey(sourceTask)){
            reelIdScanCount = historylMap.get(sourceTask).getReelIdCount();
        }
        return reelIdScanCount;
    }

    /**
     * 该批次注册的ReelId数量
     * @param pkCodeInfoMap
     * @param sourceTask
     * @param reelIdTaskCount
     * @return 数值
     */
    private BigDecimal getReelIdTaskCount(Map<String, PkCodeInfoDTO> pkCodeInfoMap, String sourceTask, BigDecimal reelIdTaskCount) {
        if(null != pkCodeInfoMap && !pkCodeInfoMap.isEmpty() && pkCodeInfoMap.containsKey(sourceTask)){
            reelIdTaskCount = pkCodeInfoMap.get(sourceTask).getReelIdCount();
        }
        return reelIdTaskCount;
    }

    /**
     * 单板批次数
     * @param basicMap
     * @param sourceTask
     * @param totalWorkOderQty
     * @return
     */
    private BigDecimal getTotalWorkOderQty(Map<String, PsWorkOrderBasicDTO> basicMap, String sourceTask, BigDecimal totalWorkOderQty) {
        if(null != basicMap && !basicMap.isEmpty() && basicMap.containsKey(sourceTask)){
            totalWorkOderQty = basicMap.get(sourceTask).getWorkOrderQty();
        }
        return totalWorkOderQty;
    }

    /**
     * 条码扫描率
     * @param snCount 追溯数据中该批次指令条码数量
     * @param totalWorkOderQty 该批次单板计划数
     * @return 条码扫描率
     */
    private String countWorkOrderNoPercent(BigDecimal snCount,
                                           BigDecimal totalWorkOderQty){
        /**
         * 指标1：条码扫描率
         * 公式：分子=追溯数据中该批次指令条码数量（数据来源：产品物料追溯查询，按批次指令查询，统计单板条码数量）
         * 分母=该批次单板计划数
         * 目的：统计某批次单板条码追溯的比例，比如单板计划数为1000，系统能否采集齐A、B面各1000个条码。
         * 指令的获取方式：
         * a。指令的实际完工时间是空&实际开始时间不为空&时间开始时间<今天，对应的指令的数量，就是单板计划数。或
         * b。指令的实际完工时间不为空且在昨天的时间段，对应的指令的数量，就是单板计划数。
         */
        String workOrderNoPercent;
        if(BigDecimal.ZERO.equals(totalWorkOderQty)){
            LOG.info("指令1 条码扫描率计算结果： {}",Constant.ZERO_PERCENT);
            return Constant.ZERO_PERCENT;
        }
        BigDecimal workOrderCount = snCount.divide(totalWorkOderQty, 4, RoundingMode.HALF_UP);
        workOrderNoPercent =  CommonUtils.divToPercent(workOrderCount, 2);
        LOG.info("指令1 条码扫描率计算结果：{} " ,workOrderCount);
        return workOrderNoPercent;
    }

    /**
     * Reel ID追溯率
     * @param reelIdCount 追溯数据中该批次Reel ID数量
     * @param reelIdTaskCount 该批次注册的Reel ID数量
     * @return 百分比
     * @throws Exception
     */
    private String countReelIdPercent(BigDecimal reelIdCount,
                                      BigDecimal reelIdTaskCount){
        /**
         * Reel ID追溯率
         * 指标1中的到的指令，拿到对应的批次(source_task)，查询这些批次在追溯数据中有多少个Reel ID（去重），即是分子
         * 该批次注册的Reel ID数量：pk_code_infor表，product_task=批次
         * 公式：分子=追溯数据中该批次Reel ID数量
         * 分母=该批次注册的Reel ID数量
         * 目的：按批次统计Reel ID追溯比例
         */
        String reelIdPercent;
        if(BigDecimal.ZERO.equals(reelIdTaskCount)){
            LOG.info("指令2 Reel ID追溯比例计算结果： {}",Constant.ZERO_PERCENT);
            return Constant.ZERO_PERCENT;
        }
        BigDecimal count = reelIdCount.divide(reelIdTaskCount, 4, RoundingMode.HALF_UP);
        LOG.info("指令2 Reel ID追溯比例计算结果： {}",count);
        reelIdPercent =  CommonUtils.divToPercent(count, 2);
        return reelIdPercent;
    }

    /**
     * Reel ID扫描执行率
     * @param reelIdTaskCount 该批次转机+接料扫描Reel ID数量
     * @param reelIdTaskCount 该批次注册的Reel ID数量
     * @return Reel ID扫描执行率
     * @throws Exception
     */
    private String getReelIdScanPrecent(BigDecimal reelIdScanCount, BigDecimal reelIdTaskCount){
        /**
         * 指标3：Reel ID扫描执行率
         * 分子=该批次转机+接料扫描Reel ID数量
         * 分母=该批次注册的Reel ID数量
         * 目的：按批次统计产线员工Reel ID扫描比例
         * 指标1中的到的指令，拿到对应的批次(source_task)，查询这些指令的上料历史表（上料历史类型是转机扫描或接料扫描），历史行表中的reel id 的个数（去重），就是分子
         * 该批次注册的Reel ID数量：pk_code_infor表，product_task=批次
         */
        String reelIdScanPrecent;

        if(BigDecimal.ZERO.equals(reelIdTaskCount)){
            LOG.info("指令3 Reel ID扫描执行率计算结果： {}",Constant.ZERO_PERCENT);
            return Constant.ZERO_PERCENT;
        }
        BigDecimal count = reelIdScanCount.divide(reelIdTaskCount, 4, RoundingMode.HALF_UP);
        reelIdScanPrecent =  CommonUtils.divToPercent(count, 2);
        LOG.info("指令3 Reel ID扫描执行率计算结果： {}",count);
        return reelIdScanPrecent;
    }

    /**
     * 获取次注册的Reel ID数量
     * @param productTaskList
     * @return
     */
    private List<PkCodeInfoDTO> getPkCodeInfoMap(List<String> productTaskList){
        PkCodeInfoDTO pkCodeInfoDto = new PkCodeInfoDTO();
        pkCodeInfoDto.setProductBatchCodeList(productTaskList);
        pkCodeInfoDto.setSort(MpConstant.ORDER_FIELD);
        pkCodeInfoDto.setOrder(Constant.DESC);
       return  pkCodeInfoService.getRegisterBatchReelIdCount(pkCodeInfoDto);
    }

    /**
     * 获取Reel ID扫描执行率的分子 历史行表中的reel id 的个数（去重）
     * @param productBatchCodeList
     * @param factoryId
     * @return
     */
    private List<QuerySmtMachineMTLHistoryLDTO> getBatchReelIdList(List<String> productBatchCodeList, String factoryId){
        /**
         * 指标3：Reel ID扫描执行率
         * 分子： 指标1中的到的指令，拿到对应的批次(source_task)，查询这些指令的上料历史表（上料历史类型是转机扫描或接料扫描），
         * 历史行表中的reel id 的个数（去重）
         */
        QuerySmtMachineMTLHistoryLDTO queryHistorylDto = new QuerySmtMachineMTLHistoryLDTO();
        queryHistorylDto.setFactoryId(Long.parseLong(factoryId));
        queryHistorylDto.setProductBatchCodeList(productBatchCodeList);
        //上料类型（0:配送绑定，1:转机，2:接料，3:QC）
        List<String> mountTypeList = new ArrayList<>();
        mountTypeList.add(Constant.STRAGETY_ONE);
        mountTypeList.add(Constant.STRAGETY_TWO);
        queryHistorylDto.setMountTypeList(mountTypeList);
        return smtMachineMtlHistorylService.countReelIdCountBySourceBatchAndTime(queryHistorylDto);
    }


    @Override
    public String getTableData(String emailTile, String beforeDay, List<SingleBatchesReportVo> singleBatchesReportVoList) {
        // 生成报表格式
        StringBuilder stb = new StringBuilder();
        stb.append("<?xml version='1.0' encoding='utf-8'?>");
        stb.append("<H4>" + emailTile + "（" + beforeDay + "）</H4>");
        stb.append("<table border=\"1\" cellspacing=\"1\" cellpadding=\"0\"  width=\"100%\"  style=\"BORDER: silver 1px solid;  BACKGROUND: #C6D5F5; \" >");
        stb.append("<TR style=\"BORDER-TOP: white 1px solid; BACKGROUND: #D2E1FD; BORDER-LEFT: white 1px solid ; text-align: center;  \" >");
        stb.append(Constant.SERIAL_TD);
        stb.append(Constant.SOURCE_TASK_TD);
        stb.append(Constant.SN_SCAN_PERCENT_TD);
        stb.append(Constant.REEL_ID_PERCENT_TD);
        stb.append(Constant.REEL_ID_SCAN_PERCENT_TD);
        stb.append("</TR>");

        int count = Constant.TINT_ZERO;
        for (SingleBatchesReportVo singleBatchesReportVo : singleBatchesReportVoList) {
            stb.append("<TR style=\"PADDING-RIGHT: 1px; PADDING-LEFT: 4px; BACKGROUND: white; PADDING-BOTTOM: 1px; PADDING-TOP: 3px; text-align: center; \" >");
            stb.append("<TD>" + (count + 1) + "</TD>");
            stb.append("<TD>" + singleBatchesReportVo.getSourceTask() + "</TD>");
            stb.append("<TD>" + singleBatchesReportVo.getWorkOrderNoScanPercent() + "</TD>");
            stb.append("<TD>" + singleBatchesReportVo.getReelIdPercent() + "</TD>");
            stb.append("<TD>" + singleBatchesReportVo.getReelIdScanPrecent() + "</TD>");
            stb.append("</TR>");
            count++;
        }
        if(singleBatchesReportVoList.isEmpty() || singleBatchesReportVoList.size() == 0){
            stb.append("<TR style=\"PADDING-RIGHT: 1px; PADDING-LEFT: 4px; BACKGROUND: white; PADDING-BOTTOM: 1px; PADDING-TOP: 3px; text-align: center; \" >");
            stb.append("<TD>1</TD>");
            stb.append(Constant.NO_DATA_TD);
            stb.append(Constant.NO_DATA_TD);
            stb.append(Constant.NO_DATA_TD);
            stb.append(Constant.NO_DATA_TD);
            stb.append("</TR>");
        }
        stb.append("</table>");
        stb.append("<br>");
        stb.append("<br><hr>");
        return stb.toString();
    }

    /**
     * 设置深圳工厂邮件信息
     * @param sysLookupTypesDTO
     * @param entity
     */
    @Override
    public void setSzEmail(SysLookupTypesDTO sysLookupTypesDTO, SingleBatchesReportDTO entity) {
        if(Constant.SZ_EMAIL_TITLE.equals(sysLookupTypesDTO.getLookupCode().toString())){
            entity.setEmailTitle(sysLookupTypesDTO.getLookupMeaning());
        }
        if(Constant.SZ_EMAIL_LIST.equals(sysLookupTypesDTO.getLookupCode().toString())){
            entity.setEmailList(sysLookupTypesDTO.getLookupMeaning());
        }
        if(Constant.SZ_EMAIL_CC_LIST.equals(sysLookupTypesDTO.getLookupCode().toString())){
            entity.setEmailCCList(sysLookupTypesDTO.getLookupMeaning());
        }
    }

    /**
     * 设置河源工厂邮件信息
     *
     * @param sysLookupTypesDTO
     * @param entity
     */
    @Override
    public void setHyEmail(SysLookupTypesDTO sysLookupTypesDTO, SingleBatchesReportDTO entity) {
        if(Constant.HY_EMAIL_TITLE.equals(sysLookupTypesDTO.getLookupCode().toString())){
            entity.setEmailTitle(sysLookupTypesDTO.getLookupMeaning());
        }
        if(Constant.HY_EMAIL_LIST.equals(sysLookupTypesDTO.getLookupCode().toString())){
            entity.setEmailList(sysLookupTypesDTO.getLookupMeaning());
        }
        if(Constant.HY_EMAIL_CC_LIST.equals(sysLookupTypesDTO.getLookupCode().toString())){
            entity.setEmailCCList(sysLookupTypesDTO.getLookupMeaning());
        }
     }

    /**
     * 设置长沙工厂邮件信息
     *
     * @param sysLookupTypesDTO
     * @param entity
     */
    @Override
    public void setCsEmail(SysLookupTypesDTO sysLookupTypesDTO, SingleBatchesReportDTO entity) {
        if(Constant.CS_EMAIL_TITLE.equals(sysLookupTypesDTO.getLookupCode().toString())){
            entity.setEmailTitle(sysLookupTypesDTO.getLookupMeaning());
        }
        if(Constant.CS_EMAIL_LIST.equals(sysLookupTypesDTO.getLookupCode().toString())){
            entity.setEmailList(sysLookupTypesDTO.getLookupMeaning());
        }
        if(Constant.CS_EMAIL_CC_LIST.equals(sysLookupTypesDTO.getLookupCode().toString())){
            entity.setEmailCCList(sysLookupTypesDTO.getLookupMeaning());
        }
    }

    /**
     * 设置西安工厂邮件信息
     *
     * @param sysLookupTypesDTO
     * @param entity
     */
    @Override
    public void setXaEmail(SysLookupTypesDTO sysLookupTypesDTO, SingleBatchesReportDTO entity) {
        if(Constant.XA_EMAIL_TITLE.equals(sysLookupTypesDTO.getLookupCode().toString())){
            entity.setEmailTitle(sysLookupTypesDTO.getLookupMeaning());
        }
        if(Constant.XA_EMAIL_LIST.equals(sysLookupTypesDTO.getLookupCode().toString())){
            entity.setEmailList(sysLookupTypesDTO.getLookupMeaning());
        }
        if(Constant.XA_EMAIL_CC_LIST.equals(sysLookupTypesDTO.getLookupCode().toString())){
            entity.setEmailCCList(sysLookupTypesDTO.getLookupMeaning());
        }
    }

    /**
     * 设置南京工厂邮件信息
     *
     * @param sysLookupTypesDTO
     * @param entity
     */
    @Override
    public void setNjEmail(SysLookupTypesDTO sysLookupTypesDTO, SingleBatchesReportDTO entity) {
        if(Constant.NJ_EMAIL_TITLE.equals(sysLookupTypesDTO.getLookupCode().toString())){
            entity.setEmailTitle(sysLookupTypesDTO.getLookupMeaning());
        }
        if(Constant.NJ_EMAIL_LIST.equals(sysLookupTypesDTO.getLookupCode().toString())){
            entity.setEmailList(sysLookupTypesDTO.getLookupMeaning());
        }
        if(Constant.NJ_EMAIL_CC_LIST.equals(sysLookupTypesDTO.getLookupCode().toString())){
            entity.setEmailCCList(sysLookupTypesDTO.getLookupMeaning());
        }
    }

}
