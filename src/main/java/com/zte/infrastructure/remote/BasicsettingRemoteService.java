package com.zte.infrastructure.remote;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Maps;
import com.zte.common.CommonUtils;
import com.zte.common.ConstantInterface;
import com.zte.common.enums.InterfaceEnum;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.*;
import com.zte.domain.model.CFLine;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.itp.msa.util.string.StringHelper;
import com.zte.microservice.name.MicroServiceNameEum;
import com.zte.springbootframe.common.annotation.RecordLogAnnotation;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.JsonConvertUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpMethod;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 点对点调用基础服务接口类
 *
 * <AUTHOR>
 */
public class BasicsettingRemoteService {

    /**
     * 整机子卡校验
     *
     * @param objectMap
     * @return
     */
    public static int checkCount(Map<String, Object> objectMap) {
        int result = NumConstant.NUM_ZERO;
        String url = "/bsItemInfoCtrl/checkCount";
        JsonNode jsonNode = HttpRemoteService.pointToPointSelective(MicroServiceNameEum.BASICSETTING,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEGET, url, objectMap);
        if (null != jsonNode) {
            String boStr = jsonNode.get(MpConstant.JSON_BO).toString();
            String temp = JacksonJsonConverUtil.jsonToListBean(boStr,
                    new TypeReference<String>() {
                    });
            result = Integer.valueOf(temp);
        }
        return result;
    }

    /**
     * 获取工厂list
     *
     * @param objectMap
     * @return
     */
    public static List<CFFactory> getFactory(Map<String, Object> objectMap) {

        List<CFFactory> list = new ArrayList<>();
        String url = "/CF/CFFactory";
        JsonNode jsonNode = HttpRemoteService.pointToPointSelective(MicroServiceNameEum.BASICSETTING,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEGET, url, objectMap);
        if (null != jsonNode) {
            String bo = jsonNode.get(MpConstant.JSON_BO).get(MpConstant.JSON_ROWS).toString();
            list = JacksonJsonConverUtil.jsonToListBean(bo,
                    new TypeReference<List<CFFactory>>() {
                    });
        }
        return list;
    }

    /**
     * 获取数据字典信息
     *
     * @param map
     * @return
     * @throws Exception
     */
    public static List<SysLookupTypesDTO> getSysLookUpValue(Map<String, Object> map) {
        String getUrl = "/BS/sysLookupTypesAll";
        String msg = MicroServiceRestUtil.invokeService(MicroServiceNameEum.BASICSETTING,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEGET, getUrl, JSON.toJSONString(map),
                MESHttpHelper.getHttpRequestHeader());
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<SysLookupTypesDTO>>() {
        });
    }

    /**
     * 查询物料代码对应的abcType和物料类
     *
     * @param itemCodeList 物料代码
     * @return 查询特定物料类型物料代码
     */
    public static List<BsItemInfo> querySemiItemNo(List<String> itemCodeList) {
        // 点对点调用服务
        String postUrl = "/bsItemInfoCtrl/querySemiItemNo";
        String responseStr = MicroServiceRestUtil.invokeService(MicroServiceNameEum.BASICSETTING,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST,
                postUrl, JacksonJsonConverUtil.beanToJson(itemCodeList), MESHttpHelper.getHttpRequestHeader());
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(responseStr);
        if (StringUtils.isBlank(bo)) {
            return new LinkedList<>();
        }
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<BsItemInfo>>() {
        });
    }

    /**
     * 根据数据字典代码lookupCode查询数据字典明细
     *
     * @param lookupCode 数据字典编码
     * @return
     */
    public static SysLookupValues getSysLookUpValueByCode(String lookupCode) {
        SysLookupValues sysLookupValues = null;
        if (StringUtils.isEmpty(lookupCode)) {
            return sysLookupValues;
        }
        String getUrl = "/BS/sysLookupValues/" + lookupCode;
        ServiceData<SysLookupValues> serviceData = HttpRemoteService.pointToPointCall(MicroServiceNameEum.BASICSETTING,
                HttpMethod.GET, getUrl, null,
                new TypeReference<ServiceData<SysLookupValues>>() {
                });
        return serviceData.getBo();
    }

    /**
     * 根据数据字典代码lookupCode获取数据字典明细LookupMeaning值
     *
     * @param lookupCode
     * @return
     */
    public static String getLookupMeaningByCode(String lookupCode) {
        SysLookupValues sysLookupValues = getSysLookUpValueByCode(lookupCode);
        if (sysLookupValues == null) {
            return Constant.STR_EMPTY;
        }
        return sysLookupValues.getLookupMeaning();
    }

    /**
     * 远程调用CFLINE接口获取设备物料消耗标识
     *
     * @throws Exception
     */
    public static List<CFLine> getLineCodeMaterielMarker(Collection<String> lineCodes) throws Exception {
        Map<String, Object> paramsMap = new HashMap<>(NumConstant.NUM_SIXTEEN);
        paramsMap.put(Constant.IN_LINE_LIST, lineCodes);
        JsonNode json = HttpRemoteService.pointToPointSelective(InterfaceEnum.basicSettingMarkerList.getServiceName(),
                MicroServiceNameEum.VERSION, InterfaceEnum.basicSettingMarkerList.getReqType(),
                InterfaceEnum.basicSettingMarkerList.getUrl(), paramsMap);
        if (Objects.isNull(json)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.DATA_NOT_FOUND);
        }
        String retCode = json.get(MpConstant.JSON_CODE).get(MpConstant.JSON_CODE).asText();
        if (!RetCode.SUCCESS_CODE.equalsIgnoreCase(retCode)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.NO_RETURN_BO_DATA);
        }
        return JsonConvertUtil.jsonToBean(json.get(MpConstant.JSON_BO).toString(), List.class, CFLine.class);
    }

    /**
     * 获取数据字典
     *
     * @param map
     * @return
     * @throws Exception
     */
    public static List<SysLookupTypesDTO> getListByLookupType(Map<String, Object> map) throws Exception {

        List<SysLookupTypesDTO> types = new ArrayList<>();
        String getUrl = "/BS/findListByLookupType";
        JsonNode json = HttpRemoteService.pointToPointSelective(MicroServiceNameEum.BASICSETTING,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEGET, getUrl, map);
        if (null != json) {
            String bo = json.get(MpConstant.JSON_BO).toString();
            types = (List<SysLookupTypesDTO>) JacksonJsonConverUtil.jsonToListBean(bo,
                    new TypeReference<List<SysLookupTypesDTO>>() {
                    });
        }
        return types;
    }

    /**
     * 获取数据字典
     *
     * @param
     * @return
     * @throws Exception
     */
    public static List<MtlItemLocations> getLocationList(String subinventoryCode) {

        List<MtlItemLocations> types = new ArrayList<>();
        String getUrl = "/BS/mtlItemLocations/" + subinventoryCode;
        JsonNode json = HttpRemoteService.pointToPointSelective(MicroServiceNameEum.BASICSETTING,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEGET, getUrl, null);
        if (null != json) {
            String bo = json.get(MpConstant.JSON_BO).toString();
            types = (List<MtlItemLocations>) JacksonJsonConverUtil.jsonToListBean(bo,
                    new TypeReference<List<MtlItemLocations>>() {
                    });
        }
        return types;
    }

    /**
     * 获取数据字典信息，  用于获取字典类型下的所有字典
     *
     * @param lookupType 字典类型
     * @return
     */
    public static SysLookupTypesDTO getSysLookUpValue(String lookupType, String lookupCode) {
        Map<String, Object> map = Maps.newHashMap();
        map.put(Constant.FIELD_LOOKUP_TYPE, lookupType);
        List<SysLookupTypesDTO> sysLookupTypesDTOS = getSysLookUpValue(map);
        for (SysLookupTypesDTO sysLookupTypesDTO : sysLookupTypesDTOS) {
            if (StringHelper.isNotEmpty(lookupCode) && sysLookupTypesDTO != null && lookupCode.equals(CommonUtils.getStrTransNull(sysLookupTypesDTO.getLookupCode()))) {
                return sysLookupTypesDTO;
            }
        }
        return null;
    }

    /**
     * 更新数据字典值
     *
     * @param lookupCode    数据字典代码
     * @param lookupMeaning
     * @return
     * @throws Exception
     */
    public static void updateSysLookupValuesMeaning(BigDecimal lookupCode, String lookupMeaning) throws Exception {
        updateSysLookupValuesMeaning(lookupCode, lookupMeaning, null);
    }

    /**
     * 更新数据字典值
     *
     * @param lookupCode    数据字典代码
     * @param lookupMeaning
     * @return
     * @throws Exception
     */
    public static void updateSysLookupValuesMeaning(BigDecimal lookupCode, String lookupMeaning, String attribute1) throws Exception {
        Map<String, Object> map = Maps.newHashMap();
        map.put(Constant.FIELD_LOOKUP_CODE, lookupCode);
        map.put(Constant.FIELD_LOOKUP_MEANING, lookupMeaning);
        map.put(Constant.FIELD_LOOKUP_ATTRIBUTE1, attribute1);
        String getUrl = "/BS/updateSysLookupValuesBySelective";
        JsonNode json = HttpRemoteService.pointToPointSelective(MicroServiceNameEum.BASICSETTING,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST, getUrl, map);
        if (json != null) {
            // 返回错误，记录失败日志
            if (json.get(Constant.STR_CODE) == null || json.get(Constant.STR_CODE).get(Constant.STR_CODE) == null) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.FAILED_TO_UPDATE_SYS_LOOK_UP_MEANING);
            }
            String codeStr = json.get(Constant.STR_CODE).get(Constant.STR_CODE).asText();
            if (!Constant.SUCCESS_CODE.equals(codeStr)) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.FAILED_TO_UPDATE_SYS_LOOK_UP_MEANING);
            }
        } else {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.FAILED_TO_UPDATE_SYS_LOOK_UP_MEANING);
        }
    }

    /**
     * 获取数据字典信息，  用于获取字典类型下的所有字典
     *
     * @param lookupType 字典类型
     * @return
     */
    public static List<SysLookupTypesDTO> getSysLookUpValue(String lookupType) {
        Map<String, Object> map = Maps.newHashMap();
        map.put(Constant.FIELD_LOOKUP_TYPE, lookupType);
        return getSysLookUpValue(map);
    }

    /**
     * 获取符合条件的线体
     */
    public static List<CFLine> getLine(Map<String, Object> objectMap) {
        String url = "/CF/CFLineByPost";
        String msg = MicroServiceRestUtil.invokeService(MicroServiceNameEum.BASICSETTING,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST,
                url, JacksonJsonConverUtil.beanToJson(objectMap), MESHttpHelper.getHttpRequestHeader());
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
        Page<CFLine> listPage = JacksonJsonConverUtil.jsonToBean(bo, new TypeReference<Page<CFLine>>() {
        });
        if (Objects.isNull(listPage)) {
            return new LinkedList<>();
        }
        return listPage.getRows();
    }

    /**
     * 获取符合条件的线体
     */
    public static CFLine getLineByName(String lineName) throws Exception {

        String url = "/CF/GetCFLineByName" + Constant.FORWARD_SLASH + lineName;
        JsonNode jsonNode = HttpRemoteService.pointToPointSelective(MicroServiceNameEum.BASICSETTING,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEGET, url, null);
        if (null == jsonNode || null == jsonNode.get(MpConstant.JSON_BO)) {
            return null;
        }
        String boStr = jsonNode.get(MpConstant.JSON_BO).toString();
        return JsonConvertUtil.jsonToBean(boStr, CFLine.class);
    }

    /**
     * 根据线体代码 获取线体
     *
     * @param lineCode
     * @return
     * @throws Exception
     */
    public static CFLine getLine(String lineCode) {
        if (StringUtils.isEmpty(lineCode)) {
            return null;
        }
        Map<String, Object> param = Maps.newHashMap();
        param.put(Constant.LINE_CODE, lineCode);
        List<CFLine> lines = getLine(param);
        if (CollectionUtils.isEmpty(lines)) {
            return null;
        } else {
            return lines.get(NumConstant.NUM_ZERO);
        }
    }


    /**
     * 获取产能信息
     *
     * @param lineCode
     * @param itemCode
     * @param processCode
     * @param factoryId
     * @return
     * @throws Exception
     */
    public static List<BManufactureCapacityDTO> getBManufactureCapacityInfo(String lineCode, String itemCode, String processCode,
                                                                            String factoryId) {
        // 点对点调用指令查询服务
        BManufactureCapacityDTO capacityDTO = new BManufactureCapacityDTO();
        capacityDTO.setLineCode(lineCode);
        capacityDTO.setItemCode(itemCode);
        capacityDTO.setProcessCode(processCode);
        capacityDTO.setFactoryId(factoryId);
        String url = "/BMC/bsBarcodeInfo";
        JsonNode jsonNode = HttpRemoteService.pointToPointSelectiveByObj(MicroServiceNameEum.BASICSETTING, MicroServiceNameEum.VERSION,
                MicroServiceNameEum.SENDTYPEGET, url, capacityDTO);
        List<BManufactureCapacityDTO> capacityDTOS = new ArrayList<>();
        if (jsonNode != null) {
            String bo = jsonNode.get(MpConstant.JSON_BO).toString();
            capacityDTOS = JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<BManufactureCapacityDTO>>() {
            });
        }
        return capacityDTOS;
    }

    /**
     * 获取产能信息
     *
     * @param factoryId
     * @return
     * @throws Exception
     */
    public static List<BManufactureCapacityDTO> getBManufactureCapacityInfoByPost(List<String> lineCodes, List<String> itemCodes, List<String> processCodes,
                                                                                  String factoryId) {
        // 点对点调用指令查询服务
        BManufactureCapacityDTO capacityDTO = new BManufactureCapacityDTO();
        capacityDTO.setLineCodeList(lineCodes);
        capacityDTO.setItemCodeList(itemCodes);
        capacityDTO.setProcessCodeList(processCodes);
        capacityDTO.setFactoryId(factoryId);
        String url = "/BMC/bsBarcodeInfoByPost";
        String msg = MicroServiceRestUtil.invokeService(MicroServiceNameEum.BASICSETTING, MicroServiceNameEum.VERSION,
                MicroServiceNameEum.SENDTYPEPOST, url, JSON.toJSONString(capacityDTO), MESHttpHelper.getHttpRequestHeader());
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<BManufactureCapacityDTO>>() {
        });
    }

    public static List<BsItemInfo> getItemInfo(String itemNo) throws Exception {
        Map<String, Object> objectMap = new HashMap<>();
        objectMap.put("itemNo", itemNo);
        // 点对点调用服务，获取该批次的任务信息
        String url = "/bsItemInfoCtrl/bsItemInfoList";
        JsonNode jsonNode = HttpRemoteService.pointToPointSelective(MicroServiceNameEum.BASICSETTING, MicroServiceNameEum.VERSION,
                MicroServiceNameEum.SENDTYPEGET, url, objectMap);

        List<BsItemInfo> itemInfos = new ArrayList<>();
        if (jsonNode != null) {
            String bo = jsonNode.get(MpConstant.JSON_BO).get(MpConstant.JSON_ROWS).toString();
            itemInfos = JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<BsItemInfo>>() {
            });
        }
        return getBsItemInfo(itemNo, itemInfos);
    }

    public static List<BsItemInfo> getBsItemInfo(String itemNo, List<BsItemInfo> itemInfos) {
        if (!CollectionUtils.isEmpty(itemInfos)) {
            itemInfos = itemInfos.stream().filter(e -> StringUtils.equals(e.getItemNo(), itemNo)).collect(Collectors.toList());
        }
        return itemInfos;
    }

    public static BsItemInfo getItemInfoByItemNo(String itemNo) {
        // 点对点调用服务，获取该批次的任务信息
        String getUrl = "/bsItemInfoCtrl/bsItemInfo/" + itemNo;
        JsonNode json = HttpRemoteService.pointToPointSelective(MicroServiceNameEum.BASICSETTING, MicroServiceNameEum.VERSION,
                MicroServiceNameEum.SENDTYPEGET, getUrl, null);
        if (null == json) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.ITEM_INFO_NOT_FOUND);
        }
        String retCode = json.get(Constant.CODE).get(Constant.CODE).asText();
        if (!RetCode.SUCCESS_CODE.equalsIgnoreCase(retCode)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.ITEM_INFO_NOT_FOUND);
        }
        String bo = json.get(Constant.BO).toString();
        if (StringUtils.isNotEmpty(bo)) {
            return JacksonJsonConverUtil.jsonToBean(bo, BsItemInfo.class);
        }
        return null;
    }

    public static List<CFFactory> getLocationNo(String factoryId) throws Exception {
        Map<String, Object> objectMap = new HashMap<>();
        objectMap.put("factoryId", factoryId);
        // 点对点调用服务，获取该批次的任务信息
        String url = "/CF/CFFactory";
        JsonNode jsonNode = HttpRemoteService.pointToPointSelective(MicroServiceNameEum.BASICSETTING, MicroServiceNameEum.VERSION,
                MicroServiceNameEum.SENDTYPEGET, url, objectMap);

        List<CFFactory> factories = new ArrayList<>();
        if (jsonNode != null) {
            String bo = jsonNode.get(MpConstant.JSON_BO).get(MpConstant.JSON_ROWS).toString();
            factories = JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<CFFactory>>() {
            });
        }
        return factories;
    }

    public static List<CFFactory> getFactoryInfo(String factoryId) {
        Map<String, Object> objectMap = new HashMap<>();
        objectMap.put("factoryId", factoryId);
        // 点对点调用服务，获取该批次的任务信息
        String url = "/CF/CFFactory";
        JsonNode jsonNode = HttpRemoteService.pointToPointSelective(MicroServiceNameEum.BASICSETTING, MicroServiceNameEum.VERSION,
                MicroServiceNameEum.SENDTYPEGET, url, objectMap);

        List<CFFactory> factories = new ArrayList<>();
        if (jsonNode != null) {
            String bo = jsonNode.get(MpConstant.JSON_BO).get(MpConstant.JSON_ROWS).toString();
            factories = JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<CFFactory>>() {
            });
        }
        return factories;
    }

    public static List<BsItemInfo> getStyleInfo(Map map) throws Exception {
        // 点对点调用服务，获取该批次的任务信息
        String url = "/bsItemInfoCtrl/getList";
        JsonNode jsonNode = HttpRemoteService.pointToPointSelective(MicroServiceNameEum.BASICSETTING, MicroServiceNameEum.VERSION,
                MicroServiceNameEum.SENDTYPEGET, url, map);
        List<BsItemInfo> itemInfos = new ArrayList<>();
        if (jsonNode != null) {
            String bo = jsonNode.get(MpConstant.JSON_BO).toString();
            itemInfos = JsonConvertUtil.jsonToBean(bo, List.class, BsItemInfo.class);
        }
        return itemInfos;
    }

    public static List<BsItemInfo> getStyleInfo(String inItemNo) throws Exception {
        Map<String, Object> objectMap = new HashMap<>();
        objectMap.put("inItemNo", inItemNo);
        // 点对点调用服务，获取该批次的任务信息
        String url = "/bsItemInfoCtrl/getList";
        JsonNode jsonNode = HttpRemoteService.pointToPointSelective(MicroServiceNameEum.BASICSETTING, MicroServiceNameEum.VERSION,
                MicroServiceNameEum.SENDTYPEGET, url, objectMap);
        List<BsItemInfo> itemInfos = new ArrayList<>();
        if (jsonNode != null) {
            String bo = jsonNode.get(MpConstant.JSON_BO).toString();
            itemInfos = JsonConvertUtil.jsonToBean(bo, List.class, BsItemInfo.class);
        }
        return itemInfos;
    }

    /**
     * 根据工艺段（Attribute1）查找数据字典信息
     *
     * @param craftSection 传入单个工艺段字符串
     * @return 数据字典集合
     * @throws Exception
     */
    public static List<SysLookupTypesDTO> findListByAttribute1(String craftSection) {

        List<SysLookupTypesDTO> typesDTOS = new ArrayList<>();
        String url = "/BS/findListByAttribute1/" + craftSection;
        JsonNode jsonNode = HttpRemoteService.pointToPointSelective(MicroServiceNameEum.BASICSETTING,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEGET, url, null);
        if (jsonNode != null) {
            String bo = jsonNode.get(MpConstant.JSON_BO).toString();
            typesDTOS = JacksonJsonConverUtil.jsonToListBean(bo,
                    new TypeReference<List<SysLookupTypesDTO>>() {
                    });
        }
        return typesDTOS;
    }


    /**
     * 根据车间编码和工厂id获取线体
     * 6507000003
     */
    public static List<CFLine> getLineByWorkshopCode(Map<String, Object> map) throws Exception {
        JsonNode jsonNode = BasicsettingRemoteService.getLineJsonByWorkshopCode(map);
        if (null == jsonNode || null == jsonNode.get(Constant.JSON_CODE)) {
            throw new Exception();
        }
        String code = jsonNode.get(Constant.JSON_CODE).get(Constant.JSON_CODE).asText();
        String boStr = jsonNode.get(Constant.BO).get(MpConstant.JSON_ROWS).toString();
        if (!RetCode.SUCCESS_CODE.equalsIgnoreCase(code)) {
            throw new Exception(boStr);
        }
        return JsonConvertUtil.jsonToBean(boStr, List.class, CFLine.class);
    }

    /**
     * 根据车间编码和工厂id获取线体
     * 6507000003
     */
    public static JsonNode getLineJsonByWorkshopCode(Map<String, Object> map) {
        String url = "/CF/getLineHRList";
        return HttpRemoteService.pointToPointSelective(MicroServiceNameEum.BASICSETTING,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEGET, url, map);
    }

    /**
     * 根据物料List<itemNo> 批量获取集合
     *
     * @param itemList 物料集合
     * @return 物料信息
     * @throws Exception 异常
     */
    public static List<AgeingInfoFencePointToPointQueryItemInfoDTO> getItemInfo(List<AgeingInfoFencePointToPointQueryItemInfoDTO> itemList) throws Exception {

        List<AgeingInfoFencePointToPointQueryItemInfoDTO> itemInfo = new ArrayList<>();
        String getUrl = "/bsItemInfoCtrl/bsItemInfoBatch";
        JsonNode json = HttpRemoteService.pointToPointSelectiveByObj(MicroServiceNameEum.BASICSETTING,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST, getUrl, itemList);
        if (json != null) {
            String bo = json.get(MpConstant.JSON_BO).toString();
            itemInfo = JacksonJsonConverUtil.jsonToListBean(bo,
                    new TypeReference<List<AgeingInfoFencePointToPointQueryItemInfoDTO>>() {
                    });
        }
        return itemInfo;
    }


    public static List<BBomDetailDTO> getBbomDetail(String productCode) throws Exception {
        Map<String, Object> objectMap = new HashMap<>();
        objectMap.put("product_code", productCode);
        Map<String, String> header = MESHttpHelper.getHttpRequestHeader();
        String params = JacksonJsonConverUtil.beanToJson(objectMap);
        String urlStatic = ConstantInterface.getUrlStatic(InterfaceEnum.centerFactoryBBomInfoList);
        String result = HttpRemoteUtil.remoteExe(params, header, urlStatic, MicroServiceNameEum.SENDTYPEGET);
        JsonNode jsonNode = JacksonJsonConverUtil.getMapperInstance().readTree(result);
        if (null == jsonNode || null == jsonNode.get(Constant.JSON_CODE)) {
            throw new Exception();
        }
        String code = jsonNode.get(Constant.JSON_CODE).get(Constant.JSON_CODE).asText();
        String bo = jsonNode.get(Constant.BO).get(MpConstant.JSON_ROWS).toString();
        if (!RetCode.SUCCESS_CODE.equalsIgnoreCase(code)) {
            throw new Exception(bo);
        }
        return JsonConvertUtil.jsonToBean(bo, List.class, BBomDetailDTO.class);
    }

    public static List<BBomDetail> getBomListofDipNew(Map map) {
        String url = "/bsItemInfoCtrl/getBomListofDipNew";
        JsonNode jsonNode = HttpRemoteService.pointToPointSelective(MicroServiceNameEum.BASICSETTING,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEGET, url, map);
        if (null == jsonNode) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.NO_BOM_DETAIL);
        }
        String code = jsonNode.get(Constant.CODE).get(Constant.CODE).asText();
        if (!RetCode.SUCCESS_CODE.equalsIgnoreCase(code)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SUB_LEVEL_QUERY_ERROR);
        }
        String bo = jsonNode.get(Constant.BO).toString();
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<BBomDetail>>() {
        });
    }

    public static Page<BBomDetail> getBomListofDIP(Map map) {
        String url = "/bsItemInfoCtrl/getBomListofDIP";
        JsonNode jsonNode = HttpRemoteService.pointToPointSelective(MicroServiceNameEum.BASICSETTING,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEGET, url, map);
        if (null == jsonNode) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.NO_BOM_DETAIL);
        }
        String retCode = jsonNode.get(Constant.CODE).get(Constant.CODE).asText();
        if (!RetCode.SUCCESS_CODE.equalsIgnoreCase(retCode)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SUB_LEVEL_QUERY_ERROR);
        }
        String rows = jsonNode.get(Constant.BO).toString();
        return JacksonJsonConverUtil.jsonToListBean(rows, new TypeReference<Page<BBomDetail>>() {
        });
    }

    public static ServiceData zsUniteInterface(Object param, String interfaceName) {
        String getUrl = "/BS/zsUniteInterface";
        JSONObject jsonParam = new JSONObject();
        jsonParam.put("interfaceName", interfaceName);
        jsonParam.put("detail", param);
        JsonNode json = HttpRemoteService.pointToPointSelectiveByObj(MicroServiceNameEum.BASICSETTING,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST, getUrl, jsonParam);
        if (null == json) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.CALL_ZS_SERVICE_FAILED);
        }
        return JacksonJsonConverUtil.jsonToListBean(json.toString(), new TypeReference<ServiceData>() {
        });
    }

    public static ServiceData zsUniteInterface(JsonNode jsonNode) {
        String getUrl = "/BS/zsUniteInterface";
        JsonNode json = HttpRemoteService.pointToPointSelectiveByObj(MicroServiceNameEum.BASICSETTING,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST, getUrl, jsonNode);
        if (null == json) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.CALL_ZS_SERVICE_FAILED);
        }
        return JacksonJsonConverUtil.jsonToListBean(json.toString(), new TypeReference<ServiceData>() {
        });
    }

    /**
     * 查询AllFactory
     *
     * @return
     */
    public static List<CFFactoryDTO> getAllFactory() {
        // 点对点调用服务
        String url = "/CF/CFFactory";
        JsonNode jsonNode = HttpRemoteService.pointToPointSelective(MicroServiceNameEum.BASICSETTING,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEGET, url, null);
        if (null != jsonNode) {
            JsonNode bo = jsonNode.get("bo");
            if (null != bo) {
                JsonNode rows = bo.get("rows");
                if (rows != null) {
                    List<CFFactoryDTO> factoryDTOS = JSON.parseArray(rows.toString(), CFFactoryDTO.class);
                    return factoryDTOS;
                }
            }
        }
        return null;
    }


    /**
     * 数据字典查询（in查询）
     */
    public static List<SysLookupValuesDTO> getLookupValueByTypeCodes(Map<String, Object> map) {
        List<SysLookupValuesDTO> valuesDTOS = new ArrayList<>();
        // 点对点调用服务
        String url = "/BS/findListByLookupType";
        JsonNode jsonNode = HttpRemoteService.pointToPointSelective(MicroServiceNameEum.BASICSETTING,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEGET, url, map);
        if (null != jsonNode) {
            JsonNode bo = jsonNode.get(Constant.BO);
            if (null != bo) {
                String boStr = bo.toString();
                valuesDTOS = JacksonJsonConverUtil.jsonToListBean(boStr, new TypeReference<ArrayList<SysLookupValuesDTO>>() {
                });
            }
        }
        return valuesDTOS;
    }

    /**
     * 数据字典查询（in查询）
     *
     * @param code
     * @return
     */
    public static List<SysLookupValuesDTO> getLookupValueByTypeCodes(String code) {
        Map<String, Object> map = new HashMap<>();
        map.put(Constant.LOOK_UP_TYPE, code);
        List<SysLookupValuesDTO> valuesDTOS = new ArrayList<>();
        String url = "/BS/findListByLookupType";
        JsonNode jsonNode = HttpRemoteService.pointToPointSelective(MicroServiceNameEum.BASICSETTING,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEGET, url, map);
        if (null != jsonNode) {
            JsonNode node = jsonNode.get("bo");
            if (null != node) {
                String string = node.toString();
                valuesDTOS = JacksonJsonConverUtil.jsonToListBean(string,
                        new TypeReference<ArrayList<SysLookupValuesDTO>>() {
                        });
            }
        }
        return valuesDTOS;
    }


    /**
     * ProductCodes查询BBomHeader
     *
     * @param code
     * @return
     */
    public static List<BBomHeaderDTO> getBBomHeaderByProductCodes(String code) {
        String url = "/bBomHeaderCtrl/getByProductCodes?codes=" + code;
        JsonNode node = HttpRemoteService.pointToPointSelective(MicroServiceNameEum.BASICSETTING,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEGET, url, null);
        if (null != node) {
            JsonNode jsonNode = node.get("bo");
            if (null != jsonNode) {
                return JSON.parseArray(jsonNode.toString(), BBomHeaderDTO.class);
            }
        }
        return null;
    }

    /**
     * 根据物料代码集合获得一组物料代码和PCB版本的映射关系
     *
     * @return java.util.Map<java.lang.String, java.lang.String>
     * @Author: 10307315陈俊熙
     * @date 2021/10/27 下午2:54
     */
    public static Map<String, String> getVerNoInfoByItemNos(Set<String> itemNos) {
        //获取请求头信息
        Map<String, String> headParams = MESHttpHelper.getHttpRequestHeader();
        //初始化返回的map
        Map<String, String> result = new HashMap<>(Constant.INT_64);
        // 请求参数构造
        String serviceName = MicroServiceNameEum.BASICSETTING;
        String version = "v1";
        String sendType = "POST";
        String getUrl = "/bBomHeaderCtrl/getVerNoInfoByItemNos";
        //发送请求
        String response = MicroServiceRestUtil.invokeService(serviceName, version, sendType, getUrl, JacksonJsonConverUtil.beanToJson(itemNos.toArray()),
                headParams);
        //如果没结果，直接返回默认
        if (StringHelper.isEmpty(response)) {
            return result;
        }
        result = responseStringToJavaMap(response);
        return result;
    }

    /**
     * 是否存在PCB版本
     */
    public static boolean checkExistVerNo(String verNo) {
        String url = "/bBomHeaderCtrl/checkExistVerNo?verNo=" + verNo;
        JsonNode node = HttpRemoteService.pointToPointSelective(MicroServiceNameEum.BASICSETTING,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEGET, url, null);
        if (null != node) {
            JsonNode jsonNode = node.get("bo");
            if (null != jsonNode) {
                return Constant.STR_1.equals(jsonNode.toString());
            }
        }
        return false;
    }

    private static Map<String, String> responseStringToJavaMap(String response) {
        //初始化返回的map
        Map<String, String> resultMap = new HashMap<>(Constant.INT_64);
        JSONObject jsonObject = JSON.parseObject(response);
        JSONObject bo = jsonObject.getJSONObject(Constant.BO);
        resultMap = JSONObject.toJavaObject(bo, Map.class);
        return resultMap;
    }

    /**
     * 数据字典查询-lookupType/lookupCode
     */
    public static List<SysLookupValuesDTO> getSysLookupValuesList(SysLookupValuesDTO valuesDTO) {
        String params = JacksonJsonConverUtil.beanToJson(valuesDTO);
        // 点对点调用服务
        String url = "/BS/getSysLookupValuesList";
        String msg = MicroServiceRestUtil.invokeService(MicroServiceNameEum.BASICSETTING,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST,
                url, params, MESHttpHelper.getHttpRequestHeader());
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<SysLookupValuesDTO>>() {
        });
    }

    public static List<BsItemInfo> getStyleInfoPost(String inItemNo) {
        Map<String, Object> mapItemNos = new HashMap<String, Object>();
        mapItemNos.put("inItemNo", inItemNo);
        // 点对点调用服务，获取该批次的物料信息
        String params = JacksonJsonConverUtil.beanToJson(mapItemNos);
        // 点对点调用服务
        String url = "/bsItemInfoCtrl/getList";
        String msg = MicroServiceRestUtil.invokeService(MicroServiceNameEum.BASICSETTING,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST,
                url, params, MESHttpHelper.getHttpRequestHeader());
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<BsItemInfo>>() {
        });
    }

    /**
     * 数据字典查询-lookupType/lookupCode
     */
    public static List<BBomTechnicalChangeInfo> getBBomTechnicalChangeInfoList(Map<String, Object> params) {
        // 点对点调用服务
        String postUrl = "/bBomTechnicalChangeInfoCtrl/getList";
        String msg = MicroServiceRestUtil.invokeService(MicroServiceNameEum.BASICSETTING,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST, postUrl, JSON.toJSONString(params),
                MESHttpHelper.getHttpRequestHeader());
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<BBomTechnicalChangeInfo>>() {
        });
    }

    public static List<String> checkCIV(String itemNos, String productCode) throws IOException {
        String getUrl = "/civControl/itemNoCiv";
        JsonNode json = HttpRemoteService.pointToPointSelective(MicroServiceNameEum.BASICSETTING,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST, getUrl, new HashMap() {{
                    put("itemNos", itemNos);
                    put("itemCode", productCode);
                }});
        if (null != json) {
            JsonNode boNode = json.get(Constant.BO);
            if (null != boNode) {
                String bo = boNode.toString();
                return JsonConvertUtil.jsonToBean(bo, List.class, String.class);
            }
        }
        return null;
    }

    /**
     * /bBomTechnicalChangeInfoCtrl/getList
     * 通过指令查询技改物料信息
     *
     * @param workOrderNo
     */
    public static List<BBomTechnicalChangeInfo> queryTechnicalListByWorkOrder(String workOrderNo) {
        BBomTechnicalChangeInfo dto = new BBomTechnicalChangeInfo();
        dto.setWorkOrderNo(workOrderNo);
        String msg = MicroServiceRestUtil.invokeService(MicroServiceNameEum.BASICSETTING,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST,
                "/bBomTechnicalChangeInfoCtrl/getList", JSON.toJSONString(dto), MESHttpHelper.getHttpRequestHeader());
        String result = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
        return JacksonJsonConverUtil.jsonToListBean(result,
                new TypeReference<List<BBomTechnicalChangeInfo>>() {
                });
    }

    /**
     * 获取BOM 清单信息
     *
     * @param productCode 料单代码
     * @return
     * @throws Exception
     */
    public static List<BBomDetailDTO> getBomDetailByProductCode(String productCode) {
        String msg = MicroServiceRestUtil.invokeService(MicroServiceNameEum.BASICSETTING,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEGET,
                "/bBomDetailCtrl/getBomDetailByProductCode/" + productCode, null,
                MESHttpHelper.getHttpRequestHeader());
        String result = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
        return JacksonJsonConverUtil.jsonToListBean(result,
                new TypeReference<List<BBomDetailDTO>>() {
                });
    }

    //获取erp货位信息
    @RecordLogAnnotation("获取erp货位信息")
    public static List<MtlItemLocations> mtlItemLocations(MtlItemLocationsDTO dto) {
        // 点对点调用服务，获取erp货位信息
        String getUrl = "/BS/mtlItemLocations";
        JsonNode json = HttpRemoteService.pointToPointSelectiveByObj(MicroServiceNameEum.BASICSETTING, MicroServiceNameEum.VERSION,
                MicroServiceNameEum.SENDTYPEPOST, getUrl, dto);
        List<MtlItemLocations> itemInfoList = new ArrayList<>();
        if (json != null && MpConstant.BUSINESS_SUCCESS.equals(json.get(MpConstant.JSON_CODE).get(MpConstant.JSON_CODE).asText())) {
            String bo = json.get(MpConstant.JSON_BO).toString();
            itemInfoList = JSON.parseArray(bo, MtlItemLocations.class);
        }
        return itemInfoList;
    }

    public static List<String> getListByLocationType(List<SmtLocationInfoDTO> locationList) {
        // 点对点调用服务，获取erp货位信息
        String url = "/smtLocationInfo/getListByLocationType";
        String reString = MicroServiceRestUtil.invokeService(MicroServiceNameEum.BASICSETTING,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST, url,
                JacksonJsonConverUtil.beanToJson(locationList), MESHttpHelper.getHttpRequestHeader());

        if (StringUtils.isBlank(reString)) {
            return null;
        }
        JSONObject reJson = JSON.parseObject(reString);
        if (reJson.isEmpty() || reJson.getJSONObject(MpConstant.JSON_CODE).isEmpty()
                || !MpConstant.BUSINESS_SUCCESS.equals(reJson.getJSONObject(MpConstant.JSON_CODE).getString(MpConstant.JSON_CODE))) {
            return null;
        }

        return reJson.getJSONArray(MpConstant.JSON_BO).toJavaList(String.class);
    }


    public static List<SmtLocationInfoDTO> getListSmtLocation(String lineCode, String locationNo) {
        // 点对点调用服务，smt 模组信息
        if (StringUtils.isBlank(lineCode)) {
            return new LinkedList<>();
        }
        Map<String, String> params = new HashMap<>();
        params.put("lineCode", lineCode);
        if (StringUtils.isNotBlank(locationNo)) {
            params.put("locationNo", locationNo);
        }
        String url = "/smtLocationInfo/getList";
        String msg = MicroServiceRestUtil.invokeService(MicroServiceNameEum.BASICSETTING,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEGET, url,
                JacksonJsonConverUtil.beanToJson(params), MESHttpHelper.getHttpRequestHeader());
        msg = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
        return JacksonJsonConverUtil.jsonToListBean(msg, new TypeReference<LinkedList<SmtLocationInfoDTO>>() {
        });
    }


    public static List<SmtLocationInfoDTO> getSmtLocationByLineCodeAndLocationType(String lineCode, String locationType) {
        // 点对点调用服务，smt 模组信息
        if (StringUtils.isBlank(lineCode)) {
            return new LinkedList<>();
        }
        SmtLocationInfoDTO smtLocationInfoDTO = new SmtLocationInfoDTO();
        smtLocationInfoDTO.setLineCode(lineCode);
        smtLocationInfoDTO.setLocationType(locationType);
        String url = "/smtLocationInfo/getList";
        String msg = MicroServiceRestUtil.invokeService(MicroServiceNameEum.BASICSETTING, MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEGET, url, JSON.toJSONString(smtLocationInfoDTO), MESHttpHelper.getHttpRequestHeader());
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<LinkedList<SmtLocationInfoDTO>>() {
        });
    }


    /**
     * 获取数据字典配置
     *
     * @param lookupTypes lookupTypes列表
     * @return
     */
    public static List<SysLookupValuesDTO> getSettings(List<String> lookupTypes) {
        // 查询数据字典
        Map<String, Object> map = new HashMap<>();
        String lookupType = String.join(Constant.COMMA, lookupTypes);
        map.put("lookupType", lookupType);
        return BasicsettingRemoteService.getLookupValueByTypeCodes(map);
    }

    /**
     * 获取单条配置信息，获取不到报错
     *
     * @param settings   ERP相关数据字典
     * @param lookupCode lookupCode
     * @return 配置的LookupMeaning
     */
    public static String getSetting(List<SysLookupValuesDTO> settings, String lookupCode) {
        return getSetting(settings, lookupCode, false);
    }

    /**
     * 获取单条配置信息
     *
     * @param settings   ERP相关数据字典
     * @param lookupCode lookupCode
     * @param nullable   是否可为空
     * @return 配置的LookupMeaning
     */
    public static String getSetting(List<SysLookupValuesDTO> settings, String lookupCode, boolean nullable) {
        BigDecimal lookupCodeDec = new BigDecimal(lookupCode);
        Optional<SysLookupValuesDTO> first = settings.stream()
                .filter(p -> lookupCodeDec.equals(p.getLookupCode()))
                .findFirst();
        if (!first.isPresent()) {
            if (nullable) {
                return Constant.STRING_EMPTY;
            }
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SETTING_IS_NULL, new Object[]{lookupCode});
        }
        return first.get().getLookupMeaning();
    }

    /**
     * 获取单条配置信息
     *
     * @param settings     ERP相关数据字典
     * @param lookupCode   lookupCode
     * @param defaultValue 默认值
     * @return 配置的LookupMeaning
     */
    public static String getSetting(List<SysLookupValuesDTO> settings, String lookupCode, String defaultValue) {
        String setting = getSetting(settings, lookupCode, true);
        if (StringUtils.isEmpty(setting)) {
            return defaultValue;
        }
        return setting;
    }

    /**
     * 校验站位并返回线体配置
     *
     * @param lineCode
     * @param moduleNo
     * @param machineNo
     * @param locationNo
     * @return
     * @throws MesBusinessException
     */
    public static SmtLocationInfo asmInfoQuery(String lineCode, String moduleNo, String machineNo, String locationNo) {
        SmtLocationInfo smtLocationInfo = null;
        String url = "/smtLocationInfo/asmInfoQuery";
        Map<String, Object> map = new HashMap<>();
        map.put("lineCode", lineCode);
        map.put("moduleNo", moduleNo);
        map.put("machineNo", machineNo);
        map.put("locationNo", locationNo);
        map.put("enabledFlag", Constant.FLAG_Y);
        JsonNode json = HttpRemoteService.pointToPointSelectiveByObj(MicroServiceNameEum.BASICSETTING,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEGET, url, map);
        // 不支持设备对接情况返回空,只有支持设备对接情况才返回数据
        if (null != json && json.get(Constant.CODE) != null && json.get(Constant.BO) != null
                && json.get(Constant.CODE).get(Constant.CODE) != null && RetCode.SUCCESS_CODE.equalsIgnoreCase(json.get(Constant.CODE).get(Constant.CODE).asText())) {
            String boStr = json.get(Constant.BO).toString();
            smtLocationInfo = JacksonJsonConverUtil.jsonToBean(boStr, SmtLocationInfo.class);
        }
        return smtLocationInfo;
    }


    public static OneKeySwitchMoutingDTO getSmtLocationLineInfo(String locationSn) {
        String url = "/smtLocationInfo/getSmtLocationLineInfo";
        JsonNode jsonNode = HttpRemoteService.pointToPointSelectiveByObj(MicroServiceNameEum.BASICSETTING,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEGET, url, new HashMap() {{
                    put("locationSn", locationSn);
                }});
        if (null != jsonNode) {
            JsonNode bo = jsonNode.get(Constant.BO);
            if (null != bo) {
                String boStr = bo.toString();
                return JacksonJsonConverUtil.jsonToBean(boStr, OneKeySwitchMoutingDTO.class);
            }
        }
        return null;
    }

    /**
     * 获取车间list
     *
     * @param workshopNameList
     * @return
     */
    public static List<CfWorkshop> getCfWorkshopListByWorkshopNameList(List<String> workshopNameList) {

        List<CfWorkshop> list = new ArrayList<>();
        String url = "/CF/getListByWorkshopNameList";
        JsonNode jsonNode = HttpRemoteService.pointToPointSelectiveByObj(MicroServiceNameEum.BASICSETTING,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST, url, workshopNameList);
        if (null != jsonNode) {
            String bo = jsonNode.get(MpConstant.JSON_BO).get(MpConstant.JSON_ROWS).toString();
            List<CfWorkshop> tempList = JacksonJsonConverUtil.jsonToListBean(bo,
                    new TypeReference<List<CfWorkshop>>() {
                    });
            if (!CollectionUtils.isEmpty(tempList)) {
                list.addAll(tempList);
            }
        }
        return list;
    }

    public static Map<String, String> getLineNameByCodeList(List<String> codeList) {
        Map<String, String> resultMap = new HashMap<>();
        String url = "/CF/getLineNameByCodeList";
        JsonNode jsonNode = HttpRemoteService.pointToPointSelectiveByObj(MicroServiceNameEum.BASICSETTING,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST, url, codeList);
        if (null == jsonNode) {
            return resultMap;
        }
        String bo = jsonNode.get(MpConstant.JSON_BO).toString();
        List<CFLine> cfLines = JacksonJsonConverUtil.jsonToListBean(bo,
                new TypeReference<List<CFLine>>() {
                });
        if (CollectionUtils.isEmpty(cfLines)) {
            return resultMap;
        }
        resultMap = cfLines.stream().collect(Collectors.toMap(k -> k.getLineCode(), v -> v.getLineName(),
                (oldValue, newValue) -> newValue));
        return resultMap;
    }

    public static List<BsItemInfo> getItemType(List<String> itemList) throws Exception {
        // 点对点调用服务，获取该批次的任务信息
        Map<String, String> headerParamsMap = MESHttpHelper.getHttpRequestHeader();
        String url = ConstantInterface.getUrlStatic(InterfaceEnum.getItemTypeListByList);
        String params = JacksonJsonConverUtil.beanToJson(itemList);
        List<BsItemInfo> list = new ArrayList<>();
        String msg = HttpRemoteUtil.remoteExe(params, headerParamsMap, url, MicroServiceNameEum.SENDTYPEPOST);
        JsonNode json = JacksonJsonConverUtil.getMapperInstance().readTree(msg);
        if (null == json) {
            return null;
        }
        JsonNode codeObj = json.get(MpConstant.JSON_CODE);
        if (null != codeObj && RetCode.SUCCESS_CODE.equals(codeObj.get(MpConstant.JSON_CODE).asText())) {
            String bo = json.get(MpConstant.JSON_BO).toString();
            list = JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<ArrayList<BsItemInfo>>() {
            });
            return list;
        }
        return list;
    }


    /**
     * @return
     * <AUTHOR>
     * 根据料单代码集合获取料单版本
     * @Date 2022/11/9 15:58
     * @Param [java.util.List<java.lang.String>]
     **/
    public static List<BBomHeaderDTO> getBBomHeaderByProductCodeList(List<String> bomNoList) throws Exception {
        String url = "/bBomHeaderCtrl/getBBomHeaderByProductCodeList";
        JsonNode json = HttpRemoteService.pointToPointSelectiveByObj(MicroServiceNameEum.BASICSETTING,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST, url, bomNoList);
        List<BBomHeaderDTO> headerDTOList = new ArrayList<>();
        if (null == json || null == json.get(Constant.BO)) {
            return headerDTOList;
        }
        String bo = json.get(MpConstant.JSON_BO).toString();
        String retCode = json.get(Constant.CODE).get(Constant.CODE).asText();
        if (!RetCode.SUCCESS_CODE.equalsIgnoreCase(retCode)) {
            throw new Exception(bo);
        }
        headerDTOList = JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<BBomHeaderDTO>>() {
        });
        return headerDTOList;
    }

    /**
     * @return
     * <AUTHOR>
     * 获取SMT线体及DIP线体代码与名称
     * @Date 2022/11/11 9:54
     * @Param []
     **/
    public static Map<String, String> getSmtAndDipLine() throws Exception {
        List<String> processSectionList = new ArrayList<>();
        processSectionList.add(Constant.SMT);
        processSectionList.add(Constant.DIP);
        String url = "/CF/getSmtAndDipLine";
        JsonNode json = HttpRemoteService.pointToPointSelectiveByObj(MicroServiceNameEum.BASICSETTING,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST, url, processSectionList);
        if (null == json || null == json.get(Constant.BO)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.LINE_INFO_IS_NULL);
        }
        String bo = json.get(MpConstant.JSON_BO).toString();
        String retCode = json.get(Constant.CODE).get(Constant.CODE).asText();
        if (!RetCode.SUCCESS_CODE.equalsIgnoreCase(retCode)) {
            throw new Exception(bo);
        }
        List<CFLine> lineList = JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<CFLine>>() {
        });
        if (org.apache.commons.collections.CollectionUtils.isEmpty(lineList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.LINE_INFO_IS_NULL);
        }
        Map<String, String> lineMap = new HashMap<>();
        for (CFLine cfLine : lineList) {
            lineMap.put(cfLine.getLineCode(), cfLine.getLineName());
        }
        return lineMap;
    }

    /**
     * 批量获取数据字典
     *
     * @param lookUpCodeList 数据字典配置
     * @return 数据字典配置项
     * @throws Exception 业务异常
     */
    public static List<SysLookupValuesDTO> getBatchSysValueByCode(List<String> lookUpCodeList) {
        String url = "/BS/getLookUpValuesBatch";
        String msg = MicroServiceRestUtil.invokeService(MicroServiceNameEum.BASICSETTING,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST,
                url, JSON.toJSONString(lookUpCodeList), MESHttpHelper.getHttpRequestHeader());
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
        if (StringUtils.isBlank(bo)) {
            return new LinkedList<>();
        }
        List<SysLookupValuesDTO> valuesDTOList = JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<SysLookupValuesDTO>>() {
        });
        if (valuesDTOList == null) {
            valuesDTOList = new LinkedList<>();
        }
        return valuesDTOList;
    }

    /**
     * 根据请求头获取指定工厂cfFactory
     *
     * @param factoryCode
     * @throws Exception
     */
    public static List<CFFactory> getFactoryByFactoryCode(String factoryCode) throws Exception {
        Map<String, Object> objectMap = new HashMap<>();
        objectMap.put("factoryCode", factoryCode);
        Map<String, String> headerParamsMap = MESHttpHelper.getHttpRequestHeader();
        // 点对点调用服务
        String sendType = MicroServiceNameEum.SENDTYPEGET;
        String service = MicroServiceNameEum.BASICSETTING;
        String version = MicroServiceNameEum.VERSION;
        String url = "/CF/CFFactory";
        String result = MicroServiceRestUtil.invokeService(service, version, sendType, url, JacksonJsonConverUtil.beanToJson(objectMap),
                headerParamsMap);
        JsonNode resultJson = JacksonJsonConverUtil.getMapperInstance().readTree(result);
        List<CFFactory> factories = new ArrayList<>();
        if (resultJson != null) {
            String bo = resultJson.get(MpConstant.JSON_BO).get(MpConstant.JSON_ROWS).toString();
            factories = JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<CFFactory>>() {
            });
        }
        return factories;
    }

    public static List<ProcessPositionsCheckInDTO> getProcessPositionsCheckIn(String lineCode) {
        List<ProcessPositionsCheckInDTO> resultList = new ArrayList<>();
        String url = "/processPositionsCheckIn/getByLineCode?lineCode=" + lineCode;
        String response = MicroServiceRestUtil.invokeService(MicroServiceNameEum.BASICSETTING,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEGET, url, null,
                MESHttpHelper.getHttpRequestHeader());
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(response);
        if (StringUtils.isBlank(bo)) {
            return resultList;
        }
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<ProcessPositionsCheckInDTO>>() {
        });
    }

    /**
     * @Description: 根据批次查询对应的单板版本
     * @Param: [itemNoSet]
     * @return: java.util.List<com.zte.interfaces.dto.BBomHeaderDTO>
     * @Author: Saber[10307315]
     * @Date: 2022/12/15 下午4:44
     */
    public static List<BBomHeaderDTO> getBBomHeaderByProductCodeSet(Set<String> itemNoSet) {
        List<BBomHeaderDTO> resultList = new ArrayList<>();
        String url = "/bBomHeaderCtrl/getByProductCodeSet";
        String response = MicroServiceRestUtil.invokeService(MicroServiceNameEum.BASICSETTING,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST, url, JSON.toJSONString(itemNoSet),
                MESHttpHelper.getHttpRequestHeader());
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(response);
        if (StringUtils.isBlank(bo)) {
            return resultList;
        }
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<BBomHeaderDTO>>() {
        });
    }

    /**
     * 批量获取班组
     */
    public static List<CfGroupDTO> getWorkGroupBatch(Collection<String> workGroups) {
        String url = "/CfGroup/getWorkGroupBatch";
        String msg = MicroServiceRestUtil.invokeService(MicroServiceNameEum.BASICSETTING,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST,
                url, JSON.toJSONString(workGroups), MESHttpHelper.getHttpRequestHeader());
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
        if (StringUtils.isBlank(bo)) {
            return new LinkedList<>();
        }
        List<CfGroupDTO> valuesDTOList = JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<CfGroupDTO>>() {
        });
        if (valuesDTOList == null) {
            valuesDTOList = new LinkedList<>();
        }
        return valuesDTOList;
    }

    /**
     * 根据线体和工站获取校验工站
     *
     * @param lineCode
     * @param station
     * @return
     */
    public static String getCheckStation(String lineCode, String station) {
        String url = "/lineStationConfig/getCheckStation/" + lineCode + "/" + station;
        String result = MicroServiceRestUtil.invokeService(MicroServiceNameEum.BASICSETTING,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEGET,
                url, null, MESHttpHelper.getHttpRequestHeader());
        ServiceData<String> serviceData = JacksonJsonConverUtil.jsonToListBean(result, new TypeReference<ServiceData<String>>() {
        });
        return serviceData.getBo();
    }

    /**
     * @param lookupType lookupType
     * @return ss
     */
    public static List<SysLookupValuesDTO> getSysLookupValues(String lookupType) {
        Map<String, String> map = new HashMap();
        map.put("lookupType", lookupType);
        String params = JacksonJsonConverUtil.beanToJson(map);
        Map<String, String> headerParamsMap = MESHttpHelper.getHttpRequestHeader();
        // 点对点调用服务
        String serviceName = MicroServiceNameEum.BASICSETTING;
        String version = MicroServiceNameEum.VERSION;
        String sendType = MicroServiceNameEum.SENDTYPEGET;
        String getUrl = "/BS/findListByLookupType";
        String msg = MicroServiceRestUtil.invokeService(serviceName, version, sendType, getUrl, params, headerParamsMap);
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<LinkedList<SysLookupValuesDTO>>() {
        });
    }

    /**
     * 获取线体信息
     *
     * @param lineCodeList
     * @return
     */
    public static List<CFLine> getLinesByCodeList(List<String> lineCodeList) {
        String url = "/CF/getLineNameByCodeList";

        String response = MicroServiceRestUtil.invokeService(MicroServiceNameEum.BASICSETTING, MicroServiceNameEum.VERSION,
                MicroServiceNameEum.SENDTYPEPOST, url, JSON.toJSONString(lineCodeList), MESHttpHelper.getHttpRequestHeader());
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(response);
        if (StringUtils.isBlank(bo)) {
            return new ArrayList<>();
        }
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<CFLine>>() {
        });
    }

    /**
     * 通过仓库批量查线体
     *
     * @return 线体
     */
    public static List<CFLine> queryLineInWarehouseCode(List<String> list) {
        // 点对点调用服务
        List<CFLine> cfLines = new ArrayList<>();
        Map<String, Object> map = new HashMap<>(Constant.INT_2);
        map.put("inWarehouseCode", list);
        String postUrl = "/CF/queryLineInWarehouseCode";
        String response = MicroServiceRestUtil.invokeService(MicroServiceNameEum.BASICSETTING, MicroServiceNameEum.VERSION,
                MicroServiceNameEum.SENDTYPEPOST, postUrl, JSON.toJSONString(map), MESHttpHelper.getHttpRequestHeader());
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(response);

        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<CFLine>>() {
        });
    }

    public static List<BsItemInfo> getItemByAmbiguityNos(List<String> itemList) {

        String url = "/bsItemInfoCtrl/getItemByAmbiguityNos";

        String response = MicroServiceRestUtil.invokeService(MicroServiceNameEum.BASICSETTING, MicroServiceNameEum.VERSION,
                MicroServiceNameEum.SENDTYPEPOST, url, JSON.toJSONString(itemList), MESHttpHelper.getHttpRequestHeader());
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(response);
        if (StringUtils.isBlank(bo)) {
            return new ArrayList<>();
        }
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<BsItemInfo>>() {
        });
    }

    /**
     * 根据物料代码分批获取物料信息
     *
     * @param itemNoList
     * @return
     */
    public static List<BsItemInfo> getItemInfoByItemNoList(List<String> itemNoList) {
        List<BsItemInfo> itemInfoList = new ArrayList<>();
        if (CollectionUtils.isEmpty(itemNoList)) {
            return itemInfoList;
        }
        for (List<String> partitonList : CommonUtils.splitList(itemNoList, NumConstant.NUM_500)) {
            List<BsItemInfo> list = getBsItemInfoByItemNoList(partitonList);
            if (CollectionUtils.isEmpty(list)) {
                continue;
            }
            itemInfoList.addAll(list);
        }
        return itemInfoList;
    }

    /**
     * 根据线体获取托盘物料的站位信息
     *
     * @param smtLocationInfoDTO
     * @return
     */
    public static List<SmtLocationInfoDTO> getLocationInfoByLineCode(SmtLocationInfoDTO smtLocationInfoDTO) {
        String url = "/smtLocationInfo/getListByPost";
        String response = MicroServiceRestUtil.invokeService(MicroServiceNameEum.BASICSETTING, MicroServiceNameEum.VERSION,
                MicroServiceNameEum.SENDTYPEPOST, url, JSON.toJSONString(smtLocationInfoDTO), MESHttpHelper.getHttpRequestHeader());
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(response);
        if (StringUtils.isBlank(bo)) {
            return new ArrayList<>();
        }
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<SmtLocationInfoDTO>>() {
        });
    }

    private static List<BsItemInfo> getBsItemInfoByItemNoList(List<String> itemNoList) {
        // 点对点调用服务，根据物料代码获取物料信息
        String url = "/bsItemInfoCtrl/getItemInfoByItemNoList";

        String response = MicroServiceRestUtil.invokeService(MicroServiceNameEum.BASICSETTING, MicroServiceNameEum.VERSION,
                MicroServiceNameEum.SENDTYPEPOST, url, JSON.toJSONString(itemNoList), MESHttpHelper.getHttpRequestHeader());
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(response);
        if (StringUtils.isBlank(bo)) {
            return new ArrayList<>();
        }
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<BsItemInfo>>() {
        });
    }

    public static List<SmtLocationInfoDTO> getListSmtLocation(String lineCode, String locationNo, String moduleNo) {
        // 点对点调用服务，smt 模组信息
        if (StringUtils.isBlank(lineCode)) {
            return new LinkedList<>();
        }
        Map<String, String> params = new HashMap<>();
        params.put("lineCode", lineCode);
        params.put("moduleNo", moduleNo);
        params.put("locationNo", locationNo);
        String url = "/smtLocationInfo/getList";
        String msg = MicroServiceRestUtil.invokeService(MicroServiceNameEum.BASICSETTING,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEGET, url,
                JacksonJsonConverUtil.beanToJson(params), MESHttpHelper.getHttpRequestHeader());
        msg = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
        return JacksonJsonConverUtil.jsonToListBean(msg, new TypeReference<LinkedList<SmtLocationInfoDTO>>() {
        });
    }

    public static SmtLocationInfoDTO getSmtLocationInfoByLocationSn(String locationSn) {
        String url = "/smtLocationInfo/getSmtLocationLineInfo";
        JsonNode jsonNode = HttpRemoteService.pointToPointSelectiveByObj(MicroServiceNameEum.BASICSETTING,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEGET, url, new HashMap() {{
                    put("locationSn", locationSn);
                }});
        if (null != jsonNode) {
            JsonNode bo = jsonNode.get(Constant.BO);
            if (null != bo) {
                String boStr = bo.toString();
                return JacksonJsonConverUtil.jsonToBean(boStr, SmtLocationInfoDTO.class);
            }
        }
        return null;
    }


    public static void updateOrInsertItemAngle(PolarItemInfoDTO polarItemInfoDTO) {
        // 点对点调用服务，根据物料代码获取物料信息
        String url = "/PolarItemInfo/updateOrInsertForAngle";
        String params = JacksonJsonConverUtil.beanToJson(polarItemInfoDTO);
        String result = MicroServiceRestUtil.invokeService(MicroServiceNameEum.BASICSETTING, MicroServiceNameEum.VERSION,
                MicroServiceNameEum.SENDTYPEPOST, url, params, MESHttpHelper.getHttpRequestHeader());
        ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(result);
    }

    public static List<PolarItemInfo> queryMultiBrandByItemNo(List<String> itemNos) {
        String url = "/PolarItemInfo/queryMultiBrandByItemNo";
        String response = MicroServiceRestUtil.invokeService(MicroServiceNameEum.BASICSETTING,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST, url,
                JSON.toJSONString(itemNos), MESHttpHelper.getHttpRequestHeader());
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(response);
        if (StringUtils.isBlank(bo)) {
            return new ArrayList<>();
        }
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<PolarItemInfo>>() {
        });
    }

    public static List<PolarItemInfoDTO> getPolarItemInfoList(PolarItemInfoDTO polarItemInfoDTO) {
        // 点对点调用服务，根据物料代码获取物料信息
        String url = "/PolarItemInfo/getList";
        String response = MicroServiceRestUtil.invokeService(MicroServiceNameEum.BASICSETTING, MicroServiceNameEum.VERSION,
                MicroServiceNameEum.SENDTYPEPOST, url, JSON.toJSONString(polarItemInfoDTO), MESHttpHelper.getHttpRequestHeader());
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(response);
        if (StringUtils.isBlank(bo)) {
            return new ArrayList<>();
        }
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<PolarItemInfoDTO>>() {
        });
    }


    public static List<SysLookupValuesDTO> getLookupTypesByValue(SysLookupValuesDTO dto) {
        String params = JacksonJsonConverUtil.beanToJson(dto);
        // 点对点调用服务
        String result = MicroServiceRestUtil.invokeService(MicroServiceNameEum.BASICSETTING,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST, InterfaceEnum.getLookupTypesByValue.getUrl(), params, MESHttpHelper.getHttpRequestHeader());
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(result);

        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<SysLookupValuesDTO>>() {
        });
    }

    /**
     * 查询错误代码信息
     *
     * @return
     */
    public static List<BsErrorCodeInfoDTO> getErrorCodeInfoList() {
        // 点对点调用服务
        String serviceName = MicroServiceNameEum.BASICSETTING;
        String getUrl = "/BS/bsErrorCodeInfo";
        String msg = MicroServiceRestUtil.invokeService(serviceName, MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEGET,
                getUrl, "{}", MESHttpHelper.getHttpRequestHeader());
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<LinkedList<BsErrorCodeInfoDTO>>() {
        });
    }


    /**
     * 获取产能信息
     *
     * @param lineCode,itemCodes
     * @return
     * @throws Exception
     */
    public static List<BManufactureCapacityDTO> getBManufactureCapacityInfo(String lineCode, List<String> itemCodes) {
        // 点对点调用指令查询服务
        BManufactureCapacityDTO capacityDTO = new BManufactureCapacityDTO();
        capacityDTO.setLineCode(lineCode);
        capacityDTO.setItemCodeList(itemCodes);
        String url = "/BMC/bsBarcodeInfoByPost";
        String msg = MicroServiceRestUtil.invokeService(MicroServiceNameEum.BASICSETTING, MicroServiceNameEum.VERSION,
                MicroServiceNameEum.SENDTYPEPOST, url, JSON.toJSONString(capacityDTO), MESHttpHelper.getHttpRequestHeader());
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<BManufactureCapacityDTO>>() {
        });
    }

    /***
     * 获取品牌物料定义的物料角度
     *
     * @return
     */
    public static List<PolarItemInfoDTO> getBrandItemDirection(List<PolarItemInfoDTO> list) {
        String url = "/PolarItemInfo/getBrandItemDirection";
        String msg = MicroServiceRestUtil.invokeService(MicroServiceNameEum.BASICSETTING, MicroServiceNameEum.VERSION,
                MicroServiceNameEum.SENDTYPEPOST, url, JSON.toJSONString(list), MESHttpHelper.getHttpRequestHeader());
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
        if (StringUtils.isBlank(bo)) {
            return new ArrayList<>();
        }
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<PolarItemInfoDTO>>() {
        });
    }

    public static List<BBomDetailDTO> getBomDetailTagNo(String productCode) {
        if (StringUtils.isBlank(productCode)) {
            return new ArrayList<>();
        }
        String url = "/bBomDetailCtrl/getBomDetailTagNo/" + productCode;
        String msg = MicroServiceRestUtil.invokeService(MicroServiceNameEum.BASICSETTING,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEGET, url, null, MESHttpHelper.getHttpRequestHeader());
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
        if (StringUtils.isBlank(bo)) {
            return new ArrayList<>();
        }
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<BBomDetailDTO>>() {
        });
    }

    public static List<PolarItemInfo> queryDirectionByItemNo(List<String> itemNos) {
        String url = "/PolarItemInfo/queryDirectionByItemNo";
        String response = MicroServiceRestUtil.invokeService(MicroServiceNameEum.BASICSETTING,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST, url,
                JSON.toJSONString(itemNos), MESHttpHelper.getHttpRequestHeader());
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(response);
        if (StringUtils.isBlank(bo)) {
            return new ArrayList<>();
        }
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<PolarItemInfo>>() {
        });
    }

    public static List<MtlSecondaryInventories> getMtlSecondaryInventories(MtlSecondaryInventories secondaryInventories) {
        String getUrl = "/BS/mtlSecondaryInventories";
        // 调用微服务并获取响应消息
        String responseMsg = MicroServiceRestUtil.invokeService(
                MicroServiceNameEum.BASICSETTING,
                MicroServiceNameEum.VERSION,
                MicroServiceNameEum.SENDTYPEGET,
                getUrl,
                JSON.toJSONString(secondaryInventories),
                MESHttpHelper.getHttpRequestHeader()
        );
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(responseMsg);
        if (StringUtils.isBlank(bo)) {
            return new ArrayList<>();
        }
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<MtlSecondaryInventories>>() {
        });
    }

    public static Page<BsBomHierarchicalDetail> selectBsBomHierarchicalByPage(Page<BsBomHierarchicalDetail> bsBomHierarchicalDetailPage) {
        String getUrl = "/bsBomHierarchicalController/selectBsBomHierarchicalByPage";
        // 调用微服务并获取响应消息
        String responseMsg = MicroServiceRestUtil.invokeService(
                MicroServiceNameEum.BASICSETTING,
                MicroServiceNameEum.VERSION,
                MicroServiceNameEum.SENDTYPEPOST,
                getUrl,
                JSON.toJSONString(bsBomHierarchicalDetailPage),
                MESHttpHelper.getHttpRequestHeader()
        );
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(responseMsg);
        if (StringUtils.isBlank(bo)) {
            return null;
        }
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<Page<BsBomHierarchicalDetail>>() {
        });
    }
}
