<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.PmRepairInfoRepository">
  <resultMap id="BaseResultMap" type="com.zte.domain.model.PmRepairInfo">
    <id column="REPAIR_ID" jdbcType="VARCHAR" property="repairId" />
    <result column="SN" jdbcType="VARCHAR" property="sn" />
    <result column="ITEM_NAME" jdbcType="VARCHAR" property="itemName" />
    <result column="ITEM_CODE" jdbcType="VARCHAR" property="itemCode" />
    <result column="PRODPLAN_ID" jdbcType="VARCHAR" property="prodplanId" />
    <result column="WORK_ORDER_NO" jdbcType="VARCHAR" property="workOrderNo" />
    <result column="STATUS" jdbcType="DECIMAL" property="status" />
    <result column="REPAIR_RESULT" jdbcType="DECIMAL" property="repairResult" />
    <result column="CRAFT_SECTION" jdbcType="VARCHAR" property="craftSection" />
    <result column="WORK_STATION" jdbcType="VARCHAR" property="workStation" />
    <result column="NEXT_STATION" jdbcType="VARCHAR" property="nextStation" />
    <result column="NEXT_PROCESS" jdbcType="VARCHAR" property="nextProcess" />
    <result column="REPAIR_PRODUCT_TYPE" jdbcType="VARCHAR" property="repairProducetType" />
    <result column="REPAIR_PRODUCT_STYPE" jdbcType="VARCHAR" property="repairProductStype" />
    <result column="REPAIR_PRODUCT_MSTYPE" jdbcType="VARCHAR" property="repairProuctMstype" />
    <result column="REPAIR_REMARK" jdbcType="VARCHAR" property="repairRemark" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
    <result column="LAST_UPDATED_BY" jdbcType="VARCHAR" property="lastUpdatedBy" />
    <result column="LAST_UPDATED_DATE" jdbcType="TIMESTAMP" property="lastUpdatedDate" />
    <result column="ENABLED_FLAG" jdbcType="VARCHAR" property="enabledFlag" />
    <result column="ORG_ID" jdbcType="DECIMAL" property="orgId" />
    <result column="FACTORY_ID" jdbcType="DECIMAL" property="factoryId" />
    <result column="ENTITY_ID" jdbcType="DECIMAL" property="entityId" />
    <result column="RECEPTION_ID" jdbcType="VARCHAR" property="receptionId" />
    <result column="REPAIR_PROCESS" jdbcType="VARCHAR" property="repairProcess" />
  </resultMap>
  <!--引用查询1对多 -->
  <resultMap id="RelResultMap" type="com.zte.domain.model.PmRepairInfo">
    <id column="REPAIR_ID" jdbcType="VARCHAR" property="repairId" />
    <result column="SN" jdbcType="VARCHAR" property="sn" />
    <result column="ITEM_NAME" jdbcType="VARCHAR" property="itemName" />
    <result column="ITEM_CODE" jdbcType="VARCHAR" property="itemCode" />
    <result column="PRODPLAN_ID" jdbcType="VARCHAR" property="prodplanId" />
    <result column="WORK_ORDER_NO" jdbcType="VARCHAR" property="workOrderNo" />
    <result column="STATUS" jdbcType="DECIMAL" property="status" />
    <result column="REPAIR_RESULT" jdbcType="DECIMAL" property="repairResult" />
    <result column="CRAFT_SECTION" jdbcType="VARCHAR" property="craftSection" />
    <result column="WORK_STATION" jdbcType="VARCHAR" property="workStation" />
    <result column="NEXT_STATION" jdbcType="VARCHAR" property="nextStation" />
    <result column="NEXT_PROCESS" jdbcType="VARCHAR" property="nextProcess" />
    <result column="REPAIR_PRODUCT_TYPE" jdbcType="VARCHAR" property="repairProducetType" />
    <result column="REPAIR_PRODUCT_STYPE" jdbcType="VARCHAR" property="repairProductStype" />
    <result column="REPAIR_PRODUCT_MSTYPE" jdbcType="VARCHAR" property="repairProuctMstype" />
    <result column="REPAIR_REMARK" jdbcType="VARCHAR" property="repairRemark" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
    <result column="LAST_UPDATED_BY" jdbcType="VARCHAR" property="lastUpdatedBy" />
    <result column="LAST_UPDATED_DATE" jdbcType="TIMESTAMP" property="lastUpdatedDate" />
    <result column="ENABLED_FLAG" jdbcType="VARCHAR" property="enabledFlag" />
    <result column="ORG_ID" jdbcType="DECIMAL" property="orgId" />
    <result column="FACTORY_ID" jdbcType="DECIMAL" property="factoryId" />
    <result column="ENTITY_ID" jdbcType="DECIMAL" property="entityId" />
    <result column="RECEPTION_ID" jdbcType="VARCHAR" property="receptionId" />
    <result column="REPAIR_PROCESS" jdbcType="VARCHAR" property="repairProcess" />
    <collection property = "pmRepairDetails" javaType = "java.util.ArrayList"
      ofType="com.zte.domain.model.PmRepairDetail">
      <id column="REPAIR_ID" jdbcType="VARCHAR" property="repairId" />
      <result column="ERROR_CODE" jdbcType="VARCHAR" property="errorCode" />
      <result column="ERROR_DESCRIPTION" jdbcType="VARCHAR" property="errorDescription" />
      <result column="REASON_CODE" jdbcType="VARCHAR" property="reasonCode" />
      <result column="REASON_DESCRIPTION" jdbcType="VARCHAR" property="reasonDescription" />
      <result column="LOCATION_NO" jdbcType="VARCHAR" property="locationNo" />
    </collection>
  </resultMap>
  <!--引用查询1对1 -->
  <resultMap id="RelOneResultMap" type="com.zte.domain.model.PmRepairInfo">
    <id column="REPAIR_ID" jdbcType="VARCHAR" property="repairId" />
    <result column="SN" jdbcType="VARCHAR" property="sn" />
    <result column="ITEM_NAME" jdbcType="VARCHAR" property="itemName" />
    <result column="ITEM_CODE" jdbcType="VARCHAR" property="itemCode" />
    <result column="PRODPLAN_ID" jdbcType="VARCHAR" property="prodplanId" />
    <result column="WORK_ORDER_NO" jdbcType="VARCHAR" property="workOrderNo" />
    <result column="STATUS" jdbcType="DECIMAL" property="status" />
    <result column="REPAIR_RESULT" jdbcType="DECIMAL" property="repairResult" />
    <result column="CRAFT_SECTION" jdbcType="VARCHAR" property="craftSection" />
    <result column="WORK_STATION" jdbcType="VARCHAR" property="workStation" />
    <result column="NEXT_STATION" jdbcType="VARCHAR" property="nextStation" />
    <result column="NEXT_PROCESS" jdbcType="VARCHAR" property="nextProcess" />
    <result column="REPAIR_PRODUCT_TYPE" jdbcType="VARCHAR" property="repairProducetType" />
    <result column="REPAIR_PRODUCT_STYPE" jdbcType="VARCHAR" property="repairProductStype" />
    <result column="REPAIR_PRODUCT_MSTYPE" jdbcType="VARCHAR" property="repairProuctMstype" />
    <result column="REPAIR_REMARK" jdbcType="VARCHAR" property="repairRemark" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
    <result column="LAST_UPDATED_DATE" jdbcType="TIMESTAMP" property="lastUpdatedDate" />
    <result column="ENABLED_FLAG" jdbcType="VARCHAR" property="enabledFlag" />
    <result column="ORG_ID" jdbcType="DECIMAL" property="orgId" />
    <result column="FACTORY_ID" jdbcType="DECIMAL" property="factoryId" />
    <result column="ENTITY_ID" jdbcType="DECIMAL" property="entityId" />

    <result column="ERROR_CODE" jdbcType="VARCHAR" property="errorCode" />
    <result column="ERROR_DESCRIPTION" jdbcType="VARCHAR" property="errorDescription" />
    <result column="REASON_CODE" jdbcType="VARCHAR" property="reasonCode" />
    <result column="REASON_DESCRIPTION" jdbcType="VARCHAR" property="reasonDescription" />
    <result column="REPAIR_DETAIL_ID" jdbcType="VARCHAR" property="repairDetailId" />
    <result column="LOCATION_NO" jdbcType="VARCHAR" property="locationNo" />
    <result column="RECEPTION_ID" jdbcType="VARCHAR" property="receptionId" />
    <result column="REPAIRSTATUS" jdbcType="VARCHAR" property="repairStatus" />
    <result column="REPAIRCREATE" jdbcType="TIMESTAMP" property="repairCreate" />
    <result column="REPAIRUPDATE" jdbcType="TIMESTAMP" property="repairUpdate" />
    <result column="REPAIR_RCV_DATE" jdbcType="TIMESTAMP" property="repairRcvDate" />
    <result column="RCV_PRODPLAN_ID" jdbcType="VARCHAR" property="rcvProdplanId" />
    <result column="FROM_STATION" jdbcType="VARCHAR" property="fromStation" />
    <result column="WAREHOUSE_CODE" jdbcType="VARCHAR" property="warehouseCode" />
    <result column="BUILDING" jdbcType="VARCHAR" property="building" />
    <result column="RCV_CRAFT_SECTION" jdbcType="VARCHAR" property="rcvCraftSection" />

    <result column="SEND_FLAG" jdbcType="VARCHAR" property="sendFlag" />
    <result column="RCV_FLAG" jdbcType="VARCHAR" property="rcvFlag" />

    <result column="WIP_CRAFT_SECTION" jdbcType="VARCHAR" property="wipCraftSection" />
    <result column="LINE_CODE" jdbcType="VARCHAR" property="lineCode" />
    <result column="LAST_UPDATED_BY" jdbcType="VARCHAR" property="lastUpdatedBy" />
    <result column="DELIVERY_BY" jdbcType="VARCHAR" property="deliveryBy" />
    <result column="RECEPTION_BY" jdbcType="VARCHAR" property="receptionBy" />
    <result column="DELIVERY_NO" jdbcType="VARCHAR" property="deliveryNo" />

    <result column="SCRAP_REASON" jdbcType="VARCHAR" property="scrapReason" />
    <result column="SCAN_TIME" jdbcType="TIMESTAMP" property="scanTime" />
    <result column="repairCycle" jdbcType="DECIMAL" property="repairCycle" />

    <result column="ITEM_NO" jdbcType="VARCHAR" property="itemNo" />
    <result column="ATTRIBUTE1" jdbcType="VARCHAR" property="attribute1" />
    <result column="ATTRIBUTE2" jdbcType="VARCHAR" property="attribute2" />

    <result column="IS_SUB" jdbcType="VARCHAR" property="isSub" />
    <result column="SUB_ITEM_SN" jdbcType="VARCHAR" property="subItemSn" />
    <result column="SUB_ITEM_CODE" jdbcType="VARCHAR" property="subItemCode" />
    <result column="SUN_ITEM_NAME" jdbcType="VARCHAR" property="sunItemName" />
    <result column="IS_LOCATION_NO" jdbcType="VARCHAR" property="isLocationNo" />
    <result column="ITEM_SN" jdbcType="VARCHAR" property="itemSn" />
    <result column="PRD_ITEM_CODE" jdbcType="VARCHAR" property="prdItemCode" />
    <result column="PRD_ITEM_NAME" jdbcType="VARCHAR" property="prdItemnName" />
    <result column="STYLE" jdbcType="VARCHAR" property="style" />
    <result column="BG_BRAND_NO" jdbcType="VARCHAR" property="bgBrandNo" />
    <result column="SUPPLIER_NAME" jdbcType="VARCHAR" property="supplierName" />
    <result column="IS_CONTROL" jdbcType="VARCHAR" property="isControl" />
    <result column="REPLACE_SN" jdbcType="VARCHAR" property="replaceSn" />
    <result column="REPLACE_ITEM_CODE" jdbcType="VARCHAR" property="replaceItemCode" />
    <result column="REPLACE_STYLE" jdbcType="VARCHAR" property="replaceStyle" />
    <result column="REPLACE_BRAND" jdbcType="VARCHAR" property="replaceBrand" />
    <result column="REPLACE_SUPPLIER" jdbcType="VARCHAR" property="replaceSupplier" />
    <result column="RESULT" jdbcType="VARCHAR" property="result" />
    <result column="ADVERSE_TYPE" jdbcType="VARCHAR" property="adverseType" />
    <result column="REPAIR_METHOD" jdbcType="VARCHAR" property="repairMethod" />
    <result column="APPROVER" jdbcType="VARCHAR" property="approver" />
    <result column="EXCHANGE_SN" jdbcType="VARCHAR" property="exchangeSn" />

    <result column="PCB_VERSION" jdbcType="VARCHAR" property="pcbVersion" />
    <result column="RECEPTION_BY" jdbcType="VARCHAR" property="receptionBy" />
    <result column="RECEIVING_TIME" jdbcType="VARCHAR" property="receivingTime" />
    <result column="RETURNED_DATE" jdbcType="VARCHAR" property="returnedDate" />
    <result column="APPLICATION_DEPARTMENT" jdbcType="VARCHAR" property="applicationDepartment"/>
    <result column="APPLICATION_SECTION" jdbcType="VARCHAR" property="applicationSection" />
    <result column="PRODUCT_CLASS" jdbcType="VARCHAR" property="productClass" />
    <result column="PRODUCT_SMLCLASS" jdbcType="VARCHAR" property="productSmlclass" />
    <result column="REPAIR_PROCESS" jdbcType="VARCHAR" property="repairProcess" />
    <result column="RETURNED_TO" jdbcType="VARCHAR" property="returnedTo" />
    <result column="RETURNED_BY" jdbcType="VARCHAR" property="returnedBy" />
    <result column="RETURNED_DATE" jdbcType="TIMESTAMP" property="returnedDate" />
    <result column="REPAIR_COUNT" jdbcType="DECIMAL" property="repairCount" />
    <result column="REPAIR_BY" jdbcType="VARCHAR" property="repairBy" />
    <result column="SN_TYPE" jdbcType="VARCHAR" property="snType" />
    <result column="PROCESS_CODE" jdbcType="VARCHAR" property="processCode" />
  </resultMap>
  <!--
      	统计结果
  -->
  <resultMap id="CollecMap" type="com.zte.domain.model.PmRepairInfoCollec">
    <result column="REPAIR_PRODUCT_STYPE" jdbcType="VARCHAR" property="repairProductStype" />
    <result column="CNT" jdbcType="DECIMAL" property="cnt" />
    <result column="PER" jdbcType="DECIMAL" property="per" />
  </resultMap>
  <!--  iFIS统计结果-->
  <resultMap id="StatisticsMap" type="com.zte.domain.model.PmRepairInfoStatistics">
    <result column="RESULT" jdbcType="VARCHAR" property="result"/>
    <result column="SN" jdbcType="VARCHAR" property="sn"/>
    <result column="PRODPLAN_ID" jdbcType="VARCHAR" property="prodplanId"/>
    <result column="ITEM_CODE" jdbcType="VARCHAR" property="itemCode"/>
    <result column="QTY" jdbcType="DECIMAL" property="qty"/>
  </resultMap>

  <!--mybatis引用： 表名 -->
  <sql id="Table_Name">
    PM_REPAIR_INFO
  </sql>
  <!--mybatis引用： 基础列结构  begin -->
  <!---用于查询 -->
  <sql id="Base_Column_List">
    REPAIR_ID, SN, ITEM_NAME, ITEM_CODE, STATUS,PRODPLAN_ID, WORK_ORDER_NO,
    REPAIR_RESULT,CRAFT_SECTION,WORK_STATION,NEXT_STATION,NEXT_PROCESS,
    REPAIR_PRODUCT_TYPE,REPAIR_PRODUCT_STYPE,REPAIR_PRODUCT_MSTYPE,IS_FIRST_PIECE,
    REPAIR_REMARK, CREATE_BY, CREATE_DATE, LAST_UPDATED_BY, LAST_UPDATED_DATE,
    ENABLED_FLAG,ORG_ID, FACTORY_ID, ENTITY_ID,RECEPTION_ID
  </sql>
  <!--mybatis引用： 基础列筛选结构 -->
  <sql id="Base_Column_Filter_List">
    <if test="repairRemark != null">REPAIR_REMARK,</if>
    <if test="createBy != null">CREATE_BY,</if>
    <if test="lastUpdatedBy != null">LAST_UPDATED_BY,</if>
    <if test="orgId != null">ORG_ID,</if>
    <if test="factoryId != null">FACTORY_ID,</if>
    <if test="entityId != null">ENTITY_ID,</if>
    ENABLED_FLAG,CREATE_DATE, LAST_UPDATED_DATE
  </sql>
  <!--mybatis引用： 基础字段筛选结构 -->
  <sql id="Base_Filed_Filter_List">
      <if test="repairRemark != null">#{repairRemark,jdbcType=VARCHAR},</if>
      <if test="createBy != null">#{createBy,jdbcType=VARCHAR},</if>
      <if test="lastUpdatedBy != null">#{lastUpdatedBy,jdbcType=VARCHAR},</if>
      <if test="orgId != null">#{orgId,jdbcType=DECIMAL},</if>
      <if test="factoryId != null">#{factoryId,jdbcType=DECIMAL},</if>
      <if test="entityId != null">#{entityId,jdbcType=DECIMAL},</if>
      'Y',SYSDATE,SYSDATE
  </sql>
  <sql id="Base_Condtions">
    <include refid="Table_Name" />.ENABLED_FLAG = 'Y'
    <if test="repairId != null and repairId != ''"> AND <include refid="Table_Name" />.REPAIR_ID = #{repairId}</if>
  </sql>

  <!--mybatis引用： 列筛选结构 -->
  <sql id="Column_Filter_List">
    <if test="repairId != null">REPAIR_ID,</if>
    <if test="sn != null">SN,</if>
    <if test="itemName != null">ITEM_NAME,</if>
    <if test="itemCode != null">ITEM_CODE,</if>
    <if test="status != null">STATUS,</if>
    <if test="prodplanId != null">PRODPLAN_ID,</if>
    <if test="workOrderNo != null">WORK_ORDER_NO,</if>
    <if test="repairResult != null">REPAIR_RESULT,</if>
    <if test="craftSection != null">CRAFT_SECTION,</if>
    <if test="workStation != null">WORK_STATION,</if>
    <if test="nextStation != null">NEXT_STATION,</if>
    <if test="nextProcess != null">NEXT_PROCESS,</if>
    <if test="repairProducetType != null">REPAIR_PRODUCT_TYPE,</if>
    <if test="repairProductStype != null">REPAIR_PRODUCT_STYPE,</if>
    <if test="repairProuctMstype != null">REPAIR_PRODUCT_MSTYPE,</if>
    <if test="isFirstPiece != null">IS_FIRST_PIECE,</if>
    <if test="receptionId != null">RECEPTION_ID,</if>
    <include refid="Base_Column_Filter_List" />
  </sql>
  <!--mybatis引用： 字段筛选结构 -->
  <sql id="Filed_Filter_List">
    <if test="repairId != null">#{repairId,jdbcType=VARCHAR},</if>
    <if test="sn != null">#{sn,jdbcType=DECIMAL},</if>
    <if test="itemName != null">#{itemName,jdbcType=VARCHAR},</if>
    <if test="itemCode != null">#{itemCode,jdbcType=VARCHAR},</if>
    <if test="status != null">#{status,jdbcType=DECIMAL},</if>
    <if test="prodplanId != null">#{prodplanId,jdbcType=VARCHAR},</if>
    <if test="workOrderNo != null">#{workOrderNo,jdbcType=VARCHAR},</if>
    <if test="repairResult != null">#{repairResult,jdbcType=VARCHAR},</if>
    <if test="craftSection != null">#{craftSection,jdbcType=VARCHAR},</if>
    <if test="workStation != null">#{workStation,jdbcType=VARCHAR},</if>
    <if test="nextStation != null">#{nextStation,jdbcType=VARCHAR},</if>
    <if test="nextProcess != null">#{nextProcess,jdbcType=VARCHAR},</if>
    <if test="repairProducetType != null">#{repairProducetType,jdbcType=VARCHAR},</if>
    <if test="repairProductStype != null">#{repairProductStype,jdbcType=VARCHAR},</if>
    <if test="repairProuctMstype != null">#{repairProuctMstype,jdbcType=VARCHAR},</if>
    <if test="isFirstPiece != null">#{isFirstPiece,jdbcType=DECIMAL},</if>
    <if test="receptionId != null">#{receptionId,jdbcType=VARCHAR},</if>
    <include refid="Base_Filed_Filter_List" />
  </sql>
  <!--mybatis引用： 列字段筛选结构 -->
  <sql id="Column_Filed_List">
    <if test="sn != null">SN = #{sn,jdbcType=DECIMAL},</if>
    <if test="itemName != null">ITEM_NAME = #{itemName,jdbcType=VARCHAR},</if>
    <if test="itemCode != null">ITEM_CODE = #{itemCode,jdbcType=VARCHAR},</if>
    <if test="status != null">STATUS = #{status,jdbcType=DECIMAL},</if>
    <if test="prodplanId != null">PRODPLAN_ID = #{prodplanId,jdbcType=VARCHAR},</if>

    <if test="workOrderNo != null">REPAIR_RESULT = #{workOrderNo,jdbcType=DECIMAL},</if>
    <if test="repairResult != null">CRAFT_SECTION = #{repairResult,jdbcType=DECIMAL}::text,</if>
    <if test="craftSection != null">WORK_STATION = #{craftSection,jdbcType=VARCHAR},</if>
    <if test="workStation != null">NEXT_STATION = #{workStation,jdbcType=VARCHAR},</if>
    <if test="nextStation != null">NEXT_PROCESS = #{nextStation,jdbcType=VARCHAR},</if>
    <if test="nextProcess != null">REPAIR_REMARK = #{nextProcess,jdbcType=VARCHAR},</if>
    <if test="repairProducetType != null">REPAIR_PRODUCT_TYPE = #{repairProducetType,jdbcType=VARCHAR},</if>
    <if test="repairProductStype != null">REPAIR_PRODUCT_STYPE = #{repairProductStype,jdbcType=VARCHAR},</if>
    <if test="repairProuctMstype != null">REPAIR_PRODUCT_MSTYPE = #{repairProuctMstype,jdbcType=VARCHAR},</if>
    <if test="isFirstPiece != null">IS_FIRST_PIECE = #{isFirstPiece,jdbcType=DECIMAL},</if>

    <if test="repairRemark != null">REPAIR_REMARK = #{repairRemark,jdbcType=VARCHAR},</if>
    <if test="lastUpdatedBy != null">LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR}, </if>
    <if test="receptionId != null">RECEPTION_ID = #{receptionId,jdbcType=VARCHAR},</if>
    LAST_UPDATED_DATE =SYSDATE
  </sql>
  <sql id="Condtions">
    <include refid="Base_Condtions" />
    <if test="sn != null and sn != ''"> AND <include refid="Table_Name" />.SN = #{sn}</if>
    <if test="inSnList != null and inSnList.size > 0"> AND <include refid="Table_Name" />.SN in
      <foreach collection="inSnList" index="index" item="item"
               separator="," open="(" close=")">
        #{item,jdbcType=VARCHAR}
      </foreach>
    </if>
    <if test="itemName != null and itemName != ''"> AND <include refid="Table_Name" />.ITEM_NAME like concat(#{itemName},'%')</if>
    <if test="itemCode != null and itemCode != ''"> AND <include refid="Table_Name" />.ITEM_CODE like concat(#{itemCode},'%')</if>
    <if test="status != null and status != ''"> AND <include refid="Table_Name" />.STATUS = #{status}</if>
    <if test="prodplanId != null and prodplanId != ''"> AND <include refid="Table_Name" />.PRODPLAN_ID like concat(#{prodplanId},'%')</if>
    <if test="workOrderNo != null and workOrderNo != ''"> AND <include refid="Table_Name" />.WORK_ORDER_NO like concat(#{workOrderNo},'%')</if>
    <if test="repairResult != null and repairResult != ''"> AND <include refid="Table_Name" />.REPAIR_RESULT = #{repairResult}</if>
    <if test="craftSection != null and craftSection != ''"> AND <include refid="Table_Name" />.CRAFT_SECTION like concat(#{craftSection},'%')</if>
    <if test="workStation != null and workStation != ''"> AND <include refid="Table_Name" />.WORK_STATION like concat(#{workStation},'%')</if>
    <if test="nextStation != null and nextStation != ''"> AND <include refid="Table_Name" />.NEXT_STATION like concat(#{nextStation},'%')</if>
    <if test="nextProcess != null and nextProcess != ''"> AND <include refid="Table_Name" />.NEXT_PROCESS like concat(#{nextProcess},'%')</if>
    <if test="repairProducetType != null and repairProducetType != ''"> AND <include refid="Table_Name" />.REPAIR_PRODUCT_TYPE like concat(#{repairProducetType},'%')</if>
    <if test="repairRemark != null and repairRemark != ''"> AND <include refid="Table_Name" />.REPAIR_REMARK like concat(#{repairRemark},'%')</if>
    <if test="repairProductStype != null and repairProductStype != ''"> AND <include refid="Table_Name" />.REPAIR_PRODUCT_STYPE like concat(#{repairProductStype},'%')</if>
    <if test="repairProuctMstype != null and repairProuctMstype != ''"> AND <include refid="Table_Name" />.REPAIR_PRODUCT_MSTYPE like concat(#{repairProuctMstype},'%')</if>
    <if test="isFirstPiece != null and isFirstPiece != ''"> AND <include refid="Table_Name" />.IS_FIRST_PIECE = #{isFirstPiece}</if>
	<if test="receptionId != null and receptionId != ''"> AND <include refid="Table_Name" />.RECEPTION_ID = #{receptionId}</if>

    <if test="orgId != null"> AND <include refid="Table_Name" />.ORG_ID = cast(#{orgId} as numeric)</if>
    <if test="factoryId != null"> AND <include refid="Table_Name" />.FACTORY_ID = cast(#{factoryId} as numeric)</if>
    <if test="entityId != null"> AND <include refid="Table_Name" />.ENTITY_ID = cast(#{entityId} as numeric)</if>
    <if test="lastUpdatedBy != null and lastUpdatedBy !=''"> AND <include refid="Table_Name" />.LAST_UPDATED_BY = #{lastUpdatedBy}</if>
    <if test="startTime !=null and startTime != ''"> AND <include refid="Table_Name" />.LAST_UPDATED_DATE &gt;= to_timestamp(#{startTime},'yyyy-mm-dd hh24:mi:ss') </if>
    <if test="endTime !=null and endTime != ''"> AND <include refid="Table_Name" />.LAST_UPDATED_DATE &lt;= to_timestamp(#{endTime},'yyyy-mm-dd hh24:mi:ss') </if>
    <if test="creatStartTime !=null and creatStartTime != ''"> AND <include refid="Table_Name" />.CREATE_DATE &gt;= to_timestamp(#{creatStartTime},'yyyy-mm-dd hh24:mi:ss') </if>
    <if test="creatEndTime !=null and creatEndTime != ''"> AND <include refid="Table_Name" />.CREATE_DATE &lt;= to_timestamp(#{creatEndTime},'yyyy-mm-dd hh24:mi:ss') </if>
  </sql>

   <sql id="RcvCondtions">
    <if test="sn != null and sn != ''"> AND PM_REPAIR_RCV_DETAIL.SN = #{sn}</if>
    <if test="itemName != null and itemName != ''"> AND PM_REPAIR_RCV_DETAIL.ITEM_NAME like concat('%',#{itemName},'%')</if>
    <if test="itemCode != null and itemCode != ''"> AND PM_REPAIR_RCV_DETAIL.ITEM_CODE like concat(#{itemCode},'%')</if>
    <if test="status != null and status != ''"> AND <include refid="Table_Name" />.STATUS = #{status}</if>
    <if test="prodplanId != null and prodplanId != ''"> AND PM_REPAIR_RCV_DETAIL.RCV_PRODPLAN_ID like concat(#{prodplanId},'%')</if>
    <if test="prodplanIds != null and prodplanIds.size() > 0">
      AND PM_REPAIR_RCV_DETAIL.RCV_PRODPLAN_ID in
      <foreach item="item" collection="prodplanIds" separator="," open="(" close=")" >
        #{item,jdbcType=VARCHAR}
      </foreach>
    </if>
    <if test="workOrderNo != null and workOrderNo != ''"> AND <include refid="Table_Name" />.WORK_ORDER_NO like concat(#{workOrderNo},'%')</if>
    <if test="repairResult != null and repairResult != ''"> AND <include refid="Table_Name" />.REPAIR_RESULT = #{repairResult}</if>
    <if test="craftSection != null and craftSection != ''"> AND <include refid="Table_Name" />.CRAFT_SECTION like concat(#{craftSection},'%')</if>
    <if test="nextStation != null and nextStation != ''"> AND <include refid="Table_Name" />.NEXT_STATION like concat(#{nextStation},'%')</if>
    <if test="nextProcess != null and nextProcess != ''"> AND <include refid="Table_Name" />.NEXT_PROCESS like concat(#{nextProcess},'%')</if>
    <if test="repairProducetType != null and repairProducetType != ''"> AND <include refid="Table_Name" />.REPAIR_PRODUCT_TYPE like concat(#{repairProducetType},'%')</if>
    <if test="repairRemark != null and repairRemark != ''"> AND <include refid="Table_Name" />.REPAIR_REMARK like concat(#{repairRemark},'%')</if>
    <if test="repairProductStype != null and repairProductStype != ''"> AND <include refid="Table_Name" />.REPAIR_PRODUCT_STYPE like concat(#{repairProductStype},'%')</if>
    <if test="repairProuctMstype != null and repairProuctMstype != ''"> AND <include refid="Table_Name" />.REPAIR_PRODUCT_MSTYPE like concat(#{repairProuctMstype},'%')</if>
    <if test="isFirstPiece != null and isFirstPiece != ''"> AND <include refid="Table_Name" />.IS_FIRST_PIECE = #{isFirstPiece}</if>
	<if test="receptionId != null and receptionId != ''"> AND <include refid="Table_Name" />.RECEPTION_ID = #{receptionId}</if>
    <if test="lastUpdatedBy != null and lastUpdatedBy !=''"> AND <include refid="Table_Name" />.LAST_UPDATED_BY = #{lastUpdatedBy}</if>
    <if test="startTime !=null and startTime != ''"> AND PM_REPAIR_DETAIL.CREATE_DATE &gt;= to_timestamp(#{startTime},'yyyy-mm-dd hh24:mi:ss') </if>
    <if test="endTime !=null and endTime != ''"> AND PM_REPAIR_DETAIL.CREATE_DATE &lt;= to_timestamp(#{endTime},'yyyy-mm-dd hh24:mi:ss') </if>
    <if test="creatStartTime !=null and creatStartTime != ''"> AND <include refid="Table_Name" />.CREATE_DATE &gt;= to_timestamp(#{creatStartTime},'yyyy-mm-dd hh24:mi:ss') </if>
    <if test="creatEndTime !=null and creatEndTime != ''"> AND <include refid="Table_Name" />.CREATE_DATE &lt;= to_timestamp(#{creatEndTime},'yyyy-mm-dd hh24:mi:ss') </if>
    <if test="sendStartTime !=null and sendStartTime != ''"> AND PM_REPAIR_RCV_DETAIL.CREATE_DATE &gt;= to_timestamp(#{sendStartTime},'yyyy-mm-dd hh24:mi:ss') </if>
    <if test="sendEndTime !=null and sendEndTime != ''"> AND PM_REPAIR_RCV_DETAIL.CREATE_DATE &lt;= to_timestamp(#{sendEndTime},'yyyy-mm-dd hh24:mi:ss') </if>
    <if test="startRepairRcvDate !=null and startRepairRcvDate != ''"> AND PM_REPAIR_RCV_DETAIL.REPAIR_RCV_DATE &gt;= to_timestamp(#{startRepairRcvDate},'yyyy-mm-dd hh24:mi:ss') </if>
    <if test="endRepairRcvDate !=null and endRepairRcvDate != ''"> AND PM_REPAIR_RCV_DETAIL.REPAIR_RCV_DATE &lt;= to_timestamp(#{endRepairRcvDate},'yyyy-mm-dd hh24:mi:ss') </if>
 	<if test="sendFlag != null and sendFlag != ''"> AND PM_REPAIR_RCV_DETAIL.SEND_FLAG = #{sendFlag}</if>
 	<if test="rcvFlag != null and rcvFlag != ''"> AND PM_REPAIR_RCV_DETAIL.RCV_FLAG = #{rcvFlag}</if>
 	<if test="isAccept != null and isAccept != ''"> AND PM_REPAIR_RCV_DETAIL.IS_ACCEPT = cast(#{isAccept} as numeric)</if>
    <if test="productClass != null and productClass != ''"> AND PM_REPAIR_RCV_DETAIL.PRODUCT_CLASS = #{productClass}</if>
 	<if test="locationNo != null and locationNo != ''"> AND PM_REPAIR_DETAIL.LOCATION_NO = #{locationNo}</if>
    <if test="prdItemCode != null and prdItemCode != ''"> AND PM_REPAIR_DETAIL.ITEM_CODE = #{prdItemCode}</if>
    <if test="lineCode != null and lineCode != ''"> AND PM_REPAIR_RCV_DETAIL.LINE_CODE = #{lineCode}</if>
    <if test="style != null and style != ''"> AND PM_REPAIR_RCV_DETAIL.STYLE = #{style}</if>
    <if test="result != null and result != ''"> AND PM_REPAIR_DETAIL.RESULT = #{result}</if>
    <if test="adverseType != null and adverseType != ''"> AND PM_REPAIR_DETAIL.ADVERSE_TYPE = #{adverseType}</if>
    <if test="repairMethod != null and repairMethod != ''"> AND PM_REPAIR_DETAIL.REPAIR_METHOD = #{repairMethod}</if>
     <if test="applicationDepartment != null and applicationDepartment != ''">
       AND PM_REPAIR_RCV.APPLICATION_DEPARTMENT = #{applicationDepartment,jdbcType=VARCHAR}
     </if>
     <if test="applicationSection != null and applicationSection != ''">
       AND PM_REPAIR_RCV.APPLICATION_SECTION = #{applicationSection,jdbcType=VARCHAR}
     </if>
 	<if test="deliveryNo != null and deliveryNo != ''"> AND PM_REPAIR_RCV.DELIVERY_NO = #{deliveryNo}</if>
 	<if test="item != null">
 		<foreach item="item" collection="item" separator="," open="AND PM_REPAIR_RCV_DETAIL.STATUS IN (" close=")"  >
        	#{item}
      	</foreach>
    </if>
     <if test="applicationSectionList != null and applicationSectionList.size() > 0">
       AND PM_REPAIR_RCV.APPLICATION_SECTION in
       <foreach item="item" collection="applicationSectionList" separator="," open="(" close=")" >
         #{item,jdbcType=VARCHAR}
       </foreach>
     </if>
     <if test="repairTeam != null and repairTeam.size() > 0">
       AND PM_REPAIR_DETAIL.REPAIR_TEAM in
       <foreach item="item" collection="repairTeam" separator="," open="(" close=")" >
         #{item,jdbcType=VARCHAR}
       </foreach>
     </if>
 </sql>

   <sql id="RCV_ORDER_FILELD">
    <if test="sort != null">
    <choose>
      <when test="sort=='sn'"> order by PM_REPAIR_RCV_DETAIL.SN <if test="order != null and order == 'desc'"> desc </if> </when>
      <when test="sort=='lastUpdatedDate'"> order by PM_REPAIR_RCV.last_updated_date <if test="order != null and order == 'desc'"> desc </if> </when>
      <when test="sort=='lastUpdatedBy'"> order by PM_REPAIR_RCV.last_updated_by <if test="order != null and order == 'desc'"> desc </if> </when>
      <when test="sort=='createDate'"> order by PM_REPAIR_RCV.create_date <if test="order != null and order == 'desc'"> desc </if> </when>
      <when test="sort=='createBy'"> order by PM_REPAIR_RCV.create_by <if test="order != null and order == 'desc'"> desc </if> </when>
      <when test="sort=='repareCreateDate'"> order by PM_REPAIR_DETAIL.create_date <if test="order != null and order == 'desc'"> desc </if> </when>
    </choose>
    </if>
  </sql>

  <sql id="ORDER_FILELD">
    <if test="sort != null">
    <choose>
      <when test="sort=='repairId'"> order by <include refid="Table_Name" />.REPAIR_ID <if test="order != null and order == 'desc'"> desc </if> </when>
      <when test="sort=='sn'"> order by <include refid="Table_Name" />.SN <if test="order != null and order == 'desc'"> desc </if> </when>
      <when test="sort=='itemName'"> order by <include refid="Table_Name" />.ITEM_NAME <if test="order != null and order == 'desc'"> desc </if> </when>
      <when test="sort=='itemCode'"> order by <include refid="Table_Name" />.ITEM_CODE <if test="order != null and order == 'desc'"> desc </if> </when>
      <when test="sort=='status'"> order by <include refid="Table_Name" />.STATUS <if test="order != null and order == 'desc'"> desc </if> </when>
      <when test="sort=='prodplanId'"> order by <include refid="Table_Name" />.PRODPLAN_ID <if test="order != null and order == 'desc'"> desc </if> </when>
      <when test="sort=='lastUpdatedDate'"> order by <include refid="Table_Name" />.last_updated_date <if test="order != null and order == 'desc'"> desc </if> </when>
      <when test="sort=='lastUpdatedBy'"> order by <include refid="Table_Name" />.last_updated_by <if test="order != null and order == 'desc'"> desc </if> </when>
      <when test="sort=='createDate'"> order by <include refid="Table_Name" />.create_date <if test="order != null and order == 'desc'"> desc </if> </when>
      <when test="sort=='createBy'"> order by <include refid="Table_Name" />.create_by <if test="order != null and order == 'desc'"> desc </if> </when>
      <when test="sort=='repareCreateDate'"> order by PM_REPAIR_DETAIL.create_date <if test="order != null and order == 'desc'"> desc </if> </when>
    </choose>
    </if>
  </sql>

  <!--mybatis引用： 基础列结构   end -->

  <!--mybatis引用： 引用列结构  begin>>-->
  <sql id="Rel_Column_List">
    <include refid="Table_Name" />.REPAIR_ID,
    <include refid="Table_Name" />.SN,
    <include refid="Table_Name" />.ITEM_NAME,
    <include refid="Table_Name" />.ITEM_CODE,
    <include refid="Table_Name" />.STATUS,
    PM_REPAIR_DETAIL.ERROR_CODE,
    PM_REPAIR_DETAIL.ERROR_DESCRIPTION,
    PM_REPAIR_DETAIL.REASON_CODE,
    PM_REPAIR_DETAIL.REASON_DESCRIPTION,
    PM_REPAIR_DETAIL.REPAIR_DETAIL_ID,
    PM_REPAIR_DETAIL.LOCATION_NO,

    PM_REPAIR_DETAIL.IS_SUB,
	PM_REPAIR_DETAIL.SUB_ITEM_SN,
	PM_REPAIR_DETAIL.SUB_ITEM_CODE,
	PM_REPAIR_DETAIL.SUN_ITEM_NAME,
	PM_REPAIR_DETAIL.IS_LOCATION_NO,
	PM_REPAIR_DETAIL.ITEM_SN,
	PM_REPAIR_DETAIL.ITEM_CODE AS PRD_ITEM_CODE,
    PM_REPAIR_DETAIL.ITEM_NAME AS PRD_ITEM_NAME,
	PM_REPAIR_DETAIL.STYLE,
	PM_REPAIR_DETAIL.BG_BRAND_NO,
	PM_REPAIR_DETAIL.SUPPLIER_NAME,
	PM_REPAIR_DETAIL.IS_CONTROL,
	PM_REPAIR_DETAIL.REPLACE_SN,
	PM_REPAIR_DETAIL.REPLACE_ITEM_CODE,
	PM_REPAIR_DETAIL.REPLACE_STYLE,
	PM_REPAIR_DETAIL.REPLACE_BRAND,
	PM_REPAIR_DETAIL.REPLACE_SUPPLIER,
	PM_REPAIR_DETAIL.RESULT,
	PM_REPAIR_DETAIL.ADVERSE_TYPE,
    PM_REPAIR_DETAIL.REPAIR_METHOD,
    PM_REPAIR_DETAIL.APPROVER,
    PM_REPAIR_DETAIL.EXCHANGE_SN,
    PM_REPAIR_DETAIL.REPAIR_TEAM,

    <include refid="Table_Name" />.WORK_STATION,
    <include refid="Table_Name" />.WORK_ORDER_NO,
    <include refid="Table_Name" />.CRAFT_SECTION,
    <include refid="Table_Name" />.REPAIR_PRODUCT_TYPE,
    <include refid="Table_Name" />.REPAIR_PRODUCT_STYPE,
    <include refid="Table_Name" />.REPAIR_PRODUCT_MSTYPE,
    <include refid="Table_Name" />.PRODPLAN_ID,
    <include refid="Table_Name" />.RECEPTION_ID,

    <include refid="Table_Name" />.REPAIR_REMARK,
    <include refid="Table_Name" />.CREATE_BY,
    <include refid="Table_Name" />.CREATE_DATE,
    <include refid="Table_Name" />.LAST_UPDATED_BY,
    <include refid="Table_Name" />.LAST_UPDATED_DATE,
    <include refid="Table_Name" />.ENABLED_FLAG,
    <include refid="Table_Name" />.ORG_ID,
    <include refid="Table_Name" />.FACTORY_ID,
    <include refid="Table_Name" />.ENTITY_ID,
    PM_REPAIR_RCV.APPLICATION_DEPARTMENT,
    PM_REPAIR_RCV.APPLICATION_SECTION
  </sql>
   <!--mybatis引用： 引用列结构  begin>>-->
  <sql id="Rel_One_Column_List">
    PM_REPAIR_INFO.REPAIR_ID,
    PM_REPAIR_RCV_DETAIL.SN,
    PM_REPAIR_RCV_DETAIL.ITEM_NAME,
    PM_REPAIR_RCV_DETAIL.ITEM_CODE,
    PM_REPAIR_INFO.STATUS,
    nvl(PM_REPAIR_DETAIL.ERROR_CODE, PM_REPAIR_RCV_DETAIL.ERROR_CODE) ERROR_CODE,
    nvl(PM_REPAIR_DETAIL.ERROR_DESCRIPTION, PM_REPAIR_RCV_DETAIL.ERROR_DESCRIPTION) ERROR_DESCRIPTION,
    PM_REPAIR_DETAIL.REASON_CODE,
    PM_REPAIR_DETAIL.REASON_DESCRIPTION,
    PM_REPAIR_DETAIL.REPAIR_DETAIL_ID,
    PM_REPAIR_RCV_DETAIL.PRODUCT_CLASS,
    PM_REPAIR_RCV_DETAIL.PRODUCT_SMLCLASS,
    PM_REPAIR_DETAIL.LOCATION_NO,
    PM_REPAIR_DETAIL.REMARK,
    PM_REPAIR_INFO.CRAFT_SECTION,
    PM_REPAIR_INFO.WORK_STATION,
	nvl(PM_REPAIR_DETAIL.REPAIR_PRODUCT_TYPE, PM_REPAIR_INFO.REPAIR_PRODUCT_TYPE) REPAIR_PRODUCT_TYPE,
	nvl(PM_REPAIR_DETAIL.REPAIR_PRODUCT_STYPE, PM_REPAIR_INFO.REPAIR_PRODUCT_STYPE) REPAIR_PRODUCT_STYPE,
	nvl(PM_REPAIR_DETAIL.REPAIR_PRODUCT_MSTYPE, PM_REPAIR_INFO.REPAIR_PRODUCT_MSTYPE) REPAIR_PRODUCT_MSTYPE,
    PM_REPAIR_INFO.RECEPTION_ID,
    PM_REPAIR_INFO.WORK_ORDER_NO,

    PM_REPAIR_INFO.REPAIR_REMARK,
    PM_REPAIR_DETAIL.CREATE_DATE,
    PM_REPAIR_RCV.CREATE_BY,

    PM_REPAIR_INFO.LAST_UPDATED_BY,
    PM_REPAIR_RCV.DELIVERY_BY,
    nvl(PM_REPAIR_RCV.RECEPTION_BY, PM_REPAIR_RCV_DETAIL.RECEPTION_BY) RECEPTION_BY,

    PM_REPAIR_RCV.LAST_UPDATED_DATE,
    PM_REPAIR_RCV.ENABLED_FLAG,
    PM_REPAIR_RCV.ORG_ID,
    PM_REPAIR_RCV.FACTORY_ID,
    PM_REPAIR_RCV.ENTITY_ID,
    PM_REPAIR_RCV.FROM_STATION,
    PM_REPAIR_RCV.WAREHOUSE_CODE,
    PM_REPAIR_RCV.BUILDING,
    PM_REPAIR_RCV.SN_TYPE,
    PM_REPAIR_RCV_DETAIL.STATUS AS REPAIRSTATUS,
    PM_REPAIR_RCV_DETAIL.Create_Date AS REPAIRCREATE,
    PM_REPAIR_RCV_DETAIL.LAST_UPDATED_DATE AS REPAIRUPDATE,
    PM_REPAIR_RCV_DETAIL.REPAIR_RCV_DATE,
    PM_REPAIR_RCV_DETAIL.CRAFT_SECTION AS RCV_CRAFT_SECTION,
    PM_REPAIR_RCV_DETAIL.RCV_PRODPLAN_ID,
    PM_REPAIR_RCV_DETAIL.SEND_FLAG,
    PM_REPAIR_RCV_DETAIL.RCV_FLAG,
    PM_REPAIR_RCV_DETAIL.PROCESS_CODE,
    PM_REPAIR_RCV.DELIVERY_NO,
    WIP_INFO.CRAFT_SECTION WIP_CRAFT_SECTION,
    nvl(WIP_INFO.LINE_CODE, PM_REPAIR_RCV_DETAIL.LINE_CODE) LINE_CODE,
    WIP_INFO.ITEM_NO,
    WIP_INFO.ATTRIBUTE1,
    WIP_INFO.ATTRIBUTE2,
    PM_REPAIR_RCV_DETAIL.RCV_PRODPLAN_ID AS PRODPLAN_ID,
    PM_REPAIR_RCV_DETAIL.SCRAP_REASON,
    PM_REPAIR_RCV_DETAIL.SCAN_TIME,
    PM_REPAIR_RCV_DETAIL.RETURNED_BY,
    PM_REPAIR_RCV_DETAIL.RETURNED_DATE,
    PM_REPAIR_RCV_DETAIL.RETURNED_TO,
    PM_REPAIR_DETAIL.IS_SUB,
	PM_REPAIR_DETAIL.SUB_ITEM_SN,
	PM_REPAIR_DETAIL.SUB_ITEM_CODE,
	PM_REPAIR_DETAIL.SUN_ITEM_NAME,
	PM_REPAIR_DETAIL.IS_LOCATION_NO,
	PM_REPAIR_DETAIL.ITEM_SN,
	PM_REPAIR_DETAIL.ITEM_CODE AS PRD_ITEM_CODE,
    PM_REPAIR_DETAIL.ITEM_NAME AS PRD_ITEM_NAME,
    nvl(nullif(PM_REPAIR_DETAIL.STYLE,''), PM_REPAIR_RCV_DETAIL.STYLE) STYLE,
	PM_REPAIR_DETAIL.BG_BRAND_NO,
	PM_REPAIR_DETAIL.SUPPLIER_NAME,
	PM_REPAIR_DETAIL.IS_CONTROL,
	PM_REPAIR_DETAIL.REPLACE_SN,
	PM_REPAIR_DETAIL.REPLACE_ITEM_CODE,
	PM_REPAIR_DETAIL.REPLACE_STYLE,
	PM_REPAIR_DETAIL.REPLACE_BRAND,
	PM_REPAIR_DETAIL.REPLACE_SUPPLIER,
	PM_REPAIR_DETAIL.RESULT,
	PM_REPAIR_DETAIL.ADVERSE_TYPE,
    PM_REPAIR_DETAIL.REPAIR_METHOD,
    PM_REPAIR_DETAIL.APPROVER,
    PM_REPAIR_DETAIL.EXCHANGE_SN,
    PM_REPAIR_DETAIL.REPAIR_PROCESS,
    PM_REPAIR_DETAIL.REPAIR_BY,
    PM_REPAIR_DETAIL.REPAIR_TEAM,
    PM_REPAIR_RCV_DETAIL.REPAIR_COUNT,
    PM_REPAIR_RCV.APPLICATION_DEPARTMENT,
    PM_REPAIR_RCV.APPLICATION_SECTION,
    <!-- 维修周期： 返还时间 - 接收时间 = 维修周期(天) -->
      <choose>
          <when test="overStockNewOrOld !=null and overStockNewOrOld != ''">
              CASE
              WHEN PM_REPAIR_DETAIL.REPAIR_DATE is null THEN null
              ELSE trunc((extract (epoch from (PM_REPAIR_DETAIL.REPAIR_DATE - PM_REPAIR_RCV_DETAIL.CREATE_DATE))/60/60/24)::numeric,3) END repairCycle
          </when>
          <otherwise>
              trunc((extract (epoch from (PM_REPAIR_RCV_DETAIL.repair_rcv_date - PM_REPAIR_RCV_DETAIL.RECEIVING_TIME))/60/60/24)::numeric,3) as repairCycle
          </otherwise>
      </choose>
  </sql>
  <!--mybatis引用： 关联表名 -->
  <sql id="Rel_Table_Name">

    PM_REPAIR_RCV,<include refid="Table_Name" /> left join
    PM_REPAIR_DETAIL on <include refid="Table_Name" />.REPAIR_ID
    =PM_REPAIR_DETAIL.REPAIR_ID

  </sql>
  <sql id="Rel_One_Table_Name">
    PM_REPAIR_RCV LEFT JOIN PM_REPAIR_RCV_DETAIL ON
    PM_REPAIR_RCV.RECEPTION_ID=PM_REPAIR_RCV_DETAIL.RECEPTION_ID and PM_REPAIR_RCV.ENABLED_FLAG = 'Y' and PM_REPAIR_RCV_DETAIL.ENABLED_FLAG = 'Y'
   LEFT JOIN PM_REPAIR_INFO
    ON PM_REPAIR_INFO.RECEPTION_ID = PM_REPAIR_RCV_DETAIL.RECEPTION_DETAIL_ID and PM_REPAIR_INFO.ENABLED_FLAG = 'Y'
    LEFT JOIN PM_REPAIR_DETAIL
    ON PM_REPAIR_INFO.REPAIR_ID=PM_REPAIR_DETAIL.REPAIR_ID and PM_REPAIR_DETAIL.ENABLED_FLAG = 'Y'
    LEFT JOIN WIP_INFO
    ON WIP_INFO.SN = PM_REPAIR_RCV_DETAIL.SN
  </sql>
  <sql id="Rel_Condtions">

    PM_REPAIR_DETAIL.ENABLED_FLAG = 'Y'  AND
    <if test="productClass != null and productClass != ''"> 1 = 2 AND</if>
    <if test="locationNo != null and locationNo != ''"> PM_REPAIR_DETAIL.LOCATION_NO = #{locationNo} AND</if>
    <if test="prdItemCode != null and prdItemCode != ''">PM_REPAIR_DETAIL.ITEM_CODE = #{prdItemCode} AND</if>
    <if test="applicationDepartment != null and applicationDepartment != ''">
      PM_REPAIR_RCV.APPLICATION_DEPARTMENT = #{applicationDepartment,jdbcType=VARCHAR} AND
    </if>
    <if test="applicationSection != null and applicationSection != ''">
      PM_REPAIR_RCV.APPLICATION_SECTION = #{applicationSection,jdbcType=VARCHAR} AND
    </if>
    <if test="applicationSectionList != null and applicationSectionList.size() > 0">
      PM_REPAIR_RCV.APPLICATION_SECTION in
      <foreach item="item" collection="applicationSectionList" separator="," open="(" close=")" >
        #{item,jdbcType=VARCHAR}
      </foreach>
      AND
    </if>
    <if test="repairTeam != null and repairTeam.size() > 0">
      PM_REPAIR_DETAIL.REPAIR_TEAM in
      <foreach item="item" collection="repairTeam" separator="," open="(" close=")" >
        #{item,jdbcType=VARCHAR}
      </foreach>
      AND
    </if>
    <include refid="Condtions" />

    <if test="(locationNo == null or locationNo == '') and (prdItemCode == null or prdItemCode == '')
        and (applicationDepartment == null or applicationDepartment == '') and (applicationSection == null or applicationSection == '')
        and (repairId == null or repairId == '') and (sn == null or sn == '')
        and (inSnList == null or inSnList.size() ==  0) and (itemName == null or itemName == '')
        and (itemCode == null or itemCode == '') and (status == null or status == '')
        and (prodplanId == null or prodplanId == '') and (workOrderNo == null or workOrderNo == '')
        and (repairResult == null or repairResult == '') and (craftSection == null or craftSection == '')
        and (workStation == null or workStation == '') and (nextStation == null or nextStation == '')
        and (nextProcess == null or nextProcess == '') and (repairProducetType == null or repairProducetType == '')
        and (repairRemark == null or repairRemark == '') and (repairProductStype == null or repairProductStype == '')
         and (repairProuctMstype == null or repairProuctMstype == '') and (isFirstPiece == null or isFirstPiece == '')
          and (receptionId == null or receptionId == '') and (lastUpdatedBy == null or lastUpdatedBy == '')
           and (startTime == null or startTime == '') and (endTime == null or endTime == '')
            and (creatStartTime == null or creatStartTime == '') and (creatEndTime == null or creatEndTime == '')
            and  (startRow == null or endRow == null)  "> and 1=2</if>

  </sql>
  <sql id="Rcv_Condtion">
  	PM_REPAIR_RCV_DETAIL.SN IS NOT NULL AND PM_REPAIR_RCV.BILL_TYPE='10'
  	<if test="fromStation != null and fromStation != ''"> AND PM_REPAIR_RCV.FROM_STATION = #{fromStation}</if>
  	<if test="repairStatus != null and repairStatus != ''"> AND PM_REPAIR_RCV_DETAIL.STATUS = #{repairStatus}</if>
  	<if test="building != null and building != ''"> AND PM_REPAIR_RCV.BUILDING = #{building}</if>
    <if test="inSnList != null and inSnList.size > 0"> and pm_repair_rcv_detail.SN in
      <foreach collection="inSnList" index="index" item="item"
               separator="," open="(" close=")">
        #{item,jdbcType=VARCHAR}
      </foreach>
    </if>
  </sql>
  <!--mybatis引用：引用列结构  <<end -->



  <!--
      mybatis 插入命令：有选择性的增加不良原因维护数据
  -->
  <insert id="insertPmRepairInfoSelective" parameterType="com.zte.domain.model.PmRepairInfo">
    insert into <include refid="Table_Name" />
    <trim prefix="(" suffix=")" suffixOverrides=","><include refid="Column_Filter_List" /></trim>
    <trim prefix="values (" suffix=")" suffixOverrides=","><include refid="Filed_Filter_List" /></trim>
  </insert>
  <!--
      mybatis 删除命令：删除不良原因维护根据主键
                   说明：将有效标识设置为N
                    条件：REPAIR_ID
  -->
  <delete id="deletePmRepairInfoByCode" parameterType="com.zte.domain.model.PmRepairInfo">
    update <include refid="Table_Name" />
       set ENABLED_FLAG = 'N'
     where <include refid="Base_Condtions" />
  </delete>

  <!--
      mybatis 查询命令：获取符合条件的不良原因维护数据
  -->
  <select id="getRelList" parameterType="com.zte.interfaces.dto.PmRepairInfoDTO" resultMap="RelResultMap">
    SELECT <include refid="Rel_Column_List" />
    FROM   <include refid="Rel_Table_Name" />
    WHERE <include refid="Rel_Condtions" />
    <include refid="ORDER_FILELD" />
  </select>
  <!--
      mybatis 翻页函数:获取符合条件的记录数
  -->
  <select id="getRelCount" parameterType="com.zte.interfaces.dto.PmRepairInfoDTO" resultType="java.lang.Long">
      select count(*)
      from <include refid="Rel_Table_Name" />
      WHERE <include refid="Rel_Condtions" />
  </select>
  <!-- 翻页函数:获取一页的记录集 -->
  <select id="getRelPage" parameterType="com.zte.interfaces.dto.PmRepairInfoDTO" resultMap="RelResultMap">
      SELECT <include refid="Rel_Column_List" />
      FROM <include refid="Rel_Table_Name" />
      WHERE
      <include refid="Rel_Condtions" />
      <include refid="ORDER_FILELD" />
      <if test="startRow != null and endRow != null">
        limit #{endRow}::numeric-#{startRow}::numeric+1 offset case when ( #{startRow}::numeric-1 &lt; 0) then 0 else  #{startRow}::numeric-1 end
      </if>
  </select>


  <!--
      mybatis 查询命令：获取符合条件的维修表关联维修详情表数据
  -->
  <select id="getRelListChange" parameterType="com.zte.interfaces.dto.PmRepairInfoDTO" resultMap="RelOneResultMap">
    SELECT <include refid="Rel_Column_List" />
    FROM   <include refid="Rel_Table_Name" />
    WHERE <include refid="Rel_Condtions" />
    <include refid="ORDER_FILELD" />
  </select>
    <!-- 翻页函数:获取一页的记录集 -->
  <select id="getRelPageChange" parameterType="com.zte.interfaces.dto.PmRepairInfoDTO" resultMap="RelOneResultMap">
      SELECT <include refid="Rel_Column_List" />
      FROM <include refid="Rel_Table_Name" />
      WHERE
      <include refid="Rel_Condtions" />
      <include refid="ORDER_FILELD" />
      <if test="startRow != null and endRow != null">
        limit #{endRow}::numeric-#{startRow}::numeric+1 offset case when ( #{startRow}::numeric-1 &lt; 0) then 0 else  #{startRow}::numeric-1 end
      </if>
  </select>

  <!-- 一维;根据条码查询维修次数 -->
  <select id="getRelChangeRepairCount" parameterType="java.lang.String" resultType="java.lang.Long">
   	select count(*)
      from
    PM_REPAIR_INFO
    left join
    PM_REPAIR_DETAIL on
    PM_REPAIR_INFO
   .REPAIR_ID
    =PM_REPAIR_DETAIL.REPAIR_ID
      WHERE
    PM_REPAIR_DETAIL.ENABLED_FLAG = 'Y'  AND
    PM_REPAIR_INFO
   .ENABLED_FLAG = 'Y'
    <if test="sn != null and sn != ''"> AND PM_REPAIR_INFO.SN = #{sn}</if>
    <if test="workStation != null and workStation != ''"> AND PM_REPAIR_INFO.WORK_STATION like concat('%',#{workStation},'%')</if>
  </select>


  <!--
      mybatis 查询命令：获取符合条件的维修表关联维修详情表关联送修表关联送修详情表数据
  -->
  <select id="getRelOneList" parameterType="com.zte.interfaces.dto.PmRepairInfoDTO" resultMap="RelOneResultMap">
    SELECT <include refid="Rel_One_Column_List" />
    FROM   <include refid="Rel_One_Table_Name" />
    WHERE <include refid="Rcv_Condtion" />
      <include refid="RcvCondtions" />
    <if test="sort != null">
      <choose>
        <when test="sort=='sn'"> order by PM_REPAIR_RCV_DETAIL.SN <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='lastUpdatedDate'"> order by PM_REPAIR_RCV.last_updated_date <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='lastUpdatedBy'"> order by PM_REPAIR_RCV.last_updated_by <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='createDate'"> order by PM_REPAIR_RCV.create_date <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='createBy'"> order by PM_REPAIR_RCV.create_by <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='repareCreateDate'"> order by PM_REPAIR_DETAIL.create_date <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='repairInfoCreateDate'"> order by PM_REPAIR_INFO.CREATE_DATE <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='rcvDetailCreateDate'"> order by PM_REPAIR_RCV_DETAIL.CREATE_DATE <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='repairRcvDate'"> order by PM_REPAIR_RCV_DETAIL.REPAIR_RCV_DATE <if test="order != null and order == 'desc'"> desc </if> </when>
      </choose>
    </if>
  </select>
  <!--
      mybatis 翻页函数:获取符合条件的记录数
  -->
  <select id="getRelOneCount" parameterType="com.zte.interfaces.dto.PmRepairInfoDTO" resultType="java.lang.Long">
      select count(*)
      from <include refid="Rel_One_Table_Name" />
      WHERE <include refid="Rcv_Condtion" />
      <include refid="RcvCondtions" />
  </select>
  <!-- 翻页函数:获取一页的记录集 -->
  <select id="getRelOnePage" parameterType="com.zte.interfaces.dto.PmRepairInfoDTO" resultMap="RelOneResultMap">
      SELECT <include refid="Rel_One_Column_List" />
      FROM <include refid="Rel_One_Table_Name" />
      WHERE <include refid="Rcv_Condtion" />
      <include refid="RcvCondtions" />
    <if test="sort != null">
      <choose>
        <when test="sort=='sn'"> order by PM_REPAIR_RCV_DETAIL.SN <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='lastUpdatedDate'"> order by PM_REPAIR_RCV.last_updated_date <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='lastUpdatedBy'"> order by PM_REPAIR_RCV.last_updated_by <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='createDate'"> order by PM_REPAIR_RCV.create_date <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='createBy'"> order by PM_REPAIR_RCV.create_by <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='repareCreateDate'"> order by PM_REPAIR_DETAIL.create_date <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='repairInfoCreateDate'"> order by PM_REPAIR_INFO.CREATE_DATE <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='rcvDetailCreateDate'"> order by PM_REPAIR_RCV_DETAIL.CREATE_DATE <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='repairRcvDate'"> order by PM_REPAIR_RCV_DETAIL.REPAIR_RCV_DATE <if test="order != null and order == 'desc'"> desc </if> </when>
      </choose>
    </if>
      <if test="startRow != null and endRow != null">
        limit #{endRow}::numeric-#{startRow}::numeric+1 offset case when ( #{startRow}::numeric-1 &lt; 0) then 0 else  #{startRow}::numeric-1 end
      </if>
  </select>

  <select id="getRelOnePageRecent" parameterType="com.zte.interfaces.dto.PmRepairInfoDTO" resultMap="RelOneResultMap">
    SELECT U.*
    FROM (
    SELECT
    <include refid="Rel_One_Column_List"/>
    , row_number() over (partition by PM_REPAIR_RCV_DETAIL.SN,PM_REPAIR_DETAIL.LOCATION_NO order by PM_REPAIR_DETAIL.CREATE_DATE DESC) as RANK
    FROM
    <include refid="Rel_One_Table_Name"/>
    WHERE
    <include refid="Rcv_Condtion"/>
    <include refid="RcvCondtions"/>
    <if test="sort != null">
      <choose>
        <when test="sort=='sn'"> order by PM_REPAIR_RCV_DETAIL.SN <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='lastUpdatedDate'"> order by PM_REPAIR_RCV.last_updated_date <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='lastUpdatedBy'"> order by PM_REPAIR_RCV.last_updated_by <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='createDate'"> order by PM_REPAIR_RCV.create_date <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='createBy'"> order by PM_REPAIR_RCV.create_by <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='repareCreateDate'"> order by PM_REPAIR_DETAIL.create_date <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='repairInfoCreateDate'"> order by PM_REPAIR_INFO.CREATE_DATE <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='rcvDetailCreateDate'"> order by PM_REPAIR_RCV_DETAIL.CREATE_DATE <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='repairRcvDate'"> order by PM_REPAIR_RCV_DETAIL.REPAIR_RCV_DATE <if test="order != null and order == 'desc'"> desc </if> </when>
      </choose>
    </if>
    ) U where U.RANK = 1
    <if test="startRow != null and endRow != null">
      limit #{endRow}::numeric-#{startRow}::numeric+1 offset case when ( #{startRow}::numeric-1 &lt; 0) then 0 else  #{startRow}::numeric-1 end
    </if>
  </select>


  <select id="getDetailPageRecent" parameterType="com.zte.interfaces.dto.PmRepairInfoDTO" resultMap="RelOneResultMap">
    SELECT U.*
    FROM (
    SELECT <include refid="Detail_Column_List" />
    , row_number() over (partition by PM_REPAIR_RCV_DETAIL.SN,PM_REPAIR_DETAIL.LOCATION_NO order by PM_REPAIR_DETAIL.CREATE_DATE DESC) as RANK
    FROM <include refid="Rel_One_Table_Name" />
    WHERE <include refid="Rcv_Condtion" />
    <include refid="RcvCondtions" />
    <if test="sort != null">
      <choose>
        <when test="sort=='sn'"> order by PM_REPAIR_RCV_DETAIL.SN <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='lastUpdatedDate'"> order by PM_REPAIR_RCV.last_updated_date <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='lastUpdatedBy'"> order by PM_REPAIR_RCV.last_updated_by <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='createDate'"> order by PM_REPAIR_RCV.create_date <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='createBy'"> order by PM_REPAIR_RCV.create_by <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='repareCreateDate'"> order by PM_REPAIR_DETAIL.create_date <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='repairInfoCreateDate'"> order by PM_REPAIR_INFO.CREATE_DATE <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='rcvDetailCreateDate'"> order by PM_REPAIR_RCV_DETAIL.CREATE_DATE <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='repairRcvDate'"> order by PM_REPAIR_RCV_DETAIL.REPAIR_RCV_DATE <if test="order != null and order == 'desc'"> desc </if> </when>
      </choose>
    </if>
    ) U where U.RANK = 1
    <if test="startRow != null and endRow != null">
      limit #{endRow}::numeric-#{startRow}::numeric+1 offset case when ( #{startRow}::numeric-1 &lt; 0) then 0 else  #{startRow}::numeric-1 end
    </if>
  </select>

  <!-- 二维：根据条码查询维修次数 -->
  <select id="getRelRepairCountBySn" parameterType="java.lang.String" resultType="java.lang.Long">
    SELECT COUNT(DISTINCT (PM_REPAIR_RCV.DELIVERY_NO))     FROM        PM_REPAIR_RCV LEFT JOIN PM_REPAIR_RCV_DETAIL ON
	PM_REPAIR_RCV.RECEPTION_ID=PM_REPAIR_RCV_DETAIL.RECEPTION_ID
	and PM_REPAIR_RCV.ENABLED_FLAG = 'Y' and PM_REPAIR_RCV_DETAIL.ENABLED_FLAG = 'Y'
	LEFT JOIN PM_REPAIR_INFO     ON PM_REPAIR_INFO.RECEPTION_ID = PM_REPAIR_RCV_DETAIL.RECEPTION_DETAIL_ID
	and PM_REPAIR_INFO.ENABLED_FLAG = 'Y'     LEFT JOIN PM_REPAIR_DETAIL     ON PM_REPAIR_INFO.REPAIR_ID=PM_REPAIR_DETAIL.REPAIR_ID
	and PM_REPAIR_DETAIL.ENABLED_FLAG = 'Y'     LEFT JOIN WIP_INFO     ON WIP_INFO.SN = PM_REPAIR_RCV_DETAIL.SN
	WHERE      PM_REPAIR_RCV_DETAIL.SN IS NOT NULL AND PM_REPAIR_RCV.BILL_TYPE='10'
    <if test="sn != null and sn != ''"> AND PM_REPAIR_RCV_DETAIL.SN = #{sn}</if>
  </select>

  <sql id="Rel_One_Condtion">
    (SELECT <include refid="Rel_One_Column_List"/>
    FROM <include refid="Rel_One_Table_Name"/>
    WHERE <include refid="Rcv_Condtion"/>
    <include refid="RcvCondtions"/>
    <include refid="RCV_ORDER_FILELD" />)T
  </sql>
  <!-- 根据送修时间、维修时间排序获取最新的记录集（二维） 20211126从按条码，送修单号分组排序改成按条码排序-->
  <select id="getRelOneListRecent" parameterType="com.zte.interfaces.dto.PmRepairInfoDTO" resultMap="RelOneResultMap">
    select S.*
    from (
    select T.*, row_number() over (partition by SN order by CREATE_DATE DESC) as RANK
    from <include refid="Rel_One_Condtion" />
    ) S
    where S.RANK = 1
  </select>
  <!-- 根据送修时间、维修时间排序获取最新的记录集条数（二维） -->
  <select id="getRelOneCountRecent" parameterType="com.zte.interfaces.dto.PmRepairInfoDTO" resultType="java.lang.Long">
    select COUNT(*)
    from (
    select T.*, row_number() over (partition by SN,DELIVERY_NO order by REPAIRCREATE,CREATE_DATE DESC) as RANK
    from <include refid="Rel_One_Condtion" />
    ) S
    where S.RANK = 1
  </select>

  <!-- 根据送修时间、维修时间排序获取最新的记录集条数（二维） 根据条码、位号-->
  <select id="getRelOneCountRecentTerminal" parameterType="com.zte.interfaces.dto.PmRepairInfoDTO" resultType="java.lang.Long">
    select COUNT(*)
    from (
    select T.*, row_number() over (partition by SN,LOCATION_NO order by CREATE_DATE DESC) as RANK
    from <include refid="Rel_One_Condtion" />
    ) S
    where S.RANK = 1
  </select>

  <sql id="Rel_Condition">
    (SELECT <include refid="Rel_Column_List"/>
    FROM <include refid="Rel_Table_Name"/>
    WHERE
    <include refid="Rel_Condtions"/>
    <include refid="ORDER_FILELD" />)T
  </sql>
  <!-- 根据维修时间排序获取最新的记录集(一维) -->
  <select id="getRelRecent" parameterType="com.zte.interfaces.dto.PmRepairInfoDTO" resultMap="RelOneResultMap">
    select S.*
    from (
    select T.*, row_number() over (partition by SN order by CREATE_DATE DESC) as RANK
    from <include refid="Rel_Condition" />
    ) S
    where S.RANK = 1
  </select>
  <!-- 根据维修时间排序获取最新的记录集条数（一维） -->
  <select id="getRelCountRecent" parameterType="com.zte.interfaces.dto.PmRepairInfoDTO" resultType="java.lang.Long">
    select COUNT(*)
    from (
    select T.*, row_number() over (partition by SN order by CREATE_DATE DESC) as RANK
    from <include refid="Rel_Condition" />
    ) S
    where S.RANK = 1
  </select>

  <!-- 批量写入 -->
   <insert id="insertPmRepairInfoBatch" parameterType="java.util.List" >
    insert into PM_REPAIR_INFO
    (
    REPAIR_ID, SN, ITEM_NAME, ITEM_CODE, STATUS,PRODPLAN_ID, WORK_ORDER_NO,
    REPAIR_RESULT,CRAFT_SECTION,WORK_STATION,NEXT_STATION,NEXT_PROCESS,
    REPAIR_PRODUCT_TYPE,REPAIR_PRODUCT_STYPE,REPAIR_PRODUCT_MSTYPE,IS_FIRST_PIECE,
    REPAIR_REMARK, CREATE_BY, CREATE_DATE, LAST_UPDATED_BY, LAST_UPDATED_DATE,
    ENABLED_FLAG,ORG_ID, FACTORY_ID, ENTITY_ID,RECEPTION_ID
    )
     select REPAIR_ID, SN, ITEM_NAME, ITEM_CODE, STATUS,PRODPLAN_ID, WORK_ORDER_NO,
    REPAIR_RESULT,CRAFT_SECTION,WORK_STATION,NEXT_STATION,NEXT_PROCESS,
    REPAIR_PRODUCT_TYPE,REPAIR_PRODUCT_STYPE,REPAIR_PRODUCT_MSTYPE,IS_FIRST_PIECE,
    REPAIR_REMARK, CREATE_BY, CREATE_DATE, LAST_UPDATED_BY, LAST_UPDATED_DATE,
    ENABLED_FLAG,ORG_ID, FACTORY_ID, ENTITY_ID,RECEPTION_ID
    from (
     <foreach collection ="list" item="item" index= "index" separator ="UNION ALL">
       SELECT
        #{item.repairId,jdbcType=VARCHAR} REPAIR_ID,
        #{item.sn,jdbcType=VARCHAR} SN,
        #{item.itemName,jdbcType=VARCHAR} ITEM_NAME,
        #{item.itemCode,jdbcType=VARCHAR} ITEM_CODE,
        #{item.status,jdbcType=DECIMAL} STATUS,
        #{item.prodplanId,jdbcType=VARCHAR} PRODPLAN_ID,
        #{item.workOrderNo,jdbcType=VARCHAR} WORK_ORDER_NO,
        #{item.repairResult,jdbcType=DECIMAL} REPAIR_RESULT,
        #{item.craftSection,jdbcType=VARCHAR} CRAFT_SECTION,
        #{item.workStation,jdbcType=VARCHAR} WORK_STATION,
        #{item.nextStation,jdbcType=VARCHAR} NEXT_STATION,
        #{item.nextProcess,jdbcType=VARCHAR} NEXT_PROCESS,
        #{item.repairProducetType,jdbcType=VARCHAR} REPAIR_PRODUCT_TYPE,
        #{item.repairProductStype,jdbcType=VARCHAR} REPAIR_PRODUCT_STYPE,
        #{item.repairProuctMstype,jdbcType=VARCHAR} REPAIR_PRODUCT_MSTYPE,
        #{item.isFirstPiece,jdbcType=VARCHAR} IS_FIRST_PIECE,
        #{item.repairRemark,jdbcType=VARCHAR} REPAIR_REMARK,
        #{item.createBy,jdbcType=VARCHAR} CREATE_BY,
        SYSDATE CREATE_DATE,
        #{item.lastUpdatedBy,jdbcType=VARCHAR} LAST_UPDATED_BY,
        SYSDATE LAST_UPDATED_DATE,
        'Y' ENABLED_FLAG,
        #{item.orgId,jdbcType=DECIMAL} ORG_ID,
        #{item.factoryId,jdbcType=DECIMAL} FACTORY_ID,
        #{item.entityId,jdbcType=DECIMAL} ENTITY_ID,
        #{item.receptionId,jdbcType=VARCHAR} RECEPTION_ID

    </foreach>
   ) a
  </insert>


  <update id="updatePmRepairInfoBatch" parameterType="java.util.List">
  	 <foreach collection="list" separator=";" item="item" >
        UPDATE PM_REPAIR_INFO F
        	<set>
	        	F.STATUS=20,
	        	F.LAST_UPDATED_DATE = SYSDATE
	        </set>
	        WHERE F.REPAIR_ID= #{item.repairId}
	        AND F.ENABLED_FLAG='Y'
	  </foreach>
  </update>

  <!--
      统计不良品数量
      统计每条线的不良品块数（维修录入表，线体，条码，最后更新时间统计）；汇总
      根据时间范围（8：00到次日8:00）
  <AUTHOR>
  -->
  <select id="collectErroCnt" parameterType="com.zte.interfaces.dto.PmRepairInfoDTO" resultType="java.lang.Long">
      select count(distinct T.SN)
      from PM_REPAIR_INFO T
      WHERE T.ENABLED_FLAG='Y'   AND
        <if test="prodplanIds != null">
          <foreach item="item" collection="prodplanIds" separator="," open="T.PRODPLAN_ID IN (" close=") AND" >
            #{item,jdbcType=VARCHAR}
          </foreach>
        </if>

        <![CDATA[ T.LAST_UPDATED_DATE  >= to_timestamp(#{startTime},'yyyy/mm/dd hh24:mi:ss')  ]]> AND
        <![CDATA[ T.LAST_UPDATED_DATE  <= to_timestamp(#{endTime},'yyyy/mm/dd hh24:mi:ss')  ]]>
  </select>

   <!--
      不良品分析
      统计每条线的不良品块数（维修录入表，线体，不良原因，最后更新时间统计）；
      不良原因对应的不良品块数/不良品总块数；
      根据时间范围（8：00到次日8:00）
  <AUTHOR>
  -->
  <select id="collectErroCntByProdplanIds" parameterType="com.zte.interfaces.dto.PmRepairInfoDTO" resultMap="CollecMap">
      SELECT * FROM (
		SELECT T.REPAIR_PRODUCT_STYPE,COUNT(distinct T.SN) CNT, TRUNC(COUNT(distinct T.SN)/${errorTotal},2) PER
		 FROM  PM_REPAIR_INFO T
		 WHERE T.ENABLED_FLAG='Y'   AND
        	<if test="prodplanIds != null">
          		<foreach item="item" collection="prodplanIds" separator="," open="T.PRODPLAN_ID IN (" close=") AND" >
            		#{item,jdbcType=VARCHAR}
          		</foreach>
        	</if>
        	<![CDATA[ T.LAST_UPDATED_DATE  >= to_timestamp(#{startTime},'yyyy/mm/dd hh24:mi:ss')  ]]> AND
        	<![CDATA[ T.LAST_UPDATED_DATE  <= to_timestamp(#{endTime},'yyyy/mm/dd hh24:mi:ss')  ]]>
		GROUP BY T.REPAIR_PRODUCT_STYPE
		ORDER BY CNT DESC)
	WHERE <![CDATA[ ROWNUM<=#{rows} ]]>
  </select>

    <!-- 维修积压库存总记录数 -->
    <select id="countTotalRecord" parameterType="com.zte.interfaces.dto.PmRepairInfoDTO" resultType="java.lang.Long">
        select
        count(*)
        from PM_REPAIR_RCV,
        (
        SELECT
        t.STATUS AS REPAIRSTATUS,
        t.Create_Date AS REPAIRCREATE,
        t.LAST_UPDATED_DATE AS REPAIRUPDATE,
        t.REPAIR_RCV_DATE,
        t.CRAFT_SECTION AS RCV_CRAFT_SECTION,
        t.RCV_PRODPLAN_ID,
        t.SEND_FLAG,
        t.RCV_FLAG,
        t.ITEM_NAME,
        t.ITEM_CODE,
        t.ERROR_CODE,
        t.ERROR_DESCRIPTION,
        t.RCV_PRODPLAN_ID AS PRODPLAN_ID,
        t.SCRAP_REASON,
        t.SCAN_TIME,
        t.STATUS,t.RECEPTION_DETAIL_ID,t.SN,t.RECEPTION_ID,
        t.PCB_VERSION,
        t.RECEIVING_TIME,
        t.RETURNED_DATE,
        t.PRODUCT_CLASS,
        t.PRODUCT_SMLCLASS,
        pd.location_no,
        <!--
            维修库存积压查询：
                返还时间 - 接收时间 = date
                返还时间=null(取系统当前时间) - 接收时间 = date
                返还时间(null) - 接收时间(null) = null;
        -->
        <choose>
            <when test="overStockNewOrOld !=null and overStockNewOrOld != ''">
                CASE
                WHEN pd.REPAIR_DATE is null THEN trunc((extract (epoch from (sysdate - t.CREATE_DATE))/60/60/24)::numeric)
                ELSE trunc((extract (epoch from (pd.REPAIR_DATE - t.CREATE_DATE))/60/60/24)::numeric,3) END repairCycle
            </when>
            <otherwise>
                CASE
                WHEN t.REPAIR_RCV_DATE is null THEN trunc((extract (epoch from (sysdate - t.RECEIVING_TIME))/60/60/24)::numeric)
                ELSE trunc((extract (epoch from (t.REPAIR_RCV_DATE - t.RECEIVING_TIME))/60/60/24)::numeric,3) END repairCycle
            </otherwise>
        </choose>
        FROM
        PM_REPAIR_RCV_DETAIL t
        LEFT JOIN PM_REPAIR_INFO pri
        ON pri.RECEPTION_ID = t.RECEPTION_DETAIL_ID
        LEFT JOIN PM_REPAIR_DETAIL pd
        ON pri.REPAIR_ID=pd.REPAIR_ID
        where t.SN IS NOT NULL
        and t.STATUS IN ('10560001','10560002')
        GROUP BY
        t.STATUS,
        t.Create_Date,
        t.LAST_UPDATED_DATE,
        t.REPAIR_RCV_DATE,
        t.CRAFT_SECTION,
        t.RCV_PRODPLAN_ID,
        t.SEND_FLAG,
        t.RCV_FLAG,
        t.SN,
        t.ITEM_NAME,
        t.ITEM_CODE,
        t.ERROR_CODE,
        t.ERROR_DESCRIPTION,
        t.RCV_PRODPLAN_ID,
        t.SCRAP_REASON,
        t.SCAN_TIME,
        t.STATUS,t.RECEPTION_ID,t.repair_rcv_date,t.RECEIVING_TIME,t.RECEPTION_DETAIL_ID,pd.REPAIR_DATE,
        t.PCB_VERSION,
        t.RECEIVING_TIME,
        t.RETURNED_DATE,
        t.PRODUCT_CLASS,
        t.PRODUCT_SMLCLASS,
        pd.location_no
        <!-- ORDER BY t.RECEPTION_ID -->
        ) PM_REPAIR_RCV_DETAIL
        LEFT JOIN PM_REPAIR_INFO
        ON PM_REPAIR_INFO.RECEPTION_ID = PM_REPAIR_RCV_DETAIL.RECEPTION_DETAIL_ID
        LEFT JOIN WIP_INFO
        ON WIP_INFO.SN = PM_REPAIR_RCV_DETAIL.SN
        where
        PM_REPAIR_RCV.RECEPTION_ID = PM_REPAIR_RCV_DETAIL.RECEPTION_ID
        AND PM_REPAIR_RCV.BILL_TYPE='10'

        <!-- 条件 -->
        <if test="fromStation != null and fromStation != ''"> AND PM_REPAIR_RCV.FROM_STATION = #{fromStation}</if>
        <if test="repairStatus != null and repairStatus != ''"> AND PM_REPAIR_RCV_DETAIL.STATUS = #{repairStatus}</if>
        <if test="building != null and building != ''"> AND PM_REPAIR_RCV.BUILDING = #{building}</if>
        <if test="applicationSection != null and applicationSection != ''"> AND PM_REPAIR_RCV.APPLICATION_SECTION =
        #{applicationSection}</if>
        <if test="prodplanId != null and prodplanId != ''"> AND PM_REPAIR_RCV_DETAIL.RCV_PRODPLAN_ID = #{prodplanId}</if>
        <if test="sn != null and sn != ''"> AND PM_REPAIR_RCV_DETAIL.SN = #{sn}</if>
        <if test="itemName != null and itemName != ''"> AND PM_REPAIR_RCV_DETAIL.ITEM_NAME like concat(#{itemName},'%')</if>
        <if test="itemCode != null and itemCode != ''"> AND PM_REPAIR_RCV_DETAIL.ITEM_CODE like concat(#{itemCode},'%')</if>
        <if test="status != null and status != ''"> AND <include refid="Table_Name" />.STATUS = #{status}</if>
        <if test="prodplanId != null and prodplanId != ''"> AND PM_REPAIR_RCV_DETAIL.RCV_PRODPLAN_ID like concat(#{prodplanId},'%')</if>
        <if test="workOrderNo != null and workOrderNo != ''"> AND <include refid="Table_Name" />.WORK_ORDER_NO like concat(#{workOrderNo},'%')</if>
        <if test="repairResult != null and repairResult != ''"> AND <include refid="Table_Name" />.REPAIR_RESULT = #{repairResult}</if>
        <if test="craftSection != null and craftSection != ''"> AND <include refid="Table_Name" />.CRAFT_SECTION like concat(#{craftSection},'%')</if>
        <if test="nextStation != null and nextStation != ''"> AND <include refid="Table_Name" />.NEXT_STATION like concat(#{nextStation},'%')</if>
        <if test="nextProcess != null and nextProcess != ''"> AND <include refid="Table_Name" />.NEXT_PROCESS like concat(#{nextProcess},'%')</if>
        <if test="repairProducetType != null and repairProducetType != ''"> AND <include refid="Table_Name" />.REPAIR_PRODUCT_TYPE like concat(#{repairProducetType},'%')</if>
        <if test="repairRemark != null and repairRemark != ''"> AND <include refid="Table_Name" />.REPAIR_REMARK like concat(#{repairRemark},'%')</if>
        <if test="repairProductStype != null and repairProductStype != ''"> AND <include refid="Table_Name" />.REPAIR_PRODUCT_STYPE like concat(#{repairProductStype},'%')</if>
        <if test="repairProuctMstype != null and repairProuctMstype != ''"> AND <include refid="Table_Name" />.REPAIR_PRODUCT_MSTYPE like concat(#{repairProuctMstype},'%')</if>
        <if test="isFirstPiece != null and isFirstPiece != ''"> AND <include refid="Table_Name" />.IS_FIRST_PIECE = #{isFirstPiece}</if>
        <if test="receptionId != null and receptionId != ''"> AND <include refid="Table_Name" />.RECEPTION_ID = #{receptionId}</if>
        <if test="orgId != null"> AND PM_REPAIR_RCV.ORG_ID = cast(#{orgId} as numeric)</if>
        <if test="factoryId != null"> AND PM_REPAIR_RCV.FACTORY_ID = cast(#{factoryId} as numeric)</if>
        <if test="entityId != null"> AND PM_REPAIR_RCV.ENTITY_ID = cast(#{entityId} as numeric)</if>
        <if test="lastUpdatedBy != null and lastUpdatedBy !=''"> AND <include refid="Table_Name" />.LAST_UPDATED_BY = #{lastUpdatedBy}</if>
        <if test="startTime !=null and startTime != ''"> AND <include refid="Table_Name" />.CREATE_DATE &gt;= to_timestamp(#{startTime},'yyyy-mm-dd hh24:mi:ss') </if>
        <if test="endTime !=null and endTime != ''"> AND <include refid="Table_Name" />.CREATE_DATE &lt;= to_timestamp(#{endTime},'yyyy-mm-dd hh24:mi:ss') </if>
        <if test="creatStartTime !=null and creatStartTime != ''"> AND <include refid="Table_Name" />.CREATE_DATE &gt;= to_timestamp(#{creatStartTime},'yyyy-mm-dd hh24:mi:ss') </if>
        <if test="creatEndTime !=null and creatEndTime != ''"> AND <include refid="Table_Name" />.CREATE_DATE &lt;= to_timestamp(#{creatEndTime},'yyyy-mm-dd hh24:mi:ss') </if>
        <if test="sendStartTime !=null and sendStartTime != ''"> AND PM_REPAIR_RCV_DETAIL.REPAIRCREATE &gt;= to_timestamp(#{sendStartTime},'yyyy-mm-dd hh24:mi:ss') </if>
        <if test="sendEndTime !=null and sendEndTime != ''"> AND PM_REPAIR_RCV_DETAIL.REPAIRCREATE &lt;= to_timestamp(#{sendEndTime},'yyyy-mm-dd hh24:mi:ss') </if>
        <if test="startRepairRcvDate !=null and startRepairRcvDate != ''"> AND PM_REPAIR_RCV_DETAIL.REPAIR_RCV_DATE &gt;= to_timestamp(#{startRepairRcvDate},'yyyy-mm-dd hh24:mi:ss') </if>
        <if test="endRepairRcvDate !=null and endRepairRcvDate != ''"> AND PM_REPAIR_RCV_DETAIL.REPAIR_RCV_DATE &lt;= to_timestamp(#{endRepairRcvDate},'yyyy-mm-dd hh24:mi:ss') </if>
        <if test="sendFlag != null and sendFlag != ''"> AND PM_REPAIR_RCV_DETAIL.SEND_FLAG = #{sendFlag}</if>
        <if test="rcvFlag != null and rcvFlag != ''"> AND PM_REPAIR_RCV_DETAIL.RCV_FLAG = #{rcvFlag}</if>
        <if test="isAccept != null and isAccept != ''"> AND PM_REPAIR_RCV_DETAIL.IS_ACCEPT = cast(#{isAccept} as numeric)</if>
        <if test="deliveryNo != null and deliveryNo != ''"> AND PM_REPAIR_RCV.DELIVERY_NO = #{deliveryNo}</if>
        <if test="locationNo != null and locationNo != ''"> AND PM_REPAIR_RCV_DETAIL.LOCATION_NO = #{locationNo}</if>
    </select>

    <!-- 分页获取积压库存列表 -->
    <select id="listRepairStockPage" parameterType="com.zte.interfaces.dto.PmRepairInfoDTO" resultMap="RelOneResultMap">
        <!-- 维修库存积压（头表）-->
        select
        PM_REPAIR_RCV_DETAIL.repairCycle,
        <!-- PM_REPAIR_RCV.*, -->
        <!-- PM_REPAIR_INFO -->
        PM_REPAIR_INFO.CRAFT_SECTION,
        PM_REPAIR_INFO.STATUS,
        PM_REPAIR_INFO.REPAIR_ID,
        PM_REPAIR_INFO.WORK_STATION,
        PM_REPAIR_INFO.REPAIR_PRODUCT_TYPE,
        PM_REPAIR_INFO.REPAIR_PRODUCT_STYPE,
        PM_REPAIR_INFO.REPAIR_PRODUCT_MSTYPE,
        PM_REPAIR_INFO.RECEPTION_ID,
        PM_REPAIR_INFO.WORK_ORDER_NO,
        PM_REPAIR_INFO.REPAIR_REMARK,
        PM_REPAIR_INFO.CREATE_DATE,
        PM_REPAIR_INFO.LAST_UPDATED_BY,
        PM_REPAIR_INFO.CRAFT_SECTION as WIP_CRAFT_SECTION,
        WIP_INFO.LINE_CODE,
        WIP_INFO.ITEM_NO,
        WIP_INFO.ATTRIBUTE1,
        WIP_INFO.ATTRIBUTE2,

        <!-- PM_REPAIR_RCV -->
        PM_REPAIR_RCV.CREATE_BY,
        PM_REPAIR_RCV.DELIVERY_BY,
        PM_REPAIR_RCV.LAST_UPDATED_DATE,
        PM_REPAIR_RCV.ENABLED_FLAG,
        PM_REPAIR_RCV.ORG_ID,
        PM_REPAIR_RCV.FACTORY_ID,
        PM_REPAIR_RCV.ENTITY_ID,
        PM_REPAIR_RCV.FROM_STATION,
        PM_REPAIR_RCV.WAREHOUSE_CODE,
        PM_REPAIR_RCV.BUILDING,
        PM_REPAIR_RCV.DELIVERY_NO,
        PM_REPAIR_RCV.APPLICATION_SECTION,

        <!-- PM_REPAIR_RCV_DETAIL -->
        PM_REPAIR_RCV_DETAIL.RECEPTION_BY,
        PM_REPAIR_RCV_DETAIL.STATUS AS REPAIRSTATUS,
        PM_REPAIR_RCV_DETAIL.REPAIRCREATE,
        PM_REPAIR_RCV_DETAIL.REPAIRUPDATE,
        PM_REPAIR_RCV_DETAIL.REPAIR_RCV_DATE,
        PM_REPAIR_RCV_DETAIL.RCV_CRAFT_SECTION,
        PM_REPAIR_RCV_DETAIL.RCV_PRODPLAN_ID,
        PM_REPAIR_RCV_DETAIL.SEND_FLAG,
        PM_REPAIR_RCV_DETAIL.RCV_FLAG,
        PM_REPAIR_RCV_DETAIL.SN,
        PM_REPAIR_RCV_DETAIL.ITEM_NAME,
        PM_REPAIR_RCV_DETAIL.ITEM_CODE,
        PM_REPAIR_RCV_DETAIL.ERROR_CODE,
        PM_REPAIR_RCV_DETAIL.ERROR_DESCRIPTION,
        PM_REPAIR_RCV_DETAIL.RCV_PRODPLAN_ID AS PRODPLAN_ID,
        PM_REPAIR_RCV_DETAIL.SCRAP_REASON,
        PM_REPAIR_RCV_DETAIL.SCAN_TIME,
        PM_REPAIR_RCV_DETAIL.PCB_VERSION,
        to_char(PM_REPAIR_RCV_DETAIL.RECEIVING_TIME,'yyyy-MM-dd hh24:mi:ss') as RECEIVING_TIME,
       	PM_REPAIR_RCV_DETAIL.RETURNED_DATE,
       	PM_REPAIR_RCV_DETAIL.PRODUCT_CLASS,
       	PM_REPAIR_RCV_DETAIL.PRODUCT_SMLCLASS,
       	PM_REPAIR_RCV_DETAIL.LOCATION_NO,
        PM_REPAIR_RCV_DETAIL.REPAIR_PROCESS
        from PM_REPAIR_RCV,
        (
        SELECT
        t.STATUS AS REPAIRSTATUS,
        t.Create_Date AS REPAIRCREATE,
        t.LAST_UPDATED_DATE AS REPAIRUPDATE,
        t.REPAIR_RCV_DATE,
        t.CRAFT_SECTION AS RCV_CRAFT_SECTION,
        t.RCV_PRODPLAN_ID,
        t.SEND_FLAG,
        t.RCV_FLAG,
        t.ITEM_NAME,
        t.ITEM_CODE,
        t.ERROR_CODE,
        t.ERROR_DESCRIPTION,
        t.RCV_PRODPLAN_ID AS PRODPLAN_ID,
        t.SCRAP_REASON,
        t.SCAN_TIME,
        t.STATUS,t.RECEPTION_DETAIL_ID,t.SN,t.RECEPTION_ID,
        t.PCB_VERSION,
        t.RECEIVING_TIME,
        t.RETURNED_DATE,
        t.PRODUCT_CLASS,
        t.PRODUCT_SMLCLASS,
        t.RECEPTION_BY,
        pd.location_no,
         pd.REPAIR_PROCESS,
        <!--
            维修库存积压查询：
                返还时间 - 接收时间 = date
                返还时间=null(取系统当前时间) - 接收时间 = date
                返还时间(null) - 接收时间(null) = null;
        -->
        <choose>
            <when test="overStockNewOrOld !=null and overStockNewOrOld != ''">
                CASE
                WHEN pd.REPAIR_DATE is null THEN trunc((extract (epoch from (sysdate - t.CREATE_DATE))/60/60/24)::numeric)
                ELSE trunc((extract (epoch from (pd.REPAIR_DATE - t.CREATE_DATE))/60/60/24)::numeric,3) END repairCycle
            </when>
            <otherwise>
                CASE
                WHEN t.REPAIR_RCV_DATE is null THEN trunc((extract (epoch from (sysdate - t.RECEIVING_TIME))/60/60/24)::numeric)
                ELSE trunc((extract (epoch from (t.REPAIR_RCV_DATE - t.RECEIVING_TIME))/60/60/24)::numeric,3) END repairCycle
            </otherwise>
        </choose>
        FROM
        PM_REPAIR_RCV_DETAIL t
        LEFT JOIN PM_REPAIR_INFO pri
        ON pri.RECEPTION_ID = t.RECEPTION_DETAIL_ID
        LEFT JOIN PM_REPAIR_DETAIL pd
        ON pri.REPAIR_ID=pd.REPAIR_ID
        where t.SN IS NOT NULL
        and t.STATUS IN ('10560001','10560002')
        GROUP BY
        t.STATUS,
        t.Create_Date,
        t.LAST_UPDATED_DATE,
        t.REPAIR_RCV_DATE,
        t.CRAFT_SECTION,
        t.RCV_PRODPLAN_ID,
        t.SEND_FLAG,
        t.RCV_FLAG,
        t.SN,
        t.ITEM_NAME,
        t.ITEM_CODE,
        t.ERROR_CODE,
        t.ERROR_DESCRIPTION,
        t.RCV_PRODPLAN_ID,
        t.SCRAP_REASON,
        t.SCAN_TIME,
        t.STATUS,t.RECEPTION_ID,t.repair_rcv_date,t.RECEIVING_TIME,t.RECEPTION_DETAIL_ID,pd.REPAIR_DATE,
        t.PCB_VERSION,
        t.RECEIVING_TIME,
        t.RETURNED_DATE,
        t.PRODUCT_CLASS,
        t.PRODUCT_SMLCLASS,
        pd.location_no,
        pd.REPAIR_PROCESS
        <!-- ORDER BY t.RECEPTION_ID -->
        ) PM_REPAIR_RCV_DETAIL
        LEFT JOIN PM_REPAIR_INFO
        ON PM_REPAIR_INFO.RECEPTION_ID = PM_REPAIR_RCV_DETAIL.RECEPTION_DETAIL_ID
        LEFT JOIN WIP_INFO
        ON WIP_INFO.SN = PM_REPAIR_RCV_DETAIL.SN
        where
        PM_REPAIR_RCV.RECEPTION_ID = PM_REPAIR_RCV_DETAIL.RECEPTION_ID
        AND PM_REPAIR_RCV.BILL_TYPE='10'

        <!-- 条件 -->
        <if test="fromStation != null and fromStation != ''"> AND PM_REPAIR_RCV.FROM_STATION = #{fromStation}</if>
        <if test="repairStatus != null and repairStatus != ''"> AND PM_REPAIR_RCV_DETAIL.STATUS = #{repairStatus}</if>
        <if test="building != null and building != ''"> AND PM_REPAIR_RCV.BUILDING = #{building}</if>
        <if test="applicationSection != null and applicationSection != ''"> AND PM_REPAIR_RCV.APPLICATION_SECTION =
          #{applicationSection}</if>
        <if test="prodplanId != null and prodplanId != ''"> AND PM_REPAIR_RCV_DETAIL.RCV_PRODPLAN_ID = #{prodplanId}</if>
        <if test="sn != null and sn != ''"> AND PM_REPAIR_RCV_DETAIL.SN = #{sn}</if>
        <if test="itemName != null and itemName != ''"> AND PM_REPAIR_RCV_DETAIL.ITEM_NAME like concat(#{itemName},'%')</if>
        <if test="itemCode != null and itemCode != ''"> AND PM_REPAIR_RCV_DETAIL.ITEM_CODE like concat(#{itemCode},'%')</if>
        <if test="status != null and status != ''"> AND <include refid="Table_Name" />.STATUS = #{status}</if>
        <if test="prodplanId != null and prodplanId != ''"> AND PM_REPAIR_RCV_DETAIL.RCV_PRODPLAN_ID like concat(#{prodplanId},'%')</if>
        <if test="workOrderNo != null and workOrderNo != ''"> AND <include refid="Table_Name" />.WORK_ORDER_NO like concat(#{workOrderNo},'%')</if>
        <if test="repairResult != null and repairResult != ''"> AND <include refid="Table_Name" />.REPAIR_RESULT = #{repairResult}</if>
        <if test="craftSection != null and craftSection != ''"> AND <include refid="Table_Name" />.CRAFT_SECTION like concat(#{craftSection},'%')</if>
        <if test="nextStation != null and nextStation != ''"> AND <include refid="Table_Name" />.NEXT_STATION like concat(#{nextStation},'%')</if>
        <if test="nextProcess != null and nextProcess != ''"> AND <include refid="Table_Name" />.NEXT_PROCESS like concat(#{nextProcess},'%')</if>
        <if test="repairProducetType != null and repairProducetType != ''"> AND <include refid="Table_Name" />.REPAIR_PRODUCT_TYPE like concat(#{repairProducetType},'%')</if>
        <if test="repairRemark != null and repairRemark != ''"> AND <include refid="Table_Name" />.REPAIR_REMARK like concat(#{repairRemark},'%')</if>
        <if test="repairProductStype != null and repairProductStype != ''"> AND <include refid="Table_Name" />.REPAIR_PRODUCT_STYPE like concat(#{repairProductStype},'%')</if>
        <if test="repairProuctMstype != null and repairProuctMstype != ''"> AND <include refid="Table_Name" />.REPAIR_PRODUCT_MSTYPE like concat(#{repairProuctMstype},'%')</if>
        <if test="isFirstPiece != null and isFirstPiece != ''"> AND <include refid="Table_Name" />.IS_FIRST_PIECE = #{isFirstPiece}</if>
        <if test="receptionId != null and receptionId != ''"> AND <include refid="Table_Name" />.RECEPTION_ID = #{receptionId}</if>
        <if test="orgId != null"> AND PM_REPAIR_RCV.ORG_ID = cast(#{orgId} as numeric)</if>
        <if test="factoryId != null"> AND PM_REPAIR_RCV.FACTORY_ID = cast(#{factoryId} as numeric)</if>
        <if test="entityId != null"> AND PM_REPAIR_RCV.ENTITY_ID = cast(#{entityId} as numeric)</if>
        <if test="lastUpdatedBy != null and lastUpdatedBy !=''"> AND <include refid="Table_Name" />.LAST_UPDATED_BY = #{lastUpdatedBy}</if>
        <if test="startTime !=null and startTime != ''"> AND <include refid="Table_Name" />.CREATE_DATE &gt;= to_timestamp(#{startTime},'yyyy-mm-dd hh24:mi:ss') </if>
        <if test="endTime !=null and endTime != ''"> AND <include refid="Table_Name" />.CREATE_DATE &lt;= to_timestamp(#{endTime},'yyyy-mm-dd hh24:mi:ss') </if>
        <if test="creatStartTime !=null and creatStartTime != ''"> AND <include refid="Table_Name" />.CREATE_DATE &gt;= to_timestamp(#{creatStartTime},'yyyy-mm-dd hh24:mi:ss') </if>
        <if test="creatEndTime !=null and creatEndTime != ''"> AND <include refid="Table_Name" />.CREATE_DATE &lt;= to_timestamp(#{creatEndTime},'yyyy-mm-dd hh24:mi:ss') </if>
        <if test="sendStartTime !=null and sendStartTime != ''"> AND PM_REPAIR_RCV_DETAIL.REPAIRCREATE &gt;= to_timestamp(#{sendStartTime},'yyyy-mm-dd hh24:mi:ss') </if>
        <if test="sendEndTime !=null and sendEndTime != ''"> AND PM_REPAIR_RCV_DETAIL.REPAIRCREATE &lt;= to_timestamp(#{sendEndTime},'yyyy-mm-dd hh24:mi:ss') </if>
        <if test="startRepairRcvDate !=null and startRepairRcvDate != ''"> AND PM_REPAIR_RCV_DETAIL.REPAIR_RCV_DATE &gt;= to_timestamp(#{startRepairRcvDate},'yyyy-mm-dd hh24:mi:ss') </if>
        <if test="endRepairRcvDate !=null and endRepairRcvDate != ''"> AND PM_REPAIR_RCV_DETAIL.REPAIR_RCV_DATE &lt;= to_timestamp(#{endRepairRcvDate},'yyyy-mm-dd hh24:mi:ss') </if>
        <if test="sendFlag != null and sendFlag != ''"> AND PM_REPAIR_RCV_DETAIL.SEND_FLAG = #{sendFlag}</if>
        <if test="rcvFlag != null and rcvFlag != ''"> AND PM_REPAIR_RCV_DETAIL.RCV_FLAG = #{rcvFlag}</if>
        <if test="isAccept != null and isAccept != ''"> AND PM_REPAIR_RCV_DETAIL.IS_ACCEPT = cast(#{isAccept} as numeric)</if>
        <if test="deliveryNo != null and deliveryNo != ''"> AND PM_REPAIR_RCV.DELIVERY_NO = #{deliveryNo}</if>
        <if test="locationNo != null and locationNo != ''"> AND PM_REPAIR_RCV_DETAIL.LOCATION_NO = #{locationNo}</if>
        <include refid="RCV_ORDER_FILELD" />
        <if test="startRow != null and endRow != null">
          limit #{endRow}::numeric-#{startRow}::numeric+1 offset case when ( #{startRow}::numeric-1 &lt; 0) then 0 else  #{startRow}::numeric-1 end
        </if>
    </select>

    <!-- 导出符合条件的信息 -->
    <select id="listAllRepairStock" parameterType="com.zte.interfaces.dto.PmRepairInfoDTO" resultMap="RelOneResultMap">
        <!-- 维修库存积压（头表）-->
        select
        PM_REPAIR_RCV_DETAIL.repairCycle,
        <!-- PM_REPAIR_RCV.*, -->
        <!-- PM_REPAIR_INFO -->
        PM_REPAIR_INFO.CRAFT_SECTION,
        PM_REPAIR_INFO.STATUS,
        PM_REPAIR_INFO.REPAIR_ID,
        PM_REPAIR_INFO.WORK_STATION,
        PM_REPAIR_INFO.REPAIR_PRODUCT_TYPE,
        PM_REPAIR_INFO.REPAIR_PRODUCT_STYPE,
        PM_REPAIR_INFO.REPAIR_PRODUCT_MSTYPE,
        PM_REPAIR_INFO.RECEPTION_ID,
        PM_REPAIR_INFO.WORK_ORDER_NO,
        PM_REPAIR_INFO.REPAIR_REMARK,
        PM_REPAIR_INFO.CREATE_DATE,
        PM_REPAIR_INFO.LAST_UPDATED_BY,
        PM_REPAIR_INFO.CRAFT_SECTION as WIP_CRAFT_SECTION,
        WIP_INFO.LINE_CODE,
        WIP_INFO.ITEM_NO,
        WIP_INFO.ATTRIBUTE1,
        WIP_INFO.ATTRIBUTE2,

        <!-- PM_REPAIR_RCV -->
        PM_REPAIR_RCV.CREATE_BY,
        PM_REPAIR_RCV.DELIVERY_BY,
        PM_REPAIR_RCV.LAST_UPDATED_DATE,
        PM_REPAIR_RCV.ENABLED_FLAG,
        PM_REPAIR_RCV.ORG_ID,
        PM_REPAIR_RCV.FACTORY_ID,
        PM_REPAIR_RCV.ENTITY_ID,
        PM_REPAIR_RCV.FROM_STATION,
        PM_REPAIR_RCV.WAREHOUSE_CODE,
        PM_REPAIR_RCV.BUILDING,
        PM_REPAIR_RCV.DELIVERY_NO,
        PM_REPAIR_RCV.APPLICATION_SECTION,

        <!-- PM_REPAIR_RCV_DETAIL -->
        PM_REPAIR_RCV_DETAIL.RECEPTION_BY,
        PM_REPAIR_RCV_DETAIL.STATUS AS REPAIRSTATUS,
        PM_REPAIR_RCV_DETAIL.REPAIRCREATE,
        PM_REPAIR_RCV_DETAIL.REPAIRUPDATE,
        PM_REPAIR_RCV_DETAIL.REPAIR_RCV_DATE,
        PM_REPAIR_RCV_DETAIL.RCV_CRAFT_SECTION,
        PM_REPAIR_RCV_DETAIL.RCV_PRODPLAN_ID,
        PM_REPAIR_RCV_DETAIL.SEND_FLAG,
        PM_REPAIR_RCV_DETAIL.RCV_FLAG,
        PM_REPAIR_RCV_DETAIL.SN,
        PM_REPAIR_RCV_DETAIL.ITEM_NAME,
        PM_REPAIR_RCV_DETAIL.ITEM_CODE,
        PM_REPAIR_RCV_DETAIL.ERROR_CODE,
        PM_REPAIR_RCV_DETAIL.ERROR_DESCRIPTION,
        PM_REPAIR_RCV_DETAIL.RCV_PRODPLAN_ID AS PRODPLAN_ID,
        PM_REPAIR_RCV_DETAIL.SCRAP_REASON,
        PM_REPAIR_RCV_DETAIL.SCAN_TIME,
        PM_REPAIR_RCV_DETAIL.PCB_VERSION,
        to_char(PM_REPAIR_RCV_DETAIL.RECEIVING_TIME,'yyyy-MM-dd hh24:mi:ss') as RECEIVING_TIME,
       	PM_REPAIR_RCV_DETAIL.RETURNED_DATE,
       	PM_REPAIR_RCV_DETAIL.PRODUCT_CLASS,
       	PM_REPAIR_RCV_DETAIL.PRODUCT_SMLCLASS,
       	PM_REPAIR_RCV_DETAIL.LOCATION_NO
        from PM_REPAIR_RCV,
        (
        SELECT
        t.STATUS AS REPAIRSTATUS,
        t.Create_Date AS REPAIRCREATE,
        t.LAST_UPDATED_DATE AS REPAIRUPDATE,
        t.REPAIR_RCV_DATE,
        t.CRAFT_SECTION AS RCV_CRAFT_SECTION,
        t.RCV_PRODPLAN_ID,
        t.SEND_FLAG,
        t.RCV_FLAG,
        t.ITEM_NAME,
        t.ITEM_CODE,
        t.ERROR_CODE,
        t.ERROR_DESCRIPTION,
        t.RCV_PRODPLAN_ID AS PRODPLAN_ID,
        t.SCRAP_REASON,
        t.SCAN_TIME,
        t.STATUS,t.RECEPTION_DETAIL_ID,t.SN,t.RECEPTION_ID,
        t.PCB_VERSION,
        t.RECEIVING_TIME,
        t.RETURNED_DATE,
        t.PRODUCT_CLASS,
        t.PRODUCT_SMLCLASS,
        t.RECEPTION_BY,
        pd.location_no,
        <!--
         维修库存积压查询：
            返还时间 - 接收时间 = date
            返还时间=null(取系统当前时间) - 接收时间 = date
            返还时间(null) - 接收时间(null) = null;
        -->
        <choose>
            <when test="overStockNewOrOld !=null and overStockNewOrOld != ''">
                CASE
                WHEN pd.REPAIR_DATE is null THEN trunc((extract (epoch from (sysdate - t.CREATE_DATE))/60/60/24)::numeric)
                ELSE trunc((extract (epoch from (pd.REPAIR_DATE - t.CREATE_DATE))/60/60/24)::numeric,3) END repairCycle
            </when>
            <otherwise>
                CASE
                WHEN t.REPAIR_RCV_DATE is null THEN trunc((extract (epoch from (sysdate - t.RECEIVING_TIME))/60/60/24)::numeric)
                ELSE trunc((extract (epoch from (t.REPAIR_RCV_DATE - t.RECEIVING_TIME))/60/60/24)::numeric,3) END repairCycle
            </otherwise>
        </choose>

        FROM
        PM_REPAIR_RCV_DETAIL t
        LEFT JOIN PM_REPAIR_INFO pri
        ON pri.RECEPTION_ID = t.RECEPTION_DETAIL_ID
        LEFT JOIN PM_REPAIR_DETAIL pd
        ON pri.REPAIR_ID=pd.REPAIR_ID
        where t.SN IS NOT NULL
        and t.STATUS IN ('10560001','10560002')
        GROUP BY
        t.STATUS,
        t.Create_Date,
        t.LAST_UPDATED_DATE,
        t.REPAIR_RCV_DATE,
        t.CRAFT_SECTION,
        t.RCV_PRODPLAN_ID,
        t.SEND_FLAG,
        t.RCV_FLAG,
        t.SN,
        t.ITEM_NAME,
        t.ITEM_CODE,
        t.ERROR_CODE,
        t.ERROR_DESCRIPTION,
        t.RCV_PRODPLAN_ID,
        t.SCRAP_REASON,
        t.SCAN_TIME,
        t.STATUS,t.RECEPTION_ID,t.repair_rcv_date,t.RECEIVING_TIME,t.RECEPTION_DETAIL_ID,pd.REPAIR_DATE,
        t.PCB_VERSION,
        t.RECEIVING_TIME,
        t.RETURNED_DATE,
        t.PRODUCT_CLASS,
        t.PRODUCT_SMLCLASS,
        pd.location_no
        <!-- ORDER BY t.RECEPTION_ID -->
        ) PM_REPAIR_RCV_DETAIL
        LEFT JOIN PM_REPAIR_INFO
        ON PM_REPAIR_INFO.RECEPTION_ID = PM_REPAIR_RCV_DETAIL.RECEPTION_DETAIL_ID
        LEFT JOIN WIP_INFO
        ON WIP_INFO.SN = PM_REPAIR_RCV_DETAIL.SN
        where
        PM_REPAIR_RCV.RECEPTION_ID = PM_REPAIR_RCV_DETAIL.RECEPTION_ID
        AND PM_REPAIR_RCV.BILL_TYPE='10'

        <!-- 条件 -->
        <if test="fromStation != null and fromStation != ''"> AND PM_REPAIR_RCV.FROM_STATION = #{fromStation}</if>
        <if test="repairStatus != null and repairStatus != ''"> AND PM_REPAIR_RCV_DETAIL.STATUS = #{repairStatus}</if>
        <if test="building != null and building != ''"> AND PM_REPAIR_RCV.BUILDING = #{building}</if>
        <if test="applicationSection != null and applicationSection != ''"> AND PM_REPAIR_RCV.APPLICATION_SECTION =
        #{applicationSection}</if>
        <if test="prodplanId != null and prodplanId != ''"> AND PM_REPAIR_RCV_DETAIL.RCV_PRODPLAN_ID = #{prodplanId}</if>
        <if test="sn != null and sn != ''"> AND PM_REPAIR_RCV_DETAIL.SN = #{sn}</if>
        <if test="itemName != null and itemName != ''"> AND PM_REPAIR_RCV_DETAIL.ITEM_NAME like concat(#{itemName},'%')</if>
        <if test="itemCode != null and itemCode != ''"> AND PM_REPAIR_RCV_DETAIL.ITEM_CODE like concat(#{itemCode},'%')</if>
        <if test="status != null and status != ''"> AND <include refid="Table_Name" />.STATUS = #{status}</if>
        <if test="prodplanId != null and prodplanId != ''"> AND PM_REPAIR_RCV_DETAIL.RCV_PRODPLAN_ID like concat(#{prodplanId},'%')</if>
        <if test="workOrderNo != null and workOrderNo != ''"> AND <include refid="Table_Name" />.WORK_ORDER_NO like concat(#{workOrderNo},'%')</if>
        <if test="repairResult != null and repairResult != ''"> AND <include refid="Table_Name" />.REPAIR_RESULT = #{repairResult}</if>
        <if test="craftSection != null and craftSection != ''"> AND <include refid="Table_Name" />.CRAFT_SECTION like concat(#{craftSection},'%')</if>
        <if test="nextStation != null and nextStation != ''"> AND <include refid="Table_Name" />.NEXT_STATION like concat(#{nextStation},'%')</if>
        <if test="nextProcess != null and nextProcess != ''"> AND <include refid="Table_Name" />.NEXT_PROCESS like concat(#{nextProcess},'%')</if>
        <if test="repairProducetType != null and repairProducetType != ''"> AND <include refid="Table_Name" />.REPAIR_PRODUCT_TYPE like concat(#{repairProducetType},'%')</if>
        <if test="repairRemark != null and repairRemark != ''"> AND <include refid="Table_Name" />.REPAIR_REMARK like concat(#{repairRemark},'%')</if>
        <if test="repairProductStype != null and repairProductStype != ''"> AND <include refid="Table_Name" />.REPAIR_PRODUCT_STYPE like concat(#{repairProductStype},'%')</if>
        <if test="repairProuctMstype != null and repairProuctMstype != ''"> AND <include refid="Table_Name" />.REPAIR_PRODUCT_MSTYPE like concat(#{repairProuctMstype},'%')</if>
        <if test="isFirstPiece != null and isFirstPiece != ''"> AND <include refid="Table_Name" />.IS_FIRST_PIECE = #{isFirstPiece}</if>
        <if test="receptionId != null and receptionId != ''"> AND <include refid="Table_Name" />.RECEPTION_ID = #{receptionId}</if>
        <if test="orgId != null"> AND PM_REPAIR_RCV.ORG_ID = cast(#{orgId} as numeric)</if>
        <if test="factoryId != null"> AND PM_REPAIR_RCV.FACTORY_ID = cast(#{factoryId} as numeric)</if>
        <if test="entityId != null"> AND PM_REPAIR_RCV.ENTITY_ID = cast(#{entityId} as numeric)</if>
        <if test="lastUpdatedBy != null and lastUpdatedBy !=''"> AND <include refid="Table_Name" />.LAST_UPDATED_BY = #{lastUpdatedBy}</if>
        <if test="startTime !=null and startTime != ''"> AND <include refid="Table_Name" />.CREATE_DATE &gt;= to_timestamp(#{startTime},'yyyy-mm-dd hh24:mi:ss') </if>
        <if test="endTime !=null and endTime != ''"> AND <include refid="Table_Name" />.CREATE_DATE &lt;= to_timestamp(#{endTime},'yyyy-mm-dd hh24:mi:ss') </if>
        <if test="creatStartTime !=null and creatStartTime != ''"> AND <include refid="Table_Name" />.CREATE_DATE &gt;= to_timestamp(#{creatStartTime},'yyyy-mm-dd hh24:mi:ss') </if>
        <if test="creatEndTime !=null and creatEndTime != ''"> AND <include refid="Table_Name" />.CREATE_DATE &lt;= to_timestamp(#{creatEndTime},'yyyy-mm-dd hh24:mi:ss') </if>
        <if test="sendStartTime !=null and sendStartTime != ''"> AND PM_REPAIR_RCV_DETAIL.REPAIRCREATE &gt;= to_timestamp(#{sendStartTime},'yyyy-mm-dd hh24:mi:ss') </if>
        <if test="sendEndTime !=null and sendEndTime != ''"> AND PM_REPAIR_RCV_DETAIL.REPAIRCREATE &lt;= to_timestamp(#{sendEndTime},'yyyy-mm-dd hh24:mi:ss') </if>
        <if test="startRepairRcvDate !=null and startRepairRcvDate != ''"> AND PM_REPAIR_RCV_DETAIL.REPAIR_RCV_DATE &gt;= to_timestamp(#{startRepairRcvDate},'yyyy-mm-dd hh24:mi:ss') </if>
        <if test="endRepairRcvDate !=null and endRepairRcvDate != ''"> AND PM_REPAIR_RCV_DETAIL.REPAIR_RCV_DATE &lt;= to_timestamp(#{endRepairRcvDate},'yyyy-mm-dd hh24:mi:ss') </if>
        <if test="sendFlag != null and sendFlag != ''"> AND PM_REPAIR_RCV_DETAIL.SEND_FLAG = #{sendFlag}</if>
        <if test="rcvFlag != null and rcvFlag != ''"> AND PM_REPAIR_RCV_DETAIL.RCV_FLAG = #{rcvFlag}</if>
        <if test="isAccept != null and isAccept != ''"> AND PM_REPAIR_RCV_DETAIL.IS_ACCEPT = cast(#{isAccept} as numeric)</if>
        <if test="deliveryNo != null and deliveryNo != ''"> AND PM_REPAIR_RCV.DELIVERY_NO = #{deliveryNo}</if>
        <if test="locationNo != null and locationNo != ''"> AND PM_REPAIR_RCV_DETAIL.LOCATION_NO = #{locationNo}</if>
        <include refid="RCV_ORDER_FILELD" />
    </select>


    <select id="repairInfoCount" parameterType="com.zte.interfaces.dto.PmRepairQueryOutParamDTO" resultType="java.lang.Integer">
        select
         count(*)
        from
         PM_REPAIR_RCV
         LEFT JOIN PM_REPAIR_RCV_DETAIL ON PM_REPAIR_RCV.RECEPTION_ID=PM_REPAIR_RCV_DETAIL.RECEPTION_ID
         LEFT JOIN PM_REPAIR_INFO ON PM_REPAIR_INFO.RECEPTION_ID = PM_REPAIR_RCV_DETAIL.RECEPTION_DETAIL_ID
         LEFT JOIN PM_REPAIR_DETAIL ON PM_REPAIR_INFO.REPAIR_ID=PM_REPAIR_DETAIL.REPAIR_ID
         -- LEFT JOIN WIP_INFO ON WIP_INFO.SN = PM_REPAIR_RCV_DETAIL.SN
        WHERE
         PM_REPAIR_RCV_DETAIL.SN IS NOT NULL
         and PM_REPAIR_RCV.BILL_TYPE='10'
         and PM_REPAIR_RCV.ENABLED_FLAG = 'Y'
         and PM_REPAIR_RCV_DETAIL.ENABLED_FLAG = 'Y'
         and PM_REPAIR_INFO.ENABLED_FLAG = 'Y'
         and PM_REPAIR_DETAIL.ENABLED_FLAG = 'Y'
         <!-- 查询条件 -->
         <include refid="repairCondition" />
    </select>

    <select id="listRepairInfoPage" parameterType="com.zte.interfaces.dto.PmRepairQueryInParamDTO" resultType="com.zte.interfaces.dto.PmRepairQueryOutParamDTO">
            select
              <!-- 展示字段 -->
                pm_repair_rcv.DELIVERY_NO as deliveryNo
                ,pm_repair_rcv_detail.STATUS as status
                ,PM_REPAIR_RCV_DETAIL.MATERIAL_VERSION as materialVersion
                ,PM_REPAIR_RCV_DETAIL.PCB_VERSION as pcbVersion
                ,PM_REPAIR_RCV_DETAIL.ERROR_CODE as errorCode
                ,PM_REPAIR_RCV_DETAIL.ERROR_DESCRIPTION as errorDescription
                ,PM_REPAIR_RCV.FROM_STATION as fromStation
                ,PM_REPAIR_RCV_DETAIL.PRODUCT_CLASS as productClass
                ,PM_REPAIR_RCV_DETAIL.PRODUCT_SMLCLASS as productSmlclass
                ,PM_REPAIR_RCV_DETAIL.STYLE as style
                ,PM_REPAIR_RCV_DETAIL.RECEPTION_BY as receptionBy
                ,PM_REPAIR_RCV_DETAIL.RECEIVING_TIME as receivingTime
                ,pm_repair_info.RESULT as result
                ,PM_REPAIR_DETAIL.REPAIR_PRODUCT_TYPE as repairProductType
                ,PM_REPAIR_DETAIL.REPAIR_PRODUCT_STYPE as repairProductStype
                ,PM_REPAIR_DETAIL.REPAIR_PRODUCT_MSTYPE as repairProductMstype
                ,PM_REPAIR_DETAIL.REPAIR_METHOD as repairMethod
                ,PM_REPAIR_DETAIL.REMARK as remark
                ,PM_REPAIR_DETAIL.IS_SUB as isSub
                ,PM_REPAIR_DETAIL.IS_LOCATION_NO as isLocationNo
                ,PM_REPAIR_DETAIL.LOCATION_NO as locationNo
                ,pm_repair_rcv_detail.SN as sn
                ,PM_REPAIR_DETAIL.ITEM_CODE as itemCode
                ,PM_REPAIR_DETAIL.ITEM_NAME as itemName
                ,PM_REPAIR_DETAIL.STYLE as recordStyle
                ,PM_REPAIR_DETAIL.BG_BRAND_NO as bgBrandNo
                ,PM_REPAIR_DETAIL.SUPPLIER_NAME as supplierName
                ,PM_REPAIR_DETAIL.IS_CONTROL as isControl
                ,PM_REPAIR_DETAIL.REPLACE_SN as replaceSn
                ,PM_REPAIR_DETAIL.REPLACE_ITEM_CODE as replaceItemCode
                ,PM_REPAIR_DETAIL.REPLACE_BRAND as replaceBrand
                ,PM_REPAIR_DETAIL.REPLACE_SUPPLIER as replaceSupplier
                ,PM_REPAIR_DETAIL.REPAIR_BY as repairBy
                ,PM_REPAIR_DETAIL.REPAIR_DEPATRMENT as repairDepatrment
                ,PM_REPAIR_DETAIL.REPAIR_DATE as repairDate
                ,PM_REPAIR_DETAIL.REPAIR_DONE_DATE as repairDoneDate
                ,PM_REPAIR_DETAIL.SUB_ITEM_SN as subItemSn
                ,PM_REPAIR_DETAIL.SUB_ITEM_CODE as subItemCode
                ,PM_REPAIR_DETAIL.SUN_ITEM_NAME as sunItemName
                ,PM_REPAIR_RCV_DETAIL.RCV_PRODPLAN_ID as rcvProdplanId
                ,pm_repair_detail.item_sn as itemSn
                ,trunc((extract (epoch from (PM_REPAIR_RCV_DETAIL.repair_rcv_date - PM_REPAIR_RCV_DETAIL.RECEIVING_TIME))/60/60/24)::numeric,3) as repairCycle
                ,PM_REPAIR_DETAIL.reason_description as reasonDescription
                ,PM_REPAIR_RCV_DETAIL.item_code as itemNo
                ,PM_REPAIR_RCV_DETAIL.item_name as pmItemName
                from
                 PM_REPAIR_RCV
                 LEFT JOIN PM_REPAIR_RCV_DETAIL ON PM_REPAIR_RCV.RECEPTION_ID=PM_REPAIR_RCV_DETAIL.RECEPTION_ID
                 LEFT JOIN PM_REPAIR_INFO ON PM_REPAIR_INFO.RECEPTION_ID = PM_REPAIR_RCV_DETAIL.RECEPTION_DETAIL_ID
                 LEFT JOIN PM_REPAIR_DETAIL ON PM_REPAIR_INFO.REPAIR_ID=PM_REPAIR_DETAIL.REPAIR_ID
                 -- LEFT JOIN WIP_INFO ON WIP_INFO.SN = PM_REPAIR_RCV_DETAIL.SN
                WHERE
                 PM_REPAIR_RCV_DETAIL.SN IS NOT NULL
                 and PM_REPAIR_RCV.BILL_TYPE='10'
                 and PM_REPAIR_RCV.ENABLED_FLAG = 'Y'
                 and PM_REPAIR_RCV_DETAIL.ENABLED_FLAG = 'Y'
                 and PM_REPAIR_INFO.ENABLED_FLAG = 'Y'
                 and PM_REPAIR_DETAIL.ENABLED_FLAG = 'Y'
                 <!-- 查询条件 -->
                 <include refid="repairCondition" />
              <if test="startRow != null and endRow != null">
                limit #{endRow}::numeric-#{startRow}::numeric+1 offset case when ( #{startRow}::numeric-1 &lt; 0) then 0 else  #{startRow}::numeric-1 end
              </if>
    </select>

    <!-- 维修查询的条件 -->
    <sql id="repairCondition">
        <if test="deliveryNo != null and deliveryNo != ''">and pm_repair_rcv.DELIVERY_NO = #{deliveryNo}</if>
        <if test="status != null and status != ''">and pm_repair_rcv_detail.STATUS = #{status}</if>
        <if test="materialVersion != null and materialVersion != ''">and PM_REPAIR_RCV_DETAIL.MATERIAL_VERSION = #{materialVersion}</if>
        <if test="pcbVersion != null and pcbVersion != ''">and PM_REPAIR_RCV_DETAIL.PCB_VERSION = #{pcbVersion}</if>
        <if test="errorCode != null and errorCode != ''">and PM_REPAIR_RCV_DETAIL.ERROR_CODE = #{errorCode}</if>
        <if test="errorDescription != null and errorDescription != ''">and PM_REPAIR_RCV_DETAIL.ERROR_DESCRIPTION = #{errorDescription}</if>
        <if test="fromStation != null and fromStation != ''">and PM_REPAIR_RCV.FROM_STATION = #{fromStation}</if>
        <if test="productClass != null and productClass != ''">and PM_REPAIR_RCV_DETAIL.PRODUCT_CLASS = #{productClass}</if>
        <if test="productSmlclass != null and productSmlclass != ''">and PM_REPAIR_RCV_DETAIL.PRODUCT_SMLCLASS = #{productSmlclass}</if>
        <if test="style != null and style != ''">and PM_REPAIR_RCV_DETAIL.STYLE = #{style}</if>
        <if test="receptionBy != null and receptionBy != ''">and PM_REPAIR_RCV_DETAIL.RECEPTION_BY = #{receptionBy}</if>
        <if test="receivingTime != null and receivingTime != ''">and PM_REPAIR_RCV_DETAIL.RECEIVING_TIME = to_timestamp(#{receivingTime},'yyyy-MM-dd hh24:mi:ss') </if>
        <if test="result != null and result != ''">and pm_repair_info.RESULT = #{result}</if>
        <if test="repairProductType != null and repairProductType != ''">and PM_REPAIR_DETAIL.REPAIR_PRODUCT_TYPE = #{repairProductType}</if>
        <if test="repairProductStype != null and repairProductStype != ''">and PM_REPAIR_DETAIL.REPAIR_PRODUCT_STYPE = #{repairProductStype}</if>
        <if test="repairProductMstype != null and repairProductMstype != ''">and PM_REPAIR_DETAIL.REPAIR_PRODUCT_MSTYPE = #{repairProductMstype}</if>
        <if test="repairMethod != null and repairMethod != ''">and PM_REPAIR_DETAIL.REPAIR_METHOD = #{repairMethod}</if>
        <if test="remark != null and remark != ''">and PM_REPAIR_DETAIL.REMARK = #{remark}</if>
        <if test="isSub != null and isSub != ''">and PM_REPAIR_DETAIL.IS_SUB = #{isSub}</if>
        <if test="isLocationNo != null and isLocationNo != ''">and PM_REPAIR_DETAIL.IS_LOCATION_NO = #{isLocationNo}</if>
        <if test="locationNo != null and locationNo != ''">and PM_REPAIR_DETAIL.LOCATION_NO = #{locationNo}</if>
        <!-- 后续若需要使用sn作为查询条件请另写sql,直接在这加回影响以前功能。渗透测试修复修改 -->
        <!--<if test="sn != null and sn != ''">and pm_repair_rcv_detail.SN in (${sn})</if>-->
        <if test="inSnList != null and inSnList.size > 0"> and pm_repair_rcv_detail.SN in
          <foreach collection="inSnList" index="index" item="item"
                   separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
          </foreach>
        </if>
        <if test="itemCode != null and itemCode != ''">and PM_REPAIR_DETAIL.ITEM_CODE = #{itemCode}</if>
        <if test="itemName != null and itemName != ''">and PM_REPAIR_DETAIL.ITEM_NAME = #{itemName}</if>
        <if test="recordStyle != null and recordStyle != ''"> and PM_REPAIR_DETAIL.STYLE = #{recordStyle}</if>
        <if test="bgBrandNo != null and bgBrandNo != ''">and PM_REPAIR_DETAIL.BG_BRAND_NO = #{bgBrandNo}</if>
        <if test="supplierName != null and supplierName != ''">and PM_REPAIR_DETAIL.SUPPLIER_NAME = #{supplierName}</if>
        <if test="isControl != null and isControl != ''">and PM_REPAIR_DETAIL.IS_CONTROL = #{isControl}</if>
        <if test="replaceSn != null and replaceSn != ''">and PM_REPAIR_DETAIL.REPLACE_SN = #{replaceSn}</if>
        <if test="replaceItemCode != null and replaceItemCode != ''">and PM_REPAIR_DETAIL.REPLACE_ITEM_CODE = #{replaceItemCode}</if>
        <if test="replaceBrand != null and replaceBrand != ''">and PM_REPAIR_DETAIL.REPLACE_BRAND = #{replaceBrand}</if>
        <if test="replaceSupplier != null and replaceSupplier != ''">and PM_REPAIR_DETAIL.REPLACE_SUPPLIER = #{replaceSupplier}</if>
        <if test="repairBy != null and repairBy != ''">and PM_REPAIR_DETAIL.REPAIR_BY = #{repairBy}</if>
        <if test="repairDepatrment != null and repairDepatrment != ''">and PM_REPAIR_DETAIL.REPAIR_DEPATRMENT = #{repairDepatrment}</if>
        <if test="repairDate != null and repairDate != ''">and PM_REPAIR_DETAIL.REPAIR_DATE = to_timestamp(#{repairDate},'yyyy-MM-dd hh24:mi:ss')</if>
        <if test="repairDoneDate != null and repairDoneDate != ''">and PM_REPAIR_DETAIL.REPAIR_DONE_DATE = to_timestamp(#{repairDoneDate},'yyyy-MM-dd hh24:mi:ss') </if>
        <if test="subItemSn != null and subItemSn != ''">and PM_REPAIR_DETAIL.SUB_ITEM_SN = #{subItemSn}</if>
        <if test="subItemCode != null and subItemCode != ''">and PM_REPAIR_DETAIL.SUB_ITEM_CODE = #{subItemCode}</if>
        <if test="sunItemName != null and sunItemName != ''">and PM_REPAIR_DETAIL.SUN_ITEM_NAME = #{sunItemName}</if>
        <if test="rcvProdplanId != null and rcvProdplanId != ''">and PM_REPAIR_RCV_DETAIL.RCV_PRODPLAN_ID = #{rcvProdplanId}</if>
        <if test="itemSn != null and itemSn != ''">and pm_repair_detail.item_sn = #{itemSn}</if>

        <if test="receivingTimeStart != null and receivingTimeStart != '' and receivingTimeEnd != null and receivingTimeEnd != ''">
            <![CDATA[and PM_REPAIR_RCV_DETAIL.RECEIVING_TIME >= to_timestamp(#{receivingTimeStart},'yyyy-MM-dd hh24:mi:ss')]]>
            <![CDATA[and PM_REPAIR_RCV_DETAIL.RECEIVING_TIME <= to_timestamp(#{receivingTimeEnd},'yyyy-MM-dd hh24:mi:ss')]]>
        </if>

        <if test="repairDateStart != null and repairDateStart != '' and repairDateEnd != null and repairDateEnd != ''">
            <![CDATA[and PM_REPAIR_DETAIL.REPAIR_DATE >= to_timestamp(#{repairDateStart},'yyyy-MM-dd hh24:mi:ss')]]>
            <![CDATA[and PM_REPAIR_DETAIL.REPAIR_DATE <= to_timestamp(#{repairDateEnd},'yyyy-MM-dd hh24:mi:ss')]]>
        </if>

        <if test="repairDoneDateStart != null and repairDoneDateStart != '' and repairDoneDateEnd != null and repairDoneDateEnd != ''">
            <![CDATA[and PM_REPAIR_DETAIL.REPAIR_DONE_DATE >= to_timestamp(#{repairDoneDateStart},'yyyy-MM-dd hh24:mi:ss')]]>
            <![CDATA[and PM_REPAIR_DETAIL.REPAIR_DONE_DATE <= to_timestamp(#{repairDoneDateEnd},'yyyy-MM-dd hh24:mi:ss')]]>
        </if>

    </sql>

  <!-- 维修积压库存明细查询 -->
  <sql id="repairStockQuery">
    <!-- 维修库存积压（头表）-->
    (select
    PM_REPAIR_RCV_DETAIL.repairCycle,
    <!-- PM_REPAIR_RCV.*, -->
    <!-- PM_REPAIR_INFO -->
    PM_REPAIR_INFO.CRAFT_SECTION,
    PM_REPAIR_INFO.STATUS,
    PM_REPAIR_INFO.REPAIR_ID,
    PM_REPAIR_INFO.WORK_STATION,
    PM_REPAIR_INFO.REPAIR_PRODUCT_TYPE,
    PM_REPAIR_INFO.REPAIR_PRODUCT_STYPE,
    PM_REPAIR_INFO.REPAIR_PRODUCT_MSTYPE,
    PM_REPAIR_INFO.RECEPTION_ID,
    PM_REPAIR_INFO.WORK_ORDER_NO,
    PM_REPAIR_INFO.REPAIR_REMARK,
    PM_REPAIR_INFO.CREATE_DATE,
    PM_REPAIR_INFO.LAST_UPDATED_BY,
    PM_REPAIR_INFO.CRAFT_SECTION as WIP_CRAFT_SECTION,
    nvl(WIP_INFO.LINE_CODE, PM_REPAIR_RCV_DETAIL.LINE_CODE) LINE_CODE,
    WIP_INFO.ITEM_NO,
    WIP_INFO.ATTRIBUTE1,
    WIP_INFO.ATTRIBUTE2,

    <!-- PM_REPAIR_RCV -->
    PM_REPAIR_RCV.CREATE_BY,
    PM_REPAIR_RCV.DELIVERY_BY,
    nvl(PM_REPAIR_RCV.RECEPTION_BY, PM_REPAIR_RCV_DETAIL.RECEPTION_BY) RECEPTION_BY,
    PM_REPAIR_RCV.LAST_UPDATED_DATE,
    PM_REPAIR_RCV.ENABLED_FLAG,
    PM_REPAIR_RCV.ORG_ID,
    PM_REPAIR_RCV.FACTORY_ID,
    PM_REPAIR_RCV.ENTITY_ID,
    PM_REPAIR_RCV.FROM_STATION,
    PM_REPAIR_RCV.WAREHOUSE_CODE,
    PM_REPAIR_RCV.BUILDING,
    PM_REPAIR_RCV.DELIVERY_NO,
    PM_REPAIR_RCV.APPLICATION_SECTION,

    <!-- PM_REPAIR_RCV_DETAIL -->
    PM_REPAIR_RCV_DETAIL.STATUS AS REPAIRSTATUS,
    PM_REPAIR_RCV_DETAIL.REPAIRCREATE,
    PM_REPAIR_RCV_DETAIL.REPAIRUPDATE,
    PM_REPAIR_RCV_DETAIL.REPAIR_RCV_DATE,
    PM_REPAIR_RCV_DETAIL.RCV_CRAFT_SECTION,
    PM_REPAIR_RCV_DETAIL.RCV_PRODPLAN_ID,
    PM_REPAIR_RCV_DETAIL.SEND_FLAG,
    PM_REPAIR_RCV_DETAIL.RCV_FLAG,
    PM_REPAIR_RCV_DETAIL.SN,
    PM_REPAIR_RCV_DETAIL.ITEM_NAME,
    PM_REPAIR_RCV_DETAIL.ITEM_CODE,
    PM_REPAIR_RCV_DETAIL.ERROR_CODE,
    PM_REPAIR_RCV_DETAIL.ERROR_DESCRIPTION,
    PM_REPAIR_RCV_DETAIL.RCV_PRODPLAN_ID AS PRODPLAN_ID,
    PM_REPAIR_RCV_DETAIL.SCRAP_REASON,
    PM_REPAIR_RCV_DETAIL.SCAN_TIME,
    PM_REPAIR_RCV_DETAIL.PCB_VERSION,
    PM_REPAIR_RCV_DETAIL.RECEIVING_TIME,
    PM_REPAIR_RCV_DETAIL.RETURNED_DATE,
    PM_REPAIR_RCV_DETAIL.PRODUCT_CLASS,
    PM_REPAIR_RCV_DETAIL.PRODUCT_SMLCLASS,
    PM_REPAIR_RCV_DETAIL.LOCATION_NO
    from PM_REPAIR_RCV,
    (
    SELECT
    t.STATUS AS REPAIRSTATUS,
    t.Create_Date AS REPAIRCREATE,
    t.LAST_UPDATED_DATE AS REPAIRUPDATE,
    t.REPAIR_RCV_DATE,
    t.CRAFT_SECTION AS RCV_CRAFT_SECTION,
    t.RCV_PRODPLAN_ID,
    t.SEND_FLAG,
    t.RCV_FLAG,
    t.ITEM_NAME,
    t.ITEM_CODE,
    t.ERROR_CODE,
    t.ERROR_DESCRIPTION,
    t.RCV_PRODPLAN_ID AS PRODPLAN_ID,
    t.SCRAP_REASON,
    t.SCAN_TIME,
    t.STATUS,t.RECEPTION_DETAIL_ID,t.SN,t.RECEPTION_ID,
    t.PCB_VERSION,
    t.RECEIVING_TIME,
    t.RETURNED_DATE,
    t.PRODUCT_CLASS,
    t.PRODUCT_SMLCLASS,
    t.LINE_CODE,
    t.RECEPTION_BY,
    pd.location_no,
    <!--
     维修库存积压查询：
        返还时间 - 接收时间 = date
        返还时间=null(取系统当前时间) - 接收时间 = date
        返还时间(null) - 接收时间(null) = null;
    -->
    <choose>
      <when test="overStockNewOrOld !=null and overStockNewOrOld != ''">
        CASE
        WHEN pd.REPAIR_DATE is null THEN trunc((extract (epoch from (sysdate - t.CREATE_DATE))/60/60/24)::numeric)
        ELSE trunc((extract (epoch from (pd.REPAIR_DATE - t.CREATE_DATE))/60/60/24)::numeric,3) END repairCycle
      </when>
      <otherwise>
        CASE
        WHEN t.REPAIR_RCV_DATE is null THEN trunc((extract (epoch from (sysdate - t.RECEIVING_TIME))/60/60/24)::numeric)
        ELSE trunc((extract (epoch from (t.REPAIR_RCV_DATE - t.RECEIVING_TIME))/60/60/24)::numeric,3) END repairCycle
      </otherwise>
    </choose>

    FROM
    PM_REPAIR_RCV_DETAIL t
    LEFT JOIN PM_REPAIR_INFO pri
    ON pri.RECEPTION_ID = t.RECEPTION_DETAIL_ID
    LEFT JOIN PM_REPAIR_DETAIL pd
    ON pri.REPAIR_ID=pd.REPAIR_ID
    where t.SN IS NOT NULL
    and t.STATUS IN ('10560001','10560002')
    GROUP BY
    t.STATUS,
    t.Create_Date,
    t.LAST_UPDATED_DATE,
    t.REPAIR_RCV_DATE,
    t.CRAFT_SECTION,
    t.RCV_PRODPLAN_ID,
    t.SEND_FLAG,
    t.RCV_FLAG,
    t.SN,
    t.ITEM_NAME,
    t.ITEM_CODE,
    t.ERROR_CODE,
    t.ERROR_DESCRIPTION,
    t.RCV_PRODPLAN_ID,
    t.SCRAP_REASON,
    t.SCAN_TIME,
    t.STATUS,t.RECEPTION_ID,t.repair_rcv_date,t.RECEIVING_TIME,t.RECEPTION_DETAIL_ID,pd.REPAIR_DATE,
    t.PCB_VERSION,
    t.RECEIVING_TIME,
    t.RETURNED_DATE,
    t.PRODUCT_CLASS,
    t.PRODUCT_SMLCLASS,
    pd.location_no
    <!-- ORDER BY t.RECEPTION_ID -->
    ) PM_REPAIR_RCV_DETAIL
    LEFT JOIN PM_REPAIR_INFO
    ON PM_REPAIR_INFO.RECEPTION_ID = PM_REPAIR_RCV_DETAIL.RECEPTION_DETAIL_ID
    LEFT JOIN WIP_INFO
    ON WIP_INFO.SN = PM_REPAIR_RCV_DETAIL.SN
    where
    PM_REPAIR_RCV.RECEPTION_ID = PM_REPAIR_RCV_DETAIL.RECEPTION_ID
    AND PM_REPAIR_RCV.BILL_TYPE='10'

    <!-- 条件 -->
    <if test="fromStation != null and fromStation != ''"> AND PM_REPAIR_RCV.FROM_STATION = #{fromStation}</if>
    <if test="repairStatus != null and repairStatus != ''"> AND PM_REPAIR_RCV_DETAIL.STATUS = #{repairStatus}</if>
    <if test="building != null and building != ''"> AND PM_REPAIR_RCV.BUILDING = #{building}</if>
    <if test="applicationSection != null and applicationSection != ''"> AND PM_REPAIR_RCV.APPLICATION_SECTION =
      #{applicationSection}</if>
    <if test="prodplanId != null and prodplanId != ''"> AND PM_REPAIR_RCV_DETAIL.RCV_PRODPLAN_ID = #{prodplanId}</if>
    <if test="sn != null and sn != ''"> AND PM_REPAIR_RCV_DETAIL.SN = #{sn}</if>
    <if test="itemName != null and itemName != ''"> AND PM_REPAIR_RCV_DETAIL.ITEM_NAME like concat(#{itemName},'%')</if>
    <if test="itemCode != null and itemCode != ''"> AND PM_REPAIR_RCV_DETAIL.ITEM_CODE like concat(#{itemCode},'%')</if>
    <if test="status != null and status != ''"> AND <include refid="Table_Name" />.STATUS = #{status}</if>
    <if test="prodplanId != null and prodplanId != ''"> AND PM_REPAIR_RCV_DETAIL.RCV_PRODPLAN_ID like concat(#{prodplanId},'%')</if>
    <if test="workOrderNo != null and workOrderNo != ''"> AND <include refid="Table_Name" />.WORK_ORDER_NO like concat(#{workOrderNo},'%')</if>
    <if test="repairResult != null and repairResult != ''"> AND <include refid="Table_Name" />.REPAIR_RESULT = #{repairResult}</if>
    <if test="craftSection != null and craftSection != ''"> AND <include refid="Table_Name" />.CRAFT_SECTION like concat(#{craftSection},'%')</if>
    <if test="nextStation != null and nextStation != ''"> AND <include refid="Table_Name" />.NEXT_STATION like concat(#{nextStation},'%')</if>
    <if test="nextProcess != null and nextProcess != ''"> AND <include refid="Table_Name" />.NEXT_PROCESS like concat(#{nextProcess},'%')</if>
    <if test="repairProducetType != null and repairProducetType != ''"> AND <include refid="Table_Name" />.REPAIR_PRODUCT_TYPE like concat(#{repairProducetType},'%')</if>
    <if test="repairRemark != null and repairRemark != ''"> AND <include refid="Table_Name" />.REPAIR_REMARK like concat(#{repairRemark},'%')</if>
    <if test="repairProductStype != null and repairProductStype != ''"> AND <include refid="Table_Name" />.REPAIR_PRODUCT_STYPE like concat(#{repairProductStype},'%')</if>
    <if test="repairProuctMstype != null and repairProuctMstype != ''"> AND <include refid="Table_Name" />.REPAIR_PRODUCT_MSTYPE like concat(#{repairProuctMstype},'%')</if>
    <if test="isFirstPiece != null and isFirstPiece != ''"> AND <include refid="Table_Name" />.IS_FIRST_PIECE = #{isFirstPiece}</if>
    <if test="receptionId != null and receptionId != ''"> AND <include refid="Table_Name" />.RECEPTION_ID = #{receptionId}</if>
    <if test="orgId != null"> AND PM_REPAIR_RCV.ORG_ID = cast(#{orgId} as numeric)</if>
    <if test="factoryId != null"> AND PM_REPAIR_RCV.FACTORY_ID = cast(#{factoryId} as numeric)</if>
    <if test="entityId != null"> AND PM_REPAIR_RCV.ENTITY_ID = cast(#{entityId} as numeric)</if>
    <if test="lastUpdatedBy != null and lastUpdatedBy !=''"> AND <include refid="Table_Name" />.LAST_UPDATED_BY = #{lastUpdatedBy}</if>
    <if test="startTime !=null and startTime != ''"> AND <include refid="Table_Name" />.CREATE_DATE &gt;= to_timestamp(#{startTime},'yyyy-mm-dd hh24:mi:ss') </if>
    <if test="endTime !=null and endTime != ''"> AND <include refid="Table_Name" />.CREATE_DATE &lt;= to_timestamp(#{endTime},'yyyy-mm-dd hh24:mi:ss') </if>
    <if test="creatStartTime !=null and creatStartTime != ''"> AND <include refid="Table_Name" />.CREATE_DATE &gt;= to_timestamp(#{creatStartTime},'yyyy-mm-dd hh24:mi:ss') </if>
    <if test="creatEndTime !=null and creatEndTime != ''"> AND <include refid="Table_Name" />.CREATE_DATE &lt;= to_timestamp(#{creatEndTime},'yyyy-mm-dd hh24:mi:ss') </if>
    <if test="sendStartTime !=null and sendStartTime != ''"> AND PM_REPAIR_RCV_DETAIL.REPAIRCREATE &gt;= to_timestamp(#{sendStartTime},'yyyy-mm-dd hh24:mi:ss') </if>
    <if test="sendEndTime !=null and sendEndTime != ''"> AND PM_REPAIR_RCV_DETAIL.REPAIRCREATE &lt;= to_timestamp(#{sendEndTime},'yyyy-mm-dd hh24:mi:ss') </if>
    <if test="startRepairRcvDate !=null and startRepairRcvDate != ''"> AND PM_REPAIR_RCV_DETAIL.REPAIR_RCV_DATE &gt;= to_timestamp(#{startRepairRcvDate},'yyyy-mm-dd hh24:mi:ss') </if>
    <if test="endRepairRcvDate !=null and endRepairRcvDate != ''"> AND PM_REPAIR_RCV_DETAIL.REPAIR_RCV_DATE &lt;= to_timestamp(#{endRepairRcvDate},'yyyy-mm-dd hh24:mi:ss') </if>
    <if test="sendFlag != null and sendFlag != ''"> AND PM_REPAIR_RCV_DETAIL.SEND_FLAG = #{sendFlag}</if>
    <if test="rcvFlag != null and rcvFlag != ''"> AND PM_REPAIR_RCV_DETAIL.RCV_FLAG = #{rcvFlag}</if>
    <if test="isAccept != null and isAccept != ''"> AND PM_REPAIR_RCV_DETAIL.IS_ACCEPT = cast(#{isAccept} as numeric)</if>
    <if test="deliveryNo != null and deliveryNo != ''"> AND PM_REPAIR_RCV.DELIVERY_NO = #{deliveryNo}</if>
    <if test="locationNo != null and locationNo != ''"> AND PM_REPAIR_RCV_DETAIL.LOCATION_NO = #{locationNo}</if>
    <include refid="RCV_ORDER_FILELD" />)U
  </sql>
  <!-- 维修积压库存明细查询(最新记录) -->
  <select id="listAllRepairStockRecent" parameterType="com.zte.interfaces.dto.PmRepairInfoDTO" resultMap="RelOneResultMap">
    select S.*
    from (
    select U.*, row_number() over (partition by SN,DELIVERY_NO order by REPAIRCREATE DESC) as RANK
    from <include refid="repairStockQuery" />
    ) S
    where S.RANK = 1
  </select>
  <!-- 维修积压库存总记录数(最新记录) -->
  <select id="countTotalRecordRecent" parameterType="com.zte.interfaces.dto.PmRepairInfoDTO" resultType="java.lang.Long">
    select COUNT(*)
    from (
    select U.*, row_number() over (partition by SN,DELIVERY_NO order by REPAIRCREATE DESC) as RANK
    from <include refid="repairStockQuery" />
    ) S
    where S.RANK = 1
  </select>

  <!--mybatis引用： 引用列结构  begin>>-->
  <sql id="Detail_Column_List">
    <include refid="Table_Name" />.REPAIR_ID,
    PM_REPAIR_RCV_DETAIL.SN,
    PM_REPAIR_RCV_DETAIL.ITEM_NAME,
    PM_REPAIR_RCV_DETAIL.ITEM_CODE,
    PM_REPAIR_RCV_DETAIL.RECEIVING_TIME,
    <include refid="Table_Name" />.STATUS,
    nvl(PM_REPAIR_DETAIL.ERROR_CODE, PM_REPAIR_RCV_DETAIL.ERROR_CODE) ERROR_CODE,
    nvl(PM_REPAIR_DETAIL.ERROR_DESCRIPTION, PM_REPAIR_RCV_DETAIL.ERROR_DESCRIPTION) ERROR_DESCRIPTION,
    PM_REPAIR_DETAIL.REASON_CODE,
    PM_REPAIR_DETAIL.REASON_DESCRIPTION,
    PM_REPAIR_DETAIL.REPAIR_DETAIL_ID,
    PM_REPAIR_RCV_DETAIL.PRODUCT_CLASS,
    PM_REPAIR_RCV_DETAIL.PRODUCT_SMLCLASS,
    PM_REPAIR_DETAIL.LOCATION_NO,
    <include refid="Table_Name" />.CRAFT_SECTION,
    <include refid="Table_Name" />.WORK_STATION,
    nvl(PM_REPAIR_DETAIL.REPAIR_PRODUCT_TYPE, <include refid="Table_Name" />.REPAIR_PRODUCT_TYPE) REPAIR_PRODUCT_TYPE,
    nvl(PM_REPAIR_DETAIL.REPAIR_PRODUCT_STYPE, <include refid="Table_Name" />.REPAIR_PRODUCT_STYPE) REPAIR_PRODUCT_STYPE,
    nvl(PM_REPAIR_DETAIL.REPAIR_PRODUCT_MSTYPE, <include refid="Table_Name" />.REPAIR_PRODUCT_MSTYPE) REPAIR_PRODUCT_MSTYPE,
    <include refid="Table_Name" />.RECEPTION_ID,
    <include refid="Table_Name" />.WORK_ORDER_NO,
    PM_REPAIR_DETAIL.REMARK,
    PM_REPAIR_DETAIL.CREATE_DATE,
    PM_REPAIR_DETAIL.CREATE_BY,
    PM_REPAIR_RCV.DELIVERY_BY,
    nvl(PM_REPAIR_RCV.RECEPTION_BY, PM_REPAIR_RCV_DETAIL.RECEPTION_BY) RECEPTION_BY,
    PM_REPAIR_DETAIL.REPAIR_BY as LAST_UPDATED_BY,
    PM_REPAIR_DETAIL.REPAIR_BY,

    PM_REPAIR_RCV.LAST_UPDATED_DATE,
    PM_REPAIR_RCV.ENABLED_FLAG,
    PM_REPAIR_RCV.ORG_ID,
    PM_REPAIR_RCV.FACTORY_ID,
    PM_REPAIR_RCV.ENTITY_ID,
    PM_REPAIR_RCV.FROM_STATION,
    PM_REPAIR_RCV.WAREHOUSE_CODE,
    PM_REPAIR_RCV.BUILDING,
    PM_REPAIR_RCV_DETAIL.STATUS AS REPAIRSTATUS,
    PM_REPAIR_RCV_DETAIL.Create_Date AS REPAIRCREATE,
    PM_REPAIR_RCV_DETAIL.LAST_UPDATED_DATE AS REPAIRUPDATE,
    PM_REPAIR_RCV_DETAIL.REPAIR_RCV_DATE,
    PM_REPAIR_RCV_DETAIL.CRAFT_SECTION AS RCV_CRAFT_SECTION,
    PM_REPAIR_RCV_DETAIL.RCV_PRODPLAN_ID,
    PM_REPAIR_RCV_DETAIL.SEND_FLAG,
    PM_REPAIR_RCV_DETAIL.RCV_FLAG,
    PM_REPAIR_RCV.DELIVERY_NO,
    WIP_INFO.CRAFT_SECTION WIP_CRAFT_SECTION,
    nvl(WIP_INFO.LINE_CODE, PM_REPAIR_RCV_DETAIL.LINE_CODE) LINE_CODE,
    WIP_INFO.ITEM_NO,
    WIP_INFO.ATTRIBUTE1,
    WIP_INFO.ATTRIBUTE2,
    PM_REPAIR_RCV_DETAIL.RCV_PRODPLAN_ID AS PRODPLAN_ID,
    PM_REPAIR_RCV_DETAIL.SCRAP_REASON,
    PM_REPAIR_RCV_DETAIL.SCAN_TIME,
    PM_REPAIR_RCV_DETAIL.RETURNED_BY,
    PM_REPAIR_RCV_DETAIL.RETURNED_DATE,
    PM_REPAIR_RCV_DETAIL.RETURNED_TO,
    PM_REPAIR_DETAIL.IS_SUB,
    PM_REPAIR_DETAIL.SUB_ITEM_SN,
    PM_REPAIR_DETAIL.SUB_ITEM_CODE,
    PM_REPAIR_DETAIL.SUN_ITEM_NAME,
    PM_REPAIR_DETAIL.IS_LOCATION_NO,
    PM_REPAIR_DETAIL.ITEM_SN,
    PM_REPAIR_DETAIL.ITEM_CODE AS PRD_ITEM_CODE,
    PM_REPAIR_DETAIL.ITEM_NAME AS PRD_ITEM_NAME,
    nvl(nullif(PM_REPAIR_DETAIL.STYLE,''), PM_REPAIR_RCV_DETAIL.STYLE) STYLE,
    PM_REPAIR_DETAIL.BG_BRAND_NO,
    PM_REPAIR_DETAIL.SUPPLIER_NAME,
    PM_REPAIR_DETAIL.IS_CONTROL,
    PM_REPAIR_DETAIL.REPLACE_SN,
    PM_REPAIR_DETAIL.REPLACE_ITEM_CODE,
    PM_REPAIR_DETAIL.REPLACE_STYLE,
    PM_REPAIR_DETAIL.REPLACE_BRAND,
    PM_REPAIR_DETAIL.REPLACE_SUPPLIER,
    PM_REPAIR_DETAIL.RESULT,
    PM_REPAIR_DETAIL.REPAIR_PROCESS,
    PM_REPAIR_DETAIL.ADVERSE_TYPE,
    PM_REPAIR_DETAIL.REPAIR_METHOD,
    PM_REPAIR_DETAIL.APPROVER,
    PM_REPAIR_DETAIL.EXCHANGE_SN,
    PM_REPAIR_DETAIL.REPAIR_TEAM,
    PM_REPAIR_RCV.SN_TYPE,
    PM_REPAIR_RCV_DETAIL.REPAIR_COUNT,
    PM_REPAIR_RCV.APPLICATION_DEPARTMENT,
    PM_REPAIR_RCV.APPLICATION_SECTION,
    <!-- 维修周期： 返还时间 - 接收时间 = 维修周期(天) -->
    <choose>
      <when test="overStockNewOrOld !=null and overStockNewOrOld != ''">
        CASE
        WHEN PM_REPAIR_DETAIL.REPAIR_DATE is null THEN null
        ELSE trunc((extract (epoch from (PM_REPAIR_DETAIL.REPAIR_DATE - PM_REPAIR_RCV_DETAIL.CREATE_DATE))/60/60/24)::numeric,3) END repairCycle
      </when>
      <otherwise>
        trunc((extract (epoch from (PM_REPAIR_RCV_DETAIL.repair_rcv_date - PM_REPAIR_RCV_DETAIL.RECEIVING_TIME))/60/60/24)::numeric,3) as repairCycle
      </otherwise>
    </choose>
  </sql>


  <select id="getDetailPage" parameterType="com.zte.interfaces.dto.PmRepairInfoDTO" resultMap="RelOneResultMap">
    SELECT <include refid="Detail_Column_List" />
    FROM <include refid="Rel_One_Table_Name" />
    WHERE <include refid="Rcv_Condtion" />
    <include refid="RcvCondtions" />
    <if test="sort != null">
      <choose>
        <when test="sort=='sn'"> order by PM_REPAIR_RCV_DETAIL.SN <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='lastUpdatedDate'"> order by PM_REPAIR_RCV.last_updated_date <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='lastUpdatedBy'"> order by PM_REPAIR_RCV.last_updated_by <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='createDate'"> order by PM_REPAIR_RCV.create_date <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='createBy'"> order by PM_REPAIR_RCV.create_by <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='repareCreateDate'"> order by PM_REPAIR_DETAIL.create_date <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='repairInfoCreateDate'"> order by PM_REPAIR_INFO.CREATE_DATE <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='rcvDetailCreateDate'"> order by PM_REPAIR_RCV_DETAIL.CREATE_DATE <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='repairRcvDate'"> order by PM_REPAIR_RCV_DETAIL.REPAIR_RCV_DATE <if test="order != null and order == 'desc'"> desc </if> </when>
      </choose>
    </if>
    <if test="startRow != null and endRow != null">
      limit #{endRow}::numeric-#{startRow}::numeric+1 offset case when ( #{startRow}::numeric-1 &lt; 0) then 0 else  #{startRow}::numeric-1 end
    </if>
  </select>


  <select id="getRelOneDetailList" parameterType="com.zte.interfaces.dto.PmRepairInfoDTO" resultMap="RelOneResultMap">
    SELECT <include refid="Detail_Column_List" />
    FROM  <include refid="Rel_One_Table_Name" />
    WHERE <include refid="Rcv_Condtion" />
    <include refid="RcvCondtions" />
    <if test="sort != null">
      <choose>
        <when test="sort=='sn'"> order by PM_REPAIR_RCV_DETAIL.SN <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='lastUpdatedDate'"> order by PM_REPAIR_RCV.last_updated_date <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='lastUpdatedBy'"> order by PM_REPAIR_RCV.last_updated_by <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='createDate'"> order by PM_REPAIR_RCV.create_date <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='createBy'"> order by PM_REPAIR_RCV.create_by <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='repareCreateDate'"> order by PM_REPAIR_DETAIL.create_date <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='repairInfoCreateDate'"> order by PM_REPAIR_INFO.CREATE_DATE <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='rcvDetailCreateDate'"> order by PM_REPAIR_RCV_DETAIL.CREATE_DATE <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="sort=='repairRcvDate'"> order by PM_REPAIR_RCV_DETAIL.REPAIR_RCV_DATE <if test="order != null and order == 'desc'"> desc </if> </when>
      </choose>
    </if>
  </select>

  <select id="getSendRepairCount" parameterType="com.zte.interfaces.dto.PmRepairInfoDTO" resultMap="StatisticsMap">
    SELECT b.RCV_PRODPLAN_ID as PRODPLAN_ID,
    b.ITEM_CODE as ITEM_CODE,
    count(b.sn) as QTY
    FROM PM_REPAIR_RCV a
    inner join PM_REPAIR_RCV_DETAIL b on a.reception_id = b.reception_id
    where b.status IN ('10560001','10560002','10560003','10560004','10560007')
    and a.from_station = #{fromStation}
    and b.create_date >= to_timestamp(#{startTime},'yyyy-mm-dd hh24:mi:ss')
    and b.create_date <![CDATA[<=]]> to_timestamp(#{endTime},'yyyy-mm-dd hh24:mi:ss')
    <if test="prodplanId != null and prodplanId != ''">and b.RCV_PRODPLAN_ID = #{prodplanId}</if>
    <if test="itemCode != null and itemCode != ''">and b.ITEM_CODE = #{itemCode}</if>
    <if test="building != null and building != ''">and a.BUILDING = #{building}</if>
    group by b.RCV_PRODPLAN_ID, b.ITEM_CODE
    order by b.ITEM_CODE, b.RCV_PRODPLAN_ID
  </select>

  <select id="getRepairReturnCount" parameterType="com.zte.interfaces.dto.PmRepairInfoDTO" resultMap="StatisticsMap">
    SELECT b.RCV_PRODPLAN_ID as PRODPLAN_ID,
    b.ITEM_CODE as ITEM_CODE,
    count(b.sn) as QTY
    FROM PM_REPAIR_RCV a
    inner join PM_REPAIR_RCV_DETAIL b on a.reception_id = b.reception_id
    where b.status = '10560003'
    and a.from_station = #{fromStation}
    and b.returned_date >= to_timestamp(#{startTime},'yyyy-mm-dd hh24:mi:ss')
    and b.returned_date <![CDATA[<=]]> to_timestamp(#{endTime},'yyyy-mm-dd hh24:mi:ss')
    <if test="prodplanId != null and prodplanId != ''">and b.RCV_PRODPLAN_ID = #{prodplanId}</if>
    <if test="itemCode != null and itemCode != ''">and b.ITEM_CODE = #{itemCode}</if>
    <if test="building != null and building != ''">and a.BUILDING = #{building}</if>
    group by b.RCV_PRODPLAN_ID, b.ITEM_CODE
    order by b.ITEM_CODE, b.RCV_PRODPLAN_ID
  </select>


  <select id="getMultiRepairCount" parameterType="com.zte.interfaces.dto.PmRepairInfoDTO" resultMap="StatisticsMap">
    select sn,prodplan_id,item_code,sum(qty) qty from (
    select c.sn,b.rcv_prodplan_id prodplan_id,b.item_code, c.location_no, count(*) - 1 qty
    FROM PM_REPAIR_RCV a
    inner join PM_REPAIR_RCV_DETAIL b on a.reception_id = b.reception_id
    inner join PM_REPAIR_DETAIL c on b.repair_id = c.repair_id
    where b.status in ('10560002','10560003','10560004','10560007')
    and a.from_station = #{fromStation}
    and b.create_date >= to_timestamp(#{startTime},'yyyy-mm-dd hh24:mi:ss')
    and b.create_date <![CDATA[<=]]> to_timestamp(#{endTime},'yyyy-mm-dd hh24:mi:ss')
    <if test="prodplanId != null and prodplanId != ''">and b.RCV_PRODPLAN_ID = #{prodplanId}</if>
    <if test="itemCode != null and itemCode != ''">and b.ITEM_CODE = #{itemCode}</if>
    <if test="building != null and building != ''">and a.BUILDING = #{building}</if>
    and a.sn_type != '2'
    and c.location_no is not null and c.location_no != ''
    group by c.sn, c.location_no, b.rcv_prodplan_id, b.item_code
    having count(*) > 1
    ) group by sn,prodplan_id,item_code
    order by item_code, sn
  </select>


  <select id="getRepairResultCount" parameterType="com.zte.interfaces.dto.PmRepairInfoDTO" resultMap="StatisticsMap">
    select c.result,b.rcv_prodplan_id prodplan_id,b.item_code,count(*) qty
    FROM PM_REPAIR_RCV a
    inner join PM_REPAIR_RCV_DETAIL b on a.reception_id = b.reception_id
    inner join PM_REPAIR_DETAIL c on b.repair_id = c.repair_id
    where b.status in ('10560002','10560003','10560004','10560007')
    and a.from_station = #{fromStation}
    and b.create_date >= to_timestamp(#{startTime},'yyyy-mm-dd hh24:mi:ss')
    and b.create_date <![CDATA[<=]]> to_timestamp(#{endTime},'yyyy-mm-dd hh24:mi:ss')
    <if test="prodplanId != null and prodplanId != ''">and b.RCV_PRODPLAN_ID = #{prodplanId}</if>
    <if test="itemCode != null and itemCode != ''">and b.ITEM_CODE = #{itemCode}</if>
    <if test="building != null and building != ''">and a.BUILDING = #{building}</if>
    <if test="result != null and result != ''">and c.result = #{result}</if>
    group by c.result, b.rcv_prodplan_id, b.item_code
    order by c.result, b.item_code, b.rcv_prodplan_id
  </select>


</mapper>
