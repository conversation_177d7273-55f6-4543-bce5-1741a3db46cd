/*Started by AICoder, pid:24f52xf96av078814ac00a15607bfe726ff33fe6*/
package com.zte.application.datawb.impl;

import com.alibaba.fastjson.JSON;
import com.zte.application.datawb.WsmAssembleLinesService;
import com.zte.application.datawb.ZmsDeviceInventoryUploadService;
import com.zte.common.CommonUtils;
import com.zte.common.utils.DateUtil;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.datawb.ZmsByteDanceMRRepository;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.interfaces.dto.CustomerItemsDTO;
import com.zte.interfaces.dto.ZmsByteDanceMRDTO;
import com.zte.interfaces.dto.ZmsByteDanceMRDetailDTO;
import com.zte.interfaces.dto.ZmsByteDanceMREntityDTO;
import com.zte.springbootframe.util.json.JacksonJsonConverUtil;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;

@RunWith(PowerMockRunner.class)
@PrepareForTest({CenterfactoryRemoteService.class,
        WsmAssembleLinesService.class, JacksonJsonConverUtil.class,
        JSON.class, CommonUtils.class, DateUtil.class})
@PowerMockIgnore({"javax.net.ssl.*", "javax.security.*"})
public class ZmsByteDanceMRServiceImpl_uploadZmsByteDanceMRData_0_Test {
    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Mock
    private ZmsByteDanceMRRepository zmsByteDanceMRRepository;

    @InjectMocks
    private ZmsByteDanceMRServiceImpl zmsByteDanceMRServiceImpl;

    @Mock
    private WsmAssembleLinesService wsmAssembleLinesService;

    @Mock
    private ZmsDeviceInventoryUploadService zmsDeviceInventoryUploadService;

    @Before
    public void setUp() throws Exception {
        ReflectionTestUtils.setField(zmsByteDanceMRServiceImpl, "b2bSize", 100);
        List<SysLookupValues> emptyList = new ArrayList<>();
        PowerMockito.when(wsmAssembleLinesService.getSysLookupValues(anyString())).thenReturn(listWithItems());

        /* Started by AICoder, pid:5580ay34c9b0a6a146790999504cea116f46e4dc */

        // 创建一个新的ArrayList对象
        List<ZmsByteDanceMREntityDTO> zmsByteDanceMREntityDTOList = new ArrayList<>();

        // 添加两个ZmsByteDanceMREntityDTO对象到列表中
        ZmsByteDanceMREntityDTO dto1 = new ZmsByteDanceMREntityDTO();
        // 假设ZmsByteDanceMREntityDTO类有一些属性，你需要为它们赋值
        dto1.setMaterialDocNo("111");
        dto1.setUserAddress("222");

        ZmsByteDanceMREntityDTO dto2 = new ZmsByteDanceMREntityDTO();
        // 假设ZmsByteDanceMREntityDTO类有一些属性，你需要为它们赋值
        dto2.setUserAddress("333");

        zmsByteDanceMREntityDTOList.add(dto1);
        zmsByteDanceMREntityDTOList.add(dto2);
        PowerMockito.when(zmsByteDanceMRRepository.getEntityByMaterialRequisition(any())).thenReturn(zmsByteDanceMREntityDTOList);
        /* Ended by AICoder, pid:5580ay34c9b0a6a146790999504cea116f46e4dc */

        String dataTransferBatchNo = "ZTE120240505000008";
        PowerMockito.when(zmsDeviceInventoryUploadService.getDataTransferBatchNo()).thenReturn(dataTransferBatchNo);

        //添加两行serviceSnCustomerItemsDTOList内容，并PowerMockito.when给centerfactoryRemoteService.getCustomerItemsInfo
        /* Started by AICoder, pid:k4edevbb4bhc406145050b47c0a4df06d1a41163 */
        CustomerItemsDTO customerItemsDTO1 = new CustomerItemsDTO();
        customerItemsDTO1.setZteCode("111");
        CustomerItemsDTO customerItemsDTO2 = new CustomerItemsDTO();
        customerItemsDTO2.setZteCode("222");
        customerItemsDTO2.setCustomerCode("2A");
        CustomerItemsDTO customerItemsDTO3 = new CustomerItemsDTO();
        customerItemsDTO3.setZteCode("333");
        customerItemsDTO3.setCustomerItemName("3A");
        CustomerItemsDTO customerItemsDTO4 = new CustomerItemsDTO();
        customerItemsDTO4.setZteCode("444");
        customerItemsDTO4.setCustomerCode("4A");
        customerItemsDTO4.setCustomerItemName("4B");
        List<CustomerItemsDTO> serviceSnCustomerItemsDTOList = new ArrayList<>();
        serviceSnCustomerItemsDTOList.add(customerItemsDTO1);
        serviceSnCustomerItemsDTOList.add(customerItemsDTO2);
        serviceSnCustomerItemsDTOList.add(customerItemsDTO3);
        serviceSnCustomerItemsDTOList.add(customerItemsDTO4);
        PowerMockito.when(centerfactoryRemoteService.getCustomerItemsInfo(anyString(), any())).thenReturn(serviceSnCustomerItemsDTOList);
        /* Ended by AICoder, pid:k4edevbb4bhc406145050b47c0a4df06d1a41163 */
    }

    @Test(timeout = 8000)
    public void testUploadZmsByteDanceMRData_EmptyEntityNameList() throws Exception {
        ZmsByteDanceMRDTO dto = new ZmsByteDanceMRDTO();
        /* Started by AICoder, pid:y5808q792dr7d6714a7b0affb0a1370f75417cf0 */
        List<String> entityList = Arrays.asList("element1", "element2");
        /* Ended by AICoder, pid:y5808q792dr7d6714a7b0affb0a1370f75417cf0 */
        dto.setEntityNameList(entityList);
        PowerMockito.when(zmsByteDanceMRRepository.getEntityByMaterialRequisition(any())).thenReturn(new ArrayList<>());
        zmsByteDanceMRServiceImpl.uploadZmsByteDanceMRData(dto, "123");
        Assert.assertNull(null);
    }

    @Test(timeout = 8000)
    public void testUploadZmsByteDanceMRData_NullEntityNameList() throws Exception {
        ZmsByteDanceMRDTO dto = new ZmsByteDanceMRDTO();
        dto.setEntityNameList(null);
        zmsByteDanceMRServiceImpl.uploadZmsByteDanceMRData(dto, "123");
        Assert.assertNull(null);
    }

    @Test(timeout = 8000)
    public void testUploadZmsByteDanceMRData_ValidEntityNameList() throws Exception {
        ZmsByteDanceMRDTO dto = new ZmsByteDanceMRDTO();
        List<String> entityNameList = new ArrayList<>();
        entityNameList.add("name1");
        entityNameList.add("name2");
        dto.setEntityNameList(entityNameList);
        //添加两行zmsByteDanceMRDetailDTOList内容，并PowerMockito.when给zmsByteDanceMRRepository.getDetailByMaterialRequisition
        /* Started by AICoder, pid:p809dv5b9cm452f149c409b6700bc70380b4526c */
        ZmsByteDanceMRDetailDTO zmsByteDanceMRDetailDTO1 = new ZmsByteDanceMRDetailDTO();
        zmsByteDanceMRDetailDTO1.setItemCode("111");
        ZmsByteDanceMRDetailDTO zmsByteDanceMRDetailDTO2 = new ZmsByteDanceMRDetailDTO();
        zmsByteDanceMRDetailDTO2.setItemCode("222");
        ZmsByteDanceMRDetailDTO zmsByteDanceMRDetailDTO3 = new ZmsByteDanceMRDetailDTO();
        zmsByteDanceMRDetailDTO3.setItemCode("333");
        ZmsByteDanceMRDetailDTO zmsByteDanceMRDetailDTO4 = new ZmsByteDanceMRDetailDTO();
        zmsByteDanceMRDetailDTO4.setItemCode("444");
        List<ZmsByteDanceMRDetailDTO> zmsByteDanceMRDetailDTOList = new ArrayList<>();
        zmsByteDanceMRDetailDTOList.add(zmsByteDanceMRDetailDTO1);
        zmsByteDanceMRDetailDTOList.add(zmsByteDanceMRDetailDTO2);
        zmsByteDanceMRDetailDTOList.add(zmsByteDanceMRDetailDTO3);
        zmsByteDanceMRDetailDTOList.add(zmsByteDanceMRDetailDTO4);
        PowerMockito.when(zmsByteDanceMRRepository.getDetailByMaterialRequisition(anyString())).thenReturn(zmsByteDanceMRDetailDTOList);

        /* Ended by AICoder, pid:p809dv5b9cm452f149c409b6700bc70380b4526c */
        zmsByteDanceMRServiceImpl.uploadZmsByteDanceMRData(dto, "123");
        Assert.assertNull(null);
    }

    private List<SysLookupValues> listWithItems() {
        List<SysLookupValues> list = new ArrayList<>();
        SysLookupValues item1 = new SysLookupValues();
        item1.setDescription("Description1");
        item1.setLookupCode(new BigDecimal("824005900001"));
        list.add(item1);
        SysLookupValues item2 = new SysLookupValues();
        item2.setDescription("Description2");
        item2.setLookupCode(new BigDecimal("824005900002"));
        list.add(item2);
        return list;
    }
}
/*Ended by AICoder, pid:24f52xf96av078814ac00a15607bfe726ff33fe6*/